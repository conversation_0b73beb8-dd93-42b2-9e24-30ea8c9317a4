/*
==============================================================================
    وحدة الزاحف - Praetorian Web Crawler
    
    الوصف: زاحف للبحث عن الروابط والمسارات في تطبيقات الويب
    المؤلف: Praetorian Team
==============================================================================
*/

/*
==============================================================================
    كلاس الزاحف
==============================================================================
*/

class PraetorianWebCrawler

    # خصائص الزاحف
    oHTTPClient = NULL
    oLogger = NULL
    bVerbose = false
    
    # إعدادات الزحف
    nMaxDepth = 3
    nMaxPages = 100
    nDelay = 1000  # تأخير بالميلي ثانية بين الطلبات
    
    # قوائم التتبع
    aVisitedURLs = []
    aFoundURLs = []
    aFoundForms = []
    aFoundInputs = []
    
    # فلاتر الروابط
    aAllowedExtensions = [".html", ".htm", ".php", ".asp", ".aspx", ".jsp", ".do"]
    aBlockedExtensions = [".jpg", ".jpeg", ".png", ".gif", ".css", ".js", ".pdf", ".zip"]
    
    /*
    دالة البناء
    */
    func init
        oLogger = PraetorianLoggerInstance
        oHTTPClient = new PraetorianHTTPClient
        oLogger.debug("تم تهيئة الزاحف")
    
    /*
    تعيين العمق الأقصى للزحف
    المدخلات: nDepth - العمق الأقصى
    */
    func setMaxDepth nDepth
        nMaxDepth = nDepth
        oLogger.debug("تم تعيين العمق الأقصى إلى " + nMaxDepth)
    
    /*
    تعيين العدد الأقصى للصفحات
    المدخلات: nPages - العدد الأقصى للصفحات
    */
    func setMaxPages nPages
        nMaxPages = nPages
        oLogger.debug("تم تعيين العدد الأقصى للصفحات إلى " + nMaxPages)
    
    /*
    تعيين التأخير بين الطلبات
    المدخلات: nDelayMs - التأخير بالميلي ثانية
    */
    func setDelay nDelayMs
        nDelay = nDelayMs
        oLogger.debug("تم تعيين التأخير إلى " + nDelay + " ميلي ثانية")
    
    /*
    تفعيل/إلغاء الوضع المفصل
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func setVerbose bEnable
        bVerbose = bEnable
        oHTTPClient.setVerbose(bEnable)
    
    /*
    تنظيف رابط (إزالة المعاملات والمراسي)
    المدخلات: cURL - الرابط
    المخرجات: الرابط منظف
    */
    func cleanURL cURL
        # إزالة المرساة (#)
        nPos = substr(cURL, "#")
        if nPos > 0
            cURL = substr(cURL, 1, nPos - 1)
        ok
        
        # إزالة المعاملات الشائعة غير المهمة
        cURL = substr(cURL, "?utm_source", "")
        cURL = substr(cURL, "?utm_medium", "")
        cURL = substr(cURL, "?utm_campaign", "")
        
        return trim(cURL)
    
    /*
    التحقق من صحة الرابط
    المدخلات: cURL - الرابط
    المخرجات: true إذا كان صحيحاً، false إذا لم يكن كذلك
    */
    func isValidURL cURL
        if len(cURL) = 0
            return false
        ok
        
        # التحقق من البروتوكول
        if not (substr(cURL, "http://") = 1 or substr(cURL, "https://") = 1)
            return false
        ok
        
        # التحقق من الامتدادات المحظورة
        cURLLower = lower(cURL)
        for cExt in aBlockedExtensions
            if substr(cURLLower, cExt) > 0
                return false
            ok
        next
        
        return true
    
    /*
    تحويل رابط نسبي إلى مطلق
    المدخلات: cBaseURL - الرابط الأساسي، cRelativeURL - الرابط النسبي
    المخرجات: الرابط المطلق
    */
    func makeAbsoluteURL cBaseURL, cRelativeURL
        if substr(cRelativeURL, "http://") = 1 or substr(cRelativeURL, "https://") = 1
            return cRelativeURL
        ok
        
        # إزالة الشرطة المائلة الأخيرة من الرابط الأساسي
        if right(cBaseURL, 1) = "/"
            cBaseURL = substr(cBaseURL, 1, len(cBaseURL) - 1)
        ok
        
        # إضافة الشرطة المائلة إذا لم تكن موجودة
        if left(cRelativeURL, 1) != "/"
            cRelativeURL = "/" + cRelativeURL
        ok
        
        return cBaseURL + cRelativeURL
    
    /*
    استخراج الروابط من محتوى HTML
    المدخلات: cHTML - محتوى HTML، cBaseURL - الرابط الأساسي
    المخرجات: قائمة الروابط المستخرجة
    */
    func extractLinks cHTML, cBaseURL
        aLinks = []
        
        # البحث عن روابط <a href="">
        cPattern = 'href="([^"]*)"'
        aMatches = findAllMatches(cHTML, cPattern)
        
        for cMatch in aMatches
            cURL = cleanURL(cMatch)
            cAbsoluteURL = makeAbsoluteURL(cBaseURL, cURL)
            
            if isValidURL(cAbsoluteURL) and not find(aLinks, cAbsoluteURL)
                add(aLinks, cAbsoluteURL)
            ok
        next
        
        # البحث عن روابط في JavaScript (مبسط)
        cJSPattern = "location.href"
        aJSMatches = []  # تبسيط مؤقت
        
        for cMatch in aJSMatches
            cURL = cleanURL(cMatch)
            cAbsoluteURL = makeAbsoluteURL(cBaseURL, cURL)
            
            if isValidURL(cAbsoluteURL) and not find(aLinks, cAbsoluteURL)
                add(aLinks, cAbsoluteURL)
            ok
        next
        
        return aLinks
    
    /*
    البحث عن تطابقات في النص (مبسط)
    المدخلات: cText - النص، cPattern - النمط
    المخرجات: قائمة التطابقات
    */
    func findAllMatches cText, cPattern
        aMatches = []
        
        # هذه دالة مبسطة للبحث عن href
        # في التطبيق الحقيقي ستحتاج لمكتبة regex
        
        if substr(cPattern, 'href="') > 0
            nStart = 1
            while true
                nPos = substr(cText, 'href="', nStart)
                if nPos = 0 break ok
                
                nStart = nPos + 6  # طول 'href="'
                nEnd = substr(cText, '"', nStart)
                if nEnd = 0 break ok
                
                cMatch = substr(cText, nStart, nEnd - nStart)
                if len(cMatch) > 0
                    add(aMatches, cMatch)
                ok
                
                nStart = nEnd + 1
            end
        ok
        
        return aMatches
    
    /*
    استخراج النماذج من محتوى HTML
    المدخلات: cHTML - محتوى HTML، cBaseURL - الرابط الأساسي
    المخرجات: قائمة النماذج المستخرجة
    */
    func extractForms cHTML, cBaseURL
        aForms = []
        
        # البحث عن نماذج <form>
        nStart = 1
        while true
            nFormStart = substr(cHTML, "<form", nStart)
            if nFormStart = 0 break ok
            
            nFormEnd = substr(cHTML, "</form>", nFormStart)
            if nFormEnd = 0 break ok
            
            cFormHTML = substr(cHTML, nFormStart, nFormEnd - nFormStart + 7)
            
            # استخراج معلومات النموذج
            aFormInfo = [
                :action = extractFormAction(cFormHTML, cBaseURL),
                :method = extractFormMethod(cFormHTML),
                :inputs = extractFormInputs(cFormHTML)
            ]
            
            add(aForms, aFormInfo)
            nStart = nFormEnd + 7
        end
        
        return aForms
    
    /*
    استخراج action من النموذج
    */
    func extractFormAction cFormHTML, cBaseURL
        nPos = substr(cFormHTML, 'action="')
        if nPos = 0
            return cBaseURL
        ok
        
        nStart = nPos + 8
        nEnd = substr(cFormHTML, '"', nStart)
        if nEnd = 0
            return cBaseURL
        ok
        
        cAction = substr(cFormHTML, nStart, nEnd - nStart)
        return makeAbsoluteURL(cBaseURL, cAction)
    
    /*
    استخراج method من النموذج
    */
    func extractFormMethod cFormHTML
        nPos = substr(cFormHTML, 'method="')
        if nPos = 0
            return "GET"
        ok
        
        nStart = nPos + 8
        nEnd = substr(cFormHTML, '"', nStart)
        if nEnd = 0
            return "GET"
        ok
        
        return upper(substr(cFormHTML, nStart, nEnd - nStart))
    
    /*
    استخراج حقول الإدخال من النموذج
    */
    func extractFormInputs cFormHTML
        aInputs = []
        
        # البحث عن حقول <input>
        nStart = 1
        while true
            nInputPos = substr(cFormHTML, "<input", nStart)
            if nInputPos = 0 break ok
            
            nInputEnd = substr(cFormHTML, ">", nInputPos)
            if nInputEnd = 0 break ok
            
            cInputHTML = substr(cFormHTML, nInputPos, nInputEnd - nInputPos + 1)
            
            # استخراج معلومات الحقل
            aInputInfo = [
                :name = extractInputAttribute(cInputHTML, "name"),
                :type = extractInputAttribute(cInputHTML, "type"),
                :value = extractInputAttribute(cInputHTML, "value")
            ]
            
            add(aInputs, aInputInfo)
            nStart = nInputEnd + 1
        end
        
        return aInputs
    
    /*
    استخراج خاصية من حقل الإدخال
    */
    func extractInputAttribute cInputHTML, cAttribute
        cPattern = cAttribute + '="'
        nPos = substr(cInputHTML, cPattern)
        if nPos = 0
            return ""
        ok
        
        nStart = nPos + len(cPattern)
        nEnd = substr(cInputHTML, '"', nStart)
        if nEnd = 0
            return ""
        ok
        
        return substr(cInputHTML, nStart, nEnd - nStart)
    
    /*
    زحف صفحة واحدة
    المدخلات: cURL - رابط الصفحة
    المخرجات: محتوى الصفحة أو NULL في حالة الفشل
    */
    func crawlPage cURL
        if find(aVisitedURLs, cURL)
            return NULL
        ok
        
        oLogger.info("زحف الصفحة: " + cURL)
        add(aVisitedURLs, cURL)
        
        # إرسال طلب GET
        oResponse = oHTTPClient.get(cURL, NULL)
        
        if not oResponse[:success]
            oLogger.warning("فشل في زحف الصفحة: " + cURL)
            return NULL
        ok
        
        # تأخير بين الطلبات
        if nDelay > 0
            sleep(nDelay)
        ok
        
        return oResponse[:content]
    
    /*
    بدء الزحف من رابط أساسي
    المدخلات: cStartURL - الرابط الأساسي
    المخرجات: تقرير الزحف
    */
    func crawl cStartURL
        oLogger.startOperation("بدء الزحف من: " + cStartURL)
        
        # إعادة تعيين القوائم
        aVisitedURLs = []
        aFoundURLs = []
        aFoundForms = []
        
        # قائمة الروابط المراد زحفها
        aURLsToVisit = [cStartURL]
        nCurrentDepth = 0
        
        while len(aURLsToVisit) > 0 and nCurrentDepth < nMaxDepth and len(aVisitedURLs) < nMaxPages
            aCurrentLevelURLs = aURLsToVisit
            aURLsToVisit = []
            
            ? "زحف المستوى " + (nCurrentDepth + 1) + " - " + len(aCurrentLevelURLs) + " صفحة"
            
            for cURL in aCurrentLevelURLs
                if len(aVisitedURLs) >= nMaxPages
                    exit
                ok
                
                cContent = crawlPage(cURL)
                if cContent != NULL
                    # استخراج الروابط
                    aPageLinks = extractLinks(cContent, cURL)
                    for cLink in aPageLinks
                        if not find(aFoundURLs, cLink) and not find(aVisitedURLs, cLink)
                            add(aFoundURLs, cLink)
                            add(aURLsToVisit, cLink)
                        ok
                    next
                    
                    # استخراج النماذج
                    aPageForms = extractForms(cContent, cURL)
                    for aForm in aPageForms
                        add(aFoundForms, aForm)
                    next
                    
                    if bVerbose
                        ? "  تم العثور على " + len(aPageLinks) + " رابط و " + len(aPageForms) + " نموذج"
                    ok
                ok
            next
            
            nCurrentDepth++
        end
        
        oLogger.endOperation("انتهاء الزحف")
        
        # إنشاء تقرير
        return createCrawlReport()
    
    /*
    إنشاء تقرير الزحف
    */
    func createCrawlReport
        return [
            :visited_urls = aVisitedURLs,
            :found_urls = aFoundURLs,
            :found_forms = aFoundForms,
            :total_pages = len(aVisitedURLs),
            :total_links = len(aFoundURLs),
            :total_forms = len(aFoundForms)
        ]

# إنشاء مثيل عام من الزاحف
PraetorianCrawlerInstance = new PraetorianWebCrawler

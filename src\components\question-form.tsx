'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Trash2, Check, MoveVertical, AlertCircle } from 'lucide-react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';

interface QuestionOption {
  id?: number;
  text: string;
  isCorrect: boolean;
  order: number;
}

interface QuestionAnswer {
  id?: number;
  text: string;
  isCorrect: boolean;
  explanation: string | null;
}

interface Question {
  id?: number;
  text: string;
  type: string;
  difficultyLevel: string;
  points: number;
  bankId: number;
  options: QuestionOption[];
  answers: QuestionAnswer[];
}

interface QuestionBank {
  id: number;
  name: string;
}

interface QuestionFormProps {
  questionBanks: QuestionBank[];
  question?: Question;
  defaultBankId?: number;
  onSave: () => void;
  onCancel: () => void;
}

export function QuestionForm({ questionBanks, question, defaultBankId, onSave, onCancel }: QuestionFormProps) {
  const [formData, setFormData] = useState<Question>({
    text: '',
    type: 'MULTIPLE_CHOICE',
    difficultyLevel: 'MEDIUM',
    points: 1,
    bankId: defaultBankId || (questionBanks.length > 0 ? questionBanks[0].id : 0),
    options: [],
    answers: []
  });

  const [newOption, setNewOption] = useState('');
  const [newAnswer, setNewAnswer] = useState('');
  const [newExplanation, setNewExplanation] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (question) {
      setFormData({
        id: question.id,
        text: question.text,
        type: question.type,
        difficultyLevel: question.difficultyLevel,
        points: question.points,
        bankId: question.bankId,
        options: question.options.map(option => ({
          id: option.id,
          text: option.text,
          isCorrect: option.isCorrect,
          order: option.order
        })),
        answers: question.answers.map(answer => ({
          id: answer.id,
          text: answer.text,
          isCorrect: answer.isCorrect,
          explanation: answer.explanation
        }))
      });
    } else if (defaultBankId) {
      setFormData(prev => ({
        ...prev,
        bankId: defaultBankId
      }));
    }
  }, [question, defaultBankId]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.text.trim()) {
      newErrors.text = 'نص السؤال مطلوب';
    }

    if (!formData.bankId) {
      newErrors.bankId = 'بنك الأسئلة مطلوب';
    }

    if (['MULTIPLE_CHOICE', 'TRUE_FALSE', 'MATCHING', 'ORDERING'].includes(formData.type)) {
      if (formData.options.length === 0) {
        newErrors.options = 'يجب إضافة خيار واحد على الأقل';
      }

      if (formData.type !== 'TRUE_FALSE' && !formData.options.some(option => option.isCorrect)) {
        newErrors.correctOption = 'يجب تحديد خيار صحيح واحد على الأقل';
      }
    }

    if (['SHORT_ANSWER', 'ESSAY', 'FILL_BLANK'].includes(formData.type)) {
      if (formData.answers.length === 0) {
        newErrors.answers = 'يجب إضافة إجابة واحدة على الأقل';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('يرجى تصحيح الأخطاء في النموذج');
      return;
    }

    setIsSubmitting(true);

    try {
      const url = '/api/questions';
      const method = formData.id ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(formData.id ? 'تم تحديث السؤال بنجاح' : 'تم إنشاء السؤال بنجاح');
        onSave();
      } else {
        toast.error(result.error || 'حدث خطأ أثناء حفظ السؤال');
      }
    } catch (error) {
      console.error('Error saving question:', error);
      toast.error('حدث خطأ أثناء حفظ السؤال');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addOption = () => {
    if (!newOption.trim()) {
      toast.error('يرجى إدخال نص الخيار');
      return;
    }

    const newOptions = [
      ...formData.options,
      {
        text: newOption,
        isCorrect: formData.options.length === 0, // الخيار الأول صحيح افتراضيًا
        order: formData.options.length
      }
    ];

    setFormData({ ...formData, options: newOptions });
    setNewOption('');
    setErrors({ ...errors, options: '', correctOption: '' });
  };

  const removeOption = (index: number) => {
    const newOptions = [...formData.options];
    newOptions.splice(index, 1);

    // إعادة ترتيب الخيارات
    const reorderedOptions = newOptions.map((option, idx) => ({
      ...option,
      order: idx
    }));

    setFormData({ ...formData, options: reorderedOptions });
  };

  const toggleCorrectOption = (index: number) => {
    const newOptions = [...formData.options];

    if (formData.type === 'TRUE_FALSE' || formData.type === 'MULTIPLE_CHOICE') {
      // في حالة الاختيار من متعدد أو صح/خطأ، يمكن اختيار خيار واحد فقط
      newOptions.forEach((option, idx) => {
        option.isCorrect = idx === index;
      });
    } else {
      // في الأنواع الأخرى، يمكن اختيار عدة خيارات
      newOptions[index].isCorrect = !newOptions[index].isCorrect;
    }

    setFormData({ ...formData, options: newOptions });
    setErrors({ ...errors, correctOption: '' });
  };

  const addAnswer = () => {
    if (!newAnswer.trim()) {
      toast.error('يرجى إدخال نص الإجابة');
      return;
    }

    const newAnswers = [
      ...formData.answers,
      {
        text: newAnswer,
        isCorrect: true,
        explanation: newExplanation || null
      }
    ];

    setFormData({ ...formData, answers: newAnswers });
    setNewAnswer('');
    setNewExplanation('');
    setErrors({ ...errors, answers: '' });
  };

  const removeAnswer = (index: number) => {
    const newAnswers = [...formData.answers];
    newAnswers.splice(index, 1);
    setFormData({ ...formData, answers: newAnswers });
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(formData.options);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // إعادة ترتيب الخيارات
    const reorderedOptions = items.map((item, index) => ({
      ...item,
      order: index
    }));

    setFormData({ ...formData, options: reorderedOptions });
  };

  const getQuestionTypeOptions = () => {
    return [
      { value: 'MULTIPLE_CHOICE', label: 'اختيار من متعدد' },
      { value: 'TRUE_FALSE', label: 'صح أو خطأ' },
      { value: 'SHORT_ANSWER', label: 'إجابة قصيرة' },
      { value: 'ESSAY', label: 'مقال' },
      { value: 'MATCHING', label: 'مطابقة' },
      { value: 'FILL_BLANK', label: 'ملء الفراغات' },
      { value: 'ORDERING', label: 'ترتيب' }
    ];
  };

  const getDifficultyLevelOptions = () => {
    return [
      { value: 'EASY', label: 'سهل' },
      { value: 'MEDIUM', label: 'متوسط' },
      { value: 'HARD', label: 'صعب' },
      { value: 'VERY_HARD', label: 'صعب جداً' }
    ];
  };

  const handleTypeChange = (type: string) => {
    // إعادة تعيين الخيارات والإجابات عند تغيير نوع السؤال
    let newOptions: QuestionOption[] = [];
    const newAnswers: QuestionAnswer[] = [];

    if (type === 'TRUE_FALSE') {
      // إنشاء خيارات صح وخطأ تلقائيًا
      newOptions = [
        { text: 'صح', isCorrect: true, order: 0 },
        { text: 'خطأ', isCorrect: false, order: 1 }
      ];
    }

    setFormData({
      ...formData,
      type,
      options: newOptions,
      answers: newAnswers
    });

    // إعادة تعيين الأخطاء
    setErrors({});
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-right block font-medium">نص السؤال <span className="text-red-500">*</span></label>
          <Textarea
            value={formData.text}
            onChange={(e) => {
              setFormData({ ...formData, text: e.target.value });
              if (e.target.value.trim()) {
                setErrors({ ...errors, text: '' });
              }
            }}
            dir="rtl"
            className="text-right min-h-[100px]"
            placeholder="أدخل نص السؤال هنا..."
          />
          {errors.text && (
            <p className="text-red-500 text-sm flex items-center justify-end mt-1">
              <AlertCircle className="h-4 w-4 ml-1" />
              {errors.text}
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="text-right block font-medium">نوع السؤال <span className="text-red-500">*</span></label>
            <Select
              value={formData.type}
              onValueChange={handleTypeChange}
            >
              <SelectTrigger className="w-full text-right">
                <SelectValue placeholder="اختر نوع السؤال" />
              </SelectTrigger>
              <SelectContent>
                {getQuestionTypeOptions().map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-right block font-medium">بنك الأسئلة <span className="text-red-500">*</span></label>
            <Select
              value={formData.bankId.toString()}
              onValueChange={(value) => {
                setFormData({ ...formData, bankId: parseInt(value) });
                setErrors({ ...errors, bankId: '' });
              }}
            >
              <SelectTrigger className="w-full text-right">
                <SelectValue placeholder="اختر بنك الأسئلة" />
              </SelectTrigger>
              <SelectContent>
                {questionBanks.map(bank => (
                  <SelectItem key={bank.id} value={bank.id.toString()}>
                    {bank.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.bankId && (
              <p className="text-red-500 text-sm flex items-center justify-end mt-1">
                <AlertCircle className="h-4 w-4 ml-1" />
                {errors.bankId}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-right block font-medium">مستوى الصعوبة</label>
            <Select
              value={formData.difficultyLevel}
              onValueChange={(value) => setFormData({ ...formData, difficultyLevel: value })}
            >
              <SelectTrigger className="w-full text-right">
                <SelectValue placeholder="اختر مستوى الصعوبة" />
              </SelectTrigger>
              <SelectContent>
                {getDifficultyLevelOptions().map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-right block font-medium">النقاط</label>
            <Input
              type="number"
              min="0.5"
              step="0.5"
              value={formData.points}
              onChange={(e) => setFormData({ ...formData, points: parseFloat(e.target.value) || 1 })}
              dir="rtl"
              className="text-right"
            />
          </div>
        </div>

        <Tabs defaultValue="options" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="options">الخيارات</TabsTrigger>
            <TabsTrigger value="answers">الإجابات الصحيحة</TabsTrigger>
          </TabsList>

          <TabsContent value="options" className="p-4 border rounded-lg mt-2">
            {['MULTIPLE_CHOICE', 'TRUE_FALSE', 'MATCHING', 'ORDERING'].includes(formData.type) ? (
              <div className="space-y-4">
                <div className="flex items-end space-x-2">
                  <Input
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    placeholder="أدخل نص الخيار..."
                    dir="rtl"
                    className="text-right flex-1 ml-2"
                    disabled={formData.type === 'TRUE_FALSE'}
                  />
                  <Button
                    type="button"
                    onClick={addOption}
                    disabled={formData.type === 'TRUE_FALSE'}
                  >
                    <Plus className="ml-1" size={16} />
                    إضافة خيار
                  </Button>
                </div>

                {errors.options && (
                  <p className="text-red-500 text-sm flex items-center justify-end mt-1">
                    <AlertCircle className="h-4 w-4 ml-1" />
                    {errors.options}
                  </p>
                )}

                {errors.correctOption && (
                  <p className="text-red-500 text-sm flex items-center justify-end mt-1">
                    <AlertCircle className="h-4 w-4 ml-1" />
                    {errors.correctOption}
                  </p>
                )}

                {formData.options.length > 0 ? (
                  <DragDropContext onDragEnd={handleDragEnd}>
                    <Droppable droppableId="options">
                      {(provided) => (
                        <ul
                          className="space-y-2"
                          {...provided.droppableProps}
                          ref={provided.innerRef}
                        >
                          {formData.options.map((option, index) => (
                            <Draggable
                              key={`option-${index}`}
                              draggableId={`option-${index}`}
                              index={index}
                              isDragDisabled={formData.type === 'TRUE_FALSE'}
                            >
                              {(provided) => (
                                <li
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  className={`p-2 border rounded flex justify-between items-center ${
                                    option.isCorrect ? 'bg-green-50 border-green-200' : ''
                                  }`}
                                >
                                  <div className="flex items-center">
                                    {formData.type !== 'TRUE_FALSE' && (
                                      <div {...provided.dragHandleProps} className="ml-2 cursor-move">
                                        <MoveVertical size={16} />
                                      </div>
                                    )}
                                    <span>{option.text}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <Button
                                      type="button"
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => toggleCorrectOption(index)}
                                      className={`ml-2 ${
                                        option.isCorrect
                                          ? 'text-primary-color hover:text-green-700'
                                          : 'text-gray-400 hover:text-gray-500'
                                      }`}
                                    >
                                      {option.isCorrect ? (
                                        <Check className="h-5 w-5" />
                                      ) : (
                                        <Check className="h-5 w-5" />
                                      )}
                                    </Button>
                                    {formData.type !== 'TRUE_FALSE' && (
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeOption(index)}
                                        className="text-red-500 hover:text-red-700"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    )}
                                  </div>
                                </li>
                              )}
                            </Draggable>
                          ))}
                          {provided.placeholder}
                        </ul>
                      )}
                    </Droppable>
                  </DragDropContext>
                ) : (
                  <p className="text-center text-gray-500 py-4">
                    لا توجد خيارات. أضف خيارات للسؤال.
                  </p>
                )}
              </div>
            ) : (
              <p className="text-center text-gray-500 py-4">
                هذا النوع من الأسئلة لا يتطلب خيارات.
              </p>
            )}
          </TabsContent>

          <TabsContent value="answers" className="p-4 border rounded-lg mt-2">
            {['SHORT_ANSWER', 'ESSAY', 'FILL_BLANK'].includes(formData.type) ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-right block">نص الإجابة</label>
                  <Input
                    value={newAnswer}
                    onChange={(e) => setNewAnswer(e.target.value)}
                    placeholder="أدخل نص الإجابة الصحيحة..."
                    dir="rtl"
                    className="text-right"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-right block">شرح الإجابة (اختياري)</label>
                  <Textarea
                    value={newExplanation}
                    onChange={(e) => setNewExplanation(e.target.value)}
                    placeholder="أدخل شرح الإجابة..."
                    dir="rtl"
                    className="text-right"
                  />
                </div>

                <div className="flex justify-end">
                  <Button type="button" onClick={addAnswer}>
                    <Plus className="ml-1" size={16} />
                    إضافة إجابة
                  </Button>
                </div>

                {errors.answers && (
                  <p className="text-red-500 text-sm flex items-center justify-end mt-1">
                    <AlertCircle className="h-4 w-4 ml-1" />
                    {errors.answers}
                  </p>
                )}

                {formData.answers.length > 0 ? (
                  <ul className="space-y-4">
                    {formData.answers.map((answer, index) => (
                      <li key={index} className="p-3 border rounded bg-green-50">
                        <div className="flex justify-between items-start">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeAnswer(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          <div className="text-right flex-1 ml-4">
                            <div className="font-semibold">{answer.text}</div>
                            {answer.explanation && (
                              <div className="text-sm text-gray-600 mt-2">
                                <span className="font-semibold">الشرح: </span>
                                {answer.explanation}
                              </div>
                            )}
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-center text-gray-500 py-4">
                    لا توجد إجابات. أضف إجابات للسؤال.
                  </p>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-center text-gray-500 py-2">
                  الإجابات الصحيحة محددة في قسم الخيارات.
                </p>

                {formData.options.filter(option => option.isCorrect).length > 0 ? (
                  <ul className="space-y-2">
                    {formData.options
                      .filter(option => option.isCorrect)
                      .map((option, index) => (
                        <li key={index} className="p-3 border rounded bg-green-50">
                          <div className="font-semibold text-right">{option.text}</div>
                        </li>
                      ))}
                  </ul>
                ) : (
                  <p className="text-center text-red-500">
                    لم يتم تحديد أي إجابة صحيحة في الخيارات.
                  </p>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="ml-2"
        >
          إلغاء
        </Button>
        <Button
          type="submit"
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <span className="animate-spin ml-1">⏳</span>
              جاري الحفظ...
            </>
          ) : (
            <>
              {formData.id ? 'حفظ التغييرات' : 'إنشاء السؤال'}
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

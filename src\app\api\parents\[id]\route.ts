import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import { UserRole } from '@prisma/client';

// GET /api/parents/[id] - جلب بيانات ولي أمر واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🔍 بدء جلب بيانات الولي:', params.id);

    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.TEACHER)) {
      return NextResponse.json(
        { error: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
        { status: 401 }
      );
    }

    // جلب بيانات الولي
    const parent = await prisma.parent.findUnique({
      where: {
        id: parseInt(params.id)
      },
      include: {
        students: {
          include: {
            classe: true
          }
        }
      }
    });

    if (!parent) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الولي' },
        { status: 404 }
      );
    }

    console.log('✅ تم جلب بيانات الولي بنجاح:', parent.name);

    return NextResponse.json({
      success: true,
      parent: {
        id: parent.id,
        name: parent.name,
        phone: parent.phone,
        email: parent.email,
        address: parent.address,
        amountPerStudent: parent.amountPerStudent,
        students: parent.students.map(student => ({
          id: student.id,
          name: student.name,
          grade: student.classe?.name || 'غير محدد'
        }))
      }
    });

  } catch (error) {
    console.error('❌ خطأ في جلب بيانات الولي:', error);
    return NextResponse.json(
      { error: 'فشل في جلب بيانات الولي' },
      { status: 500 }
    );
  }
}

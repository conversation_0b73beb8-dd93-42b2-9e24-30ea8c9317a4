'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'react-toastify'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { FaTags, FaPlus, FaEdit, FaTrash, FaFolder, FaFolderOpen, FaSearch, FaFilter } from 'react-icons/fa'
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'

interface ExpenseCategory {
  id: number
  name: string
  description: string | null
  icon: string | null
  color: string | null
  isActive: boolean
  parentId: number | null
  subCategories: ExpenseCategory[]
  _count: {
    expenses: number
    subCategories: number
  }
}

export default function ExpenseCategoriesPage() {
  const [categories, setCategories] = useState<ExpenseCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<ExpenseCategory | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [showInactive, setShowInactive] = useState(false)
  const [parentFilter, setParentFilter] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '',
    color: 'var(--primary-color)',
    parentId: '',
    isActive: true
  })

  // جلب فئات المصروفات
  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch(
        `/api/expense-categories?query=${searchQuery}&includeInactive=${showInactive}${
          parentFilter ? `&parentId=${parentFilter}` : ''
        }`
      )

      if (!response.ok) {
        throw new Error('فشل في جلب فئات المصروفات')
      }

      const data = await response.json()
      setCategories(data.categories)
    } catch (error) {
      console.error('خطأ في جلب فئات المصروفات:', error)
      toast.error('فشل في جلب فئات المصروفات')
    } finally {
      setLoading(false)
    }
  }

  // إضافة فئة جديدة
  const handleAddCategory = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (!formData.name) {
        toast.error('اسم الفئة مطلوب')
        return
      }

      const response = await fetch('/api/expense-categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || null,
          icon: formData.icon || null,
          color: formData.color || null,
          parentId: formData.parentId ? parseInt(formData.parentId) : null
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في إضافة الفئة')
      }

      toast.success('تم إضافة الفئة بنجاح')
      setIsAddDialogOpen(false)
      resetForm()
      fetchCategories()
    } catch (error) {
      console.error('خطأ في إضافة الفئة:', error)
      toast.error(error instanceof Error ? error.message : 'فشل في إضافة الفئة')
    }
  }

  // تحديث فئة
  const handleUpdateCategory = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (!selectedCategory) return
      if (!formData.name) {
        toast.error('اسم الفئة مطلوب')
        return
      }

      const response = await fetch(`/api/expense-categories/${selectedCategory.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || null,
          icon: formData.icon || null,
          color: formData.color || null,
          parentId: formData.parentId ? parseInt(formData.parentId) : null,
          isActive: formData.isActive
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تحديث الفئة')
      }

      toast.success('تم تحديث الفئة بنجاح')
      setIsEditDialogOpen(false)
      setSelectedCategory(null)
      resetForm()
      fetchCategories()
    } catch (error) {
      console.error('خطأ في تحديث الفئة:', error)
      toast.error(error instanceof Error ? error.message : 'فشل في تحديث الفئة')
    }
  }

  // حذف فئة
  const handleDeleteCategory = async (id: number) => {
    try {
      if (!confirm('هل أنت متأكد من حذف هذه الفئة؟')) return

      const response = await fetch(`/api/expense-categories/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في حذف الفئة')
      }

      toast.success('تم حذف الفئة بنجاح')
      fetchCategories()
    } catch (error) {
      console.error('خطأ في حذف الفئة:', error)
      toast.error(error instanceof Error ? error.message : 'فشل في حذف الفئة')
    }
  }

  // إعادة تعيين نموذج البيانات
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      icon: '',
      color: 'var(--primary-color)',
      parentId: '',
      isActive: true
    })
  }

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchCategories()
  }, [searchQuery, showInactive, parentFilter])

  return (
    <OptimizedProtectedRoute requiredPermission="admin.expense-categories.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaTags className="text-[var(--primary-color)]" />
          فئات المصروفات
        </h1>
        <QuickActionButtons
          entityType="expense-categories"
          actions={[
            {
              key: 'create',
              label: 'إضافة فئة جديدة',
              icon: <FaPlus />,
              onClick: () => {
                resetForm()
                setIsAddDialogOpen(true)
              },
              variant: 'primary'
            }
          ]}
        />
      </div>

      <div className="flex flex-col md:flex-row gap-4 bg-white p-4 rounded-lg shadow-sm border border-green-100 mb-4">
        <div className="flex-1 flex items-center gap-2">
          <FaSearch className="text-[var(--primary-color)]" />
          <Input
            type="text"
            placeholder="بحث..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full border-[var(--primary-color)] focus:border-[var(--secondary-color)]"
          />
        </div>

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="show-inactive" className="text-sm text-gray-600">
              إظهار غير النشطة
            </Label>
            <Switch
              id="show-inactive"
              checked={showInactive}
              onCheckedChange={setShowInactive}
            />
          </div>

          <div className="flex items-center gap-2">
            <FaFilter className="text-[var(--primary-color)]" />
            <Select
              value={parentFilter === null ? 'all' : parentFilter === '' ? 'main' : parentFilter}
              onValueChange={(value) => {
                if (value === 'all') setParentFilter(null)
                else if (value === 'main') setParentFilter('')
                else setParentFilter(value)
              }}
            >
              <SelectTrigger className="w-[180px] border-[var(--primary-color)] focus:border-[var(--secondary-color)]">
                <SelectValue placeholder="تصفية حسب الفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفئات</SelectItem>
                <SelectItem value="main">الفئات الرئيسية فقط</SelectItem>
                {categories
                  .filter((cat) => !cat.parentId)
                  .map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : categories.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg border border-dashed border-gray-300">
          <FaTags className="mx-auto text-4xl text-gray-300 mb-4" />
          <h3 className="text-xl font-bold text-gray-600 mb-2">لا توجد فئات مصروفات</h3>
          <p className="text-gray-500 mb-4">لم يتم العثور على أي فئات مصروفات مطابقة لمعايير البحث</p>
          <PermissionGuard requiredPermission="admin.expense-categories.create">
            <Button
              onClick={() => {
                resetForm()
                setIsAddDialogOpen(true)
              }}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
            >
              <FaPlus className="ml-2" /> إضافة فئة جديدة
            </Button>
          </PermissionGuard>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => (
            <Card
              key={category.id}
              className={`hover:shadow-md transition-shadow duration-300 ${
                !category.isActive ? 'opacity-60' : ''
              }`}
            >
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: category.color || 'var(--primary-color)' }}
                  >
                    {category.parentId ? (
                      <FaFolder className="text-white text-xs" />
                    ) : (
                      <FaFolderOpen className="text-white text-xs" />
                    )}
                  </div>
                  <span className={!category.isActive ? 'line-through' : ''}>{category.name}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {category.description && <p className="text-gray-600 text-sm mb-3">{category.description}</p>}

                <div className="flex flex-wrap gap-2 mb-3">
                  {category.parentId ? (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                      فئة فرعية
                    </span>
                  ) : (
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                      فئة رئيسية
                    </span>
                  )}
                  {!category.isActive && (
                    <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                      غير نشطة
                    </span>
                  )}
                  {category._count.expenses > 0 && (
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                      {category._count.expenses} مصروف
                    </span>
                  )}
                  {category._count.subCategories > 0 && (
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                      {category._count.subCategories} فئة فرعية
                    </span>
                  )}
                </div>

                <div className="flex justify-end gap-2 mt-4">
                  <OptimizedActionButtonGroup
                    entityType="expense-categories"
                    onEdit={() => {
                      setSelectedCategory(category)
                      setFormData({
                        name: category.name,
                        description: category.description || '',
                        icon: category.icon || '',
                        color: category.color || 'var(--primary-color)',
                        parentId: category.parentId ? category.parentId.toString() : '',
                        isActive: category.isActive
                      })
                      setIsEditDialogOpen(true)
                    }}
                    onDelete={() => handleDeleteCategory(category.id)}
                    showEdit={true}
                    showDelete={category._count.expenses === 0 && category._count.subCategories === 0}
                    size="sm"
                    className="gap-2"
                    deleteConfirmTitle="هل أنت متأكد من حذف هذه الفئة؟"
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* نافذة إضافة فئة جديدة */}
      <AnimatedDialog
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        title="إضافة فئة مصروفات جديدة"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="add-category-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaPlus size={14} />
            <span>إضافة</span>
          </Button>
        }
      >
        <form id="add-category-form" onSubmit={handleAddCategory}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right col-span-1">
                اسم الفئة <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right col-span-1">
                الوصف
              </Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="parent" className="text-right col-span-1">
                الفئة الأب
              </Label>
              <Select
                value={formData.parentId}
                onValueChange={(value) => setFormData({ ...formData, parentId: value })}
              >
                <SelectTrigger id="parent" className="col-span-3">
                  <SelectValue placeholder="اختر الفئة الأب (اختياري)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة أب</SelectItem>
                  {categories
                    .filter((cat) => !cat.parentId)
                    .map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="color" className="text-right col-span-1">
                اللون
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  className="w-12 h-10 p-1"
                />
                <Input
                  type="text"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  className="flex-1"
                />
              </div>
            </div>
          </div>
        </form>
      </AnimatedDialog>

      {/* نافذة تعديل الفئة */}
      <AnimatedDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        title="تعديل فئة المصروفات"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="edit-category-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaEdit size={14} />
            <span>تحديث</span>
          </Button>
        }
      >
        <form id="edit-category-form" onSubmit={handleUpdateCategory}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right col-span-1">
                اسم الفئة <span className="text-red-500">*</span>
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right col-span-1">
                الوصف
              </Label>
              <Input
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-parent" className="text-right col-span-1">
                الفئة الأب
              </Label>
              <Select
                value={formData.parentId}
                onValueChange={(value) => setFormData({ ...formData, parentId: value })}
              >
                <SelectTrigger id="edit-parent" className="col-span-3">
                  <SelectValue placeholder="اختر الفئة الأب (اختياري)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة أب</SelectItem>
                  {categories
                    .filter((cat) => !cat.parentId && cat.id !== selectedCategory?.id)
                    .map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-color" className="text-right col-span-1">
                اللون
              </Label>
              <div className="col-span-3 flex items-center gap-2">
                <Input
                  id="edit-color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  className="w-12 h-10 p-1"
                />
                <Input
                  type="text"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  className="flex-1"
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-active" className="text-right col-span-1">
                نشط
              </Label>
              <div className="col-span-3 flex items-center">
                <Switch
                  id="edit-active"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
              </div>
            </div>
          </div>
        </form>
      </AnimatedDialog>
      </div>
    </OptimizedProtectedRoute>
  )
}

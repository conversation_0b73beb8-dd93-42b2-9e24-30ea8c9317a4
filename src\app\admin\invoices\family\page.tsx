'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import {
  FaFileInvoice, FaPlus, FaSearch, FaSync, FaEye,
  FaUsers, FaMoneyBillWave, FaCalendarAlt, FaTimes,
  FaPrint, FaInfoCircle, FaCreditCard
} from 'react-icons/fa';

interface FamilyInvoice {
  id: number;
  amount: number;
  dueDate: string;
  month: number;
  year: number;
  description: string;
  status: string;
  totalPaid: number;
  remaining: number;
  studentsCount: number;
  parent: {
    id: number;
    name: string;
    phone: string;
    students: Array<{
      id: number;
      name: string;
      grade: string;
    }>;
  };
}

interface Parent {
  id: number;
  name: string;
  phone: string;
  email?: string;
  studentsCount: number;
  amountPerStudent?: number;
  totalAmount?: number;
  students: Array<{
    id: number;
    name: string;
    grade: string;
  }>;
}

export default function FamilyInvoicesPage() {
  const [invoices, setInvoices] = useState<FamilyInvoice[]>([]);
  const [parents, setParents] = useState<Parent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [monthFilter, setMonthFilter] = useState('');
  const [yearFilter, setYearFilter] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);
  const [selectedParents, setSelectedParents] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { addToast } = useToast();

  // بيانات نموذج إنشاء فاتورة
  const [invoiceData, setInvoiceData] = useState({
    parentId: '',
    amount: '',
    dueDate: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    description: ''
  });

  // بيانات نموذج الفواتير المتعددة
  const [bulkData, setBulkData] = useState({
    amount: '',
    dueDate: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    description: '',
    useIndividualAmounts: false
  });

  // جلب الفواتير الجماعية
  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        ...(statusFilter && { status: statusFilter }),
        ...(monthFilter && { month: monthFilter }),
        ...(yearFilter && { year: yearFilter }),
        limit: '50'
      });

      const response = await fetch(`/api/invoices/family?${queryParams}`);
      const data = await response.json();

      if (data.success) {
        setInvoices(data.invoices);
      } else {
        throw new Error(data.error || 'فشل في جلب الفواتير');
      }
    } catch (error) {
      console.error('خطأ في جلب الفواتير:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في جلب الفواتير الجماعية',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // جلب أولياء الأمور
  const fetchParents = async () => {
    try {
      const response = await fetch('/api/parents?withStudents=true');
      const data = await response.json();

      if (data.success) {
        setParents(data.parents);
      } else if (Array.isArray(data)) {
        // إذا كان الـ API يرجع مصفوفة مباشرة (الـ API القديم)
        const formattedParents = data.map(parent => ({
          id: parent.id,
          name: parent.name,
          phone: parent.phone,
          email: parent.email,
          studentsCount: parent.students?.length || 0,
          amountPerStudent: parent.amountPerStudent,
          totalAmount: parent.amountPerStudent ? parent.amountPerStudent * (parent.students?.length || 0) : null,
          students: parent.students?.map(student => ({
            id: student.id,
            name: student.name,
            grade: student.classe?.name || 'غير محدد'
          })) || []
        }));
        setParents(formattedParents.filter(p => p.studentsCount > 0)); // فقط الأولياء الذين لديهم أبناء
      } else {
        throw new Error(data.error || 'فشل في جلب أولياء الأمور');
      }
    } catch (error) {
      console.error('خطأ في جلب أولياء الأمور:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في جلب أولياء الأمور',
        variant: 'destructive'
      });
    }
  };

  useEffect(() => {
    fetchInvoices();
    fetchParents();
  }, [statusFilter, monthFilter, yearFilter]);

  // تصفية الفواتير
  const filteredInvoices = invoices.filter(invoice =>
    invoice.parent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    invoice.parent.phone.includes(searchQuery) ||
    invoice.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // دالة تنسيق العملة
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // دالة الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800';
      case 'PARTIALLY_PAID': return 'bg-blue-100 text-blue-800';
      case 'OVERDUE': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  // دالة الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'PAID': return 'مدفوعة';
      case 'PARTIALLY_PAID': return 'مدفوعة جزئياً';
      case 'OVERDUE': return 'متأخرة';
      default: return 'غير مدفوعة';
    }
  };

  // عرض الفاتورة PDF
  const handleViewInvoice = (invoiceId: number) => {
    window.open(`/api/invoices/pdf/${invoiceId}`, '_blank');
  };

  // طباعة الفاتورة
  const handlePrintInvoice = (invoiceId: number) => {
    const printWindow = window.open(`/api/invoices/pdf/${invoiceId}`, '_blank');
    if (printWindow) {
      printWindow.onload = () => {
        printWindow.print();
      };
    } else {
      addToast({
        title: 'خطأ',
        description: 'تعذر فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح.',
        variant: 'destructive'
      });
    }
  };

  // عرض تفاصيل الفاتورة والولي
  const handleViewDetails = (invoice: FamilyInvoice) => {
    addToast({
      title: 'تفاصيل الفاتورة',
      description: `الولي: ${invoice.parent.name} | المبلغ: ${formatCurrency(invoice.amount)} | الأطفال: ${invoice.studentsCount}`,
      variant: 'default'
    });
  };

  // دفع الفاتورة الجماعية
  const handlePayInvoice = (invoice: FamilyInvoice) => {
    // توجيه إلى صفحة المدفوعات مع تحديد الولي
    window.open(`/admin/payments/by-parent?parentId=${invoice.parent.id}&invoiceId=${invoice.id}`, '_blank');
  };

  // إنشاء فاتورة جماعية
  const handleCreateInvoice = async () => {
    if (!invoiceData.parentId || !invoiceData.amount || !invoiceData.dueDate) {
      addToast({
        title: 'خطأ',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch('/api/invoices/family', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...invoiceData,
          amount: parseFloat(invoiceData.amount)
        })
      });

      const data = await response.json();

      if (data.success) {
        addToast({
          title: 'تم بنجاح',
          description: data.message,
          variant: 'default'
        });
        setShowCreateModal(false);
        setInvoiceData({
          parentId: '',
          amount: '',
          dueDate: '',
          month: new Date().getMonth() + 1,
          year: new Date().getFullYear(),
          description: ''
        });
        fetchInvoices();
      } else {
        throw new Error(data.error || 'فشل في إنشاء الفاتورة');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفاتورة:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في إنشاء الفاتورة الجماعية',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // إنشاء فواتير متعددة
  const handleCreateBulkInvoices = async () => {
    if (selectedParents.length === 0 || (!bulkData.amount && !bulkData.useIndividualAmounts) || !bulkData.dueDate) {
      addToast({
        title: 'خطأ',
        description: 'يرجى اختيار أولياء أمور، تحديد تاريخ استحقاق، وإدخال مبلغ موحد أو تفعيل خيار المبالغ الفردية.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch('/api/invoices/family/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          parentIds: selectedParents,
          ...bulkData
        })
      });

      const data = await response.json();

      if (data.success) {
        addToast({
          title: 'تم بنجاح',
          description: data.message,
          variant: 'default'
        });
        setShowBulkModal(false);
        setSelectedParents([]);
        setBulkData({
          amount: '',
          dueDate: '',
          month: new Date().getMonth() + 1,
          year: new Date().getFullYear(),
          description: '',
          useIndividualAmounts: false
        });
        fetchInvoices();
      } else {
        throw new Error(data.error || 'فشل في إنشاء الفواتير');
      }
    } catch (error) {
      console.error('خطأ في إنشاء الفواتير:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في إنشاء الفواتير الجماعية',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermissions={['admin.invoices.view', 'admin.payments.view']}>
      <div className="container mx-auto p-6 space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
              <FaFileInvoice className="text-[var(--primary-color)]" />
              الفواتير الجماعية
            </h1>
            <p className="text-gray-600 mt-1 pr-3">إدارة الفواتير الجماعية لأولياء الأمور</p>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-green-600 hover:bg-green-700"
            >
              <FaPlus className="mr-2" />
              فاتورة جديدة
            </Button>
            <Button
              onClick={() => setShowBulkModal(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <FaUsers className="mr-2" />
              فواتير متعددة
            </Button>
            <Button
              variant="outline"
              onClick={fetchInvoices}
              disabled={loading}
            >
              <FaSync className={`mr-2 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </Button>
          </div>
        </div>

        {/* شريط البحث والفلترة */}
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">البحث</Label>
                <div className="relative">
                  <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="اسم الولي، الهاتف، أو الوصف..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="status">الحالة</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الحالات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الحالات</SelectItem>
                    <SelectItem value="UNPAID">غير مدفوعة</SelectItem>
                    <SelectItem value="PARTIALLY_PAID">مدفوعة جزئياً</SelectItem>
                    <SelectItem value="PAID">مدفوعة</SelectItem>
                    <SelectItem value="OVERDUE">متأخرة</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="month">الشهر</Label>
                <Select value={monthFilter} onValueChange={setMonthFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الشهور" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الشهور</SelectItem>
                    {Array.from({ length: 12 }, (_, i) => (
                      <SelectItem key={i + 1} value={(i + 1).toString()}>
                        {new Date(2024, i).toLocaleDateString('ar-DZ', { month: 'long' })}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="year">السنة</Label>
                <Select value={yearFilter} onValueChange={setYearFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع السنوات" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع السنوات</SelectItem>
                    <SelectItem value="2024">2024</SelectItem>
                    <SelectItem value="2025">2025</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* قائمة الفواتير */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaFileInvoice className="text-orange-600" />
              الفواتير الجماعية ({filteredInvoices.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <FaSync className="animate-spin text-4xl text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">جاري تحميل الفواتير...</p>
              </div>
            ) : filteredInvoices.length === 0 ? (
              <div className="text-center py-8">
                <FaFileInvoice className="text-4xl text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">لا توجد فواتير جماعية</p>
                <Button
                  onClick={() => setShowCreateModal(true)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <FaPlus className="mr-2" />
                  إنشاء أول فاتورة جماعية
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredInvoices.map((invoice) => (
                  <div key={invoice.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-bold text-lg text-gray-900">{invoice.parent.name}</h4>
                        <p className="text-gray-600">{invoice.parent.phone}</p>
                        <p className="text-sm text-gray-500">{invoice.description}</p>
                      </div>
                      <Badge className={getStatusColor(invoice.status)}>
                        {getStatusText(invoice.status)}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-3">
                      <div className="text-center">
                        <p className="text-sm text-gray-600">المبلغ</p>
                        <p className="font-bold text-gray-900">{formatCurrency(invoice.amount)}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">المدفوع</p>
                        <p className="font-bold text-green-600">{formatCurrency(invoice.totalPaid)}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">المتبقي</p>
                        <p className="font-bold text-red-600">{formatCurrency(invoice.remaining)}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">عدد الأطفال</p>
                        <p className="font-bold text-blue-600">{invoice.studentsCount}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">تاريخ الاستحقاق</p>
                        <p className="font-bold text-gray-900">
                          {new Date(invoice.dueDate).toLocaleDateString('ar-DZ')}
                        </p>
                      </div>
                    </div>

                    <div className="flex gap-2 flex-wrap">
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-blue-600 hover:text-blue-700"
                        onClick={() => handleViewInvoice(invoice.id)}
                        title="عرض الفاتورة PDF"
                      >
                        <FaEye className="mr-1" />
                        عرض الفاتورة
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-green-600 hover:text-green-700"
                        onClick={() => handlePrintInvoice(invoice.id)}
                        title="طباعة الفاتورة"
                      >
                        <FaPrint className="mr-1" />
                        طباعة
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="text-purple-600 hover:text-purple-700"
                        onClick={() => handleViewDetails(invoice)}
                        title="عرض تفاصيل الولي والأطفال"
                      >
                        <FaInfoCircle className="mr-1" />
                        التفاصيل
                      </Button>
                      {(invoice.status === 'UNPAID' || invoice.status === 'PARTIALLY_PAID' || invoice.status === 'OVERDUE') && (
                        <Button
                          size="sm"
                          className="bg-orange-600 hover:bg-orange-700"
                          onClick={() => handlePayInvoice(invoice)}
                          title="دفع الفاتورة الجماعية"
                        >
                          <FaCreditCard className="mr-1" />
                          دفع
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة إنشاء فاتورة جديدة */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-bold text-gray-900">إنشاء فاتورة جماعية جديدة</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowCreateModal(false)}
                  >
                    <FaTimes />
                  </Button>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="parentId">ولي الأمر *</Label>
                    <Select value={invoiceData.parentId} onValueChange={(value) => {
                      const selectedParent = parents.find(p => p.id.toString() === value);
                      if (selectedParent) {
                        const totalAmount = (selectedParent.amountPerStudent || 0) * (selectedParent.studentsCount || 0);
                        setInvoiceData(prev => ({
                          ...prev,
                          parentId: value,
                          amount: totalAmount > 0 ? totalAmount.toString() : ''
                        }));
                      } else {
                        setInvoiceData(prev => ({ ...prev, parentId: value, amount: '' }));
                      }
                    }}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر ولي الأمر" />
                      </SelectTrigger>
                      <SelectContent>
                        {parents.map((parent) => (
                          <SelectItem key={parent.id} value={parent.id.toString()}>
                            {parent.name} - {parent.studentsCount} أطفال
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="amount">المبلغ (دج) *</Label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="أدخل المبلغ"
                      value={invoiceData.amount}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, amount: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="dueDate">تاريخ الاستحقاق *</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={invoiceData.dueDate}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="month">الشهر</Label>
                      <Select value={invoiceData.month.toString()} onValueChange={(value) => setInvoiceData(prev => ({ ...prev, month: parseInt(value) }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {Array.from({ length: 12 }, (_, i) => (
                            <SelectItem key={i + 1} value={(i + 1).toString()}>
                              {new Date(2024, i).toLocaleDateString('ar-DZ', { month: 'long' })}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="year">السنة</Label>
                      <Select value={invoiceData.year.toString()} onValueChange={(value) => setInvoiceData(prev => ({ ...prev, year: parseInt(value) }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="2024">2024</SelectItem>
                          <SelectItem value="2025">2025</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">الوصف</Label>
                    <Textarea
                      id="description"
                      placeholder="وصف الفاتورة (اختياري)"
                      value={invoiceData.description}
                      onChange={(e) => setInvoiceData(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                    />
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <Button
                    onClick={handleCreateInvoice}
                    disabled={isSubmitting}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    {isSubmitting ? 'جاري الإنشاء...' : 'إنشاء الفاتورة'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateModal(false)}
                    disabled={isSubmitting}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نافذة إنشاء فواتير متعددة */}
        {showBulkModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-bold text-gray-900">إنشاء فواتير جماعية متعددة</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowBulkModal(false)}
                  >
                    <FaTimes />
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* بيانات الفاتورة */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">بيانات الفاتورة</h4>

                    <div>
                      <Label htmlFor="bulkAmount">المبلغ (دج) *</Label>
                      <Input
                        id="bulkAmount"
                        type="number"
                        placeholder={bulkData.useIndividualAmounts ? "سيتم استخدام المبلغ الفردي لكل ولي" : "أدخل المبلغ"}
                        value={bulkData.amount}
                        onChange={(e) => setBulkData(prev => ({ ...prev, amount: e.target.value }))}
                        disabled={bulkData.useIndividualAmounts}
                      />
                    </div>

                    <div>
                      <Label htmlFor="bulkDueDate">تاريخ الاستحقاق *</Label>
                      <Input
                        id="bulkDueDate"
                        type="date"
                        value={bulkData.dueDate}
                        onChange={(e) => setBulkData(prev => ({ ...prev, dueDate: e.target.value }))}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="bulkMonth">الشهر</Label>
                        <Select value={bulkData.month.toString()} onValueChange={(value) => setBulkData(prev => ({ ...prev, month: parseInt(value) }))}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: 12 }, (_, i) => (
                              <SelectItem key={i + 1} value={(i + 1).toString()}>
                                {new Date(2024, i).toLocaleDateString('ar-DZ', { month: 'long' })}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="bulkYear">السنة</Label>
                        <Select value={bulkData.year.toString()} onValueChange={(value) => setBulkData(prev => ({ ...prev, year: parseInt(value) }))}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="2024">2024</SelectItem>
                            <SelectItem value="2025">2025</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="bulkDescription">الوصف</Label>
                      <Textarea
                        id="bulkDescription"
                        placeholder="وصف الفاتورة (اختياري)"
                        value={bulkData.description}
                        onChange={(e) => setBulkData(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="useIndividualAmounts"
                        checked={bulkData.useIndividualAmounts}
                        onChange={(e) => setBulkData(prev => ({ ...prev, useIndividualAmounts: e.target.checked }))}
                        className="rounded"
                      />
                      <Label htmlFor="useIndividualAmounts" className="text-sm">
                        استخدام المبالغ الفردية لكل ولي
                      </Label>
                    </div>
                  </div>

                  {/* اختيار أولياء الأمور */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">اختيار أولياء الأمور ({selectedParents.length})</h4>
                    <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                      {parents.map((parent) => (
                        <label key={parent.id} className="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0">
                          <input
                            type="checkbox"
                            checked={selectedParents.includes(parent.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedParents(prev => [...prev, parent.id]);
                              } else {
                                setSelectedParents(prev => prev.filter(id => id !== parent.id));
                              }
                            }}
                            className="mr-3 rounded"
                          />
                          <div className="flex-1">
                            <div className="font-medium text-gray-900">{parent.name}</div>
                            <div className="text-sm text-gray-600">
                              {parent.phone} • {parent.studentsCount} أطفال
                              {parent.amountPerStudent && (
                                <span className="text-green-600"> • {formatCurrency(parent.amountPerStudent)} لكل طفل</span>
                              )}
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <Button
                    onClick={handleCreateBulkInvoices}
                    disabled={isSubmitting || selectedParents.length === 0}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isSubmitting ? 'جاري الإنشاء...' : `إنشاء ${selectedParents.length} فاتورة`}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedParents([]);
                      setShowBulkModal(false);
                    }}
                    disabled={isSubmitting}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </OptimizedProtectedRoute>
  );
}

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ActivityLogger, ActivityType } from "@/lib/activity-logger";

// GET /api/exam-questions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const examId = searchParams.get('examId');

    if (!examId) {
      return NextResponse.json({
        error: 'معرف الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    const examQuestions = await prisma.examQuestion.findMany({
      where: {
        examId: parseInt(examId)
      },
      include: {
        question: {
          include: {
            options: {
              orderBy: {
                order: 'asc'
              }
            },
            answers: true
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    });

    return NextResponse.json({
      data: examQuestions,
      success: true,
      message: 'تم جلب أسئلة الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error fetching exam questions:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب أسئلة الامتحان',
      success: false
    }, { status: 500 });
  }
}

// POST /api/exam-questions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { examId, questionId, order, points } = body;

    // التحقق من البيانات المطلوبة
    if (!examId || !questionId) {
      return NextResponse.json({
        error: 'معرف الامتحان ومعرف السؤال مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من عدم وجود السؤال في الامتحان مسبقًا
    const existingQuestion = await prisma.examQuestion.findUnique({
      where: {
        examId_questionId: {
          examId: parseInt(examId),
          questionId: parseInt(questionId)
        }
      }
    });

    if (existingQuestion) {
      return NextResponse.json({
        error: 'السؤال موجود بالفعل في هذا الامتحان',
        success: false
      }, { status: 400 });
    }

    // إضافة السؤال إلى الامتحان
    const examQuestion = await prisma.examQuestion.create({
      data: {
        examId: parseInt(examId),
        questionId: parseInt(questionId),
        order: order ? parseInt(order) : 0,
        points: points ? parseFloat(points) : null
      },
      include: {
        question: {
          include: {
            options: {
              orderBy: {
                order: 'asc'
              }
            },
            answers: true
          }
        },
        exam: {
          select: {
            id: true,
            evaluationType: true,
            month: true
          }
        }
      }
    });

    // تحديث حقل hasAutoGrading في الامتحان إذا كان نوع السؤال يدعم التصحيح الآلي
    const autoGradingQuestionTypes = ['MULTIPLE_CHOICE', 'TRUE_FALSE', 'MATCHING', 'ORDERING'];
    if (autoGradingQuestionTypes.includes(examQuestion.question.type)) {
      await prisma.exam.update({
        where: { id: parseInt(examId) },
        data: { hasAutoGrading: true }
      });
    }

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.EXAM,
      `تمت إضافة سؤال إلى الامتحان: ${examQuestion.exam.month} - ${examQuestion.exam.evaluationType}`
    );

    return NextResponse.json({
      data: examQuestion,
      success: true,
      message: 'تمت إضافة السؤال إلى الامتحان بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding question to exam:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إضافة السؤال إلى الامتحان',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/exam-questions
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, order, points } = body;

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json({
        error: 'معرف سؤال الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    // تحديث سؤال الامتحان
    const examQuestion = await prisma.examQuestion.update({
      where: { id: parseInt(id) },
      data: {
        order: order !== undefined ? parseInt(order) : undefined,
        points: points !== undefined ? parseFloat(points) : undefined
      },
      include: {
        question: true,
        exam: {
          select: {
            id: true,
            evaluationType: true,
            month: true
          }
        }
      }
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.EXAM,
      `تم تحديث سؤال في الامتحان: ${examQuestion.exam.month} - ${examQuestion.exam.evaluationType}`
    );

    return NextResponse.json({
      data: examQuestion,
      success: true,
      message: 'تم تحديث سؤال الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error updating exam question:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث سؤال الامتحان',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/exam-questions
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف سؤال الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    // الحصول على معلومات سؤال الامتحان قبل الحذف
    const examQuestion = await prisma.examQuestion.findUnique({
      where: { id: parseInt(id) },
      include: {
        exam: {
          select: {
            id: true,
            evaluationType: true,
            month: true
          }
        }
      }
    });

    if (!examQuestion) {
      return NextResponse.json({
        error: 'سؤال الامتحان غير موجود',
        success: false
      }, { status: 404 });
    }

    // حذف سؤال الامتحان
    await prisma.examQuestion.delete({
      where: { id: parseInt(id) }
    });

    // تحديث حقل hasAutoGrading في الامتحان إذا لزم الأمر
    const remainingQuestions = await prisma.examQuestion.findMany({
      where: { examId: examQuestion.examId },
      include: {
        question: true
      }
    });

    const hasAutoGradingQuestions = remainingQuestions.some(eq =>
      ['MULTIPLE_CHOICE', 'TRUE_FALSE', 'MATCHING', 'ORDERING'].includes(eq.question.type)
    );

    if (!hasAutoGradingQuestions) {
      await prisma.exam.update({
        where: { id: examQuestion.examId },
        data: { hasAutoGrading: false }
      });
    }

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.EXAM,
      `تم حذف سؤال من الامتحان: ${examQuestion.exam.month} - ${examQuestion.exam.evaluationType}`
    );

    return NextResponse.json({
      success: true,
      message: 'تم حذف سؤال الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error deleting exam question:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف سؤال الامتحان',
      success: false
    }, { status: 500 });
  }
}

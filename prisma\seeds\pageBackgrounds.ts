import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedPageBackgrounds() {
  console.log('🎨 بدء إضافة خلفيات الصفحات الافتراضية...');

  const defaultBackgrounds = [
    {
      pageName: 'home',
      displayName: 'الصفحة الرئيسية',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.1,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'scroll',
      isActive: false,
      priority: 1
    },
    {
      pageName: 'about',
      displayName: 'من نحن',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.15,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'scroll',
      isActive: false,
      priority: 1
    },
    {
      pageName: 'contact',
      displayName: 'اتصل بنا',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.2,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'scroll',
      isActive: false,
      priority: 1
    },
    {
      pageName: 'programs',
      displayName: 'البرامج',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.15,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'scroll',
      isActive: false,
      priority: 1
    },
    {
      pageName: 'khatm-sessions',
      displayName: 'مجالس الختم',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.25,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'scroll',
      isActive: false,
      priority: 1
    },
    {
      pageName: 'donations',
      displayName: 'التبرعات',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.2,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'scroll',
      isActive: false,
      priority: 1
    },
    {
      pageName: 'login',
      displayName: 'تسجيل الدخول',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.1,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'fixed',
      isActive: false,
      priority: 1
    },
    {
      pageName: 'register',
      displayName: 'التسجيل',
      imageUrl: null,
      overlayColor: '#136907',
      overlayOpacity: 0.1,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'fixed',
      isActive: false,
      priority: 1
    }
  ];

  let created = 0;
  let updated = 0;

  for (const backgroundData of defaultBackgrounds) {
    try {
      const existingBackground = await prisma.pageBackground.findUnique({
        where: { pageName: backgroundData.pageName }
      });

      if (existingBackground) {
        // تحديث الخلفية الموجودة إذا لم تكن نشطة
        if (!existingBackground.isActive) {
          await prisma.pageBackground.update({
            where: { id: existingBackground.id },
            data: {
              overlayColor: backgroundData.overlayColor,
              overlayOpacity: backgroundData.overlayOpacity,
              position: backgroundData.position,
              size: backgroundData.size,
              repeat: backgroundData.repeat,
              attachment: backgroundData.attachment,
              priority: backgroundData.priority
            }
          });
          updated++;
          console.log(`✅ تم تحديث خلفية صفحة: ${backgroundData.displayName}`);
        } else {
          console.log(`⏭️ تم تخطي خلفية صفحة نشطة: ${backgroundData.displayName}`);
        }
      } else {
        // إنشاء خلفية جديدة
        await prisma.pageBackground.create({
          data: backgroundData
        });
        created++;
        console.log(`✅ تم إنشاء خلفية صفحة: ${backgroundData.displayName}`);
      }
    } catch (error) {
      console.error(`❌ خطأ في معالجة خلفية صفحة ${backgroundData.displayName}:`, error);
    }
  }

  console.log(`🎨 تم الانتهاء من إضافة خلفيات الصفحات:`);
  console.log(`   - تم إنشاء: ${created} خلفية جديدة`);
  console.log(`   - تم تحديث: ${updated} خلفية موجودة`);
  console.log(`   - المجموع: ${created + updated} خلفية`);
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  seedPageBackgrounds()
    .catch((e) => {
      console.error('❌ خطأ في إضافة خلفيات الصفحات:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

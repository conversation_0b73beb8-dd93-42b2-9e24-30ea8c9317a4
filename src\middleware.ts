import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import * as jose from "jose";

export type JWTPayload = {
  id: number;
  username: string;
  role: string;
  roleId?: number;
};

const protectedRoutes = new Set(["/admin"]);

// نظام الصلاحيات الجديد - سيتم التحقق من الصلاحيات على مستوى الصفحات
const roleBasedRoutes: { [key: string]: Set<string> } = {
  ADMIN: new Set(["/admin"]), // المدير يصل لكل شيء
  EMPLOYEE: new Set(["/admin"]), // الموظف يصل للأدمن لكن الصلاحيات تحدد الصفحات
  TEACHER: new Set(["/teachers", "/students", "/attendance"]),
  STUDENT: new Set(["/students", "/profile"]),
  PARENT: new Set(["/parents", "/profile"]),
};

const roleRedirects: { [key: string]: string } = {
  TEACHER: "/teachers",
  STUDENT: "/students",
  PARENT: "/parents",
  EMPLOYEE: "/admin/employee-dashboard",
  ADMIN: "/admin",
};

async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const secret = new TextEncoder().encode(process.env.JWT_SECRET);
    const { payload } = await jose.jwtVerify(token, secret);
    return payload as JWTPayload;
  } catch (error) {
    console.error("JWT Verification Failed:", error);
    return null;
  }
}

// ملاحظة: التحقق من الصلاحيات التفصيلية يتم على مستوى الصفحات باستخدام ProtectedRoute

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // السماح بالمسارات العامة
  if (
    pathname === "/" ||
    pathname.startsWith("/_next") ||
    pathname.startsWith("/static") ||
    pathname.startsWith("/api") ||
    pathname.startsWith("/uploads") ||
    ["/login", "/register", "/about", "/contact", "/programs"].includes(pathname)
  ) {
    return NextResponse.next();
  }

  const isProtectedRoute = [...protectedRoutes].some((route) => pathname.startsWith(route));

  if (isProtectedRoute) {
    const token = req.cookies.get("jwtToken")?.value;

    if (!token) {
      return NextResponse.redirect(new URL("/login", req.url));
    }

    const userData = await verifyToken(token);

    if (!userData) {
      return NextResponse.redirect(new URL("/login", req.url));
    }

    const userRole = userData.role;

    // إذا كان المستخدم مدير، السماح بالوصول لكل شيء
    if (userRole === 'ADMIN') {
      return NextResponse.next();
    }

    // التحقق من النظام الجديد للموظفين
    if (userRole === 'EMPLOYEE') {
      // إعادة توجيه الموظفين من /admin إلى لوحة تحكم الموظفين
      if (pathname === '/admin') {
        return NextResponse.redirect(new URL("/admin/employee-dashboard", req.url));
      }

      // السماح بالوصول للوحة تحكم الموظفين
      if (pathname === '/admin/employee-dashboard') {
        return NextResponse.next();
      }

      // السماح بالوصول لجميع صفحات الأدمن (التحقق التفصيلي سيتم على مستوى الصفحات)
      if (pathname.startsWith('/admin/')) {
        return NextResponse.next();
      }
    }

    // للأدوار الأخرى (TEACHER, STUDENT, PARENT)، استخدام النظام القديم
    const allowedRoutes = roleBasedRoutes[userRole] || new Set();
    const hasAccess = [...allowedRoutes].some((route) => pathname.startsWith(route));

    if (!hasAccess) {
      return NextResponse.redirect(new URL(roleRedirects[userRole] || "/", req.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!api/.*|_next/static|_next/image|favicon.ico).*)"],
};

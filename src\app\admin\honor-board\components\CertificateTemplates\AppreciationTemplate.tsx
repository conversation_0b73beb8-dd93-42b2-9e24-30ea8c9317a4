import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Download, Printer } from 'lucide-react';
import { CertificateData } from './index';
import html2canvas from 'html2canvas';

interface AppreciationTemplateProps {
  certificateData: CertificateData;
  showControls?: boolean;
}

export default function AppreciationTemplate({
  certificateData,
  showControls = true
}: AppreciationTemplateProps) {
  const certificateRef = useRef<HTMLDivElement>(null);

  const handlePrint = async () => {
    if (!certificateRef.current) return;

    try {
      // فتح نافذة جديدة للطباعة
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert('يرجى السماح بالنوافذ المنبثقة لطباعة الشهادة');
        return;
      }

      // إنشاء محتوى HTML للشهادة
      const certificateHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
          <title>شهادة تقدير</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 20px;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: #f8f9fa;
              color: #333;
            }

            .certificate-container {
              max-width: 800px;
              margin: 0 auto;
              background-color: white;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              overflow: hidden;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 4px solid #e9d5ff;
              margin: 20px;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #f9f0ff 0%, #ffffff 50%, #fcf4ff 100%);
              z-index: -1;
            }

            .certificate-header {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-icon {
              width: 80px;
              height: 80px;
              background-color: #9333ea;
              border-radius: 50%;
              margin: 0 auto 15px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 40px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: #9333ea;
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: #9333ea;
              margin-bottom: 10px;
              border-bottom: 2px solid #e9d5ff;
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 40px;
              margin-top: auto;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #e9d5ff;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 40px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #e9d5ff;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #9333ea;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 20px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }

            /* تحسينات للأجهزة المحمولة */
            @media (max-width: 768px) {
              body {
                padding: 10px;
              }

              .certificate {
                padding: 20px;
                margin: 10px;
              }

              .certificate-title {
                font-size: 24px;
              }

              .certificate-subtitle {
                font-size: 16px;
              }

              .student-name {
                font-size: 22px;
              }

              .certificate-description {
                font-size: 16px;
              }

              .certificate-footer {
                gap: 20px;
              }

              .certificate-stamp {
                width: 80px;
                height: 80px;
                bottom: 30px;
                left: 20px;
              }

              .certificate-date {
                bottom: 15px;
                right: 20px;
                font-size: 12px;
              }
            }

            @media print {
              body {
                margin: 0;
                padding: 0;
                background: none;
              }

              .certificate-container {
                box-shadow: none;
                max-width: 100%;
              }

              .certificate {
                page-break-inside: avoid;
              }
            }

            /* أزرار التحكم للأجهزة المحمولة */
            .mobile-controls {
              position: fixed;
              bottom: 20px;
              left: 0;
              right: 0;
              display: flex;
              justify-content: center;
              gap: 10px;
              z-index: 1000;
              padding: 10px;
            }

            .mobile-controls button {
              background-color: #9333ea;
              color: white;
              border: none;
              border-radius: 4px;
              padding: 10px 15px;
              font-family: 'Cairo', sans-serif;
              font-size: 16px;
              cursor: pointer;
              box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            }

            .mobile-controls button:hover {
              background-color: #7928ca;
            }

            @media print {
              .mobile-controls {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="certificate-container">
            <div class="certificate">
              <div class="certificate-bg"></div>

              <!-- رأس الشهادة -->
              <div class="certificate-header">
                <div class="certificate-icon">👍</div>
                <h1 class="certificate-title">شهادة تقدير</h1>
                <p class="certificate-subtitle">${certificateData.title}</p>
              </div>

              <!-- محتوى الشهادة -->
              <div class="certificate-content">
                <p class="certificate-intro">تقديراً للجهود المتميزة، تمنح هذه الشهادة إلى:</p>
                <h2 class="student-name">${certificateData.student?.name || 'الطالب المتميز'}</h2>
                ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
                <p class="certificate-description">${certificateData.description}</p>
              </div>

              <!-- توقيع الشهادة -->
              <div class="certificate-footer">
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المدير</p>
                </div>
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المعلم</p>
                </div>
              </div>

              <!-- ختم المدرسة -->
              <div class="certificate-stamp">ختم المدرسة</div>

              <!-- تاريخ الإصدار -->
              ${certificateData.issueDate ? `
              <div class="certificate-date">
                تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
              </div>
              ` : ''}
            </div>
          </div>

          <!-- أزرار التحكم للأجهزة المحمولة -->
          <div class="mobile-controls">
            <button onclick="window.print()">طباعة</button>
            <button onclick="window.close()">إغلاق</button>
          </div>

          <script>
            // التحقق مما إذا كان الجهاز محمولاً
            const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

            // إذا لم يكن جهازاً محمولاً، قم بالطباعة تلقائياً
            if (!isMobile) {
              window.onload = () => {
                window.print();
                window.onafterprint = () => window.close();
              };
            }
          </script>
        </body>
        </html>
      `;

      // كتابة المحتوى إلى النافذة الجديدة
      printWindow.document.open();
      printWindow.document.write(certificateHTML);
      printWindow.document.close();
    } catch (error) {
      console.error('Error printing certificate:', error);
      alert('حدث خطأ أثناء طباعة الشهادة');
    }
  };

  const handleDownloadImage = async () => {
    try {
      // إنشاء عنصر div مؤقت لاحتواء الشهادة
      const tempDiv = document.createElement('div');
      document.body.appendChild(tempDiv);

      // إنشاء محتوى HTML للشهادة (نفس المحتوى المستخدم في الطباعة)
      const certificateHTML = `
        <div style="width: 800px; height: 1130px; position: relative;">
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 0;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: white;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 4px solid #e9d5ff;
              margin: 20px;
              background-color: white;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #f9f0ff 0%, #ffffff 50%, #fcf4ff 100%);
              z-index: -1;
            }

            .certificate-header {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-icon {
              width: 80px;
              height: 80px;
              background-color: #9333ea;
              border-radius: 50%;
              margin: 0 auto 15px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 40px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: #9333ea;
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: #9333ea;
              margin-bottom: 10px;
              border-bottom: 2px solid #e9d5ff;
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 40px;
              margin-top: auto;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #e9d5ff;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 40px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #e9d5ff;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #9333ea;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 20px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }
          </style>

          <div class="certificate">
            <div class="certificate-bg"></div>

            <!-- رأس الشهادة -->
            <div class="certificate-header">
              <div class="certificate-icon">👍</div>
              <h1 class="certificate-title">شهادة تقدير</h1>
              <p class="certificate-subtitle">${certificateData.title}</p>
            </div>

            <!-- محتوى الشهادة -->
            <div class="certificate-content">
              <p class="certificate-intro">تقديراً للجهود المتميزة، تمنح هذه الشهادة إلى:</p>
              <h2 class="student-name">${certificateData.student?.name || 'الطالب المتميز'}</h2>
              ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
              <p class="certificate-description">${certificateData.description}</p>
            </div>

            <!-- توقيع الشهادة -->
            <div class="certificate-footer">
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المدير</p>
              </div>
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المعلم</p>
              </div>
            </div>

            <!-- ختم المدرسة -->
            <div class="certificate-stamp">ختم المدرسة</div>

            <!-- تاريخ الإصدار -->
            ${certificateData.issueDate ? `
            <div class="certificate-date">
              تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
            </div>
            ` : ''}
          </div>
        </div>
      `;

      // إضافة المحتوى إلى العنصر المؤقت
      tempDiv.innerHTML = certificateHTML;

      // انتظار تحميل الخطوط والصور
      await new Promise(resolve => setTimeout(resolve, 500));

      // تحويل العنصر إلى صورة باستخدام html2canvas
      const canvas = await html2canvas(tempDiv.firstElementChild as HTMLElement, {
        scale: 3, // زيادة الدقة للحصول على صورة أوضح
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
        allowTaint: true,
        imageTimeout: 5000
      });

      // إزالة العنصر المؤقت
      document.body.removeChild(tempDiv);

      // تحويل الصورة إلى PNG بجودة عالية
      const image = canvas.toDataURL('image/png', 1.0);

      // إنشاء رابط تنزيل وتنفيذه
      const link = document.createElement('a');
      link.href = image;
      link.download = `شهادة_تقدير_${certificateData.student?.name || 'تقدير'}_${new Date().toLocaleDateString('fr-FR')}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error generating certificate image:', error);
      alert('حدث خطأ أثناء تحميل الشهادة كصورة');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <div className="space-y-4">
      <div ref={certificateRef} className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* الشهادة */}
        <div className="relative" data-certificate="appreciation">
          {/* خلفية الشهادة */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-white to-pink-50" />

          {/* زخارف الشهادة */}
          <div className="absolute top-0 left-0 w-full h-16 bg-purple-100" />
          <div className="absolute bottom-0 left-0 w-full h-16 bg-purple-100" />
          <div className="absolute top-0 left-0 w-16 h-full bg-purple-100" />
          <div className="absolute top-0 right-0 w-16 h-full bg-purple-100" />

          {/* إطار الشهادة */}
          <div className="relative z-10 p-8 border-4 border-purple-200 m-8 min-h-[600px]">
            <div className="flex flex-col items-center justify-center h-full text-center">
              {/* رأس الشهادة */}
              <div className="mb-8">
                <div className="w-24 h-24 mx-auto mb-4 bg-purple-600 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                  </svg>
                </div>
                <h1 className="text-4xl font-bold text-purple-600 mb-2" data-arabic-text="true">شهادة تقدير</h1>
                <p className="text-xl text-gray-600" data-arabic-text="true">{certificateData.title}</p>
              </div>

              {/* محتوى الشهادة */}
              <div className="mb-8 max-w-2xl">
                <p className="text-lg mb-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">تقديراً للجهود المتميزة، تمنح هذه الشهادة إلى:</p>
                <h2 className="text-3xl font-bold text-purple-600 mb-4 border-b-2 border-purple-200 pb-2 inline-block" data-arabic-text="true">
                  {certificateData.student?.name || 'الطالب المتميز'}
                </h2>
                {certificateData.student?.classe && (
                  <p className="text-xl text-gray-600 mb-6" data-arabic-text="true">
                    {certificateData.student.classe.name}
                  </p>
                )}
                <p className="text-lg my-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">{certificateData.description}</p>
              </div>

              {/* توقيع الشهادة */}
              <div className="mt-auto grid grid-cols-2 gap-12 w-full">
                <div className="text-center">
                  <div className="h-16 border-b border-purple-200 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المدير</p>
                </div>
                <div className="text-center">
                  <div className="h-16 border-b border-purple-200 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المعلم</p>
                </div>
              </div>

              {/* تاريخ الإصدار */}
              {certificateData.issueDate && (
                <div className="mt-8 text-sm text-gray-500" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">
                  تم إصدار هذه الشهادة بتاريخ: {formatDate(certificateData.issueDate)}
                </div>
              )}

              {/* ختم المدرسة */}
              <div className="absolute bottom-8 left-8 w-24 h-24 border-2 border-dashed border-purple-300 rounded-full flex items-center justify-center text-purple-400" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">
                ختم المدرسة
              </div>
            </div>
          </div>
        </div>
      </div>

      {showControls && (
        <Card className="p-4 flex justify-center space-x-4 space-x-reverse">
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            <span>طباعة</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={handleDownloadImage}
          >
            <Download className="h-4 w-4" />
            <span>تحميل</span>
          </Button>
        </Card>
      )}
    </div>
  );
}

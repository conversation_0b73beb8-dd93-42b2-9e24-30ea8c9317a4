import { NextRequest, NextResponse } from 'next/server';
import  prisma  from '@/lib/prisma';

// GET /api/student-albums - الحصول على ألبومات الصور
export async function GET() {
  try {
    // الحصول على الألبومات مع عدد الصور في كل ألبوم
    const albums = await prisma.studentAlbum.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
 
    // إضافة عدد الصور لكل ألبوم
    const albumsWithImageCount = await Promise.all(
      albums.map(async (album) => {
        const imageCount = await prisma.studentImage.count({
          where: {
            albumId: album.id
          }
        });

        return {
          ...album,
          imageCount
        };
      })
    );

    return NextResponse.json({
      success: true,
      data: albumsWithImageCount,
      message: 'تم جلب ألبومات الصور بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student albums:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب ألبومات الصور'
    }, { status: 500 });
  }
}

// POST /api/student-albums - إنشاء ألبوم جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description } = body;

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json({
        success: false,
        error: 'اسم الألبوم مطلوب'
      }, { status: 400 });
    }

    // إنشاء الألبوم الجديد
    const album = await prisma.studentAlbum.create({
      data: {
        name,
        description
      }
    });

    return NextResponse.json({
      success: true,
      data: album,
      message: 'تم إنشاء الألبوم بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating student album:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء إنشاء الألبوم'
    }, { status: 500 });
  }
}

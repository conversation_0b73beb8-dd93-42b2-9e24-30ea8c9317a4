import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        {
          success: false,
          error: 'معرف غير صالح'
        },
        { status: 400 }
      );
    }

    // التحقق من وجود العلاقة
    const classSubject = await prisma.classSubject.findUnique({
      where: { id }
    });

    if (!classSubject) {
      return NextResponse.json(
        {
          success: false,
          error: 'لم يتم العثور على علاقة القسم بالمادة'
        },
        { status: 404 }
      );
    }

    // حذف العلاقة
    await prisma.classSubject.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف علاقة القسم بالمادة بنجاح'
    });
  } catch (error) {
    console.error('Error deleting class subject:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'حدث خطأ أثناء حذف علاقة القسم بالمادة',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

# API الفواتير المحسن

## 📋 الوصف
API endpoint محسن لإدارة الفواتير الفردية والجماعية مع دعم شامل للتحقق من البيانات والعمليات المتقدمة.

## 🎯 الهدف
- دعم الفواتير الفردية والجماعية
- التحقق الشامل من صحة البيانات
- تحسين منطق العمل والأمان
- رسائل خطأ واضحة ومفيدة

## 🔗 المسارات

### POST /api/invoices
إنشاء فاتورة جديدة (فردية أو جماعية)

#### المعاملات المطلوبة:
- `type`: نوع الفاتورة ('INDIVIDUAL' أو 'FAMILY')
- `amount`: مبلغ الفاتورة
- `dueDate`: تاريخ الاستحقاق
- `month`: الشهر (1-12)
- `year`: السنة

#### للفواتير الفردية:
- `studentId`: معرف التلميذ (مطلوب)
- `parentId`: يجب أن يكون null

#### للفواتير الجماعية:
- `parentId`: معرف الولي (مطلوب)
- `studentId`: يجب أن يكون null

#### المعاملات الاختيارية:
- `issueDate`: تاريخ الإصدار (افتراضي: اليوم)
- `description`: وصف الفاتورة
- `status`: حالة الفاتورة (افتراضي: 'UNPAID')

#### مثال على الطلب (فاتورة فردية):
```json
{
  "type": "INDIVIDUAL",
  "studentId": 123,
  "amount": 5000,
  "dueDate": "2025-07-15",
  "month": 7,
  "year": 2025,
  "description": "رسوم شهر يوليو"
}
```

#### مثال على الطلب (فاتورة جماعية):
```json
{
  "type": "FAMILY",
  "parentId": 456,
  "amount": 15000,
  "dueDate": "2025-07-15",
  "month": 7,
  "year": 2025,
  "description": "رسوم جماعية لشهر يوليو"
}
```

#### الاستجابة الناجحة:
```json
{
  "success": true,
  "invoice": {
    "id": 789,
    "type": "INDIVIDUAL",
    "studentId": 123,
    "amount": 5000,
    "dueDate": "2025-07-15T00:00:00.000Z",
    "status": "UNPAID",
    "student": {
      "id": 123,
      "name": "أحمد محمد",
      "guardian": {
        "name": "محمد أحمد"
      }
    }
  },
  "message": "تم إنشاء الفاتورة الفردية بنجاح"
}
```

### GET /api/invoices
جلب جميع الفواتير مع إمكانية الفلترة

#### المعاملات الاختيارية:
- `search`: البحث في الوصف أو اسم التلميذ/الولي
- `status`: فلترة حسب الحالة
- `type`: فلترة حسب النوع
- `month`: فلترة حسب الشهر
- `year`: فلترة حسب السنة
- `page`: رقم الصفحة
- `limit`: عدد النتائج في الصفحة

### PATCH /api/invoices
تحديث فاتورة موجودة

#### المعاملات:
- `id`: معرف الفاتورة
- `status`: الحالة الجديدة (اختياري)
- `amount`: المبلغ الجديد (اختياري)
- `dueDate`: تاريخ الاستحقاق الجديد (اختياري)

### DELETE /api/invoices
حذف فاتورة

#### المعاملات:
- `id`: معرف الفاتورة

## 🔧 التحسينات المطبقة

### 1. التحقق الشامل من البيانات
```typescript
// استخدام دوال التحقق المتقدمة
const validationErrors = validateInvoiceData({
  studentId,
  parentId,
  amount,
  dueDate,
  issueDate,
  month,
  year,
  description,
  type
});

if (validationErrors.length > 0) {
  return NextResponse.json({
    success: false,
    error: 'بيانات غير صحيحة',
    details: validationErrors
  }, { status: 400 });
}
```

### 2. تنظيف المدخلات
```typescript
// تنظيف جميع المدخلات من الأحرف الضارة
studentId = sanitizeInput(studentId);
parentId = sanitizeInput(parentId);
amount = sanitizeInput(amount);
dueDate = sanitizeInput(dueDate);
// ... باقي الحقول
```

### 3. التحقق من وجود الكيانات
```typescript
// للفواتير الفردية
if (type === 'INDIVIDUAL') {
  const student = await prisma.student.findUnique({
    where: { id: studentId },
    include: { guardian: true, classe: true }
  });
  
  if (!student) {
    return NextResponse.json(
      { error: 'التلميذ غير موجود' },
      { status: 404 }
    );
  }
}

// للفواتير الجماعية
else if (type === 'FAMILY') {
  const parent = await prisma.parent.findUnique({
    where: { id: parentId },
    include: { students: true }
  });
  
  if (!parent) {
    return NextResponse.json(
      { error: 'الولي غير موجود' },
      { status: 404 }
    );
  }
  
  if (parent.students.length === 0) {
    return NextResponse.json(
      { error: 'الولي ليس لديه أبناء مسجلون' },
      { status: 400 }
    );
  }
}
```

### 4. تسجيل الأنشطة المحسن
```typescript
// تسجيل مفصل للأنشطة
const entityName = type === 'INDIVIDUAL' 
  ? `التلميذ ${invoice.student?.name}` 
  : `الولي ${invoice.parent?.name}`;

await ActivityLogger.log(
  adminUser.id,
  ActivityType.INVOICE,
  `إنشاء فاتورة ${type === 'FAMILY' ? 'جماعية' : 'فردية'} بقيمة ${amount} دج لـ ${entityName}`
);
```

### 5. رسائل نجاح واضحة
```typescript
return NextResponse.json({
  success: true,
  invoice,
  message: `تم إنشاء الفاتورة ${type === 'FAMILY' ? 'الجماعية' : 'الفردية'} بنجاح`
});
```

## 🔒 قواعد التحقق

### قواعد نوع الفاتورة
1. **INDIVIDUAL**: يجب تحديد `studentId` فقط
2. **FAMILY**: يجب تحديد `parentId` فقط
3. لا يمكن تحديد كلاهما أو عدم تحديد أي منهما

### قواعد المبلغ
- يجب أن يكون أكبر من صفر
- يجب أن يكون أقل من 1,000,000 دج
- يجب أن يكون رقماً صحيحاً

### قواعد التواريخ
- `dueDate` يجب أن يكون بعد `issueDate`
- التواريخ يجب أن تكون صحيحة وبعد 2020-01-01

### قواعد الشهر والسنة
- الشهر: 1-12
- السنة: 2020-2030

## 📊 أمثلة الاستجابات

### نجاح إنشاء فاتورة فردية
```json
{
  "success": true,
  "invoice": {
    "id": 123,
    "type": "INDIVIDUAL",
    "studentId": 456,
    "amount": 5000,
    "dueDate": "2025-07-15T00:00:00.000Z",
    "issueDate": "2025-06-24T00:00:00.000Z",
    "month": 7,
    "year": 2025,
    "description": "رسوم شهرية",
    "status": "UNPAID",
    "student": {
      "id": 456,
      "name": "أحمد محمد",
      "guardian": {
        "name": "محمد أحمد"
      }
    }
  },
  "message": "تم إنشاء الفاتورة الفردية بنجاح"
}
```

### خطأ في البيانات
```json
{
  "success": false,
  "error": "بيانات غير صحيحة",
  "details": [
    {
      "field": "studentId",
      "message": "معرف التلميذ مطلوب للفواتير الفردية"
    },
    {
      "field": "amount",
      "message": "مبلغ الفاتورة يجب أن يكون أكبر من صفر"
    }
  ]
}
```

### خطأ عدم وجود الكيان
```json
{
  "error": "التلميذ غير موجود"
}
```

## 🎯 الفوائد المحققة

### 1. دقة أعلى
- تحقق شامل من جميع البيانات
- منع إنشاء فواتير خاطئة
- ضمان سلامة العلاقات

### 2. أمان محسن
- تنظيف المدخلات من الأحرف الضارة
- التحقق من وجود الكيانات
- رسائل خطأ آمنة

### 3. تجربة مستخدم أفضل
- رسائل خطأ واضحة ومفيدة
- رسائل نجاح مفصلة
- دعم كامل للفواتير الجماعية

### 4. صيانة أسهل
- كود منظم ومقسم
- استخدام دوال مساعدة موحدة
- تسجيل شامل للأنشطة

---

**تاريخ التحديث:** 2025-06-24  
**المطور:** Augment Agent  
**الإصدار:** 2.0  
**الحالة:** محسن ومختبر

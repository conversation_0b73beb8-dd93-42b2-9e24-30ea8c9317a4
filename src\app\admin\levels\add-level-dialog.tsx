'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface AddLevelDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
}

export default function AddLevelDialog({ isOpen, onCloseAction, onSuccessAction }: AddLevelDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    order: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddLevel = async () => {
    if (!formData.name.trim()) {
      toast.error('الرجاء إدخال اسم المستوى');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/levels', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || null,
          order: formData.order ? parseInt(formData.order) : undefined
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add level');
      }

      toast.success('تمت إضافة المستوى بنجاح');
      setFormData({ name: '', description: '', order: '' });
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error adding level:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة المستوى');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleAddLevel}
      disabled={isLoading || !formData.name.trim()}
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري الإضافة...' : 'إضافة'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إضافة مستوى جديد"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <Label>اسم المستوى</Label>
          <Input
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="أدخل اسم المستوى"
          />
        </div>
        
        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف المستوى"
            rows={3}
          />
        </div>
        
        <div className="space-y-2">
          <Label>الترتيب (اختياري)</Label>
          <Input
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="أدخل ترتيب المستوى"
            min={1}
          />
          <p className="text-xs text-gray-500">إذا تركت هذا الحقل فارغًا، سيتم وضع المستوى في نهاية القائمة</p>
        </div>
      </div>
    </AnimatedDialog>
  );
}

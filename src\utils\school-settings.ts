import { useState, useEffect } from 'react';

/**
 * مساعد لجلب وإدارة إعدادات المدرسة
 */

export interface SchoolSettings {
  siteName: string;
  siteDescription: string;
  logoUrl: string;
  contactInfo: {
    email: string;
    phone: string;
    address: string;
  };
  primaryColor?: string;
  secondaryColor?: string;
}

/**
 * جلب إعدادات المدرسة من API
 */
export const fetchSchoolSettings = async (): Promise<SchoolSettings | null> => {
  try {
    const response = await fetch('/api/settings');
    if (response.ok) {
      const data = await response.json();
      return data.settings;
    }
    return null;
  } catch (error) {
    console.error('Error fetching school settings:', error);
    return null;
  }
};

/**
 * الحصول على إعدادات المدرسة الافتراضية
 */
export const getDefaultSchoolSettings = (): SchoolSettings => {
  return {
    siteName: 'نظام برهان للقرآن الكريم',
    siteDescription: 'منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد',
    logoUrl: '/logo.svg',
    contactInfo: {
      email: '<EMAIL>',
      phone: '+213 123 456 789',
      address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر'
    },
    primaryColor: '#169b88',
    secondaryColor: '#1ab19c'
  };
};

/**
 * دمج إعدادات المدرسة مع الإعدادات الافتراضية
 */
export const mergeWithDefaultSettings = (settings: Partial<SchoolSettings> | null): SchoolSettings => {
  const defaultSettings = getDefaultSchoolSettings();

  if (!settings) {
    return defaultSettings;
  }

  return {
    ...defaultSettings,
    ...settings,
    contactInfo: {
      ...defaultSettings.contactInfo,
      ...(settings.contactInfo || {})
    }
  };
};

/**
 * تنسيق معلومات المدرسة للاستخدام في التقارير
 */
export const formatSchoolInfoForReports = (settings: SchoolSettings | null) => {
  if (!settings) {
    const defaultSettings = getDefaultSchoolSettings();
    return {
      name: defaultSettings.siteName,
      address: defaultSettings.contactInfo.address,
      phone: defaultSettings.contactInfo.phone,
      email: defaultSettings.contactInfo.email
    };
  }

  return {
    name: settings.siteName || 'مدرسة القرآن الكريم',
    address: settings.contactInfo?.address || '',
    phone: settings.contactInfo?.phone || '',
    email: settings.contactInfo?.email || ''
  };
};

/**
 * Hook مخصص لجلب إعدادات المدرسة (للاستخدام في React Components)
 */
export const useSchoolSettings = () => {
  const [settings, setSettings] = useState<SchoolSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const fetchedSettings = await fetchSchoolSettings();
      const mergedSettings = mergeWithDefaultSettings(fetchedSettings);
      setSettings(mergedSettings);
      setError(null);
    } catch (err) {
      console.error('Error loading school settings:', err);
      setError('فشل في تحميل إعدادات المدرسة');
      setSettings(getDefaultSchoolSettings());
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  return { settings, loading, error, refetch: loadSettings };
};

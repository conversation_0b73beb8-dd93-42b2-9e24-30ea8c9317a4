import { NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { hash } from 'bcrypt'
import { UserRole, Prisma } from '@prisma/client'
import { sanitizeInput, validateAmount } from '@/utils/payment-utils'

export async function GET() {
  try {
    // جلب جميع الأولياء مع بيانات أبنائهم والفواتير والمدفوعات
    const parents = await prisma.parent.findMany({
      include: {
        students: {
          include: {
            classe: true,
            invoices: {
              include: {
                payments: {
                  where: {
                    status: 'PAID'
                  },
                  orderBy: {
                    date: 'desc'
                  }
                }
              }
            },
            payments: {
              where: {
                status: 'PAID'
              },
              orderBy: {
                date: 'desc'
              },
              take: 1 // آخر دفعة فقط
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    // جلب بيانات المستخدمين المرتبطين بالأولياء وحساب الديون
    const parentsWithUsersAndDebts = await Promise.all(
      parents.map(async (parent) => {
        // البحث عن مستخدم بنفس اسم ولي الأمر ودور PARENT
        const user = await prisma.user.findFirst({
          where: {
            profile: {
              name: parent.name
            },
            role: UserRole.PARENT
          },
          select: {
            id: true,
            username: true,
            profile: true
          }
        })

        // حساب إجمالي الديون والمدفوعات لجميع أبناء الولي + الفواتير الجماعية
        let totalRequired = 0;
        let totalPaid = 0;
        let dueInvoices = 0;
        let lastPaymentDate: Date | null = null;

        // 1. حساب فواتير التلاميذ الفردية
        for (const student of parent.students) {
          // حساب إجمالي الفواتير للطالب (استبعاد الملغاة والجماعية)
          for (const invoice of student.invoices) {
            // تجاهل الفواتير الملغاة والجماعية (الجماعية تُحسب منفصلة)
            if (invoice.status === 'CANCELLED' || invoice.type === 'FAMILY') {
              continue;
            }

            totalRequired += invoice.amount;

            // جلب المدفوعات المرتبطة بالفاتورة الفردية (نفس منطق API المدفوعات)
            const individualInvoicePayments = await prisma.payment.findMany({
              where: {
                invoiceId: invoice.id,
                status: 'PAID'
              }
            });

            const invoicePaid = individualInvoicePayments.reduce((sum, payment) => sum + payment.amount, 0);
            totalPaid += invoicePaid;

            // إذا كانت الفاتورة غير مدفوعة بالكامل وليست ملغاة
            if (invoicePaid < invoice.amount && invoice.status !== 'CANCELLED') {
              dueInvoices++;
            }

            console.log(`💰 فاتورة فردية ${invoice.id} للتلميذ ${student.name} (API الأولياء):`, {
              amount: invoice.amount,
              totalPaid: invoicePaid,
              paymentsCount: individualInvoicePayments.length,
              remaining: invoice.amount - invoicePaid
            });
          }

          // تحديث آخر تاريخ دفعة (من المدفوعات المؤكدة فقط)
          const confirmedPayments = student.payments.filter(payment => payment.status === 'PAID');
          if (confirmedPayments.length > 0) {
            const studentLastPayment = new Date(confirmedPayments[0].date);
            if (!lastPaymentDate || studentLastPayment > lastPaymentDate) {
              lastPaymentDate = studentLastPayment;
            }
          }
        }

        // 2. حساب الفواتير الجماعية للولي
        const familyInvoices = await prisma.invoice.findMany({
          where: {
            parentId: parent.id,
            type: 'FAMILY',
            status: { not: 'CANCELLED' }
          },
          include: {
            payments: {
              where: { status: 'PAID' }
            }
          }
        });

        for (const invoice of familyInvoices) {
          totalRequired += invoice.amount;

          // جلب المدفوعات المرتبطة بالفاتورة الجماعية (نفس منطق API المدفوعات)
          const familyInvoicePayments = await prisma.payment.findMany({
            where: {
              invoiceId: invoice.id,
              status: 'PAID'
            }
          });

          const invoicePaid = familyInvoicePayments.reduce((sum, payment) => sum + payment.amount, 0);
          totalPaid += invoicePaid;

          // إذا كانت الفاتورة الجماعية غير مدفوعة بالكامل
          if (invoicePaid < invoice.amount) {
            dueInvoices++;
          }

          // تحديث آخر تاريخ دفعة من الفواتير الجماعية
          if (familyInvoicePayments.length > 0) {
            const familyLastPayment = new Date(Math.max(...familyInvoicePayments.map(p => new Date(p.date).getTime())));
            if (!lastPaymentDate || familyLastPayment > lastPaymentDate) {
              lastPaymentDate = familyLastPayment;
            }
          }

          console.log(`💰 فاتورة جماعية ${invoice.id} للولي ${parent.name} (API الأولياء):`, {
            amount: invoice.amount,
            totalPaid: invoicePaid,
            paymentsCount: familyInvoicePayments.length,
            remaining: invoice.amount - invoicePaid
          });
        }

        // حساب المبلغ المتبقي (الدين الفعلي)
        const remainingDebt = Math.max(0, totalRequired - totalPaid);

        // تسجيل مفصل للحسابات
        console.log(`💰 حسابات الولي ${parent.name}:`, {
          totalRequired,
          totalPaid,
          remainingDebt,
          dueInvoices,
          familyInvoicesCount: familyInvoices.length,
          studentsCount: parent.students.length
        });

        // إضافة بيانات المستخدم وبيانات الديون إلى بيانات ولي الأمر
        return {
          ...parent,
          user: user || null,
          totalDebt: remainingDebt, // المبلغ المتبقي (الدين الفعلي)
          totalRequired, // إجمالي المطلوب
          totalPaid, // إجمالي المدفوع
          dueInvoices,
          lastPaymentDate
        }
      })
    )

    return NextResponse.json(parentsWithUsersAndDebts)
  } catch (error) {
    console.error('Error fetching parents:', error)
    return NextResponse.json({ error: 'Failed to fetch parents' }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    let { name, phone, email, address, amountPerStudent, username, password } = body

    // تنظيف المدخلات
    name = sanitizeInput(name)
    phone = sanitizeInput(phone)
    email = sanitizeInput(email)
    address = sanitizeInput(address)
    username = sanitizeInput(username)
    password = sanitizeInput(password)

    // التحقق من وجود الحقول المطلوبة
    if (!name || !phone || !username || !password) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من صحة المبلغ لكل تلميذ إذا تم تحديده
    if (amountPerStudent && !validateAmount(parseFloat(amountPerStudent))) {
      return NextResponse.json(
        { error: 'المبلغ لكل تلميذ غير صحيح' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUser = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'اسم المستخدم موجود مسبقاً' },
        { status: 400 }
      )
    }

    // تشفير كلمة المرور
    const hashedPassword = await hash(password, 10)

    // إنشاء المستخدم وولي الأمر في نفس الوقت
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء المستخدم
      const user = await tx.user.create({
        data: {
          username,
          password: hashedPassword,
          email,
          role: UserRole.PARENT,
          profile: {
            create: {
              name,
              phone
            }
          }
        }
      })

      // إنشاء ولي الأمر
      const parent = await tx.parent.create({
        data: {
          name,
          phone,
          email,
          address,
          amountPerStudent: amountPerStudent ? parseFloat(amountPerStudent) : null
        }
      })

      return { user, parent }
    })

    return NextResponse.json(result.parent)
  } catch (error) {
    console.error('Error creating parent:', error)
    return NextResponse.json({ error: 'Failed to create parent' }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json()
    let { id, name, phone, email, address, amountPerStudent, username, password } = body

    // تنظيف المدخلات
    name = sanitizeInput(name)
    phone = sanitizeInput(phone)
    email = sanitizeInput(email)
    address = sanitizeInput(address)
    username = sanitizeInput(username)
    password = sanitizeInput(password)

    // التحقق من صحة المبلغ لكل تلميذ إذا تم تحديده
    if (amountPerStudent && !validateAmount(parseFloat(amountPerStudent))) {
      return NextResponse.json(
        { error: 'المبلغ لكل تلميذ غير صحيح' },
        { status: 400 }
      )
    }

    // تحديث بيانات ولي الأمر
    const parent = await prisma.parent.update({
      where: { id: Number(id) },
      data: {
        name,
        phone,
        email,
        address,
        amountPerStudent: amountPerStudent ? parseFloat(amountPerStudent) : null,
        updatedAt: new Date()
      }
    })

    // إذا تم توفير اسم المستخدم وكلمة المرور، قم بتحديث أو إنشاء حساب المستخدم
    if (username) {
      // البحث عن مستخدم بنفس اسم ولي الأمر
      const existingUser = await prisma.user.findFirst({
        where: {
          profile: {
            name: parent.name
          },
          role: UserRole.PARENT
        }
      })

      if (existingUser) {
        // إعداد بيانات التحديث
        const updateData: Prisma.UserUpdateInput = {
          username,
          email,
          profile: {
            update: {
              name,
              phone
            }
          }
        };

        // إضافة كلمة المرور المشفرة إذا تم توفيرها
        if (password) {
          updateData.password = await hash(password, 10);
        }

        // تحديث بيانات المستخدم الموجود
        await prisma.user.update({
          where: { id: existingUser.id },
          data: updateData
        })
      } else if (password) {
        // إنشاء مستخدم جديد (فقط إذا تم توفير كلمة المرور)
        const hashedPassword = await hash(password, 10);

        await prisma.user.create({
          data: {
            username,
            password: hashedPassword,
            email,
            role: UserRole.PARENT,
            profile: {
              create: {
                name,
                phone
              }
            }
          }
        })
      }
    }

    return NextResponse.json(parent)
  } catch (error) {
    console.error('Error updating parent:', error)
    return NextResponse.json({ error: 'Failed to update parent' }, { status: 500 })
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'Parent ID is required' }, { status: 400 })
    }

    await prisma.parent.delete({
      where: { id: Number(id) }
    })

    return NextResponse.json({ message: 'Parent deleted successfully' })
  } catch {
    return NextResponse.json({ error: 'Failed to delete parent' }, { status: 500 })
  }
}
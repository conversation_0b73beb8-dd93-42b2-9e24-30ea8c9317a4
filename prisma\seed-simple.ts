#!/usr/bin/env ts-node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 بدء إنشاء بيانات تجريبية بسيطة للمدرسة القرآنية...');
  console.log('=' .repeat(60));

  try {
    // التحقق من وجود البيانات الأساسية
    const studentsCount = await prisma.student.count();
    const classesCount = await prisma.classe.count();
    const examsCount = await prisma.exam.count();
    
    console.log(`📊 الوضع الحالي:`);
    console.log(`   👥 الطلاب: ${studentsCount}`);
    console.log(`   🏫 الفصول: ${classesCount}`);
    console.log(`   📝 الامتحانات: ${examsCount}`);
    console.log('');

    if (studentsCount === 0) {
      console.log('⚠️  لا يوجد طلاب في النظام!');
      console.log('💡 يرجى إضافة طلاب أولاً من لوحة التحكم.');
      return;
    }

    if (examsCount === 0) {
      console.log('⚠️  لا يوجد امتحانات في النظام!');
      console.log('💡 يرجى إضافة امتحانات أولاً من لوحة التحكم.');
      return;
    }

    // جلب البيانات الموجودة
    const students = await prisma.student.findMany({
      include: { classe: true }
    });

    const exams = await prisma.exam.findMany({
      include: { subject: true, examType: true }
    });

    console.log('🎯 إنشاء نقاط تجريبية للامتحانات الموجودة...');

    let pointsCreated = 0;

    // إنشاء نقاط للطلاب في الامتحانات الموجودة
    for (const exam of exams) {
      for (const student of students) {
        // التحقق من عدم وجود نقاط مسبقة
        const existingPoint = await prisma.exam_points.findFirst({
          where: {
            examId: exam.id,
            studentId: student.id
          }
        });

        if (!existingPoint) {
          // حساب درجة تجريبية
          const grade = calculateRandomGrade(student, exam);
          
          try {
            await prisma.exam_points.create({
              data: {
                examId: exam.id,
                studentId: student.id,
                grade: grade,
                status: grade >= 10 ? 'PASSED' : 'FAILED',
                note: generateNote(grade),
                feedback: generateFeedback(grade),
                classSubjectId: 1 // قيمة افتراضية
              }
            });
            pointsCreated++;
          } catch (error) {
            console.log(`⚠️  تخطي إنشاء نقطة للطالب ${student.name} في امتحان ${exam.description}`);
          }
        }
      }
    }

    console.log(`✅ تم إنشاء ${pointsCreated} نقطة امتحان جديدة`);

    // إحصائيات نهائية
    const finalStats = {
      students: await prisma.student.count(),
      classes: await prisma.classe.count(),
      exams: await prisma.exam.count(),
      examPoints: await prisma.exam_points.count()
    };

    console.log('');
    console.log('📊 الإحصائيات النهائية:');
    console.log(`   👥 الطلاب: ${finalStats.students}`);
    console.log(`   🏫 الفصول: ${finalStats.classes}`);
    console.log(`   📝 الامتحانات: ${finalStats.exams}`);
    console.log(`   📈 نقاط الامتحانات: ${finalStats.examPoints}`);

    console.log('');
    console.log('🔍 لعرض كشف درجات طالب:');
    console.log('   http://localhost:3000/admin/evaluation/student-report');

    console.log('');
    console.log('✅ تم إنشاء البيانات التجريبية بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// دالة لحساب درجة عشوائية واقعية
function calculateRandomGrade(student: any, exam: any): number {
  // محاكاة مستويات مختلفة للطلاب
  const studentLevel = (student.id % 4) + 1;
  
  let baseGrade: number;
  
  switch (studentLevel) {
    case 1: // طالب ممتاز
      baseGrade = 16 + Math.random() * 4;
      break;
    case 2: // طالب جيد جداً
      baseGrade = 13 + Math.random() * 4;
      break;
    case 3: // طالب جيد
      baseGrade = 10 + Math.random() * 4;
      break;
    case 4: // طالب يحتاج تحسين
      baseGrade = 6 + Math.random() * 6;
      break;
    default:
      baseGrade = 10 + Math.random() * 8;
  }

  // تعديل حسب نوع الامتحان
  if (exam.evaluationType === 'QURAN_MEMORIZATION') {
    baseGrade += 1; // الطلاب عادة أفضل في الحفظ
  } else if (exam.evaluationType === 'QURAN_RECITATION') {
    baseGrade += 0.5;
  }

  // التأكد من أن الدرجة في النطاق الصحيح
  return Math.min(Math.max(Math.round(baseGrade * 10) / 10, 0), 20);
}

// دالة لإنشاء ملاحظة
function generateNote(grade: number): string {
  if (grade >= 18) {
    return 'أداء ممتاز، استمر على هذا المستوى المتميز.';
  } else if (grade >= 15) {
    return 'أداء جيد جداً، يمكن تحسينه أكثر.';
  } else if (grade >= 12) {
    return 'أداء جيد، يحتاج إلى مزيد من المراجعة.';
  } else if (grade >= 10) {
    return 'أداء مقبول، يحتاج إلى تركيز أكبر.';
  } else {
    return 'أداء ضعيف، يحتاج إلى مراجعة شاملة ومتابعة خاصة.';
  }
}

// دالة لإنشاء تغذية راجعة
function generateFeedback(grade: number): string {
  const feedbacks = {
    excellent: [
      'بارك الله فيك، أداء رائع!',
      'ممتاز، واصل التفوق!',
      'أحسنت، نموذج يُحتذى به!'
    ],
    good: [
      'جيد، يمكن تحسينه أكثر',
      'أداء طيب، استمر في المراجعة',
      'بداية جيدة، واصل الجهد'
    ],
    needsImprovement: [
      'يحتاج إلى مزيد من المراجعة',
      'راجع الدروس مرة أخرى',
      'اطلب المساعدة من المعلم'
    ]
  };

  if (grade >= 15) {
    return feedbacks.excellent[Math.floor(Math.random() * feedbacks.excellent.length)];
  } else if (grade >= 10) {
    return feedbacks.good[Math.floor(Math.random() * feedbacks.good.length)];
  } else {
    return feedbacks.needsImprovement[Math.floor(Math.random() * feedbacks.needsImprovement.length)];
  }
}

// تنفيذ الدالة الرئيسية
main()
  .catch((e) => {
    console.error('💥 خطأ غير متوقع:', e);
    process.exit(1);
  });

import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/quran/memorization
export async function GET() {
  try {
    // استخدام جدول Exam_points الموجود بالفعل للحصول على بيانات حفظ القرآن
    // نستخدم هذا الجدول بدلاً من QuranProgress لأنه يحتوي على بيانات الامتحانات المتعلقة بالقرآن
    const quranRecords = await prisma.exam_points.findMany({
      where: {
        exam: {
          evaluationType: 'QURAN_MEMORIZATION' // نختار فقط امتحانات حفظ القرآن
        }
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            classeId: true,
            classe: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        surah: {
          select: {
            id: true,
            name: true,
            number: true
          }
        },
        exam: {
          select: {
            id: true,
            description: true,
            evaluationType: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // تنسيق البيانات للاستخدام في واجهة المستخدم
    const formattedRecords = quranRecords.map(record => ({
      id: record.id,
      studentId: record.studentId,
      studentName: record.student.name,
      classeId: record.student.classeId,
      className: record.student.classe?.name || '-',
      examId: record.examId,
      examDescription: record.exam.description || 'امتحان حفظ القرآن',
      surahId: record.surahId,
      surahName: record.surah?.name || 'غير محدد',
      surahNumber: record.surah?.number || 0,
      startVerse: record.startVerse || 1,
      endVerse: record.endVerse || 1,
      versesCount: record.startVerse && record.endVerse ? (record.endVerse - record.startVerse + 1) : 0,
      grade: Number(record.grade), // درجة الحفظ
      tajweedGrade: 0, // لا يوجد درجة تجويد في هذا الجدول، نضع قيمة افتراضية
      startDate: record.createdAt,
      completionDate: record.updatedAt,
      createdAt: record.createdAt
    }));

    return NextResponse.json(formattedRecords);
  } catch (error) {
    console.error('Error fetching Quran memorization data:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات حفظ القرآن' },
      { status: 500 }
    );
  }
}

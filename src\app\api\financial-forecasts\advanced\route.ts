import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// تعريف واجهات البيانات
interface DataPoint {
  yearMonth: string;
  amount: number;
}

interface CategoryForecast {
  categoryId: number | null;
  categoryName: string;
  percentage: number;
  forecasts: DataPoint[];
}

interface TrendPoint {
  month: string;
  percentChange: number;
}

interface TrendAnalysisData {
  increasing: TrendPoint[];
  decreasing: TrendPoint[];
  stable: TrendPoint[];
}

interface HistoricalExpenseResult {
  month: Date;
  total: string | number;
}

// تعريف نوع شروط البحث
type ExpenseWhereInput = {
  date: {
    gte: Date;
    lte: Date;
  };
  categoryId?: number;
};

// GET /api/financial-forecasts/advanced - الحصول على التنبؤات المالية المتقدمة
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const months = parseInt(searchParams.get('months') || '6'); // عدد الأشهر للتنبؤ (افتراضيًا 6 أشهر)
    const categoryId = searchParams.get('categoryId'); // فئة محددة (اختياري)
    const forecastType = searchParams.get('type') || 'realistic'; // نوع التنبؤ (متفائل، واقعي، متشائم)
    const seasonalityFactor = parseFloat(searchParams.get('seasonality') || '1'); // عامل الموسمية

    // الحصول على تاريخ اليوم
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    // تحديد فترة البيانات التاريخية (12 شهرًا سابقة)
    const historicalStartDate = new Date(currentYear, currentMonth - 12, 1);
    const historicalEndDate = new Date(currentYear, currentMonth, 0);

    // بناء شروط البحث للبيانات التاريخية
    const whereHistorical: ExpenseWhereInput = {
      date: {
        gte: historicalStartDate,
        lte: historicalEndDate,
      },
    };

    if (categoryId) {
      whereHistorical.categoryId = parseInt(categoryId);
    }

    // الحصول على البيانات التاريخية للمصروفات
    const historicalExpenses = await prisma.$queryRaw`
      SELECT
        DATE_TRUNC('month', date) as month,
        SUM(amount) as total
      FROM "Expense"
      WHERE date >= ${historicalStartDate} AND date <= ${historicalEndDate}
      ${categoryId ? `AND "categoryId" = ${parseInt(categoryId)}` : ''}
      GROUP BY DATE_TRUNC('month', date)
      ORDER BY month ASC
    `;

    // تنسيق البيانات التاريخية
    const historicalData: DataPoint[] = (historicalExpenses as HistoricalExpenseResult[]).map(item => ({
      yearMonth: new Date(item.month).toISOString().substring(0, 7),
      amount: parseFloat(item.total.toString()),
    }));

    // حساب معدل النمو الشهري
    let growthRate = 0;
    if (historicalData.length > 1) {
      const growthRates = [];
      for (let i = 1; i < historicalData.length; i++) {
        if (historicalData[i - 1].amount > 0) {
          const monthlyGrowth = (historicalData[i].amount - historicalData[i - 1].amount) / historicalData[i - 1].amount;
          growthRates.push(monthlyGrowth);
        }
      }

      // حساب متوسط معدل النمو
      if (growthRates.length > 0) {
        growthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
      }
    }

    // تعديل معدل النمو حسب نوع التنبؤ
    let adjustedGrowthRate = growthRate;
    switch (forecastType) {
      case 'optimistic':
        adjustedGrowthRate = Math.max(0, growthRate * 1.2); // زيادة بنسبة 20%
        break;
      case 'pessimistic':
        adjustedGrowthRate = Math.min(0, growthRate * 1.2); // زيادة بنسبة 20% إذا كان سالبًا
        break;
      default: // realistic
        // استخدام معدل النمو كما هو
        break;
    }

    // التنبؤ بالمصروفات المستقبلية
    const forecasts: DataPoint[] = [];
    let lastAmount = historicalData.length > 0
      ? historicalData[historicalData.length - 1].amount
      : 0;

    for (let i = 1; i <= months; i++) {
      const forecastMonth = new Date(currentYear, currentMonth + i, 1);
      const monthIndex = forecastMonth.getMonth(); // 0-11

      // تطبيق عامل الموسمية (زيادة في بعض الأشهر مثل رمضان أو بداية العام الدراسي)
      let seasonalAdjustment = 1;
      if (monthIndex === 8) { // سبتمبر - بداية العام الدراسي
        seasonalAdjustment = seasonalityFactor;
      } else if (monthIndex === 0) { // يناير - بداية العام
        seasonalAdjustment = seasonalityFactor * 0.8;
      }

      // حساب المبلغ المتوقع مع تطبيق معدل النمو وعامل الموسمية
      lastAmount = lastAmount * (1 + adjustedGrowthRate) * seasonalAdjustment;

      forecasts.push({
        yearMonth: forecastMonth.toISOString().substring(0, 7),
        amount: Math.max(0, lastAmount), // لا يمكن أن تكون المصروفات سالبة
      });
    }

    // التنبؤ حسب الفئات
    let categoryForecasts: CategoryForecast[] = [];

    if (!categoryId) {
      // الحصول على توزيع المصروفات حسب الفئة
      const categoryDistribution = await prisma.expense.groupBy({
        by: ['categoryId'],
        where: whereHistorical,
        _sum: {
          amount: true,
        },
      });

      // الحصول على إجمالي المصروفات التاريخية
      const totalHistoricalAmount = categoryDistribution.reduce(
        (sum, item) => sum + (item._sum.amount || 0),
        0
      );

      // الحصول على أسماء الفئات
      const categories = await prisma.expenseCategory.findMany({
        where: {
          id: {
            in: categoryDistribution
              .filter(item => item.categoryId !== null)
              .map(item => item.categoryId) as number[],
          },
        },
        select: {
          id: true,
          name: true,
        },
      });

      // حساب نسبة كل فئة من إجمالي المصروفات
      // نتأكد من أن إجمالي المصروفات التاريخية أكبر من صفر
      if (totalHistoricalAmount > 0) {
        categoryForecasts = categoryDistribution.map(item => {
          const category = categories.find(cat => cat.id === item.categoryId);
          const percentage = totalHistoricalAmount > 0
            ? ((item._sum.amount || 0) / totalHistoricalAmount) * 100
            : 0;

          // التنبؤ بمصروفات كل فئة بناءً على نسبتها من الإجمالي
          const categoryForecastData = forecasts.map(forecast => ({
            yearMonth: forecast.yearMonth,
            amount: (forecast.amount * percentage) / 100,
          }));

          return {
            categoryId: item.categoryId,
            categoryName: item.categoryId ? category?.name || 'غير معروف' : 'بدون فئة',
            percentage,
            forecasts: categoryForecastData,
          };
        });
      }
    }

    // تحليل الاتجاهات
    const trendAnalysis: TrendAnalysisData = {
      increasing: [],
      decreasing: [],
      stable: [],
    };

    if (historicalData.length > 3) {
      // حساب متوسط التغير للأشهر الثلاثة الأخيرة
      const recentMonths = historicalData.slice(-3);

      for (let i = 1; i < recentMonths.length; i++) {
        const change = recentMonths[i].amount - recentMonths[i - 1].amount;
        const percentChange = recentMonths[i - 1].amount > 0
          ? (change / recentMonths[i - 1].amount) * 100
          : 0;

        if (percentChange > 5) {
          trendAnalysis.increasing.push({
            month: recentMonths[i].yearMonth,
            percentChange,
          });
        } else if (percentChange < -5) {
          trendAnalysis.decreasing.push({
            month: recentMonths[i].yearMonth,
            percentChange,
          });
        } else {
          trendAnalysis.stable.push({
            month: recentMonths[i].yearMonth,
            percentChange,
          });
        }
      }
    }

    return NextResponse.json({
      historicalData,
      forecasts,
      growthRate: Math.round(adjustedGrowthRate * 100 * 100) / 100, // تقريب إلى رقمين عشريين
      categoryForecasts,
      forecastType,
      trendAnalysis,
      seasonalityFactor,
    });
  } catch (error) {
    console.error('خطأ في جلب التنبؤات المالية المتقدمة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب التنبؤات المالية المتقدمة' },
      { status: 500 }
    );
  }
}

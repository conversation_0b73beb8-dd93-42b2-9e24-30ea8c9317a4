"use client";
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { FaUserGraduate, FaArrowLeft, FaFileAlt, FaVideo, FaLink, FaDownload } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface CourseDetails {
  id: number;
  subjectId: number;
  subjectName: string;
  className: string;
  classId: number;
  studentsCount: number;
  description: string;
  students: {
    id: number;
    name: string;
  }[];
  materials: {
    id: number;
    title: string;
    type: string;
    url: string;
    createdAt: string;
  }[];
}

const CourseDetailsPage = ({ params }: { params: { id: string } }) => {
  const [courseDetails, setCourseDetails] = useState<CourseDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const fetchCourseDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب تفاصيل المقرر من API
        const response = await fetch(`/api/teacher-courses/${params.id}`);

        if (!response.ok) {
          throw new Error('فشل في جلب تفاصيل المقرر');
        }

        const data = await response.json();

        if (data.success) {
          setCourseDetails(data.courseDetails);
        } else {
          throw new Error(data.message || 'فشل في جلب تفاصيل المقرر');
        }

      } catch (err) {
        console.error('Error fetching course details:', err);
        setError('حدث خطأ أثناء جلب تفاصيل المقرر');
        toast.error('فشل في جلب تفاصيل المقرر');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourseDetails();
  }, [params.id]);

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FaFileAlt className="text-red-500" />;
      case 'video':
        return <FaVideo className="text-blue-500" />;
      case 'doc':
        return <FaFileAlt className="text-blue-700" />;
      default:
        return <FaLink className="text-gray-500" />;
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center">
        <Link href="/teachers/courses">
          <Button variant="outline" className="flex items-center gap-2 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white">
            <FaArrowLeft />
            العودة إلى المقررات
          </Button>
        </Link>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card className="border border-[#e0f2ef] shadow-md">
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : courseDetails ? (
        <>
          <Card className="border border-[#e0f2ef] shadow-md">
            <CardHeader className="bg-[#f8fffd]">
              <CardTitle className="text-2xl text-[var(--primary-color)]">{courseDetails.subjectName}</CardTitle>
              <CardDescription className="flex items-center gap-2 mt-2">
                <FaUserGraduate className="text-gray-500" />
                <span>{courseDetails.className} - {courseDetails.studentsCount} طالب</span>
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <p className="text-gray-700">{courseDetails.description}</p>
            </CardContent>
          </Card>

          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#e9f7f5] p-1 rounded-xl">
              <TabsTrigger value="overview" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">نظرة عامة</TabsTrigger>
              <TabsTrigger value="materials" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">المواد التعليمية</TabsTrigger>
              <TabsTrigger value="students" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">الطلاب</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <Card className="border border-[#e0f2ef] shadow-md">
                <CardHeader>
                  <CardTitle className="text-[var(--primary-color)]">نظرة عامة على المقرر</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-bold text-gray-700 mb-2">وصف المقرر</h3>
                      <p className="text-gray-600">{courseDetails.description}</p>
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-700 mb-2">أهداف المقرر</h3>
                      <ul className="list-disc list-inside text-gray-600 space-y-1">
                        <li>تعليم الطلاب أساسيات التجويد</li>
                        <li>حفظ سور جزء عم</li>
                        <li>تحسين مهارات التلاوة</li>
                        <li>فهم معاني الآيات بشكل مبسط</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-700 mb-2">المتطلبات</h3>
                      <p className="text-gray-600">لا توجد متطلبات سابقة لهذا المقرر.</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="materials">
              <Card className="border border-[#e0f2ef] shadow-md">
                <CardHeader>
                  <CardTitle className="text-[var(--primary-color)]">المواد التعليمية</CardTitle>
                  <CardDescription>المواد التعليمية المتاحة للطلاب في هذا المقرر</CardDescription>
                </CardHeader>
                <CardContent>
                  {courseDetails.materials.length > 0 ? (
                    <div className="space-y-4">
                      {courseDetails.materials.map((material) => (
                        <div key={material.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-center gap-3">
                            {getFileIcon(material.type)}
                            <div>
                              <h4 className="font-medium">{material.title}</h4>
                              <p className="text-sm text-gray-500">تم الإضافة: {material.createdAt}</p>
                            </div>
                          </div>
                          <Button variant="ghost" size="sm" className="text-[var(--primary-color)]">
                            <FaDownload />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 py-4">لا توجد مواد تعليمية متاحة</div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Link href={`/teachers/courses/${params.id}/materials`}>
                    <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">إدارة المواد التعليمية</Button>
                  </Link>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="students">
              <Card className="border border-[#e0f2ef] shadow-md">
                <CardHeader>
                  <CardTitle className="text-[var(--primary-color)]">الطلاب المسجلين</CardTitle>
                  <CardDescription>قائمة الطلاب المسجلين في هذا المقرر</CardDescription>
                </CardHeader>
                <CardContent>
                  {courseDetails.students.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {courseDetails.students.map((student) => (
                        <div key={student.id} className="p-3 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full bg-[var(--primary-color)] text-white flex items-center justify-center">
                              {student.name.charAt(0)}
                            </div>
                            <div>
                              <h4 className="font-medium">{student.name}</h4>
                              <p className="text-sm text-gray-500">رقم الطالب: {student.id}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 py-4">لا يوجد طلاب مسجلين</div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <Card className="border border-[#e0f2ef] shadow-md">
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-4">لم يتم العثور على تفاصيل المقرر</div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CourseDetailsPage;

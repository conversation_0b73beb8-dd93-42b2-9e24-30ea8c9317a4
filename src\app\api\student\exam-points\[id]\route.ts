import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { verifyToken } from "@/utils/verifyToken";
import { ActivityLogger, ActivityType } from "@/lib/activity-logger";

// PATCH /api/student/exam-points/[id]
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json({
        error: 'معرف نقطة الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من توكن المستخدم
    const payload = await verifyToken(request);

    if (!payload) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    // التحقق من أن المستخدم طالب
    const student = await prisma.student.findFirst({
      where: {
        username: payload.username
      }
    });

    if (!student) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 403 });
    }

    // التحقق من أن نقطة الامتحان تنتمي للطالب
    const examPoint = await prisma.exam_points.findUnique({
      where: {
        id: parseInt(id)
      },
      include: {
        exam: true,
        student: true
      }
    });

    if (!examPoint) {
      return NextResponse.json({
        error: 'نقطة الامتحان غير موجودة',
        success: false
      }, { status: 404 });
    }

    if (examPoint.studentId !== student.id) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول إلى نقطة الامتحان هذه',
        success: false
      }, { status: 403 });
    }

    const body = await request.json();
    const { status } = body;

    // تحديث حالة نقطة الامتحان
    const updatedExamPoint = await prisma.exam_points.update({
      where: {
        id: parseInt(id)
      },
      data: {
        status
      }
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      payload.id,
      ActivityType.EXAM,
      `قام الطالب ${student.name} بإكمال امتحان ${examPoint.exam.evaluationType} للشهر ${examPoint.exam.month}`
    );

    return NextResponse.json({
      data: updatedExamPoint,
      success: true,
      message: 'تم تحديث حالة نقطة الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error updating exam point status:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث حالة نقطة الامتحان',
      success: false
    }, { status: 500 });
  }
}

'use client';

import React, { useState, useEffect } from 'react';
import { FaCheckCircle, FaTimesCircle, FaExclamationTriangle, FaSync } from 'react-icons/fa';
import { validateImageUrl, fixImagePath, cleanImagePath } from '@/utils/imageUtils';

interface LogoDiagnosticProps {
  logoUrl?: string;
  onFixSuggestion?: (fixedUrl: string) => void;
}

interface DiagnosticResult {
  status: 'success' | 'error' | 'warning';
  message: string;
  suggestion?: string;
}

const LogoDiagnostic: React.FC<LogoDiagnosticProps> = ({ logoUrl, onFixSuggestion }) => {
  const [isChecking, setIsChecking] = useState(false);
  const [results, setResults] = useState<DiagnosticResult[]>([]);

  const runDiagnostic = async () => {
    setIsChecking(true);
    const diagnosticResults: DiagnosticResult[] = [];

    try {
      // فحص وجود رابط الشعار
      if (!logoUrl) {
        diagnosticResults.push({
          status: 'warning',
          message: 'لم يتم تحديد شعار للموقع',
          suggestion: 'قم برفع شعار من صفحة إعدادات المسؤول'
        });
      } else {
        // فحص صحة المسار
        const cleanedPath = cleanImagePath(logoUrl);
        const fixedPath = fixImagePath(cleanedPath);

        if (cleanedPath !== logoUrl) {
          diagnosticResults.push({
            status: 'warning',
            message: 'مسار الشعار يحتوي على معاملات إضافية',
            suggestion: `المسار المنظف: ${cleanedPath}`
          });
        }

        if (fixedPath !== cleanedPath) {
          diagnosticResults.push({
            status: 'warning',
            message: 'مسار الشعار يحتاج إلى إصلاح',
            suggestion: `المسار المصحح: ${fixedPath}`
          });
        }

        // فحص إمكانية تحميل الصورة
        const isValid = await validateImageUrl(fixedPath);
        
        if (isValid) {
          diagnosticResults.push({
            status: 'success',
            message: 'الشعار يتم تحميله بنجاح'
          });
        } else {
          diagnosticResults.push({
            status: 'error',
            message: 'فشل في تحميل الشعار',
            suggestion: 'تأكد من وجود الملف في المسار المحدد أو قم برفع شعار جديد'
          });

          // اقتراح مسارات بديلة
          const alternativePaths = [
            logoUrl.replace('/uploads/', '/public/uploads/'),
            logoUrl.replace('header-icons', 'images'),
            `/images/${logoUrl.split('/').pop()}`
          ];

          for (const altPath of alternativePaths) {
            const isAltValid = await validateImageUrl(altPath);
            if (isAltValid) {
              diagnosticResults.push({
                status: 'warning',
                message: `وجد مسار بديل: ${altPath}`,
                suggestion: altPath
              });
              break;
            }
          }
        }

        // فحص نوع الملف
        const fileName = logoUrl.split('/').pop() || '';
        const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
        const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        
        if (!validExtensions.includes(extension)) {
          diagnosticResults.push({
            status: 'warning',
            message: 'نوع الملف قد لا يكون مدعوماً',
            suggestion: 'استخدم صيغ الصور المدعومة: JPG, PNG, GIF, WebP, SVG'
          });
        }
      }

    } catch (error) {
      diagnosticResults.push({
        status: 'error',
        message: 'حدث خطأ أثناء فحص الشعار',
        suggestion: 'تحقق من اتصال الإنترنت وحاول مرة أخرى'
      });
    }

    setResults(diagnosticResults);
    setIsChecking(false);
  };

  useEffect(() => {
    if (logoUrl) {
      runDiagnostic();
    }
  }, [logoUrl]);

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <FaCheckCircle className="text-green-500" />;
      case 'error':
        return <FaTimesCircle className="text-red-500" />;
      case 'warning':
        return <FaExclamationTriangle className="text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">تشخيص الشعار</h3>
        <button
          onClick={runDiagnostic}
          disabled={isChecking}
          className="flex items-center space-x-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          <FaSync className={`${isChecking ? 'animate-spin' : ''}`} />
          <span>{isChecking ? 'جاري الفحص...' : 'إعادة الفحص'}</span>
        </button>
      </div>

      {results.length === 0 && !isChecking && (
        <p className="text-gray-500 text-center py-4">لا توجد نتائج فحص</p>
      )}

      {isChecking && (
        <div className="flex items-center justify-center py-4">
          <FaSync className="animate-spin text-blue-500 mr-2" />
          <span className="text-gray-600">جاري فحص الشعار...</span>
        </div>
      )}

      <div className="space-y-3">
        {results.map((result, index) => (
          <div
            key={index}
            className={`p-3 rounded-lg border ${getStatusColor(result.status)}`}
          >
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getStatusIcon(result.status)}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-800">
                  {result.message}
                </p>
                {result.suggestion && (
                  <div className="mt-2">
                    <p className="text-xs text-gray-600 mb-2">
                      اقتراح: {result.suggestion}
                    </p>
                    {onFixSuggestion && result.suggestion.startsWith('/') && (
                      <button
                        onClick={() => onFixSuggestion(result.suggestion!)}
                        className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200"
                      >
                        تطبيق الإصلاح
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {logoUrl && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-xs text-gray-600 mb-1">المسار الحالي:</p>
          <code className="text-xs bg-white px-2 py-1 rounded border break-all">
            {logoUrl}
          </code>
        </div>
      )}
    </div>
  );
};

export default LogoDiagnostic;

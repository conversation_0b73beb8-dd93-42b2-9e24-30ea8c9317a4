'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, Pencil, Trash2, Search, ArrowLeft, Filter, Eye } from 'lucide-react';
import Link from 'next/link';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { QuestionForm } from '@/components/question-form';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface Question {
  id: number;
  text: string;
  type: string;
  difficultyLevel: string;
  points: number;
  bankId: number;
  bank: {
    id: number;
    name: string;
  };
  options: QuestionOption[];
  answers: QuestionAnswer[];
  _count: {
    examQuestions: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface QuestionOption {
  id: number;
  questionId: number;
  text: string;
  isCorrect: boolean;
  order: number;
}

interface QuestionAnswer {
  id: number;
  questionId: number;
  text: string;
  isCorrect: boolean;
  explanation: string | null;
}

interface QuestionBank {
  id: number;
  name: string;
}

export default function QuestionsPage() {
  const searchParams = useSearchParams();
  const bankIdParam = searchParams ? searchParams.get('bankId') : null;

  const [questions, setQuestions] = useState<Question[]>([]);
  const [questionBanks, setQuestionBanks] = useState<QuestionBank[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBank, setFilterBank] = useState(bankIdParam || '');
  const [filterType, setFilterType] = useState('');
  const [filterDifficulty, setFilterDifficulty] = useState('');
  const [currentBank, setCurrentBank] = useState<QuestionBank | null>(null);

  useEffect(() => {
    fetchQuestionBanks();
    fetchQuestions();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bankIdParam]);

  const fetchQuestionBanks = async () => {
    try {
      const response = await fetch('/api/question-banks');
      const result = await response.json();

      if (result.success) {
        setQuestionBanks(result.data);

        // إذا كان هناك معرف بنك أسئلة في الرابط، قم بتعيين البنك الحالي
        if (bankIdParam) {
          const bank = result.data.find((b: QuestionBank) => b.id === parseInt(bankIdParam));
          if (bank) {
            setCurrentBank(bank);
          }
        }
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب بنوك الأسئلة');
      }
    } catch (error) {
      console.error('Error fetching question banks:', error);
      toast.error('حدث خطأ أثناء جلب بنوك الأسئلة');
    }
  };

  const fetchQuestions = async () => {
    setIsLoading(true);
    try {
      let url = '/api/questions';
      const params = new URLSearchParams();

      if (bankIdParam) {
        params.append('bankId', bankIdParam);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      const result = await response.json();

      if (result.success) {
        setQuestions(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب الأسئلة');
      }
    } catch (error) {
      console.error('Error fetching questions:', error);
      toast.error('حدث خطأ أثناء جلب الأسئلة');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddQuestion = () => {
    setIsAddDialogOpen(true);
  };

  const handleEditQuestion = (question: Question) => {
    setSelectedQuestion(question);
    setIsEditDialogOpen(true);
  };

  const handleViewQuestion = (question: Question) => {
    setSelectedQuestion(question);
    setIsViewDialogOpen(true);
  };

  const handleDeleteQuestion = (question: Question) => {
    setSelectedQuestion(question);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedQuestion) return;

    try {
      const response = await fetch(`/api/questions?id=${selectedQuestion.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        toast.success('تم حذف السؤال بنجاح');
        setIsDeleteDialogOpen(false);
        fetchQuestions();
      } else {
        toast.error(result.error || 'حدث خطأ أثناء حذف السؤال');
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('حدث خطأ أثناء حذف السؤال');
    }
  };

  const handleQuestionSaved = () => {
    setIsAddDialogOpen(false);
    setIsEditDialogOpen(false);
    fetchQuestions();
  };

  const getQuestionTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      MULTIPLE_CHOICE: 'اختيار من متعدد',
      TRUE_FALSE: 'صح أو خطأ',
      SHORT_ANSWER: 'إجابة قصيرة',
      ESSAY: 'مقال',
      MATCHING: 'مطابقة',
      FILL_BLANK: 'ملء الفراغات',
      ORDERING: 'ترتيب'
    };
    return types[type] || type;
  };

  const getDifficultyLevelLabel = (level: string) => {
    const levels: Record<string, string> = {
      EASY: 'سهل',
      MEDIUM: 'متوسط',
      HARD: 'صعب',
      VERY_HARD: 'صعب جداً'
    };
    return levels[level] || level;
  };

  const getDifficultyColor = (level: string) => {
    const colors: Record<string, string> = {
      EASY: 'bg-green-100 text-green-800',
      MEDIUM: 'bg-blue-100 text-blue-800',
      HARD: 'bg-orange-100 text-orange-800',
      VERY_HARD: 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      MULTIPLE_CHOICE: 'bg-purple-100 text-purple-800',
      TRUE_FALSE: 'bg-indigo-100 text-indigo-800',
      SHORT_ANSWER: 'bg-blue-100 text-blue-800',
      ESSAY: 'bg-teal-100 text-teal-800',
      MATCHING: 'bg-yellow-100 text-yellow-800',
      FILL_BLANK: 'bg-orange-100 text-orange-800',
      ORDERING: 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const filteredQuestions = questions.filter(question => {
    const matchesSearch =
      question.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      question.bank.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesBank = !filterBank || question.bankId === parseInt(filterBank);
    const matchesType = !filterType || question.type === filterType;
    const matchesDifficulty = !filterDifficulty || question.difficultyLevel === filterDifficulty;

    return matchesSearch && matchesBank && matchesType && matchesDifficulty;
  });

  return (
    <OptimizedProtectedRoute requiredPermission="admin.evaluation.questions.view">
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Link href="/admin/evaluation/question-banks" className="ml-4">
            <Button variant="outline" size="sm">
              <ArrowLeft className="ml-2" size={16} />
              العودة إلى بنوك الأسئلة
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">
            {currentBank ? `أسئلة بنك "${currentBank.name}"` : 'إدارة الأسئلة'}
          </h1>
        </div>
        <QuickActionButtons
          entityType="evaluation.questions"
          actions={[
            {
              key: 'create',
              label: 'إضافة سؤال جديد',
              icon: <Plus size={16} />,
              onClick: handleAddQuestion,
              variant: 'primary'
            }
          ]}
        />
      </div>

      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative col-span-1 md:col-span-2">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <Input
            placeholder="البحث في الأسئلة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-12 text-right"
            dir="rtl"
          />
        </div>

        <div className="col-span-1">
          <Select value={filterBank} onValueChange={setFilterBank}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع بنوك الأسئلة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_banks">جميع بنوك الأسئلة</SelectItem>
              {questionBanks.map((bank) => (
                <SelectItem key={bank.id} value={bank.id.toString()}>
                  {bank.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1">
          <div className="flex space-x-2">
            <div className="flex-1 ml-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="نوع السؤال" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_types">جميع الأنواع</SelectItem>
                  <SelectItem value="MULTIPLE_CHOICE">اختيار من متعدد</SelectItem>
                  <SelectItem value="TRUE_FALSE">صح أو خطأ</SelectItem>
                  <SelectItem value="SHORT_ANSWER">إجابة قصيرة</SelectItem>
                  <SelectItem value="ESSAY">مقال</SelectItem>
                  <SelectItem value="MATCHING">مطابقة</SelectItem>
                  <SelectItem value="FILL_BLANK">ملء الفراغات</SelectItem>
                  <SelectItem value="ORDERING">ترتيب</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <Select value={filterDifficulty} onValueChange={setFilterDifficulty}>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="مستوى الصعوبة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_levels">جميع المستويات</SelectItem>
                  <SelectItem value="EASY">سهل</SelectItem>
                  <SelectItem value="MEDIUM">متوسط</SelectItem>
                  <SelectItem value="HARD">صعب</SelectItem>
                  <SelectItem value="VERY_HARD">صعب جداً</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
        </div>
      ) : filteredQuestions.length === 0 ? (
        <div className="text-center p-8 bg-white rounded-lg shadow">
          <Filter className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أسئلة</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || filterBank || filterType || filterDifficulty
              ? 'لم يتم العثور على أسئلة تطابق معايير البحث. حاول تغيير المعايير أو إضافة سؤال جديد.'
              : 'لم يتم العثور على أي أسئلة. يمكنك إضافة سؤال جديد.'}
          </p>
          <Button
            onClick={handleAddQuestion}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
          >
            <Plus className="ml-2" size={16} />
            إضافة سؤال جديد
          </Button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <Table>
            <TableHeader className="bg-[var(--primary-color)]">
              <TableRow>
                <TableHead className="text-white text-right">نص السؤال</TableHead>
                <TableHead className="text-white text-right">النوع</TableHead>
                <TableHead className="text-white text-right">الصعوبة</TableHead>
                <TableHead className="text-white text-right">النقاط</TableHead>
                <TableHead className="text-white text-right">بنك الأسئلة</TableHead>
                <TableHead className="text-white text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredQuestions.map((question) => (
                <TableRow key={question.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    {question.text.length > 50
                      ? `${question.text.substring(0, 50)}...`
                      : question.text}
                  </TableCell>
                  <TableCell>
                    <Badge className={`${getTypeColor(question.type)}`}>
                      {getQuestionTypeLabel(question.type)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={`${getDifficultyColor(question.difficultyLevel)}`}>
                      {getDifficultyLevelLabel(question.difficultyLevel)}
                    </Badge>
                  </TableCell>
                  <TableCell>{question.points}</TableCell>
                  <TableCell>{question.bank.name}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewQuestion(question)}
                        className="ml-2"
                      >
                        <Eye className="h-4 w-4 ml-1" />
                        عرض
                      </Button>
                      <OptimizedActionButtonGroup
                        entityType="evaluation.questions"
                        onEdit={() => handleEditQuestion(question)}
                        onDelete={() => handleDeleteQuestion(question)}
                        showEdit={true}
                        showDelete={true}
                        size="sm"
                        className="gap-2"
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Question Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => !open && setIsAddDialogOpen(false)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>إضافة سؤال جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل السؤال الجديد</DialogDescription>
          </DialogHeader>
          <QuestionForm
            questionBanks={questionBanks}
            defaultBankId={bankIdParam ? parseInt(bankIdParam) : undefined}
            onSave={handleQuestionSaved}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Question Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>تعديل السؤال</DialogTitle>
            <DialogDescription>قم بتعديل تفاصيل السؤال</DialogDescription>
          </DialogHeader>
          {selectedQuestion && (
            <QuestionForm
              questionBanks={questionBanks}
              question={selectedQuestion}
              onSave={handleQuestionSaved}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* View Question Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={(open) => !open && setIsViewDialogOpen(false)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>عرض السؤال</DialogTitle>
            <DialogDescription>تفاصيل السؤال</DialogDescription>
          </DialogHeader>
          {selectedQuestion && (
            <div className="space-y-6">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-semibold mb-2 text-right">نص السؤال:</h3>
                <p className="text-right">{selectedQuestion.text}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h3 className="text-sm font-semibold mb-1 text-right">نوع السؤال:</h3>
                  <Badge className={`${getTypeColor(selectedQuestion.type)} w-full justify-center py-1`}>
                    {getQuestionTypeLabel(selectedQuestion.type)}
                  </Badge>
                </div>
                <div>
                  <h3 className="text-sm font-semibold mb-1 text-right">مستوى الصعوبة:</h3>
                  <Badge className={`${getDifficultyColor(selectedQuestion.difficultyLevel)} w-full justify-center py-1`}>
                    {getDifficultyLevelLabel(selectedQuestion.difficultyLevel)}
                  </Badge>
                </div>
                <div>
                  <h3 className="text-sm font-semibold mb-1 text-right">النقاط:</h3>
                  <div className="bg-gray-100 rounded p-2 text-center">{selectedQuestion.points}</div>
                </div>
              </div>

              <Tabs defaultValue="options" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="options">الخيارات</TabsTrigger>
                  <TabsTrigger value="answers">الإجابات الصحيحة</TabsTrigger>
                </TabsList>
                <TabsContent value="options" className="p-4 border rounded-lg mt-2">
                  {selectedQuestion.options && selectedQuestion.options.length > 0 ? (
                    <ul className="space-y-2">
                      {selectedQuestion.options.map((option) => (
                        <li key={option.id} className="p-2 border rounded flex justify-between items-center">
                          <span className={option.isCorrect ? 'text-primary-color font-semibold' : ''}>
                            {option.text}
                          </span>
                          {option.isCorrect && (
                            <Badge className="bg-green-100 text-green-800">إجابة صحيحة</Badge>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-center text-gray-500">لا توجد خيارات لهذا السؤال</p>
                  )}
                </TabsContent>
                <TabsContent value="answers" className="p-4 border rounded-lg mt-2">
                  {selectedQuestion.answers && selectedQuestion.answers.length > 0 ? (
                    <ul className="space-y-4">
                      {selectedQuestion.answers.map((answer) => (
                        <li key={answer.id} className="p-3 border rounded bg-green-50">
                          <div className="font-semibold text-right mb-1">{answer.text}</div>
                          {answer.explanation && (
                            <div className="text-sm text-gray-600 text-right mt-2">
                              <span className="font-semibold">الشرح: </span>
                              {answer.explanation}
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-center text-gray-500">لا توجد إجابات محددة لهذا السؤال</p>
                  )}
                </TabsContent>
              </Tabs>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setIsViewDialogOpen(false)}
                >
                  إغلاق
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Question Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-red-500">
          <DialogHeader>
            <DialogTitle>حذف السؤال</DialogTitle>
            <DialogDescription>هل أنت متأكد من رغبتك في حذف هذا السؤال؟</DialogDescription>
          </DialogHeader>
          {selectedQuestion && (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                <p className="text-red-700 text-right">
                  سيتم حذف السؤال التالي:
                </p>
                <p className="text-gray-700 text-right mt-2 font-semibold">
                  {selectedQuestion.text}
                </p>
                {selectedQuestion._count.examQuestions > 0 && (
                  <p className="text-red-700 text-right mt-2">
                    <strong>تحذير:</strong> هذا السؤال مستخدم في {selectedQuestion._count.examQuestions} امتحان.
                    حذفه سيؤثر على هذه الامتحانات.
                  </p>
                )}
                <p className="text-red-700 text-right mt-2">
                  هذا الإجراء لا يمكن التراجع عنه.
                </p>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDeleteDialogOpen(false)}
                  className="ml-2"
                >
                  إلغاء
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  onClick={confirmDelete}
                >
                  تأكيد الحذف
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

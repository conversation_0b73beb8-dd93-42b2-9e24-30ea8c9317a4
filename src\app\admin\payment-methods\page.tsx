'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Popconfirm, message, Spin } from 'antd';
import { FaCreditCard, FaEdit, FaTrash, FaPlus, FaCheck, FaTimes } from 'react-icons/fa';
import PaymentMethodModal from './PaymentMethodModal';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface PaymentMethod {
  id: number;
  name: string;
  description: string | null;
  isActive: boolean;
  requiresCard: boolean;
  icon: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function PaymentMethodsPage() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);

  // جلب طرق الدفع
  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/payment-methods');
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);
      setPaymentMethods(data);
    } catch (error: Error | unknown) {
      console.error('Error fetching payment methods:', error);
      message.error('فشل في جلب طرق الدفع');
    } finally {
      setLoading(false);
    }
  };

  // جلب طرق الدفع عند تحميل الصفحة
  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  // تصفية طرق الدفع بناءً على البحث
  const filteredPaymentMethods = paymentMethods.filter(method =>
    method.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (method.description && method.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // تغيير حالة طريقة الدفع (نشطة/غير نشطة)
  const handleToggleActive = async (id: number, isActive: boolean) => {
    try {
      const response = await fetch('/api/payment-methods', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id, isActive: !isActive }),
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      message.success(`تم ${!isActive ? 'تفعيل' : 'تعطيل'} طريقة الدفع بنجاح`);
      fetchPaymentMethods();
    } catch (error: Error | unknown) {
      console.error('Error toggling payment method status:', error);
      message.error(error instanceof Error ? error.message : 'فشل في تغيير حالة طريقة الدفع');
    }
  };

  // حذف طريقة دفع
  const handleDeletePaymentMethod = async (id: number) => {
    try {
      const response = await fetch(`/api/payment-methods?id=${id}`, {
        method: 'DELETE',
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      message.success('تم حذف طريقة الدفع بنجاح');
      fetchPaymentMethods();
    } catch (error: Error | unknown) {
      console.error('Error deleting payment method:', error);
      message.error(error instanceof Error ? error.message : 'فشل في حذف طريقة الدفع');
    }
  };

  // تعديل طريقة دفع
  const handleEditPaymentMethod = (paymentMethod: PaymentMethod) => {
    setSelectedPaymentMethod(paymentMethod);
    setIsModalVisible(true);
  };

  // إضافة طريقة دفع جديدة
  const handleAddPaymentMethod = () => {
    setSelectedPaymentMethod(null);
    setIsModalVisible(true);
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.payment-methods.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaCreditCard className="text-[var(--primary-color)]" />
          طرق الدفع
        </h1>
        <QuickActionButtons
          entityType="payment-methods"
          actions={[
            {
              key: 'create',
              label: 'إضافة طريقة دفع',
              icon: <FaPlus />,
              onClick: handleAddPaymentMethod,
              variant: 'primary'
            }
          ]}
          className="w-full sm:w-auto"
        />
      </div>

      {/* قسم البحث */}
      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-700 mb-1">بحث</label>
          <Input
            placeholder="ابحث عن طريقة دفع..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>
      </div>

      {/* قائمة طرق الدفع */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {loading ? (
          <div className="col-span-full flex justify-center py-8">
            <Spin size="large" />
          </div>
        ) : filteredPaymentMethods.length === 0 ? (
          <div className="col-span-full text-center py-8 bg-white rounded-lg shadow-sm">
            <p className="text-gray-500">لا توجد طرق دفع</p>
          </div>
        ) : (
          filteredPaymentMethods.map((method) => (
            <Card key={method.id} className={`overflow-hidden ${!method.isActive ? 'opacity-70' : ''}`}>
              <CardHeader className="pb-2">
                <div className="flex flex-col sm:flex-row justify-between items-start gap-3">
                  <div>
                    <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
                      {method.icon ? (
                        <img src={method.icon} alt={method.name} className="w-5 h-5 md:w-6 md:h-6" />
                      ) : (
                        <FaCreditCard className="text-[var(--primary-color)] text-lg md:text-xl" />
                      )}
                      <span>{method.name}</span>
                    </CardTitle>
                    {method.description && (
                      <CardDescription className="mt-1 text-sm">{method.description}</CardDescription>
                    )}
                  </div>
                  <div className="flex items-center gap-2 self-end sm:self-start">
                    <OptimizedActionButtonGroup
                      entityType="payment-methods"
                      onEdit={() => handleEditPaymentMethod(method)}
                      onDelete={() => handleDeletePaymentMethod(method.id)}
                      showEdit={true}
                      showDelete={true}
                      size="sm"
                      className="gap-2"
                      deleteConfirmTitle="هل أنت متأكد من حذف طريقة الدفع هذه؟"
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 pt-2 border-t">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">الحالة:</span>
                    {method.isActive ? (
                      <span className="text-sm text-primary-color flex items-center gap-1">
                        <FaCheck size={12} />
                        نشطة
                      </span>
                    ) : (
                      <span className="text-sm text-red-600 flex items-center gap-1">
                        <FaTimes size={12} />
                        غير نشطة
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2 mt-2 sm:mt-0">
                    <PermissionGuard requiredPermission="admin.payment-methods.edit">
                      <Switch
                        checked={method.isActive}
                        onCheckedChange={() => handleToggleActive(method.id, method.isActive)}
                      />
                      <Label htmlFor={`active-${method.id}`} className="text-sm">
                        {method.isActive ? 'نشطة' : 'غير نشطة'}
                      </Label>
                    </PermissionGuard>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* مكون النافذة المنبثقة */}
      <PaymentMethodModal
        isOpen={isModalVisible}
        onCloseAction={() => {
          setIsModalVisible(false);
          setSelectedPaymentMethod(null);
        }}
        onSuccessAction={fetchPaymentMethods}
        paymentMethod={selectedPaymentMethod}
      />
      </div>
    </OptimizedProtectedRoute>
  );
}

'use client';
import React, { useMemo } from 'react';
import { usePermissions } from '@/contexts/PermissionsContext';
import { FaEdit, FaTrash, FaPlus, FaEye, FaDownload, FaPrint } from 'react-icons/fa';

interface ActionButtonProps {
  onClick: () => void;
  variant?: 'edit' | 'delete' | 'add' | 'view' | 'download' | 'print';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
}

interface OptimizedActionButtonGroupProps {
  entityType: string;
  onEdit?: () => void;
  onDelete?: () => void;
  onView?: () => void;
  onDownload?: () => void;
  onPrint?: () => void;
  showEdit?: boolean;
  showDelete?: boolean;
  showView?: boolean;
  showDownload?: boolean;
  showPrint?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  customPermissions?: {
    edit?: string;
    delete?: string;
    view?: string;
    download?: string;
    print?: string;
  };
}

const ActionButton: React.FC<ActionButtonProps> = ({
  onClick,
  variant = 'edit',
  size = 'sm',
  className = '',
  children,
  disabled = false
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'edit':
        return 'bg-blue-500 hover:bg-blue-600 text-white focus:ring-blue-500';
      case 'delete':
        return 'bg-red-500 hover:bg-red-600 text-white focus:ring-red-500';
      case 'add':
        return 'bg-green-500 hover:bg-green-600 text-white focus:ring-green-500';
      case 'view':
        return 'bg-gray-500 hover:bg-gray-600 text-white focus:ring-gray-500';
      case 'download':
        return 'bg-indigo-500 hover:bg-indigo-600 text-white focus:ring-indigo-500';
      case 'print':
        return 'bg-purple-500 hover:bg-purple-600 text-white focus:ring-purple-500';
      default:
        return 'bg-gray-500 hover:bg-gray-600 text-white focus:ring-gray-500';
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'md':
        return 'px-3 py-2 text-sm';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-2 py-1 text-xs';
    }
  };

  const getIcon = () => {
    switch (variant) {
      case 'edit':
        return <FaEdit />;
      case 'delete':
        return <FaTrash />;
      case 'add':
        return <FaPlus />;
      case 'view':
        return <FaEye />;
      case 'download':
        return <FaDownload />;
      case 'print':
        return <FaPrint />;
      default:
        return <FaEdit />;
    }
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        inline-flex items-center justify-center rounded-md font-medium transition-colors
        focus:outline-none focus:ring-2 focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        ${getVariantStyles()}
        ${getSizeStyles()}
        ${className}
      `}
    >
      {children || getIcon()}
    </button>
  );
};

/**
 * مجموعة أزرار محسنة للأداء
 * تتحقق من جميع الصلاحيات مرة واحدة بدلاً من التحقق لكل زر منفصل
 */
export const OptimizedActionButtonGroup: React.FC<OptimizedActionButtonGroupProps> = ({
  entityType,
  onEdit,
  onDelete,
  onView,
  onDownload,
  onPrint,
  showEdit = true,
  showDelete = true,
  showView = false,
  showDownload = false,
  showPrint = false,
  size = 'sm',
  className = '',
  customPermissions
}) => {
  const { hasAnyPermission, userRole, isReady } = usePermissions();

  // تحديد الصلاحيات المطلوبة
  const requiredPermissions = useMemo(() => {
    const permissions: string[] = [];
    
    if (showEdit && onEdit) {
      permissions.push(customPermissions?.edit || `admin.${entityType}.edit`);
    }
    if (showDelete && onDelete) {
      permissions.push(customPermissions?.delete || `admin.${entityType}.delete`);
    }
    if (showView && onView) {
      permissions.push(customPermissions?.view || `admin.${entityType}.view`);
    }
    if (showDownload && onDownload) {
      permissions.push(customPermissions?.download || `admin.${entityType}.download`);
    }
    if (showPrint && onPrint) {
      permissions.push(customPermissions?.print || `admin.${entityType}.print`);
    }
    
    return permissions;
  }, [entityType, showEdit, showDelete, showView, showDownload, showPrint, onEdit, onDelete, onView, onDownload, onPrint, customPermissions]);

  // التحقق من الصلاحيات مرة واحدة
  const permissionResults = useMemo(() => {
    if (!isReady) return {};
    
    if (userRole === 'ADMIN') {
      return {
        canEdit: true,
        canDelete: true,
        canView: true,
        canDownload: true,
        canPrint: true
      };
    }

    return {
      canEdit: showEdit && onEdit ? hasAnyPermission([customPermissions?.edit || `admin.${entityType}.edit`]) : false,
      canDelete: showDelete && onDelete ? hasAnyPermission([customPermissions?.delete || `admin.${entityType}.delete`]) : false,
      canView: showView && onView ? hasAnyPermission([customPermissions?.view || `admin.${entityType}.view`]) : false,
      canDownload: showDownload && onDownload ? hasAnyPermission([customPermissions?.download || `admin.${entityType}.download`]) : false,
      canPrint: showPrint && onPrint ? hasAnyPermission([customPermissions?.print || `admin.${entityType}.print`]) : false
    };
  }, [isReady, userRole, hasAnyPermission, entityType, showEdit, showDelete, showView, showDownload, showPrint, onEdit, onDelete, onView, onDownload, onPrint, customPermissions]);

  // إذا لم يكن المستخدم لديه أي صلاحية، لا نعرض شيء
  if (!isReady || (!userRole || (userRole !== 'ADMIN' && !hasAnyPermission(requiredPermissions)))) {
    return null;
  }

  const { canEdit, canDelete, canView, canDownload, canPrint } = permissionResults;

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {canView && onView && (
        <ActionButton
          variant="view"
          size={size}
          onClick={onView}
        />
      )}
      {canEdit && onEdit && (
        <ActionButton
          variant="edit"
          size={size}
          onClick={onEdit}
        />
      )}
      {canDelete && onDelete && (
        <ActionButton
          variant="delete"
          size={size}
          onClick={onDelete}
        />
      )}
      {canDownload && onDownload && (
        <ActionButton
          variant="download"
          size={size}
          onClick={onDownload}
        />
      )}
      {canPrint && onPrint && (
        <ActionButton
          variant="print"
          size={size}
          onClick={onPrint}
        />
      )}
    </div>
  );
};

export default OptimizedActionButtonGroup;

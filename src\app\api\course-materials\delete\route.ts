import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { verifyToken } from '@/utils/verifyToken';
import { unlink } from 'fs/promises';
import { join } from 'path';

// DELETE /api/course-materials/delete?id=123 - حذف مادة تعليمية
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const userData = await verifyToken(request);
    if (!userData) {
      return NextResponse.json(
        { success: false, message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userData.id
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // استخراج معرف المادة التعليمية من الطلب
    const { searchParams } = new URL(request.url);
    const materialId = parseInt(searchParams.get('id') || '');

    if (isNaN(materialId)) {
      return NextResponse.json(
        { success: false, message: "معرف المادة التعليمية غير صالح" },
        { status: 400 }
      );
    }

    // جلب المادة التعليمية
    const material = await prisma.courseMaterial.findUnique({
      where: {
        id: materialId
      },
      include: {
        classSubject: {
          include: {
            teacherSubject: true
          }
        }
      }
    });

    if (!material) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على المادة التعليمية" },
        { status: 404 }
      );
    }

    // التحقق من أن المادة التعليمية تنتمي للمعلم
    if (material.classSubject.teacherSubject.teacherId !== teacher.id) {
      return NextResponse.json(
        { success: false, message: "غير مصرح لك بحذف هذه المادة التعليمية" },
        { status: 403 }
      );
    }

    // حذف الملف إذا كان موجوداً على الخادم
    if (material.type !== 'link' && material.url.startsWith('/uploads/')) {
      try {
        const filePath = join(process.cwd(), 'public', material.url);
        await unlink(filePath);
      } catch (error) {
        console.error('Error deleting file:', error);
        // نستمر في حذف السجل حتى لو فشل حذف الملف
      }
    }

    // حذف المادة التعليمية من قاعدة البيانات
    await prisma.courseMaterial.delete({
      where: {
        id: materialId
      }
    });

    return NextResponse.json({
      success: true,
      message: "تم حذف المادة التعليمية بنجاح"
    });
  } catch (error) {
    console.error('Error deleting course material:', error);
    return NextResponse.json(
      { success: false, message: "حدث خطأ أثناء حذف المادة التعليمية" },
      { status: 500 }
    );
  }
}

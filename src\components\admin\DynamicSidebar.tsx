'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import { FaCog, FaUsers, FaChalkboardTeacher, FaSchool, FaCalendarCheck, FaMoneyBillWave,
         FaHandHoldingHeart, FaBookOpen, FaLayerGroup, FaGraduationCap, FaClipboardList,
         FaDollarSign, FaChartBar, FaImages, FaQuran, FaTrophy, FaMedal, FaBell,
         FaFileInvoiceDollar, FaWallet, FaReceipt } from 'react-icons/fa';

interface Permission {
  id: number;
  key: string;
  name: string;
  description?: string;
  category: string;
  route?: string;
  isActive: boolean;
}

interface SidebarItem {
  permissionKey: string;
  title: string;
  href: string;
  icon: React.ReactNode;
  subItems?: {
    title: string;
    href: string;
    icon?: React.ReactNode;
    permissionKey?: string;
  }[];
}

const sidebarItems: SidebarItem[] = [
  {
    permissionKey: 'admin.dashboard.view',
    title: 'لوحة التحكم',
    href: '/admin/employee-dashboard',
    icon: <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
    </svg>
  },
  {
    permissionKey: 'admin.users.view',
    title: 'إدارة المستخدمين',
    href: '/admin/users',
    icon: <FaUsers className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.roles.view',
    title: 'إدارة الأدوار',
    href: '/admin/roles-permissions',
    icon: <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
    </svg>
  },
  {
    permissionKey: 'admin.teachers.view',
    title: 'المعلمون',
    href: '/admin/teachers',
    icon: <FaChalkboardTeacher className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.students.view',
    title: 'التلاميذ',
    href: '/admin/students',
    icon: <FaUsers className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.parents.view',
    title: 'أولياء الأمور',
    href: '/admin/parents',
    icon: <FaUsers className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.attendance.view',
    title: 'الحضور',
    href: '/admin/attendance',
    icon: <FaCalendarCheck className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.classes.view',
    title: 'الفصول',
    href: '/admin/classes',
    icon: <FaSchool className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.subjects.view',
    title: 'المواد',
    href: '/admin/subjects',
    icon: <FaBookOpen className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.levels.view',
    title: 'المستويات',
    href: '/admin/levels',
    icon: <FaLayerGroup className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.programs.view',
    title: 'البرامج',
    href: '/admin/programs',
    icon: <FaGraduationCap className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.evaluation.view',
    title: 'التقييم',
    href: '/admin/evaluation',
    icon: <FaClipboardList className="h-6 w-6 ml-3 text-white" />,
    subItems: [
      { title: 'لوحة التحكم', href: '/admin/evaluation', permissionKey: 'admin.evaluation.view' },
      { title: 'الامتحانات', href: '/admin/evaluation/exams', permissionKey: 'admin.evaluation.exams.view' },
      { title: 'الأسئلة', href: '/admin/evaluation/questions', permissionKey: 'admin.evaluation.questions.view' },
      { title: 'بنوك الأسئلة', href: '/admin/evaluation/question-banks', permissionKey: 'admin.evaluation.question-banks.view' },
      { title: 'النتائج', href: '/admin/evaluation/results', permissionKey: 'admin.evaluation.results.view' },
      { title: 'التصحيح', href: '/admin/evaluation/scoring', permissionKey: 'admin.evaluation.scoring.view' },
      { title: 'المساعدة', href: '/admin/evaluation/help', permissionKey: 'admin.evaluation.help.view' }
    ]
  },
  {
    permissionKey: 'admin.payments.view',
    title: 'المدفوعات',
    href: '/admin/payments',
    icon: <FaMoneyBillWave className="h-6 w-6 ml-3 text-white" />,
    subItems: [
      { title: 'إدارة المدفوعات', href: '/admin/payments', icon: <FaReceipt />, permissionKey: 'admin.payments.view' },
      { title: 'المدفوعات حسب الولي', href: '/admin/payments/by-parent', icon: <FaUsers />, permissionKey: 'admin.payments.view' },
      { title: 'الفواتير الإلكترونية', href: '/admin/invoices', icon: <FaFileInvoiceDollar />, permissionKey: 'admin.invoices.view' },
      { title: 'طرق الدفع', href: '/admin/payment-methods', icon: <FaWallet />, permissionKey: 'admin.payments.methods' }
    ]
  },
  {
    permissionKey: 'admin.treasury.view',
    title: 'الخزينة',
    href: '/admin/treasury',
    icon: <FaDollarSign className="h-6 w-6 ml-3 text-white" />,
    subItems: [
      { title: 'إدارة الخزينة', href: '/admin/treasury', permissionKey: 'admin.treasury.view' },
      { title: 'الميزانيات', href: '/admin/budgets', permissionKey: 'admin.budgets.view' },
      { title: 'التوقعات المالية', href: '/admin/financial-forecasts', permissionKey: 'admin.financial.forecasts' },
      { title: 'التقارير المالية', href: '/admin/financial-reports', permissionKey: 'admin.financial.reports' }
    ]
  },
  {
    permissionKey: 'admin.donations.view',
    title: 'التبرعات',
    href: '/admin/donations',
    icon: <FaHandHoldingHeart className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.expenses.view',
    title: 'المصروفات',
    href: '/admin/expenses',
    icon: <FaReceipt className="h-6 w-6 ml-3 text-white" />,
    subItems: [
      { title: 'إدارة المصروفات', href: '/admin/expenses', permissionKey: 'admin.expenses.view' },
      { title: 'فئات المصروفات', href: '/admin/expense-categories', permissionKey: 'admin.expense-categories.view' },
      { title: 'المصروفات الدورية', href: '/admin/expenses/recurring', permissionKey: 'admin.expenses.recurring.view' },
      { title: 'تذكيرات المصروفات', href: '/admin/expenses/reminders', permissionKey: 'admin.expenses.reminders.view' }
    ]
  },
  {
    permissionKey: 'admin.reports.view',
    title: 'التقارير',
    href: '/admin/reports',
    icon: <FaChartBar className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.supervisor-reports.view',
    title: 'التقارير الأدبية والمالية',
    href: '/admin/supervisor-reports',
    icon: <FaChartBar className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.student-images.view',
    title: 'صور الطلاب',
    href: '/admin/student-images',
    icon: <FaImages className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.khatm-sessions.view',
    title: 'مجالس الختم',
    href: '/admin/khatm-sessions',
    icon: <FaQuran className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.quran-progress.view',
    title: 'تقدم القرآن',
    href: '/admin/quran-progress',
    icon: <FaQuran className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.activities.view',
    title: 'الأنشطة',
    href: '/admin/activities',
    icon: <FaTrophy className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.rewards.view',
    title: 'المكافآت',
    href: '/admin/rewards',
    icon: <FaTrophy className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.honor-board.view',
    title: 'لوحة الشرف',
    href: '/admin/honor-board',
    icon: <FaMedal className="h-6 w-6 ml-3 text-white" />,
    subItems: [
      { title: 'لوحة الشرف', href: '/admin/honor-board', permissionKey: 'admin.honor-board.view' },
      { title: 'شهادات التقدير', href: '/admin/honor-board/certificates', permissionKey: 'admin.honor-board.certificates.view' },
      { title: 'معايير التقييم', href: '/admin/honor-board/criteria', permissionKey: 'admin.honor-board.criteria.view' }
    ]
  },
  {
    permissionKey: 'admin.notifications.view',
    title: 'الإشعارات',
    href: '/admin/notifications',
    icon: <FaBell className="h-6 w-6 ml-3 text-white" />
  },
  {
    permissionKey: 'admin.settings.view',
    title: 'إعدادات الموقع',
    href: '/admin/admin-setup',
    icon: <FaCog className="h-6 w-6 ml-3 text-white" />
  }
];

interface DynamicSidebarProps {
  isSidebarOpen: boolean;
  setIsSidebarOpen: (open: boolean) => void;
  userPermissions?: Permission[];
  userRole?: string;
}

const DynamicSidebar: React.FC<DynamicSidebarProps> = ({
  isSidebarOpen,
  setIsSidebarOpen,
  userPermissions = [],
  userRole
}) => {
  const [openSubMenus, setOpenSubMenus] = useState<string[]>([]);

  const toggleSubMenu = (key: string) => {
    setOpenSubMenus(prev =>
      prev.includes(key)
        ? prev.filter(item => item !== key)
        : [...prev, key]
    );
  };

  // التحقق من وجود صلاحية معينة
  const hasPermission = (permissionKey: string) => {
    // المدير لديه جميع الصلاحيات
    if (userRole === 'ADMIN') return true;

    // التحقق من وجود الصلاحية في قائمة صلاحيات المستخدم
    return userPermissions.some(permission => permission.key === permissionKey);
  };

  // فلترة عناصر الشريط الجانبي بناءً على الصلاحيات
  const filteredSidebarItems = sidebarItems.filter(item => hasPermission(item.permissionKey));

  console.log('DynamicSidebar - userRole:', userRole);
  console.log('DynamicSidebar - userPermissions:', userPermissions);
  console.log('DynamicSidebar - filteredSidebarItems:', filteredSidebarItems);

  return (
    <>
      {/* Overlay for mobile when sidebar is open */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}
      <aside
      className={`fixed top-[100px] bottom-[80px] right-0 w-72 md:w-64 text-white shadow-lg transform transition-all duration-300 ease-in-out z-20 overflow-y-auto
        ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full'}`}
      style={{ backgroundColor: 'var(--sidebar-color, var(--primary-color))' }}
    >
      <div className="p-4 border-b" style={{ borderColor: 'var(--secondary-color, var(--secondary-color))' }}>
        <h2 className="text-xl font-semibold text-white">لوحة الإدارة</h2>
      </div>
      <nav className="p-4">
        {filteredSidebarItems.length > 0 ? (
          <ul className="space-y-1">
            {filteredSidebarItems.map((item) => (
              <li key={item.permissionKey}>
                {item.subItems ? (
                  <>
                    <button
                      onClick={() => toggleSubMenu(item.permissionKey)}
                      className="w-full flex items-center justify-between px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                    >
                      <div className="flex items-center">
                        {item.icon}
                        <span>{item.title}</span>
                      </div>
                      <span className={`transform transition-transform ${openSubMenus.includes(item.permissionKey) ? 'rotate-180' : ''}`}>
                        ▼
                      </span>
                    </button>
                    {openSubMenus.includes(item.permissionKey) && (
                      <ul className="mr-6 mt-2 space-y-1 border-r-2 pr-4" style={{ borderColor: 'var(--secondary-color, var(--secondary-color))' }}>
                        {item.subItems
                          .filter(subItem => !subItem.permissionKey || hasPermission(subItem.permissionKey))
                          .map((subItem) => (
                          <li key={subItem.href}>
                            <Link
                              href={subItem.href}
                              className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                              onClick={() => setIsSidebarOpen(false)}
                            >
                              {subItem.icon && <span className="ml-2">{subItem.icon}</span>}
                              <span>{subItem.title}</span>
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </>
                ) : (
                  <Link
                    href={item.href}
                    className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                    onClick={() => setIsSidebarOpen(false)}
                  >
                    {item.icon}
                    <span>{item.title}</span>
                  </Link>
                )}
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-center text-white/70 py-8">
            <div className="mb-4">
              <svg className="h-12 w-12 mx-auto opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">لا توجد صلاحيات</h3>
            <p className="text-sm text-white/60 mb-4">
              لم يتم تخصيص دور لحسابك بعد.<br/>
              يرجى التواصل مع المسؤول لتخصيص الصلاحيات المناسبة.
            </p>
            <Link
              href="/admin/employee-dashboard"
              className="inline-block bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm transition-colors"
              onClick={() => setIsSidebarOpen(false)}
            >
              العودة للوحة التحكم
            </Link>
          </div>
        )}
      </nav>
    </aside>
    </>
  );
};

export default DynamicSidebar;

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// ألوان للطباعة الملونة في الكونسول
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// قائمة الاستبدالات
const replacements = [
  // الألوان المباشرة
  { from: '#169b88', to: 'var(--primary-color)', type: 'color' },
  { from: '#1ab19c', to: 'var(--secondary-color)', type: 'color' },
  { from: 'rgb(22, 155, 136)', to: 'var(--primary-color)', type: 'color' },
  { from: 'rgb(26, 177, 156)', to: 'var(--secondary-color)', type: 'color' },
  
  // Tailwind classes - الخلفيات
  { from: 'bg-[#169b88]', to: 'bg-primary-color', type: 'class' },
  { from: 'bg-[#1ab19c]', to: 'bg-secondary-color', type: 'class' },
  { from: 'bg-green-500', to: 'bg-primary-color', type: 'class' },
  { from: 'bg-green-600', to: 'bg-primary-color', type: 'class' },
  { from: 'bg-emerald-500', to: 'bg-primary-color', type: 'class' },
  { from: 'bg-emerald-600', to: 'bg-primary-color', type: 'class' },
  { from: 'bg-teal-500', to: 'bg-primary-color', type: 'class' },
  { from: 'bg-teal-600', to: 'bg-primary-color', type: 'class' },
  
  // Tailwind classes - النصوص
  { from: 'text-[#169b88]', to: 'text-primary-color', type: 'class' },
  { from: 'text-[#1ab19c]', to: 'text-secondary-color', type: 'class' },
  { from: 'text-green-500', to: 'text-primary-color', type: 'class' },
  { from: 'text-green-600', to: 'text-primary-color', type: 'class' },
  { from: 'text-emerald-500', to: 'text-primary-color', type: 'class' },
  { from: 'text-emerald-600', to: 'text-primary-color', type: 'class' },
  { from: 'text-teal-500', to: 'text-primary-color', type: 'class' },
  { from: 'text-teal-600', to: 'text-primary-color', type: 'class' },
  
  // Tailwind classes - الحدود
  { from: 'border-[#169b88]', to: 'border-primary-color', type: 'class' },
  { from: 'border-[#1ab19c]', to: 'border-secondary-color', type: 'class' },
  { from: 'border-green-500', to: 'border-primary-color', type: 'class' },
  { from: 'border-green-600', to: 'border-primary-color', type: 'class' },
  
  // Hover states
  { from: 'hover:bg-[#169b88]', to: 'hover:bg-primary-color', type: 'class' },
  { from: 'hover:bg-[#1ab19c]', to: 'hover:bg-secondary-color', type: 'class' },
  { from: 'hover:text-[#169b88]', to: 'hover:text-primary-color', type: 'class' },
  { from: 'hover:text-[#1ab19c]', to: 'hover:text-secondary-color', type: 'class' },
  
  // Focus states
  { from: 'focus:border-[#169b88]', to: 'focus:border-primary-color', type: 'class' },
  { from: 'focus:ring-[#169b88]', to: 'focus:ring-primary-color', type: 'class' },
  
  // Data states
  { from: 'data-[state=active]:bg-[#169b88]', to: 'data-[state=active]:bg-primary-color', type: 'class' },
];

// قائمة المجلدات المراد البحث فيها
const searchDirs = [
  'src/components',
  'src/app',
  'src/pages',
];

// قائمة امتدادات الملفات المراد البحث فيها
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];

let totalFiles = 0;
let fixedFiles = 0;
let totalFixes = 0;

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let fileFixCount = 0;

    replacements.forEach(replacement => {
      const regex = new RegExp(escapeRegExp(replacement.from), 'g');
      const matches = content.match(regex);
      if (matches) {
        content = content.replace(regex, replacement.to);
        fileFixCount += matches.length;
        console.log(`  ${colors.green}✓${colors.reset} ${replacement.from} → ${replacement.to} (${matches.length} مرة)`);
      }
    });

    if (fileFixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      fixedFiles++;
      totalFixes += fileFixCount;
      console.log(`${colors.bright}${colors.blue}📁 ${filePath}${colors.reset} - ${colors.green}${fileFixCount} إصلاح${colors.reset}`);
    }

    return fileFixCount;
  } catch (error) {
    console.error(`${colors.red}خطأ في إصلاح الملف ${filePath}: ${error.message}${colors.reset}`);
    return 0;
  }
}

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function searchInDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`${colors.yellow}المجلد غير موجود: ${dirPath}${colors.reset}`);
    return;
  }

  const items = fs.readdirSync(dirPath);

  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory()) {
      // تجاهل مجلدات معينة
      if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(item)) {
        searchInDirectory(itemPath);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (fileExtensions.includes(ext)) {
        totalFiles++;
        fixFile(itemPath);
      }
    }
  });
}

function generateReport() {
  console.log(`\n${colors.bright}${colors.cyan}📊 تقرير إصلاح الألوان${colors.reset}`);
  console.log('==================================================');
  console.log(`📁 إجمالي الملفات المفحوصة: ${totalFiles}`);
  console.log(`${colors.green}✅ الملفات المُصلحة: ${fixedFiles}${colors.reset}`);
  console.log(`${colors.green}🔧 إجمالي الإصلاحات: ${totalFixes}${colors.reset}`);
  
  if (totalFixes > 0) {
    console.log(`\n${colors.bright}${colors.green}✅ تم إصلاح جميع الألوان بنجاح!${colors.reset}`);
    console.log(`${colors.yellow}💡 تذكر:${colors.reset}`);
    console.log(`   • اختبر الموقع للتأكد من عمل الألوان`);
    console.log(`   • شغل ${colors.cyan}npm run find-colors${colors.reset} للتحقق من عدم وجود مشاكل`);
    console.log(`   • استخدم صفحة إعدادات الإدارة لتغيير الألوان`);
  } else {
    console.log(`\n${colors.bright}${colors.green}✅ لا توجد ألوان تحتاج إصلاح!${colors.reset}`);
  }
}

function main() {
  console.log(`${colors.bright}${colors.cyan}🔧 بدء إصلاح الألوان تلقائياً...${colors.reset}\n`);
  
  searchDirs.forEach(dir => {
    console.log(`${colors.bright}البحث في: ${dir}${colors.reset}`);
    searchInDirectory(dir);
  });
  
  generateReport();
}

// تشغيل الأداة
main();

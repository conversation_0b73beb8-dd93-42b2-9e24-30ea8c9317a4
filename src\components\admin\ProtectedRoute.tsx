'use client';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUserPermissions } from '@/hooks/useUserPermissions';
import SiteLogo from '@/components/SiteLogo';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission: string;
  fallbackPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  fallbackPath = '/admin'
}) => {
  const { hasPermission, userRole, loading } = useUserPermissions();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

  useEffect(() => {
    if (loading) return;

    console.log('ProtectedRoute - Checking permission:', requiredPermission);
    console.log('ProtectedRoute - User role:', userRole);

    // إذا كان المستخدم مدير، السماح بالوصول لكل شيء
    if (userRole === 'ADMIN') {
      console.log('ProtectedRoute - Admin access granted');
      setIsAuthorized(true);
      return;
    }

    // إذا كان المستخدم موظف، التحقق من الصلاحيات باستخدام النظام الجديد
    if (userRole === 'EMPLOYEE') {
      const hasRequiredPermission = hasPermission(requiredPermission);
      console.log(`ProtectedRoute - Employee permission for ${requiredPermission}:`, hasRequiredPermission);

      if (hasRequiredPermission) {
        setIsAuthorized(true);
      } else {
        console.log('ProtectedRoute - Access denied, redirecting to:', fallbackPath);
        setIsAuthorized(false);
        router.replace(fallbackPath);
      }
      return;
    }

    // للأدوار الأخرى، التحقق من الصلاحيات الأساسية
    if (userRole === 'TEACHER') {
      // المعلمون لديهم صلاحيات محدودة
      const teacherPermissions = ['admin.dashboard.view', 'admin.students.view', 'admin.attendance.view'];
      if (teacherPermissions.includes(requiredPermission)) {
        setIsAuthorized(true);
        return;
      }
    }

    // إذا لم يكن هناك صلاحيات أو دور غير معروف
    console.log('ProtectedRoute - No permissions or unknown role, redirecting');
    setIsAuthorized(false);
    router.replace('/login');
  }, [hasPermission, userRole, loading, requiredPermission, router, fallbackPath]);

  // عرض شاشة تحميل أثناء التحقق
  if (loading || isAuthorized === null) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من الصلاحيات...</p>
        </div>
      </div>
    );
  }

  // عرض رسالة خطأ إذا لم يكن مخول
  if (isAuthorized === false) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-lg shadow-md p-8">
            {/* شعار الموقع */}
            <div className="flex justify-center mb-6">
              <SiteLogo size="lg" showText={false} />
            </div>

            <div className="text-red-500 text-6xl mb-4">🚫</div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4">غير مخول للوصول</h1>
            <p className="text-gray-600 mb-6">
              عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة.
            </p>
            <button
              onClick={() => router.push(fallbackPath)}
              className="bg-[var(--primary-color)] text-white px-6 py-2 rounded-lg hover:bg-[var(--secondary-color)] transition-colors"
            >
              العودة إلى لوحة التحكم
            </button>
          </div>
        </div>
      </div>
    );
  }

  // عرض المحتوى إذا كان مخول
  return <>{children}</>;
};

export default ProtectedRoute;

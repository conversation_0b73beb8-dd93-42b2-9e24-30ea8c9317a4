import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 فحص المدفوعات الحديثة...');

    // جلب المدفوعات الحديثة للأولياء المحددين
    const recentPayments = await prisma.payment.findMany({
      where: {
        student: {
          guardian: {
            name: {
              in: ['أحمد محمود', 'حسن محمد']
            }
          }
        },
        createdAt: {
          gte: new Date('2025-06-20') // آخر 4 أيام
        }
      },
      include: {
        student: {
          include: {
            guardian: true
          }
        },
        invoice: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`💰 تم العثور على ${recentPayments.length} دفعة حديثة`);

    // تجميع المدفوعات حسب الولي
    const paymentsByParent = recentPayments.reduce((acc: any, payment) => {
      const parentName = payment.student.guardian?.name || 'غير محدد';
      
      if (!acc[parentName]) {
        acc[parentName] = {
          parentName,
          payments: [],
          totalAmount: 0,
          count: 0
        };
      }

      acc[parentName].payments.push({
        id: payment.id,
        amount: payment.amount,
        date: payment.date,
        createdAt: payment.createdAt,
        status: payment.status,
        studentName: payment.student.name,
        invoiceId: payment.invoiceId,
        invoiceType: payment.invoice?.type || 'غير محدد',
        invoiceDescription: payment.invoice?.description || 'غير محدد'
      });

      if (payment.status === 'PAID') {
        acc[parentName].totalAmount += payment.amount;
        acc[parentName].count++;
      }

      return acc;
    }, {});

    // فحص حالة الفواتير الحالية
    const currentInvoices = await prisma.invoice.findMany({
      where: {
        OR: [
          {
            parent: {
              name: {
                in: ['أحمد محمود', 'حسن محمد']
              }
            },
            type: 'FAMILY'
          },
          {
            student: {
              guardian: {
                name: {
                  in: ['أحمد محمود', 'حسن محمد']
                }
              }
            },
            type: 'INDIVIDUAL'
          }
        ]
      },
      include: {
        parent: true,
        student: {
          include: {
            guardian: true
          }
        },
        payments: {
          where: { status: 'PAID' }
        }
      }
    });

    // حساب حالة كل فاتورة
    const invoiceStatus = currentInvoices.map(invoice => {
      const totalPaid = invoice.payments.reduce((sum, p) => sum + p.amount, 0);
      const remaining = invoice.amount - totalPaid;
      const parentName = invoice.parent?.name || invoice.student?.guardian?.name || 'غير محدد';

      return {
        invoiceId: invoice.id,
        parentName,
        type: invoice.type,
        amount: invoice.amount,
        totalPaid,
        remaining,
        status: invoice.status,
        description: invoice.description,
        paymentsCount: invoice.payments.length
      };
    });

    return NextResponse.json({
      success: true,
      recentPayments: Object.values(paymentsByParent),
      invoiceStatus,
      summary: {
        totalRecentPayments: recentPayments.length,
        totalInvoices: currentInvoices.length,
        parentsWithRecentPayments: Object.keys(paymentsByParent).length
      }
    });

  } catch (error) {
    console.error('❌ خطأ في فحص المدفوعات الحديثة:', error);
    return NextResponse.json(
      { error: 'فشل في فحص المدفوعات' },
      { status: 500 }
    );
  }
}

/*
==============================================================================
    ملف تكوين ReconDash
    
    الوصف: إعدادات قابلة للتخصيص لتطبيق ReconDash
    المؤلف: Praetorian Team
==============================================================================
*/

/*
==============================================================================
    إعدادات الواجهة الرسومية
==============================================================================
*/

# أبعاد النافذة الافتراضية
WINDOW_WIDTH = 900
WINDOW_HEIGHT = 700
WINDOW_RESIZABLE = 1

# عنوان النافذة
WINDOW_TITLE = "ReconDash - لوحة التحكم الاستطلاعية v1.0"

# الهدف الافتراضي
DEFAULT_TARGET = "example.com"

/*
==============================================================================
    إعدادات الفحص
==============================================================================
*/

# إعدادات فاحص الشبكة
SCANNER_TIMEOUT = 3000          # مهلة الاتصال بالميلي ثانية
SCANNER_MAX_THREADS = 50        # أقصى عدد خيوط للفحص
SCANNER_VERBOSE = false         # عرض تفاصيل إضافية

# المنافذ الشائعة للفحص
COMMON_PORTS = [
    21,    # FTP
    22,    # SSH
    23,    # Telnet
    25,    # SMTP
    53,    # DNS
    80,    # HTTP
    110,   # POP3
    143,   # IMAP
    443,   # HTTPS
    993,   # IMAPS
    995,   # POP3S
    135,   # RPC
    139,   # NetBIOS
    445,   # SMB
    1433,  # MSSQL
    1521,  # Oracle
    3306,  # MySQL
    3389,  # RDP
    5432,  # PostgreSQL
    5900,  # VNC
    8080,  # HTTP-Alt
    8443   # HTTPS-Alt
]

# المنافذ الآمنة للفحص SSL
SSL_PORTS = [443, 993, 995, 8443, 465, 587]

/*
==============================================================================
    إعدادات عميل HTTP
==============================================================================
*/

# إعدادات عميل HTTP
HTTP_TIMEOUT = 10               # مهلة طلبات HTTP بالثواني
HTTP_USER_AGENT = "ReconDash/1.0 (Praetorian Scanner)"
HTTP_FOLLOW_REDIRECTS = true    # تتبع إعادة التوجيه
HTTP_VERIFY_SSL = false         # التحقق من شهادات SSL

# رؤوس HTTP افتراضية
HTTP_DEFAULT_HEADERS = [
    "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language: en-US,en;q=0.5",
    "Accept-Encoding: gzip, deflate",
    "Connection: keep-alive",
    "Upgrade-Insecure-Requests: 1"
]

/*
==============================================================================
    إعدادات الزاحف
==============================================================================
*/

# إعدادات زاحف الويب
CRAWLER_MAX_DEPTH = 2           # العمق الأقصى للزحف
CRAWLER_MAX_PAGES = 25          # العدد الأقصى للصفحات
CRAWLER_DELAY = 500             # التأخير بين الطلبات بالميلي ثانية
CRAWLER_VERBOSE = false         # عرض تفاصيل إضافية

# امتدادات الملفات المسموحة للزحف
CRAWLER_ALLOWED_EXTENSIONS = [
    ".html", ".htm", ".php", ".asp", ".aspx", ".jsp", ".do"
]

# امتدادات الملفات المحظورة
CRAWLER_BLOCKED_EXTENSIONS = [
    ".jpg", ".jpeg", ".png", ".gif", ".css", ".js", ".pdf", 
    ".zip", ".rar", ".exe", ".dmg", ".iso", ".mp3", ".mp4", 
    ".avi", ".mov", ".doc", ".docx", ".xls", ".xlsx"
]

/*
==============================================================================
    إعدادات SSL
==============================================================================
*/

# إعدادات مدقق SSL
SSL_TIMEOUT = 10                # مهلة اتصال SSL بالثواني
SSL_VERBOSE = false             # عرض تفاصيل إضافية

# الشيفرات الضعيفة للفحص
SSL_WEAK_CIPHERS = [
    "DES-CBC-SHA",
    "DES-CBC3-SHA", 
    "RC4-MD5",
    "RC4-SHA",
    "NULL-MD5",
    "NULL-SHA",
    "EXP-DES-CBC-SHA",
    "EXP-RC4-MD5",
    "EXP-RC2-CBC-MD5"
]

# بروتوكولات SSL/TLS للفحص
SSL_PROTOCOLS = [
    "SSLv2",
    "SSLv3", 
    "TLSv1.0",
    "TLSv1.1",
    "TLSv1.2",
    "TLSv1.3"
]

/*
==============================================================================
    إعدادات التسجيل
==============================================================================
*/

# إعدادات نظام التسجيل
LOG_LEVEL = 2                   # مستوى التسجيل (1=DEBUG, 2=INFO, 3=WARNING, 4=ERROR, 5=CRITICAL)
LOG_TO_FILE = true              # التسجيل في ملف
LOG_TO_CONSOLE = true           # التسجيل في وحدة التحكم
LOG_FILE_NAME = "recondash.log" # اسم ملف السجل
LOG_INCLUDE_TIMESTAMP = true    # إضافة الوقت والتاريخ
LOG_INCLUDE_LEVEL = true        # إضافة مستوى التسجيل

/*
==============================================================================
    إعدادات الألوان والواجهة
==============================================================================
*/

# ألوان الحالة (إذا كانت مدعومة)
COLOR_SUCCESS = "green"
COLOR_ERROR = "red"
COLOR_WARNING = "yellow"
COLOR_INFO = "blue"

# رسائل الحالة
STATUS_READY = "جاهز للفحص..."
STATUS_SCANNING = "جاري الفحص..."
STATUS_COMPLETE = "تم الانتهاء من الفحص"
STATUS_ERROR = "حدث خطأ أثناء الفحص"

/*
==============================================================================
    إعدادات الأداء
==============================================================================
*/

# إعدادات الأداء
MAX_CONCURRENT_SCANS = 1        # عدد الفحوصات المتزامنة
MEMORY_LIMIT_MB = 512           # حد الذاكرة بالميجابايت
CACHE_RESULTS = true            # تخزين النتائج مؤقتاً
CACHE_TIMEOUT = 300             # مهلة التخزين المؤقت بالثواني

/*
==============================================================================
    إعدادات الأمان
==============================================================================
*/

# إعدادات الأمان
ENABLE_SAFE_MODE = true         # تفعيل الوضع الآمن
MAX_SCAN_DURATION = 300         # أقصى مدة للفحص بالثواني
RATE_LIMIT_REQUESTS = 100       # حد الطلبات في الدقيقة
BLACKLIST_PRIVATE_IPS = true    # منع فحص عناوين IP الخاصة

# عناوين IP المحظورة
BLACKLISTED_IPS = [
    "127.0.0.1",
    "localhost",
    "0.0.0.0"
]

# نطاقات IP الخاصة المحظورة
PRIVATE_IP_RANGES = [
    "10.0.0.0/8",
    "**********/12", 
    "***********/16",
    "***********/16"
]

/*
==============================================================================
    إعدادات التصدير
==============================================================================
*/

# إعدادات تصدير النتائج
EXPORT_FORMAT = "json"          # تنسيق التصدير (json, xml, csv, txt)
EXPORT_DIRECTORY = "exports"    # مجلد التصدير
EXPORT_FILENAME_PREFIX = "recondash_scan_"
EXPORT_INCLUDE_TIMESTAMP = true # إضافة الوقت لاسم الملف

/*
==============================================================================
    إعدادات التحديث
==============================================================================
*/

# إعدادات التحديث التلقائي
CHECK_UPDATES = true            # فحص التحديثات عند البدء
UPDATE_URL = "https://github.com/praetorian-ring/updates"
UPDATE_FREQUENCY = 7            # فحص التحديثات كل 7 أيام

/*
==============================================================================
    دوال مساعدة للتكوين
==============================================================================
*/

/*
تحميل إعدادات مخصصة من ملف
*/
func loadCustomConfig cConfigFile
    if fexists(cConfigFile)
        try
            load cConfigFile
            return true
        catch
            return false
        done
    ok
    return false

/*
حفظ الإعدادات الحالية في ملف
*/
func saveCurrentConfig cConfigFile
    try
        cConfigContent = "# إعدادات ReconDash المخصصة" + nl
        cConfigContent += "# تم إنشاؤها في: " + date() + " " + time() + nl + nl
        
        # إضافة الإعدادات الأساسية
        cConfigContent += "WINDOW_WIDTH = " + WINDOW_WIDTH + nl
        cConfigContent += "WINDOW_HEIGHT = " + WINDOW_HEIGHT + nl
        cConfigContent += "SCANNER_TIMEOUT = " + SCANNER_TIMEOUT + nl
        cConfigContent += "HTTP_TIMEOUT = " + HTTP_TIMEOUT + nl
        cConfigContent += "CRAWLER_MAX_DEPTH = " + CRAWLER_MAX_DEPTH + nl
        cConfigContent += "CRAWLER_MAX_PAGES = " + CRAWLER_MAX_PAGES + nl
        
        write(cConfigFile, cConfigContent)
        return true
    catch
        return false
    done

/*
إعادة تعيين الإعدادات للقيم الافتراضية
*/
func resetToDefaults
    WINDOW_WIDTH = 900
    WINDOW_HEIGHT = 700
    SCANNER_TIMEOUT = 3000
    HTTP_TIMEOUT = 10
    CRAWLER_MAX_DEPTH = 2
    CRAWLER_MAX_PAGES = 25

/*
التحقق من صحة الإعدادات
*/
func validateConfig
    bValid = true
    
    if WINDOW_WIDTH < 600 or WINDOW_WIDTH > 2000
        WINDOW_WIDTH = 900
        bValid = false
    ok
    
    if WINDOW_HEIGHT < 400 or WINDOW_HEIGHT > 1500
        WINDOW_HEIGHT = 700
        bValid = false
    ok
    
    if SCANNER_TIMEOUT < 1000 or SCANNER_TIMEOUT > 30000
        SCANNER_TIMEOUT = 3000
        bValid = false
    ok
    
    return bValid

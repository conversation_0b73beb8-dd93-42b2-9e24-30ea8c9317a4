import { LiteraryReportData } from '@/lib/reports/literary-report-service';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import * as XLSX from 'xlsx';

// تعريف خيارات التصدير
export interface ExportOptions {
  includeCharts?: boolean;
  includeDetails?: boolean;
  customTitle?: string;
  watermark?: string;
}

export class LiteraryReportExporter {
  /**
   * تصدير التقرير الأدبي بصيغة PDF
   */
  static async exportToPDF(
    reportData: LiteraryReportData,
    options: ExportOptions = {}
  ): Promise<Blob> {
    try {
      // إنشاء محتوى HTML منسق للتقرير الأدبي
      const reportContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>التقرير الأدبي</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #3b82f6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        h1 {
            color: #3b82f6;
            font-size: 28px;
            margin-bottom: 10px;
        }
        h2 {
            color: #1e40af;
            font-size: 20px;
            margin-top: 30px;
            margin-bottom: 15px;
            border-right: 4px solid #3b82f6;
            padding-right: 10px;
        }
        .period {
            color: #666;
            font-size: 16px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        th {
            background-color: #3b82f6;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8fafc;
        }
        .stats-table th {
            background-color: #3b82f6;
        }
        .classes-table th {
            background-color: #10b981;
        }
        .quran-table th {
            background-color: #8b5a2b;
        }
        .activities-table th {
            background-color: #a855f7;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #3b82f6;
            color: #666;
        }
        .number {
            font-weight: bold;
            color: #3b82f6;
        }
        .percentage {
            font-weight: bold;
            color: #10b981;
        }
        .section {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>التقرير الأدبي</h1>
        <h2>${options.customTitle || 'تقرير شامل للأنشطة التعليمية'}</h2>
        <div class="period">للفترة من ${format(new Date(reportData.period.startDate), 'PPP', { locale: ar })} إلى ${format(new Date(reportData.period.endDate), 'PPP', { locale: ar })}</div>
    </div>

    <div class="section">
        <h2>الإحصائيات العامة</h2>
        <table class="stats-table">
            <tr><th>البيان</th><th>العدد</th></tr>
            <tr><td>إجمالي الطلاب</td><td class="number">${reportData.generalStats.totalStudents}</td></tr>
            <tr><td>إجمالي المعلمين</td><td class="number">${reportData.generalStats.totalTeachers}</td></tr>
            <tr><td>إجمالي الفصول</td><td class="number">${reportData.generalStats.totalClasses}</td></tr>
            <tr><td>الحفاظ</td><td class="number">${reportData.generalStats.totalMemorizers}</td></tr>
            <tr><td>مجالس الختم</td><td class="number">${reportData.generalStats.totalKhatmSessions}</td></tr>
            <tr><td>الأنشطة</td><td class="number">${reportData.generalStats.totalActivities}</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>توزيع الطلاب على الفصول</h2>
        <table class="classes-table">
            <tr><th>الفصل</th><th>عدد الطلاب</th><th>السعة</th><th>نسبة الإشغال</th></tr>
            ${reportData.studentsDetails.classes.map(classe => `
            <tr>
                <td>${classe.name}</td>
                <td class="number">${classe.studentsCount}</td>
                <td class="number">${classe.capacity}</td>
                <td class="percentage">${classe.capacity > 0 ? Math.round((classe.studentsCount / classe.capacity) * 100) : 0}%</td>
            </tr>
            `).join('')}
        </table>
    </div>

    <div class="section">
        <h2>إحصائيات القرآن الكريم</h2>
        <table class="quran-table">
            <tr><th>البيان</th><th>القيمة</th></tr>
            <tr><td>عدد الحفاظ</td><td class="number">${reportData.quranDetails.memorizers}</td></tr>
            <tr><td>متوسط درجات الحفظ</td><td class="number">${reportData.quranDetails.averageMemorization.toFixed(1)}</td></tr>
            <tr><td>متوسط درجات التجويد</td><td class="number">${reportData.quranDetails.averageTajweed.toFixed(1)}</td></tr>
            <tr><td>إجمالي سجلات التقدم</td><td class="number">${reportData.quranDetails.totalProgress}</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>مجالس الختم</h2>
        <table class="activities-table">
            <tr><th>العنوان</th><th>التاريخ</th><th>المعلم</th><th>الحضور</th></tr>
            ${reportData.khatmDetails.sessions.slice(0, 10).map(session => `
            <tr>
                <td>${session.title}</td>
                <td>${format(session.date, 'PPP', { locale: ar })}</td>
                <td>${session.teacherName}</td>
                <td class="number">${session.presentCount}/${session.attendeesCount}</td>
            </tr>
            `).join('')}
        </table>
        <p><strong>إجمالي المجالس:</strong> <span class="number">${reportData.khatmDetails.total}</span></p>
    </div>

    <div class="section">
        <h2>الامتحانات</h2>
        <table class="activities-table">
            <tr><th>الوصف</th><th>عدد الطلاب</th><th>متوسط الدرجات</th><th>الناجحون</th></tr>
            ${reportData.examsDetails.exams.slice(0, 10).map(exam => `
            <tr>
                <td>${exam.description}</td>
                <td class="number">${exam.studentsCount}</td>
                <td class="number">${exam.averageGrade.toFixed(1)}</td>
                <td class="number">${exam.passedStudents}</td>
            </tr>
            `).join('')}
        </table>
        <p><strong>إجمالي الامتحانات:</strong> <span class="number">${reportData.examsDetails.total}</span></p>
    </div>

    <div class="section">
        <h2>الأنشطة</h2>
        <table class="activities-table">
            <tr><th>النشاط</th><th>المنظم</th><th>التاريخ</th></tr>
            ${reportData.activitiesDetails.activities.slice(0, 10).map(activity => `
            <tr>
                <td>${activity.title}</td>
                <td>${activity.organizer}</td>
                <td>${format(activity.date, 'PPP', { locale: ar })}</td>
            </tr>
            `).join('')}
        </table>
        <p><strong>إجمالي الأنشطة:</strong> <span class="number">${reportData.activitiesDetails.total}</span></p>
    </div>

    <div class="footer">
        <p><strong>تقرير يوم ${format(new Date(), 'PPP', { locale: ar })}</strong></p>
    </div>
</body>
</html>
      `;

      return new Blob([reportContent], { type: 'text/html; charset=utf-8' });
    } catch (error) {
      console.error('خطأ في تصدير PDF:', error);
      throw new Error('فشل في تصدير التقرير بصيغة PDF');
    }
  }

  /**
   * تصدير التقرير الأدبي بصيغة Word
   */
  static async exportToWord(
    reportData: LiteraryReportData,
    options: ExportOptions = {}
  ): Promise<Blob> {
    try {
      // إنشاء محتوى HTML للتقرير
      const htmlContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>التقرير الأدبي</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
        h1, h2, h3 { color: #2563eb; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f3f4f6; }
    </style>
</head>
<body>
    <h1>التقرير الأدبي</h1>
    <h2>${options.customTitle || 'تقرير شامل للأنشطة التعليمية'}</h2>
    <p>للفترة من ${format(new Date(reportData.period.startDate), 'PPP', { locale: ar })} إلى ${format(new Date(reportData.period.endDate), 'PPP', { locale: ar })}</p>

    <h3>الإحصائيات العامة</h3>
    <table>
        <tr><th>البيان</th><th>العدد</th></tr>
        <tr><td>إجمالي الطلاب</td><td>${reportData.generalStats.totalStudents}</td></tr>
        <tr><td>إجمالي المعلمين</td><td>${reportData.generalStats.totalTeachers}</td></tr>
        <tr><td>إجمالي الفصول</td><td>${reportData.generalStats.totalClasses}</td></tr>
        <tr><td>الحفاظ</td><td>${reportData.generalStats.totalMemorizers}</td></tr>
        <tr><td>مجالس الختم</td><td>${reportData.generalStats.totalKhatmSessions}</td></tr>
        <tr><td>الأنشطة</td><td>${reportData.generalStats.totalActivities}</td></tr>
    </table>

    <h3>توزيع الطلاب على الفصول</h3>
    <table>
        <tr><th>الفصل</th><th>عدد الطلاب</th><th>السعة</th><th>نسبة الإشغال</th></tr>
        ${reportData.studentsDetails.classes.map(classe => `
        <tr>
            <td>${classe.name}</td>
            <td>${classe.studentsCount}</td>
            <td>${classe.capacity}</td>
            <td>${classe.capacity > 0 ? Math.round((classe.studentsCount / classe.capacity) * 100) : 0}%</td>
        </tr>
        `).join('')}
    </table>

    <h3>إحصائيات القرآن الكريم</h3>
    <ul>
        <li>عدد الحفاظ: ${reportData.quranDetails.memorizers}</li>
        <li>متوسط درجات الحفظ: ${reportData.quranDetails.averageMemorization.toFixed(1)}</li>
        <li>متوسط درجات التجويد: ${reportData.quranDetails.averageTajweed.toFixed(1)}</li>
        <li>إجمالي سجلات التقدم: ${reportData.quranDetails.totalProgress}</li>
    </ul>

    <p><strong>تقرير يوم ${format(new Date(), 'PPP', { locale: ar })}</strong></p>
</body>
</html>
      `;

      return new Blob([htmlContent], { type: 'text/html; charset=utf-8' });
    } catch (error) {
      console.error('خطأ في تصدير Word:', error);
      throw new Error('فشل في تصدير التقرير بصيغة Word');
    }
  }

  /**
   * تصدير التقرير كـ HTML
   */
  static async exportToHTML(
    reportData: LiteraryReportData,
    options: ExportOptions = {}
  ): Promise<Blob> {
    try {
      const htmlContent = this.generateHTMLContent(reportData, options);
      return new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    } catch (error) {
      console.error('خطأ في تصدير HTML:', error);
      throw new Error('فشل في تصدير التقرير بصيغة HTML');
    }
  }

  /**
   * إضافة رأس التقرير
   */
  private static addReportHeader(
    pdf: jsPDF,
    reportData: LiteraryReportData,
    yPosition: number,
    pageWidth: number,
    margin: number
  ): number {
    const centerX = pageWidth / 2;

    // عنوان الجمعية
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.text('جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن', centerX, yPosition, { align: 'center' });
    yPosition += 8;

    pdf.setFontSize(14);
    pdf.text('المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر', centerX, yPosition, { align: 'center' });
    yPosition += 6;

    pdf.setFontSize(12);
    pdf.text('شعبة بلدية المنقر', centerX, yPosition, { align: 'center' });
    yPosition += 10;

    // عنوان التقرير
    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.text('التقريــر الأدبــــــــي لشعبة بلديــة المنقر', centerX, yPosition, { align: 'center' });
    yPosition += 8;

    // الفترة الزمنية
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    const startDate = format(new Date(reportData.period.startDate), 'PPP', { locale: ar });
    const endDate = format(new Date(reportData.period.endDate), 'PPP', { locale: ar });
    pdf.text(`للفترة من: ${startDate} إلى: ${endDate}`, centerX, yPosition, { align: 'center' });
    yPosition += 6;

    // تاريخ إنشاء التقرير
    const currentDate = format(new Date(), 'PPP', { locale: ar });
    pdf.text(`تاريخ إنشاء التقرير: ${currentDate}`, centerX, yPosition, { align: 'center' });
    yPosition += 15;

    // خط فاصل
    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;

    return yPosition;
  }

  /**
   * إضافة المقدمة
   */
  private static addIntroduction(
    pdf: jsPDF,
    yPosition: number,
    contentWidth: number,
    margin: number
  ): number {
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('* مقدمة:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    const introText = [
      'بناء على القرار المؤرخ في صفر 1436هـ الموافق لـ ديسمبر 2014م الذي يُحدد دفتر الشروط',
      'المتعلق بإنشاء المؤسسات التعليمية والنوادي والمعاهد التابعة لجمعية العلماء المسلمين',
      'الجزائريين ومراقبتها.',
      '',
      '- وعملا بقانون الجمعيات رقم 12 - 06، وتطبيقا للمادتين 05 و 58 من القانون الأساسي.',
      '',
      '- ووفقا للقانون المنظم للعملية التربوية لاسيما المادة 12 المحددة لمهام مدير المدرسة وواجباته.',
      '',
      'نَعرض على أسماعكم المحترمة الأعمال التالية التي قامت بها الجمعية بهياكلها أو من يمثلها',
      'نيابة عن أعضائها ومن ذلك:'
    ];

    introText.forEach(line => {
      if (line === '') {
        yPosition += 4;
      } else {
        const splitText = pdf.splitTextToSize(line, contentWidth);
        pdf.text(splitText, margin, yPosition);
        yPosition += splitText.length * 5;
      }
    });

    yPosition += 10;
    return yPosition;
  }

  /**
   * إضافة الإحصائيات العامة
   */
  private static addGeneralStats(
    pdf: jsPDF,
    reportData: LiteraryReportData,
    yPosition: number,
    contentWidth: number,
    margin: number
  ): number {
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('* الإحصائيات العامة:', margin, yPosition);
    yPosition += 10;

    // جدول الإحصائيات
    const stats = [
      ['البيان', 'العدد'],
      ['إجمالي الطلاب', reportData.generalStats.totalStudents.toString()],
      ['إجمالي المعلمين', reportData.generalStats.totalTeachers.toString()],
      ['عدد الصفوف', reportData.generalStats.totalClasses.toString()],
      ['عدد الحفاظ', reportData.generalStats.totalMemorizers.toString()],
      ['مجالس الختم', reportData.generalStats.totalKhatmSessions.toString()],
      ['الأنشطة المنجزة', reportData.generalStats.totalActivities.toString()],
    ];

    yPosition = this.addTable(pdf, stats, yPosition, margin, contentWidth);
    yPosition += 10;

    return yPosition;
  }

  /**
   * إضافة تفاصيل الطلاب
   */
  private static addStudentDetails(
    pdf: jsPDF,
    reportData: LiteraryReportData,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    // التحقق من المساحة المتاحة
    if (yPosition > pageHeight - 50) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('1/ الجانب التربوي:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    const studentText = `يبلغ إجمالي عدد الطلاب المسجلين في الشعبة ${reportData.studentsDetails.total} طالباً وطالبة، موزعين على ${reportData.studentsDetails.classes.length} صفوف دراسية.`;
    const splitText = pdf.splitTextToSize(studentText, contentWidth);
    pdf.text(splitText, margin, yPosition);
    yPosition += splitText.length * 5 + 8;

    // جدول توزيع الطلاب حسب الصفوف
    const classesData = [
      ['الصف', 'عدد الطلاب', 'السعة', 'نسبة الإشغال'],
      ...reportData.studentsDetails.classes.map(classe => [
        classe.name,
        classe.studentsCount.toString(),
        classe.capacity.toString(),
        `${classe.capacity > 0 ? Math.round((classe.studentsCount / classe.capacity) * 100) : 0}%`
      ])
    ];

    yPosition = this.addTable(pdf, classesData, yPosition, margin, contentWidth);

    // معدل الحضور
    yPosition += 8;
    pdf.text(`معدل الحضور العام: ${reportData.attendanceDetails.attendanceRate.toFixed(1)}%`, margin, yPosition);
    yPosition += 10;

    return yPosition;
  }

  /**
   * إضافة تفاصيل القرآن
   */
  private static addQuranDetails(
    pdf: jsPDF,
    reportData: LiteraryReportData,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    if (yPosition > pageHeight - 50) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('2/ الجانب القرآني:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    const quranText = [
      `حققنا هذا الموسم أفضل نسب حفظ للقرآن الكريم حيث بلغ عدد الحفاظ ${reportData.quranDetails.memorizers} طالباً،`,
      `بمتوسط درجات حفظ ${reportData.quranDetails.averageMemorization.toFixed(1)} ومتوسط درجات تجويد ${reportData.quranDetails.averageTajweed.toFixed(1)}.`,
      '',
      `إجمالي سجلات التقدم في الحفظ: ${reportData.quranDetails.totalProgress} سجل.`,
      '',
      `وقد أقمنا ${reportData.khatmDetails.total} مجلس ختم خلال هذه الفترة، بمشاركة إجمالية بلغت ${reportData.khatmDetails.sessions.reduce((sum, session) => sum + session.attendeesCount, 0)} مشارك.`
    ];

    quranText.forEach(line => {
      if (line === '') {
        yPosition += 4;
      } else {
        const splitText = pdf.splitTextToSize(line, contentWidth);
        pdf.text(splitText, margin, yPosition);
        yPosition += splitText.length * 5;
      }
    });

    yPosition += 10;
    return yPosition;
  }

  /**
   * إضافة تفاصيل الأنشطة
   */
  private static addActivitiesDetails(
    pdf: jsPDF,
    reportData: LiteraryReportData,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    if (yPosition > pageHeight - 50) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('3/ الأنشطة والفعاليات:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    const activitiesText = `تم تنظيم ${reportData.activitiesDetails.total} نشاط وفعالية خلال الفترة المحددة، شملت:`;
    const splitText = pdf.splitTextToSize(activitiesText, contentWidth);
    pdf.text(splitText, margin, yPosition);
    yPosition += splitText.length * 5 + 6;

    const activitiesList = [
      '• محاضرات ودروس للرجال والنساء',
      '• دورات تكوينية في العلوم الشرعية واللغة العربية',
      '• مسابقات قرآنية ومسابقة المواظبة على صلاة الصبح',
      '• رحلات ترفيهية ومخيمات تعليمية',
      '• حفلات تكريم وختم القرآن الكريم'
    ];

    activitiesList.forEach(item => {
      pdf.text(item, margin + 5, yPosition);
      yPosition += 6;
    });

    yPosition += 10;
    return yPosition;
  }

  /**
   * إضافة الخاتمة والتوقيع
   */
  private static addConclusion(
    pdf: jsPDF,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): void {
    if (yPosition > pageHeight - 80) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('* الخاتمة:', margin, yPosition);
    yPosition += 8;

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    const conclusionText = 'هذا ما تيسر عرضه من أعمال الشعبة خلال الفترة المحددة، ونسأل الله التوفيق والسداد في خدمة كتاب الله وسنة رسوله صلى الله عليه وسلم.';
    const splitText = pdf.splitTextToSize(conclusionText, contentWidth);
    pdf.text(splitText, margin, yPosition);
    yPosition += splitText.length * 5 + 20;

    // التوقيع
    const currentDate = format(new Date(), 'PPP', { locale: ar });
    pdf.text(`تقرير يوم ${currentDate}`, pageHeight - margin - 60, yPosition);
    yPosition += 8;
    pdf.text('عن رئيس المكتب البلدي:', pageHeight - margin - 60, yPosition);
    yPosition += 15;
    pdf.setFont('helvetica', 'bold');
    pdf.text('الوليد بن ناصر قصي', pageHeight - margin - 60, yPosition);
  }

  /**
   * إضافة جدول
   */
  private static addTable(
    pdf: jsPDF,
    data: string[][],
    yPosition: number,
    margin: number,
    contentWidth: number
  ): number {
    const rowHeight = 8;
    const colWidth = contentWidth / data[0].length;

    data.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        const x = margin + (colIndex * colWidth);
        const y = yPosition + (rowIndex * rowHeight);

        // رسم حدود الخلية
        pdf.rect(x, y - 6, colWidth, rowHeight);

        // إضافة النص
        pdf.setFontSize(9);
        if (rowIndex === 0) {
          pdf.setFont('helvetica', 'bold');
        } else {
          pdf.setFont('helvetica', 'normal');
        }

        pdf.text(cell, x + 2, y, { maxWidth: colWidth - 4 });
      });
    });

    return yPosition + (data.length * rowHeight) + 5;
  }

  /**
   * إضافة علامة مائية
   */
  private static addWatermark(pdf: jsPDF, watermarkText: string): void {
    const pageCount = pdf.getNumberOfPages();

    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setTextColor(200, 200, 200);
      pdf.setFontSize(50);
      pdf.text(watermarkText, 105, 150, {
        align: 'center',
        angle: 45,
      });
    }

    pdf.setTextColor(0, 0, 0); // إعادة تعيين اللون
  }

  /**
   * إنشاء محتوى HTML للتقرير
   */
  private static generateHTMLContent(
    reportData: LiteraryReportData,
    options: ExportOptions
  ): string {
    const startDate = format(new Date(reportData.period.startDate), 'PPP', { locale: ar });
    const endDate = format(new Date(reportData.period.endDate), 'PPP', { locale: ar });
    const currentDate = format(new Date(), 'PPP', { locale: ar });

    return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير الأدبي</title>
    <style>
        body { font-family: 'Cairo', Arial, sans-serif; line-height: 1.6; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 18px; font-weight: bold; margin: 10px 0; }
        .subtitle { font-size: 14px; margin: 5px 0; }
        .section { margin: 20px 0; }
        .section-title { font-size: 14px; font-weight: bold; margin-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #000; padding: 8px; text-align: right; }
        th { background-color: #f0f0f0; font-weight: bold; }
        .signature { margin-top: 50px; text-align: left; }
        @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن</div>
        <div class="subtitle">المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر</div>
        <div class="subtitle">شعبة بلدية المنقر</div>
        <div class="title">التقريــر الأدبــــــــي لشعبة بلديــة المنقر</div>
        <div class="subtitle">للفترة من: ${startDate} إلى: ${endDate}</div>
        <div class="subtitle">تاريخ إنشاء التقرير: ${currentDate}</div>
    </div>

    <div class="section">
        <div class="section-title">* الإحصائيات العامة:</div>
        <table>
            <tr><th>البيان</th><th>العدد</th></tr>
            <tr><td>إجمالي الطلاب</td><td>${reportData.generalStats.totalStudents}</td></tr>
            <tr><td>إجمالي المعلمين</td><td>${reportData.generalStats.totalTeachers}</td></tr>
            <tr><td>عدد الصفوف</td><td>${reportData.generalStats.totalClasses}</td></tr>
            <tr><td>عدد الحفاظ</td><td>${reportData.generalStats.totalMemorizers}</td></tr>
            <tr><td>مجالس الختم</td><td>${reportData.generalStats.totalKhatmSessions}</td></tr>
            <tr><td>الأنشطة المنجزة</td><td>${reportData.generalStats.totalActivities}</td></tr>
        </table>
    </div>

    <div class="signature">
        <p>تقرير يوم ${currentDate}</p>
        <p>عن رئيس المكتب البلدي:</p>
        <p><strong>الوليد بن ناصر قصي</strong></p>
    </div>
</body>
</html>`;
  }
}

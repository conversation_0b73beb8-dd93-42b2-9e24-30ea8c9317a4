'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>ooter, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { toast } from '@/components/ui/use-toast'
import { AddClassModal } from './add-class-modal'
import { EditClassModal } from './edit-class-modal'
import { StudentDistributionModal } from './student-distribution-modal'
import { ClassScheduleModal } from './class-schedule-modal'
import { ClassReportModal } from './class-report-modal'
import { FaChalkboardTeacher, FaSearch, FaEdit, FaTrash, FaSync } from 'react-icons/fa'
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'

interface Subject {
  id: number
  name: string
}

interface Teacher {
  id: number
  name: string
}

interface TeacherSubject {
  id: number;
  teacher: Teacher;
  subject: Subject;
}

// تصدير واجهة Class لاستخدامها في الملفات الأخرى
export interface Class {
  id: number
  name: string
  capacity: number
  description?: string
  students: unknown[] // للتوافق مع التعريف في edit-class-modal.tsx
  classSubjects: {
    teacherSubject: TeacherSubject
  }[]
  createdAt: string
  updatedAt?: string
}

export default function ClassesPage() {
  const [classes, setClasses] = useState<Class[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [isStudentDistributionDialogOpen, setIsStudentDistributionDialogOpen] = useState(false)
  const [isClassReportDialogOpen, setIsClassReportDialogOpen] = useState(false)
  const [selectedClass, setSelectedClass] = useState<Class | null>(null)
  const [activeTab, setActiveTab] = useState<'classes' | 'schedules' | 'reports'>('classes')

  useEffect(() => {
    fetchClasses()
  }, [])

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes?includeSubjects=true&includeStudents=true')
      if (!response.ok) throw new Error('Failed to fetch classes')
      const data = await response.json()

      // التحقق من هيكل البيانات المستلمة
      let classesData = [];

      if (data && data.classes && Array.isArray(data.classes)) {
        // البيانات موجودة في خاصية classes
        classesData = data.classes;
      } else if (Array.isArray(data)) {
        // البيانات هي مصفوفة مباشرة
        classesData = data;
      }

      // التأكد من أن كل فصل لديه مصفوفة طلاب فارغة على الأقل
      const processedData = classesData.map((cls: Partial<Class>) => ({
        ...cls,
        students: Array.isArray(cls.students) ? cls.students : []
      }));

      console.log('Fetched classes data:', processedData);
      setClasses(processedData);
    } catch (error) {
      console.error('Error fetching classes:', error);
      setClasses([]);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب بيانات الأقسام',
        variant: 'destructive'
      });
    }
  }

  const handleDelete = async () => {
    try {
      if (!selectedClass) return;
      const response = await fetch(`/api/classes?id=${selectedClass.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete class');

      await fetchClasses();
      setIsDeleteDialogOpen(false);
      toast({
        title: 'نجاح',
        description: 'تم حذف القسم بنجاح'
      });
    } catch (error) {
      console.error('Error deleting class:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في حذف القسم',
        variant: 'destructive'
      });
    }
  }
  return (
    <OptimizedProtectedRoute requiredPermission="admin.classes.view">
      <div className="p-3 sm:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-3 mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-2 sm:pr-3 flex items-center gap-2">
          <FaChalkboardTeacher className="text-[var(--primary-color)]" />
          إدارة الأقسام
        </h1>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto justify-center">
          <QuickActionButtons
            entityType="classes"
            actions={[
              {
                key: 'create',
                label: 'إضافة قسم جديد',
                icon: <FaChalkboardTeacher />,
                onClick: () => setIsAddDialogOpen(true),
                variant: 'primary'
              }
            ]}
            className="w-full sm:w-auto"
          />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef] mb-4 sm:mb-6">
        <div className="flex flex-wrap sm:flex-nowrap border-b border-[#e0f2ef]">
          <button
            onClick={() => setActiveTab('classes')}
            className={`flex-1 py-2 sm:py-3 px-2 sm:px-4 text-center font-medium text-xs sm:text-sm ${
              activeTab === 'classes'
                ? 'bg-[var(--primary-color)] text-white'
                : 'text-[var(--primary-color)] hover:bg-[#e0f2ef]'
            }`}
          >
            الفصول الدراسية
          </button>
          <PermissionGuard requiredPermission="admin.classes.schedules">
            <button
              onClick={() => setActiveTab('schedules')}
              className={`flex-1 py-2 sm:py-3 px-2 sm:px-4 text-center font-medium text-xs sm:text-sm ${
                activeTab === 'schedules'
                  ? 'bg-[var(--primary-color)] text-white'
                  : 'text-[var(--primary-color)] hover:bg-[#e0f2ef]'
              }`}
            >
              جداول الحصص
            </button>
          </PermissionGuard>
          <PermissionGuard requiredPermission="admin.reports.view">
            <button
              onClick={() => setActiveTab('reports')}
              className={`flex-1 py-2 sm:py-3 px-2 sm:px-4 text-center font-medium text-xs sm:text-sm ${
                activeTab === 'reports'
                  ? 'bg-[var(--primary-color)] text-white'
                  : 'text-[var(--primary-color)] hover:bg-[#e0f2ef]'
              }`}
            >
              تقارير الأداء
            </button>
          </PermissionGuard>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row items-center gap-2 sm:space-x-4 bg-white p-3 sm:p-4 rounded-lg shadow-md mb-4 sm:mb-6">
        <div className="relative w-full">
          <Input
            type="text"
            placeholder="البحث عن قسم..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] text-xs sm:text-sm"
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
        <Button
          onClick={fetchClasses}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2 w-full sm:w-auto"
          title="تحديث البيانات"
        >
          <FaSync className="h-4 w-4 sm:h-5 sm:w-5" />
          <span className="sm:hidden mr-2">تحديث البيانات</span>
        </Button>
      </div>

      {activeTab === 'classes' && (
        <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-[var(--primary-color)]">
                  <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4">اسم القسم</TableHead>
                  <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">المعلمين والمواد</TableHead>
                  <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">عدد الطلاب</TableHead>
                  <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">السعة</TableHead>
                  <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">نسبة الإشغال</TableHead>
                  <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {classes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-sm text-gray-500">
                      لا توجد أقسام متاحة
                    </TableCell>
                  </TableRow>
                ) : classes
                  .filter((cls) =>
                    cls.name.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                  .map((cls) => {
                    const studentsCount = Array.isArray(cls.students) ? cls.students.length : 0;
                    const occupancyRate = cls.capacity ? (studentsCount / cls.capacity) * 100 : 0;
                    const isNearCapacity = occupancyRate >= 80;
                    const isOverCapacity = occupancyRate > 100;

                    return (
                      <TableRow key={cls.id}>
                        <TableCell className="font-medium text-xs sm:text-sm p-2 sm:p-4">
                          <div>
                            {cls.name}
                            {cls.description && (
                              <p className="text-xs text-gray-500 mt-1">{cls.description}</p>
                            )}

                            {/* معلومات إضافية تظهر فقط على الشاشات الصغيرة */}
                            <div className="sm:hidden mt-2 space-y-2">
                              <div className="flex items-center justify-between text-xs">
                                <span className="text-gray-500">الطلاب:</span>
                                <span className="font-medium">{studentsCount} / {cls.capacity || 30}</span>
                              </div>

                              <div className="flex items-center gap-1">
                                <div className="w-full bg-gray-200 rounded-full h-2 max-w-[100px]">
                                  <div
                                    className={`h-2 rounded-full ${
                                      isOverCapacity
                                        ? 'bg-red-600'
                                        : isNearCapacity
                                          ? 'bg-yellow-500'
                                          : 'bg-[var(--primary-color)]'
                                    }`}
                                    style={{ width: `${Math.min(occupancyRate, 100)}%` }}
                                  ></div>
                                </div>
                                <span className={`text-xs ${
                                  isOverCapacity
                                    ? 'text-red-600'
                                    : isNearCapacity
                                      ? 'text-yellow-500'
                                      : 'text-[var(--primary-color)]'
                                }`}>
                                  {occupancyRate.toFixed(0)}%
                                </span>
                              </div>

                              {/* عرض المعلمين والمواد على الشاشات الصغيرة */}
                              {cls.classSubjects && cls.classSubjects.length > 0 && (
                                <div className="mt-2">
                                  <span className="text-xs text-gray-500 block mb-1">المعلمين والمواد:</span>
                                  <div className="space-y-1">
                                    {cls.classSubjects.map((cs) => (
                                      <div key={cs.teacherSubject.id} className="text-xs bg-gray-50 p-1 rounded">
                                        <span className="font-medium">{cs.teacherSubject.teacher.name}</span> - {cs.teacherSubject.subject.name}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">
                          {!cls.classSubjects || cls.classSubjects.length === 0 ? (
                            <span className="text-gray-500 text-sm">لا يوجد</span>
                          ) : (
                            <div className="space-y-1">
                              {cls.classSubjects.map((cs) => (
                                <div key={cs.teacherSubject.id} className="text-sm bg-gray-50 p-1 rounded">
                                  <span className="font-medium">{cs.teacherSubject.teacher.name}</span> - {cs.teacherSubject.subject.name}
                                </div>
                              ))}
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">{studentsCount}</TableCell>
                        <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">{cls.capacity || 30}</TableCell>
                        <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">
                          <div className="flex items-center gap-2">
                            <div className="w-full bg-gray-200 rounded-full h-2.5 max-w-[100px]">
                              <div
                                className={`h-2.5 rounded-full ${
                                  isOverCapacity
                                    ? 'bg-red-600'
                                    : isNearCapacity
                                      ? 'bg-yellow-500'
                                      : 'bg-[var(--primary-color)]'
                                }`}
                                style={{ width: `${Math.min(occupancyRate, 100)}%` }}
                              ></div>
                            </div>
                            <span className={`text-sm ${
                              isOverCapacity
                                ? 'text-red-600'
                                : isNearCapacity
                                  ? 'text-yellow-500'
                                  : 'text-[var(--primary-color)]'
                            }`}>
                              {occupancyRate.toFixed(0)}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-xs sm:text-sm p-2 sm:p-4">
                          <div className="flex flex-col sm:flex-row gap-2 justify-end">
                            <OptimizedActionButtonGroup
                              entityType="classes"
                              onEdit={() => {
                                setSelectedClass(cls)
                                setIsEditDialogOpen(true)
                              }}
                              onDelete={() => {
                                setSelectedClass(cls)
                                setIsDeleteDialogOpen(true)
                              }}
                              showEdit={true}
                              showDelete={true}
                              className="gap-2"
                            />
                            <PermissionGuard requiredPermission="admin.classes.students">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedClass(cls)
                                  setIsStudentDistributionDialogOpen(true)
                                }}
                                className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 flex items-center gap-1 text-xs"
                              >
                                <FaEdit className="ml-1" />
                                <span className="sm:inline">توزيع الطلاب</span>
                              </Button>
                            </PermissionGuard>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
              </TableBody>
            </Table>
          </div>
        </div>
      )}

      {activeTab === 'schedules' && (
        <PermissionGuard requiredPermission="admin.classes.schedules">
          <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef] p-3 sm:p-6">
          <div className="text-center py-4 sm:py-8">
            <h3 className="text-lg sm:text-xl font-bold text-[var(--primary-color)] mb-2 sm:mb-4">جداول الحصص التفاعلية</h3>
            <p className="text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6">قم باختيار فصل لعرض وإدارة جدول الحصص الخاص به</p>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mt-4 sm:mt-6">
              {classes.length === 0 ? (
                <div className="col-span-full text-center py-4 text-gray-500 text-sm">
                  لا توجد أقسام متاحة
                </div>
              ) : (
                classes.map(cls => (
                  <div
                    key={cls.id}
                    className="border border-[#e0f2ef] rounded-lg p-3 sm:p-4 hover:bg-[#f8fffd] cursor-pointer transition-all duration-300 shadow-sm hover:shadow"
                    onClick={() => {
                      setSelectedClass(cls);
                      setIsScheduleDialogOpen(true);
                    }}
                  >
                    <h4 className="font-bold text-[var(--primary-color)] mb-1 sm:mb-2 text-sm sm:text-base">{cls.name}</h4>
                    <p className="text-xs sm:text-sm text-gray-500">
                      {Array.isArray(cls.students) ? cls.students.length : 0} طالب | {cls.classSubjects?.length || 0} مادة
                    </p>
                  </div>
                ))
              )}
            </div>
          </div>
          </div>
        </PermissionGuard>
      )}

      {activeTab === 'reports' && (
        <PermissionGuard requiredPermission="admin.reports.view">
          <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef] p-3 sm:p-6">
          <div className="text-center py-4 sm:py-8">
            <h3 className="text-lg sm:text-xl font-bold text-[var(--primary-color)] mb-2 sm:mb-4">تقارير أداء الفصول</h3>
            <p className="text-xs sm:text-sm text-gray-500 mb-4 sm:mb-6">قم باختيار فصل لعرض تقارير الأداء الخاصة به</p>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mt-4 sm:mt-6">
              {classes.length === 0 ? (
                <div className="col-span-full text-center py-4 text-gray-500 text-sm">
                  لا توجد أقسام متاحة
                </div>
              ) : (
                classes.map(cls => (
                  <div
                    key={cls.id}
                    className="border border-[#e0f2ef] rounded-lg p-3 sm:p-4 hover:bg-[#f8fffd] cursor-pointer transition-all duration-300 shadow-sm hover:shadow"
                    onClick={() => {
                      setSelectedClass(cls);
                      setIsClassReportDialogOpen(true);
                    }}
                  >
                    <h4 className="font-bold text-[var(--primary-color)] mb-1 sm:mb-2 text-sm sm:text-base">{cls.name}</h4>
                    <p className="text-xs sm:text-sm text-gray-500">
                      {Array.isArray(cls.students) ? cls.students.length : 0} طالب | {cls.classSubjects?.length || 0} مادة
                    </p>
                  </div>
                ))
              )}
            </div>
          </div>
          </div>
        </PermissionGuard>
      )}

      <AddClassModal
        isOpen={isAddDialogOpen}
        onCloseAction={() => setIsAddDialogOpen(false)}
        onSuccessAction={fetchClasses}
      />

      {/* Edit Dialog */}
      <EditClassModal
        isOpen={isEditDialogOpen}
        onCloseAction={() => setIsEditDialogOpen(false)}
        onSuccessAction={fetchClasses}
        classItem={selectedClass as Class}
      />

      {/* Student Distribution Dialog */}
      <StudentDistributionModal
        isOpen={isStudentDistributionDialogOpen}
        onCloseAction={() => setIsStudentDistributionDialogOpen(false)}
        onSuccessAction={fetchClasses}
        classItem={selectedClass}
        allClasses={classes}
      />

      {/* Class Schedule Dialog */}
      <ClassScheduleModal
        isOpen={isScheduleDialogOpen}
        onCloseAction={() => setIsScheduleDialogOpen(false)}
        onSuccessAction={fetchClasses}
        classItem={selectedClass}
      />

      {/* Class Report Dialog */}
      <ClassReportModal
        isOpen={isClassReportDialogOpen}
        onCloseAction={() => setIsClassReportDialogOpen(false)}
        classItem={selectedClass}
      />

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] bg-[#fff8f8] border border-red-200 p-3 sm:p-6" dir="rtl">
          <DialogHeader>
            <DialogTitle className="text-red-600 font-bold text-lg sm:text-xl flex items-center">
              <FaTrash className="ml-2" />
              تأكيد الحذف
            </DialogTitle>
          </DialogHeader>
          <div className="py-2 sm:py-4">
            <p className="text-sm sm:text-base">هل أنت متأكد من رغبتك في حذف هذا القسم؟</p>
            {selectedClass && (
              <div className="mt-3 sm:mt-4 p-3 sm:p-4 bg-red-50 rounded-md border border-red-200">
                <p className="text-xs sm:text-sm"><strong>اسم القسم:</strong> {selectedClass.name}</p>
                <p className="text-xs sm:text-sm"><strong>عدد الطلاب:</strong> {Array.isArray(selectedClass.students) ? selectedClass.students.length : 0}</p>
                <p className="text-red-600 text-xs sm:text-sm mt-2">
                  سيتم حذف جميع البيانات المتعلقة بهذا القسم. هذه العملية لا يمكن التراجع عنها.
                </p>
              </div>
            )}
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100 text-xs sm:text-sm w-full sm:w-auto"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white shadow-md hover:shadow-lg transition-all duration-300 text-xs sm:text-sm w-full sm:w-auto"
            >
              <FaTrash className="ml-2" />
              تأكيد الحذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  )
}

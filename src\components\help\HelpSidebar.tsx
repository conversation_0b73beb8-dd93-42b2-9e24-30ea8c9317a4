'use client';

import React from 'react';
import {
  BookOpen,
  User,
  FileText,
  Video,
  Award,
  BarChart,
  HelpCircle,
  Settings,
  Users,
  Gift,
  Bell,
  Image
} from 'lucide-react';

export interface HelpCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
}

interface HelpSidebarProps {
  categories: HelpCategory[];
  activeCategory: string;
  onCategoryChange: (categoryId: string) => void;
  className?: string;
}

export const helpCategories: HelpCategory[] = [
  {
    id: 'getting-started',
    title: 'البدء مع النظام',
    icon: BookOpen,
    description: 'دليل شامل للمدير لفهم لوحة التحكم'
  },
  {
    id: 'user-management',
    title: 'إدارة المستخدمين',
    icon: Users,
    description: 'إدارة الطلاب والمعلمين وأولياء الأمور'
  },
  {
    id: 'evaluation-system',
    title: 'نظام التقييم والامتحانات',
    icon: FileText,
    description: 'إنشاء وإدارة الامتحانات وبنوك الأسئلة'
  },
  {
    id: 'remote-classes',
    title: 'الفصول الافتراضية والحضور',
    icon: Video,
    description: 'إدارة الفصول الافتراضية ونظام الحضور'
  },
  {
    id: 'khatm-sessions',
    title: 'مجالس الختم ولوحة الشرف',
    icon: Award,
    description: 'إدارة مجالس الختم وتكريم المتميزين'
  },
  {
    id: 'account-management',
    title: 'إدارة الحساب الشخصي',
    icon: User,
    description: 'إعدادات الحساب والملف الشخصي'
  },
  {
    id: 'reports-analytics',
    title: 'التقارير والإحصائيات',
    icon: BarChart,
    description: 'إنشاء وفهم التقارير المختلفة'
  },
  {
    id: 'activities-rewards',
    title: 'الأنشطة والمكافآت',
    icon: Gift,
    description: 'إدارة الأنشطة وأنظمة المكافآت'
  },
  {
    id: 'notifications-content',
    title: 'الإشعارات وإدارة المحتوى',
    icon: Bell,
    description: 'إدارة الإشعارات والمحتوى والصور'
  },
  {
    id: 'system-settings',
    title: 'إعدادات النظام والمالية',
    icon: Settings,
    description: 'تخصيص النظام والإدارة المالية'
  },
  {
    id: 'faq',
    title: 'الأسئلة الشائعة والدعم',
    icon: HelpCircle,
    description: 'إجابات شاملة والدعم التقني'
  }
];

export default function HelpSidebar({
  categories,
  activeCategory,
  onCategoryChange,
  className = ''
}: HelpSidebarProps) {
  return (
    <aside className={`w-80 help-sidebar ${className}`}>
      <div className="help-sidebar-header">
        <div className="flex items-center gap-3 mb-2">
          <div className="w-10 h-10 bg-white/60 rounded-xl flex items-center justify-center shadow-sm">
            <span className="text-xl">📚</span>
          </div>
          <div>
            <h2 className="text-xl font-bold">مواضيع المساعدة</h2>
            <p className="text-sm opacity-80">+100 موضوع شامل</p>
          </div>
        </div>
        <div className="bg-white/40 rounded-lg p-3 mt-4 border border-white/30">
          <p className="text-sm leading-relaxed opacity-90">
            اختر أي موضوع للحصول على دليل مفصل مع خطوات عملية ونصائح مفيدة
          </p>
        </div>
      </div>

      <nav className="p-4">
        <ul className="space-y-1">
          {categories.map((category, index) => {
            const IconComponent = category.icon;
            const isActive = activeCategory === category.id;

            return (
              <li key={category.id} style={{ animationDelay: `${index * 0.1}s` }} className="help-fade-in">
                <button
                  onClick={() => onCategoryChange(category.id)}
                  className={`help-sidebar-item w-full text-right p-4 rounded-xl ${
                    isActive ? 'active' : ''
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 ${
                      isActive
                        ? 'bg-[var(--primary-color)] text-white shadow-lg'
                        : 'bg-[var(--primary-color)]/10 text-[var(--primary-color)]'
                    }`}>
                      <IconComponent size={20} />
                    </div>
                    <div className="flex-1 text-right">
                      <div className={`help-sidebar-title font-semibold text-sm ${
                        isActive ? 'text-[var(--primary-color)]' : 'text-gray-900'
                      }`}>
                        {category.title}
                      </div>
                      <div className={`help-sidebar-description text-xs mt-1 ${
                        isActive ? 'text-[var(--primary-color)]/80' : 'text-gray-500'
                      }`}>
                        {category.description}
                      </div>
                    </div>
                    {isActive && (
                      <div className="w-2 h-2 bg-[var(--primary-color)] rounded-full animate-pulse"></div>
                    )}
                  </div>
                </button>
              </li>
            );
          })}
        </ul>

        {/* إحصائيات سريعة */}
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-xl border border-blue-100">
          <h3 className="font-semibold text-gray-800 mb-3 text-sm">📊 إحصائيات المساعدة</h3>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-600">إجمالي المواضيع:</span>
              <span className="font-bold text-[var(--primary-color)]">+100</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">الخطوات العملية:</span>
              <span className="font-bold text-[var(--primary-color)]">+500</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">النصائح المفيدة:</span>
              <span className="font-bold text-[var(--primary-color)]">+200</span>
            </div>
          </div>
        </div>
      </nav>
    </aside>
  );
}

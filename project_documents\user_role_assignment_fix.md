# إصلاح مشكلة عدم حفظ دور المستخدم عند التسجيل

## وصف المشكلة
عند تسجيل مستخدم جديد من القائمة المنبثقة في صفحة إدارة المستخدمين، لا يتم حفظ الدور المحدد من القائمة المنسدلة رغم اختياره.

## تحليل المشكلة

### المشاكل المكتشفة:
1. **عدم التحقق من الدور في النموذج**: دالة `validateForm` في `UserForm.tsx` لم تكن تتحقق من أن الدور مطلوب
2. **عدم عرض رسائل خطأ الدور**: لم تكن هناك رسالة خطأ تظهر للمستخدم عند عدم اختيار دور
3. **مشكلة في API**: API إنشاء المستخدم كان يتعامل فقط مع الأدوار الأساسية ولا يدعم الأدوار المخصصة
4. **عدم حفظ الدور المخصص**: لم يكن يتم حفظ `roleId` للأدوار المخصصة

## الحلول المطبقة

### T01: إصلاح التحقق من صحة النموذج
- [x] **T01.01: إضافة التحقق من الدور المطلوب**
  - **الملف:** `src/app/admin/users/components/UserForm.tsx`
  - **التغيير:** إضافة التحقق من `formData.role` في دالة `validateForm`
  - **الكود:**
    ```typescript
    if (!formData.role || formData.role.trim() === '') {
      newErrors.role = 'الدور مطلوب';
    }
    ```

- [x] **T01.02: إضافة عرض رسالة خطأ الدور**
  - **الملف:** `src/app/admin/users/components/UserForm.tsx`
  - **التغيير:** إضافة عرض `errors.role` في واجهة المستخدم
  - **الكود:**
    ```typescript
    {errors.role && <p className="text-red-500 text-sm mt-1 flex items-center gap-1 error-message">
      <span className="text-xs">⚠️</span>
      {errors.role}
    </p>}
    ```

### T02: تحسين إرسال البيانات
- [x] **T02.01: تبسيط إرسال الدور**
  - **الملف:** `src/app/admin/users/components/UserForm.tsx`
  - **التغيير:** إرسال الدور مباشرة كما هو محدد في النموذج
  - **إزالة:** المنطق المعقد لتحديد الدور الأساسي في العميل

### T03: تحسين API إنشاء المستخدم
- [x] **T03.01: دعم الأدوار المخصصة**
  - **الملف:** `src/app/api/users/create/route.ts`
  - **التغيير:** إضافة منطق للتعامل مع الأدوار المخصصة
  - **الميزات:**
    - التحقق من الأدوار الأساسية أولاً
    - البحث عن الأدوار المخصصة في قاعدة البيانات
    - تحديد الدور الأساسي المناسب للأدوار المخصصة
    - حفظ `roleId` للأدوار المخصصة

- [x] **T03.02: تحسين معالجة الأخطاء**
  - **الملف:** `src/app/api/users/create/route.ts`
  - **التغيير:** إضافة رسائل خطأ واضحة ومفصلة
  - **إضافة:** console.log للتتبع والتشخيص

### T04: إضافة التتبع والتشخيص
- [x] **T04.01: إضافة console.log للتتبع**
  - **الملفات:** `UserForm.tsx` و `route.ts`
  - **الهدف:** تسهيل تشخيص المشاكل المستقبلية
  - **المعلومات المتتبعة:**
    - بيانات النموذج المرسلة
    - الدور المحدد
    - البيانات المستلمة في API
    - الدور النهائي المحدد

## الكود المحدث

### UserForm.tsx - دالة التحقق المحدثة:
```typescript
const validateForm = () => {
  const newErrors: Record<string, string> = {};

  if (!formData.name.trim()) {
    newErrors.name = 'الاسم مطلوب';
  }

  if (!isEdit && !formData.username.trim()) {
    newErrors.username = 'اسم المستخدم مطلوب';
  }

  if (!formData.role || formData.role.trim() === '') {
    newErrors.role = 'الدور مطلوب';
  }

  // باقي التحققات...

  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};
```

### API route.ts - معالجة الأدوار المحدثة:
```typescript
// التحقق من صحة الدور
let userRole: UserRole;
const basicRoles = ['ADMIN', 'TEACHER', 'STUDENT', 'PARENT', 'EMPLOYEE', 'PENDING'];

if (basicRoles.includes(body.role.toUpperCase())) {
    // دور أساسي
    switch (body.role.toUpperCase()) {
        case 'ADMIN': userRole = UserRole.ADMIN; break;
        case 'TEACHER': userRole = UserRole.TEACHER; break;
        // ... باقي الأدوار
    }
} else {
    // دور مخصص - تحديد الدور الأساسي المناسب
    const customRole = await prisma.role.findUnique({
        where: { name: body.role }
    });

    if (!customRole || !customRole.isActive) {
        return NextResponse.json(
            { message: "الدور المحدد غير موجود أو غير نشط" },
            { status: 400 }
        );
    }

    // تحديد الدور الأساسي
    const { determineBaseRole } = await import('@/utils/roleUtils');
    const allRoles = await prisma.role.findMany({ where: { isActive: true } });
    const baseRoleName = determineBaseRole(customRole, allRoles);
    // ... تحويل إلى UserRole enum
}
```

## النتائج المتوقعة
1. **التحقق الصحيح**: النموذج يتحقق من اختيار الدور قبل الإرسال
2. **رسائل خطأ واضحة**: المستخدم يرى رسالة خطأ إذا لم يختر دور
3. **دعم الأدوار المخصصة**: API يدعم الأدوار الأساسية والمخصصة
4. **حفظ صحيح**: يتم حفظ الدور المحدد بشكل صحيح في قاعدة البيانات
5. **تتبع أفضل**: console.log يساعد في تشخيص المشاكل

## إصلاح خطأ Prisma Schema
- [x] **T05.01: إصلاح مرجع العلاقة في API**
  - **المشكلة:** استخدام `customRole` بدلاً من `userRole` في include statement
  - **الحل:** تغيير `customRole: true` إلى `userRole: true` في Prisma query
  - **الملف:** `src/app/api/users/create/route.ts`

## اختبار الإصلاح
1. فتح صفحة إدارة المستخدمين
2. النقر على "إضافة مستخدم"
3. ملء البيانات المطلوبة
4. اختيار دور من القائمة المنسدلة
5. النقر على "إضافة المستخدم"
6. التحقق من حفظ الدور بشكل صحيح

## إصلاح مشكلة تعيين الدور للمستخدمين الجدد
- [x] **T06.01: إصلاح تعيين roleId للمستخدمين الجدد**
  - **المشكلة:** المستخدمون الجدد المضافون من قبل المسؤول لا يحصلون على roleId
  - **الحل:** تحسين منطق البحث عن الأدوار وتعيين roleId تلقائياً
  - **الملف:** `src/app/api/users/create/route.ts`
  - **السلوك الأمني:**
    - التسجيل من الإنترنت: يبقى PENDING بدون roleId (أمان)
    - الإضافة من المسؤول: يحصل على roleId مباشرة (فعالية)

## الحالة النهائية
✅ **تم إصلاح المشكلة بالكامل**
- التحقق من صحة النموذج يعمل بشكل صحيح
- رسائل الخطأ تظهر للمستخدم
- API يدعم الأدوار الأساسية والمخصصة
- تم إصلاح خطأ Prisma Schema
- الدور يتم حفظه بشكل صحيح في قاعدة البيانات
- **جديد:** المستخدمون المضافون من المسؤول يحصلون على roleId مباشرة

## إصلاح مشكلة تحديث الأدوار بعد تغيير هيكل البيانات
- [x] **T07.01: إصلاح UserRoleManager لاستخدام originalId**
  - **المشكلة:** تغيير `user.id` من `number` إلى `string` كسر وظائف تحديث الأدوار
  - **الحل:** تحديث المكونات لاستخدام `user.originalId` في API calls
  - **الملف:** `src/app/admin/users/components/UserRoleManager.tsx`

- [x] **T07.02: إصلاح NewPermissionsManager لاستخدام originalId**
  - **المشكلة:** نفس المشكلة في مكون إدارة الأدوار والصلاحيات
  - **الحل:** تحديث المكون لاستخدام `user.originalId` في API calls
  - **الملف:** `src/app/admin/users/components/NewPermissionsManager.tsx`

## ملاحظات
- تم الاحتفاظ بالتوافق مع النظام الحالي
- الإصلاح يدعم الأدوار الأساسية والمخصصة
- تم إضافة تتبع مفصل لعملية تعيين الأدوار
- السلوك الأمني محفوظ: التسجيل العام يبقى PENDING
- **جديد:** تم إصلاح مشكلة تحديث الأدوار بعد تغيير هيكل البيانات

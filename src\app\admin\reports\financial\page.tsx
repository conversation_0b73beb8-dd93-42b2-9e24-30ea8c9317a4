'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-toastify';
import {
  FaChartBar,
  FaMoneyBillWave,
  FaFileInvoiceDollar,
  FaHandHoldingUsd,
  FaArrowUp,
  FaArrowDown,
  FaPercent,
  FaCalendarAlt,
  FaFileExcel,
  FaFilePdf
} from 'react-icons/fa';
import { exportToPdf, exportToExcel } from '@/utils/export-utils';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { Bar, Pie, Line } from 'react-chartjs-2';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

// تسجيل مكونات الرسوم البيانية
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  PointElement,
  LineElement
);

// تعريف واجهات البيانات
interface Student {
  id: number;
  name: string;
}

interface PaymentMethod {
  id: number;
  name: string;
}

interface Discount {
  id: number;
  name: string;
  type: string;
  value: number;
  isActive: boolean;
}

interface Payment {
  id: number;
  amount: number;
  date: string;
  student: Student;
  paymentMethod?: PaymentMethod;
  discount?: Discount;
}

interface Donation {
  id: number;
  amount: number;
  date: string;
  donorName: string | null;
  paymentMethod?: PaymentMethod;
}

interface Expense {
  id: number;
  amount: number;
  date: string;
  purpose: string;
  category?: {
    id: number;
    name: string;
  };
}

interface Income {
  id: number;
  amount: number;
  date: string;
  source: string;
}

interface FinancialReport {
  generalStats: {
    balance: number;
    totalIncome: number;
    totalExpense: number;
    netProfit: number;
  };
  periodTotals: {
    totalPayments: number;
    totalDonations: number;
    totalExpenses: number;
    totalIncomes: number;
    totalDiscounts: number;
  };
  paymentMethodStats: Array<{
    id: number;
    name: string;
    paymentsCount: number;
    donationsCount: number;
    paymentsAmount: number;
    donationsAmount: number;
    totalAmount: number;
  }>;
  discountStats: Array<{
    id: number;
    name: string;
    paymentsCount: number;
    invoicesCount: number;
    totalDiscountAmount: number;
    type: string;
    value: number;
    isActive: boolean;
  }>;
  monthlyStats: Array<{
    month: string;
    payments: {
      count: number;
      amount: number;
    };
    donations: {
      count: number;
      amount: number;
    };
    expenses: {
      count: number;
      amount: number;
    };
    totalIncome: number;
    netProfit: number;
  }>;
  payments: Array<Payment>;
  donations: Array<Donation>;
  expenses: Array<Expense>;
  incomes: Array<Income>;
  period: {
    startDate: string;
    endDate: string;
  };
}

export default function FinancialReportsPage() {
  const [startDate, setStartDate] = useState<string>(() => {
    const date = new Date();
    date.setMonth(date.getMonth() - 1);
    return date.toISOString().split('T')[0];
  });
  const [endDate, setEndDate] = useState<string>(() => {
    const date = new Date();
    return date.toISOString().split('T')[0];
  });
  const [reportType, setReportType] = useState<string>('all');
  const [loading, setLoading] = useState<boolean>(false);
  const [report, setReport] = useState<FinancialReport | null>(null);

  // جلب التقارير المالية
  const fetchFinancialReport = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        startDate,
        endDate,
        type: reportType
      });

      const response = await fetch(`/api/admin/financial-reports?${queryParams}`);
      if (!response.ok) {
        throw new Error('فشل في جلب التقارير المالية');
      }

      const result = await response.json();
      if (result.success) {
        setReport(result.data);
      } else {
        throw new Error(result.error || 'فشل في جلب التقارير المالية');
      }
    } catch (error) {
      console.error('Error fetching financial report:', error);
      toast.error('فشل في جلب التقارير المالية');
    } finally {
      setLoading(false);
    }
  };

  // جلب التقارير عند تحميل الصفحة
  useEffect(() => {
    // تحميل البيانات عند تحميل الصفحة فقط
    // لتحميل البيانات عند تغيير المعايير، يستخدم المستخدم زر "عرض التقرير"
    fetchFinancialReport();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // تصدير التقرير إلى Excel
  const handleExportToExcel = () => {
    if (!report) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // إعداد بيانات الإحصائيات العامة
      const generalStatsData = [
        { المؤشر: 'الرصيد الحالي', القيمة: formatCurrency(report.generalStats.balance) },
        { المؤشر: 'إجمالي الإيرادات', القيمة: formatCurrency(report.generalStats.totalIncome) },
        { المؤشر: 'إجمالي المصروفات', القيمة: formatCurrency(report.generalStats.totalExpense) },
        { المؤشر: 'صافي الربح', القيمة: formatCurrency(report.generalStats.netProfit) }
      ];

      // إعداد بيانات إحصائيات الفترة
      const periodStatsData = [
        { المؤشر: 'إجمالي المدفوعات', القيمة: formatCurrency(report.periodTotals.totalPayments), العدد: report.payments.length },
        { المؤشر: 'إجمالي التبرعات', القيمة: formatCurrency(report.periodTotals.totalDonations), العدد: report.donations.length },
        { المؤشر: 'إجمالي المصروفات', القيمة: formatCurrency(report.periodTotals.totalExpenses), العدد: report.expenses.length },
        { المؤشر: 'إجمالي المداخيل الأخرى', القيمة: formatCurrency(report.periodTotals.totalIncomes), العدد: report.incomes.length },
        { المؤشر: 'إجمالي الخصومات', القيمة: formatCurrency(report.periodTotals.totalDiscounts), العدد: report.discountStats.length }
      ];

      // إعداد بيانات طرق الدفع
      const paymentMethodsData = report.paymentMethodStats.map(method => ({
        'طريقة الدفع': method.name,
        'عدد المدفوعات': method.paymentsCount,
        'عدد التبرعات': method.donationsCount,
        'مبلغ المدفوعات': formatCurrency(method.paymentsAmount),
        'مبلغ التبرعات': formatCurrency(method.donationsAmount),
        'المبلغ الإجمالي': formatCurrency(method.totalAmount)
      }));

      // إعداد بيانات الخصومات
      const discountsData = report.discountStats.map(discount => ({
        'اسم الخصم': discount.name,
        'عدد المدفوعات': discount.paymentsCount,
        'عدد الفواتير': discount.invoicesCount,
        'قيمة الخصم': discount.type === 'PERCENTAGE' ? `${discount.value}%` : formatCurrency(discount.value),
        'إجمالي قيمة الخصومات': formatCurrency(discount.totalDiscountAmount),
        'نشط': discount.isActive ? 'نعم' : 'لا'
      }));

      // إعداد بيانات الإحصائيات الشهرية
      const monthlyStatsData = report.monthlyStats.map(stat => {
        const date = new Date(stat.month);
        return {
          'الشهر': date.toLocaleDateString('ar', { month: 'long', year: 'numeric' }),
          'عدد المدفوعات': stat.payments.count,
          'مبلغ المدفوعات': formatCurrency(stat.payments.amount),
          'عدد التبرعات': stat.donations.count,
          'مبلغ التبرعات': formatCurrency(stat.donations.amount),
          'عدد المصروفات': stat.expenses.count,
          'مبلغ المصروفات': formatCurrency(stat.expenses.amount),
          'إجمالي الإيرادات': formatCurrency(stat.totalIncome),
          'صافي الربح': formatCurrency(stat.netProfit)
        };
      });

      // تصدير البيانات إلى Excel
      exportToExcel(
        [
          ...generalStatsData,
          { المؤشر: '', القيمة: '' }, // سطر فارغ للفصل
          ...periodStatsData,
          { المؤشر: '', القيمة: '' }, // سطر فارغ للفصل
          ...paymentMethodsData,
          { 'طريقة الدفع': '', 'عدد المدفوعات': '' }, // سطر فارغ للفصل
          ...discountsData,
          { 'اسم الخصم': '', 'عدد المدفوعات': '' }, // سطر فارغ للفصل
          ...monthlyStatsData
        ],
        `التقرير_المالي_${new Date().toISOString().split('T')[0]}.xlsx`,
        'التقرير المالي'
      );
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تصدير التقرير إلى PDF
  const handleExportToPdf = () => {
    if (!report) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // إعداد بيانات الإحصائيات العامة
      const generalStatsTable = {
        title: 'الإحصائيات العامة',
        headers: ['المؤشر', 'القيمة'],
        data: [
          ['الرصيد الحالي', formatCurrency(report.generalStats.balance)],
          ['إجمالي الإيرادات', formatCurrency(report.generalStats.totalIncome)],
          ['إجمالي المصروفات', formatCurrency(report.generalStats.totalExpense)],
          ['صافي الربح', formatCurrency(report.generalStats.netProfit)]
        ]
      };

      // إعداد بيانات إحصائيات الفترة
      const periodStatsTable = {
        title: `إحصائيات الفترة (${new Date(report.period.startDate).toLocaleDateString('ar')} - ${new Date(report.period.endDate).toLocaleDateString('ar')})`,
        headers: ['المؤشر', 'القيمة', 'العدد'],
        data: [
          ['إجمالي المدفوعات', formatCurrency(report.periodTotals.totalPayments), report.payments.length],
          ['إجمالي التبرعات', formatCurrency(report.periodTotals.totalDonations), report.donations.length],
          ['إجمالي المصروفات', formatCurrency(report.periodTotals.totalExpenses), report.expenses.length],
          ['إجمالي المداخيل الأخرى', formatCurrency(report.periodTotals.totalIncomes), report.incomes.length],
          ['إجمالي الخصومات', formatCurrency(report.periodTotals.totalDiscounts), report.discountStats.length]
        ]
      };

      // إعداد بيانات طرق الدفع
      const paymentMethodsTable = {
        title: 'إحصائيات طرق الدفع',
        headers: ['طريقة الدفع', 'عدد المدفوعات', 'عدد التبرعات', 'مبلغ المدفوعات', 'مبلغ التبرعات', 'المبلغ الإجمالي'],
        data: report.paymentMethodStats.map(method => [
          method.name,
          method.paymentsCount,
          method.donationsCount,
          formatCurrency(method.paymentsAmount),
          formatCurrency(method.donationsAmount),
          formatCurrency(method.totalAmount)
        ])
      };

      // إعداد بيانات الخصومات
      const discountsTable = {
        title: 'إحصائيات الخصومات',
        headers: ['اسم الخصم', 'عدد المدفوعات', 'عدد الفواتير', 'قيمة الخصم', 'إجمالي قيمة الخصومات', 'نشط'],
        data: report.discountStats.map(discount => [
          discount.name,
          discount.paymentsCount,
          discount.invoicesCount,
          discount.type === 'PERCENTAGE' ? `${discount.value}%` : formatCurrency(discount.value),
          formatCurrency(discount.totalDiscountAmount),
          discount.isActive ? 'نعم' : 'لا'
        ])
      };

      // إعداد بيانات الإحصائيات الشهرية
      const monthlyStatsTable = {
        title: 'الإحصائيات الشهرية',
        headers: ['الشهر', 'عدد المدفوعات', 'مبلغ المدفوعات', 'عدد التبرعات', 'مبلغ التبرعات', 'عدد المصروفات', 'مبلغ المصروفات', 'إجمالي الإيرادات', 'صافي الربح'],
        data: report.monthlyStats.map(stat => {
          const date = new Date(stat.month);
          return [
            date.toLocaleDateString('ar', { month: 'long', year: 'numeric' }),
            stat.payments.count,
            formatCurrency(stat.payments.amount),
            stat.donations.count,
            formatCurrency(stat.donations.amount),
            stat.expenses.count,
            formatCurrency(stat.expenses.amount),
            formatCurrency(stat.totalIncome),
            formatCurrency(stat.netProfit)
          ];
        })
      };

      // إعداد بيانات الرسوم البيانية
      const charts = [
        {
          title: 'الإيرادات والمصروفات الشهرية',
          type: 'bar' as const,
          data: {
            labels: report.monthlyStats.map(stat => {
              const date = new Date(stat.month);
              return date.toLocaleDateString('ar', { month: 'long', year: 'numeric' });
            }),
            datasets: [
              {
                label: 'الإيرادات',
                data: report.monthlyStats.map(stat => stat.totalIncome),
                backgroundColor: 'rgba(22, 155, 136, 0.5)',
                borderColor: 'var(--primary-color)',
                borderWidth: 1,
              },
              {
                label: 'المصروفات',
                data: report.monthlyStats.map(stat => stat.expenses.amount),
                backgroundColor: 'rgba(231, 76, 60, 0.5)',
                borderColor: '#e74c3c',
                borderWidth: 1,
              },
            ],
          }
        },
        {
          title: 'الربح الصافي الشهري',
          type: 'line' as const,
          data: {
            labels: report.monthlyStats.map(stat => {
              const date = new Date(stat.month);
              return date.toLocaleDateString('ar', { month: 'long', year: 'numeric' });
            }),
            datasets: [
              {
                label: 'الربح الصافي',
                data: report.monthlyStats.map(stat => stat.netProfit),
                backgroundColor: 'rgba(52, 152, 219, 0.2)',
                borderColor: '#3498db',
                borderWidth: 2,
              },
            ],
          }
        },
        {
          title: 'توزيع المدفوعات حسب طريقة الدفع',
          type: 'pie' as const,
          data: {
            labels: report.paymentMethodStats.map(method => method.name),
            datasets: [
              {
                label: 'المبلغ الإجمالي',
                data: report.paymentMethodStats.map(method => method.totalAmount),
                backgroundColor: [
                  'var(--primary-color)',
                  '#3498db',
                  '#9b59b6',
                  '#e67e22',
                  '#f1c40f',
                  '#1abc9c',
                  '#34495e',
                ],
                borderWidth: 1,
              },
            ],
          }
        }
      ];

      // تصدير البيانات إلى PDF
      exportToPdf({
        title: 'التقرير المالي',
        fileName: `التقرير_المالي_${new Date().toISOString().split('T')[0]}.pdf`,
        tables: [
          generalStatsTable,
          periodStatsTable,
          paymentMethodsTable,
          discountsTable,
          monthlyStatsTable
        ],
        charts: charts
      });
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تنسيق المبالغ
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('ar-DZ', { style: 'currency', currency: 'DZD' })
      .replace(/\s+/g, ' ')
      .replace('DZD', 'د.ج');
  };

  // بيانات الرسم البياني للإيرادات والمصروفات
  const incomeExpenseData = {
    labels: report?.monthlyStats.map(stat => {
      const date = new Date(stat.month);
      return date.toLocaleDateString('ar', { month: 'long', year: 'numeric' });
    }) || [],
    datasets: [
      {
        label: 'الإيرادات',
        data: report?.monthlyStats.map(stat => stat.totalIncome) || [],
        backgroundColor: 'rgba(22, 155, 136, 0.5)',
        borderColor: 'var(--primary-color)',
        borderWidth: 1,
      },
      {
        label: 'المصروفات',
        data: report?.monthlyStats.map(stat => stat.expenses.amount) || [],
        backgroundColor: 'rgba(231, 76, 60, 0.5)',
        borderColor: '#e74c3c',
        borderWidth: 1,
      },
    ],
  };

  // بيانات الرسم البياني للربح الصافي
  const netProfitData = {
    labels: report?.monthlyStats.map(stat => {
      const date = new Date(stat.month);
      return date.toLocaleDateString('ar', { month: 'long', year: 'numeric' });
    }) || [],
    datasets: [
      {
        label: 'الربح الصافي',
        data: report?.monthlyStats.map(stat => stat.netProfit) || [],
        backgroundColor: 'rgba(52, 152, 219, 0.2)',
        borderColor: '#3498db',
        borderWidth: 2,
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // بيانات الرسم البياني لطرق الدفع
  const paymentMethodData = {
    labels: report?.paymentMethodStats.map(method => method.name) || [],
    datasets: [
      {
        label: 'المبلغ الإجمالي',
        data: report?.paymentMethodStats.map(method => method.totalAmount) || [],
        backgroundColor: [
          'var(--primary-color)',
          '#3498db',
          '#9b59b6',
          '#e67e22',
          '#f1c40f',
          '#1abc9c',
          '#34495e',
        ],
        borderWidth: 1,
      },
    ],
  };

  // بيانات الرسم البياني للخصومات
  const discountData = {
    labels: report?.discountStats.map(discount => discount.name) || [],
    datasets: [
      {
        label: 'قيمة الخصومات',
        data: report?.discountStats.map(discount => discount.totalDiscountAmount) || [],
        backgroundColor: [
          '#e74c3c',
          '#f39c12',
          '#2ecc71',
          '#3498db',
          '#9b59b6',
          '#1abc9c',
          '#34495e',
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.reports.financial">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaChartBar className="text-[var(--primary-color)]" />
          التقارير المالية
        </h1>
        <QuickActionButtons
          entityType="reports.financial"
          actions={[
            {
              key: 'export-excel',
              label: 'تصدير Excel',
              icon: <FaFileExcel />,
              onClick: handleExportToExcel,
              variant: 'primary'
            },
            {
              key: 'export-pdf',
              label: 'تصدير PDF',
              icon: <FaFilePdf />,
              onClick: handleExportToPdf,
              variant: 'danger'
            }
          ]}
          className="flex gap-2 w-full sm:w-auto"
        />
      </div>

      {/* فلاتر التقرير */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="flex flex-col">
            <Label htmlFor="startDate" className="mb-2">تاريخ البداية</Label>
            <div className="flex items-center">
              <FaCalendarAlt className="text-[var(--primary-color)] ml-2 min-w-5" />
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="border-[var(--primary-color)] w-full"
              />
            </div>
          </div>
          <div className="flex flex-col">
            <Label htmlFor="endDate" className="mb-2">تاريخ النهاية</Label>
            <div className="flex items-center">
              <FaCalendarAlt className="text-[var(--primary-color)] ml-2 min-w-5" />
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="border-[var(--primary-color)] w-full"
                min={startDate}
              />
            </div>
          </div>
          <div className="flex flex-col">
            <Label htmlFor="reportType" className="mb-2">نوع التقرير</Label>
            <select
              id="reportType"
              value={reportType}
              onChange={(e) => setReportType(e.target.value)}
              className="border rounded-md p-2 border-[var(--primary-color)] w-full"
            >
              <option value="all">جميع البيانات</option>
              <option value="payments">المدفوعات فقط</option>
              <option value="donations">التبرعات فقط</option>
              <option value="expenses">المصروفات فقط</option>
              <option value="discounts">الخصومات فقط</option>
            </select>
          </div>
          <div className="flex items-end">
            <Button
              onClick={fetchFinancialReport}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full"
              disabled={loading}
            >
              {loading ? 'جاري التحميل...' : 'عرض التقرير'}
            </Button>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto"></div>
            <p className="mt-4 text-[var(--primary-color)]">جاري تحميل التقرير...</p>
          </div>
        </div>
      ) : report ? (
        <div className="space-y-6">
          {/* الإحصائيات العامة */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="border-t-4 border-[var(--primary-color)]">
              <CardHeader className="pb-2">
                <CardTitle className="text-base md:text-lg flex items-center">
                  <FaMoneyBillWave className="ml-2 text-[var(--primary-color)]" />
                  الرصيد الحالي
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl md:text-3xl font-bold">{formatCurrency(report.generalStats.balance)}</div>
                <p className="text-xs md:text-sm text-gray-500">إجمالي الرصيد في الخزينة</p>
              </CardContent>
            </Card>
            <Card className="border-t-4 border-blue-500">
              <CardHeader className="pb-2">
                <CardTitle className="text-base md:text-lg flex items-center">
                  <FaArrowUp className="ml-2 text-blue-500" />
                  إجمالي الإيرادات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl md:text-3xl font-bold text-blue-500">{formatCurrency(report.generalStats.totalIncome)}</div>
                <p className="text-xs md:text-sm text-gray-500">إجمالي الإيرادات منذ البداية</p>
              </CardContent>
            </Card>
            <Card className="border-t-4 border-red-500">
              <CardHeader className="pb-2">
                <CardTitle className="text-base md:text-lg flex items-center">
                  <FaArrowDown className="ml-2 text-red-500" />
                  إجمالي المصروفات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl md:text-3xl font-bold text-red-500">{formatCurrency(report.generalStats.totalExpense)}</div>
                <p className="text-xs md:text-sm text-gray-500">إجمالي المصروفات منذ البداية</p>
              </CardContent>
            </Card>
            <Card className="border-t-4 border-primary-color">
              <CardHeader className="pb-2">
                <CardTitle className="text-base md:text-lg flex items-center">
                  <FaHandHoldingUsd className="ml-2 text-primary-color" />
                  صافي الربح
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl md:text-3xl font-bold text-primary-color">{formatCurrency(report.generalStats.netProfit)}</div>
                <p className="text-xs md:text-sm text-gray-500">صافي الربح (الإيرادات - المصروفات)</p>
              </CardContent>
            </Card>
          </div>

          {/* إحصائيات الفترة */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg md:text-xl flex items-center flex-wrap">
                <FaCalendarAlt className="ml-2 text-[var(--primary-color)]" />
                <span>إحصائيات الفترة</span>
                <span className="text-sm md:text-base mr-1">
                  ({new Date(report.period.startDate).toLocaleDateString('ar')} - {new Date(report.period.endDate).toLocaleDateString('ar')})
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="bg-blue-50 p-3 md:p-4 rounded-lg">
                  <div className="text-blue-500 font-bold text-base md:text-lg">المدفوعات</div>
                  <div className="text-xl md:text-2xl font-bold">{formatCurrency(report.periodTotals.totalPayments)}</div>
                  <div className="text-xs md:text-sm text-gray-500">{report.payments.length} عملية</div>
                </div>
                <div className="bg-green-50 p-3 md:p-4 rounded-lg">
                  <div className="text-primary-color font-bold text-base md:text-lg">التبرعات</div>
                  <div className="text-xl md:text-2xl font-bold">{formatCurrency(report.periodTotals.totalDonations)}</div>
                  <div className="text-xs md:text-sm text-gray-500">{report.donations.length} عملية</div>
                </div>
                <div className="bg-red-50 p-3 md:p-4 rounded-lg">
                  <div className="text-red-500 font-bold text-base md:text-lg">المصروفات</div>
                  <div className="text-xl md:text-2xl font-bold">{formatCurrency(report.periodTotals.totalExpenses)}</div>
                  <div className="text-xs md:text-sm text-gray-500">{report.expenses.length} عملية</div>
                </div>
                <div className="bg-purple-50 p-3 md:p-4 rounded-lg">
                  <div className="text-purple-500 font-bold text-base md:text-lg">المداخيل الأخرى</div>
                  <div className="text-xl md:text-2xl font-bold">{formatCurrency(report.periodTotals.totalIncomes)}</div>
                  <div className="text-xs md:text-sm text-gray-500">{report.incomes.length} عملية</div>
                </div>
                <div className="bg-yellow-50 p-3 md:p-4 rounded-lg">
                  <div className="text-yellow-500 font-bold text-base md:text-lg">الخصومات</div>
                  <div className="text-xl md:text-2xl font-bold">{formatCurrency(report.periodTotals.totalDiscounts)}</div>
                  <div className="text-xs md:text-sm text-gray-500">{report.discountStats.length} خصم</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* الرسوم البيانية */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* رسم بياني للإيرادات والمصروفات */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">الإيرادات والمصروفات الشهرية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-60 md:h-80">
                  <Bar
                    data={incomeExpenseData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                          labels: {
                            boxWidth: 12,
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          ticks: {
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        },
                        x: {
                          ticks: {
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        }
                      },
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* رسم بياني للربح الصافي */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">الربح الصافي الشهري</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-60 md:h-80">
                  <Line
                    data={netProfitData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'top',
                          labels: {
                            boxWidth: 12,
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          ticks: {
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        },
                        x: {
                          ticks: {
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        }
                      },
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* رسم بياني لطرق الدفع */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">توزيع المدفوعات حسب طريقة الدفع</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-60 md:h-80">
                  <Pie
                    data={paymentMethodData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: window.innerWidth < 768 ? 'bottom' : 'right',
                          labels: {
                            boxWidth: 12,
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        },
                      },
                    }}
                  />
                </div>
              </CardContent>
            </Card>

            {/* رسم بياني للخصومات */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl">توزيع الخصومات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-60 md:h-80">
                  <Pie
                    data={discountData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: window.innerWidth < 768 ? 'bottom' : 'right',
                          labels: {
                            boxWidth: 12,
                            font: {
                              size: window.innerWidth < 768 ? 10 : 12
                            }
                          }
                        },
                      },
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* جدول المدفوعات */}
          {report.payments.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl flex items-center">
                  <FaFileInvoiceDollar className="ml-2 text-[var(--primary-color)]" />
                  آخر المدفوعات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="responsive-table-container">
                  <table className="min-w-full bg-white card-mode-table">
                    <thead>
                      <tr className="bg-[var(--primary-color)] text-white">
                        <th className="px-6 py-3 text-right font-semibold">رقم</th>
                        <th className="px-6 py-3 text-right font-semibold">الطالب</th>
                        <th className="px-6 py-3 text-right font-semibold">المبلغ</th>
                        <th className="px-6 py-3 text-right font-semibold">التاريخ</th>
                        <th className="px-6 py-3 text-right font-semibold">طريقة الدفع</th>
                        <th className="px-6 py-3 text-right font-semibold">الخصم</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {report.payments.slice(0, 5).map((payment) => (
                        <tr key={payment.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4" data-label="رقم">{payment.id}</td>
                          <td className="px-6 py-4" data-label="الطالب">{payment.student.name}</td>
                          <td className="px-6 py-4" data-label="المبلغ">{formatCurrency(payment.amount)}</td>
                          <td className="px-6 py-4" data-label="التاريخ">{new Date(payment.date).toLocaleDateString('ar')}</td>
                          <td className="px-6 py-4" data-label="طريقة الدفع">{payment.paymentMethod?.name || '-'}</td>
                          <td className="px-6 py-4" data-label="الخصم">
                            {payment.discount ? (
                              <span className="px-2 py-1 rounded-full bg-yellow-100 text-yellow-800 text-xs">
                                {payment.discount.name}
                              </span>
                            ) : (
                              '-'
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {report.payments.length > 5 && (
                  <div className="mt-4 text-center">
                    <Button variant="outline" className="border-[var(--primary-color)] text-[var(--primary-color)] w-full sm:w-auto">
                      عرض المزيد من المدفوعات
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* جدول الخصومات */}
          {report.discountStats.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg md:text-xl flex items-center">
                  <FaPercent className="ml-2 text-[var(--primary-color)]" />
                  الخصومات المطبقة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="responsive-table-container">
                  <table className="min-w-full bg-white card-mode-table">
                    <thead>
                      <tr className="bg-[var(--primary-color)] text-white">
                        <th className="px-6 py-3 text-right font-semibold">الاسم</th>
                        <th className="px-6 py-3 text-right font-semibold">النوع</th>
                        <th className="px-6 py-3 text-right font-semibold">القيمة</th>
                        <th className="px-6 py-3 text-right font-semibold">عدد المدفوعات</th>
                        <th className="px-6 py-3 text-right font-semibold">إجمالي قيمة الخصم</th>
                        <th className="px-6 py-3 text-right font-semibold">الحالة</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {report.discountStats.map((discount) => (
                        <tr key={discount.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4" data-label="الاسم">{discount.name}</td>
                          <td className="px-6 py-4" data-label="النوع">
                            {discount.type === 'PERCENTAGE' ? (
                              <span className="flex items-center gap-1">
                                <FaPercent className="text-blue-500" />
                                نسبة مئوية
                              </span>
                            ) : (
                              <span className="flex items-center gap-1">
                                <FaMoneyBillWave className="text-primary-color" />
                                مبلغ ثابت
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4" data-label="القيمة">
                            {discount.type === 'PERCENTAGE'
                              ? `${discount.value}%`
                              : formatCurrency(discount.value)}
                          </td>
                          <td className="px-6 py-4" data-label="عدد المدفوعات">{discount.paymentsCount}</td>
                          <td className="px-6 py-4" data-label="إجمالي قيمة الخصم">{formatCurrency(discount.totalDiscountAmount)}</td>
                          <td className="px-6 py-4" data-label="الحالة">
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                discount.isActive
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}
                            >
                              {discount.isActive ? 'نشط' : 'غير نشط'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm p-4 md:p-8 text-center">
          <FaChartBar className="text-[var(--primary-color)] text-4xl md:text-5xl mx-auto mb-4" />
          <h3 className="text-lg md:text-xl font-bold mb-2">لا توجد بيانات للعرض</h3>
          <p className="text-sm md:text-base text-gray-500 mb-4">يرجى تحديد معايير البحث والضغط على زر &quot;عرض التقرير&quot لعرض التقارير المالية.</p>
          <Button
            onClick={fetchFinancialReport}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
          >
            عرض التقرير
          </Button>
        </div>
      )}
      </div>
    </OptimizedProtectedRoute>
  );
}

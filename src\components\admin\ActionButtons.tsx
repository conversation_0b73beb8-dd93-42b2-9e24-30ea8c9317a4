'use client';
import React from 'react';
import PermissionGuard from './PermissionGuard';
import { FaEdit, FaTrash, FaPlus, FaEye, FaDownload, FaPrint } from 'react-icons/fa';

interface ActionButtonProps {
  onClick: () => void;
  variant?: 'edit' | 'delete' | 'add' | 'view' | 'download' | 'print';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
}

interface ProtectedActionButtonProps extends ActionButtonProps {
  requiredPermission: string;
}

// أزرار الإجراءات الأساسية
const ActionButton: React.FC<ActionButtonProps> = ({
  onClick,
  variant = 'edit',
  size = 'sm',
  className = '',
  children,
  disabled = false
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'edit':
        return 'bg-blue-500 hover:bg-blue-600 text-white';
      case 'delete':
        return 'bg-red-500 hover:bg-red-600 text-white';
      case 'add':
        return 'bg-green-500 hover:bg-green-600 text-white';
      case 'view':
        return 'bg-gray-500 hover:bg-gray-600 text-white';
      case 'download':
        return 'bg-purple-500 hover:bg-purple-600 text-white';
      case 'print':
        return 'bg-orange-500 hover:bg-orange-600 text-white';
      default:
        return 'bg-blue-500 hover:bg-blue-600 text-white';
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'md':
        return 'px-3 py-2 text-sm';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-2 py-1 text-xs';
    }
  };

  const getIcon = () => {
    switch (variant) {
      case 'edit':
        return <FaEdit className="h-3 w-3" />;
      case 'delete':
        return <FaTrash className="h-3 w-3" />;
      case 'add':
        return <FaPlus className="h-3 w-3" />;
      case 'view':
        return <FaEye className="h-3 w-3" />;
      case 'download':
        return <FaDownload className="h-3 w-3" />;
      case 'print':
        return <FaPrint className="h-3 w-3" />;
      default:
        return <FaEdit className="h-3 w-3" />;
    }
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        inline-flex items-center justify-center rounded-md font-medium transition-colors
        focus:outline-none focus:ring-2 focus:ring-offset-2
        disabled:opacity-50 disabled:cursor-not-allowed
        ${getVariantStyles()}
        ${getSizeStyles()}
        ${className}
      `}
    >
      {children || getIcon()}
    </button>
  );
};

// أزرار محمية بالصلاحيات
export const ProtectedActionButton: React.FC<ProtectedActionButtonProps> = ({
  requiredPermission,
  ...props
}) => {
  return (
    <PermissionGuard requiredPermission={requiredPermission}>
      <ActionButton {...props} />
    </PermissionGuard>
  );
};

// أزرار مخصصة للاستخدامات الشائعة
export const EditButton: React.FC<Omit<ProtectedActionButtonProps, 'variant'>> = (props) => (
  <ProtectedActionButton {...props} variant="edit" />
);

export const DeleteButton: React.FC<Omit<ProtectedActionButtonProps, 'variant'>> = (props) => (
  <ProtectedActionButton {...props} variant="delete" />
);

export const AddButton: React.FC<Omit<ProtectedActionButtonProps, 'variant'>> = (props) => (
  <ProtectedActionButton {...props} variant="add" />
);

export const ViewButton: React.FC<Omit<ProtectedActionButtonProps, 'variant'>> = (props) => (
  <ProtectedActionButton {...props} variant="view" />
);

// مجموعة أزرار الإجراءات
interface ActionButtonGroupProps {
  entityType: string; // مثل 'students', 'teachers', إلخ
  onEdit?: () => void;
  onDelete?: () => void;
  onView?: () => void;
  showEdit?: boolean;
  showDelete?: boolean;
  showView?: boolean;
  className?: string;
}

export const ActionButtonGroup: React.FC<ActionButtonGroupProps> = ({
  entityType,
  onEdit,
  onDelete,
  onView,
  showEdit = true,
  showDelete = true,
  showView = false,
  className = ''
}) => {
  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {showView && onView && (
        <ViewButton
          requiredPermission={`admin.${entityType}.view`}
          onClick={onView}
        />
      )}
      {showEdit && onEdit && (
        <EditButton
          requiredPermission={`admin.${entityType}.edit`}
          onClick={onEdit}
        />
      )}
      {showDelete && onDelete && (
        <DeleteButton
          requiredPermission={`admin.${entityType}.delete`}
          onClick={onDelete}
        />
      )}
    </div>
  );
};

export default ActionButton;

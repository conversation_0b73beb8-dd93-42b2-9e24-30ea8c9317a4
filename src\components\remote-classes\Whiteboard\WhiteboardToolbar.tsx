'use client';

import React from 'react';
import {
  <PERSON>a<PERSON>en,
  FaEraser,
  FaFont,
  FaMousePointer,
  FaSquare,
  FaCircle,
  FaSlash,
  FaTrash,
  FaSave,
  FaUpload,
  FaDownload,
  FaUndo,
  FaRedo
} from 'react-icons/fa';


interface WhiteboardToolbarProps {
  currentTool: 'pen' | 'eraser' | 'text' | 'select' | 'rectangle' | 'circle' | 'line';
  setCurrentTool: (tool: 'pen' | 'eraser' | 'text' | 'select' | 'rectangle' | 'circle' | 'line') => void;
  currentColor: string;
  setCurrentColor: (color: string) => void;
  brushSize: number;
  setBrushSize: (size: number) => void;
  onClear: () => void;
  onAddText: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  onSave?: () => void;
  onLoad?: () => void;
  onExport?: () => void;
  readOnly?: boolean;
}

/**
 * Toolbar component for the whiteboard
 */
const WhiteboardToolbar: React.FC<WhiteboardToolbarProps> = ({
  currentTool,
  setCurrentTool,
  currentColor,
  setCurrentColor,
  brushSize,
  setBrushSize,
  onClear,
  onAddText,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  onSave,
  onLoad,
  onExport,
  readOnly = false,
}) => {
  // Common button style
  const buttonClass = (isActive: boolean) =>
    `p-2 rounded-md ${isActive ? 'bg-[var(--primary-color)] text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`;

  // Color options
  const colors = [
    '#000000', // أسود
    '#ff0000', // أحمر
    '#0000ff', // أزرق
    '#008000', // أخضر
    '#ffa500', // برتقالي
    '#800080', // أرجواني
  ];

  return (
    <div className="p-2 bg-gray-50 border-b border-gray-300 flex flex-wrap items-center gap-2" dir="rtl">
      {/* Drawing tools */}
      <div className="flex items-center gap-1 border-l border-gray-300 pl-2">
        <button
          className={buttonClass(currentTool === 'pen')}
          onClick={() => setCurrentTool('pen')}
          disabled={readOnly}
          title="قلم"
        >
          <FaPen />
        </button>
        <button
          className={buttonClass(currentTool === 'eraser')}
          onClick={() => setCurrentTool('eraser')}
          disabled={readOnly}
          title="ممحاة"
        >
          <FaEraser />
        </button>
        <button
          className={buttonClass(currentTool === 'text')}
          onClick={() => {
            setCurrentTool('text');
            onAddText();
          }}
          disabled={readOnly}
          title="نص"
        >
          <FaFont />
        </button>
        <button
          className={buttonClass(currentTool === 'select')}
          onClick={() => setCurrentTool('select')}
          disabled={readOnly}
          title="تحديد"
        >
          <FaMousePointer />
        </button>
      </div>

      {/* Shapes */}
      <div className="flex items-center gap-1 border-l border-gray-300 pl-2">
        <button
          className={buttonClass(currentTool === 'rectangle')}
          onClick={() => setCurrentTool('rectangle')}
          disabled={readOnly}
          title="مستطيل"
        >
          <FaSquare />
        </button>
        <button
          className={buttonClass(currentTool === 'circle')}
          onClick={() => setCurrentTool('circle')}
          disabled={readOnly}
          title="دائرة"
        >
          <FaCircle />
        </button>
        <button
          className={buttonClass(currentTool === 'line')}
          onClick={() => setCurrentTool('line')}
          disabled={readOnly}
          title="خط"
        >
          <FaSlash />
        </button>
      </div>

      {/* Colors */}
      <div className="flex items-center gap-1 border-l border-gray-300 pl-2">
        {colors.map((color) => (
          <button
            key={color}
            className={`w-6 h-6 rounded-full ${currentColor === color ? 'ring-2 ring-offset-2 ring-[var(--primary-color)]' : ''}`}
            style={{ backgroundColor: color }}
            onClick={() => setCurrentColor(color)}
            disabled={readOnly}
            title={`لون ${color}`}
          />
        ))}
      </div>

      {/* Brush size */}
      <div className="flex items-center gap-1 border-l border-gray-300 pl-2">
        <span className="text-sm text-gray-700">حجم:</span>
        <input
          type="range"
          min="1"
          max="20"
          value={brushSize}
          onChange={(e) => setBrushSize(parseInt(e.target.value))}
          disabled={readOnly}
          className="w-24"
        />
      </div>

      {/* History actions */}
      <div className="flex items-center gap-1 border-l border-gray-300 pl-2">
        {onUndo && (
          <button
            className={`p-2 rounded-md ${canUndo ? 'bg-amber-100 text-amber-700 hover:bg-amber-200' : 'bg-gray-100 text-gray-400'}`}
            onClick={onUndo}
            disabled={!canUndo || readOnly}
            title="تراجع"
          >
            <FaUndo />
          </button>
        )}

        {onRedo && (
          <button
            className={`p-2 rounded-md ${canRedo ? 'bg-amber-100 text-amber-700 hover:bg-amber-200' : 'bg-gray-100 text-gray-400'}`}
            onClick={onRedo}
            disabled={!canRedo || readOnly}
            title="إعادة"
          >
            <FaRedo />
          </button>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center gap-1">
        <button
          className="p-2 rounded-md bg-red-100 text-red-700 hover:bg-red-200"
          onClick={onClear}
          disabled={readOnly}
          title="مسح الكل"
        >
          <FaTrash />
        </button>

        {onSave && (
          <button
            className="p-2 rounded-md bg-blue-100 text-blue-700 hover:bg-blue-200"
            onClick={onSave}
            disabled={readOnly}
            title="حفظ"
          >
            <FaSave />
          </button>
        )}

        {onLoad && (
          <button
            className="p-2 rounded-md bg-purple-100 text-purple-700 hover:bg-purple-200"
            onClick={onLoad}
            disabled={readOnly}
            title="تحميل"
          >
            <FaUpload />
          </button>
        )}

        {onExport && (
          <button
            className="p-2 rounded-md bg-green-100 text-green-700 hover:bg-green-200"
            onClick={onExport}
            title="تصدير"
          >
            <FaDownload />
          </button>
        )}
      </div>
    </div>
  );
};

export default WhiteboardToolbar;

import { PrismaClient, UserRole, EvaluationType, AttendanceStatus, QuestionType, DifficultyLevel } from '@prisma/client';
import bcrypt from 'bcrypt';
import { seedEvaluationSystem } from './seed/evaluation-system';
import { seedExpenseCategories } from './seed-expense-categories';
import { seedBudgets } from './seed-budgets';
import seedUsers from './seed/users-data';
import { seedPermissions } from './seeds/permissions';
import { seedPageBackgrounds } from './seeds/pageBackgrounds';

const prisma = new PrismaClient();

async function main() {
  console.log('🕌 بدء إنشاء المدرسة النموذجية لتعليم القرآن وعلوم الشريعة');
  console.log('=' .repeat(80));
  console.log('📚 هذا النظام سينشئ مدرسة قرآنية متكاملة تشمل:');
  console.log('   🕌 تحفيظ القرآن الكريم وعلوم التجويد');
  console.log('   📖 الدراسات الإسلامية (فقه، حديث، سيرة، عقيدة)');
  console.log('   👥 إدارة الطلاب والمعلمين');
  console.log('   📊 نظام تقييم شامل');
  console.log('   🏆 أنشطة ومسابقات قرآنية');
  console.log('   💰 إدارة مالية متكاملة');
  console.log('=' .repeat(80));
  console.log('');

  // إدراج الأدوار والصلاحيات أولاً
  console.log('🔐 المرحلة 1/10: إدراج الأدوار والصلاحيات...');
  await seedPermissions();
  console.log('✅ تم إدراج الأدوار والصلاحيات بنجاح\n');

  // المرحلة 2/10: إنشاء المستخدمين الأساسيين
  console.log('👤 المرحلة 2/10: إنشاء المستخدمين الأساسيين...');

  // Hash the password
  const hashedPassword = await bcrypt.hash('admin123', 10);

  // Check if admin user exists
  let admin = await prisma.user.findUnique({
    where: { username: 'admin' }
  });

  // Create admin user if not exists
  if (!admin) {
    console.log('✅ إنشاء مستخدم المدير العام...');
    admin = await prisma.user.create({
      data: {
        username: 'admin',
        password: hashedPassword,
        role: UserRole.ADMIN,
        profile: {
          create: {
            name: 'مدير المدرسة القرآنية',
            phone: '0123456789'
          }
        }
      }
    });
  } else {
    console.log('ℹ️ مستخدم المدير العام موجود بالفعل');
  }

  // Check if teacher user exists
  let teacherUser = await prisma.user.findUnique({
    where: { username: 'teacher1' },
    include: { teacher: true }
  });

  // Create a teacher user if not exists
  if (!teacherUser) {
    console.log('✅ إنشاء الشيخ المعلم الأول...');
    teacherUser = await prisma.user.create({
      data: {
        username: 'teacher1',
        password: await bcrypt.hash('teacher123', 10),
        role: UserRole.TEACHER,
        profile: {
          create: {
            name: 'الشيخ محمد الحافظ',
            phone: '0111111111'
          }
        },
        teacher: {
          create: {
            name: 'الشيخ محمد الحافظ',
            phone: '0111111111',
            specialization: 'حفظ القرآن الكريم وعلوم التجويد'
          }
        }
      },
      include: {
        teacher: true
      }
    });
  } else {
    console.log('ℹ️ الشيخ المعلم موجود بالفعل');
  }

  // المرحلة 3/10: إنشاء المواد الدراسية للمدرسة القرآنية
  console.log('📚 المرحلة 3/10: إنشاء المواد الدراسية...');

  // المواد الأساسية للمدرسة القرآنية
  const subjects = [
    { name: 'حفظ القرآن الكريم', description: 'حفظ وتسميع آيات القرآن الكريم', hasStudyPlan: true },
    { name: 'تلاوة وتجويد', description: 'تلاوة القرآن الكريم بأحكام التجويد الصحيحة', hasStudyPlan: true },
    { name: 'التفسير المبسط', description: 'فهم معاني آيات القرآن الكريم', hasStudyPlan: true },
    { name: 'الحديث الشريف', description: 'دراسة الأحاديث النبوية الشريفة', hasStudyPlan: true },
    { name: 'الفقه الإسلامي', description: 'أحكام العبادات والمعاملات', hasStudyPlan: true },
    { name: 'العقيدة الإسلامية', description: 'أصول الإيمان والعقيدة الصحيحة', hasStudyPlan: true },
    { name: 'السيرة النبوية', description: 'سيرة الرسول صلى الله عليه وسلم', hasStudyPlan: true },
    { name: 'الأخلاق والآداب', description: 'الأخلاق الإسلامية والآداب الشرعية', hasStudyPlan: true },
    { name: 'اللغة العربية', description: 'قواعد اللغة العربية', hasStudyPlan: true },
    { name: 'الخط العربي', description: 'فن الخط العربي الجميل', hasStudyPlan: false }
  ];

  const createdSubjects = [];
  for (const subjectData of subjects) {
    let subject = await prisma.subject.findFirst({
      where: { name: subjectData.name }
    });

    if (!subject) {
      subject = await prisma.subject.create({ data: subjectData });
      console.log(`✅ تم إنشاء مادة: ${subject.name}`);
    } else {
      console.log(`ℹ️ مادة موجودة: ${subject.name}`);
    }
    createdSubjects.push(subject);
  }

  // الحصول على المواد الرئيسية
  const quranSubject = createdSubjects.find(s => s.name === 'حفظ القرآن الكريم');
  const tajweedSubject = createdSubjects.find(s => s.name === 'تلاوة وتجويد');

  // Check if teacher subject exists
  let teacherSubject = null;
  if (teacherUser && teacherUser.teacher && quranSubject) {
    teacherSubject = await prisma.teacherSubject.findFirst({
      where: {
        teacherId: teacherUser.teacher.id,
        subjectId: quranSubject.id
      }
    });

    // Link teacher with subjects if not exists
    if (!teacherSubject) {
      console.log('✅ ربط الشيخ بمادة حفظ القرآن...');
      teacherSubject = await prisma.teacherSubject.create({
        data: {
          teacherId: teacherUser.teacher.id,
          subjectId: quranSubject.id
        }
      });
    } else {
      console.log('ℹ️ الشيخ مرتبط بالمادة بالفعل');
    }
  }

  // المرحلة 4/10: إنشاء الفصول الدراسية للمدرسة القرآنية
  console.log('🏫 المرحلة 4/10: إنشاء الفصول الدراسية...');
  let class1 = null;
  try {
    const classesCount = await prisma.classe.count();

    if (classesCount < 8 && teacherSubject) {
      console.log('✅ إنشاء فصول المدرسة القرآنية...');

      // فصول المدرسة القرآنية النموذجية
      const classesData = [
        {
          name: 'فصل المبتدئين - تأسيس',
          description: 'فصل تأسيسي لتعلم الحروف والكلمات القرآنية والقراءة الصحيحة',
          capacity: 20
        },
        {
          name: 'فصل الحفظ الأول - جزء عم',
          description: 'فصل لحفظ الجزء الثلاثين (جزء عم) مع تعلم أساسيات التجويد',
          capacity: 25
        },
        {
          name: 'فصل الحفظ الثاني - تبارك',
          description: 'فصل لحفظ الجزء التاسع والعشرين (تبارك) مع تطوير مهارات التجويد',
          capacity: 22
        },
        {
          name: 'فصل الحفظ المتوسط',
          description: 'فصل للطلاب الذين أتموا حفظ الأجزاء الأخيرة ويتقدمون في الحفظ',
          capacity: 20
        },
        {
          name: 'فصل الحفظ المتقدم',
          description: 'فصل للطلاب المتقدمين في حفظ القرآن الكريم',
          capacity: 18
        },
        {
          name: 'فصل التجويد والقراءات',
          description: 'فصل متخصص في علوم التجويد والقراءات القرآنية',
          capacity: 15
        },
        {
          name: 'فصل الحفاظ والإجازة',
          description: 'فصل للحفاظ المتميزين الذين أتموا حفظ القرآن الكريم',
          capacity: 12
        },
        {
          name: 'فصل العلوم الشرعية',
          description: 'فصل متخصص في دراسة الفقه والحديث والسيرة النبوية',
          capacity: 25
        }
      ];

      // إنشاء الفصول
      for (const classData of classesData) {
        const existingClass = await prisma.classe.findFirst({
          where: { name: classData.name }
        });

        if (!existingClass) {
          const classe = await prisma.classe.create({
            data: {
              ...classData,
              classSubjects: {
                create: {
                  teacherSubjectId: teacherSubject.id
                }
              }
            }
          });

          console.log(`🏫 تم إنشاء فصل: ${classe.name}`);

          // تعيين class1 للفصل الأول
          if (classData.name === 'فصل المبتدئين - تأسيس') {
            class1 = classe;
          }
        } else {
          console.log(`ℹ️ الفصل موجود بالفعل: ${existingClass.name}`);

          // تعيين class1 للفصل الأول
          if (classData.name === 'فصل المبتدئين - تأسيس') {
            class1 = existingClass;
          }
        }
      }
    } else {
      console.log(`ℹ️ يوجد ${classesCount} فصل موجود بالفعل`);

      // الحصول على الفصل الأول للاستخدام لاحقاً
      class1 = await prisma.classe.findFirst({
        where: { name: 'فصل المبتدئين - تأسيس' }
      }) || await prisma.classe.findFirst(); // أي فصل إذا لم يوجد الفصل المحدد
    }
  } catch (error) {
    console.error('خطأ في إنشاء الفصول الدراسية:', error);
  }

  // Check if parent exists
  let parent1 = await prisma.parent.findFirst({
    where: { phone: '0123456788' }
  });

  // Create a parent if not exists
  if (!parent1) {
    console.log('✅ إنشاء ولي الأمر...');
    parent1 = await prisma.parent.create({
      data: {
        name: 'أحمد محمود',
        phone: '0123456788'
      }
    });
  } else {
    console.log('ℹ️ ولي الأمر موجود بالفعل');
  }

  // Check if student exists
  let student1 = await prisma.student.findFirst({
    where: { username: 'student1' }
  });

  // Create student if not exists
  if (!student1 && class1 && parent1) {
    console.log('✅ إنشاء الطالب...');
    student1 = await prisma.student.create({
      data: {
        username: 'student1',
        name: 'عبد الرحمن أحمد',
        age: 10,
        phone: '0123456787',
        guardianId: parent1.id,
        classeId: class1.id
      }
    });
  } else {
    console.log('ℹ️ الطالب موجود بالفعل');
  }

  // Check if evaluation configs exist
  const recitationConfig = await prisma.evaluationConfig.findFirst({
    where: { evaluationType: EvaluationType.QURAN_RECITATION }
  });

  const memorizationConfig = await prisma.evaluationConfig.findFirst({
    where: { evaluationType: EvaluationType.QURAN_MEMORIZATION }
  });

  // Create evaluation configs if not exist
  if (!recitationConfig || !memorizationConfig) {
    console.log('✅ إنشاء إعدادات التقييم...');

    const configsToCreate = [];

    if (!recitationConfig) {
      configsToCreate.push({
        evaluationType: EvaluationType.QURAN_RECITATION,
        weight: 0.4,
        isRequired: true
      });
    }

    if (!memorizationConfig) {
      configsToCreate.push({
        evaluationType: EvaluationType.QURAN_MEMORIZATION,
        weight: 0.6,
        isRequired: true
      });
    }

    if (configsToCreate.length > 0) {
      await prisma.evaluationConfig.createMany({
        data: configsToCreate
      });
    }
  } else {
    console.log('ℹ️ إعدادات التقييم موجودة بالفعل');
  }

  console.log('Default data created successfully');

  // إنشاء سور القرآن
  console.log('📖 إنشاء سور القرآن...');
  const surahs = [
    { name: 'الفاتحة', number: 1, totalAyahs: 7, sura_name_ar: 'الفاتحة' },
    { name: 'البقرة', number: 2, totalAyahs: 286, sura_name_ar: 'البقرة' },
    { name: 'آل عمران', number: 3, totalAyahs: 200, sura_name_ar: 'آل عمران' },
    { name: 'النساء', number: 4, totalAyahs: 176, sura_name_ar: 'النساء' },
    { name: 'المائدة', number: 5, totalAyahs: 120, sura_name_ar: 'المائدة' },
    { name: 'الأنعام', number: 6, totalAyahs: 165, sura_name_ar: 'الأنعام' },
    { name: 'الأعراف', number: 7, totalAyahs: 206, sura_name_ar: 'الأعراف' },
    { name: 'الأنفال', number: 8, totalAyahs: 75, sura_name_ar: 'الأنفال' },
    { name: 'التوبة', number: 9, totalAyahs: 129, sura_name_ar: 'التوبة' },
    { name: 'يونس', number: 10, totalAyahs: 109, sura_name_ar: 'يونس' },
  ];

  const createdSurahs = [];
  for (const surah of surahs) {
    try {
      const existingSurah = await prisma.surah.findUnique({
        where: { number: surah.number }
      });

      if (!existingSurah) {
        const createdSurah = await prisma.surah.create({
          data: surah
        });
        createdSurahs.push(createdSurah);
        console.log(`📖 تم إنشاء سورة: ${createdSurah.name}`);
      } else {
        createdSurahs.push(existingSurah);
        console.log(`📖 السورة موجودة بالفعل: ${existingSurah.name}`);
      }
    } catch (error) {
      console.error(`خطأ في إنشاء سورة ${surah.name}:`, error);
    }
  }

  // إنشاء مجالس الختم
  console.log('📚 إنشاء مجالس الختم...');
  try {
    // التحقق من عدد مجالس الختم الموجودة
    const khatmSessionsCount = await prisma.khatmSession.count();

    if (khatmSessionsCount < 8) {
      console.log('✅ إنشاء مجالس ختم نموذجية...');

      // مجالس الختم النموذجية
      const khatmSessionsData = [
        {
          title: 'مجلس ختم سورة البقرة',
          description: 'مجلس أسبوعي لختم سورة البقرة مع التدبر وشرح المعاني',
          location: 'القاعة الرئيسية - الطابق الأول',
          isPublic: true,
          surahName: 'البقرة',
          daysOffset: 2 // بعد يومين من اليوم
        },
        {
          title: 'مجلس ختم جزء عم',
          description: 'مجلس لختم جزء عم للأطفال مع تصحيح التلاوة وتحسين الحفظ',
          location: 'قاعة الأطفال - الطابق الثاني',
          isPublic: true,
          surahName: 'الفاتحة',
          daysOffset: 5 // بعد 5 أيام من اليوم
        },
        {
          title: 'مجلس ختم سورة آل عمران',
          description: 'مجلس لختم سورة آل عمران مع شرح أسباب النزول والأحكام',
          location: 'قاعة المحاضرات - الطابق الأرضي',
          isPublic: true,
          surahName: 'آل عمران',
          daysOffset: 7 // بعد أسبوع من اليوم
        },
        {
          title: 'مجلس ختم سورة النساء',
          description: 'مجلس لختم سورة النساء مع التركيز على الأحكام الفقهية',
          location: 'القاعة الرئيسية - الطابق الأول',
          isPublic: false,
          surahName: 'النساء',
          daysOffset: 10 // بعد 10 أيام من اليوم
        },
        {
          title: 'مجلس ختم سورة المائدة',
          description: 'مجلس لختم سورة المائدة مع دراسة الأحكام الشرعية',
          location: 'قاعة الدراسات - الطابق الثاني',
          isPublic: true,
          surahName: 'المائدة',
          daysOffset: 14 // بعد أسبوعين من اليوم
        },
        {
          title: 'مجلس ختم الجزء الأول',
          description: 'مجلس لختم الجزء الأول من القرآن الكريم للمبتدئين',
          location: 'قاعة المبتدئين - الطابق الأرضي',
          isPublic: true,
          surahName: 'البقرة',
          daysOffset: 3 // بعد 3 أيام من اليوم
        },
        {
          title: 'مجلس ختم سورة الأنعام',
          description: 'مجلس لختم سورة الأنعام مع التركيز على العقيدة',
          location: 'قاعة المحاضرات الكبرى',
          isPublic: false,
          surahName: 'الأنعام',
          daysOffset: 21 // بعد 3 أسابيع من اليوم
        },
        {
          title: 'مجلس ختم سورة الأعراف',
          description: 'مجلس لختم سورة الأعراف مع دراسة قصص الأنبياء',
          location: 'قاعة القصص - الطابق الثالث',
          isPublic: true,
          surahName: 'الأعراف',
          daysOffset: 28 // بعد 4 أسابيع من اليوم
        }
      ];

      const khatmSessions = [];

      // إنشاء مجالس الختم
      for (const sessionData of khatmSessionsData) {
        // البحث عن السورة المرتبطة
        const surah = createdSurahs.find(s => s.name === sessionData.surahName);

        if (!surah) {
          console.log(`⚠️ لم يتم العثور على سورة ${sessionData.surahName}`);
          continue;
        }

        // تحديد تاريخ المجلس
        const sessionDate = new Date();
        sessionDate.setDate(sessionDate.getDate() + sessionData.daysOffset);

        // التحقق من وجود المجلس
        const existingSession = await prisma.khatmSession.findFirst({
          where: { title: sessionData.title }
        });

        if (!existingSession) {
          const khatmSession = await prisma.khatmSession.create({
            data: {
              title: sessionData.title,
              description: sessionData.description,
              date: sessionDate,
              location: sessionData.location,
              teacherId: teacherUser.teacher?.id ?? 1,
              surahId: surah.id,
              isPublic: sessionData.isPublic
            },
          });

          khatmSessions.push(khatmSession);
          console.log(`📚 تم إنشاء مجلس ختم: ${khatmSession.title}`);

          // إنشاء سجل حضور لمجلس الختم
          if (student1) {
            await prisma.khatmSessionAttendance.create({
              data: {
                khatmSessionId: khatmSession.id,
                studentId: student1?.id ?? undefined,
                status: AttendanceStatus.PRESENT,
                note: 'حضر بانتظام',
              },
            });
            console.log(`👥 تم إنشاء سجل حضور للطالب في مجلس الختم: ${khatmSession.title}`);
          }
        } else {
          console.log(`ℹ️ مجلس الختم موجود بالفعل: ${existingSession.title}`);
          khatmSessions.push(existingSession);
        }
      }
    } else {
      console.log(`ℹ️ يوجد ${khatmSessionsCount} مجالس ختم موجودة بالفعل`);
    }
  } catch (error) {
    console.error(`خطأ في إنشاء مجالس الختم:`, error);
  }

  // إنشاء صور للطلاب (بيانات وهمية للصور)
  console.log('🖼️ إنشاء صور للطلاب...');
  try {
    await prisma.studentImage.create({
      data: {
        studentId: student1?.id ?? 1,
        imageUrl: '/uploads/student/default_student_image.txt',
        description: 'الصورة الشخصية',
        isProfilePic: true,
      },
    });

    if (student1) {
      console.log(`🖼️ تم إنشاء صورة للطالب: ${student1.name}`);
    } else {
      console.log('⚠️ الطالب غير موجود، لا يمكن عرض اسمه.');
    }
  } catch (error) {
    console.error(`خطأ في إنشاء صورة للطالب:`, error);
  }

  // إنشاء سجلات الحضور للطالب
  console.log('📋 إنشاء سجلات الحضور للطالب...');
  try {
    if (student1) {
      // التحقق من وجود سجلات حضور للطالب
      const attendanceCount = await prisma.attendance.count({
        where: { studentId: student1.id }
      });

      if (attendanceCount < 30) {
        console.log('✅ إنشاء سجلات حضور جديدة...');

        // إنشاء سجلات حضورconst statuses = ['PRESENT', 'ABSENT', 'EXCUSED'];
        const today = new Date();

        for (let i = 0; i < 30; i++) {
          const date = new Date();
          date.setDate(today.getDate() - i);

          // تخطي أيام الجمعة والسبت
          const dayOfWeek = date.getDay();
          if (dayOfWeek === 5 || dayOfWeek === 6) continue;

          // تحديد حالة الحضور (غالباً حاضر مع بعض الغيابات)
          let status: AttendanceStatus;
          if (i % 10 === 0) {
            status = AttendanceStatus.ABSENT; // غائب كل 10 أيام
          } else if (i % 15 === 0) {
            status = AttendanceStatus.EXCUSED; // معذور كل 15 يوم
          } else {
            status = AttendanceStatus.PRESENT; // حاضر في باقي الأيام
          }

          const attendance = await prisma.attendance.create({
            data: {
              studentId: student1.id,
              date,
              status: status, // استخدام النوع الصحيح AttendanceStatus
              hisass: Math.floor(i / 5) % 3 + 1, // الحصة من 1 إلى 3
            },
          });

          // إنشاء صورة للحضور (اختياري)
          if (i % 5 === 0) {
            await prisma.attendanceImage.create({
              data: {
                attendanceId: attendance.id,
                imageUrl: '/uploads/attendance/default_attendance_image.txt',
              },
            });
          }
        }

        console.log(`📋 تم إنشاء سجلات حضور للطالب: ${student1.name}`);
      } else {
        console.log(`ℹ️ يوجد ${attendanceCount} سجلات حضور موجودة بالفعل للطالب`);
      }
    }
  } catch (error) {
    console.error(`خطأ في إنشاء سجلات الحضور:`, error);
  }

  // إنشاء سجلات تقدم حفظ القرآن
  console.log('📖 إنشاء سجلات تقدم حفظ القرآن...');
  try {
    if (student1 && createdSurahs.length > 0) {
      // التحقق من وجود سجلات تقدم حفظ القرآن للطالب
      const quranProgressCount = await prisma.quranProgress.count({
        where: { studentId: student1.id }
      });

      if (quranProgressCount < 20) {
        console.log('✅ إنشاء سجلات تقدم حفظ القرآن جديدة...');

        // إنشاء سجلات تقدم حفظ القرآن للشهرين الماضيين
        const today = new Date();

        // سجلات لسورة البقرة
        const surahBaqarah = createdSurahs.find(s => s.name === 'البقرة');
        console.log('سورة البقرة:', surahBaqarah);
        if (surahBaqarah) {
          // تقسيم السورة إلى أجزاء للحفظ
          const totalVerses = surahBaqarah.totalAyahs;
          const versesPerSession = 10; // عدد الآيات في كل جلسة

          for (let i = 0; i < 15; i++) {
            const startDate = new Date();
            startDate.setDate(today.getDate() - (i * 2)); // كل يومين جلسة

            const startVerse = (i * versesPerSession) % totalVerses + 1;
            const endVerse = Math.min(startVerse + versesPerSession - 1, totalVerses);

            // تحديد درجات الحفظ والتجويد (متفاوتة للواقعية)
            let memorization, tajweed;

            if (i < 5) {
              // الجلسات الأولى: درجات عالية
              memorization = 8 + Math.floor(Math.random() * 3); // 8-10
              tajweed = 7 + Math.floor(Math.random() * 4); // 7-10
            } else if (i < 10) {
              // الجلسات المتوسطة: درجات متوسطة
              memorization = 6 + Math.floor(Math.random() * 3); // 6-8
              tajweed = 5 + Math.floor(Math.random() * 4); // 5-8
            } else {
              // الجلسات الأخيرة: درجات متفاوتة
              memorization = 4 + Math.floor(Math.random() * 7); // 4-10
              tajweed = 4 + Math.floor(Math.random() * 7); // 4-10
            }

            // الحصول على امتحان حفظ القرآن
            let quranExam = await prisma.exam.findFirst({
              where: {
                evaluationType: EvaluationType.QURAN_MEMORIZATION
              }
            });

            // إذا لم يوجد امتحان، قم بإنشاء واحد
            if (!quranExam) {
              // تحديد الشهر بتنسيق MM-YYYY
              const month = `${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;

              quranExam = await prisma.exam.create({
                data: {
                  description: 'امتحان حفظ القرآن',
                  month: month,
                  maxPoints: 20,
                  passingPoints: 10,
                  evaluationType: EvaluationType.QURAN_MEMORIZATION,
                  requiresSurah: true,
                  isPeriodic: true,
                  period: 'شهري'
                }
              });
            }

            await prisma.quranProgress.create({
              data: {
                studentId: student1.id,
                surahId: surahBaqarah.id,
                examId: quranExam.id,
                startVerse,
                endVerse,
                memorization,
                tajweed,
                startDate,
                completionDate: new Date(startDate), // نفس اليوم
              },
            }).catch(error => {
              console.error(`خطأ في إنشاء سجل تقدم حفظ القرآن:`, error);
            });
          }
        }

        // سجلات لسورة آل عمران
        const surahImran = createdSurahs.find(s => s.name === 'آل عمران');
        if (surahImran) {
          // تقسيم السورة إلى أجزاء للحفظ
          const totalVerses = surahImran.totalAyahs;
          const versesPerSession = 8; // عدد الآيات في كل جلسة

          for (let i = 0; i < 5; i++) {
            const startDate = new Date();
            startDate.setDate(today.getDate() - (i * 3) - 30); // كل 3 أيام جلسة، بدءاً من قبل شهر

            const startVerse = (i * versesPerSession) % totalVerses + 1;
            const endVerse = Math.min(startVerse + versesPerSession - 1, totalVerses);

            // درجات متفاوتة
            const memorization = 5 + Math.floor(Math.random() * 6); // 5-10
            const tajweed = 5 + Math.floor(Math.random() * 6); // 5-10

            // الحصول على امتحان حفظ القرآن
            let quranExam = await prisma.exam.findFirst({
              where: {
                evaluationType: EvaluationType.QURAN_MEMORIZATION
              }
            });

            // إذا لم يوجد امتحان، قم بإنشاء واحد
            if (!quranExam) {
              // تحديد الشهر بتنسيق MM-YYYY
              const month = `${String(startDate.getMonth() + 1).padStart(2, '0')}-${startDate.getFullYear()}`;

              quranExam = await prisma.exam.create({
                data: {
                  description: 'امتحان حفظ القرآن',
                  month: month,
                  maxPoints: 20,
                  passingPoints: 10,
                  evaluationType: EvaluationType.QURAN_MEMORIZATION,
                  requiresSurah: true,
                  isPeriodic: true,
                  period: 'شهري'
                }
              });
            }

            await prisma.quranProgress.create({
              data: {
                studentId: student1.id,
                surahId: surahImran.id,
                examId: quranExam.id,
                startVerse,
                endVerse,
                memorization,
                tajweed,
                startDate,
                completionDate: new Date(startDate), // نفس اليوم
              },
            }).catch(error => {
              console.error(`خطأ في إنشاء سجل تقدم حفظ القرآن:`, error);
            });
          }
        }

        console.log(`📖 تم إنشاء سجلات تقدم حفظ القرآن للطالب: ${student1.name}`);
      } else {
        console.log(`ℹ️ يوجد ${quranProgressCount} سجلات تقدم حفظ القرآن موجودة بالفعل للطالب`);
      }
    }
  } catch (error) {
    console.error(`خطأ في إنشاء سجلات تقدم حفظ القرآن:`, error);
  }

  // إنشاء جدول الحصص
  console.log('📅 فحص جدول الحصص...');

  // التحقق من وجود جدول الحصص
  const scheduleCount = await prisma.classSchedule.count();

  if (scheduleCount < 5 && class1 && teacherSubject) {
    console.log('✅ إنشاء جدول الحصص...');
    const daysOfWeek = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY'];
    const timeSlots = [
      { start: '08:00', end: '09:00' },
      { start: '09:15', end: '10:15' },
      { start: '10:30', end: '11:30' },
      { start: '12:00', end: '13:00' },
      { start: '13:15', end: '14:15' }
    ];

    try {
      // إنشاء الحصص المتبقية
      for (let i = scheduleCount; i < 5; i++) {
        const day = daysOfWeek[i % daysOfWeek.length];
        const timeSlot = timeSlots[i % timeSlots.length];

        await prisma.classSchedule.create({
          data: {
            day,
            startTime: timeSlot.start,
            endTime: timeSlot.end,
            classeId: class1.id,
            teacherSubjectId: teacherSubject.id
          }
        });

        console.log(`✅ تم إنشاء حصة في يوم ${day} من ${timeSlot.start} إلى ${timeSlot.end}`);
      }

      console.log(`✅ تم إنشاء ${5 - scheduleCount} حصص جديدة`);
    } catch (error) {
      console.error('خطأ في إنشاء جدول الحصص:', error);
    }
  } else {
    console.log(`ℹ️ يوجد ${scheduleCount} حصص موجودة بالفعل`);
  }

  // إنشاء وحدات المنهج الدراسي
  console.log('📚 فحص وحدات المنهج الدراسي...');

  // التحقق من وجود وحدات المنهج
  const curriculumUnitCount = await prisma.curriculumUnit.count();

  if (curriculumUnitCount === 0 && quranSubject && tajweedSubject) {
    console.log('✅ إنشاء وحدات المنهج الدراسي...');

    try {
      // إنشاء وحدات لمادة القرآن الكريم
      const quranUnits = [
      {
        title: 'أساسيات التجويد',
        description: 'تعلم أساسيات علم التجويد وأحكامه',
        order: 1,
        subjectId: quranSubject.id,
        lessons: [
          {
            title: 'مخارج الحروف',
            description: 'تعلم مخارج الحروف العربية',
            order: 1,
            resources: [
              {
                title: 'مخارج الحروف - ملف PDF',
                type: 'pdf',
                url: '/uploads/curriculum/makharij_al_huroof.pdf'
              },
              {
                title: 'تمارين على مخارج الحروف',
                type: 'doc',
                url: '/uploads/curriculum/makharij_exercises.doc'
              }
            ]
          },
          {
            title: 'أحكام النون الساكنة والتنوين',
            description: 'تعلم أحكام النون الساكنة والتنوين',
            order: 2,
            resources: [
              {
                title: 'أحكام النون الساكنة والتنوين - ملف PDF',
                type: 'pdf',
                url: '/uploads/curriculum/noon_sakinah.pdf'
              }
            ]
          }
        ]
      },
      {
        title: 'حفظ سورة البقرة',
        description: 'حفظ سورة البقرة مع التجويد',
        order: 2,
        subjectId: quranSubject.id,
        lessons: [
          {
            title: 'الآيات 1-20',
            description: 'حفظ الآيات 1-20 من سورة البقرة',
            order: 1,
            resources: [
              {
                title: 'تسميع الآيات 1-20',
                type: 'pdf',
                url: '/uploads/curriculum/baqarah_1_20.pdf'
              }
            ]
          },
          {
            title: 'الآيات 21-40',
            description: 'حفظ الآيات 21-40 من سورة البقرة',
            order: 2,
            resources: [
              {
                title: 'تسميع الآيات 21-40',
                type: 'pdf',
                url: '/uploads/curriculum/baqarah_21_40.pdf'
              }
            ]
          }
        ]
      }
    ];

    // إنشاء وحدات لمادة التجويد
    const tajweedUnits = [
      {
        title: 'مقدمة في علم التجويد',
        description: 'مقدمة عامة عن علم التجويد وأهميته',
        order: 1,
        subjectId: tajweedSubject.id,
        lessons: [
          {
            title: 'تعريف علم التجويد',
            description: 'تعريف علم التجويد وفضله',
            order: 1,
            resources: [
              {
                title: 'مقدمة في علم التجويد',
                type: 'pdf',
                url: '/uploads/curriculum/tajweed_intro.pdf'
              }
            ]
          }
        ]
      }
    ];

    // إنشاء وحدات المنهج
    for (const unitData of [...quranUnits, ...tajweedUnits]) {
      const { lessons, ...unitInfo } = unitData;

      const unit = await prisma.curriculumUnit.create({
        data: unitInfo
      });

      console.log(`📚 تم إنشاء وحدة: ${unit.title}`);

      // إنشاء دروس الوحدة
      for (const lessonData of lessons) {
        const { resources, ...lessonInfo } = lessonData;

        const lesson = await prisma.curriculumLesson.create({
          data: {
            ...lessonInfo,
            unitId: unit.id
          }
        });

        console.log(`📖 تم إنشاء درس: ${lesson.title}`);

        // إنشاء موارد الدرس
        for (const resourceData of resources) {
          const resource = await prisma.curriculumResource.create({
            data: {
              ...resourceData,
              lessonId: lesson.id
            }
          });

          console.log(`💾 تم إنشاء مورد: ${resource.title}`);
        }
      }
    }
    } catch (error) {
      console.error('خطأ في إنشاء وحدات المنهج:', error);
    }
  } else {
    console.log(`ℹ️ يوجد ${curriculumUnitCount} وحدات منهج موجودة بالفعل`);
  }

  // إنشاء بيانات نظام التقييم
  await seedEvaluationSystem();

  // إنشاء فئات المصروفات
  console.log('\n🔹 بدء تنفيذ ملف seed-expense-categories.ts...');
  await seedExpenseCategories();
  console.log('✅ تم الانتهاء من تنفيذ ملف seed-expense-categories.ts\n');

  // إنشاء بيانات الميزانية
  console.log('\n🔹 بدء تنفيذ ملف seed-budgets.ts...');
  await seedBudgets();
  console.log('✅ تم الانتهاء من تنفيذ ملف seed-budgets.ts\n');

  // إنشاء نتائج امتحانات للطالب
  console.log('📝 إنشاء نتائج امتحانات للطالب...');
  try {
    if (student1 && createdSurahs.length > 0) {
      // التحقق من وجود نتائج امتحانات للطالب
      const examResultsCount = await prisma.exam_points.count({
        where: { studentId: student1.id }
      });

      if (examResultsCount < 10) {
        console.log('✅ إنشاء نتائج امتحانات جديدة...');

        // الحصول على الامتحانات الموجودة
        const exams = await prisma.exam.findMany({
          take: 5,
          orderBy: { id: 'desc' }
        });

        // إذا لم توجد امتحانات، قم بإنشاء بعضها
        if (exams.length === 0) {
          console.log('✅ إنشاء امتحانات جديدة...');

          // إنشاء أنواع امتحانات
          const examType1 = await prisma.examType.create({
            data: {
              name: 'امتحان شهري',
              description: 'امتحان شهري لتقييم مستوى الطلاب',
              evaluationType: 'شفهي'
            }
          });

          const examType2 = await prisma.examType.create({
            data: {
              name: 'امتحان فصلي',
              description: 'امتحان نهاية الفصل الدراسي',
              evaluationType: 'تحريري'
            }
          });

          // إنشاء امتحانات متنوعة بنظام الدرجات الموحد
          const examTemplates = [
            {
              description: 'امتحان حفظ القرآن الشهري',
              evaluationType: EvaluationType.QURAN_MEMORIZATION,
              maxPoints: 20,
              passingPoints: 10,
              requiresSurah: true,
              typeId: examType1.id
            },
            {
              description: 'امتحان تلاوة وتجويد',
              evaluationType: EvaluationType.QURAN_RECITATION,
              maxPoints: 20,
              passingPoints: 10,
              requiresSurah: false,
              typeId: examType1.id
            },
            {
              description: 'امتحان فصلي شامل',
              evaluationType: EvaluationType.QURAN_MEMORIZATION,
              maxPoints: 20,
              passingPoints: 10,
              requiresSurah: true,
              typeId: examType2.id
            },
            {
              description: 'امتحان أحكام التجويد',
              evaluationType: EvaluationType.QURAN_RECITATION,
              maxPoints: 20,
              passingPoints: 10,
              requiresSurah: false,
              typeId: examType1.id
            },
            {
              description: 'امتحان نهاية الفصل',
              evaluationType: EvaluationType.QURAN_MEMORIZATION,
              maxPoints: 20,
              passingPoints: 10,
              requiresSurah: true,
              typeId: examType2.id
            }
          ];

          for (let i = 0; i < examTemplates.length; i++) {
            const template = examTemplates[i];
            const examDate = new Date();
            examDate.setDate(examDate.getDate() - (i * 15)); // كل 15 يوم امتحان

            // تحديد الشهر بتنسيق MM-YYYY
            const month = `${String(examDate.getMonth() + 1).padStart(2, '0')}-${examDate.getFullYear()}`;

            const exam = await prisma.exam.create({
              data: {
                description: template.description,
                month: month,
                maxPoints: template.maxPoints,
                passingPoints: template.passingPoints,
                evaluationType: template.evaluationType,
                requiresSurah: template.requiresSurah,
                isPeriodic: true,
                period: template.typeId === examType1.id ? 'شهري' : 'فصلي',
                examType: { connect: { id: template.typeId } }
              }
            });

            exams.push(exam);
            console.log(`📝 تم إنشاء امتحان: ${exam.description} (${exam.maxPoints} نقطة)`);
          }
        }

        // الحصول على جميع الطلاب لإنشاء نتائج امتحانات لهم
        const allStudents = await prisma.student.findMany({
          include: { classe: true }
        });

        // إنشاء طلاب إضافيين إذا كان العدد قليل
        let studentsToProcess = allStudents;
        if (allStudents.length < 5) {
          console.log('✅ إنشاء طلاب إضافيين...');
          const additionalStudents = [];
          const studentNames = [
            'فاطمة أحمد محمد',
            'عبد الله علي حسن',
            'عائشة محمود إبراهيم',
            'يوسف خالد أحمد',
            'زينب عمر محمد',
            'حمزة سعد علي',
            'مريم حسن محمود'
          ];

          for (let i = 0; i < Math.min(studentNames.length, 7 - allStudents.length); i++) {
            if (class1 && parent1) {
              const newStudent = await prisma.student.create({
                data: {
                  name: studentNames[i],
                  username: `student${allStudents.length + i + 1}`,
                  age: 8 + Math.floor(Math.random() * 8), // عمر بين 8-15
                  phone: `012345678${allStudents.length + i + 1}`,
                  classeId: class1.id,
                  guardianId: parent1.id
                },
                include: { classe: true }
              });
              additionalStudents.push(newStudent);
              console.log(`👦👧 تم إنشاء طالب: ${newStudent.name}`);
            }
          }
          studentsToProcess = [...allStudents, ...additionalStudents];
        }

        // إنشاء نتائج امتحانات لجميع الطلاب
        for (const student of studentsToProcess) {
          for (const exam of exams) {
            // اختيار سورة عشوائية للامتحان
            const randomSurahIndex = Math.floor(Math.random() * createdSurahs.length);
            const surah = createdSurahs[randomSurahIndex];

            // تحديد درجة الامتحان (متفاوتة للواقعية)
            let grade;

            if (Math.random() > 0.25) {
              // 75% من الامتحانات ناجحة
              grade = exam.passingPoints + Math.floor(Math.random() * (exam.maxPoints - exam.passingPoints));
            } else {
              // 25% من الامتحانات راسبة أو ضعيفة
              grade = Math.floor(Math.random() * exam.passingPoints);
            }

            // تاريخ الامتحان (قبل تاريخ الامتحان المحدد بيوم أو يومين)
            const resultDate = new Date(exam.createdAt);
            resultDate.setDate(resultDate.getDate() - Math.floor(Math.random() * 2) - 1);

            // الحصول على classSubjectId
            let classSubject = null;
            if (student.classe && teacherSubject) {
              classSubject = await prisma.classSubject.findFirst({
                where: {
                  classeId: student.classe.id,
                  teacherSubjectId: teacherSubject.id
                }
              });
            }

            if (!classSubject) {
              console.log(`⚠️ لا يمكن إنشاء نتيجة امتحان للطالب ${student.name}: classSubject غير موجود`);
              continue;
            }

            // التحقق من عدم وجود نتيجة مسبقة لنفس الطالب والامتحان
            const existingResult = await prisma.exam_points.findFirst({
              where: {
                examId: exam.id,
                studentId: student.id
              }
            });

            if (!existingResult) {
              await prisma.exam_points.create({
                data: {
                  examId: exam.id,
                  studentId: student.id,
                  classSubjectId: classSubject.id,
                  surahId: surah.id,
                  grade,
                  status: grade >= exam.passingPoints ? 'PASSED' : 'FAILED',
                  note: grade >= exam.maxPoints * 0.8 ? 'أداء ممتاز' :
                        grade >= exam.passingPoints ? 'أداء جيد' : 'يحتاج تحسين',
                  feedback: `الطالب حصل على ${grade} من ${exam.maxPoints} في ${exam.description}`,
                  createdAt: resultDate,
                  updatedAt: resultDate
                }
              });
            }
          }
        }

        console.log(`📝 تم إنشاء نتائج امتحانات لـ ${studentsToProcess.length} طالب في ${exams.length} امتحان`);
      } else {
        console.log(`ℹ️ يوجد ${examResultsCount} نتائج امتحانات موجودة بالفعل`);
      }
    }
  } catch (error) {
    console.error(`خطأ في إنشاء نتائج امتحانات:`, error);
  }

  // إنشاء بنوك الأسئلة
  console.log('❓ إنشاء بنوك الأسئلة...');
  try {
    // التحقق من وجود بنوك أسئلة
    const questionBanksCount = await prisma.questionBank.count();

    if (questionBanksCount < 5 && quranSubject && tajweedSubject) {
      console.log('✅ إنشاء بنوك أسئلة جديدة...');

      // بنوك الأسئلة النموذجية
      const questionBanksData = [
        {
          name: 'بنك أسئلة التجويد الأساسي',
          description: 'مجموعة أسئلة في أساسيات علم التجويد للمبتدئين',
          subjectId: tajweedSubject.id,
          questions: [
            {
              text: 'ما هو تعريف علم التجويد؟',
              type: QuestionType.SHORT_ANSWER,
              difficultyLevel: DifficultyLevel.EASY,
              points: 5,
              options: [
                { text: 'إخراج كل حرف من مخرجه مع إعطائه حقه ومستحقه', isCorrect: true },
                { text: 'قراءة القرآن بصوت جميل', isCorrect: false },
                { text: 'حفظ القرآن الكريم', isCorrect: false }
              ]
            },
            {
              text: 'ما هي أنواع المدود الأصلية؟',
              type: QuestionType.MULTIPLE_CHOICE,
              difficultyLevel: DifficultyLevel.MEDIUM,
              points: 10,
              options: [
                { text: 'المد الطبيعي، المد المتصل، المد المنفصل', isCorrect: false },
                { text: 'المد الطبيعي، مد البدل، مد العوض، مد الصلة الصغرى', isCorrect: true },
                { text: 'المد اللازم، المد العارض للسكون، مد اللين', isCorrect: false }
              ]
            }
          ]
        },
        {
          name: 'بنك أسئلة التجويد المتقدم',
          description: 'مجموعة أسئلة متقدمة في علم التجويد للمستويات المتوسطة والمتقدمة',
          subjectId: tajweedSubject.id,
          questions: [
            {
              text: 'ما هي أقسام المد الفرعي؟',
              type: QuestionType.MULTIPLE_CHOICE,
              difficultyLevel: DifficultyLevel.HARD,
              points: 15,
              options: [
                { text: 'المد المتصل، المد المنفصل، المد اللازم، المد العارض للسكون، مد اللين', isCorrect: true },
                { text: 'المد الطبيعي، المد المتصل، المد المنفصل', isCorrect: false },
                { text: 'مد البدل، مد العوض، مد الصلة الصغرى', isCorrect: false }
              ]
            }
          ]
        },
        {
          name: 'بنك أسئلة حفظ القرآن - جزء عم',
          description: 'أسئلة لاختبار حفظ الطلاب لسور جزء عم',
          subjectId: quranSubject.id,
          questions: [
            {
              text: 'أكمل: "وَالسَّمَاءِ ذَاتِ الْبُرُوجِ * وَالْيَوْمِ ..."',
              type: QuestionType.FILL_BLANK,
              difficultyLevel: DifficultyLevel.MEDIUM,
              points: 10,
              options: [
                { text: 'الْمَوْعُودِ', isCorrect: true },
                { text: 'الْمَشْهُودِ', isCorrect: false },
                { text: 'الْمَعْلُومِ', isCorrect: false }
              ]
            }
          ]
        },
        {
          name: 'بنك أسئلة أحكام النون الساكنة والتنوين',
          description: 'أسئلة متنوعة حول أحكام النون الساكنة والتنوين',
          subjectId: tajweedSubject.id,
          questions: [
            {
              text: 'ما هي أحكام النون الساكنة والتنوين؟',
              type: QuestionType.MULTIPLE_CHOICE,
              difficultyLevel: DifficultyLevel.MEDIUM,
              points: 10,
              options: [
                { text: 'الإظهار، الإدغام، الإقلاب، الإخفاء', isCorrect: true },
                { text: 'الإظهار، الإدغام، القلقلة، الإخفاء', isCorrect: false },
                { text: 'المد، الغنة، الإقلاب، الإخفاء', isCorrect: false }
              ]
            }
          ]
        },
        {
          name: 'بنك أسئلة مخارج الحروف',
          description: 'أسئلة لاختبار معرفة الطلاب بمخارج الحروف وصفاتها',
          subjectId: tajweedSubject.id,
          questions: [
            {
              text: 'ما هي الحروف الشفوية؟',
              type: QuestionType.MULTIPLE_CHOICE,
              difficultyLevel: DifficultyLevel.EASY,
              points: 5,
              options: [
                { text: 'ب، م، و، ف', isCorrect: true },
                { text: 'ط، د، ت', isCorrect: false },
                { text: 'ق، ك', isCorrect: false }
              ]
            }
          ]
        }
      ];

      // إنشاء بنوك الأسئلة
      for (const bankData of questionBanksData) {
        // التحقق من وجود بنك الأسئلة
        const existingBank = await prisma.questionBank.findFirst({
          where: { name: bankData.name }
        });

        if (!existingBank) {
          const { questions, ...bankInfo } = bankData;

          const bank = await prisma.questionBank.create({
            data: bankInfo
          });

          console.log(`❓ تم إنشاء بنك أسئلة: ${bank.name}`);

          // إنشاء الأسئلة لبنك الأسئلة
          for (const questionData of questions) {
            const { options, ...questionInfo } = questionData;

            const question = await prisma.question.create({
              data: {
                ...questionInfo,
                bankId: bank.id
              }
            });

            console.log(`📝 تم إنشاء سؤال: ${question.text.substring(0, 30)}...`);

            // إنشاء خيارات السؤال
            for (const optionData of options) {
              await prisma.questionOption.create({
                data: {
                  ...optionData,
                  questionId: question.id
                }
              });
            }

            // إنشاء إجابة للسؤال (للأسئلة ذات الإجابة الصحيحة الواحدة)
            const correctOption = options.find(opt => opt.isCorrect);
            if (correctOption) {
              await prisma.questionAnswer.create({
                data: {
                  text: correctOption.text,
                  isCorrect: true,
                  explanation: 'الإجابة الصحيحة',
                  questionId: question.id
                }
              });
            }
          }
        } else {
          console.log(`ℹ️ بنك الأسئلة موجود بالفعل: ${existingBank.name}`);
        }
      }
    } else {
      console.log(`ℹ️ يوجد ${questionBanksCount} بنوك أسئلة موجودة بالفعل`);
    }
  } catch (error) {
    console.error('خطأ في إنشاء بنوك الأسئلة:', error);
  }

  // إنشاء المستويات التعليمية
  console.log('📊 إنشاء المستويات التعليمية...');
  try {
    // التحقق من وجود مستويات تعليمية
    const levelsCount = await prisma.level.count();

    if (levelsCount < 5) {
      console.log('✅ إنشاء مستويات تعليمية جديدة...');

      // المستويات التعليمية النموذجية
      const educationalLevels = [
        {
          name: 'المستوى التمهيدي',
          description: 'مستوى مخصص للمبتدئين في تعلم القرآن الكريم، يركز على تعلم القراءة الصحيحة وحفظ السور القصيرة',
          order: 1
        },
        {
          name: 'المستوى الأساسي',
          description: 'مستوى يركز على إتقان أحكام التجويد الأساسية وحفظ الجزء الثلاثين من القرآن الكريم',
          order: 2
        },
        {
          name: 'المستوى المتوسط',
          description: 'مستوى يهدف إلى تعميق فهم أحكام التجويد وحفظ الأجزاء من 26 إلى 29 من القرآن الكريم',
          order: 3
        },
        {
          name: 'المستوى المتقدم',
          description: 'مستوى متقدم يركز على إتقان جميع أحكام التجويد وحفظ الأجزاء من 20 إلى 25 من القرآن الكريم',
          order: 4
        },
        {
          name: 'مستوى الإجازة',
          description: 'المستوى النهائي الذي يؤهل الطالب للحصول على إجازة في القراءة والتجويد بعد إتمام حفظ القرآن الكريم كاملاً',
          order: 5
        }
      ];

      // إنشاء المستويات التعليمية
      for (const levelData of educationalLevels) {
        // التحقق من وجود المستوى
        const existingLevel = await prisma.level.findFirst({
          where: { name: levelData.name }
        });

        if (!existingLevel) {
          const level = await prisma.level.create({
            data: levelData
          });

          console.log(`📊 تم إنشاء مستوى تعليمي: ${level.name}`);
        } else {
          console.log(`ℹ️ المستوى التعليمي موجود بالفعل: ${existingLevel.name}`);
        }
      }
    } else {
      console.log(`ℹ️ يوجد ${levelsCount} مستويات تعليمية موجودة بالفعل`);
    }
  } catch (error) {
    console.error('خطأ في إنشاء المستويات التعليمية:', error);
  }

  // إنشاء البرامج التعليمية
  console.log('🎓 إنشاء البرامج التعليمية...');
  try {
    // التحقق من وجود برامج تعليمية
    const programsCount = await prisma.program.count();

    if (programsCount < 5) {
      console.log('✅ إنشاء برامج تعليمية جديدة...');

      // البرامج التعليمية النموذجية
      const educationalPrograms = [
        {
          title: 'برنامج تحفيظ القرآن الكريم للأطفال',
          description: 'برنامج متكامل لتحفيظ القرآن الكريم للأطفال من سن 6 إلى 12 سنة، يشمل تعليم أحكام التجويد الأساسية وتحسين مهارات القراءة',
          iconName: 'FaQuran',
          price: '500',
          popular: true,
          features: [
            'تحفيظ جزء عم كاملاً',
            'تعليم أحكام التجويد الأساسية',
            'تحسين مخارج الحروف',
            'أنشطة تفاعلية لتثبيت الحفظ',
            'شهادة إتمام البرنامج'
          ]
        },
        {
          title: 'برنامج إتقان التلاوة',
          description: 'برنامج متخصص في تحسين التلاوة وإتقان أحكام التجويد للمبتدئين والمتوسطين، يركز على تصحيح الأخطاء الشائعة في التلاوة',
          iconName: 'FaBookReader',
          price: '400',
          popular: false,
          features: [
            'تصحيح مخارج الحروف',
            'إتقان أحكام التجويد',
            'تدريبات عملية على التلاوة',
            'تسجيلات صوتية للمتابعة',
            'تقييم دوري للأداء'
          ]
        },
        {
          title: 'برنامج حفظ القرآن المكثف',
          description: 'برنامج مكثف لحفظ القرآن الكريم كاملاً خلال 3 سنوات، مع التركيز على الإتقان والمراجعة المستمرة',
          iconName: 'FaBookOpen',
          price: '1200',
          popular: true,
          features: [
            'خطة حفظ منظمة ومتدرجة',
            'جلسات تسميع يومية',
            'مراجعة أسبوعية للمحفوظ',
            'اختبارات شهرية',
            'متابعة فردية مع كل طالب',
            'شهادة إجازة عند إتمام الحفظ'
          ]
        },
        {
          title: 'برنامج تعليم اللغة العربية للناطقين بغيرها',
          description: 'برنامج متكامل لتعليم اللغة العربية للناطقين بغيرها، يركز على مهارات القراءة والكتابة والمحادثة',
          iconName: 'FaLanguage',
          price: '600',
          popular: false,
          features: [
            'تعلم القراءة والكتابة',
            'تطوير مهارات المحادثة',
            'فهم قواعد النحو الأساسية',
            'تنمية الثروة اللغوية',
            'تدريبات تفاعلية'
          ]
        },
        {
          title: 'برنامج إعداد معلمي القرآن الكريم',
          description: 'برنامج متخصص لإعداد وتأهيل معلمي القرآن الكريم، يشمل طرق التدريس الحديثة وأساليب التعامل مع الطلاب',
          iconName: 'FaChalkboardTeacher',
          price: '800',
          popular: false,
          features: [
            'طرق تدريس القرآن الكريم',
            'أساليب التعامل مع الطلاب',
            'مهارات إدارة الصف',
            'تقنيات التحفيز والتشجيع',
            'تطبيقات عملية وورش تدريبية',
            'شهادة تأهيل معتمدة'
          ]
        }
      ];

      // إنشاء البرامج التعليمية
      for (const programData of educationalPrograms) {
        const { features, ...programInfo } = programData;

        // التحقق من وجود البرنامج
        const existingProgram = await prisma.program.findFirst({
          where: { title: programInfo.title }
        });

        if (!existingProgram) {
          const program = await prisma.program.create({
            data: {
              ...programInfo,
              features: {
                create: features.map((feature, index) => ({
                  text: feature,
                  order: index
                }))
              }
            }
          });

          console.log(`🎓 تم إنشاء برنامج تعليمي: ${program.title}`);
        } else {
          console.log(`ℹ️ البرنامج التعليمي موجود بالفعل: ${existingProgram.title}`);
        }
      }
    } else {
      console.log(`ℹ️ يوجد ${programsCount} برامج تعليمية موجودة بالفعل`);
    }
  } catch (error) {
    console.error('خطأ في إنشاء البرامج التعليمية:', error);
  }

  // المرحلة 8/10: إضافة بيانات المستخدمين الإضافية
  console.log('👥 المرحلة 8/10: إضافة بيانات المستخدمين الإضافية...');
  await seedUsers();
  console.log('✅ تم إضافة بيانات المستخدمين بنجاح\n');

  // المرحلة 9/10: إضافة نظام التقييم المتقدم
  console.log('📊 المرحلة 9/10: إضافة نظام التقييم المتقدم...');
  await seedEvaluationSystem();
  console.log('✅ تم إضافة نظام التقييم بنجاح\n');

  // المرحلة 10/10: إضافة النظام المالي والإعدادات
  console.log('💰 المرحلة 10/10: إضافة النظام المالي والإعدادات...');
  await seedExpenseCategories();
  await seedBudgets();
  console.log('✅ تم إضافة النظام المالي بنجاح\n');

  // إضافة خلفيات الصفحات الافتراضية
  console.log('🎨 إضافة خلفيات الصفحات...');
  await seedPageBackgrounds();
  console.log('✅ تم إضافة خلفيات الصفحات بنجاح\n');

  // إحصائيات نهائية
  await showFinalStatistics();

  console.log('🎉 تم إنشاء المدرسة القرآنية النموذجية بنجاح!');
  console.log('=' .repeat(80));
  console.log('🔗 روابط مهمة:');
  console.log('   🏠 الصفحة الرئيسية: http://localhost:3000');
  console.log('   👨‍💼 لوحة التحكم: http://localhost:3000/admin');
  console.log('   📋 كشف الدرجات: http://localhost:3000/admin/evaluation/student-report');
  console.log('   👥 إدارة الطلاب: http://localhost:3000/admin/students');
  console.log('   👨‍🏫 إدارة المعلمين: http://localhost:3000/admin/teachers');
  console.log('   💰 النظام المالي: http://localhost:3000/admin/finance');
  console.log('');
  console.log('🤲 بارك الله فيكم ووفقكم في خدمة كتاب الله الكريم');
  console.log('=' .repeat(80));
}

// دالة لعرض الإحصائيات النهائية
async function showFinalStatistics() {
  try {
    console.log('📊 إحصائيات المدرسة القرآنية النموذجية:');
    console.log('');

    const stats = await Promise.all([
      prisma.user.count(),
      prisma.student.count(),
      prisma.teacher.count(),
      prisma.classe.count(),
      prisma.subject.count(),
      prisma.exam.count(),
      prisma.exam_points.count(),
      prisma.parent.count(),
      prisma.surah.count(),
      prisma.khatmSession.count(),
      prisma.program.count()
    ]);

    console.log('👥 الأشخاص:');
    console.log(`   👤 المستخدمين: ${stats[0]}`);
    console.log(`   👦👧 الطلاب: ${stats[1]}`);
    console.log(`   👨‍🏫 المعلمين: ${stats[2]}`);
    console.log(`   👨‍👩‍👧‍👦 أولياء الأمور: ${stats[7]}`);
    console.log('');

    console.log('🏫 التعليم:');
    console.log(`   🏫 الفصول: ${stats[3]}`);
    console.log(`   📚 المواد: ${stats[4]}`);
    console.log(`   📝 الامتحانات: ${stats[5]}`);
    console.log(`   📊 نقاط الامتحانات: ${stats[6]}`);
    console.log('');

    console.log('📖 القرآن الكريم:');
    console.log(`   📖 السور: ${stats[8]}`);
    console.log(`   📚 مجالس الختم: ${stats[9]}`);
    console.log('');

    console.log('🎓 البرامج:');
    console.log(`   🎓 البرامج التعليمية: ${stats[10]}`);
    console.log('');

  } catch (error) {
    console.log('⚠️  لا يمكن عرض الإحصائيات');
  }
}

// تنفيذ الدالة الرئيسية
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
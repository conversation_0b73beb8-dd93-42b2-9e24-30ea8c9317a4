# الخطة التنفيذية لإصلاحات شؤون التلاميذ

هذا المستند يتتبع المهام المطلوبة لمعالجة المشاكل المتعلقة ببطاقات التلاميذ، وصولات التسجيل، الفواتير، والكشوف.

---

## قائمة المهام (To-Do List)

### T01: بطاقة التلميذ

- [x] **T01.01: تحليل مكون بطاقة التلميذ الحالية**
  - **الحالة:** منجز
  - **المكونات:** `src/components/admin/students/StudentCard.tsx`
  - **الاعتماديات:** لا يوجد
  - **ملاحظات المستخدم:** يجب أن تشبه بطاقة التعريف وليس جدول.

- [x] **T01.02: البحث عن الإصدار السابق لبطاقة التلميذ**
  - **الحالة:** منجز
  - **المكونات:** (لا يوجد)
  - **الاعتماديات:** T01.01
  - **ملاحظات المستخدم:** إرجاعها للشكل القديم.

- [x] **T01.03: إعادة تصميم بطاقة التلميذ**
  - **الحالة:** منجز
  - **المكونات:** `src/components/admin/students/StudentCard.tsx`
  - **الاعتماديات:** T01.02
  - **ملاحظات المستخدم:** تطبيق التصميم الشبيه ببطاقة التعريف.

- [x] **T01.04: إضافة اسم المؤسسة لبطاقة التلميذ**
  - **الحالة:** منجز
  - **المكونات:** `src/components/admin/students/StudentCard.tsx`
  - **الاعتماديات:** T03.01
  - **ملاحظات المستخدم:** اسم المؤسسة غير موجود حالياً.

### T02: وصل التسجيل

- [x] **T02.01: تحليل مكون وصل التسجيل الحالي**
  - **الحالة:** منجز
  - **المكونات:** `src/components/admin/students/RegistrationReceipt.tsx`
  - **الاعتماديات:** لا يوجد
  - **ملاحظات المستخدم:** حجم الوصل كبير جداً.

- [x] **T02.02: إعادة تصميم الوصل ليكون مدمجاً**
  - **الحالة:** منجز
  - **المكونات:** `src/components/admin/students/RegistrationReceipt.tsx`
  - **الاعتماديات:** T02.01
  - **ملاحظات المستخدم:** دمج عدة عناصر في سطر واحد.

- [x] **T02.03: إضافة اسم المؤسسة لوصل التسجيل**
  - **الحالة:** منجز
  - **المكونات:** `src/components/admin/students/RegistrationReceipt.tsx`
  - **الاعتماديات:** T03.01
  - **ملاحظات المستخدم:** اسم المؤسسة غير موجود حالياً.

### T03: اسم المؤسسة

- [x] **T03.01: تحديد مكان تخزين اسم المؤسسة**
  - **الحالة:** منجز
  - **المكونات:** `src/utils/school-settings.ts`, `prisma.systemSettings`
  - **الاعتماديات:** لا يوجد
  - **ملاحظات المستخدم:** لا يعرف أين يتم تعريف اسم المؤسسة.

- [x] **T03.02: توثيق طريقة تعديل اسم المؤسسة**
  - **الحالة:** منجز
  - **المكونات:** `g:/qouran/project_documents/student_affairs_fixes/school_name_guide.md`
  - **الاعتماديات:** T03.01

### T04: الفواتير

- [ ] **T04.01: تصحيح خطأ إضافة فاتورة جديدة**
  - **الحالة:** قيد الانتظار
  - **المكونات:** (سيتم تحديدها بعد التحليل)
  - **الاعتماديات:** لا يوجد
  - **ملاحظات المستخدم:** لا يمكن إضافة فاتورة جديدة.

- [ ] **T04.02: تحليل وتصغير حجم الفاتورة الفردية**
  - **الحالة:** قيد الانتظار
  - **المكونات:** (سيتم تحديدها)
  - **الاعتماديات:** لا يوجد
  - **ملاحظات المستخدم:** حجم الفاتورة ما زال كبيراً.

### T05: الكشوف

- [ ] **T05.01: جدولة جلسة لمناقشة تنسيق الكشوف**
  - **الحالة:** قيد الانتظار
  - **المكونات:** (نقاش تفاعلي)
  - **الاعتماديات:** لا يوجد
  - **ملاحظات المستخدم:** التنسيق بحاجة إلى تنظيم شامل لجميع عمليات الطباعة.

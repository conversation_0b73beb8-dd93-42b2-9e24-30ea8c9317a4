import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

// GET /api/student-courses - جلب الدروس الخاصة بالطالب المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'STUDENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات الطالب
    const student = await prisma.student.findFirst({
      where: {
        username: userData.username
      },
      include: {
        classe: true
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات الطالب" },
        { status: 404 }
      );
    }

    // جلب المواد الدراسية للفصل الذي ينتمي إليه الطالب
    const classSubjects = await prisma.classSubject.findMany({
      where: {
        classeId: student.classeId ?? undefined
      },
      include: {
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    // تنسيق البيانات للعرض
    const courses = classSubjects.map(cs => ({
      id: cs.id,
      subjectId: cs.teacherSubject.subject.id,
      subjectName: cs.teacherSubject.subject.name,
      teacherId: cs.teacherSubject.teacher.id,
      teacherName: cs.teacherSubject.teacher.name,
      description: cs.teacherSubject.subject.description || '',
      progress: Math.floor(Math.random() * 100) // قيمة عشوائية للتقدم (سيتم استبدالها بالقيمة الفعلية لاحقًا)
    }));

    return NextResponse.json({
      courses,
      message: "تم جلب بيانات الدروس بنجاح"
    });
  } catch (error) {
    console.error('Error fetching student courses:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب بيانات الدروس" },
      { status: 500 }
    );
  }
}

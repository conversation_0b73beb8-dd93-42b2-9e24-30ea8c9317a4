"use client";
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
import { FaCalendar<PERSON>lt, FaChalk<PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaUsers } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface Class {
  id: number;
  name: string;
  studentsCount: number;
}

interface Subject {
  id: number;
  name: string;
}

interface ScheduleItem {
  id: number;
  day: string;
  startTime: string;
  endTime: string;
  classeId: number;
  className: string;
  subjectId: number;
  subjectName: string;
}

const daysOfWeek = [
  { id: 'SUNDAY', name: 'الأحد' },
  { id: 'MONDAY', name: 'الإثنين' },
  { id: 'TUESDAY', name: 'الثلاثاء' },
  { id: 'WEDNESDAY', name: 'الأربعاء' },
  { id: 'THURSDAY', name: 'الخميس' },
  { id: 'FRIDAY', name: 'الجمعة' },
  { id: 'SATURDAY', name: 'السبت' }
];

const TeacherSchedulePage = () => {
  const [classes, setClasses] = useState<Class[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [schedule, setSchedule] = useState<ScheduleItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب الفصول
        const classesResponse = await fetch('/api/teacher-classes');
        if (!classesResponse.ok) {
          throw new Error('فشل في جلب بيانات الفصول');
        }
        const classesData = await classesResponse.json();
        setClasses(classesData.classes);

        // جلب المواد
        const subjectsResponse = await fetch('/api/teacher-subjects');
        if (!subjectsResponse.ok) {
          throw new Error('فشل في جلب بيانات المواد');
        }
        const subjectsData = await subjectsResponse.json();
        console.log('Subjects data received:', subjectsData);

        try {
          // التحقق من شكل البيانات المستلمة وتنسيقها بشكل صحيح
          if (subjectsData && subjectsData.data && Array.isArray(subjectsData.data)) {
            // إذا كانت البيانات في شكل {data: [...]}
            console.log('Using data array from response');
            setSubjects(subjectsData.data.map((item: { subject: { id: number, name: string } }) => ({
              id: item.subject.id,
              name: item.subject.name
            })));
          } else if (Array.isArray(subjectsData)) {
            // إذا كانت البيانات مصفوفة مباشرة
            console.log('Using direct array response');
            setSubjects(subjectsData.map((item: { subject: { id: number, name: string } }) => ({
              id: item.subject.id,
              name: item.subject.name
            })));
          } else {
            // إذا كان هناك شكل آخر للبيانات
            console.error('Unexpected data format:', subjectsData);
            setSubjects([]);
          }
        } catch (error) {
          console.error('Error processing subjects data:', error);
          setSubjects([]);
        }

        // جلب جدول الحصص
        const scheduleResponse = await fetch('/api/teacher-schedule');
        if (!scheduleResponse.ok) {
          throw new Error('فشل في جلب جدول الحصص');
        }
        const scheduleData = await scheduleResponse.json();
        setSchedule(scheduleData.schedule);
      } catch (err: unknown) {
        console.error('Error fetching data:', err);
        setError('حدث خطأ أثناء جلب البيانات');
        toast.error('فشل في جلب البيانات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // تنظيم جدول الحصص حسب اليوم
  const scheduleByDay = daysOfWeek.map(day => {
    const daySchedule = schedule.filter(item => item.day === day.id);
    return {
      ...day,
      schedule: daySchedule.sort((a, b) => {
        return a.startTime.localeCompare(b.startTime);
      })
    };
  });

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">جدول الحصص</h1>
          <p className="text-gray-500">عرض جدول الحصص الأسبوعي</p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* ملخص */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-blue-100 text-blue-500">
                    <FaChalkboardTeacher className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">عدد الحصص</p>
                    <p className="text-2xl font-bold text-gray-800">{schedule.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-green-100 text-primary-color">
                    <FaBook className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">المواد</p>
                    <p className="text-2xl font-bold text-gray-800">{subjects.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-purple-100 text-purple-500">
                    <FaUsers className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الفصول</p>
                    <p className="text-2xl font-bold text-gray-800">{classes.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* جدول الحصص */}
          {scheduleByDay.map(day => (
            <Card key={day.id} className={day.schedule.length > 0 ? '' : 'opacity-70'}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaCalendarAlt className="text-[var(--primary-color)]" />
                  <span>{day.name}</span>
                </CardTitle>
                <CardDescription>
                  {day.schedule.length > 0
                    ? `${day.schedule.length} حصة`
                    : 'لا توجد حصص في هذا اليوم'}
                </CardDescription>
              </CardHeader>
              {day.schedule.length > 0 && (
                <CardContent>
                  <div className="space-y-4">
                    {day.schedule.map(item => (
                      <div
                        key={item.id}
                        className="p-4 border rounded-lg flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
                      >
                        <div>
                          <h3 className="font-semibold text-lg">{item.subjectName}</h3>
                          <p className="text-gray-500">{item.className}</p>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="text-center">
                            <p className="text-sm text-gray-500">من</p>
                            <p className="font-medium">{item.startTime}</p>
                          </div>
                          <div className="text-center">
                            <p className="text-sm text-gray-500">إلى</p>
                            <p className="font-medium">{item.endTime}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default TeacherSchedulePage;

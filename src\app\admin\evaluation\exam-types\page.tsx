'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-toastify';
import { Loader2, Plus, Pencil, Trash } from 'lucide-react';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

type ExamType = {
  id: number;
  name: string;
  description: string | null;
  evaluationType: string;
  _count?: {
    exams: number;
  };
};

export default function ExamTypesPage() {
  const [examTypes, setExamTypes] = useState<ExamType[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedExamType, setSelectedExamType] = useState<ExamType | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    evaluationType: 'ORAL',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchExamTypes();
  }, []);

  const fetchExamTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/exam-types');
      if (!response.ok) throw new Error('Failed to fetch exam types');
      const data = await response.json();
      setExamTypes(data.data || []);
    } catch (error) {
      console.error('Error fetching exam types:', error);
      toast.error('حدث خطأ أثناء جلب أنواع الامتحانات');
    } finally {
      setLoading(false);
    }
  };

  const handleAddExamType = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || !formData.evaluationType) {
        toast.error('يرجى إدخال اسم نوع الامتحان ونوع التقييم');
        return;
      }

      const response = await fetch('/api/exam-types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add exam type');
      }

      toast.success('تم إضافة نوع الامتحان بنجاح');
      setIsAddDialogOpen(false);
      setFormData({ name: '', description: '', evaluationType: 'ORAL' });
      fetchExamTypes();
    } catch (error) {
      console.error('Error adding exam type:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة نوع الامتحان');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditExamType = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || !formData.evaluationType) {
        toast.error('يرجى إدخال اسم نوع الامتحان ونوع التقييم');
        return;
      }

      if (!selectedExamType) return;

      const response = await fetch('/api/exam-types', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: selectedExamType.id,
          ...formData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update exam type');
      }

      toast.success('تم تحديث نوع الامتحان بنجاح');
      setIsEditDialogOpen(false);
      setSelectedExamType(null);
      fetchExamTypes();
    } catch (error) {
      console.error('Error updating exam type:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث نوع الامتحان');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteExamType = async () => {
    try {
      setIsSubmitting(true);

      if (!selectedExamType) return;

      const response = await fetch(`/api/exam-types?id=${selectedExamType.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete exam type');
      }

      toast.success('تم حذف نوع الامتحان بنجاح');
      setIsDeleteDialogOpen(false);
      setSelectedExamType(null);
      fetchExamTypes();
    } catch (error) {
      console.error('Error deleting exam type:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حذف نوع الامتحان');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (examType: ExamType) => {
    setSelectedExamType(examType);
    setFormData({
      name: examType.name,
      description: examType.description || '',
      evaluationType: examType.evaluationType,
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (examType: ExamType) => {
    setSelectedExamType(examType);
    setIsDeleteDialogOpen(true);
  };

  const getEvaluationTypeLabel = (type: string) => {
    switch (type) {
      case 'ORAL':
        return 'شفهي';
      case 'WRITTEN':
        return 'تحريري';
      case 'PRACTICAL':
        return 'عملي';
      default:
        return type;
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.evaluation.exam-types.view">
      <div className="container mx-auto p-4">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <h1 className="text-2xl font-bold">إدارة أنواع الامتحانات</h1>
        <QuickActionButtons
          entityType="evaluation.exam-types"
          actions={[
            {
              key: 'create',
              label: 'إضافة نوع امتحان جديد',
              icon: <Plus size={16} />,
              onClick: () => {
                setFormData({ name: '', description: '', evaluationType: 'ORAL' });
                setIsAddDialogOpen(true);
              },
              variant: 'primary'
            }
          ]}
          className="w-full md:w-auto"
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      ) : examTypes.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-gray-500">لا توجد أنواع امتحانات حتى الآن</p>
        </div>
      ) : (
        <div>
          {/* عرض الجدول على الشاشات الكبيرة */}
          <div className="hidden md:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">المعرف</TableHead>
                  <TableHead className="text-right">اسم نوع الامتحان</TableHead>
                  <TableHead className="text-right">نوع التقييم</TableHead>
                  <TableHead className="text-right">الوصف</TableHead>
                  <TableHead className="text-right">عدد الامتحانات</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {examTypes.map((examType) => (
                  <TableRow key={examType.id}>
                    <TableCell>{examType.id}</TableCell>
                    <TableCell>{examType.name}</TableCell>
                    <TableCell>{getEvaluationTypeLabel(examType.evaluationType)}</TableCell>
                    <TableCell>{examType.description || '-'}</TableCell>
                    <TableCell>{examType._count?.exams || 0}</TableCell>
                    <TableCell>
                      <OptimizedActionButtonGroup
                        entityType="evaluation.exam-types"
                        onEdit={() => openEditDialog(examType)}
                        onDelete={() => openDeleteDialog(examType)}
                        showEdit={true}
                        showDelete={!(examType._count?.exams && examType._count.exams > 0)}
                        size="sm"
                        className="gap-2"
                        deleteConfirmTitle="هل أنت متأكد من حذف هذا النوع؟"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* عرض البطاقات على الشاشات الصغيرة */}
          <div className="md:hidden space-y-4">
            {examTypes.map((examType) => (
              <div key={examType.id} className="border rounded-lg p-4 bg-white shadow-sm">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold text-lg">{examType.name}</h3>
                  <span className="text-sm text-gray-500">#{examType.id}</span>
                </div>
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">نوع التقييم:</span>
                    <span>{getEvaluationTypeLabel(examType.evaluationType)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">عدد الامتحانات:</span>
                    <span>{examType._count?.exams || 0}</span>
                  </div>
                  {examType.description && (
                    <div>
                      <span className="text-gray-600 block mb-1">الوصف:</span>
                      <p className="text-sm text-gray-700">{examType.description}</p>
                    </div>
                  )}
                </div>
                <OptimizedActionButtonGroup
                  entityType="evaluation.exam-types"
                  onEdit={() => openEditDialog(examType)}
                  onDelete={() => openDeleteDialog(examType)}
                  showEdit={true}
                  showDelete={!(examType._count?.exams && examType._count.exams > 0)}
                  size="sm"
                  className="flex gap-2 w-full"
                  deleteConfirmTitle="هل أنت متأكد من حذف هذا النوع؟"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => !open && setIsAddDialogOpen(false)}>
        <DialogContent className="w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>إضافة نوع امتحان جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل نوع الامتحان الجديد</DialogDescription>
          </DialogHeader>
          <form id="addExamTypeForm" onSubmit={handleAddExamType} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم نوع الامتحان</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">نوع التقييم</label>
              <Select
                value={formData.evaluationType}
                onValueChange={(value) => setFormData({ ...formData, evaluationType: value })}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع التقييم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ORAL">شفهي</SelectItem>
                  <SelectItem value="WRITTEN">تحريري</SelectItem>
                  <SelectItem value="PRACTICAL">عملي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent className="w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>تعديل نوع الامتحان</DialogTitle>
            <DialogDescription>قم بتعديل تفاصيل نوع الامتحان</DialogDescription>
          </DialogHeader>
          <form id="editExamTypeForm" onSubmit={handleEditExamType} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم نوع الامتحان</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">نوع التقييم</label>
              <Select
                value={formData.evaluationType}
                onValueChange={(value) => setFormData({ ...formData, evaluationType: value })}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع التقييم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ORAL">شفهي</SelectItem>
                  <SelectItem value="WRITTEN">تحريري</SelectItem>
                  <SelectItem value="PRACTICAL">عملي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
        <DialogContent className="w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>حذف نوع الامتحان</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف نوع الامتحان {selectedExamType ? selectedExamType.name : ''}؟
              <br />
              هذا الإجراء لا يمكن التراجع عنه.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteExamType}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحذف...' : 'حذف'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

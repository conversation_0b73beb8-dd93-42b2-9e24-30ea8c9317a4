import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { createHash } from 'crypto';

// GET /api/shared-reports/[token] - الحصول على التقرير المشترك
export async function GET(
  req: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token;
    
    if (!token) {
      return NextResponse.json(
        { error: 'رمز المشاركة مطلوب' },
        { status: 400 }
      );
    }
    
    // البحث عن المشاركة
    const share = await prisma.reportShare.findFirst({
      where: {
        token: token
      }
    });
    
    if (!share) {
      return NextResponse.json(
        { error: 'التقرير المشترك غير موجود' },
        { status: 404 }
      );
    }
    
    // التحقق من تاريخ انتهاء الصلاحية
    if (share.expiryDate && new Date() > new Date(share.expiryDate)) {
      return NextResponse.json(
        { error: 'التقرير المشترك منتهي الصلاحية' },
        { status: 410 }
      );
    }
    
    // التحقق من كلمة المرور
    if (share.hasPassword) {
      return NextResponse.json(
        { error: 'هذا التقرير محمي بكلمة مرور' },
        { status: 401 }
      );
    }
    
    // الحصول على بيانات التقرير
    let reportData;
    let reportTitle;
    
    switch (share.reportType) {
      case 'financial':
        // الحصول على بيانات التقرير المالي
        const financialReport = await prisma.report.findUnique({
          where: { id: parseInt(share.reportId) }
        });
        
        if (!financialReport) {
          return NextResponse.json(
            { error: 'التقرير غير موجود' },
            { status: 404 }
          );
        }
        
        reportData = JSON.parse(financialReport.data);
        reportTitle = financialReport.title;
        break;
        
      case 'budget':
        // الحصول على بيانات تقرير الميزانية
        const budget = await prisma.budget.findUnique({
          where: { id: parseInt(share.reportId) },
          include: {
            items: {
              include: {
                category: true
              }
            }
          }
        });
        
        if (!budget) {
          return NextResponse.json(
            { error: 'الميزانية غير موجودة' },
            { status: 404 }
          );
        }
        
        reportData = budget;
        reportTitle = `تقرير ميزانية: ${budget.name}`;
        break;
        
      // يمكن إضافة المزيد من أنواع التقارير هنا
        
      default:
        return NextResponse.json(
          { error: 'نوع التقرير غير مدعوم' },
          { status: 400 }
        );
    }
    
    // تحديث عدد مرات الوصول
    await prisma.reportShare.update({
      where: { id: share.id },
      data: {
        accessCount: {
          increment: 1
        },
        lastAccessedAt: new Date()
      }
    });
    
    return NextResponse.json({
      success: true,
      report: {
        id: share.reportId,
        title: reportTitle,
        type: share.reportType,
        data: reportData,
        createdAt: share.createdAt,
        expiryDate: share.expiryDate
      }
    });
  } catch (error) {
    console.error('خطأ في جلب التقرير المشترك:', error);
    return NextResponse.json(
      { error: 'فشل في جلب التقرير المشترك' },
      { status: 500 }
    );
  }
}

// POST /api/shared-reports/[token] - التحقق من كلمة المرور للتقرير المشترك
export async function POST(
  req: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token;
    const body = await req.json();
    const { password } = body;
    
    if (!token) {
      return NextResponse.json(
        { error: 'رمز المشاركة مطلوب' },
        { status: 400 }
      );
    }
    
    if (!password) {
      return NextResponse.json(
        { error: 'كلمة المرور مطلوبة' },
        { status: 400 }
      );
    }
    
    // البحث عن المشاركة
    const share = await prisma.reportShare.findFirst({
      where: {
        token: token
      }
    });
    
    if (!share) {
      return NextResponse.json(
        { error: 'التقرير المشترك غير موجود' },
        { status: 404 }
      );
    }
    
    // التحقق من تاريخ انتهاء الصلاحية
    if (share.expiryDate && new Date() > new Date(share.expiryDate)) {
      return NextResponse.json(
        { error: 'التقرير المشترك منتهي الصلاحية' },
        { status: 410 }
      );
    }
    
    // التحقق من كلمة المرور
    if (share.hasPassword) {
      const hashedPassword = createHash('sha256').update(password).digest('hex');
      
      if (hashedPassword !== share.password) {
        return NextResponse.json(
          { error: 'كلمة المرور غير صحيحة' },
          { status: 401 }
        );
      }
    }
    
    // الحصول على بيانات التقرير
    let reportData;
    let reportTitle;
    
    switch (share.reportType) {
      case 'financial':
        // الحصول على بيانات التقرير المالي
        const financialReport = await prisma.report.findUnique({
          where: { id: parseInt(share.reportId) }
        });
        
        if (!financialReport) {
          return NextResponse.json(
            { error: 'التقرير غير موجود' },
            { status: 404 }
          );
        }
        
        reportData = JSON.parse(financialReport.data);
        reportTitle = financialReport.title;
        break;
        
      case 'budget':
        // الحصول على بيانات تقرير الميزانية
        const budget = await prisma.budget.findUnique({
          where: { id: parseInt(share.reportId) },
          include: {
            items: {
              include: {
                category: true
              }
            }
          }
        });
        
        if (!budget) {
          return NextResponse.json(
            { error: 'الميزانية غير موجودة' },
            { status: 404 }
          );
        }
        
        reportData = budget;
        reportTitle = `تقرير ميزانية: ${budget.name}`;
        break;
        
      // يمكن إضافة المزيد من أنواع التقارير هنا
        
      default:
        return NextResponse.json(
          { error: 'نوع التقرير غير مدعوم' },
          { status: 400 }
        );
    }
    
    // تحديث عدد مرات الوصول
    await prisma.reportShare.update({
      where: { id: share.id },
      data: {
        accessCount: {
          increment: 1
        },
        lastAccessedAt: new Date()
      }
    });
    
    return NextResponse.json({
      success: true,
      report: {
        id: share.reportId,
        title: reportTitle,
        type: share.reportType,
        data: reportData,
        createdAt: share.createdAt,
        expiryDate: share.expiryDate
      }
    });
  } catch (error) {
    console.error('خطأ في التحقق من كلمة المرور للتقرير المشترك:', error);
    return NextResponse.json(
      { error: 'فشل في التحقق من كلمة المرور للتقرير المشترك' },
      { status: 500 }
    );
  }
}

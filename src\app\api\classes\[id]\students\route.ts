import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';

// GET: جلب الطلاب المنتمين إلى فصل محدد
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const classId = parseInt(params.id);
        if (isNaN(classId)) {
            return NextResponse.json(
                { message: "معرف الفصل غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من وجود الفصل
        const classe = await prisma.classe.findUnique({
            where: { id: classId }
        });

        if (!classe) {
            return NextResponse.json(
                { message: "الفصل غير موجود" },
                { status: 404 }
            );
        }

        // جلب الطلاب المنتمين إلى الفصل
        const students = await prisma.student.findMany({
            where: { classeId: classId },
            select: {
                id: true,
                name: true,
                username: true,
                age: true,
                phone: true
            },
            orderBy: { name: 'asc' }
        });

        return NextResponse.json({ students });
    } catch (error) {
        console.error('Error fetching students:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب بيانات الطلاب" },
            { status: 500 }
        );
    }
}

fetch("https://meetmob.mobilis.dz/crm/ms/ecare/v1/offering/queryAvailableSOffering", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "ar-DZ,ar;q=0.9,en-US;q=0.8,en;q=0.7",
      "access-control-allow-origin": "*",
      "content-type": "application/json",
      "csrftoken": "f19695a347b30c85b1e9a20d4d767294ae4afba0530e7ec5ec615649c1cff71b",
      "http_x_msisdn": "0665683221",
      "lang": "ar_AE",
      "locale": "ar_SA",
      "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"",
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": "\"Windows\"",
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      "us-busi-type": "EC042",
      "cookie": "imgKey=27341958487352875241624485117682; route=053cdcb3a8a31b8f2792761ff4b8107c; login_auth_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlSWQiOiJFQ0FSRV9JTkRJVklEVUFMIiwiaXNzIjoiY3JtX3Nob3BwaW5nY2FydCIsImV4cCI6MTc0MTk1MzE3NiwidXNlck5hbWUiOiI2NzA3MzU5MTYifQ.LfoQ3IrQqw-Ml-nU3yw5xEHLtrLBIl8uuToP3-HNqFQ; TS01078075=013a81d6736c1aad8829613ca49af1633f2def857414e6789162c8d43edd3d13f7792b6a328bfab4e12b7032825ff3c943696056f04be694e5c123d55f37d0d48d62a767ea9f618d111adcb799aa253d0f7b5955cb479f0b111c0d36a46a96081af47b326f; TS6560ff0d027=08966702b5ab2000e75a61a2eada8f18932e5832485bfd94e208dd98f3e8101b14801a336ae8888a0834a53895113000c35317cc292b549c0a3c30ef4829403d15eec97e0719a08f1b6991382e0730b9403385d8e40329188294746a1fb573c0",
      "Referer": "https://meetmob.mobilis.dz/EcareWeb/",
      "Referrer-Policy": "strict-origin-when-cross-origin"
    },
    "body": "{\"accessInfos\":[{\"objectId\":\"0665683221\",\"objectIdType\":\"4\"}],\"serviceCategory\":\"Q,H,F,BB,C,G,INS,M,O,P,R\"}",
    "method": "POST"
  })
  .then(response => response.json())
  .then(data => {
    console.log(JSON.stringify(data, null, 2)); // Print the formatted response data
  })
  .catch(err => console.error(err));
  
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { message: "غير مصرح به - يجب أن تكون مشرفاً", success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    console.log('Fetching top students...');

    // جلب جميع الطلاب مع نقاط امتحاناتهم
    const students = await prisma.student.findMany({
      include: {
        classe: true,
        exam_points: {
          include: {
            exam: true
          }
        }
      }
    });

    console.log('Students found:', students.length);

    // حساب متوسط الدرجات لكل طالب
    const studentsWithAverages = students.map(student => {
      const examPoints = student.exam_points;
      
      if (examPoints.length === 0) {
        return {
          ...student,
          averageGrade: 0,
          totalExams: 0,
          calculatedPoints: 0
        };
      }

      // حساب متوسط الدرجات
      const totalGrades = examPoints.reduce((sum, point) => sum + Number(point.grade), 0);
      const averageGrade = totalGrades / examPoints.length;
      
      // حساب النقاط المحسوبة (متوسط الدرجات × عدد الامتحانات)
      const calculatedPoints = averageGrade * examPoints.length;

      return {
        ...student,
        averageGrade: Math.round(averageGrade * 100) / 100,
        totalExams: examPoints.length,
        calculatedPoints: Math.round(calculatedPoints * 100) / 100
      };
    });

    // ترتيب الطلاب حسب النقاط المحسوبة ثم متوسط الدرجات
    const topStudents = studentsWithAverages
      .filter(student => student.totalExams > 0) // فقط الطلاب الذين لديهم امتحانات
      .sort((a, b) => {
        // ترتيب أولاً حسب النقاط المحسوبة
        if (b.calculatedPoints !== a.calculatedPoints) {
          return b.calculatedPoints - a.calculatedPoints;
        }
        // إذا كانت النقاط متساوية، ترتيب حسب متوسط الدرجات
        return b.averageGrade - a.averageGrade;
      })
      .slice(0, limit);

    console.log('Top students calculated:', topStudents.length);

    // تنسيق البيانات للإرجاع
    const formattedStudents = topStudents.map((student, index) => ({
      id: student.id,
      name: student.name,
      username: student.username,
      age: student.age,
      phone: student.phone,
      classe: student.classe,
      totalPoints: student.calculatedPoints, // استخدام النقاط المحسوبة
      averageGrade: student.averageGrade,
      totalExams: student.totalExams,
      rank: index + 1
    }));

    return NextResponse.json({
      success: true,
      data: formattedStudents,
      message: `تم جلب أفضل ${formattedStudents.length} طلاب`
    });

  } catch (error) {
    console.error('Error fetching top students:', error);
    return NextResponse.json(
      { 
        message: "حدث خطأ أثناء جلب الطلاب المتفوقين", 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

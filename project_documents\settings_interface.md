# تعديل واجهة الإعدادات لإضافة معلومات التبرع

## التغييرات المطلوبة في SiteSettings Interface

### إضافة حقول التبرع الجديدة:
```typescript
interface SiteSettings {
  // ... الحقول الموجودة
  contactInfo: {
    email: string;
    phone: string;
    address: string;
    // إضافة معلومات التبرع الجديدة
    donationInfo?: {
      phone1: string;        // رقم الهاتف الأول للتبرعات
      phone2?: string;       // رقم الهاتف الثاني (اختياري)
      ccpAccount: string;    // حساب CCP
      cpaAccount: string;    // حساب CPA  
      bdrAccount: string;    // حساب BDR
      description?: string;  // وصف إضافي للتبرعات (اختياري)
    };
  };
}
```

## التغييرات في الإعدادات الافتراضية

### إضافة القيم الافتراضية:
```typescript
const defaultSettings: SiteSettings = {
  // ... الإعدادات الموجودة
  contactInfo: {
    email: '<EMAIL>',
    phone: '+213 123 456 789',
    address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر',
    donationInfo: {
      phone1: '+213 123 456 789',
      phone2: '+213 987 654 321',
      ccpAccount: '**********',
      cpaAccount: '**********',
      bdrAccount: '**********',
      description: 'يمكنكم التبرع من خلال الحسابات التالية أو التواصل معنا هاتفياً'
    }
  }
};
```

## تعديل قسم معلومات الاتصال في الإعدادات

### إضافة حقول التبرع في TabsContent value="contact":
```tsx
<TabsContent value="contact" className="space-y-6">
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <FaPhone className="text-[var(--primary-color)]" />
        <span>معلومات الاتصال</span>
      </CardTitle>
      <CardDescription>
        إدارة معلومات الاتصال التي تظهر في الموقع
      </CardDescription>
    </CardHeader>
    <CardContent className="space-y-4">
      {/* الحقول الموجودة: email, phone, address */}
      
      {/* قسم جديد لمعلومات التبرع */}
      <div className="border-t pt-6 mt-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <FaHandHoldingHeart className="ml-2 text-[var(--primary-color)]" />
          معلومات التبرع
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* رقم الهاتف الأول للتبرعات */}
          <div>
            <label htmlFor="donationPhone1" className="block text-sm font-medium text-gray-700 mb-1">
              رقم الهاتف الأول للتبرعات
            </label>
            <input
              id="donationPhone1"
              name="donationPhone1"
              type="tel"
              value={settings.contactInfo.donationInfo?.phone1 || ''}
              onChange={handleDonationInfoChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              placeholder="+213 123 456 789"
            />
          </div>

          {/* رقم الهاتف الثاني للتبرعات */}
          <div>
            <label htmlFor="donationPhone2" className="block text-sm font-medium text-gray-700 mb-1">
              رقم الهاتف الثاني (اختياري)
            </label>
            <input
              id="donationPhone2"
              name="donationPhone2"
              type="tel"
              value={settings.contactInfo.donationInfo?.phone2 || ''}
              onChange={handleDonationInfoChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              placeholder="+213 987 654 321"
            />
          </div>

          {/* حساب CCP */}
          <div>
            <label htmlFor="ccpAccount" className="block text-sm font-medium text-gray-700 mb-1">
              حساب CCP
            </label>
            <input
              id="ccpAccount"
              name="ccpAccount"
              type="text"
              value={settings.contactInfo.donationInfo?.ccpAccount || ''}
              onChange={handleDonationInfoChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              placeholder="**********"
            />
          </div>

          {/* حساب CPA */}
          <div>
            <label htmlFor="cpaAccount" className="block text-sm font-medium text-gray-700 mb-1">
              حساب CPA
            </label>
            <input
              id="cpaAccount"
              name="cpaAccount"
              type="text"
              value={settings.contactInfo.donationInfo?.cpaAccount || ''}
              onChange={handleDonationInfoChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              placeholder="**********"
            />
          </div>

          {/* حساب BDR */}
          <div>
            <label htmlFor="bdrAccount" className="block text-sm font-medium text-gray-700 mb-1">
              حساب BDR
            </label>
            <input
              id="bdrAccount"
              name="bdrAccount"
              type="text"
              value={settings.contactInfo.donationInfo?.bdrAccount || ''}
              onChange={handleDonationInfoChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              placeholder="**********"
            />
          </div>
        </div>

        {/* وصف التبرعات */}
        <div className="mt-4">
          <label htmlFor="donationDescription" className="block text-sm font-medium text-gray-700 mb-1">
            وصف إضافي للتبرعات (اختياري)
          </label>
          <textarea
            id="donationDescription"
            name="donationDescription"
            value={settings.contactInfo.donationInfo?.description || ''}
            onChange={handleDonationInfoChange}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
            placeholder="يمكنكم التبرع من خلال الحسابات التالية أو التواصل معنا هاتفياً"
          />
        </div>
      </div>
    </CardContent>
  </Card>
</TabsContent>
```

## إضافة دالة معالجة تغيير معلومات التبرع

```typescript
const handleDonationInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
  const { name, value } = e.target;
  setSettings(prev => ({
    ...prev,
    contactInfo: {
      ...prev.contactInfo,
      donationInfo: {
        ...prev.contactInfo.donationInfo,
        [name]: value
      }
    }
  }));
};
```

## تحديث API الإعدادات

### في src/app/api/settings/route.ts:
- إضافة دعم حفظ واسترجاع معلومات التبرع
- تحديث الإعدادات الافتراضية لتشمل donationInfo
- التأكد من التوافق مع الواجهة الجديدة

## الملفات المتأثرة:
1. `src/app/admin/admin-setup/page.tsx` - الواجهة الرئيسية
2. `src/app/api/settings/route.ts` - API الإعدادات
3. `src/components/footer/footer.tsx` - قد تحتاج تحديث للإعدادات الافتراضية
4. أي مكونات أخرى تستخدم contactInfo

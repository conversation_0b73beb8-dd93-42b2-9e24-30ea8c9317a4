"use client";

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'react-toastify';
import {
  FaUpload,
  FaFile,
  FaFilePdf,
  FaFileImage,
  FaTrash,
  FaEye,
  FaSpinner,
  FaCheck,
  FaTimes
} from 'react-icons/fa';
import Image from 'next/image';
import Link from 'next/link';

interface Receipt {
  id: number;
  fileName: string;
  fileType: string;
  fileSize: number;
  filePath: string;
  uploadDate: string;
}

interface ReceiptUploaderProps {
  expenseId: number;
  currentReceipt: string | null;
  onReceiptChangeAction: (receiptUrl: string | null) => void;
}

export default function ReceiptUploader({ expenseId, currentReceipt, onReceiptChangeAction }: ReceiptUploaderProps) {
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // جلب إيصالات المصروف
  const fetchReceipts = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/expenses/receipts?expenseId=${expenseId}`);

      if (!response.ok) {
        throw new Error('فشل في جلب الإيصالات');
      }

      const data = await response.json();
      setReceipts(data.receipts);
    } catch (error) {
      console.error('خطأ في جلب الإيصالات:', error);
      toast.error('فشل في جلب الإيصالات');
    } finally {
      setLoading(false);
    }
  };

  // رفع إيصال جديد
  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, WebP, PDF');
      return;
    }

    // التحقق من حجم الملف (الحد الأقصى 5 ميجابايت)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast.error('حجم الملف يتجاوز الحد المسموح (5 ميجابايت)');
      return;
    }

    try {
      setUploading(true);
      const formData = new FormData();
      formData.append('file', file);
      formData.append('expenseId', expenseId.toString());

      const response = await fetch('/api/expenses/receipts', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في رفع الإيصال');
      }

      const data = await response.json();
      toast.success('تم رفع الإيصال بنجاح');

      // تحديث قائمة الإيصالات
      fetchReceipts();

      // تحديث رابط الإيصال الحالي
      onReceiptChangeAction(data.receipt.filePath);
    } catch (error) {
      console.error('خطأ في رفع الإيصال:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في رفع الإيصال');
    } finally {
      setUploading(false);
      // إعادة تعيين حقل الملف
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // حذف إيصال
  const handleDeleteReceipt = async (receiptId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الإيصال؟')) return;

    try {
      const response = await fetch(`/api/expenses/receipts/${receiptId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف الإيصال');
      }

      toast.success('تم حذف الإيصال بنجاح');

      // تحديث قائمة الإيصالات
      fetchReceipts();

      // إذا كان الإيصال المحذوف هو الإيصال الحالي، قم بتحديث الرابط
      if (currentReceipt && currentReceipt.includes(`/${receiptId}/`)) {
        onReceiptChangeAction(null);
      }
    } catch (error) {
      console.error('خطأ في حذف الإيصال:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في حذف الإيصال');
    }
  };

  // معاينة الإيصال
  const handlePreview = (receipt: Receipt) => {
    setPreviewUrl(receipt.filePath);
    setIsPreviewOpen(true);
  };

  // تنسيق حجم الملف
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  // الحصول على أيقونة الملف حسب نوعه
  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FaFilePdf className="text-red-600" />;
    if (fileType.includes('image')) return <FaFileImage className="text-blue-600" />;
    return <FaFile className="text-gray-600" />;
  };

  return (
    <div className="space-y-4">
      {/* زر رفع إيصال جديد */}
      <div className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleUpload}
          className="hidden"
          accept=".jpg,.jpeg,.png,.webp,.pdf"
        />
        <Button
          onClick={() => fileInputRef.current?.click()}
          variant="outline"
          className="flex items-center gap-2"
          disabled={uploading}
        >
          {uploading ? (
            <>
              <FaSpinner className="animate-spin" />
              <span>جاري الرفع...</span>
            </>
          ) : (
            <>
              <FaUpload />
              <span>رفع إيصال جديد</span>
            </>
          )}
        </Button>
      </div>

      {/* عرض الإيصالات */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-gray-700">الإيصالات المرفقة</h3>

        {loading ? (
          <div className="text-center py-4">
            <FaSpinner className="animate-spin mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">جاري تحميل الإيصالات...</p>
          </div>
        ) : receipts.length === 0 ? (
          <div className="text-center py-4 bg-gray-50 rounded-lg border border-dashed border-gray-300">
            <p className="text-sm text-gray-500">لا توجد إيصالات مرفقة</p>
          </div>
        ) : (
          <div className="space-y-2">
            {receipts.map((receipt) => (
              <Card key={receipt.id} className="overflow-hidden">
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getFileIcon(receipt.fileType)}
                      <div>
                        <p className="text-sm font-medium truncate max-w-[150px]">
                          {receipt.fileName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(receipt.fileSize)} • {new Date(receipt.uploadDate).toLocaleDateString('fr-FR')}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-blue-600"
                        onClick={() => handlePreview(receipt)}
                      >
                        <FaEye />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-600"
                        onClick={() => handleDeleteReceipt(receipt.id)}
                      >
                        <FaTrash />
                      </Button>
                      {currentReceipt === receipt.filePath ? (
                        <div className="text-primary-color flex items-center gap-1 text-xs">
                          <FaCheck />
                          <span>الإيصال الحالي</span>
                        </div>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-7"
                          onClick={() => onReceiptChangeAction(receipt.filePath)}
                        >
                          تعيين كإيصال رئيسي
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* نافذة معاينة الإيصال */}
      {isPreviewOpen && previewUrl && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-3xl w-full max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="font-semibold">معاينة الإيصال</h3>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setIsPreviewOpen(false)}
              >
                <FaTimes />
              </Button>
            </div>
            <div className="flex-1 overflow-auto p-4 flex items-center justify-center">
              {previewUrl.endsWith('.pdf') ? (
                <iframe
                  src={previewUrl}
                  className="w-full h-full"
                  title="معاينة PDF"
                />
              ) : (
                <div className="relative w-full h-full">
                  <Image
                    src={previewUrl}
                    alt="معاينة الإيصال"
                    layout="fill"
                    objectFit="contain"
                  />
                </div>
              )}
            </div>
            <div className="p-4 border-t flex justify-end">
              <Link href={previewUrl} target="_blank" rel="noopener noreferrer">
                <Button variant="outline" className="flex items-center gap-2">
                  <FaEye />
                  <span>فتح في نافذة جديدة</span>
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

// PATCH: تحديث حالة مستلم إشعار جماعي (مثلاً، تحديد كمقروء)
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
    try {
        const { id } = params;
        const { read } = await request.json();

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userId = userData.id;

        const updatedRecipient = await prisma.notificationRecipient.update({
            where: {
                id: parseInt(id),
                userId: userId, // التأكد من أن المستخدم هو المستلم
            },
            data: {
                read: read,
                readAt: read ? new Date() : null, // تعيين readAt فقط إذا تم التحديد كمقروء
            },
        });

        if (!updatedRecipient) {
            return NextResponse.json(
                { message: "لم يتم العثور على المستلم أو لا تملك الصلاحية" },
                { status: 404 }
            );
        }

        return NextResponse.json(updatedRecipient);
    } catch (error) {
        console.error('Error updating notification recipient:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء تحديث حالة الإشعار" },
            { status: 500 }
        );
    }
}

// DELETE: حذف مستلم إشعار جماعي (بمعنى إزالة الإشعار من قائمة المستخدم)
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
    try {
        const { id } = params;

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userId = userData.id;

        // التحقق من أن المستلم موجود ومرتبط بالمستخدم الحالي قبل الحذف
        const existingRecipient = await prisma.notificationRecipient.findUnique({
            where: {
                id: parseInt(id),
            },
        });

        if (!existingRecipient || existingRecipient.userId !== userId) {
            return NextResponse.json(
                { message: "لم يتم العثور على المستلم أو لا تملك الصلاحية لحذفه" },
                { status: 404 }
            );
        }

        await prisma.notificationRecipient.delete({
            where: {
                id: parseInt(id),
            },
        });

        return NextResponse.json({ message: "تم حذف الإشعار بنجاح" });
    } catch (error) {
        console.error('Error deleting notification recipient:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء حذف الإشعار" },
            { status: 500 }
        );
    }
}

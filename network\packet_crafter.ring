/*
==============================================================================
    وحدة صانع الحزم - Praetorian Packet Crafter
    
    الوصف: أدوات لبناء وإرسال حزم TCP/ICMP خام باستخدام Raw Sockets
    المؤلف: Praetorian Team
    
    ملاحظة: يتطلب صلاحيات المدير لاستخدام Raw Sockets
==============================================================================
*/

/*
==============================================================================
    ثوابت بروتوكولات الشبكة
==============================================================================
*/

# بروتوكولات IP
IPPROTO_ICMP = 1
IPPROTO_TCP = 6
IPPROTO_UDP = 17

# أعلام TCP
TCP_FLAG_FIN = 1
TCP_FLAG_SYN = 2
TCP_FLAG_RST = 4
TCP_FLAG_PSH = 8
TCP_FLAG_ACK = 16
TCP_FLAG_URG = 32

# أنواع رسائل ICMP
ICMP_ECHO_REQUEST = 8
ICMP_ECHO_REPLY = 0
ICMP_DEST_UNREACHABLE = 3
ICMP_TIME_EXCEEDED = 11

# إنشاء مثيل عام من صانع الحزم
PraetorianPacketCrafterInstance = new PraetorianPacketCrafter

/*
==============================================================================
    كلاس صانع الحزم
==============================================================================
*/

class PraetorianPacketCrafter

    oLogger = NULL
    bVerbose = false
    
    /*
    دالة البناء
    */
    func init
        oLogger = PraetorianLoggerInstance
        oLogger.debug("تم تهيئة صانع الحزم")
    
    /*
    تفعيل/إلغاء الوضع المفصل
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func setVerbose bEnable
        bVerbose = bEnable
    
    /*
    حساب checksum لبيانات
    المدخلات: cData - البيانات
    المخرجات: قيمة checksum
    */
    func calculateChecksum cData
        nSum = 0
        nLen = len(cData)
        
        # جمع كل 16 بت
        for i = 1 to nLen step 2
            if i + 1 <= nLen
                nWord = (ascii(cData[i]) << 8) + ascii(cData[i + 1])
            else
                nWord = ascii(cData[i]) << 8
            ok
            nSum += nWord
        next
        
        # إضافة carry bits
        while (nSum >> 16) > 0
            nSum = (nSum & 0xFFFF) + (nSum >> 16)
        end
        
        # إرجاع المكمل
        return (~nSum) & 0xFFFF
    
    /*
    تحويل عنوان IP من نص إلى 4 بايت
    المدخلات: cIP - عنوان IP كنص
    المخرجات: عنوان IP كـ 4 بايت
    */
    func ipToBytes cIP
        aParts = split(cIP, ".")
        if len(aParts) != 4
            oLogger.error("عنوان IP غير صحيح: " + cIP)
            return ""
        ok
        
        cResult = ""
        for cPart in aParts
            nPart = number(cPart)
            if nPart < 0 or nPart > 255
                oLogger.error("جزء من عنوان IP غير صحيح: " + cPart)
                return ""
            ok
            cResult += char(nPart)
        next
        
        return cResult
    
    /*
    تحويل رقم إلى 2 بايت (big endian)
    المدخلات: nValue - القيمة
    المخرجات: 2 بايت
    */
    func numberTo2Bytes nValue
        return char((nValue >> 8) & 0xFF) + char(nValue & 0xFF)
    
    /*
    تحويل رقم إلى 4 بايت (big endian)
    المدخلات: nValue - القيمة
    المخرجات: 4 بايت
    */
    func numberTo4Bytes nValue
        return char((nValue >> 24) & 0xFF) + 
               char((nValue >> 16) & 0xFF) + 
               char((nValue >> 8) & 0xFF) + 
               char(nValue & 0xFF)
    
    /*
    بناء رأس IP
    المدخلات: cSourceIP, cDestIP, nProtocol, nDataLength
    المخرجات: رأس IP كبايت
    */
    func buildIPHeader cSourceIP, cDestIP, nProtocol, nDataLength
        cHeader = ""
        
        # Version (4) + IHL (5) = 0x45
        cHeader += char(0x45)
        
        # Type of Service
        cHeader += char(0)
        
        # Total Length
        nTotalLength = 20 + nDataLength  # 20 بايت لرأس IP
        cHeader += numberTo2Bytes(nTotalLength)
        
        # Identification
        cHeader += numberTo2Bytes(12345)
        
        # Flags + Fragment Offset
        cHeader += numberTo2Bytes(0x4000)  # Don't Fragment
        
        # TTL
        cHeader += char(64)
        
        # Protocol
        cHeader += char(nProtocol)
        
        # Header Checksum (سيتم حسابه لاحقاً)
        cHeader += char(0) + char(0)
        
        # Source IP
        cHeader += ipToBytes(cSourceIP)
        
        # Destination IP
        cHeader += ipToBytes(cDestIP)
        
        # حساب checksum
        nChecksum = calculateChecksum(cHeader)
        cHeader = substr(cHeader, 1, 10) + 
                  numberTo2Bytes(nChecksum) + 
                  substr(cHeader, 13)
        
        return cHeader
    
    /*
    بناء رأس TCP
    المدخلات: nSourcePort, nDestPort, nSeqNum, nAckNum, nFlags, nWindowSize
    المخرجات: رأس TCP كبايت
    */
    func buildTCPHeader nSourcePort, nDestPort, nSeqNum, nAckNum, nFlags, nWindowSize
        cHeader = ""
        
        # Source Port
        cHeader += numberTo2Bytes(nSourcePort)
        
        # Destination Port
        cHeader += numberTo2Bytes(nDestPort)
        
        # Sequence Number
        cHeader += numberTo4Bytes(nSeqNum)
        
        # Acknowledgment Number
        cHeader += numberTo4Bytes(nAckNum)
        
        # Data Offset (5) + Reserved (0) = 0x50
        cHeader += char(0x50)
        
        # Flags
        cHeader += char(nFlags)
        
        # Window Size
        cHeader += numberTo2Bytes(nWindowSize)
        
        # Checksum (سيتم حسابه مع pseudo header)
        cHeader += char(0) + char(0)
        
        # Urgent Pointer
        cHeader += char(0) + char(0)
        
        return cHeader
    
    /*
    بناء حزمة TCP كاملة
    المدخلات: cSourceIP, cDestIP, nSourcePort, nDestPort, nFlags, cData
    المخرجات: حزمة TCP كاملة
    */
    func createTCPPacket cSourceIP, cDestIP, nSourcePort, nDestPort, nFlags, cData
        if bVerbose
            oLogger.info("بناء حزمة TCP من " + cSourceIP + ":" + nSourcePort + 
                        " إلى " + cDestIP + ":" + nDestPort)
        ok
        
        # بناء رأس TCP
        cTCPHeader = buildTCPHeader(nSourcePort, nDestPort, 0, 0, nFlags, 8192)
        
        # بناء pseudo header لحساب checksum
        cPseudoHeader = ipToBytes(cSourceIP) + 
                       ipToBytes(cDestIP) + 
                       char(0) + 
                       char(IPPROTO_TCP) + 
                       numberTo2Bytes(len(cTCPHeader) + len(cData))
        
        # حساب TCP checksum
        cTCPData = cPseudoHeader + cTCPHeader + cData
        nChecksum = calculateChecksum(cTCPData)
        
        # تحديث checksum في رأس TCP
        cTCPHeader = substr(cTCPHeader, 1, 16) + 
                    numberTo2Bytes(nChecksum) + 
                    substr(cTCPHeader, 19)
        
        # بناء رأس IP
        cIPHeader = buildIPHeader(cSourceIP, cDestIP, IPPROTO_TCP, 
                                 len(cTCPHeader) + len(cData))
        
        # الحزمة الكاملة
        cPacket = cIPHeader + cTCPHeader + cData
        
        if bVerbose
            oLogger.info("تم بناء حزمة TCP بحجم " + len(cPacket) + " بايت")
        ok
        
        return cPacket
    
    /*
    بناء حزمة ICMP Echo Request
    المدخلات: cSourceIP, cDestIP, nID, nSequence, cData
    المخرجات: حزمة ICMP كاملة
    */
    func createICMPPacket cSourceIP, cDestIP, nID, nSequence, cData
        if bVerbose
            oLogger.info("بناء حزمة ICMP من " + cSourceIP + " إلى " + cDestIP)
        ok
        
        # بناء رأس ICMP
        cICMPHeader = ""
        
        # Type (Echo Request)
        cICMPHeader += char(ICMP_ECHO_REQUEST)
        
        # Code
        cICMPHeader += char(0)
        
        # Checksum (سيتم حسابه لاحقاً)
        cICMPHeader += char(0) + char(0)
        
        # Identifier
        cICMPHeader += numberTo2Bytes(nID)
        
        # Sequence Number
        cICMPHeader += numberTo2Bytes(nSequence)
        
        # حساب checksum
        cICMPData = cICMPHeader + cData
        nChecksum = calculateChecksum(cICMPData)
        
        # تحديث checksum
        cICMPHeader = substr(cICMPHeader, 1, 2) + 
                     numberTo2Bytes(nChecksum) + 
                     substr(cICMPHeader, 5)
        
        # بناء رأس IP
        cIPHeader = buildIPHeader(cSourceIP, cDestIP, IPPROTO_ICMP, 
                                 len(cICMPHeader) + len(cData))
        
        # الحزمة الكاملة
        cPacket = cIPHeader + cICMPHeader + cData
        
        if bVerbose
            oLogger.info("تم بناء حزمة ICMP بحجم " + len(cPacket) + " بايت")
        ok
        
        return cPacket
    
    /*
    إرسال حزمة خام (يتطلب صلاحيات المدير)
    المدخلات: cPacket - الحزمة، cDestIP - عنوان الهدف
    المخرجات: true إذا نجح الإرسال، false إذا فشل
    */
    func sendRawPacket cPacket, cDestIP
        oLogger.warning("إرسال الحزم الخام يتطلب صلاحيات المدير وقد لا يعمل على جميع الأنظمة")
        
        try
            # محاولة إنشاء raw socket
            # ملاحظة: هذا قد لا يعمل على Windows بدون صلاحيات خاصة
            sock = socket(AF_INET, SOCK_STREAM)  # استخدام TCP socket كبديل
            
            if sock = NULL
                oLogger.error("فشل في إنشاء المقبس")
                return false
            ok
            
            # في التطبيق الحقيقي، ستحتاج لاستخدام raw socket
            # وإرسال الحزمة مباشرة
            
            close(sock)
            oLogger.info("تم إرسال الحزمة إلى " + cDestIP)
            return true
            
        catch
            oLogger.error("فشل في إرسال الحزمة: " + cCatchError)
            return false
        done


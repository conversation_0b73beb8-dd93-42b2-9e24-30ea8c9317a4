import { NextRequest, NextResponse } from "next/server";
import { UserRole } from '@prisma/client';
import prisma from '@/lib/prisma';
import bcrypt from 'bcryptjs';

// هذا API مخصص فقط لإنشاء حساب مسؤول أول مرة
// يجب حذفه أو تعطيله بعد إنشاء الحساب الأول

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();

        // التحقق من وجود الحقول المطلوبة
        if (!body.username || !body.password || !body.name) {
            return NextResponse.json(
                { message: "جميع الحقول المطلوبة يجب ملؤها" },
                { status: 400 }
            );
        }

        // التحقق من عدم وجود المستخدم مسبقاً
        const existingUser = await prisma.user.findUnique({
            where: {
                username: body.username
            }
        });

        if (existingUser) {
            return NextResponse.json(
                { message: "اسم المستخدم موجود بالفعل" },
                { status: 400 }
            );
        }

        // تشفير كلمة المرور
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(body.password, salt);

        // إنشاء المستخدم والملف الشخصي في نفس الوقت
        const newAdmin = await prisma.user.create({
            data: {
                role: UserRole.ADMIN,
                username: body.username,
                password: hashedPassword,
                profile: {
                    create: {
                        name: body.name,
                        phone: body.phone || ""
                    }
                }
            },
            select: {
                id: true,
                username: true,
                role: true,
                profile: {
                    select: {
                        name: true,
                        phone: true
                    }
                }
            }
        });

        return NextResponse.json(
            {
                user: newAdmin,
                message: "تم إنشاء حساب المسؤول بنجاح"
            },
            { status: 201 }
        );

    } catch (error: unknown) {
        console.error('Error creating admin:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء معالجة الطلب" },
            { status: 500 }
        );
    }
}

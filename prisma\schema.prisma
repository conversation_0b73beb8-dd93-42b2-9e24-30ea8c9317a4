generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

/// جدول السمات
model Theme {
  id              Int      @id @default(autoincrement())
  name            String
  primaryColor    String
  secondaryColor  String
  accentColor     String?
  backgroundColor String    @default("#ffffff")
  textColor       String    @default("#000000")
  fontFamily      String    @default("Arial")
  isDefault       Boolean   @default(false)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

/// جدول خلفيات الصفحات العامة
model PageBackground {
  id              Int      @id @default(autoincrement())
  pageName        String   @unique // اسم الصفحة (home, about, contact, programs, etc.)
  displayName     String   // الاسم المعروض للصفحة
  imageUrl        String?  // رابط صورة الخلفية
  overlayColor    String?  // لون الطبقة العلوية (rgba)
  overlayOpacity  Float    @default(0.5) // شفافية الطبقة العلوية (0-1)
  position        String   @default("center") // موضع الصورة (center, top, bottom, left, right)
  size            String   @default("cover") // حجم الصورة (cover, contain, auto)
  repeat          String   @default("no-repeat") // تكرار الصورة
  attachment      String   @default("scroll") // ثبات الصورة (scroll, fixed)
  isActive        Boolean  @default(true) // هل الخلفية نشطة
  priority        Int      @default(0) // أولوية الخلفية (للترتيب)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([pageName])
  @@index([isActive])
}

/// جدول المستخدمين لتسجيل الدخول وإدارة الأدوار
model User {
  id                Int                 @id @default(autoincrement())
  username          String              @unique
  password          String?
  email             String?             @unique
  role              UserRole            // دور المستخدم (للتوافق مع النظام الحالي)
  roleId            Int?                // معرف الدور الجديد
  userRole          Role?               @relation(fields: [roleId], references: [id], onDelete: SetNull)
  isActive          Boolean             @default(true) // حالة المستخدم (نشط/غير نشط)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  profile           Profile?
  comments          Comment[]
  teacher           Teacher?
  employee          Employee?
  notifications     Notification[]      @relation("UserNotifications")
  remoteClasses     RemoteClass[]       @relation("Instructor")
  attendees         RemoteClass[]       @relation("Attendees")
  activities        Activity[]
  communications    ParentCommunication[] // سجلات التواصل مع الأولياء
  evaluations       TeacherEvaluation[] // تقييمات المعلمين التي قام بها المستخدم

  // علاقات جديدة للإشعارات المحسنة
  createdGroups     NotificationGroup[] @relation("CreatedGroups")
  createdTemplates  NotificationTemplate[] @relation("CreatedTemplates")
  receivedNotifications NotificationRecipient[] @relation("ReceivedNotifications")

  // علاقات جديدة
  whiteboards       Whiteboard[]        // السبورات التي أنشأها المستخدم
  screenShares      ScreenShare[]       // مشاركات الشاشة
  supervisorReports SupervisorReport[]  // التقارير الموحدة التي أنشأها المستخدم

  // إعدادات الوسائط
  preferredVideoQuality String?         // جودة الفيديو المفضلة
  preferredAudioQuality String?         // جودة الصوت المفضلة

  @@index([roleId])
}

/// جدول الملفات الشخصية للمستخدمين
model Profile {
  id        Int    @id @default(autoincrement())
  name      String // الاسم الكامل
  phone     String? // رقم الهاتف (اختياري)
  userId    Int    @unique
  user      User   @relation(fields: [userId], references: [id], onDelete: Cascade) // العلاقة مع المستخدم
}

/// الأدوار المتاحة في النظام
enum UserRole {
  ADMIN    // المدير
  EMPLOYEE // الموظف
  TEACHER  // المعلم
  STUDENT  // التلميذ
  PARENT   // الولي
  PENDING  // في انتظار تعيين الدور
}

/// جدول التلاميذ
model Student {
  id             Int             @id @default(autoincrement())
  username       String          @unique
  name           String
  age            Int
  phone          String?
  guardianId     Int?
  guardian       Parent?         @relation(fields: [guardianId], references: [id], onDelete: SetNull)
  classeId       Int?
  classe         Classe?         @relation(fields: [classeId], references: [id], onDelete: SetNull)
  attendance     Attendance[]
  payments       Payment[]
  invoices       Invoice[]       // الفواتير الخاصة بالطالب
  exam_points    Exam_points[]
  quranProgress  QuranProgress[]
  totalPoints    Float           @default(0)
  images         StudentImage[]  // صور الطالب
  khatmSessions  KhatmSessionAttendance[] // حضور مجالس الختم
  achievementReports KhatmAchievementReport[] // تقارير إنجاز الختم
  points         StudentPoint[]  // نقاط الطالب
  rewards        StudentReward[] // مكافآت الطالب
  certificates   StudentCertificate[] // شهادات الطالب

  // حقول بداية الحفظ الجديدة
  memorizationStartDate DateTime?   // تاريخ بداية الحفظ
  startingJuz          Int?         // الجزء الذي بدأ فيه الحفظ (1-30)
  memorizationLevel    String?      // المستوى: مبتدئ، متوسط، متقدم
  memorizationNotes    String?      @db.Text // ملاحظات حول بداية الحفظ

  // علاقات جديدة
  memorizationStarts   StudentMemorizationStart[] // سجلات بداية الحفظ
  registrationReceipts StudentRegistrationReceipt[] // وصولات التسجيل

  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  @@index([classeId])
  @@index([totalPoints])
  @@index([memorizationStartDate])
  @@index([startingJuz])
}

/// جدول الأولياء
model Parent {
  id                Int                 @id @default(autoincrement())
  name              String
  phone             String
  email             String?             // البريد الإلكتروني (اختياري)
  address           String?             // العنوان (اختياري)
  amountPerStudent  Float?              // المبلغ الذي يجب دفعه لكل تلميذ (اختياري)
  students          Student[]
  invoices          Invoice[]           // الفواتير الجماعية للولي
  communications    ParentCommunication[] // سجل التواصل مع الولي
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @default(now()) @updatedAt
}

/// جدول المستويات التعليمية
model Level {
  id          Int       @id @default(autoincrement())
  name        String    // اسم المستوى
  description String?   @db.Text // وصف المستوى
  order       Int       // ترتيب المستوى
  subjects    Subject[] // المواد في هذا المستوى
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([order])
}

/// جدول المواد الدراسية
model Subject {
  id              Int             @id @default(autoincrement())
  name            String
  description     String?         @db.Text
  levelId         Int?            // معرف المستوى (اختياري)
  level           Level?          @relation(fields: [levelId], references: [id], onDelete: SetNull)
  hasStudyPlan    Boolean         @default(false) // هل لديها خطة دراسية
  teacherSubjects TeacherSubject[]
  units           CurriculumUnit[] // وحدات المنهج
  teacherSchedules TeacherSchedule[] // جدول حصص المعلمين
  questionBanks   QuestionBank[]  // بنوك الأسئلة المرتبطة بالمادة
  exams           Exam[]          // الامتحانات المرتبطة بالمادة
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @default(now()) @updatedAt

  @@index([levelId])
}

/// جدول ربط المعلمين بالمواد
model TeacherSubject {
  id        Int      @id @default(autoincrement())
  teacherId Int
  subjectId Int
  teacher   Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  subject   Subject  @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  classes   ClassSubject[]
  schedules ClassSchedule[] // جدول الحصص
  createdAt DateTime @default(now())

  @@unique([teacherId, subjectId])
}

/// جدول ربط الأقسام بالمواد
model ClassSubject {
  id               Int            @id @default(autoincrement())
  classeId         Int
  teacherSubjectId Int
  classe           Classe         @relation(fields: [classeId], references: [id], onDelete: Cascade)
  teacherSubject   TeacherSubject @relation(fields: [teacherSubjectId], references: [id], onDelete: Cascade)
  Exam_points      Exam_points[]
  courseMaterials  CourseMaterial[] // المواد التعليمية للمقرر
  createdAt        DateTime       @default(now())

  @@unique([classeId, teacherSubjectId])
}

/// جدول المواد التعليمية للمقررات
model CourseMaterial {
  id              Int          @id @default(autoincrement())
  title           String       // عنوان المادة
  description     String?      // وصف المادة
  type            String       // نوع المادة (pdf, doc, video, link)
  url             String       // رابط المادة
  classSubjectId  Int          // معرف المقرر
  classSubject    ClassSubject @relation(fields: [classSubjectId], references: [id], onDelete: Cascade)
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  @@index([classSubjectId])
}



/// أنواع المدارس
enum SchoolType {
  REGULAR    // المدارس العادية
  LANGUAGE   // مدارس اللغات
  QURAN      // مدارس القرآن
}



/// جدول إعدادات التقييم
model EvaluationConfig {
  id              Int            @id @default(autoincrement())
  evaluationType  EvaluationType
  weight          Float          // الوزن النسبي للتقييم (0.0 - 1.0)
  isRequired      Boolean        @default(true)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  @@unique([evaluationType])
}

/// أنواع التقييم
enum EvaluationType {
  WRITTEN_EXAM      // امتحان تحريري
  ORAL_EXAM         // امتحان شفوي
  HOMEWORK          // واجب منزلي
  PROJECT           // مشروع
  QURAN_RECITATION  // تلاوة القرآن
  QURAN_MEMORIZATION // حفظ القرآن
  PRACTICAL_TEST    // اختبار عملي
  REMOTE_EXAM       // امتحان عن بعد
}

/// جدول الفصول الافتراضية (التعلم عن بعد)
model RemoteClass {
  id              Int       @id @default(autoincrement())
  title           String    // عنوان الفصل
  description     String    @db.Text // وصف الفصل
  startTime       DateTime  // وقت بدء الفصل
  endTime         DateTime  // وقت انتهاء الفصل
  meetingLink     String    // رابط الاجتماع
  meetingId       String?   // معرف الاجتماع
  meetingPassword String?   // كلمة مرور الاجتماع
  platform        String    // المنصة المستخدمة (Zoom, Google Meet, etc.)

  // حقول جديدة
  isScreenShareEnabled Boolean @default(false) // تمكين مشاركة الشاشة
  isWhiteboardEnabled Boolean @default(false) // تمكين السبورة التفاعلية
  videoQuality      String?   // جودة الفيديو (low, medium, high)
  audioQuality      String?   // جودة الصوت (low, medium, high)

  instructorId    Int       // معرف المعلم
  instructor      User      @relation("Instructor", fields: [instructorId], references: [id])
  attendees       User[]    @relation("Attendees")
  classeId        Int?      // الفصل المرتبط (اختياري)
  classe          Classe?   @relation(fields: [classeId], references: [id], onDelete: SetNull)
  recordingUrl    String?   // رابط التسجيل (إذا كان متاحًا)
  materials       Material[] // المواد التعليمية المرتبطة

  // علاقات جديدة
  whiteboards     Whiteboard[] // السبورات التفاعلية
  screenShares    ScreenShare[] // مشاركات الشاشة

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([instructorId])
  @@index([startTime])
}

/// جدول المواد التعليمية
model Material {
  id            Int         @id @default(autoincrement())
  title         String      // عنوان المادة
  description   String?     // وصف المادة
  fileUrl       String      // رابط الملف
  fileType      String      // نوع الملف (PDF, DOC, VIDEO, etc.)
  remoteClassId Int         // معرف الفصل الافتراضي
  remoteClass   RemoteClass @relation(fields: [remoteClassId], references: [id], onDelete: Cascade)
  createdAt     DateTime    @default(now())

  @@index([remoteClassId])
}
/// جدول الامتحان
model Exam {
  id              Int            @id @default(autoincrement())
  evaluationType  EvaluationType
  month           String         // Format: MM-YYYY
  description     String?
  maxPoints       Float          @default(100) // Maximum points possible
  passingPoints   Float          @default(60)  // Minimum points to pass
  attachments     String?        // URLs to PDF/audio files
  requiresSurah   Boolean        @default(false) // هل يتطلب الامتحان تحديد سورة وآيات
  typeId          Int?           // نوع الامتحان
  isPeriodic      Boolean        @default(false) // هل هو امتحان دوري
  period          String?        // الفترة (يومي، أسبوعي، شهري، فصلي)
  hasAutoGrading  Boolean        @default(false) // هل يستخدم التصحيح الآلي
  subjectId       Int?           // معرف المادة الدراسية
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  exam_points     Exam_points[]
  quran_progress  QuranProgress[]
  examType        ExamType?      @relation(fields: [typeId], references: [id], onDelete: SetNull)
  subject         Subject?       @relation(fields: [subjectId], references: [id], onDelete: SetNull)
  questions       ExamQuestion[] // أسئلة الامتحان
  examCriteria    ExamCriteria[] // معايير التقييم المرتبطة بالامتحان

  @@index([month, evaluationType])
  @@index([typeId])
  @@index([subjectId])
}

/// جدول نقاط الامتحان
model Exam_points {
  id              Int            @id @default(autoincrement())
  examId          Int
  studentId       Int
  classSubjectId  Int
  surahId         Int?           // معرف السورة (اختياري لغير امتحانات الحفظ)
  startVerse      Int?           // رقم الآية البداية (اختياري لغير امتحانات الحفظ)
  endVerse        Int?           // رقم الآية النهاية (اختياري لغير امتحانات الحفظ)
  grade           Decimal        @db.Decimal(5,2)
  status          ExamStatus     @default(PENDING)
  note            String?        // الملاحظات
  feedback        String?        // التعليقات التفصيلية
  exam            Exam           @relation(fields: [examId], references: [id], onDelete: Cascade)
  student         Student        @relation(fields: [studentId], references: [id], onDelete: Cascade)
  classSubject    ClassSubject   @relation(fields: [classSubjectId], references: [id], onDelete: Cascade)
  surah           Surah?         @relation(fields: [surahId], references: [id], onDelete: SetNull)
  criteriaScores  CriteriaScore[] // درجات معايير التقييم
  studentAnswers  StudentAnswer[] // إجابات الطالب على أسئلة الامتحان
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  @@unique([studentId, classSubjectId, examId])
  @@index([studentId, status])
  @@index([surahId])
}

enum ExamStatus {
  PENDING
  PASSED
  FAILED
  EXCELLENT
}

model QuranProgress {
  id              Int      @id @default(autoincrement())
  studentId       Int
  examId          Int
  surahId         Int
  startVerse      Int
  endVerse        Int
  memorization    Int      @db.TinyInt // 0-10 scale
  tajweed         Int      @db.TinyInt // 0-10 scale
  startDate       DateTime @default(now())
  completionDate  DateTime?
  student         Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  exam            Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  surah           Surah    @relation(fields: [surahId], references: [id])

  @@unique([studentId, surahId, examId])
  @@index([studentId, completionDate])
}


/// جدول المعلمين
model Teacher {
  id                Int                @id @default(autoincrement())
  name              String
  phone             String?
  specialization    String
  userId            Int                @unique
  user              User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  teacherSubjects   TeacherSubject[]
  khatmSessions     KhatmSession[]     // مجالس الختم التي يشرف عليها المعلم
  evaluations       TeacherEvaluation[] // تقييمات المعلم
  schedules         TeacherSchedule[]  // جدول حصص المعلم
  achievements      TeacherAchievement[] // إنجازات المعلم
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt @default(now())
}

/// جدول الموظفين
model Employee {
  id         Int    @id @default(autoincrement())
  name       String
  phone      String?
  position   String
  userId     Int    @unique
  user       User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

/// جدول الأدوار
model Role {
  id          Int      @id @default(autoincrement())
  name        String   @unique // اسم الدور (مدير، موظف، معلم، إلخ)
  displayName String   // الاسم المعروض
  description String?  // وصف الدور
  isSystem    Boolean  @default(false) // هل هو دور نظام (لا يمكن حذفه)
  isActive    Boolean  @default(true)  // حالة الدور

  users       User[]   // المستخدمون الذين لديهم هذا الدور
  permissions RolePermission[] // صلاحيات الدور

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([name])
  @@index([isActive])
}

/// جدول الصلاحيات
model Permission {
  id          Int      @id @default(autoincrement())
  key         String   @unique // مفتاح الصلاحية (مثل: admin.users.view)
  name        String   // اسم الصلاحية
  description String?  // وصف الصلاحية
  category    String   // فئة الصلاحية (users, students, teachers, إلخ)
  route       String?  // المسار المرتبط بالصلاحية
  isActive    Boolean  @default(true)

  roles       RolePermission[] // الأدوار التي لديها هذه الصلاحية

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([key])
  @@index([category])
  @@index([isActive])
}

/// جدول ربط الأدوار بالصلاحيات
model RolePermission {
  id           Int        @id @default(autoincrement())
  roleId       Int
  permissionId Int

  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  createdAt    DateTime   @default(now())

  @@unique([roleId, permissionId])
  @@index([roleId])
  @@index([permissionId])
}



/// جدول الأقسام
model Classe {
  id                Int            @id @default(autoincrement())
  name              String
  capacity          Int            @default(30) // السعة الاستيعابية للفصل
  description       String?        @db.Text // وصف الفصل
  students          Student[]
  classSubjects     ClassSubject[]
  remoteClasses     RemoteClass[]
  schedules         ClassSchedule[] // جدول الحصص
  teacherSchedules  TeacherSchedule[] // جدول حصص المعلمين
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @default(now()) @updatedAt
}

/// جدول تسجيل الحضور والغياب
model Attendance {
  id         Int    @id @default(autoincrement())
  studentId  Int
  hisass     Int
  student    Student @relation(fields: [studentId], references: [id], onDelete: Cascade)
  date       DateTime
  status     AttendanceStatus
  images     AttendanceImage[] // صور الحضور

  @@index([studentId])
  @@unique([studentId, date,hisass])
}

enum AttendanceStatus {
  PRESENT  // حاضر
  ABSENT   // غائب
  EXCUSED  // غياب بعذر
}

/// جدول المدفوعات
model Payment {
  id             Int            @id @default(autoincrement())
  studentId      Int
  student        Student        @relation(fields: [studentId], references: [id], onDelete: Cascade)
  amount         Float
  date           DateTime
  createdAt      DateTime       @default(now())
  status         PaymentStatus
  invoiceId      Int?
  invoice        Invoice?       @relation(fields: [invoiceId], references: [id], onDelete: SetNull)
  paymentMethodId Int?
  paymentMethod  PaymentMethod? @relation(fields: [paymentMethodId], references: [id], onDelete: SetNull)
  paymentMethodName String?     // اسم طريقة الدفع (مباشر أو غيرها)
  paymentMethods PaymentMethodsOnPayment[] // طرق الدفع المتعددة المستخدمة في هذه الدفعة
  transactionId  String?        // رقم المعاملة (في حالة الدفع الإلكتروني)
  notes          String?        // ملاحظات إضافية
  receiptNumber  String?        // رقم الإيصال
  receiptImage   String?        // صورة الإيصال (مسار الملف)
  discountId     Int?           // معرف الخصم المطبق (إن وجد)
  discount       Discount?      @relation(fields: [discountId], references: [id], onDelete: SetNull)
  originalAmount Float?         // المبلغ الأصلي قبل الخصم

  @@index([studentId])
  @@index([invoiceId])
  @@index([paymentMethodId])
  @@index([discountId])
}

enum PaymentStatus {
  PAID
  PENDING
  CANCELED
}

/// جدول طرق الدفع
model PaymentMethod {
  id          Int       @id @default(autoincrement())
  name        String    // اسم طريقة الدفع
  description String?   // وصف طريقة الدفع
  isActive    Boolean   @default(true) // حالة طريقة الدفع (نشطة/غير نشطة)
  requiresCard Boolean   @default(false) // هل تتطلب معلومات البطاقة
  icon        String?   // أيقونة طريقة الدفع
  payments    Payment[] // المدفوعات التي تمت بهذه الطريقة
  paymentMethods PaymentMethodsOnPayment[] // المدفوعات التي تمت بهذه الطريقة (علاقة متعددة)
  donations   Donation[] // التبرعات التي تمت بهذه الطريقة
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

/// جدول ربط المدفوعات بطرق الدفع المتعددة
model PaymentMethodsOnPayment {
  paymentId       Int
  paymentMethodId Int
  amount          Float           // المبلغ المدفوع بهذه الطريقة
  transactionId   String?         // رقم المعاملة (في حالة الدفع الإلكتروني)
  cardDetails     Json?           // تفاصيل البطاقة (إذا كانت طريقة الدفع تتطلب بطاقة)
  payment         Payment         @relation(fields: [paymentId], references: [id], onDelete: Cascade)
  paymentMethod   PaymentMethod   @relation(fields: [paymentMethodId], references: [id], onDelete: Cascade)
  createdAt       DateTime        @default(now())

  @@id([paymentId, paymentMethodId])
  @@index([paymentId])
  @@index([paymentMethodId])
}

/// جدول الفواتير الإلكترونية
model Invoice {
  id              Int             @id @default(autoincrement())
  studentId       Int?            // جعل الطالب اختياري للفواتير الجماعية
  student         Student?        @relation(fields: [studentId], references: [id], onDelete: Cascade)
  parentId        Int?            // إضافة ربط بالولي للفواتير الجماعية
  parent          Parent?         @relation(fields: [parentId], references: [id], onDelete: Cascade)
  amount          Float
  dueDate         DateTime
  issueDate       DateTime        @default(now())
  month           Int             // شهر الفاتورة (1-12)
  year            Int             // سنة الفاتورة
  description     String?         // وصف الفاتورة
  status          InvoiceStatus   @default(UNPAID)
  type            InvoiceType     @default(INDIVIDUAL) // نوع الفاتورة (فردية أو جماعية)
  payments        Payment[]       // المدفوعات المرتبطة بهذه الفاتورة
  remindersSent   Int             @default(0) // عدد التذكيرات المرسلة
  lastReminderDate DateTime?      // تاريخ آخر تذكير
  notes           String?         // ملاحظات إضافية
  discountId      Int?            // معرف الخصم المطبق (إن وجد)
  discount        Discount?       @relation(fields: [discountId], references: [id], onDelete: SetNull)
  originalAmount  Float?          // المبلغ الأصلي قبل الخصم
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@index([studentId])
  @@index([parentId])
  @@index([status])
  @@index([dueDate])
  @@index([month, year])
  @@index([type])
  @@index([discountId])
}

enum InvoiceStatus {
  PAID            // مدفوعة بالكامل
  PARTIALLY_PAID  // مدفوعة جزئيًا
  UNPAID          // غير مدفوعة
  OVERDUE         // متأخرة
  CANCELLED       // ملغاة
}

enum InvoiceType {
  INDIVIDUAL      // فاتورة فردية لطالب واحد
  FAMILY          // فاتورة جماعية لولي أمر (جميع أبنائه)
}

/// جدول الخصومات
model Discount {
  id              Int       @id @default(autoincrement())
  name            String    // اسم الخصم
  description     String?   // وصف الخصم
  type            DiscountType // نوع الخصم (نسبة مئوية أو مبلغ ثابت)
  value           Float     // قيمة الخصم (نسبة مئوية أو مبلغ)
  isActive        Boolean   @default(true) // حالة الخصم (نشط/غير نشط)
  startDate       DateTime? // تاريخ بداية الخصم (اختياري)
  endDate         DateTime? // تاريخ نهاية الخصم (اختياري)
  minAmount       Float?    // الحد الأدنى للمبلغ لتطبيق الخصم (اختياري)
  maxAmount       Float?    // الحد الأقصى للمبلغ لتطبيق الخصم (اختياري)
  maxUsage        Int?      // الحد الأقصى لعدد مرات استخدام الخصم (اختياري)
  usageCount      Int       @default(0) // عدد مرات استخدام الخصم
  payments        Payment[] // المدفوعات التي تم تطبيق الخصم عليها
  invoices        Invoice[] // الفواتير التي تم تطبيق الخصم عليها
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([isActive])
  @@index([startDate, endDate])
  @@index([type])
}

/// أنواع الخصومات
enum DiscountType {
  PERCENTAGE      // نسبة مئوية
  FIXED_AMOUNT    // مبلغ ثابت
}

/// جدول التبرعات
model Donation {
  id               Int            @id @default(autoincrement())
  donorName        String?
  amount           Float
  date             DateTime       @default(now())
  note             String?
  paymentMethodId  Int?
  paymentMethod    PaymentMethod? @relation(fields: [paymentMethodId], references: [id], onDelete: SetNull)
  cardDetails      Json?          // تفاصيل البطاقة الذهبية (إذا كانت طريقة الدفع بالبطاقة)
  treasuryId       Int
  treasury         Treasury       @relation(fields: [treasuryId], references: [id])

  @@index([paymentMethodId])
}



/// جدول الخزينة
model Treasury {
  id           Int    @id @default(autoincrement())
  balance      Float  @default(0)
  totalIncome  Float  @default(0)
  totalExpense Float  @default(0)
  incomes      Income[]
  expenses     Expense[]
  donations    Donation[]
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

/// جدول المداخيل
model Income {
  id         Int    @id @default(autoincrement())
  treasuryId Int
  treasury   Treasury @relation(fields: [treasuryId], references: [id])
  source     String
  amount     Float
  date       DateTime @default(now())
}

/// جدول فئات المصروفات
model ExpenseCategory {
  id          Int       @id @default(autoincrement())
  name        String    // اسم الفئة
  description String?   // وصف الفئة
  icon        String?   // أيقونة الفئة
  color       String?   // لون الفئة
  isActive    Boolean   @default(true) // حالة الفئة (نشطة/غير نشطة)
  parentId    Int?      // معرف الفئة الأب (للفئات الفرعية)
  parent      ExpenseCategory? @relation("SubCategories", fields: [parentId], references: [id], onDelete: SetNull)
  subCategories ExpenseCategory[] @relation("SubCategories")
  expenses    Expense[] // المصاريف المرتبطة بهذه الفئة
  recurringExpenses RecurringExpense[] // المصاريف الدورية المرتبطة بهذه الفئة
  reminders   ExpenseReminder[] // تذكيرات المصروفات المرتبطة بهذه الفئة
  budgetItems BudgetItem[] // بنود الميزانية المرتبطة بهذه الفئة
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([parentId])
  @@index([isActive])
}

/// جدول الميزانية
model Budget {
  id          Int       @id @default(autoincrement())
  name        String    // اسم الميزانية
  description String?   // وصف الميزانية
  startDate   DateTime  // تاريخ بداية الميزانية
  endDate     DateTime  // تاريخ نهاية الميزانية
  totalAmount Float     // إجمالي مبلغ الميزانية
  status      BudgetStatus @default(ACTIVE) // حالة الميزانية
  items       BudgetItem[] // بنود الميزانية
  transfers   BudgetTransfer[] // تحويلات الميزانية
  reviews     BudgetReview[] // سجل مراجعات الميزانية
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([startDate, endDate])
  @@index([status])
}

/// حالات الميزانية
enum BudgetStatus {
  DRAFT      // مسودة
  ACTIVE     // نشطة
  COMPLETED  // مكتملة
  ARCHIVED   // مؤرشفة
}

/// جدول بنود الميزانية
model BudgetItem {
  id          Int       @id @default(autoincrement())
  budgetId    Int       // معرف الميزانية
  budget      Budget    @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  categoryId  Int       // معرف فئة المصروفات
  category    ExpenseCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  amount      Float     // المبلغ المخصص
  notes       String?   // ملاحظات
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  fromTransfers BudgetTransfer[] @relation("FromItem") // التحويلات التي تم فيها استخدام هذا البند كمصدر
  toTransfers   BudgetTransfer[] @relation("ToItem") // التحويلات التي تم فيها استخدام هذا البند كهدف

  @@index([budgetId])
  @@index([categoryId])
}

/// جدول المصاريف
model Expense {
  id           Int    @id @default(autoincrement())
  treasuryId   Int
  treasury     Treasury @relation(fields: [treasuryId], references: [id])
  categoryId   Int?
  category     ExpenseCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  purpose      String
  amount       Float
  date         DateTime @default(now())
  receipt      String?  // رابط صورة الإيصال
  notes        String?  // ملاحظات إضافية
  receipts     ExpenseReceipt[] // إيصالات المصروف
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt

  @@index([categoryId])
  @@index([date])
}

/// جدول المصاريف الدورية
model RecurringExpense {
  id                 Int       @id @default(autoincrement())
  purpose            String    // الغرض من المصروف
  amount             Float     // المبلغ
  categoryId         Int?      // معرف الفئة (اختياري)
  category           ExpenseCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  startDate          DateTime  // تاريخ بدء التكرار
  endDate            DateTime? // تاريخ انتهاء التكرار (اختياري)
  frequency          String    // التكرار (daily, weekly, monthly, yearly)
  interval           Int       // الفاصل الزمني (كل كم يوم/أسبوع/شهر/سنة)
  lastGeneratedDate  DateTime? // تاريخ آخر توليد
  nextGenerationDate DateTime  // تاريخ التوليد التالي
  isActive           Boolean   @default(true) // حالة المصروف الدوري (نشط/غير نشط)
  notes              String?   // ملاحظات إضافية
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  @@index([categoryId])
  @@index([isActive])
  @@index([nextGenerationDate])
}

/// جدول تذكيرات المصروفات
model ExpenseReminder {
  id            Int       @id @default(autoincrement())
  title         String    // عنوان التذكير
  description   String?   @db.Text // وصف التذكير
  amount        Float?    // المبلغ (اختياري)
  categoryId    Int?      // معرف الفئة (اختياري)
  category      ExpenseCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  dueDate       DateTime  // تاريخ الاستحقاق
  reminderDate  DateTime? // تاريخ التذكير (اختياري)
  priority      String    // الأولوية (LOW, MEDIUM, HIGH)
  status        String    // الحالة (PENDING, COMPLETED, CANCELLED)
  notifyByEmail Boolean   @default(false) // إرسال إشعار بالبريد الإلكتروني
  notifyByPush  Boolean   @default(false) // إرسال إشعار دفع
  emailAddress  String?   // عنوان البريد الإلكتروني (اختياري)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@index([categoryId])
  @@index([status])
  @@index([dueDate])
  @@index([priority])
}

/// جدول إيصالات المصاريف
// هذا حل مؤقت حتى يتم إضافة نموذج ExpenseReceipt إلى ملف schema.prisma
model ExpenseReceipt {
  id           Int      @id @default(autoincrement())
  expenseId    Int      // معرف المصروف
  expense      Expense  @relation(fields: [expenseId], references: [id], onDelete: Cascade)
  fileName     String   // اسم الملف
  fileType     String   // نوع الملف
  fileSize     Int      // حجم الملف
  filePath     String   // مسار الملف
  uploadDate   DateTime @default(now()) // تاريخ الرفع

  @@index([expenseId])
}

/// جدول المقالات
model Article {
  id          Int       @id @default(autoincrement())
  title       String    @db.VarChar(200)
  description String
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  comments    Comment[]
}

/// جدول التعليقات
model Comment {
  id        Int      @id @default(autoincrement())
  text      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  articleId Int
  userId    Int

  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

/// جدول الإشعارات
model Notification {
  id          Int      @id @default(autoincrement())
  title       String
  content     String   @db.Text
  type        NotificationType @default(GENERAL)
  read        Boolean  @default(false) // سيتم إهمال هذا الحقل تدريجياً
  createdAt   DateTime @default(now())
  userId      Int?     // جعل الحقل اختياري للإشعارات الجماعية
  user        User?    @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)
  relatedId   Int?     // معرف العنصر المرتبط (مثل الدرس، الامتحان، إلخ)
  link        String?  // رابط للانتقال إليه عند النقر على الإشعار

  // الحقول الجديدة للإشعارات الجماعية
  groupId     Int?     // معرف المجموعة
  group       NotificationGroup? @relation("GroupNotifications", fields: [groupId], references: [id], onDelete: SetNull)
  isGroupNotification Boolean @default(false) // هل هو إشعار جماعي
  templateId  Int?     // معرف القالب المستخدم
  template    NotificationTemplate? @relation(fields: [templateId], references: [id], onDelete: SetNull)

  // إعدادات الإرسال
  priority    String   @default("MEDIUM") // HIGH, MEDIUM, LOW
  scheduledAt DateTime? // وقت الإرسال المجدول
  sentAt      DateTime? // وقت الإرسال الفعلي
  status      String   @default("PENDING") // PENDING, SENDING, SENT, FAILED

  // العلاقات الجديدة
  recipients  NotificationRecipient[]
  stats       NotificationStats?

  @@index([userId, read])
  @@index([createdAt])
  @@index([groupId])
  @@index([isGroupNotification])
  @@index([status])
  @@index([scheduledAt])
}

/// أنواع الإشعارات
enum NotificationType {
  GENERAL       // إشعار عام
  LESSON        // إشعار متعلق بدرس
  EXAM          // إشعار متعلق بامتحان
  ATTENDANCE    // إشعار متعلق بالحضور
  PAYMENT       // إشعار متعلق بالمدفوعات
  ACHIEVEMENT   // إشعار متعلق بإنجاز
  REMOTE_CLASS  // إشعار متعلق بفصل عن بعد
}

/// أنواع مجموعات الإشعارات
enum GroupType {
  ALL_USERS        // جميع المستخدمين
  BY_ROLE          // حسب الدور
  CUSTOM_SELECTION // اختيار مخصص
  PREDEFINED_GROUP // مجموعة محددة مسبقاً
}

/// جدول مجموعات الإشعارات
model NotificationGroup {
  id              Int      @id @default(autoincrement())
  name            String   // اسم المجموعة
  description     String?  @db.Text // وصف المجموعة
  type            GroupType // نوع المجموعة
  targetRole      UserRole? // الدور المستهدف (للنوع BY_ROLE)
  targetUserIds   String?  @db.Text // معرفات المستخدمين (JSON array للنوع CUSTOM)
  isActive        Boolean  @default(true) // هل المجموعة نشطة
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdBy       Int      // معرف منشئ المجموعة
  creator         User     @relation("CreatedGroups", fields: [createdBy], references: [id])

  // العلاقات
  notifications   Notification[] @relation("GroupNotifications")
  recipients      NotificationRecipient[] @relation("GroupRecipients")

  @@index([type])
  @@index([targetRole])
  @@index([createdBy])
}

/// جدول مستلمي الإشعارات
model NotificationRecipient {
  id              Int      @id @default(autoincrement())
  notificationId  Int      // معرف الإشعار
  notification    Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  userId          Int      // معرف المستلم
  user            User     @relation("ReceivedNotifications", fields: [userId], references: [id], onDelete: Cascade)
  groupId         Int?     // معرف المجموعة (اختياري)
  group           NotificationGroup? @relation("GroupRecipients", fields: [groupId], references: [id], onDelete: SetNull)

  // حالة التسليم والقراءة
  delivered       Boolean  @default(false) // هل تم التسليم
  read            Boolean  @default(false) // هل تم القراءة
  deliveredAt     DateTime? // وقت التسليم
  readAt          DateTime? // وقت القراءة

  // معلومات إضافية
  deliveryMethod  String?  // طريقة التسليم (system, email, sms)
  errorMessage    String?  // رسالة خطأ في حالة فشل التسليم
  retryCount      Int      @default(0) // عدد محاولات الإعادة

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([notificationId, userId]) // منع التكرار
  @@index([userId, read])
  @@index([delivered])
  @@index([groupId])
}

/// جدول قوالب الإشعارات
model NotificationTemplate {
  id          Int      @id @default(autoincrement())
  name        String   @unique // اسم القالب
  title       String   // عنوان القالب
  content     String   @db.Text // محتوى القالب
  type        NotificationType @default(GENERAL) // نوع الإشعار
  variables   String?  @db.Text // المتغيرات المدعومة (JSON)
  isActive    Boolean  @default(true) // هل القالب نشط

  // إعدادات المجموعة الافتراضية
  defaultGroupType    GroupType? // نوع المجموعة الافتراضي
  defaultTargetRole   UserRole?  // الدور المستهدف الافتراضي

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   Int      // معرف منشئ القالب
  creator     User     @relation("CreatedTemplates", fields: [createdBy], references: [id])

  // العلاقات
  notifications Notification[]

  @@index([type])
  @@index([isActive])
  @@index([createdBy])
}

/// جدول إحصائيات الإشعارات
model NotificationStats {
  id                Int      @id @default(autoincrement())
  notificationId    Int      @unique // معرف الإشعار
  notification      Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)

  // إحصائيات التسليم
  totalRecipients   Int      // إجمالي المستلمين
  deliveredCount    Int      @default(0) // عدد المسلم إليهم
  failedCount       Int      @default(0) // عدد الفاشلين
  pendingCount      Int      @default(0) // عدد المعلقين

  // إحصائيات القراءة
  readCount         Int      @default(0) // عدد المقروءة
  unreadCount       Int      @default(0) // عدد غير المقروءة

  // معدلات النجاح
  deliveryRate      Float    @default(0) // معدل التسليم (%)
  readRate          Float    @default(0) // معدل القراءة (%)
  engagementRate    Float    @default(0) // معدل التفاعل (%)

  // أوقات الإحصائيات
  firstDeliveredAt  DateTime? // أول تسليم
  lastDeliveredAt   DateTime? // آخر تسليم
  firstReadAt       DateTime? // أول قراءة
  lastReadAt        DateTime? // آخر قراءة

  calculatedAt      DateTime @default(now()) // وقت حساب الإحصائيات
  updatedAt         DateTime @updatedAt

  @@index([deliveryRate])
  @@index([readRate])
  @@index([calculatedAt])
}

model Surah {
  id           Int     @id @default(autoincrement())
  name         String  @unique
  number       Int     @unique
  totalAyahs   Int
  Exam_points  Exam_points[]
  khatmSessions KhatmSession[] // مجالس الختم المرتبطة بهذه السورة
  quranProgress QuranProgress[]
}

/// جدول صور الطلاب
model StudentImage {
  id          Int      @id @default(autoincrement())
  studentId   Int
  student     Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  imageUrl    String   // رابط الصورة
  description String?  // وصف الصورة
  uploadDate  DateTime @default(now())
  isProfilePic Boolean  @default(false) // هل هي صورة الملف الشخصي
  albumId     Int?     // معرف الألبوم (اختياري)
  album       StudentAlbum? @relation(fields: [albumId], references: [id], onDelete: SetNull)

  @@index([studentId])
  @@index([albumId])
}

/// جدول ألبومات صور الطلاب
model StudentAlbum {
  id          Int      @id @default(autoincrement())
  name        String   // اسم الألبوم
  description String?  // وصف الألبوم
  coverImage  String?  // صورة الغلاف
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  images      StudentImage[] // الصور في الألبوم
}

/// جدول البرامج التعليمية
model Program {
  id          Int      @id @default(autoincrement())
  title       String   // عنوان البرنامج
  description String   @db.Text // وصف البرنامج
  iconName    String   // اسم الأيقونة (FaQuran, FaBookReader, etc.)
  price       String   // سعر البرنامج
  popular     Boolean  @default(false) // هل هو برنامج شائع
  features    ProgramFeature[] // ميزات البرنامج
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

/// جدول ميزات البرامج
model ProgramFeature {
  id        Int     @id @default(autoincrement())
  text      String  // نص الميزة
  programId Int
  program   Program @relation(fields: [programId], references: [id], onDelete: Cascade)
  order     Int     @default(0) // ترتيب الميزة

  @@index([programId])
}

/// جدول صور الحضور
model AttendanceImage {
  id           Int       @id @default(autoincrement())
  attendanceId Int?
  attendance   Attendance? @relation(fields: [attendanceId], references: [id], onDelete: SetNull)
  khatmSessionAttendanceId Int?
  khatmSessionAttendance KhatmSessionAttendance? @relation(fields: [khatmSessionAttendanceId], references: [id], onDelete: SetNull)
  imageUrl     String    // رابط الصورة
  uploadDate   DateTime  @default(now())

  @@index([attendanceId])
  @@index([khatmSessionAttendanceId])
}

/// جدول مجالس الختم
model KhatmSession {
  id          Int       @id @default(autoincrement())
  title       String    // عنوان المجلس
  description String?   @db.Text // وصف المجلس
  date        DateTime  // تاريخ المجلس
  location    String?   // مكان المجلس
  surahId     Int?      // السورة المرتبطة بالمجلس (اختياري)
  surah       Surah?    @relation(fields: [surahId], references: [id], onDelete: SetNull)
  teacherId   Int       // معرف المعلم المسؤول
  teacher     Teacher   @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  attendances KhatmSessionAttendance[] // سجلات الحضور
  isPublic    Boolean   @default(false) // هل المجلس متاح للعرض العام
  isRecurring Boolean   @default(false) // هل المجلس دوري
  recurrencePattern String? // نمط التكرار (يومي، أسبوعي، شهري)
  recurrenceEndDate DateTime? // تاريخ انتهاء التكرار
  parentSessionId Int? // معرف المجلس الأصلي (للمجالس المتكررة)
  parentSession KhatmSession? @relation("RecurringSessions", fields: [parentSessionId], references: [id], onDelete: SetNull)
  childSessions KhatmSession[] @relation("RecurringSessions")
  progressRecords KhatmProgressRecord[] // سجلات تقدم الحفظ
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([date])
  @@index([teacherId])
  @@index([surahId])
  @@index([parentSessionId])
}

/// جدول حضور مجالس الختم
model KhatmSessionAttendance {
  id            Int       @id @default(autoincrement())
  khatmSessionId Int
  khatmSession  KhatmSession @relation(fields: [khatmSessionId], references: [id], onDelete: Cascade)
  studentId     Int
  student       Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  status        AttendanceStatus @default(PRESENT) // حالة الحضور
  note          String?   // ملاحظات
  images        AttendanceImage[] // صور الحضور
  progressRecords KhatmProgressRecord[] // سجلات تقدم الحفظ المرتبطة بالحضور
  createdAt     DateTime  @default(now())

  @@unique([khatmSessionId, studentId])
  @@index([studentId])
}

/// جدول سجلات تقدم الحفظ
model KhatmProgressRecord {
  id                Int       @id @default(autoincrement())
  khatmSessionId    Int
  khatmSession      KhatmSession @relation(fields: [khatmSessionId], references: [id], onDelete: Cascade)
  attendanceId      Int
  attendance        KhatmSessionAttendance @relation(fields: [attendanceId], references: [id], onDelete: Cascade)
  startAyah         Int       // رقم الآية البداية
  endAyah           Int       // رقم الآية النهاية
  memorizedAyahs    Int       // عدد الآيات المحفوظة
  reviewedAyahs     Int       // عدد الآيات المراجعة
  qualityRating     Int       // تقييم جودة الحفظ (من 1 إلى 5)
  notes             String?   @db.Text // ملاحظات المعلم
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([khatmSessionId])
  @@index([attendanceId])
}

/// جدول تقارير إنجاز الختم
model KhatmAchievementReport {
  id                Int       @id @default(autoincrement())
  studentId         Int
  student           Student   @relation(fields: [studentId], references: [id], onDelete: Cascade)
  title             String    // عنوان التقرير
  description       String?   @db.Text // وصف التقرير
  startDate         DateTime  // تاريخ بداية فترة التقرير
  endDate           DateTime  // تاريخ نهاية فترة التقرير
  totalSessions     Int       // إجمالي عدد الجلسات
  attendedSessions  Int       // عدد الجلسات التي تم حضورها
  totalAyahs        Int       // إجمالي عدد الآيات
  memorizedAyahs    Int       // عدد الآيات المحفوظة
  reviewedAyahs     Int       // عدد الآيات المراجعة
  averageRating     Float     // متوسط تقييم الجودة
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([studentId])
  @@index([startDate, endDate])
}

/// جدول النشاطات
model Activity {
  id          Int      @id @default(autoincrement())
  userId      Int
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  type        String   // نوع النشاط (login, attendance, etc.)
  description String   // وصف النشاط
  createdAt   DateTime @default(now())

  @@index([userId])
  @@index([createdAt])
}

/// جدول سجل التواصل مع الأولياء
model ParentCommunication {
  id          Int      @id @default(autoincrement())
  parentId    Int
  parent      Parent   @relation(fields: [parentId], references: [id], onDelete: Cascade)
  userId      Int      // معرف المستخدم الذي قام بالتواصل (معلم أو مدير)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  type        CommunicationType // نوع التواصل
  title       String   // عنوان التواصل
  content     String   @db.Text // محتوى التواصل
  date        DateTime @default(now()) // تاريخ التواصل
  status      CommunicationStatus @default(PENDING) // حالة التواصل
  response    String?  @db.Text // الرد على التواصل (إن وجد)
  responseDate DateTime? // تاريخ الرد
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([parentId])
  @@index([userId])
  @@index([date])
  @@index([status])
}

/// أنواع التواصل مع الأولياء
enum CommunicationType {
  PHONE_CALL  // مكالمة هاتفية
  MEETING     // اجتماع
  EMAIL       // بريد إلكتروني
  SMS         // رسالة نصية
  WHATSAPP    // واتساب
  OTHER       // أخرى
}

/// حالات التواصل مع الأولياء
enum CommunicationStatus {
  PENDING     // قيد الانتظار
  COMPLETED   // مكتمل
  FOLLOW_UP   // متابعة
  CANCELLED   // ملغي
}

/// جدول جدول الحصص
model ClassSchedule {
  id               Int            @id @default(autoincrement())
  day              String         // يوم الأسبوع (SUNDAY, MONDAY, etc.)
  startTime        String         // وقت بدء الحصة (HH:MM)
  endTime          String         // وقت انتهاء الحصة (HH:MM)
  classeId         Int            // معرف الفصل
  classe           Classe         @relation(fields: [classeId], references: [id], onDelete: Cascade)
  teacherSubjectId Int            // معرف علاقة المعلم بالمادة
  teacherSubject   TeacherSubject @relation(fields: [teacherSubjectId], references: [id], onDelete: Cascade)
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@index([classeId])
  @@index([teacherSubjectId])
  @@index([day])
}

/// جدول وحدات المنهج الدراسي
model CurriculumUnit {
  id          Int               @id @default(autoincrement())
  title       String            // عنوان الوحدة
  description String?           @db.Text // وصف الوحدة
  order       Int               // ترتيب الوحدة
  subjectId   Int               // معرف المادة
  subject     Subject           @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  lessons     CurriculumLesson[] // الدروس في هذه الوحدة
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  @@index([subjectId])
  @@index([order])
}

/// جدول دروس المنهج
model CurriculumLesson {
  id          Int                 @id @default(autoincrement())
  title       String              // عنوان الدرس
  description String?             @db.Text // وصف الدرس
  order       Int                 // ترتيب الدرس
  unitId      Int                 // معرف الوحدة
  unit        CurriculumUnit      @relation(fields: [unitId], references: [id], onDelete: Cascade)
  resources   CurriculumResource[] // الموارد التعليمية لهذا الدرس
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  @@index([unitId])
  @@index([order])
}

/// جدول موارد المنهج التعليمية
model CurriculumResource {
  id          Int              @id @default(autoincrement())
  title       String           // عنوان المورد
  type        String           // نوع المورد (pdf, doc, video, etc.)
  url         String           // رابط المورد
  lessonId    Int              // معرف الدرس
  lesson      CurriculumLesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@index([lessonId])
}

/// جدول معايير التقييم
model EvaluationCriteria {
  id          Int      @id @default(autoincrement())
  name        String   // اسم المعيار
  weight      Decimal  @db.Decimal(5,2) // وزن المعيار (0.0 - 1.0)
  description String?  // وصف المعيار
  scores      CriteriaScore[] // درجات المعيار
  examCriteria ExamCriteria[] // الامتحانات المرتبطة بهذا المعيار
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

/// جدول أنواع الامتحانات
model ExamType {
  id              Int      @id @default(autoincrement())
  name            String   // اسم نوع الامتحان
  description     String?  // وصف نوع الامتحان
  evaluationType  String   // نوع التقييم: شفهي، تحريري، عملي
  exams           Exam[]   // الامتحانات من هذا النوع
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

/// جدول المكافآت
model Reward {
  id              Int      @id @default(autoincrement())
  name            String   // اسم المكافأة
  description     String?  // وصف المكافأة
  requiredPoints  Int      // النقاط المطلوبة
  type            String   // نوع المكافأة: شهادة، جائزة، إلخ
  studentRewards  StudentReward[] // الطلاب الذين حصلوا على هذه المكافأة
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

/// جدول معايير التقييم للطلاب المتميزين
model HonorCriteria {
  id              Int      @id @default(autoincrement())
  name            String   // اسم المعيار
  description     String?  // وصف المعيار
  pointsThreshold Int      // الحد الأدنى للنقاط
  isActive        Boolean  @default(true) // حالة المعيار (نشط/غير نشط)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

/// جدول شهادات التقدير
model Certificate {
  id              Int      @id @default(autoincrement())
  title           String   // عنوان الشهادة
  description     String?  // وصف الشهادة
  templateUrl     String?  // رابط قالب الشهادة
  type            String   // نوع الشهادة: إنجاز، تفوق، تقدير
  studentCertificates StudentCertificate[] // الطلاب الذين حصلوا على هذه الشهادة
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

/// جدول شهادات الطلاب
model StudentCertificate {
  id              Int      @id @default(autoincrement())
  studentId       Int
  certificateId   Int
  issueDate       DateTime @default(now()) // تاريخ إصدار الشهادة
  student         Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  certificate     Certificate @relation(fields: [certificateId], references: [id], onDelete: Cascade)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([studentId])
  @@index([certificateId])
}

/// جدول نقاط الطلاب
model StudentPoint {
  id          Int      @id @default(autoincrement())
  studentId   Int
  points      Int      // عدد النقاط
  date        DateTime @default(now()) // تاريخ الحصول على النقاط
  reason      String?  // سبب الحصول على النقاط
  student     Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([studentId])
  @@index([date])
}

/// جدول مكافآت الطلاب
model StudentReward {
  id          Int      @id @default(autoincrement())
  studentId   Int
  rewardId    Int
  date        DateTime @default(now()) // تاريخ الحصول على المكافأة
  student     Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  reward      Reward   @relation(fields: [rewardId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([studentId])
  @@index([rewardId])
}

/// جدول درجات معايير التقييم
model CriteriaScore {
  id            Int                @id @default(autoincrement())
  examPointId   Int
  criteriaId    Int
  score         Decimal            @db.Decimal(5,2) // الدرجة
  examPoint     Exam_points        @relation(fields: [examPointId], references: [id], onDelete: Cascade)
  criteria      EvaluationCriteria @relation(fields: [criteriaId], references: [id], onDelete: Cascade)
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt

  @@index([examPointId])
  @@index([criteriaId])
}

/// جدول أنواع التقييم المخصصة
model CustomEvaluationType {
  id          String    @id @default(uuid())
  name        String    // اسم نوع التقييم
  description String    // وصف نوع التقييم
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

/// جدول تقييم أداء المعلمين
model TeacherEvaluation {
  id              Int      @id @default(autoincrement())
  teacherId       Int      // معرف المعلم
  teacher         Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  evaluatorId     Int      // معرف المقيم (مدير أو مشرف)
  evaluator       User     @relation(fields: [evaluatorId], references: [id], onDelete: Cascade)
  evaluationDate  DateTime @default(now()) // تاريخ التقييم
  teachingSkills  Decimal  @db.Decimal(5,2) // مهارات التدريس (0-10)
  classManagement Decimal  @db.Decimal(5,2) // إدارة الفصل (0-10)
  studentProgress Decimal  @db.Decimal(5,2) // تقدم الطلاب (0-10)
  attendance      Decimal  @db.Decimal(5,2) // الحضور والالتزام (0-10)
  communication   Decimal  @db.Decimal(5,2) // التواصل مع الطلاب والزملاء (0-10)
  overallRating   Decimal  @db.Decimal(5,2) // التقييم العام (0-10)
  strengths       String?  @db.Text // نقاط القوة
  improvements    String?  @db.Text // مجالات التحسين
  comments        String?  @db.Text // ملاحظات إضافية
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([teacherId])
  @@index([evaluatorId])
  @@index([evaluationDate])
}

/// جدول جدول حصص المعلمين
model TeacherSchedule {
  id              Int      @id @default(autoincrement())
  teacherId       Int      // معرف المعلم
  teacher         Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  day             String   // يوم الأسبوع (SUNDAY, MONDAY, etc.)
  startTime       String   // وقت بدء الحصة (HH:MM)
  endTime         String   // وقت انتهاء الحصة (HH:MM)
  classeId        Int      // معرف الفصل
  classe          Classe   @relation(fields: [classeId], references: [id], onDelete: Cascade)
  subjectId       Int      // معرف المادة
  subject         Subject  @relation(fields: [subjectId], references: [id], onDelete: Cascade)
  location        String?  // مكان الحصة (اختياري)
  notes           String?  // ملاحظات (اختياري)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([teacherId])
  @@index([classeId])
  @@index([subjectId])
  @@index([day])
}

/// جدول إنجازات المعلمين
model TeacherAchievement {
  id              Int      @id @default(autoincrement())
  teacherId       Int      // معرف المعلم
  teacher         Teacher  @relation(fields: [teacherId], references: [id], onDelete: Cascade)
  title           String   // عنوان الإنجاز
  description     String   @db.Text // وصف الإنجاز
  achievementDate DateTime @default(now()) // تاريخ الإنجاز
  type            String   // نوع الإنجاز (تعليمي، إداري، إلخ)
  attachmentUrl   String?  // رابط المرفق (اختياري)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([teacherId])
  @@index([achievementDate])
  @@index([type])
}

/// جدول بنك الأسئلة
model QuestionBank {
  id          Int       @id @default(autoincrement())
  name        String    // اسم بنك الأسئلة
  description String?   @db.Text // وصف بنك الأسئلة
  subjectId   Int?      // معرف المادة (اختياري)
  subject     Subject?  @relation(fields: [subjectId], references: [id], onDelete: SetNull)
  questions   Question[] // الأسئلة في هذا البنك
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([subjectId])
}

/// أنواع الأسئلة
enum QuestionType {
  MULTIPLE_CHOICE  // اختيار من متعدد
  TRUE_FALSE       // صح أو خطأ
  SHORT_ANSWER     // إجابة قصيرة
  ESSAY            // مقال
  MATCHING         // مطابقة
  FILL_BLANK       // ملء الفراغات
  ORDERING         // ترتيب
}

/// مستويات صعوبة الأسئلة
enum DifficultyLevel {
  EASY      // سهل
  MEDIUM    // متوسط
  HARD      // صعب
  VERY_HARD // صعب جداً
}

/// جدول الأسئلة
model Question {
  id              Int             @id @default(autoincrement())
  text            String          @db.Text // نص السؤال
  type            QuestionType    // نوع السؤال
  difficultyLevel DifficultyLevel @default(MEDIUM) // مستوى الصعوبة
  points          Float           @default(1) // النقاط المخصصة للسؤال
  bankId          Int             // معرف بنك الأسئلة
  bank            QuestionBank    @relation(fields: [bankId], references: [id], onDelete: Cascade)
  options         QuestionOption[] // خيارات السؤال
  answers         QuestionAnswer[] // إجابات السؤال
  examQuestions   ExamQuestion[]  // علاقة السؤال بالامتحانات
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@index([bankId])
  @@index([type])
  @@index([difficultyLevel])
}

/// جدول خيارات السؤال
model QuestionOption {
  id          Int       @id @default(autoincrement())
  questionId  Int       // معرف السؤال
  question    Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)
  text        String    @db.Text // نص الخيار
  isCorrect   Boolean   @default(false) // هل هو الخيار الصحيح
  order       Int       @default(0) // ترتيب الخيار
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([questionId])
}

/// جدول إجابات السؤال
model QuestionAnswer {
  id          Int       @id @default(autoincrement())
  questionId  Int       // معرف السؤال
  question    Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)
  text        String    @db.Text // نص الإجابة
  isCorrect   Boolean   @default(true) // هل هي الإجابة الصحيحة
  explanation String?   @db.Text // شرح الإجابة
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([questionId])
}

/// جدول ربط الأسئلة بالامتحانات
model ExamQuestion {
  id          Int       @id @default(autoincrement())
  examId      Int       // معرف الامتحان
  exam        Exam      @relation(fields: [examId], references: [id], onDelete: Cascade)
  questionId  Int       // معرف السؤال
  question    Question  @relation(fields: [questionId], references: [id], onDelete: Cascade)
  order       Int       @default(0) // ترتيب السؤال في الامتحان
  points      Float?    // النقاط المخصصة للسؤال في هذا الامتحان (اختياري، يستخدم قيمة السؤال الافتراضية إذا لم يتم تحديده)
  studentAnswers StudentAnswer[] // إجابات الطلاب على هذا السؤال
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([examId, questionId])
  @@index([examId])
  @@index([questionId])
}

/// جدول إجابات الطلاب على الأسئلة
model StudentAnswer {
  id              Int       @id @default(autoincrement())
  examPointId     Int       // معرف نقطة الامتحان
  examPoint       Exam_points @relation(fields: [examPointId], references: [id], onDelete: Cascade)
  examQuestionId  Int       // معرف سؤال الامتحان
  examQuestion    ExamQuestion @relation(fields: [examQuestionId], references: [id], onDelete: Cascade)
  answer          String    @db.Text // إجابة الطالب
  isCorrect       Boolean?  // هل الإجابة صحيحة (قد تكون null إذا لم يتم تصحيحها بعد)
  points          Float?    // النقاط المحصلة
  feedback        String?   @db.Text // ملاحظات المصحح
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@unique([examPointId, examQuestionId])
  @@index([examPointId])
  @@index([examQuestionId])
}

/// جدول ربط الامتحانات بمعايير التقييم
model ExamCriteria {
  id                  Int                 @id @default(autoincrement())
  examId              Int                 // معرف الامتحان
  criteriaId          Int                 // معرف معيار التقييم
  exam                Exam                @relation(fields: [examId], references: [id], onDelete: Cascade)
  criteria            EvaluationCriteria  @relation(fields: [criteriaId], references: [id], onDelete: Cascade)
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt

  @@unique([examId, criteriaId])
  @@index([examId])
  @@index([criteriaId])
}

/// جدول إعدادات النظام
model SystemSettings {
  id        Int      @id @default(autoincrement())
  key       String   @unique // مفتاح الإعداد
  value     String   @db.Text // قيمة الإعداد (JSON)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([key])
}

/// جدول تحويلات الميزانية
model BudgetTransfer {
  id          Int       @id @default(autoincrement())
  budgetId    Int       // معرف الميزانية
  budget      Budget    @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  fromItemId  Int       // معرف البند المصدر
  fromItem    BudgetItem @relation("FromItem", fields: [fromItemId], references: [id], onDelete: Cascade)
  toItemId    Int       // معرف البند الهدف
  toItem      BudgetItem @relation("ToItem", fields: [toItemId], references: [id], onDelete: Cascade)
  amount      Float     // المبلغ المحول
  date        DateTime  @default(now()) // تاريخ التحويل
  notes       String?   // ملاحظات
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([budgetId])
  @@index([fromItemId])
  @@index([toItemId])
  @@index([date])
}

/// جدول مراجعات الميزانية
model BudgetReview {
  id          Int       @id @default(autoincrement())
  budgetId    Int       // معرف الميزانية
  budget      Budget    @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  oldStatus   BudgetStatus // الحالة السابقة
  newStatus   BudgetStatus // الحالة الجديدة
  notes       String?   @db.Text // ملاحظات المراجعة
  date        DateTime  @default(now()) // تاريخ المراجعة
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([budgetId])
  @@index([date])
}

/// نموذج السبورة التفاعلية
model Whiteboard {
  id              Int       @id @default(autoincrement())
  name            String    // اسم السبورة
  content         String?   @db.Text // محتوى السبورة (JSON)
  isActive        Boolean   @default(true) // هل السبورة نشطة
  remoteClassId   Int       // معرف الفصل الافتراضي
  remoteClass     RemoteClass @relation(fields: [remoteClassId], references: [id], onDelete: Cascade)
  createdBy       Int       // معرف المستخدم الذي أنشأ السبورة
  creator         User      @relation(fields: [createdBy], references: [id])

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

/// نموذج مشاركة الشاشة
model ScreenShare {
  id              Int       @id @default(autoincrement())
  status          String    // حالة مشاركة الشاشة (active, stopped)
  remoteClassId   Int       // معرف الفصل الافتراضي
  remoteClass     RemoteClass @relation(fields: [remoteClassId], references: [id], onDelete: Cascade)
  sharedBy        Int       // معرف المستخدم الذي يشارك الشاشة
  user            User      @relation(fields: [sharedBy], references: [id])

  startTime       DateTime  @default(now()) // وقت بدء المشاركة
  endTime         DateTime? // وقت انتهاء المشاركة
}

/// جدول التقارير
model Report {
  id          Int       @id @default(autoincrement())
  title       String    // عنوان التقرير
  description String?   @db.Text // وصف التقرير
  type        String    // نوع التقرير (financial, academic, etc.)
  data        String    @db.LongText // بيانات التقرير بصيغة JSON
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@index([type])
  @@index([createdAt])
}

/// جدول التقارير الموحدة للمشرفين (أدبي + مالي)
model SupervisorReport {
  id                Int       @id @default(autoincrement())
  title             String    // عنوان التقرير
  description       String?   @db.Text // وصف التقرير
  periodStart       DateTime  // تاريخ بداية الفترة
  periodEnd         DateTime  // تاريخ نهاية الفترة
  literaryContent   String?   @db.LongText // محتوى التقرير الأدبي بصيغة HTML
  financialData     String?   @db.LongText // بيانات التقرير المالي بصيغة JSON
  officeSettings    String?   @db.Text // إعدادات المكتب البلدي بصيغة JSON
  status            String    @default("DRAFT") // حالة التقرير: DRAFT, PUBLISHED, ARCHIVED
  createdBy         Int?      // معرف المستخدم الذي أنشأ التقرير
  creator           User?     @relation(fields: [createdBy], references: [id], onDelete: SetNull)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@index([periodStart, periodEnd])
  @@index([status])
  @@index([createdAt])
  @@index([createdBy])
}

/// جدول مشاركة التقارير
model ReportShare {
  id              Int       @id @default(autoincrement())
  token           String    @unique // رمز المشاركة الفريد
  reportId        String    // معرف التقرير
  reportType      String    // نوع التقرير (financial, budget, etc.)
  createdBy       String    // معرف المستخدم الذي أنشأ المشاركة
  shareMethod     String?   // طريقة المشاركة (LINK, EMAIL)
  recipients      String?   // المستلمون (في حالة المشاركة بالبريد الإلكتروني)
  expiryDate      DateTime? // تاريخ انتهاء صلاحية المشاركة
  hasPassword     Boolean   @default(false) // هل المشاركة محمية بكلمة مرور
  password        String?   // كلمة المرور المشفرة
  accessCount     Int       @default(0) // عدد مرات الوصول
  lastAccessedAt  DateTime? // تاريخ آخر وصول
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([token])
  @@index([reportId, reportType])
  @@index([createdBy])
  @@index([expiryDate])
}

/// جدول جدولة إرسال التقارير
model ReportSchedule {
  id              Int       @id @default(autoincrement())
  reportId        String    // معرف التقرير
  reportType      String    // نوع التقرير (financial, budget, etc.)
  recipients      String    // المستلمون (مفصولين بفواصل)
  frequency       String    // تكرار الإرسال (daily, weekly, monthly)
  startDate       DateTime  // تاريخ بدء الجدولة
  dayOfWeek       Int?      // يوم الأسبوع (0-6، حيث 0 هو الأحد) - للتكرار الأسبوعي
  dayOfMonth      Int?      // يوم الشهر (1-31) - للتكرار الشهري
  format          String    // صيغة التقرير (pdf, excel)
  createdBy       String    // معرف المستخدم الذي أنشأ الجدولة
  isActive        Boolean   @default(true) // هل الجدولة نشطة
  lastSentAt      DateTime? // تاريخ آخر إرسال
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([reportId, reportType])
  @@index([createdBy])
  @@index([frequency])
  @@index([isActive])
}

/// جدول سجلات بداية حفظ القرآن للتلاميذ
model StudentMemorizationStart {
  id              Int      @id @default(autoincrement())
  studentId       Int      // معرف التلميذ
  student         Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  startDate       DateTime // تاريخ بداية الحفظ (بصيغة DD/MM/YYYY)
  startingJuz     Int      // الجزء الذي بدأ فيه الحفظ (1-30)
  startingSurah   Int?     // السورة التي بدأ فيها (اختياري)
  startingVerse   Int?     // الآية التي بدأ فيها (اختياري)
  level           String   // المستوى: مبتدئ، متوسط، متقدم
  notes           String?  @db.Text // ملاحظات إضافية
  isActive        Boolean  @default(true) // هل هذا السجل نشط (آخر نقطة بداية)
  createdBy       String?  // معرف المستخدم الذي أدخل البيانات
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([studentId])
  @@index([startDate])
  @@index([startingJuz])
  @@index([isActive])
  @@unique([studentId, isActive]) // تلميذ واحد يمكن أن يكون له سجل نشط واحد فقط
}

/// جدول وصولات التسجيل للتلاميذ
model StudentRegistrationReceipt {
  id              Int      @id @default(autoincrement())
  studentId       Int      // معرف التلميذ
  student         Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  receiptNumber   String   @unique // رقم الوصل الفريد
  issueDate       DateTime @default(now()) // تاريخ الإصدار (بصيغة DD/MM/YYYY)
  registrationFee Float    // رسوم التسجيل
  paymentStatus   String   @default("PENDING") // حالة الدفع: PENDING, PAID, CANCELLED
  receiptData     Json?    // بيانات الوصل بصيغة JSON (للطباعة)
  pdfPath         String?  // مسار ملف PDF للوصل
  isPrinted       Boolean  @default(false) // هل تم طباعة الوصل
  printedAt       DateTime? // تاريخ الطباعة
  printedBy       String?  // معرف المستخدم الذي طبع الوصل
  notes           String?  @db.Text // ملاحظات إضافية
  createdBy       String?  // معرف المستخدم الذي أنشأ الوصل
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([studentId])
  @@index([receiptNumber])
  @@index([issueDate])
  @@index([paymentStatus])
  @@index([isPrinted])
}
'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

import { FaHandHoldingHeart, FaQuran, FaGraduationCap, FaUsers, FaCreditCard, FaPhone } from 'react-icons/fa'

import SiteLogo from '@/components/SiteLogo'

// واجهة لمعلومات التبرع
interface DonationInfo {
  phone1: string;
  phone2?: string;
  ccpAccount: string;
  cpaAccount: string;
  bdrAccount: string;
  description?: string;

}



export default function DonationsPage() {
  
  const [donationInfo, setDonationInfo] = useState<DonationInfo | null>(null)
  const [isLoadingDonationInfo, setIsLoadingDonationInfo] = useState(false)

  // جلب معلومات التبرع وطرق الدفع
  useEffect(() => {
    const fetchDonationInfo = async () => {
      try {
        setIsLoadingDonationInfo(true)
        const response = await fetch('/api/settings')
        if (!response.ok) {
          throw new Error('فشل في جلب معلومات التبرع')
        }
        const data = await response.json()
        if (data.settings && data.settings.contactInfo && data.settings.contactInfo.donationInfo) {
          setDonationInfo(data.settings.contactInfo.donationInfo)
        }
      } catch (error) {
        console.error('خطأ في جلب معلومات التبرع:', error)
        // لا نظهر رسالة خطأ للمستخدم هنا لتجنب الإزعاج
      } finally {
        setIsLoadingDonationInfo(false)
      }
    }



    fetchDonationInfo()
  }, [])


  return (
    <div className="container mx-auto py-12 px-4 bg-gradient-to-b from-[#f8fffd] to-white" dir="rtl">
      {/* Hero Section */}
      <div className="text-center mb-12">
        {/* شعار الموقع */}
        <div className="flex justify-center mb-6">
          <SiteLogo size="xl" showText={false} />
        </div>

        <div className="inline-block mb-4">
          <FaHandHoldingHeart className="text-6xl text-[var(--primary-color)] mx-auto mb-2" />
        </div>
        <h1 className="text-3xl md:text-4xl font-bold text-[var(--primary-color)] mb-4 border-r-4 border-[var(--primary-color)] pr-3 inline-block">ساهم في دعم مشروعنا التعليمي</h1>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto mt-4">
          تبرعك يساعدنا في توفير تعليم قرآني عالي الجودة للطلاب وتطوير المنصة التعليمية
        </p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">

        {/* Information Section */}
        <div className="space-y-8">
          <div className="bg-white p-8 rounded-lg shadow-md border border-[#e0f2ef] hover:shadow-lg transition-shadow duration-300">
            <h3 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center">
              <FaQuran className="ml-2" />
              كيف نستخدم تبرعاتكم؟
            </h3>
            <div className="mb-4 p-3 bg-[#f0f9f7] rounded-lg border border-[var(--primary-color)] border-opacity-20">
              <p className="text-sm text-gray-700 flex items-center">
                <FaCreditCard className="ml-2 text-[var(--primary-color)]" />
                يمكنك التبرع باستخدام طرق دفع متعددة مباشرة من خلال نموذج التبرع
              </p>
            </div>
            <ul className="space-y-3">
              <li className="flex items-start">
                <svg className="w-5 h-5 text-[var(--primary-color)] mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>توفير منح دراسية للطلاب المتميزين</span>
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-[var(--primary-color)] mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>تطوير المنصة التعليمية وتحسين تجربة المستخدم</span>
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-[var(--primary-color)] mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>دعم المعلمين وتوفير دورات تدريبية لهم</span>
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-[var(--primary-color)] mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>إنتاج محتوى تعليمي عالي الجودة</span>
              </li>
            </ul>
          </div>

          <div className="bg-white p-8 rounded-lg shadow-md border border-[#e0f2ef] hover:shadow-lg transition-shadow duration-300">
            <h3 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center">
              <FaGraduationCap className="ml-2" />
              لماذا تدعمنا؟
            </h3>
            <p className="text-gray-600 mb-4">
              مشروعنا يهدف إلى نشر تعليم القرآن الكريم وعلومه بطرق حديثة ومبتكرة، وتبرعك يساهم في:
            </p>
            <ul className="space-y-3">
              <li className="flex items-start">
                <svg className="w-5 h-5 text-[var(--primary-color)] mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>نشر تعليم القرآن الكريم للجميع</span>
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-[var(--primary-color)] mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>دعم الطلاب غير القادرين على دفع الرسوم</span>
              </li>
              <li className="flex items-start">
                <svg className="w-5 h-5 text-[var(--primary-color)] mt-1 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>المساهمة في مشروع خيري تعليمي</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Donation Information Section */}
      {donationInfo && (
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-[var(--primary-color)] mb-8 text-center border-r-4 border-[var(--primary-color)] pr-3 inline-block">
            <FaHandHoldingHeart className="inline-block ml-2" />
            معلومات التبرع
          </h2>
          <div className="bg-white p-8 rounded-lg shadow-md border border-[#e0f2ef] hover:shadow-lg transition-shadow duration-300">
            {donationInfo.description && (
              <p className="text-gray-600 mb-6 text-center text-lg">{donationInfo.description}</p>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* أرقام الهاتف */}
              <div className="space-y-4">
                <h3 className="font-bold text-xl text-gray-800 flex items-center">
                  <FaPhone className="ml-2 text-[var(--primary-color)]" />
                  أرقام الهاتف
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center p-4 bg-[#f0f9f7] rounded-lg border border-[var(--primary-color)] border-opacity-20">
                    <span className="font-medium text-gray-700 ml-2">الهاتف الأول:</span>
                    <span className="text-[var(--primary-color)] font-bold text-lg">{donationInfo.phone1}</span>
                  </div>
                  {donationInfo.phone2 && (
                    <div className="flex items-center p-4 bg-[#f0f9f7] rounded-lg border border-[var(--primary-color)] border-opacity-20">
                      <span className="font-medium text-gray-700 ml-2">الهاتف الثاني:</span>
                      <span className="text-[var(--primary-color)] font-bold text-lg">{donationInfo.phone2}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* الحسابات البنكية */}
              <div className="space-y-4">
                <h3 className="font-bold text-xl text-gray-800 flex items-center">
                  <FaCreditCard className="ml-2 text-[var(--primary-color)]" />
                  الحسابات البنكية
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center p-4 bg-[#f0f9f7] rounded-lg border border-[var(--primary-color)] border-opacity-20">
                    <span className="font-medium text-gray-700 ml-2">حساب CCP:</span>
                    <span className="text-[var(--primary-color)] font-bold text-lg">{donationInfo.ccpAccount}</span>
                  </div>
                  <div className="flex items-center p-4 bg-[#f0f9f7] rounded-lg border border-[var(--primary-color)] border-opacity-20">
                    <span className="font-medium text-gray-700 ml-2">حساب CPA:</span>
                    <span className="text-[var(--primary-color)] font-bold text-lg">{donationInfo.cpaAccount}</span>
                  </div>
                  <div className="flex items-center p-4 bg-[#f0f9f7] rounded-lg border border-[var(--primary-color)] border-opacity-20">
                    <span className="font-medium text-gray-700 ml-2">حساب BDR:</span>
                    <span className="text-[var(--primary-color)] font-bold text-lg">{donationInfo.bdrAccount}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Testimonials */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-[var(--primary-color)] mb-8 text-center border-r-4 border-[var(--primary-color)] pr-3 inline-block">
          <FaUsers className="inline-block ml-2" />
          ماذا يقول المتبرعون عنا
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md border border-[#e0f2ef] hover:shadow-lg transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-[var(--primary-color)] rounded-full flex items-center justify-center text-white text-xl font-bold">
                أ
              </div>
              <div className="mr-4">
                <h4 className="font-semibold">أحمد محمد</h4>
                <p className="text-sm text-gray-500">متبرع منذ 2023</p>
              </div>
            </div>
            <p className="text-gray-600">
              سعيد جداً بالمساهمة في هذا المشروع المبارك، رأيت بنفسي تأثيره الإيجابي على تعليم أبنائنا القرآن الكريم.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-[#e0f2ef] hover:shadow-lg transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-[var(--primary-color)] rounded-full flex items-center justify-center text-white text-xl font-bold">
                س
              </div>
              <div className="mr-4">
                <h4 className="font-semibold">سارة علي</h4>
                <p className="text-sm text-gray-500">متبرعة شهرية</p>
              </div>
            </div>
            <p className="text-gray-600">
              أقوم بالتبرع الشهري لهذا المشروع لأنني أؤمن برسالته السامية في نشر تعليم القرآن الكريم بطرق عصرية.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md border border-[#e0f2ef] hover:shadow-lg transition-all duration-300">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-[var(--primary-color)] rounded-full flex items-center justify-center text-white text-xl font-bold">
                م
              </div>
              <div className="mr-4">
                <h4 className="font-semibold">محمود خالد</h4>
                <p className="text-sm text-gray-500">متبرع</p>
              </div>
            </div>
            <p className="text-gray-600">
              أعجبني الشفافية في استخدام التبرعات والتقارير الدورية التي توضح كيفية استثمار الأموال في تطوير المنصة.
            </p>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-16 bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] text-white p-8 rounded-lg text-center shadow-lg">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">ساهم معنا في نشر تعليم القرآن الكريم</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          كل تبرع، مهما كان صغيراً، يساهم في تحقيق رسالتنا. انضم إلينا اليوم ودعم مشروعنا التعليمي.
        </p>
        <Button
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          className="bg-white text-[var(--primary-color)] hover:bg-gray-100 text-lg px-8 py-3 shadow-md hover:shadow-lg transition-all duration-300 font-bold"
        >
          <FaHandHoldingHeart className="inline-block ml-2" />
          تبرع الآن
        </Button>
      </div>


    </div>
  )
}

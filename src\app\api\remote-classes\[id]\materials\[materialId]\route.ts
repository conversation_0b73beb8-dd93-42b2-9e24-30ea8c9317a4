import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";

// GET: جلب مادة تعليمية محددة
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string, materialId: string } }
) {
    try {
        const remoteClassId = parseInt(params.id);
        const materialId = parseInt(params.materialId);

        if (isNaN(remoteClassId) || isNaN(materialId)) {
            return NextResponse.json(
                { message: "معرف غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // جلب المادة التعليمية
        const material = await prisma.material.findUnique({
            where: {
                id: materialId,
                remoteClassId
            }
        });

        if (!material) {
            return NextResponse.json(
                { message: "المادة التعليمية غير موجودة" },
                { status: 404 }
            );
        }

        // التحقق من الصلاحيات (المسؤول يرى كل شيء)
        if (userData.role !== 'ADMIN') {
            // التحقق من وجود البيانات المطلوبة
            const remoteClass = await prisma.remoteClass.findUnique({
                where: { id: remoteClassId },
                include: {
                    attendees: {
                        select: { id: true }
                    },
                    classe: {
                        include: {
                            students: {
                                select: { id: true }
                            }
                        }
                    }
                }
            });

            if (!remoteClass) {
                return NextResponse.json(
                    { message: "بيانات الفصل غير متوفرة" },
                    { status: 404 }
                );
            }

            // المعلم يرى المواد التعليمية للفصول التي يدرسها
            if (userData.role === 'TEACHER' && remoteClass.instructorId !== userData.id) {
                return NextResponse.json(
                    { message: "غير مصرح به" },
                    { status: 403 }
                );
            }

            // الطالب يرى المواد التعليمية للفصول التي ينتمي إليها
            if (userData.role === 'STUDENT') {
                // التحقق من قائمة الحضور
                const isAttendee = remoteClass.attendees.some((a: { id: number }) => a.id === userData.id);

                // التحقق من طلاب الفصل
                let isClassStudent = false;
                if (remoteClass.classe) {
                    // جلب الطالب المرتبط بالمستخدم الحالي
                    const student = await prisma.student.findFirst({
                        where: {
                            username: userData.username,
                            classeId: remoteClass.classe.id
                        }
                    });

                    isClassStudent = !!student;
                }

                // السماح بالوصول إذا كان الطالب في قائمة الحضور أو في الفصل
                if (!isAttendee && !isClassStudent) {
                    return NextResponse.json(
                        { message: "غير مصرح به" },
                        { status: 403 }
                    );
                }
            }
        }

        return NextResponse.json(material);
    } catch (error) {
        console.error('Error fetching material:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب المادة التعليمية" },
            { status: 500 }
        );
    }
}

// PATCH: تحديث مادة تعليمية
export async function PATCH(
    request: NextRequest,
    { params }: { params: { id: string, materialId: string } }
) {
    try {
        const remoteClassId = parseInt(params.id);
        const materialId = parseInt(params.materialId);

        if (isNaN(remoteClassId) || isNaN(materialId)) {
            return NextResponse.json(
                { message: "معرف غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
                { status: 401 }
            );
        }

        // جلب المادة التعليمية والفصل للتحقق من الصلاحيات
        const material = await prisma.material.findUnique({
            where: {
                id: materialId,
                remoteClassId
            },
            include: {
                remoteClass: true
            }
        });

        if (!material) {
            return NextResponse.json(
                { message: "المادة التعليمية غير موجودة" },
                { status: 404 }
            );
        }

        // التحقق من أن المستخدم هو المعلم المسؤول عن الفصل أو مسؤول
        if (userData.role === 'TEACHER' && material.remoteClass.instructorId !== userData.id) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون المعلم المسؤول عن هذا الفصل" },
                { status: 403 }
            );
        }

        const body = await request.json();

        // تحديث المادة التعليمية
        const updatedMaterial = await prisma.material.update({
            where: {
                id: materialId
            },
            data: {
                title: body.title,
                description: body.description,
                fileUrl: body.fileUrl,
                fileType: body.fileType
            }
        });

        return NextResponse.json(updatedMaterial);
    } catch (error) {
        console.error('Error updating material:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء تحديث المادة التعليمية" },
            { status: 500 }
        );
    }
}

// DELETE: حذف مادة تعليمية
export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string, materialId: string } }
) {
    try {
        const remoteClassId = parseInt(params.id);
        const materialId = parseInt(params.materialId);

        if (isNaN(remoteClassId) || isNaN(materialId)) {
            return NextResponse.json(
                { message: "معرف غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
                { status: 401 }
            );
        }

        // جلب المادة التعليمية والفصل للتحقق من الصلاحيات
        const material = await prisma.material.findUnique({
            where: {
                id: materialId,
                remoteClassId
            },
            include: {
                remoteClass: true
            }
        });

        if (!material) {
            return NextResponse.json(
                { message: "المادة التعليمية غير موجودة" },
                { status: 404 }
            );
        }

        // التحقق من أن المستخدم هو المعلم المسؤول عن الفصل أو مسؤول
        if (userData.role === 'TEACHER' && material.remoteClass.instructorId !== userData.id) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون المعلم المسؤول عن هذا الفصل" },
                { status: 403 }
            );
        }

        // حذف المادة التعليمية
        await prisma.material.delete({
            where: {
                id: materialId
            }
        });

        return NextResponse.json({ message: "تم حذف المادة التعليمية بنجاح" });
    } catch (error) {
        console.error('Error deleting material:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء حذف المادة التعليمية" },
            { status: 500 }
        );
    }
}

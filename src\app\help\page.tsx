'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ArrowRight, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import HelpSidebar, { helpCategories } from '@/components/help/HelpSidebar';
import HelpContent from '@/components/help/HelpContent';
import { helpContentData } from '@/data/helpContent';
import '@/styles/help-page.css';

export default function HelpPage() {
  const [activeCategory, setActiveCategory] = useState('getting-started');
  const [searchQuery, setSearchQuery] = useState('');
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  // فلترة المحتوى بناءً على البحث
  const getFilteredContent = () => {
    const categoryContent = helpContentData[activeCategory] || [];

    if (!searchQuery.trim()) {
      return categoryContent;
    }

    return categoryContent.filter(item =>
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.content.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const filteredContent = getFilteredContent();
  const activeCatego = helpCategories.find(cat => cat.id === activeCategory);

  return (
    <div className="help-page-background help-floating-elements" dir="rtl">
      {/* Header */}
      <header className="help-main-header">
        <div className="container mx-auto px-4 py-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                className="lg:hidden p-3 rounded-xl hover:bg-gray-100 transition-all duration-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
              <div className="flex items-center gap-3">
                <div className="help-icon-bounce">
                  <div className="w-12 h-12 bg-gradient-to-br from-[var(--primary-color)] to-[var(--secondary-color)] rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg">
                    ؟
                  </div>
                </div>
                <h1 className="help-main-title text-4xl font-bold">
                  مركز المساعدة الشامل
                </h1>
              </div>
            </div>
            <Link href="/">
              <Button variant="outline" className="help-button-outline flex items-center gap-2">
                <ArrowRight size={16} />
                العودة إلى الصفحة الرئيسية
              </Button>
            </Link>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-6 mb-6 border border-blue-100">
            <p className="text-gray-700 text-lg leading-relaxed">
              🎯 مرحباً بك في مركز المساعدة الشامل للمديرين. هنا ستجد دليلاً تفصيلياً لفهم وإدارة جميع أقسام نظام برهان للقرآن الكريم مع أكثر من <strong>100 موضوع مساعدة</strong> و<strong>500 خطوة عملية</strong>.
            </p>
          </div>

          {/* شريط البحث المحسن */}
          <div className="help-search-container max-w-lg mx-auto">
            <Search className="help-search-icon" size={20} />
            <Input
              type="text"
              placeholder="ابحث في مواضيع المساعدة... (أكثر من 100 موضوع)"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="help-search-input"
            />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex min-h-[calc(100vh-200px)]">
        {/* Sidebar */}
        <div className={`${isSidebarOpen ? 'block' : 'hidden'} lg:block ${isSidebarOpen ? 'help-sidebar open' : 'help-sidebar'}`}>
          <HelpSidebar
            categories={helpCategories}
            activeCategory={activeCategory}
            onCategoryChange={setActiveCategory}
            className="h-full help-sidebar"
          />
        </div>

        {/* Content Area */}
        <div className="flex-1 flex flex-col help-content-area">
          {/* Category Header */}
          <div className="help-category-header p-8">
            <div className="max-w-6xl mx-auto">
              <div className="flex items-center gap-4 mb-4">
                {activeCatego && (
                  <>
                    <div className="w-16 h-16 bg-gradient-to-br from-[var(--primary-color)] to-[var(--secondary-color)] rounded-2xl flex items-center justify-center text-white shadow-lg">
                      <activeCatego.icon size={28} />
                    </div>
                    <div>
                      <h2 className="help-category-title text-3xl font-bold">{activeCatego.title}</h2>
                      <p className="help-category-description text-lg mt-1">{activeCatego?.description}</p>
                    </div>
                  </>
                )}
              </div>

              {searchQuery && (
                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4 mt-4">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <Search size={20} />
                    <span className="font-medium">
                      عرض {filteredContent.length} نتيجة للبحث عن "{searchQuery}"
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Help Content */}
          <div className="help-fade-in">
            <HelpContent
              categoryId={activeCategory}
              items={filteredContent}
            />
          </div>
        </div>
      </div>

      {/* Overlay for mobile sidebar */}
      {isSidebarOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}
    </div>
  );
}

/*
==============================================================================
    مثال تدقيق تطبيق الويب - Praetorian Web Audit Example
    
    الوصف: مثال شامل لتدقيق تطبيق ويب باستخدام مكتبة Praetorian
    المؤلف: Praetorian Team
==============================================================================
*/

# تحميل مكتبة Praetorian
load "../praetorian.ring"

/*
==============================================================================
    الدالة الرئيسية لتدقيق الويب
==============================================================================
*/

func main
    # إنشاء مثيل من المكتبة
    oPraetorian = CreatePraetorian()
    
    # طباعة رسالة الترحيب
    ? ""
    ? "=============================================="
    ? "مدقق تطبيقات الويب - Praetorian Web Auditor"
    ? "=============================================="
    
    # تعيين الهدف
    cTarget = "http://example.com"  # يمكنك تغيير هذا إلى الهدف المطلوب
    
    ? "الهدف: " + cTarget
    ? "تاريخ التدقيق: " + date() + " " + time()
    ? ""
    
    # إنشاء تقرير التدقيق
    aAuditReport = [
        :target = cTarget,
        :start_time = time(),
        :http_response = [],
        :crawl_results = [],
        :fuzz_results = [],
        :security_headers = [],
        :vulnerabilities = [],
        :recommendations = []
    ]
    
    # 1. فحص HTTP أساسي
    ? "=== 1. فحص HTTP الأساسي ==="
    aAuditReport[:http_response] = performBasicHTTPCheck(oPraetorian, cTarget)
    
    # 2. زحف الموقع
    ? ""
    ? "=== 2. زحف الموقع ==="
    aAuditReport[:crawl_results] = performWebCrawling(oPraetorian, cTarget)
    
    # 3. Fuzzing للمجلدات والملفات
    ? ""
    ? "=== 3. Fuzzing للمجلدات والملفات ==="
    aAuditReport[:fuzz_results] = performWebFuzzing(oPraetorian, cTarget)
    
    # 4. فحص رؤوس الأمان
    ? ""
    ? "=== 4. فحص رؤوس الأمان ==="
    aAuditReport[:security_headers] = checkSecurityHeaders(oPraetorian, cTarget)
    
    # 5. تحليل الثغرات الأمنية
    ? ""
    ? "=== 5. تحليل الثغرات الأمنية ==="
    aAuditReport[:vulnerabilities] = analyzeVulnerabilities(aAuditReport)
    
    # 6. إنشاء التوصيات
    aAuditReport[:recommendations] = generateSecurityRecommendations(aAuditReport)
    
    # 7. طباعة التقرير النهائي
    ? ""
    ? "=== التقرير النهائي ==="
    printWebAuditReport(aAuditReport)
    
    # حفظ التقرير في ملف
    saveAuditReport(aAuditReport)

/*
==============================================================================
    فحص HTTP أساسي
==============================================================================
*/

func performBasicHTTPCheck oPraetorian, cTarget
    ? "إرسال طلب HTTP إلى " + cTarget + "..."
    
    oResponse = oPraetorian.Web.HTTPClient.get(cTarget, NULL)
    
    aHTTPInfo = [
        :success = oResponse[:success],
        :status_code = oResponse[:status_code],
        :content_type = oResponse[:content_type],
        :content_length = oResponse[:content_length],
        :server_header = "",
        :response_time = 0
    ]
    
    if oResponse[:success]
        ? "✓ نجح الاتصال - كود الاستجابة: " + oResponse[:status_code]
        ? "  نوع المحتوى: " + oResponse[:content_type]
        ? "  حجم المحتوى: " + oResponse[:content_length] + " بايت"
    else
        ? "✗ فشل الاتصال"
    ok
    
    return aHTTPInfo

/*
==============================================================================
    زحف الموقع
==============================================================================
*/

func performWebCrawling oPraetorian, cTarget
    ? "بدء زحف الموقع..."
    
    # تعيين إعدادات الزحف
    oPraetorian.Web.Crawler.setMaxDepth(2)
    oPraetorian.Web.Crawler.setMaxPages(50)
    oPraetorian.Web.Crawler.setDelay(500)
    
    # بدء الزحف
    aCrawlReport = oPraetorian.Web.Crawler.crawl(cTarget)
    
    ? "✓ تم زحف " + aCrawlReport[:total_pages] + " صفحة"
    ? "  تم العثور على " + aCrawlReport[:total_links] + " رابط"
    ? "  تم العثور على " + aCrawlReport[:total_forms] + " نموذج"
    
    # طباعة بعض الروابط المكتشفة
    if len(aCrawlReport[:found_urls]) > 0
        ? ""
        ? "بعض الروابط المكتشفة:"
        nCount = 0
        for cURL in aCrawlReport[:found_urls]
            if nCount >= 5 break ok  # عرض أول 5 روابط فقط
            ? "  - " + cURL
            nCount++
        next
        
        if len(aCrawlReport[:found_urls]) > 5
            ? "  ... و " + (len(aCrawlReport[:found_urls]) - 5) + " رابط آخر"
        ok
    ok
    
    return aCrawlReport

/*
==============================================================================
    Fuzzing للويب
==============================================================================
*/

func performWebFuzzing oPraetorian, cTarget
    ? "بدء Fuzzing للمجلدات والملفات..."
    
    # تعيين إعدادات Fuzzing
    oPraetorian.Web.Fuzzer.setDelay(200)
    oPraetorian.Web.Fuzzer.setTimeout(5)
    
    # تشغيل Fuzzing سريع
    aFuzzReport = oPraetorian.Web.Fuzzer.quickFuzz(cTarget)
    
    ? "✓ تم العثور على " + aFuzzReport[:total_directories] + " مجلد"
    ? "  تم العثور على " + aFuzzReport[:total_files] + " ملف"
    ? "  نتائج مثيرة للاهتمام: " + len(aFuzzReport[:interesting_findings])
    
    # طباعة النتائج المثيرة للاهتمام
    if len(aFuzzReport[:interesting_findings]) > 0
        ? ""
        ? "النتائج المثيرة للاهتمام:"
        for aFinding in aFuzzReport[:interesting_findings]
            cStatus = ""
            switch aFinding[:status_code]
                on 403
                    cStatus = "ممنوع الوصول"
                on 401
                    cStatus = "يتطلب مصادقة"
                on 500
                    cStatus = "خطأ خادم"
                other
                    cStatus = "غير معروف"
            off
            ? "  ⚠ " + aFinding[:url] + " [" + aFinding[:status_code] + "] - " + cStatus
        next
    ok
    
    return aFuzzReport

/*
==============================================================================
    فحص رؤوس الأمان
==============================================================================
*/

func checkSecurityHeaders oPraetorian, cTarget
    ? "فحص رؤوس الأمان..."
    
    # إرسال طلب HEAD للحصول على الرؤوس فقط
    oResponse = oPraetorian.Web.HTTPClient.head(cTarget, NULL)
    
    aSecurityHeaders = [
        :x_frame_options = false,
        :x_content_type_options = false,
        :x_xss_protection = false,
        :strict_transport_security = false,
        :content_security_policy = false,
        :referrer_policy = false,
        :permissions_policy = false
    ]
    
    # في التطبيق الحقيقي، ستحتاج لتحليل رؤوس الاستجابة
    # هذا مثال مبسط
    
    ? "✓ تم فحص رؤوس الأمان"
    ? "  X-Frame-Options: " + (aSecurityHeaders[:x_frame_options] ? "موجود" : "مفقود")
    ? "  X-Content-Type-Options: " + (aSecurityHeaders[:x_content_type_options] ? "موجود" : "مفقود")
    ? "  X-XSS-Protection: " + (aSecurityHeaders[:x_xss_protection] ? "موجود" : "مفقود")
    ? "  Strict-Transport-Security: " + (aSecurityHeaders[:strict_transport_security] ? "موجود" : "مفقود")
    ? "  Content-Security-Policy: " + (aSecurityHeaders[:content_security_policy] ? "موجود" : "مفقود")
    
    return aSecurityHeaders

/*
==============================================================================
    تحليل الثغرات الأمنية
==============================================================================
*/

func analyzeVulnerabilities aAuditReport
    aVulnerabilities = []
    
    # فحص رؤوس الأمان المفقودة
    aHeaders = aAuditReport[:security_headers]
    if not aHeaders[:x_frame_options]
        add(aVulnerabilities, [
            :type = "Missing Security Header",
            :name = "X-Frame-Options مفقود",
            :severity = "متوسط",
            :description = "قد يسمح بهجمات Clickjacking"
        ])
    ok
    
    if not aHeaders[:content_security_policy]
        add(aVulnerabilities, [
            :type = "Missing Security Header",
            :name = "Content-Security-Policy مفقود",
            :severity = "عالي",
            :description = "قد يسمح بهجمات XSS"
        ])
    ok
    
    # فحص النتائج المثيرة للاهتمام من Fuzzing
    aFuzzResults = aAuditReport[:fuzz_results]
    for aFinding in aFuzzResults[:interesting_findings]
        if aFinding[:status_code] = 403
            add(aVulnerabilities, [
                :type = "Information Disclosure",
                :name = "مجلد محمي مكشوف: " + aFinding[:url],
                :severity = "منخفض",
                :description = "قد يكشف عن بنية الموقع"
            ])
        but aFinding[:status_code] = 500
            add(aVulnerabilities, [
                :type = "Server Error",
                :name = "خطأ خادم في: " + aFinding[:url],
                :severity = "متوسط",
                :description = "قد يكشف عن معلومات حساسة"
            ])
        ok
    next
    
    # فحص النماذج المكتشفة
    aCrawlResults = aAuditReport[:crawl_results]
    if aCrawlResults[:total_forms] > 0
        add(aVulnerabilities, [
            :type = "Input Validation",
            :name = "نماذج تتطلب فحص",
            :severity = "متوسط",
            :description = "تم العثور على " + aCrawlResults[:total_forms] + " نموذج يتطلب فحص أمني"
        ])
    ok
    
    ? "✓ تم تحليل الثغرات الأمنية"
    ? "  تم العثور على " + len(aVulnerabilities) + " مشكلة أمنية محتملة"
    
    return aVulnerabilities

/*
==============================================================================
    إنشاء التوصيات الأمنية
==============================================================================
*/

func generateSecurityRecommendations aAuditReport
    aRecommendations = []
    
    # توصيات رؤوس الأمان
    aHeaders = aAuditReport[:security_headers]
    if not aHeaders[:x_frame_options]
        add(aRecommendations, "إضافة رأس X-Frame-Options: DENY أو SAMEORIGIN")
    ok
    
    if not aHeaders[:content_security_policy]
        add(aRecommendations, "تطبيق Content Security Policy قوي")
    ok
    
    if not aHeaders[:strict_transport_security]
        add(aRecommendations, "تفعيل HTTPS وإضافة رأس HSTS")
    ok
    
    # توصيات عامة
    add(aRecommendations, "تحديث جميع المكونات والمكتبات بانتظام")
    add(aRecommendations, "تطبيق مبدأ الصلاحيات الأدنى")
    add(aRecommendations, "تفعيل تسجيل الأحداث الأمنية")
    add(aRecommendations, "إجراء اختبارات اختراق دورية")
    
    return aRecommendations

/*
==============================================================================
    طباعة تقرير التدقيق
==============================================================================
*/

func printWebAuditReport aAuditReport
    ? ""
    ? "================================================"
    ? "تقرير تدقيق تطبيق الويب"
    ? "================================================"
    ? "الهدف: " + aAuditReport[:target]
    ? "وقت البداية: " + aAuditReport[:start_time]
    ? "وقت الانتهاء: " + time()
    ? "================================================"
    
    # ملخص النتائج
    ? ""
    ? "ملخص النتائج:"
    ? "-------------"
    ? "الصفحات المزحوفة: " + aAuditReport[:crawl_results][:total_pages]
    ? "الروابط المكتشفة: " + aAuditReport[:crawl_results][:total_links]
    ? "النماذج المكتشفة: " + aAuditReport[:crawl_results][:total_forms]
    ? "المجلدات الموجودة: " + aAuditReport[:fuzz_results][:total_directories]
    ? "الملفات الموجودة: " + aAuditReport[:fuzz_results][:total_files]
    ? "الثغرات المحتملة: " + len(aAuditReport[:vulnerabilities])
    
    # الثغرات الأمنية
    if len(aAuditReport[:vulnerabilities]) > 0
        ? ""
        ? "الثغرات الأمنية المحتملة:"
        ? "-------------------------"
        for aVuln in aAuditReport[:vulnerabilities]
            ? "⚠ [" + aVuln[:severity] + "] " + aVuln[:name]
            ? "   النوع: " + aVuln[:type]
            ? "   الوصف: " + aVuln[:description]
            ? ""
        next
    ok
    
    # التوصيات
    if len(aAuditReport[:recommendations]) > 0
        ? "التوصيات الأمنية:"
        ? "----------------"
        for cRecommendation in aAuditReport[:recommendations]
            ? "💡 " + cRecommendation
        next
    ok
    
    ? ""
    ? "================================================"

/*
==============================================================================
    حفظ تقرير التدقيق
==============================================================================
*/

func saveAuditReport aAuditReport
    cFileName = "web_audit_report_" + substr(date(), "/", "_") + "_" + 
                substr(time(), ":", "_") + ".txt"
    
    cReport = "تقرير تدقيق تطبيق الويب" + nl +
              "======================" + nl +
              "الهدف: " + aAuditReport[:target] + nl +
              "التاريخ: " + date() + " " + time() + nl + nl
    
    # إضافة تفاصيل التقرير
    cReport += "ملخص النتائج:" + nl
    cReport += "الصفحات المزحوفة: " + aAuditReport[:crawl_results][:total_pages] + nl
    cReport += "الثغرات المحتملة: " + len(aAuditReport[:vulnerabilities]) + nl + nl
    
    # إضافة الثغرات
    if len(aAuditReport[:vulnerabilities]) > 0
        cReport += "الثغرات الأمنية:" + nl
        for aVuln in aAuditReport[:vulnerabilities]
            cReport += "- [" + aVuln[:severity] + "] " + aVuln[:name] + nl
        next
        cReport += nl
    ok
    
    # إضافة التوصيات
    if len(aAuditReport[:recommendations]) > 0
        cReport += "التوصيات:" + nl
        for cRecommendation in aAuditReport[:recommendations]
            cReport += "- " + cRecommendation + nl
        next
    ok
    
    try
        write(cFileName, cReport)
        ? "تم حفظ التقرير في: " + cFileName
    catch
        ? "خطأ في حفظ التقرير: " + cCatchError
    done

'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'react-toastify';
import { exportToPdf as exportToPdfUtil } from '@/utils/export-utils';
import { FaArrowRight, FaUserGraduate, FaCalendarCheck, FaBook, FaGraduationCap, FaFilePdf, FaCheckCircle, FaTimesCircle, FaExclamationCircle, FaBookOpen, FaClipboardCheck, FaImage, FaChartBar, FaCalendarAlt, FaSync, FaPlus } from 'react-icons/fa';
import { use } from 'react';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

type Student = {
  id: number;
  username: string;
  name: string;
  age: number;
  phone?: string;
  guardianId?: number;
  classeId?: number;
  guardian?: { name: string; phone: string };
  classe?: { name: string };
};

type AttendanceRecord = {
  id: number;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'EXCUSED';
  hisass: number;
  images: {
    id: number;
    url: string;
  }[];
};

type MonthlyAttendanceStat = {
  month: string;
  total: number;
  present: number;
  absent: number;
  excused: number;
  rate: number;
};

type ScheduleItem = {
  id: number;
  day: string;
  startTime: string;
  endTime: string;
  teacher: string;
  subject: string;
};

type QuranRecord = {
  id: number;
  surahName: string;
  startVerse: number;
  endVerse: number;
  memorization: number;
  tajweed: number;
  date: string;
};

type ExamRecord = {
  id: number;
  name: string;
  score: number;
  maxPoints: number;
  passingPoints: number;
  isPassed: boolean;
  date: string;
};

type ProgressData = {
  attendance?: {
    present: number;
    absent: number;
    excused: number;
    total: number;
    rate: number;
    monthlyStats?: MonthlyAttendanceStat[];
    schedule?: ScheduleItem[];
    records?: AttendanceRecord[];
  };
  quran?: {
    memorized: number;
    total: number;
    rate: number;
    lastSurah: string;
    records?: QuranRecord[];
  };
  exams?: {
    passed: number;
    failed: number;
    total: number;
    average: number;
    lastExam?: {
      id: number;
      name: string;
      score: number;
      maxPoints: number;
      date: string;
    };
    records?: ExamRecord[];
  };
};

export default function StudentProgressPage({ params }: { params: Promise<{ id: string }> | { id: string } }) {
  const router = useRouter();
  const [student, setStudent] = useState<Student | null>(null);
  const [progressData, setProgressData] = useState<ProgressData>({});
  const [loading, setLoading] = useState(true);

  // Unwrap the params Promise using React.use()
  const resolvedParams = params instanceof Promise ? use(params) : params;
  const studentId = resolvedParams.id;

  useEffect(() => {
    fetchStudentData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [studentId]);

  const fetchStudentData = async () => {
    try {
      setLoading(true);
      // جلب بيانات الطالب وتقدمه من API
      const response = await fetch(`/api/students/progress/${studentId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch student data');
      }

      const data = await response.json();
      console.log('Student progress data:', data); // للتحقق من البيانات المستلمة
      setStudent(data.student);
      setProgressData(data.progressData);

      // إضافة رسالة توضيحية إذا كانت البيانات فارغة
      if (!data.progressData.attendance?.records?.length &&
          !data.progressData.quran?.records?.length &&
          !data.progressData.exams?.records?.length) {
        toast.info('لا توجد بيانات تقدم كافية لهذا الطالب. يرجى إضافة سجلات حضور، حفظ قرآن، أو نتائج امتحانات.');
      }
    } catch (error) {
      console.error('Error fetching student data:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحميل بيانات الطالب');
    } finally {
      setLoading(false);
    }
  };

  // تحديث بيانات الطالب
  const refreshData = async () => {
    toast.info('جاري تحديث البيانات...');
    await fetchStudentData();
    toast.success('تم تحديث البيانات بنجاح');
  };

  // تصدير التقرير إلى PDF
  const exportToPdf = () => {
    toast.info('جاري تصدير التقرير...');

    try {
      // إعداد بيانات الطالب
      const studentData = [
        ['الاسم الكامل', student?.name || ''],
        ['اسم المستخدم', student?.username || ''],
        ['العمر', student?.age ? `${student.age} سنة` : ''],
        ['رقم الهاتف', student?.phone || 'غير متوفر'],
        ['القسم', student?.classe?.name || 'غير محدد'],
        ['الولي', student?.guardian?.name || 'غير محدد']
      ];

      // إعداد بيانات الحضور
      const attendanceData = [
        ['عدد أيام الحضور', progressData.attendance?.present?.toString() || '0'],
        ['عدد أيام الغياب', progressData.attendance?.absent?.toString() || '0'],
        ['عدد أيام الاستئذان', progressData.attendance?.excused?.toString() || '0'],
        ['إجمالي الأيام', progressData.attendance?.total?.toString() || '0'],
        ['نسبة الحضور', `${progressData.attendance?.rate || 0}%`]
      ];

      // إعداد بيانات حفظ القرآن
      const quranData = [
        ['عدد الآيات المحفوظة', progressData.quran?.memorized?.toString() || '0'],
        ['إجمالي الآيات', progressData.quran?.total?.toString() || '0'],
        ['نسبة الإنجاز', `${progressData.quran?.rate || 0}%`],
        ['آخر سورة تم حفظها', progressData.quran?.lastSurah || 'لم يتم تسجيل حفظ بعد']
      ];

      // إعداد بيانات الامتحانات
      const examsData = [
        ['عدد الامتحانات الناجحة', progressData.exams?.passed?.toString() || '0'],
        ['عدد الامتحانات الراسبة', progressData.exams?.failed?.toString() || '0'],
        ['إجمالي الامتحانات', progressData.exams?.total?.toString() || '0'],
        ['متوسط الدرجات', progressData.exams?.average?.toString() || '0']
      ];

      // إعداد بيانات الرسوم البيانية

      // 1. رسم بياني دائري لتوزيع الحضور
      const attendanceChart = {
        title: 'توزيع سجلات الحضور',
        type: 'pie' as const,
        data: {
          labels: ['حضور', 'غياب', 'استئذان'],
          datasets: [
            {
              label: 'عدد السجلات',
              data: [
                progressData.attendance?.present || 0,
                progressData.attendance?.absent || 0,
                progressData.attendance?.excused || 0
              ],
              backgroundColor: [
                'var(--primary-color)', // أخضر للحضور
                '#f44336', // أحمر للغياب
                '#ff9800'  // برتقالي للاستئذان
              ]
            }
          ]
        },
        options: {
          width: 400,
          height: 300
        }
      };

      // 2. رسم بياني لتقدم حفظ القرآن
      const quranProgressChart = {
        title: 'نسبة تقدم حفظ القرآن الكريم',
        type: 'pie' as const,
        data: {
          labels: ['الآيات المحفوظة', 'الآيات المتبقية'],
          datasets: [
            {
              label: 'عدد الآيات',
              data: [
                progressData.quran?.memorized || 0,
                (progressData.quran?.total || 0) - (progressData.quran?.memorized || 0)
              ],
              backgroundColor: [
                'var(--primary-color)', // أخضر للآيات المحفوظة
                '#e0f2ef'  // أخضر فاتح للآيات المتبقية
              ]
            }
          ]
        },
        options: {
          width: 400,
          height: 300
        }
      };

      // 3. رسم بياني لنتائج الامتحانات
      const examsChart = {
        title: 'نتائج الامتحانات',
        type: 'bar' as const,
        data: {
          labels: progressData.exams?.records?.slice(0, 5).map(record => record.name) || [],
          datasets: [
            {
              label: 'الدرجة المحصلة',
              data: progressData.exams?.records?.slice(0, 5).map(record => record.score) || [],
              backgroundColor: 'var(--primary-color)'
            },
            {
              label: 'درجة النجاح',
              data: progressData.exams?.records?.slice(0, 5).map(record => record.passingPoints) || [],
              backgroundColor: '#ff9800'
            }
          ]
        },
        options: {
          width: 500,
          height: 300
        }
      };

      // 4. رسم بياني للحضور الشهري (إذا كانت البيانات متوفرة)
      const monthlyAttendanceChart = progressData.attendance?.monthlyStats && progressData.attendance.monthlyStats.length > 0 ? {
        title: 'معدلات الحضور الشهرية',
        type: 'line' as const,
        data: {
          labels: progressData.attendance.monthlyStats.map(stat => {
            const [year, month] = stat.month.split('-');
            return `${month}/${year}`;
          }),
          datasets: [
            {
              label: 'نسبة الحضور',
              data: progressData.attendance.monthlyStats.map(stat => stat.rate),
              backgroundColor: 'rgba(22, 155, 136, 0.2)',
              borderColor: 'var(--primary-color)',
              borderWidth: 2,
              fill: true
            }
          ]
        },
        options: {
          width: 500,
          height: 300
        }
      } : null;

      // 5. رسم بياني لدرجات حفظ القرآن والتجويد (إذا كانت البيانات متوفرة)
      const quranGradesChart = progressData.quran?.records && progressData.quran.records.length > 0 ? {
        title: 'درجات حفظ القرآن والتجويد',
        type: 'bar' as const,
        data: {
          labels: progressData.quran.records.slice(0, 5).map(record => record.surahName),
          datasets: [
            {
              label: 'درجة الحفظ',
              data: progressData.quran.records.slice(0, 5).map(record => record.memorization),
              backgroundColor: 'var(--primary-color)'
            },
            {
              label: 'درجة التجويد',
              data: progressData.quran.records.slice(0, 5).map(record => record.tajweed),
              backgroundColor: '#4caf50'
            }
          ]
        },
        options: {
          width: 500,
          height: 300
        }
      } : null;

      // تجميع الرسوم البيانية المتوفرة
      const charts: {
        title?: string;
        type: 'bar' | 'line' | 'pie' | 'doughnut';
        data: {
          labels: string[];
          datasets: {
            label: string;
            data: number[];
            backgroundColor?: string | string[];
            borderColor?: string | string[];
            borderWidth?: number;
            fill?: boolean;
          }[];
        };
        options?: {
          width?: number;
          height?: number;
        };
      }[] = [
        attendanceChart,
        quranProgressChart
      ];

      // إضافة الرسوم البيانية الإضافية إذا كانت البيانات متوفرة
      if (progressData.exams?.records && progressData.exams.records.length > 0) {
        charts.push(examsChart);
      }

      if (monthlyAttendanceChart) {
        charts.push(monthlyAttendanceChart);
      }

      if (quranGradesChart) {
        charts.push(quranGradesChart);
      }

      // استخدام دالة التصدير المشتركة
      const fileName = `تقرير_تقدم_الطالب_${student?.name || 'غير_معروف'}_${new Date().toISOString().slice(0, 10)}.pdf`;

      exportToPdfUtil({
        title: `تقرير تقدم الطالب: ${student?.name || 'غير معروف'}`,
        fileName: fileName,
        tables: [
          {
            title: 'معلومات الطالب',
            headers: ['البيان', 'القيمة'],
            data: studentData,
            startY: 40
          },
          {
            title: 'بيانات الحضور',
            headers: ['البيان', 'القيمة'],
            data: attendanceData
          },
          {
            title: 'بيانات حفظ القرآن',
            headers: ['البيان', 'القيمة'],
            data: quranData
          },
          {
            title: 'بيانات الامتحانات',
            headers: ['البيان', 'القيمة'],
            data: examsData
          }
        ],
        // إضافة الرسوم البيانية
        charts: charts,
        additionalContent: [
          {
            text: '* البيانات المعروضة تعكس البيانات الفعلية من قاعدة البيانات',
            x: 105,
            y: 0,
            options: { align: 'center' }
          }
        ]
      });
    } catch (error) {
      console.error('Error exporting student progress to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير التقرير');
    }
  };

  // إضافة سجل حضور جديد
  const addAttendanceRecord = async () => {
    router.push(`/admin/attendance?studentId=${studentId}`);
  };

  // إضافة سجل حفظ قرآن جديد
  const addQuranRecord = async () => {
    router.push(`/admin/quran-progress?studentId=${studentId}`);
  };

  // إضافة نتيجة امتحان جديدة
  const addExamResult = async () => {
    router.push(`/admin/evaluation/results?studentId=${studentId}`);
  };

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-screen" dir="rtl">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto"></div>
          <p className="mt-4 text-[var(--primary-color)]">جاري تحميل البيانات...</p>
        </div>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="p-6 flex justify-center items-center min-h-screen" dir="rtl">
        <div className="text-center">
          <p className="text-xl text-red-500">لم يتم العثور على الطالب</p>
          <Button
            onClick={() => router.push('/admin/students')}
            className="mt-4 bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
          >
            العودة إلى قائمة الطلاب
          </Button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute requiredPermission="admin.students.progress">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
        <div>
          <Button
            onClick={() => router.push('/admin/students')}
            variant="outline"
            className="mb-4 border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <FaArrowRight className="ml-1" />
            العودة إلى قائمة الطلاب
          </Button>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaUserGraduate className="text-[var(--primary-color)]" />
            تقرير تقدم الطالب
          </h1>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={refreshData}
            variant="outline"
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <FaSync className="ml-1" />
            تحديث البيانات
          </Button>
          <Button
            onClick={exportToPdf}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
          >
            <FaFilePdf className="ml-1" />
            تصدير التقرير
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="bg-white shadow-md border border-[#e0f2ef] col-span-3">
          <CardHeader className="pb-2 border-b border-[#e0f2ef]">
            <CardTitle className="text-xl text-[var(--primary-color)]">معلومات الطالب</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-gray-500">الاسم الكامل</p>
                <p className="font-semibold text-lg">{student.name}</p>
              </div>
              <div>
                <p className="text-gray-500">اسم المستخدم</p>
                <p className="font-semibold">{student.username}</p>
              </div>
              <div>
                <p className="text-gray-500">العمر</p>
                <p className="font-semibold">{student.age} سنة</p>
              </div>
              <div>
                <p className="text-gray-500">رقم الهاتف</p>
                <p className="font-semibold">{student.phone || 'غير متوفر'}</p>
              </div>
              <div>
                <p className="text-gray-500">القسم</p>
                <p className="font-semibold">{student.classe?.name || 'غير محدد'}</p>
              </div>
              <div>
                <p className="text-gray-500">الولي</p>
                <p className="font-semibold">{student.guardian?.name || 'غير محدد'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* الإجراءات السريعة */}
        <Card className="bg-white shadow-md border border-[#e0f2ef]">
          <CardHeader className="pb-2 border-b border-[#e0f2ef]">
            <CardTitle className="text-lg text-[var(--primary-color)]">الإجراءات السريعة</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="flex flex-col gap-2">
              <Button
                onClick={addAttendanceRecord}
                className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center justify-center gap-1"
                size="sm"
              >
                <FaCalendarCheck className="ml-1" />
                تسجيل حضور
              </Button>
              <Button
                onClick={addQuranRecord}
                className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center justify-center gap-1"
                size="sm"
              >
                <FaBook className="ml-1" />
                تسجيل حفظ قرآن
              </Button>
              <Button
                onClick={addExamResult}
                className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center justify-center gap-1"
                size="sm"
              >
                <FaGraduationCap className="ml-1" />
                تسجيل نتيجة امتحان
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* قسم التنبيهات والتوصيات */}
      {(
        (progressData.attendance?.rate !== undefined && progressData.attendance.rate < 70) ||
        (progressData.quran?.records && progressData.quran.records.length > 0 &&
          (progressData.quran.records[0].memorization < 5 || progressData.quran.records[0].tajweed < 5)) ||
        (progressData.exams?.lastExam &&
          progressData.exams.lastExam.score < progressData.exams.lastExam.maxPoints * 0.6)
      ) && (
        <Card className="bg-white shadow-md border border-yellow-200 mb-6">
          <CardHeader className="pb-2 border-b border-yellow-200 bg-yellow-50">
            <CardTitle className="text-lg text-yellow-700 flex items-center gap-2">
              <FaExclamationCircle className="text-yellow-500" />
              تنبيهات وتوصيات
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="space-y-3">
              {progressData.attendance?.rate !== undefined && progressData.attendance.rate < 70 && (
                <div className="flex items-start gap-2 pb-2 border-b border-gray-100">
                  <FaCalendarCheck className="text-red-500 mt-1" />
                  <div>
                    <p className="font-medium text-red-700">انخفاض نسبة الحضور</p>
                    <p className="text-sm text-gray-600">نسبة حضور الطالب ({progressData.attendance.rate}%) أقل من المعدل المطلوب (70%). يرجى متابعة الطالب وتشجيعه على الالتزام بالحضور.</p>
                  </div>
                </div>
              )}

              {progressData.quran?.records && progressData.quran.records.length > 0 && progressData.quran.records[0].memorization < 5 && (
                <div className="flex items-start gap-2 pb-2 border-b border-gray-100">
                  <FaBook className="text-orange-500 mt-1" />
                  <div>
                    <p className="font-medium text-orange-700">ضعف في مستوى الحفظ</p>
                    <p className="text-sm text-gray-600">درجة الحفظ في آخر تقييم ({progressData.quran.records[0].memorization}/10) منخفضة. يرجى تقديم دعم إضافي للطالب في حفظ القرآن.</p>
                  </div>
                </div>
              )}

              {progressData.quran?.records && progressData.quran.records.length > 0 && progressData.quran.records[0].tajweed < 5 && (
                <div className="flex items-start gap-2 pb-2 border-b border-gray-100">
                  <FaBook className="text-orange-500 mt-1" />
                  <div>
                    <p className="font-medium text-orange-700">ضعف في مستوى التجويد</p>
                    <p className="text-sm text-gray-600">درجة التجويد في آخر تقييم ({progressData.quran.records[0].tajweed}/10) منخفضة. يرجى التركيز على تحسين مهارات التجويد لدى الطالب.</p>
                  </div>
                </div>
              )}

              {progressData.exams?.lastExam && progressData.exams.lastExam.score < progressData.exams.lastExam.maxPoints * 0.6 && (
                <div className="flex items-start gap-2">
                  <FaGraduationCap className="text-red-500 mt-1" />
                  <div>
                    <p className="font-medium text-red-700">ضعف في نتائج الامتحانات</p>
                    <p className="text-sm text-gray-600">حصل الطالب على درجة منخفضة ({progressData.exams.lastExam.score}/{progressData.exams.lastExam.maxPoints}) في آخر امتحان. يرجى تقديم دروس تقوية للطالب.</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="summary" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="summary">ملخص التقدم</TabsTrigger>
          <TabsTrigger value="attendance">الحضور</TabsTrigger>
          <TabsTrigger value="quran">حفظ القرآن</TabsTrigger>
          <TabsTrigger value="exams">نتائج الامتحانات</TabsTrigger>
        </TabsList>

        <TabsContent value="summary">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* بطاقة الحضور */}
            <Card className="overflow-hidden border border-[#e0f2ef]">
              <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                <CardTitle className="text-lg flex items-center gap-2">
                  <FaCalendarCheck className="text-[var(--primary-color)]" />
                  الحضور
                </CardTitle>
                <CardDescription>معدل الحضور الإجمالي</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="py-4">
                  {/* مؤشر دائري للحضور */}
                  <div className="relative w-32 h-32 mx-auto mb-3">
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      {/* الدائرة الخلفية */}
                      <circle
                        className="text-gray-200"
                        strokeWidth="10"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                      />
                      {/* الدائرة الأمامية (نسبة الحضور) */}
                      <circle
                        className={`${
                          (progressData.attendance?.rate || 0) >= 80 ? 'text-primary-color' :
                          (progressData.attendance?.rate || 0) >= 60 ? 'text-yellow-500' :
                          'text-red-500'
                        }`}
                        strokeWidth="10"
                        strokeDasharray={`${(progressData.attendance?.rate || 0) * 2.51} 251`}
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                        transform="rotate(-90 50 50)"
                      />
                      {/* النسبة المئوية في المنتصف */}
                      <text
                        x="50"
                        y="50"
                        className="text-[var(--primary-color)] font-bold text-lg"
                        dominantBaseline="middle"
                        textAnchor="middle"
                      >
                        {progressData.attendance?.rate || 0}%
                      </text>
                    </svg>
                  </div>

                  <div className="text-center">
                    <div className="flex justify-center items-center gap-4 mb-2">
                      <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-primary-color mr-1"></span>
                        <span className="text-sm">حاضر: {progressData.attendance?.present || 0}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                        <span className="text-sm">غائب: {progressData.attendance?.absent || 0}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"></span>
                        <span className="text-sm">معذور: {progressData.attendance?.excused || 0}</span>
                      </div>
                    </div>

                    <div className="mt-2 text-sm text-gray-500">
                      إجمالي أيام الحضور: {progressData.attendance?.total || 0} يوم
                    </div>

                    {progressData.attendance?.monthlyStats && progressData.attendance.monthlyStats.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <div className="text-sm font-medium text-gray-700 mb-1">آخر شهر:</div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                          <div
                            className={`h-2.5 rounded-full ${
                              (progressData.attendance.monthlyStats[progressData.attendance.monthlyStats.length - 1].rate) >= 80 ? 'bg-primary-color' :
                              (progressData.attendance.monthlyStats[progressData.attendance.monthlyStats.length - 1].rate) >= 60 ? 'bg-yellow-400' : 'bg-red-600'
                            }`}
                            style={{ width: `${progressData.attendance.monthlyStats[progressData.attendance.monthlyStats.length - 1].rate}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500">
                          {progressData.attendance.monthlyStats[progressData.attendance.monthlyStats.length - 1].rate}% ({progressData.attendance.monthlyStats[progressData.attendance.monthlyStats.length - 1].present} من {progressData.attendance.monthlyStats[progressData.attendance.monthlyStats.length - 1].total})
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* بطاقة حفظ القرآن */}
            <Card className="overflow-hidden border border-[#e0f2ef]">
              <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                <CardTitle className="text-lg flex items-center gap-2">
                  <FaBook className="text-[var(--primary-color)]" />
                  حفظ القرآن
                </CardTitle>
                <CardDescription>نسبة الإنجاز في الحفظ</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="py-4">
                  {/* مؤشر تقدم حفظ القرآن */}
                  <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                    <div
                      className="h-4 rounded-full bg-[var(--primary-color)] text-xs text-white flex items-center justify-center"
                      style={{ width: `${progressData.quran?.rate || 0}%` }}
                    >
                      {(progressData.quran?.rate || 0) > 5 ? `${progressData.quran?.rate || 0}%` : ''}
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-[var(--primary-color)] mb-2">
                      {progressData.quran?.memorized || 0} <span className="text-sm font-normal text-gray-500">من</span> {progressData.quran?.total || 0} <span className="text-sm font-normal text-gray-500">آية</span>
                    </div>

                    <div className="mt-3 flex flex-col items-center">
                      <div className="text-sm font-medium text-gray-700 mb-1">آخر سورة تم حفظها:</div>
                      <div className="bg-[#e0f2ef] text-[var(--primary-color)] px-4 py-2 rounded-full font-semibold">
                        {progressData.quran?.lastSurah || 'لم يتم تسجيل حفظ بعد'}
                      </div>
                    </div>

                    {progressData.quran?.records && progressData.quran.records.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <div className="text-sm font-medium text-gray-700 mb-1">آخر تقييم:</div>
                        <div className="flex justify-center gap-4">
                          <div className="text-center">
                            <div className="text-xs text-gray-500">الحفظ</div>
                            <div className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                              progressData.quran.records[0].memorization >= 8
                                ? 'bg-green-100 text-green-800'
                                : progressData.quran.records[0].memorization >= 5
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {progressData.quran.records[0].memorization}/10
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-xs text-gray-500">التجويد</div>
                            <div className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                              progressData.quran.records[0].tajweed >= 8
                                ? 'bg-green-100 text-green-800'
                                : progressData.quran.records[0].tajweed >= 5
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {progressData.quran.records[0].tajweed}/10
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* بطاقة الامتحانات */}
            <Card className="overflow-hidden border border-[#e0f2ef]">
              <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                <CardTitle className="text-lg flex items-center gap-2">
                  <FaGraduationCap className="text-[var(--primary-color)]" />
                  الامتحانات
                </CardTitle>
                <CardDescription>متوسط الدرجات</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="py-4">
                  {/* مؤشر دائري لمتوسط الدرجات */}
                  <div className="relative w-32 h-32 mx-auto mb-3">
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      {/* الدائرة الخلفية */}
                      <circle
                        className="text-gray-200"
                        strokeWidth="10"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                      />
                      {/* الدائرة الأمامية (متوسط الدرجات) */}
                      <circle
                        className={`${
                          (progressData.exams?.average || 0) >= 80 ? 'text-primary-color' :
                          (progressData.exams?.average || 0) >= 60 ? 'text-yellow-500' :
                          'text-red-500'
                        }`}
                        strokeWidth="10"
                        strokeDasharray={`${(progressData.exams?.average || 0) * 2.51} 251`}
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                        transform="rotate(-90 50 50)"
                      />
                      {/* متوسط الدرجات في المنتصف */}
                      <text
                        x="50"
                        y="50"
                        className="text-[var(--primary-color)] font-bold text-lg"
                        dominantBaseline="middle"
                        textAnchor="middle"
                      >
                        {progressData.exams?.average || 0}
                      </text>
                    </svg>
                  </div>

                  <div className="text-center">
                    <div className="flex justify-center items-center gap-4 mb-2">
                      <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-primary-color mr-1"></span>
                        <span className="text-sm">ناجح: {progressData.exams?.passed || 0}</span>
                      </div>
                      <div className="flex items-center">
                        <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                        <span className="text-sm">راسب: {progressData.exams?.failed || 0}</span>
                      </div>
                    </div>

                    <div className="mt-2 text-sm text-gray-500">
                      إجمالي الامتحانات: {progressData.exams?.total || 0}
                    </div>

                    {progressData.exams?.lastExam && (
                      <div className="mt-3 pt-3 border-t border-gray-100">
                        <div className="text-sm font-medium text-gray-700 mb-1">آخر امتحان:</div>
                        <div className="bg-[#e0f2ef] text-[var(--primary-color)] px-3 py-1 rounded-lg inline-block">
                          {progressData.exams.lastExam.name}
                        </div>
                        <div className="mt-1 flex justify-center items-center gap-2">
                          <div className={`px-2 py-1 rounded-full text-xs ${
                            (progressData.exams.lastExam.score / progressData.exams.lastExam.maxPoints * 100) >= 80
                              ? 'bg-green-100 text-green-800'
                              : (progressData.exams.lastExam.score / progressData.exams.lastExam.maxPoints * 100) >= 60
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {progressData.exams.lastExam.score} / {progressData.exams.lastExam.maxPoints}
                          </div>
                          <div className="text-xs text-gray-500">
                            {progressData.exams.lastExam.date}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="attendance">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaCalendarCheck className="text-[var(--primary-color)]" />
                سجل الحضور
              </CardTitle>
              <CardDescription>تفاصيل حضور الطالب</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-[var(--primary-color)]">سجلات الحضور</h3>
                <Button
                  onClick={addAttendanceRecord}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                  size="sm"
                >
                  <FaPlus className="ml-1" />
                  إضافة سجل حضور
                </Button>
              </div>

              {progressData.attendance?.records && progressData.attendance.records.length > 0 ? (
                <>
                  {/* إحصائيات الحضور الشهرية */}
                  {progressData.attendance.monthlyStats && progressData.attendance.monthlyStats.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-md font-semibold mb-3 text-gray-700 flex items-center">
                        <FaChartBar className="ml-2 text-[var(--primary-color)]" />
                        إحصائيات الحضور الشهرية
                      </h4>
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="bg-[#e0f2ef]">
                              <th className="p-2 text-right border border-[#e0f2ef]">الشهر</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">الحضور</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">الغياب</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">المعذور</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">المعدل</th>
                            </tr>
                          </thead>
                          <tbody>
                            {progressData.attendance.monthlyStats.map((stat) => {
                              // تحويل تنسيق الشهر من YYYY-MM إلى MM/YYYY
                              const [year, month] = stat.month.split('-');
                              const formattedMonth = `${month}/${year}`;

                              return (
                                <tr key={stat.month} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                                  <td className="p-2 text-right border border-[#e0f2ef]">{formattedMonth}</td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">
                                    <span className="text-primary-color font-semibold">{stat.present}</span>
                                  </td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">
                                    <span className="text-red-600 font-semibold">{stat.absent}</span>
                                  </td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">
                                    <span className="text-yellow-600 font-semibold">{stat.excused}</span>
                                  </td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">
                                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                                      <div
                                        className={`h-2.5 rounded-full ${
                                          stat.rate >= 80 ? 'bg-primary-color' :
                                          stat.rate >= 60 ? 'bg-yellow-400' : 'bg-red-600'
                                        }`}
                                        style={{ width: `${stat.rate}%` }}
                                      ></div>
                                    </div>
                                    <span className="text-sm mt-1 inline-block">{stat.rate}%</span>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* جدول الحصص */}
                  {progressData.attendance.schedule && progressData.attendance.schedule.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-md font-semibold mb-3 text-gray-700 flex items-center">
                        <FaCalendarAlt className="ml-2 text-[var(--primary-color)]" />
                        جدول الحصص
                      </h4>
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse">
                          <thead>
                            <tr className="bg-[#e0f2ef]">
                              <th className="p-2 text-right border border-[#e0f2ef]">اليوم</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">من</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">إلى</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">المادة</th>
                              <th className="p-2 text-right border border-[#e0f2ef]">المعلم</th>
                            </tr>
                          </thead>
                          <tbody>
                            {progressData.attendance.schedule.map((item) => (
                              <tr key={item.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                                <td className="p-2 text-right border border-[#e0f2ef]">{item.day}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">{item.startTime}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">{item.endTime}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">{item.subject}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">{item.teacher}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* سجلات الحضور */}
                  <div>
                    <h4 className="text-md font-semibold mb-3 text-gray-700 flex items-center">
                      <FaCalendarCheck className="ml-2 text-[var(--primary-color)]" />
                      سجلات الحضور
                    </h4>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-[#e0f2ef]">
                            <th className="p-2 text-right border border-[#e0f2ef]">التاريخ</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">الحصة</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">الحالة</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">الصور</th>
                          </tr>
                        </thead>
                        <tbody>
                          {progressData.attendance.records.map((record) => (
                            <tr key={record.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                              <td className="p-2 text-right border border-[#e0f2ef]">{record.date}</td>
                              <td className="p-2 text-right border border-[#e0f2ef]">{record.hisass}</td>
                              <td className="p-2 text-right border border-[#e0f2ef]">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                                  record.status === 'PRESENT'
                                    ? 'bg-green-100 text-green-800'
                                    : record.status === 'ABSENT'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {record.status === 'PRESENT' && <FaCheckCircle className="ml-1" />}
                                  {record.status === 'ABSENT' && <FaTimesCircle className="ml-1" />}
                                  {record.status === 'EXCUSED' && <FaExclamationCircle className="ml-1" />}
                                  {record.status === 'PRESENT' ? 'حاضر' : record.status === 'ABSENT' ? 'غائب' : 'معذور'}
                                </span>
                              </td>
                              <td className="p-2 text-right border border-[#e0f2ef]">
                                {record.images && record.images.length > 0 ? (
                                  <div className="flex gap-1">
                                    {record.images.map((img) => (
                                      <a
                                        key={img.id}
                                        href={img.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-600 hover:text-blue-800"
                                      >
                                        <FaImage className="text-lg" />
                                      </a>
                                    ))}
                                  </div>
                                ) : (
                                  <span className="text-gray-400 text-sm">لا توجد صور</span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 text-lg mb-4">لا توجد سجلات حضور لهذا الطالب</p>
                  <p className="text-sm text-gray-400">يمكنك إضافة سجلات حضور من خلال الزر أعلاه</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quran">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaBookOpen className="text-[var(--primary-color)]" />
                سجل حفظ القرآن
              </CardTitle>
              <CardDescription>تفاصيل تقدم الطالب في حفظ القرآن</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-[var(--primary-color)]">سجلات حفظ القرآن</h3>
                <Button
                  onClick={addQuranRecord}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                  size="sm"
                >
                  <FaPlus className="ml-1" />
                  إضافة سجل حفظ
                </Button>
              </div>

              {progressData.quran?.records && progressData.quran.records.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-[#e0f2ef]">
                        <th className="p-2 text-right border border-[#e0f2ef]">التاريخ</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">السورة</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">من الآية</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">إلى الآية</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">درجة الحفظ</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">درجة التجويد</th>
                      </tr>
                    </thead>
                    <tbody>
                      {progressData.quran.records.map((record) => (
                        <tr key={record.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.date}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.surahName}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.startVerse}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.endVerse}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                              record.memorization >= 8
                                ? 'bg-green-100 text-green-800'
                                : record.memorization >= 5
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {record.memorization}/10
                            </span>
                          </td>
                          <td className="p-2 text-right border border-[#e0f2ef]">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                              record.tajweed >= 8
                                ? 'bg-green-100 text-green-800'
                                : record.tajweed >= 5
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {record.tajweed}/10
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 text-lg mb-4">لا توجد سجلات حفظ قرآن لهذا الطالب</p>
                  <p className="text-sm text-gray-400">يمكنك إضافة سجلات حفظ القرآن من خلال الزر أعلاه</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exams">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaClipboardCheck className="text-[var(--primary-color)]" />
                سجل الامتحانات
              </CardTitle>
              <CardDescription>تفاصيل نتائج الطالب في الامتحانات</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-[var(--primary-color)]">سجلات الامتحانات</h3>
                <Button
                  onClick={addExamResult}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                  size="sm"
                >
                  <FaPlus className="ml-1" />
                  إضافة نتيجة امتحان
                </Button>
              </div>

              {progressData.exams?.records && progressData.exams.records.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-[#e0f2ef]">
                        <th className="p-2 text-right border border-[#e0f2ef]">التاريخ</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">الامتحان</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">الدرجة</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">الدرجة الكاملة</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">درجة النجاح</th>
                        <th className="p-2 text-right border border-[#e0f2ef]">النتيجة</th>
                      </tr>
                    </thead>
                    <tbody>
                      {progressData.exams.records.map((record) => (
                        <tr key={record.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.date}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.name}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.score}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.maxPoints}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">{record.passingPoints}</td>
                          <td className="p-2 text-right border border-[#e0f2ef]">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                              record.isPassed
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {record.isPassed ? (
                                <>
                                  <FaCheckCircle className="ml-1" />
                                  ناجح
                                </>
                              ) : (
                                <>
                                  <FaTimesCircle className="ml-1" />
                                  راسب
                                </>
                              )}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 text-lg mb-4">لا توجد سجلات امتحانات لهذا الطالب</p>
                  <p className="text-sm text-gray-400">يمكنك إضافة نتائج امتحانات من خلال الزر أعلاه</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </ProtectedRoute>
  );
}

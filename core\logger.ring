/*
==============================================================================
    نظام التسجيل - Praetorian Logger
    
    الوصف: نظام تسجيل احترافي لتتبع العمليات والأخطاء في مكتبة Praetorian
    المؤلف: Praetorian Team
==============================================================================
*/

/*
==============================================================================
    ثوابت مستويات التسجيل
==============================================================================
*/

LOG_LEVEL_DEBUG = 1
LOG_LEVEL_INFO = 2
LOG_LEVEL_WARNING = 3
LOG_LEVEL_ERROR = 4
LOG_LEVEL_CRITICAL = 5

# إنشاء مثيل عام من نظام التسجيل
PraetorianLoggerInstance = new PraetorianLogger

/*
==============================================================================
    كلاس نظام التسجيل
==============================================================================
*/

class PraetorianLogger

    # خصائص النظام
    cLogFile = "praetorian.log"
    nLogLevel = LOG_LEVEL_INFO
    bLogToFile = true
    bLogToConsole = true
    bIncludeTimestamp = true
    bIncludeLevel = true
    
    /*
    دالة البناء
    */
    func init
        # إنشاء ملف السجل إذا لم يكن موجوداً
        if bLogToFile and not fexists(cLogFile)
            write(cLogFile, "")
        ok
    
    /*
    تعيين ملف السجل
    المدخلات: cFileName - اسم ملف السجل
    */
    func setLogFile cFileName
        cLogFile = cFileName
        if bLogToFile and not fexists(cLogFile)
            write(cLogFile, "")
        ok
    
    /*
    تعيين مستوى التسجيل
    المدخلات: nLevel - مستوى التسجيل
    */
    func setLogLevel nLevel
        if nLevel >= LOG_LEVEL_DEBUG and nLevel <= LOG_LEVEL_CRITICAL
            nLogLevel = nLevel
        ok
    
    /*
    تفعيل/إلغاء التسجيل في ملف
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func enableFileLogging bEnable
        bLogToFile = bEnable
    
    /*
    تفعيل/إلغاء التسجيل في وحدة التحكم
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func enableConsoleLogging bEnable
        bLogToConsole = bEnable
    
    /*
    تفعيل/إلغاء إضافة الوقت والتاريخ
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func enableTimestamp bEnable
        bIncludeTimestamp = bEnable
    
    /*
    الحصول على الوقت والتاريخ الحالي
    المخرجات: نص يحتوي على الوقت والتاريخ
    */
    func getCurrentTimestamp
        return date() + " " + time()
    
    /*
    الحصول على اسم مستوى التسجيل
    المدخلات: nLevel - مستوى التسجيل
    المخرجات: اسم المستوى كنص
    */
    func getLevelName nLevel
        switch nLevel
            on LOG_LEVEL_DEBUG
                return "DEBUG"
            on LOG_LEVEL_INFO
                return "INFO"
            on LOG_LEVEL_WARNING
                return "WARNING"
            on LOG_LEVEL_ERROR
                return "ERROR"
            on LOG_LEVEL_CRITICAL
                return "CRITICAL"
            other
                return "UNKNOWN"
        off
    
    /*
    تنسيق رسالة السجل
    المدخلات: nLevel - مستوى التسجيل، cMessage - الرسالة
    المخرجات: الرسالة منسقة
    */
    func formatMessage nLevel, cMessage
        cFormattedMessage = ""
        
        # إضافة الوقت والتاريخ
        if bIncludeTimestamp
            cFormattedMessage += "[" + getCurrentTimestamp() + "] "
        ok
        
        # إضافة مستوى التسجيل
        if bIncludeLevel
            cFormattedMessage += "[" + getLevelName(nLevel) + "] "
        ok
        
        # إضافة الرسالة
        cFormattedMessage += cMessage
        
        return cFormattedMessage
    
    /*
    كتابة رسالة في السجل
    المدخلات: nLevel - مستوى التسجيل، cMessage - الرسالة
    */
    func log nLevel, cMessage
        # التحقق من مستوى التسجيل
        if nLevel < nLogLevel
            return
        ok
        
        # تنسيق الرسالة
        cFormattedMessage = formatMessage(nLevel, cMessage)
        
        # الكتابة في وحدة التحكم
        if bLogToConsole
            ? cFormattedMessage
        ok
        
        # الكتابة في الملف
        if bLogToFile
            try
                cCurrentContent = read(cLogFile)
                write(cLogFile, cCurrentContent + cFormattedMessage + nl)
            catch
                # في حالة فشل الكتابة، اطبع رسالة خطأ
                ? "خطأ في كتابة السجل: " + cCatchError
            done
        ok
    
    /*
    تسجيل رسالة تصحيح (Debug)
    المدخلات: cMessage - الرسالة
    */
    func debug cMessage
        log(LOG_LEVEL_DEBUG, cMessage)
    
    /*
    تسجيل رسالة معلومات (Info)
    المدخلات: cMessage - الرسالة
    */
    func info cMessage
        log(LOG_LEVEL_INFO, cMessage)
    
    /*
    تسجيل رسالة تحذير (Warning)
    المدخلات: cMessage - الرسالة
    */
    func warning cMessage
        log(LOG_LEVEL_WARNING, cMessage)
    
    /*
    تسجيل رسالة خطأ (Error)
    المدخلات: cMessage - الرسالة
    */
    func error cMessage
        log(LOG_LEVEL_ERROR, cMessage)
    
    /*
    تسجيل رسالة خطأ حرج (Critical)
    المدخلات: cMessage - الرسالة
    */
    func critical cMessage
        log(LOG_LEVEL_CRITICAL, cMessage)
    
    /*
    تسجيل بداية عملية
    المدخلات: cOperation - اسم العملية
    */
    func startOperation cOperation
        info("بداية العملية: " + cOperation)
    
    /*
    تسجيل انتهاء عملية
    المدخلات: cOperation - اسم العملية
    */
    func endOperation cOperation
        info("انتهاء العملية: " + cOperation)
    
    /*
    تسجيل نتيجة عملية
    المدخلات: cOperation - اسم العملية، bSuccess - نجح أم لا، cDetails - تفاصيل إضافية
    */
    func logOperationResult cOperation, bSuccess, cDetails
        if bSuccess
            info("نجحت العملية: " + cOperation + " - " + cDetails)
        else
            error("فشلت العملية: " + cOperation + " - " + cDetails)
        ok
    
    /*
    تسجيل معلومات الهدف
    المدخلات: cTarget - الهدف، cType - نوع الهدف
    */
    func logTarget cTarget, cType
        info("الهدف: " + cTarget + " (النوع: " + cType + ")")
    
    /*
    تسجيل نتيجة فحص
    المدخلات: cTarget - الهدف، cResult - النتيجة، cDetails - التفاصيل
    */
    func logScanResult cTarget, cResult, cDetails
        info("نتيجة فحص " + cTarget + ": " + cResult + " - " + cDetails)
    
    /*
    تسجيل اكتشاف ثغرة أمنية
    المدخلات: cVulnerability - اسم الثغرة، cTarget - الهدف، cSeverity - الخطورة
    */
    func logVulnerability cVulnerability, cTarget, cSeverity
        warning("تم اكتشاف ثغرة أمنية: " + cVulnerability + 
                " في الهدف: " + cTarget + 
                " (الخطورة: " + cSeverity + ")")
    
    /*
    مسح محتوى ملف السجل
    */
    func clearLog
        if bLogToFile
            write(cLogFile, "")
            info("تم مسح ملف السجل")
        ok
    
    /*
    الحصول على محتوى ملف السجل
    المخرجات: محتوى ملف السجل
    */
    func getLogContent
        if bLogToFile and fexists(cLogFile)
            return read(cLogFile)
        else
            return ""
        ok
    
    /*
    طباعة إحصائيات السجل
    */
    func printLogStats
        if not bLogToFile or not fexists(cLogFile)
            ? "ملف السجل غير متاح"
            return
        ok
        
        cContent = read(cLogFile)
        aLines = split(cContent, nl)
        nTotalLines = len(aLines)
        
        nDebugCount = 0
        nInfoCount = 0
        nWarningCount = 0
        nErrorCount = 0
        nCriticalCount = 0
        
        for cLine in aLines
            if substr(cLine, "[DEBUG]") > 0
                nDebugCount++
            but substr(cLine, "[INFO]") > 0
                nInfoCount++
            but substr(cLine, "[WARNING]") > 0
                nWarningCount++
            but substr(cLine, "[ERROR]") > 0
                nErrorCount++
            but substr(cLine, "[CRITICAL]") > 0
                nCriticalCount++
            ok
        next
        
        ? "============== إحصائيات السجل =============="
        ? "إجمالي الأسطر: " + nTotalLines
        ? "رسائل التصحيح: " + nDebugCount
        ? "رسائل المعلومات: " + nInfoCount
        ? "رسائل التحذير: " + nWarningCount
        ? "رسائل الأخطاء: " + nErrorCount
        ? "رسائل الأخطاء الحرجة: " + nCriticalCount
        ? "============================================="



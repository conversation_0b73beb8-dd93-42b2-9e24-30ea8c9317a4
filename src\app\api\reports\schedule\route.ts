import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// POST /api/reports/schedule - جدولة إرسال التقرير
export async function POST(req: NextRequest) {
  try {
    // التحقق من المستخدم
    const jwtToken = req.cookies.get("jwtToken")?.value;
    if (!jwtToken) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    const token = await getToken(jwtToken);
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    // استخراج البيانات من الطلب
    const body = await req.json();
    const {
      reportId,
      reportType,
      recipients,
      frequency,
      startDate,
      time,
      dayOfWeek,
      dayOfMonth,
      format
    } = body;

    if (!reportId || !reportType || !recipients || !frequency || !startDate || !time) {
      return NextResponse.json(
        { error: 'جميع الحقول الأساسية مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من صحة التكرار
    if (!['daily', 'weekly', 'monthly'].includes(frequency)) {
      return NextResponse.json(
        { error: 'تكرار غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من صحة يوم الأسبوع إذا كان التكرار أسبوعي
    if (frequency === 'weekly' && (dayOfWeek === undefined || dayOfWeek < 0 || dayOfWeek > 6)) {
      return NextResponse.json(
        { error: 'يوم الأسبوع غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من صحة يوم الشهر إذا كان التكرار شهري
    if (frequency === 'monthly' && (dayOfMonth === undefined || dayOfMonth < 1 || dayOfMonth > 31)) {
      return NextResponse.json(
        { error: 'يوم الشهر غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من صحة الصيغة
    if (!['pdf', 'excel'].includes(format)) {
      return NextResponse.json(
        { error: 'صيغة غير صالحة' },
        { status: 400 }
      );
    }

    // إنشاء تاريخ البدء
    const [hours, minutes] = time.split(':').map(Number);
    const startDateTime = new Date(startDate);
    startDateTime.setHours(hours, minutes, 0, 0);

    // إنشاء جدولة التقرير
    const schedule = await prisma.reportSchedule.create({
      data: {
        reportId,
        reportType,
        recipients: Array.isArray(recipients) ? recipients.join(',') : recipients,
        frequency,
        startDate: startDateTime,
        dayOfWeek: frequency === 'weekly' ? parseInt(dayOfWeek) : null,
        dayOfMonth: frequency === 'monthly' ? parseInt(dayOfMonth) : null,
        format,
        createdBy: token.id.toString(),
        isActive: true,
        lastSentAt: null
      }
    });

    return NextResponse.json({
      success: true,
      message: 'تم جدولة إرسال التقرير بنجاح',
      schedule
    });
  } catch (error) {
    console.error('خطأ في جدولة إرسال التقرير:', error);
    return NextResponse.json(
      { error: 'فشل في جدولة إرسال التقرير' },
      { status: 500 }
    );
  }
}

// GET /api/reports/schedule - الحصول على قائمة جدولة التقارير
export async function GET(req: NextRequest) {
  try {
    // التحقق من المستخدم
    const jwtToken = req.cookies.get("jwtToken")?.value;
    if (!jwtToken) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    const token = await getToken(jwtToken);
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    // الحصول على جدولة التقارير الخاصة بالمستخدم
    const schedules = await prisma.reportSchedule.findMany({
      where: {
        createdBy: token.id.toString()
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      schedules
    });
  } catch (error) {
    console.error('خطأ في جلب جدولة التقارير:', error);
    return NextResponse.json(
      { error: 'فشل في جلب جدولة التقارير' },
      { status: 500 }
    );
  }
}

// DELETE /api/reports/schedule - حذف جدولة تقرير
export async function DELETE(req: NextRequest) {
  try {
    // التحقق من المستخدم
    const jwtToken = req.cookies.get("jwtToken")?.value;
    if (!jwtToken) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    const token = await getToken(jwtToken);
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    // استخراج معرف الجدولة من الطلب
    const { searchParams } = new URL(req.url);
    const scheduleId = searchParams.get('id');

    if (!scheduleId) {
      return NextResponse.json(
        { error: 'معرف الجدولة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الجدولة وملكيتها
    const schedule = await prisma.reportSchedule.findUnique({
      where: {
        id: parseInt(scheduleId)
      }
    });

    if (!schedule) {
      return NextResponse.json(
        { error: 'الجدولة غير موجودة' },
        { status: 404 }
      );
    }

    if (schedule.createdBy !== token.id.toString()) {
      return NextResponse.json(
        { error: 'غير مصرح لك بحذف هذه الجدولة' },
        { status: 403 }
      );
    }

    // حذف الجدولة
    await prisma.reportSchedule.delete({
      where: {
        id: parseInt(scheduleId)
      }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف الجدولة بنجاح'
    });
  } catch (error) {
    console.error('خطأ في حذف جدولة التقرير:', error);
    return NextResponse.json(
      { error: 'فشل في حذف جدولة التقرير' },
      { status: 500 }
    );
  }
}

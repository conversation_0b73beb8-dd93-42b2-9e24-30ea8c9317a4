'use client';

import { useState, useEffect } from 'react';
import { Payment } from '@prisma/client';
import { Button, Select, Input, DatePicker, message, Spin } from 'antd';
import PaymentModal from './PaymentModal';
import { FaMoneyBillWave, FaS<PERSON>ch, FaFilter, FaCalendarAlt } from 'react-icons/fa';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

import dayjs from 'dayjs';
import 'dayjs/locale/ar'; // تحميل اللغة العربية

dayjs.locale('ar'); // تعيين اللغة العربية
interface Student {
  id: string;
  name: string;
}

interface PaymentMethod {
  id: number;
  name: string;
  description: string | null;
  isActive: boolean;
  icon: string | null;
}

interface PaymentMethodOnPayment {
  paymentId: number;
  paymentMethodId: number;
  amount: number;
  transactionId?: string | null;
  paymentMethod: PaymentMethod;
}

interface PaymentWithStudent extends Payment {
  student: Student;
  paymentMethod?: PaymentMethod | null;
  paymentMethodName: string | null;
  paymentMethods?: PaymentMethodOnPayment[];
}

interface PaginationData {
  total: number;
  pages: number;
  current: number;
}

export default function PaymentsPage() {
  const [payments, setPayments] = useState<PaymentWithStudent[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({ total: 0, pages: 0, current: 1 });
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedMonth, setSelectedMonth] = useState<dayjs.Dayjs>(dayjs());
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handlePrintReceipt = async (payment: PaymentWithStudent) => {
    try {
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        message.error('فشل في فتح نافذة الطباعة');
        return;
      }

      // Create the receipt content
      const receiptContent = `
        <html dir="rtl">
          <head>
            <title>وصل دفع</title>
            <style>
              @media print {
                body { margin: 0; padding: 10px; }
                .receipt { width: 80mm; }
                .logo { max-width: 60px; }
              }
              body {
                font-family: Arial, sans-serif;
                background: #f9f9f9;
              }
              .receipt {
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                margin: 0 auto;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
              }
              .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 2px solid var(--primary-color);
                position: relative;
              }
              .header h2 {
                color: var(--primary-color);
                margin: 0 0 5px 0;
                font-size: 1.6em;
              }
              .header p {
                color: #555;
                margin: 5px 0;
                font-size: 0.9em;
              }
              .receipt-number {
                position: absolute;
                top: 0;
                left: 0;
                background: var(--primary-color);
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 0.8em;
              }
              .school-info {
                text-align: center;
                margin-bottom: 20px;
                font-size: 0.9em;
                color: #333;
                display: flex;
                flex-direction: column;
                align-items: center;
              }
              .logo {
                width: 60px;
                height: 60px;
                margin-bottom: 10px;
                object-fit: contain;
              }
              .details {
                margin-bottom: 20px;
                font-size: 0.95em;
                border: 1px solid #eee;
                border-radius: 6px;
                padding: 10px;
                background-color: #f9f9f9;
              }
              .row {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px dashed #ddd;
              }
              .row:last-child {
                border-bottom: none;
              }
              .row span:first-child {
                color: #555;
                font-weight: bold;
              }
              .row span:last-child {
                font-weight: bold;
                color: var(--primary-color);
              }
              .amount-row {
                background-color: #e6f7f5;
                border-radius: 4px;
                padding: 8px;
                margin: 10px 0;
              }
              .amount-row span:last-child {
                font-size: 1.1em;
                color: var(--primary-color);
              }
              .status-paid {
                background-color: #d4edda;
                color: #155724 !important;
                padding: 3px 8px;
                border-radius: 4px;
              }
              .status-pending {
                background-color: #fff3cd;
                color: #856404 !important;
                padding: 3px 8px;
                border-radius: 4px;
              }
              .status-cancelled {
                background-color: #f8d7da;
                color: #721c24 !important;
                padding: 3px 8px;
                border-radius: 4px;
              }
              .footer {
                text-align: center;
                margin-top: 20px;
                padding-top: 15px;
                border-top: 2px solid var(--primary-color);
                font-size: 0.85em;
                color: #555;
              }
              .qr-code {
                text-align: center;
                margin-top: 15px;
              }
              .qr-code img {
                width: 80px;
                height: 80px;
              }
              .payment-methods {
                display: flex;
                flex-direction: column;
                gap: 5px;
              }
              .payment-method {
                background-color: #e6f7f5;
                border-radius: 4px;
                padding: 5px 8px;
                display: inline-block;
              }
              .watermark {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 3em;
                color: rgba(22, 155, 136, 0.1);
                pointer-events: none;
                z-index: 0;
                white-space: nowrap;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <div class="watermark">مدرسة القرآن الكريم</div>
              <div class="header">
                <div class="receipt-number">رقم: ${payment.id}</div>
                <h2>وصل دفع</h2>
                <p>${payment.receiptNumber ? 'رقم الإيصال: ' + payment.receiptNumber : ''}</p>
              </div>
              <div class="school-info">
                <img src="/logo.png" alt="شعار المدرسة" class="logo" onerror="this.style.display='none'" />
                <p style="font-weight: bold; font-size: 1.1em;">مدرسة القرآن الكريم</p>
                <p>تعليم القرآن وتحفيظه</p>
              </div>
              <div class="details">
                <div class="row">
                  <span>اسم الطالب:</span>
                  <span>${payment.student.name}</span>
                </div>
                <div class="row amount-row">
                  <span>المبلغ:</span>
                  <span>${payment.amount} دج</span>
                </div>
                <div class="row">
                  <span>تاريخ الدفع:</span>
                  <span>${payment.createdAt.toString().split("T")[0]}</span>
                </div>
                <div class="row">
                  <span>لشهر:</span>
                  <span>${dayjs(payment.date).locale("ar").format("MMMM YYYY")}</span>
                </div>
                <div class="row">
                  <span>طريقة الدفع:</span>
                  <span class="payment-methods">
                    ${payment.paymentMethods && payment.paymentMethods.length > 0
                      ? payment.paymentMethods.map(pm =>
                          `<span class="payment-method">${pm.paymentMethod.name} (${pm.amount} دج)</span>`
                        ).join('')
                      : payment.paymentMethodName === 'مباشر'
                        ? `<span class="payment-method" style="background-color: #d4edda; color: #155724;">مباشر</span>`
                        : payment.paymentMethod
                          ? `<span class="payment-method">${payment.paymentMethod.name}</span>`
                          : 'غير محدد'}
                  </span>
                </div>
                <div class="row">
                  <span>الحالة:</span>
                  <span class="${
                    payment.status === 'PAID'
                      ? 'status-paid'
                      : payment.status === 'PENDING'
                        ? 'status-pending'
                        : 'status-cancelled'
                  }">
                    ${payment.status === 'PAID'
                      ? 'مدفوع'
                      : payment.status === 'PENDING'
                        ? 'قيد الانتظار'
                        : 'ملغي'}
                  </span>
                </div>
                ${payment.transactionId ? `
                <div class="row">
                  <span>رقم المعاملة:</span>
                  <span>${payment.transactionId}</span>
                </div>
                ` : ''}
                ${payment.notes ? `
                <div class="row">
                  <span>ملاحظات:</span>
                  <span>${payment.notes}</span>
                </div>
                ` : ''}
              </div>
              <div class="footer">
                <p>شكراً لكم - بارك الله فيكم</p>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('fr-FR')}</p>
              </div>
              <div class="qr-code">
                <img src="https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=payment_id_${payment.id}" alt="رمز QR" />
              </div>
            </div>
            <script>
              window.onload = () => {
                window.print();
                window.onafterprint = () => window.close();
              };
            </script>
          </body>
        </html>
      `;

      // Write the content to the new window and trigger print
      printWindow.document.open();
      printWindow.document.write(receiptContent);
      printWindow.document.close();
    } catch {
      message.error('تعذر طباعة الوصل');
    }
  };

  const fetchPayments = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: pagination.current.toString(),
        query: searchQuery,
        status: statusFilter,
        month: selectedMonth ? selectedMonth.format('M') : '',
        year: selectedMonth ? selectedMonth.format('YYYY') : ''
      });

      const response = await fetch(`/api/payments?${queryParams}`);
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      const formattedPayments = data.payments.map((payment: PaymentWithStudent) => ({
        ...payment,
        date1: payment.date ? dayjs(payment.date).format('DD/MM/YYYY') : '-',
        status: payment.status || 'PENDING',
        paymentMethodName: payment.paymentMethod ? payment.paymentMethod.name : '-'
      }));

      setPayments(formattedPayments);
      setPagination(data.pagination);
    } catch {
      message.error('تعذر جلب بيانات المدفوعات');
    } finally {
      setLoading(false);
    }
  };


  useEffect(() => {
    fetchPayments();
  }, [pagination.current, searchQuery, statusFilter, selectedMonth]); // eslint-disable-line react-hooks/exhaustive-deps



  return (
    <OptimizedProtectedRoute requiredPermission="admin.payments.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaMoneyBillWave className="text-[var(--primary-color)]" />
          المدفوعات
        </h1>
        <QuickActionButtons
          entityType="payments"
          actions={[
            {
              key: 'create',
              label: 'تسجيل دفعة جديدة',
              icon: <FaMoneyBillWave />,
              onClick: () => setIsModalVisible(true),
              variant: 'primary'
            }
          ]}
          className="w-full md:w-auto"
        />

        {/* مكون النافذة المنبثقة الجديد */}
        <PaymentModal
          isOpen={isModalVisible}
          onCloseAction={() => setIsModalVisible(false)}
          onSuccessAction={fetchPayments}
        />
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center gap-2 flex-1">
          <FaSearch className="text-[var(--primary-color)] min-w-5" />
          <Input.Search
            placeholder="البحث عن طالب أو رقم الدفعة"
            onSearch={value => {
              setSearchQuery(value);
              setPagination(prev => ({ ...prev, current: 1 }));
            }}
            style={{ width: '100%' }}
            className="border-[var(--primary-color)] focus:border-[var(--secondary-color)]"
          />
        </div>
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <div className="flex items-center gap-2 w-full md:w-auto">
            <FaFilter className="text-[var(--primary-color)] min-w-5" />
            <Select
              value={statusFilter}
              onChange={value => {
                setStatusFilter(value);
                setPagination(prev => ({ ...prev, current: 1 }));
              }}
              style={{ width: '100%' }}
              className="border-[var(--primary-color)]"
            >
              <Select.Option value="all">جميع الحالات</Select.Option>
              <Select.Option value="PENDING">قيد الانتظار</Select.Option>
              <Select.Option value="PAID">مدفوع</Select.Option>
            </Select>
          </div>
          <div className="flex items-center gap-2 w-full md:w-auto">
            <FaCalendarAlt className="text-[var(--primary-color)] min-w-5" />
            <DatePicker
              picker="month"
              value={selectedMonth}
              onChange={(date) => {
                setSelectedMonth(date || dayjs());
                setPagination(prev => ({ ...prev, current: 1 }));
              }}
              format="YYYY/MM"
              className="border-[var(--primary-color)] w-full"
            />
          </div>
        </div>
      </div>

      <div className="responsive-table-container">
        <table className="min-w-full bg-white border border-green-100 rounded-lg card-mode-table">
          <thead>
            <tr className="bg-[var(--primary-color)]">
              <th className="px-6 py-3 border-b text-right text-white font-bold">رقم الدفعة</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">اسم الطالب</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">المبلغ</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">التاريخ</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">طريقة الدفع</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">الحالة</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={7} className="text-center py-4">
                  <Spin />
                </td>
              </tr>
            ) : payments.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center py-4">لا توجد مدفوعات</td>
              </tr>
            ) : (
              payments.map((payment) => (
                <tr key={payment.id} className="hover:bg-green-50/50 transition-colors duration-150">
                  <td className="px-6 py-4 border-b" data-label="رقم الدفعة">{payment.id}</td>
                  <td className="px-6 py-4 border-b" data-label="اسم الطالب">{payment.student.name}</td>
                  <td className="px-6 py-4 border-b" data-label="المبلغ">{payment.amount} دج</td>
                  <td className="px-6 py-4 border-b" data-label="التاريخ">{payment.date.toString().split("T")[0]}</td>
                  <td className="px-6 py-4 border-b" data-label="طريقة الدفع">
                    {payment.paymentMethods && payment.paymentMethods.length > 0 ? (
                      <div className="flex flex-col gap-1">
                        {payment.paymentMethods.map((pm, index) => (
                          <span key={index} className="px-2 py-1 rounded text-sm bg-blue-100 text-blue-800 inline-block">
                            {pm.paymentMethod.name} ({pm.amount} دج)
                          </span>
                        ))}
                      </div>
                    ) : payment.paymentMethodName === 'مباشر' ? (
                      <span className="px-2 py-1 rounded text-sm bg-green-100 text-green-800">
                        مباشر
                      </span>
                    ) : payment.paymentMethod ? (
                      <span className="px-2 py-1 rounded text-sm bg-blue-100 text-blue-800">
                        {payment.paymentMethod.name}
                      </span>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </td>
                  <td className="px-6 py-4 border-b" data-label="الحالة">
                    <span
                      className={`px-2 py-1 rounded text-sm ${
                        payment.status === 'PAID'
                          ? 'bg-green-100 text-green-800'
                          : payment.status === 'PENDING'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {payment.status === 'PAID'
                        ? 'مدفوع'
                        : payment.status === 'PENDING'
                        ? 'قيد الانتظار'
                        : 'ملغي'}
                    </span>
                  </td>
                  <td className="px-6 py-4 border-b actions" data-label="الإجراءات">
                    {payment.status !== 'PENDING' && (
                      <PermissionGuard requiredPermission="admin.payments.print">
                        <Button
                          type="primary"
                          size="small"
                          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                          onClick={() => handlePrintReceipt(payment)}
                        >
                          طباعة الوصل
                        </Button>
                      </PermissionGuard>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {!loading && payments.length > 0 && (
        <div className="mt-4 flex flex-col sm:flex-row justify-center sm:justify-end items-center gap-3 bg-white p-3 rounded-lg shadow-sm">
          <div className="flex items-center justify-center w-full sm:w-auto">
            <span className="text-[var(--primary-color)] text-sm">
              الصفحة {pagination.current} من {pagination.pages}
            </span>
          </div>
          <div className="flex gap-2 w-full sm:w-auto justify-center">
            <Button
              disabled={pagination.current === 1}
              onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
              className={`border-[var(--primary-color)] text-[var(--primary-color)] ${pagination.current === 1 ? 'opacity-50' : 'hover:bg-green-50'}`}
            >
              السابق
            </Button>
            <Button
              disabled={pagination.current === pagination.pages}
              onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
              className={`border-[var(--primary-color)] text-[var(--primary-color)] ${pagination.current === pagination.pages ? 'opacity-50' : 'hover:bg-green-50'}`}
            >
              التالي
            </Button>
          </div>
        </div>
      )}

      </div>
    </OptimizedProtectedRoute>
  );
}

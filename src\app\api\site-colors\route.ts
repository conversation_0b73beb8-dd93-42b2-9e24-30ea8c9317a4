import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/site-colors - الحصول على ألوان الموقع
export async function GET() {
  try {
    // البحث عن ألوان الموقع في قاعدة البيانات
    const siteColors = await prisma.systemSettings.findUnique({
      where: { key: 'SITE_COLORS' },
    });

    // إذا لم تكن هناك ألوان مخزنة، إرجاع null
    if (!siteColors) {
      return NextResponse.json({
        colors: null,
        success: true,
        message: 'No colors found in database'
      });
    }

    const colors = JSON.parse(siteColors.value);

    return NextResponse.json({
      colors,
      success: true,
    });
  } catch (error) {
    console.error('خطأ في جلب ألوان الموقع:', error);
    return NextResponse.json(
      { error: 'فشل في جلب ألوان الموقع', success: false },
      { status: 500 }
    );
  }
}

// POST /api/site-colors - تحديث ألوان الموقع
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { colors, mode } = body; // إضافة mode لتحديد الوضع

    // التحقق من وجود الألوان
    if (!colors) {
      return NextResponse.json(
        { error: 'لم يتم توفير ألوان الموقع', success: false },
        { status: 400 }
      );
    }

    // التحقق من صحة الألوان
    const requiredColors = ['primaryColor', 'secondaryColor', 'sidebarColor', 'backgroundColor', 'accentColor', 'textColor'];
    const missingColors = requiredColors.filter(color => !colors[color]);

    if (missingColors.length > 0) {
      return NextResponse.json(
        { error: `الألوان التالية مطلوبة: ${missingColors.join(', ')}`, success: false },
        { status: 400 }
      );
    }

    // حفظ ألوان الوضع النهاري فقط في قاعدة البيانات (للمشاركة بين جميع المستخدمين)
    // الوضع المظلم يبقى شخصي للمستخدمين
    if (!mode || mode === 'light') {
      await prisma.systemSettings.upsert({
        where: { key: 'LIGHT_MODE_COLORS' },
        update: {
          value: JSON.stringify(colors),
        },
        create: {
          key: 'LIGHT_MODE_COLORS',
          value: JSON.stringify(colors),
        },
      });
    }

    // تحديث الألوان العامة أيضاً (للتوافق مع النظام الحالي)
    const updatedColors = await prisma.systemSettings.upsert({
      where: { key: 'SITE_COLORS' },
      update: {
        value: JSON.stringify(colors),
      },
      create: {
        key: 'SITE_COLORS',
        value: JSON.stringify(colors),
      },
    });

    // تحديث الألوان في إعدادات الموقع العامة أيضاً
    try {
      const siteSettings = await prisma.systemSettings.findUnique({
        where: { key: 'SITE_SETTINGS' },
      });

      if (siteSettings) {
        const settings = JSON.parse(siteSettings.value);
        const updatedSettings = {
          ...settings,
          ...colors
        };

        await prisma.systemSettings.update({
          where: { key: 'SITE_SETTINGS' },
          data: {
            value: JSON.stringify(updatedSettings),
          },
        });
      }
    } catch (error) {
      console.error('خطأ في تحديث الألوان في إعدادات الموقع:', error);
    }

    return NextResponse.json({
      success: true,
      colors: JSON.parse(updatedColors.value),
      mode: mode || 'light',
      message: `تم تحديث ألوان ${mode === 'dark' ? 'الوضع المظلم' : 'الوضع النهاري'} بنجاح`
    });
  } catch (error) {
    console.error('خطأ في تحديث ألوان الموقع:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث ألوان الموقع', success: false },
      { status: 500 }
    );
  }
}

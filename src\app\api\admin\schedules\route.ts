import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const classeId = searchParams.get('classeId');

    // استخدام ClassSchedule بدلاً من Schedule الذي لا يوجد في مخطط Prisma
    const schedules = await prisma.classSchedule.findMany({
      where: classeId ? { classeId: parseInt(classeId) } : {},
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      },
      orderBy: [
        { day: 'asc' },
        { startTime: 'asc' }
      ]
    });

    return NextResponse.json(schedules);
  } catch (error) {
    console.error('Schedule fetch error:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الجداول', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { classeId, day, startTime, endTime, teacherSubjectId } = body;

    if (!classeId || !day || !startTime || !endTime || !teacherSubjectId) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من وجود تعارض في الجدول
    const existingSchedule = await prisma.classSchedule.findFirst({
      where: {
        classeId: parseInt(classeId),
        day,
        OR: [
          {
            startTime: {
              gte: startTime,
              lt: endTime
            }
          },
          {
            endTime: {
              gt: startTime,
              lte: endTime
            }
          },
          {
            startTime: {
              lte: startTime
            },
            endTime: {
              gte: endTime
            }
          }
        ]
      }
    });

    if (existingSchedule) {
      return NextResponse.json(
        { error: 'يوجد تعارض في الجدول، الرجاء اختيار وقت آخر' },
        { status: 400 }
      );
    }

    // استخدام ClassSchedule بدلاً من Schedule
    const schedule = await prisma.classSchedule.create({
      data: {
        classeId: parseInt(classeId),
        day,
        startTime,
        endTime,
        teacherSubjectId: parseInt(teacherSubjectId)
      },
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'تم إنشاء الجدول بنجاح',
      schedule
    });
  } catch (error) {
    console.error('Schedule creation error:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الجدول', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
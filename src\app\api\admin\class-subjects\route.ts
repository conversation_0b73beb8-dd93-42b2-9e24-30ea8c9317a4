import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { classeId, teacherSubjectId } = body;

    if (!classeId || !teacherSubjectId || typeof classeId !== 'number' || typeof teacherSubjectId !== 'number') {
      return NextResponse.json(
        { message: "يجب توفير معرف الصف ومعرف علاقة المعلم بالمادة بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود الصف وعلاقة المعلم بالمادة
    const [classExists, teacherSubjectExists] = await Promise.all([
      prisma.classe.findUnique({ where: { id: classeId } }),
      prisma.teacherSubject.findUnique({ 
        where: { id: teacherSubjectId },
        include: { teacher: true, subject: true }
      }),
    ]);

    if (!classExists) {
      return NextResponse.json(
        { message: "الصف غير موجود" },
        { status: 404 }
      );
    }

    if (!teacherSubjectExists) {
      return NextResponse.json(
        { message: "علاقة المعلم بالمادة غير موجودة" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود تكرار للمادة في نفس الصف
    const existingClassSubject = await prisma.classSubject.findFirst({
      where: {
        classeId,
        teacherSubjectId,
      },
    });

    if (existingClassSubject) {
      return NextResponse.json(
        { message: "هذه المادة مسجلة مسبقاً لهذا الصف مع نفس المعلم" },
        { status: 400 }
      );
    }

    // إنشاء العلاقة بين الصف والمادة والمعلم
    const classSubject = await prisma.classSubject.create({
      data: {
        classeId,
        teacherSubjectId,
      },
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      },
    });

    return NextResponse.json(classSubject, { status: 201 });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء معالجة الطلب" },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const classSubjects = await prisma.classSubject.findMany({
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      },
    });

    return NextResponse.json(classSubjects);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء استرجاع البيانات" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, teacherSubjectId } = body;

    if (!id || !teacherSubjectId || typeof id !== 'number' || typeof teacherSubjectId !== 'number') {
      return NextResponse.json(
        { message: "يجب توفير معرف العلاقة ومعرف علاقة المعلم بالمادة الجديدة بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود العلاقة وعلاقة المعلم بالمادة
    const [classSubject, teacherSubjectExists] = await Promise.all([
      prisma.classSubject.findUnique({ where: { id } }),
      prisma.teacherSubject.findUnique({ 
        where: { id: teacherSubjectId },
        include: { teacher: true, subject: true }
      }),
    ]);

    if (!classSubject) {
      return NextResponse.json(
        { message: "العلاقة غير موجودة" },
        { status: 404 }
      );
    }

    if (!teacherSubjectExists) {
      return NextResponse.json(
        { message: "علاقة المعلم بالمادة غير موجودة" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود تكرار للمادة في نفس الصف مع نفس المعلم
    const existingClassSubject = await prisma.classSubject.findFirst({
      where: {
        classeId: classSubject.classeId,
        teacherSubjectId,
        NOT: { id }
      },
    });

    if (existingClassSubject) {
      return NextResponse.json(
        { message: "هذه المادة مسجلة مسبقاً لهذا الصف مع نفس المعلم" },
        { status: 400 }
      );
    }

    // تحديث علاقة المعلم بالمادة للصف
    const updatedClassSubject = await prisma.classSubject.update({
      where: { id },
      data: {
        teacherSubjectId
      },
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    return NextResponse.json(updatedClassSubject);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث البيانات" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const numericId = id ? parseInt(id) : null;

    if (!id || !numericId || isNaN(numericId)) {
      return NextResponse.json(
        { message: "يجب توفير معرف العلاقة بتنسيق صحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود العلاقة
    const classSubject = await prisma.classSubject.findUnique({
      where: { id: parseInt(id) },
    });

    if (!classSubject) {
      return NextResponse.json(
        { message: "العلاقة غير موجودة" },
        { status: 404 }
      );
    }

    // حذف العلاقة
    await prisma.classSubject.delete({
      where: { id: parseInt(id) },
    });

    return NextResponse.json(
      { message: "تم حذف العلاقة بنجاح" },
      { status: 200 }
    );
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف البيانات" },
      { status: 500 }
    );
  }
}
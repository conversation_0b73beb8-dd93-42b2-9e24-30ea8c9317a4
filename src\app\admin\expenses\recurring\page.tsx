"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  FaSync,
  FaPlus,
  FaEdit,
  FaTrash,
  FaTags,
  FaCheck,
  FaArrowLeft,
  FaSave,
  FaPlay,
  FaPause,
  FaRedo,
  FaMoneyBillWave
} from 'react-icons/fa';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface ExpenseCategory {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  color: string | null;
  isActive: boolean;
}

interface RecurringExpense {
  id: number;
  purpose: string;
  amount: number;
  categoryId: number | null;
  category: ExpenseCategory | null;
  startDate: string;
  endDate: string | null;
  frequency: string;
  interval: number;
  lastGeneratedDate: string | null;
  nextGenerationDate: string;
  isActive: boolean;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function RecurringExpensesPage() {
  const [recurringExpenses, setRecurringExpenses] = useState<RecurringExpense[]>([]);
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<RecurringExpense | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [formData, setFormData] = useState({
    purpose: '',
    amount: '',
    categoryId: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    frequency: 'monthly',
    interval: '1',
    notes: '',
  });
  const [generating, setGenerating] = useState(false);

  // جلب المصروفات الدورية
  const fetchRecurringExpenses = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      if (statusFilter !== 'all') {
        queryParams.append('isActive', statusFilter === 'active' ? 'true' : 'false');
      }

      if (categoryFilter !== 'all') {
        queryParams.append('categoryId', categoryFilter);
      }

      const response = await fetch(`/api/expenses/recurring?${queryParams}`);

      if (!response.ok) {
        throw new Error('فشل في جلب المصروفات الدورية');
      }

      const data = await response.json();
      setRecurringExpenses(data.recurringExpenses);
    } catch (error) {
      console.error('خطأ في جلب المصروفات الدورية:', error);
      toast.error('فشل في جلب المصروفات الدورية');
    } finally {
      setLoading(false);
    }
  };

  // جلب فئات المصروفات
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories');

      if (!response.ok) {
        throw new Error('فشل في جلب فئات المصروفات');
      }

      const data = await response.json();
      setCategories(data.categories);
    } catch (error) {
      console.error('خطأ في جلب فئات المصروفات:', error);
      toast.error('فشل في جلب فئات المصروفات');
    }
  };

  // إضافة مصروف دوري جديد
  const handleAddRecurringExpense = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (!formData.purpose || !formData.amount || !formData.startDate || !formData.frequency || !formData.interval) {
        toast.error('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      // إظهار رسالة تحميل
      const toastId = toast.loading('جاري إضافة المصروف الدوري...');

      try {
        const response = await fetch('/api/expenses/recurring', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            purpose: formData.purpose,
            amount: parseFloat(formData.amount),
            categoryId: formData.categoryId ? parseInt(formData.categoryId) : null,
            startDate: formData.startDate,
            endDate: formData.endDate || null,
            frequency: formData.frequency,
            interval: parseInt(formData.interval),
            notes: formData.notes || null,
          }),
        });

        // تحديث الرسالة بناءً على نتيجة الطلب
        if (response.ok) {
          toast.update(toastId, {
            render: 'تم إضافة المصروف الدوري بنجاح',
            type: "success",
            isLoading: false,
            autoClose: 3000
          });
          setIsAddDialogOpen(false);
          resetForm();
          fetchRecurringExpenses();
        } else {
          toast.update(toastId, {
            render: 'فشل في إضافة المصروف الدوري',
            type: "error",
            isLoading: false,
            autoClose: 3000
          });
        }
      } catch (error) {
        // إذا حدث خطأ في الطلب نفسه
        console.error('خطأ في إضافة المصروف الدوري:', error);
        toast.update(toastId, {
          render: 'فشل في إضافة المصروف الدوري',
          type: "error",
          isLoading: false,
          autoClose: 3000
        });
      }
    } catch (error) {
      // إذا حدث خطأ في معالجة toast
      console.error('خطأ في إضافة المصروف الدوري:', error);
      toast.error('فشل في إضافة المصروف الدوري');
    }
  };

  // تعديل مصروف دوري
  const handleEditRecurringExpense = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedExpense) return;

    try {
      if (!formData.purpose || !formData.amount || !formData.startDate || !formData.frequency || !formData.interval) {
        toast.error('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      // إظهار رسالة تحميل
      const toastId = toast.loading('جاري تعديل المصروف الدوري...');

      try {
        const response = await fetch(`/api/expenses/recurring/${selectedExpense.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            purpose: formData.purpose,
            amount: parseFloat(formData.amount),
            categoryId: formData.categoryId ? parseInt(formData.categoryId) : null,
            startDate: formData.startDate,
            endDate: formData.endDate || null,
            frequency: formData.frequency,
            interval: parseInt(formData.interval),
            notes: formData.notes || null,
          }),
        });

        // تحديث الرسالة بناءً على نتيجة الطلب
        if (response.ok) {
          toast.update(toastId, {
            render: 'تم تعديل المصروف الدوري بنجاح',
            type: "success",
            isLoading: false,
            autoClose: 3000
          });
          setIsEditDialogOpen(false);
          fetchRecurringExpenses();
        } else {
          toast.update(toastId, {
            render: 'فشل في تعديل المصروف الدوري',
            type: "error",
            isLoading: false,
            autoClose: 3000
          });
        }
      } catch (error) {
        // إذا حدث خطأ في الطلب نفسه
        console.error('خطأ في تعديل المصروف الدوري:', error);
        toast.update(toastId, {
          render: 'فشل في تعديل المصروف الدوري',
          type: "error",
          isLoading: false,
          autoClose: 3000
        });
      }
    } catch (error) {
      // إذا حدث خطأ في معالجة toast
      console.error('خطأ في تعديل المصروف الدوري:', error);
      toast.error('فشل في تعديل المصروف الدوري');
    }
  };

  // حذف مصروف دوري
  const handleDeleteRecurringExpense = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المصروف الدوري؟')) return;

    try {
      // إظهار رسالة تحميل
      const toastId = toast.loading('جاري حذف المصروف الدوري...');

      try {
        console.log(`Sending DELETE request to /api/expenses/recurring/${id}`);

        const response = await fetch(`/api/expenses/recurring/${id}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        console.log(`Response status:`, response.status);
        const responseText = await response.text();
        console.log(`Response text:`, responseText);

        let responseData;
        try {
          responseData = JSON.parse(responseText);
          console.log(`Parsed response data:`, responseData);
        } catch (e) {
          console.error(`Failed to parse response as JSON:`, e);
        }

        // تحديث الرسالة بناءً على نتيجة الطلب
        if (response.ok) {
          toast.update(toastId, {
            render: responseData?.message || 'تم حذف المصروف الدوري بنجاح',
            type: "success",
            isLoading: false,
            autoClose: 3000
          });
          fetchRecurringExpenses();
        } else {
          toast.update(toastId, {
            render: responseData?.error || 'فشل في حذف المصروف الدوري',
            type: "error",
            isLoading: false,
            autoClose: 3000
          });
        }
      } catch (error) {
        // إذا حدث خطأ في الطلب نفسه
        console.error('خطأ في حذف المصروف الدوري:', error);
        toast.update(toastId, {
          render: 'فشل في حذف المصروف الدوري',
          type: "error",
          isLoading: false,
          autoClose: 3000
        });
      }
    } catch (error) {
      // إذا حدث خطأ في معالجة toast
      console.error('خطأ في حذف المصروف الدوري:', error);
      toast.error('فشل في حذف المصروف الدوري');
    }
  };

  // تغيير حالة المصروف الدوري (نشط/غير نشط)
  const handleToggleStatus = async (expense: RecurringExpense) => {
    try {
      // إظهار رسالة نجاح مبكرة لتحسين تجربة المستخدم
      const toastId = toast.loading(`جاري ${expense.isActive ? 'تعطيل' : 'تفعيل'} المصروف الدوري...`);

      try {
        console.log(`Sending request to /api/expenses/recurring/${expense.id}`);
        console.log(`Request body:`, { isActive: !expense.isActive });

        const response = await fetch(`/api/expenses/recurring/${expense.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            isActive: !expense.isActive,
          }),
        });

        console.log(`Response status:`, response.status);
        const responseText = await response.text();
        console.log(`Response text:`, responseText);

        let responseData;
        try {
          responseData = JSON.parse(responseText);
          console.log(`Parsed response data:`, responseData);
        } catch (e) {
          console.error(`Failed to parse response as JSON:`, e);
        }

        // تحديث الرسالة بناءً على نتيجة الطلب
        if (response.ok) {
          toast.update(toastId, {
            render: `تم ${expense.isActive ? 'تعطيل' : 'تفعيل'} المصروف الدوري بنجاح`,
            type: "success",
            isLoading: false,
            autoClose: 3000
          });
          fetchRecurringExpenses();
        } else {
          toast.update(toastId, {
            render: responseData?.error || 'فشل في تغيير حالة المصروف الدوري',
            type: "error",
            isLoading: false,
            autoClose: 3000
          });
        }
      } catch (error) {
        // إذا حدث خطأ في الطلب نفسه
        console.error('خطأ في تغيير حالة المصروف الدوري:', error);
        toast.update(toastId, {
          render: 'فشل في تغيير حالة المصروف الدوري',
          type: "error",
          isLoading: false,
          autoClose: 3000
        });
      }
    } catch (error) {
      // إذا حدث خطأ في معالجة toast
      console.error('خطأ في تغيير حالة المصروف الدوري:', error);
      toast.error('فشل في تغيير حالة المصروف الدوري');
    }
  };

  // توليد المصروفات الدورية المستحقة
  const handleGenerateRecurringExpenses = async () => {
    try {
      setGenerating(true);

      // إظهار رسالة تحميل
      const toastId = toast.loading('جاري توليد المصروفات الدورية المستحقة...');

      try {
        console.log(`Sending POST request to /api/expenses/recurring/generate`);

        const response = await fetch('/api/expenses/recurring/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({}), // إرسال كائن فارغ لتجنب مشكلة تحليل JSON
        });

        console.log(`Response status:`, response.status);
        const responseText = await response.text();
        console.log(`Response text:`, responseText);

        let responseData;
        try {
          responseData = JSON.parse(responseText);
          console.log(`Parsed response data:`, responseData);
        } catch (e) {
          console.error(`Failed to parse response as JSON:`, e);
        }

        if (response.ok) {
          toast.update(toastId, {
            render: responseData?.message || 'تم توليد المصروفات الدورية بنجاح',
            type: "success",
            isLoading: false,
            autoClose: 3000
          });
          fetchRecurringExpenses();
        } else {
          toast.update(toastId, {
            render: responseData?.error || 'فشل في توليد المصروفات الدورية',
            type: "error",
            isLoading: false,
            autoClose: 3000
          });
        }
      } catch (error) {
        // إذا حدث خطأ في الطلب نفسه
        console.error('خطأ في توليد المصروفات الدورية:', error);
        toast.update(toastId, {
          render: 'فشل في توليد المصروفات الدورية',
          type: "error",
          isLoading: false,
          autoClose: 3000
        });
      }
    } catch (error) {
      // إذا حدث خطأ في معالجة toast
      console.error('خطأ في توليد المصروفات الدورية:', error);
      toast.error('فشل في توليد المصروفات الدورية');
    } finally {
      setGenerating(false);
    }
  };

  // إعادة تعيين نموذج الإضافة
  const resetForm = () => {
    setFormData({
      purpose: '',
      amount: '',
      categoryId: '',
      startDate: new Date().toISOString().split('T')[0],
      endDate: '',
      frequency: 'monthly',
      interval: '1',
      notes: '',
    });
  };

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchCategories();
    fetchRecurringExpenses();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [statusFilter, categoryFilter]);

  // ترجمة تكرار المصروف
  const translateFrequency = (frequency: string, interval: number): string => {
    switch (frequency) {
      case 'daily':
        return interval === 1 ? 'يومي' : `كل ${interval} أيام`;
      case 'weekly':
        return interval === 1 ? 'أسبوعي' : `كل ${interval} أسابيع`;
      case 'monthly':
        return interval === 1 ? 'شهري' : `كل ${interval} أشهر`;
      case 'yearly':
        return interval === 1 ? 'سنوي' : `كل ${interval} سنوات`;
      default:
        return '';
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.expenses.recurring.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-4 md:space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center gap-2 md:gap-4">
          <Link href="/admin/expenses">
            <Button variant="outline" className="h-8 w-8 md:h-10 md:w-10 p-0">
              <FaArrowLeft className="text-xs md:text-base" />
            </Button>
          </Link>
          <h1 className="text-xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaSync className="text-[var(--primary-color)]" />
            المصروفات الدورية
          </h1>
        </div>
        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <Button
            onClick={handleGenerateRecurringExpenses}
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1 text-xs md:text-sm"
            disabled={generating}
          >
            {generating ? (
              <>
                <FaSync className="animate-spin" size={12} />
                <span>جاري التوليد...</span>
              </>
            ) : (
              <>
                <FaRedo size={12} />
                <span>توليد المصروفات المستحقة</span>
              </>
            )}
          </Button>
          <Button
            onClick={() => {
              resetForm();
              setIsAddDialogOpen(true);
            }}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1 text-xs md:text-sm"
          >
            <FaPlus size={12} />
            <span>إضافة مصروف دوري</span>
          </Button>
        </div>
      </div>

      {/* أزرار التنقل بين صفحات المصروفات */}
      <div className="grid grid-cols-2 gap-3 mb-4 md:mb-6">
        <Link href="/admin/expenses" className="w-full">
          <Button
            className="w-full bg-white border border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center justify-center gap-2"
            variant="outline"
          >
            <FaMoneyBillWave />
            <span>المصروفات العادية</span>
          </Button>
        </Link>
        <Link href="/admin/expenses/reminders" className="w-full">
          <Button
            className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center justify-center gap-2"
            variant="default"
          >
            <FaSync />
            <span>تذكيرات المصروفات</span>
          </Button>
        </Link>
      </div>

      {/* مرشحات البحث */}
      <Card className="bg-white shadow-md border border-[#e0f2ef] mb-4 md:mb-6">
        <CardContent className="pt-4 md:pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-4">
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaCheck className="text-[var(--primary-color)]" />
                <span>الحالة</span>
              </label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="جميع الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="active">نشط</SelectItem>
                  <SelectItem value="inactive">غير نشط</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaTags className="text-[var(--primary-color)]" />
                <span>الفئة</span>
              </label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="جميع الفئات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                onClick={fetchRecurringExpenses}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full text-xs md:text-sm"
              >
                تحديث البيانات
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة المصروفات الدورية */}
      <Card className="bg-white shadow-md border border-[#e0f2ef]">
        <CardHeader className="pb-1 md:pb-2 pt-3 md:pt-4">
          <CardTitle className="text-base md:text-xl flex items-center gap-2">
            <FaSync className="text-[var(--primary-color)]" />
            <span>قائمة المصروفات الدورية</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4 md:py-8">
              <p className="text-gray-500 text-xs md:text-sm">جاري تحميل البيانات...</p>
            </div>
          ) : recurringExpenses.length === 0 ? (
            <div className="text-center py-4 md:py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <p className="text-gray-500 mb-3 md:mb-4 text-xs md:text-sm">لم يتم العثور على أي مصروفات دورية</p>
              <Button
                onClick={() => {
                  resetForm();
                  setIsAddDialogOpen(true);
                }}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white text-xs md:text-sm"
              >
                <FaPlus className="ml-1 md:ml-2" size={12} />
                إضافة مصروف دوري جديد
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs md:text-sm">الغرض</TableHead>
                    <TableHead className="text-xs md:text-sm">المبلغ</TableHead>
                    <TableHead className="text-xs md:text-sm">الفئة</TableHead>
                    <TableHead className="text-xs md:text-sm">التكرار</TableHead>
                    <TableHead className="text-xs md:text-sm">التوليد التالي</TableHead>
                    <TableHead className="text-xs md:text-sm">الحالة</TableHead>
                    <TableHead className="text-xs md:text-sm">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recurringExpenses.map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell className="font-medium text-xs md:text-sm">{expense.purpose}</TableCell>
                      <TableCell className="text-xs md:text-sm">{expense.amount.toLocaleString('fr-FR')} د.ج</TableCell>
                      <TableCell className="text-xs md:text-sm">
                        {expense.category ? (
                          <div className="flex items-center gap-1">
                            <div
                              className="w-2 h-2 md:w-3 md:h-3 rounded-full"
                              style={{ backgroundColor: expense.category.color || 'var(--primary-color)' }}
                            ></div>
                            <span>{expense.category.name}</span>
                          </div>
                        ) : (
                          'بدون فئة'
                        )}
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">
                        {translateFrequency(expense.frequency, expense.interval)}
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">
                        <div className="flex flex-col">
                          <span>{new Date(expense.nextGenerationDate).toLocaleDateString('fr-FR')}</span>
                          <span className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(expense.nextGenerationDate), { locale: ar, addSuffix: true })}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">
                        <Badge
                          className={`${
                            expense.isActive
                              ? 'bg-green-100 text-green-800 hover:bg-green-200'
                              : 'bg-red-100 text-red-800 hover:bg-red-200'
                          } text-xs`}
                        >
                          {expense.isActive ? 'نشط' : 'غير نشط'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 md:gap-2">
                          <Button
                            variant="ghost"
                            className={`h-6 w-6 md:h-8 md:w-8 p-0 ${expense.isActive ? 'text-red-600' : 'text-primary-color'}`}
                            onClick={() => handleToggleStatus(expense)}
                            title={expense.isActive ? 'تعطيل' : 'تفعيل'}
                          >
                            {expense.isActive ? <FaPause className="text-xs md:text-base" /> : <FaPlay className="text-xs md:text-base" />}
                          </Button>
                          <Button
                            variant="ghost"
                            className="h-6 w-6 md:h-8 md:w-8 p-0 text-blue-600"
                            onClick={() => {
                              setSelectedExpense(expense);
                              setFormData({
                                purpose: expense.purpose,
                                amount: expense.amount.toString(),
                                categoryId: expense.categoryId ? expense.categoryId.toString() : '',
                                startDate: new Date(expense.startDate).toISOString().split('T')[0],
                                endDate: expense.endDate
                                  ? new Date(expense.endDate).toISOString().split('T')[0]
                                  : '',
                                frequency: expense.frequency,
                                interval: expense.interval.toString(),
                                notes: expense.notes || '',
                              });
                              setIsEditDialogOpen(true);
                            }}
                            title="تعديل"
                          >
                            <FaEdit className="text-xs md:text-base" />
                          </Button>
                          <Button
                            variant="ghost"
                            className="h-6 w-6 md:h-8 md:w-8 p-0 text-red-600"
                            onClick={() => handleDeleteRecurringExpense(expense.id)}
                            title="حذف"
                          >
                            <FaTrash className="text-xs md:text-base" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نافذة إضافة مصروف دوري جديد */}
      <AnimatedDialog
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        title="إضافة مصروف دوري جديد"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="add-recurring-expense-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaPlus size={14} />
            <span>إضافة</span>
          </Button>
        }
      >
        <form id="add-recurring-expense-form" onSubmit={handleAddRecurringExpense} className="space-y-4 p-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="purpose">الغرض <span className="text-red-500">*</span></Label>
              <Input
                id="purpose"
                value={formData.purpose}
                onChange={(e) => setFormData({ ...formData, purpose: e.target.value })}
                placeholder="أدخل غرض المصروف الدوري"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">المبلغ <span className="text-red-500">*</span></Label>
              <Input
                id="amount"
                type="number"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                placeholder="أدخل مبلغ المصروف"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="categoryId">الفئة</Label>
              <Select value={formData.categoryId} onValueChange={(value) => setFormData({ ...formData, categoryId: value })}>
                <SelectTrigger id="categoryId">
                  <SelectValue placeholder="اختر فئة المصروف" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate">تاريخ البدء <span className="text-red-500">*</span></Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">تاريخ الانتهاء (اختياري)</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="frequency">التكرار <span className="text-red-500">*</span></Label>
                <Select value={formData.frequency} onValueChange={(value) => setFormData({ ...formData, frequency: value })}>
                  <SelectTrigger id="frequency">
                    <SelectValue placeholder="اختر نوع التكرار" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">يومي</SelectItem>
                    <SelectItem value="weekly">أسبوعي</SelectItem>
                    <SelectItem value="monthly">شهري</SelectItem>
                    <SelectItem value="yearly">سنوي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="interval">الفاصل الزمني <span className="text-red-500">*</span></Label>
                <Input
                  id="interval"
                  type="number"
                  min="1"
                  value={formData.interval}
                  onChange={(e) => setFormData({ ...formData, interval: e.target.value })}
                  placeholder="كل كم وحدة زمنية"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">ملاحظات</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="أدخل أي ملاحظات إضافية"
                rows={3}
              />
            </div>
          </div>
        </form>
      </AnimatedDialog>

      {/* نافذة تعديل مصروف دوري */}
      <AnimatedDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        title="تعديل مصروف دوري"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="edit-recurring-expense-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaSave size={14} />
            <span>حفظ التغييرات</span>
          </Button>
        }
      >
        <form id="edit-recurring-expense-form" onSubmit={handleEditRecurringExpense} className="space-y-4 p-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-purpose">الغرض <span className="text-red-500">*</span></Label>
              <Input
                id="edit-purpose"
                value={formData.purpose}
                onChange={(e) => setFormData({ ...formData, purpose: e.target.value })}
                placeholder="أدخل غرض المصروف الدوري"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-amount">المبلغ <span className="text-red-500">*</span></Label>
              <Input
                id="edit-amount"
                type="number"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                placeholder="أدخل مبلغ المصروف"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-categoryId">الفئة</Label>
              <Select value={formData.categoryId} onValueChange={(value) => setFormData({ ...formData, categoryId: value })}>
                <SelectTrigger id="edit-categoryId">
                  <SelectValue placeholder="اختر فئة المصروف" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-startDate">تاريخ البدء <span className="text-red-500">*</span></Label>
              <Input
                id="edit-startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-endDate">تاريخ الانتهاء (اختياري)</Label>
              <Input
                id="edit-endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-frequency">التكرار <span className="text-red-500">*</span></Label>
                <Select value={formData.frequency} onValueChange={(value) => setFormData({ ...formData, frequency: value })}>
                  <SelectTrigger id="edit-frequency">
                    <SelectValue placeholder="اختر نوع التكرار" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">يومي</SelectItem>
                    <SelectItem value="weekly">أسبوعي</SelectItem>
                    <SelectItem value="monthly">شهري</SelectItem>
                    <SelectItem value="yearly">سنوي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-interval">الفاصل الزمني <span className="text-red-500">*</span></Label>
                <Input
                  id="edit-interval"
                  type="number"
                  min="1"
                  value={formData.interval}
                  onChange={(e) => setFormData({ ...formData, interval: e.target.value })}
                  placeholder="كل كم وحدة زمنية"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-notes">ملاحظات</Label>
              <Textarea
                id="edit-notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="أدخل أي ملاحظات إضافية"
                rows={3}
              />
            </div>
          </div>
        </form>
      </AnimatedDialog>
      </div>
    </ProtectedRoute>
  );
}

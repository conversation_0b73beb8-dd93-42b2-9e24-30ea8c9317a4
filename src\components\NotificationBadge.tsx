'use client';
import React, { useState, useEffect } from 'react';
import { FaBell } from 'react-icons/fa';
import Link from 'next/link';
import axios from 'axios';

const NotificationBadge = () => {
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // جلب عدد الإشعارات غير المقروءة
  useEffect(() => {
    const fetchUnreadCount = async () => {
      try {
        const response = await axios.get('/api/notifications?unread=true&limit=1');
        setUnreadCount((response.data as { unreadCount: number }).unreadCount);
      } catch (error) {
        console.error('Error fetching unread notifications:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUnreadCount();

    // تحديث عدد الإشعارات كل دقيقة
    const interval = setInterval(fetchUnreadCount, 60000);
    return () => clearInterval(interval);
  }, []);

  return (
    <Link href="/notifications" className="relative">
      <FaBell className="text-xl text-gray-600 hover:text-[var(--primary-color)]" />
      {!isLoading && unreadCount > 0 && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {unreadCount > 9 ? '9+' : unreadCount}
        </span>
      )}
    </Link>
  );
};

export default NotificationBadge;

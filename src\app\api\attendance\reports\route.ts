import { NextRequest, NextResponse } from 'next/server';
import prisma from '../../../../lib/prisma';
import { getToken } from '../../../../lib/auth';
import { UserRole, Prisma } from '@prisma/client';

// GET /api/attendance/reports
export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
      return NextResponse.json(
        { error: 'غير مصرح به، يجب أن تكون مسؤول أو معلم' },
        { status: 401 }
      );
    }

    // استخراج المعلمات من URL
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get('type') || 'daily'; // daily, weekly, monthly
    const classeId = searchParams.get('classeId');
    const studentId = searchParams.get('studentId');
    const dateParam = searchParams.get('date');

    // تحديد نطاق التاريخ بناءً على نوع التقرير
    const today = new Date();
    let startDate: Date;
    const endDate = new Date(today);
    endDate.setHours(23, 59, 59, 999);

    switch (reportType) {
      case 'daily':
        // اليوم الحالي أو التاريخ المحدد
        startDate = dateParam ? new Date(dateParam) : new Date(today);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'weekly':
        // الأسبوع الحالي (من الأحد إلى السبت)
        startDate = new Date(today);
        startDate.setDate(today.getDate() - today.getDay()); // الأحد
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'monthly':
        // الشهر الحالي
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        startDate.setHours(0, 0, 0, 0);
        break;
      default:
        startDate = new Date(today);
        startDate.setHours(0, 0, 0, 0);
    }

    // بناء شروط البحث
    const whereCondition: Prisma.AttendanceWhereInput = {
      date: {
        gte: startDate,
        lte: endDate
      },
      status: {
        in: ['ABSENT', 'EXCUSED']
      }
    };

    // إضافة شرط القسم إذا تم تحديده
    if (classeId) {
      whereCondition.student = {
        classeId: parseInt(classeId)
      };
    }

    // إضافة شرط الطالب إذا تم تحديده
    if (studentId) {
      whereCondition.studentId = parseInt(studentId);
    }

    // جلب سجلات الغياب
    const absenceRecords = await prisma.attendance.findMany({
      where: whereCondition,
      include: {
        student: {
          include: {
            classe: true,
            guardian: true
          }
        }
      },
      orderBy: [
        {
          student: {
            name: 'asc'
          }
        },
        {
          date: 'desc'
        }
      ]
    });

    // تجميع البيانات حسب الطالب
    const studentAbsences = new Map();

    absenceRecords.forEach(record => {
      const studentId = record.studentId;

      if (!studentAbsences.has(studentId)) {
        studentAbsences.set(studentId, {
          student: {
            id: record.student.id,
            name: record.student.name,
            classe: record.student.classe ? {
              id: record.student.classe.id,
              name: record.student.classe.name
            } : null,
            guardian: record.student.guardian ? {
              id: record.student.guardian.id,
              name: record.student.guardian.name,
              phone: record.student.guardian.phone
            } : null
          },
          absences: [],
          totalAbsences: 0,
          excusedAbsences: 0
        });
      }

      const studentData = studentAbsences.get(studentId);
      studentData.absences.push({
        id: record.id,
        date: record.date,
        status: record.status,
        hisass: record.hisass
      });

      studentData.totalAbsences++;
      if (record.status === 'EXCUSED') {
        studentData.excusedAbsences++;
      }
    });

    // تحويل البيانات إلى مصفوفة
    const result = Array.from(studentAbsences.values());

    // تجميع إحصائيات إضافية
    const totalStudents = await prisma.student.count({
      where: classeId ? { classeId: parseInt(classeId) } : undefined
    });

    const totalAbsences = absenceRecords.length;
    const excusedAbsences = absenceRecords.filter(record => record.status === 'EXCUSED').length;
    const unexcusedAbsences = totalAbsences - excusedAbsences;

    // تجميع البيانات حسب القسم
    const classeAbsences = new Map();

    absenceRecords.forEach(record => {
      if (!record.student.classe) return;

      const classeId = record.student.classe.id;

      if (!classeAbsences.has(classeId)) {
        classeAbsences.set(classeId, {
          id: classeId,
          name: record.student.classe.name,
          totalAbsences: 0,
          excusedAbsences: 0,
          students: new Set()
        });
      }

      const classeData = classeAbsences.get(classeId);
      classeData.totalAbsences++;
      if (record.status === 'EXCUSED') {
        classeData.excusedAbsences++;
      }
      classeData.students.add(record.studentId);
    });

    // تحويل بيانات الأقسام إلى مصفوفة وحساب عدد الطلاب
    const classeStats = Array.from(classeAbsences.values()).map(classe => ({
      id: classe.id,
      name: classe.name,
      totalAbsences: classe.totalAbsences,
      excusedAbsences: classe.excusedAbsences,
      unexcusedAbsences: classe.totalAbsences - classe.excusedAbsences,
      studentsCount: classe.students.size
    }));

    // إعداد البيانات النهائية
    const reportData = {
      reportType,
      period: {
        startDate,
        endDate
      },
      summary: {
        totalStudents,
        totalAbsences,
        excusedAbsences,
        unexcusedAbsences,
        studentsWithAbsences: result.length
      },
      classeStats,
      studentAbsences: result
    };

    return NextResponse.json(reportData);
  } catch (error) {
    console.error('Error generating attendance report:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء تقرير الغياب' },
      { status: 500 }
    );
  }
}

// POST /api/attendance/reports - إرسال تقرير الغياب للمعلمين والإداريين
export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'غير مصرح به، يجب أن تكون مسؤول' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { reportType, classeId, date, sendToTeachers, sendToAdmins } = body;

    if (!reportType) {
      return NextResponse.json(
        { error: 'يجب تحديد نوع التقرير', success: false },
        { status: 400 }
      );
    }

    // بناء URL للتقرير
    const params = new URLSearchParams();
    params.append('type', reportType);
    if (classeId) params.append('classeId', classeId.toString());
    if (date) params.append('date', date);

    // جلب بيانات التقرير
    const reportResponse = await fetch(`${request.nextUrl.origin}/api/attendance/reports?${params.toString()}`, {
      headers: {
        Cookie: `jwtToken=${token}`
      }
    });

    if (!reportResponse.ok) {
      throw new Error('فشل في جلب بيانات التقرير');
    }

    const reportData = await reportResponse.json();

    // تحديد عنوان ومحتوى الإشعار
    let reportTitle = '';
    switch (reportType) {
      case 'daily':
        reportTitle = `تقرير الغياب اليومي - ${new Date(date || new Date()).toLocaleDateString('fr-FR')}`;
        break;
      case 'weekly':
        reportTitle = 'تقرير الغياب الأسبوعي';
        break;
      case 'monthly':
        reportTitle = 'تقرير الغياب الشهري';
        break;
      default:
        reportTitle = 'تقرير الغياب';
    }

    // إعداد محتوى الإشعار
    const className = classeId
      ? (await prisma.classe.findUnique({ where: { id: parseInt(classeId) } }))?.name || 'غير محدد'
      : 'جميع الأقسام';

    const reportContent = `
      تقرير الغياب ${reportType === 'daily' ? 'اليومي' : reportType === 'weekly' ? 'الأسبوعي' : 'الشهري'} للقسم: ${className}

      ملخص التقرير:
      - إجمالي الطلاب: ${reportData.summary.totalStudents}
      - إجمالي حالات الغياب: ${reportData.summary.totalAbsences}
      - حالات الغياب بعذر: ${reportData.summary.excusedAbsences}
      - حالات الغياب بدون عذر: ${reportData.summary.unexcusedAbsences}
      - عدد الطلاب المتغيبين: ${reportData.summary.studentsWithAbsences}

      يمكنك الاطلاع على التفاصيل الكاملة من خلال صفحة تقارير الحضور والغياب.
    `;

    // إرسال الإشعارات للمعلمين إذا تم تحديد ذلك
    if (sendToTeachers) {
      // إذا تم تحديد فصل، نبحث عن المعلمين الذين يدرسون في هذا الفصل
      let teachers = [];

      if (classeId) {
        // نجلب أولاً ClassSubject للفصل المحدد
        const classSubjects = await prisma.classSubject.findMany({
          where: {
            classeId: parseInt(classeId)
          },
          include: {
            teacherSubject: {
              include: {
                teacher: {
                  include: {
                    user: true
                  }
                }
              }
            }
          }
        });

        // نستخرج معرفات المستخدمين للمعلمين
        const teacherUserIds = classSubjects
          .map(cs => cs.teacherSubject.teacher.user.id)
          .filter(id => id !== undefined);

        // نجلب المستخدمين بناءً على المعرفات
        teachers = await prisma.user.findMany({
          where: {
            id: {
              in: teacherUserIds
            }
          }
        });
      } else {
        // إذا لم يتم تحديد فصل، نجلب جميع المعلمين
        teachers = await prisma.user.findMany({
          where: {
            role: UserRole.TEACHER
          }
        });
      }

      for (const teacher of teachers) {
        await prisma.notification.create({
          data: {
            title: reportTitle,
            content: reportContent,
            type: 'ATTENDANCE',
            userId: teacher.id,
            link: '/admin/attendance/reports'
          }
        });
      }
    }

    // إرسال الإشعارات للإداريين إذا تم تحديد ذلك
    if (sendToAdmins) {
      const admins = await prisma.user.findMany({
        where: {
          role: UserRole.ADMIN
        }
      });

      for (const admin of admins) {
        await prisma.notification.create({
          data: {
            title: reportTitle,
            content: reportContent,
            type: 'ATTENDANCE',
            userId: admin.id,
            link: '/admin/attendance/reports'
          }
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم إرسال تقرير الغياب بنجاح',
      recipientsCount: {
        teachers: sendToTeachers ? await prisma.user.count({ where: { role: UserRole.TEACHER } }) : 0,
        admins: sendToAdmins ? await prisma.user.count({ where: { role: UserRole.ADMIN } }) : 0
      }
    });
  } catch (error) {
    console.error('Error sending attendance report:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إرسال تقرير الغياب', success: false },
      { status: 500 }
    );
  }
}

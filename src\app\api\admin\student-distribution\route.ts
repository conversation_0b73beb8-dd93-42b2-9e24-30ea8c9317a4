import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentIds, targetClassId } = body;

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return NextResponse.json(
        { message: "يجب توفير معرفات الطلاب بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    if (!targetClassId || typeof targetClassId !== 'number') {
      return NextResponse.json(
        { message: "يجب توفير معرف الفصل المستهدف بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود الفصل المستهدف
    const targetClass = await prisma.classe.findUnique({
      where: { id: targetClassId },
      include: {
        students: true
      }
    });

    if (!targetClass) {
      return NextResponse.json(
        { message: "الفصل المستهدف غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من السعة الاستيعابية للفصل
    const currentStudentsCount = targetClass.students.length;
    const newStudentsCount = studentIds.length;
    const totalStudentsCount = currentStudentsCount + newStudentsCount;

    if (totalStudentsCount > targetClass.capacity) {
      return NextResponse.json(
        { 
          message: `لا يمكن إضافة ${newStudentsCount} طالب للفصل. السعة المتبقية ${targetClass.capacity - currentStudentsCount} طالب فقط.`,
          currentCapacity: currentStudentsCount,
          maxCapacity: targetClass.capacity,
          remainingCapacity: targetClass.capacity - currentStudentsCount
        },
        { status: 400 }
      );
    }

    // تحديث الفصل للطلاب
    const updatePromises = studentIds.map(studentId => 
      prisma.student.update({
        where: { id: studentId },
        data: { classeId: targetClassId }
      })
    );

    await Promise.all(updatePromises);

    return NextResponse.json({
      message: `تم نقل ${studentIds.length} طالب إلى الفصل بنجاح`,
      success: true
    });
  } catch (error) {
    console.error("Error distributing students:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء توزيع الطلاب" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const classeId = searchParams.get("classeId");

    if (!classeId) {
      return NextResponse.json(
        { message: "يجب توفير معرف الفصل" },
        { status: 400 }
      );
    }

    const classe = await prisma.classe.findUnique({
      where: { id: parseInt(classeId) },
      include: {
        students: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!classe) {
      return NextResponse.json(
        { message: "الفصل غير موجود" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      students: classe.students,
      capacity: classe.capacity,
      occupancyRate: (classe.students.length / classe.capacity) * 100
    });
  } catch (error) {
    console.error("Error fetching class students:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب بيانات الطلاب" },
      { status: 500 }
    );
  }
}

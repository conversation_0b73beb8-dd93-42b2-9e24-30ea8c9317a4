'use client';

import React from 'react';
import { FaTimes, FaUser, FaEnvelope, FaPhone, FaCalendarAlt, FaUserTag, FaGraduationCap, FaBriefcase, FaHome, FaUsers } from 'react-icons/fa';

interface User {
  id: string;
  originalId: number;
  name: string;
  username: string;
  email?: string | null;
  role: string;
  roleId?: number | null;
  phone?: string | null;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  lastLogin?: string | null;
  userType: 'registered' | 'student' | 'parent';
  additionalInfo?: any;
  teacher?: any;
  employee?: any;
  profile?: {
    name: string;
    phone?: string;
  } | null;
  userRole?: {
    displayName: string;
    name: string;
  } | null;
}

interface UserDetailsModalProps {
  user: User;
  isOpen: boolean;
  onClose: () => void;
}

const UserDetailsModal: React.FC<UserDetailsModalProps> = ({ user, isOpen, onClose }) => {
  if (!isOpen) return null;

  const getRoleDisplayName = (role: string) => {
    const roleNames: Record<string, string> = {
      'ADMIN': 'مدير',
      'TEACHER': 'معلم',
      'STUDENT': 'طالب',
      'PARENT': 'ولي أمر',
      'EMPLOYEE': 'موظف',
      'PENDING': 'في الانتظار'
    };
    return roleNames[role] || role;
  };

  const getUserTypeDisplayName = (userType: string) => {
    const typeNames: Record<string, string> = {
      'registered': 'مستخدم مسجل',
      'student': 'طالب',
      'parent': 'ولي أمر'
    };
    return typeNames[userType] || userType;
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'غير محدد';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
            <FaUser className="text-[var(--primary-color)]" />
            تفاصيل المستخدم
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
          >
            <FaTimes className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaUser className="text-[var(--primary-color)]" />
              المعلومات الأساسية
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الاسم</label>
                <p className="text-gray-900 bg-white p-2 rounded border">{user.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">اسم المستخدم</label>
                <p className="text-gray-900 bg-white p-2 rounded border">{user.username}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                <p className="text-gray-900 bg-white p-2 rounded border flex items-center gap-2">
                  <FaEnvelope className="text-gray-400" />
                  {user.email || 'غير محدد'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
                <p className="text-gray-900 bg-white p-2 rounded border flex items-center gap-2">
                  <FaPhone className="text-gray-400" />
                  {user.phone || 'غير محدد'}
                </p>
              </div>
            </div>
          </div>

          {/* Role Information */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaUserTag className="text-blue-600" />
              معلومات الدور
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الدور</label>
                <p className="text-gray-900 bg-white p-2 rounded border">
                  {getRoleDisplayName(user.role)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">نوع المستخدم</label>
                <p className="text-gray-900 bg-white p-2 rounded border">
                  {getUserTypeDisplayName(user.userType)}
                </p>
              </div>
              {user.userRole && (
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">الدور المخصص</label>
                  <p className="text-gray-900 bg-white p-2 rounded border">
                    {user.userRole.displayName || user.userRole.name}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Additional Information */}
          {user.additionalInfo && (
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                {user.additionalInfo.type === 'teacher' && <FaGraduationCap className="text-green-600" />}
                {user.additionalInfo.type === 'employee' && <FaBriefcase className="text-green-600" />}
                {user.additionalInfo.type === 'student' && <FaGraduationCap className="text-green-600" />}
                {user.additionalInfo.type === 'parent' && <FaUsers className="text-green-600" />}
                معلومات إضافية
              </h3>
              
              {user.additionalInfo.type === 'teacher' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">التخصص</label>
                  <p className="text-gray-900 bg-white p-2 rounded border">
                    {user.additionalInfo.specialization || 'غير محدد'}
                  </p>
                </div>
              )}

              {user.additionalInfo.type === 'employee' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">المنصب</label>
                  <p className="text-gray-900 bg-white p-2 rounded border">
                    {user.additionalInfo.position || 'غير محدد'}
                  </p>
                </div>
              )}

              {user.additionalInfo.type === 'student' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">العمر</label>
                    <p className="text-gray-900 bg-white p-2 rounded border">
                      {user.additionalInfo.age || 'غير محدد'} سنة
                    </p>
                  </div>
                  {user.additionalInfo.guardian && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">ولي الأمر</label>
                      <p className="text-gray-900 bg-white p-2 rounded border">
                        {user.additionalInfo.guardian.name} - {user.additionalInfo.guardian.phone}
                      </p>
                    </div>
                  )}
                  {user.additionalInfo.classe && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الفصل</label>
                      <p className="text-gray-900 bg-white p-2 rounded border">
                        {user.additionalInfo.classe.name}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {user.additionalInfo.type === 'parent' && (
                <div className="space-y-4">
                  {user.additionalInfo.address && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                      <p className="text-gray-900 bg-white p-2 rounded border flex items-center gap-2">
                        <FaHome className="text-gray-400" />
                        {user.additionalInfo.address}
                      </p>
                    </div>
                  )}
                  {user.additionalInfo.students && user.additionalInfo.students.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">الأطفال</label>
                      <div className="bg-white p-2 rounded border space-y-2">
                        {user.additionalInfo.students.map((student: any, index: number) => (
                          <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                            <FaUser className="text-gray-400" />
                            <span>{student.name} (@{student.username})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* System Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <FaCalendarAlt className="text-gray-600" />
              معلومات النظام
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الإنشاء</label>
                <p className="text-gray-900 bg-white p-2 rounded border">
                  {formatDate(user.createdAt)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">آخر تحديث</label>
                <p className="text-gray-900 bg-white p-2 rounded border">
                  {formatDate(user.updatedAt)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {user.status === 'active' ? 'نشط' : 'غير نشط'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">المعرف</label>
                <p className="text-gray-900 bg-white p-2 rounded border font-mono text-sm">
                  {user.id}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default UserDetailsModal;

/*
==============================================================================
    مشغل تطبيقات Praetorian.ring
    
    الوصف: واجهة موحدة لتشغيل جميع تطبيقات Praetorian
    المؤلف: Praetorian Team
==============================================================================
*/

# تحميل مكتبة Praetorian
load "../praetorian.ring"

/*
==============================================================================
    دوال مساعدة
==============================================================================
*/

/*
دالة iif
*/
func iif bCondition, vTrueValue, vFalseValue
    if bCondition
        return vTrueValue
    else
        return vFalseValue
    ok

/*
طباعة البانر الرئيسي
*/
func printMainBanner
    ? ""
    ? "  ____                _             _             "
    ? " |  _ \ _ __ __ _  ___| |_ ___  _ __(_) __ _ _ __   "
    ? " | |_) | '__/ _` |/ _ \ __/ _ \| '__| |/ _` | '_ \  "
    ? " |  __/| | | (_| |  __/ || (_) | |  | | (_| | | | | "
    ? " |_|   |_|  \__,_|\___|\__\___/|_|  |_|\__,_|_| |_| "
    ? ""
    ? "        مشغل التطبيقات - Application Launcher"
    ? "=============================================="

/*
عرض القائمة الرئيسية
*/
func showMainMenu
    ? ""
    ? "التطبيقات المتاحة:"
    ? "=================="
    ? "1. ReconDash - لوحة التحكم الاستطلاعية (GUI)"
    ? "2. DirHunter - أداة تخمين المجلدات (CLI)"
    ? "3. اختبار التطبيقات"
    ? "4. عرض معلومات مكتبة Praetorian"
    ? "5. فحص المتطلبات"
    ? "0. خروج"
    ? ""
    write("اختر رقم التطبيق: ")

/*
فحص المتطلبات
*/
func checkRequirements
    ? ""
    ? "=== فحص المتطلبات ==="
    
    # فحص Ring
    ? "Ring Programming Language: ✓ متاح"
    
    # فحص مكتبة Praetorian
    try
        oPraetorian = CreatePraetorian()
        ? "Praetorian.ring Library: ✓ متاح"
        bPraetorianOK = true
    catch
        ? "Praetorian.ring Library: ✗ غير متاح"
        bPraetorianOK = false
    done
    
    # فحص libui
    try
        load "libui.ring"
        ? "libui.ring (ReconDash): ✓ متاح"
        bLibUIAvailable = true
    catch
        ? "libui.ring (ReconDash): ✗ غير متاح"
        bLibUIAvailable = false
    done
    
    # فحص rogueutil
    try
        load "rogueutil.ring"
        ? "rogueutil.ring (DirHunter): ✓ متاح"
        bRogueUtilAvailable = true
    catch
        ? "rogueutil.ring (DirHunter): ✗ غير متاح (اختياري)"
        bRogueUtilAvailable = false
    done
    
    # فحص threads
    try
        load "threads.ring"
        ? "threads.ring (Threading): ✓ متاح"
        bThreadsAvailable = true
    catch
        ? "threads.ring (Threading): ✗ غير متاح (اختياري)"
        bThreadsAvailable = false
    done
    
    ? ""
    ? "حالة التطبيقات:"
    ? "==============="
    ? "ReconDash: " + iif(bPraetorianOK and bLibUIAvailable, "جاهز للتشغيل", "غير جاهز")
    ? "DirHunter: " + iif(bPraetorianOK, "جاهز للتشغيل", "غير جاهز")
    
    if not bPraetorianOK
        ? ""
        ? "تحذير: مكتبة Praetorian.ring غير متاحة!"
        ? "تأكد من وجود الملف praetorian.ring في المجلد الصحيح."
    ok
    
    if not bLibUIAvailable
        ? ""
        ? "ملاحظة: libui.ring غير متاح - ReconDash لن يعمل"
        ? "لتثبيت libui، راجع: https://github.com/ring-lang/ring/tree/master/extensions/libui"
    ok

/*
تشغيل ReconDash
*/
func runReconDash
    ? ""
    ? "=== تشغيل ReconDash ==="
    
    # التحقق من وجود الملف
    if not fexists("ReconDash/ReconDash.ring")
        ? "خطأ: ملف ReconDash.ring غير موجود!"
        return
    ok
    
    # التحقق من libui
    try
        load "libui.ring"
    catch
        ? "خطأ: libui.ring غير متاح!"
        ? "ReconDash يحتاج libui.ring للواجهة الرسومية."
        return
    done
    
    ? "تشغيل ReconDash..."
    ? "ملاحظة: ستفتح نافذة جديدة للتطبيق"
    ? ""
    
    try
        # تغيير المجلد وتشغيل التطبيق
        system("cd ReconDash && ring ReconDash.ring")
    catch
        ? "خطأ في تشغيل ReconDash: " + cCatchError
    done

/*
تشغيل DirHunter
*/
func runDirHunter
    ? ""
    ? "=== تشغيل DirHunter ==="
    
    # التحقق من وجود الملف
    if not fexists("DirHunter/DirHunter.ring")
        ? "خطأ: ملف DirHunter.ring غير موجود!"
        return
    ok
    
    ? "DirHunter هو تطبيق سطر أوامر يتطلب معلمات."
    ? ""
    ? "الاستخدام الأساسي:"
    ? "ring DirHunter.ring -u [URL] -w [WORDLIST]"
    ? ""
    ? "أمثلة:"
    ? "ring DirHunter.ring -u http://httpbin.org -w wordlist.txt"
    ? "ring DirHunter.ring -u https://example.com -w wordlist.txt -t 10"
    ? ""
    
    write("هل تريد تشغيل مثال تجريبي؟ (y/n): ")
    cChoice = getchar()
    
    if lower(cChoice) = "y"
        ? ""
        ? "تشغيل مثال تجريبي..."
        try
            system("cd DirHunter && ring DirHunter.ring -u http://httpbin.org -w wordlist.txt -t 5")
        catch
            ? "خطأ في تشغيل DirHunter: " + cCatchError
        done
    else
        ? ""
        ? "لتشغيل DirHunter يدوياً:"
        ? "cd DirHunter"
        ? "ring DirHunter.ring -h    # لعرض المساعدة"
    ok

/*
تشغيل اختبار التطبيقات
*/
func runApplicationTests
    ? ""
    ? "=== تشغيل اختبار التطبيقات ==="
    
    if not fexists("test_applications.ring")
        ? "خطأ: ملف test_applications.ring غير موجود!"
        return
    ok
    
    try
        load "test_applications.ring"
    catch
        ? "خطأ في تشغيل الاختبارات: " + cCatchError
    done

/*
عرض معلومات مكتبة Praetorian
*/
func showPraetorianInfo
    ? ""
    ? "=== معلومات مكتبة Praetorian.ring ==="
    
    try
        oPraetorian = CreatePraetorian()
        oPraetorian.printInfo()
    catch
        ? "خطأ: لم يتم العثور على مكتبة Praetorian.ring"
        ? "تأكد من وجود الملف praetorian.ring في المجلد الصحيح."
    done

/*
==============================================================================
    الدالة الرئيسية
==============================================================================
*/

func main
    printMainBanner()
    
    while true
        showMainMenu()
        cChoice = getchar()
        
        switch cChoice
            on "1"
                runReconDash()
                
            on "2"
                runDirHunter()
                
            on "3"
                runApplicationTests()
                
            on "4"
                showPraetorianInfo()
                
            on "5"
                checkRequirements()
                
            on "0"
                ? ""
                ? "شكراً لاستخدام تطبيقات Praetorian.ring!"
                ? "وداعاً!"
                exit
                
            other
                ? ""
                ? "خيار غير صحيح. يرجى اختيار رقم من 0 إلى 5."
        off
        
        ? ""
        write("اضغط Enter للمتابعة...")
        getchar()
    end

# تشغيل المشغل
main()

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

interface AddResourceDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  lessonId: number | null;
}

export default function AddResourceDialog({ isOpen, onCloseAction, onSuccessAction, lessonId }: AddResourceDialogProps) {
  const [formData, setFormData] = useState({
    title: '',
    type: '',
    url: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddResource = async () => {
    if (!lessonId) return;
    
    if (!formData.title.trim()) {
      toast.error('الرجاء إدخال عنوان المورد');
      return;
    }

    if (!formData.type) {
      toast.error('الرجاء اختيار نوع المورد');
      return;
    }

    if (!formData.url.trim()) {
      toast.error('الرجاء إدخال رابط المورد');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/curriculum/resources', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          type: formData.type,
          url: formData.url,
          lessonId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add resource');
      }

      toast.success('تمت إضافة المورد بنجاح');
      setFormData({
        title: '',
        type: '',
        url: ''
      });
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error adding resource:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة المورد');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleAddResource}
      disabled={isLoading || !formData.title.trim() || !formData.type || !formData.url.trim() || !lessonId}
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري الإضافة...' : 'إضافة'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إضافة مورد تعليمي جديد"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
        <div className="space-y-2">
          <Label>عنوان المورد</Label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="أدخل عنوان المورد"
          />
        </div>
        
        <div className="space-y-2">
          <Label>نوع المورد</Label>
          <Select
            value={formData.type}
            onValueChange={(value) => handleSelectChange('type', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="اختر نوع المورد" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pdf">ملف PDF</SelectItem>
              <SelectItem value="doc">مستند Word</SelectItem>
              <SelectItem value="video">فيديو</SelectItem>
              <SelectItem value="link">رابط</SelectItem>
              <SelectItem value="image">صورة</SelectItem>
              <SelectItem value="audio">ملف صوتي</SelectItem>
              <SelectItem value="other">أخرى</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label>رابط المورد</Label>
          <Input
            name="url"
            value={formData.url}
            onChange={handleChange}
            placeholder="أدخل رابط المورد"
          />
          <p className="text-xs text-gray-500">يمكنك إدخال رابط مباشر للملف أو رابط لموقع خارجي</p>
        </div>
      </div>
    </AnimatedDialog>
  );
}

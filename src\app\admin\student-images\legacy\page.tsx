'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { PlusCircle, Trash2, Edit, ArrowLeft } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Textarea } from '@/components/ui/textarea';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import Image from 'next/image';
import Link from 'next/link';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

type Student = {
  id: number;
  name: string;
};

type StudentImage = {
  id: number;
  studentId: number;
  imageUrl: string;
  description: string | null;
  isProfilePic: boolean;
  uploadDate: string;
};

export default function LegacyStudentImagesPage() {
  const [students, setStudents] = useState<Student[]>([]);
  const [images, setImages] = useState<StudentImage[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [loading, setLoading] = useState({
    students: false,
    images: false,
    upload: false,
    submit: false
  });
  const [formData, setFormData] = useState({
    id: '',
    studentId: '',
    imageUrl: '',
    description: '',
    isProfilePic: false
  });
  const [isEditing, setIsEditing] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    fetchStudents();
  }, []);

  useEffect(() => {
    if (selectedStudent) {
      fetchStudentImages(selectedStudent);
    } else {
      setImages([]);
    }
  }, [selectedStudent]);

  const fetchStudents = async () => {
    setLoading(prev => ({ ...prev, students: true }));
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('فشل في جلب الطلاب');
      const data = await response.json();
      // Extract students array from the response
      const studentsArray = data.students || [];
      setStudents(studentsArray);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب الطلاب',
        variant: 'destructive',
      });
      // Ensure students is always an array even on error
      setStudents([]);
    } finally {
      setLoading(prev => ({ ...prev, students: false }));
    }
  };

  const fetchStudentImages = async (studentId: string) => {
    setLoading(prev => ({ ...prev, images: true }));
    try {
      const response = await fetch(`/api/student-images?studentId=${studentId}`);
      if (!response.ok) throw new Error('فشل في جلب صور الطالب');
      const data = await response.json();
      setImages(data.data || []);
    } catch (error) {
      console.error('Error fetching student images:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب صور الطالب',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, images: false }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isProfilePic: checked }));
  };

  const resetForm = () => {
    setFormData({
      id: '',
      studentId: selectedStudent,
      imageUrl: '',
      description: '',
      isProfilePic: false
    });
    setIsEditing(false);
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    try {
      const file = files[0];
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'student');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`فشل في رفع الملف ${file.name}`);
      }

      const result = await response.json();
      setFormData(prev => ({
        ...prev,
        imageUrl: result.data.filePath
      }));

      toast({
        title: 'نجاح',
        description: 'تم رفع الصورة بنجاح',
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في رفع الصورة',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
      // إعادة تعيين قيمة حقل الملف
      e.target.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(prev => ({ ...prev, submit: true }));

    try {
      if (!formData.studentId) {
        throw new Error('يرجى اختيار الطالب');
      }

      if (!formData.imageUrl && !isEditing) {
        throw new Error('يرجى رفع صورة');
      }

      const url = '/api/student-images';
      const method = isEditing ? 'PUT' : 'POST';
      const payload = isEditing
        ? { id: formData.id, description: formData.description, isProfilePic: formData.isProfilePic }
        : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: isEditing ? 'تم تحديث الصورة بنجاح' : 'تم إضافة الصورة بنجاح',
        });
        resetForm();
        setShowForm(false);
        fetchStudentImages(selectedStudent);
      } else {
        throw new Error(result.error || 'حدث خطأ أثناء حفظ الصورة');
      }
    } catch (error) {
      console.error('Error saving student image:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ الصورة',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, submit: false }));
    }
  };

  const handleEdit = (image: StudentImage) => {
    setFormData({
      id: image.id.toString(),
      studentId: image.studentId.toString(),
      imageUrl: image.imageUrl,
      description: image.description || '',
      isProfilePic: image.isProfilePic
    });
    setIsEditing(true);
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/student-images?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم حذف الصورة بنجاح',
        });
        fetchStudentImages(selectedStudent);
      } else {
        const result = await response.json();
        throw new Error(result.error || 'حدث خطأ أثناء حذف الصورة');
      }
    } catch (error) {
      console.error('Error deleting student image:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حذف الصورة',
        variant: 'destructive',
      });
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.student-images.legacy">
      <div className="container mx-auto py-6">
      <div className="mb-6">
        <Link href="/admin/student-images">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 ml-2" />
            العودة إلى إدارة الصور
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>صور الطلاب (النظام القديم)</CardTitle>
          <div className="flex items-center gap-2">
            <Select
              value={selectedStudent}
              onValueChange={setSelectedStudent}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="اختر الطالب" />
              </SelectTrigger>
              <SelectContent>
                {Array.isArray(students) && students.length > 0 ? (
                  students.map(student => (
                    <SelectItem key={student.id} value={student.id.toString()}>
                      {student.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-students" disabled>
                    لا يوجد طلاب
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            <Button
              onClick={() => { resetForm(); setShowForm(true); }}
              disabled={!selectedStudent}
            >
              <PlusCircle className="ml-2 h-4 w-4" />
              إضافة صورة
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {showForm && (
            <form onSubmit={handleSubmit} className="space-y-4 mb-6 p-4 border rounded-lg">
              <h3 className="text-lg font-medium">{isEditing ? 'تعديل الصورة' : 'إضافة صورة جديدة'}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {!isEditing && (
                  <div className="space-y-2">
                    <Label htmlFor="image">الصورة</Label>
                    <Input
                      id="image"
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      disabled={uploading}
                    />
                    {uploading && <span className="text-sm">جاري الرفع...</span>}
                  </div>
                )}
                {formData.imageUrl && (
                  <div className="space-y-2 md:col-span-2">
                    <Label>معاينة الصورة</Label>
                    <div className="w-full max-w-xs">
                      <Image
                        src={formData.imageUrl}
                        alt="معاينة الصورة"
                        className="max-h-48 rounded-md"
                        width={200}
                        height={150}
                      />
                    </div>
                  </div>
                )}
                <div className="space-y-2">
                  <Label htmlFor="description">وصف الصورة</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={2}
                  />
                </div>
                <div className="space-y-2 flex items-center">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Checkbox
                      id="isProfilePic"
                      checked={formData.isProfilePic}
                      onCheckedChange={handleCheckboxChange}
                    />
                    <Label htmlFor="isProfilePic">تعيين كصورة الملف الشخصي</Label>
                  </div>
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => { setShowForm(false); resetForm(); }}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={loading.submit}
                >
                  {loading.submit ? 'جاري الحفظ...' : isEditing ? 'تحديث' : 'إضافة'}
                </Button>
              </div>
            </form>
          )}

          {!selectedStudent ? (
            <div className="text-center py-8">يرجى اختيار طالب لعرض الصور</div>
          ) : loading.images ? (
            <div className="text-center py-4">جاري تحميل الصور...</div>
          ) : images.length === 0 ? (
            <div className="text-center py-4">لا توجد صور لهذا الطالب</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {images.map(image => (
                <Card key={image.id} className="overflow-hidden">
                  <div className="relative h-48">
                    <Image
                      src={image.imageUrl}
                      alt={image.description || 'صورة الطالب'}
                      className="w-full h-full object-cover"
                      width={300}
                      height={200}
                      style={{ objectFit: 'cover' }}
                    />
                    {image.isProfilePic && (
                      <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-md text-xs">
                        الصورة الشخصية
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(image.uploadDate), 'PPP', { locale: ar })}
                        </p>
                        <p className="mt-1">{image.description || 'بدون وصف'}</p>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(image)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(image.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </ProtectedRoute>
  );
}

# مكون بطاقة التلميذ - التوثيق

## 📋 نظرة عامة

مكون React لعرض بطاقة شاملة لبيانات التلميذ مع إمكانية الطباعة والتحميل.

## 🔗 المسار
`src/components/admin/students/StudentCard.tsx`

## 🎯 الغرض

- عرض بطاقة شاملة لبيانات التلميذ
- طباعة البطاقة بتصميم احترافي
- تحميل البطاقة كملف PDF (قيد التطوير)
- عرض الإحصائيات والأداء

## 📊 الخصائص (Props)

```typescript
interface StudentCardProps {
  studentId: number;          // معرف التلميذ (مطلوب)
  onPrint?: () => void;       // دالة تُستدعى عند الطباعة
  onDownload?: () => void;    // دالة تُستدعى عند التحميل
  className?: string;         // فئات CSS إضافية
}
```

## 🎨 أقسام البطاقة

### 1. رأس البطاقة
- **اسم المدرسة:** "مدرسة القرآن الكريم"
- **تاريخ الإصدار:** بصيغة DD/MM/YYYY
- **رقم التلميذ:** معرف فريد للتلميذ
- **تصميم متدرج:** من اللون الأساسي إلى درجة أغمق

### 2. المعلومات الأساسية
```typescript
// البيانات الشخصية
{
  name: string;           // الاسم الكامل
  age: number;            // العمر
  phone?: string;         // رقم الهاتف
  username: string;       // اسم المستخدم
  createdAt: string;      // تاريخ التسجيل
}

// الصورة الشخصية
{
  imageUrl: string;       // رابط الصورة
  description?: string;   // وصف الصورة
  uploadDate: string;     // تاريخ الرفع
}

// الفصل
{
  name: string;           // اسم الفصل
  description?: string;   // وصف الفصل
}
```

### 3. معلومات ولي الأمر
```typescript
{
  name: string;           // اسم ولي الأمر
  phone: string;          // رقم الهاتف
  email?: string;         // البريد الإلكتروني
  address?: string;       // العنوان
}
```

### 4. معلومات بداية الحفظ
```typescript
{
  startDate: string;      // تاريخ بداية الحفظ (DD/MM/YYYY)
  startingJuz: number;    // الجزء المبدئي (1-30)
  startingSurah?: number; // السورة المبدئية
  startingVerse?: number; // الآية المبدئية
  level: string;          // المستوى (مبتدئ، متوسط، متقدم)
  notes?: string;         // ملاحظات إضافية
}
```

### 5. الإحصائيات والأداء
```typescript
{
  attendance: {
    present: number;      // أيام الحضور
    absent: number;       // أيام الغياب
    excused: number;      // أيام الغياب بعذر
    total: number;        // إجمالي الأيام
    rate: number;         // معدل الحضور (%)
  };
  quran: {
    totalVerses: number;        // إجمالي الآيات المحفوظة
    averageMemorization: number; // متوسط درجة الحفظ
    averageTajweed: number;     // متوسط درجة التجويد
    completedSurahs: number;    // عدد السور المكتملة
  };
  totalPoints: number;    // إجمالي النقاط
  totalRewards: number;   // عدد المكافآت
}
```

## 🎨 التصميم والواجهة

### الألوان والأيقونات
- **اللون الأساسي:** `var(--primary-color)`
- **التدرج:** من الأساسي إلى `#0d7e6d`
- **أيقونات متنوعة:** لكل قسم أيقونة مناسبة
- **ألوان الإحصائيات:** أزرق، أخضر، بنفسجي، أصفر

### التخطيط
- **الشبكة:** Grid responsive للمعلومات
- **البطاقات الفرعية:** لكل قسم تصميم منفصل
- **المحاذاة:** RTL للنصوص العربية
- **المسافات:** متسقة ومريحة للعين

### الاستجابة
- **الشاشات الكبيرة:** عرض كامل مع 4 أعمدة للإحصائيات
- **الشاشات المتوسطة:** 3 أعمدة مع تكيف تلقائي
- **الشاشات الصغيرة:** عمودين أو عمود واحد

## 🖨️ ميزة الطباعة

### التحضير للطباعة
```typescript
const handlePrint = () => {
  if (cardRef.current) {
    const printContent = cardRef.current.innerHTML;
    const originalContent = document.body.innerHTML;
    
    // إعداد محتوى الطباعة
    document.body.innerHTML = `
      <div style="direction: rtl; font-family: Arial, sans-serif;">
        <style>
          @media print {
            body { margin: 0; padding: 20px; }
            .no-print { display: none !important; }
            .print-only { display: block !important; }
          }
        </style>
        ${printContent}
      </div>
    `;
    
    window.print();
    document.body.innerHTML = originalContent;
    window.location.reload();
  }
};
```

### تحسينات الطباعة
- **إخفاء العناصر:** `.no-print` للأزرار والتفاعلات
- **تعديل الألوان:** تحويل الخلفيات الملونة لرمادي
- **الحدود:** إضافة حدود للعناصر المهمة
- **الخط:** Arial للوضوح في الطباعة

## 📱 حالات التحميل والأخطاء

### حالة التحميل
```tsx
if (isLoading) {
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          <span className="mr-3">جاري تحميل بيانات التلميذ...</span>
        </div>
      </CardContent>
    </Card>
  );
}
```

### حالة الخطأ
```tsx
if (error || !studentData) {
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-8">
        <div className="text-center text-red-600">
          <p>{error || 'لم يتم العثور على بيانات التلميذ'}</p>
        </div>
      </CardContent>
    </Card>
  );
}
```

## 🔧 التكامل مع API

### جلب بيانات البطاقة
```typescript
const fetchStudentCard = async () => {
  const response = await fetch(`/api/students/${studentId}/card`);
  const data = await response.json();
  
  if (data.success) {
    setStudentData(data.data);
  } else {
    throw new Error(data.error);
  }
};
```

### معالجة الأخطاء
- **404:** التلميذ غير موجود
- **401:** غير مصرح بالوصول
- **500:** خطأ في الخادم
- **Network:** مشاكل الاتصال

## 📊 عرض الإحصائيات

### بطاقات الإحصائيات
```tsx
<div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
  <div className="text-center p-3 bg-blue-50 rounded-lg">
    <div className="text-2xl font-bold text-blue-600">
      {studentData.stats.attendance.rate}%
    </div>
    <div className="text-sm text-gray-600">معدل الحضور</div>
  </div>
  // ... المزيد من البطاقات
</div>
```

### الألوان المستخدمة
- **الحضور:** أزرق (`blue-50`, `blue-600`)
- **الحفظ:** أخضر (`green-50`, `green-600`)
- **النقاط:** بنفسجي (`purple-50`, `purple-600`)
- **المكافآت:** أصفر (`yellow-50`, `yellow-600`)

## 🎨 تنسيق التواريخ

جميع التواريخ تُعرض بصيغة DD/MM/YYYY:
- **تاريخ الإصدار:** `new Date().toLocaleDateString('fr-FR')`
- **تاريخ التسجيل:** من بيانات التلميذ
- **تاريخ بداية الحفظ:** من بيانات بداية الحفظ

## 📝 مثال على الاستخدام

### الاستخدام الأساسي
```tsx
<StudentCard
  studentId={1}
  onPrint={() => {
    console.log('تم طباعة البطاقة');
  }}
  onDownload={() => {
    console.log('تم تحميل البطاقة');
  }}
  className="my-4"
/>
```

### في صفحة إدارة التلاميذ
```tsx
const [selectedStudentId, setSelectedStudentId] = useState<number | null>(null);

return (
  <div>
    {selectedStudentId && (
      <StudentCard
        studentId={selectedStudentId}
        onPrint={() => {
          // تسجيل عملية الطباعة
          logPrintAction(selectedStudentId);
        }}
        onDownload={() => {
          // تسجيل عملية التحميل
          logDownloadAction(selectedStudentId);
        }}
      />
    )}
  </div>
);
```

## 🔍 الاعتماديات

### مكونات UI
- `Button`, `Card`, `Badge`, `Separator` من `@/components/ui`
- `Image` من `next/image`

### المكتبات الخارجية
- `react-toastify` للإشعارات
- `react-icons/fa` للأيقونات

### APIs المطلوبة
- `/api/students/[id]/card` لجلب بيانات البطاقة

## ⚠️ ملاحظات مهمة

1. **الطباعة:** تتطلب إعادة تحميل الصفحة بعد الطباعة
2. **التحميل:** ميزة PDF قيد التطوير
3. **الصور:** تحتاج إلى معالجة خاصة للطباعة
4. **التواريخ:** جميعها بصيغة DD/MM/YYYY كما طلب المستخدم
5. **الاستجابة:** مُحسنة لجميع أحجام الشاشات

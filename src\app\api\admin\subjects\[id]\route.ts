import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/admin/subjects/[id] - Get a subject
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المادة غير صالح" },
        { status: 400 }
      );
    }

    const subject = await prisma.subject.findUnique({
      where: { id },
      include: {
        level: true,
        teacherSubjects: {
          include: {
            teacher: true
          }
        },
        units: {
          include: {
            lessons: {
              include: {
                resources: true
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    });

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    return NextResponse.json(subject);
  } catch (error) {
    console.error("Error fetching subject:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب المادة" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/subjects/[id] - Update a subject
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المادة غير صالح" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, description, levelId, hasStudyPlan } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير اسم المادة بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المادة
    const subject = await prisma.subject.findUnique({
      where: { id },
    });

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مادة أخرى بنفس الاسم
    const existingSubject = await prisma.subject.findFirst({
      where: {
        name,
        NOT: { id },
      },
    });

    if (existingSubject) {
      return NextResponse.json(
        { message: "يوجد مادة أخرى بهذا الاسم" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستوى إذا تم تحديده
    if (levelId !== null && levelId !== undefined) {
      if (levelId !== 0) { // إذا كان levelId = 0، فهذا يعني إزالة المستوى
        const level = await prisma.level.findUnique({
          where: { id: levelId },
        });

        if (!level) {
          return NextResponse.json(
            { message: "المستوى المحدد غير موجود" },
            { status: 400 }
          );
        }
      }
    }

    const updatedSubject = await prisma.subject.update({
      where: { id },
      data: {
        name,
        description: description !== undefined ? description : subject.description,
        levelId: levelId === 0 ? null : (levelId !== undefined ? levelId : subject.levelId),
        hasStudyPlan: hasStudyPlan !== undefined ? hasStudyPlan : subject.hasStudyPlan
      },
      include: {
        level: true
      }
    });

    return NextResponse.json(updatedSubject);
  } catch (error) {
    console.error("Error updating subject:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث المادة" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/subjects/[id] - Delete a subject
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المادة غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المادة
    const subject = await prisma.subject.findUnique({
      where: { id },
    });

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود علاقات مع المعلمين
    const teacherSubjects = await prisma.teacherSubject.findFirst({
      where: { subjectId: id },
    });

    if (teacherSubjects) {
      return NextResponse.json(
        { message: "لا يمكن حذف المادة لأنها مرتبطة بمعلمين" },
        { status: 400 }
      );
    }

    await prisma.subject.delete({
      where: { id },
    });

    return NextResponse.json(
      { message: "تم حذف المادة بنجاح" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting subject:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف المادة" },
      { status: 500 }
    );
  }
}

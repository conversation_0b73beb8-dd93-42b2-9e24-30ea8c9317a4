import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/payment-methods - جلب جميع طرق الدفع
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const activeOnly = searchParams.get('activeOnly') === 'true';

    const whereCondition = activeOnly ? { isActive: true } : {};

    const paymentMethods = await prisma.paymentMethod.findMany({
      where: whereCondition,
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json(paymentMethods);
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return NextResponse.json(
      { error: 'فشل في جلب طرق الدفع' },
      { status: 500 }
    );
  }
}

// POST /api/payment-methods - إنشاء طريقة دفع جديدة
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, description, icon, isActive, requiresCard } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'اسم طريقة الدفع مطلوب' },
        { status: 400 }
      );
    }

    const paymentMethod = await prisma.paymentMethod.create({
      data: {
        name,
        description,
        icon,
        isActive: isActive ?? true,
        requiresCard: requiresCard ?? false
      }
    });

    return NextResponse.json(paymentMethod);
  } catch (error) {
    console.error('Error creating payment method:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء طريقة الدفع' },
      { status: 500 }
    );
  }
}

// PATCH /api/payment-methods - تحديث طريقة دفع
export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { id, name, description, icon, isActive, requiresCard } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف طريقة الدفع مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود طريقة الدفع
    const existingPaymentMethod = await prisma.paymentMethod.findUnique({
      where: { id: parseInt(id.toString()) }
    });

    if (!existingPaymentMethod) {
      return NextResponse.json(
        { error: 'طريقة الدفع غير موجودة' },
        { status: 404 }
      );
    }

    // تحديث طريقة الدفع
    const updatedPaymentMethod = await prisma.paymentMethod.update({
      where: { id: parseInt(id.toString()) },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(icon !== undefined && { icon }),
        ...(isActive !== undefined && { isActive }),
        ...(requiresCard !== undefined && { requiresCard })
      }
    });

    return NextResponse.json(updatedPaymentMethod);
  } catch (error) {
    console.error('Error updating payment method:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث طريقة الدفع' },
      { status: 500 }
    );
  }
}

// DELETE /api/payment-methods - حذف طريقة دفع
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف طريقة الدفع مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود طريقة الدفع
    const existingPaymentMethod = await prisma.paymentMethod.findUnique({
      where: { id: parseInt(id) },
      include: {
        payments: {
          take: 1
        },
        donations: {
          take: 1
        }
      }
    });

    if (!existingPaymentMethod) {
      return NextResponse.json(
        { error: 'طريقة الدفع غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مدفوعات أو تبرعات مرتبطة بطريقة الدفع
    if (existingPaymentMethod.payments.length > 0 || existingPaymentMethod.donations.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف طريقة الدفع لأنها مرتبطة بمدفوعات أو تبرعات' },
        { status: 400 }
      );
    }

    // حذف طريقة الدفع
    await prisma.paymentMethod.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting payment method:', error);
    return NextResponse.json(
      { error: 'فشل في حذف طريقة الدفع' },
      { status: 500 }
    );
  }
}

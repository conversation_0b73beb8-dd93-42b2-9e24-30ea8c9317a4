import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// تعريف الأنواع المستخدمة في التقرير
type QueryResultItem = {
  month?: string;
  day?: string;
  total: string | number;
}

// GET /api/reports/detailed - الحصول على تقارير مالية تفصيلية
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get('startDate')
      ? new Date(searchParams.get('startDate') as string)
      : new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1); // الشهر السابق

    const endDate = searchParams.get('endDate')
      ? new Date(searchParams.get('endDate') as string)
      : new Date(); // اليوم الحالي

    // نوع التقرير: expenses, income, budget (سيتم استخدامه في المستقبل)
    // const type = searchParams.get('type') || 'expenses';
    const categoryId = searchParams.get('categoryId')
      ? parseInt(searchParams.get('categoryId') as string)
      : null;
    const comparison = searchParams.get('comparison') || 'previous-period'; // نوع المقارنة: previous-period, previous-year, budget

    // حساب فترة المقارنة
    const periodDuration = endDate.getTime() - startDate.getTime();
    const previousPeriodStartDate = new Date(startDate.getTime() - periodDuration);
    const previousPeriodEndDate = new Date(startDate.getTime() - 1);
    const previousYearStartDate = new Date(
      startDate.getFullYear() - 1,
      startDate.getMonth(),
      startDate.getDate()
    );
    const previousYearEndDate = new Date(
      endDate.getFullYear() - 1,
      endDate.getMonth(),
      endDate.getDate()
    );

    // بناء شروط البحث للفترة الحالية
    const whereCurrentPeriod: {
      date: {
        gte: Date;
        lte: Date;
      };
      categoryId?: number;
    } = {
      date: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (categoryId) {
      whereCurrentPeriod.categoryId = categoryId;
    }

    // بناء شروط البحث للفترة السابقة
    const wherePreviousPeriod: {
      date: {
        gte: Date;
        lte: Date;
      };
      categoryId?: number;
    } = {
      date: {
        gte: comparison === 'previous-year' ? previousYearStartDate : previousPeriodStartDate,
        lte: comparison === 'previous-year' ? previousYearEndDate : previousPeriodEndDate,
      },
    };

    if (categoryId) {
      wherePreviousPeriod.categoryId = categoryId;
    }

    // الحصول على إجمالي المصروفات للفترة الحالية
    const totalCurrentExpenses = await prisma.expense.aggregate({
      where: whereCurrentPeriod,
      _sum: {
        amount: true,
      },
    });

    // الحصول على إجمالي المصروفات للفترة السابقة
    const totalPreviousExpenses = await prisma.expense.aggregate({
      where: wherePreviousPeriod,
      _sum: {
        amount: true,
      },
    });

    // الحصول على المصروفات حسب الفئة للفترة الحالية
    const currentExpensesByCategory = await prisma.expense.groupBy({
      by: ['categoryId'],
      where: whereCurrentPeriod,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    });

    // الحصول على المصروفات حسب الفئة للفترة السابقة
    const previousExpensesByCategory = await prisma.expense.groupBy({
      by: ['categoryId'],
      where: wherePreviousPeriod,
      _sum: {
        amount: true,
      },
    });

    // الحصول على أسماء الفئات
    const categoryIds = [
      ...new Set([
        ...currentExpensesByCategory.map(item => item.categoryId),
        ...previousExpensesByCategory.map(item => item.categoryId),
      ]),
    ].filter(id => id !== null) as number[];

    const categories = await prisma.expenseCategory.findMany({
      where: {
        id: {
          in: categoryIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    // تنسيق المصروفات حسب الفئة للفترة الحالية
    const formattedCurrentExpensesByCategory = currentExpensesByCategory.map(item => {
      const category = categories.find(cat => cat.id === item.categoryId);
      return {
        categoryId: item.categoryId,
        categoryName: item.categoryId ? category?.name || 'غير معروف' : 'بدون فئة',
        amount: item._sum.amount || 0,
        count: item._count.id,
        percentage: totalCurrentExpenses._sum.amount
          ? ((item._sum.amount || 0) / totalCurrentExpenses._sum.amount) * 100
          : 0,
      };
    });

    // تنسيق المصروفات حسب الفئة للفترة السابقة
    const formattedPreviousExpensesByCategory = previousExpensesByCategory.map(item => {
      const category = categories.find(cat => cat.id === item.categoryId);
      return {
        categoryId: item.categoryId,
        categoryName: item.categoryId ? category?.name || 'غير معروف' : 'بدون فئة',
        amount: item._sum.amount || 0,
      };
    });

    // الحصول على المصروفات الشهرية
    const monthlyExpenses = await prisma.$queryRaw`
      SELECT
        DATE_TRUNC('month', date) as month,
        SUM(amount) as total
      FROM "Expense"
      WHERE date >= ${startDate} AND date <= ${endDate}
      ${categoryId ? `AND "categoryId" = ${categoryId}` : ''}
      GROUP BY DATE_TRUNC('month', date)
      ORDER BY month ASC
    `;

    // تنسيق المصروفات الشهرية
    const formattedMonthlyExpenses = (monthlyExpenses as QueryResultItem[]).map(item => ({
      month: item.month ? new Date(item.month).toISOString().substring(0, 7) : '',
      amount: item.total ? parseFloat(String(item.total)) : 0,
    }));

    // الحصول على المصروفات اليومية
    const dailyExpenses = await prisma.$queryRaw`
      SELECT
        DATE_TRUNC('day', date) as day,
        SUM(amount) as total
      FROM "Expense"
      WHERE date >= ${startDate} AND date <= ${endDate}
      ${categoryId ? `AND "categoryId" = ${categoryId}` : ''}
      GROUP BY DATE_TRUNC('day', date)
      ORDER BY day ASC
    `;

    // تنسيق المصروفات اليومية
    const formattedDailyExpenses = (dailyExpenses as QueryResultItem[]).map(item => ({
      date: item.day ? new Date(item.day).toISOString().split('T')[0] : '',
      amount: item.total ? parseFloat(String(item.total)) : 0,
    }));

    // الحصول على أعلى المصروفات
    const topExpenses = await prisma.expense.findMany({
      where: whereCurrentPeriod,
      orderBy: {
        amount: 'desc',
      },
      take: 10,
      include: {
        category: {
          select: {
            name: true,
          },
        },
      },
    });

    // تنسيق أعلى المصروفات
    const formattedTopExpenses = topExpenses.map(expense => ({
      id: expense.id,
      purpose: expense.purpose,
      amount: expense.amount,
      date: expense.date.toISOString().split('T')[0],
      categoryName: expense.category?.name || 'بدون فئة',
    }));

    // البحث عن ميزانية نشطة في هذه الفترة
    const budget = await prisma.budget.findFirst({
      where: {
        startDate: { lte: endDate },
        endDate: { gte: startDate },
        status: 'ACTIVE',
      },
      include: {
        items: {
          include: {
            category: true,
          },
        },
      },
    });

    // تحليل الميزانية إذا وجدت
    let budgetAnalysis = null;
    if (budget) {
      // حساب المصروفات الفعلية لكل بند من بنود الميزانية
      const budgetItemExpenses = await Promise.all(
        budget.items.map(async item => {
          const expenses = await prisma.expense.aggregate({
            where: {
              date: {
                gte: budget.startDate,
                lte: budget.endDate,
              },
              categoryId: item.categoryId,
            },
            _sum: {
              amount: true,
            },
          });

          return {
            categoryId: item.categoryId,
            categoryName: item.category.name,
            budgetAmount: item.amount,
            actualAmount: expenses._sum.amount || 0,
            remainingAmount: item.amount - (expenses._sum.amount || 0),
            usagePercentage: item.amount > 0 ? ((expenses._sum.amount || 0) / item.amount) * 100 : (expenses._sum.amount || 0) > 0 ? 100 : 0,
          };
        })
      );

      // حساب إجمالي المصروفات الفعلية للميزانية
      const totalActualExpenses = budgetItemExpenses.reduce(
        (sum, item) => sum + item.actualAmount,
        0
      );

      budgetAnalysis = {
        id: budget.id,
        name: budget.name,
        totalAmount: budget.totalAmount,
        usedAmount: totalActualExpenses,
        remainingAmount: budget.totalAmount - totalActualExpenses,
        usagePercentage: budget.totalAmount > 0 ? (totalActualExpenses / budget.totalAmount) * 100 : totalActualExpenses > 0 ? 100 : 0,
        items: budgetItemExpenses,
      };
    }

    // إعداد مقارنة المصروفات بين الفترتين
    const comparisonByCategory = formattedCurrentExpensesByCategory.map(currentCat => {
      const previousCat = formattedPreviousExpensesByCategory.find(
        prevCat => prevCat.categoryId === currentCat.categoryId
      );

      return {
        categoryId: currentCat.categoryId,
        categoryName: currentCat.categoryName,
        currentAmount: currentCat.amount,
        previousAmount: previousCat?.amount || 0,
        difference: currentCat.amount - (previousCat?.amount || 0),
        percentageChange: previousCat?.amount && previousCat.amount !== 0
          ? ((currentCat.amount - previousCat.amount) / previousCat.amount) * 100
          : 100,
      };
    });

    // إضافة الفئات التي كانت موجودة في الفترة السابقة فقط
    formattedPreviousExpensesByCategory.forEach(previousCat => {
      const exists = formattedCurrentExpensesByCategory.some(
        currentCat => currentCat.categoryId === previousCat.categoryId
      );

      if (!exists) {
        comparisonByCategory.push({
          categoryId: previousCat.categoryId,
          categoryName: previousCat.categoryName,
          currentAmount: 0,
          previousAmount: previousCat.amount,
          difference: -previousCat.amount,
          percentageChange: -100,
        });
      }
    });

    return NextResponse.json({
      expenses: {
        total: totalCurrentExpenses._sum.amount || 0,
        byCategory: formattedCurrentExpensesByCategory,
        monthly: formattedMonthlyExpenses,
        daily: formattedDailyExpenses,
        topExpenses: formattedTopExpenses,
      },
      budget: budgetAnalysis,
      comparisons: {
        previousPeriod: {
          total: totalPreviousExpenses._sum.amount || 0,
          difference: (totalCurrentExpenses._sum.amount || 0) - (totalPreviousExpenses._sum.amount || 0),
          percentageChange: totalPreviousExpenses._sum.amount && totalPreviousExpenses._sum.amount !== 0
            ? ((totalCurrentExpenses._sum.amount || 0) - (totalPreviousExpenses._sum.amount || 0)) /
              totalPreviousExpenses._sum.amount * 100
            : 100,
        },
        byCategory: comparisonByCategory,
      },
      period: {
        current: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
        previous: {
          startDate:
            comparison === 'previous-year'
              ? previousYearStartDate.toISOString()
              : previousPeriodStartDate.toISOString(),
          endDate:
            comparison === 'previous-year'
              ? previousYearEndDate.toISOString()
              : previousPeriodEndDate.toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('خطأ في جلب التقارير المالية التفصيلية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب التقارير المالية التفصيلية' },
      { status: 500 }
    );
  }
}

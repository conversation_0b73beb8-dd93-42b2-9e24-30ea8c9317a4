import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    // استخراج معرف المعلم من المسار
    const { id } = context.params;


    if (!id) {
      return NextResponse.json(
        { error: 'معرف المعلم غير موجود', success: false },
        { status: 400 }
      );
    }

    // استخدام المعرف من المسار
    const teacherId = parseInt(id);

    if (isNaN(teacherId)) {
      return NextResponse.json(
        { error: 'معرف المعلم غير صالح', success: false },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const includeClasses = searchParams.get('includeClasses') === 'true';

    // جلب المواد التي يدرسها المعلم
    const teacherSubjects = await prisma.teacherSubject.findMany({
      where: {
        teacherId: teacherId
      },
      include: {
        subject: true,
        classes: includeClasses ? {
          include: {
            classe: true
          }
        } : false
      }
    });

    // إذا كان المستخدم يريد معلومات الفصول، نقوم بجلب الفصول الفريدة لكل معلم
    if (includeClasses) {
      // جلب جميع علاقات الفصول بالمواد للمعلم
      const classSubjects = await prisma.classSubject.findMany({
        where: {
          teacherSubject: {
            teacherId: teacherId
          }
        },
        include: {
          classe: {
            include: {
              students: {
                select: {
                  id: true
                }
              }
            }
          },
          teacherSubject: {
            include: {
              subject: true
            }
          }
        }
      });

      // إنشاء قائمة بالفصول الفريدة
      const uniqueClasses = new Map();
      classSubjects.forEach(cs => {
        if (!uniqueClasses.has(cs.classeId)) {
          uniqueClasses.set(cs.classeId, {
            id: cs.classeId,
            name: cs.classe.name,
            studentsCount: cs.classe.students.length
          });
        }
      });

      // إضافة الفصول إلى الاستجابة
      return NextResponse.json({
        data: teacherSubjects,
        classes: Array.from(uniqueClasses.values()),
        success: true,
        message: 'تم جلب المواد التي يدرسها المعلم بنجاح'
      });
    }

    // إذا لم يطلب المستخدم معلومات الفصول
    return NextResponse.json({
      data: teacherSubjects,
      success: true,
      message: 'تم جلب المواد التي يدرسها المعلم بنجاح'
    });
  } catch (error) {
    console.error('Error fetching teacher subjects:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المواد التي يدرسها المعلم', success: false },
      { status: 500 }
    );
  }
}

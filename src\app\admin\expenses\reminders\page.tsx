"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  FaBell,
  FaPlus,
  FaEdit,
  FaTrash,
  FaTags,
  FaCheck,
  FaArrowLeft,
  FaSave,
  FaExclamationCircle,
  FaExclamationTriangle,
  FaInfoCircle,
  Fa<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaMoneyBillWave,
  FaSync
} from 'react-icons/fa';
import Link from 'next/link';
import { formatDistanceToNow, isBefore, isToday } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';

interface ExpenseCategory {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  color: string | null;
  isActive: boolean;
}

interface ExpenseReminder {
  id: number;
  title: string;
  description: string | null;
  amount: number | null;
  categoryId: number | null;
  category: ExpenseCategory | null;
  dueDate: string;
  reminderDate: string | null;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  notifyByEmail: boolean;
  notifyByPush: boolean;
  emailAddress: string | null;
  createdAt: string;
  updatedAt: string;
}

interface ReminderStats {
  total: number;
  pending: number;
  completed: number;
  overdue: number;
  upcoming: number;
  highPriority: number;
}

export default function ExpenseRemindersPage() {
  const [reminders, setReminders] = useState<ExpenseReminder[]>([]);
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedReminder, setSelectedReminder] = useState<ExpenseReminder | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [stats, setStats] = useState<ReminderStats>({
    total: 0,
    pending: 0,
    completed: 0,
    overdue: 0,
    upcoming: 0,
    highPriority: 0,
  });
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: '',
    categoryId: '',
    dueDate: new Date().toISOString().split('T')[0],
    reminderDate: '',
    priority: 'MEDIUM',
    notifyByEmail: false,
    notifyByPush: false,
    emailAddress: '',
  });

  // جلب تذكيرات المصروفات
  const fetchReminders = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      if (statusFilter !== 'all') {
        if (statusFilter === 'overdue') {
          queryParams.append('overdue', 'true');
        } else if (statusFilter === 'upcoming') {
          queryParams.append('upcoming', 'true');
        } else {
          queryParams.append('status', statusFilter.toUpperCase());
        }
      }

      if (priorityFilter !== 'all') {
        queryParams.append('priority', priorityFilter.toUpperCase());
      }

      if (categoryFilter !== 'all') {
        queryParams.append('categoryId', categoryFilter);
      }

      const response = await fetch(`/api/expenses/reminders?${queryParams}`);

      if (!response.ok) {
        throw new Error('فشل في جلب تذكيرات المصروفات');
      }

      const data = await response.json();
      setReminders(data.reminders);
      setStats(data.stats);
    } catch (error) {
      console.error('خطأ في جلب تذكيرات المصروفات:', error);
      toast.error('فشل في جلب تذكيرات المصروفات');
    } finally {
      setLoading(false);
    }
  };

  // جلب فئات المصروفات
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories');

      if (!response.ok) {
        throw new Error('فشل في جلب فئات المصروفات');
      }

      const data = await response.json();
      setCategories(data.categories);
    } catch (error) {
      console.error('خطأ في جلب فئات المصروفات:', error);
      toast.error('فشل في جلب فئات المصروفات');
    }
  };

  // إضافة تذكير مصروف جديد
  const handleAddReminder = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (!formData.title || !formData.dueDate) {
        toast.error('العنوان وتاريخ الاستحقاق مطلوبان');
        return;
      }

      const response = await fetch('/api/expenses/reminders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          amount: formData.amount ? parseFloat(formData.amount) : null,
          categoryId: formData.categoryId ? parseInt(formData.categoryId) : null,
          dueDate: formData.dueDate,
          reminderDate: formData.reminderDate || null,
          priority: formData.priority,
          notifyByEmail: formData.notifyByEmail,
          notifyByPush: formData.notifyByPush,
          emailAddress: formData.emailAddress || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إضافة تذكير المصروف');
      }

      toast.success('تم إضافة تذكير المصروف بنجاح');
      setIsAddDialogOpen(false);
      resetForm();
      fetchReminders();
    } catch (error) {
      console.error('خطأ في إضافة تذكير المصروف:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في إضافة تذكير المصروف');
    }
  };

  // تعديل تذكير مصروف
  const handleEditReminder = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedReminder) return;

    try {
      if (!formData.title || !formData.dueDate) {
        toast.error('العنوان وتاريخ الاستحقاق مطلوبان');
        return;
      }

      const response = await fetch(`/api/expenses/reminders/${selectedReminder.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          amount: formData.amount ? parseFloat(formData.amount) : null,
          categoryId: formData.categoryId ? parseInt(formData.categoryId) : null,
          dueDate: formData.dueDate,
          reminderDate: formData.reminderDate || null,
          priority: formData.priority,
          notifyByEmail: formData.notifyByEmail,
          notifyByPush: formData.notifyByPush,
          emailAddress: formData.emailAddress || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في تعديل تذكير المصروف');
      }

      toast.success('تم تعديل تذكير المصروف بنجاح');
      setIsEditDialogOpen(false);
      fetchReminders();
    } catch (error) {
      console.error('خطأ في تعديل تذكير المصروف:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في تعديل تذكير المصروف');
    }
  };

  // حذف تذكير مصروف
  const handleDeleteReminder = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا التذكير؟')) return;

    try {
      const response = await fetch(`/api/expenses/reminders/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف تذكير المصروف');
      }

      toast.success('تم حذف تذكير المصروف بنجاح');
      fetchReminders();
    } catch (error) {
      console.error('خطأ في حذف تذكير المصروف:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في حذف تذكير المصروف');
    }
  };

  // إكمال تذكير مصروف
  const handleCompleteReminder = async (id: number, createExpense: boolean) => {
    try {
      const response = await fetch(`/api/expenses/reminders/${id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          createExpense,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إكمال تذكير المصروف');
      }

      const data = await response.json();
      toast.success(data.message);

      if (data.expense) {
        toast.info('تم إنشاء مصروف جديد بناءً على التذكير');
      }

      fetchReminders();
    } catch (error) {
      console.error('خطأ في إكمال تذكير المصروف:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في إكمال تذكير المصروف');
    }
  };

  // إعادة تعيين نموذج الإضافة
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      amount: '',
      categoryId: '',
      dueDate: new Date().toISOString().split('T')[0],
      reminderDate: '',
      priority: 'MEDIUM',
      notifyByEmail: false,
      notifyByPush: false,
      emailAddress: '',
    });
  };

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchCategories();
    fetchReminders();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [statusFilter, priorityFilter, categoryFilter]);

  // الحصول على لون الأولوية
  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'HIGH':
        return 'text-red-600';
      case 'MEDIUM':
        return 'text-yellow-600';
      case 'LOW':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  // الحصول على أيقونة الأولوية
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return <FaExclamationCircle className={getPriorityColor(priority)} />;
      case 'MEDIUM':
        return <FaExclamationTriangle className={getPriorityColor(priority)} />;
      case 'LOW':
        return <FaInfoCircle className={getPriorityColor(priority)} />;
      default:
        return null;
    }
  };

  // الحصول على حالة التذكير
  const getReminderStatus = (reminder: ExpenseReminder): { status: string; color: string } => {
    const dueDate = new Date(reminder.dueDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (reminder.status === 'COMPLETED') {
      return { status: 'مكتمل', color: 'bg-green-100 text-green-800' };
    } else if (reminder.status === 'CANCELLED') {
      return { status: 'ملغي', color: 'bg-gray-100 text-gray-800' };
    } else if (isBefore(dueDate, today)) {
      return { status: 'متأخر', color: 'bg-red-100 text-red-800' };
    } else if (isToday(dueDate)) {
      return { status: 'اليوم', color: 'bg-yellow-100 text-yellow-800' };
    } else {
      return { status: 'قادم', color: 'bg-blue-100 text-blue-800' };
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.expenses.reminders.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-4 md:space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center gap-2 md:gap-4">
          <Link href="/admin/expenses">
            <Button variant="outline" className="h-8 w-8 md:h-10 md:w-10 p-0">
              <FaArrowLeft className="text-xs md:text-base" />
            </Button>
          </Link>
          <h1 className="text-xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaBell className="text-[var(--primary-color)]" />
            تذكيرات المصروفات
          </h1>
        </div>
        <PermissionGuard requiredPermission="admin.expenses.reminders.create">
          <Button
            onClick={() => {
              resetForm();
              setIsAddDialogOpen(true);
            }}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1 text-xs md:text-sm"
          >
            <FaPlus size={12} />
            <span>إضافة تذكير جديد</span>
          </Button>
        </PermissionGuard>
      </div>

      {/* أزرار التنقل بين صفحات المصروفات */}
      <div className="grid grid-cols-3 gap-3 mb-4 md:mb-6">
        <Link href="/admin/expenses" className="w-full">
          <Button
            className="w-full bg-white border border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center justify-center gap-2"
            variant="outline"
          >
            <FaMoneyBillWave />
            <span>المصروفات العادية</span>
          </Button>
        </Link>
        <Link href="/admin/expenses/recurring" className="w-full">
          <Button
            className="w-full bg-white border border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center justify-center gap-2"
            variant="outline"
          >
            <FaSync />
            <span>المصروفات المتكررة</span>
          </Button>
        </Link>

      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-6 gap-3 md:gap-4">
        <Card className="bg-white shadow-sm border border-[#e0f2ef]">
          <CardContent className="p-3 md:p-4 flex flex-col items-center justify-center">
            <div className="text-xl md:text-3xl font-bold text-[var(--primary-color)] mb-1">{stats.total}</div>
            <div className="text-xs md:text-sm text-gray-500">إجمالي التذكيرات</div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border border-[#e0f2ef]">
          <CardContent className="p-3 md:p-4 flex flex-col items-center justify-center">
            <div className="text-xl md:text-3xl font-bold text-blue-600 mb-1">{stats.pending}</div>
            <div className="text-xs md:text-sm text-gray-500">قيد الانتظار</div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border border-[#e0f2ef]">
          <CardContent className="p-3 md:p-4 flex flex-col items-center justify-center">
            <div className="text-xl md:text-3xl font-bold text-primary-color mb-1">{stats.completed}</div>
            <div className="text-xs md:text-sm text-gray-500">مكتملة</div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border border-[#e0f2ef]">
          <CardContent className="p-3 md:p-4 flex flex-col items-center justify-center">
            <div className="text-xl md:text-3xl font-bold text-red-600 mb-1">{stats.overdue}</div>
            <div className="text-xs md:text-sm text-gray-500">متأخرة</div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border border-[#e0f2ef]">
          <CardContent className="p-3 md:p-4 flex flex-col items-center justify-center">
            <div className="text-xl md:text-3xl font-bold text-yellow-600 mb-1">{stats.upcoming}</div>
            <div className="text-xs md:text-sm text-gray-500">قادمة</div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border border-[#e0f2ef]">
          <CardContent className="p-3 md:p-4 flex flex-col items-center justify-center">
            <div className="text-xl md:text-3xl font-bold text-purple-600 mb-1">{stats.highPriority}</div>
            <div className="text-xs md:text-sm text-gray-500">أولوية عالية</div>
          </CardContent>
        </Card>
      </div>

      {/* مرشحات البحث */}
      <Card className="bg-white shadow-md border border-[#e0f2ef] mb-4 md:mb-6">
        <CardContent className="pt-4 md:pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaFilter className="text-[var(--primary-color)]" />
                <span>الحالة</span>
              </label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="جميع الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="pending">قيد الانتظار</SelectItem>
                  <SelectItem value="completed">مكتملة</SelectItem>
                  <SelectItem value="overdue">متأخرة</SelectItem>
                  <SelectItem value="upcoming">قادمة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaFlag className="text-[var(--primary-color)]" />
                <span>الأولوية</span>
              </label>
              <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="جميع الأولويات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأولويات</SelectItem>
                  <SelectItem value="high">عالية</SelectItem>
                  <SelectItem value="medium">متوسطة</SelectItem>
                  <SelectItem value="low">منخفضة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaTags className="text-[var(--primary-color)]" />
                <span>الفئة</span>
              </label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="جميع الفئات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                onClick={fetchReminders}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full text-xs md:text-sm"
              >
                تحديث البيانات
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة التذكيرات */}
      <Card className="bg-white shadow-md border border-[#e0f2ef]">
        <CardHeader className="pb-1 md:pb-2 pt-3 md:pt-4">
          <CardTitle className="text-base md:text-xl flex items-center gap-2">
            <FaBell className="text-[var(--primary-color)]" />
            <span>قائمة التذكيرات</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4 md:py-8">
              <p className="text-gray-500 text-xs md:text-sm">جاري تحميل البيانات...</p>
            </div>
          ) : reminders.length === 0 ? (
            <div className="text-center py-4 md:py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <p className="text-gray-500 mb-3 md:mb-4 text-xs md:text-sm">لم يتم العثور على أي تذكيرات</p>
              <Button
                onClick={() => {
                  resetForm();
                  setIsAddDialogOpen(true);
                }}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white text-xs md:text-sm"
              >
                <FaPlus className="ml-1 md:ml-2" size={12} />
                إضافة تذكير جديد
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs md:text-sm">العنوان</TableHead>
                    <TableHead className="text-xs md:text-sm">المبلغ</TableHead>
                    <TableHead className="text-xs md:text-sm">الفئة</TableHead>
                    <TableHead className="text-xs md:text-sm">تاريخ الاستحقاق</TableHead>
                    <TableHead className="text-xs md:text-sm">الأولوية</TableHead>
                    <TableHead className="text-xs md:text-sm">الحالة</TableHead>
                    <TableHead className="text-xs md:text-sm">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reminders.map((reminder) => (
                    <TableRow key={reminder.id}>
                      <TableCell className="font-medium text-xs md:text-sm">{reminder.title}</TableCell>
                      <TableCell className="text-xs md:text-sm">
                        {reminder.amount ? `${reminder.amount.toLocaleString('fr-FR')} د.ج` : '-'}
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">
                        {reminder.category ? (
                          <div className="flex items-center gap-1">
                            <div
                              className="w-2 h-2 md:w-3 md:h-3 rounded-full"
                              style={{ backgroundColor: reminder.category.color || 'var(--primary-color)' }}
                            ></div>
                            <span>{reminder.category.name}</span>
                          </div>
                        ) : (
                          'بدون فئة'
                        )}
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">
                        <div className="flex flex-col">
                          <span>{new Date(reminder.dueDate).toLocaleDateString('fr-FR')}</span>
                          <span className="text-xs text-gray-500">
                            {formatDistanceToNow(new Date(reminder.dueDate), { locale: ar, addSuffix: true })}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">
                        <div className="flex items-center gap-1">
                          {getPriorityIcon(reminder.priority)}
                          <span className={getPriorityColor(reminder.priority)}>
                            {reminder.priority === 'HIGH'
                              ? 'عالية'
                              : reminder.priority === 'MEDIUM'
                              ? 'متوسطة'
                              : 'منخفضة'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">
                        <Badge className={`${getReminderStatus(reminder).color} text-xs`}>
                          {getReminderStatus(reminder).status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 md:gap-2">
                          {reminder.status === 'PENDING' && (
                            <Button
                              variant="ghost"
                              className="h-6 w-6 md:h-8 md:w-8 p-0 text-primary-color"
                              onClick={() => {
                                if (reminder.amount) {
                                  const createExpense = confirm('هل ترغب في إنشاء مصروف جديد بناءً على هذا التذكير؟');
                                  handleCompleteReminder(reminder.id, createExpense);
                                } else {
                                  handleCompleteReminder(reminder.id, false);
                                }
                              }}
                              title="إكمال"
                            >
                              <FaCheck className="text-xs md:text-base" />
                            </Button>
                          )}
                          <PermissionGuard requiredPermission="admin.expenses.reminders.edit">
                            <Button
                              variant="ghost"
                              className="h-6 w-6 md:h-8 md:w-8 p-0 text-blue-600"
                              onClick={() => {
                                setSelectedReminder(reminder);
                                setFormData({
                                  title: reminder.title,
                                  description: reminder.description || '',
                                  amount: reminder.amount ? reminder.amount.toString() : '',
                                  categoryId: reminder.categoryId ? reminder.categoryId.toString() : '',
                                  dueDate: new Date(reminder.dueDate).toISOString().split('T')[0],
                                  reminderDate: reminder.reminderDate
                                    ? new Date(reminder.reminderDate).toISOString().split('T')[0]
                                    : '',
                                  priority: reminder.priority,
                                  notifyByEmail: reminder.notifyByEmail,
                                  notifyByPush: reminder.notifyByPush,
                                  emailAddress: reminder.emailAddress || '',
                                });
                                setIsEditDialogOpen(true);
                              }}
                              title="تعديل"
                            >
                              <FaEdit className="text-xs md:text-base" />
                            </Button>
                          </PermissionGuard>
                          <PermissionGuard requiredPermission="admin.expenses.reminders.delete">
                            <Button
                              variant="ghost"
                              className="h-6 w-6 md:h-8 md:w-8 p-0 text-red-600"
                              onClick={() => handleDeleteReminder(reminder.id)}
                              title="حذف"
                            >
                              <FaTrash className="text-xs md:text-base" />
                            </Button>
                          </PermissionGuard>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نافذة إضافة تذكير جديد */}
      <AnimatedDialog
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        title="إضافة تذكير مصروف جديد"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="add-reminder-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaPlus size={14} />
            <span>إضافة</span>
          </Button>
        }
      >
        <form id="add-reminder-form" onSubmit={handleAddReminder} className="space-y-4 p-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">العنوان <span className="text-red-500">*</span></Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="أدخل عنوان التذكير"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">الوصف</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="أدخل وصف التذكير"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">المبلغ (اختياري)</Label>
              <Input
                id="amount"
                type="number"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                placeholder="أدخل مبلغ المصروف المتوقع"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="categoryId">الفئة</Label>
              <Select value={formData.categoryId} onValueChange={(value) => setFormData({ ...formData, categoryId: value })}>
                <SelectTrigger id="categoryId">
                  <SelectValue placeholder="اختر فئة المصروف" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dueDate">تاريخ الاستحقاق <span className="text-red-500">*</span></Label>
              <Input
                id="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="reminderDate">تاريخ التذكير (اختياري)</Label>
              <Input
                id="reminderDate"
                type="date"
                value={formData.reminderDate}
                onChange={(e) => setFormData({ ...formData, reminderDate: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">الأولوية</Label>
              <Select value={formData.priority} onValueChange={(value) => setFormData({ ...formData, priority: value as 'LOW' | 'MEDIUM' | 'HIGH' })}>
                <SelectTrigger id="priority">
                  <SelectValue placeholder="اختر أولوية التذكير" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">منخفضة</SelectItem>
                  <SelectItem value="MEDIUM">متوسطة</SelectItem>
                  <SelectItem value="HIGH">عالية</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>خيارات الإشعارات</Label>
              <div className="flex flex-col gap-2 mt-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="notifyByEmail" className="cursor-pointer">إشعار بالبريد الإلكتروني</Label>
                  <Switch
                    id="notifyByEmail"
                    checked={formData.notifyByEmail}
                    onCheckedChange={(checked) => setFormData({ ...formData, notifyByEmail: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="notifyByPush" className="cursor-pointer">إشعار في التطبيق</Label>
                  <Switch
                    id="notifyByPush"
                    checked={formData.notifyByPush}
                    onCheckedChange={(checked) => setFormData({ ...formData, notifyByPush: checked })}
                  />
                </div>
              </div>
            </div>

            {formData.notifyByEmail && (
              <div className="space-y-2">
                <Label htmlFor="emailAddress">البريد الإلكتروني</Label>
                <Input
                  id="emailAddress"
                  type="email"
                  value={formData.emailAddress}
                  onChange={(e) => setFormData({ ...formData, emailAddress: e.target.value })}
                  placeholder="أدخل البريد الإلكتروني للإشعارات"
                />
              </div>
            )}
          </div>
        </form>
      </AnimatedDialog>

      {/* نافذة تعديل تذكير */}
      <AnimatedDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        title="تعديل تذكير مصروف"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="edit-reminder-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaSave size={14} />
            <span>حفظ التغييرات</span>
          </Button>
        }
      >
        <form id="edit-reminder-form" onSubmit={handleEditReminder} className="space-y-4 p-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-title">العنوان <span className="text-red-500">*</span></Label>
              <Input
                id="edit-title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="أدخل عنوان التذكير"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">الوصف</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="أدخل وصف التذكير"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-amount">المبلغ (اختياري)</Label>
              <Input
                id="edit-amount"
                type="number"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                placeholder="أدخل مبلغ المصروف المتوقع"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-categoryId">الفئة</Label>
              <Select value={formData.categoryId} onValueChange={(value) => setFormData({ ...formData, categoryId: value })}>
                <SelectTrigger id="edit-categoryId">
                  <SelectValue placeholder="اختر فئة المصروف" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-dueDate">تاريخ الاستحقاق <span className="text-red-500">*</span></Label>
              <Input
                id="edit-dueDate"
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-reminderDate">تاريخ التذكير (اختياري)</Label>
              <Input
                id="edit-reminderDate"
                type="date"
                value={formData.reminderDate}
                onChange={(e) => setFormData({ ...formData, reminderDate: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-priority">الأولوية</Label>
              <Select value={formData.priority} onValueChange={(value) => setFormData({ ...formData, priority: value as 'LOW' | 'MEDIUM' | 'HIGH' })}>
                <SelectTrigger id="edit-priority">
                  <SelectValue placeholder="اختر أولوية التذكير" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="LOW">منخفضة</SelectItem>
                  <SelectItem value="MEDIUM">متوسطة</SelectItem>
                  <SelectItem value="HIGH">عالية</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>خيارات الإشعارات</Label>
              <div className="flex flex-col gap-2 mt-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="edit-notifyByEmail" className="cursor-pointer">إشعار بالبريد الإلكتروني</Label>
                  <Switch
                    id="edit-notifyByEmail"
                    checked={formData.notifyByEmail}
                    onCheckedChange={(checked) => setFormData({ ...formData, notifyByEmail: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="edit-notifyByPush" className="cursor-pointer">إشعار في التطبيق</Label>
                  <Switch
                    id="edit-notifyByPush"
                    checked={formData.notifyByPush}
                    onCheckedChange={(checked) => setFormData({ ...formData, notifyByPush: checked })}
                  />
                </div>
              </div>
            </div>

            {formData.notifyByEmail && (
              <div className="space-y-2">
                <Label htmlFor="edit-emailAddress">البريد الإلكتروني</Label>
                <Input
                  id="edit-emailAddress"
                  type="email"
                  value={formData.emailAddress}
                  onChange={(e) => setFormData({ ...formData, emailAddress: e.target.value })}
                  placeholder="أدخل البريد الإلكتروني للإشعارات"
                />
              </div>
            )}
          </div>
        </form>
      </AnimatedDialog>
      </div>
    </ProtectedRoute>
  );
}

# UML Diagrams for Quran School Management System

This directory contains UML diagrams that describe the architecture and functionality of the Quran School Management System.

## Available Diagrams

1. **Class Diagram** (`class-diagram.puml`)
   - Shows the main entities in the system and their relationships
   - Includes classes like User, Student, Teacher, Parent, Class, etc.
   - Displays attributes and relationships between entities

2. **Use Case Diagram** (`use-case-diagram.puml`)
   - Illustrates the main functionalities of the system from the perspective of different users
   - Shows what administrators, teachers, students, and parents can do in the system

3. **Sequence Diagrams**
   - **Attendance Tracking** (`attendance-sequence-diagram.puml`): Shows the process of recording student attendance
   - **Quran Progress Tracking** (`quran-progress-sequence-diagram.puml`): Illustrates how teachers record and track student progress in Quran memorization

4. **Activity Diagrams**
   - **Exam Process** (`exam-activity-diagram.puml`): Shows the workflow for creating, conducting, and evaluating exams
   - **Payment Process** (`payment-activity-diagram.puml`): Illustrates the payment workflow for parents

5. **Component Diagram** (`component-diagram.puml`)
   - Shows the main components of the system and their interactions
   - Includes frontend, backend, database, and external services

6. **Deployment Diagram** (`deployment-diagram.puml`)
   - Illustrates the physical deployment of the system
   - Shows how different servers and services are connected

## How to View These Diagrams

These diagrams are written in PlantUML format. To view them, you can:

1. Use an online PlantUML viewer like [PlantUML Server](https://www.plantuml.com/plantuml/uml/)
2. Use a PlantUML plugin for your IDE (available for VS Code, IntelliJ, etc.)
3. Install PlantUML locally and generate images from the .puml files

## Diagram Descriptions

### Class Diagram
The class diagram shows the data model of the system, including all major entities and their relationships. It helps understand how data is structured in the database.

### Use Case Diagram
The use case diagram provides a high-level view of system functionality from the user's perspective. It shows what different types of users can do with the system.

### Sequence Diagrams
Sequence diagrams show the interaction between different components of the system over time. They illustrate how objects communicate with each other to accomplish specific tasks.

### Activity Diagrams
Activity diagrams show the workflow or business process for specific operations in the system. They help understand the steps involved in completing a task.

### Component Diagram
The component diagram shows the organization of the system's components and their dependencies. It helps understand the system's architecture.

### Deployment Diagram
The deployment diagram shows how the software is deployed on hardware components. It illustrates the physical architecture of the system.

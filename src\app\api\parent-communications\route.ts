import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";
import { checkUserPermission } from "@/lib/permissions";
import { CommunicationStatus, CommunicationType, UserRole, NotificationType } from "@prisma/client";

// GET: جلب سجلات التواصل مع الأولياء
export async function GET(request: NextRequest) {
  try {
    // التحقق من الصلاحيات باستخدام نظام الصلاحيات الجديد
    const permissionCheck = await checkUserPermission(request, 'admin.parents.communicate');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const userData = permissionCheck.userData!;

    // استخراج المعلمات من URL
    const { searchParams } = new URL(request.url);
    const parentId = searchParams.get('parentId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const status = searchParams.get('status') as CommunicationStatus | null;
    const type = searchParams.get('type') as CommunicationType | null;

    // بناء شروط البحث
    const where: {
      parentId?: number;
      status?: CommunicationStatus;
      type?: CommunicationType;
      userId?: number;
    } = {};

    if (parentId) {
      const parentIdNum = parseInt(parentId);
      if (isNaN(parentIdNum)) {
        return NextResponse.json({
          message: "معرف الولي يجب أن يكون رقماً صحيحاً"
        }, { status: 400 });
      }
      where.parentId = parentIdNum;
    }

    if (status) {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    // إذا كان المستخدم معلمًا، يجب أن يرى فقط سجلات التواصل التي قام بها
    if (userData.role === UserRole.TEACHER) {
      where.userId = userData.id;
    }

    // جلب إجمالي عدد السجلات
    const total = await prisma.parentCommunication.count({ where });

    // جلب سجلات التواصل
    const communications = await prisma.parentCommunication.findMany({
      where,
      include: {
        parent: {
          select: {
            name: true,
            phone: true,
            email: true,
            students: {
              select: {
                id: true,
                name: true,
                classe: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        },
        user: {
          select: {
            username: true,
            role: true,
            profile: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        date: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    return NextResponse.json({
      data: communications,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching parent communications:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب سجلات التواصل" },
      { status: 500 }
    );
  }
}

// POST: إنشاء سجل تواصل جديد
export async function POST(request: NextRequest) {
  try {
    // التحقق من الصلاحيات باستخدام نظام الصلاحيات الجديد
    const permissionCheck = await checkUserPermission(request, 'admin.parents.communicate');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const userData = permissionCheck.userData!;

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.parentId || !body.type || !body.title || !body.content) {
      return NextResponse.json(
        { message: "البيانات غير مكتملة" },
        { status: 400 }
      );
    }

    // تحويل المعرف إلى رقم
    const parentIdNum = parseInt(body.parentId);
    if (isNaN(parentIdNum)) {
      return NextResponse.json(
        { message: "معرف الولي يجب أن يكون رقماً صحيحاً" },
        { status: 400 }
      );
    }

    // التحقق من وجود الولي
    const parent = await prisma.parent.findUnique({
      where: { id: parentIdNum }
    });

    if (!parent) {
      return NextResponse.json(
        { message: "الولي غير موجود" },
        { status: 404 }
      );
    }

    // إنشاء سجل التواصل
    const communication = await prisma.parentCommunication.create({
      data: {
        parentId: parentIdNum,
        userId: userData.id,
        type: body.type as CommunicationType,
        title: body.title,
        content: body.content,
        date: body.date ? new Date(body.date) : new Date(),
        status: body.status ? (body.status as CommunicationStatus) : CommunicationStatus.PENDING,
        response: body.response || null,
        responseDate: body.responseDate ? new Date(body.responseDate) : null
      }
    });

    // إنشاء إشعار للمستخدم إذا كان مطلوبًا
    if (body.sendNotification) {
      // جلب معلومات الولي مع الطلاب
      const parentWithStudents = await prisma.parent.findUnique({
        where: { id: parentIdNum },
        include: {
          students: true
        }
      });

      if (parentWithStudents) {
        // البحث عن مستخدم مرتبط بالولي
        const parentUser = await prisma.user.findFirst({
          where: {
            profile: {
              name: parentWithStudents.name
            },
            role: UserRole.PARENT
          }
        });

        if (parentUser) {
          // إنشاء إشعار للولي
          await prisma.notification.create({
            data: {
              title: `تواصل جديد: ${body.title}`,
              content: body.content,
              type: 'GENERAL' as NotificationType,
              userId: parentUser.id,
              relatedId: communication.id,
              link: `/parent/communications`
            }
          });
        }
      }
    }

    return NextResponse.json(communication, { status: 201 });
  } catch (error) {
    console.error('Error creating parent communication:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء سجل التواصل" },
      { status: 500 }
    );
  }
}

// PUT: تحديث سجل تواصل
export async function PUT(request: NextRequest) {
  try {
    // التحقق من الصلاحيات باستخدام نظام الصلاحيات الجديد
    const permissionCheck = await checkUserPermission(request, 'admin.parents.communicate');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const userData = permissionCheck.userData!;

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.id) {
      return NextResponse.json(
        { message: "معرف سجل التواصل مطلوب" },
        { status: 400 }
      );
    }

    // تحويل المعرف إلى رقم
    const idNum = parseInt(body.id);
    if (isNaN(idNum)) {
      return NextResponse.json(
        { message: "معرف سجل التواصل يجب أن يكون رقماً صحيحاً" },
        { status: 400 }
      );
    }

    // التحقق من وجود سجل التواصل
    const existingCommunication = await prisma.parentCommunication.findUnique({
      where: { id: idNum }
    });

    if (!existingCommunication) {
      return NextResponse.json(
        { message: "سجل التواصل غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من أن المستخدم هو من أنشأ سجل التواصل أو أنه مسؤول
    if (existingCommunication.userId !== userData.id && userData.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { message: "غير مصرح لك بتحديث هذا السجل" },
        { status: 403 }
      );
    }

    // إعداد بيانات التحديث
    const updateData: {
      type?: CommunicationType;
      title?: string;
      content?: string;
      status?: CommunicationStatus;
      response?: string | null;
      responseDate?: Date | null;
    } = {};

    if (body.type) {
      updateData.type = body.type as CommunicationType;
    }

    if (body.title) {
      updateData.title = body.title;
    }

    if (body.content) {
      updateData.content = body.content;
    }

    if (body.status) {
      updateData.status = body.status as CommunicationStatus;
    }

    if (body.response !== undefined) {
      updateData.response = body.response;
    }

    if (body.responseDate) {
      updateData.responseDate = new Date(body.responseDate);
    }

    // تحديث سجل التواصل
    const updatedCommunication = await prisma.parentCommunication.update({
      where: { id: idNum },
      data: updateData
    });

    return NextResponse.json(updatedCommunication);
  } catch (error) {
    console.error('Error updating parent communication:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث سجل التواصل" },
      { status: 500 }
    );
  }
}

// DELETE: حذف سجل تواصل
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من الصلاحيات باستخدام نظام الصلاحيات الجديد
    const permissionCheck = await checkUserPermission(request, 'admin.parents.delete');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const userData = permissionCheck.userData!;

    // استخراج معرف سجل التواصل من URL
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: "معرف سجل التواصل مطلوب" },
        { status: 400 }
      );
    }

    // تحويل المعرف إلى رقم
    const idNum = parseInt(id);
    if (isNaN(idNum)) {
      return NextResponse.json(
        { message: "معرف سجل التواصل يجب أن يكون رقماً صحيحاً" },
        { status: 400 }
      );
    }

    // التحقق من وجود سجل التواصل
    const existingCommunication = await prisma.parentCommunication.findUnique({
      where: { id: idNum }
    });

    if (!existingCommunication) {
      return NextResponse.json(
        { message: "سجل التواصل غير موجود" },
        { status: 404 }
      );
    }

    // حذف سجل التواصل
    await prisma.parentCommunication.delete({
      where: { id: idNum }
    });

    return NextResponse.json({ message: "تم حذف سجل التواصل بنجاح" });
  } catch (error) {
    console.error('Error deleting parent communication:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف سجل التواصل" },
      { status: 500 }
    );
  }
}

// سكريبت لإنشاء خلفيات الصفحات الأولية
const pageBackgrounds = [
  {
    pageName: 'home',
    displayName: 'الصفحة الرئيسية',
    overlayColor: '#136907',
    overlayOpacity: 0.6,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 10
  },
  {
    pageName: 'about',
    displayName: 'من نحن',
    overlayColor: '#0f5132',
    overlayOpacity: 0.5,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 9
  },
  {
    pageName: 'contact',
    displayName: 'اتصل بنا',
    overlayColor: '#1e3a8a',
    overlayOpacity: 0.4,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 8
  },
  {
    pageName: 'programs',
    displayName: 'البرامج',
    overlayColor: '#7c2d12',
    overlayOpacity: 0.5,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 7
  },
  {
    pageName: 'khatm-sessions',
    displayName: 'مجالس الختم',
    overlayColor: '#581c87',
    overlayOpacity: 0.6,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 6
  },
  {
    pageName: 'donations',
    displayName: 'التبرعات',
    overlayColor: '#be185d',
    overlayOpacity: 0.5,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 5
  },
  {
    pageName: 'login',
    displayName: 'تسجيل الدخول',
    overlayColor: '#374151',
    overlayOpacity: 0.7,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 4
  },
  {
    pageName: 'register',
    displayName: 'التسجيل',
    overlayColor: '#059669',
    overlayOpacity: 0.6,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 3
  }
];

async function createPageBackgrounds() {
  console.log('🎨 بدء إنشاء خلفيات الصفحات...');
  
  for (const background of pageBackgrounds) {
    try {
      const response = await fetch('http://localhost:3000/api/page-backgrounds', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(background)
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log(`✅ تم إنشاء خلفية: ${background.displayName}`);
      } else {
        console.log(`⚠️ خلفية موجودة: ${background.displayName}`);
      }
    } catch (error) {
      console.error(`❌ خطأ في إنشاء خلفية ${background.displayName}:`, error.message);
    }
  }
  
  console.log('🎉 انتهى إنشاء خلفيات الصفحات!');
}

// تشغيل السكريبت
createPageBackgrounds();

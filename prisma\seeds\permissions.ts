import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// قائمة شاملة بجميع الصلاحيات منظمة حسب الصفحات الفعلية
export const permissions = [
  // === لوحة التحكم الرئيسية ===
  { key: 'admin.dashboard.view', name: 'عرض لوحة التحكم', category: 'لوحة_التحكم', route: '/admin' },

  // === إدارة المستخدمين ===
  { key: 'admin.users.view', name: 'عرض المستخدمين', category: 'إدارة_المستخدمين', route: '/admin/users' },
  { key: 'admin.users.create', name: 'إضافة مستخدم', category: 'إدارة_المستخدمين', route: '/admin/users' },
  { key: 'admin.users.edit', name: 'تعديل مستخدم', category: 'إدارة_المستخدمين', route: '/admin/users' },
  { key: 'admin.users.delete', name: 'حذف مستخدم', category: 'إدارة_المستخدمين', route: '/admin/users' },
  { key: 'admin.users.roles', name: 'إدارة أدوار المستخدم', category: 'إدارة_المستخدمين', route: '/admin/users' },
  { key: 'admin.users.bulk', name: 'الإجراءات المجمعة', category: 'إدارة_المستخدمين', route: '/admin/users' },
  { key: 'admin.users.permissions', name: 'إدارة صلاحيات المستخدمين', category: 'إدارة_المستخدمين', route: '/admin/users' },

  // === إدارة الأدوار والصلاحيات ===
  { key: 'admin.roles.view', name: 'عرض الأدوار والصلاحيات', category: 'إدارة_الأدوار', route: '/admin/roles-permissions' },
  { key: 'admin.roles.add', name: 'إضافة دور جديد', category: 'إدارة_الأدوار', route: '/admin/roles-permissions' },
  { key: 'admin.roles.edit', name: 'تعديل الدور', category: 'إدارة_الأدوار', route: '/admin/roles-permissions' },
  { key: 'admin.roles.delete', name: 'حذف الدور', category: 'إدارة_الأدوار', route: '/admin/roles-permissions' },
  { key: 'admin.roles.permissions.edit', name: 'تعديل صلاحيات الأدوار', category: 'إدارة_الأدوار', route: '/admin/roles-permissions' },

  // === إدارة الطلاب ===
  { key: 'admin.students.view', name: 'عرض الطلاب', category: 'إدارة_الطلاب', route: '/admin/students' },
  { key: 'admin.students.add', name: 'إضافة طالب', category: 'إدارة_الطلاب', route: '/admin/students/add' },
  { key: 'admin.students.edit', name: 'تعديل طالب', category: 'إدارة_الطلاب', route: '/admin/students/edit/[id]' },
  { key: 'admin.students.delete', name: 'حذف طالب', category: 'إدارة_الطلاب', route: '/admin/students' },
  { key: 'admin.students.progress', name: 'عرض تقدم الطالب', category: 'إدارة_الطلاب', route: '/admin/students/progress/[id]' },
  { key: 'admin.students.attendance', name: 'إدارة حضور الطالب', category: 'إدارة_الطلاب', route: '/admin/students' },

  // === الوظائف المحسنة للطلاب ===
  { key: 'admin.students.memorization.manage', name: 'إدارة معلومات بداية الحفظ', category: 'إدارة_الطلاب', route: '/admin/students/[id]/memorization-start' },
  { key: 'admin.students.card.view', name: 'عرض وطباعة بطاقة التلميذ', category: 'إدارة_الطلاب', route: '/admin/students/[id]/card' },
  { key: 'admin.students.receipt.manage', name: 'إدارة وصولات التسجيل', category: 'إدارة_الطلاب', route: '/admin/students/[id]/receipt' },

  // === إدارة المعلمين ===
  { key: 'admin.teachers.view', name: 'عرض المعلمين', category: 'إدارة_المعلمين', route: '/admin/teachers' },
  { key: 'admin.teachers.create', name: 'إضافة معلم', category: 'إدارة_المعلمين', route: '/admin/teachers' },
  { key: 'admin.teachers.edit', name: 'تعديل معلم', category: 'إدارة_المعلمين', route: '/admin/teachers' },
  { key: 'admin.teachers.delete', name: 'حذف معلم', category: 'إدارة_المعلمين', route: '/admin/teachers' },
  { key: 'admin.teachers.evaluate', name: 'إضافة تقييمات للمعلم', category: 'إدارة_المعلمين', route: '/admin/teachers' },
  { key: 'admin.teachers.schedule', name: 'إدارة جداول المعلم', category: 'إدارة_المعلمين', route: '/admin/teachers' },
  { key: 'admin.teachers.achievements', name: 'إضافة إنجازات للمعلم', category: 'إدارة_المعلمين', route: '/admin/teachers' },

  // === إدارة أولياء الأمور ===
  { key: 'admin.parents.view', name: 'عرض أولياء الأمور', category: 'إدارة_أولياء_الأمور', route: '/admin/parents' },
  { key: 'admin.parents.create', name: 'إضافة ولي أمر', category: 'إدارة_أولياء_الأمور', route: '/admin/parents' },
  { key: 'admin.parents.edit', name: 'تعديل ولي أمر', category: 'إدارة_أولياء_الأمور', route: '/admin/parents' },
  { key: 'admin.parents.delete', name: 'حذف ولي أمر', category: 'إدارة_أولياء_الأمور', route: '/admin/parents' },
  { key: 'admin.parents.communicate', name: 'التواصل مع أولياء الأمور', category: 'إدارة_أولياء_الأمور', route: '/admin/parents' },
  { key: 'admin.parents.link', name: 'ربط أولياء الأمور بالطلاب', category: 'إدارة_أولياء_الأمور', route: '/admin/parents' },
  { key: 'admin.parents.notify', name: 'إرسال تنبيهات لأولياء الأمور', category: 'إدارة_أولياء_الأمور', route: '/admin/parents' },

  // === إدارة الحضور ===
  { key: 'admin.attendance.view', name: 'عرض الحضور', category: 'إدارة_الحضور', route: '/admin/attendance' },
  { key: 'admin.attendance.create', name: 'تسجيل حضور', category: 'إدارة_الحضور', route: '/admin/attendance' },
  { key: 'admin.attendance.edit', name: 'تعديل حضور', category: 'إدارة_الحضور', route: '/admin/attendance' },
  { key: 'admin.attendance.reports.view', name: 'عرض تقارير الحضور', category: 'إدارة_الحضور', route: '/admin/attendance/reports' },
  { key: 'admin.attendance.reports.send', name: 'إرسال تقارير الحضور', category: 'إدارة_الحضور', route: '/admin/attendance/reports' },
  { key: 'admin.attendance.reports.export', name: 'تصدير تقارير الحضور', category: 'إدارة_الحضور', route: '/admin/attendance/reports' },

  // === إدارة الفصول ===
  { key: 'admin.classes.view', name: 'عرض الفصول', category: 'إدارة_الفصول', route: '/admin/classes' },
  { key: 'admin.classes.create', name: 'إضافة فصل', category: 'إدارة_الفصول', route: '/admin/classes' },
  { key: 'admin.classes.edit', name: 'تعديل فصل', category: 'إدارة_الفصول', route: '/admin/classes' },
  { key: 'admin.classes.delete', name: 'حذف فصل', category: 'إدارة_الفصول', route: '/admin/classes' },
  { key: 'admin.classes.students', name: 'توزيع الطلاب على الفصول', category: 'إدارة_الفصول', route: '/admin/classes' },
  { key: 'admin.classes.schedules', name: 'إدارة جداول الحصص', category: 'إدارة_الفصول', route: '/admin/classes' },
  { key: 'admin.classes.reports', name: 'تقارير الفصول', category: 'إدارة_الفصول', route: '/admin/classes' },

  // === مواد الفصول (صفحة فرعية) ===
  { key: 'admin.class-subjects.view', name: 'عرض مواد الفصول', category: 'إدارة_الفصول', route: '/admin/class-subjects' },
  { key: 'admin.class-subjects.create', name: 'إضافة مادة للفصل', category: 'إدارة_الفصول', route: '/admin/class-subjects' },
  { key: 'admin.class-subjects.edit', name: 'تعديل مواد الفصل', category: 'إدارة_الفصول', route: '/admin/class-subjects' },
  { key: 'admin.class-subjects.delete', name: 'حذف مادة من الفصل', category: 'إدارة_الفصول', route: '/admin/class-subjects' },

  // === إدارة المواد ===
  { key: 'admin.subjects.view', name: 'عرض المواد', category: 'إدارة_المواد', route: '/admin/subjects' },
  { key: 'admin.subjects.create', name: 'إضافة مادة', category: 'إدارة_المواد', route: '/admin/subjects' },
  { key: 'admin.subjects.edit', name: 'تعديل مادة', category: 'إدارة_المواد', route: '/admin/subjects' },
  { key: 'admin.subjects.delete', name: 'حذف مادة', category: 'إدارة_المواد', route: '/admin/subjects' },
  { key: 'admin.subjects.curriculum.view', name: 'عرض منهج المادة', category: 'إدارة_المواد', route: '/admin/subjects/[id]/curriculum' },

  // === ربط المعلمين بالمواد ===
  { key: 'admin.teacher-subjects.view', name: 'عرض ربط المعلمين بالمواد', category: 'إدارة_المعلمين', route: '/admin/teacher-subjects' },
  { key: 'admin.teacher-subjects.create', name: 'ربط معلم بمادة', category: 'إدارة_المعلمين', route: '/admin/teacher-subjects' },
  { key: 'admin.teacher-subjects.delete', name: 'إلغاء ربط معلم بمادة', category: 'إدارة_المعلمين', route: '/admin/teacher-subjects' },
  { key: 'admin.subjects.curriculum', name: 'إدارة المناهج', category: 'إدارة_المواد', route: '/admin/subjects' },

  // === إدارة المستويات ===
  { key: 'admin.levels.view', name: 'عرض المستويات', category: 'إدارة_المستويات', route: '/admin/levels' },
  { key: 'admin.levels.create', name: 'إضافة مستوى', category: 'إدارة_المستويات', route: '/admin/levels' },
  { key: 'admin.levels.edit', name: 'تعديل مستوى', category: 'إدارة_المستويات', route: '/admin/levels' },
  { key: 'admin.levels.delete', name: 'حذف مستوى', category: 'إدارة_المستويات', route: '/admin/levels' },

  // === إدارة البرامج ===
  { key: 'admin.programs.view', name: 'عرض البرامج', category: 'إدارة_البرامج', route: '/admin/programs' },
  { key: 'admin.programs.create', name: 'إضافة برنامج', category: 'إدارة_البرامج', route: '/admin/programs' },
  { key: 'admin.programs.edit', name: 'تعديل برنامج', category: 'إدارة_البرامج', route: '/admin/programs' },
  { key: 'admin.programs.delete', name: 'حذف برنامج', category: 'إدارة_البرامج', route: '/admin/programs' },

  // === مواد المعلمين (صفحة فرعية) - تم دمجها مع القسم السابق ===

  // === نظام التقييم ===
  { key: 'admin.evaluation.view', name: 'عرض التقييم', category: 'نظام_التقييم', route: '/admin/evaluation' },
  { key: 'admin.evaluation.create', name: 'إنشاء امتحان جديد', category: 'نظام_التقييم', route: '/admin/evaluation' },
  { key: 'admin.evaluation.edit', name: 'تعديل الامتحان', category: 'نظام_التقييم', route: '/admin/evaluation' },
  { key: 'admin.evaluation.delete', name: 'حذف الامتحان', category: 'نظام_التقييم', route: '/admin/evaluation' },
  { key: 'admin.evaluation.results.view', name: 'عرض نتائج الامتحانات', category: 'نظام_التقييم', route: '/admin/evaluation/results' },
  { key: 'admin.evaluation.questions.view', name: 'عرض الأسئلة', category: 'نظام_التقييم', route: '/admin/evaluation/questions' },
  { key: 'admin.evaluation.dashboard.view', name: 'عرض لوحة تحكم التقييم', category: 'نظام_التقييم', route: '/admin/evaluation/dashboard' },
  { key: 'admin.evaluation.exams', name: 'إدارة الامتحانات', category: 'نظام_التقييم', route: '/admin/evaluation' },
  { key: 'admin.evaluation.question-banks.view', name: 'عرض بنوك الأسئلة', category: 'نظام_التقييم', route: '/admin/evaluation/question-banks' },
  { key: 'admin.evaluation.exam-questions.view', name: 'عرض أسئلة الامتحانات', category: 'نظام_التقييم', route: '/admin/evaluation/exam-questions' },
  { key: 'admin.evaluation.exam-types.view', name: 'عرض أنواع الامتحانات', category: 'نظام_التقييم', route: '/admin/evaluation/exam-types' },
  { key: 'admin.evaluation.evaluation-types.view', name: 'عرض أنواع التقييم', category: 'نظام_التقييم', route: '/admin/evaluation/evaluation-types' },
  { key: 'admin.evaluation.criteria.view', name: 'عرض معايير التقييم', category: 'نظام_التقييم', route: '/admin/evaluation/criteria' },
  { key: 'admin.evaluation.scoring.view', name: 'عرض تسجيل النقاط', category: 'نظام_التقييم', route: '/admin/evaluation/scoring' },
  { key: 'admin.evaluation.analysis.view', name: 'عرض تحليل النتائج', category: 'نظام_التقييم', route: '/admin/evaluation/analysis' },
  { key: 'admin.evaluation.help.view', name: 'عرض مساعدة التقييم', category: 'نظام_التقييم', route: '/admin/evaluation/help' },

  // === إدارة المدفوعات ===
  { key: 'admin.payments.view', name: 'عرض المدفوعات', category: 'النظام_المالي', route: '/admin/payments' },
  { key: 'admin.payments.create', name: 'إضافة دفعة', category: 'النظام_المالي', route: '/admin/payments' },
  { key: 'admin.payments.edit', name: 'تعديل دفعة', category: 'النظام_المالي', route: '/admin/payments' },
  { key: 'admin.payments.delete', name: 'حذف دفعة', category: 'النظام_المالي', route: '/admin/payments' },
  { key: 'admin.payments.print', name: 'طباعة الوصل', category: 'النظام_المالي', route: '/admin/payments' },

  // === طرق الدفع (صفحة فرعية) ===
  { key: 'admin.payment-methods.view', name: 'عرض طرق الدفع', category: 'النظام_المالي', route: '/admin/payment-methods' },
  { key: 'admin.payment-methods.create', name: 'إضافة طريقة دفع', category: 'النظام_المالي', route: '/admin/payment-methods' },
  { key: 'admin.payment-methods.edit', name: 'تعديل طريقة دفع', category: 'النظام_المالي', route: '/admin/payment-methods' },
  { key: 'admin.payment-methods.delete', name: 'حذف طريقة دفع', category: 'النظام_المالي', route: '/admin/payment-methods' },

  // === إدارة الفواتير ===
  { key: 'admin.invoices.view', name: 'عرض الفواتير', category: 'النظام_المالي', route: '/admin/invoices' },
  { key: 'admin.invoices.create', name: 'إنشاء فاتورة', category: 'النظام_المالي', route: '/admin/invoices' },
  { key: 'admin.invoices.edit', name: 'تعديل فاتورة', category: 'النظام_المالي', route: '/admin/invoices' },
  { key: 'admin.invoices.delete', name: 'حذف فاتورة', category: 'النظام_المالي', route: '/admin/invoices' },

  // === تقارير التدفق النقدي ===
  { key: 'admin.reports.cash-flow.view', name: 'عرض تقرير التدفق النقدي', category: 'التقارير', route: '/admin/reports/cash-flow' },

  // === التقارير التفصيلية ===
  { key: 'admin.reports.detailed.view', name: 'عرض التقارير التفصيلية', category: 'التقارير', route: '/admin/reports/detailed' },

  // === إدارة الخصومات ===
  { key: 'admin.discounts.view', name: 'عرض الخصومات', category: 'النظام_المالي', route: '/admin/discounts' },
  { key: 'admin.discounts.create', name: 'إضافة خصم', category: 'النظام_المالي', route: '/admin/discounts' },
  { key: 'admin.discounts.edit', name: 'تعديل خصم', category: 'النظام_المالي', route: '/admin/discounts' },
  { key: 'admin.discounts.delete', name: 'حذف خصم', category: 'النظام_المالي', route: '/admin/discounts' },

  // === إدارة الخزينة ===
  { key: 'admin.treasury.view', name: 'عرض الخزينة', category: 'النظام_المالي', route: '/admin/treasury' },
  { key: 'admin.treasury.create', name: 'إضافة معاملة مالية', category: 'النظام_المالي', route: '/admin/treasury' },
  { key: 'admin.treasury.edit', name: 'تعديل معاملة مالية', category: 'النظام_المالي', route: '/admin/treasury' },
  { key: 'admin.treasury.delete', name: 'حذف معاملة مالية', category: 'النظام_المالي', route: '/admin/treasury' },
  { key: 'admin.treasury.income', name: 'تسجيل مدخول', category: 'النظام_المالي', route: '/admin/treasury' },
  { key: 'admin.treasury.expense', name: 'تسجيل مصروف', category: 'النظام_المالي', route: '/admin/treasury' },
  { key: 'admin.treasury.forecasts.view', name: 'عرض التنبؤات المالية', category: 'النظام_المالي', route: '/admin/treasury/forecasts' },

  // === إدارة الميزانيات ===
  { key: 'admin.budgets.view', name: 'عرض الميزانيات', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.create', name: 'إنشاء ميزانية', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.edit', name: 'تعديل ميزانية', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.delete', name: 'حذف ميزانية', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.items.create', name: 'إضافة بند للميزانية', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.copy', name: 'نسخ الميزانية', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.review', name: 'مراجعة واعتماد الميزانية', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.export', name: 'تصدير الميزانية', category: 'النظام_المالي', route: '/admin/budgets' },
  { key: 'admin.budgets.transfers.view', name: 'عرض تحويلات الميزانية', category: 'النظام_المالي', route: '/admin/budgets/transfers' },
  { key: 'admin.budgets.alerts.view', name: 'عرض تنبيهات الميزانية', category: 'النظام_المالي', route: '/admin/budgets/alerts' },

  // === إدارة التبرعات ===
  { key: 'admin.donations.view', name: 'عرض التبرعات', category: 'النظام_المالي', route: '/admin/donations' },
  { key: 'admin.donations.create', name: 'إضافة تبرع', category: 'النظام_المالي', route: '/admin/donations' },
  { key: 'admin.donations.edit', name: 'تعديل تبرع', category: 'النظام_المالي', route: '/admin/donations' },
  { key: 'admin.donations.delete', name: 'حذف تبرع', category: 'النظام_المالي', route: '/admin/donations' },
  { key: 'admin.donations.campaigns.view', name: 'عرض حملات التبرع', category: 'النظام_المالي', route: '/admin/donations/campaigns' },
  { key: 'admin.donations.reports.view', name: 'عرض تقارير التبرعات', category: 'النظام_المالي', route: '/admin/donations/reports' },

  // === إدارة المصروفات ===
  { key: 'admin.expenses.view', name: 'عرض المصروفات', category: 'النظام_المالي', route: '/admin/expenses' },
  { key: 'admin.expenses.create', name: 'إضافة مصروف', category: 'النظام_المالي', route: '/admin/expenses' },
  { key: 'admin.expenses.edit', name: 'تعديل مصروف', category: 'النظام_المالي', route: '/admin/expenses' },
  { key: 'admin.expenses.delete', name: 'حذف مصروف', category: 'النظام_المالي', route: '/admin/expenses' },
  { key: 'admin.expenses.recurring.view', name: 'عرض المصروفات الدورية', category: 'النظام_المالي', route: '/admin/expenses/recurring' },
  { key: 'admin.expenses.reminders.view', name: 'عرض تذكيرات المصروفات', category: 'النظام_المالي', route: '/admin/expenses/reminders' },
  { key: 'admin.expenses.reminders.create', name: 'إضافة تذكير مصروف', category: 'النظام_المالي', route: '/admin/expenses/reminders' },
  { key: 'admin.expenses.reminders.edit', name: 'تعديل تذكير مصروف', category: 'النظام_المالي', route: '/admin/expenses/reminders' },
  { key: 'admin.expenses.reminders.delete', name: 'حذف تذكير مصروف', category: 'النظام_المالي', route: '/admin/expenses/reminders' },

  // === فئات المصروفات (صفحة فرعية) ===
  { key: 'admin.expense-categories.view', name: 'عرض فئات المصروفات', category: 'النظام_المالي', route: '/admin/expense-categories' },
  { key: 'admin.expense-categories.create', name: 'إضافة فئة مصروف', category: 'النظام_المالي', route: '/admin/expense-categories' },
  { key: 'admin.expense-categories.edit', name: 'تعديل فئة مصروف', category: 'النظام_المالي', route: '/admin/expense-categories' },
  { key: 'admin.expense-categories.delete', name: 'حذف فئة مصروف', category: 'النظام_المالي', route: '/admin/expense-categories' },

  // === التقارير ===
  { key: 'admin.reports.view', name: 'عرض التقارير', category: 'التقارير', route: '/admin/reports' },
  { key: 'admin.reports.export', name: 'تصدير التقارير', category: 'التقارير', route: '/admin/reports' },
  { key: 'admin.reports.financial', name: 'التقارير المالية', category: 'التقارير', route: '/admin/reports' },
  { key: 'admin.reports.detailed', name: 'التقارير التفصيلية', category: 'التقارير', route: '/admin/reports' },
  { key: 'admin.reports.cash_flow', name: 'تقارير التدفق النقدي', category: 'التقارير', route: '/admin/reports' },

  // === التقارير المالية (صفحة فرعية) ===
  { key: 'admin.financial-reports.view', name: 'عرض التقارير المالية', category: 'التقارير', route: '/admin/financial-reports' },
  { key: 'admin.financial-reports.create', name: 'إنشاء تقرير مالي', category: 'التقارير', route: '/admin/financial-reports' },
  { key: 'admin.financial-reports.export', name: 'تصدير التقارير المالية', category: 'التقارير', route: '/admin/financial-reports' },

  // === التوقعات المالية (صفحة فرعية) ===
  { key: 'admin.financial-forecasts.view', name: 'عرض التوقعات المالية', category: 'التقارير', route: '/admin/financial-forecasts' },
  { key: 'admin.financial-forecasts.create', name: 'إنشاء توقع مالي', category: 'التقارير', route: '/admin/financial-forecasts' },
  { key: 'admin.financial-forecasts.edit', name: 'تعديل التوقعات المالية', category: 'التقارير', route: '/admin/financial-forecasts' },

  // === الأنشطة ===
  { key: 'admin.activities.view', name: 'عرض الأنشطة', category: 'الأنشطة', route: '/admin/activities' },

  // === المكافآت ===
  { key: 'admin.rewards.view', name: 'عرض المكافآت', category: 'المكافآت', route: '/admin/rewards' },
  { key: 'admin.rewards.create', name: 'إضافة مكافأة جديدة', category: 'المكافآت', route: '/admin/rewards' },
  { key: 'admin.rewards.edit', name: 'تعديل المكافأة', category: 'المكافآت', route: '/admin/rewards' },
  { key: 'admin.rewards.delete', name: 'حذف المكافأة', category: 'المكافآت', route: '/admin/rewards' },

  // === لوحة الشرف ===
  { key: 'admin.honor-board.view', name: 'عرض لوحة الشرف', category: 'لوحة_الشرف', route: '/admin/honor-board' },
  { key: 'admin.honor-board.create', name: 'إضافة إنجاز', category: 'لوحة_الشرف', route: '/admin/honor-board' },
  { key: 'admin.honor-board.edit', name: 'تعديل إنجاز', category: 'لوحة_الشرف', route: '/admin/honor-board' },
  { key: 'admin.honor-board.delete', name: 'حذف إنجاز', category: 'لوحة_الشرف', route: '/admin/honor-board' },
  { key: 'admin.honor-board.criteria.view', name: 'عرض معايير التقييم', category: 'لوحة_الشرف', route: '/admin/honor-board/criteria' },
  { key: 'admin.honor-board.certificates.view', name: 'عرض شهادات التقدير', category: 'لوحة_الشرف', route: '/admin/honor-board/certificates' },

  // === مجالس الختم ===
  { key: 'admin.khatm-sessions.view', name: 'عرض مجالس الختم', category: 'مجالس_الختم', route: '/admin/khatm-sessions' },
  { key: 'admin.khatm-sessions.create', name: 'إضافة مجلس جديد', category: 'مجالس_الختم', route: '/admin/khatm-sessions' },
  { key: 'admin.khatm-sessions.edit', name: 'تعديل المجلس', category: 'مجالس_الختم', route: '/admin/khatm-sessions' },
  { key: 'admin.khatm-sessions.delete', name: 'حذف المجلس', category: 'مجالس_الختم', route: '/admin/khatm-sessions' },

  // === حضور مجالس الختم (صفحة فرعية) ===
  { key: 'admin.khatm-attendance.view', name: 'عرض حضور مجالس الختم', category: 'مجالس_الختم', route: '/admin/khatm-attendance' },
  { key: 'admin.khatm-attendance.create', name: 'تسجيل حضور مجلس ختم', category: 'مجالس_الختم', route: '/admin/khatm-attendance' },
  { key: 'admin.khatm-attendance.edit', name: 'تعديل حضور مجلس ختم', category: 'مجالس_الختم', route: '/admin/khatm-attendance' },

  // === تقارير مجالس الختم (صفحة فرعية) ===
  { key: 'admin.khatm-reports.view', name: 'عرض تقارير مجالس الختم', category: 'مجالس_الختم', route: '/admin/khatm-reports' },
  { key: 'admin.khatm-reports.create', name: 'إنشاء تقرير مجلس ختم', category: 'مجالس_الختم', route: '/admin/khatm-reports' },
  { key: 'admin.khatm-reports.delete', name: 'حذف تقرير مجلس ختم', category: 'مجالس_الختم', route: '/admin/khatm-reports' },
  { key: 'admin.khatm-reports.download', name: 'تنزيل تقرير مجلس ختم', category: 'مجالس_الختم', route: '/admin/khatm-reports' },
  { key: 'admin.khatm-reports.export', name: 'تصدير تقارير مجالس الختم', category: 'مجالس_الختم', route: '/admin/khatm-reports' },

  // === تقدم مجالس الختم (صفحة فرعية) ===
  { key: 'admin.khatm-progress.view', name: 'عرض تقدم مجالس الختم', category: 'مجالس_الختم', route: '/admin/khatm-progress' },
  { key: 'admin.khatm-progress.create', name: 'تسجيل تقدم مجلس ختم', category: 'مجالس_الختم', route: '/admin/khatm-progress' },
  { key: 'admin.khatm-progress.edit', name: 'تعديل تقدم مجلس ختم', category: 'مجالس_الختم', route: '/admin/khatm-progress' },

  // === تقدم القرآن ===
  { key: 'admin.quran-progress.view', name: 'عرض تقدم القرآن', category: 'تقدم_القرآن', route: '/admin/quran-progress' },
  { key: 'admin.quran-progress.create', name: 'تسجيل تقدم جديد', category: 'تقدم_القرآن', route: '/admin/quran-progress' },
  { key: 'admin.quran-progress.edit', name: 'تعديل التقدم', category: 'تقدم_القرآن', route: '/admin/quran-progress' },
  { key: 'admin.quran-progress.delete', name: 'حذف التقدم', category: 'تقدم_القرآن', route: '/admin/quran-progress' },

  // === صور الطلاب ===
  { key: 'admin.student-images.view', name: 'عرض صور الطلاب', category: 'صور_الطلاب', route: '/admin/student-images' },
  { key: 'admin.student-images.upload', name: 'رفع صور جديدة', category: 'صور_الطلاب', route: '/admin/student-images' },
  { key: 'admin.student-images.edit', name: 'تعديل الصور', category: 'صور_الطلاب', route: '/admin/student-images' },
  { key: 'admin.student-images.delete', name: 'حذف الصور', category: 'صور_الطلاب', route: '/admin/student-images' },
  { key: 'admin.student-images.albums', name: 'إدارة الألبومات', category: 'صور_الطلاب', route: '/admin/student-images' },
  { key: 'admin.student-images.gallery', name: 'معرض الصور', category: 'صور_الطلاب', route: '/admin/student-images' },
  { key: 'admin.student-images.legacy', name: 'الصور القديمة', category: 'صور_الطلاب', route: '/admin/student-images' },

  // === الإعدادات ===
  { key: 'admin.settings.view', name: 'عرض الإعدادات', category: 'الإعدادات', route: '/admin/settings' },
  { key: 'admin.settings.edit', name: 'تعديل الإعدادات', category: 'الإعدادات', route: '/admin/settings' },
  { key: 'admin.settings.evaluation-config.view', name: 'عرض إعدادات التقييم', category: 'الإعدادات', route: '/admin/settings/evaluation-config' },
  { key: 'admin.settings.colors', name: 'تخصيص ألوان الموقع', category: 'الإعدادات', route: '/admin/admin-setup' },
  { key: 'admin.settings.logos', name: 'تحميل الشعارات', category: 'الإعدادات', route: '/admin/admin-setup' },

  // === لوحة تحكم الموظف ===
  { key: 'admin.employee_dashboard.view', name: 'عرض لوحة تحكم الموظف', category: 'لوحة_الموظف', route: '/admin/employee-dashboard' },
];

export const roles = [
  {
    name: 'ADMIN',
    displayName: 'مدير النظام',
    description: 'مدير النظام له صلاحية الوصول لجميع الوظائف',
    isSystem: true,
    isActive: true
  },
  {
    name: 'EMPLOYEE',
    displayName: 'موظف',
    description: 'موظف في النظام مع صلاحيات محدودة',
    isSystem: true,
    isActive: true
  },
  {
    name: 'TEACHER',
    displayName: 'معلم',
    description: 'معلم في النظام',
    isSystem: true,
    isActive: true
  }
  /*,
  {
    name: 'STUDENT',
    displayName: 'طالب',
    description: 'طالب في النظام',
    isSystem: true,
    isActive: true
  },
  {
    name: 'PARENT',
    displayName: 'ولي أمر',
    description: 'ولي أمر طالب',
    isSystem: true,
    isActive: true
  }*/
];

export async function seedPermissions() {
  console.log('🌱 بدء إدراج الصلاحيات والأدوار...');

  // إدراج الأدوار
  for (const roleData of roles) {
    await prisma.role.upsert({
      where: { name: roleData.name },
      update: {},
      create: roleData
    });
  }

  // إدراج الصلاحيات
  for (const permissionData of permissions) {
    await prisma.permission.upsert({
      where: { key: permissionData.key },
      update: {},
      create: permissionData
    });
  }

  // إعطاء المدير جميع الصلاحيات
  const adminRole = await prisma.role.findUnique({
    where: { name: 'ADMIN' }
  });

  if (adminRole) {
    const allPermissions = await prisma.permission.findMany();

    for (const permission of allPermissions) {
      await prisma.rolePermission.upsert({
        where: {
          roleId_permissionId: {
            roleId: adminRole.id,
            permissionId: permission.id
          }
        },
        update: {},
        create: {
          roleId: adminRole.id,
          permissionId: permission.id
        }
      });
    }
  }

  console.log('✅ تم إدراج الصلاحيات والأدوار بنجاح');
}

/* - لوحة التحكم الرئيسية
- إدارة المستخدمين
- إدارة الأدوار والصلاحيات
- إدارة الطلاب
- إدارة المعلمين
- إدارة أولياء الأمور
- إدارة الفصول
- مواد الفصول
- إدارة المواد
- إدارة المستويات
- إدارة البرامج
- مواد المعلمين
- إدارة الحضور
- نظام التقييم
- إدارة المدفوعات
- طرق الدفع
- إدارة الفواتير
- إدارة الخصومات
- إدارة الخزينة
- إدارة الميزانيات
- إدارة التبرعات
- إدارة المصروفات
- فئات المصروفات
- التقارير
- التقارير المالية
- التوقعات المالية
- الأنشطة
- المكافآت
- لوحة الشرف
- مجالس الختم
- حضور مجالس الختم
- تقارير مجالس الختم
- تقدم مجالس الختم
- تقدم القرآن
- صور الطلاب
- إعدادات النظام
- لوحة تحكم الموظف */
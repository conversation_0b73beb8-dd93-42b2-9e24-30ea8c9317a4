"use client";

import { useState, useEffect } from 'react';
import { FaUserTag, FaCheck, FaTimes, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-hot-toast';

// CSS للـ scrollbar المخصص
const scrollbarStyles = `
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
`;

interface Role {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  isSystem: boolean;
  isActive: boolean;
}

interface User {
  id: string;
  originalId: number;
  username: string;
  email?: string | null;
  role: string;
  roleId?: number | null;
  profile?: {
    name: string;
  } | null;
}

interface UserRoleManagerProps {
  user: User;
  onRoleUpdated: () => void;
  onClose: () => void;
}

export default function UserRoleManager({ user, onRoleUpdated, onClose }: UserRoleManagerProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedRoleId, setSelectedRoleId] = useState<number | null>(user.roleId || null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // جلب الأدوار المتاحة
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/admin/roles');
      if (!response.ok) throw new Error('فشل في جلب الأدوار');
      const data = await response.json();
      setRoles(data.roles || []);
    } catch (error) {
      console.error('Error fetching roles:', error);
      toast.error('فشل في جلب الأدوار');
    } finally {
      setLoading(false);
    }
  };

  // تحديث دور المستخدم
  const updateUserRole = async () => {
    if (!selectedRoleId) {
      toast.error('يرجى اختيار دور للمستخدم');
      return;
    }

    setUpdating(true);
    try {
      // العثور على الدور المحدد لتحديد الدور الأساسي المناسب
      const selectedRole = roles.find(r => r.id === selectedRoleId);
      let baseRole = 'EMPLOYEE'; // افتراضي

      if (selectedRole) {
        // استخدام نفس منطق تحديد الدور الأساسي المستخدم في الخادم
        const { determineBaseRole } = await import('@/utils/roleUtils');
        baseRole = determineBaseRole(selectedRole, roles);
      }

      const response = await fetch(`/api/admin/users/${user.originalId}/role`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ roleId: selectedRoleId, baseRole }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في تحديث دور المستخدم');
      }

      toast.success('تم تحديث دور المستخدم بنجاح');
      onRoleUpdated();
      onClose();
    } catch (error: any) {
      console.error('Error updating user role:', error);
      toast.error(error.message || 'فشل في تحديث دور المستخدم');
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  if (loading) {
    return (
      <>
        <style dangerouslySetInnerHTML={{ __html: scrollbarStyles }} />
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="text-center">
              <FaSpinner className="animate-spin text-2xl text-[var(--primary-color)] mx-auto mb-4" />
              <p>جاري تحميل الأدوار...</p>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: scrollbarStyles }} />
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto custom-scrollbar">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <FaUserTag className="text-[var(--primary-color)]" />
            تحديد دور المستخدم
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        <div className="mb-6">
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <h3 className="font-medium text-gray-800 mb-2">معلومات المستخدم</h3>
            <p className="text-sm text-gray-600">
              <span className="font-medium">الاسم:</span> {user.profile?.name || user.username}
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-medium">اسم المستخدم:</span> {user.username}
            </p>
            {user.email && (
              <p className="text-sm text-gray-600">
                <span className="font-medium">البريد الإلكتروني:</span> {user.email}
              </p>
            )}
            <p className="text-sm text-gray-600">
              <span className="font-medium">الدور الحالي:</span> {user.role}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              اختر الدور الجديد:
            </label>
            <div className="space-y-2 max-h-60 overflow-y-auto pr-2 custom-scrollbar">
              {roles.filter(role => role.isActive).map((role) => (
                <label
                  key={role.id}
                  className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedRoleId === role.id
                      ? 'border-[var(--primary-color)] bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="role"
                    value={role.id}
                    checked={selectedRoleId === role.id}
                    onChange={() => setSelectedRoleId(role.id)}
                    className="w-4 h-4 text-[var(--primary-color)] border-gray-300 focus:ring-[var(--primary-color)]"
                  />
                  <div className="mr-3 flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-800">
                        {role.displayName}
                      </span>
                      {role.isSystem && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          نظام
                        </span>
                      )}
                    </div>
                    {role.description && (
                      <p className="text-sm text-gray-500 mt-1">
                        {role.description}
                      </p>
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="flex gap-3">
          <button
            onClick={updateUserRole}
            disabled={updating || !selectedRoleId}
            className="flex-1 bg-[var(--primary-color)] text-white px-4 py-2 rounded-lg hover:bg-[var(--secondary-color)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            {updating ? (
              <>
                <FaSpinner className="animate-spin" />
                جاري التحديث...
              </>
            ) : (
              <>
                <FaCheck />
                تحديث الدور
              </>
            )}
          </button>
          <button
            onClick={onClose}
            disabled={updating}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            إلغاء
          </button>
        </div>
        </div>
      </div>
    </>
  );
}

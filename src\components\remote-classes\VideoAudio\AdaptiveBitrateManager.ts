/**
 * Class to manage adaptive bitrate for video streaming
 * This class monitors network conditions and adjusts video quality accordingly
 */
export class AdaptiveBitrateManager {
  private videoElement: HTMLVideoElement | null = null;
  private mediaStream: MediaStream | null = null;
  private videoTrack: MediaStreamTrack | null = null;

  // Quality levels (bitrates in kbps)
  private qualityLevels = {
    low: 250,
    medium: 750,
    high: 1500
  };

  // Current quality level
  private currentQuality: 'low' | 'medium' | 'high' = 'medium';

  // Network monitoring
  private lastBandwidthCheck = 0;
  private bandwidthCheckInterval = 5000; // Check every 5 seconds
  private bandwidthSamples: number[] = [];
  private maxSamples = 5;

  // Callbacks
  private onQualityChange: ((quality: 'low' | 'medium' | 'high') => void) | null = null;

  /**
   * Constructor
   * @param onQualityChange Callback function when quality changes
   */
  constructor(onQualityChange?: (quality: 'low' | 'medium' | 'high') => void) {
    this.onQualityChange = onQualityChange || null;
  }

  /**
   * Initialize the manager with a video element and media stream
   * @param videoElement HTML video element
   * @param mediaStream Media stream
   */
  public initialize(videoElement: HTMLVideoElement, mediaStream: MediaStream): void {
    this.videoElement = videoElement;
    this.mediaStream = mediaStream;
    this.videoTrack = mediaStream.getVideoTracks()[0] || null;

    // Start monitoring
    this.startMonitoring();
  }

  /**
   * Set the current quality level manually
   * @param quality Quality level (low, medium, high)
   */
  public setQuality(quality: 'low' | 'medium' | 'high'): void {
    if (this.currentQuality === quality) return;

    this.currentQuality = quality;
    this.applyQualitySettings();

    if (this.onQualityChange) {
      this.onQualityChange(quality);
    }
  }

  /**
   * Get the current quality level
   * @returns Current quality level
   */
  public getQuality(): 'low' | 'medium' | 'high' {
    return this.currentQuality;
  }

  /**
   * Start monitoring network conditions
   */
  private startMonitoring(): void {
    // Check if the browser supports the necessary APIs
    if (!this.videoElement || !this.mediaStream || !this.videoTrack) {
      console.warn('AdaptiveBitrateManager: Missing required elements');
      return;
    }

    // Initial quality settings
    this.applyQualitySettings();

    // Set up periodic bandwidth checks
    const checkBandwidth = () => {
      const now = Date.now();

      // Only check bandwidth every X milliseconds
      if (now - this.lastBandwidthCheck < this.bandwidthCheckInterval) {
        return;
      }

      this.lastBandwidthCheck = now;
      this.measureBandwidth();
    };

    // Check bandwidth periodically
    const intervalId = setInterval(checkBandwidth, this.bandwidthCheckInterval);

    // Clean up when the track ends
    this.videoTrack.addEventListener('ended', () => {
      clearInterval(intervalId);
    });
  }

  /**
   * Measure available bandwidth and adjust quality if needed
   */
  private measureBandwidth(): void {
    // This is a simplified implementation
    // In a real application, you would use WebRTC stats API to get accurate bandwidth measurements

    // Simulate bandwidth measurement based on video playback stats
    if (!this.videoElement || this.videoElement.readyState < 2) return;

    // Get some basic metrics from the video element
    const buffered = this.videoElement.buffered;
    let bufferHealth = 0;

    if (buffered.length > 0) {
      bufferHealth = buffered.end(buffered.length - 1) - this.videoElement.currentTime;
    }

    // Estimate bandwidth based on buffer health
    // This is just a simple heuristic - real implementations would use more sophisticated methods
    let estimatedBandwidth = 1000; // Default assumption (1 Mbps)

    if (bufferHealth < 1) {
      // Buffer is low, assume bandwidth is low
      estimatedBandwidth = 300; // 300 kbps
    } else if (bufferHealth > 5) {
      // Buffer is healthy, assume bandwidth is good
      estimatedBandwidth = 2000; // 2 Mbps
    }

    // Add to samples
    this.bandwidthSamples.push(estimatedBandwidth);

    // Keep only the last N samples
    if (this.bandwidthSamples.length > this.maxSamples) {
      this.bandwidthSamples.shift();
    }

    // Calculate average bandwidth
    const avgBandwidth = this.bandwidthSamples.reduce((sum, value) => sum + value, 0) /
                         this.bandwidthSamples.length;

    // Determine appropriate quality level
    let newQuality: 'low' | 'medium' | 'high' = this.currentQuality;

    if (avgBandwidth < this.qualityLevels.medium * 0.8) {
      newQuality = 'low';
    } else if (avgBandwidth > this.qualityLevels.high * 0.8) {
      newQuality = 'high';
    } else {
      newQuality = 'medium';
    }

    // Change quality if needed
    if (newQuality !== this.currentQuality) {
      this.setQuality(newQuality);
    }
  }

  /**
   * Apply quality settings to the video track
   */
  private applyQualitySettings(): void {
    if (!this.videoTrack) return;

    // Get bitrate from quality levels (not directly used but kept for future implementation)
    // const bitrate = this.qualityLevels[this.currentQuality];

    // Apply constraints to the video track
    // Note: This is a simplified implementation
    // In a real application, you would use more sophisticated methods
    // and consider browser compatibility
    try {
      const constraints: MediaTrackConstraints = {
        // Set appropriate video constraints based on quality level
        width: this.getWidthForQuality(this.currentQuality),
        height: this.getHeightForQuality(this.currentQuality),
        frameRate: this.getFrameRateForQuality(this.currentQuality),
      };

      // Apply constraints
      // Note: applyConstraints is not fully supported in all browsers
      this.videoTrack.applyConstraints(constraints)
        .catch(error => {
          console.warn('Failed to apply video constraints:', error);
        });
    } catch (error) {
      console.warn('Error applying quality settings:', error);
    }
  }

  /**
   * Get appropriate width for quality level
   */
  private getWidthForQuality(quality: 'low' | 'medium' | 'high'): number {
    switch (quality) {
      case 'low': return 640;
      case 'medium': return 1280;
      case 'high': return 1920;
    }
  }

  /**
   * Get appropriate height for quality level
   */
  private getHeightForQuality(quality: 'low' | 'medium' | 'high'): number {
    switch (quality) {
      case 'low': return 360;
      case 'medium': return 720;
      case 'high': return 1080;
    }
  }

  /**
   * Get appropriate frame rate for quality level
   */
  private getFrameRateForQuality(quality: 'low' | 'medium' | 'high'): number {
    switch (quality) {
      case 'low': return 15;
      case 'medium': return 24;
      case 'high': return 30;
    }
  }

  /**
   * Clean up resources
   */
  public dispose(): void {
    this.videoElement = null;
    this.mediaStream = null;
    this.videoTrack = null;
    this.bandwidthSamples = [];
  }
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, Pencil, Trash, Award } from 'lucide-react';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

type Reward = {
  id: number;
  name: string;
  description: string | null;
  requiredPoints: number;
  type: string;
  _count?: {
    studentRewards: number;
  };
};

export default function RewardsPage() {
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    requiredPoints: 0,
    type: 'CERTIFICATE',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchRewards();
  }, []);

  const fetchRewards = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/rewards');
      if (!response.ok) throw new Error('Failed to fetch rewards');
      const data = await response.json();
      setRewards(data.data || []);
    } catch (error) {
      console.error('Error fetching rewards:', error);
      toast.error('حدث خطأ أثناء جلب المكافآت');
    } finally {
      setLoading(false);
    }
  };

  const handleAddReward = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || formData.requiredPoints < 0 || !formData.type) {
        toast.error('يرجى إدخال اسم المكافأة والنقاط المطلوبة ونوع المكافأة');
        return;
      }

      const response = await fetch('/api/rewards', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add reward');
      }

      toast.success('تم إضافة المكافأة بنجاح');
      setIsAddDialogOpen(false);
      setFormData({ name: '', description: '', requiredPoints: 0, type: 'CERTIFICATE' });
      fetchRewards();
    } catch (error) {
      console.error('Error adding reward:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة المكافأة');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditReward = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || formData.requiredPoints < 0 || !formData.type) {
        toast.error('يرجى إدخال اسم المكافأة والنقاط المطلوبة ونوع المكافأة');
        return;
      }

      if (!selectedReward) return;

      const response = await fetch('/api/rewards', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: selectedReward.id,
          ...formData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update reward');
      }

      toast.success('تم تحديث المكافأة بنجاح');
      setIsEditDialogOpen(false);
      setSelectedReward(null);
      fetchRewards();
    } catch (error) {
      console.error('Error updating reward:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث المكافأة');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteReward = async () => {
    try {
      setIsSubmitting(true);

      if (!selectedReward) return;

      const response = await fetch(`/api/rewards?id=${selectedReward.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete reward');
      }

      toast.success('تم حذف المكافأة بنجاح');
      setIsDeleteDialogOpen(false);
      setSelectedReward(null);
      fetchRewards();
    } catch (error) {
      console.error('Error deleting reward:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حذف المكافأة');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (reward: Reward) => {
    setSelectedReward(reward);
    setFormData({
      name: reward.name,
      description: reward.description || '',
      requiredPoints: reward.requiredPoints,
      type: reward.type,
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (reward: Reward) => {
    setSelectedReward(reward);
    setIsDeleteDialogOpen(true);
  };

  const getRewardTypeLabel = (type: string) => {
    switch (type) {
      case 'CERTIFICATE':
        return 'شهادة';
      case 'PRIZE':
        return 'جائزة';
      case 'BADGE':
        return 'وسام';
      case 'OTHER':
        return 'أخرى';
      default:
        return type;
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.rewards.view">
      <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة المكافآت</h1>
        <QuickActionButtons
          entityType="rewards"
          actions={[
            {
              key: 'create',
              label: 'إضافة مكافأة جديدة',
              icon: <Plus size={16} />,
              onClick: () => {
                setFormData({ name: '', description: '', requiredPoints: 0, type: 'CERTIFICATE' });
                setIsAddDialogOpen(true);
              },
              variant: 'primary'
            }
          ]}
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      ) : rewards.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-gray-500">لا توجد مكافآت حتى الآن</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">المعرف</TableHead>
                <TableHead className="text-right">اسم المكافأة</TableHead>
                <TableHead className="text-right">النقاط المطلوبة</TableHead>
                <TableHead className="text-right">نوع المكافأة</TableHead>
                <TableHead className="text-right">الوصف</TableHead>
                <TableHead className="text-right">عدد الطلاب</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {rewards.map((reward) => (
                <TableRow key={reward.id}>
                  <TableCell>{reward.id}</TableCell>
                  <TableCell className="flex items-center">
                    <Award className="h-4 w-4 ml-2 text-yellow-500" />
                    {reward.name}
                  </TableCell>
                  <TableCell>{reward.requiredPoints}</TableCell>
                  <TableCell>{getRewardTypeLabel(reward.type)}</TableCell>
                  <TableCell>{reward.description || '-'}</TableCell>
                  <TableCell>{reward._count?.studentRewards || 0}</TableCell>
                  <TableCell>
                    <OptimizedActionButtonGroup
                      entityType="rewards"
                      onEdit={() => openEditDialog(reward)}
                      onDelete={() => openDeleteDialog(reward)}
                      showEdit={true}
                      showDelete={!(reward._count?.studentRewards && reward._count.studentRewards > 0)}
                      size="sm"
                      className="gap-2"
                      deleteConfirmTitle="هل أنت متأكد من حذف هذه المكافأة؟"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => !open && setIsAddDialogOpen(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إضافة مكافأة جديدة</DialogTitle>
            <DialogDescription>أدخل تفاصيل المكافأة الجديدة</DialogDescription>
          </DialogHeader>
          <form id="addRewardForm" onSubmit={handleAddReward} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم المكافأة</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">النقاط المطلوبة</label>
              <Input
                type="number"
                min="0"
                value={formData.requiredPoints}
                onChange={(e) => setFormData({ ...formData, requiredPoints: parseInt(e.target.value) })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">نوع المكافأة</label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value })}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع المكافأة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CERTIFICATE">شهادة</SelectItem>
                  <SelectItem value="PRIZE">جائزة</SelectItem>
                  <SelectItem value="BADGE">وسام</SelectItem>
                  <SelectItem value="OTHER">أخرى</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="button"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                onClick={(e) => {
                  e.preventDefault();
                  const form = document.getElementById('addRewardForm') as HTMLFormElement;
                  if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                }}
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل المكافأة</DialogTitle>
            <DialogDescription>قم بتعديل تفاصيل المكافأة</DialogDescription>
          </DialogHeader>
          <form id="editRewardForm" onSubmit={handleEditReward} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم المكافأة</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">النقاط المطلوبة</label>
              <Input
                type="number"
                min="0"
                value={formData.requiredPoints}
                onChange={(e) => setFormData({ ...formData, requiredPoints: parseInt(e.target.value) })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">نوع المكافأة</label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value })}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع المكافأة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CERTIFICATE">شهادة</SelectItem>
                  <SelectItem value="PRIZE">جائزة</SelectItem>
                  <SelectItem value="BADGE">وسام</SelectItem>
                  <SelectItem value="OTHER">أخرى</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="button"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                onClick={(e) => {
                  e.preventDefault();
                  const form = document.getElementById('editRewardForm') as HTMLFormElement;
                  if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                }}
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف المكافأة</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف المكافأة {selectedReward?.name}؟
              <br />
              هذا الإجراء لا يمكن التراجع عنه.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteReward}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحذف...' : 'حذف'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

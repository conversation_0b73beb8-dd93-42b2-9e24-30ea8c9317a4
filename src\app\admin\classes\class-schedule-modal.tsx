'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { FaCalendarAlt, FaPlus, FaTrash, FaEdit } from 'react-icons/fa'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'

interface Teacher {
  id: number
  name: string
}

interface Subject {
  id: number
  name: string
}

interface TeacherSubject {
  id: number
  teacher: Teacher
  subject: Subject
}

interface ClassScheduleItem {
  id: number
  day: string
  startTime: string
  endTime: string
  teacherSubjectId: number
  teacherSubject: {
    teacher: Teacher
    subject: Subject
  }
}

interface Class {
  id: number
  name: string
  description?: string
  classSubjects: {
    teacherSubject: TeacherSubject
  }[]
}

interface ClassScheduleModalProps {
  isOpen: boolean
  // تم تغيير اسم الخاصية لتتوافق مع قواعد Next.js للإشارة إلى أنها Server Action
  onCloseAction: () => void
  onSuccessAction?: () => void
  classItem: Class | null
}

const DAYS_OF_WEEK = [
  { value: 'SUNDAY', label: 'الأحد' },
  { value: 'MONDAY', label: 'الإثنين' },
  { value: 'TUESDAY', label: 'الثلاثاء' },
  { value: 'WEDNESDAY', label: 'الأربعاء' },
  { value: 'THURSDAY', label: 'الخميس' },
  { value: 'FRIDAY', label: 'الجمعة' },
  { value: 'SATURDAY', label: 'السبت' }
]

export function ClassScheduleModal({
  isOpen,
  onCloseAction,
  onSuccessAction,
  classItem
}: ClassScheduleModalProps) {
  const [schedules, setSchedules] = useState<ClassScheduleItem[]>([])
  const [isAddingSchedule, setIsAddingSchedule] = useState(false)
  const [isEditingSchedule, setIsEditingSchedule] = useState(false)
  const [selectedSchedule, setSelectedSchedule] = useState<ClassScheduleItem | null>(null)
  const [formData, setFormData] = useState({
    day: 'SUNDAY',
    startTime: '08:00',
    endTime: '09:00',
    teacherSubjectId: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen && classItem) {
      fetchSchedules()
    } else {
      setSchedules([])
      resetForm()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, classItem])

  const fetchSchedules = async () => {
    if (!classItem) return

    try {
      const response = await fetch(`/api/admin/schedules?classeId=${classItem.id}`)
      if (!response.ok) throw new Error('Failed to fetch schedules')

      const data = await response.json()
      setSchedules(data || [])
    } catch (err) {
      console.error('Error fetching schedules:', err)
      setError('فشل في جلب بيانات جدول الحصص')
    }
  }

  const resetForm = () => {
    setFormData({
      day: 'SUNDAY',
      startTime: '08:00',
      endTime: '09:00',
      teacherSubjectId: ''
    })
    setIsAddingSchedule(false)
    setIsEditingSchedule(false)
    setSelectedSchedule(null)
  }

  const handleAddSchedule = async () => {
    if (!classItem || !formData.teacherSubjectId) {
      toast({
        title: 'تنبيه',
        description: 'الرجاء إكمال جميع البيانات المطلوبة',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin/schedules', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          classeId: classItem.id,
          day: formData.day,
          startTime: formData.startTime,
          endTime: formData.endTime,
          teacherSubjectId: parseInt(formData.teacherSubjectId)
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to add schedule')
      }

      toast({
        title: 'نجاح',
        description: 'تم إضافة الحصة بنجاح'
      })

      fetchSchedules()
      resetForm()

      // استدعاء دالة النجاح إذا كانت موجودة
      if (onSuccessAction) {
        onSuccessAction()
      }
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'فشل في إضافة الحصة')
      toast({
        title: 'خطأ',
        description: err instanceof Error ? err.message : 'فشل في إضافة الحصة',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditSchedule = async () => {
    if (!selectedSchedule || !formData.teacherSubjectId) {
      toast({
        title: 'تنبيه',
        description: 'الرجاء إكمال جميع البيانات المطلوبة',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch(`/api/admin/schedules/${selectedSchedule.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          day: formData.day,
          startTime: formData.startTime,
          endTime: formData.endTime,
          teacherSubjectId: parseInt(formData.teacherSubjectId)
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to update schedule')
      }

      toast({
        title: 'نجاح',
        description: 'تم تحديث الحصة بنجاح'
      })

      fetchSchedules()
      resetForm()

      // استدعاء دالة النجاح إذا كانت موجودة
      if (onSuccessAction) {
        onSuccessAction()
      }
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'فشل في تحديث الحصة')
      toast({
        title: 'خطأ',
        description: err instanceof Error ? err.message : 'فشل في تحديث الحصة',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteSchedule = async (scheduleId: number) => {
    if (!confirm('هل أنت متأكد من رغبتك في حذف هذه الحصة؟')) {
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch(`/api/admin/schedules/${scheduleId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to delete schedule')
      }

      toast({
        title: 'نجاح',
        description: 'تم حذف الحصة بنجاح'
      })

      fetchSchedules()

      // استدعاء دالة النجاح إذا كانت موجودة
      if (onSuccessAction) {
        onSuccessAction()
      }
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'فشل في حذف الحصة')
      toast({
        title: 'خطأ',
        description: err instanceof Error ? err.message : 'فشل في حذف الحصة',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const startEditSchedule = (schedule: ClassScheduleItem) => {
    setSelectedSchedule(schedule)
    setFormData({
      day: schedule.day,
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      teacherSubjectId: schedule.teacherSubjectId.toString()
    })
    setIsEditingSchedule(true)
    setIsAddingSchedule(false)
  }

  // ملاحظة: يمكن إضافة وظيفة getDayLabel هنا في المستقبل عند الحاجة
  // لتحويل قيمة اليوم إلى اسم اليوم بالعربية

  const groupSchedulesByDay = () => {
    const grouped: Record<string, ClassScheduleItem[]> = {}

    DAYS_OF_WEEK.forEach(day => {
      grouped[day.value] = schedules.filter(schedule => schedule.day === day.value)
        .sort((a, b) => a.startTime.localeCompare(b.startTime))
    })

    return grouped
  }

  const groupedSchedules = groupSchedulesByDay()

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title={
        <div className="flex items-center gap-2 text-[var(--primary-color)]">
          <FaCalendarAlt className="text-[var(--primary-color)]" />
          <span>جدول الحصص - {classItem?.name}</span>
        </div>
      }
      variant="primary"
      className="rtl"
    >
      {error && <div className="text-red-500 text-center p-2 bg-red-50 rounded-md mb-4 border border-red-100">{error}</div>}

      <div className="grid gap-4 py-4">
        <div className="flex justify-between items-center">
          <h3 className="font-bold text-[var(--primary-color)]">جدول الحصص الأسبوعي</h3>
          <Button
            onClick={() => {
              setIsAddingSchedule(true)
              setIsEditingSchedule(false)
              setSelectedSchedule(null)
            }}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
            size="sm"
          >
            <FaPlus className="ml-1" />
            إضافة حصة
          </Button>
        </div>

        {(isAddingSchedule || isEditingSchedule) && (
          <div className="border border-[#e0f2ef] rounded-md p-4 bg-[#f8fffd]">
            <h4 className="font-bold text-[var(--primary-color)] mb-4">
              {isEditingSchedule ? 'تعديل الحصة' : 'إضافة حصة جديدة'}
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="day" className="text-right font-medium text-[var(--primary-color)]">اليوم</Label>
                <Select
                  value={formData.day}
                  onValueChange={(value) => setFormData({ ...formData, day: value })}
                >
                  <SelectTrigger id="day" className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                    <SelectValue placeholder="اختر اليوم" />
                  </SelectTrigger>
                  <SelectContent>
                    {DAYS_OF_WEEK.map(day => (
                      <SelectItem key={day.value} value={day.value}>
                        {day.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="teacherSubject" className="text-right font-medium text-[var(--primary-color)]">المعلم والمادة</Label>
                <Select
                  value={formData.teacherSubjectId}
                  onValueChange={(value) => setFormData({ ...formData, teacherSubjectId: value })}
                >
                  <SelectTrigger id="teacherSubject" className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                    <SelectValue placeholder="اختر المعلم والمادة" />
                  </SelectTrigger>
                  <SelectContent>
                    {classItem?.classSubjects.map(cs => (
                      <SelectItem key={cs.teacherSubject.id} value={cs.teacherSubject.id.toString()}>
                        {cs.teacherSubject.teacher.name} - {cs.teacherSubject.subject.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="startTime" className="text-right font-medium text-[var(--primary-color)]">وقت البدء</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
                  className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="endTime" className="text-right font-medium text-[var(--primary-color)]">وقت الانتهاء</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
                  className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-4">
              <Button
                onClick={resetForm}
                variant="outline"
                className="border-gray-300 hover:bg-gray-100"
              >
                إلغاء
              </Button>
              <Button
                onClick={isEditingSchedule ? handleEditSchedule : handleAddSchedule}
                disabled={isLoading || !formData.teacherSubjectId}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
              >
                {isLoading ? 'جاري الحفظ...' : isEditingSchedule ? 'تحديث الحصة' : 'إضافة الحصة'}
              </Button>
            </div>
          </div>
        )}

        <div className="border border-[#e0f2ef] rounded-md overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-7 bg-[var(--primary-color)] text-white">
            {DAYS_OF_WEEK.map(day => (
              <div key={day.value} className="p-2 text-center font-bold border-l border-[var(--secondary-color)]">
                {day.label}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-7 min-h-[300px]">
            {DAYS_OF_WEEK.map(day => (
              <div key={day.value} className="p-2 border-l border-t border-[#e0f2ef]">
                {groupedSchedules[day.value].length === 0 ? (
                  <div className="text-center text-gray-400 py-4 text-sm">لا توجد حصص</div>
                ) : (
                  <div className="space-y-2">
                    {groupedSchedules[day.value].map(schedule => (
                      <div
                        key={schedule.id}
                        className="bg-[#f8fffd] p-2 rounded-md border border-[#e0f2ef] text-sm"
                      >
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-bold text-[var(--primary-color)]">
                            {schedule.startTime} - {schedule.endTime}
                          </span>
                          <div className="flex gap-1">
                            <button
                              onClick={() => startEditSchedule(schedule)}
                              className="text-blue-500 hover:text-blue-700"
                              title="تعديل"
                            >
                              <FaEdit size={14} />
                            </button>
                            <button
                              onClick={() => handleDeleteSchedule(schedule.id)}
                              className="text-red-500 hover:text-red-700"
                              title="حذف"
                            >
                              <FaTrash size={14} />
                            </button>
                          </div>
                        </div>
                        <div className="text-xs">
                          <div>{schedule.teacherSubject.subject.name}</div>
                          <div className="text-gray-500">{schedule.teacherSubject.teacher.name}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </AnimatedDialog>
  )
}

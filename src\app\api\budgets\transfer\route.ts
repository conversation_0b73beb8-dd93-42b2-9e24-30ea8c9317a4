import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// تعريف واجهات البيانات
interface BudgetTransferRequest {
  budgetId: number;
  fromItemId: number;
  toItemId: number;
  amount: number;
  notes?: string;
}

interface BudgetItem {
  id: number;
  budgetId: number;
  categoryId: number;
  amount: number;
  notes?: string | null;
  category: {
    id: number;
    name: string;
  };
}

// واجهة لتمثيل تحويلات الميزانية كما هي مخزنة في قاعدة البيانات
// نستخدمها لتوثيق الكود فقط
/* eslint-disable @typescript-eslint/no-unused-vars */
type BudgetTransfer = {
  id: number;
  budgetId: number;
  fromItemId: number;
  toItemId: number;
  amount: number;
  date: Date;
  notes?: string | null;
  fromItem: BudgetItem;
  toItem: BudgetItem;
}
/* eslint-enable @typescript-eslint/no-unused-vars */

interface FormattedTransfer {
  id: number;
  amount: number;
  date: Date;
  notes?: string | null;
  fromItem: {
    id: number;
    categoryId: number;
    categoryName: string;
  };
  toItem: {
    id: number;
    categoryId: number;
    categoryName: string;
  };
}

// POST /api/budgets/transfer - نقل مبلغ بين بنود الميزانية
export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as BudgetTransferRequest;
    const { budgetId, fromItemId, toItemId, amount, notes } = body;

    // التحقق من صحة البيانات
    if (!budgetId || !fromItemId || !toItemId || !amount) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة (معرف الميزانية، البند المصدر، البند الهدف، المبلغ)' },
        { status: 400 }
      );
    }

    if (fromItemId === toItemId) {
      return NextResponse.json(
        { error: 'لا يمكن نقل المبلغ إلى نفس البند' },
        { status: 400 }
      );
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'المبلغ يجب أن يكون رقمًا موجبًا' },
        { status: 400 }
      );
    }

    // التحقق من وجود الميزانية
    const budget = await prisma.budget.findUnique({
      where: { id: budgetId },
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من حالة الميزانية
    if (budget.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: 'لا يمكن نقل المبالغ إلا في الميزانيات النشطة' },
        { status: 400 }
      );
    }

    // التحقق من وجود البند المصدر
    const fromItem = await prisma.budgetItem.findUnique({
      where: {
        id: fromItemId,
        budgetId,
      },
      include: {
        category: true,
      },
    });

    if (!fromItem) {
      return NextResponse.json(
        { error: 'البند المصدر غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود البند الهدف
    const toItem = await prisma.budgetItem.findUnique({
      where: {
        id: toItemId,
        budgetId,
      },
      include: {
        category: true,
      },
    });

    if (!toItem) {
      return NextResponse.json(
        { error: 'البند الهدف غير موجود' },
        { status: 404 }
      );
    }

    // حساب المصروفات الفعلية للبند المصدر
    const fromItemExpenses = await prisma.expense.aggregate({
      where: {
        date: {
          gte: budget.startDate,
          lte: budget.endDate,
        },
        categoryId: fromItem.categoryId,
      },
      _sum: {
        amount: true,
      },
    });

    const fromItemActualExpenses = fromItemExpenses._sum.amount || 0;
    const fromItemRemainingAmount = fromItem.amount - fromItemActualExpenses;

    // التحقق من توفر المبلغ في البند المصدر
    if (amount > fromItemRemainingAmount) {
      return NextResponse.json(
        { error: `المبلغ المتاح في البند المصدر (${fromItemRemainingAmount}) أقل من المبلغ المطلوب نقله (${amount})` },
        { status: 400 }
      );
    }

    // نقل المبلغ بين البنود في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // تخفيض المبلغ من البند المصدر
      const updatedFromItem = await tx.budgetItem.update({
        where: { id: fromItemId },
        data: {
          amount: { decrement: amount },
        },
      });

      // زيادة المبلغ في البند الهدف
      const updatedToItem = await tx.budgetItem.update({
        where: { id: toItemId },
        data: {
          amount: { increment: amount },
        },
      });

      // تسجيل عملية النقل في سجل التحويلات
      const transfer = await tx.budgetTransfer.create({
        data: {
          budgetId,
          fromItemId,
          toItemId,
          amount,
          notes: notes || null,
          date: new Date(),
        },
      });

      return {
        fromItem: updatedFromItem,
        toItem: updatedToItem,
        transfer,
      };
    });

    return NextResponse.json({
      success: true,
      message: `تم نقل ${amount} من "${fromItem.category.name}" إلى "${toItem.category.name}" بنجاح`,
      data: {
        fromItem: {
          id: result.fromItem.id,
          categoryId: fromItem.categoryId,
          categoryName: fromItem.category.name,
          newAmount: result.fromItem.amount,
        },
        toItem: {
          id: result.toItem.id,
          categoryId: toItem.categoryId,
          categoryName: toItem.category.name,
          newAmount: result.toItem.amount,
        },
        transfer: result.transfer,
      },
    });
  } catch (error) {
    console.error('خطأ في نقل المبلغ بين بنود الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في نقل المبلغ بين بنود الميزانية' },
      { status: 500 }
    );
  }
}

// GET /api/budgets/:id/transfers - الحصول على سجل تحويلات الميزانية
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const budgetId = searchParams.get('budgetId');

    if (!budgetId) {
      return NextResponse.json(
        { error: 'معرف الميزانية مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الميزانية
    const budget = await prisma.budget.findUnique({
      where: { id: parseInt(budgetId) },
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // الحصول على سجل التحويلات
    const transfers = await prisma.budgetTransfer.findMany({
      where: { budgetId: parseInt(budgetId) },
      include: {
        fromItem: {
          include: {
            category: true,
          },
        },
        toItem: {
          include: {
            category: true,
          },
        },
      },
      orderBy: { date: 'desc' },
    });

    // تنسيق البيانات
    const formattedTransfers: FormattedTransfer[] = transfers.map((transfer) => ({
      id: transfer.id,
      amount: transfer.amount,
      date: transfer.date,
      notes: transfer.notes,
      fromItem: {
        id: transfer.fromItemId,
        categoryId: transfer.fromItem.categoryId,
        categoryName: transfer.fromItem.category.name,
      },
      toItem: {
        id: transfer.toItemId,
        categoryId: transfer.toItem.categoryId,
        categoryName: transfer.toItem.category.name,
      },
    }));

    return NextResponse.json({
      budget: {
        id: budget.id,
        name: budget.name,
      },
      transfers: formattedTransfers,
    });
  } catch (error) {
    console.error('خطأ في جلب سجل تحويلات الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب سجل تحويلات الميزانية' },
      { status: 500 }
    );
  }
}

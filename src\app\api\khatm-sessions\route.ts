import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';

// GET /api/khatm-sessions - الحصول على قائمة مجالس الختم
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');
    const surahId = searchParams.get('surahId');
    const fromDate = searchParams.get('fromDate');
    const toDate = searchParams.get('toDate');

    // بناء شروط البحث
    const where: Prisma.KhatmSessionWhereInput = {};

    if (teacherId) {
      where.teacherId = parseInt(teacherId);
    }

    if (surahId) {
      where.surahId = parseInt(surahId);
    }

    // إضافة نطاق التاريخ إذا تم تحديده
    if (fromDate || toDate) {
      where.date = {};

      if (fromDate) {
        where.date.gte = new Date(fromDate);
      }

      if (toDate) {
        where.date.lte = new Date(toDate);
      }
    }

    const khatmSessions = await prisma.khatmSession.findMany({
      where,
      include: {
        teacher: {
          select: {
            id: true,
            name: true
          }
        },
        surah: true,
        attendances: {
          include: {
            student: {
              select: {
                id: true,
                name: true
              }
            },
            images: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: khatmSessions,
      message: 'تم جلب مجالس الختم بنجاح'
    });
  } catch (error) {
    console.error('Error fetching khatm sessions:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب مجالس الختم',
    }, { status: 500 });
  }
}

// POST /api/khatm-sessions - إنشاء مجلس ختم جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      title,
      description,
      date,
      location,
      surahId,
      teacherId,
      isPublic,
      isRecurring,
      recurrencePattern,
      recurrenceEndDate
    } = body;

    // التحقق من البيانات المطلوبة
    if (!title || !date || !teacherId) {
      return NextResponse.json({
        success: false,
        error: 'العنوان والتاريخ ومعرف المعلم مطلوبة'
      }, { status: 400 });
    }

    // التحقق من وجود المعلم
    const teacher = await prisma.teacher.findUnique({
      where: { id: parseInt(teacherId) }
    });

    if (!teacher) {
      return NextResponse.json({
        success: false,
        error: 'المعلم غير موجود'
      }, { status: 404 });
    }

    // التحقق من وجود السورة إذا تم تحديدها
    if (surahId) {
      const surah = await prisma.surah.findUnique({
        where: { id: parseInt(surahId) }
      });

      if (!surah) {
        return NextResponse.json({
          success: false,
          error: 'السورة غير موجودة'
        }, { status: 404 });
      }
    }

    // إنشاء مجلس الختم الأساسي
    const khatmSession = await prisma.khatmSession.create({
      data: {
        title,
        description,
        date: new Date(date),
        location,
        surahId: surahId ? parseInt(surahId) : null,
        teacherId: parseInt(teacherId),
        isPublic: isPublic === true || isPublic === 'true',
        isRecurring: isRecurring === true || isRecurring === 'true',
        recurrencePattern: isRecurring ? recurrencePattern : null,
        recurrenceEndDate: isRecurring && recurrenceEndDate ? new Date(recurrenceEndDate) : null
      }
    });

    // إذا كان المجلس متكرر، قم بإنشاء المجالس المتكررة
    if (isRecurring && recurrencePattern && recurrenceEndDate) {
      const createdSessions = await createRecurringSessions(
        khatmSession.id,
        title,
        description,
        new Date(date),
        new Date(recurrenceEndDate),
        recurrencePattern,
        location,
        surahId ? parseInt(surahId) : null,
        parseInt(teacherId),
        isPublic === true || isPublic === 'true'
      );

      return NextResponse.json({
        success: true,
        data: {
          mainSession: khatmSession,
          recurringSessions: createdSessions
        },
        message: `تم إنشاء مجلس الختم بنجاح مع ${createdSessions.length} مجالس متكررة`
      }, { status: 201 });
    }

    return NextResponse.json({
      success: true,
      data: khatmSession,
      message: 'تم إنشاء مجلس الختم بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating khatm session:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء إنشاء مجلس الختم'
    }, { status: 500 });
  }
}

// دالة مساعدة لإنشاء المجالس المتكررة
async function createRecurringSessions(
  parentId: number,
  title: string,
  description: string | null,
  startDate: Date,
  endDate: Date,
  recurrencePattern: string,
  location: string | null,
  surahId: number | null,
  teacherId: number,
  isPublic: boolean
) {
  const createdSessions = [];
  let currentDate = new Date(startDate);
  currentDate.setHours(startDate.getHours());
  currentDate.setMinutes(startDate.getMinutes());

  // تحديد الفاصل الزمني بناءً على نمط التكرار
  let interval = 1; // الفاصل الزمني بالأيام

  switch (recurrencePattern) {
    case 'daily':
      interval = 1;
      break;
    case 'weekly':
      interval = 7;
      break;
    case 'monthly':
      // سنستخدم نفس اليوم من الشهر التالي
      break;
    default:
      interval = 1;
  }

  // إنشاء المجالس المتكررة
  while (true) {
    // حساب التاريخ التالي بناءً على نمط التكرار
    if (recurrencePattern === 'daily' || recurrencePattern === 'weekly') {
      currentDate = new Date(currentDate);
      currentDate.setDate(currentDate.getDate() + interval);
    } else if (recurrencePattern === 'monthly') {
      currentDate = new Date(currentDate);
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // التحقق من أن التاريخ لم يتجاوز تاريخ الانتهاء
    if (currentDate > endDate) {
      break;
    }

    // إنشاء مجلس ختم متكرر
    try {
      const sessionTitle = `${title} - ${formatDate(currentDate)}`;

      const session = await prisma.khatmSession.create({
        data: {
          title: sessionTitle,
          description,
          date: new Date(currentDate),
          location,
          surahId,
          teacherId,
          isPublic,
          parentSessionId: parentId
        }
      });

      createdSessions.push(session);
    } catch (error) {
      console.error('Error creating recurring session:', error);
    }
  }

  return createdSessions;
}

// دالة مساعدة لتنسيق التاريخ
function formatDate(date: Date) {
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

// PUT /api/khatm-sessions - تحديث مجلس ختم
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      id,
      title,
      description,
      date,
      location,
      surahId,
      teacherId,
      isPublic,
      isRecurring,
      recurrencePattern,
      recurrenceEndDate,
      updateRecurringSessions
    } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !title || !date) {
      return NextResponse.json({
        success: false,
        error: 'المعرف والعنوان والتاريخ مطلوبة'
      }, { status: 400 });
    }

    // التحقق من وجود مجلس الختم
    const existingSession = await prisma.khatmSession.findUnique({
      where: { id: parseInt(id) },
      include: {
        childSessions: true
      }
    });

    if (!existingSession) {
      return NextResponse.json({
        success: false,
        error: 'مجلس الختم غير موجود'
      }, { status: 404 });
    }

    // التحقق من وجود المعلم إذا تم تحديده
    if (teacherId) {
      const teacher = await prisma.teacher.findUnique({
        where: { id: parseInt(teacherId) }
      });

      if (!teacher) {
        return NextResponse.json({
          success: false,
          error: 'المعلم غير موجود'
        }, { status: 404 });
      }
    }

    // التحقق من وجود السورة إذا تم تحديدها
    if (surahId) {
      const surah = await prisma.surah.findUnique({
        where: { id: parseInt(surahId) }
      });

      if (!surah) {
        return NextResponse.json({
          success: false,
          error: 'السورة غير موجودة'
        }, { status: 404 });
      }
    }

    // تحديث مجلس الختم
    const updatedSession = await prisma.khatmSession.update({
      where: { id: parseInt(id) },
      data: {
        title,
        description,
        date: new Date(date),
        location,
        surahId: surahId ? parseInt(surahId) : null,
        teacherId: teacherId ? parseInt(teacherId) : undefined,
        isPublic: isPublic === true || isPublic === 'true',
        isRecurring: isRecurring === true || isRecurring === 'true',
        recurrencePattern: isRecurring ? recurrencePattern : null,
        recurrenceEndDate: isRecurring && recurrenceEndDate ? new Date(recurrenceEndDate) : null
      }
    });

    // إذا كان المجلس متكرر وتم طلب تحديث المجالس المتكررة
    if (updateRecurringSessions && existingSession.childSessions && existingSession.childSessions.length > 0) {
      // تحديث المجالس المتكررة
      for (const childSession of existingSession.childSessions) {
        await prisma.khatmSession.update({
          where: { id: childSession.id },
          data: {
            description,
            location,
            surahId: surahId ? parseInt(surahId) : null,
            teacherId: teacherId ? parseInt(teacherId) : undefined,
            isPublic: isPublic === true || isPublic === 'true'
          }
        });
      }

      return NextResponse.json({
        success: true,
        data: {
          mainSession: updatedSession,
          updatedChildSessions: existingSession.childSessions.length
        },
        message: `تم تحديث مجلس الختم بنجاح مع ${existingSession.childSessions.length} مجالس متكررة`
      });
    }

    return NextResponse.json({
      success: true,
      data: updatedSession,
      message: 'تم تحديث مجلس الختم بنجاح'
    });
  } catch (error) {
    console.error('Error updating khatm session:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تحديث مجلس الختم'
    }, { status: 500 });
  }
}

// DELETE /api/khatm-sessions - حذف مجلس ختم
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف مجلس الختم مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود مجلس الختم
    const existingSession = await prisma.khatmSession.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingSession) {
      return NextResponse.json({
        success: false,
        error: 'مجلس الختم غير موجود'
      }, { status: 404 });
    }

    // حذف مجلس الختم (سيتم حذف سجلات الحضور تلقائيًا بسبب onDelete: Cascade)
    await prisma.khatmSession.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف مجلس الختم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting khatm session:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف مجلس الختم'
    }, { status: 500 });
  }
}

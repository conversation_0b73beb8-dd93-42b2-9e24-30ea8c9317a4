'use client';

import { useEffect, useState } from 'react';

interface SiteColors {
  primaryColor: string;
  secondaryColor: string;
  sidebarColor: string;
  backgroundColor: string;
  accentColor: string;
  textColor: string;
}

// دالة آمنة لتطبيق الألوان
const safeApplyColors = (colors: SiteColors) => {
  try {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    const root = document.documentElement;
    if (!root) return;

    // تطبيق الألوان الأساسية
    root.style.setProperty('--primary-color', colors.primaryColor);
    root.style.setProperty('--secondary-color', colors.secondaryColor);
    root.style.setProperty('--sidebar-color', colors.sidebarColor);
    root.style.setProperty('--background-color', colors.backgroundColor);
    root.style.setProperty('--accent-color', colors.accentColor);
    root.style.setProperty('--text-color', colors.textColor);

    // حساب ألوان التباين
    const getContrastColor = (backgroundColor: string): string => {
      try {
        const hex = backgroundColor.replace('#', '');
        if (hex.length !== 6) return '#ffffff';
        
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        
        if (isNaN(r) || isNaN(g) || isNaN(b)) return '#ffffff';
        
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        return brightness > 128 ? '#1f2937' : '#ffffff';
      } catch (error) {
        console.error('Error calculating contrast color:', error);
        return '#ffffff';
      }
    };

    // تطبيق ألوان التباين
    root.style.setProperty('--primary-contrast', getContrastColor(colors.primaryColor));
    root.style.setProperty('--secondary-contrast', getContrastColor(colors.secondaryColor));
    root.style.setProperty('--sidebar-contrast', getContrastColor(colors.sidebarColor));
    root.style.setProperty('--background-contrast', getContrastColor(colors.backgroundColor));

    // تحويل hex إلى RGB
    const hexToRgb = (hex: string) => {
      try {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16)
        } : null;
      } catch (error) {
        console.error('Error converting hex to RGB:', error);
        return null;
      }
    };

    const primaryRgb = hexToRgb(colors.primaryColor);
    if (primaryRgb) {
      root.style.setProperty('--primary-color-rgb', `${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}`);
    }

    const secondaryRgb = hexToRgb(colors.secondaryColor);
    if (secondaryRgb) {
      root.style.setProperty('--secondary-color-rgb', `${secondaryRgb.r}, ${secondaryRgb.g}, ${secondaryRgb.b}`);
    }

    // تطبيق الألوان على العناصر الموجودة بطريقة آمنة
    requestAnimationFrame(() => {
      safeApplyToElements(colors);
    });

  } catch (error) {
    console.error('Error in safeApplyColors:', error);
  }
};

// دالة آمنة لتطبيق الألوان على العناصر
const safeApplyToElements = (colors: SiteColors) => {
  try {
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    // تطبيق آمن على العناصر التي تحتوي على ألوان خضراء
    const selectors = [
      '[class*="bg-green"]',
      '[class*="bg-emerald"]', 
      '[class*="bg-teal"]',
      '[class*="text-green"]',
      '[class*="text-emerald"]',
      '[class*="text-teal"]',
      '[style*="var(--primary-color)"]',
      '[style*="var(--secondary-color)"]'
    ];

    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          try {
            const htmlElement = element as HTMLElement;
            if (!htmlElement || !htmlElement.style) return;

            const className = htmlElement.className || '';
            
            // تطبيق ألوان الخلفية
            if (typeof className === 'string' && className.includes('bg-')) {
              htmlElement.style.backgroundColor = colors.primaryColor;
              htmlElement.style.color = getContrastColor(colors.primaryColor);
            }
            
            // تطبيق ألوان النص
            if (typeof className === 'string' && className.includes('text-')) {
              htmlElement.style.color = colors.primaryColor;
            }
            
            // إصلاح الألوان المباشرة في style
            const style = htmlElement.getAttribute('style');
            if (style && typeof style === 'string') {
              let newStyle = style
                .replace(/var(--primary-color)/g, colors.primaryColor)
                .replace(/var(--secondary-color)/g, colors.secondaryColor);
              htmlElement.setAttribute('style', newStyle);
            }
            
          } catch (elementError) {
            console.error('Error applying color to element:', elementError);
          }
        });
      } catch (selectorError) {
        console.error(`Error with selector ${selector}:`, selectorError);
      }
    });

  } catch (error) {
    console.error('Error in safeApplyToElements:', error);
  }
};

// دالة آمنة لحساب التباين
const getContrastColor = (backgroundColor: string): string => {
  try {
    const hex = backgroundColor.replace('#', '');
    if (hex.length !== 6) return '#ffffff';
    
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    
    if (isNaN(r) || isNaN(g) || isNaN(b)) return '#ffffff';
    
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#1f2937' : '#ffffff';
  } catch (error) {
    console.error('Error calculating contrast color:', error);
    return '#ffffff';
  }
};

export default function SafeColorProvider() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const initializeColors = async () => {
      try {
        // جلب الألوان من localStorage أولاً
        const savedColors = localStorage.getItem('siteColors');
        if (savedColors) {
          try {
            const colors = JSON.parse(savedColors);
            if (colors && typeof colors === 'object') {
              safeApplyColors(colors);
            }
          } catch (parseError) {
            console.error('Error parsing saved colors:', parseError);
          }
        }

        // جلب الألوان من الخادم
        try {
          const response = await fetch('/api/site-colors');
          if (response.ok) {
            const data = await response.json();
            if (data.colors && typeof data.colors === 'object') {
              localStorage.setItem('siteColors', JSON.stringify(data.colors));
              safeApplyColors(data.colors);
            }
          }
        } catch (fetchError) {
          console.error('Error fetching colors from server:', fetchError);
        }

      } catch (error) {
        console.error('Error initializing colors:', error);
      }
    };

    initializeColors();

    // مراقبة التغييرات في localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteColors' && e.newValue) {
        try {
          const newColors = JSON.parse(e.newValue);
          if (newColors && typeof newColors === 'object') {
            safeApplyColors(newColors);
          }
        } catch (error) {
          console.error('Error applying colors from storage change:', error);
        }
      }
    };

    // مراقبة التغييرات في DOM بطريقة آمنة
    let observer: MutationObserver | null = null;
    try {
      observer = new MutationObserver(() => {
        try {
          const savedColors = localStorage.getItem('siteColors');
          if (savedColors) {
            const colors = JSON.parse(savedColors);
            if (colors && typeof colors === 'object') {
              safeApplyToElements(colors);
            }
          }
        } catch (error) {
          console.error('Error in mutation observer:', error);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
      });
    } catch (observerError) {
      console.error('Error creating mutation observer:', observerError);
    }

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      if (observer) {
        observer.disconnect();
      }
    };
  }, [mounted]);

  return null;
}

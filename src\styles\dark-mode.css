/* ملف CSS للوضع المظلم */

/* الوضع المظلم العام */
.dark-mode,
.dark {
  color-scheme: dark;
}

/* إصلاح شامل لجميع العناصر */
.dark-mode *,
.dark * {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الخلفيات البيضاء الثابتة */
.dark-mode,
.dark {
  background-color: var(--background-color, #1e293b) !important;
}

.dark-mode *[style*="background-color: white"],
.dark *[style*="background-color: white"],
.dark-mode *[style*="background-color: #fff"],
.dark *[style*="background-color: #fff"],
.dark-mode *[style*="background-color: #ffffff"],
.dark *[style*="background-color: #ffffff"],
.dark-mode *[style*="background: white"],
.dark *[style*="background: white"],
.dark-mode *[style*="background: #fff"],
.dark *[style*="background: #fff"],
.dark-mode *[style*="background: #ffffff"],
.dark *[style*="background: #ffffff"] {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح النصوص الشفافة والمخفية */
.dark-mode *[style*="color: transparent"],
.dark *[style*="color: transparent"],
.dark-mode *[style*="opacity: 0"],
.dark *[style*="opacity: 0"],
.dark-mode *[style*="color: rgba(0,0,0,0)"],
.dark *[style*="color: rgba(0,0,0,0)"] {
  color: var(--text-color, #f1f5f9) !important;
  opacity: 1 !important;
}

/* خلفيات الوضع المظلم */
.dark-mode,
.dark {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode body,
.dark body {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode *,
.dark * {
  border-color: #475569 !important;
}

.dark-mode .bg-white,
.dark .bg-white {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-50,
.dark .bg-gray-50 {
  background-color: #334155 !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-100,
.dark .bg-gray-100 {
  background-color: #475569 !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-200,
.dark .bg-gray-200 {
  background-color: #64748b !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-300,
.dark .bg-gray-300 {
  background-color: #64748b !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-400,
.dark .bg-gray-400 {
  background-color: #64748b !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-500,
.dark .bg-gray-500 {
  background-color: #64748b !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-600,
.dark .bg-gray-600 {
  background-color: #475569 !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-700,
.dark .bg-gray-700 {
  background-color: #334155 !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-800,
.dark .bg-gray-800 {
  background-color: #1e293b !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gray-900,
.dark .bg-gray-900 {
  background-color: #0f172a !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* نصوص الوضع المظلم */
.dark-mode .text-gray-900,
.dark .text-gray-900,
.dark-mode .text-gray-800,
.dark .text-gray-800,
.dark-mode .text-gray-700,
.dark .text-gray-700,
.dark-mode .text-gray-600,
.dark .text-gray-600 {
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .text-gray-500,
.dark .text-gray-500 {
  color: #cbd5e1 !important;
}

.dark-mode .text-gray-400,
.dark .text-gray-400 {
  color: #94a3b8 !important;
}

.dark-mode .text-gray-300,
.dark .text-gray-300 {
  color: #cbd5e1 !important;
}

.dark-mode .text-gray-200,
.dark .text-gray-200 {
  color: #e2e8f0 !important;
}

.dark-mode .text-gray-100,
.dark .text-gray-100 {
  color: #f1f5f9 !important;
}

.dark-mode .text-black,
.dark .text-black {
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .text-white,
.dark .text-white {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح النصوص الشفافة */
.dark-mode p,
.dark p,
.dark-mode span,
.dark span,
.dark-mode div,
.dark div,
.dark-mode h1,
.dark h1,
.dark-mode h2,
.dark h2,
.dark-mode h3,
.dark h3,
.dark-mode h4,
.dark h4,
.dark-mode h5,
.dark h5,
.dark-mode h6,
.dark h6,
.dark-mode label,
.dark label {
  color: var(--text-color, #f1f5f9) !important;
}

/* حدود الوضع المظلم */
.dark-mode .border-gray-300,
.dark .border-gray-300,
.dark-mode .border-gray-200,
.dark .border-gray-200 {
  border-color: #475569 !important;
}

.dark-mode .border-gray-100,
.dark .border-gray-100 {
  border-color: #334155 !important;
}

/* البطاقات والحاويات */
.dark-mode .shadow-md,
.dark .shadow-md,
.dark-mode .shadow-lg,
.dark .shadow-lg {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
}

/* الأزرار */
.dark-mode .bg-blue-50,
.dark .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: #93c5fd !important;
}

.dark-mode .text-blue-900,
.dark .text-blue-900 {
  color: #93c5fd !important;
}

/* الجداول */
.dark-mode table,
.dark table {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إزالة التطبيق العام للألوان على جميع عناصر th في الوضع المظلم - سيتم تطبيق الألوان بشكل محدد */

.dark-mode td,
.dark td {
  border-color: #475569 !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* النماذج */
.dark-mode input,
.dark input,
.dark-mode textarea,
.dark textarea,
.dark-mode select,
.dark select {
  background-color: #334155 !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

.dark-mode input::placeholder,
.dark input::placeholder,
.dark-mode textarea::placeholder,
.dark textarea::placeholder {
  color: #94a3b8 !important;
}

.dark-mode input:focus,
.dark input:focus,
.dark-mode textarea:focus,
.dark textarea:focus,
.dark-mode select:focus,
.dark select:focus {
  border-color: var(--primary-color, #22d3ee) !important;
  box-shadow: 0 0 0 1px var(--primary-color, #22d3ee) !important;
}

/* القوائم المنسدلة */
.dark-mode .dropdown-menu,
.dark .dropdown-menu {
  background-color: #334155 !important;
  border-color: #475569 !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .dropdown-item:hover,
.dark .dropdown-item:hover {
  background-color: #475569 !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* النوافذ المنبثقة */
.dark-mode .modal-content,
.dark .modal-content,
.dark-mode .dialog-content,
.dark .dialog-content {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

/* التنبيهات */
.dark-mode .alert,
.dark .alert {
  background-color: #334155 !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

/* الشريط الجانبي */
.dark-mode .sidebar,
.dark .sidebar {
  background-color: var(--sidebar-color, #0f172a) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* التنقل */
.dark-mode .nav-link,
.dark .nav-link {
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .nav-link:hover,
.dark .nav-link:hover {
  color: var(--primary-color, #22d3ee) !important;
}

/* الأكورديون */
.dark-mode .accordion-item,
.dark .accordion-item {
  background-color: #334155 !important;
  border-color: #475569 !important;
}

.dark-mode .accordion-header,
.dark .accordion-header {
  background-color: #475569 !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* التبويبات */
.dark-mode .tab-content,
.dark .tab-content {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .tab-pane,
.dark .tab-pane {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* الشارات */
.dark-mode .badge,
.dark .badge {
  background-color: var(--primary-color, #22d3ee) !important;
  color: var(--primary-text-color, #0f172a) !important;
}

/* التحديد */
.dark-mode ::selection,
.dark ::selection {
  background-color: var(--primary-color, #22d3ee) !important;
  color: var(--primary-text-color, #0f172a) !important;
}

/* شريط التمرير */
.dark-mode ::-webkit-scrollbar,
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark-mode ::-webkit-scrollbar-track,
.dark ::-webkit-scrollbar-track {
  background: #334155;
}

.dark-mode ::-webkit-scrollbar-thumb,
.dark ::-webkit-scrollbar-thumb {
  background: #64748b;
  border-radius: 4px;
}

.dark-mode ::-webkit-scrollbar-thumb:hover,
.dark ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* انتقالات سلسة */
.dark-mode *,
.dark * {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* تحسينات إضافية للوضع المظلم */
.dark-mode .text-black,
.dark .text-black {
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .bg-gradient-to-r,
.dark .bg-gradient-to-r {
  background: linear-gradient(to right, var(--primary-color, #22d3ee), var(--secondary-color, #06b6d4)) !important;
}

/* إصلاح مشاكل التباين */
.dark-mode .contrast-fix,
.dark .contrast-fix {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

/* إصلاحات إضافية للوضع المظلم */
.dark-mode main,
.dark main {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode section,
.dark section {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode article,
.dark article {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode header,
.dark header {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode footer,
.dark footer {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الخلفيات البيضاء العنيدة */
.dark-mode [class*="bg-white"],
.dark [class*="bg-white"] {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode [style*="background-color: white"],
.dark [style*="background-color: white"],
.dark-mode [style*="background-color: #fff"],
.dark [style*="background-color: #fff"],
.dark-mode [style*="background-color: #ffffff"],
.dark [style*="background-color: #ffffff"] {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح النصوص الشفافة العنيدة */
.dark-mode [style*="color: transparent"],
.dark [style*="color: transparent"],
.dark-mode [style*="opacity: 0"],
.dark [style*="opacity: 0"] {
  color: var(--text-color, #f1f5f9) !important;
  opacity: 1 !important;
}

/* إصلاح الكروت والحاويات */
.dark-mode .card,
.dark .card,
.dark-mode .container,
.dark .container,
.dark-mode .wrapper,
.dark .wrapper {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الأزرار */
.dark-mode button,
.dark button {
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode .btn,
.dark .btn {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الروابط */
.dark-mode a,
.dark a {
  color: var(--primary-color, #22d3ee) !important;
}

.dark-mode a:hover,
.dark a:hover {
  color: var(--secondary-color, #06b6d4) !important;
}

/* إصلاح التنقل */
.dark-mode nav,
.dark nav {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح القوائم */
.dark-mode ul,
.dark ul,
.dark-mode ol,
.dark ol,
.dark-mode li,
.dark li {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الجداول المتقدم */
.dark-mode table,
.dark table {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode thead,
.dark thead,
.dark-mode tbody,
.dark tbody,
.dark-mode tfoot,
.dark tfoot {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode tr,
.dark tr {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

.dark-mode tr:nth-child(even),
.dark tr:nth-child(even) {
  background-color: #334155 !important;
}

.dark-mode tr:hover,
.dark tr:hover {
  background-color: #475569 !important;
}

/* إصلاحات شاملة إضافية للوضع المظلم */

/* إصلاح جميع الخلفيات البيضاء */
.dark-mode [class*="bg-white"],
.dark [class*="bg-white"],
.dark-mode [class*="bg-gray-50"],
.dark [class*="bg-gray-50"],
.dark-mode [class*="bg-gray-100"],
.dark [class*="bg-gray-100"],
.dark-mode [class*="bg-gray-200"],
.dark [class*="bg-gray-200"] {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح جميع النصوص الداكنة */
.dark-mode [class*="text-gray-900"],
.dark [class*="text-gray-900"],
.dark-mode [class*="text-gray-800"],
.dark [class*="text-gray-800"],
.dark-mode [class*="text-gray-700"],
.dark [class*="text-gray-700"],
.dark-mode [class*="text-gray-600"],
.dark [class*="text-gray-600"],
.dark-mode [class*="text-black"],
.dark [class*="text-black"] {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الحدود */
.dark-mode [class*="border-gray"],
.dark [class*="border-gray"] {
  border-color: #475569 !important;
}

/* إصلاح العناصر التفاعلية */
.dark-mode button,
.dark button,
.dark-mode .btn,
.dark .btn,
.dark-mode input,
.dark input,
.dark-mode textarea,
.dark textarea,
.dark-mode select,
.dark select {
  background-color: #334155 !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

.dark-mode button:hover,
.dark button:hover,
.dark-mode .btn:hover,
.dark .btn:hover {
  background-color: #475569 !important;
}

/* إصلاح الكروت والبطاقات */
.dark-mode .card,
.dark .card,
.dark-mode [class*="card"],
.dark [class*="card"] {
  background-color: #334155 !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

/* إصلاح التنقل والقوائم */
.dark-mode nav,
.dark nav,
.dark-mode .navbar,
.dark .navbar,
.dark-mode .menu,
.dark .menu {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الجداول */
.dark-mode table,
.dark table {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إزالة التطبيق العام للألوان على جميع عناصر th في الوضع المظلم - سيتم تطبيق الألوان بشكل محدد */

.dark-mode td,
.dark td {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

/* إصلاح النماذج */
.dark-mode form,
.dark form {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode label,
.dark label {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الأقسام والحاويات */
.dark-mode .container,
.dark .container,
.dark-mode .wrapper,
.dark .wrapper,
.dark-mode .content,
.dark .content,
.dark-mode .main,
.dark .main {
  background-color: var(--background-color, #1e293b) !important;
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح العناوين */
.dark-mode h1,
.dark h1,
.dark-mode h2,
.dark h2,
.dark-mode h3,
.dark h3,
.dark-mode h4,
.dark h4,
.dark-mode h5,
.dark h5,
.dark-mode h6,
.dark h6 {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الفقرات والنصوص */
.dark-mode p,
.dark p,
.dark-mode span,
.dark span,
.dark-mode div,
.dark div {
  color: var(--text-color, #f1f5f9) !important;
}

/* إصلاح الروابط */
.dark-mode a,
.dark a {
  color: var(--primary-color, #22d3ee) !important;
}

.dark-mode a:hover,
.dark a:hover {
  color: var(--secondary-color, #06b6d4) !important;
}

/* إصلاح التنبيهات والإشعارات */
.dark-mode .alert,
.dark .alert,
.dark-mode .notification,
.dark .notification,
.dark-mode .toast,
.dark .toast {
  background-color: #334155 !important;
  color: var(--text-color, #f1f5f9) !important;
  border-color: #475569 !important;
}

/* إصلاح شريط التمرير */
.dark-mode ::-webkit-scrollbar,
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark-mode ::-webkit-scrollbar-track,
.dark ::-webkit-scrollbar-track {
  background: #334155 !important;
}

.dark-mode ::-webkit-scrollbar-thumb,
.dark ::-webkit-scrollbar-thumb {
  background: #64748b !important;
  border-radius: 4px;
}

.dark-mode ::-webkit-scrollbar-thumb:hover,
.dark ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8 !important;
}

/* إصلاح العناصر المخفية أو الشفافة */
.dark-mode [style*="visibility: hidden"],
.dark [style*="visibility: hidden"] {
  visibility: visible !important;
}

/* إصلاح الخلفيات المتدرجة */
.dark-mode [class*="gradient"],
.dark [class*="gradient"] {
  background: linear-gradient(to right, var(--primary-color, #22d3ee), var(--secondary-color, #06b6d4)) !important;
}

/* إصلاح الظلال */
.dark-mode [class*="shadow"],
.dark [class*="shadow"] {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
}

/* إصلاح نهائي شامل */
.dark-mode *:not([class*="text-"]):not([style*="color:"]),
.dark *:not([class*="text-"]):not([style*="color:"]) {
  color: var(--text-color, #f1f5f9) !important;
}

.dark-mode *:not([class*="bg-"]):not([style*="background"]),
.dark *:not([class*="bg-"]):not([style*="background"]) {
  background-color: inherit !important;
}

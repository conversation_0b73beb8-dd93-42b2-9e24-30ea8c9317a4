'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FaChartBar, FaArrowLeft, FaSpinner } from 'react-icons/fa';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';

export default function FinancialReportsRedirect() {
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [quickStats, setQuickStats] = useState<{
    current: {
      totals: {
        totalIncome: number;
        totalExpenses: number;
        netProfit: number;
      };
      treasury: {
        balance: number;
      };
    };
  } | null>(null);

  useEffect(() => {
    // جلب إحصائيات سريعة لعرضها أثناء التوجيه
    fetchQuickStats();

    // تأخير التوجيه قليلاً لإظهار المعلومات المفيدة
    const timer = setTimeout(() => {
      setIsRedirecting(true);
      router.replace('/admin/reports/financial');
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  const fetchQuickStats = async () => {
    try {
      const response = await fetch('/api/admin/financial-reports/quick-stats?period=month');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setQuickStats(result.data);
        }
      }
    } catch (error) {
      console.error('Error fetching quick stats:', error);
    }
  };

  const handleDirectNavigation = () => {
    setIsRedirecting(true);
    router.replace('/admin/reports/financial');
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('ar-DZ', { style: 'currency', currency: 'DZD' })
      .replace(/\s+/g, ' ')
      .replace('DZD', 'د.ج');
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.reports.financial">
      <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white flex items-center justify-center p-4">
        <div className="max-w-2xl w-full space-y-6">
          {/* العنوان الرئيسي */}
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-[var(--primary-color)] p-4 rounded-full">
                <FaChartBar className="text-white text-3xl" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-[var(--primary-color)] mb-2">
              التقارير المالية
            </h1>
            <p className="text-gray-600">
              {isRedirecting ? 'جاري التوجيه إلى صفحة التقارير المالية...' : 'نظرة سريعة على الوضع المالي'}
            </p>
          </div>

          {/* الإحصائيات السريعة */}
          {quickStats && !isRedirecting && (
            <Card className="border-t-4 border-[var(--primary-color)]">
              <CardHeader>
                <CardTitle className="text-lg text-center">إحصائيات الشهر الماضي</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(quickStats.current.totals.totalIncome)}
                    </div>
                    <div className="text-sm text-gray-500">إجمالي الإيرادات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {formatCurrency(quickStats.current.totals.totalExpenses)}
                    </div>
                    <div className="text-sm text-gray-500">إجمالي المصروفات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(quickStats.current.totals.netProfit)}
                    </div>
                    <div className="text-sm text-gray-500">صافي الربح</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-[var(--primary-color)]">
                      {formatCurrency(quickStats.current.treasury.balance)}
                    </div>
                    <div className="text-sm text-gray-500">الرصيد الحالي</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* مؤشر التحميل والأزرار */}
          <div className="text-center space-y-4">
            {isRedirecting ? (
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <FaSpinner className="animate-spin text-[var(--primary-color)] text-xl" />
                <span className="text-[var(--primary-color)]">جاري التحميل...</span>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="animate-pulse">
                  <div className="h-2 bg-[var(--primary-color)] rounded-full w-1/3 mx-auto"></div>
                </div>
                <Button
                  onClick={handleDirectNavigation}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white px-6 py-2"
                >
                  <FaArrowLeft className="ml-2" />
                  الانتقال الآن
                </Button>
              </div>
            )}
          </div>

          {/* معلومات إضافية */}
          <div className="text-center text-sm text-gray-500">
            <p>ستتم إعادة توجيهك تلقائياً خلال ثوانٍ قليلة</p>
          </div>
        </div>
      </div>
    </OptimizedProtectedRoute>
  );
}

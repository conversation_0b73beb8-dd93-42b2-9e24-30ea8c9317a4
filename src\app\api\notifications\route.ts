import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, NotificationType } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

// GET: جلب الإشعارات للمستخدم الحالي
export async function GET(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userId = userData.id;

        // استخراج المعلمات من URL
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '10');
        const page = parseInt(searchParams.get('page') || '1');
        const unreadOnly = searchParams.get('unread') === 'true';
        const type = searchParams.get('type') as NotificationType | null;

        // جلب الإشعارات الفردية
        const individualWhere = {
            userId,
            ...(unreadOnly ? { read: false } : {}),
            ...(type ? { type } : {})
        };

        const individualNotifications = await prisma.notification.findMany({
            where: {
                userId,
                isGroupNotification: false, // Ensure it's explicitly not a group notification
                ...(unreadOnly ? { read: false } : {}),
                ...(type ? { type } : {})
            },
            include: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        // جلب الإشعارات الجماعية للمستخدم من خلال جدول NotificationRecipient
        const groupNotificationRecipients = await prisma.notificationRecipient.findMany({
            where: {
                userId: userId,
                ...(unreadOnly ? { read: false } : {}),
                notification: {
                    isGroupNotification: true,
                    ...(type ? { type } : {})
                }
            },
            include: {
                notification: {
                    include: {
                        user: {
                            select: {
                                id: true,
                                username: true,
                                profile: {
                                    select: {
                                        name: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
            orderBy: {
                notification: {
                    createdAt: 'desc' // Order by the creation date of the actual notification
                }
            }
        });

        // دمج الإشعارات وتنسيقها
        const allNotifications = [
            ...individualNotifications.map(notification => ({
                id: `individual-${notification.id}`, // Unique key for individual notifications
                title: notification.title,
                content: notification.content,
                type: notification.type,
                read: notification.read,
                createdAt: notification.createdAt.toISOString(),
                link: notification.link,
                isGroupNotification: false,
                sender: notification.user ? {
                    id: notification.user.id,
                    name: notification.user.profile?.name || notification.user.username,
                    username: notification.user.username
                } : null
            })),
            ...groupNotificationRecipients.map(recipient => ({
                id: `group-${recipient.id}`, // Unique key for group notifications (using recipient ID)
                title: recipient.notification.title,
                content: recipient.notification.content,
                type: recipient.notification.type,
                read: recipient.read, // Use the read status from NotificationRecipient
                createdAt: recipient.notification.createdAt.toISOString(),
                link: recipient.notification.link,
                isGroupNotification: true,
                sender: recipient.notification.user ? {
                    id: recipient.notification.user.id,
                    name: recipient.notification.user.profile?.name || recipient.notification.user.username,
                    username: recipient.notification.user.username
                } : null
            }))
        ];

        // ترتيب جميع الإشعارات حسب التاريخ
        allNotifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        // تطبيق التصفح (pagination)
        const total = allNotifications.length;
        const notifications = allNotifications.slice((page - 1) * limit, page * limit);

        // حساب عدد الإشعارات غير المقروءة
        const individualUnreadCount = await prisma.notification.count({
            where: {
                userId,
                isGroupNotification: false,
                read: false
            }
        });

        const groupUnreadCount = await prisma.notificationRecipient.count({
            where: {
                userId: userId,
                read: false,
                notification: {
                    isGroupNotification: true
                }
            }
        });

        const unreadCount = individualUnreadCount + groupUnreadCount;

        return NextResponse.json({
            notifications,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            },
            unreadCount
        });
    } catch (error) {
        console.error('Error fetching notifications:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب الإشعارات" },
            { status: 500 }
        );
    }
}

// POST: إنشاء إشعار جديد
export async function POST(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم أو موظف مخول" },
                { status: 401 }
            );
        }

        const body = await request.json();

        // التحقق من البيانات المطلوبة
        if (!body.title || !body.content || !body.userId) {
            return NextResponse.json(
                { message: "البيانات غير مكتملة" },
                { status: 400 }
            );
        }

        // إنشاء الإشعار
        const notification = await prisma.notification.create({
            data: {
                title: body.title,
                content: body.content,
                type: body.type || 'GENERAL',
                userId: body.userId,
                relatedId: body.relatedId,
                link: body.link,
                isGroupNotification: false, // Explicitly set for individual notifications
            }
        });

        return NextResponse.json(notification, { status: 201 });
    } catch (error) {
        console.error('Error creating notification:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء إنشاء الإشعار" },
            { status: 500 }
        );
    }
}

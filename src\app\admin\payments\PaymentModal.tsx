'use client';

import { useState, useEffect, useCallback } from 'react';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'react-toastify';

interface Student {
  id: string;
  name: string;
}

interface Discount {
  id: number;
  name: string;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  value: number;
  isActive: boolean;
}

interface PaymentMethod {
  id: number;
  name: string;
  description: string | null;
  isActive: boolean;
  requiresCard: boolean;
  icon: string | null;
}

interface PaymentModalProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
}

export default function PaymentModal({ isOpen, onCloseAction, onSuccessAction }: PaymentModalProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [loadingDiscounts, setLoadingDiscounts] = useState(false);
  const [applyDiscount, setApplyDiscount] = useState(false);
  const [originalAmount, setOriginalAmount] = useState('');
  const [formData, setFormData] = useState({
    studentId: '',
    amount: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    paymentMethodId: 'direct', // تعيين طريقة الدفع "مباشر" افتراضياً
    transactionId: '',
    notes: '',
    receiptNumber: '',
    invoiceId: '',
    discountId: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // جلب الطلاب وطرق الدفع والخصومات عند فتح النافذة المنبثقة
  useEffect(() => {
    if (isOpen) {
      fetchStudents();
      fetchPaymentMethods();
      fetchDiscounts();
    }
  }, [isOpen]);

  // جلب طرق الدفع
  const fetchPaymentMethods = async () => {
    try {
      const response = await fetch('/api/payment-methods?activeOnly=true');
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);
      setPaymentMethods(data);
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      toast.error('فشل في جلب طرق الدفع');
    }
  };

  // جلب الخصومات النشطة
  const fetchDiscounts = async () => {
    try {
      setLoadingDiscounts(true);
      const response = await fetch('/api/discounts?activeOnly=true');
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);
      setDiscounts(data.discounts || []);
    } catch (error) {
      console.error('Error fetching discounts:', error);
      toast.error('فشل في جلب الخصومات');
    } finally {
      setLoadingDiscounts(false);
    }
  };

  const fetchStudents = async () => {
    setLoadingStudents(true);
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('Failed to fetch students');
      const data = await response.json();
      setStudents(data.students || []);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('فشل في جلب بيانات الطلاب');
    } finally {
      setLoadingStudents(false);
    }
  };

  // تصفية الطلاب بناءً على البحث
  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // حساب المبلغ بعد الخصم
  const calculateDiscountedAmount = useCallback((originalAmount: string, discountId: string) => {
    if (!applyDiscount || !discountId || !originalAmount) return originalAmount;

    const amount = parseFloat(originalAmount);
    if (isNaN(amount) || amount <= 0) return originalAmount;

    const discount = discounts.find(d => d.id.toString() === discountId);
    if (!discount) return originalAmount;

    let discountedAmount = amount;

    if (discount.type === 'PERCENTAGE') {
      // خصم نسبة مئوية
      discountedAmount = amount - (amount * (discount.value / 100));
    } else if (discount.type === 'FIXED_AMOUNT') {
      // خصم مبلغ ثابت
      discountedAmount = amount - discount.value;
      if (discountedAmount < 0) discountedAmount = 0;
    }

    return discountedAmount.toString();
  }, [applyDiscount, discounts]);

  // تحديث المبلغ عند تغيير الخصم
  useEffect(() => {
    if (applyDiscount && originalAmount) {
      const discountedAmount = calculateDiscountedAmount(originalAmount, formData.discountId);
      setFormData(prev => ({ ...prev, amount: discountedAmount }));
    }
  }, [formData.discountId, applyDiscount, calculateDiscountedAmount, originalAmount]);

  // تحديث المبلغ الأصلي والمبلغ بعد الخصم
  const handleOriginalAmountChange = (value: string) => {
    setOriginalAmount(value);
    if (applyDiscount && formData.discountId) {
      const discountedAmount = calculateDiscountedAmount(value, formData.discountId);
      setFormData(prev => ({ ...prev, amount: discountedAmount }));
    } else {
      setFormData(prev => ({ ...prev, amount: value }));
    }
  };

  // تبديل تطبيق الخصم
  const handleToggleDiscount = (checked: boolean) => {
    setApplyDiscount(checked);
    if (!checked) {
      // إذا تم إلغاء تحديد الخصم، استعادة المبلغ الأصلي
      setFormData(prev => ({ ...prev, amount: originalAmount, discountId: '' }));
    } else if (originalAmount && formData.discountId) {
      // إذا تم تحديد الخصم وكان هناك مبلغ أصلي وخصم محدد، حساب المبلغ بعد الخصم
      const discountedAmount = calculateDiscountedAmount(originalAmount, formData.discountId);
      setFormData(prev => ({ ...prev, amount: discountedAmount }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.studentId || !formData.amount) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) <= 0) {
      toast.error('يرجى إدخال مبلغ صحيح');
      return;
    }

    setIsSubmitting(true);

    try {
      // تحويل معرف الطالب إلى رقم
      const studentIdNumber = parseInt(formData.studentId);

      if (isNaN(studentIdNumber)) {
        throw new Error('معرف الطالب غير صالح');
      }

      // إعداد بيانات الدفعة
      interface PaymentData {
        studentId: number;
        amount: number;
        month: string;
        year: string;
        status: string;
        paymentMethodId?: number;
        isDirectPayment?: boolean;
        transactionId?: string;
        notes?: string;
        receiptNumber?: string;
        invoiceId?: number;
        discountId?: number;
        originalAmount?: number;
      }

      const paymentData: PaymentData = {
        studentId: studentIdNumber,
        amount: parseFloat(formData.amount),
        month: String(formData.month).padStart(2, '0'),
        year: String(formData.year),
        status: 'PAID'
      };

      // إضافة البيانات الاختيارية إذا كانت موجودة
      if (formData.paymentMethodId) {
        if (formData.paymentMethodId === 'direct') {
          // إذا كانت طريقة الدفع "مباشر"، نضيف حقل يشير إلى ذلك
          paymentData.isDirectPayment = true;
        } else if (formData.paymentMethodId !== 'none') {
          // إذا كانت طريقة دفع أخرى، نستخدم معرف طريقة الدفع
          paymentData.paymentMethodId = parseInt(formData.paymentMethodId);
        }
      }

      if (formData.transactionId) {
        paymentData.transactionId = formData.transactionId;
      }

      if (formData.notes) {
        paymentData.notes = formData.notes;
      }

      if (formData.receiptNumber) {
        paymentData.receiptNumber = formData.receiptNumber;
      }

      if (formData.invoiceId) {
        paymentData.invoiceId = parseInt(formData.invoiceId);
      }

      // إضافة معلومات الخصم إذا تم تطبيقه
      if (applyDiscount && formData.discountId) {
        paymentData.discountId = parseInt(formData.discountId);
        paymentData.originalAmount = parseFloat(originalAmount);
      }

      console.log('بيانات الدفعة المرسلة:', paymentData);

      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData)
      });

      // محاولة قراءة الاستجابة كنص أولاً للتشخيص
      const responseText = await response.text();
      console.log('استجابة الخادم:', responseText);

      if (!response.ok) {
        let errorMessage = 'فشل في تسجيل الدفعة';
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          console.error('خطأ في تحليل استجابة الخادم:', e);
        }
        throw new Error(errorMessage);
      }

      toast.success('تم تسجيل الدفعة بنجاح');
      resetForm();
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error saving payment:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الدفعة');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      studentId: '',
      amount: '',
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      paymentMethodId: '',
      transactionId: '',
      notes: '',
      receiptNumber: '',
      invoiceId: '',
      discountId: ''
    });
    setSearchQuery('');
    setOriginalAmount('');
    setApplyDiscount(false);
  };

  // إنشاء قائمة بالأشهر
  const months = [
    { value: 1, label: 'يناير' },
    { value: 2, label: 'فبراير' },
    { value: 3, label: 'مارس' },
    { value: 4, label: 'أبريل' },
    { value: 5, label: 'مايو' },
    { value: 6, label: 'يونيو' },
    { value: 7, label: 'يوليو' },
    { value: 8, label: 'أغسطس' },
    { value: 9, label: 'سبتمبر' },
    { value: 10, label: 'أكتوبر' },
    { value: 11, label: 'نوفمبر' },
    { value: 12, label: 'ديسمبر' }
  ];

  // إنشاء قائمة بالسنوات (السنة الحالية و5 سنوات سابقة و5 سنوات قادمة)
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  const dialogFooter = (
    <>
      <Button type="button" variant="outline" onClick={onCloseAction} disabled={isSubmitting}>
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isSubmitting}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('paymentForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="تسجيل دفعة جديدة"
      variant="primary"
      footer={dialogFooter}
    >
      <form id="paymentForm" onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="studentId" className="text-right col-span-1">
                الطالب <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className="relative">
                  <div className="mb-2">
                    <Input
                      placeholder="ابحث عن الطالب..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <Select
                    value={formData.studentId}
                    onValueChange={(value) => {
                      // العثور على الطالب المحدد لعرض اسمه
                      const selectedStudent = students.find(s => s.id === value);
                      setFormData({ ...formData, studentId: value });
                      // تحديث البحث ليعرض اسم الطالب المحدد
                      if (selectedStudent) {
                        setSearchQuery(selectedStudent.name);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الطالب" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingStudents ? (
                        <div className="text-center py-2">جاري التحميل...</div>
                      ) : filteredStudents.length > 0 ? (
                        filteredStudents.map((student) => (
                          <SelectItem key={student.id} value={student.id}>
                            {student.name}
                          </SelectItem>
                        ))
                      ) : (
                        <div className="text-center py-2">لا يوجد طلاب</div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="originalAmount" className="text-right col-span-1">
                المبلغ <span className="text-red-500">*</span>
              </Label>
              <Input
                id="originalAmount"
                type="number"
                value={originalAmount}
                onChange={(e) => handleOriginalAmountChange(e.target.value)}
                className="col-span-3"
                placeholder="أدخل المبلغ"
                min="0"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="applyDiscount" className="text-right col-span-1">
                تطبيق خصم
              </Label>
              <div className="flex items-center space-x-2 space-x-reverse col-span-3">
                <Checkbox
                  id="applyDiscount"
                  checked={applyDiscount}
                  onCheckedChange={handleToggleDiscount}
                />
                <label
                  htmlFor="applyDiscount"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  تطبيق خصم على المبلغ
                </label>
              </div>
            </div>

            {applyDiscount && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="discountId" className="text-right col-span-1">
                  الخصم
                </Label>
                <div className="col-span-3">
                  <Select
                    value={formData.discountId}
                    onValueChange={(value) => setFormData({ ...formData, discountId: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الخصم" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingDiscounts ? (
                        <div className="text-center py-2">جاري التحميل...</div>
                      ) : discounts.length > 0 ? (
                        discounts.map((discount) => (
                          <SelectItem key={discount.id} value={discount.id.toString()}>
                            {discount.name} ({discount.type === 'PERCENTAGE' ? `${discount.value}%` : `${discount.value} د.ج`})
                          </SelectItem>
                        ))
                      ) : (
                        <div className="text-center py-2">لا توجد خصومات</div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}

            {applyDiscount && formData.discountId && originalAmount && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="amount" className="text-right col-span-1">
                  المبلغ بعد الخصم
                </Label>
                <Input
                  id="amount"
                  type="number"
                  value={formData.amount}
                  readOnly
                  className="col-span-3 bg-gray-100"
                  placeholder="المبلغ بعد الخصم"
                />
              </div>
            )}

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right col-span-1">
                الشهر <span className="text-red-500">*</span>
              </Label>
              <Select
                value={String(formData.month)}
                onValueChange={(value) => setFormData({ ...formData, month: parseInt(value) })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر الشهر" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value.toString()}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right col-span-1">
                السنة <span className="text-red-500">*</span>
              </Label>
              <Select
                value={String(formData.year)}
                onValueChange={(value) => setFormData({ ...formData, year: parseInt(value) })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر السنة" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="paymentMethodId" className="text-right col-span-1">
                طريقة الدفع
              </Label>
              <div className="col-span-3">
                <Select
                  value={formData.paymentMethodId}
                  onValueChange={(value) => setFormData({ ...formData, paymentMethodId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر طريقة الدفع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="direct">مباشر</SelectItem>
                    <SelectItem value="none">بدون طريقة دفع</SelectItem>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method.id} value={method.id.toString()}>
                        {method.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="transactionId" className="text-right col-span-1">
                رقم المعاملة
              </Label>
              <Input
                id="transactionId"
                value={formData.transactionId}
                onChange={(e) => setFormData({ ...formData, transactionId: e.target.value })}
                className="col-span-3"
                placeholder="رقم المعاملة (اختياري)"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="receiptNumber" className="text-right col-span-1">
                رقم الإيصال
              </Label>
              <Input
                id="receiptNumber"
                value={formData.receiptNumber}
                onChange={(e) => setFormData({ ...formData, receiptNumber: e.target.value })}
                className="col-span-3"
                placeholder="رقم الإيصال (اختياري)"
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="notes" className="text-right col-span-1 mt-2">
                ملاحظات
              </Label>
              <textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                className="col-span-3 min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="ملاحظات إضافية (اختياري)"
              />
            </div>
          </div>
        </form>
    </AnimatedDialog>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    const subjects = await prisma.subject.findMany();
    return NextResponse.json(subjects);
  } catch (error: unknown) {
    console.error('Error fetching subjects:', error);
    return NextResponse.json({ error: 'فشل في جلب المواد الدراسية' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'اسم المادة مطلوب' },
        { status: 400 }
      );
    }

    const newSubject = await prisma.subject.create({
      data: { name }
    });

    return NextResponse.json(newSubject);
  } catch (error: unknown) {
    console.error('Error creating subject:', error);
    return NextResponse.json(
      { error: 'فشل في إضافة المادة الدراسية' },
      { status: 500 }
    );
  }
}
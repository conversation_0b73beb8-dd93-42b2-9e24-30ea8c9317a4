'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { ArrowRight, Loader2, BookOpen, Users, CalendarIcon, BarChart, Star, Edit, Trash2 } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useRouter, useParams } from 'next/navigation';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

type KhatmSession = {
  id: number;
  title: string;
  date: string;
  teacher: {
    name: string;
  };
  surah: {
    name: string;
    totalAyahs: number;
  } | null;
};

type Attendance = {
  id: number;
  studentId: number;
  status: string;
  note: string | null;
  student: {
    id: number;
    name: string;
  };
  progressRecords: ProgressRecord[];
};

type ProgressRecord = {
  id: number;
  startAyah: number;
  endAyah: number;
  memorizedAyahs: number;
  reviewedAyahs: number;
  qualityRating: number;
  notes: string | null;
};

export default function KhatmProgressPage() {
  const router = useRouter();
  const params = useParams();
  const sessionId = params?.id ? String(params.id) : '';

  const [session, setSession] = useState<KhatmSession | null>(null);
  const [attendances, setAttendances] = useState<Attendance[]>([]);
  const [loading, setLoading] = useState({
    session: false,
    attendances: false,
    submit: false
  });
  const [formData, setFormData] = useState({
    id: '',
    attendanceId: '',
    startAyah: 1,
    endAyah: 1,
    memorizedAyahs: 0,
    reviewedAyahs: 0,
    qualityRating: 3,
    notes: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [selectedAttendance, setSelectedAttendance] = useState<Attendance | null>(null);

  useEffect(() => {
    fetchSession();
    fetchAttendances();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  const fetchSession = async () => {
    setLoading(prev => ({ ...prev, session: true }));
    try {
      const response = await fetch(`/api/khatm-sessions?id=${sessionId}`);
      if (!response.ok) throw new Error('فشل في جلب معلومات المجلس');
      const data = await response.json();
      if (data.data && data.data.length > 0) {
        setSession(data.data[0]);
      } else {
        throw new Error('مجلس الختم غير موجود');
      }
    } catch (error) {
      console.error('Error fetching khatm session:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب معلومات المجلس',
        variant: 'destructive',
      });
      router.push('/admin/khatm-sessions');
    } finally {
      setLoading(prev => ({ ...prev, session: false }));
    }
  };

  const fetchAttendances = async () => {
    setLoading(prev => ({ ...prev, attendances: true }));
    try {
      const response = await fetch(`/api/khatm-attendance?khatmSessionId=${sessionId}&includeProgress=true`);
      if (!response.ok) throw new Error('فشل في جلب سجلات الحضور');
      const data = await response.json();
      setAttendances(data.data || []);
    } catch (error) {
      console.error('Error fetching attendances:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب سجلات الحضور',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, attendances: false }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: name === 'notes' ? value : parseInt(value) || 0 }));
  };

  const resetForm = () => {
    setFormData({
      id: '',
      attendanceId: '',
      startAyah: 1,
      endAyah: 1,
      memorizedAyahs: 0,
      reviewedAyahs: 0,
      qualityRating: 3,
      notes: ''
    });
    setIsEditing(false);
    setSelectedAttendance(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(prev => ({ ...prev, submit: true }));

    try {
      const payload = {
        ...formData,
        khatmSessionId: sessionId
      };

      if (!payload.attendanceId) {
        throw new Error('يرجى اختيار الطالب');
      }

      const url = '/api/khatm-progress';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: isEditing ? 'تم تحديث سجل التقدم بنجاح' : 'تم تسجيل التقدم بنجاح',
        });
        resetForm();
        setShowForm(false);
        fetchAttendances();
      } else {
        throw new Error(result.error || 'حدث خطأ أثناء حفظ سجل التقدم');
      }
    } catch (error) {
      console.error('Error saving progress:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ سجل التقدم',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, submit: false }));
    }
  };

  const handleEdit = (attendance: Attendance, progress: ProgressRecord) => {
    setFormData({
      id: progress.id.toString(),
      attendanceId: attendance.id.toString(),
      startAyah: progress.startAyah,
      endAyah: progress.endAyah,
      memorizedAyahs: progress.memorizedAyahs,
      reviewedAyahs: progress.reviewedAyahs,
      qualityRating: progress.qualityRating,
      notes: progress.notes || ''
    });
    setSelectedAttendance(attendance);
    setIsEditing(true);
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف سجل التقدم هذا؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/khatm-progress?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم حذف سجل التقدم بنجاح',
        });
        fetchAttendances();
      } else {
        const result = await response.json();
        throw new Error(result.error || 'حدث خطأ أثناء حذف سجل التقدم');
      }
    } catch (error) {
      console.error('Error deleting progress:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حذف سجل التقدم',
        variant: 'destructive',
      });
    }
  };

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <Star key={i} className={`h-4 w-4 ${i < rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`} />
    ));
  };

  const addProgressForAttendance = (attendance: Attendance) => {
    setFormData(prev => ({
      ...prev,
      attendanceId: attendance.id.toString(),
      startAyah: 1,
      endAyah: session?.surah?.totalAyahs || 1
    }));
    setSelectedAttendance(attendance);
    setIsEditing(false);
    setShowForm(true);
  };

  return (
    <div className="container mx-auto py-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <Card className="shadow-md border-t-4 border-t-[var(--primary-color)]">
        <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-[#f8fffd] to-white">
          <div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/admin/khatm-sessions')}
              className="mb-2 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)]/10 text-xs md:text-sm"
            >
              <ArrowRight className="ml-1 md:ml-2 h-3 w-3 md:h-4 md:w-4" />
              <span className="hidden sm:inline">العودة إلى مجالس الختم</span>
              <span className="sm:hidden">العودة</span>
            </Button>
            <CardTitle className="text-2xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">
              {loading.session ? 'جاري التحميل...' : session ? `متابعة تقدم الحفظ: ${session.title}` : 'مجلس الختم'}
            </CardTitle>
            {session && (
              <div className="text-sm text-muted-foreground mt-2 mr-3 flex flex-wrap gap-4">
                <span className="flex items-center">
                  <CalendarIcon className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                  التاريخ: {format(new Date(session.date), 'PPP', { locale: ar })}
                </span>
                <span className="flex items-center">
                  <Users className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                  المعلم: {session.teacher.name}
                </span>
                {session.surah && (
                  <span className="flex items-center">
                    <BookOpen className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                    السورة: {session.surah.name} ({session.surah.totalAyahs} آية)
                  </span>
                )}
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {showForm && (
            <form onSubmit={handleSubmit} className="space-y-4 mb-6 p-6 border rounded-lg bg-[#f8fffd] shadow-sm">
              <h3 className="text-xl font-bold text-[var(--primary-color)] mb-4">
                {isEditing ? 'تعديل سجل تقدم الحفظ' : 'تسجيل تقدم الحفظ الجديد'}
              </h3>
              {selectedAttendance && (
                <div className="p-3 bg-blue-50 rounded-md mb-4">
                  <p className="font-semibold">الطالب: {selectedAttendance.student.name}</p>
                </div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startAyah">من الآية</Label>
                  <Input
                    id="startAyah"
                    name="startAyah"
                    type="number"
                    min="1"
                    max={session?.surah?.totalAyahs || 286}
                    value={formData.startAyah}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endAyah">إلى الآية</Label>
                  <Input
                    id="endAyah"
                    name="endAyah"
                    type="number"
                    min={formData.startAyah}
                    max={session?.surah?.totalAyahs || 286}
                    value={formData.endAyah}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="memorizedAyahs">عدد الآيات المحفوظة</Label>
                  <Input
                    id="memorizedAyahs"
                    name="memorizedAyahs"
                    type="number"
                    min="0"
                    max={formData.endAyah - formData.startAyah + 1}
                    value={formData.memorizedAyahs}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="reviewedAyahs">عدد الآيات المراجعة</Label>
                  <Input
                    id="reviewedAyahs"
                    name="reviewedAyahs"
                    type="number"
                    min="0"
                    max={formData.endAyah - formData.startAyah + 1}
                    value={formData.reviewedAyahs}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="qualityRating">تقييم جودة الحفظ (من 1 إلى 5)</Label>
                  <Input
                    id="qualityRating"
                    name="qualityRating"
                    type="number"
                    min="1"
                    max="5"
                    value={formData.qualityRating}
                    onChange={handleInputChange}
                    required
                  />
                  <div className="flex mt-1">
                    {renderStars(formData.qualityRating)}
                  </div>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="notes">ملاحظات</Label>
                  <Textarea
                    id="notes"
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={2}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => { setShowForm(false); resetForm(); }}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={loading.submit}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  {loading.submit ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : isEditing ? 'تحديث' : 'تسجيل'}
                </Button>
              </div>
            </form>
          )}

          {loading.attendances ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-[var(--primary-color)]" />
              <p className="mt-2 text-muted-foreground">جاري تحميل سجلات الحضور...</p>
            </div>
          ) : attendances.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <div className="flex flex-col items-center justify-center">
                <Users className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-1">لا توجد سجلات حضور لهذا المجلس</h3>
                <p className="text-sm text-gray-500 mb-4">قم بتسجيل حضور الطلاب أولاً</p>
                <Button
                  onClick={() => router.push(`/admin/khatm-attendance/${sessionId}`)}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  <Users className="ml-2 h-4 w-4" />
                  الذهاب إلى تسجيل الحضور
                </Button>
              </div>
            </div>
          ) : (
            <div>
              {attendances.map(attendance => (
                <div key={attendance.id} className="mb-8 border rounded-lg overflow-hidden">
                  <div className="bg-[var(--primary-color)]/10 p-4 flex justify-between items-center">
                    <h3 className="text-lg font-bold">{attendance.student.name}</h3>
                    <Button
                      onClick={() => addProgressForAttendance(attendance)}
                      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-xs md:text-sm"
                    >
                      <BarChart className="ml-1 md:ml-2 h-3 w-3 md:h-4 md:w-4" />
                      <span className="hidden sm:inline">إضافة تقدم جديد</span>
                      <span className="sm:hidden">إضافة</span>
                    </Button>
                  </div>

                  {attendance.progressRecords && attendance.progressRecords.length > 0 ? (
                    <Table>
                      <TableHeader className="bg-gray-50">
                        <TableRow>
                          <TableHead className="px-2 md:px-4">من</TableHead>
                          <TableHead className="px-2 md:px-4">إلى</TableHead>
                          <TableHead className="px-2 md:px-4">محفوظة</TableHead>
                          <TableHead className="px-2 md:px-4">مراجعة</TableHead>
                          <TableHead className="px-2 md:px-4">تقييم</TableHead>
                          <TableHead className="hidden md:table-cell">ملاحظات</TableHead>
                          <TableHead className="px-2 md:px-4">إجراءات</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {attendance.progressRecords.map(progress => (
                          <TableRow key={progress.id}>
                            <TableCell className="px-2 md:px-4">{progress.startAyah}</TableCell>
                            <TableCell className="px-2 md:px-4">{progress.endAyah}</TableCell>
                            <TableCell className="px-2 md:px-4">{progress.memorizedAyahs}</TableCell>
                            <TableCell className="px-2 md:px-4">{progress.reviewedAyahs}</TableCell>
                            <TableCell className="px-2 md:px-4">
                              <div className="flex">
                                {renderStars(progress.qualityRating)}
                              </div>
                            </TableCell>
                            <TableCell className="hidden md:table-cell">{progress.notes || '-'}</TableCell>
                            <TableCell className="px-2 md:px-4">
                              <div className="flex gap-1 md:gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEdit(attendance, progress)}
                                  className="text-blue-600 border-blue-600 hover:bg-blue-50 px-2 md:px-3"
                                  title="تعديل سجل التقدم"
                                >
                                  <Edit className="h-3 w-3 md:h-4 md:w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDelete(progress.id)}
                                  className="text-red-600 border-red-600 hover:bg-red-50 px-2 md:px-3"
                                  title="حذف سجل التقدم"
                                >
                                  <Trash2 className="h-3 w-3 md:h-4 md:w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="p-6 text-center text-gray-500">
                      لا توجد سجلات تقدم لهذا الطالب
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

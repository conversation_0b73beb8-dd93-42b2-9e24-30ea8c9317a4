'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { MultiSelect, Option } from '@/components/ui/multi-select'
import { Button } from '@/components/ui/button'
import { ChevronDown } from 'lucide-react'

export default function TestDropdownsPage() {
  const [selectedValue, setSelectedValue] = useState('')
  const [multiSelectValues, setMultiSelectValues] = useState<string[]>([])

  const options: Option[] = [
    { value: 'option1', label: 'الخيار الأول' },
    { value: 'option2', label: 'الخيار الثاني' },
    { value: 'option3', label: 'الخيار الثالث' },
    { value: 'option4', label: 'الخيار الرابع' },
    { value: 'option5', label: 'الخيار الخامس' },
  ]

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] mb-2">
          اختبار تحسينات القوائم المنسدلة
        </h1>
        <p className="text-gray-600">
          هذه الصفحة لاختبار التحسينات الجديدة على مظهر القوائم المنسدلة
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* اختبار Select العادي */}
        <Card>
          <CardHeader>
            <CardTitle className="text-[var(--primary-color)]">قائمة منسدلة عادية</CardTitle>
          </CardHeader>
          <CardContent>
            <Select value={selectedValue} onValueChange={setSelectedValue}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="اختر خياراً" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="item1">العنصر الأول</SelectItem>
                <SelectItem value="item2">العنصر الثاني</SelectItem>
                <SelectItem value="item3">العنصر الثالث</SelectItem>
                <SelectItem value="item4">العنصر الرابع</SelectItem>
                <SelectItem value="item5">العنصر الخامس</SelectItem>
                <SelectItem value="item6">العنصر السادس</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500 mt-2">
              القيمة المحددة: {selectedValue || 'لا يوجد'}
            </p>
          </CardContent>
        </Card>

        {/* اختبار DropdownMenu */}
        <Card>
          <CardHeader>
            <CardTitle className="text-[var(--primary-color)]">قائمة منسدلة للإجراءات</CardTitle>
          </CardHeader>
          <CardContent>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full">
                  الإجراءات
                  <ChevronDown className="h-4 w-4 mr-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem>عرض التفاصيل</DropdownMenuItem>
                <DropdownMenuItem>تعديل</DropdownMenuItem>
                <DropdownMenuItem>نسخ</DropdownMenuItem>
                <DropdownMenuItem>مشاركة</DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">حذف</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardContent>
        </Card>

        {/* اختبار MultiSelect */}
        <Card>
          <CardHeader>
            <CardTitle className="text-[var(--primary-color)]">قائمة متعددة الاختيار</CardTitle>
          </CardHeader>
          <CardContent>
            <MultiSelect
              options={options}
              value={multiSelectValues}
              onChangeAction={setMultiSelectValues}
              placeholder="اختر عدة خيارات"
            />
            <p className="text-sm text-gray-500 mt-2">
              عدد العناصر المحددة: {multiSelectValues.length}
            </p>
          </CardContent>
        </Card>

        {/* اختبار Select مع خيارات كثيرة */}
        <Card>
          <CardHeader>
            <CardTitle className="text-[var(--primary-color)]">قائمة بخيارات كثيرة</CardTitle>
          </CardHeader>
          <CardContent>
            <Select>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="اختر من قائمة طويلة" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: 20 }, (_, i) => (
                  <SelectItem key={i} value={`item-${i + 1}`}>
                    العنصر رقم {i + 1}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* اختبار Select مع خيارات معطلة */}
        <Card>
          <CardHeader>
            <CardTitle className="text-[var(--primary-color)]">قائمة مع خيارات معطلة</CardTitle>
          </CardHeader>
          <CardContent>
            <Select>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="اختر خياراً" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="enabled1">خيار متاح 1</SelectItem>
                <SelectItem value="disabled1" disabled>خيار معطل 1</SelectItem>
                <SelectItem value="enabled2">خيار متاح 2</SelectItem>
                <SelectItem value="disabled2" disabled>خيار معطل 2</SelectItem>
                <SelectItem value="enabled3">خيار متاح 3</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* اختبار DropdownMenu مع فواصل */}
        <Card>
          <CardHeader>
            <CardTitle className="text-[var(--primary-color)]">قائمة مع فواصل</CardTitle>
          </CardHeader>
          <CardContent>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full">
                  المزيد من الخيارات
                  <ChevronDown className="h-4 w-4 mr-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem>إنشاء جديد</DropdownMenuItem>
                <DropdownMenuItem>فتح</DropdownMenuItem>
                <DropdownMenuItem>حفظ</DropdownMenuItem>
                <div className="border-t border-gray-200 my-1"></div>
                <DropdownMenuItem>تصدير</DropdownMenuItem>
                <DropdownMenuItem>طباعة</DropdownMenuItem>
                <div className="border-t border-gray-200 my-1"></div>
                <DropdownMenuItem>إعدادات</DropdownMenuItem>
                <DropdownMenuItem>مساعدة</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </CardContent>
        </Card>
      </div>

      <div className="text-center text-sm text-gray-500 mt-8">
        <p>
          جرب فتح القوائم المنسدلة لرؤية التحسينات الجديدة:
        </p>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>خطوط فاصلة بين العناصر</li>
          <li>تمييز أفضل للعناصر المحددة</li>
          <li>تأثيرات انتقالية سلسة</li>
          <li>ألوان متناسقة مع نظام الألوان</li>
          <li>تحسين إمكانية الوصول</li>
        </ul>
      </div>
    </div>
  )
}

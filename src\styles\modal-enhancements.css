/*
  تحسينات مظهر النوافذ المنبثقة والحوارات
  هذا الملف يحتوي على تحسينات CSS لمظهر النوافذ المنبثقة وطبقات الخلفية
*/

/* تحسين طبقة الخلفية العامة */
[data-radix-dialog-overlay],
[data-radix-alert-dialog-overlay],
.dialog-overlay,
.modal-overlay {
  background: rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  transition: all 0.3s ease-in-out !important;
}

/* تحسين مظهر النوافذ المنبثقة */
[data-radix-dialog-content],
[data-radix-alert-dialog-content],
.dialog-content,
.modal-content {
  background: var(--background-color, #f8f9fa) !important;
  color: var(--text-color, #111827) !important;
  border: 1px solid var(--border-primary, #e5e7eb) !important;
  border-radius: 12px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
}

/* تحسين الانتقالات */
[data-state="open"] {
  animation: modalShow 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

[data-state="closed"] {
  animation: modalHide 0.2s cubic-bezier(0.16, 1, 0.3, 1) !important;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes modalHide {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.95);
  }
}

/* تحسين مظهر النوافذ على الأجهزة المحمولة */
@media (max-width: 768px) {
  [data-radix-dialog-content],
  [data-radix-alert-dialog-content],
  .dialog-content,
  .modal-content {
    width: 95vw !important;
    max-width: 95vw !important;
    margin: 1rem !important;
    max-height: 85vh !important;
  }

  [data-radix-dialog-overlay],
  [data-radix-alert-dialog-overlay],
  .dialog-overlay,
  .modal-overlay {
    background: rgba(0, 0, 0, 0.15) !important;
  }
}

/* تحسين مظهر النوافذ في الوضع المظلم */
.dark-mode [data-radix-dialog-content],
.dark [data-radix-dialog-content],
.dark-mode [data-radix-alert-dialog-content],
.dark [data-radix-alert-dialog-content],
.dark-mode .dialog-content,
.dark .dialog-content,
.dark-mode .modal-content,
.dark .modal-content {
  background: var(--background-color, #1f2937) !important;
  border-color: var(--border-primary, #374151) !important;
  color: var(--text-color, #f9fafb) !important;
}

.dark-mode [data-radix-dialog-overlay],
.dark [data-radix-dialog-overlay],
.dark-mode [data-radix-alert-dialog-overlay],
.dark [data-radix-alert-dialog-overlay],
.dark-mode .dialog-overlay,
.dark .dialog-overlay,
.dark-mode .modal-overlay,
.dark .modal-overlay {
  background: rgba(0, 0, 0, 0.3) !important;
}

/* تحسين مظهر العناوين والمحتوى */
.dialog-title,
.modal-title,
[data-radix-dialog-title],
[data-radix-alert-dialog-title] {
  color: var(--primary-color, #22c55e) !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
}

.dialog-description,
.modal-description,
[data-radix-dialog-description],
[data-radix-alert-dialog-description] {
  color: #6b7280 !important;
  margin-bottom: 1.5rem !important;
}

/* تحسين مظهر Labels في النوافذ المنبثقة - قواعد أكثر تحديداً */
[data-radix-dialog-content] label,
[data-radix-alert-dialog-content] label,
.dialog-content label,
.modal-content label,
[data-radix-dialog-content] [data-radix-label-root],
[data-radix-alert-dialog-content] [data-radix-label-root],
.dialog-content [data-radix-label-root],
.modal-content [data-radix-label-root] {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  line-height: 1.25rem !important;
}

/* إصلاح محدد لمكون Label من Radix UI */
.dialog-content .text-gray-900,
.modal-content .text-gray-900,
[data-radix-dialog-content] .text-gray-900,
[data-radix-alert-dialog-content] .text-gray-900 {
  color: #1f2937 !important;
}

/* إصلاح ألوان النصوص في النوافذ المنبثقة */
[data-radix-dialog-content] *,
[data-radix-alert-dialog-content] *,
.dialog-content *,
.modal-content * {
  color: #1f2937 !important;
}

/* إصلاح ألوان النصوص الفرعية */
[data-radix-dialog-content] .text-gray-500,
[data-radix-alert-dialog-content] .text-gray-500,
.dialog-content .text-gray-500,
.modal-content .text-gray-500,
[data-radix-dialog-content] .text-muted-foreground,
[data-radix-alert-dialog-content] .text-muted-foreground,
.dialog-content .text-muted-foreground,
.modal-content .text-muted-foreground {
  color: #6b7280 !important;
}

/* إصلاح ألوان placeholder */
[data-radix-dialog-content] input::placeholder,
[data-radix-alert-dialog-content] input::placeholder,
.dialog-content input::placeholder,
.modal-content input::placeholder,
[data-radix-dialog-content] textarea::placeholder,
[data-radix-alert-dialog-content] textarea::placeholder,
.dialog-content textarea::placeholder,
.modal-content textarea::placeholder {
  color: #9ca3af !important;
}

/* تحسين مظهر Labels المطلوبة */
[data-radix-dialog-content] label .text-red-500,
[data-radix-alert-dialog-content] label .text-red-500,
.dialog-content label .text-red-500,
.modal-content label .text-red-500 {
  color: #ef4444 !important;
  font-weight: 600 !important;
}

/* تحسين مظهر Labels في الوضع المظلم */
@media (prefers-color-scheme: dark) {
  [data-radix-dialog-content] label,
  [data-radix-alert-dialog-content] label,
  .dialog-content label,
  .modal-content label {
    color: #f9fafb !important;
  }
}

/* تحسين مظهر الأزرار في النوافذ */
.dialog-footer,
.modal-footer {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 0.75rem !important;
  margin-top: 1.5rem !important;
  padding-top: 1rem !important;
  border-top: 1px solid #e5e7eb !important;
}

.dialog-footer button,
.modal-footer button {
  transition: all 0.2s ease !important;
}

/* تحسين مظهر النوافذ الصغيرة */
.dialog-sm,
.modal-sm {
  max-width: 400px !important;
}

.dialog-md,
.modal-md {
  max-width: 500px !important;
}

.dialog-lg,
.modal-lg {
  max-width: 700px !important;
}

.dialog-xl,
.modal-xl {
  max-width: 900px !important;
}

/* تحسين مظهر النوافذ ذات المحتوى الطويل */
.dialog-scrollable,
.modal-scrollable {
  max-height: 80vh !important;
  overflow-y: auto !important;
}

.dialog-scrollable .dialog-body,
.modal-scrollable .modal-body {
  max-height: 60vh !important;
  overflow-y: auto !important;
  padding-right: 0.5rem !important;
}

/* تحسين شريط التمرير */
.dialog-scrollable .dialog-body::-webkit-scrollbar,
.modal-scrollable .modal-body::-webkit-scrollbar {
  width: 6px !important;
}

.dialog-scrollable .dialog-body::-webkit-scrollbar-track,
.modal-scrollable .modal-body::-webkit-scrollbar-track {
  background: #f1f5f9 !important;
  border-radius: 3px !important;
}

.dialog-scrollable .dialog-body::-webkit-scrollbar-thumb,
.modal-scrollable .modal-body::-webkit-scrollbar-thumb {
  background: #cbd5e1 !important;
  border-radius: 3px !important;
}

.dialog-scrollable .dialog-body::-webkit-scrollbar-thumb:hover,
.modal-scrollable .modal-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8 !important;
}

/* تحسين إمكانية الوصول */
[data-radix-dialog-content]:focus,
[data-radix-alert-dialog-content]:focus,
.dialog-content:focus,
.modal-content:focus {
  outline: 2px solid var(--primary-color, #22c55e) !important;
  outline-offset: 2px !important;
}

/* تحسين مظهر النوافذ التحذيرية */
.dialog-warning,
.modal-warning {
  border-left: 4px solid #f59e0b !important;
}

.dialog-error,
.modal-error {
  border-left: 4px solid #ef4444 !important;
}

.dialog-success,
.modal-success {
  border-left: 4px solid var(--primary-color, #22c55e) !important;
}

.dialog-info,
.modal-info {
  border-left: 4px solid #3b82f6 !important;
}

/* تحسين مظهر النوافذ بدون خلفية مظلمة */
.dialog-no-overlay,
.modal-no-overlay {
  background: transparent !important;
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

/* تحسين مظهر النوافذ شبه الشفافة */
.dialog-transparent,
.modal-transparent {
  background: rgba(248, 249, 250, 0.95) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

/* تحسين مظهر النوافذ في وضع ملء الشاشة */
.dialog-fullscreen,
.modal-fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  border-radius: 0 !important;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
}

/* تحسين مظهر النوافذ المتداخلة */
.dialog-nested,
.modal-nested {
  z-index: 60 !important;
}

.dialog-nested [data-radix-dialog-overlay],
.modal-nested .modal-overlay {
  background: rgba(0, 0, 0, 0.05) !important;
}

/* تحسين الحركات للنوافذ المختلفة */
.dialog-slide-up {
  animation: slideUp 0.3s ease-out !important;
}

.dialog-slide-down {
  animation: slideDown 0.3s ease-out !important;
}

.dialog-fade-in {
  animation: fadeIn 0.3s ease-out !important;
}

@keyframes slideUp {
  from {
    transform: translate(-50%, 100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* تحسين مظهر العناصر داخل النوافذ المنبثقة */

/* تحسين مظهر الحقول والمدخلات */
[data-radix-dialog-content] input,
[data-radix-alert-dialog-content] input,
.dialog-content input,
.modal-content input {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  transition: border-color 0.2s ease !important;
  color: #1f2937 !important;
  background-color: white !important;
}

/* تحسين مظهر textarea */
[data-radix-dialog-content] textarea,
[data-radix-alert-dialog-content] textarea,
.dialog-content textarea,
.modal-content textarea {
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  transition: border-color 0.2s ease !important;
  color: #1f2937 !important;
  background-color: white !important;
}

[data-radix-dialog-content] input:focus,
[data-radix-alert-dialog-content] input:focus,
.dialog-content input:focus,
.modal-content input:focus,
[data-radix-dialog-content] textarea:focus,
[data-radix-alert-dialog-content] textarea:focus,
.dialog-content textarea:focus,
.modal-content textarea:focus {
  border-color: var(--primary-color, #22c55e) !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1) !important;
}

/* إصلاح ألوان القوائم المنسدلة في النوافذ المنبثقة */
[data-radix-dialog-content] [data-radix-select-trigger],
[data-radix-alert-dialog-content] [data-radix-select-trigger],
.dialog-content [data-radix-select-trigger],
.modal-content [data-radix-select-trigger] {
  color: #1f2937 !important;
  background-color: white !important;
  border: 1px solid #d1d5db !important;
}

[data-radix-dialog-content] [data-radix-select-value],
[data-radix-alert-dialog-content] [data-radix-select-value],
.dialog-content [data-radix-select-value],
.modal-content [data-radix-select-value] {
  color: #1f2937 !important;
}

/* إصلاح ألوان الأزرار في النوافذ المنبثقة */
[data-radix-dialog-content] button,
[data-radix-alert-dialog-content] button,
.dialog-content button,
.modal-content button {
  color: inherit !important;
}

/* تحسين مظهر الفواصل والحدود */
[data-radix-dialog-content] hr,
[data-radix-alert-dialog-content] hr,
.dialog-content hr,
.modal-content hr,
[data-radix-dialog-content] .border-t,
[data-radix-alert-dialog-content] .border-t,
.dialog-content .border-t,
.modal-content .border-t {
  border-color: #e5e7eb !important;
  margin: 1rem 0 !important;
}

/* تحسين مظهر الجداول في النوافذ */
[data-radix-dialog-content] table,
[data-radix-alert-dialog-content] table,
.dialog-content table,
.modal-content table {
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  overflow: hidden !important;
}

[data-radix-dialog-content] table th,
[data-radix-alert-dialog-content] table th,
.dialog-content table th,
.modal-content table th {
  background-color: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 0.75rem !important;
  font-weight: 600 !important;
  color: #374151 !important;
}

[data-radix-dialog-content] table td,
[data-radix-alert-dialog-content] table td,
.dialog-content table td,
.modal-content table td {
  border-bottom: 1px solid #f3f4f6 !important;
  padding: 0.75rem !important;
  color: #1f2937 !important;
}

/* تحسين مظهر البطاقات في النوافذ */
[data-radix-dialog-content] .card,
[data-radix-alert-dialog-content] .card,
.dialog-content .card,
.modal-content .card {
  border: 1px solid var(--border-primary, #e5e7eb) !important;
  border-radius: 8px !important;
  background: var(--bg-secondary, #f9fafb) !important;
  color: var(--text-color, #111827) !important;
  padding: 1rem !important;
  margin: 0.5rem 0 !important;
}

/* تحسين مظهر النصوص والفقرات */
[data-radix-dialog-content] p,
[data-radix-alert-dialog-content] p,
.dialog-content p,
.modal-content p {
  color: var(--text-color, #374151) !important;
  line-height: 1.5 !important;
  margin-bottom: 0.75rem !important;
}

/* تحسين مظهر القوائم */
[data-radix-dialog-content] ul,
[data-radix-alert-dialog-content] ul,
.dialog-content ul,
.modal-content ul,
[data-radix-dialog-content] ol,
[data-radix-alert-dialog-content] ol,
.dialog-content ol,
.modal-content ol {
  padding-right: 1.5rem !important;
  margin-bottom: 1rem !important;
}

[data-radix-dialog-content] li,
[data-radix-alert-dialog-content] li,
.dialog-content li,
.modal-content li {
  color: var(--text-color, #374151) !important;
  margin-bottom: 0.25rem !important;
}

/* تحسين مظهر الروابط */
[data-radix-dialog-content] a,
[data-radix-alert-dialog-content] a,
.dialog-content a,
.modal-content a {
  color: var(--primary-color, #22c55e) !important;
  text-decoration: underline !important;
  transition: color 0.2s ease !important;
}

[data-radix-dialog-content] a:hover,
[data-radix-alert-dialog-content] a:hover,
.dialog-content a:hover,
.modal-content a:hover {
  color: #16a34a !important;
}

/* تحسين مظهر الكود */
[data-radix-dialog-content] code,
[data-radix-alert-dialog-content] code,
.dialog-content code,
.modal-content code {
  background: #f3f4f6 !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 4px !important;
  padding: 0.125rem 0.25rem !important;
  font-family: monospace !important;
  font-size: 0.875rem !important;
  color: #1f2937 !important;
}

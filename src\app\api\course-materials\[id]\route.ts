import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { verifyToken } from '@/utils/verifyToken';

// GET /api/course-materials/[id] - جلب المواد التعليمية لمقرر معين
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من المصادقة
    const userData = await verifyToken(request);
    if (!userData) {
      return NextResponse.json(
        { success: false, message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // التحقق من وجود معرف المقرر
    const courseId = parseInt(params.id);
    if (isNaN(courseId)) {
      return NextResponse.json(
        { success: false, message: "معرف المقرر غير صالح" },
        { status: 400 }
      );
    }

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userData.id
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // التحقق من أن المقرر ينتمي للمعلم
    const classSubject = await prisma.classSubject.findUnique({
      where: {
        id: courseId
      },
      include: {
        teacherSubject: true
      }
    });

    if (!classSubject) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على المقرر" },
        { status: 404 }
      );
    }

    if (classSubject.teacherSubject.teacherId !== teacher.id) {
      return NextResponse.json(
        { success: false, message: "غير مصرح لك بالوصول إلى هذا المقرر" },
        { status: 403 }
      );
    }

    // جلب المواد التعليمية للمقرر
    const materials = await prisma.courseMaterial.findMany({
      where: {
        classSubjectId: courseId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // تنسيق البيانات للعرض
    const formattedMaterials = materials.map(material => ({
      id: material.id,
      title: material.title,
      description: material.description || '',
      type: material.type,
      url: material.url,
      createdAt: material.createdAt.toISOString().split('T')[0]
    }));

    return NextResponse.json({
      success: true,
      materials: formattedMaterials,
      message: "تم جلب المواد التعليمية بنجاح"
    });
  } catch (error) {
    console.error('Error fetching course materials:', error);
    return NextResponse.json(
      { success: false, message: "حدث خطأ أثناء جلب المواد التعليمية" },
      { status: 500 }
    );
  }
}

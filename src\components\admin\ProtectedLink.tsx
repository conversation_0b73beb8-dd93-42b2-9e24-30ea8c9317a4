'use client';
import React from 'react';
import Link from 'next/link';
import { useUserPermissions } from '@/hooks/useUserPermissions';

interface ProtectedLinkProps {
  href: string;
  requiredPermission: string;
  children: React.ReactNode;
  className?: string;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * مكون Link محمي بالصلاحيات
 * يعرض الرابط فقط إذا كان المستخدم لديه الصلاحية المطلوبة
 */
const ProtectedLink: React.FC<ProtectedLinkProps> = ({
  href,
  requiredPermission,
  children,
  className = '',
  fallback = null,
  showFallback = false
}) => {
  const { hasPermission, userRole, loading } = useUserPermissions();

  // أثناء التحميل، لا نعرض شيء
  if (loading) {
    return null;
  }

  // المدير لديه جميع الصلاحيات
  if (userRole === 'ADMIN') {
    return (
      <Link href={href} className={className}>
        {children}
      </Link>
    );
  }

  // التحقق من الصلاحية
  const hasRequiredPermission = hasPermission(requiredPermission);

  if (hasRequiredPermission) {
    return (
      <Link href={href} className={className}>
        {children}
      </Link>
    );
  }

  // إذا لم يكن لديه صلاحية وطُلب عرض fallback
  if (showFallback && fallback) {
    return <>{fallback}</>;
  }

  // لا يعرض شيء إذا لم يكن لديه صلاحية
  return null;
};

export default ProtectedLink;

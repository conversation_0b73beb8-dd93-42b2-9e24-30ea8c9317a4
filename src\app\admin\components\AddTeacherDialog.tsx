'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import { toast } from 'react-toastify';

interface Subject {
  id: number;
  name: string;
}

interface AddTeacherDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
}

export default function AddTeacherDialog({ isOpen, onCloseAction, onSuccessAction }: AddTeacherDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    password: '',
    phone: '',
    specialization: '',
    subjects: [] as number[]
  });
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      fetchSubjects();
    }
  }, [isOpen]);

  const fetchSubjects = async () => {
    try {
      const response = await fetch('/api/admin/subjects');
      if (!response.ok) throw new Error('Failed to fetch subjects');
      const data = await response.json();
      setSubjects(data);
    } catch (err: unknown) {
      setError('Failed to load subjects');
      console.error(err);
    }
  };

  const handleSubjectToggle = (subjectId: number) => {
    setFormData(prev => {
      const isSelected = prev.subjects.includes(subjectId);
      if (isSelected) {
        return { ...prev, subjects: prev.subjects.filter(id => id !== subjectId) };
      } else {
        return { ...prev, subjects: [...prev.subjects, subjectId] };
      }
    });
  };

  const resetForm = () => {
    setFormData({
      name: '',
      username: '',
      password: '',
      phone: '',
      specialization: '',
      subjects: []
    });
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/teachers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ أثناء إضافة المعلم');
      }

      toast.success('تم إضافة المعلم بنجاح');
      resetForm();
      onSuccessAction();
      onCloseAction();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء إضافة المعلم';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <>
      <Button type="button" variant="outline" onClick={onCloseAction} disabled={isLoading}>
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isLoading}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('addTeacherForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isLoading ? 'جاري الإضافة...' : 'إضافة'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إضافة معلم جديد"
      variant="primary"
      footer={dialogFooter}
    >
      <form id="addTeacherForm" onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="text-red-600 text-sm text-center">{error}</div>
          )}
          <div className="space-y-2">
            <Input
              required
              placeholder="اسم المعلم"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            />
            <Input
              required
              placeholder="اسم المستخدم"
              value={formData.username}
              onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
            />
            <Input
              required
              type="password"
              placeholder="كلمة المرور"
              value={formData.password}
              onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
            />
            <div className="space-y-2">
              <label className="text-sm font-medium text-right block">المواد</label>
              <div className="grid grid-cols-2 gap-2 rtl">
                {subjects.map(subject => (
                  <label key={subject.id} className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-2 rounded">
                    <input
                      type="checkbox"
                      className="rounded text-primary"
                      checked={formData.subjects.includes(subject.id)}
                      onChange={() => handleSubjectToggle(subject.id)}
                    />
                    <span>{subject.name}</span>
                  </label>
                ))}
              </div>
            </div>
            <Input
              required
              placeholder="رقم الهاتف"
              value={formData.phone}
              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            />
            <Input
              required
              placeholder="التخصص"
              value={formData.specialization}
              onChange={(e) => setFormData(prev => ({ ...prev, specialization: e.target.value }))}
            />
          </div>
        </form>
    </AnimatedDialog>
  );
}

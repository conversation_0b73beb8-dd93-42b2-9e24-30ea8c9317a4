'use client';

import React, { useState, useEffect, useRef } from 'react';
import { FaArrowRight, FaVideo, FaMicrophone, FaNetworkWired } from 'react-icons/fa';
import Link from 'next/link';
import VideoSettings from '@/components/remote-classes/VideoAudio/VideoSettings';
import AudioSettings from '@/components/remote-classes/VideoAudio/AudioSettings';
import { AdaptiveBitrateManager } from '@/components/remote-classes/VideoAudio/AdaptiveBitrateManager';
import { BandwidthOptimizer } from '@/components/remote-classes/VideoAudio/BandwidthOptimizer';

/**
 * Test page for video and audio quality improvements
 */
const VideoAudioTestPage: React.FC = () => {
  // Video state
  const [isVideoEnabled, setIsVideoEnabled] = useState(false);
  const [videoQuality, setVideoQuality] = useState<'low' | 'medium' | 'high'>('medium');
  const [videoDeviceId, setVideoDeviceId] = useState('');
  const [videoStream, setVideoStream] = useState<MediaStream | null>(null);
  const [useAdaptiveBitrate, setUseAdaptiveBitrate] = useState(false);
  
  // Audio state
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [audioQuality, setAudioQuality] = useState<'low' | 'medium' | 'high'>('medium');
  const [audioDeviceId, setAudioDeviceId] = useState('');
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [volume, setVolume] = useState(1.0);
  
  // Network simulation
  const [networkCondition, setNetworkCondition] = useState<'good' | 'fair' | 'poor'>('good');
  const [simulatedBandwidth, setSimulatedBandwidth] = useState(2000); // kbps
  
  // References
  const videoRef = useRef<HTMLVideoElement>(null);
  const bandwidthOptimizerRef = useRef<BandwidthOptimizer | null>(null);
  
  // Initialize bandwidth optimizer
  useEffect(() => {
    bandwidthOptimizerRef.current = new BandwidthOptimizer();
    
    return () => {
      if (bandwidthOptimizerRef.current) {
        bandwidthOptimizerRef.current.dispose();
      }
    };
  }, []);
  
  // Handle video stream
  useEffect(() => {
    const getVideoStream = async () => {
      try {
        if (isVideoEnabled) {
          const constraints: MediaStreamConstraints = {
            video: {
              deviceId: videoDeviceId ? { exact: videoDeviceId } : undefined,
            },
          };
          
          const stream = await navigator.mediaDevices.getUserMedia(constraints);
          setVideoStream(stream);
          
          // Add to bandwidth optimizer
          if (bandwidthOptimizerRef.current) {
            bandwidthOptimizerRef.current.addVideoStream(stream);
            bandwidthOptimizerRef.current.updateBandwidth(simulatedBandwidth);
          }
          
          // Connect to video element
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
          }
        } else {
          // Stop video stream
          if (videoStream) {
            videoStream.getTracks().forEach(track => track.stop());
            
            // Remove from bandwidth optimizer
            if (bandwidthOptimizerRef.current) {
              bandwidthOptimizerRef.current.removeStream(videoStream);
            }
          }
          setVideoStream(null);
        }
      } catch (error) {
        console.error('Error accessing video:', error);
        setIsVideoEnabled(false);
      }
    };
    
    getVideoStream();
    
    return () => {
      if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [isVideoEnabled, videoDeviceId]);
  
  // Handle audio stream
  useEffect(() => {
    const getAudioStream = async () => {
      try {
        if (isAudioEnabled) {
          const constraints: MediaStreamConstraints = {
            audio: {
              deviceId: audioDeviceId ? { exact: audioDeviceId } : undefined,
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
            },
          };
          
          const stream = await navigator.mediaDevices.getUserMedia(constraints);
          setAudioStream(stream);
          
          // Add to bandwidth optimizer
          if (bandwidthOptimizerRef.current) {
            bandwidthOptimizerRef.current.addAudioStream(stream);
            bandwidthOptimizerRef.current.updateBandwidth(simulatedBandwidth);
          }
        } else {
          // Stop audio stream
          if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop());
            
            // Remove from bandwidth optimizer
            if (bandwidthOptimizerRef.current) {
              bandwidthOptimizerRef.current.removeStream(audioStream);
            }
          }
          setAudioStream(null);
        }
      } catch (error) {
        console.error('Error accessing audio:', error);
        setIsAudioEnabled(false);
      }
    };
    
    getAudioStream();
    
    return () => {
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [isAudioEnabled, audioDeviceId]);
  
  // Update bandwidth optimizer when network condition changes
  useEffect(() => {
    let bandwidth = 2000; // Default: 2 Mbps
    
    switch (networkCondition) {
      case 'good':
        bandwidth = 2000; // 2 Mbps
        break;
      case 'fair':
        bandwidth = 1000; // 1 Mbps
        break;
      case 'poor':
        bandwidth = 500; // 500 kbps
        break;
    }
    
    setSimulatedBandwidth(bandwidth);
    
    if (bandwidthOptimizerRef.current) {
      bandwidthOptimizerRef.current.updateBandwidth(bandwidth);
    }
  }, [networkCondition]);
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/remote-classes"
            className="inline-flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] mb-4"
          >
            <FaArrowRight className="ml-1" />
            <span>العودة إلى الفصول الافتراضية</span>
          </Link>
          
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaVideo className="text-[var(--primary-color)]" />
            اختبار جودة الصوت والفيديو
          </h1>
          <p className="text-gray-600 mr-4">
            هذه الصفحة مخصصة لاختبار تحسينات جودة الصوت والفيديو في الفصول الافتراضية
          </p>
        </div>
        
        {/* Main content */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Video preview */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4 text-[var(--primary-color)] flex items-center gap-2">
              <FaVideo />
              معاينة الفيديو
            </h2>
            
            <div className="bg-gray-100 rounded-lg overflow-hidden aspect-video mb-4">
              {isVideoEnabled ? (
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <p className="text-gray-500">الفيديو غير مفعل</p>
                </div>
              )}
            </div>
            
            {/* Video settings */}
            <VideoSettings
              onVideoChange={setIsVideoEnabled}
              onQualityChange={setVideoQuality}
              onDeviceChange={setVideoDeviceId}
              isVideoEnabled={isVideoEnabled}
              currentQuality={videoQuality}
              currentDeviceId={videoDeviceId}
              stream={videoStream}
              useAdaptiveBitrate={useAdaptiveBitrate}
              onAdaptiveBitrateChange={setUseAdaptiveBitrate}
            />
          </div>
          
          {/* Audio settings and network simulation */}
          <div className="space-y-6">
            {/* Audio settings */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold mb-4 text-[var(--primary-color)] flex items-center gap-2">
                <FaMicrophone />
                إعدادات الصوت
              </h2>
              
              <AudioSettings
                onAudioChange={setIsAudioEnabled}
                onQualityChange={setAudioQuality}
                onDeviceChange={setAudioDeviceId}
                onVolumeChange={setVolume}
                isAudioEnabled={isAudioEnabled}
                currentQuality={audioQuality}
                currentDeviceId={audioDeviceId}
                currentVolume={volume}
              />
            </div>
            
            {/* Network simulation */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-bold mb-4 text-[var(--primary-color)] flex items-center gap-2">
                <FaNetworkWired />
                محاكاة ظروف الشبكة
              </h2>
              
              <div className="space-y-4">
                <p className="text-gray-600">
                  يمكنك محاكاة ظروف شبكة مختلفة لاختبار كيفية تكيف جودة الصوت والفيديو
                </p>
                
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium text-gray-700">حالة الشبكة</label>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setNetworkCondition('good')}
                      className={`flex-1 py-2 px-4 rounded-md ${
                        networkCondition === 'good'
                          ? 'bg-primary-color text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      جيدة (2 ميجابت/ثانية)
                    </button>
                    <button
                      onClick={() => setNetworkCondition('fair')}
                      className={`flex-1 py-2 px-4 rounded-md ${
                        networkCondition === 'fair'
                          ? 'bg-yellow-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      متوسطة (1 ميجابت/ثانية)
                    </button>
                    <button
                      onClick={() => setNetworkCondition('poor')}
                      className={`flex-1 py-2 px-4 rounded-md ${
                        networkCondition === 'poor'
                          ? 'bg-red-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      ضعيفة (500 كيلوبت/ثانية)
                    </button>
                  </div>
                </div>
                
                <div className="mt-4 p-3 bg-blue-50 rounded-md border border-blue-200">
                  <p className="text-blue-800 text-sm">
                    عند تغيير حالة الشبكة، سيقوم النظام تلقائيًا بضبط جودة الصوت والفيديو للحفاظ على استقرار الاتصال.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoAudioTestPage;

# API وصل التسجيل - التوثيق

## 📋 نظرة عامة

هذا الملف يحتوي على واجهة برمجة التطبيقات لإدارة وصولات التسجيل للتلاميذ.

## 🔗 المسار
`/api/students/[id]/registration-receipt`

## 🔐 المصادقة
جميع العمليات تتطلب مصادقة المستخدم عبر NextAuth.

## 📊 العمليات المدعومة

### 1. GET - جلب وصولات التسجيل

**الوصف:** جلب وصولات التسجيل للتلميذ (جميع الوصولات أو وصل محدد).

**المعاملات:**
- `id` (في المسار): معرف التلميذ
- `receiptId` (اختياري): معرف وصل محدد

**مثال على الطلب:**
```
GET /api/students/1/registration-receipt
GET /api/students/1/registration-receipt?receiptId=1
```

**الاستجابة (وصل واحد):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "studentId": 1,
    "receiptNumber": "REG-202506001",
    "issueDate": "15/06/2025",
    "registrationFee": 500.0,
    "paymentStatus": "PAID",
    "receiptData": {
      "student": {
        "id": 1,
        "name": "أحمد محمد علي",
        "username": "ahmed123",
        "age": 12,
        "phone": "0123456789"
      },
      "guardian": {
        "name": "محمد علي أحمد",
        "phone": "0123456789",
        "email": "<EMAIL>",
        "address": "شارع النيل، القاهرة"
      },
      "classe": {
        "name": "الفصل الأول"
      },
      "schoolInfo": {
        "name": "مدرسة القرآن الكريم",
        "address": "العنوان الرسمي للمدرسة",
        "phone": "رقم هاتف المدرسة",
        "email": "<EMAIL>"
      }
    },
    "pdfPath": null,
    "isPrinted": true,
    "printedAt": "15/06/2025",
    "printedBy": "<EMAIL>",
    "notes": "تم الدفع نقداً",
    "createdBy": "<EMAIL>",
    "createdAt": "15/06/2025",
    "updatedAt": "15/06/2025",
    "student": {
      "id": 1,
      "name": "أحمد محمد علي",
      "username": "ahmed123",
      "age": 12,
      "phone": "0123456789",
      "guardian": {
        "id": 1,
        "name": "محمد علي أحمد",
        "phone": "0123456789",
        "email": "<EMAIL>",
        "address": "شارع النيل، القاهرة"
      },
      "classe": {
        "id": 1,
        "name": "الفصل الأول"
      }
    }
  }
}
```

### 2. POST - إنشاء وصل تسجيل جديد

**الوصف:** إنشاء وصل تسجيل جديد للتلميذ مع توليد رقم وصل فريد.

**المعاملات:**
- `id` (في المسار): معرف التلميذ

**البيانات المطلوبة:**
```json
{
  "registrationFee": 500.0,
  "paymentStatus": "PENDING",
  "notes": "ملاحظات إضافية"
}
```

**الحقول الإجبارية:**
- `registrationFee`: رسوم التسجيل (يجب أن تكون أكبر من صفر)

**الحقول الاختيارية:**
- `paymentStatus`: حالة الدفع (افتراضي: "PENDING")
- `notes`: ملاحظات إضافية

**الاستجابة:**
```json
{
  "success": true,
  "message": "تم إنشاء وصل التسجيل بنجاح",
  "data": {
    "id": 1,
    "studentId": 1,
    "receiptNumber": "REG-202506001",
    "issueDate": "15/06/2025",
    "registrationFee": 500.0,
    "paymentStatus": "PENDING",
    "isPrinted": false,
    "createdAt": "15/06/2025",
    "updatedAt": "15/06/2025"
  }
}
```

### 3. PUT - تحديث وصل التسجيل

**الوصف:** تحديث بيانات وصل التسجيل أو تسجيل الطباعة.

**المعاملات:**
- `id` (في المسار): معرف التلميذ

**البيانات المطلوبة:**
```json
{
  "receiptId": 1,
  "paymentStatus": "PAID",
  "notes": "تم الدفع نقداً",
  "markAsPrinted": true
}
```

**الحقول الإجبارية:**
- `receiptId`: معرف الوصل المراد تحديثه

**الحقول الاختيارية:**
- `paymentStatus`: حالة الدفع الجديدة
- `notes`: ملاحظات محدثة
- `markAsPrinted`: تسجيل أن الوصل تم طباعته

## 🔢 نظام ترقيم الوصولات

### تنسيق رقم الوصل
`REG-YYYYMM####`

حيث:
- `REG`: بادئة ثابتة للتسجيل
- `YYYY`: السنة (4 أرقام)
- `MM`: الشهر (رقمان)
- `####`: رقم تسلسلي (4 أرقام)

**أمثلة:**
- `REG-202506001`: أول وصل في يونيو 2025
- `REG-202506002`: ثاني وصل في يونيو 2025
- `REG-202507001`: أول وصل في يوليو 2025

### آلية التوليد
1. البحث عن آخر رقم وصل في الشهر الحالي
2. زيادة الرقم التسلسلي بـ 1
3. تنسيق الرقم الجديد حسب النمط المحدد

## 📄 بيانات الوصل (receiptData)

يتم حفظ جميع البيانات اللازمة للطباعة في حقل `receiptData` بصيغة JSON:

```json
{
  "student": {
    "id": 1,
    "name": "أحمد محمد علي",
    "username": "ahmed123",
    "age": 12,
    "phone": "0123456789"
  },
  "guardian": {
    "name": "محمد علي أحمد",
    "phone": "0123456789",
    "email": "<EMAIL>",
    "address": "شارع النيل، القاهرة"
  },
  "classe": {
    "name": "الفصل الأول"
  },
  "receiptNumber": "REG-202506001",
  "issueDate": "15/06/2025",
  "registrationFee": 500.0,
  "paymentStatus": "PENDING",
  "notes": "ملاحظات إضافية",
  "schoolInfo": {
    "name": "مدرسة القرآن الكريم",
    "address": "العنوان الرسمي للمدرسة",
    "phone": "رقم هاتف المدرسة",
    "email": "<EMAIL>"
  }
}
```

## 📊 حالات الدفع المدعومة

- `PENDING`: في انتظار الدفع
- `PAID`: تم الدفع
- `CANCELLED`: ملغي
- `PARTIAL`: دفع جزئي
- `REFUNDED`: مسترد

## 🖨️ تتبع الطباعة

### الحقول المرتبطة بالطباعة
- `isPrinted`: هل تم طباعة الوصل
- `printedAt`: تاريخ الطباعة
- `printedBy`: معرف المستخدم الذي طبع الوصل

### تسجيل الطباعة
عند تحديث الوصل مع `markAsPrinted: true`:
1. يتم تعيين `isPrinted` إلى `true`
2. يتم تسجيل `printedAt` بالتاريخ الحالي
3. يتم تسجيل `printedBy` بمعرف المستخدم الحالي

## 🎨 تنسيق التواريخ

جميع التواريخ تُعرض بصيغة DD/MM/YYYY (الأرقام الفرنسية):
- `issueDate`: تاريخ إصدار الوصل
- `printedAt`: تاريخ الطباعة
- `createdAt`: تاريخ الإنشاء
- `updatedAt`: تاريخ آخر تحديث

## ⚠️ رموز الأخطاء

- `401`: غير مصرح بالوصول (لا توجد جلسة مستخدم)
- `400`: بيانات غير صحيحة أو مفقودة
- `404`: التلميذ أو الوصل غير موجود
- `500`: خطأ في الخادم

## 🔄 التكامل مع النظام

### التكامل مع المدفوعات
- يمكن ربط الوصل بسجلات المدفوعات
- تحديث حالة الدفع عند استلام المبلغ

### التكامل مع الطباعة
- بيانات الوصل محفوظة بصيغة JSON للطباعة
- تتبع عمليات الطباعة ومن قام بها

### التكامل مع التقارير
- إمكانية تصدير وصولات التسجيل
- إحصائيات الرسوم المحصلة

## 📝 ملاحظات التطوير

- رقم الوصل فريد على مستوى النظام
- البيانات محفوظة بشكل آمن مع تسجيل المستخدم المنشئ
- دعم كامل للتواريخ بالصيغة المطلوبة
- إمكانية إعادة طباعة الوصولات

/* 
  تحسينات موضع السايد بار
  هذا الملف يحتوي على تحسينات CSS لضمان أن السايد بار محصور بين الهيدر والفوتر
*/

/* تحديد ارتفاع الهيدر والفوتر */
:root {
  --header-height: 100px;
  --footer-height: 80px;
}

/* تحسين موضع السايد بار في الشاشات الكبيرة */
@media (min-width: 1024px) {
  /* السايد بار الثابت للمسؤولين */
  .admin-sidebar {
    top: var(--header-height) !important;
    bottom: var(--footer-height) !important;
    height: calc(100vh - var(--header-height) - var(--footer-height)) !important;
  }

  /* السايد بار الديناميكي للموظفين */
  .dynamic-sidebar {
    top: var(--header-height) !important;
    bottom: var(--footer-height) !important;
    height: calc(100vh - var(--header-height) - var(--footer-height)) !important;
  }

  /* السايد بار للمعلمين والطلاب وأولياء الأمور */
  .user-sidebar {
    position: sticky !important;
    top: 0 !important;
    height: calc(100vh - var(--header-height) - var(--footer-height)) !important;
    margin-top: 0 !important;
  }
}

/* تحسين موضع السايد بار في الشاشات الصغيرة */
@media (max-width: 1023px) {
  /* السايد بار في الموبايل */
  .mobile-sidebar {
    top: var(--header-height) !important;
    bottom: var(--footer-height) !important;
    height: calc(100vh - var(--header-height) - var(--footer-height)) !important;
  }
}

/* تحسين التمرير داخل السايد بار */
.sidebar-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* تحسين شريط التمرير */
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* تحسين مظهر السايد بار عند الإخفاء/الإظهار */
.sidebar-transition {
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

/* تحسين مظهر المحتوى الرئيسي عند فتح/إغلاق السايد بار */
.main-content-with-sidebar {
  transition: margin-right 0.3s ease-in-out;
}

/* تحسين مظهر السايد بار في الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .sidebar-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
  }

  .sidebar-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.4);
  }

  .sidebar-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.6);
  }
}

/* تحسين مظهر السايد بار على الأجهزة اللوحية */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-sidebar {
    width: 280px !important;
  }
}

/* تحسين مظهر السايد بار على الهواتف */
@media (max-width: 767px) {
  .mobile-sidebar {
    width: 100vw !important;
    max-width: 320px !important;
  }
}

/* تحسين مظهر الخلفية المظلمة للسايد بار في الموبايل */
.sidebar-overlay {
  position: fixed;
  top: var(--header-height);
  bottom: var(--footer-height);
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 15;
  transition: opacity 0.3s ease-in-out;
}

/* تحسين مظهر أزرار التبديل */
.sidebar-toggle {
  position: relative;
  z-index: 25;
}

/* تحسين مظهر السايد بار عند التحميل */
.sidebar-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* تحسين مظهر عناصر السايد بار */
.sidebar-item {
  transition: all 0.2s ease-in-out;
  border-radius: 8px;
  margin: 2px 0;
}

.sidebar-item:hover {
  transform: translateX(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebar-item.active {
  background: rgba(255, 255, 255, 0.2) !important;
  border-left: 4px solid white;
}

/* تحسين مظهر القوائم الفرعية */
.sidebar-submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.sidebar-submenu.open {
  max-height: 500px;
}

.sidebar-submenu-item {
  padding-right: 2rem;
  font-size: 0.875rem;
  opacity: 0.9;
}

.sidebar-submenu-item:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

/* تحسين مظهر أيقونات السايد بار */
.sidebar-icon {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  margin-left: 0.75rem;
}

/* تحسين مظهر نصوص السايد بار */
.sidebar-text {
  font-weight: 500;
  color: white;
  transition: color 0.2s ease-in-out;
}

/* تحسين مظهر فواصل السايد بار */
.sidebar-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
  margin: 1rem 0;
}

/* تحسين مظهر رأس السايد بار */
.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.1);
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

/* تحسين مظهر السايد بار في حالة الخطأ */
.sidebar-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  text-align: center;
  padding: 2rem;
}

/* تحسين إمكانية الوصول */
.sidebar-item:focus {
  outline: 2px solid white;
  outline-offset: 2px;
}

.sidebar-item:focus-visible {
  outline: 2px solid white;
  outline-offset: 2px;
}

/* تحسين مظهر السايد بار للطباعة */
@media print {
  .sidebar,
  .sidebar-overlay,
  .sidebar-toggle {
    display: none !important;
  }
}

/* تحسين مظهر السايد بار في وضع التباين العالي */
@media (prefers-contrast: high) {
  .sidebar-item {
    border: 1px solid white;
  }

  .sidebar-item:hover,
  .sidebar-item:focus {
    background: white !important;
    color: black !important;
  }
}

/* تحسين مظهر السايد بار للحركة المقللة */
@media (prefers-reduced-motion: reduce) {
  .sidebar-transition,
  .sidebar-item,
  .sidebar-submenu,
  .main-content-with-sidebar {
    transition: none !important;
  }

  .sidebar-loading {
    animation: none !important;
  }
}

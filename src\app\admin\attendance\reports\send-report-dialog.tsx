'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'react-toastify'
import { FaPaperPlane, FaCalendarAlt, FaUserGraduate, FaUsers } from 'react-icons/fa'

interface Class {
  id: number
  name: string
}

interface SendReportDialogProps {
  isOpen: boolean
  onClose: () => void
  classes: Class[]
}

export default function SendReportDialog({ isOpen, onClose, classes }: SendReportDialogProps) {
  const [reportType, setReportType] = useState('daily')
  const [selectedClass, setSelectedClass] = useState('all')
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [sendToTeachers, setSendToTeachers] = useState(true)
  const [sendToAdmins, setSendToAdmins] = useState(true)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!reportType) {
      toast.error('يرجى تحديد نوع التقرير')
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/attendance/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reportType,
          classeId: selectedClass === 'all' ? undefined : selectedClass,
          date: reportType === 'daily' ? selectedDate : undefined,
          sendToTeachers,
          sendToAdmins
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في إرسال التقرير')
      }

      const data = await response.json()
      toast.success(`تم إرسال التقرير بنجاح إلى ${data.recipientsCount.teachers + data.recipientsCount.admins} مستخدم`)
      onClose()
    } catch (error) {
      console.error('Error sending report:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إرسال التقرير')
    } finally {
      setIsLoading(false)
    }
  }

  const resetForm = () => {
    setReportType('daily')
    setSelectedClass('all')
    setSelectedDate(new Date().toISOString().split('T')[0])
    setSendToTeachers(true)
    setSendToAdmins(true)
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const dialogFooter = (
    <div className="flex justify-end gap-2 mt-4">
      <Button
        type="button"
        variant="outline"
        onClick={handleClose}
        disabled={isLoading}
      >
        إلغاء
      </Button>
      <Button
        type="submit"
        form="sendReportForm"
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
            جاري الإرسال...
          </>
        ) : (
          <>
            <FaPaperPlane className="ml-2" />
            إرسال التقرير
          </>
        )}
      </Button>
    </div>
  )

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={handleClose}
      title="إرسال تقرير الغياب"
      variant="primary"
      footer={dialogFooter}
    >
      <form id="sendReportForm" onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="report-type" className="flex items-center gap-2">
            <FaCalendarAlt className="text-[var(--primary-color)]" />
            نوع التقرير
          </Label>
          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger id="report-type">
              <SelectValue placeholder="اختر نوع التقرير" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">تقرير يومي</SelectItem>
              <SelectItem value="weekly">تقرير أسبوعي</SelectItem>
              <SelectItem value="monthly">تقرير شهري</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="class-select" className="flex items-center gap-2">
            <FaUserGraduate className="text-[var(--primary-color)]" />
            القسم
          </Label>
          <Select value={selectedClass} onValueChange={setSelectedClass}>
            <SelectTrigger id="class-select">
              <SelectValue placeholder="اختر القسم (اختياري)" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">كل الأقسام</SelectItem>
              {classes.map((cls) => (
                <SelectItem key={cls.id} value={cls.id.toString()}>
                  {cls.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {reportType === 'daily' && (
          <div className="space-y-2">
            <Label htmlFor="date-input" className="flex items-center gap-2">
              <FaCalendarAlt className="text-[var(--primary-color)]" />
              التاريخ
            </Label>
            <Input
              id="date-input"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="w-full text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
            />
          </div>
        )}

        <div className="space-y-2 pt-2 border-t border-gray-100">
          <Label className="flex items-center gap-2">
            <FaUsers className="text-[var(--primary-color)]" />
            إرسال التقرير إلى
          </Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="send-to-teachers"
                checked={sendToTeachers}
                onCheckedChange={(checked) => setSendToTeachers(checked as boolean)}
              />
              <Label htmlFor="send-to-teachers" className="cursor-pointer">المعلمين</Label>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="send-to-admins"
                checked={sendToAdmins}
                onCheckedChange={(checked) => setSendToAdmins(checked as boolean)}
              />
              <Label htmlFor="send-to-admins" className="cursor-pointer">الإداريين</Label>
            </div>
          </div>
        </div>
      </form>
    </AnimatedDialog>
  )
}

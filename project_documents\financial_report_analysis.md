# تحليل هيكل التقرير المالي

## النظام المالي الحالي في قاعدة البيانات

### الجداول المالية الرئيسية:

#### 1. جدول الخزينة (Treasury)
- **الحقول الأساسية:**
  - `balance`: الرصيد الحالي
  - `totalIncome`: إجمالي المداخيل
  - `totalExpense`: إجمالي المصروفات
  - `createdAt`, `updatedAt`: تواريخ الإنشاء والتحديث

#### 2. جدول المداخيل (Income)
- **الحقول الأساسية:**
  - `source`: مصدر الدخل
  - `amount`: المبلغ
  - `date`: تاريخ الدخل
  - `treasuryId`: ربط بالخزينة

#### 3. جدول المصروفات (Expense)
- **الحقول الأساسية:**
  - `purpose`: الغرض من المصروف
  - `amount`: المبلغ
  - `date`: تاريخ المصروف
  - `categoryId`: فئة المصروف
  - `receipt`: صورة الإيصال
  - `notes`: ملاحظات إضافية
  - `treasuryId`: ربط بالخزينة

#### 4. جدول فئات المصروفات (ExpenseCategory)
- **الحقول الأساسية:**
  - `name`: اسم الفئة
  - `description`: وصف الفئة
  - `icon`: أيقونة الفئة
  - `color`: لون الفئة
  - `isActive`: حالة النشاط

#### 5. جدول المدفوعات (Payment)
- **الحقول الأساسية:**
  - `studentId`: معرف الطالب
  - `amount`: المبلغ المدفوع
  - `date`: تاريخ الدفع
  - `status`: حالة الدفع
  - `paymentMethodId`: طريقة الدفع

#### 6. جدول التبرعات (Donation)
- **الحقول الأساسية:**
  - `donorName`: اسم المتبرع
  - `amount`: مبلغ التبرع
  - `date`: تاريخ التبرع
  - `paymentMethodId`: طريقة الدفع

#### 7. جدول الميزانية (Budget)
- **الحقول الأساسية:**
  - `name`: اسم الميزانية
  - `description`: وصف الميزانية
  - `startDate`, `endDate`: فترة الميزانية
  - `totalAmount`: إجمالي مبلغ الميزانية
  - `status`: حالة الميزانية

## هيكل التقرير المالي المطلوب

### 1. معلومات الرأس
- **اسم الجمعية**: جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن
- **المكتب**: المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر
- **الشعبة**: شعبة بلدية المنقر
- **نوع التقرير**: التقرير المالي
- **الفترة**: من تاريخ إلى تاريخ (قابل للتخصيص)

### 2. الملخص التنفيذي
- **الرصيد الافتتاحي**: رصيد بداية الفترة
- **إجمالي المداخيل**: مجموع جميع المداخيل خلال الفترة
- **إجمالي المصروفات**: مجموع جميع المصروفات خلال الفترة
- **الرصيد الختامي**: رصيد نهاية الفترة
- **صافي الربح/الخسارة**: الفرق بين المداخيل والمصروفات

### 3. تفصيل المداخيل

#### أ. مدفوعات الطلاب
- **رسوم التسجيل**: مدفوعات التسجيل الجديد
- **الرسوم الشهرية**: المدفوعات الشهرية المنتظمة
- **رسوم إضافية**: رسوم الأنشطة والدورات
- **المجموع الفرعي**: إجمالي مدفوعات الطلاب

#### ب. التبرعات
- **تبرعات نقدية**: التبرعات المالية المباشرة
- **تبرعات عينية**: قيمة التبرعات العينية (إن وجدت)
- **تبرعات مخصصة**: تبرعات لأغراض محددة
- **المجموع الفرعي**: إجمالي التبرعات

#### ج. مداخيل أخرى
- **إيرادات الأنشطة**: عوائد الفعاليات والأنشطة
- **إيرادات الخدمات**: عوائد الخدمات المقدمة
- **فوائد واستثمارات**: عوائد الاستثمارات (إن وجدت)
- **المجموع الفرعي**: إجمالي المداخيل الأخرى

### 4. تفصيل المصروفات

#### أ. المصروفات التشغيلية
- **رواتب ومكافآت**: رواتب المعلمين والموظفين
- **مرافق وخدمات**: كهرباء، ماء، إنترنت، هاتف
- **صيانة وإصلاحات**: صيانة المباني والمعدات
- **مواد تعليمية**: كتب، قرطاسية، مواد تعليمية
- **المجموع الفرعي**: إجمالي المصروفات التشغيلية

#### ب. المصروفات الإدارية
- **مصروفات إدارية**: مصاريف إدارية عامة
- **اتصالات ومواصلات**: مصاريف الاتصال والنقل
- **ضرائب ورسوم**: الضرائب والرسوم الحكومية
- **تأمينات**: تأمينات المباني والمعدات
- **المجموع الفرعي**: إجمالي المصروفات الإدارية

#### ج. مصروفات الأنشطة
- **فعاليات ومناسبات**: تكاليف الفعاليات والمناسبات
- **رحلات ومخيمات**: تكاليف الرحلات والمخيمات
- **مسابقات وجوائز**: تكاليف المسابقات والجوائز
- **ضيافة**: مصاريف الضيافة والاستقبال
- **المجموع الفرعي**: إجمالي مصروفات الأنشطة

#### د. مصروفات رأسمالية
- **معدات وأجهزة**: شراء معدات وأجهزة جديدة
- **أثاث ومفروشات**: شراء أثاث ومفروشات
- **تحسينات المباني**: تحسينات وتطويرات المباني
- **المجموع الفرعي**: إجمالي المصروفات الرأسمالية

### 5. تحليل طرق الدفع
- **نقدي**: المبالغ المحصلة نقداً
- **تحويل بنكي**: المبالغ المحولة بنكياً
- **شيكات**: المبالغ المحصلة بالشيكات
- **طرق أخرى**: طرق دفع أخرى

### 6. إحصائيات شهرية
- **جدول شهري**: يوضح المداخيل والمصروفات لكل شهر
- **رسم بياني**: يوضح الاتجاهات الشهرية
- **مقارنات**: مقارنة مع الفترات السابقة

### 7. تحليل الميزانية (إن وجدت)
- **الميزانية المخططة**: المبالغ المخططة لكل فئة
- **الإنفاق الفعلي**: المبالغ المنفقة فعلياً
- **الانحرافات**: الفروقات بين المخطط والفعلي
- **نسب الإنجاز**: نسب تنفيذ الميزانية

## الاستعلامات المطلوبة من قاعدة البيانات

### 1. استعلامات المداخيل
```sql
-- إجمالي مدفوعات الطلاب
SELECT SUM(amount) FROM Payment 
WHERE date BETWEEN ? AND ? AND status = 'PAID'

-- إجمالي التبرعات
SELECT SUM(amount) FROM Donation 
WHERE date BETWEEN ? AND ?

-- إجمالي المداخيل الأخرى
SELECT SUM(amount) FROM Income 
WHERE date BETWEEN ? AND ?
```

### 2. استعلامات المصروفات
```sql
-- المصروفات حسب الفئة
SELECT c.name, SUM(e.amount) 
FROM Expense e 
LEFT JOIN ExpenseCategory c ON e.categoryId = c.id
WHERE e.date BETWEEN ? AND ?
GROUP BY c.id, c.name

-- إجمالي المصروفات
SELECT SUM(amount) FROM Expense 
WHERE date BETWEEN ? AND ?
```

### 3. استعلامات طرق الدفع
```sql
-- إحصائيات طرق الدفع للمدفوعات
SELECT pm.name, COUNT(*), SUM(p.amount)
FROM Payment p
JOIN PaymentMethod pm ON p.paymentMethodId = pm.id
WHERE p.date BETWEEN ? AND ?
GROUP BY pm.id, pm.name

-- إحصائيات طرق الدفع للتبرعات
SELECT pm.name, COUNT(*), SUM(d.amount)
FROM Donation d
JOIN PaymentMethod pm ON d.paymentMethodId = pm.id
WHERE d.date BETWEEN ? AND ?
GROUP BY pm.id, pm.name
```

### 4. استعلامات شهرية
```sql
-- الإحصائيات الشهرية
SELECT 
  DATE_FORMAT(date, '%Y-%m') as month,
  'income' as type,
  SUM(amount) as total
FROM (
  SELECT date, amount FROM Payment WHERE status = 'PAID'
  UNION ALL
  SELECT date, amount FROM Donation
  UNION ALL
  SELECT date, amount FROM Income
) combined
WHERE date BETWEEN ? AND ?
GROUP BY DATE_FORMAT(date, '%Y-%m')

UNION ALL

SELECT 
  DATE_FORMAT(date, '%Y-%m') as month,
  'expense' as type,
  SUM(amount) as total
FROM Expense
WHERE date BETWEEN ? AND ?
GROUP BY DATE_FORMAT(date, '%Y-%m')
```

## تصميم واجهة التقرير المالي

### العناصر المطلوبة:
1. **حقول الإدخال**:
   - تاريخ البداية (مطلوب)
   - تاريخ النهاية (مطلوب)
   - فئات المصروفات (اختياري - لتصفية التقرير)
   - طرق الدفع (اختياري - لتصفية التقرير)

2. **أزرار العمليات**:
   - إنشاء التقرير
   - معاينة التقرير
   - تصدير Excel
   - تصدير PDF
   - طباعة

3. **عرض التقرير**:
   - جداول تفصيلية للمداخيل والمصروفات
   - رسوم بيانية للاتجاهات
   - ملخص تنفيذي بالأرقام الرئيسية
   - إمكانية التنقل بين الأقسام

### ملاحظات التطوير:
- استخدام مكتبة xlsx لتصدير Excel
- استخدام مكتبة jsPDF لتصدير PDF
- تطبيق تصميم متجاوب للعرض على الأجهزة المختلفة
- إضافة تحقق من صحة التواريخ المدخلة
- تحسين الأداء للاستعلامات الكبيرة
- إضافة إمكانية حفظ التقارير المولدة

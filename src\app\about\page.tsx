import React from 'react';
import { FaQuran, FaHistory, FaUsers, FaAward, FaChalkboardTeacher } from 'react-icons/fa';
import SiteLogo from '@/components/SiteLogo';
import PageBackground from '@/components/PageBackground';


const AboutPage = () => {
  const stats = [
    { label: 'سنوات الخبرة', value: '15+' },
    { label: 'الطلاب المسجلين', value: '1000+' },
    { label: 'المعلمون المتخصصون', value: '25+' },
    { label: 'الخريجون الحافظون', value: '500+' },
  ];

  const values = [
    {
      icon: <FaQuran className="text-4xl text-[var(--primary-color)] mb-4" />,
      title: 'الالتزام بالمنهج الصحيح',
      description: 'نلتزم بتعليم القرآن الكريم وفق المنهج الصحيح المستمد من السلف الصالح.'
    },
    {
      icon: <FaUsers className="text-4xl text-[var(--primary-color)] mb-4" />,
      title: 'الاهتمام بالطالب',
      description: 'نولي اهتماماً خاصاً بكل طالب، ونراعي الفروق الفردية والقدرات المتنوعة.'
    },
    {
      icon: <FaChalkboardTeacher className="text-4xl text-[var(--primary-color)] mb-4" />,
      title: 'التميز في التعليم',
      description: 'نسعى دائماً للتميز في طرق التعليم واستخدام أحدث الوسائل التعليمية.'
    },
    {
      icon: <FaAward className="text-4xl text-[var(--primary-color)] mb-4" />,
      title: 'الجودة والإتقان',
      description: 'نهتم بجودة التعليم وإتقان الحفظ والتلاوة وفق أحكام التجويد الصحيحة.'
    },
  ];

  return (
    <PageBackground
      pageName="about"
      className="min-h-screen bg-gray-50"
      fallbackBackground="linear-gradient(to bottom, #f9fafb, #f3f4f6)"
    >
      <div dir="rtl">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-[var(--primary-color)] to-[var(--secondary-color)] text-white py-20">
        <div className="container mx-auto px-4 text-center">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-8">
            <SiteLogo size="2xl" showText={false} iconColor="white" />
          </div>

          <h1 className="text-4xl md:text-5xl font-bold mb-6">من نحن</h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            مؤسسة تعليمية رائدة في مجال تعليم القرآن الكريم وعلومه منذ أكثر من 15 عاماً
          </p>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <div className="relative h-80 w-full rounded-lg overflow-hidden shadow-xl">
                {/* خلفية متدرجة مع الشعار */}
                <div className="absolute inset-0 bg-gradient-to-br from-[var(--primary-color)] to-[var(--secondary-color)] opacity-90"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <SiteLogo size="xl" showText={false} iconColor="white" className="transform scale-150" />
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center">
                <FaHistory className="text-[var(--primary-color)] mr-3" />
                قصتنا
              </h2>
              <p className="text-gray-600 mb-4">
                تأسست مؤسسة برهان لتعليم القرآن الكريم عام 2008م بهدف نشر كتاب الله وتعليمه للناشئة والكبار على حد سواء.
              </p>
              <p className="text-gray-600 mb-4">
                بدأنا بفصل دراسي واحد وعدد قليل من الطلاب، وبفضل الله تعالى ثم بجهود المعلمين المخلصين، توسعت المؤسسة لتضم اليوم أكثر من 1000 طالب وطالبة.
              </p>
              <p className="text-gray-600">
                نفتخر بأننا ساهمنا في تخريج المئات من حفظة كتاب الله، ونسعى دائماً لتطوير برامجنا ومناهجنا لتقديم أفضل خدمة تعليمية.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">إنجازاتنا بالأرقام</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
                <p className="text-4xl font-bold text-[var(--primary-color)] mb-2">{stat.value}</p>
                <p className="text-gray-600">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">قيمنا</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md text-center">
                <div className="flex justify-center">{value.icon}</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-16 bg-gray-100">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-800 mb-12 text-center">فريقنا</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* يمكن إضافة صور حقيقية للفريق */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <FaChalkboardTeacher className="text-6xl text-gray-400" />
              </div>
              <div className="p-6 text-center">
                <h3 className="text-xl font-bold text-gray-800 mb-1">الشيخ أحمد محمد</h3>
                <p className="text-[var(--primary-color)] mb-3">مدير المؤسسة</p>
                <p className="text-gray-600">حاصل على إجازة في القراءات العشر ولديه خبرة 20 عاماً في مجال تعليم القرآن.</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <FaChalkboardTeacher className="text-6xl text-gray-400" />
              </div>
              <div className="p-6 text-center">
                <h3 className="text-xl font-bold text-gray-800 mb-1">الشيخ عبدالرحمن علي</h3>
                <p className="text-[var(--primary-color)] mb-3">مشرف التحفيظ</p>
                <p className="text-gray-600">حافظ لكتاب الله ومجاز برواية حفص عن عاصم، ولديه خبرة 15 عاماً في التدريس.</p>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <FaChalkboardTeacher className="text-6xl text-gray-400" />
              </div>
              <div className="p-6 text-center">
                <h3 className="text-xl font-bold text-gray-800 mb-1">الأستاذة فاطمة محمود</h3>
                <p className="text-[var(--primary-color)] mb-3">مشرفة قسم النساء</p>
                <p className="text-gray-600">حافظة لكتاب الله ومتخصصة في علوم القرآن، ولديها خبرة 12 عاماً في تعليم النساء.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-[var(--primary-color)] text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">انضم إلينا اليوم</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">ابدأ رحلتك في حفظ كتاب الله وتعلم أحكام التجويد مع نخبة من المعلمين المتخصصين.</p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <a href="/register" className="bg-white text-[var(--primary-color)] px-6 py-3 rounded-md font-medium hover:bg-gray-100 transition-colors">سجل الآن</a>
            <a href="/contact" className="bg-transparent text-white border border-white px-6 py-3 rounded-md font-medium hover:bg-white/10 transition-colors">تواصل معنا</a>
          </div>
        </div>
      </section>
      </div>
    </PageBackground>
  );
};

export default AboutPage;
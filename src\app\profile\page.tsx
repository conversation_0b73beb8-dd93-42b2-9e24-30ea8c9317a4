"use client";
import { useState, useEffect } from 'react';
import { toast } from '@/components/ui/use-toast';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { UserCircle, Mail, Calendar, Phone, Briefcase, GraduationCap, User } from 'lucide-react';
import SiteLogo from '@/components/SiteLogo';

interface UserProfile {
  id: number;
  username: string;
  email: string | null;
  role: string;
  createdAt: string;
  profile?: {
    name: string;
    phone: string | null;
  };
  teacher?: {
    name: string;
    specialization: string;
  };
  employee?: {
    name: string;
    position: string;
  };
}

const getRoleLabel = (role: string): string => {
  const roleMap: Record<string, string> = {
    'ADMIN': 'مدير النظام',
    'TEACHER': 'معلم',
    'STUDENT': 'طالب',
    'PARENT': 'ولي أمر',
    'EMPLOYEE': 'موظف'
  };
  return roleMap[role] || role;
};

const getRoleBadgeColor = (role: string): string => {
  const colorMap: Record<string, string> = {
    'ADMIN': 'bg-red-100 text-red-800 border-red-200',
    'TEACHER': 'bg-blue-100 text-blue-800 border-blue-200',
    'STUDENT': 'bg-green-100 text-green-800 border-green-200',
    'PARENT': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'EMPLOYEE': 'bg-purple-100 text-purple-800 border-purple-200'
  };
  return colorMap[role] || 'bg-gray-100 text-gray-800 border-gray-200';
};

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch('/api/users/profile');
        if (!response.ok) {
          throw new Error('فشل في تحميل الملف الشخصي');
        }
        const data = await response.json();
        setProfile(data);
      } catch (error) {
        toast({
          title: 'خطأ',
          description: error instanceof Error ? error.message : 'فشل في تحميل الملف الشخصي',
          variant: 'destructive'
        });
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <Skeleton className="h-8 w-3/4 mx-auto mb-2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <Card className="max-w-3xl mx-auto">
          <CardContent className="pt-6">
            <p className="text-lg text-gray-600">لم يتم العثور على بيانات الملف الشخصي</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // تحديد الاسم المعروض بناءً على الدور
  const displayName = profile.profile?.name ||
                      profile.teacher?.name ||
                      profile.employee?.name ||
                      profile.username;

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-3xl mx-auto border-t-4 border-t-[var(--primary-color)]">
        <CardHeader className="pb-2">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-4">
            <SiteLogo size="md" showText={false} />
          </div>

          <CardTitle className="text-2xl font-bold text-center text-[var(--primary-color)]">
            الملف الشخصي
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center mb-6">
            <div className="w-24 h-24 bg-[var(--primary-color)]/10 rounded-full flex items-center justify-center mb-4">
              <UserCircle className="w-16 h-16 text-[var(--primary-color)]" />
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-1">{displayName}</h2>
            <Badge className={`${getRoleBadgeColor(profile.role)} font-medium`}>
              {getRoleLabel(profile.role)}
            </Badge>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="space-y-6">
              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <User className="w-5 h-5 text-[var(--primary-color)] mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-gray-500">اسم المستخدم</h3>
                  <p className="text-base font-medium">{profile.username}</p>
                </div>
              </div>

              {profile.email && (
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <Mail className="w-5 h-5 text-[var(--primary-color)] mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">البريد الإلكتروني</h3>
                    <p className="text-base font-medium">{profile.email}</p>
                  </div>
                </div>
              )}

              <div className="flex items-start space-x-3 rtl:space-x-reverse">
                <Calendar className="w-5 h-5 text-[var(--primary-color)] mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-gray-500">تاريخ الانضمام</h3>
                  <p className="text-base font-medium">
                    {new Date(profile.createdAt).toLocaleDateString('fr-FR')}
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              {profile.profile?.phone && (
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <Phone className="w-5 h-5 text-[var(--primary-color)] mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">رقم الهاتف</h3>
                    <p className="text-base font-medium">{profile.profile.phone}</p>
                  </div>
                </div>
              )}

              {profile.teacher?.specialization && (
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <GraduationCap className="w-5 h-5 text-[var(--primary-color)] mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">التخصص</h3>
                    <p className="text-base font-medium">{profile.teacher.specialization}</p>
                  </div>
                </div>
              )}

              {profile.employee?.position && (
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <Briefcase className="w-5 h-5 text-[var(--primary-color)] mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">المنصب</h3>
                    <p className="text-base font-medium">{profile.employee.position}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";

export async function GET(request: NextRequest) {
    try {
        // الحصول على معرف المستخدم من التوكن
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || userData.role !== 'TEACHER') {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userId = userData.id;

        // جلب معلومات المستخدم والملف الشخصي
        const user = await prisma.user.findUnique({
            where: {
                id: userId
            },
            include: {
                profile: true,
                teacher: true
            }
        });

        if (!user || !user.teacher) {
            return NextResponse.json(
                { message: "لم يتم العثور على بيانات المعلم" },
                { status: 404 }
            );
        }

        // محاولة جلب المواد التي يدرسها المعلم
        const teacherSubjects = await prisma.teacherSubject.findMany({
            where: {
                teacherId: user.teacher.id
            },
            include: {
                subject: true,
                classes: {
                    include: {
                        classe: {
                            include: {
                                students: true
                            }
                        }
                    }
                }
            }
        });

        // حساب عدد الطلاب والفصول
        let totalStudents = 0;
        let totalClasses = 0;

        // تجميع الفصول لعرضها
        const classes: Array<{ id: string; name: string; students: number; subject: string }> = [];

        teacherSubjects.forEach(ts => {
            totalClasses += ts.classes.length;

            ts.classes.forEach(cs => {
                if (cs.classe && cs.classe.students) {
                    totalStudents += cs.classe.students.length;

                    // إضافة الفصل إلى قائمة الفصول
                    classes.push({
                        id: cs.classe.id.toString(),
                        name: cs.classe.name,
                        students: cs.classe.students.length,
                        subject: ts.subject.name
                    });
                }
            });
        });

        // جلب الفصول القادمة
        const upcomingClasses = classes.slice(0, 3).map(cls => ({
            id: cls.id,
            name: cls.name,
            time: cls.subject || 'غير محدد',
            students: cls.students
        }));

        // جلب نشاطات الطلاب
        interface StudentActivity {
            id: number;
            studentName: string;
            activityType: string;
            description: string;
            time: string;
            progress: number;
            createdAt: Date;
        }

        let studentActivities: StudentActivity[] = [];
        try {
            // جلب تقدم حفظ القرآن للطلاب
            const quranProgress = await prisma.quranProgress.findMany({
                where: {
                    student: {
                        classe: {
                            classSubjects: {
                                some: {
                                    teacherSubject: {
                                        teacherId: user.teacher.id
                                    }
                                }
                            }
                        }
                    }
                },
                include: {
                    student: true,
                    surah: true,
                    exam: true
                },
                orderBy: {
                    completionDate: 'desc'
                },
                take: 10
            });

            // جلب نقاط الامتحانات للطلاب
            const examPoints = await prisma.exam_points.findMany({
                where: {
                    classSubject: {
                        teacherSubject: {
                            teacherId: user.teacher.id
                        }
                    }
                },
                include: {
                    student: true,
                    exam: true,
                    surah: true
                },
                orderBy: {
                    updatedAt: 'desc'
                },
                take: 10
            });

            // جلب سجلات الحضور للطلاب
            const attendance = await prisma.attendance.findMany({
                where: {
                    student: {
                        classe: {
                            classSubjects: {
                                some: {
                                    teacherSubject: {
                                        teacherId: user.teacher.id
                                    }
                                }
                            }
                        }
                    }
                },
                include: {
                    student: true
                },
                orderBy: {
                    date: 'desc'
                },
                take: 10
            });

            // تحويل تقدم حفظ القرآن إلى نشاطات
            const quranActivities = quranProgress.map(progress => ({
                id: progress.id,
                studentName: progress.student.name,
                activityType: 'quran_memorization',
                description: `أتم حفظ سورة ${progress.surah.name} من الآية ${progress.startVerse} إلى الآية ${progress.endVerse}`,
                time: formatTimeAgo(progress.completionDate || progress.startDate),
                progress: ((progress.memorization + progress.tajweed) / 20) * 100, // تحويل التقييم إلى نسبة مئوية
                createdAt: progress.completionDate || progress.startDate
            }));

            // تحويل نقاط الامتحانات إلى نشاطات
            const examActivities = examPoints.map(point => ({
                id: point.id + 1000, // إضافة 1000 لتجنب تكرار المعرفات
                studentName: point.student.name,
                activityType: 'exam',
                description: `حصل على درجة ${point.grade} في امتحان ${point.exam.evaluationType}${point.surah ? ` (سورة ${point.surah.name})` : ''}`,
                time: formatTimeAgo(point.updatedAt),
                progress: (Number(point.grade) / 10) * 100, // تحويل الدرجة إلى نسبة مئوية
                createdAt: point.updatedAt
            }));

            // تحويل سجلات الحضور إلى نشاطات
            const attendanceActivities = attendance.map(record => ({
                id: record.id + 2000, // إضافة 2000 لتجنب تكرار المعرفات
                studentName: record.student.name,
                activityType: 'attendance',
                description: record.status === 'PRESENT' ? 'حضر الحصة' : (record.status === 'EXCUSED' ? 'غائب بعذر' : 'غائب'),
                time: formatTimeAgo(record.date),
                progress: record.status === 'PRESENT' ? 100 : (record.status === 'EXCUSED' ? 50 : 0),
                createdAt: record.date
            }));

            // دمج جميع النشاطات وترتيبها حسب التاريخ
            studentActivities = [...quranActivities, ...examActivities, ...attendanceActivities]
                .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
                .slice(0, 10); // أخذ أحدث 10 نشاطات فقط
        } catch (error) {
            console.error('Error fetching student activities:', error);
            studentActivities = [];
        }

        // جلب نسبة الحضور - ملاحظة: لا يوجد حقل teacherId في نموذج Attendance
        // نستخدم استعلام بديل
        const attendanceData = await prisma.attendance.findMany({
            where: {
                // نستخدم معرف المعلم في شرط آخر أو نجلب كل سجلات الحضور
            },
            select: {
                status: true
            }
        });

        let attendanceRate = 0;
        if (attendanceData.length > 0) {
            const presentCount = attendanceData.filter(a => a.status === 'PRESENT').length;
            attendanceRate = Math.round((presentCount / attendanceData.length) * 100);
        }

        return NextResponse.json({
            name: user.profile?.name || user.username,
            specialization: user.teacher.specialization || 'معلم القرآن الكريم',
            totalStudents: totalStudents,
            totalClasses: totalClasses,
            attendanceRate: attendanceRate,
            upcomingClasses: upcomingClasses,
            studentActivities: studentActivities
        });
    } catch (error: unknown) {
        console.error('Error fetching teacher stats:', error);
        return NextResponse.json({
            name: '',
            specialization: '',
            totalStudents: 0,
            totalClasses: 0,
            attendanceRate: 0,
            upcomingClasses: [],
            studentActivities: []
        });
    }
}

// دالة مساعدة لتنسيق الوقت
function formatTimeAgo(date: Date): string {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
        return `منذ ${diffInMinutes} دقيقة`;
    } else if (diffInMinutes < 1440) { // أقل من يوم
        const hours = Math.floor(diffInMinutes / 60);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInMinutes / 1440);
        return `منذ ${days} يوم`;
    }
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, FileText, Calendar, Users, HelpCircle } from 'lucide-react';
import Link from 'next/link';
import { EvaluationType } from '@prisma/client';
// import { useSession } from 'next-auth/react';

// دالة لتحويل نوع التقييم إلى نص مقروء
function getExamTypeLabel(type: EvaluationType): string {
  const labels: Partial<Record<EvaluationType, string>> = {
    QURAN_MEMORIZATION: 'حفظ القرآن',
    QURAN_RECITATION: 'تلاوة القرآن',
    WRITTEN_EXAM: 'تحريري',
    ORAL_EXAM: 'شفهي',
    PRACTICAL_TEST: 'عملي',
    HOMEWORK: 'واجب منزلي',
    PROJECT: 'مشروع',
    REMOTE_EXAM: 'امتحان عن بعد'
  };
  return labels[type] || String(type);
};

// دالة لتنسيق التاريخ
const formatMonthYear = (dateString: string): string => {
  const [year, month] = dateString.split('-');
  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  return `${monthNames[parseInt(month) - 1]} ${year}`;
};

export default function TeacherEvaluationDashboardPage() {
  const [teacherId, setTeacherId] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateExamOpen, setIsCreateExamOpen] = useState(false);
  const [isTeacherLoading, setIsTeacherLoading] = useState(true);

  // تعريف أنواع البيانات
  interface Class {
    id: number;
    name: string;
    studentsCount: number;
  }

  interface Exam {
    id: number;
    evaluationType: EvaluationType;
    month: string;
    description: string | null;
    className: string;
    classId: number;
  }

  interface ExamResult extends Exam {
    avgScore: number;
  }

  const [myClasses, setMyClasses] = useState<Class[]>([]);
  const [upcomingExams, setUpcomingExams] = useState<Exam[]>([]);
  const [recentResults, setRecentResults] = useState<ExamResult[]>([]);
  const [examTypes, setExamTypes] = useState<{id: number, name: string}[]>([]);
  const [isExamTypesLoading, setIsExamTypesLoading] = useState(true);
  const [evaluationTypes, setEvaluationTypes] = useState<{id: string, name: string, description: string}[]>([]);
  const [isEvaluationTypesLoading, setIsEvaluationTypesLoading] = useState(true);
  const [evaluationCriteria, setEvaluationCriteria] = useState<{id: number, name: string, weight: number, description: string}[]>([]);
  const [isEvaluationCriteriaLoading, setIsEvaluationCriteriaLoading] = useState(true);
  const [selectedCriteria, setSelectedCriteria] = useState<number[]>([]);
  const [subjects, setSubjects] = useState<{id: number, name: string}[]>([]);
  const [isSubjectsLoading, setIsSubjectsLoading] = useState(true);

  // استخدام useEffect لجلب معرف المعلم أولاً
  useEffect(() => {
    const fetchTeacherId = async () => {
      try {
        setIsTeacherLoading(true);
        // جلب معرف المعلم من API
        const teacherResponse = await fetch('/api/teacher-id');
        if (!teacherResponse.ok) {
          throw new Error('فشل في جلب معرف المعلم');
        }

        const teacherData = await teacherResponse.json();
        if (teacherData.success && teacherData.teacherId) {
          console.log('تم جلب معرف المعلم:', teacherData.teacherId);
          setTeacherId(teacherData.teacherId);
        } else {
          throw new Error('لم يتم العثور على معرف المعلم');
        }
      } catch (error) {
        console.error('خطأ في جلب معرف المعلم:', error);
        toast.error('فشل في جلب بيانات المعلم');
      } finally {
        setIsTeacherLoading(false);
      }
    };

    fetchTeacherId();
  }, []);

  // استخدام useEffect لجلب البيانات بعد الحصول على معرف المعلم
  useEffect(() => {
    // إذا لم يتم جلب معرف المعلم بعد، لا نقوم بجلب البيانات
    if (teacherId === null) return;

    // تعريف دالة جلب البيانات
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // جلب أنواع الامتحانات
        try {
          setIsExamTypesLoading(true);
          const examTypesResponse = await fetch('/api/exam-types');
          if (examTypesResponse.ok) {
            const examTypesData = await examTypesResponse.json();
            console.log('Exam types data:', examTypesData);
            // التأكد من أن البيانات عبارة عن مصفوفة
            if (Array.isArray(examTypesData)) {
              setExamTypes(examTypesData);
            } else if (examTypesData && Array.isArray(examTypesData.data)) {
              setExamTypes(examTypesData.data);
            } else {
              console.error('Unexpected exam types data format:', examTypesData);
              setExamTypes([]);
            }
          } else {
            console.error('Failed to fetch exam types:', examTypesResponse.status);
            setExamTypes([]);
          }
        } catch (error) {
          console.error('Error fetching exam types:', error);
          setExamTypes([]);
        } finally {
          setIsExamTypesLoading(false);
        }

        // جلب المواد الدراسية
        try {
          setIsSubjectsLoading(true);
          const subjectsResponse = await fetch('/api/subjects');
          if (subjectsResponse.ok) {
            const subjectsData = await subjectsResponse.json();
            console.log('Subjects data:', subjectsData);
            // التأكد من أن البيانات عبارة عن مصفوفة
            if (Array.isArray(subjectsData)) {
              setSubjects(subjectsData);
            } else if (subjectsData && Array.isArray(subjectsData.data)) {
              setSubjects(subjectsData.data);
            } else {
              console.error('Unexpected subjects data format:', subjectsData);
              setSubjects([]);
            }
          } else {
            console.error('Failed to fetch subjects:', subjectsResponse.status);
            setSubjects([]);
          }
        } catch (error) {
          console.error('Error fetching subjects:', error);
          setSubjects([]);
        } finally {
          setIsSubjectsLoading(false);
        }

        // جلب معايير التقييم
        try {
          setIsEvaluationCriteriaLoading(true);
          const evaluationCriteriaResponse = await fetch('/api/evaluation-criteria');
          if (evaluationCriteriaResponse.ok) {
            const evaluationCriteriaData = await evaluationCriteriaResponse.json();
            console.log('Evaluation criteria data:', evaluationCriteriaData);
            if (evaluationCriteriaData.success && Array.isArray(evaluationCriteriaData.data)) {
              // إذا كانت قاعدة البيانات تحتوي على معايير تقييم، نعرضها
              setEvaluationCriteria(evaluationCriteriaData.data);
            } else {
              console.error('Unexpected evaluation criteria data format:', evaluationCriteriaData);
              // إذا كان هناك خطأ في تنسيق البيانات، نترك القائمة فارغة
              setEvaluationCriteria([]);
            }
          } else {
            console.error('Failed to fetch evaluation criteria:', evaluationCriteriaResponse.status);
            // إذا فشل الطلب، نترك القائمة فارغة
            setEvaluationCriteria([]);
          }
        } catch (error) {
          console.error('Error fetching evaluation criteria:', error);
          // إذا حدث خطأ، نترك القائمة فارغة
          setEvaluationCriteria([]);
        } finally {
          setIsEvaluationCriteriaLoading(false);
        }

        // جلب أنواع التقييم
        try {
          setIsEvaluationTypesLoading(true);
          const evaluationTypesResponse = await fetch('/api/evaluation/types');
          if (evaluationTypesResponse.ok) {
            const evaluationTypesData = await evaluationTypesResponse.json();
            console.log('Evaluation types data:', evaluationTypesData);
            if (Array.isArray(evaluationTypesData)) {
              setEvaluationTypes(evaluationTypesData);
            } else {
              console.error('Unexpected evaluation types data format:', evaluationTypesData);
              // إذا لم يتم الحصول على البيانات بشكل صحيح، نترك القائمة فارغة
              setEvaluationTypes([]);
            }
          } else {
            console.error('Failed to fetch evaluation types:', evaluationTypesResponse.status);
            // إذا فشل الطلب، نترك القائمة فارغة
            setEvaluationTypes([]);
          }
        } catch (error) {
          console.error('Error fetching evaluation types:', error);
          // إذا حدث خطأ، نترك القائمة فارغة
          setEvaluationTypes([]);
        } finally {
          setIsEvaluationTypesLoading(false);
        }

        // جلب الفصول الدراسية للمعلم من API الجديد
        const teacherSubjectsResponse = await fetch(`/api/teachers/${teacherId}/subjects?includeClasses=true`);
        if (!teacherSubjectsResponse.ok) throw new Error('فشل في جلب بيانات المواد والفصول');
        const teacherSubjectsData = await teacherSubjectsResponse.json();

        // تنسيق بيانات الفصول
        if (teacherSubjectsData.success && Array.isArray(teacherSubjectsData.classes)) {
          setMyClasses(teacherSubjectsData.classes);
        } else {
          // الطريقة القديمة كاحتياطي
          const classesResponse = await fetch(`/api/classes?teacherId=${teacherId}&includeStudents=true`);
          if (!classesResponse.ok) throw new Error('فشل في جلب بيانات الفصول');
          const classesData = await classesResponse.json();

          // تنسيق بيانات الفصول
          interface ClassData {
            id: number;
            name: string;
            students?: { id: number }[];
          }

          const classes = classesData.map((cls: ClassData) => ({
            id: cls.id,
            name: cls.name,
            studentsCount: cls.students?.length || 0
          }));
          setMyClasses(classes);
        }

        // جلب الامتحانات مع تمرير معرف المعلم
        const examsResponse = await fetch(`/api/evaluation/exams?teacherId=${teacherId}`);
        if (!examsResponse.ok) throw new Error('فشل في جلب بيانات الامتحانات');
        const examsData = await examsResponse.json();

        if (examsData.success && Array.isArray(examsData.data)) {
          const exams = examsData.data;
          const now = new Date();
          const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

          // تحديد الامتحانات القادمة والسابقة
          const upcoming: Exam[] = [];
          const recent: ExamResult[] = [];

          // جلب نقاط الامتحانات للحصول على متوسط الدرجات مع تمرير معرف المعلم
          const examPointsResponse = await fetch(`/api/evaluation/exam-points?teacherId=${teacherId}`);
          const examPointsData = await examPointsResponse.json();
          const examPoints = examPointsData.success ? examPointsData.data : [];

          // حساب متوسط الدرجات لكل امتحان
          interface ExamPoint {
            examId: number;
            grade: number | string;
          }

          interface ExamAverage {
            total: number;
            count: number;
          }

          const examAverages: Record<number, ExamAverage> = {};
          examPoints.forEach((point: ExamPoint) => {
            if (!examAverages[point.examId]) {
              examAverages[point.examId] = { total: 0, count: 0 };
            }
            examAverages[point.examId].total += Number(point.grade);
            examAverages[point.examId].count += 1;
          });

          // إنشاء قائمة بمعرفات الفصول التي يدرسها المعلم
          const teacherClassIds = new Set<number>();

          // إضافة معرفات الفصول من الفصول التي تم جلبها
          const localClasses = Array.isArray(myClasses) ? [...myClasses] : [];
          localClasses.forEach(cls => {
            if (cls && typeof cls.id === 'number') {
              teacherClassIds.add(cls.id);
            }
          });

          console.log('Teacher class IDs:', Array.from(teacherClassIds));

          // تنسيق بيانات الامتحانات
          for (const exam of exams) {
            // التحقق مما إذا كان المعلم مسؤولاً عن هذا الامتحان
            let isTeacherExam = false;
            let classId = 0;
            let className = '';

            // طريقة 1: التحقق من نقاط الامتحان
            if (exam.exam_points && exam.exam_points.length > 0) {
              interface ExamPointWithClassSubject {
                classSubject?: {
                  teacherSubject?: {
                    teacherId: number;
                  };
                  classeId: number;
                  classe?: {
                    name: string;
                  };
                };
              }

              const examClassSubjects = exam.exam_points.filter((point: ExamPointWithClassSubject) =>
                point.classSubject?.teacherSubject?.teacherId === Number(teacherId)
              );

              if (examClassSubjects && examClassSubjects.length > 0) {
                isTeacherExam = true;
                classId = examClassSubjects[0].classSubject.classeId;
                // الحصول على اسم الفصل من نقطة الامتحان
                className = examClassSubjects[0].classSubject.classe?.name || 'غير معروف';
              }
            }

            // طريقة 2: التحقق من الفصول التي يدرسها المعلم (للامتحانات التي لا تحتوي على نقاط)
            if (!isTeacherExam && exam.classSubjects && Array.isArray(exam.classSubjects)) {
              for (const classSubject of exam.classSubjects) {
                // التحقق من أن هذا الفصل يدرسه المعلم
                if (classSubject.teacherSubject?.teacherId === Number(teacherId)) {
                  isTeacherExam = true;
                  classId = classSubject.classeId;
                  className = classSubject.classe?.name || 'غير معروف';
                  break;
                }
                // التحقق من الفصول المحلية أيضاً
                if (teacherClassIds.has(classSubject.classeId)) {
                  isTeacherExam = true;
                  classId = classSubject.classeId;
                  className = classSubject.classe?.name || 'غير معروف';
                  break;
                }
              }
            }

            // طريقة 3: إذا لم نجد أي فصول مرتبطة، نتحقق من أن المعلم يدرس فصول (جميع الامتحانات متاحة)
            if (!isTeacherExam && (!exam.classSubjects || exam.classSubjects.length === 0)) {
              if (teacherClassIds.size > 0) {
                isTeacherExam = true;
                // استخدام أول فصل يدرسه المعلم كافتراضي
                const firstClass = Array.from(teacherClassIds)[0];
                const classInfo = localClasses.find(cls => cls.id === firstClass);
                classId = firstClass;
                className = classInfo?.name || 'غير معروف';
              }
            }

            // إذا كان الامتحان للمعلم، أضفه إلى القائمة المناسبة
            if (isTeacherExam) {
              const examData = {
                id: exam.id,
                evaluationType: exam.evaluationType,
                month: exam.month,
                description: exam.description,
                className,
                classId
              };

              if (exam.month >= currentMonth) {
                upcoming.push(examData);
              } else {
                const avgScore = examAverages[exam.id]
                  ? examAverages[exam.id].total / examAverages[exam.id].count
                  : 0;
                recent.push({ ...examData, avgScore });
              }
            }
          }

          // ترتيب الامتحانات حسب التاريخ
          upcoming.sort((a, b) => a.month.localeCompare(b.month));
          recent.sort((a, b) => b.month.localeCompare(a.month));

          setUpcomingExams(upcoming);
          setRecentResults(recent);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('حدث خطأ أثناء جلب البيانات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
    // استخدام teacherId كتبعية لـ useEffect لإعادة جلب البيانات عند تغيير معرف المعلم
  }, [teacherId]);

  // تنسيق التاريخ للعرض
  const formatDate = (dateString: string) => {
    return formatMonthYear(dateString);
  };

  return (
    <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 w-full">
          <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">إدارة الامتحانات والتقييم</h1>
          <Link href="/teachers/evaluation/help" className="flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] transition-colors duration-300">
            <HelpCircle className="ml-2" size={18} />
            <span>دليل المساعدة</span>
          </Link>
        </div>
        <Button
          onClick={() => setIsCreateExamOpen(true)}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full md:w-auto"
        >
          <Plus className="ml-2" size={16} />
          إنشاء امتحان جديد
        </Button>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-center mb-4 gap-4">
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-[#e9f7f5] p-1 rounded-xl">
            <TabsTrigger value="overview" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white text-xs md:text-sm">نظرة عامة</TabsTrigger>
            <TabsTrigger value="exams" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white text-xs md:text-sm">الامتحانات</TabsTrigger>
            <TabsTrigger value="classes" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white text-xs md:text-sm">الفصول</TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="flex gap-2 w-full md:w-auto">
          <Link href="/teachers/evaluation/results" className="w-1/2 md:w-auto">
            <Button variant="outline" size="sm" className="w-full">عرض النتائج</Button>
          </Link>
          <Link href="/teachers/evaluation/scoring" className="w-1/2 md:w-auto">
            <Button variant="outline" size="sm" className="w-full">تسجيل النقاط</Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
        {isTeacherLoading ? (
          <div className="flex flex-col justify-center items-center h-64 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
            <div className="relative">
              <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
              <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
            </div>
            <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري التحقق من بيانات المعلم...</span>
          </div>
        ) : isLoading ? (
          <div className="flex flex-col justify-center items-center h-64 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
            <div className="relative">
              <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
              <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
            </div>
            <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري تحميل البيانات...</span>
          </div>
        ) : (
          <>
            <TabsContent value="overview">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg">الامتحانات القادمة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {upcomingExams.length > 0 ? (
                      <div className="space-y-4">
                        {upcomingExams.map(exam => (
                          <div key={exam.id} className="flex items-center p-3 border rounded-lg bg-[#f8fffd] hover:shadow-md transition-shadow duration-300">
                            <Calendar className="ml-3 text-[var(--primary-color)]" size={24} />
                            <div>
                              <h3 className="font-medium">{getExamTypeLabel(exam.evaluationType)}</h3>
                              <p className="text-sm text-gray-500">{formatDate(exam.month)} - {exam.className}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-gray-500 py-4">لا توجد امتحانات قادمة</p>
                    )}
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full" onClick={() => setIsCreateExamOpen(true)}>
                      <Plus className="ml-2" size={16} />
                      إنشاء امتحان جديد
                    </Button>
                  </CardFooter>
                </Card>

                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg">فصولي الدراسية</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {myClasses.length > 0 ? (
                      <div className="space-y-4">
                        {myClasses.map(cls => (
                          <div key={cls.id} className="flex justify-between items-center p-3 border rounded-lg bg-[#f8fffd] hover:shadow-md transition-shadow duration-300">
                            <div className="flex items-center">
                              <Users className="ml-3 text-[var(--primary-color)]" size={24} />
                              <h3 className="font-medium">{cls.name}</h3>
                            </div>
                            <div className="text-sm text-gray-500">{cls.studentsCount} طالب</div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-gray-500 py-4">لا توجد فصول دراسية</p>
                    )}
                  </CardContent>
                </Card>
              </div>

              <Card className="border border-[#e9f7f5] shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardHeader>
                  <CardTitle className="text-lg">نتائج الامتحانات الأخيرة</CardTitle>
                </CardHeader>
                <CardContent>
                  {recentResults.length > 0 ? (
                    <div className="divide-y">
                      {recentResults.map(result => (
                        <div key={result.id} className="flex justify-between items-center py-3">
                          <div>
                            <h3 className="font-medium">{getExamTypeLabel(result.evaluationType)}</h3>
                            <p className="text-sm text-gray-500">{formatDate(result.month)} - {result.className}</p>
                          </div>
                          <div className="flex items-center">
                            <span className="text-lg font-bold text-[var(--primary-color)] ml-2">{result.avgScore.toFixed(1)}</span>
                            <span className="text-sm text-gray-500">متوسط الدرجات</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-500 py-4">لا توجد نتائج امتحانات</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Link href="/teachers/evaluation/results" className="w-full">
                    <Button variant="outline" className="w-full">
                      عرض جميع النتائج
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="exams">
              <div className="space-y-6">
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg">الامتحانات القادمة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {upcomingExams.length > 0 ? (
                      <div className="divide-y">
                        {upcomingExams.map(exam => (
                          <div key={exam.id} className="flex justify-between items-center py-4">
                            <div className="flex items-center">
                              <Calendar className="ml-3 text-[var(--primary-color)]" size={24} />
                              <div>
                                <h3 className="font-medium">{getExamTypeLabel(exam.evaluationType)}</h3>
                                <p className="text-sm text-gray-500">{formatDate(exam.month)} - {exam.className}</p>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Link href={`/teachers/evaluation/scoring?examId=${exam.id.toString()}&classId=${exam.classId}`}>
                                <Button variant="outline" size="sm">تسجيل النقاط</Button>
                              </Link>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-gray-500 py-4">لا توجد امتحانات قادمة</p>
                    )}
                  </CardContent>
                </Card>

                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg">الامتحانات السابقة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {recentResults.length > 0 ? (
                      <div className="divide-y">
                        {recentResults.map(result => (
                          <div key={result.id} className="flex justify-between items-center py-4">
                            <div className="flex items-center">
                              <FileText className="ml-3 text-[var(--primary-color)]" size={24} />
                              <div>
                                <h3 className="font-medium">{getExamTypeLabel(result.evaluationType)}</h3>
                                <p className="text-sm text-gray-500">{formatDate(result.month)} - {result.className}</p>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <div className="flex space-x-2">
                                <Link href={`/teachers/evaluation/results?examId=${result.id.toString()}`} prefetch={false}>
                                  <Button variant="outline" size="sm">عرض النتائج</Button>
                                </Link>
                                <Link href={`/teachers/evaluation/scoring?examId=${result.id.toString()}&classId=${result.classId}`} prefetch={false}>
                                  <Button variant="outline" size="sm">تسجيل النقاط</Button>
                                </Link>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-center text-gray-500 py-4">لا توجد امتحانات سابقة</p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="classes">
              <div className="space-y-6">
                {myClasses.map(cls => (
                  <Card key={cls.id} className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                    <CardHeader>
                      <CardTitle className="text-lg">{cls.name}</CardTitle>
                      <CardDescription>{cls.studentsCount} طالب</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* حساب متوسط الدرجات للفصل */}
                        <div className="flex justify-between items-center">
                          <h3 className="font-medium">متوسط الدرجات</h3>
                          <span className="text-lg font-bold text-[var(--primary-color)]">
                            {(() => {
                              // حساب متوسط الدرجات للفصل من نتائج الامتحانات
                              const classResults = recentResults.filter(result => result.classId === cls.id);
                              if (classResults.length === 0) return '-';
                              const avgScore = classResults.reduce((sum, result) => sum + result.avgScore, 0) / classResults.length;
                              return avgScore.toFixed(1) + '/10';
                            })()}
                          </span>
                        </div>
                        {/* عدد الامتحانات للفصل */}
                        <div className="flex justify-between items-center">
                          <h3 className="font-medium">عدد الامتحانات</h3>
                          <span className="text-lg font-bold text-[var(--primary-color)]">
                            {(() => {
                              // حساب عدد الامتحانات للفصل
                              const classExams = [...upcomingExams, ...recentResults].filter(exam => exam.classId === cls.id);
                              return classExams.length || '-';
                            })()}
                          </span>
                        </div>
                        {/* عدد الطلاب */}
                        <div className="flex justify-between items-center">
                          <h3 className="font-medium">عدد الطلاب</h3>
                          <span className="text-lg font-bold text-[var(--primary-color)]">{cls.studentsCount || '-'}</span>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Link href={`/teachers/classes/${cls.id}/students`}>
                        <Button variant="outline">عرض الطلاب</Button>
                      </Link>
                      <Link href={`/teachers/evaluation/results?classId=${cls.id.toString()}`} prefetch={false}>
                        <Button variant="outline">عرض النتائج</Button>
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>






          </>
        )}
      </Tabs>

      {/* Create Exam Dialog */}
      <Dialog open={isCreateExamOpen} onOpenChange={(open) => !open && setIsCreateExamOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)] w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>إنشاء امتحان جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل الامتحان الجديد</DialogDescription>
          </DialogHeader>
          <form
            className="space-y-4"
            onSubmit={async (e) => {
              e.preventDefault();
              try {
                const formData = new FormData(e.currentTarget as HTMLFormElement);

                // تحويل معايير التقييم إلى أرقام
                const numericCriteriaIds = selectedCriteria.map(id => Number(id));

                const data = {
                  evaluationType: formData.get('evaluationType'),
                  month: formData.get('month'),
                  description: formData.get('description') || null,
                  maxPoints: Number(formData.get('maxPoints')),
                  passingPoints: Number(formData.get('passingPoints')),
                  typeId: formData.get('examTypeId') || null,
                  requiresSurah: formData.get('evaluationType') === 'QURAN_MEMORIZATION',
                  criteriaIds: numericCriteriaIds,
                  isPeriodic: formData.get('isPeriodic') === 'true',
                  period: formData.get('period') || null,
                  subjectId: formData.get('subjectId') || null
                };

                console.log('Sending data to create exam:', data);

                const response = await fetch('/api/evaluation/exams', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(data),
                });

                const result = await response.json();

                if (result.success) {
                  toast.success('تم إنشاء الامتحان بنجاح');
                  setIsCreateExamOpen(false);
                  // إعادة تحميل البيانات
                  window.location.reload();
                } else {
                  toast.error(result.error || 'حدث خطأ أثناء إنشاء الامتحان');
                }
              } catch (error) {
                console.error('Error creating exam:', error);
                toast.error('حدث خطأ أثناء إنشاء الامتحان');
              }
            }}
          >
            <div className="space-y-2">
              <label className="text-right block">نوع التقييم</label>
              <Select name="evaluationType" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع التقييم" />
                </SelectTrigger>
                <SelectContent>
                  {isEvaluationTypesLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل أنواع التقييم...</SelectItem>
                  ) : evaluationTypes.length > 0 ? (
                    evaluationTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>{type.description}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد أنواع تقييم متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">نوع الامتحان</label>
              <Select name="examTypeId" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع الامتحان" />
                </SelectTrigger>
                <SelectContent>
                  {isExamTypesLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل أنواع الامتحانات...</SelectItem>
                  ) : Array.isArray(examTypes) && examTypes.length > 0 ? (
                    examTypes.map(type => (
                      <SelectItem key={type.id} value={type.id.toString()}>{type.name}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد أنواع امتحانات متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">المادة الدراسية</label>
              <Select name="subjectId" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر المادة الدراسية" />
                </SelectTrigger>
                <SelectContent>
                  {isSubjectsLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل المواد الدراسية...</SelectItem>
                  ) : Array.isArray(subjects) && subjects.length > 0 ? (
                    subjects.map(subject => (
                      <SelectItem key={subject.id} value={subject.id.toString()}>{subject.name}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد مواد دراسية متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الفصل الدراسي</label>
              <Select name="classId">
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر الفصل الدراسي" />
                </SelectTrigger>
                <SelectContent>
                  {myClasses.map(cls => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>{cls.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الشهر</label>
              <Input name="month" type="month" className="text-right" required />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Input name="description" className="text-right" placeholder="وصف الامتحان" />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الدرجة القصوى</label>
              <Input name="maxPoints" type="number" defaultValue={100} className="text-right" required />
            </div>
            <div className="space-y-2">
              <label className="text-right block">درجة النجاح</label>
              <Input name="passingPoints" type="number" defaultValue={60} className="text-right" required />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-right block">امتحان دوري</label>
                <Select name="isPeriodic" defaultValue="false">
                  <SelectTrigger className="w-32 text-right">
                    <SelectValue placeholder="اختر" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">نعم</SelectItem>
                    <SelectItem value="false">لا</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-right block">الفترة (للامتحانات الدورية)</label>
              <Select name="period">
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر الفترة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="يومي">يومي</SelectItem>
                  <SelectItem value="أسبوعي">أسبوعي</SelectItem>
                  <SelectItem value="شهري">شهري</SelectItem>
                  <SelectItem value="فصلي">فصلي</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-right block">معايير التقييم (اختيارية)</label>
              <div className="border border-[#e9f7f5] rounded-md p-3 max-h-60 overflow-y-auto bg-white">
                {isEvaluationCriteriaLoading ? (
                  <div className="text-center py-2">
                    <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                    <p className="text-sm text-gray-500 mt-1">جاري تحميل معايير التقييم...</p>
                  </div>
                ) : evaluationCriteria.length > 0 ? (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm p-1 mb-2 border-b pb-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCriteria(evaluationCriteria.map(c => c.id))}
                      >
                        تحديد الكل
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCriteria([])}
                      >
                        إلغاء تحديد الكل
                      </Button>
                    </div>
                    {evaluationCriteria.map(criteria => (
                      <div key={criteria.id} className="flex justify-between items-center text-sm p-1 hover:bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={`criteria-${criteria.id}`}
                            checked={selectedCriteria.includes(criteria.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCriteria(prev => [...prev, criteria.id]);
                              } else {
                                setSelectedCriteria(prev => prev.filter(id => id !== criteria.id));
                              }
                            }}
                          />
                          <span className="text-gray-500">{(Number(criteria.weight) * 100).toFixed(0)}%</span>
                        </div>
                        <label
                          htmlFor={`criteria-${criteria.id}`}
                          className="font-medium cursor-pointer flex-1 text-right"
                        >
                          {criteria.name}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-sm text-gray-500 py-2">لا توجد معايير تقييم متاحة</p>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1 text-right">اختر معايير التقييم التي سيتم استخدامها عند تقييم الطلاب في هذا الامتحان</p>
            </div>
            <div className="flex flex-col-reverse sm:flex-row justify-end gap-2 sm:space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateExamOpen(false)}
                className="w-full sm:w-auto"
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
              >
                إنشاء
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

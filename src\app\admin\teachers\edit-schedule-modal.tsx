'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Teacher {
  id: number
  name: string
  specialization: string
}

interface Classe {
  id: number
  name: string
}

interface Subject {
  id: number
  name: string
}

interface TeacherSchedule {
  id: number
  teacherId: number
  day: string
  startTime: string
  endTime: string
  classeId: number
  subjectId: number
  location?: string
  notes?: string
  teacher?: Teacher
  classe?: Classe
  subject?: Subject
}

interface EditScheduleModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  schedule: TeacherSchedule | null
}

const DAYS_OF_WEEK = [
  { value: 'SUNDAY', label: 'الأحد' },
  { value: 'MONDAY', label: 'الإثنين' },
  { value: 'TUESDAY', label: 'الثلاثاء' },
  { value: 'WEDNESDAY', label: 'الأربعاء' },
  { value: 'THURSDAY', label: 'الخميس' },
  { value: 'FRIDAY', label: 'الجمعة' },
  { value: 'SATURDAY', label: 'السبت' }
]

export function EditScheduleModal({ isOpen, onClose, onSuccess, schedule }: EditScheduleModalProps) {
  const [formData, setFormData] = useState({
    id: '',
    teacherId: '',
    day: '',
    startTime: '',
    endTime: '',
    classeId: '',
    subjectId: '',
    location: '',
    notes: ''
  })
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [classes, setClasses] = useState<Classe[]>([])
  const [subjects, setSubjects] = useState<Subject[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب المعلمين
        const teachersResponse = await fetch('/api/teachers')
        if (!teachersResponse.ok) throw new Error('Failed to fetch teachers')
        const teachersData = await teachersResponse.json()
        setTeachers(teachersData.teachers || [])

        // جلب الفصول
        const classesResponse = await fetch('/api/classes')
        if (!classesResponse.ok) throw new Error('Failed to fetch classes')
        const classesData = await classesResponse.json()
        setClasses(classesData.classes || [])

        // جلب المواد
        const subjectsResponse = await fetch('/api/admin/subjects')
        if (!subjectsResponse.ok) throw new Error('Failed to fetch subjects')
        const subjectsData = await subjectsResponse.json()
        setSubjects(subjectsData || [])
      } catch (err: unknown) {
        setError('Failed to load data')
        console.error(err)
      }
    }
    fetchData()
  }, [])

  useEffect(() => {
    if (schedule) {
      setFormData({
        id: schedule.id.toString(),
        teacherId: schedule.teacherId.toString(),
        day: schedule.day,
        startTime: schedule.startTime,
        endTime: schedule.endTime,
        classeId: schedule.classeId.toString(),
        subjectId: schedule.subjectId.toString(),
        location: schedule.location || '',
        notes: schedule.notes || ''
      })
    }
  }, [schedule])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // التحقق من البيانات
    if (!formData.teacherId || !formData.day || !formData.startTime ||
        !formData.endTime || !formData.classeId || !formData.subjectId) {
      setError('يرجى إدخال جميع البيانات المطلوبة')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/teacher-schedules', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: parseInt(formData.id),
          teacherId: parseInt(formData.teacherId),
          day: formData.day,
          startTime: formData.startTime,
          endTime: formData.endTime,
          classeId: parseInt(formData.classeId),
          subjectId: parseInt(formData.subjectId),
          location: formData.location || null,
          notes: formData.notes || null
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'حدث خطأ أثناء تحديث جدول الحصص')
      }

      onSuccess()
      onClose()
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء تحديث جدول الحصص';
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] bg-white max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold text-[var(--primary-color)]">تعديل حصة</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded-md">{error}</div>
          )}

          <div className="space-y-2">
            <Label htmlFor="teacherId">المعلم</Label>
            <Select
              value={formData.teacherId}
              onValueChange={(value) => setFormData(prev => ({ ...prev, teacherId: value }))}
            >
              <SelectTrigger id="teacherId" className="w-full">
                <SelectValue placeholder="اختر المعلم" />
              </SelectTrigger>
              <SelectContent>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id.toString()}>
                    {teacher.name} - {teacher.specialization}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="day">اليوم</Label>
              <Select
                value={formData.day}
                onValueChange={(value) => setFormData(prev => ({ ...prev, day: value }))}
              >
                <SelectTrigger id="day" className="w-full">
                  <SelectValue placeholder="اختر اليوم" />
                </SelectTrigger>
                <SelectContent>
                  {DAYS_OF_WEEK.map((day) => (
                    <SelectItem key={day.value} value={day.value}>
                      {day.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="classeId">الفصل</Label>
              <Select
                value={formData.classeId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, classeId: value }))}
              >
                <SelectTrigger id="classeId" className="w-full">
                  <SelectValue placeholder="اختر الفصل" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((classe) => (
                    <SelectItem key={classe.id} value={classe.id.toString()}>
                      {classe.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startTime">وقت البدء</Label>
              <Input
                id="startTime"
                type="time"
                value={formData.startTime}
                onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="endTime">وقت الانتهاء</Label>
              <Input
                id="endTime"
                type="time"
                value={formData.endTime}
                onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="subjectId">المادة</Label>
              <Select
                value={formData.subjectId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, subjectId: value }))}
              >
                <SelectTrigger id="subjectId" className="w-full">
                  <SelectValue placeholder="اختر المادة" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id.toString()}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">المكان (اختياري)</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                placeholder="أدخل مكان الحصة"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">ملاحظات (اختياري)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="أدخل أي ملاحظات إضافية"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-gray-300 hover:bg-gray-100"
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
            >
              {isLoading ? 'جاري التحديث...' : 'تحديث الحصة'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

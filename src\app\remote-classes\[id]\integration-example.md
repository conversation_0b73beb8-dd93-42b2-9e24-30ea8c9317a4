# مثال على تكامل المكونات الجديدة في صفحة الفصل الافتراضي

هذا الملف يوضح كيفية دمج المكونات الجديدة (مشاركة الشاشة، السبورة التفاعلية، تحسينات الصوت والفيديو) في صفحة الفصل الافتراضي الحالية.

## تحديث صفحة الفصل الافتراضي

```tsx
'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { FaVideo, FaLink, FaCalendarAlt, FaClock, FaUsers, FaChalkboardTeacher, FaEdit, FaTrash, FaDownload, FaUpload, FaExclamationCircle, FaFileAlt } from 'react-icons/fa';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import axios from 'axios';

// استيراد المكونات الجديدة
import ScreenShareButton from '@/components/remote-classes/ScreenShare/ScreenShareButton';
import ScreenShareDisplay from '@/components/remote-classes/ScreenShare/ScreenShareDisplay';
import Whiteboard from '@/components/remote-classes/Whiteboard/Whiteboard';
import VideoSettings from '@/components/remote-classes/VideoAudio/VideoSettings';
import AudioSettings from '@/components/remote-classes/VideoAudio/AudioSettings';

// واجهة الفصل الافتراضي المحدثة
interface RemoteClass {
  id: number;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  meetingLink: string;
  meetingId?: string;
  meetingPassword?: string;
  platform: string;
  recordingUrl?: string;
  
  // حقول جديدة
  isScreenShareEnabled: boolean;
  isWhiteboardEnabled: boolean;
  videoQuality?: string;
  audioQuality?: string;
  
  instructor: {
    id: number;
    username: string;
    profile?: {
      name: string;
    };
  };
  classe?: {
    id: number;
    name: string;
    students: Array<{
      id: number;
      user: {
        id: number;
        username: string;
        profile?: {
          name: string;
        };
      };
    }>;
  };
  attendees: Array<{
    id: number;
    username: string;
    profile?: {
      name: string;
    };
  }>;
  materials: Array<{
    id: number;
    title: string;
    description?: string;
    fileUrl: string;
    fileType: string;
    createdAt: string;
  }>;
  
  // علاقات جديدة
  whiteboards?: Array<{
    id: number;
    name: string;
    content?: string;
    isActive: boolean;
    createdBy: number;
  }>;
}

const RemoteClassPage = ({ params }: { params: { id: string } }) => {
  const router = useRouter();
  const [remoteClass, setRemoteClass] = useState<RemoteClass | null>(null);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [userId, setUserId] = useState<number | null>(null);
  
  // حالة مشاركة الشاشة
  const [screenShareStream, setScreenShareStream] = useState<MediaStream | null>(null);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  
  // حالة السبورة التفاعلية
  const [activeWhiteboard, setActiveWhiteboard] = useState<number | null>(null);
  const [whiteboardData, setWhiteboardData] = useState<string | null>(null);
  
  // حالة الصوت والفيديو
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [videoQuality, setVideoQuality] = useState<'low' | 'medium' | 'high'>('medium');
  const [audioQuality, setAudioQuality] = useState<'low' | 'medium' | 'high'>('medium');
  const [videoDeviceId, setVideoDeviceId] = useState<string>('');
  const [audioDeviceId, setAudioDeviceId] = useState<string>('');
  const [volume, setVolume] = useState(1.0);

  // جلب بيانات الفصل الافتراضي
  useEffect(() => {
    const fetchRemoteClass = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/remote-classes/${params.id}`);
        setRemoteClass(response.data);
        
        // تعيين السبورة النشطة إذا كانت موجودة
        if (response.data.whiteboards && response.data.whiteboards.length > 0) {
          const activeBoard = response.data.whiteboards.find(wb => wb.isActive) || response.data.whiteboards[0];
          setActiveWhiteboard(activeBoard.id);
          setWhiteboardData(activeBoard.content || null);
        }
        
        // جلب معلومات المستخدم
        const userResponse = await axios.get('/api/users/me');
        setUserRole(userResponse.data.role);
        setUserId(userResponse.data.id);
      } catch (error) {
        console.error('Error fetching remote class:', error);
        toast.error('فشل في جلب بيانات الفصل الافتراضي');
      } finally {
        setLoading(false);
      }
    };

    fetchRemoteClass();
  }, [params.id]);

  // معالجة بدء مشاركة الشاشة
  const handleStartScreenShare = useCallback((stream: MediaStream) => {
    setScreenShareStream(stream);
    setIsScreenSharing(true);
    
    // إرسال حالة مشاركة الشاشة إلى الخادم
    axios.post(`/api/remote-classes/${params.id}/screen-share`, {
      status: 'active',
    }).catch(error => {
      console.error('Error updating screen share status:', error);
    });
  }, [params.id]);

  // معالجة إيقاف مشاركة الشاشة
  const handleStopScreenShare = useCallback(() => {
    if (screenShareStream) {
      screenShareStream.getTracks().forEach(track => track.stop());
    }
    setScreenShareStream(null);
    setIsScreenSharing(false);
    
    // إرسال حالة مشاركة الشاشة إلى الخادم
    axios.post(`/api/remote-classes/${params.id}/screen-share`, {
      status: 'stopped',
    }).catch(error => {
      console.error('Error updating screen share status:', error);
    });
  }, [screenShareStream, params.id]);

  // معالجة تغيير بيانات السبورة
  const handleWhiteboardChange = useCallback((data: string) => {
    setWhiteboardData(data);
    
    // حفظ بيانات السبورة على الخادم
    if (activeWhiteboard) {
      axios.put(`/api/remote-classes/${params.id}/whiteboards/${activeWhiteboard}`, {
        content: data,
      }).catch(error => {
        console.error('Error saving whiteboard data:', error);
      });
    }
  }, [activeWhiteboard, params.id]);

  // التحقق من صلاحيات المستخدم
  const canEdit = () => {
    if (!remoteClass || !userRole || userId === null) return false;
    return userRole === 'ADMIN' || (userRole === 'TEACHER' && remoteClass.instructor.id === userId);
  };

  // عرض حالة التحميل
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
        <div className="container mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#169b88] mx-auto"></div>
            <p className="mt-4 text-gray-600">جاري تحميل الفصل الافتراضي...</p>
          </div>
        </div>
      </div>
    );
  }

  // عرض رسالة إذا لم يتم العثور على الفصل
  if (!remoteClass) {
    return (
      <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
        <div className="container mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <FaExclamationCircle className="text-yellow-500 text-5xl mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 mb-4">الفصل الافتراضي غير موجود</h2>
            <p className="text-gray-600 mb-6">لم يتم العثور على الفصل الافتراضي المطلوب</p>
            <Link
              href="/remote-classes"
              className="bg-[#169b88] text-white px-4 py-2 rounded-md hover:bg-[#1ab19c] transition-colors"
            >
              العودة إلى الفصول الافتراضية
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* ... الجزء العلوي من الصفحة (العنوان والمعلومات) ... */}
        
        {/* قسم الفصل الافتراضي المباشر */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-bold text-[#169b88] mb-4 flex items-center gap-2">
            <FaVideo />
            <span>الفصل المباشر</span>
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* عرض الفيديو والصوت */}
            <div className="lg:col-span-2 space-y-4">
              {/* عرض مشاركة الشاشة إذا كانت نشطة */}
              {isScreenSharing && screenShareStream ? (
                <ScreenShareDisplay 
                  stream={screenShareStream} 
                  userName={remoteClass.instructor.profile?.name || remoteClass.instructor.username} 
                />
              ) : (
                <div className="bg-gray-100 rounded-lg border border-gray-300 h-64 flex items-center justify-center">
                  <p className="text-gray-500">لا توجد مشاركة شاشة نشطة</p>
                </div>
              )}
              
              {/* أزرار التحكم في مشاركة الشاشة */}
              {canEdit() && remoteClass.isScreenShareEnabled && (
                <div className="flex justify-center">
                  <ScreenShareButton 
                    onStartSharing={handleStartScreenShare}
                    onStopSharing={handleStopScreenShare}
                    isSharing={isScreenSharing}
                  />
                </div>
              )}
              
              {/* إعدادات الفيديو والصوت */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <VideoSettings 
                  onVideoChange={setIsVideoEnabled}
                  onQualityChange={setVideoQuality}
                  onDeviceChange={setVideoDeviceId}
                  isVideoEnabled={isVideoEnabled}
                  currentQuality={videoQuality}
                  currentDeviceId={videoDeviceId}
                />
                
                <AudioSettings 
                  onAudioChange={setIsAudioEnabled}
                  onQualityChange={setAudioQuality}
                  onDeviceChange={setAudioDeviceId}
                  onVolumeChange={setVolume}
                  isAudioEnabled={isAudioEnabled}
                  currentQuality={audioQuality}
                  currentDeviceId={audioDeviceId}
                  currentVolume={volume}
                />
              </div>
            </div>
            
            {/* السبورة التفاعلية */}
            <div className="lg:col-span-1">
              {remoteClass.isWhiteboardEnabled ? (
                <div className="h-full">
                  <h3 className="text-lg font-medium mb-2">السبورة التفاعلية</h3>
                  <Whiteboard 
                    id={`whiteboard-${activeWhiteboard || 'default'}`}
                    readOnly={!canEdit()}
                    initialData={whiteboardData || undefined}
                    onDataChange={handleWhiteboardChange}
                    height={400}
                    width={350}
                  />
                </div>
              ) : (
                <div className="bg-gray-100 rounded-lg border border-gray-300 h-64 flex items-center justify-center">
                  <p className="text-gray-500">السبورة التفاعلية غير متاحة في هذا الفصل</p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* ... باقي أقسام الصفحة (المواد التعليمية، المشاركين، إلخ) ... */}
      </div>
    </div>
  );
};

export default RemoteClassPage;
```

## ملاحظات التنفيذ

1. يجب تحديث واجهات API لدعم الميزات الجديدة (مشاركة الشاشة، السبورة التفاعلية، إعدادات الصوت والفيديو).

2. يجب إضافة مكتبات WebRTC وSocket.io للتواصل في الوقت الفعلي.

3. يجب تحديث نموذج البيانات في Prisma كما هو موضح في ملف `prisma/schema-updates.md`.

4. قد تحتاج إلى إنشاء خادم WebSocket منفصل أو استخدام Next.js API Routes مع Socket.io للمزامنة في الوقت الفعلي.

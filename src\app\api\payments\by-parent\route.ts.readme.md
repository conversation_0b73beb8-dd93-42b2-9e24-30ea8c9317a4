# API المدفوعات حسب الولي

## 📋 الوصف
API endpoint لجلب وعرض المدفوعات مجمعة حسب الولي، مع حساب الإحصائيات والملخصات المالية لكل ولي وأبنائه.

## 🎯 الهدف
- عرض ملخص شامل لمدفوعات كل ولي
- حساب إجمالي المبالغ المطلوبة والمدفوعة والمتبقية
- تتبع حالة الدفع لكل طالب
- توفير إحصائيات عامة للنظام المالي

## 🔗 المسارات

### GET /api/payments/by-parent
جلب المدفوعات مجمعة حسب الولي

#### المعاملات:
- `search`: البحث بالاسم أو الهاتف أو البريد الإلكتروني
- `status`: فلترة حسب حالة الدفع (PAID, PARTIAL, UNPAID, OVERDUE)
- `page`: رقم الصفحة (افتراضي: 1)
- `limit`: عدد النتائج في الصفحة (افتراضي: 50)

#### مثال على الطلب:
```bash
GET /api/payments/by-parent?search=أحمد&status=PARTIAL&page=1&limit=20
```

#### الاستجابة:
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "name": "أحمد محمد",
      "phone": "0123456789",
      "email": "<EMAIL>",
      "totalRequired": 15000.00,
      "totalPaid": 10000.00,
      "totalRemaining": 5000.00,
      "totalStudents": 2,
      "lastPaymentDate": "2024-01-15",
      "paymentRate": 67,
      "students": [
        {
          "id": 1,
          "name": "علي أحمد",
          "grade": "الخامس الابتدائي",
          "totalRequired": 8000.00,
          "totalPaid": 6000.00,
          "totalRemaining": 2000.00,
          "dueInvoices": 1,
          "lastPaymentDate": "2024-01-15",
          "paymentStatus": "PARTIAL"
        },
        {
          "id": 2,
          "name": "فاطمة أحمد",
          "grade": "الثالث الابتدائي",
          "totalRequired": 7000.00,
          "totalPaid": 4000.00,
          "totalRemaining": 3000.00,
          "dueInvoices": 2,
          "lastPaymentDate": "2024-01-10",
          "paymentStatus": "PARTIAL"
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "totalPages": 1
  },
  "statistics": {
    "totalParents": 1,
    "parentsWithDebts": 1,
    "totalDebtAmount": 5000.00,
    "totalPaidAmount": 10000.00,
    "averagePaymentRate": 67
  }
}
```

## 📊 هيكل البيانات

### ParentPaymentSummary
```typescript
interface ParentPaymentSummary {
  id: string;                 // معرف الولي
  name: string;               // اسم الولي
  phone: string;              // رقم الهاتف
  email?: string;             // البريد الإلكتروني (اختياري)
  totalRequired: number;      // إجمالي المبلغ المطلوب
  totalPaid: number;          // إجمالي المبلغ المدفوع
  totalRemaining: number;     // إجمالي المبلغ المتبقي
  totalStudents: number;      // عدد الأبناء
  lastPaymentDate?: string;   // تاريخ آخر دفعة
  paymentRate: number;        // معدل السداد (%)
  students: StudentPaymentSummary[];
}
```

### StudentPaymentSummary
```typescript
interface StudentPaymentSummary {
  id: number;                 // معرف الطالب
  name: string;               // اسم الطالب
  grade: string;              // الصف
  totalRequired: number;      // المبلغ المطلوب للطالب
  totalPaid: number;          // المبلغ المدفوع للطالب
  totalRemaining: number;     // المبلغ المتبقي للطالب
  dueInvoices: number;        // عدد الفواتير المستحقة
  lastPaymentDate?: string;   // تاريخ آخر دفعة للطالب
  paymentStatus: 'PAID' | 'PARTIAL' | 'UNPAID' | 'OVERDUE';
}
```

## ✨ الميزات الرئيسية

### 1. حساب تلقائي للإحصائيات
- إجمالي المبالغ المطلوبة من جميع الفواتير
- إجمالي المبالغ المدفوعة من جميع المدفوعات
- حساب المبالغ المتبقية تلقائياً
- معدل السداد لكل ولي

### 2. تتبع حالة الدفع
- **PAID**: مدفوع بالكامل
- **PARTIAL**: مدفوع جزئياً
- **UNPAID**: غير مدفوع
- **OVERDUE**: متأخر عن الموعد

### 3. البحث والفلترة المتقدمة
- البحث بالاسم أو الهاتف أو البريد الإلكتروني
- فلترة حسب حالة الدفع
- ترقيم الصفحات للأداء الأمثل

### 4. إحصائيات شاملة
- عدد الأولياء الإجمالي
- عدد الأولياء الذين لديهم ديون
- إجمالي مبلغ الديون
- إجمالي المبالغ المدفوعة
- متوسط معدل السداد

## 🔒 الأمان والصلاحيات
- يتطلب تسجيل دخول صحيح
- صلاحيات ADMIN أو TEACHER فقط
- التحقق من صحة البيانات المدخلة
- حماية من SQL injection

## 📈 الأداء والتحسين
- استخدام Prisma ORM للاستعلامات المحسنة
- تجميع البيانات في استعلام واحد
- ترقيم الصفحات لتحسين الأداء
- فهرسة قاعدة البيانات للبحث السريع

## 🧪 الاختبار
```bash
# جلب جميع المدفوعات حسب الولي
curl -X GET "http://localhost:3000/api/payments/by-parent" \
  -H "Cookie: jwtToken=YOUR_TOKEN"

# البحث عن ولي معين
curl -X GET "http://localhost:3000/api/payments/by-parent?search=أحمد" \
  -H "Cookie: jwtToken=YOUR_TOKEN"

# فلترة حسب الحالة
curl -X GET "http://localhost:3000/api/payments/by-parent?status=PARTIAL" \
  -H "Cookie: jwtToken=YOUR_TOKEN"
```

## 📝 ملاحظات التطوير
- يتم حساب جميع الإحصائيات في الوقت الفعلي
- البيانات مرتبة أبجدياً حسب اسم الولي
- يتم تسجيل العمليات في console للمراقبة
- دعم كامل للغة العربية في البحث والفلترة

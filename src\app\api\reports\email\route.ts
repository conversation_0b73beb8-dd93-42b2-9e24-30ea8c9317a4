import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import { sendEmail } from '@/lib/email';
import { generateReportPdf } from '@/lib/report-generator';
import { randomBytes } from 'crypto';

// POST /api/reports/email - إرسال التقرير بالبريد الإلكتروني
export async function POST(req: NextRequest) {
  try {
    // التحقق من المستخدم
    const jwtToken = req.cookies.get("jwtToken")?.value;
    if (!jwtToken) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    const token = await getToken(jwtToken);
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    // استخراج البيانات من الطلب
    const body = await req.json();
    const { reportId, reportType, recipients, subject, message, includeAttachment = true } = body;

    if (!reportId || !reportType || !recipients || !Array.isArray(recipients) || recipients.length === 0) {
      return NextResponse.json(
        { error: 'جميع الحقول الأساسية مطلوبة' },
        { status: 400 }
      );
    }

    // الحصول على بيانات التقرير
    let reportData;
    let reportTitle;

    switch (reportType) {
      case 'financial':
        // الحصول على بيانات التقرير المالي
        const financialReport = await prisma.report.findUnique({
          where: { id: parseInt(reportId) }
        });

        if (!financialReport) {
          return NextResponse.json(
            { error: 'التقرير غير موجود' },
            { status: 404 }
          );
        }

        reportData = JSON.parse(financialReport.data);
        reportTitle = financialReport.title;
        break;

      case 'budget':
        // الحصول على بيانات تقرير الميزانية
        const budget = await prisma.budget.findUnique({
          where: { id: parseInt(reportId) },
          include: {
            items: {
              include: {
                category: true
              }
            }
          }
        });

        if (!budget) {
          return NextResponse.json(
            { error: 'الميزانية غير موجودة' },
            { status: 404 }
          );
        }

        reportData = budget;
        reportTitle = `تقرير ميزانية: ${budget.name}`;
        break;

      // يمكن إضافة المزيد من أنواع التقارير هنا

      default:
        return NextResponse.json(
          { error: 'نوع التقرير غير مدعوم' },
          { status: 400 }
        );
    }

    // إنشاء ملف PDF للتقرير إذا كان مطلوبًا
    let attachmentBuffer: Buffer | undefined;
    if (includeAttachment) {
      attachmentBuffer = await generateReportPdf(reportType, reportData);
    }

    // إرسال البريد الإلكتروني لكل مستلم
    const emailPromises = recipients.map(async (recipient: string) => {
      const emailData = {
        to: recipient,
        subject: subject || `مشاركة تقرير: ${reportTitle}`,
        html: `
          <div dir="rtl" style="text-align: right; font-family: Arial, sans-serif;">
            <h2>مشاركة تقرير</h2>
            <p>${message || `مرحباً،\n\nأرغب في مشاركة التقرير التالي معك: ${reportTitle}.`}</p>
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مدرسة القرآن.</p>
          </div>
        `,
        attachments: (includeAttachment && attachmentBuffer) ? [
          {
            filename: `${reportTitle.replace(/\s+/g, '_')}.pdf`,
            content: attachmentBuffer,
            contentType: 'application/pdf'
          }
        ] : []
      };

      return sendEmail(emailData);
    });

    // انتظار إرسال جميع رسائل البريد الإلكتروني
    await Promise.all(emailPromises);

    // تسجيل عملية المشاركة
    await prisma.reportShare.create({
      data: {
        token: randomBytes(16).toString('hex'), // إنشاء رمز فريد للمشاركة
        reportId: reportId,
        reportType: reportType,
        createdBy: token.id.toString(),
        shareMethod: 'EMAIL',
        recipients: recipients.join(',')
      }
    });

    return NextResponse.json({
      success: true,
      message: `تم إرسال التقرير بنجاح إلى ${recipients.length} مستلم`
    });
  } catch (error) {
    console.error('خطأ في إرسال التقرير بالبريد الإلكتروني:', error);
    return NextResponse.json(
      { error: 'فشل في إرسال التقرير بالبريد الإلكتروني' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/donations/[id] - جلب تبرع محدد
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف التبرع غير صالح' },
        { status: 400 }
      );
    }

    const donation = await prisma.donation.findUnique({
      where: { id },
      include: {
        treasury: true,
        paymentMethod: true,
      },
    });

    if (!donation) {
      return NextResponse.json(
        { error: 'التبرع غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(donation);
  } catch (error) {
    console.error('خطأ في جلب التبرع:', error);
    return NextResponse.json(
      { error: 'فشل في جلب التبرع' },
      { status: 500 }
    );
  }
}

// PUT /api/donations/[id] - تحديث تبرع محدد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف التبرع غير صالح' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { donorName, amount, note } = body;

    // التحقق من وجود التبرع
    const existingDonation = await prisma.donation.findUnique({
      where: { id },
    });

    if (!existingDonation) {
      return NextResponse.json(
        { error: 'التبرع غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من صحة المبلغ
    if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
      return NextResponse.json(
        { error: 'قيمة التبرع غير صحيحة' },
        { status: 400 }
      );
    }



    // تحديث التبرع والخزينة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إذا تم تغيير المبلغ، قم بتحديث الخزينة
      if (amount !== undefined && amount !== existingDonation.amount) {
        const difference = amount - existingDonation.amount;

        await tx.treasury.update({
          where: { id: existingDonation.treasuryId },
          data: {
            balance: { increment: difference },
            totalIncome: { increment: difference > 0 ? difference : 0 },
            // إذا كان الفرق سالبًا، فهذا يعني أن المبلغ قد انخفض
            totalExpense: { increment: difference < 0 ? -difference : 0 },
          },
        });
      }

      // تحديث التبرع
      const updatedDonation = await tx.donation.update({
        where: { id },
        data: {
          donorName: donorName !== undefined ? donorName : existingDonation.donorName,
          amount: amount !== undefined ? amount : existingDonation.amount,
          note: note !== undefined ? note : existingDonation.note,
        },
        include: {
          paymentMethod: true,
        }
      });

      return updatedDonation;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('خطأ في تحديث التبرع:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث التبرع' },
      { status: 500 }
    );
  }
}

// DELETE /api/donations/[id] - حذف تبرع محدد
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف التبرع غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من وجود التبرع
    const existingDonation = await prisma.donation.findUnique({
      where: { id },
    });

    if (!existingDonation) {
      return NextResponse.json(
        { error: 'التبرع غير موجود' },
        { status: 404 }
      );
    }

    // حذف التبرع وتحديث الخزينة في معاملة واحدة
    await prisma.$transaction(async (tx) => {
      // حذف التبرع
      await tx.donation.delete({
        where: { id },
      });

      // تحديث رصيد الخزينة وإجمالي الدخل
      await tx.treasury.update({
        where: { id: existingDonation.treasuryId },
        data: {
          balance: { decrement: existingDonation.amount },
          totalIncome: { decrement: existingDonation.amount },
        },
      });
    });

    return NextResponse.json({ message: 'تم حذف التبرع بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف التبرع:', error);
    return NextResponse.json(
      { error: 'فشل في حذف التبرع' },
      { status: 500 }
    );
  }
}

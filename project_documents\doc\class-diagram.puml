@startuml Class Diagram - Quran School Management System

' Main entities
package "User Management" {
  class User {
    +id: Int
    +username: String
    +password: String
    +email: String
    +role: UserRole
    +createdAt: DateTime
    +updatedAt: DateTime
    +authenticate()
    +resetPassword()
  }

  class Profile {
    +id: Int
    +userId: Int
    +fullName: String
    +phone: String
    +address: String
    +bio: String
    +avatarUrl: String
    +updateProfile()
  }

  class Teacher {
    +id: Int
    +userId: Int
    +name: String
    +specialization: String
    +phone: String
    +salary: Float
    +joinDate: DateTime
    +calculateSalary()
    +assignToClass()
  }

  class Student {
    +id: Int
    +username: String
    +name: String
    +age: Int
    +phone: String
    +guardianId: Int
    +classeId: Int
    +totalPoints: Float
    +createdAt: DateTime
    +calculateProgress()
    +enrollInClass()
  }

  class Parent {
    +id: Int
    +name: String
    +phone: String
    +email: String
    +address: String
    +addChild()
    +viewChildrenProgress()
  }
}

package "Educational System" {
  class Classe {
    +id: Int
    +name: String
    +createdAt: DateTime
    +addStudent()
    +assignTeacher()
  }

  class Subject {
    +id: Int
    +name: String
    +description: String
    +addToClass()
  }

  class Attendance {
    +id: Int
    +studentId: Int
    +hisass: Int
    +date: DateTime
    +status: AttendanceStatus
    +markAttendance()
    +generateReport()
  }

  class QuranProgress {
    +id: Int
    +studentId: Int
    +examId: Int
    +surahId: Int
    +startAyah: Int
    +endAyah: Int
    +date: DateTime
    +status: ProgressStatus
    +notes: String
    +updateProgress()
    +calculateCompletionRate()
  }

  class Exam {
    +id: Int
    +evaluationType: EvaluationType
    +month: String
    +description: String
    +maxPoints: Float
    +passingPoints: Float
    +attachments: String
    +requiresSurah: Boolean
    +createdAt: DateTime
    +updatedAt: DateTime
    +scheduleExam()
    +gradeExam()
  }

  class Surah {
    +id: Int
    +name: String
    +number: Int
    +totalAyahs: Int
    +getAyahRange()
  }
}

package "Remote Learning" {
  class RemoteClass {
    +id: Int
    +title: String
    +description: String
    +instructorId: Int
    +classeId: Int
    +startTime: DateTime
    +endTime: DateTime
    +meetingUrl: String
    +recordingUrl: String
    +status: RemoteClassStatus
    +scheduleSession()
    +startSession()
    +recordSession()
  }

  class LearningResource {
    +id: Int
    +title: String
    +description: String
    +fileUrl: String
    +resourceType: ResourceType
    +uploadDate: DateTime
    +uploadResource()
    +assignToClass()
  }
}

package "Financial System" {
  class Payment {
    +id: Int
    +studentId: Int
    +amount: Float
    +date: DateTime
    +description: String
    +status: PaymentStatus
    +processPayment()
    +generateReceipt()
  }

  class Invoice {
    +id: Int
    +studentId: Int
    +amount: Float
    +dueDate: DateTime
    +isPaid: Boolean
    +generateInvoice()
    +sendReminder()
  }
}

' Relationships
User "1" -- "1" Profile : has
User "1" -- "0..1" Teacher : can be
User "1" -- "0..1" Parent : can be
Student "0..*" -- "1" Parent : has guardian
Student "0..*" -- "1" Classe : belongs to
Teacher "1" -- "0..*" Subject : teaches
Classe "1" -- "0..*" Subject : has
Student "1" -- "0..*" Attendance : has
Student "1" -- "0..*" QuranProgress : has
Student "1" -- "0..*" Payment : makes
Exam "1" -- "0..*" QuranProgress : evaluates
RemoteClass "0..*" -- "1" Teacher : taught by
RemoteClass "0..*" -- "1" Classe : for
QuranProgress "0..*" -- "1" Surah : references
Payment "0..*" -- "1" Invoice : fulfills
RemoteClass "1" -- "0..*" LearningResource : contains

' Enums
enum UserRole {
  ADMIN
  TEACHER
  STUDENT
  PARENT
  EMPLOYEE
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  EXCUSED
  LATE
}

enum ProgressStatus {
  NEW
  IN_PROGRESS
  COMPLETED
  NEEDS_REVIEW
  EXCELLENT
  GOOD
  AVERAGE
  NEEDS_IMPROVEMENT
}

enum EvaluationType {
  MEMORIZATION
  TAJWEED
  RECITATION
  REVISION
  COMPREHENSIVE
}

enum PaymentStatus {
  PENDING
  COMPLETED
  CANCELLED
  REFUNDED
  PARTIAL
}

enum RemoteClassStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  POSTPONED
}

enum ResourceType {
  AUDIO
  VIDEO
  DOCUMENT
  IMAGE
  INTERACTIVE
}

@enduml

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// GET /api/parent-invoices - جلب فواتير أبناء ولي الأمر المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'PARENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المستخدم والملف الشخصي
    const user = await prisma.user.findUnique({
      where: {
        id: userData.id
      },
      include: {
        profile: true
      }
    });

    if (!user || !user.profile) {
      console.error('لم يتم العثور على المستخدم أو الملف الشخصي:', userData.id);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المستخدم" },
        { status: 404 }
      );
    }

    // جلب معلومات ولي الأمر باستخدام اسم الملف الشخصي أو اسم المستخدم
    const parent = await prisma.parent.findFirst({
      where: {
        OR: [
          { name: user.profile.name },
          { name: userData.username }
        ]
      },
      include: {
        students: true
      }
    });

    if (!parent) {
      console.error('لم يتم العثور على ولي الأمر للمستخدم:', userData.username, 'مع الملف الشخصي:', user.profile.name);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات ولي الأمر" },
        { status: 404 }
      );
    }

    // التحقق من وجود أبناء
    if (!parent.students || parent.students.length === 0) {
      console.log('ولي الأمر موجود ولكن لا يوجد أبناء مسجلين:', parent.name);
      return NextResponse.json({
        children: [],
        message: "لا يوجد أبناء مسجلين لولي الأمر"
      });
    }

    // الحصول على معرف الطالب من الاستعلام
    const { searchParams } = new URL(request.url);
    const childId = searchParams.get('childId');

    // إذا تم تحديد معرف الطالب، تحقق من أنه ينتمي لولي الأمر
    if (childId) {
      const childIdNum = parseInt(childId);
      const isParentChild = parent.students.some(student => student.id === childIdNum);

      if (!isParentChild) {
        return NextResponse.json(
          { message: "غير مصرح بالوصول إلى بيانات هذا الطالب" },
          { status: 403 }
        );
      }

      // جلب بيانات الطالب المحدد
      const student = await prisma.student.findUnique({
        where: { id: childIdNum },
        include: {
          classe: true
        }
      });

      if (!student) {
        return NextResponse.json(
          { message: "لم يتم العثور على بيانات الطالب" },
          { status: 404 }
        );
      }

      // جلب فواتير الطالب
      const invoices = await prisma.invoice.findMany({
        where: {
          studentId: childIdNum
        },
        include: {
          payments: true
        },
        orderBy: {
          dueDate: 'desc'
        }
      });

      // حساب المبلغ المدفوع والمتبقي لكل فاتورة
      const formattedInvoices = invoices.map(invoice => {
        const paidAmount = invoice.payments.reduce((sum, payment) => {
          if (payment.status === 'PAID') {
            return sum + payment.amount;
          }
          return sum;
        }, 0);

        return {
          ...invoice,
          paidAmount,
          remainingAmount: invoice.amount - paidAmount
        };
      });

      // حساب إجمالي المدفوعات والمستحقات
      const totalPaid = formattedInvoices.reduce((sum, invoice) => sum + invoice.paidAmount, 0);
      const totalDue = formattedInvoices.reduce((sum, invoice) => sum + invoice.remainingAmount, 0);

      return NextResponse.json({
        student: {
          id: student.id,
          name: student.name,
          grade: student.classe?.name || 'غير محدد'
        },
        invoices: formattedInvoices,
        stats: {
          totalPaid,
          totalDue,
          totalInvoices: invoices.length
        },
        message: "تم جلب بيانات الفواتير بنجاح"
      });
    } else {
      // جلب ملخص فواتير جميع الأبناء
      const childrenInvoices = await Promise.all(parent.students.map(async (student) => {
        // جلب بيانات الطالب
        const studentData = await prisma.student.findUnique({
          where: { id: student.id },
          include: {
            classe: true
          }
        });

        if (!studentData) return null;

        // جلب فواتير الطالب
        const invoices = await prisma.invoice.findMany({
          where: {
            studentId: student.id
          },
          include: {
            payments: true
          },
          orderBy: {
            dueDate: 'desc'
          }
        });

        // حساب المبلغ المدفوع والمتبقي لكل فاتورة
        const formattedInvoices = invoices.map(invoice => {
          const paidAmount = invoice.payments.reduce((sum, payment) => {
            if (payment.status === 'PAID') {
              return sum + payment.amount;
            }
            return sum;
          }, 0);

          return {
            ...invoice,
            paidAmount,
            remainingAmount: invoice.amount - paidAmount
          };
        });

        // حساب إجمالي المدفوعات والمستحقات
        const totalPaid = formattedInvoices.reduce((sum, invoice) => sum + invoice.paidAmount, 0);
        const totalDue = formattedInvoices.reduce((sum, invoice) => sum + invoice.remainingAmount, 0);

        // جلب آخر فاتورة
        const lastInvoice = invoices.length > 0 ? formattedInvoices[0] : null;

        // جلب الفواتير المستحقة (غير مدفوعة أو مدفوعة جزئيًا)
        const dueInvoices = formattedInvoices.filter(invoice =>
          invoice.status === 'UNPAID' ||
          invoice.status === 'PARTIALLY_PAID' ||
          invoice.status === 'OVERDUE'
        );

        return {
          id: student.id,
          name: student.name,
          grade: studentData.classe?.name || 'غير محدد',
          invoices: formattedInvoices.slice(0, 3), // آخر 3 فواتير فقط
          stats: {
            totalPaid,
            totalDue,
            totalInvoices: invoices.length,
            dueInvoices: dueInvoices.length
          },
          lastInvoice
        };
      }));

      // فلترة القيم null
      const children = childrenInvoices.filter(child => child !== null);

      return NextResponse.json({
        children,
        message: "تم جلب بيانات الفواتير بنجاح"
      });
    }
  } catch (error) {
    console.error('Error fetching parent invoices:', error);
    // إضافة المزيد من التفاصيل للخطأ
    const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير معروف';
    return NextResponse.json(
      {
        message: "حدث خطأ أثناء جلب بيانات الفواتير",
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

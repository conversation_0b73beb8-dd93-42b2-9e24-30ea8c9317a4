'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Award, Medal, Trophy, Star, FileText, ListFilter } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AchievementCard from './components/AchievementCard';
import CertificateTemplates from './components/CertificateTemplates';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

type Student = {
  id: number;
  name: string;
  username: string;
  totalPoints: number;
  classe: {
    id: number;
    name: string;
  } | null;
};

type StudentReward = {
  id: number;
  studentId: number;
  rewardId: number;
  date: string;
  student: Student;
  reward: {
    id: number;
    name: string;
    type: string;
    requiredPoints: number;
  };
};

type StudentCertificate = {
  id: number;
  studentId: number;
  certificateId: number;
  issueDate: string;
  student: Student;
  certificate: {
    id: number;
    title: string;
    description?: string;
    templateUrl?: string;
    type: string;
  };
};

export default function HonorBoardPage() {
  const [topStudents, setTopStudents] = useState<Student[]>([]);
  const [recentRewards, setRecentRewards] = useState<StudentReward[]>([]);
  const [studentCertificates, setStudentCertificates] = useState<StudentCertificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [rewardsLoading, setRewardsLoading] = useState(true);
  const [certificatesLoading, setCertificatesLoading] = useState(true);
  const [isStudentDetailsOpen, setIsStudentDetailsOpen] = useState(false);
  const [isCertificatePreviewOpen, setIsCertificatePreviewOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  // نستخدم هذا المتغير فقط للتتبع، البيانات الفعلية في selectedCertificateData
  const [selectedCertificateData, setSelectedCertificateData] = useState<any | null>(null);
  const [studentRewards, setStudentRewards] = useState<StudentReward[]>([]);
  const [studentCertificatesList, setStudentCertificatesList] = useState<StudentCertificate[]>([]);

  useEffect(() => {
    fetchTopStudents();
    fetchRecentRewards();
    fetchRecentCertificates();
  }, []);

  const fetchTopStudents = async () => {
    try {
      setLoading(true);
      console.log('Fetching top students from honor board API...');
      const response = await fetch('/api/honor-board/top-students?limit=10');

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error('Failed to fetch top students');
      }

      const data = await response.json();
      console.log('Top students data received:', data);

      if (data.success && data.data) {
        setTopStudents(data.data);
        console.log('Top students set:', data.data.length);
      } else {
        console.error('Invalid data structure:', data);
        setTopStudents([]);
      }
    } catch (error) {
      console.error('Error fetching top students:', error);
      toast.error('حدث خطأ أثناء جلب قائمة الطلاب المتفوقين');
      setTopStudents([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchRecentRewards = async () => {
    try {
      setRewardsLoading(true);
      const response = await fetch('/api/student-rewards?limit=5');
      if (!response.ok) throw new Error('Failed to fetch recent rewards');
      const data = await response.json();
      setRecentRewards(data.data || []);
    } catch (error) {
      console.error('Error fetching recent rewards:', error);
      toast.error('حدث خطأ أثناء جلب المكافآت الأخيرة');
    } finally {
      setRewardsLoading(false);
    }
  };

  const fetchRecentCertificates = async () => {
    try {
      setCertificatesLoading(true);
      const response = await fetch('/api/student-certificates?limit=5');
      if (!response.ok) throw new Error('Failed to fetch recent certificates');
      const data = await response.json();
      setStudentCertificates(data.data || []);
    } catch (error) {
      console.error('Error fetching recent certificates:', error);
      toast.error('حدث خطأ أثناء جلب الشهادات الأخيرة');
    } finally {
      setCertificatesLoading(false);
    }
  };

  const fetchStudentRewards = async (studentId: number) => {
    try {
      const response = await fetch(`/api/student-rewards?studentId=${studentId}`);
      if (!response.ok) throw new Error('Failed to fetch student rewards');
      const data = await response.json();
      setStudentRewards(data.data || []);
    } catch (error) {
      console.error('Error fetching student rewards:', error);
      toast.error('حدث خطأ أثناء جلب مكافآت الطالب');
    }
  };

  const fetchStudentCertificates = async (studentId: number) => {
    try {
      const response = await fetch(`/api/student-certificates?studentId=${studentId}`);
      if (!response.ok) throw new Error('Failed to fetch student certificates');
      const data = await response.json();
      setStudentCertificatesList(data.data || []);
    } catch (error) {
      console.error('Error fetching student certificates:', error);
      toast.error('حدث خطأ أثناء جلب شهادات الطالب');
    }
  };

  const openStudentDetails = async (student: Student) => {
    setSelectedStudent(student);
    await Promise.all([
      fetchStudentRewards(student.id),
      fetchStudentCertificates(student.id)
    ]);
    setIsStudentDetailsOpen(true);
  };

  const fetchCertificateDetails = async (certificateId: number) => {
    try {
      // البحث عن الشهادة في قائمة الشهادات الحالية
      const certificate = studentCertificates.find(cert => cert.id === certificateId);

      if (certificate) {
        setSelectedCertificateData({
          id: certificate.id,
          title: certificate.certificate.title,
          description: certificate.certificate.description || '',
          templateUrl: certificate.certificate.templateUrl || '',
          type: certificate.certificate.type,
          student: certificate.student,
          issueDate: certificate.issueDate
        });
        return;
      }

      // إذا لم يتم العثور على الشهادة، نقوم بجلبها من الخادم
      const response = await fetch(`/api/student-certificates?id=${certificateId}`);
      if (!response.ok) throw new Error('Failed to fetch certificate details');

      const data = await response.json();
      const certData = data.data && data.data.length > 0 ? data.data[0] : null;

      if (certData) {
        setSelectedCertificateData({
          id: certData.id,
          title: certData.certificate.title,
          description: certData.certificate.description || '',
          templateUrl: certData.certificate.templateUrl || '',
          type: certData.certificate.type,
          student: certData.student,
          issueDate: certData.issueDate
        });
      } else {
        throw new Error('Certificate not found');
      }
    } catch (error) {
      console.error('Error fetching certificate details:', error);
      toast.error('حدث خطأ أثناء جلب تفاصيل الشهادة');
    }
  };

  const openCertificatePreview = async (certificateId: number) => {
    await fetchCertificateDetails(certificateId);
    setIsCertificatePreviewOpen(true);
  };

  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'CERTIFICATE':
        return <Award className="h-5 w-5 text-blue-500" />;
      case 'PRIZE':
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 'BADGE':
        return <Medal className="h-5 w-5 text-purple-500" />;
      default:
        return <Star className="h-5 w-5 text-primary-color" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.honor-board.view">
      <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">لوحة الشرف</h1>
        <QuickActionButtons
          entityType="honor-board"
          actions={[
            {
              key: 'criteria',
              label: 'معايير التقييم',
              icon: <ListFilter className="h-4 w-4" />,
              onClick: () => window.location.href = '/admin/honor-board/criteria',
              variant: 'secondary',
              permission: 'admin.honor-board.criteria'
            },
            {
              key: 'certificates',
              label: 'شهادات التقدير',
              icon: <FileText className="h-4 w-4" />,
              onClick: () => window.location.href = '/admin/honor-board/certificates',
              variant: 'secondary',
              permission: 'admin.honor-board.certificates'
            }
          ]}
          className="flex gap-2"
        />
      </div>

      <Tabs defaultValue="top-students" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="top-students">الطلاب المتفوقون</TabsTrigger>
          <TabsTrigger value="recent-rewards">المكافآت الأخيرة</TabsTrigger>
          <TabsTrigger value="certificates">شهادات التقدير</TabsTrigger>
        </TabsList>

        <TabsContent value="top-students">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : topStudents.length === 0 ? (
            <div className="text-center p-8 border rounded-lg">
              <p className="text-gray-500">لا توجد بيانات للطلاب المتفوقين حتى الآن</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {topStudents.slice(0, 3).map((student, index) => (
                <AchievementCard
                  key={student.id}
                  student={student}
                  rank={index + 1}
                  onViewDetails={openStudentDetails}
                  className={`
                    ${index === 0 ? 'border-yellow-400 shadow-yellow-100' : ''}
                    ${index === 1 ? 'border-gray-400 shadow-gray-100' : ''}
                    ${index === 2 ? 'border-amber-700 shadow-amber-100' : ''}
                  `}
                />
              ))}
            </div>
          )}

          {!loading && topStudents.length > 3 && (
            <div className="mt-8">
              <h2 className="text-xl font-bold mb-4 text-right">قائمة المتفوقين</h2>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">الترتيب</TableHead>
                    <TableHead className="text-right">الطالب</TableHead>
                    <TableHead className="text-right">القسم</TableHead>
                    <TableHead className="text-right">النقاط</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {topStudents.slice(3).map((student, index) => (
                    <TableRow key={student.id}>
                      <TableCell>{index + 4}</TableCell>
                      <TableCell>{student.name}</TableCell>
                      <TableCell>{student.classe?.name || '-'}</TableCell>
                      <TableCell>{student.totalPoints}</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openStudentDetails(student)}
                        >
                          عرض التفاصيل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="recent-rewards">
          {rewardsLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : recentRewards.length === 0 ? (
            <div className="text-center p-8 border rounded-lg">
              <p className="text-gray-500">لا توجد مكافآت ممنوحة حتى الآن</p>
            </div>
          ) : (
            <div className="mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">المكافأة</TableHead>
                    <TableHead className="text-right">الطالب</TableHead>
                    <TableHead className="text-right">القسم</TableHead>
                    <TableHead className="text-right">النقاط المطلوبة</TableHead>
                    <TableHead className="text-right">تاريخ المنح</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentRewards.map((reward) => (
                    <TableRow key={reward.id}>
                      <TableCell className="flex items-center">
                        {getRewardIcon(reward.reward.type)}
                        <span className="mr-2">{reward.reward.name}</span>
                      </TableCell>
                      <TableCell>{reward.student.name}</TableCell>
                      <TableCell>{reward.student.classe?.name || '-'}</TableCell>
                      <TableCell>{reward.reward.requiredPoints}</TableCell>
                      <TableCell>{formatDate(reward.date)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </TabsContent>

        <TabsContent value="certificates">
          {certificatesLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : studentCertificates.length === 0 ? (
            <div className="text-center p-8 border rounded-lg">
              <p className="text-gray-500">لا توجد شهادات ممنوحة حتى الآن</p>
            </div>
          ) : (
            <div className="mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">الشهادة</TableHead>
                    <TableHead className="text-right">الطالب</TableHead>
                    <TableHead className="text-right">القسم</TableHead>
                    <TableHead className="text-right">النوع</TableHead>
                    <TableHead className="text-right">تاريخ الإصدار</TableHead>
                    <TableHead className="text-right">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {studentCertificates.map((cert) => (
                    <TableRow key={cert.id}>
                      <TableCell className="flex items-center">
                        <FileText className="h-5 w-5 text-blue-500 ml-2" />
                        <span>{cert.certificate.title}</span>
                      </TableCell>
                      <TableCell>{cert.student.name}</TableCell>
                      <TableCell>{cert.student.classe?.name || '-'}</TableCell>
                      <TableCell>{cert.certificate.type}</TableCell>
                      <TableCell>{formatDate(cert.issueDate)}</TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openCertificatePreview(cert.id)}
                        >
                          عرض الشهادة
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Student Details Dialog */}
      <Dialog open={isStudentDetailsOpen} onOpenChange={(open) => !open && setIsStudentDetailsOpen(false)}>
        <DialogContent className="w-[95vw] max-w-3xl max-h-[90vh] overflow-hidden p-4 sm:p-6">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-2">
            <DialogTitle className="text-lg sm:text-xl">تفاصيل الطالب</DialogTitle>
            {selectedStudent && (
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mt-2">
                <div>
                  <h3 className="text-base font-bold">{selectedStudent.name}</h3>
                  <p className="text-sm text-gray-500">{selectedStudent.classe?.name || '-'}</p>
                </div>
                <div className="mt-2 md:mt-0 text-center">
                  <p className="text-xs text-gray-500">إجمالي النقاط</p>
                  <p className="text-2xl font-bold text-[var(--primary-color)]">{selectedStudent.totalPoints}</p>
                </div>
              </div>
            )}
          </DialogHeader>

          <div className="py-4 overflow-y-auto max-h-[calc(90vh-12rem)]">
            {selectedStudent && (
              <Tabs defaultValue="rewards" className="w-full">
                <TabsList className="grid w-full grid-cols-2 sticky top-0 z-10 bg-white">
                  <TabsTrigger value="rewards">المكافآت المستلمة</TabsTrigger>
                  <TabsTrigger value="certificates">الشهادات</TabsTrigger>
                </TabsList>

                <TabsContent value="rewards">
                  {studentRewards.length === 0 ? (
                    <p className="text-center text-gray-500 p-4">لم يحصل الطالب على أي مكافآت حتى الآن</p>
                  ) : (
                    <div className="responsive-table-container">
                      <Table className="card-mode-table">
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-right">المكافأة</TableHead>
                            <TableHead className="text-right">النقاط المطلوبة</TableHead>
                            <TableHead className="text-right">تاريخ المنح</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {studentRewards.map((reward) => (
                            <TableRow key={reward.id}>
                              <TableCell className="flex items-center" data-label="المكافأة">
                                {getRewardIcon(reward.reward.type)}
                                <span className="mr-2">{reward.reward.name}</span>
                              </TableCell>
                              <TableCell data-label="النقاط المطلوبة">{reward.reward.requiredPoints}</TableCell>
                              <TableCell data-label="تاريخ المنح">{formatDate(reward.date)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="certificates">
                  {studentCertificatesList.length === 0 ? (
                    <p className="text-center text-gray-500 p-4">لم يحصل الطالب على أي شهادات حتى الآن</p>
                  ) : (
                    <div className="responsive-table-container">
                      <Table className="card-mode-table">
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-right">الشهادة</TableHead>
                            <TableHead className="text-right hide-on-mobile">النوع</TableHead>
                            <TableHead className="text-right hide-on-mobile">تاريخ الإصدار</TableHead>
                            <TableHead className="text-right">الإجراءات</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {studentCertificatesList.map((cert) => (
                            <TableRow key={cert.id}>
                              <TableCell className="flex items-center" data-label="الشهادة">
                                <FileText className="h-5 w-5 text-blue-500 ml-2" />
                                <span>{cert.certificate.title}</span>
                              </TableCell>
                              <TableCell className="hide-on-mobile" data-label="النوع">{cert.certificate.type}</TableCell>
                              <TableCell className="hide-on-mobile" data-label="تاريخ الإصدار">{formatDate(cert.issueDate)}</TableCell>
                              <TableCell className="actions" data-label="الإجراءات">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openCertificatePreview(cert.id)}
                                  className="w-full sm:w-auto"
                                >
                                  عرض الشهادة
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            )}
          </div>

          <div className="flex justify-end sticky bottom-0 bg-white pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsStudentDetailsOpen(false)}
              className="w-full sm:w-auto"
            >
              إغلاق
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Certificate Preview Dialog */}
      <Dialog open={isCertificatePreviewOpen} onOpenChange={(open) => !open && setIsCertificatePreviewOpen(false)}>
        <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-hidden p-4 sm:p-6">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-2">
            <DialogTitle className="text-lg sm:text-xl">معاينة الشهادة</DialogTitle>
            {selectedCertificateData && (
              <p className="text-sm text-gray-500">
                {selectedCertificateData.title} - {selectedCertificateData.student?.name}
              </p>
            )}
          </DialogHeader>
          <div className="py-4 overflow-y-auto max-h-[calc(90vh-12rem)]">
            {selectedCertificateData ? (
              <div className="min-w-[320px]">
                <CertificateTemplates
                  certificateData={{
                    id: selectedCertificateData.id,
                    title: selectedCertificateData.title,
                    description: selectedCertificateData.description,
                    templateUrl: selectedCertificateData.templateUrl,
                    type: selectedCertificateData.type,
                    student: selectedCertificateData.student,
                    issueDate: selectedCertificateData.issueDate
                  }}
                  selectedTemplate={selectedCertificateData.type}
                  onSelectTemplate={() => {}}
                />
              </div>
            ) : (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
                <span className="mr-2">جاري تحميل الشهادة...</span>
              </div>
            )}
          </div>
          <div className="flex justify-end sticky bottom-0 bg-white pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsCertificatePreviewOpen(false)}
              className="w-full sm:w-auto"
            >
              إغلاق
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

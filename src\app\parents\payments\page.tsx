"use client";
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  FaMoneyBillWave,
  FaUserGraduate,
  FaArrowLeft,
  FaCheckCircle,
  FaExclamationCircle,
  FaFileInvoiceDollar,
  FaFilePdf,
  FaPrint,
  FaCreditCard
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PaymentForm from './PaymentForm';

interface Payment {
  id: number;
  amount: number;
  date: string;
  month: string;
  year: string;
  status: 'PAID' | 'PENDING' | 'CANCELLED';
  notes: string;
}

interface Invoice {
  id: number;
  amount: number;
  dueDate: string;
  issueDate: string;
  month: number;
  year: number;
  description?: string;
  status: string;
  paidAmount: number;
  remainingAmount: number;
}

interface Student {
  id: number;
  name: string;
  grade: string;
}

interface PaymentStats {
  totalPaid: number;
  totalPending: number;
}

interface InvoiceStats {
  totalPaid: number;
  totalDue: number;
  totalInvoices: number;
  dueInvoices?: number;
}

interface ChildPayments {
  id: number;
  name: string;
  grade: string;
  payments: Payment[];
  stats: PaymentStats;
  lastPayment: Payment | null;
}

interface ChildInvoices {
  id: number;
  name: string;
  grade: string;
  invoices: Invoice[];
  stats: InvoiceStats;
  lastInvoice: Invoice | null;
}

const ParentPaymentsPage = () => {
  const searchParams = useSearchParams() as URLSearchParams;
  const childId = searchParams.get('childId');

  const [student, setStudent] = useState<Student | null>(null);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [paymentStats, setPaymentStats] = useState<PaymentStats | null>(null);
  const [invoiceStats, setInvoiceStats] = useState<InvoiceStats | null>(null);
  const [childrenPayments, setChildrenPayments] = useState<ChildPayments[]>([]);
  const [childrenInvoices, setChildrenInvoices] = useState<ChildInvoices[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('invoices');
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب الفواتير
        const invoicesUrl = childId
          ? `/api/parent-invoices?childId=${childId}`
          : '/api/parent-invoices';

        const invoicesResponse = await fetch(invoicesUrl);

        if (!invoicesResponse.ok) {
          // محاولة قراءة رسالة الخطأ من الاستجابة
          const errorData = await invoicesResponse.json();
          const errorMessage = errorData?.message || 'فشل في جلب بيانات الفواتير';
          throw new Error(errorMessage);
        }

        const invoicesData = await invoicesResponse.json();
        console.log('Invoices data received:', invoicesData);

        // جلب المدفوعات
        const paymentsUrl = childId
          ? `/api/parent-payments?childId=${childId}`
          : '/api/parent-payments';

        const paymentsResponse = await fetch(paymentsUrl);

        if (!paymentsResponse.ok) {
          // محاولة قراءة رسالة الخطأ من الاستجابة
          const errorData = await paymentsResponse.json();
          const errorMessage = errorData?.message || 'فشل في جلب بيانات المدفوعات';
          throw new Error(errorMessage);
        }

        const paymentsData = await paymentsResponse.json();
        console.log('Payments data received:', paymentsData);

        // التحقق من وجود بيانات
        if (childId) {
          // إذا تم تحديد طالب معين
          const student = invoicesData.student || paymentsData.student;
          if (!student) {
            console.warn('No student data in response');
            setError('لم يتم العثور على بيانات الطالب');
          } else {
            setStudent(student);
            setInvoices(invoicesData.invoices || []);
            setPayments(paymentsData.payments || []);
            setInvoiceStats(invoicesData.stats || null);
            setPaymentStats(paymentsData.stats || null);
          }
        } else {
          // إذا لم يتم تحديد طالب (عرض جميع الأبناء)
          if (!invoicesData.children && !paymentsData.children) {
            console.warn('No children data in response');
            setChildrenInvoices([]);
            setChildrenPayments([]);
          } else {
            setChildrenInvoices(invoicesData.children || []);
            setChildrenPayments(paymentsData.children || []);
          }
        }
      } catch (err) {
        console.error('Error fetching data:', err);

        // محاولة الحصول على رسالة خطأ أكثر تفصيلاً من الخادم
        let errorMessage = 'حدث خطأ أثناء جلب البيانات';

        if (err instanceof Error) {
          errorMessage = err.message;
        } else if (err instanceof Response) {
          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await err.json();
            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (jsonError) {
            console.error('Error parsing error response:', jsonError);
          }
        }

        setError(errorMessage);
        toast.error('فشل في جلب البيانات: ' + errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [childId]);

  // تنسيق المبلغ كعملة
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-DZ', {
      style: 'currency',
      currency: 'DZD'
    }).format(amount);
  };

  // وظيفة لفتح نموذج الدفع
  const handleOpenPaymentForm = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setIsPaymentModalOpen(true);
  };

  // وظيفة لإغلاق نموذج الدفع
  const handleClosePaymentForm = () => {
    setIsPaymentModalOpen(false);
    setSelectedInvoice(null);
  };

  // وظيفة لتحديث البيانات بعد إتمام الدفع
  const handlePaymentSuccess = () => {
    // إعادة تحميل البيانات
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب الفواتير
        const invoicesUrl = childId
          ? `/api/parent-invoices?childId=${childId}`
          : '/api/parent-invoices';

        const invoicesResponse = await fetch(invoicesUrl);

        if (!invoicesResponse.ok) {
          throw new Error('فشل في جلب بيانات الفواتير');
        }

        const invoicesData = await invoicesResponse.json();

        // جلب المدفوعات
        const paymentsUrl = childId
          ? `/api/parent-payments?childId=${childId}`
          : '/api/parent-payments';

        const paymentsResponse = await fetch(paymentsUrl);

        if (!paymentsResponse.ok) {
          throw new Error('فشل في جلب بيانات المدفوعات');
        }

        const paymentsData = await paymentsResponse.json();

        if (childId) {
          // إذا تم تحديد طالب معين
          setStudent(invoicesData.student || paymentsData.student);
          setInvoices(invoicesData.invoices || []);
          setPayments(paymentsData.payments || []);
          setInvoiceStats(invoicesData.stats || null);
          setPaymentStats(paymentsData.stats || null);
        } else {
          // إذا لم يتم تحديد طالب (عرض جميع الأبناء)
          setChildrenInvoices(invoicesData.children || []);
          setChildrenPayments(paymentsData.children || []);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('حدث خطأ أثناء جلب البيانات');
        toast.error('فشل في جلب البيانات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  };

  // طباعة الفاتورة
  const handlePrintInvoice = (id: number) => {
    const printWindow = window.open(`/api/invoices/pdf/${id}`, '_blank');
    if (printWindow) {
      printWindow.onload = () => {
        printWindow.print();
      };
    } else {
      toast.error('تعذر فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح.');
    }
  };

  // عرض تفاصيل مدفوعات وفواتير طالب محدد
  const renderStudentPayments = () => {
    if (!student) return null;

    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-xl md:text-2xl font-bold flex items-center gap-2">
              <FaMoneyBillWave className="text-[var(--primary-color)]" />
              <span>مدفوعات {student.name}</span>
            </h1>
            <p className="text-gray-500">
              الصف: {student.grade}
            </p>
          </div>
          <Link href="/parents/payments">
            <Button variant="outline" className="flex items-center gap-2 w-full md:w-auto">
              <FaArrowLeft />
              <span>العودة إلى جميع الأبناء</span>
            </Button>
          </Link>
        </div>

        {/* ملخص المدفوعات والفواتير */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-2 md:p-3 rounded-full bg-green-100 text-primary-color">
                  <FaCheckCircle className="text-lg md:text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">إجمالي المدفوعات</p>
                  <p className="text-xl md:text-2xl font-bold text-gray-800">
                    {invoiceStats ? formatCurrency(invoiceStats.totalPaid) :
                     paymentStats ? formatCurrency(paymentStats.totalPaid) :
                     formatCurrency(0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-2 md:p-3 rounded-full bg-yellow-100 text-yellow-500">
                  <FaExclamationCircle className="text-lg md:text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">المستحقات</p>
                  <p className="text-xl md:text-2xl font-bold text-gray-800">
                    {invoiceStats ? formatCurrency(invoiceStats.totalDue) :
                     paymentStats ? formatCurrency(paymentStats.totalPending) :
                     formatCurrency(0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* علامات التبويب للفواتير والمدفوعات */}
        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="invoices" className="flex items-center gap-2">
              <FaFileInvoiceDollar />
              <span>الفواتير</span>
            </TabsTrigger>
            <TabsTrigger value="payments" className="flex items-center gap-2">
              <FaMoneyBillWave />
              <span>المدفوعات</span>
            </TabsTrigger>
          </TabsList>

          {/* محتوى الفواتير */}
          <TabsContent value="invoices">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaFileInvoiceDollar className="text-[var(--primary-color)]" />
                  <span>الفواتير</span>
                </CardTitle>
                <CardDescription>
                  عرض جميع الفواتير المستحقة والمدفوعة
                </CardDescription>
              </CardHeader>
              <CardContent>
                {invoices.length === 0 ? (
                  <div className="text-center text-gray-500 py-4">
                    لا يوجد فواتير
                  </div>
                ) : (
                  <div className="responsive-table-container">
                    <table className="w-full border-collapse card-mode-table">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border p-2 text-right">رقم الفاتورة</th>
                          <th className="border p-2 text-right">الشهر/السنة</th>
                          <th className="border p-2 text-right">المبلغ</th>
                          <th className="border p-2 text-right">المدفوع</th>
                          <th className="border p-2 text-right">المتبقي</th>
                          <th className="border p-2 text-right">تاريخ الاستحقاق</th>
                          <th className="border p-2 text-right">الحالة</th>
                          <th className="border p-2 text-right">الإجراءات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {invoices.map(invoice => (
                          <tr key={invoice.id} className="hover:bg-gray-50">
                            <td className="border p-2" data-label="رقم الفاتورة">{invoice.id}</td>
                            <td className="border p-2" data-label="الشهر/السنة">{invoice.month}/{invoice.year}</td>
                            <td className="border p-2" data-label="المبلغ">{formatCurrency(invoice.amount)}</td>
                            <td className="border p-2" data-label="المدفوع">{formatCurrency(invoice.paidAmount)}</td>
                            <td className="border p-2" data-label="المتبقي">{formatCurrency(invoice.remainingAmount)}</td>
                            <td className="border p-2" data-label="تاريخ الاستحقاق">{new Date(invoice.dueDate).toLocaleDateString('fr-FR')}</td>
                            <td className="border p-2" data-label="الحالة">
                              {invoice.status === 'PAID' && (
                                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                  مدفوعة
                                </span>
                              )}
                              {invoice.status === 'PARTIALLY_PAID' && (
                                <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                  مدفوعة جزئيًا
                                </span>
                              )}
                              {invoice.status === 'UNPAID' && (
                                <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                                  غير مدفوعة
                                </span>
                              )}
                              {invoice.status === 'OVERDUE' && (
                                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                  متأخرة
                                </span>
                              )}
                              {invoice.status === 'CANCELLED' && (
                                <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">
                                  ملغاة
                                </span>
                              )}
                            </td>
                            <td className="border p-2 actions" data-label="الإجراءات">
                              <div className="flex gap-2 mobile-action-buttons">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => window.open(`/api/invoices/pdf/${invoice.id}`, '_blank')}
                                  title="عرض الفاتورة"
                                >
                                  <FaFilePdf className="text-red-500" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handlePrintInvoice(invoice.id)}
                                  title="طباعة الفاتورة"
                                >
                                  <FaPrint className="text-blue-500" />
                                </Button>
                                {(invoice.status === 'UNPAID' || invoice.status === 'PARTIALLY_PAID' || invoice.status === 'OVERDUE') && (
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleOpenPaymentForm(invoice)}
                                    title="دفع الفاتورة"
                                    className="text-primary-color"
                                  >
                                    <FaCreditCard />
                                  </Button>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* محتوى المدفوعات */}
          <TabsContent value="payments">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaMoneyBillWave className="text-[var(--primary-color)]" />
                  <span>سجل المدفوعات</span>
                </CardTitle>
                <CardDescription>
                  عرض سجل جميع المدفوعات
                </CardDescription>
              </CardHeader>
              <CardContent>
                {payments.length === 0 ? (
                  <div className="text-center text-gray-500 py-4">
                    لا يوجد سجل للمدفوعات
                  </div>
                ) : (
                  <div className="responsive-table-container">
                    <table className="w-full border-collapse card-mode-table">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border p-2 text-right">التاريخ</th>
                          <th className="border p-2 text-right">الشهر/السنة</th>
                          <th className="border p-2 text-right">المبلغ</th>
                          <th className="border p-2 text-right">الحالة</th>
                          <th className="border p-2 text-right">ملاحظات</th>
                        </tr>
                      </thead>
                      <tbody>
                        {payments.map((payment) => (
                          <tr key={payment.id}>
                            <td className="border p-2" data-label="التاريخ">
                              {new Date(payment.date).toLocaleDateString('fr-FR')}
                            </td>
                            <td className="border p-2" data-label="الشهر/السنة">
                              {payment.month}/{payment.year}
                            </td>
                            <td className="border p-2" data-label="المبلغ">
                              {formatCurrency(payment.amount)}
                            </td>
                            <td className="border p-2" data-label="الحالة">
                              {payment.status === 'PAID' && (
                                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                  <FaCheckCircle className="mr-1" />
                                  مدفوع
                                </span>
                              )}
                              {payment.status === 'PENDING' && (
                                <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                                  <FaExclamationCircle className="mr-1" />
                                  مستحق
                                </span>
                              )}
                              {payment.status === 'CANCELLED' && (
                                <span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
                                  ملغي
                                </span>
                              )}
                            </td>
                            <td className="border p-2" data-label="ملاحظات">
                              {payment.notes || '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  // عرض ملخص مدفوعات وفواتير جميع الأبناء
  const renderChildrenPayments = () => {
    // حساب إجمالي الديون لجميع الأبناء
    const totalDebts = childrenInvoices.reduce((total, child) => total + child.stats.totalDue, 0);
    const totalPaid = childrenInvoices.reduce((total, child) => total + child.stats.totalPaid, 0);
    const totalDueInvoices = childrenInvoices.reduce((total, child) => total + (child.stats.dueInvoices || 0), 0);

    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-xl md:text-2xl font-bold flex items-center gap-2">
            <FaMoneyBillWave className="text-[var(--primary-color)]" />
            إدارة مدفوعات الأبناء
          </h1>
          <p className="text-gray-500">عرض وإدارة الديون والمدفوعات لجميع أبنائك</p>
        </div>

        {/* ملخص إجمالي الديون والمدفوعات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-red-100 text-red-600">
                  <FaExclamationCircle className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-red-600 font-medium">إجمالي الديون المستحقة</p>
                  <p className="text-2xl font-bold text-red-700">{formatCurrency(totalDebts)}</p>
                  {totalDueInvoices > 0 && (
                    <p className="text-xs text-red-500">{totalDueInvoices} فاتورة مستحقة</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-green-100 text-green-600">
                  <FaCheckCircle className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-green-600 font-medium">إجمالي المدفوعات</p>
                  <p className="text-2xl font-bold text-green-700">{formatCurrency(totalPaid)}</p>
                  <p className="text-xs text-green-500">المبالغ المدفوعة</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                  <FaUserGraduate className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-blue-600 font-medium">عدد الأبناء</p>
                  <p className="text-2xl font-bold text-blue-700">{childrenInvoices.length}</p>
                  <p className="text-xs text-blue-500">طالب مسجل</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {childrenPayments.length === 0 && childrenInvoices.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-8">
                <FaUserGraduate className="text-4xl mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">لا يوجد أبناء مسجلين</h3>
                <p className="text-sm">يرجى التواصل مع الإدارة لربط حسابك بأبنائك</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {childrenInvoices.map((child) => {
              // البحث عن بيانات المدفوعات المقابلة
              const paymentChild = childrenPayments.find(p => p.id === child.id);

              return (
                <Card key={child.id} className={`border-l-4 ${child.stats.totalDue > 0 ? 'border-l-red-400' : 'border-l-green-400'}`}>
                  <CardHeader>
                    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                      <div>
                        <CardTitle className="flex items-center gap-2 text-lg md:text-xl">
                          <FaUserGraduate className="text-[var(--primary-color)]" />
                          <span>{child.name}</span>
                          {child.stats.totalDue > 0 && (
                            <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                              ديون مستحقة
                            </span>
                          )}
                        </CardTitle>
                        <CardDescription>
                          الصف: {child.grade}
                        </CardDescription>
                      </div>
                      {child.stats.totalDue > 0 && (
                        <div className="text-right">
                          <p className="text-sm text-red-600 font-medium">إجمالي الديون</p>
                          <p className="text-lg font-bold text-red-700">{formatCurrency(child.stats.totalDue)}</p>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-green-50 p-3 md:p-4 rounded-lg border border-green-200">
                        <div className="flex items-center gap-2 md:gap-3 mb-2">
                          <div className="p-1.5 md:p-2 bg-green-100 rounded-full">
                            <FaCheckCircle className="text-green-600 text-sm md:text-base" />
                          </div>
                          <h3 className="font-medium text-sm md:text-base text-green-800">المدفوعات</h3>
                        </div>
                        <p className="text-xl md:text-2xl font-bold text-green-700">{formatCurrency(child.stats.totalPaid)}</p>
                        <p className="text-xs text-green-600 mt-1">المبالغ المدفوعة</p>
                      </div>

                      <div className={`p-3 md:p-4 rounded-lg border ${child.stats.totalDue > 0 ? 'bg-red-50 border-red-200' : 'bg-gray-50 border-gray-200'}`}>
                        <div className="flex items-center gap-2 md:gap-3 mb-2">
                          <div className={`p-1.5 md:p-2 rounded-full ${child.stats.totalDue > 0 ? 'bg-red-100' : 'bg-gray-100'}`}>
                            <FaExclamationCircle className={`text-sm md:text-base ${child.stats.totalDue > 0 ? 'text-red-600' : 'text-gray-500'}`} />
                          </div>
                          <h3 className={`font-medium text-sm md:text-base ${child.stats.totalDue > 0 ? 'text-red-800' : 'text-gray-700'}`}>المستحقات</h3>
                        </div>
                        <p className={`text-xl md:text-2xl font-bold ${child.stats.totalDue > 0 ? 'text-red-700' : 'text-gray-600'}`}>
                          {formatCurrency(child.stats.totalDue)}
                        </p>
                        <p className={`text-xs mt-1 ${child.stats.totalDue > 0 ? 'text-red-600' : 'text-gray-500'}`}>
                          {child.stats.totalDue > 0 ? 'مبالغ مستحقة' : 'لا توجد ديون'}
                        </p>
                      </div>

                      <div className="bg-blue-50 p-3 md:p-4 rounded-lg border border-blue-200">
                        <div className="flex items-center gap-2 md:gap-3 mb-2">
                          <div className="p-1.5 md:p-2 bg-blue-100 rounded-full">
                            <FaFileInvoiceDollar className="text-blue-600 text-sm md:text-base" />
                          </div>
                          <h3 className="font-medium text-sm md:text-base text-blue-800">الفواتير</h3>
                        </div>
                        <p className="text-xl md:text-2xl font-bold text-blue-700">{child.stats.totalInvoices}</p>
                        <p className="text-xs text-blue-600 mt-1">
                          {child.stats.dueInvoices && child.stats.dueInvoices > 0
                            ? `${child.stats.dueInvoices} مستحقة`
                            : 'جميعها مدفوعة'}
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      {child.lastInvoice && (
                        <div className="border p-3 md:p-4 rounded-lg">
                          <h3 className="font-medium mb-2 flex items-center gap-2 text-sm md:text-base">
                            <FaFileInvoiceDollar className="text-[var(--primary-color)]" />
                            <span>آخر فاتورة</span>
                          </h3>
                          <div className="flex flex-col md:flex-row justify-between md:items-center gap-2">
                            <div>
                              <p className="text-gray-700 text-sm md:text-base">
                                {child.lastInvoice.month}/{child.lastInvoice.year}
                              </p>
                              <p className="text-xs md:text-sm text-gray-500">
                                تاريخ الاستحقاق: {new Date(child.lastInvoice.dueDate).toLocaleDateString('fr-FR')}
                              </p>
                            </div>
                            <div className="flex flex-row md:flex-col items-center md:items-end gap-2 md:gap-0">
                              <p className="font-bold text-gray-800 text-sm md:text-base">{formatCurrency(child.lastInvoice.amount)}</p>
                              <span className={`text-xs px-2 py-0.5 rounded-full ${
                                child.lastInvoice.status === 'PAID' ? 'bg-green-100 text-green-800' :
                                child.lastInvoice.status === 'PARTIALLY_PAID' ? 'bg-blue-100 text-blue-800' :
                                child.lastInvoice.status === 'OVERDUE' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {child.lastInvoice.status === 'PAID' ? 'مدفوعة' :
                                 child.lastInvoice.status === 'PARTIALLY_PAID' ? 'مدفوعة جزئيًا' :
                                 child.lastInvoice.status === 'OVERDUE' ? 'متأخرة' :
                                 'غير مدفوعة'}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      {paymentChild && paymentChild.lastPayment && (
                        <div className="border p-3 md:p-4 rounded-lg">
                          <h3 className="font-medium mb-2 flex items-center gap-2 text-sm md:text-base">
                            <FaMoneyBillWave className="text-[var(--primary-color)]" />
                            <span>آخر مدفوعة</span>
                          </h3>
                          <div className="flex flex-col md:flex-row justify-between md:items-center gap-2">
                            <div>
                              <p className="text-gray-700 text-sm md:text-base">
                                {paymentChild.lastPayment.month}/{paymentChild.lastPayment.year}
                              </p>
                              <p className="text-xs md:text-sm text-gray-500">
                                {new Date(paymentChild.lastPayment.date).toLocaleDateString('fr-FR')}
                              </p>
                            </div>
                            <div className="flex flex-row md:flex-col items-center md:items-end gap-2 md:gap-0">
                              <p className="font-bold text-gray-800 text-sm md:text-base">{formatCurrency(paymentChild.lastPayment.amount)}</p>
                              <span className={`text-xs px-2 py-0.5 rounded-full ${
                                paymentChild.lastPayment.status === 'PAID'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {paymentChild.lastPayment.status === 'PAID' ? 'مدفوع' : 'مستحق'}
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col md:flex-row justify-between items-center gap-3 pt-4 border-t border-gray-100">
                      <div className="text-sm w-full md:w-auto text-center md:text-right">
                        {child.stats.dueInvoices && child.stats.dueInvoices > 0 ? (
                          <div className="bg-red-50 p-2 rounded-lg border border-red-200">
                            <span className="text-red-600 font-medium flex items-center gap-1">
                              <FaExclamationCircle className="text-red-500" />
                              لديك {child.stats.dueInvoices} فاتورة مستحقة بقيمة {formatCurrency(child.stats.totalDue)}
                            </span>
                          </div>
                        ) : (
                          <div className="bg-green-50 p-2 rounded-lg border border-green-200">
                            <span className="text-green-600 font-medium flex items-center gap-1">
                              <FaCheckCircle className="text-green-500" />
                              جميع الفواتير مدفوعة
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="flex gap-2 w-full md:w-auto">
                        {child.stats.totalDue > 0 && (
                          <Button
                            onClick={() => {
                              // فتح نموذج دفع سريع للطفل
                              if (child.lastInvoice && child.lastInvoice.remainingAmount > 0) {
                                setSelectedInvoice({
                                  ...child.lastInvoice,
                                  remainingAmount: child.lastInvoice.remainingAmount
                                });
                                setStudent({ id: child.id, name: child.name, grade: child.grade });
                                setIsPaymentModalOpen(true);
                              }
                            }}
                            className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2"
                            size="sm"
                          >
                            <FaCreditCard />
                            دفع سريع
                          </Button>
                        )}
                        <Link href={`/parents/payments?childId=${child.id}`} className="w-full md:w-auto">
                          <Button
                            variant="outline"
                            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white w-full md:w-auto flex items-center gap-2"
                          >
                            <FaFileInvoiceDollar />
                            عرض التفاصيل
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto px-4 py-6 space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : childId ? (
        <>
          {renderStudentPayments()}
          {student && selectedInvoice && (
            <PaymentForm
              isOpen={isPaymentModalOpen}
              onClose={handleClosePaymentForm}
              onSuccess={handlePaymentSuccess}
              studentId={student.id}
              studentName={student.name}
              invoice={selectedInvoice}
            />
          )}
        </>
      ) : (
        renderChildrenPayments()
      )}
    </div>
  );
};

export default ParentPaymentsPage;

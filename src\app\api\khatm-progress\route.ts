import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/khatm-progress - الحصول على سجلات تقدم الحفظ
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const khatmSessionId = searchParams.get('khatmSessionId');
    const attendanceId = searchParams.get('attendanceId');
    const id = searchParams.get('id');

    // بناء شروط البحث
    const where: {
      id?: number;
      khatmSessionId?: number;
      attendanceId?: number;
    } = {};

    // تحويل المعرفات إلى أرقام إذا كانت موجودة
    if (id) {
      const idNum = parseInt(id);
      if (isNaN(idNum)) {
        return NextResponse.json({
          success: false,
          error: 'معرف سجل التقدم يجب أن يكون رقماً صحيحاً'
        }, { status: 400 });
      }
      where.id = idNum;
    }

    if (khatmSessionId) {
      const khatmSessionIdNum = parseInt(khatmSessionId);
      if (isNaN(khatmSessionIdNum)) {
        return NextResponse.json({
          success: false,
          error: 'معرف مجلس الختم يجب أن يكون رقماً صحيحاً'
        }, { status: 400 });
      }
      where.khatmSessionId = khatmSessionIdNum;
    }

    if (attendanceId) {
      const attendanceIdNum = parseInt(attendanceId);
      if (isNaN(attendanceIdNum)) {
        return NextResponse.json({
          success: false,
          error: 'معرف الحضور يجب أن يكون رقماً صحيحاً'
        }, { status: 400 });
      }
      where.attendanceId = attendanceIdNum;
    }

    // إذا لم يتم تحديد أي معايير بحث
    if (Object.keys(where).length === 0) {
      return NextResponse.json({
        success: false,
        error: 'يجب تحديد معرف مجلس الختم أو معرف الحضور أو معرف سجل التقدم'
      }, { status: 400 });
    }

    const progressRecords = await prisma.khatmProgressRecord.findMany({
      where,
      include: {
        attendance: {
          select: {
            id: true,
            student: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: progressRecords,
      message: 'تم جلب سجلات تقدم الحفظ بنجاح'
    });
  } catch (error) {
    console.error('Error fetching khatm progress records:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب سجلات تقدم الحفظ'
    }, { status: 500 });
  }
}

// POST /api/khatm-progress - إنشاء سجل تقدم حفظ جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { khatmSessionId, attendanceId, startAyah, endAyah, memorizedAyahs, reviewedAyahs, qualityRating, notes } = body;

    // التحقق من البيانات المطلوبة
    if (!khatmSessionId || !attendanceId || startAyah === undefined || endAyah === undefined) {
      return NextResponse.json({
        success: false,
        error: 'معرف مجلس الختم ومعرف الحضور ورقم الآية البداية والنهاية مطلوبة'
      }, { status: 400 });
    }

    // تحويل القيم إلى أرقام
    const khatmSessionIdNum = parseInt(khatmSessionId);
    const attendanceIdNum = parseInt(attendanceId);
    const startAyahNum = parseInt(startAyah);
    const endAyahNum = parseInt(endAyah);
    const memorizedAyahsNum = memorizedAyahs ? parseInt(memorizedAyahs) : 0;
    const reviewedAyahsNum = reviewedAyahs ? parseInt(reviewedAyahs) : 0;
    const qualityRatingNum = qualityRating ? parseInt(qualityRating) : 3;

    // التحقق من صحة القيم العددية
    if (isNaN(khatmSessionIdNum) || isNaN(attendanceIdNum) || isNaN(startAyahNum) || isNaN(endAyahNum)) {
      return NextResponse.json({
        success: false,
        error: 'القيم المدخلة يجب أن تكون أرقاماً صحيحة'
      }, { status: 400 });
    }

    // التحقق من وجود مجلس الختم
    const khatmSession = await prisma.khatmSession.findUnique({
      where: { id: khatmSessionIdNum }
    });

    if (!khatmSession) {
      return NextResponse.json({
        success: false,
        error: 'مجلس الختم غير موجود'
      }, { status: 404 });
    }

    // التحقق من وجود سجل الحضور
    const attendance = await prisma.khatmSessionAttendance.findUnique({
      where: { id: attendanceIdNum }
    });

    if (!attendance) {
      return NextResponse.json({
        success: false,
        error: 'سجل الحضور غير موجود'
      }, { status: 404 });
    }

    // التحقق من أن سجل الحضور ينتمي إلى مجلس الختم المحدد
    if (attendance.khatmSessionId !== khatmSessionIdNum) {
      return NextResponse.json({
        success: false,
        error: 'سجل الحضور لا ينتمي إلى مجلس الختم المحدد'
      }, { status: 400 });
    }

    // إنشاء سجل تقدم الحفظ
    const progressRecord = await prisma.khatmProgressRecord.create({
      data: {
        khatmSessionId: khatmSessionIdNum,
        attendanceId: attendanceIdNum,
        startAyah: startAyahNum,
        endAyah: endAyahNum,
        memorizedAyahs: memorizedAyahsNum,
        reviewedAyahs: reviewedAyahsNum,
        qualityRating: qualityRatingNum,
        notes
      }
    });

    // جلب معلومات الطالب والمعلم لإرسال الإشعارات
    const attendanceWithDetails = await prisma.khatmSessionAttendance.findUnique({
      where: { id: attendanceIdNum },
      include: {
        student: true,
        khatmSession: {
          include: {
            teacher: true,
            surah: true
          }
        }
      }
    });

    if (attendanceWithDetails) {
      // إنشاء إشعار للطالب
      try {
        await prisma.notification.create({
          data: {
            title: 'تم تسجيل تقدم جديد في الحفظ',
            content: `تم تسجيل تقدمك في حفظ ${attendanceWithDetails.khatmSession.surah?.name || 'القرآن الكريم'} (${memorizedAyahsNum} آية محفوظة، ${reviewedAyahsNum} آية مراجعة)`,
            type: 'ACHIEVEMENT', // تغيير النوع إلى ACHIEVEMENT بدلاً من PROGRESS
            userId: attendanceWithDetails.studentId,
            relatedId: progressRecord.id,
            link: `/student/khatm-progress/${khatmSessionIdNum}`
          }
        });
      } catch (error) {
        console.error('Error creating student notification:', error);
      }

      // إنشاء إشعار للمعلم
      try {
        await prisma.notification.create({
          data: {
            title: 'تم تسجيل تقدم جديد لطالب',
            content: `تم تسجيل تقدم الطالب ${attendanceWithDetails.student.name} في حفظ ${attendanceWithDetails.khatmSession.surah?.name || 'القرآن الكريم'} (${memorizedAyahsNum} آية محفوظة، ${reviewedAyahsNum} آية مراجعة)`,
            type: 'ACHIEVEMENT', // تغيير النوع إلى ACHIEVEMENT بدلاً من PROGRESS
            userId: attendanceWithDetails.khatmSession.teacherId,
            relatedId: progressRecord.id,
            link: `/admin/khatm-progress/${khatmSessionIdNum}`
          }
        });
      } catch (error) {
        console.error('Error creating teacher notification:', error);
      }
    }

    return NextResponse.json({
      success: true,
      data: progressRecord,
      message: 'تم إنشاء سجل تقدم الحفظ بنجاح'
    });
  } catch (error) {
    console.error('Error creating khatm progress record:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء إنشاء سجل تقدم الحفظ'
    }, { status: 500 });
  }
}

// PUT /api/khatm-progress - تحديث سجل تقدم حفظ
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, startAyah, endAyah, memorizedAyahs, reviewedAyahs, qualityRating, notes } = body;

    // التحقق من البيانات المطلوبة
    if (!id || startAyah === undefined || endAyah === undefined) {
      return NextResponse.json({
        success: false,
        error: 'معرف سجل التقدم ورقم الآية البداية والنهاية مطلوبة'
      }, { status: 400 });
    }

    // تحويل القيم إلى أرقام
    const idNum = parseInt(id);
    const startAyahNum = parseInt(startAyah);
    const endAyahNum = parseInt(endAyah);
    const memorizedAyahsNum = memorizedAyahs ? parseInt(memorizedAyahs) : 0;
    const reviewedAyahsNum = reviewedAyahs ? parseInt(reviewedAyahs) : 0;
    const qualityRatingNum = qualityRating ? parseInt(qualityRating) : 3;

    // التحقق من صحة القيم العددية
    if (isNaN(idNum) || isNaN(startAyahNum) || isNaN(endAyahNum)) {
      return NextResponse.json({
        success: false,
        error: 'القيم المدخلة يجب أن تكون أرقاماً صحيحة'
      }, { status: 400 });
    }

    // التحقق من وجود سجل التقدم
    const existingRecord = await prisma.khatmProgressRecord.findUnique({
      where: { id: idNum }
    });

    if (!existingRecord) {
      return NextResponse.json({
        success: false,
        error: 'سجل تقدم الحفظ غير موجود'
      }, { status: 404 });
    }

    // تحديث سجل تقدم الحفظ
    const updatedRecord = await prisma.khatmProgressRecord.update({
      where: { id: idNum },
      data: {
        startAyah: startAyahNum,
        endAyah: endAyahNum,
        memorizedAyahs: memorizedAyahsNum,
        reviewedAyahs: reviewedAyahsNum,
        qualityRating: qualityRatingNum,
        notes
      }
    });

    // جلب معلومات الطالب والمعلم لإرسال الإشعارات
    const progressWithDetails = await prisma.khatmProgressRecord.findUnique({
      where: { id: idNum },
      include: {
        attendance: {
          include: {
            student: true
          }
        },
        khatmSession: {
          include: {
            teacher: true,
            surah: true
          }
        }
      }
    });

    if (progressWithDetails) {
      // إنشاء إشعار للطالب
      try {
        await prisma.notification.create({
          data: {
            title: 'تم تحديث تقدم الحفظ',
            content: `تم تحديث تقدمك في حفظ ${progressWithDetails.khatmSession.surah?.name || 'القرآن الكريم'} (${memorizedAyahsNum} آية محفوظة، ${reviewedAyahsNum} آية مراجعة)`,
            type: 'ACHIEVEMENT', // تغيير النوع إلى ACHIEVEMENT بدلاً من PROGRESS
            userId: progressWithDetails.attendance.studentId,
            relatedId: updatedRecord.id,
            link: `/student/khatm-progress/${progressWithDetails.khatmSessionId}`
          }
        });
      } catch (error) {
        console.error('Error creating student notification:', error);
      }

      // إنشاء إشعار للمعلم
      try {
        await prisma.notification.create({
          data: {
            title: 'تم تحديث تقدم طالب',
            content: `تم تحديث تقدم الطالب ${progressWithDetails.attendance.student.name} في حفظ ${progressWithDetails.khatmSession.surah?.name || 'القرآن الكريم'} (${memorizedAyahsNum} آية محفوظة، ${reviewedAyahsNum} آية مراجعة)`,
            type: 'ACHIEVEMENT', // تغيير النوع إلى ACHIEVEMENT بدلاً من PROGRESS
            userId: progressWithDetails.khatmSession.teacherId,
            relatedId: updatedRecord.id,
            link: `/admin/khatm-progress/${progressWithDetails.khatmSessionId}`
          }
        });
      } catch (error) {
        console.error('Error creating teacher notification:', error);
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedRecord,
      message: 'تم تحديث سجل تقدم الحفظ بنجاح'
    });
  } catch (error) {
    console.error('Error updating khatm progress record:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تحديث سجل تقدم الحفظ'
    }, { status: 500 });
  }
}

// DELETE /api/khatm-progress - حذف سجل تقدم حفظ
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف سجل التقدم مطلوب'
      }, { status: 400 });
    }

    // تحويل المعرف إلى رقم
    const idNum = parseInt(id);

    // التحقق من صحة القيمة العددية
    if (isNaN(idNum)) {
      return NextResponse.json({
        success: false,
        error: 'معرف سجل التقدم يجب أن يكون رقماً صحيحاً'
      }, { status: 400 });
    }

    // التحقق من وجود سجل التقدم
    const existingRecord = await prisma.khatmProgressRecord.findUnique({
      where: { id: idNum }
    });

    if (!existingRecord) {
      return NextResponse.json({
        success: false,
        error: 'سجل تقدم الحفظ غير موجود'
      }, { status: 404 });
    }

    // حذف سجل تقدم الحفظ
    await prisma.khatmProgressRecord.delete({
      where: { id: idNum }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف سجل تقدم الحفظ بنجاح'
    });
  } catch (error) {
    console.error('Error deleting khatm progress record:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف سجل تقدم الحفظ'
    }, { status: 500 });
  }
}

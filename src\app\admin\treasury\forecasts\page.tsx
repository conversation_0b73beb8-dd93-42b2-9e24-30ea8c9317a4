'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { toast } from 'react-toastify'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import {
  FaChartLine,
  FaFilter,
  FaFileExport,
  FaChartPie,
  FaInfoCircle
} from 'react-icons/fa'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { Line, Pie } from 'react-chartjs-2'
import { exportToExcel, exportToPdf } from '@/utils/export-utils'
import { formatCurrency } from '@/utils/format-utils'

// تسجيل مكونات الرسوم البيانية
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

interface ForecastData {
  yearMonth: string;
  amount: number;
}

interface CategoryForecast {
  categoryId: number;
  categoryName: string;
  percentage: number;
  forecasts: ForecastData[];
}

interface FinancialForecast {
  historicalData: ForecastData[];
  forecasts: ForecastData[];
  growthRate: number;
  categoryForecasts: CategoryForecast[];
}

export default function FinancialForecastsPage() {
  const [forecastMonths, setForecastMonths] = useState<string>('6');
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [categories, setCategories] = useState<{ id: number; name: string }[]>([]);
  const [forecast, setForecast] = useState<FinancialForecast | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // جلب فئات المصروفات
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories?includeInactive=false');
      if (!response.ok) {
        throw new Error('فشل في جلب فئات المصروفات');
      }
      const data = await response.json();
      setCategories(data.categories);
    } catch (error: Error | unknown) {
      console.error('Error fetching categories:', error);
      toast.error('فشل في جلب فئات المصروفات');
    }
  };

  // جلب التنبؤات المالية
  const fetchForecasts = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        months: forecastMonths,
      });

      if (selectedCategoryId) {
        queryParams.append('categoryId', selectedCategoryId);
      }

      const response = await fetch(`/api/financial-forecasts?${queryParams}`);
      if (!response.ok) {
        throw new Error('فشل في جلب التنبؤات المالية');
      }

      const data = await response.json();
      setForecast(data);
    } catch (error: Error | unknown) {
      console.error('Error fetching forecasts:', error);
      toast.error('فشل في جلب التنبؤات المالية');
    } finally {
      setLoading(false);
    }
  };

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchCategories();
    fetchForecasts();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // تحديث التنبؤات عند تغيير المعايير
  const handleApplyFilters = () => {
    fetchForecasts();
  };

  // تنسيق التاريخ
  const formatMonthYear = (yearMonth: string) => {
    const [year, month] = yearMonth.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, 1);
    return format(date, 'MMMM yyyy', { locale: ar });
  };

  // تصدير التنبؤات إلى Excel
  const handleExportToExcel = () => {
    if (!forecast) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // إعداد بيانات التنبؤات
      const forecastData = forecast.forecasts.map(item => ({
        'الشهر': formatMonthYear(item.yearMonth),
        'المبلغ المتوقع': formatCurrency(item.amount),
      }));

      // إعداد بيانات البيانات التاريخية
      const historicalData = forecast.historicalData.map(item => ({
        'الشهر': formatMonthYear(item.yearMonth),
        'المبلغ الفعلي': formatCurrency(item.amount),
      }));

      // إعداد بيانات تنبؤات الفئات
      const categoryForecastsData = forecast.categoryForecasts.flatMap(category => {
        return category.forecasts.map(item => ({
          'الفئة': category.categoryName,
          'الشهر': formatMonthYear(item.yearMonth),
          'المبلغ المتوقع': formatCurrency(item.amount),
          'النسبة من الإجمالي': `${category.percentage}%`,
        }));
      });

      // تصدير البيانات إلى Excel
      exportToExcel(
        [
          { 'معدل النمو الشهري': `${forecast.growthRate}%` },
          { 'الشهر': '', 'المبلغ المتوقع': '' }, // سطر فارغ للفصل
          ...forecastData,
          { 'الشهر': '', 'المبلغ المتوقع': '' }, // سطر فارغ للفصل
          { 'البيانات التاريخية': '' },
          ...historicalData,
          { 'الفئة': '', 'الشهر': '', 'المبلغ المتوقع': '', 'النسبة من الإجمالي': '' }, // سطر فارغ للفصل
          { 'تنبؤات الفئات': '' },
          ...categoryForecastsData
        ],
        `التنبؤات_المالية_${new Date().toISOString().split('T')[0]}.xlsx`,
        'التنبؤات المالية'
      );
    } catch (error: Error | unknown) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تصدير التنبؤات إلى PDF
  const handleExportToPdf = () => {
    if (!forecast) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // إعداد بيانات التنبؤات
      const forecastsTable = {
        title: 'التنبؤات المالية للمصروفات',
        headers: ['الشهر', 'المبلغ المتوقع'],
        data: forecast.forecasts.map(item => [
          formatMonthYear(item.yearMonth),
          formatCurrency(item.amount)
        ])
      };

      // إعداد بيانات البيانات التاريخية
      const historicalTable = {
        title: 'البيانات التاريخية للمصروفات',
        headers: ['الشهر', 'المبلغ الفعلي'],
        data: forecast.historicalData.map(item => [
          formatMonthYear(item.yearMonth),
          formatCurrency(item.amount)
        ])
      };

      // إعداد بيانات تنبؤات الفئات
      const categoryTable = {
        title: 'تنبؤات المصروفات حسب الفئة',
        headers: ['الفئة', 'النسبة من الإجمالي', 'المبلغ المتوقع (متوسط)'],
        data: forecast.categoryForecasts.map(category => {
          const avgAmount = category.forecasts.reduce((sum, item) => sum + item.amount, 0) / category.forecasts.length;
          return [
            category.categoryName,
            `${category.percentage}%`,
            formatCurrency(avgAmount)
          ];
        })
      };

      // إعداد الرسوم البيانية
      const charts = [
        {
          title: 'تنبؤات المصروفات',
          type: 'line' as const,
          data: {
            labels: [...forecast.historicalData.map(item => formatMonthYear(item.yearMonth)), ...forecast.forecasts.map(item => formatMonthYear(item.yearMonth))],
            datasets: [
              {
                label: 'البيانات التاريخية',
                data: [...forecast.historicalData.map(item => item.amount), ...Array(forecast.forecasts.length).fill(null)],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.2)',
                borderWidth: 2,
                pointRadius: 3,
              },
              {
                label: 'التنبؤات',
                data: [...Array(forecast.historicalData.length).fill(null), ...forecast.forecasts.map(item => item.amount)],
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.2)',
                borderWidth: 2,
                pointRadius: 3,
                borderDash: [5, 5],
              }
            ],
          }
        },
        {
          title: 'توزيع المصروفات المتوقعة حسب الفئة',
          type: 'pie' as const,
          data: {
            labels: forecast.categoryForecasts.map(category => category.categoryName),
            datasets: [
              {
                label: 'النسبة المئوية',
                data: forecast.categoryForecasts.map(category => category.percentage),
                backgroundColor: [
                  'var(--primary-color)',
                  '#3498db',
                  '#9b59b6',
                  '#e67e22',
                  '#f1c40f',
                  '#1abc9c',
                  '#34495e',
                  '#e74c3c',
                  '#2ecc71',
                  '#16a085',
                ],
                borderWidth: 1,
              },
            ],
          }
        }
      ];

      // تصدير البيانات إلى PDF
      exportToPdf({
        title: 'التنبؤات المالية',
        fileName: `التنبؤات_المالية_${new Date().toISOString().split('T')[0]}.pdf`,
        tables: [forecastsTable, historicalTable, categoryTable],
        charts: charts
      });
    } catch (error: Error | unknown) {
      console.error('Error exporting to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.treasury.forecasts.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaChartLine className="text-[var(--primary-color)]" />
          التنبؤات المالية
        </h1>
        <div className="flex gap-2">
          <Button
            onClick={handleExportToExcel}
            className="bg-primary-color hover:bg-green-700 text-white flex items-center gap-2"
          >
            <FaFileExport />
            <span>تصدير Excel</span>
          </Button>
          <Button
            onClick={handleExportToPdf}
            className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-2"
          >
            <FaFileExport />
            <span>تصدير PDF</span>
          </Button>
        </div>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-xl flex items-center">
            <FaFilter className="ml-2 text-[var(--primary-color)]" />
            معايير التنبؤ
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="forecastMonths">عدد أشهر التنبؤ</Label>
              <Select
                value={forecastMonths}
                onValueChange={setForecastMonths}
              >
                <SelectTrigger id="forecastMonths">
                  <SelectValue placeholder="اختر عدد الأشهر" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="3">3 أشهر</SelectItem>
                  <SelectItem value="6">6 أشهر</SelectItem>
                  <SelectItem value="12">12 شهر</SelectItem>
                  <SelectItem value="24">24 شهر</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="categoryId">فئة المصروفات</Label>
              <Select
                value={selectedCategoryId}
                onValueChange={setSelectedCategoryId}
              >
                <SelectTrigger id="categoryId">
                  <SelectValue placeholder="جميع الفئات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">جميع الفئات</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                onClick={handleApplyFilters}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
              >
                تطبيق
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            <p className="mt-2 text-[var(--primary-color)]">جاري تحميل البيانات...</p>
          </div>
        </div>
      ) : forecast ? (
        <div className="space-y-6">
          {/* ملخص التنبؤات */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <FaInfoCircle className="ml-2 text-[var(--primary-color)]" />
                ملخص التنبؤات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-blue-500 font-bold text-lg">معدل النمو الشهري</div>
                  <div className="text-2xl font-bold">{forecast.growthRate}%</div>
                  <div className="text-sm text-gray-500">متوسط التغير الشهري في المصروفات</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-primary-color font-bold text-lg">متوسط المصروفات الشهرية</div>
                  <div className="text-2xl font-bold">
                    {formatCurrency(
                      forecast.historicalData.reduce((sum, item) => sum + item.amount, 0) /
                      (forecast.historicalData.length || 1)
                    )}
                  </div>
                  <div className="text-sm text-gray-500">بناءً على البيانات التاريخية</div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-red-500 font-bold text-lg">متوسط المصروفات المتوقعة</div>
                  <div className="text-2xl font-bold">
                    {formatCurrency(
                      forecast.forecasts.reduce((sum, item) => sum + item.amount, 0) /
                      (forecast.forecasts.length || 1)
                    )}
                  </div>
                  <div className="text-sm text-gray-500">للفترة القادمة</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* رسم بياني للتنبؤات */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl flex items-center">
                <FaChartLine className="ml-2 text-[var(--primary-color)]" />
                تنبؤات المصروفات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <Line
                  data={{
                    labels: [...forecast.historicalData.map(item => formatMonthYear(item.yearMonth)), ...forecast.forecasts.map(item => formatMonthYear(item.yearMonth))],
                    datasets: [
                      {
                        label: 'البيانات التاريخية',
                        data: [...forecast.historicalData.map(item => item.amount), ...Array(forecast.forecasts.length).fill(null)],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.2)',
                        borderWidth: 2,
                        pointRadius: 3,
                      },
                      {
                        label: 'التنبؤات',
                        data: [...Array(forecast.historicalData.length).fill(null), ...forecast.forecasts.map(item => item.amount)],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.2)',
                        borderWidth: 2,
                        pointRadius: 3,
                        borderDash: [5, 5],
                      }
                    ],
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top',
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
                          }
                        }
                      }
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: function(value) {
                            return formatCurrency(value as number);
                          }
                        }
                      }
                    }
                  }}
                />
              </div>
            </CardContent>
          </Card>

          {/* توزيع المصروفات حسب الفئة */}
          {forecast.categoryForecasts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-xl flex items-center">
                  <FaChartPie className="ml-2 text-[var(--primary-color)]" />
                  توزيع المصروفات المتوقعة حسب الفئة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="h-80">
                    <Pie
                      data={{
                        labels: forecast.categoryForecasts.map(category => category.categoryName),
                        datasets: [
                          {
                            label: 'النسبة المئوية',
                            data: forecast.categoryForecasts.map(category => category.percentage),
                            backgroundColor: [
                              'var(--primary-color)',
                              '#3498db',
                              '#9b59b6',
                              '#e67e22',
                              '#f1c40f',
                              '#1abc9c',
                              '#34495e',
                              '#e74c3c',
                              '#2ecc71',
                              '#16a085',
                            ],
                            borderWidth: 1,
                          },
                        ],
                      }}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'right',
                          },
                          tooltip: {
                            callbacks: {
                              label: function(context) {
                                return `${context.label}: ${context.parsed}%`;
                              }
                            }
                          }
                        }
                      }}
                    />
                  </div>
                  <div>
                    <table className="min-w-full bg-white">
                      <thead>
                        <tr className="bg-[var(--primary-color)] text-white">
                          <th className="px-4 py-2 text-right">الفئة</th>
                          <th className="px-4 py-2 text-right">النسبة</th>
                          <th className="px-4 py-2 text-right">متوسط المبلغ المتوقع</th>
                        </tr>
                      </thead>
                      <tbody>
                        {forecast.categoryForecasts.map((category, index) => {
                          const avgAmount = category.forecasts.reduce((sum, item) => sum + item.amount, 0) / category.forecasts.length;
                          return (
                            <tr key={category.categoryId} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                              <td className="px-4 py-2 border-b">{category.categoryName}</td>
                              <td className="px-4 py-2 border-b">{category.percentage}%</td>
                              <td className="px-4 py-2 border-b">{formatCurrency(avgAmount)}</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        <div className="text-center p-8 bg-gray-50 rounded-lg">
          <FaInfoCircle className="mx-auto text-gray-400 text-4xl mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد بيانات</h3>
          <p className="text-gray-500">
            لم يتم العثور على بيانات للتنبؤات المالية. يرجى التأكد من وجود بيانات مصروفات تاريخية كافية.
          </p>
        </div>
      )}
      </div>
    </ProtectedRoute>
  )
}

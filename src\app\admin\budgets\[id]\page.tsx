'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Progress } from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'react-toastify'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BarChart } from '@/components/charts/bar-chart'
import { PieChart } from '@/components/charts/pie-chart'
import { LineChart } from '@/components/charts/line-chart'
import { Badge } from '@/components/ui/badge'
import { exportBudgetToExcel, exportBudgetToPDF } from '@/lib/exportUtils'
import { Budget as ExportBudget } from '@/types/budget'
import {
  FaChartPie,
  FaPlus,
  FaEdit,
  FaTrash,
  FaArrowLeft,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaTags,
  FaExclamationTriangle,
  FaChartBar,
  FaChartLine,
  FaCheckCircle,
  FaExclamationCircle,
  FaTimesCircle,
  FaInfoCircle,
  FaClipboardList,
  FaFileExcel,
  FaFilePdf
} from 'react-icons/fa'
import Link from 'next/link'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'

interface ExpenseCategory {
  id: number
  name: string
  description: string | null
  isActive: boolean
}

interface BudgetItem {
  id: number
  categoryId: number
  amount: number
  notes: string | null
  category: ExpenseCategory
  actualAmount: number
  remainingAmount: number
  utilizationPercentage?: number
}

interface Budget {
  id: number
  name: string
  description: string | null
  startDate: string
  endDate: string
  totalAmount: number
  status: 'DRAFT' | 'ACTIVE' | 'COMPLETED' | 'ARCHIVED'
  items: BudgetItem[]
  createdAt: string
  updatedAt: string
  totalActualAmount: number
  totalRemainingAmount: number
}

interface MonthlyExpense {
  month: string
  amount: number
}

export default function BudgetDetailsPage() {
  const params = useParams()
  const budgetId = params?.id ? String(params.id) : ''
  const [budget, setBudget] = useState<Budget | null>(null)
  const [loading, setLoading] = useState(true)
  const [categories, setCategories] = useState<ExpenseCategory[]>([])
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false)
  const [isEditItemDialogOpen, setIsEditItemDialogOpen] = useState(false)
  const [isCopyDialogOpen, setIsCopyDialogOpen] = useState(false)
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<BudgetItem | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [monthlyExpenses, setMonthlyExpenses] = useState<MonthlyExpense[]>([])
  const [budgetReviews, setBudgetReviews] = useState<{
    id: number;
    date: string;
    oldStatus: string;
    newStatus: string;
    notes: string | null;
  }[]>([])
  const [newItemData, setNewItemData] = useState({
    categoryId: '',
    amount: '',
    notes: ''
  })
  const [copyBudgetData, setCopyBudgetData] = useState({
    newName: '',
    newStartDate: '',
    newEndDate: '',
    copyItems: true
  })
  const [reviewData, setReviewData] = useState({
    newStatus: '',
    notes: ''
  })

  const [editItemData, setEditItemData] = useState({
    amount: '',
    notes: ''
  })

  // جلب بيانات الميزانية
  useEffect(() => {
    const fetchBudget = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/budgets/${budgetId}`)

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات الميزانية')
        }

        const data = await response.json()

        // إضافة نسبة الاستخدام لكل بند
        const itemsWithPercentage = data.items.map((item: BudgetItem) => ({
          ...item,
          utilizationPercentage: item.amount > 0 ? (item.actualAmount / item.amount) * 100 : 0
        }))

        setBudget({
          ...data,
          items: itemsWithPercentage
        })

        // جلب المصروفات الشهرية
        const reportResponse = await fetch(`/api/reports/budget?budgetId=${budgetId}`)
        if (reportResponse.ok) {
          const reportData = await reportResponse.json()
          setMonthlyExpenses(reportData.monthlyExpenses || [])
        }

        // جلب سجل المراجعات
        fetchBudgetReviews()
      } catch (error) {
        console.error('خطأ:', error)
        toast.error('حدث خطأ أثناء جلب بيانات الميزانية')
      } finally {
        setLoading(false)
      }
    }

    // جلب سجل مراجعات الميزانية
    const fetchBudgetReviews = async () => {
      try {
        const response = await fetch(`/api/budgets/${budgetId}/reviews`)

        if (!response.ok) {
          console.error('فشل في جلب سجل المراجعات')
          return
        }

        const data = await response.json()
        setBudgetReviews(data.reviews || [])
      } catch (error) {
        console.error('خطأ في جلب سجل المراجعات:', error)
        // لا نعرض رسالة خطأ للمستخدم هنا لأنها ليست وظيفة أساسية
      }
    }

    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/expense-categories')

        if (!response.ok) {
          throw new Error('فشل في جلب فئات المصروفات')
        }

        const data = await response.json()
        setCategories(data.categories || [])
      } catch (error) {
        console.error('خطأ:', error)
        toast.error('حدث خطأ أثناء جلب فئات المصروفات')
      }
    }

    fetchBudget()
    fetchCategories()
  }, [budgetId])

  // إضافة بند جديد للميزانية
  const handleAddItem = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (!newItemData.categoryId || !newItemData.amount) {
        toast.error('الفئة والمبلغ مطلوبان')
        return
      }

      const amount = parseFloat(newItemData.amount)

      if (isNaN(amount) || amount <= 0) {
        toast.error('المبلغ يجب أن يكون رقمًا موجبًا')
        return
      }

      const response = await fetch(`/api/budgets/${budgetId}/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          categoryId: parseInt(newItemData.categoryId),
          amount,
          notes: newItemData.notes || null
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في إضافة البند')
      }

      // إعادة تحميل بيانات الميزانية
      const budgetResponse = await fetch(`/api/budgets/${budgetId}`)
      const budgetData = await budgetResponse.json()

      // إضافة نسبة الاستخدام لكل بند
      const itemsWithPercentage = budgetData.items.map((item: BudgetItem) => ({
        ...item,
        utilizationPercentage: item.amount > 0 ? (item.actualAmount / item.amount) * 100 : 0
      }))

      setBudget({
        ...budgetData,
        items: itemsWithPercentage
      })

      setIsAddItemDialogOpen(false)
      setNewItemData({
        categoryId: '',
        amount: '',
        notes: ''
      })

      toast.success('تم إضافة البند بنجاح')
    } catch (error) {
      console.error('خطأ:', error)
      toast.error('حدث خطأ أثناء إضافة البند')
    }
  }

  // تحرير بند ميزانية
  const handleEditItem = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedItem) return

    try {
      const amount = parseFloat(editItemData.amount)

      if (isNaN(amount) || amount <= 0) {
        toast.error('المبلغ يجب أن يكون رقمًا موجبًا')
        return
      }

      const response = await fetch(`/api/budgets/${budgetId}/items/${selectedItem.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount,
          notes: editItemData.notes || null
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تحديث البند')
      }

      // إعادة تحميل بيانات الميزانية
      const budgetResponse = await fetch(`/api/budgets/${budgetId}`)
      const budgetData = await budgetResponse.json()

      // إضافة نسبة الاستخدام لكل بند
      const itemsWithPercentage = budgetData.items.map((item: BudgetItem) => ({
        ...item,
        utilizationPercentage: item.amount > 0 ? (item.actualAmount / item.amount) * 100 : 0
      }))

      setBudget({
        ...budgetData,
        items: itemsWithPercentage
      })

      setIsEditItemDialogOpen(false)
      setSelectedItem(null)

      toast.success('تم تحديث البند بنجاح')
    } catch (error) {
      console.error('خطأ:', error)
      toast.error('حدث خطأ أثناء تحديث البند')
    }
  }

  // حذف بند ميزانية
  const handleDeleteItem = async (itemId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا البند؟')) {
      return
    }

    try {
      const response = await fetch(`/api/budgets/${budgetId}/items/${itemId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في حذف البند')
      }

      // إعادة تحميل بيانات الميزانية
      const budgetResponse = await fetch(`/api/budgets/${budgetId}`)
      const budgetData = await budgetResponse.json()

      // إضافة نسبة الاستخدام لكل بند
      const itemsWithPercentage = budgetData.items.map((item: BudgetItem) => ({
        ...item,
        utilizationPercentage: item.amount > 0 ? (item.actualAmount / item.amount) * 100 : 0
      }))

      setBudget({
        ...budgetData,
        items: itemsWithPercentage
      })

      toast.success('تم حذف البند بنجاح')
    } catch (error) {
      console.error('خطأ:', error)
      toast.error('حدث خطأ أثناء حذف البند')
    }
  }

  // نسخ الميزانية
  const handleCopyBudget = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (!copyBudgetData.newName || !copyBudgetData.newStartDate || !copyBudgetData.newEndDate) {
        toast.error('جميع الحقول الأساسية مطلوبة')
        return
      }

      // التحقق من صحة التواريخ
      const startDate = new Date(copyBudgetData.newStartDate)
      const endDate = new Date(copyBudgetData.newEndDate)

      if (startDate >= endDate) {
        toast.error('يجب أن يكون تاريخ البداية قبل تاريخ النهاية')
        return
      }

      const response = await fetch('/api/budgets/copy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          budgetId,
          newName: copyBudgetData.newName,
          newStartDate: copyBudgetData.newStartDate,
          newEndDate: copyBudgetData.newEndDate,
          copyItems: copyBudgetData.copyItems
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في نسخ الميزانية')
      }

      const data = await response.json()

      toast.success('تم نسخ الميزانية بنجاح')
      setIsCopyDialogOpen(false)

      // إعادة تعيين نموذج النسخ
      setCopyBudgetData({
        newName: '',
        newStartDate: '',
        newEndDate: '',
        copyItems: true
      })

      // الانتقال إلى الميزانية الجديدة
      window.location.href = `/admin/budgets/${data.budget.id}`
    } catch (error) {
      console.error('خطأ:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء نسخ الميزانية')
    }
  }

  // تغيير حالة الميزانية
  const handleChangeStatus = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (!reviewData.newStatus) {
        toast.error('يرجى اختيار الحالة الجديدة')
        return
      }

      const response = await fetch('/api/budgets/status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          budgetId,
          status: reviewData.newStatus,
          reviewNotes: reviewData.notes
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تغيير حالة الميزانية')
      }

      await response.json() // نستقبل البيانات ولكن لا نستخدمها هنا

      toast.success('تم تغيير حالة الميزانية بنجاح')
      setIsReviewDialogOpen(false)

      // إعادة تعيين نموذج المراجعة
      setReviewData({
        newStatus: '',
        notes: ''
      })

      // إعادة تحميل بيانات الميزانية
      const budgetResponse = await fetch(`/api/budgets/${budgetId}`)
      const budgetData = await budgetResponse.json()

      // إضافة نسبة الاستخدام لكل بند
      const itemsWithPercentage = budgetData.items.map((item: BudgetItem) => ({
        ...item,
        utilizationPercentage: item.amount > 0 ? (item.actualAmount / item.amount) * 100 : 0
      }))

      setBudget({
        ...budgetData,
        items: itemsWithPercentage
      })

      // إعادة تحميل سجل المراجعات
      const reviewsResponse = await fetch(`/api/budgets/${budgetId}/reviews`)
      if (reviewsResponse.ok) {
        const reviewsData = await reviewsResponse.json()
        setBudgetReviews(reviewsData.reviews || [])
      }
    } catch (error) {
      console.error('خطأ:', error)
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تغيير حالة الميزانية')
    }
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('fr-FR')
    } catch {
      return dateString
    }
  }

  // تنسيق المبلغ
  const formatAmount = (amount: number) => {
    return amount.toLocaleString('fr-FR') + ' د.ج'
  }

  // الحصول على لون حالة الاستخدام
  const getUtilizationColor = (percentage: number) => {
    if (percentage <= 75) return 'bg-[var(--primary-color)]' // أخضر
    if (percentage <= 90) return 'bg-[#f59e0b]' // أصفر
    return 'bg-[#ef4444]' // أحمر
  }

  // الحصول على حالة الميزانية
  const getBudgetStatusText = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'مسودة'
      case 'ACTIVE': return 'نشطة'
      case 'COMPLETED': return 'مكتملة'
      case 'ARCHIVED': return 'مؤرشفة'
      default: return status
    }
  }

  // الحصول على لون حالة الميزانية
  const getBudgetStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'bg-gray-200 text-gray-800'
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'COMPLETED': return 'bg-blue-100 text-blue-800'
      case 'ARCHIVED': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // الحصول على حالة أداء الميزانية
  const getBudgetPerformanceStatus = (percentage: number) => {
    if (percentage <= 75) return { status: 'ممتاز', color: 'bg-green-100 text-green-800', icon: <FaCheckCircle className="text-primary-color" /> }
    if (percentage <= 90) return { status: 'جيد', color: 'bg-yellow-100 text-yellow-800', icon: <FaExclamationCircle className="text-yellow-600" /> }
    if (percentage <= 100) return { status: 'تحذير', color: 'bg-orange-100 text-orange-800', icon: <FaExclamationTriangle className="text-orange-600" /> }
    return { status: 'تجاوز', color: 'bg-red-100 text-red-800', icon: <FaTimesCircle className="text-red-600" /> }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
      </div>
    )
  }

  if (!budget) {
    return (
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
        <div className="text-center py-12 bg-white rounded-lg border border-dashed border-gray-300">
          <FaExclamationTriangle className="mx-auto text-4xl text-yellow-500 mb-4" />
          <h3 className="text-xl font-bold text-gray-600 mb-2">لم يتم العثور على الميزانية</h3>
          <p className="text-gray-500 mb-4">لا يمكن العثور على الميزانية المطلوبة</p>
          <Link href="/admin/budgets">
            <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white">
              العودة إلى قائمة الميزانيات
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  // إعداد بيانات الرسوم البيانية
  const pieChartData = {
    labels: ['المصروفات الفعلية', 'المتبقي من الميزانية'],
    datasets: [
      {
        label: 'توزيع الميزانية',
        data: [budget.totalActualAmount, budget.totalRemainingAmount],
        backgroundColor: ['#ef4444', 'var(--primary-color)'],
      }
    ]
  }

  const barChartData = {
    labels: budget.items.map(item => item.category.name),
    datasets: [
      {
        label: 'المبلغ المخصص',
        data: budget.items.map(item => item.amount),
        backgroundColor: 'rgba(22, 155, 136, 0.7)',
      },
      {
        label: 'المصروفات الفعلية',
        data: budget.items.map(item => item.actualAmount),
        backgroundColor: 'rgba(239, 68, 68, 0.7)',
      }
    ]
  }

  const monthlyExpensesChartData = {
    labels: monthlyExpenses.map(exp => {
      const date = new Date(exp.month)
      return date.toLocaleDateString('ar', { month: 'long', year: 'numeric' })
    }),
    datasets: [
      {
        label: 'المصروفات الشهرية',
        data: monthlyExpenses.map(exp => exp.amount),
        backgroundColor: 'rgba(22, 155, 136, 0.7)',
        borderColor: 'var(--primary-color)',
        borderWidth: 1,
      }
    ]
  }

  return (
    <ProtectedRoute requiredPermission="admin.budgets.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Link href="/admin/budgets">
            <Button variant="outline" className="h-10 w-10 p-0">
              <FaArrowLeft />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaChartPie className="text-[var(--primary-color)]" />
            تفاصيل الميزانية
          </h1>
        </div>
        <div className="flex gap-2">
          <PermissionGuard requiredPermission="admin.budgets.items.create">
            <Button
              onClick={() => {
                setNewItemData({
                  categoryId: '',
                  amount: '',
                  notes: ''
                })
                setIsAddItemDialogOpen(true)
              }}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
            >
              <FaPlus size={14} />
              <span>إضافة بند جديد</span>
            </Button>
          </PermissionGuard>

          <PermissionGuard requiredPermission="admin.budgets.copy">
            <Button
              onClick={() => {
                setCopyBudgetData({
                  newName: `نسخة من ${budget.name}`,
                  newStartDate: '',
                  newEndDate: '',
                  copyItems: true
                })
                setIsCopyDialogOpen(true)
              }}
              variant="outline"
              className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-2"
            >
              <FaClipboardList size={14} />
              <span>نسخ الميزانية</span>
            </Button>
          </PermissionGuard>

          <PermissionGuard requiredPermission="admin.budgets.review">
            <Button
              onClick={() => {
                setReviewData({
                  newStatus: budget.status,
                  notes: ''
                })
                setIsReviewDialogOpen(true)
              }}
              variant="outline"
              className="border-blue-500 text-blue-500 hover:bg-blue-50 flex items-center gap-2"
            >
              <FaCheckCircle size={14} />
              <span>مراجعة واعتماد</span>
            </Button>
          </PermissionGuard>

          <div className="flex gap-2">
            <PermissionGuard requiredPermission="admin.budgets.export">
              <Button
                onClick={() => {
                  // تحويل البيانات إلى النوع المطلوب للتصدير
                  const exportData: ExportBudget = {
                    ...budget,
                    description: budget.description || undefined,
                    items: budget.items.map(item => ({
                      ...item,
                      budgetId: budget.id,
                      notes: item.notes || undefined,
                      category: {
                        id: item.category.id,
                        name: item.category.name
                      }
                    }))
                  };
                  exportBudgetToExcel(exportData);
                }}
                variant="outline"
                className="border-primary-color text-primary-color hover:bg-green-50 flex items-center gap-2"
                title="تصدير إلى Excel"
              >
                <FaFileExcel size={14} />
                <span className="hidden md:inline">Excel</span>
              </Button>
            </PermissionGuard>

            <PermissionGuard requiredPermission="admin.budgets.export">
              <Button
                onClick={() => {
                  // تحويل البيانات إلى النوع المطلوب للتصدير
                  const exportData: ExportBudget = {
                    ...budget,
                    description: budget.description || undefined,
                    items: budget.items.map(item => ({
                      ...item,
                      budgetId: budget.id,
                      notes: item.notes || undefined,
                      category: {
                        id: item.category.id,
                        name: item.category.name
                      }
                    }))
                  };
                  exportBudgetToPDF(exportData);
                }}
                variant="outline"
                className="border-red-600 text-red-600 hover:bg-red-50 flex items-center gap-2"
                title="تصدير إلى PDF"
              >
                <FaFilePdf size={14} />
                <span className="hidden md:inline">PDF</span>
              </Button>
            </PermissionGuard>
          </div>
        </div>
      </div>

      {/* بطاقة معلومات الميزانية */}
      <Card className="bg-white shadow-md border border-[#e0f2ef]">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl flex items-center gap-2">
                <span>{budget.name}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${getBudgetStatusColor(budget.status)}`}>
                  {getBudgetStatusText(budget.status)}
                </span>
              </CardTitle>
              <CardDescription className="mt-1 flex items-center gap-2">
                <FaCalendarAlt className="text-gray-500" />
                <span>
                  {formatDate(budget.startDate)} - {formatDate(budget.endDate)}
                </span>
              </CardDescription>
              {budget.description && (
                <p className="text-gray-600 mt-2">{budget.description}</p>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="bg-[#f8fffd] p-4 rounded-lg border border-[#e0f2ef]">
              <div className="flex items-center gap-2 mb-2">
                <FaMoneyBillWave className="text-[var(--primary-color)]" />
                <span className="font-semibold">الميزانية الإجمالية</span>
              </div>
              <p className="text-2xl font-bold text-[var(--primary-color)]">{formatAmount(budget.totalAmount)}</p>
            </div>
            <div className="bg-[#fff5f5] p-4 rounded-lg border border-[#ffebeb]">
              <div className="flex items-center gap-2 mb-2">
                <FaMoneyBillWave className="text-[#ef4444]" />
                <span className="font-semibold">المصروفات الفعلية</span>
              </div>
              <p className="text-2xl font-bold text-[#ef4444]">{formatAmount(budget.totalActualAmount)}</p>
            </div>
            <div className="bg-[#f0fdf4] p-4 rounded-lg border border-[#dcfce7]">
              <div className="flex items-center gap-2 mb-2">
                <FaMoneyBillWave className="text-[#22c55e]" />
                <span className="font-semibold">المتبقي من الميزانية</span>
              </div>
              <p className="text-2xl font-bold text-[#22c55e]">{formatAmount(budget.totalRemainingAmount)}</p>
            </div>
          </div>

          {/* مؤشرات أداء الميزانية */}
          <div className="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <FaInfoCircle className="text-[var(--primary-color)]" />
              مؤشرات أداء الميزانية
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {/* نسبة الإنجاز */}
              <div className="bg-white p-3 rounded-lg border border-gray-200">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm font-medium">نسبة الاستخدام</span>
                  <div className="flex items-center gap-2">
                    {(() => {
                      const utilizationPercentage = budget.totalAmount > 0 ? (budget.totalActualAmount / budget.totalAmount) * 100 : 0;
                      const performanceStatus = getBudgetPerformanceStatus(utilizationPercentage);
                      return (
                        <>
                          <Badge className={performanceStatus.color}>
                            <span className="flex items-center gap-1">
                              {performanceStatus.icon}
                              {performanceStatus.status}
                            </span>
                          </Badge>
                          <span className="text-sm font-medium">
                            {Math.round(utilizationPercentage)}%
                          </span>
                        </>
                      );
                    })()}
                  </div>
                </div>
                <Progress
                  value={budget.totalAmount > 0 ? (budget.totalActualAmount / budget.totalAmount) * 100 : 0}
                  indicatorClassName={getUtilizationColor(budget.totalAmount > 0 ? (budget.totalActualAmount / budget.totalAmount) * 100 : 0)}
                  className="h-3"
                />
                <div className="mt-2 text-xs text-gray-500">
                  {budget.totalAmount > 0 && (budget.totalActualAmount / budget.totalAmount) * 100 > 100
                    ? "تجاوزت المصروفات الميزانية المخصصة"
                    : "المصروفات ضمن حدود الميزانية المخصصة"}
                </div>
              </div>

              {/* تقدم الميزانية الزمني */}
              {(() => {
                const startDate = new Date(budget.startDate);
                const endDate = new Date(budget.endDate);
                const today = new Date();
                const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                const elapsedDays = Math.ceil((Math.min(today.getTime(), endDate.getTime()) - startDate.getTime()) / (1000 * 60 * 60 * 24));
                const timeProgressPercentage = totalDays > 0 ? (elapsedDays / totalDays) * 100 : 0;

                return (
                  <div className="bg-white p-3 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">التقدم الزمني</span>
                      <span className="text-sm font-medium">{Math.round(timeProgressPercentage)}%</span>
                    </div>
                    <Progress
                      value={timeProgressPercentage}
                      className="h-3"
                      indicatorClassName="bg-blue-500"
                    />
                    <div className="mt-2 text-xs text-gray-500">
                      {elapsedDays} يوم من أصل {totalDays} يوم ({Math.round(timeProgressPercentage)}% من مدة الميزانية)
                    </div>
                  </div>
                );
              })()}
            </div>

            {/* مؤشر الكفاءة */}
            {(() => {
              const startDate = new Date(budget.startDate);
              const endDate = new Date(budget.endDate);
              const today = new Date();
              const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
              const elapsedDays = Math.ceil((Math.min(today.getTime(), endDate.getTime()) - startDate.getTime()) / (1000 * 60 * 60 * 24));
              const timeProgressPercentage = totalDays > 0 ? (elapsedDays / totalDays) * 100 : 0;
              const utilizationPercentage = budget.totalAmount > 0 ? (budget.totalActualAmount / budget.totalAmount) * 100 : 0;

              // حساب مؤشر الكفاءة (نسبة الاستخدام مقارنة بالتقدم الزمني)
              const efficiencyRatio = timeProgressPercentage > 0 ? (utilizationPercentage / timeProgressPercentage) : 0;
              let efficiencyStatus = '';
              let efficiencyColor = '';
              let efficiencyIcon = null;

              if (efficiencyRatio <= 0.85) {
                efficiencyStatus = 'أقل من المتوقع';
                efficiencyColor = 'bg-green-100 text-green-800';
                efficiencyIcon = <FaCheckCircle className="text-primary-color" />;
              } else if (efficiencyRatio <= 1.1) {
                efficiencyStatus = 'ضمن المتوقع';
                efficiencyColor = 'bg-blue-100 text-blue-800';
                efficiencyIcon = <FaInfoCircle className="text-blue-600" />;
              } else if (efficiencyRatio <= 1.25) {
                efficiencyStatus = 'أعلى من المتوقع';
                efficiencyColor = 'bg-yellow-100 text-yellow-800';
                efficiencyIcon = <FaExclamationCircle className="text-yellow-600" />;
              } else {
                efficiencyStatus = 'تجاوز كبير';
                efficiencyColor = 'bg-red-100 text-red-800';
                efficiencyIcon = <FaTimesCircle className="text-red-600" />;
              }

              return (
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">مؤشر كفاءة الإنفاق</span>
                    <Badge className={efficiencyColor}>
                      <span className="flex items-center gap-1">
                        {efficiencyIcon}
                        {efficiencyStatus}
                      </span>
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <div className="flex-1 bg-gray-100 p-2 rounded">
                      <span className="font-medium">نسبة الإنفاق:</span> {Math.round(utilizationPercentage)}%
                    </div>
                    <div className="flex-1 bg-gray-100 p-2 rounded">
                      <span className="font-medium">التقدم الزمني:</span> {Math.round(timeProgressPercentage)}%
                    </div>
                    <div className="flex-1 bg-gray-100 p-2 rounded">
                      <span className="font-medium">معامل الكفاءة:</span> {efficiencyRatio.toFixed(2)}
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        </CardContent>
      </Card>

      {/* علامات التبويب */}
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="bg-[#f8fffd] border border-[#e0f2ef]">
          <TabsTrigger value="overview" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">
            نظرة عامة
          </TabsTrigger>
          <TabsTrigger value="items" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">
            بنود الميزانية
          </TabsTrigger>
          <TabsTrigger value="charts" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">
            الرسوم البيانية
          </TabsTrigger>
          <TabsTrigger value="reviews" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">
            سجل المراجعات
          </TabsTrigger>
        </TabsList>

        {/* محتوى النظرة العامة */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-white shadow-md border border-[#e0f2ef]">
              <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                  <FaChartPie className="text-[var(--primary-color)]" />
                  <span>توزيع الميزانية</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <PieChart data={pieChartData} height={300} width={400} />
                </div>
              </CardContent>
            </Card>
            <Card className="bg-white shadow-md border border-[#e0f2ef]">
              <CardHeader>
                <CardTitle className="text-xl flex items-center gap-2">
                  <FaChartLine className="text-[var(--primary-color)]" />
                  <span>المصروفات الشهرية</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {monthlyExpenses.length > 0 ? (
                    <LineChart data={monthlyExpensesChartData} height={300} width={400} />
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-gray-500">لا توجد بيانات للمصروفات الشهرية</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* محتوى بنود الميزانية */}
        <TabsContent value="items" className="space-y-4">
          <Card className="bg-white shadow-md border border-[#e0f2ef]">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <FaTags className="text-[var(--primary-color)]" />
                <span>بنود الميزانية</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {budget.items.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                  <p className="text-gray-500 mb-4">لم يتم إضافة أي بنود للميزانية بعد</p>
                  <Button
                    onClick={() => {
                      setNewItemData({
                        categoryId: '',
                        amount: '',
                        notes: ''
                      })
                      setIsAddItemDialogOpen(true)
                    }}
                    className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
                  >
                    <FaPlus className="ml-2" size={14} />
                    إضافة بند جديد
                  </Button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الفئة</TableHead>
                        <TableHead>المبلغ المخصص</TableHead>
                        <TableHead>المصروفات الفعلية</TableHead>
                        <TableHead>المتبقي</TableHead>
                        <TableHead>نسبة الإنجاز</TableHead>
                        <TableHead>الحالة</TableHead>
                        <TableHead>الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {budget.items.map((item) => {
                        const performanceStatus = getBudgetPerformanceStatus(item.utilizationPercentage || 0);
                        return (
                          <TableRow key={item.id} className="group hover:bg-gray-50">
                            <TableCell>
                              <div className="font-medium">{item.category.name}</div>
                              {item.notes && (
                                <div className="text-xs text-gray-500 mt-1">{item.notes}</div>
                              )}
                            </TableCell>
                            <TableCell>{formatAmount(item.amount)}</TableCell>
                            <TableCell>{formatAmount(item.actualAmount)}</TableCell>
                            <TableCell className={item.remainingAmount < 0 ? 'text-red-600 font-medium' : ''}>
                              {formatAmount(item.remainingAmount)}
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col gap-1">
                                <div className="flex items-center justify-between">
                                  <span className="text-xs font-medium">{Math.round(item.utilizationPercentage || 0)}%</span>
                                </div>
                                <Progress
                                  value={item.utilizationPercentage || 0}
                                  className="h-2"
                                  indicatorClassName={getUtilizationColor(item.utilizationPercentage || 0)}
                                />
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={performanceStatus.color}>
                                <span className="flex items-center gap-1 whitespace-nowrap">
                                  {performanceStatus.icon}
                                  {performanceStatus.status}
                                </span>
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2 opacity-70 group-hover:opacity-100">
                                <Button
                                  variant="ghost"
                                  className="h-8 w-8 p-0 text-[var(--primary-color)]"
                                  onClick={() => {
                                    setSelectedItem(item)
                                    setEditItemData({
                                      amount: item.amount.toString(),
                                      notes: item.notes || ''
                                    })
                                    setIsEditItemDialogOpen(true)
                                  }}
                                  title="تعديل البند"
                                >
                                  <FaEdit size={14} />
                                </Button>
                                <Button
                                  variant="ghost"
                                  className="h-8 w-8 p-0 text-red-500"
                                  onClick={() => handleDeleteItem(item.id)}
                                  title="حذف البند"
                                >
                                  <FaTrash size={14} />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* محتوى الرسوم البيانية */}
        <TabsContent value="charts" className="space-y-4">
          <Card className="bg-white shadow-md border border-[#e0f2ef]">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <FaChartBar className="text-[var(--primary-color)]" />
                <span>مقارنة المبالغ المخصصة والمصروفات الفعلية</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <BarChart data={barChartData} height={400} width={800} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* محتوى سجل المراجعات */}
        <TabsContent value="reviews" className="space-y-4">
          <Card className="bg-white shadow-md border border-[#e0f2ef]">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-xl flex items-center gap-2">
                  <FaCheckCircle className="text-[var(--primary-color)]" />
                  <span>سجل المراجعات والاعتمادات</span>
                </CardTitle>
                <CardDescription>
                  سجل تغييرات حالة الميزانية والمراجعات
                </CardDescription>
              </div>
              <Button
                onClick={() => {
                  setReviewData({
                    newStatus: budget.status,
                    notes: ''
                  })
                  setIsReviewDialogOpen(true)
                }}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
              >
                <FaCheckCircle size={14} />
                <span>مراجعة جديدة</span>
              </Button>
            </CardHeader>
            <CardContent>
              {budgetReviews.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                  <p className="text-gray-500 mb-4">لا توجد مراجعات مسجلة لهذه الميزانية</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>التاريخ</TableHead>
                        <TableHead>الحالة السابقة</TableHead>
                        <TableHead>الحالة الجديدة</TableHead>
                        <TableHead>ملاحظات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {budgetReviews.map((review) => (
                        <TableRow key={review.id}>
                          <TableCell>{formatDate(review.date)}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${getBudgetStatusColor(review.oldStatus)}`}>
                              {getBudgetStatusText(review.oldStatus)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${getBudgetStatusColor(review.newStatus)}`}>
                              {getBudgetStatusText(review.newStatus)}
                            </span>
                          </TableCell>
                          <TableCell>{review.notes || '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* نافذة إضافة بند جديد */}
      <AnimatedDialog
        isOpen={isAddItemDialogOpen}
        onClose={() => setIsAddItemDialogOpen(false)}
        title="إضافة بند جديد للميزانية"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="add-item-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaPlus size={14} />
            <span>إضافة</span>
          </Button>
        }
      >
        <form id="add-item-form" onSubmit={handleAddItem} className="space-y-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="categoryId" className="text-right col-span-1">
              الفئة <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                value={newItemData.categoryId}
                onValueChange={(value) => setNewItemData({ ...newItemData, categoryId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر فئة المصروفات" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right col-span-1">
              المبلغ <span className="text-red-500">*</span>
            </Label>
            <Input
              id="amount"
              type="number"
              value={newItemData.amount}
              onChange={(e) => setNewItemData({ ...newItemData, amount: e.target.value })}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right col-span-1">
              ملاحظات
            </Label>
            <Input
              id="notes"
              value={newItemData.notes}
              onChange={(e) => setNewItemData({ ...newItemData, notes: e.target.value })}
              className="col-span-3"
            />
          </div>
        </form>
      </AnimatedDialog>

      {/* نافذة تحرير بند */}
      <AnimatedDialog
        isOpen={isEditItemDialogOpen}
        onClose={() => setIsEditItemDialogOpen(false)}
        title={`تحرير بند: ${selectedItem?.category.name || ''}`}
        variant="primary"
        footer={
          <Button
            type="submit"
            form="edit-item-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaEdit size={14} />
            <span>تحديث</span>
          </Button>
        }
      >
        {selectedItem && (
          <form id="edit-item-form" onSubmit={handleEditItem} className="space-y-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-amount" className="text-right col-span-1">
                المبلغ <span className="text-red-500">*</span>
              </Label>
              <Input
                id="edit-amount"
                type="number"
                value={editItemData.amount}
                onChange={(e) => setEditItemData({ ...editItemData, amount: e.target.value })}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-notes" className="text-right col-span-1">
                ملاحظات
              </Label>
              <Input
                id="edit-notes"
                value={editItemData.notes}
                onChange={(e) => setEditItemData({ ...editItemData, notes: e.target.value })}
                className="col-span-3"
              />
            </div>
          </form>
        )}
      </AnimatedDialog>

      {/* نافذة نسخ الميزانية */}
      <AnimatedDialog
        isOpen={isCopyDialogOpen}
        onClose={() => setIsCopyDialogOpen(false)}
        title="نسخ الميزانية"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="copy-budget-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaClipboardList size={14} />
            <span>نسخ الميزانية</span>
          </Button>
        }
      >
        <form id="copy-budget-form" onSubmit={handleCopyBudget} className="space-y-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="newName" className="text-right col-span-1">
              اسم الميزانية الجديدة <span className="text-red-500">*</span>
            </Label>
            <Input
              id="newName"
              value={copyBudgetData.newName}
              onChange={(e) => setCopyBudgetData({ ...copyBudgetData, newName: e.target.value })}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="newStartDate" className="text-right col-span-1">
              تاريخ البداية الجديد <span className="text-red-500">*</span>
            </Label>
            <Input
              id="newStartDate"
              type="date"
              value={copyBudgetData.newStartDate}
              onChange={(e) => setCopyBudgetData({ ...copyBudgetData, newStartDate: e.target.value })}
              className="col-span-3"
              required
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="newEndDate" className="text-right col-span-1">
              تاريخ النهاية الجديد <span className="text-red-500">*</span>
            </Label>
            <Input
              id="newEndDate"
              type="date"
              value={copyBudgetData.newEndDate}
              onChange={(e) => setCopyBudgetData({ ...copyBudgetData, newEndDate: e.target.value })}
              className="col-span-3"
              required
            />
          </div>
          <div className="flex items-center gap-2 mt-4">
            <input
              type="checkbox"
              id="copyItems"
              checked={copyBudgetData.copyItems}
              onChange={(e) => setCopyBudgetData({ ...copyBudgetData, copyItems: e.target.checked })}
              className="h-4 w-4 text-[var(--primary-color)] focus:ring-[var(--primary-color)]"
            />
            <Label htmlFor="copyItems" className="cursor-pointer">
              نسخ بنود الميزانية
            </Label>
          </div>
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200 mt-4">
            <p className="text-sm text-blue-800">
              سيتم إنشاء نسخة جديدة من الميزانية الحالية مع الاسم والتواريخ الجديدة.
              {copyBudgetData.copyItems
                ? ' سيتم أيضًا نسخ جميع بنود الميزانية الحالية.'
                : ' لن يتم نسخ بنود الميزانية ويمكنك إضافتها لاحقًا.'}
            </p>
          </div>
        </form>
      </AnimatedDialog>

      {/* نافذة مراجعة واعتماد الميزانية */}
      <AnimatedDialog
        isOpen={isReviewDialogOpen}
        onClose={() => setIsReviewDialogOpen(false)}
        title="مراجعة واعتماد الميزانية"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="review-budget-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaCheckCircle size={14} />
            <span>تأكيد المراجعة</span>
          </Button>
        }
      >
        <form id="review-budget-form" onSubmit={handleChangeStatus} className="space-y-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="newStatus" className="text-right col-span-1">
              الحالة الجديدة <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                value={reviewData.newStatus}
                onValueChange={(value) => setReviewData({ ...reviewData, newStatus: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر حالة الميزانية" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="DRAFT">مسودة</SelectItem>
                  <SelectItem value="ACTIVE">نشطة</SelectItem>
                  <SelectItem value="COMPLETED">مكتملة</SelectItem>
                  <SelectItem value="ARCHIVED">مؤرشفة</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="reviewNotes" className="text-right col-span-1 mt-2">
              ملاحظات المراجعة
            </Label>
            <div className="col-span-3">
              <textarea
                id="reviewNotes"
                value={reviewData.notes}
                onChange={(e) => setReviewData({ ...reviewData, notes: e.target.value })}
                className="w-full min-h-[100px] p-2 border border-gray-300 rounded-md"
                placeholder="أدخل ملاحظات المراجعة هنا..."
              />
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200 mt-4">
            <p className="text-sm text-blue-800">
              سيتم تسجيل هذه المراجعة في سجل المراجعات مع تغيير حالة الميزانية.
            </p>
          </div>
        </form>
      </AnimatedDialog>
      </div>
    </ProtectedRoute>
  )
}

'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaQuran, FaFacebook, FaTwitter, FaInstagram, FaYoutube, FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';
import axios from 'axios';
import SiteLogo from '@/components/SiteLogo';

// تعريف واجهات البيانات
interface Program {
  id: number;
  title: string;
  description: string;
  iconName: string;
  price: string;
  popular: boolean;
}

interface FooterLink {
  id: string;
  title: string;
  url: string;
  isActive: boolean;
  order: number;
}

interface SocialLinks {
  facebook?: string;
  twitter?: string;
  instagram?: string;
  youtube?: string;
}

interface ContactInfo {
  email?: string;
  phone?: string;
  address?: string;
}

interface SiteSettings {
  siteName: string;
  logoUrl?: string;
  primaryColor: string;
  secondaryColor: string;
  footerLinks: FooterLink[];
  socialLinks: SocialLinks;
  contactInfo: ContactInfo;
}

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const [programs, setPrograms] = useState<Program[]>([]);
  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null);

  useEffect(() => {
    // تحميل الإعدادات من localStorage أولاً
    const loadSettingsFromStorage = () => {
      try {
        const savedSettings = localStorage.getItem('siteSettings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          if (settings && typeof settings === 'object') {
            setSiteSettings(settings);
            return true; // تم تحميل الإعدادات بنجاح
          }
        }
      } catch (error) {
        console.error('Error loading settings from localStorage:', error);
      }
      return false; // لم يتم تحميل الإعدادات
    };

    // تحميل الإعدادات فوراً من localStorage
    const settingsLoaded = loadSettingsFromStorage();

    const fetchData = async () => {
      try {
        const [settingsResponse, programsResponse] = await Promise.all([
          axios.get<{ settings?: SiteSettings }>('/api/settings'),
          axios.get<Program[]>('/api/programs')
        ]);

        // استخدام type assertion بطريقة آمنة
        type ResponseType = { settings?: SiteSettings };
        const settingsData = settingsResponse.data as ResponseType;

        if (settingsData && settingsData.settings) {
          // حفظ الإعدادات في localStorage
          localStorage.setItem('siteSettings', JSON.stringify(settingsData.settings));
          setSiteSettings(settingsData.settings);
        } else if (!settingsLoaded) {
          // إذا لم توجد إعدادات في الخادم ولم تكن محملة من localStorage، استخدم الافتراضية
          const defaultSettings: SiteSettings = {
            siteName: 'نظام برهان للقرآن الكريم',
            primaryColor: 'var(--primary-color)',
            secondaryColor: 'var(--secondary-color)',
            footerLinks: [
              { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
              { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
              { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
              { id: '4', title: 'اتصل بنا', url: '/contact', isActive: true, order: 4 },
              { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
              { id: '6', title: 'التسجيل', url: '/register', isActive: true, order: 6 },
              { id: '7', title: 'تسجيل الدخول', url: '/login', isActive: true, order: 7 },
            ],
            socialLinks: {
              facebook: 'https://facebook.com',
              twitter: 'https://twitter.com',
              instagram: 'https://instagram.com',
              youtube: 'https://youtube.com',
            },
            contactInfo: {
              email: '<EMAIL>',
              phone: '+213 123 456 789',
              address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر',
            }
          };
          setSiteSettings(defaultSettings);
        }

        if (Array.isArray(programsResponse.data)) {
          setPrograms(programsResponse.data);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        // إذا فشل جلب الإعدادات ولم تكن محملة من localStorage، استخدم الافتراضية
        if (!settingsLoaded) {
          const defaultSettings: SiteSettings = {
            siteName: 'نظام برهان للقرآن الكريم',
            primaryColor: 'var(--primary-color)',
            secondaryColor: 'var(--secondary-color)',
            footerLinks: [
              { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
              { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
              { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
              { id: '4', title: 'اتصل بنا', url: '/contact', isActive: true, order: 4 },
              { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
              { id: '6', title: 'التسجيل', url: '/register', isActive: true, order: 6 },
              { id: '7', title: 'تسجيل الدخول', url: '/login', isActive: true, order: 7 },
            ],
            socialLinks: {
              facebook: 'https://facebook.com',
              twitter: 'https://twitter.com',
              instagram: 'https://instagram.com',
              youtube: 'https://youtube.com',
            },
            contactInfo: {
              email: '<EMAIL>',
              phone: '+213 123 456 789',
              address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر',
            }
          };
          setSiteSettings(defaultSettings);
        }
      }
    };

    fetchData();

    // إضافة مستمع للتحديثات في localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteSettings' && e.newValue) {
        try {
          const newSettings = JSON.parse(e.newValue);
          setSiteSettings(newSettings);
        } catch (error) {
          console.error('Error parsing updated settings:', error);
        }
      }
    };

    // إضافة مستمع مخصص للتحديثات الداخلية
    const handleSettingsUpdate = (event: CustomEvent) => {
      setSiteSettings(event.detail);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);
    };
  }, []);

  return (
    <footer className="bg-gray-900 text-white" dir="rtl">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* About Section */}
          <div>
            <div className="flex items-center mb-4">
              <SiteLogo
                size="lg"
                showText={true}
                iconColor={siteSettings?.secondaryColor || 'var(--secondary-color)'}
                textClassName="text-2xl font-bold"
              />
            </div>
            <p className="text-gray-400 mb-4">
              مؤسسة تعليمية رائدة في مجال تعليم القرآن الكريم وعلومه، نسعى لنشر كتاب الله وتعليمه بأحدث الوسائل التعليمية.
            </p>
            <div className="flex space-x-4 space-x-reverse">
              {siteSettings?.socialLinks?.facebook && (
                <a
                  href={siteSettings.socialLinks.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-gray-400 hover:text-[${siteSettings?.secondaryColor}] transition-colors`}
                >
                  <FaFacebook className="text-xl" />
                </a>
              )}
              {siteSettings?.socialLinks?.twitter && (
                <a
                  href={siteSettings.socialLinks.twitter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-gray-400 hover:text-[${siteSettings?.secondaryColor}] transition-colors`}
                >
                  <FaTwitter className="text-xl" />
                </a>
              )}
              {siteSettings?.socialLinks?.instagram && (
                <a
                  href={siteSettings.socialLinks.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-gray-400 hover:text-[${siteSettings?.secondaryColor}] transition-colors`}
                >
                  <FaInstagram className="text-xl" />
                </a>
              )}
              {siteSettings?.socialLinks?.youtube && (
                <a
                  href={siteSettings.socialLinks.youtube}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`text-gray-400 hover:text-[${siteSettings?.secondaryColor}] transition-colors`}
                >
                  <FaYoutube className="text-xl" />
                </a>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-4">روابط سريعة</h3>
            <ul className="space-y-2">
              {siteSettings?.footerLinks && siteSettings.footerLinks
                .filter((link: FooterLink) => link.isActive)
                .sort((a: FooterLink, b: FooterLink) => a.order - b.order)
                .map((link: FooterLink) => (
                  <li key={link.id}>
                    <Link
                      href={link.url}
                      className={`text-gray-400 hover:text-[${siteSettings?.secondaryColor}] transition-colors`}
                    >
                      {link.title}
                    </Link>
                  </li>
                ))
              }
            </ul>
          </div>

          {/* Programs */}
          <div>
            <h3 className="text-xl font-bold mb-4">برامجنا</h3>
            <ul className="space-y-2">
              {programs.map((program) => (
                <li key={program.id}>
                  <Link
                    href={`/programs`}
                    className={`text-gray-400 hover:text-[${siteSettings?.secondaryColor}] transition-colors`}
                  >
                    {program.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-bold mb-4">معلومات الاتصال</h3>
            <ul className="space-y-3">
              {siteSettings?.contactInfo?.address && (
                <li className="flex items-start">
                  <FaMapMarkerAlt className="mt-1 ml-2" style={{ color: siteSettings?.secondaryColor || 'var(--secondary-color)' }} />
                  <span className="text-gray-400">{siteSettings.contactInfo.address}</span>
                </li>
              )}
              {siteSettings?.contactInfo?.phone && (
                <li className="flex items-center">
                  <FaPhone className="ml-2" style={{ color: siteSettings?.secondaryColor || 'var(--secondary-color)' }} />
                  <span className="text-gray-400">{siteSettings.contactInfo.phone}</span>
                </li>
              )}
              {siteSettings?.contactInfo?.email && (
                <li className="flex items-center">
                  <FaEnvelope className="ml-2" style={{ color: siteSettings?.secondaryColor || 'var(--secondary-color)' }} />
                  <span className="text-gray-400">{siteSettings.contactInfo.email}</span>
                </li>
              )}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-12 pt-8 text-center">
          <p className="text-gray-400">
            &copy; {currentYear} {siteSettings?.siteName || 'نظام برهان للقرآن الكريم'}. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

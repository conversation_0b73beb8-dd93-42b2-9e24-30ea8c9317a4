# مواصفات APIs تقارير المشرف

## نظرة عامة
هذا المستند يحدد مواصفات APIs المطلوبة لنظام تقارير المشرف، بما في ذلك التقارير الأدبية والمالية.

## API التقرير الأدبي

### GET /api/reports/literary

#### الوصف
إنشاء وإرجاع التقرير الأدبي للفترة المحددة

#### المعاملات (Query Parameters)
- `startDate` (string, required): تاريخ البداية بصيغة ISO 8601
- `endDate` (string, required): تاريخ النهاية بصيغة ISO 8601
- `format` (string, optional): صيغة الإرجاع (json, pdf, word) - افتراضي: json

#### مثال على الطلب
```
GET /api/reports/literary?startDate=2024-01-01T00:00:00.000Z&endDate=2024-12-31T23:59:59.999Z
```

#### الاستجابة الناجحة (200)
```json
{
  "success": true,
  "data": {
    "period": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T23:59:59.999Z",
      "duration": 365
    },
    "generalStats": {
      "totalStudents": 250,
      "totalTeachers": 15,
      "totalClasses": 8,
      "totalMemorizers": 45,
      "totalKhatmSessions": 12,
      "totalActivities": 25
    },
    "studentsDetails": {
      "total": 250,
      "byClass": [
        {
          "classeId": "class-1",
          "className": "الصف الأول",
          "count": 30
        }
      ],
      "classes": [
        {
          "id": "class-1",
          "name": "الصف الأول",
          "capacity": 35,
          "studentsCount": 30,
          "students": [
            {
              "id": "student-1",
              "name": "أحمد محمد"
            }
          ]
        }
      ]
    },
    "teachersDetails": {
      "total": 15,
      "bySpecialization": [
        {
          "specialization": "القرآن الكريم",
          "count": 8
        }
      ]
    },
    "attendanceDetails": {
      "total": 5000,
      "byStatus": [
        {
          "status": "PRESENT",
          "count": 4500
        },
        {
          "status": "ABSENT",
          "count": 500
        }
      ],
      "attendanceRate": 90.0,
      "absenteeRate": 10.0
    },
    "quranDetails": {
      "totalProgress": 120,
      "memorizers": 45,
      "progressRecords": [
        {
          "studentName": "أحمد محمد",
          "surahName": "الفاتحة",
          "surahNumber": 1,
          "memorization": 10,
          "tajweed": 9,
          "startDate": "2024-01-01T00:00:00.000Z",
          "completionDate": "2024-01-15T00:00:00.000Z"
        }
      ],
      "averageMemorization": 8.5,
      "averageTajweed": 8.2
    },
    "khatmDetails": {
      "total": 12,
      "sessions": [
        {
          "id": "khatm-1",
          "title": "ختم سورة البقرة",
          "date": "2024-03-15T00:00:00.000Z",
          "location": "القاعة الكبرى",
          "teacherName": "الأستاذ محمد",
          "surahName": "البقرة",
          "attendeesCount": 25,
          "presentCount": 23
        }
      ]
    },
    "examsDetails": {
      "total": 8,
      "exams": [
        {
          "id": "exam-1",
          "description": "امتحان الفصل الأول",
          "maxPoints": 100,
          "passingPoints": 60,
          "studentsCount": 30,
          "averageGrade": 75.5,
          "passedStudents": 28
        }
      ]
    },
    "activitiesDetails": {
      "total": 25,
      "activities": [
        {
          "id": "activity-1",
          "title": "محاضرة في السيرة النبوية",
          "description": "محاضرة تعليمية",
          "date": "2024-02-10T00:00:00.000Z",
          "organizer": "الأستاذ أحمد"
        }
      ]
    }
  },
  "message": "تم إنشاء التقرير الأدبي بنجاح"
}
```

#### أخطاء محتملة
- `400 Bad Request`: تواريخ غير صحيحة
- `500 Internal Server Error`: خطأ في الخادم

## API التقرير المالي

### GET /api/reports/financial

#### الوصف
إنشاء وإرجاع التقرير المالي للفترة المحددة

#### المعاملات (Query Parameters)
- `startDate` (string, required): تاريخ البداية بصيغة ISO 8601
- `endDate` (string, required): تاريخ النهاية بصيغة ISO 8601
- `type` (string, optional): نوع التقرير (all, payments, donations, expenses) - افتراضي: all
- `format` (string, optional): صيغة الإرجاع (json, excel, pdf) - افتراضي: json

#### مثال على الطلب
```
GET /api/reports/financial?startDate=2024-01-01T00:00:00.000Z&endDate=2024-12-31T23:59:59.999Z&type=all
```

#### الاستجابة الناجحة (200)
```json
{
  "success": true,
  "data": {
    "period": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T23:59:59.999Z",
      "duration": 365
    },
    "executiveSummary": {
      "openingBalance": 45000.00,
      "closingBalance": 70000.00,
      "totalIncome": 85000.00,
      "totalExpenses": 60000.00,
      "netProfit": 25000.00,
      "totalTransactions": 450
    },
    "incomeDetails": {
      "studentPayments": {
        "count": 150,
        "amount": 45000.00,
        "percentage": 53.0,
        "details": []
      },
      "donations": {
        "count": 25,
        "amount": 30000.00,
        "percentage": 35.3,
        "details": []
      },
      "otherIncomes": {
        "count": 10,
        "amount": 10000.00,
        "percentage": 11.7,
        "details": []
      }
    },
    "expenseDetails": {
      "total": 60000.00,
      "byCategory": [
        {
          "id": "cat-1",
          "name": "رواتب ومكافآت",
          "description": "رواتب المعلمين والموظفين",
          "icon": "users",
          "color": "#3B82F6",
          "expensesCount": 12,
          "totalAmount": 30000.00,
          "percentage": 50.0
        }
      ],
      "transactions": []
    },
    "paymentMethodStats": [
      {
        "id": "method-1",
        "name": "نقدي",
        "paymentsCount": 100,
        "donationsCount": 20,
        "paymentsAmount": 30000.00,
        "donationsAmount": 20000.00,
        "totalAmount": 50000.00
      }
    ],
    "monthlyStats": [
      {
        "month": "2024-01",
        "payments": {
          "count": 15,
          "amount": 4500.00
        },
        "donations": {
          "count": 3,
          "amount": 2500.00
        },
        "expenses": {
          "count": 8,
          "amount": 5000.00
        },
        "totalIncome": 7000.00,
        "netProfit": 2000.00
      }
    ]
  },
  "message": "تم إنشاء التقرير المالي بنجاح"
}
```

#### أخطاء محتملة
- `400 Bad Request`: تواريخ غير صحيحة أو معاملات خاطئة
- `404 Not Found`: لم يتم العثور على الخزينة
- `500 Internal Server Error`: خطأ في الخادم

## APIs إضافية مطلوبة

### GET /api/reports/export/literary
تصدير التقرير الأدبي بصيغ مختلفة (PDF, Word)

### GET /api/reports/export/financial
تصدير التقرير المالي بصيغ مختلفة (Excel, PDF)

### GET /api/reports/quick-stats
الحصول على إحصائيات سريعة للوحة الرئيسية

#### مثال على الاستجابة
```json
{
  "success": true,
  "data": {
    "students": 250,
    "memorizers": 45,
    "activities": 25,
    "income": 85000.00,
    "expenses": 60000.00
  }
}
```

## معايير الأمان والأداء

### الأمان
- جميع APIs تتطلب مصادقة صحيحة
- التحقق من صلاحيات المستخدم قبل الوصول للبيانات
- تشفير البيانات الحساسة في الاستجابات

### الأداء
- استخدام فهرسة قاعدة البيانات للاستعلامات الكبيرة
- تطبيق pagination للبيانات الكبيرة
- استخدام caching للبيانات المتكررة
- تحسين الاستعلامات لتقليل وقت الاستجابة

### معالجة الأخطاء
- إرجاع رسائل خطأ واضحة ومفيدة
- تسجيل الأخطاء للمراجعة والتحليل
- استخدام HTTP status codes المناسبة

### التوثيق
- توثيق جميع المعاملات والاستجابات
- أمثلة واضحة لكل API
- شرح أكواد الأخطاء المحتملة

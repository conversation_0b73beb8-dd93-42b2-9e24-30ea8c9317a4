/**
 * نظام التنقيط الموحد للمدرسة القرآنية
 * يدعم تحويل بين أنظمة التنقيط المختلفة
 */

// أنواع أنظمة التنقيط المدعومة
export enum GradingScale {
  SCALE_10 = '10',      // من 0 إلى 10
  SCALE_20 = '20',      // من 0 إلى 20 (النظام الأساسي)
  SCALE_100 = '100'     // من 0 إلى 100
}

// تعريف مستويات التقدير
export interface GradeLevel {
  name: string;
  nameAr: string;
  minPercentage: number;
  maxPercentage: number;
  color: string;
  description: string;
}

// مستويات التقدير الموحدة (بالنسبة المئوية)
export const GRADE_LEVELS: GradeLevel[] = [
  {
    name: 'EXCELLENT',
    nameAr: 'ممتاز',
    minPercentage: 80,
    maxPercentage: 100,
    color: '#10B981', // أخضر
    description: 'أداء متميز وممتاز'
  },
  {
    name: 'VERY_GOOD',
    nameAr: 'جيد جداً',
    minPercentage: 70,
    maxPercentage: 79.99,
    color: '#3B82F6', // أزرق
    description: 'أداء جيد جداً'
  },
  {
    name: 'GOOD',
    nameAr: 'جيد',
    minPercentage: 60,
    maxPercentage: 69.99,
    color: '#F59E0B', // أصفر
    description: 'أداء جيد'
  },
  {
    name: 'ACCEPTABLE',
    nameAr: 'مقبول',
    minPercentage: 50,
    maxPercentage: 59.99,
    color: '#F97316', // برتقالي
    description: 'أداء مقبول'
  },
  {
    name: 'WEAK',
    nameAr: 'ضعيف',
    minPercentage: 0,
    maxPercentage: 49.99,
    color: '#EF4444', // أحمر
    description: 'أداء ضعيف يحتاج تحسين'
  }
];

// إعدادات النظام الافتراضية
export const DEFAULT_SETTINGS = {
  primaryScale: GradingScale.SCALE_20,
  passingPercentage: 50, // 50% للنجاح
  excellencePercentage: 80, // 80% للتميز
  maxPoints: 20,
  passingPoints: 10
};

/**
 * تحويل الدرجة من مقياس إلى آخر
 */
export function convertGrade(
  grade: number,
  fromScale: GradingScale,
  toScale: GradingScale
): number {
  // تحويل إلى نسبة مئوية أولاً
  const percentage = (grade / parseFloat(fromScale)) * 100;
  
  // تحويل من النسبة المئوية إلى المقياس المطلوب
  const convertedGrade = (percentage / 100) * parseFloat(toScale);
  
  // تقريب إلى منزلة عشرية واحدة
  return Math.round(convertedGrade * 10) / 10;
}

/**
 * حساب النسبة المئوية من الدرجة
 */
export function calculatePercentage(
  grade: number,
  maxPoints: number
): number {
  if (maxPoints === 0) return 0;
  const percentage = (grade / maxPoints) * 100;
  return Math.round(percentage * 10) / 10;
}

/**
 * الحصول على مستوى التقدير بناءً على النسبة المئوية
 */
export function getGradeLevel(percentage: number): GradeLevel {
  return GRADE_LEVELS.find(level => 
    percentage >= level.minPercentage && percentage <= level.maxPercentage
  ) || GRADE_LEVELS[GRADE_LEVELS.length - 1]; // إرجاع الأضعف إذا لم يوجد
}

/**
 * الحصول على مستوى التقدير بناءً على الدرجة والحد الأقصى
 */
export function getGradeLevelByScore(
  grade: number,
  maxPoints: number
): GradeLevel {
  const percentage = calculatePercentage(grade, maxPoints);
  return getGradeLevel(percentage);
}

/**
 * تحديد حالة النجاح/الرسوب
 */
export function getPassStatus(
  grade: number,
  maxPoints: number,
  passingPercentage: number = DEFAULT_SETTINGS.passingPercentage
): 'PASSED' | 'FAILED' | 'EXCELLENT' {
  const percentage = calculatePercentage(grade, maxPoints);
  
  if (percentage >= DEFAULT_SETTINGS.excellencePercentage) {
    return 'EXCELLENT';
  } else if (percentage >= passingPercentage) {
    return 'PASSED';
  } else {
    return 'FAILED';
  }
}

/**
 * تحويل الدرجة إلى النظام الأساسي (20 نقطة)
 */
export function normalizeToScale20(
  grade: number,
  currentMaxPoints: number
): number {
  return convertGrade(grade, currentMaxPoints.toString() as GradingScale, GradingScale.SCALE_20);
}

/**
 * تحويل الدرجة من النظام الأساسي إلى مقياس آخر
 */
export function convertFromScale20(
  grade: number,
  targetMaxPoints: number
): number {
  return convertGrade(grade, GradingScale.SCALE_20, targetMaxPoints.toString() as GradingScale);
}

/**
 * تنسيق الدرجة للعرض
 */
export function formatGrade(
  grade: number,
  maxPoints: number,
  showPercentage: boolean = true
): string {
  const percentage = calculatePercentage(grade, maxPoints);
  const gradeLevel = getGradeLevel(percentage);
  
  if (showPercentage) {
    return `${grade}/${maxPoints} (${percentage}% - ${gradeLevel.nameAr})`;
  } else {
    return `${grade}/${maxPoints} - ${gradeLevel.nameAr}`;
  }
}

/**
 * حساب المعدل العام لمجموعة من الدرجات
 */
export function calculateAverage(
  grades: Array<{ grade: number; maxPoints: number; weight?: number }>
): {
  average: number;
  percentage: number;
  gradeLevel: GradeLevel;
} {
  if (grades.length === 0) {
    return {
      average: 0,
      percentage: 0,
      gradeLevel: GRADE_LEVELS[GRADE_LEVELS.length - 1]
    };
  }

  // تحويل جميع الدرجات إلى النظام الأساسي (20)
  const normalizedGrades = grades.map(g => ({
    grade: normalizeToScale20(g.grade, g.maxPoints),
    weight: g.weight || 1
  }));

  // حساب المتوسط المرجح
  const totalWeight = normalizedGrades.reduce((sum, g) => sum + g.weight, 0);
  const weightedSum = normalizedGrades.reduce((sum, g) => sum + (g.grade * g.weight), 0);
  
  const average = totalWeight > 0 ? weightedSum / totalWeight : 0;
  const percentage = calculatePercentage(average, 20);
  const gradeLevel = getGradeLevel(percentage);

  return {
    average: Math.round(average * 10) / 10,
    percentage: Math.round(percentage * 10) / 10,
    gradeLevel
  };
}

/**
 * التحقق من صحة الدرجة
 */
export function validateGrade(
  grade: number,
  maxPoints: number
): { isValid: boolean; error?: string } {
  if (isNaN(grade) || isNaN(maxPoints)) {
    return { isValid: false, error: 'الدرجة والحد الأقصى يجب أن يكونا أرقاماً' };
  }

  if (grade < 0) {
    return { isValid: false, error: 'الدرجة لا يمكن أن تكون سالبة' };
  }

  if (grade > maxPoints) {
    return { isValid: false, error: 'الدرجة لا يمكن أن تتجاوز الحد الأقصى' };
  }

  if (maxPoints <= 0) {
    return { isValid: false, error: 'الحد الأقصى للدرجة يجب أن يكون أكبر من صفر' };
  }

  return { isValid: true };
}

/**
 * إنشاء إحصائيات للدرجات
 */
export function generateGradeStatistics(
  grades: Array<{ grade: number; maxPoints: number }>
): {
  total: number;
  passed: number;
  failed: number;
  excellent: number;
  passRate: number;
  excellenceRate: number;
  average: number;
  highest: number;
  lowest: number;
  distribution: Record<string, number>;
} {
  if (grades.length === 0) {
    return {
      total: 0,
      passed: 0,
      failed: 0,
      excellent: 0,
      passRate: 0,
      excellenceRate: 0,
      average: 0,
      highest: 0,
      lowest: 0,
      distribution: {}
    };
  }

  // تحويل جميع الدرجات إلى نسب مئوية
  const percentages = grades.map(g => calculatePercentage(g.grade, g.maxPoints));
  
  const passed = percentages.filter(p => p >= DEFAULT_SETTINGS.passingPercentage).length;
  const failed = grades.length - passed;
  const excellent = percentages.filter(p => p >= DEFAULT_SETTINGS.excellencePercentage).length;
  
  const average = percentages.reduce((sum, p) => sum + p, 0) / percentages.length;
  const highest = Math.max(...percentages);
  const lowest = Math.min(...percentages);
  
  // توزيع الدرجات حسب المستويات
  const distribution: Record<string, number> = {};
  GRADE_LEVELS.forEach(level => {
    distribution[level.name] = percentages.filter(p => 
      p >= level.minPercentage && p <= level.maxPercentage
    ).length;
  });

  return {
    total: grades.length,
    passed,
    failed,
    excellent,
    passRate: Math.round((passed / grades.length) * 100 * 10) / 10,
    excellenceRate: Math.round((excellent / grades.length) * 100 * 10) / 10,
    average: Math.round(average * 10) / 10,
    highest: Math.round(highest * 10) / 10,
    lowest: Math.round(lowest * 10) / 10,
    distribution
  };
}

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  FileSpreadsheet, 
  FileText, 
  FileDown, 
  FileJson, 
  Download, 
  ChevronDown 
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { 
  exportToExcel, 
  exportToPdf, 
  exportToCsv, 
  exportToHtml,
  exportReportWithRecommendations
} from '@/utils/export-utils';

interface ExportOptionsMenuProps {
  data: {
    title: string;
    tables: {
      title?: string;
      headers: string[];
      data: (string | number | null)[][];
    }[];
    charts?: {
      title?: string;
      type: 'bar' | 'line' | 'pie' | 'doughnut';
      data: {
        labels: string[];
        datasets: {
          label: string;
          data: number[];
          backgroundColor?: string | string[];
          borderColor?: string | string[];
        }[];
      };
      options?: {
        width?: number;
        height?: number;
      };
    }[];
    additionalContent?: string;
  };
  analysisData?: Record<string, unknown>;
  fileName: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  recommendationType?: 'student' | 'class' | 'exam';
  includeRecommendations?: boolean;
}

/**
 * قائمة خيارات التصدير
 * مكون يعرض قائمة منسدلة مع خيارات تصدير البيانات بتنسيقات مختلفة
 */
export function ExportOptionsMenu({
  data,
  analysisData,
  fileName,
  variant = 'outline',
  size = 'default',
  className = '',
  recommendationType,
  includeRecommendations = true
}: ExportOptionsMenuProps) {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async (format: 'pdf' | 'excel' | 'csv' | 'html') => {
    try {
      setIsExporting(true);

      // إذا كانت بيانات التحليل متوفرة، استخدم وظيفة التصدير مع التوصيات
      if (analysisData && includeRecommendations) {
        exportReportWithRecommendations(
          data,
          analysisData,
          format,
          fileName,
          {
            recommendationType,
            includeRecommendations
          }
        );
      } else {
        // استخدم وظائف التصدير العادية
        switch (format) {
          case 'pdf':
            exportToPdf({
              title: data.title,
              fileName: `${fileName}.pdf`,
              tables: data.tables,
              charts: data.charts,
              additionalContent: data.additionalContent ? [
                {
                  text: data.additionalContent,
                  x: 10,
                  y: 10,
                  options: {
                    align: 'right'
                  }
                }
              ] : undefined
            });
            break;

          case 'excel':
            // تحويل البيانات إلى تنسيق مناسب لـ Excel
            const excelData: Record<string, unknown>[] = [];
            
            // إضافة بيانات من كل جدول
            data.tables.forEach(table => {
              // إضافة عنوان الجدول كصف منفصل إذا كان موجودًا
              if (table.title) {
                const titleRow: Record<string, unknown> = {};
                titleRow[table.headers[0]] = table.title;
                excelData.push(titleRow);
              }
              
              // إضافة بيانات الجدول
              table.data.forEach(row => {
                const rowData: Record<string, unknown> = {};
                table.headers.forEach((header, index) => {
                  rowData[header] = row[index];
                });
                excelData.push(rowData);
              });
              
              // إضافة صف فارغ بين الجداول
              const emptyRow: Record<string, unknown> = {};
              excelData.push(emptyRow);
            });
            
            exportToExcel(
              excelData,
              `${fileName}.xlsx`,
              'التقرير'
            );
            break;

          case 'csv':
            // تحويل البيانات إلى تنسيق مناسب لـ CSV
            const csvData: (string | number | null)[][] = [];
            const csvHeaders: string[] = [];
            
            // جمع جميع رؤوس الأعمدة الفريدة من جميع الجداول
            data.tables.forEach(table => {
              table.headers.forEach(header => {
                if (!csvHeaders.includes(header)) {
                  csvHeaders.push(header);
                }
              });
            });
            
            // إضافة بيانات من كل جدول
            data.tables.forEach(table => {
              // إضافة عنوان الجدول كصف منفصل إذا كان موجودًا
              if (table.title) {
                const titleRow: (string | number | null)[] = new Array(csvHeaders.length).fill(null);
                titleRow[0] = table.title;
                csvData.push(titleRow);
              }
              
              // إضافة بيانات الجدول
              table.data.forEach(row => {
                const csvRow: (string | number | null)[] = new Array(csvHeaders.length).fill(null);
                
                table.headers.forEach((header, index) => {
                  const headerIndex = csvHeaders.indexOf(header);
                  if (headerIndex !== -1) {
                    csvRow[headerIndex] = row[index];
                  }
                });
                
                csvData.push(csvRow);
              });
              
              // إضافة صف فارغ بين الجداول
              csvData.push(new Array(csvHeaders.length).fill(null));
            });
            
            exportToCsv(
              csvData,
              `${fileName}.csv`,
              csvHeaders
            );
            break;

          case 'html':
            exportToHtml({
              title: data.title,
              fileName: `${fileName}.html`,
              tables: data.tables,
              charts: data.charts,
              additionalContent: data.additionalContent
            });
            break;
        }
      }
    } catch (error) {
      console.error(`Error exporting to ${format}:`, error);
      toast.error(`حدث خطأ أثناء التصدير بتنسيق ${format}`);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={`flex items-center gap-1 ${className}`}
          disabled={isExporting}
        >
          <Download className="h-4 w-4 ml-1" />
          تصدير
          <ChevronDown className="h-4 w-4 mr-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <DropdownMenuItem onClick={() => handleExport('pdf')} className="cursor-pointer">
          <FileText className="h-4 w-4 ml-2" />
          تصدير كملف PDF
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('excel')} className="cursor-pointer">
          <FileSpreadsheet className="h-4 w-4 ml-2" />
          تصدير كملف Excel
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('csv')} className="cursor-pointer">
          <FileDown className="h-4 w-4 ml-2" />
          تصدير كملف CSV
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExport('html')} className="cursor-pointer">
          <FileJson className="h-4 w-4 ml-2" />
          تصدير كصفحة HTML
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, Pencil, Trash2, ArrowR<PERSON>, Eye, Award } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Link from 'next/link';
import { ResponsiveTablesHandler } from '@/components/responsive-tables-handler';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

import CertificateTemplates, { TemplateType } from '../components/CertificateTemplates';

type Certificate = {
  id: number;
  title: string;
  description: string;
  templateUrl: string;
  type: string;
  createdAt: string;
};

type Student = {
  id: number;
  name: string;
  username: string;
  totalPoints: number;
  classe: {
    id: number;
    name: string;
  } | null;
};

export default function CertificatesPage() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [isIssueDialogOpen, setIsIssueDialogOpen] = useState(false);
  const [selectedCertificate, setSelectedCertificate] = useState<Certificate | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    templateUrl: '',
    type: 'ACHIEVEMENT' as TemplateType
  });
  const [selectedTemplateType, setSelectedTemplateType] = useState<TemplateType>('ACHIEVEMENT');

  useEffect(() => {
    fetchCertificates();
    fetchTopStudents();
  }, []);

  const fetchCertificates = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/certificates');
      if (!response.ok) throw new Error('Failed to fetch certificates');
      const data = await response.json();
      setCertificates(data.data || []);
    } catch (error) {
      console.error('Error fetching certificates:', error);
      toast.error('حدث خطأ أثناء جلب الشهادات');
    } finally {
      setLoading(false);
    }
  };

  const fetchTopStudents = async () => {
    try {
      setLoadingStudents(true);
      const response = await fetch('/api/students?sort=totalPoints&order=desc&limit=50');
      if (!response.ok) throw new Error('Failed to fetch students');
      const data = await response.json();

      // تحقق من شكل البيانات المرجعة وتعامل معها بشكل مناسب
      if (Array.isArray(data)) {
        // إذا كانت البيانات مصفوفة مباشرة
        setStudents(data);
      } else if (data.students && Array.isArray(data.students)) {
        // إذا كانت البيانات في شكل { students, total, pages }
        setStudents(data.students);
      } else if (data.data && Array.isArray(data.data)) {
        // إذا كانت البيانات في شكل { data }
        setStudents(data.data);
      } else {
        // إذا لم يتم التعرف على شكل البيانات
        console.error('Unexpected data format:', data);
        setStudents([]);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('حدث خطأ أثناء جلب بيانات الطلاب');
    } finally {
      setLoadingStudents(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    if (name === 'type') {
      setSelectedTemplateType(value as TemplateType);
    }
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStudentSelectChange = (value: string) => {
    setSelectedStudent(value);
  };

  const handleSubmit = async () => {
    try {
      // التحقق من وجود البيانات المطلوبة
      if (!formData.title.trim()) {
        toast.error('عنوان الشهادة مطلوب');
        return;
      }

      const url = selectedCertificate
        ? `/api/certificates?id=${selectedCertificate.id}`
        : '/api/certificates';

      const method = selectedCertificate ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      // قراءة بيانات الاستجابة أولاً
      const responseData = await response.json();

      if (!response.ok) {
        // استخدام رسالة الخطأ من الخادم إذا كانت متوفرة
        throw new Error(responseData.error || 'Failed to save certificate');
      }

      toast.success(responseData.message || 'تم حفظ الشهادة بنجاح');

      fetchCertificates();
      setIsAddDialogOpen(false);
      setIsEditDialogOpen(false);
      setSelectedCertificate(null);
    } catch (error) {
      console.error('Error saving certificate:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ الشهادة');
    }
  };

  const handleDelete = async () => {
    if (!selectedCertificate) {
      toast.error('لم يتم تحديد شهادة للحذف');
      return;
    }

    try {
      const response = await fetch(`/api/certificates?id=${selectedCertificate.id}`, {
        method: 'DELETE'
      });

      // قراءة بيانات الاستجابة أولاً
      const responseData = await response.json();

      if (!response.ok) {
        // استخدام رسالة الخطأ من الخادم إذا كانت متوفرة
        throw new Error(responseData.error || 'Failed to delete certificate');
      }

      toast.success(responseData.message || 'تم حذف الشهادة بنجاح');

      fetchCertificates();
      setIsDeleteDialogOpen(false);
      setSelectedCertificate(null);
    } catch (error) {
      console.error('Error deleting certificate:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حذف الشهادة');
    }
  };

  const handleIssueCertificate = async () => {
    if (!selectedCertificate || !selectedStudent) {
      toast.error('يرجى اختيار الطالب والشهادة');
      return;
    }

    try {
      const response = await fetch('/api/student-certificates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          certificateId: selectedCertificate.id,
          studentId: parseInt(selectedStudent)
        })
      });

      // قراءة بيانات الاستجابة أولاً
      const responseData = await response.json();

      if (!response.ok) {
        // استخدام رسالة الخطأ من الخادم إذا كانت متوفرة
        throw new Error(responseData.error || 'Failed to issue certificate');
      }

      toast.success(responseData.message || 'تم إصدار الشهادة بنجاح');
      setIsIssueDialogOpen(false);
    } catch (error) {
      console.error('Error issuing certificate:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إصدار الشهادة');
    }
  };

  const openEditDialog = (certificate: Certificate) => {
    setSelectedCertificate(certificate);
    setFormData({
      title: certificate.title,
      description: certificate.description,
      templateUrl: certificate.templateUrl,
      type: certificate.type as TemplateType
    });
    setSelectedTemplateType(certificate.type as TemplateType);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (certificate: Certificate) => {
    setSelectedCertificate(certificate);
    setIsDeleteDialogOpen(true);
  };

  const openPreviewDialog = (certificate: Certificate) => {
    setSelectedCertificate(certificate);
    setIsPreviewDialogOpen(true);
  };

  const openIssueDialog = (certificate: Certificate) => {
    setSelectedCertificate(certificate);
    setSelectedStudent('');
    // تأكد من تحميل بيانات الطلاب قبل فتح النافذة
    fetchTopStudents().then(() => {
      setIsIssueDialogOpen(true);
    });
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      templateUrl: '',
      type: 'ACHIEVEMENT' as TemplateType
    });
    setSelectedTemplateType('ACHIEVEMENT');
  };

  const getCertificateTypeLabel = (type: string) => {
    switch (type) {
      case 'ACHIEVEMENT':
        return 'إنجاز';
      case 'EXCELLENCE':
        return 'تفوق';
      case 'APPRECIATION':
        return 'تقدير';
      case 'GRADUATION':
        return 'تخرج';
      case 'CUSTOM':
        return 'مخصص';
      default:
        return type;
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.honor-board.certificates.view">
      <div className="container mx-auto p-4">
      {/* إضافة مكون معالجة الجداول المتجاوبة */}
      <ResponsiveTablesHandler />

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Link href="/admin/honor-board" className="flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)]">
            <ArrowRight className="h-5 w-5 ml-1" />
            <span>العودة إلى لوحة الشرف</span>
          </Link>
          <h1 className="text-2xl font-bold mr-4">إدارة شهادات التقدير</h1>
        </div>
        <Button
          onClick={() => {
            resetForm();
            setIsAddDialogOpen(true);
          }}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
        >
          <Plus className="ml-2" size={16} />
          إضافة شهادة جديدة
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      ) : certificates.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-gray-500">لا توجد شهادات حتى الآن</p>
          <Button
            onClick={() => {
              resetForm();
              setIsAddDialogOpen(true);
            }}
            variant="outline"
            className="mt-4"
          >
            إضافة شهادة جديدة
          </Button>
        </div>
      ) : (
        <div className="responsive-table-container">
          <Table className="card-mode-table">
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">العنوان</TableHead>
                <TableHead className="text-right hide-on-mobile">الوصف</TableHead>
                <TableHead className="text-right">النوع</TableHead>
                <TableHead className="text-right hide-on-mobile">تاريخ الإنشاء</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {certificates.map((certificate) => (
                <TableRow key={certificate.id}>
                  <TableCell className="font-medium" data-label="العنوان">{certificate.title}</TableCell>
                  <TableCell data-label="الوصف" className="hide-on-mobile">{certificate.description}</TableCell>
                  <TableCell data-label="النوع">{getCertificateTypeLabel(certificate.type)}</TableCell>
                  <TableCell data-label="تاريخ الإنشاء" className="hide-on-mobile">{new Date(certificate.createdAt).toLocaleDateString('fr-FR')}</TableCell>
                  <TableCell className="actions" data-label="الإجراءات">
                    <div className="flex flex-wrap gap-2 justify-center sm:justify-start mobile-action-buttons">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openPreviewDialog(certificate)}
                      >
                        <Eye className="h-4 w-4 ml-1 sm:inline hidden" />
                        معاينة
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openIssueDialog(certificate)}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <Award className="h-4 w-4 ml-1 sm:inline hidden" />
                        إصدار
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(certificate)}
                      >
                        <Pencil className="h-4 w-4 ml-1 sm:inline hidden" />
                        تعديل
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => openDeleteDialog(certificate)}
                      >
                        <Trash2 className="h-4 w-4 ml-1 sm:inline hidden" />
                        حذف
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* إضافة شهادة جديدة */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="w-[95vw] max-w-[600px] max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-2">
            <DialogTitle className="text-lg sm:text-xl">إضافة شهادة تقدير جديدة</DialogTitle>
            <DialogDescription className="text-sm">
              أدخل تفاصيل شهادة التقدير الجديدة واختر القالب المناسب.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="title" className="text-right sm:col-span-1">
                  العنوان
                </Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="sm:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="description" className="text-right sm:col-span-1">
                  الوصف
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="sm:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="templateUrl" className="text-right sm:col-span-1">
                  رابط القالب
                </Label>
                <Input
                  id="templateUrl"
                  name="templateUrl"
                  value={formData.templateUrl}
                  onChange={handleInputChange}
                  className="sm:col-span-3"
                  placeholder="https://example.com/certificate-template.jpg"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="type" className="text-right sm:col-span-1">
                  النوع
                </Label>
                <div className="sm:col-span-3">
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الشهادة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACHIEVEMENT">إنجاز</SelectItem>
                      <SelectItem value="EXCELLENCE">تفوق</SelectItem>
                      <SelectItem value="APPRECIATION">تقدير</SelectItem>
                      <SelectItem value="GRADUATION">تخرج</SelectItem>
                      <SelectItem value="CUSTOM">مخصص</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-2 mt-2">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-2">
                <h3 className="text-sm font-medium">معاينة القالب</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs w-full sm:w-auto"
                  onClick={() => {
                    setSelectedCertificate({
                      id: 0,
                      title: formData.title || 'عنوان الشهادة',
                      description: formData.description || 'وصف الشهادة',
                      templateUrl: formData.templateUrl,
                      type: formData.type,
                      createdAt: new Date().toISOString()
                    });
                    setIsPreviewDialogOpen(true);
                  }}
                >
                  <Eye className="h-3 w-3 ml-1" />
                  معاينة كاملة
                </Button>
              </div>
              <div className="h-48 overflow-hidden">
                <div className="transform scale-50 origin-top-center -mt-20">
                  <CertificateTemplates
                    certificateData={{
                      title: formData.title || 'عنوان الشهادة',
                      description: formData.description || 'وصف الشهادة',
                      templateUrl: formData.templateUrl,
                      type: formData.type,
                      student: {
                        id: 0,
                        name: 'اسم الطالب المتميز',
                        classe: {
                          name: 'الصف الدراسي'
                        }
                      }
                    }}
                    selectedTemplate={selectedTemplateType}
                    onSelectTemplate={(template) => {
                      setSelectedTemplateType(template);
                      setFormData(prev => ({
                        ...prev,
                        type: template
                      }));
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="sticky bottom-0 bg-white pt-2 flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsAddDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              إلغاء
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
            >
              حفظ
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* تعديل شهادة */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="w-[95vw] max-w-[600px] max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-2">
            <DialogTitle className="text-lg sm:text-xl">تعديل شهادة التقدير</DialogTitle>
            <DialogDescription className="text-sm">
              قم بتعديل تفاصيل شهادة التقدير واختر القالب المناسب.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="edit-title" className="text-right sm:col-span-1">
                  العنوان
                </Label>
                <Input
                  id="edit-title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="sm:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="edit-description" className="text-right sm:col-span-1">
                  الوصف
                </Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="sm:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="edit-templateUrl" className="text-right sm:col-span-1">
                  رابط القالب
                </Label>
                <Input
                  id="edit-templateUrl"
                  name="templateUrl"
                  value={formData.templateUrl}
                  onChange={handleInputChange}
                  className="sm:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="edit-type" className="text-right sm:col-span-1">
                  النوع
                </Label>
                <div className="sm:col-span-3">
                  <Select
                    value={formData.type}
                    onValueChange={(value) => handleSelectChange('type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع الشهادة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACHIEVEMENT">إنجاز</SelectItem>
                      <SelectItem value="EXCELLENCE">تفوق</SelectItem>
                      <SelectItem value="APPRECIATION">تقدير</SelectItem>
                      <SelectItem value="GRADUATION">تخرج</SelectItem>
                      <SelectItem value="CUSTOM">مخصص</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-2 mt-2">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-2">
                <h3 className="text-sm font-medium">معاينة القالب</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs w-full sm:w-auto"
                  onClick={() => {
                    setSelectedCertificate({
                      id: selectedCertificate?.id || 0,
                      title: formData.title || 'عنوان الشهادة',
                      description: formData.description || 'وصف الشهادة',
                      templateUrl: formData.templateUrl,
                      type: formData.type,
                      createdAt: selectedCertificate?.createdAt || new Date().toISOString()
                    });
                    setIsPreviewDialogOpen(true);
                  }}
                >
                  <Eye className="h-3 w-3 ml-1" />
                  معاينة كاملة
                </Button>
              </div>
              <div className="h-48 overflow-hidden">
                <div className="transform scale-50 origin-top-center -mt-20">
                  <CertificateTemplates
                    certificateData={{
                      id: selectedCertificate?.id,
                      title: formData.title || 'عنوان الشهادة',
                      description: formData.description || 'وصف الشهادة',
                      templateUrl: formData.templateUrl,
                      type: formData.type,
                      student: {
                        id: 0,
                        name: 'اسم الطالب المتميز',
                        classe: {
                          name: 'الصف الدراسي'
                        }
                      }
                    }}
                    selectedTemplate={selectedTemplateType}
                    onSelectTemplate={(template) => {
                      setSelectedTemplateType(template);
                      setFormData(prev => ({
                        ...prev,
                        type: template
                      }));
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="sticky bottom-0 bg-white pt-2 flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              إلغاء
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
            >
              حفظ التغييرات
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حذف شهادة */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="w-[95vw] max-w-[425px] max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-2">
            <DialogTitle className="text-lg sm:text-xl">حذف شهادة التقدير</DialogTitle>
            <DialogDescription className="text-sm">
              هل أنت متأكد من رغبتك في حذف هذه الشهادة؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sticky bottom-0 bg-white pt-2 flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              إلغاء
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              className="w-full sm:w-auto"
            >
              حذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* معاينة الشهادة */}
      <Dialog open={isPreviewDialogOpen} onOpenChange={setIsPreviewDialogOpen}>
        <DialogContent className="w-[95vw] max-w-[800px] max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-2">
            <DialogTitle className="text-lg sm:text-xl">معاينة الشهادة</DialogTitle>
            <DialogDescription className="text-sm">
              {selectedCertificate?.title}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 overflow-x-auto">
            <div className="min-w-[320px]">
              {selectedCertificate && (
                <CertificateTemplates
                  certificateData={{
                    id: selectedCertificate.id,
                    title: selectedCertificate.title,
                    description: selectedCertificate.description,
                    templateUrl: selectedCertificate.templateUrl,
                    type: selectedCertificate.type as TemplateType,
                    student: {
                      id: 0,
                      name: 'اسم الطالب المتميز',
                      classe: {
                        name: 'الصف الدراسي'
                      }
                    },
                    issueDate: new Date().toISOString()
                  }}
                  selectedTemplate={selectedCertificate.type as TemplateType}
                  onSelectTemplate={(template) => {
                    setSelectedTemplateType(template);
                    setFormData(prev => ({
                      ...prev,
                      type: template
                    }));
                  }}
                />
              )}
            </div>
          </div>
          <DialogFooter className="sticky bottom-0 bg-white pt-2 flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsPreviewDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* إصدار شهادة لطالب */}
      <Dialog open={isIssueDialogOpen} onOpenChange={setIsIssueDialogOpen}>
        <DialogContent className="w-[95vw] max-w-[425px] max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="sticky top-0 bg-white z-10 pb-2">
            <DialogTitle className="text-lg sm:text-xl">إصدار شهادة لطالب</DialogTitle>
            <DialogDescription className="text-sm">
              اختر الطالب الذي تريد إصدار الشهادة له.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
              <Label htmlFor="student" className="text-right sm:col-span-1">
                الطالب
              </Label>
              <div className="sm:col-span-3">
                <Select
                  value={selectedStudent}
                  onValueChange={handleStudentSelectChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الطالب" />
                  </SelectTrigger>
                  <SelectContent className="max-h-[40vh] overflow-y-auto">
                    {loadingStudents ? (
                      <div className="flex items-center justify-center p-4">
                        <Loader2 className="h-5 w-5 animate-spin text-[var(--primary-color)] ml-2" />
                        <span>جاري تحميل بيانات الطلاب...</span>
                      </div>
                    ) : students.length === 0 ? (
                      <div className="text-center p-4 text-gray-500">
                        لا توجد بيانات للطلاب
                      </div>
                    ) : (
                      students.map((student) => (
                        <SelectItem key={student.id} value={student.id.toString()}>
                          {student.name} - {student.classe?.name || 'بدون قسم'} ({student.totalPoints} نقطة)
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter className="sticky bottom-0 bg-white pt-2 flex-col sm:flex-row gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsIssueDialogOpen(false)}
              className="w-full sm:w-auto"
            >
              إلغاء
            </Button>
            <Button
              type="button"
              onClick={handleIssueCertificate}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
              disabled={!selectedStudent}
            >
              إصدار الشهادة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </ProtectedRoute>
  );
}

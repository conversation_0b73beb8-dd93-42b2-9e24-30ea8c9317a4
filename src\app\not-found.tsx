import Link from 'next/link';
import SiteLogo from '@/components/SiteLogo';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-white rounded-lg shadow-md p-8">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-6">
            <SiteLogo size="lg" showText={false} />
          </div>
          
          <div className="text-[var(--primary-color)] text-8xl font-bold mb-4">404</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">الصفحة غير موجودة</h1>
          <p className="text-gray-600 mb-6">
            عذراً، لم نتمكن من العثور على الصفحة التي تبحث عنها.
          </p>
          <div className="space-y-3">
            <Link 
              href='/' 
              className="block w-full bg-[var(--primary-color)] text-white px-6 py-2 rounded-lg hover:bg-[var(--secondary-color)] transition-colors"
            >
              العودة إلى الصفحة الرئيسية
            </Link>
            <Link 
              href='/about' 
              className="block w-full bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              من نحن
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

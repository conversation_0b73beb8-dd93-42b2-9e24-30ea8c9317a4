/* تحسينات خاصة بالهاتف لصفحة إدارة المستخدمين */

/* تحسين الأزرار للهاتف */
@media (max-width: 640px) {
  .mobile-button {
    min-height: 44px; /* الحد الأدنى لحجم اللمس */
    font-size: 16px; /* منع التكبير التلقائي في iOS */
  }
  
  .mobile-input {
    font-size: 16px; /* منع التكبير التلقائي في iOS */
    min-height: 44px;
  }
  
  .mobile-select {
    font-size: 16px;
    min-height: 44px;
  }
}

/* تحسين البطاقات للهاتف */
.user-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.user-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين النوافذ المنبثقة للهاتف */
@media (max-width: 640px) {
  .mobile-modal {
    margin: 16px;
    max-height: calc(100vh - 32px);
    width: calc(100vw - 32px);
  }
  
  .mobile-modal-content {
    max-height: calc(100vh - 120px);
    overflow-y: auto;
  }
}

/* تحسين الجدول للأجهزة اللوحية */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-table th,
  .tablet-table td {
    padding: 12px 8px;
    font-size: 14px;
  }
  
  .tablet-table th:nth-child(3),
  .tablet-table td:nth-child(3) {
    display: none; /* إخفاء البريد الإلكتروني في الأجهزة اللوحية */
  }
}

/* تحسين شريط البحث */
.search-input {
  -webkit-appearance: none;
  appearance: none;
}

.search-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* تحسين الصلاحيات للهاتف */
@media (max-width: 640px) {
  .permissions-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .permission-item {
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
  }
  
  .permission-item:hover {
    background: #f3f4f6;
  }
  
  .permission-checkbox {
    width: 18px;
    height: 18px;
  }
}

/* تحسين التمرير */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scroll-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scroll-container::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* تحسين الرسوم المتحركة للهاتف */
@media (prefers-reduced-motion: reduce) {
  .user-card,
  .mobile-button,
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* تحسين التباعد للهاتف */
@media (max-width: 640px) {
  .mobile-spacing {
    padding: 16px;
  }
  
  .mobile-spacing-sm {
    padding: 12px;
  }
  
  .mobile-gap {
    gap: 12px;
  }
  
  .mobile-gap-sm {
    gap: 8px;
  }
}

/* تحسين النصوص للهاتف */
@media (max-width: 640px) {
  .mobile-text-sm {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .mobile-text-xs {
    font-size: 12px;
    line-height: 1.3;
  }
  
  .mobile-title {
    font-size: 20px;
    line-height: 1.3;
  }
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
  .dark-mode-card {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .dark-mode-input {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .dark-mode-input::placeholder {
    color: #9ca3af;
  }
}

/* تحسين إمكانية الوصول */
.focus-visible:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* تحسين الطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-full-width {
    width: 100% !important;
    max-width: none !important;
  }
  
  .user-card {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}

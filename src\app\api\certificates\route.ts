import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';

// GET /api/certificates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const type = searchParams.get('type');

    if (id) {
      // جلب شهادة محددة
      const certificate = await prisma.certificate.findUnique({
        where: { id: parseInt(id) }
      });

      if (!certificate) {
        return NextResponse.json({
          error: 'الشهادة غير موجودة',
          success: false
        }, { status: 404 });
      }

      return NextResponse.json({
        data: certificate,
        success: true,
        message: 'تم جلب الشهادة بنجاح'
      });
    } else {
      // بناء شروط البحث
      const where: Prisma.CertificateWhereInput = {};

      if (type) {
        where.type = type;
      }

      // جلب جميع الشهادات
      const certificates = await prisma.certificate.findMany({
        where,
        orderBy: {
          createdAt: 'desc'
        }
      });

      return NextResponse.json({
        data: certificates,
        success: true,
        message: 'تم جلب الشهادات بنجاح'
      });
    }
  } catch (error) {
    console.error('Error fetching certificates:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب الشهادات',
      success: false
    }, { status: 500 });
  }
}

// POST /api/certificates
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, description, templateUrl, type } = body;

    // التحقق من البيانات المطلوبة
    if (!title) {
      return NextResponse.json({
        error: 'عنوان الشهادة مطلوب',
        success: false
      }, { status: 400 });
    }

    // إنشاء شهادة جديدة
    const certificate = await prisma.certificate.create({
      data: {
        title,
        description: description || '',
        templateUrl: templateUrl || '',
        type: type || 'ACHIEVEMENT'
      }
    });

    return NextResponse.json({
      data: certificate,
      success: true,
      message: 'تم إنشاء الشهادة بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating certificate:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إنشاء الشهادة',
      success: false
    }, { status: 500 });
  }
}

// PATCH /api/certificates?id=123
export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف الشهادة مطلوب',
        success: false
      }, { status: 400 });
    }

    const body = await request.json();
    const { title, description, templateUrl, type } = body;

    // التحقق من وجود الشهادة
    const existingCertificate = await prisma.certificate.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingCertificate) {
      return NextResponse.json({
        error: 'الشهادة غير موجودة',
        success: false
      }, { status: 404 });
    }

    // تحديث الشهادة
    const updatedCertificate = await prisma.certificate.update({
      where: { id: parseInt(id) },
      data: {
        title: title !== undefined ? title : existingCertificate.title,
        description: description !== undefined ? description : existingCertificate.description,
        templateUrl: templateUrl !== undefined ? templateUrl : existingCertificate.templateUrl,
        type: type !== undefined ? type : existingCertificate.type
      }
    });

    return NextResponse.json({
      data: updatedCertificate,
      success: true,
      message: 'تم تحديث الشهادة بنجاح'
    });
  } catch (error) {
    console.error('Error updating certificate:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث الشهادة',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/certificates?id=123
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف الشهادة مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود الشهادة
    const existingCertificate = await prisma.certificate.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingCertificate) {
      return NextResponse.json({
        error: 'الشهادة غير موجودة',
        success: false
      }, { status: 404 });
    }

    // حذف الشهادة
    await prisma.certificate.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف الشهادة بنجاح'
    });
  } catch (error) {
    console.error('Error deleting certificate:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف الشهادة',
      success: false
    }, { status: 500 });
  }
}

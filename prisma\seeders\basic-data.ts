import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// بيانات أساسية للمدرسة القرآنية
export async function seedBasicData() {
  console.log('🏫 بدء إنشاء البيانات الأساسية للمدرسة القرآنية...');

  try {
    // إنشاء الفصول الدراسية
    const classes = [
      { name: 'الفصل الأول الابتدائي', description: 'فصل المبتدئين في حفظ القرآن' },
      { name: 'الفصل الثاني الابتدائي', description: 'فصل متوسط في حفظ القرآن' },
      { name: 'الفصل الثالث الابتدائي', description: 'فصل متقدم في حفظ القرآن' },
      { name: 'فصل الحفاظ', description: 'فصل حفاظ القرآن الكريم' },
      { name: 'فصل التجويد المتقدم', description: 'فصل متخصص في علم التجويد' }
    ];

    console.log('📚 إنشاء الفصول الدراسية...');
    const createdClasses = [];
    for (const classe of classes) {
      // البحث عن الفصل أولاً
      let existingClass = await prisma.classe.findFirst({
        where: { name: classe.name }
      });

      if (!existingClass) {
        existingClass = await prisma.classe.create({
          data: classe
        });
      }
      createdClasses.push(existingClass);
    }

    // إنشاء أولياء الأمور
    console.log('👨‍👩‍👧‍👦 إنشاء أولياء الأمور...');
    const guardians = [
      { name: 'أحمد محمد علي', phone: '0123456789', email: '<EMAIL>' },
      { name: 'فاطمة حسن محمود', phone: '0123456790', email: '<EMAIL>' },
      { name: 'محمد عبدالله أحمد', phone: '0123456791', email: '<EMAIL>' },
      { name: 'عائشة سالم محمد', phone: '0123456792', email: '<EMAIL>' },
      { name: 'عبدالرحمن يوسف علي', phone: '0123456793', email: '<EMAIL>' },
      { name: 'خديجة أحمد حسن', phone: '0123456794', email: '<EMAIL>' },
      { name: 'عمر محمد سالم', phone: '0123456795', email: '<EMAIL>' },
      { name: 'زينب عبدالله محمد', phone: '0123456796', email: '<EMAIL>' }
    ];

    const createdGuardians = [];
    for (const guardian of guardians) {
      // البحث عن ولي الأمر أولاً
      let existingGuardian = await prisma.parent.findFirst({
        where: { phone: guardian.phone }
      });

      if (!existingGuardian) {
        existingGuardian = await prisma.parent.create({
          data: guardian
        });
      }
      createdGuardians.push(existingGuardian);
    }

    // إنشاء الطلاب
    console.log('👦👧 إنشاء الطلاب...');
    const studentNames = [
      // أسماء الذكور
      'محمد أحمد علي',
      'عبدالله محمد حسن',
      'أحمد عبدالرحمن محمود',
      'يوسف سالم أحمد',
      'عمر محمد عبدالله',
      'حسن أحمد محمد',
      'علي عبدالله سالم',
      'إبراهيم محمد أحمد',
      'عبدالرحمن علي حسن',
      'سالم محمد عبدالله',
      // أسماء الإناث
      'فاطمة أحمد محمد',
      'عائشة محمد علي',
      'خديجة عبدالله أحمد',
      'زينب محمد حسن',
      'مريم علي محمد',
      'سارة أحمد عبدالله',
      'نور محمد سالم',
      'هدى عبدالله علي',
      'أمينة محمد أحمد',
      'ليلى علي حسن'
    ];

    const createdStudents = [];
    for (let i = 0; i < studentNames.length; i++) {
      const name = studentNames[i];
      const age = 8 + Math.floor(Math.random() * 8); // أعمار من 8 إلى 15
      const classeId = createdClasses[i % createdClasses.length].id;
      const guardianId = createdGuardians[i % createdGuardians.length].id;
      
      const student = await prisma.student.upsert({
        where: { username: `student_${i + 1}` },
        update: {},
        create: {
          name: name,
          username: `student_${i + 1}`,
          age: age,
          phone: `012345${String(i + 1).padStart(4, '0')}`,
          classeId: classeId,
          guardianId: guardianId
        }
      });
      createdStudents.push(student);
    }

    console.log('✅ تم تخطي إنشاء المعلمين والمعايير (سيتم إنشاؤها لاحقاً)');

    console.log('✅ تم إنشاء البيانات الأساسية بنجاح!');
    console.log(`   📚 الفصول: ${createdClasses.length}`);
    console.log(`   👥 الطلاب: ${createdStudents.length}`);
    console.log(`   👨‍👩‍👧‍👦 أولياء الأمور: ${createdGuardians.length}`);

    return {
      classes: createdClasses,
      students: createdStudents,
      guardians: createdGuardians,
      teachers: []
    };

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات الأساسية:', error);
    throw error;
  }
}

export default seedBasicData;

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/curriculum/units - إنشاء وحدة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, description, order, subjectId } = body;

    if (!title || !subjectId) {
      return NextResponse.json(
        { message: "يجب توفير عنوان الوحدة ومعرف المادة" },
        { status: 400 }
      );
    }

    // التحقق من وجود المادة
    const subject = await prisma.subject.findUnique({
      where: { id: subjectId },
    });

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    // تحديد الترتيب إذا لم يتم توفيره
    let unitOrder = order;
    if (!unitOrder) {
      const lastUnit = await prisma.curriculumUnit.findFirst({
        where: { subjectId },
        orderBy: { order: 'desc' },
      });
      unitOrder = lastUnit ? lastUnit.order + 1 : 1;
    }

    // إنشاء الوحدة
    const unit = await prisma.curriculumUnit.create({
      data: {
        title,
        description,
        order: unitOrder,
        subjectId,
      },
    });

    // تحديث حالة المادة إذا كانت هذه أول وحدة
    if (!subject.hasStudyPlan) {
      await prisma.subject.update({
        where: { id: subjectId },
        data: { hasStudyPlan: true },
      });
    }

    return NextResponse.json(unit);
  } catch (error) {
    console.error('Error creating unit:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء الوحدة" },
      { status: 500 }
    );
  }
}

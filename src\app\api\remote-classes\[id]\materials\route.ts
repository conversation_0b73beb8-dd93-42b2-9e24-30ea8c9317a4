import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";

// GET: جلب المواد التعليمية لفصل افتراضي محدد
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const remoteClassId = parseInt(params.id);
        if (isNaN(remoteClassId)) {
            return NextResponse.json(
                { message: "معرف الفصل غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // جلب الفصل الافتراضي للتحقق من الصلاحيات
        const remoteClass = await prisma.remoteClass.findUnique({
            where: { id: remoteClassId },
            include: {
                attendees: {
                    select: { id: true }
                },
                classe: {
                    include: {
                        students: true
                    }
                }
            }
        });

        if (!remoteClass) {
            return NextResponse.json(
                { message: "الفصل الافتراضي غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من الصلاحيات (المسؤول يرى كل شيء)
        if (userData.role !== 'ADMIN') {
            // المعلم يرى المواد التعليمية للفصول التي يدرسها
            if (userData.role === 'TEACHER' && remoteClass.instructorId !== userData.id) {
                return NextResponse.json(
                    { message: "غير مصرح به" },
                    { status: 403 }
                );
            }

            // الطالب يرى المواد التعليمية للفصول التي ينتمي إليها
            if (userData.role === 'STUDENT') {
                // التحقق من قائمة الحضور
                const isAttendee = remoteClass.attendees.some((a: { id: number }) => a.id === userData.id);

                // التحقق من طلاب الفصل
                let isClassStudent = false;
                if (remoteClass.classe) {
                    // جلب الطالب المرتبط بالمستخدم الحالي
                    const student = await prisma.student.findFirst({
                        where: {
                            username: userData.username,
                            classeId: remoteClass.classe.id
                        }
                    });

                    isClassStudent = !!student;
                }

                // السماح بالوصول إذا كان الطالب في قائمة الحضور أو في الفصل
                if (!isAttendee && !isClassStudent) {
                    return NextResponse.json(
                        { message: "غير مصرح به" },
                        { status: 403 }
                    );
                }
            }
        }

        // جلب المواد التعليمية
        const materials = await prisma.material.findMany({
            where: {
                remoteClassId
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        return NextResponse.json(materials);
    } catch (error) {
        console.error('Error fetching materials:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب المواد التعليمية" },
            { status: 500 }
        );
    }
}

// POST: إضافة مادة تعليمية جديدة
export async function POST(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const remoteClassId = parseInt(params.id);
        if (isNaN(remoteClassId)) {
            return NextResponse.json(
                { message: "معرف الفصل غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
                { status: 401 }
            );
        }

        // جلب الفصل الافتراضي للتحقق من الصلاحيات
        const remoteClass = await prisma.remoteClass.findUnique({
            where: { id: remoteClassId }
        });

        if (!remoteClass) {
            return NextResponse.json(
                { message: "الفصل الافتراضي غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من أن المستخدم هو المعلم المسؤول عن الفصل أو مسؤول
        if (userData.role === 'TEACHER' && remoteClass.instructorId !== userData.id) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون المعلم المسؤول عن هذا الفصل" },
                { status: 403 }
            );
        }

        const body = await request.json();

        // التحقق من البيانات المطلوبة
        if (!body.title || !body.fileUrl || !body.fileType) {
            return NextResponse.json(
                { message: "البيانات غير مكتملة" },
                { status: 400 }
            );
        }

        // إنشاء المادة التعليمية
        const material = await prisma.material.create({
            data: {
                title: body.title,
                description: body.description || '',
                fileUrl: body.fileUrl,
                fileType: body.fileType,
                remoteClassId
            }
        });

        // إنشاء إشعارات للمعلم بإضافة مادة تعليمية جديدة
        if (userData.id) {
            await prisma.notification.create({
                data: {
                    title: `تم إضافة مادة تعليمية جديدة: ${body.title}`,
                    content: `تمت إضافة مادة تعليمية جديدة "${body.title}" للفصل "${remoteClass.title}"`,
                    type: 'REMOTE_CLASS',
                    userId: userData.id,
                    relatedId: remoteClassId,
                    link: `/remote-classes/${remoteClassId}`
                }
            });
        }

        return NextResponse.json(material, { status: 201 });
    } catch (error) {
        console.error('Error creating material:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء إضافة المادة التعليمية" },
            { status: 500 }
        );
    }
}

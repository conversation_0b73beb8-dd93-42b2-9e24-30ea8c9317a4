'use client';

import React, { useState } from 'react';
import { FaImage, FaCheck, FaTimes, FaSpinner } from 'react-icons/fa';
import Image from 'next/image';

interface ImageTesterProps {
  className?: string;
}

const ImageTester: React.FC<ImageTesterProps> = ({ className = '' }) => {
  const [testUrl, setTestUrl] = useState('');
  const [testResult, setTestResult] = useState<'loading' | 'success' | 'error' | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  const testImage = async () => {
    if (!testUrl) return;

    setTestResult('loading');
    setErrorMessage('');

    try {
      // اختبار تحميل الصورة
      const img = new window.Image();
      
      img.onload = () => {
        setTestResult('success');
      };
      
      img.onerror = () => {
        setTestResult('error');
        setErrorMessage('فشل في تحميل الصورة');
      };
      
      img.src = testUrl;
    } catch (error) {
      setTestResult('error');
      setErrorMessage('خطأ في اختبار الصورة');
    }
  };

  const testPaths = [
    '/uploads/header-icons/81a8d050-808f-4c39-bd99-dc45560ba81b_Duplicate_Layer1_1.png',
    '/api/uploads/header-icons/81a8d050-808f-4c39-bd99-dc45560ba81b_Duplicate_Layer1_1.png',
    '/uploads/header-icons/53079ef2-0b93-4801-a395-49d5cadd81fd_Duplicate_Layer2_1.png',
    '/api/uploads/header-icons/53079ef2-0b93-4801-a395-49d5cadd81fd_Duplicate_Layer2_1.png'
  ];

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <FaImage className="text-[var(--primary-color)] text-xl" />
        <h2 className="text-xl font-bold text-gray-800">اختبار تحميل الصور</h2>
      </div>

      {/* اختبار مسار مخصص */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          اختبار مسار صورة
        </label>
        <div className="flex gap-2">
          <input
            type="text"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
            placeholder="أدخل مسار الصورة للاختبار"
          />
          <button
            onClick={testImage}
            disabled={!testUrl || testResult === 'loading'}
            className="px-4 py-2 bg-[var(--primary-color)] text-white rounded-lg hover:bg-[var(--secondary-color)] transition-colors disabled:opacity-50"
          >
            {testResult === 'loading' ? <FaSpinner className="animate-spin" /> : 'اختبار'}
          </button>
        </div>
        
        {/* نتيجة الاختبار */}
        {testResult && (
          <div className="mt-3 p-3 rounded-lg flex items-center gap-2">
            {testResult === 'loading' && (
              <div className="flex items-center gap-2 text-blue-600">
                <FaSpinner className="animate-spin" />
                <span>جاري الاختبار...</span>
              </div>
            )}
            {testResult === 'success' && (
              <div className="flex items-center gap-2 text-green-600">
                <FaCheck />
                <span>تم تحميل الصورة بنجاح</span>
              </div>
            )}
            {testResult === 'error' && (
              <div className="flex items-center gap-2 text-red-600">
                <FaTimes />
                <span>{errorMessage}</span>
              </div>
            )}
          </div>
        )}

        {/* معاينة الصورة */}
        {testResult === 'success' && testUrl && (
          <div className="mt-3">
            <div className="relative w-32 h-32 mx-auto border border-gray-300 rounded-lg overflow-hidden">
              <Image
                src={testUrl}
                alt="معاينة الصورة"
                fill
                className="object-contain"
                sizes="128px"
              />
            </div>
          </div>
        )}
      </div>

      {/* اختبار المسارات المحددة مسبقاً */}
      <div>
        <h3 className="text-lg font-medium text-gray-800 mb-4">اختبار المسارات الموجودة</h3>
        <div className="space-y-3">
          {testPaths.map((path, index) => (
            <TestPathItem key={index} path={path} />
          ))}
        </div>
      </div>
    </div>
  );
};

interface TestPathItemProps {
  path: string;
}

const TestPathItem: React.FC<TestPathItemProps> = ({ path }) => {
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');

  const testPath = () => {
    setStatus('loading');
    
    const img = new window.Image();
    
    img.onload = () => {
      setStatus('success');
    };
    
    img.onerror = () => {
      setStatus('error');
    };
    
    img.src = path;
  };

  React.useEffect(() => {
    testPath();
  }, [path]);

  return (
    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
      <div className="flex-1">
        <code className="text-sm text-gray-600 break-all">{path}</code>
      </div>
      <div className="flex items-center gap-2 ml-4">
        {status === 'loading' && (
          <FaSpinner className="animate-spin text-blue-500" />
        )}
        {status === 'success' && (
          <>
            <FaCheck className="text-green-500" />
            <div className="relative w-8 h-8 border border-gray-300 rounded overflow-hidden">
              <Image
                src={path}
                alt="معاينة"
                fill
                className="object-contain"
                sizes="32px"
              />
            </div>
          </>
        )}
        {status === 'error' && (
          <FaTimes className="text-red-500" />
        )}
        <button
          onClick={testPath}
          className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
        >
          إعادة اختبار
        </button>
      </div>
    </div>
  );
};

export default ImageTester;

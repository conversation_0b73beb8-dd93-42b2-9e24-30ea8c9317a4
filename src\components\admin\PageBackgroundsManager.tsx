'use client';

import React, { useState, useEffect } from 'react';
import { FaImage, FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaUpload, FaSave, FaTimes } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import axios from 'axios';
import Image from 'next/image';
import { clearBackgroundsCache } from '@/hooks/usePageBackground';

interface PageBackground {
  id: number;
  pageName: string;
  displayName: string;
  imageUrl?: string;
  overlayColor?: string;
  overlayOpacity: number;
  position: string;
  size: string;
  repeat: string;
  attachment: string;
  isActive: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
}

interface PageBackgroundsManagerProps {
  className?: string;
}

const PageBackgroundsManager: React.FC<PageBackgroundsManagerProps> = ({ className = '' }) => {
  const [backgrounds, setBackgrounds] = useState<PageBackground[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingBackground, setEditingBackground] = useState<PageBackground | null>(null);
  const [uploading, setUploading] = useState(false);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    pageName: '',
    displayName: '',
    imageUrl: '',
    overlayColor: '',
    overlayOpacity: 0.5,
    position: 'center',
    size: 'cover',
    repeat: 'no-repeat',
    attachment: 'scroll',
    isActive: true,
    priority: 0
  });

  // الصفحات المتاحة
  const availablePages = [
    { value: 'home', label: 'الصفحة الرئيسية' },
    { value: 'about', label: 'من نحن' },
    { value: 'contact', label: 'اتصل بنا' },
    { value: 'programs', label: 'البرامج' },
    { value: 'khatm-sessions', label: 'مجالس الختم' },
    { value: 'donations', label: 'التبرعات' },
    { value: 'login', label: 'تسجيل الدخول' },
    { value: 'register', label: 'التسجيل' }
  ];

  // خيارات الموضع
  const positionOptions = [
    { value: 'center', label: 'الوسط' },
    { value: 'top', label: 'الأعلى' },
    { value: 'bottom', label: 'الأسفل' },
    { value: 'left', label: 'اليسار' },
    { value: 'right', label: 'اليمين' },
    { value: 'top left', label: 'أعلى اليسار' },
    { value: 'top right', label: 'أعلى اليمين' },
    { value: 'bottom left', label: 'أسفل اليسار' },
    { value: 'bottom right', label: 'أسفل اليمين' }
  ];

  // خيارات الحجم
  const sizeOptions = [
    { value: 'cover', label: 'تغطية كاملة' },
    { value: 'contain', label: 'احتواء' },
    { value: 'auto', label: 'تلقائي' },
    { value: '100% 100%', label: 'تمدد كامل' }
  ];

  // خيارات التكرار
  const repeatOptions = [
    { value: 'no-repeat', label: 'بدون تكرار' },
    { value: 'repeat', label: 'تكرار' },
    { value: 'repeat-x', label: 'تكرار أفقي' },
    { value: 'repeat-y', label: 'تكرار عمودي' }
  ];

  // خيارات الثبات
  const attachmentOptions = [
    { value: 'scroll', label: 'متحرك' },
    { value: 'fixed', label: 'ثابت' }
  ];

  // جلب الخلفيات
  const fetchBackgrounds = async () => {
    try {
      const response = await axios.get('/api/page-backgrounds');
      if (response.data.success) {
        setBackgrounds(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching backgrounds:', error);
      toast.error('فشل في جلب خلفيات الصفحات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBackgrounds();
  }, []);

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      pageName: '',
      displayName: '',
      imageUrl: '',
      overlayColor: '',
      overlayOpacity: 0.5,
      position: 'center',
      size: 'cover',
      repeat: 'no-repeat',
      attachment: 'scroll',
      isActive: true,
      priority: 0
    });
    setEditingBackground(null);
  };

  // فتح نموذج الإضافة
  const handleAdd = () => {
    resetForm();
    setShowModal(true);
  };

  // فتح نموذج التعديل
  const handleEdit = (background: PageBackground) => {
    setFormData({
      pageName: background.pageName,
      displayName: background.displayName,
      imageUrl: background.imageUrl || '',
      overlayColor: background.overlayColor || '',
      overlayOpacity: background.overlayOpacity,
      position: background.position,
      size: background.size,
      repeat: background.repeat,
      attachment: background.attachment,
      isActive: background.isActive,
      priority: background.priority
    });
    setEditingBackground(background);
    setShowModal(true);
  };

  // رفع صورة
  const handleImageUpload = async (file: File) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'backgrounds');

      const response = await axios.post('/api/upload', formData);
      if (response.data.success) {
        setFormData(prev => ({
          ...prev,
          imageUrl: response.data.data.filePath
        }));
        toast.success('تم رفع الصورة بنجاح');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('فشل في رفع الصورة');
    } finally {
      setUploading(false);
    }
  };

  // حفظ الخلفية
  const handleSave = async () => {
    try {
      if (!formData.pageName || !formData.displayName) {
        toast.error('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      const url = editingBackground ? '/api/page-backgrounds' : '/api/page-backgrounds';
      const method = editingBackground ? 'PUT' : 'POST';
      const data = editingBackground ? { ...formData, id: editingBackground.id } : formData;

      const response = await axios({
        method,
        url,
        data
      });

      if (response.data.success) {
        toast.success(editingBackground ? 'تم تحديث الخلفية بنجاح' : 'تم إضافة الخلفية بنجاح');
        setShowModal(false);
        resetForm();
        // مسح الكاش لضمان تحديث الصفحات
        clearBackgroundsCache();
        fetchBackgrounds();
      }
    } catch (error: any) {
      console.error('Error saving background:', error);
      toast.error(error.response?.data?.error || 'حدث خطأ أثناء حفظ الخلفية');
    }
  };

  // حذف خلفية
  const handleDelete = async (id: number) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الخلفية؟')) return;

    try {
      const response = await axios.delete(`/api/page-backgrounds?id=${id}`);
      if (response.data.success) {
        toast.success('تم حذف الخلفية بنجاح');
        // مسح الكاش لضمان تحديث الصفحات
        clearBackgroundsCache();
        fetchBackgrounds();
      }
    } catch (error) {
      console.error('Error deleting background:', error);
      toast.error('فشل في حذف الخلفية');
    }
  };

  // تبديل حالة النشاط
  const toggleActive = async (background: PageBackground) => {
    try {
      const response = await axios.put('/api/page-backgrounds', {
        id: background.id,
        isActive: !background.isActive
      });

      if (response.data.success) {
        toast.success(`تم ${!background.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الخلفية`);
        // مسح الكاش لضمان تحديث الصفحات
        clearBackgroundsCache();
        fetchBackgrounds();
      }
    } catch (error) {
      console.error('Error toggling background status:', error);
      toast.error('فشل في تغيير حالة الخلفية');
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          <span className="mr-2">جاري تحميل خلفيات الصفحات...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FaImage className="text-[var(--primary-color)] text-xl" />
          <h2 className="text-xl font-bold text-gray-800">خلفيات الصفحات العامة</h2>
        </div>
        <button
          onClick={handleAdd}
          className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-lg hover:bg-[var(--secondary-color)] transition-colors flex items-center gap-2"
        >
          <FaPlus className="text-sm" />
          إضافة خلفية جديدة
        </button>
      </div>

      {/* قائمة الخلفيات */}
      <div className="space-y-4">
        {backgrounds.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FaImage className="mx-auto text-4xl mb-4 text-gray-300" />
            <p>لا توجد خلفيات محفوظة</p>
          </div>
        ) : (
          backgrounds.map((background) => (
            <div
              key={background.id}
              className="border border-gray-200 rounded-lg overflow-hidden relative"
              style={{
                backgroundImage: background.imageUrl ? `url(${background.imageUrl})` : undefined,
                backgroundPosition: background.position,
                backgroundSize: background.size,
                backgroundRepeat: background.repeat,
                backgroundAttachment: 'scroll'
              }}
            >
              {/* الطبقة العلوية */}
              {background.imageUrl && background.overlayColor && (
                <div
                  className="absolute inset-0"
                  style={{
                    backgroundColor: background.overlayColor.startsWith('#')
                      ? `${background.overlayColor}${Math.round(background.overlayOpacity * 255).toString(16).padStart(2, '0')}`
                      : background.overlayColor
                  }}
                />
              )}

              {/* المحتوى */}
              <div className="relative z-10 p-4 bg-white/90 backdrop-blur-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    {/* معاينة الصورة المصغرة */}
                    {background.imageUrl && (
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100 border-2 border-white shadow-md">
                        <Image
                          src={background.imageUrl}
                          alt={background.displayName}
                          fill
                          className="object-cover"
                          sizes="64px"
                        />
                      </div>
                    )}

                    <div>
                      <h3 className="font-semibold text-gray-800">{background.displayName}</h3>
                      <p className="text-sm text-gray-600">الصفحة: {background.pageName}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-xs text-gray-500 bg-white/70 px-2 py-1 rounded">الموضع: {background.position}</span>
                        <span className="text-xs text-gray-500 bg-white/70 px-2 py-1 rounded">الحجم: {background.size}</span>
                        <span className="text-xs text-gray-500 bg-white/70 px-2 py-1 rounded">الأولوية: {background.priority}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {/* حالة النشاط */}
                    <button
                      onClick={() => toggleActive(background)}
                      className={`p-3 rounded-lg transition-all shadow-md border-2 ${
                        background.isActive
                          ? 'bg-green-500 text-white border-green-600 hover:bg-green-600'
                          : 'bg-gray-400 text-white border-gray-500 hover:bg-gray-500'
                      }`}
                      title={background.isActive ? 'نشط' : 'غير نشط'}
                    >
                      {background.isActive ? <FaEye /> : <FaEyeSlash />}
                    </button>

                    {/* تعديل */}
                    <button
                      onClick={() => handleEdit(background)}
                      className="p-3 bg-blue-500 text-white border-2 border-blue-600 hover:bg-blue-600 rounded-lg transition-all shadow-md"
                      title="تعديل"
                    >
                      <FaEdit />
                    </button>

                    {/* حذف */}
                    <button
                      onClick={() => handleDelete(background.id)}
                      className="p-3 bg-red-500 text-white border-2 border-red-600 hover:bg-red-600 rounded-lg transition-all shadow-md"
                      title="حذف"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* نموذج الإضافة/التعديل */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800">
                  {editingBackground ? 'تعديل خلفية الصفحة' : 'إضافة خلفية جديدة'}
                </h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="space-y-4">
                {/* اسم الصفحة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الصفحة *
                  </label>
                  <select
                    value={formData.pageName}
                    onChange={(e) => {
                      const selectedPage = availablePages.find(p => p.value === e.target.value);
                      setFormData(prev => ({
                        ...prev,
                        pageName: e.target.value,
                        displayName: selectedPage?.label || ''
                      }));
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                    required
                  >
                    <option value="">اختر الصفحة</option>
                    {availablePages.map(page => (
                      <option key={page.value} value={page.value}>
                        {page.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* الاسم المعروض */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الاسم المعروض *
                  </label>
                  <input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                    placeholder="اسم الصفحة المعروض"
                    required
                  />
                </div>

                {/* رفع الصورة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    صورة الخلفية
                  </label>
                  <div className="flex items-center gap-4">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleImageUpload(file);
                      }}
                      className="hidden"
                      id="background-upload"
                    />
                    <label
                      htmlFor="background-upload"
                      className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors cursor-pointer flex items-center gap-2"
                    >
                      <FaUpload />
                      {uploading ? 'جاري الرفع...' : 'رفع صورة'}
                    </label>
                    {formData.imageUrl && (
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
                        <Image
                          src={formData.imageUrl}
                          alt="معاينة"
                          fill
                          className="object-cover"
                          sizes="64px"
                        />
                      </div>
                    )}
                  </div>
                </div>

                {/* لون الطبقة العلوية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    لون الطبقة العلوية
                  </label>
                  <input
                    type="color"
                    value={formData.overlayColor}
                    onChange={(e) => setFormData(prev => ({ ...prev, overlayColor: e.target.value }))}
                    className="w-full h-10 border border-gray-300 rounded-lg"
                  />
                </div>

                {/* شفافية الطبقة العلوية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    شفافية الطبقة العلوية: {formData.overlayOpacity}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={formData.overlayOpacity}
                    onChange={(e) => setFormData(prev => ({ ...prev, overlayOpacity: parseFloat(e.target.value) }))}
                    className="w-full"
                  />
                </div>

                {/* الموضع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    موضع الصورة
                  </label>
                  <select
                    value={formData.position}
                    onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  >
                    {positionOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* الحجم */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    حجم الصورة
                  </label>
                  <select
                    value={formData.size}
                    onChange={(e) => setFormData(prev => ({ ...prev, size: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  >
                    {sizeOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* التكرار */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    تكرار الصورة
                  </label>
                  <select
                    value={formData.repeat}
                    onChange={(e) => setFormData(prev => ({ ...prev, repeat: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  >
                    {repeatOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* الثبات */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ثبات الصورة
                  </label>
                  <select
                    value={formData.attachment}
                    onChange={(e) => setFormData(prev => ({ ...prev, attachment: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  >
                    {attachmentOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* الأولوية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الأولوية
                  </label>
                  <input
                    type="number"
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) || 0 }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                    placeholder="0"
                  />
                </div>

                {/* معاينة الخلفية */}
                {formData.imageUrl && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      معاينة الخلفية
                    </label>
                    <div
                      className="w-full h-32 rounded-lg border border-gray-300 relative overflow-hidden"
                      style={{
                        backgroundImage: `url(${formData.imageUrl})`,
                        backgroundPosition: formData.position,
                        backgroundSize: formData.size,
                        backgroundRepeat: formData.repeat,
                        backgroundAttachment: 'scroll'
                      }}
                    >
                      {/* الطبقة العلوية */}
                      {formData.overlayColor && (
                        <div
                          className="absolute inset-0"
                          style={{
                            backgroundColor: formData.overlayColor.startsWith('#')
                              ? `${formData.overlayColor}${Math.round(formData.overlayOpacity * 255).toString(16).padStart(2, '0')}`
                              : formData.overlayColor
                          }}
                        />
                      )}

                      {/* نص تجريبي */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-white text-center relative z-10">
                          <h3 className="text-lg font-bold mb-2">{formData.displayName}</h3>
                          <p className="text-sm">معاينة الخلفية</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* حالة النشاط */}
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="w-4 h-4 text-[var(--primary-color)] border-gray-300 rounded focus:ring-[var(--primary-color)]"
                  />
                  <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
                    نشط
                  </label>
                </div>
              </div>

              {/* أزرار الحفظ والإلغاء */}
              <div className="flex gap-3 pt-6 mt-6 border-t">
                <button
                  onClick={handleSave}
                  disabled={uploading}
                  className="flex-1 bg-[var(--primary-color)] text-white py-2 px-4 rounded-lg hover:bg-[var(--secondary-color)] transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                >
                  <FaSave />
                  {editingBackground ? 'تحديث' : 'حفظ'}
                </button>
                <button
                  onClick={() => setShowModal(false)}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PageBackgroundsManager;

'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'react-hot-toast';
import { Search, Filter, Loader2, Plus, Check } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface Question {
  id: number;
  text: string;
  type: string;
  difficultyLevel: string;
  points: number;
  bankId: number;
  bank: {
    id: number;
    name: string;
  };
  options: QuestionOption[];
  answers: QuestionAnswer[];
  _count: {
    examQuestions: number;
  };
}

interface QuestionOption {
  id: number;
  text: string;
  isCorrect: boolean;
  order: number;
}

interface QuestionAnswer {
  id: number;
  text: string;
  isCorrect: boolean;
  explanation: string | null;
}

interface QuestionBank {
  id: number;
  name: string;
}

interface QuestionSelectorProps {
  examId: number;
  onQuestionsAdded: () => void;
}

export function QuestionSelector({ examId, onQuestionsAdded }: QuestionSelectorProps) {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [questionBanks, setQuestionBanks] = useState<QuestionBank[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBank, setFilterBank] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterDifficulty, setFilterDifficulty] = useState('');
  const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [existingQuestionIds, setExistingQuestionIds] = useState<number[]>([]);

  const fetchQuestionBanks = async () => {
    try {
      const response = await fetch('/api/question-banks');
      const result = await response.json();

      if (result.success) {
        setQuestionBanks(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب بنوك الأسئلة');
      }
    } catch (error) {
      console.error('Error fetching question banks:', error);
      toast.error('حدث خطأ أثناء جلب بنوك الأسئلة');
    }
  };

  const fetchQuestions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/questions');
      const result = await response.json();

      if (result.success) {
        setQuestions(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب الأسئلة');
        setQuestions([]);
      }
    } catch (error) {
      console.error('Error fetching questions:', error);
      toast.error('حدث خطأ أثناء جلب الأسئلة');
      setQuestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchExistingQuestions = useCallback(async () => {
    try {
      const response = await fetch(`/api/exam-questions?examId=${examId}`);
      const result = await response.json();

      if (result.success) {
        const existingIds = result.data.map((eq: { questionId: number }) => eq.questionId);
        setExistingQuestionIds(existingIds);
      }
    } catch (error) {
      console.error('Error fetching existing exam questions:', error);
    }
  }, [examId]);

  useEffect(() => {
    fetchQuestionBanks();
    fetchQuestions();
    fetchExistingQuestions();
  }, [examId, fetchExistingQuestions]);

  const handleAddQuestions = async () => {
    if (selectedQuestions.length === 0) {
      toast.error('يرجى اختيار سؤال واحد على الأقل');
      return;
    }

    setIsSubmitting(true);

    try {
      // إضافة الأسئلة واحدًا تلو الآخر
      for (let i = 0; i < selectedQuestions.length; i++) {
        const questionId = selectedQuestions[i];
        const question = questions.find(q => q.id === questionId);

        if (!question) continue;

        const response = await fetch('/api/exam-questions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            examId,
            questionId,
            order: i,
            points: question.points
          }),
        });

        const result = await response.json();

        if (!result.success) {
          console.error(`Error adding question ${questionId}:`, result.error);
        }
      }

      toast.success('تمت إضافة الأسئلة بنجاح');
      onQuestionsAdded();
    } catch (error) {
      console.error('Error adding questions:', error);
      toast.error('حدث خطأ أثناء إضافة الأسئلة');
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleQuestionSelection = (questionId: number) => {
    setSelectedQuestions(prev => {
      if (prev.includes(questionId)) {
        return prev.filter(id => id !== questionId);
      } else {
        return [...prev, questionId];
      }
    });
  };

  const getQuestionTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      MULTIPLE_CHOICE: 'اختيار من متعدد',
      TRUE_FALSE: 'صح أو خطأ',
      SHORT_ANSWER: 'إجابة قصيرة',
      ESSAY: 'مقال',
      MATCHING: 'مطابقة',
      FILL_BLANK: 'ملء الفراغات',
      ORDERING: 'ترتيب'
    };
    return types[type] || type;
  };

  const getDifficultyLevelLabel = (level: string) => {
    const levels: Record<string, string> = {
      EASY: 'سهل',
      MEDIUM: 'متوسط',
      HARD: 'صعب',
      VERY_HARD: 'صعب جداً'
    };
    return levels[level] || level;
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      MULTIPLE_CHOICE: 'bg-purple-100 text-purple-800',
      TRUE_FALSE: 'bg-indigo-100 text-indigo-800',
      SHORT_ANSWER: 'bg-blue-100 text-blue-800',
      ESSAY: 'bg-teal-100 text-teal-800',
      MATCHING: 'bg-yellow-100 text-yellow-800',
      FILL_BLANK: 'bg-orange-100 text-orange-800',
      ORDERING: 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getDifficultyColor = (level: string) => {
    const colors: Record<string, string> = {
      EASY: 'bg-green-100 text-green-800',
      MEDIUM: 'bg-blue-100 text-blue-800',
      HARD: 'bg-orange-100 text-orange-800',
      VERY_HARD: 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const filteredQuestions = questions.filter(question => {
    const matchesSearch =
      question.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      question.bank.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesBank = !filterBank || question.bankId === parseInt(filterBank);
    const matchesType = !filterType || question.type === filterType;
    const matchesDifficulty = !filterDifficulty || question.difficultyLevel === filterDifficulty;

    return matchesSearch && matchesBank && matchesType && matchesDifficulty;
  });

  return (
    <div className="space-y-6">
      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative col-span-1 md:col-span-2">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <Input
            placeholder="البحث في الأسئلة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-12 text-right"
            dir="rtl"
          />
        </div>

        <div className="col-span-1">
          <Select value={filterBank} onValueChange={setFilterBank}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع بنوك الأسئلة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع بنوك الأسئلة</SelectItem>
              {questionBanks.map((bank) => (
                <SelectItem key={bank.id} value={bank.id.toString()}>
                  {bank.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1">
          <div className="flex space-x-2">
            <div className="flex-1 ml-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="نوع السؤال" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_types">جميع الأنواع</SelectItem>
                  <SelectItem value="MULTIPLE_CHOICE">اختيار من متعدد</SelectItem>
                  <SelectItem value="TRUE_FALSE">صح أو خطأ</SelectItem>
                  <SelectItem value="SHORT_ANSWER">إجابة قصيرة</SelectItem>
                  <SelectItem value="ESSAY">مقال</SelectItem>
                  <SelectItem value="MATCHING">مطابقة</SelectItem>
                  <SelectItem value="FILL_BLANK">ملء الفراغات</SelectItem>
                  <SelectItem value="ORDERING">ترتيب</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <Select value={filterDifficulty} onValueChange={setFilterDifficulty}>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="مستوى الصعوبة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_levels">جميع المستويات</SelectItem>
                  <SelectItem value="EASY">سهل</SelectItem>
                  <SelectItem value="MEDIUM">متوسط</SelectItem>
                  <SelectItem value="HARD">صعب</SelectItem>
                  <SelectItem value="VERY_HARD">صعب جداً</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-50 p-3 rounded-lg flex justify-between items-center">
        <div>
          <span className="font-semibold">{selectedQuestions.length}</span> سؤال محدد
        </div>
        <Button
          onClick={handleAddQuestions}
          disabled={selectedQuestions.length === 0 || isSubmitting}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="ml-2 h-4 w-4 animate-spin" />
              جاري الإضافة...
            </>
          ) : (
            <>
              <Plus className="ml-2" size={16} />
              إضافة الأسئلة المحددة
            </>
          )}
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
        </div>
      ) : filteredQuestions.length === 0 ? (
        <div className="text-center p-8 bg-white rounded-lg shadow">
          <Filter className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أسئلة</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || filterBank || filterType || filterDifficulty
              ? 'لم يتم العثور على أسئلة تطابق معايير البحث. حاول تغيير المعايير.'
              : 'لم يتم العثور على أي أسئلة. يمكنك إضافة أسئلة جديدة في صفحة بنك الأسئلة.'}
          </p>
        </div>
      ) : (
        <div className="space-y-4 max-h-[60vh] overflow-y-auto p-2">
          {filteredQuestions.map((question) => {
            const isSelected = selectedQuestions.includes(question.id);
            const isExisting = existingQuestionIds.includes(question.id);

            return (
              <div
                key={question.id}
                className={`p-4 border rounded-lg transition-colors ${
                  isSelected
                    ? 'bg-blue-50 border-blue-200'
                    : isExisting
                    ? 'bg-gray-100 border-gray-300'
                    : 'bg-white hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <div className="flex-1">
                        <h3 className="font-semibold text-right">{question.text}</h3>
                      </div>
                      <div className="flex items-center mr-4">
                        {isExisting ? (
                          <Badge className="bg-gray-200 text-gray-800">
                            <Check className="h-3 w-3 ml-1" />
                            مضاف مسبقاً
                          </Badge>
                        ) : (
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => toggleQuestionSelection(question.id)}
                            className="ml-2"
                          />
                        )}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 justify-end mt-3">
                      <Badge className={getTypeColor(question.type)}>
                        {getQuestionTypeLabel(question.type)}
                      </Badge>
                      <Badge className={getDifficultyColor(question.difficultyLevel)}>
                        {getDifficultyLevelLabel(question.difficultyLevel)}
                      </Badge>
                      <Badge className="bg-gray-100 text-gray-800">
                        {question.points} نقطة
                      </Badge>
                      <Badge className="bg-blue-50 text-blue-800">
                        {question.bank.name}
                      </Badge>
                    </div>

                    {question.type === 'MULTIPLE_CHOICE' && question.options.length > 0 && (
                      <div className="mt-3 border-t pt-2">
                        <p className="text-sm text-gray-500 text-right mb-2">الخيارات:</p>
                        <ul className="text-sm space-y-1">
                          {question.options.map((option) => (
                            <li
                              key={option.id}
                              className={`text-right ${
                                option.isCorrect ? 'text-primary-color font-semibold' : ''
                              }`}
                            >
                              {option.isCorrect && '✓ '}
                              {option.text}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

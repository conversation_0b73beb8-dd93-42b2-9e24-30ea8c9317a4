/**
 * نظام إدارة المود الليلي المحلي
 * يدير تخزين واستعادة إعدادات المود الليلي من localStorage فقط
 */

import { SiteColors } from './simpleColorSystem';

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  DARK_MODE: 'darkMode',
  SITE_COLORS: 'siteColors',
  DARK_MODE_COLORS: 'darkModeColors',
  LIGHT_MODE_COLORS: 'lightModeColors'
} as const;

// الألوان الافتراضية للوضع النهاري
export const DEFAULT_LIGHT_COLORS: SiteColors = {
  primaryColor: '#3b82f6',
  secondaryColor: '#6366f1',
  sidebarColor: '#1a202c',
  backgroundColor: '#f3f4f6',
  accentColor: '#10b981',
  textColor: '#1f2937'
};

// الألوان الافتراضية للوضع المظلم
export const DEFAULT_DARK_COLORS: SiteColors = {
  primaryColor: '#22d3ee',
  secondaryColor: '#06b6d4',
  sidebarColor: '#0f172a',
  backgroundColor: '#1e293b',
  accentColor: '#0ea5e9',
  textColor: '#f1f5f9'
};

/**
 * التحقق من توفر localStorage
 */
const isLocalStorageAvailable = (): boolean => {
  try {
    return typeof window !== 'undefined' && window.localStorage !== undefined;
  } catch {
    return false;
  }
};

/**
 * حفظ قيمة في localStorage بأمان
 */
const safeSetItem = (key: string, value: string): boolean => {
  if (!isLocalStorageAvailable()) return false;
  
  try {
    localStorage.setItem(key, value);
    return true;
  } catch (error) {
    console.error(`Error saving to localStorage (${key}):`, error);
    return false;
  }
};

/**
 * جلب قيمة من localStorage بأمان
 */
const safeGetItem = (key: string): string | null => {
  if (!isLocalStorageAvailable()) return null;
  
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.error(`Error reading from localStorage (${key}):`, error);
    return null;
  }
};

/**
 * حفظ حالة المود الليلي
 */
export const saveDarkMode = (isDarkMode: boolean): boolean => {
  return safeSetItem(STORAGE_KEYS.DARK_MODE, JSON.stringify(isDarkMode));
};

/**
 * جلب حالة المود الليلي
 */
export const loadDarkMode = (): boolean => {
  const saved = safeGetItem(STORAGE_KEYS.DARK_MODE);
  if (!saved || saved === 'undefined' || saved === 'null') {
    return false; // الافتراضي هو الوضع النهاري
  }
  
  try {
    return JSON.parse(saved);
  } catch (error) {
    console.error('Error parsing dark mode from localStorage:', error);
    return false;
  }
};

/**
 * حفظ ألوان الوضع النهاري
 */
export const saveLightModeColors = (colors: SiteColors): boolean => {
  return safeSetItem(STORAGE_KEYS.LIGHT_MODE_COLORS, JSON.stringify(colors));
};

/**
 * جلب ألوان الوضع النهاري من قاعدة البيانات
 */
const fetchLightModeColorsFromDB = async (): Promise<SiteColors | null> => {
  try {
    const response = await fetch('/api/site-colors');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.colors) {
        return data.colors;
      }
    }
  } catch (error) {
    console.error('Error fetching light mode colors from database:', error);
  }
  return null;
};

/**
 * جلب ألوان الوضع النهاري
 */
export const loadLightModeColors = async (): Promise<SiteColors> => {
  // أولاً، جرب جلب من localStorage
  const saved = safeGetItem(STORAGE_KEYS.LIGHT_MODE_COLORS);
  if (saved && saved !== 'undefined' && saved !== 'null') {
    try {
      const colors = JSON.parse(saved);
      return { ...DEFAULT_LIGHT_COLORS, ...colors };
    } catch (error) {
      console.error('Error parsing light mode colors from localStorage:', error);
    }
  }

  // إذا لم توجد في localStorage، جرب جلب من قاعدة البيانات
  const dbColors = await fetchLightModeColorsFromDB();
  if (dbColors) {
    // احفظ في localStorage للاستخدام المستقبلي
    saveLightModeColors(dbColors);
    return { ...DEFAULT_LIGHT_COLORS, ...dbColors };
  }

  // إذا فشل كل شيء، استخدم الألوان الافتراضية
  return DEFAULT_LIGHT_COLORS;
};

/**
 * حفظ ألوان الوضع المظلم
 */
export const saveDarkModeColors = (colors: SiteColors): boolean => {
  return safeSetItem(STORAGE_KEYS.DARK_MODE_COLORS, JSON.stringify(colors));
};

/**
 * جلب ألوان الوضع المظلم
 */
export const loadDarkModeColors = (): SiteColors => {
  const saved = safeGetItem(STORAGE_KEYS.DARK_MODE_COLORS);
  if (!saved || saved === 'undefined' || saved === 'null') {
    return DEFAULT_DARK_COLORS;
  }
  
  try {
    const colors = JSON.parse(saved);
    return { ...DEFAULT_DARK_COLORS, ...colors };
  } catch (error) {
    console.error('Error parsing dark mode colors from localStorage:', error);
    return DEFAULT_DARK_COLORS;
  }
};

/**
 * حفظ الألوان الحالية (للتوافق مع النظام الحالي)
 */
export const saveCurrentColors = (colors: SiteColors): boolean => {
  return safeSetItem(STORAGE_KEYS.SITE_COLORS, JSON.stringify(colors));
};

/**
 * جلب الألوان الحالية
 */
export const loadCurrentColors = (): SiteColors | null => {
  const saved = safeGetItem(STORAGE_KEYS.SITE_COLORS);
  if (!saved || saved === 'undefined' || saved === 'null') {
    return null;
  }
  
  try {
    return JSON.parse(saved);
  } catch (error) {
    console.error('Error parsing current colors from localStorage:', error);
    return null;
  }
};

/**
 * جلب الألوان المناسبة حسب الوضع الحالي
 */
export const getCurrentModeColors = async (): Promise<SiteColors> => {
  const isDarkMode = loadDarkMode();
  return isDarkMode ? loadDarkModeColors() : await loadLightModeColors();
};

/**
 * جلب الألوان المناسبة حسب الوضع الحالي (نسخة متزامنة للتوافق مع النظام الحالي)
 */
export const getCurrentModeColorsSync = (): SiteColors => {
  const isDarkMode = loadDarkMode();
  if (isDarkMode) {
    return loadDarkModeColors();
  } else {
    // للوضع النهاري، جرب localStorage أولاً
    const saved = safeGetItem(STORAGE_KEYS.LIGHT_MODE_COLORS);
    if (saved && saved !== 'undefined' && saved !== 'null') {
      try {
        const colors = JSON.parse(saved);
        return { ...DEFAULT_LIGHT_COLORS, ...colors };
      } catch (error) {
        console.error('Error parsing light mode colors from localStorage:', error);
      }
    }
    return DEFAULT_LIGHT_COLORS;
  }
};

/**
 * تبديل المود الليلي وإرجاع الحالة الجديدة
 */
export const toggleDarkMode = (): boolean => {
  const currentMode = loadDarkMode();
  const newMode = !currentMode;
  saveDarkMode(newMode);
  return newMode;
};

/**
 * تطبيق كلاسات المود الليلي على DOM
 */
export const applyDarkModeClasses = (isDarkMode: boolean): void => {
  if (typeof document === 'undefined') return;
  
  if (isDarkMode) {
    document.body.classList.add('dark-mode');
    document.documentElement.classList.add('dark');
  } else {
    document.body.classList.remove('dark-mode');
    document.documentElement.classList.remove('dark');
  }
};

/**
 * تهيئة المود الليلي عند تحميل الصفحة
 */
export const initializeDarkMode = (): void => {
  const isDarkMode = loadDarkMode();
  applyDarkModeClasses(isDarkMode);
};

/**
 * تنظيف بيانات المود الليلي من localStorage
 */
export const clearDarkModeData = (): void => {
  if (!isLocalStorageAvailable()) return;
  
  try {
    localStorage.removeItem(STORAGE_KEYS.DARK_MODE);
    localStorage.removeItem(STORAGE_KEYS.DARK_MODE_COLORS);
    localStorage.removeItem(STORAGE_KEYS.LIGHT_MODE_COLORS);
    localStorage.removeItem(STORAGE_KEYS.SITE_COLORS);
  } catch (error) {
    console.error('Error clearing dark mode data:', error);
  }
};

/**
 * إنشاء event مخصص لتغيير المود الليلي
 */
export const dispatchDarkModeChangeEvent = (isDarkMode: boolean): void => {
  if (typeof window === 'undefined') return;
  
  const event = new CustomEvent('darkModeChanged', {
    detail: { darkMode: isDarkMode }
  });
  window.dispatchEvent(event);
};

/**
 * الاستماع لتغييرات المود الليلي
 */
export const onDarkModeChange = (callback: (isDarkMode: boolean) => void): (() => void) => {
  if (typeof window === 'undefined') return () => {};
  
  const handleChange = (event: CustomEvent) => {
    callback(event.detail.darkMode);
  };
  
  window.addEventListener('darkModeChanged', handleChange as EventListener);
  
  // إرجاع دالة لإلغاء الاستماع
  return () => {
    window.removeEventListener('darkModeChanged', handleChange as EventListener);
  };
};

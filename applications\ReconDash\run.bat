@echo off
echo Starting ReconDash - Reconnaissance Dashboard
echo ============================================
echo.
echo Checking Ring installation...
ring -v
if errorlevel 1 (
    echo Error: Ring programming language not found!
    echo Please install Ring from: https://ring-lang.github.io/
    pause
    exit /b 1
)

echo.
echo Checking required libraries...
echo - Praetorian.ring: Checking...
if not exist "..\..\praetorian.ring" (
    echo Error: Praetorian.ring library not found!
    echo Please ensure you're running from the correct directory.
    pause
    exit /b 1
)

echo - libui.ring: Will be checked at runtime
echo - threads.ring: Will be checked at runtime
echo.

echo Starting ReconDash...
echo.
ring ReconDash.ring

if errorlevel 1 (
    echo.
    echo ReconDash exited with an error.
    echo Please check the error messages above.
    pause
)

echo.
echo ReconDash has been closed.
pause

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, levelId, hasStudyPlan = false } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير اسم المادة بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود مادة بنفس الاسم
    const existingSubject = await prisma.subject.findFirst({
      where: { name },
    });

    if (existingSubject) {
      return NextResponse.json(
        { message: "يوجد مادة بهذا الاسم مسبقاً" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستوى إذا تم تحديده
    if (levelId) {
      const level = await prisma.level.findUnique({
        where: { id: levelId },
      });

      if (!level) {
        return NextResponse.json(
          { message: "المستوى المحدد غير موجود" },
          { status: 400 }
        );
      }
    }

    const subject = await prisma.subject.create({
      data: {
        name,
        description: description || null,
        levelId: levelId || null,
        hasStudyPlan
      },
    });

    return NextResponse.json(subject, { status: 201 });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء المادة" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const levelId = searchParams.get("levelId");
    const numericLevelId = levelId ? parseInt(levelId) : null;

    // إنشاء شروط البحث
    const where: {
      levelId?: number;
    } = {};
    if (numericLevelId && !isNaN(numericLevelId)) {
      where.levelId = numericLevelId;
    }

    const subjects = await prisma.subject.findMany({
      where,
      include: {
        level: true,
        _count: {
          select: {
            teacherSubjects: true,
            units: true
          }
        }
      },
      orderBy: [
        { levelId: 'asc' },
        { name: 'asc' }
      ]
    });

    return NextResponse.json(subjects);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب المواد" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, description, levelId, hasStudyPlan } = body;

    if (!id || !name || typeof id !== 'number' || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير معرف المادة واسمها بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المادة
    const subject = await prisma.subject.findUnique({
      where: { id },
    });

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مادة أخرى بنفس الاسم
    const existingSubject = await prisma.subject.findFirst({
      where: {
        name,
        NOT: { id },
      },
    });

    if (existingSubject) {
      return NextResponse.json(
        { message: "يوجد مادة أخرى بهذا الاسم" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستوى إذا تم تحديده
    if (levelId !== null && levelId !== undefined) {
      if (levelId !== 0) { // إذا كان levelId = 0، فهذا يعني إزالة المستوى
        const level = await prisma.level.findUnique({
          where: { id: levelId },
        });

        if (!level) {
          return NextResponse.json(
            { message: "المستوى المحدد غير موجود" },
            { status: 400 }
          );
        }
      }
    }

    const updatedSubject = await prisma.subject.update({
      where: { id },
      data: {
        name,
        description: description !== undefined ? description : subject.description,
        levelId: levelId === 0 ? null : (levelId !== undefined ? levelId : subject.levelId),
        hasStudyPlan: hasStudyPlan !== undefined ? hasStudyPlan : subject.hasStudyPlan
      },
      include: {
        level: true
      }
    });

    return NextResponse.json(updatedSubject);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث المادة" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const numericId = id ? parseInt(id) : null;

    if (!id || !numericId || isNaN(numericId)) {
      return NextResponse.json(
        { message: "يجب توفير معرف المادة بتنسيق صحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المادة
    const subject = await prisma.subject.findUnique({
      where: { id: numericId },
    });

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    await prisma.subject.delete({
      where: { id: numericId },
    });

    return NextResponse.json(
      { message: "تم حذف المادة بنجاح" },
      { status: 200 }
    );
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف المادة" },
      { status: 500 }
    );
  }
}
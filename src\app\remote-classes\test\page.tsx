'use client';

import React from 'react';
import Link from 'next/link';
import { FaArrowRight, FaVideo, FaChalkboard, FaDesktop, FaTools, FaNetworkWired, FaUsers, FaExclamationTriangle } from 'react-icons/fa';

/**
 * Main test page for remote classes functionality
 * This page serves as a hub for accessing all test pages for remote classes features
 */
const RemoteClassesTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/remote-classes"
            className="inline-flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] mb-4"
          >
            <FaArrowRight className="ml-1" />
            <span>العودة إلى الفصول الافتراضية</span>
          </Link>

          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaTools className="text-[var(--primary-color)]" />
            اختبارات الفصول الافتراضية
          </h1>
          <p className="text-gray-600 mr-4">
            هذه الصفحة مخصصة لاختبار وتجربة ميزات الفصول الافتراضية المختلفة
          </p>
        </div>

        {/* Warning Banner */}
        <div className="bg-yellow-50 border-r-4 border-yellow-400 p-4 rounded-md mb-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="mr-3">
              <p className="text-sm text-yellow-700">
                <span className="font-bold">تنبيه:</span> هذه الصفحة مخصصة للاختبار فقط. قد تكون بعض الميزات غير مستقرة أو قيد التطوير.
              </p>
            </div>
          </div>
        </div>

        {/* Test Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Screen Share Tests */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="bg-[var(--primary-color)] text-white p-4">
              <h2 className="text-xl font-bold flex items-center gap-2">
                <FaDesktop />
                اختبارات مشاركة الشاشة
              </h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                اختبار وظائف مشاركة الشاشة وتحسين أدائها في الفصول الافتراضية
              </p>
              <Link
                href="/remote-classes/test/screen-share"
                className="block w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white py-2 px-4 rounded-md text-center transition-colors"
              >
                بدء الاختبار
              </Link>
            </div>
          </div>

          {/* Whiteboard Tests */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="bg-[var(--primary-color)] text-white p-4">
              <h2 className="text-xl font-bold flex items-center gap-2">
                <FaChalkboard />
                اختبارات السبورة التفاعلية
              </h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                اختبار وظائف السبورة التفاعلية والرسم التعاوني في الفصول الافتراضية
              </p>
              <Link
                href="/remote-classes/test/whiteboard"
                className="block w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white py-2 px-4 rounded-md text-center transition-colors"
              >
                بدء الاختبار
              </Link>
            </div>
          </div>

          {/* Video/Audio Tests */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="bg-[var(--primary-color)] text-white p-4">
              <h2 className="text-xl font-bold flex items-center gap-2">
                <FaVideo />
                اختبارات الصوت والفيديو
              </h2>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                اختبار تحسينات جودة الصوت والفيديو وضبط الإعدادات في الفصول الافتراضية
              </p>
              <Link
                href="/remote-classes/test/video-audio"
                className="block w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white py-2 px-4 rounded-md text-center transition-colors"
              >
                بدء الاختبار
              </Link>
            </div>
          </div>

          {/* Network Tests - Future */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden opacity-70">
            <div className="bg-gray-500 text-white p-4">
              <h2 className="text-xl font-bold flex items-center gap-2">
                <FaNetworkWired />
                اختبارات الشبكة
              </h2>
              <span className="text-xs bg-yellow-400 text-gray-800 px-2 py-0.5 rounded-full mr-2">
                قريباً
              </span>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                اختبار أداء الشبكة وتحسين استخدام النطاق الترددي في الفصول الافتراضية
              </p>
              <button
                disabled
                className="block w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-md text-center cursor-not-allowed"
              >
                قيد التطوير
              </button>
            </div>
          </div>

          {/* Multi-user Tests - Future */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden opacity-70">
            <div className="bg-gray-500 text-white p-4">
              <h2 className="text-xl font-bold flex items-center gap-2">
                <FaUsers />
                اختبارات متعددة المستخدمين
              </h2>
              <span className="text-xs bg-yellow-400 text-gray-800 px-2 py-0.5 rounded-full mr-2">
                قريباً
              </span>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                اختبار التفاعل بين المستخدمين المتعددين في الفصول الافتراضية
              </p>
              <button
                disabled
                className="block w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-md text-center cursor-not-allowed"
              >
                قيد التطوير
              </button>
            </div>
          </div>
        </div>

        {/* Documentation Section */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4 text-[var(--primary-color)]">وثائق الاختبار</h2>
          <p className="text-gray-600 mb-4">
            تحتوي هذه الصفحة على روابط لاختبار الميزات المختلفة للفصول الافتراضية. يمكنك استخدام هذه الاختبارات للتحقق من عمل الميزات بشكل صحيح قبل استخدامها في الفصول الافتراضية الفعلية.
          </p>

          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-medium text-lg mb-2">كيفية استخدام صفحات الاختبار</h3>
            <ol className="list-decimal list-inside space-y-1 text-gray-700 mr-4">
              <li>اختر الميزة التي ترغب في اختبارها من الخيارات أعلاه</li>
              <li>اتبع التعليمات الموجودة في صفحة الاختبار المحددة</li>
              <li>جرب جميع الوظائف المتاحة للتأكد من عملها بشكل صحيح</li>
              <li>إذا واجهت أي مشاكل، يرجى الإبلاغ عنها للفريق التقني</li>
            </ol>
          </div>

          <div className="mt-4 bg-blue-50 p-4 rounded-md border-r-4 border-blue-400">
            <h3 className="font-medium text-lg mb-2 text-blue-700">ملاحظات فنية</h3>
            <ul className="list-disc list-inside space-y-1 text-gray-700 mr-4">
              <li>تم استخدام تقنية الاستيراد الديناميكي (Dynamic Import) لتحميل مكونات السبورة التفاعلية لتجنب مشاكل التوافق مع العرض على جانب الخادم</li>
              <li>مكتبات مثل Fabric.js تعمل فقط في بيئة المتصفح وتتطلب معالجة خاصة في تطبيقات Next.js</li>
              <li>إذا واجهت مشاكل في تحميل المكونات، تأكد من تحديث الصفحة وانتظار اكتمال التحميل</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RemoteClassesTestPage;

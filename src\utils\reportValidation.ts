// التحقق من صحة بيانات التقارير الموحدة

export interface ValidationError {
  field: string;
  message: string;
  type: 'error' | 'warning';
}

export interface ReportValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

export interface FinancialRow {
  id: string;
  category: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
}

export interface ReportData {
  title: string;
  description?: string;
  periodStart: Date;
  periodEnd: Date;
  literaryContent: string;
  financialData: FinancialRow[];
}

// التحقق من صحة البيانات الأساسية
export function validateBasicInfo(data: Partial<ReportData>): ValidationError[] {
  const errors: ValidationError[] = [];

  // التحقق من العنوان
  if (!data.title || data.title.trim().length === 0) {
    errors.push({
      field: 'title',
      message: 'عنوان التقرير مطلوب',
      type: 'error'
    });
  } else if (data.title.trim().length < 5) {
    errors.push({
      field: 'title',
      message: 'عنوان التقرير قصير جداً (أقل من 5 أحرف)',
      type: 'warning'
    });
  } else if (data.title.trim().length > 200) {
    errors.push({
      field: 'title',
      message: 'عنوان التقرير طويل جداً (أكثر من 200 حرف)',
      type: 'error'
    });
  }

  // التحقق من الوصف
  if (data.description && data.description.length > 1000) {
    errors.push({
      field: 'description',
      message: 'وصف التقرير طويل جداً (أكثر من 1000 حرف)',
      type: 'warning'
    });
  }

  return errors;
}

// التحقق من صحة التواريخ
export function validateDates(periodStart?: Date, periodEnd?: Date): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!periodStart) {
    errors.push({
      field: 'periodStart',
      message: 'تاريخ بداية الفترة مطلوب',
      type: 'error'
    });
    return errors;
  }

  if (!periodEnd) {
    errors.push({
      field: 'periodEnd',
      message: 'تاريخ نهاية الفترة مطلوب',
      type: 'error'
    });
    return errors;
  }

  // التحقق من منطقية التواريخ
  if (periodStart >= periodEnd) {
    errors.push({
      field: 'period',
      message: 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية',
      type: 'error'
    });
  }

  // التحقق من أن التواريخ ليست في المستقبل البعيد
  const now = new Date();
  const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());
  
  if (periodEnd > oneYearFromNow) {
    errors.push({
      field: 'periodEnd',
      message: 'تاريخ النهاية بعيد جداً في المستقبل',
      type: 'warning'
    });
  }

  // التحقق من أن الفترة ليست طويلة جداً
  const diffInDays = Math.abs(periodEnd.getTime() - periodStart.getTime()) / (1000 * 60 * 60 * 24);
  
  if (diffInDays > 365) {
    errors.push({
      field: 'period',
      message: 'الفترة الزمنية طويلة جداً (أكثر من سنة)',
      type: 'warning'
    });
  } else if (diffInDays < 1) {
    errors.push({
      field: 'period',
      message: 'الفترة الزمنية قصيرة جداً (أقل من يوم واحد)',
      type: 'warning'
    });
  }

  return errors;
}

// التحقق من صحة المحتوى الأدبي
export function validateLiteraryContent(content: string): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!content || content.trim().length === 0) {
    errors.push({
      field: 'literaryContent',
      message: 'المحتوى الأدبي فارغ',
      type: 'warning'
    });
    return errors;
  }

  // التحقق من الحد الأدنى للمحتوى
  if (content.trim().length < 100) {
    errors.push({
      field: 'literaryContent',
      message: 'المحتوى الأدبي قصير جداً (أقل من 100 حرف)',
      type: 'warning'
    });
  }

  // التحقق من الحد الأقصى للمحتوى
  if (content.length > 50000) {
    errors.push({
      field: 'literaryContent',
      message: 'المحتوى الأدبي طويل جداً (أكثر من 50,000 حرف)',
      type: 'warning'
    });
  }

  // التحقق من وجود HTML غير صحيح
  const htmlTagPattern = /<[^>]*>/g;
  const htmlTags = content.match(htmlTagPattern);
  
  if (htmlTags) {
    // التحقق من إغلاق العلامات
    const openTags: string[] = [];
    const selfClosingTags = ['br', 'hr', 'img', 'input', 'meta', 'link'];
    
    htmlTags.forEach(tag => {
      const tagName = tag.replace(/<\/?([^\s>]+).*>/, '$1').toLowerCase();
      
      if (tag.startsWith('</')) {
        // علامة إغلاق
        const lastOpenTag = openTags.pop();
        if (lastOpenTag !== tagName) {
          errors.push({
            field: 'literaryContent',
            message: `علامة HTML غير مغلقة بشكل صحيح: ${tag}`,
            type: 'warning'
          });
        }
      } else if (!selfClosingTags.includes(tagName) && !tag.endsWith('/>')) {
        // علامة فتح
        openTags.push(tagName);
      }
    });
    
    if (openTags.length > 0) {
      errors.push({
        field: 'literaryContent',
        message: `علامات HTML غير مغلقة: ${openTags.join(', ')}`,
        type: 'warning'
      });
    }
  }

  return errors;
}

// التحقق من صحة البيانات المالية
export function validateFinancialData(data: FinancialRow[]): ValidationError[] {
  const errors: ValidationError[] = [];

  if (!data || data.length === 0) {
    errors.push({
      field: 'financialData',
      message: 'لا توجد بيانات مالية',
      type: 'warning'
    });
    return errors;
  }

  // التحقق من كل صف
  data.forEach((row, index) => {
    const rowPrefix = `الصف ${index + 1}`;

    // التحقق من الوصف
    if (!row.description || row.description.trim().length === 0) {
      errors.push({
        field: `financialData.${index}.description`,
        message: `${rowPrefix}: الوصف مطلوب`,
        type: 'error'
      });
    } else if (row.description.trim().length < 3) {
      errors.push({
        field: `financialData.${index}.description`,
        message: `${rowPrefix}: الوصف قصير جداً`,
        type: 'warning'
      });
    }

    // التحقق من المبلغ
    if (row.amount < 0) {
      errors.push({
        field: `financialData.${index}.amount`,
        message: `${rowPrefix}: المبلغ لا يمكن أن يكون سالباً`,
        type: 'error'
      });
    } else if (row.amount === 0) {
      errors.push({
        field: `financialData.${index}.amount`,
        message: `${rowPrefix}: المبلغ صفر`,
        type: 'warning'
      });
    } else if (row.amount > 10000000) {
      errors.push({
        field: `financialData.${index}.amount`,
        message: `${rowPrefix}: المبلغ كبير جداً (أكثر من 10 مليون)`,
        type: 'warning'
      });
    }

    // التحقق من النوع
    if (!['income', 'expense'].includes(row.type)) {
      errors.push({
        field: `financialData.${index}.type`,
        message: `${rowPrefix}: نوع البند غير صحيح`,
        type: 'error'
      });
    }
  });

  // التحقق من التوازن المالي
  const totalIncome = data
    .filter(row => row.type === 'income')
    .reduce((sum, row) => sum + row.amount, 0);
    
  const totalExpenses = data
    .filter(row => row.type === 'expense')
    .reduce((sum, row) => sum + row.amount, 0);
    
  const balance = totalIncome - totalExpenses;

  if (balance < 0) {
    errors.push({
      field: 'financialData',
      message: `عجز في الميزانية: ${Math.abs(balance).toLocaleString()} دج`,
      type: 'warning'
    });
  }

  if (totalIncome === 0 && totalExpenses > 0) {
    errors.push({
      field: 'financialData',
      message: 'لا توجد مداخيل مسجلة رغم وجود مصاريف',
      type: 'warning'
    });
  }

  if (totalExpenses === 0 && totalIncome > 0) {
    errors.push({
      field: 'financialData',
      message: 'لا توجد مصاريف مسجلة رغم وجود مداخيل',
      type: 'warning'
    });
  }

  // التحقق من التكرار
  const descriptions = data.map(row => row.description.trim().toLowerCase());
  const duplicates = descriptions.filter((desc, index) => 
    descriptions.indexOf(desc) !== index && desc.length > 0
  );
  
  if (duplicates.length > 0) {
    errors.push({
      field: 'financialData',
      message: `توجد أوصاف مكررة في البيانات المالية`,
      type: 'warning'
    });
  }

  return errors;
}

// التحقق الشامل من التقرير
export function validateReport(data: ReportData): ReportValidationResult {
  const allErrors: ValidationError[] = [];

  // التحقق من البيانات الأساسية
  allErrors.push(...validateBasicInfo(data));

  // التحقق من التواريخ
  allErrors.push(...validateDates(data.periodStart, data.periodEnd));

  // التحقق من المحتوى الأدبي
  allErrors.push(...validateLiteraryContent(data.literaryContent));

  // التحقق من البيانات المالية
  allErrors.push(...validateFinancialData(data.financialData));

  // فصل الأخطاء عن التحذيرات
  const errors = allErrors.filter(error => error.type === 'error');
  const warnings = allErrors.filter(error => error.type === 'warning');

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

// دالة مساعدة لتنسيق رسائل الأخطاء
export function formatValidationMessage(error: ValidationError): string {
  const prefix = error.type === 'error' ? '❌' : '⚠️';
  return `${prefix} ${error.message}`;
}

// دالة للحصول على ملخص التحقق
export function getValidationSummary(result: ReportValidationResult): string {
  if (result.isValid && result.warnings.length === 0) {
    return '✅ التقرير صحيح ولا توجد مشاكل';
  }
  
  const parts: string[] = [];
  
  if (result.errors.length > 0) {
    parts.push(`${result.errors.length} خطأ`);
  }
  
  if (result.warnings.length > 0) {
    parts.push(`${result.warnings.length} تحذير`);
  }
  
  return `${result.isValid ? '⚠️' : '❌'} ${parts.join(' و ')}`;
}

'use client';
import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Permission,
  savePermissionsToCache,
  getPermissionsFromCache,
  isCacheValid,
  clearUserCache,
  getCacheTimeRemaining
} from '@/lib/permissionsCache';

interface UserData {
  id: number;
  username: string;
  role: string;
  roleId?: number;
}

export const useUserPermissions = () => {
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [userId, setUserId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cacheTimeRemaining, setCacheTimeRemaining] = useState<number>(0);
  const [isUsingCache, setIsUsingCache] = useState<boolean>(false);

  // مرجع للتحكم في التحديث الدوري
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const cacheTimerRef = useRef<NodeJS.Timeout | null>(null);

  // دالة للتحقق من وجود صلاحية معينة
  const hasPermission = (permissionKey: string): boolean => {
    if (userRole === 'ADMIN') return true; // المدير لديه جميع الصلاحيات
    return userPermissions.some(permission => permission.key === permissionKey);
  };

  // دالة للتحقق من وجود صلاحية في فئة معينة
  const hasPermissionInCategory = (category: string): boolean => {
    if (userRole === 'ADMIN') return true;
    return userPermissions.some(permission => permission.category === category);
  };

  // دالة لتحديث مؤقت الوقت المتبقي للتخزين المؤقت
  const updateCacheTimer = useCallback((userId: number) => {
    if (cacheTimerRef.current) {
      clearInterval(cacheTimerRef.current);
    }

    const updateTimeRemaining = () => {
      const remaining = getCacheTimeRemaining(userId);
      setCacheTimeRemaining(remaining);

      if (remaining <= 0) {
        setIsUsingCache(false);
        if (cacheTimerRef.current) {
          clearInterval(cacheTimerRef.current);
          cacheTimerRef.current = null;
        }
      }
    };

    updateTimeRemaining();
    cacheTimerRef.current = setInterval(updateTimeRemaining, 60000); // تحديث كل دقيقة
  }, []);

  // دالة لجلب الصلاحيات من الخادم
  const fetchPermissionsFromServer = useCallback(async (userData: UserData) => {
    console.log('جلب الصلاحيات من الخادم للمستخدم:', userData.id, userData.role);

    const permissionsResponse = await fetch('/api/auth/permissions');
    if (!permissionsResponse.ok) {
      throw new Error(`فشل في جلب الصلاحيات: ${permissionsResponse.status}`);
    }

    const permissionsData = await permissionsResponse.json();
    const permissions = permissionsData.permissions || [];

    // حفظ الصلاحيات في التخزين المؤقت
    const saved = savePermissionsToCache(
      userData.id,
      permissions,
      userData.role,
      userData.roleId,
      permissionsData.roleName
    );

    if (saved) {
      setIsUsingCache(true);
      updateCacheTimer(userData.id);
      console.log('تم حفظ الصلاحيات في التخزين المؤقت');
    }

    return permissions;
  }, [updateCacheTimer]);

  // دالة لتحديث الصلاحيات دورياً
  const setupPeriodicRefresh = useCallback((userData: UserData) => {
    if (refreshIntervalRef.current) {
      clearInterval(refreshIntervalRef.current);
    }

    // تحديث كل 30 دقيقة
    refreshIntervalRef.current = setInterval(async () => {
      try {
        console.log('تحديث دوري للصلاحيات');
        const permissions = await fetchPermissionsFromServer(userData);
        setUserPermissions(permissions);
      } catch (error) {
        console.error('خطأ في التحديث الدوري للصلاحيات:', error);
      }
    }, 30 * 60 * 1000); // 30 دقيقة
  }, [fetchPermissionsFromServer]);

  useEffect(() => {
    const fetchUserPermissions = async () => {
      try {
        setLoading(true);
        setError(null);
        setIsUsingCache(false);

        // جلب بيانات المستخدم الحالي
        const userResponse = await fetch('/api/auth/me');

        if (!userResponse.ok) {
          if (userResponse.status === 401) {
            setUserPermissions([]);
            setUserRole(null);
            setUserId(null);
            return;
          }
          throw new Error(`فشل في جلب بيانات المستخدم: ${userResponse.status}`);
        }

        const userData: UserData = await userResponse.json();
        console.log('بيانات المستخدم في hook:', userData);
        setUserRole(userData.role);
        setUserId(userData.id);

        // التحقق من التخزين المؤقت أولاً
        if (isCacheValid(userData.id)) {
          const cachedData = getPermissionsFromCache(userData.id);
          if (cachedData && cachedData.userRole === userData.role) {
            console.log('استخدام الصلاحيات من التخزين المؤقت');
            setUserPermissions(cachedData.permissions);
            setIsUsingCache(true);
            updateCacheTimer(userData.id);
            setupPeriodicRefresh(userData);
            return;
          }
        }

        // إذا لم يكن هناك تخزين مؤقت صالح، جلب من الخادم
        try {
          const permissions = await fetchPermissionsFromServer(userData);
          setUserPermissions(permissions);
          setupPeriodicRefresh(userData);
        } catch (error) {
          console.error('خطأ في جلب الصلاحيات من الخادم:', error);
          setUserPermissions([]);
          setError('فشل في جلب الصلاحيات');
        }

      } catch (err) {
        console.error('خطأ في جلب صلاحيات المستخدم:', err);
        setError(err instanceof Error ? err.message : 'حدث خطأ غير معروف');
        setUserPermissions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUserPermissions();

    // مراقبة أحداث تنظيف التخزين المؤقت
    const handleCacheCleared = (event: CustomEvent) => {
      const { userId: clearedUserId, reason } = event.detail;
      console.log('تم تنظيف التخزين المؤقت:', event.detail);

      if (reason === 'logout' || (clearedUserId && clearedUserId === userId)) {
        // إعادة تعيين الحالة
        setUserPermissions([]);
        setIsUsingCache(false);
        setCacheTimeRemaining(0);

        // إيقاف المؤقتات
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
          refreshIntervalRef.current = null;
        }
        if (cacheTimerRef.current) {
          clearInterval(cacheTimerRef.current);
          cacheTimerRef.current = null;
        }
      }
    };

    window.addEventListener('permissionsCacheCleared', handleCacheCleared as EventListener);

    // تنظيف المؤقتات والمستمعات عند إلغاء تحميل المكون
    return () => {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
        refreshIntervalRef.current = null;
      }
      if (cacheTimerRef.current) {
        clearInterval(cacheTimerRef.current);
        cacheTimerRef.current = null;
      }
      window.removeEventListener('permissionsCacheCleared', handleCacheCleared as EventListener);
    };
  }, [fetchPermissionsFromServer, updateCacheTimer, setupPeriodicRefresh, userId]);

  // دالة لإعادة تحميل الصلاحيات يدوياً
  const refreshPermissions = useCallback(async () => {
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      // مسح التخزين المؤقت الحالي
      clearUserCache(userId);

      // جلب بيانات المستخدم مرة أخرى
      const userResponse = await fetch('/api/auth/me');
      if (userResponse.ok) {
        const userData: UserData = await userResponse.json();
        const permissions = await fetchPermissionsFromServer(userData);
        setUserPermissions(permissions);
      }
    } catch (error) {
      console.error('خطأ في إعادة تحميل الصلاحيات:', error);
      setError('فشل في إعادة تحميل الصلاحيات');
    } finally {
      setLoading(false);
    }
  }, [userId, fetchPermissionsFromServer]);

  return {
    userPermissions,
    userRole,
    userId,
    loading,
    error,
    hasPermission,
    hasPermissionInCategory,
    isAdmin: userRole === 'ADMIN',
    isEmployee: userRole === 'EMPLOYEE',
    isTeacher: userRole === 'TEACHER',
    // معلومات التخزين المؤقت
    isUsingCache,
    cacheTimeRemaining,
    refreshPermissions,
    // دوال مساعدة للتخزين المؤقت
    clearCache: () => userId && clearUserCache(userId),
    getCacheStatus: () => ({
      isValid: userId ? isCacheValid(userId) : false,
      timeRemaining: userId ? getCacheTimeRemaining(userId) : 0
    })
  };
};

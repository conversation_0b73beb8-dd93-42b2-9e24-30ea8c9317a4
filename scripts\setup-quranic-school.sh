#!/bin/bash

# 🕌 سكريبت إعداد المدرسة القرآنية
# هذا السكريبت سيقوم بإعداد النظام بالكامل

echo "🕌 مرحباً بكم في نظام إدارة المدرسة القرآنية"
echo "=" | tr '\n' '=' | head -c 60; echo ""
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "❌ خطأ: Node.js غير مثبت"
    echo "💡 يرجى تثبيت Node.js من: https://nodejs.org"
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    echo "❌ خطأ: npm غير مثبت"
    exit 1
fi

echo "✅ Node.js و npm متوفران"
echo ""

# تثبيت التبعيات
echo "📦 تثبيت التبعيات..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت التبعيات"
    exit 1
fi

echo "✅ تم تثبيت التبعيات بنجاح"
echo ""

# إعداد قاعدة البيانات
echo "🗄️  إعداد قاعدة البيانات..."
npx prisma generate

if [ $? -ne 0 ]; then
    echo "❌ فشل في إنشاء Prisma Client"
    exit 1
fi

npx prisma db push

if [ $? -ne 0 ]; then
    echo "❌ فشل في إعداد قاعدة البيانات"
    exit 1
fi

echo "✅ تم إعداد قاعدة البيانات بنجاح"
echo ""

# تنفيذ البذور
echo "🌱 إنشاء البيانات التجريبية للمدرسة القرآنية..."
npm run seed:quranic

if [ $? -ne 0 ]; then
    echo "❌ فشل في إنشاء البيانات التجريبية"
    exit 1
fi

echo ""
echo "🎉 تم إعداد المدرسة القرآنية بنجاح!"
echo ""
echo "🚀 لبدء تشغيل النظام:"
echo "   npm run dev"
echo ""
echo "🔍 لعرض كشف درجات طالب:"
echo "   http://localhost:3000/admin/evaluation/student-report"
echo ""
echo "📚 لمراجعة الدليل الكامل:"
echo "   docs/QURANIC_SCHOOL_SETUP.md"
echo ""
echo "🤲 بارك الله فيكم ووفقكم في خدمة كتاب الله الكريم"
echo "=" | tr '\n' '=' | head -c 60; echo ""

"use client";
import React, { useState, useEffect } from 'react';
import { FaUserGraduate, FaCalendarAlt, FaBook, FaMoneyBillWave, FaSync, FaUsers } from 'react-icons/fa';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';

interface ParentStats {
  name: string;
  children: Array<{
    id: number;
    name: string;
    grade: string;
    progress: number;
    attendance: number;
    nextExam: {
      title: string;
      date: string;
    } | null;
    lastPayment: {
      amount: number;
      date: string;
      status: string;
    } | null;
  }>;
  notifications: Array<{
    id: number;
    title: string;
    date: string;
    read: boolean;
  }>;
}

const ParentDashboard = () => {
  const [parentData, setParentData] = useState<ParentStats>({
    name: '',
    children: [],
    notifications: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchParentData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/parents/stats');

      if (!response.ok) {
        throw new Error('فشل في جلب بيانات ولي الأمر');
      }

      const data = await response.json();
      setParentData(data);
      toast.success('تم تحديث البيانات بنجاح');
    } catch (error) {
      console.error('Error fetching parent data:', error);
      setError('حدث خطأ أثناء جلب البيانات. يرجى المحاولة مرة أخرى.');
      toast.error('فشل في جلب بيانات ولي الأمر');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchParentData();
  }, []);

  const quickLinks = [
    { title: 'أبنائي', icon: <FaUserGraduate />, link: '/parents/children', color: 'bg-blue-500' },
    { title: 'جدول الحصص', icon: <FaCalendarAlt />, link: '/parents/schedule', color: 'bg-primary-color' },
    { title: 'المنهج الدراسي', icon: <FaBook />, link: '/parents/curriculum', color: 'bg-purple-500' },
    { title: 'المدفوعات', icon: <FaMoneyBillWave />, link: '/parents/payments', color: 'bg-yellow-500' },
  ];

  return (
    <div className="p-4 sm:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-4 sm:space-y-6" dir="rtl">
      {/* Welcome Section */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaUsers className="text-[var(--primary-color)]" />
          لوحة تحكم ولي الأمر
        </h1>
        <Button
          onClick={fetchParentData}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2 mt-2 sm:mt-0"
          disabled={isLoading}
          title="تحديث البيانات"
        >
          <FaSync className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Welcome Card */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        {isLoading ? (
          <div className="flex justify-center items-center h-20">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-4">{error}</div>
        ) : (
          <>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">مرحباً بك، {parentData.name}</h2>
            <p className="text-gray-600">يمكنك متابعة تقدم أبنائك ومعرفة آخر المستجدات</p>
          </>
        )}
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
        {quickLinks.map((link, index) => (
          <Link href={link.link} key={index}>
            <div className={`${link.color} text-white p-3 sm:p-4 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 flex flex-col items-center justify-center text-center h-24 sm:h-28 transform hover:scale-105`}>
              <div className="text-2xl mb-2">{link.icon}</div>
              <span className="font-medium">{link.title}</span>
            </div>
          </Link>
        ))}
      </div>

      {/* Children Overview */}
      <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">نظرة عامة على أبنائك</h3>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-4">{error}</div>
        ) : parentData.children && parentData.children.length > 0 ? (
          <div className="space-y-6">
            {parentData.children.map((child) => (
              <div key={child.id} className="border-r-4 border-[var(--primary-color)] pr-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 mb-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium text-gray-800 text-lg">{child.name}</h4>
                  <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">{child.grade}</span>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 mt-3">
                  <div>
                    <p className="text-sm text-gray-600">تقدم الحفظ</p>
                    <div className="flex items-center mt-1">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                        <div className="bg-[var(--primary-color)] h-2.5 rounded-full" style={{ width: `${child.progress}%` }}></div>
                      </div>
                      <span className="text-sm font-medium text-gray-700">{child.progress}%</span>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600">نسبة الحضور</p>
                    <div className="flex items-center mt-1">
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                        <div className="bg-blue-500 h-2.5 rounded-full" style={{ width: `${child.attendance}%` }}></div>
                      </div>
                      <span className="text-sm font-medium text-gray-700">{child.attendance}%</span>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600">الامتحان القادم</p>
                    {child.nextExam ? (
                      <p className="text-sm font-medium text-gray-700 mt-1">
                        {child.nextExam.title} - {new Date(child.nextExam.date).toLocaleDateString('ar-EG')}
                      </p>
                    ) : (
                      <p className="text-sm font-medium text-gray-700 mt-1">لا يوجد امتحان قادم</p>
                    )}
                  </div>
                </div>

                <div className="mt-3 flex justify-end">
                  <Link href={`/parents/children/${child.id}`} className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] text-sm font-medium">
                    عرض التفاصيل
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-4">لا يوجد أبناء مسجلين بعد</div>
        )}
      </div>

      {/* Notifications & Payments */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
        {/* Notifications */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">الإشعارات</h3>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-4">{error}</div>
          ) : parentData.notifications && parentData.notifications.length > 0 ? (
            <div className="space-y-4">
              {parentData.notifications.map((notification) => (
                <div key={notification.id} className="flex items-start p-2 hover:bg-gray-50 rounded-lg transition-colors duration-200">
                  <div className={`flex-shrink-0 h-3 w-3 rounded-full mt-1.5 ${notification.read ? 'bg-gray-300' : 'bg-red-500'}`}></div>
                  <div className="mr-3 flex-1">
                    <div className="flex justify-between">
                      <p className={`text-sm font-medium ${notification.read ? 'text-gray-600' : 'text-gray-900'}`}>
                        {notification.title}
                      </p>
                      <span className="text-xs text-gray-500">
                        {new Date(notification.date).toLocaleDateString('ar-EG')}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-4">لا توجد إشعارات جديدة</div>
          )}
          <div className="mt-4 text-center">
            <Link href="/notifications" className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] text-sm font-medium">
              عرض جميع الإشعارات
            </Link>
          </div>
        </div>

        {/* Recent Payments */}
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">آخر المدفوعات</h3>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-4">{error}</div>
          ) : parentData.children && parentData.children.length > 0 ? (
            <div className="space-y-4">
              {parentData.children.map((child) => (
                <div key={child.id} className="border p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{child.name}</p>
                      {child.lastPayment ? (
                        <p className="text-xs text-gray-500">
                          {new Date(child.lastPayment.date).toLocaleDateString('ar-EG')}
                        </p>
                      ) : (
                        <p className="text-xs text-gray-500">لا توجد مدفوعات</p>
                      )}
                    </div>
                    {child.lastPayment ? (
                      <div className="text-right">
                        <p className="text-sm font-bold text-gray-900">{child.lastPayment.amount} د.ج</p>
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">
                          {child.lastPayment.status}
                        </span>
                      </div>
                    ) : (
                      <div className="text-right">
                        <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded">
                          لم يتم تسجيل مدفوعات
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-4">لا توجد مدفوعات مسجلة</div>
          )}
          <div className="mt-4 text-center">
            <Link href="/parents/payments" className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] text-sm font-medium">
              عرض سجل المدفوعات
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParentDashboard;
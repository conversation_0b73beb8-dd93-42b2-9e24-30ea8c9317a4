import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const body = await request.json();
    const { day, startTime, endTime, teacherSubjectId } = body;

    // التحقق من وجود تعارض في الجدول
    const schedule = await prisma.classSchedule.findUnique({
      where: { id },
      include: { classe: true }
    });

    if (!schedule) {
      return NextResponse.json(
        { error: 'الجدول غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود تعارض في نفس الفصل
    const existingSchedule = await prisma.classSchedule.findFirst({
      where: {
        id: { not: id },
        classeId: schedule.classeId,
        day,
        OR: [
          {
            startTime: {
              gte: startTime,
              lt: endTime
            }
          },
          {
            endTime: {
              gt: startTime,
              lte: endTime
            }
          },
          {
            startTime: {
              lte: startTime
            },
            endTime: {
              gte: endTime
            }
          }
        ]
      }
    });

    if (existingSchedule) {
      return NextResponse.json(
        { error: 'يوجد تعارض في الجدول، الرجاء اختيار وقت آخر' },
        { status: 400 }
      );
    }

    const updatedSchedule = await prisma.classSchedule.update({
      where: { id },
      data: {
        day,
        startTime,
        endTime,
        teacherSubjectId: teacherSubjectId ? parseInt(teacherSubjectId) : undefined
      },
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    return NextResponse.json({
      message: 'تم تحديث الجدول بنجاح',
      schedule: updatedSchedule
    });
  } catch (error) {
    console.error('Schedule update error:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الجدول', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    await prisma.classSchedule.delete({ where: { id } });
    return NextResponse.json({ message: 'تم حذف الجدول بنجاح' });
  } catch (error) {
    console.error('Schedule delete error:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الجدول', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
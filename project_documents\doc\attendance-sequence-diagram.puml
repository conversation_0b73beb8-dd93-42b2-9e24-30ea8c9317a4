@startuml Attendance Tracking Sequence Diagram

actor Teacher
actor Student
participant "Web Interface" as UI
participant "API" as API
participant "Database" as DB
participant "Notification Service" as Notify

box "Authentication" #LightBlue
  Teacher -> UI: Login
  UI -> API: Send credentials
  API -> DB: Verify credentials
  DB --> API: Return user data
  API --> UI: Authentication response
  UI --> Teacher: Display dashboard
end box

box "Class Selection" #LightYellow
  Teacher -> UI: Navigate to attendance page
  UI -> API: Request class list
  API -> DB: Query classes taught by teacher
  DB --> API: Return classes data
  API --> UI: Display classes
  UI --> Teacher: Show class selection

  Teacher -> UI: Select class
  UI -> API: Request student list for class
  API -> DB: Query students in class
  DB --> API: Return student list
  API --> UI: Display student list
  UI --> Teacher: Show attendance form
end box

box "Attendance Recording" #LightGreen
  Teacher -> UI: Mark attendance for each student
  note right: Can mark as Present, Absent, Excused, or Late

  alt Student Present
    Teacher -> UI: Mark student as present
  else Student Absent
    Teacher -> UI: Mark student as absent
    Teacher -> UI: Add absence reason (optional)
  else Student Late
    Teacher -> UI: Mark student as late
    Teacher -> UI: Record arrival time
  end

  Teacher -> UI: Submit attendance
  UI -> API: Send attendance data
  API -> DB: Save attendance records
  DB --> API: Confirm save

  API -> Notify: Request notifications for absent students
  Notify -> DB: Get parent contact info
  DB --> Notify: Return parent contact info
  Notify --> API: Confirm notification queued

  API --> UI: Display confirmation
  UI --> Teacher: Show success message
end box

box "Reporting" #LightPink
  Teacher -> UI: Generate attendance report
  UI -> API: Request attendance statistics
  API -> DB: Query attendance records
  DB --> API: Return attendance data
  API --> UI: Display attendance statistics
  UI --> Teacher: Show attendance report
end box

@enduml

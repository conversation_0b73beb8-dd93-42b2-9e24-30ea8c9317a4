'use client';

import { useUserPermissions } from '@/hooks/useUserPermissions';
import { ReactNode } from 'react';

interface ProtectedSidebarItemProps {
  requiredPermission: string;
  children: ReactNode;
  fallback?: ReactNode;
}

export default function ProtectedSidebarItem({
  requiredPermission,
  children,
  fallback = null
}: ProtectedSidebarItemProps) {
  const { hasPermission, userRole } = useUserPermissions();

  // المدير لديه جميع الصلاحيات
  if (userRole === 'ADMIN' || hasPermission(requiredPermission)) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}

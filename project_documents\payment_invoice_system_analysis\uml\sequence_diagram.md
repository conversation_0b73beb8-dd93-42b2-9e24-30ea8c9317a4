# 🔄 مخطط التسلسل - نظام المدفوعات والفواتير

## نظرة عامة
يوضح هذا المخطط تسلسل العمليات الرئيسية في نظام المدفوعات والفواتير.

## 1. مخطط تسلسل تسجيل دفعة جديدة

```mermaid
sequenceDiagram
    participant Admin as 👨‍💼 المسؤول
    participant UI as 🖥️ واجهة المستخدم
    participant API as 🔌 API
    participant DB as 🗄️ قاعدة البيانات
    participant System as ⚙️ النظام

    Admin->>UI: فتح نموذج إضافة دفعة
    UI->>Admin: عرض النموذج
    
    Admin->>UI: إدخال بيانات الدفعة
    Note over Admin,UI: (التلميذ، المبلغ، طريقة الدفع)
    
    UI->>API: POST /api/admin/payments
    Note over UI,API: إرسال بيانات الدفعة
    
    API->>DB: التحقق من وجود التلميذ
    DB-->>API: بيانات التلميذ
    
    alt التلميذ موجود
        API->>DB: البحث عن طريقة الدفع
        DB-->>API: طريقة الدفع أو null
        
        alt طريقة الدفع غير موجودة
            API->>DB: إنشاء طريقة دفع جديدة
            DB-->>API: طريقة الدفع الجديدة
        end
        
        API->>DB: إنشاء الدفعة الرئيسية
        DB-->>API: الدفعة المُنشأة
        
        API->>DB: جلب الفواتير المستحقة للتلميذ
        DB-->>API: قائمة الفواتير
        
        loop لكل فاتورة مستحقة
            API->>System: حساب المبلغ القابل للدفع
            System-->>API: المبلغ المحسوب
            
            API->>DB: إنشاء دفعة للفاتورة
            DB-->>API: دفعة الفاتورة
            
            API->>System: حساب حالة الفاتورة الجديدة
            System-->>API: الحالة الجديدة
            
            API->>DB: تحديث حالة الفاتورة
            DB-->>API: تأكيد التحديث
        end
        
        API-->>UI: نجح تسجيل الدفعة
        UI-->>Admin: رسالة نجاح + تفاصيل الدفعة
        
    else التلميذ غير موجود
        API-->>UI: خطأ: التلميذ غير موجود
        UI-->>Admin: رسالة خطأ
    end
```

## 2. مخطط تسلسل إنشاء فاتورة جماعية

```mermaid
sequenceDiagram
    participant Admin as 👨‍💼 المسؤول
    participant UI as 🖥️ واجهة المستخدم
    participant API as 🔌 API الفواتير
    participant DB as 🗄️ قاعدة البيانات
    participant System as ⚙️ النظام

    Admin->>UI: فتح نموذج إنشاء فاتورة جماعية
    UI->>Admin: عرض النموذج
    
    Admin->>UI: اختيار الولي والمبلغ
    UI->>API: POST /api/invoices (نوع جماعي)
    
    API->>DB: التحقق من وجود الولي
    DB-->>API: بيانات الولي
    
    alt الولي موجود
        API->>DB: جلب أبناء الولي
        DB-->>API: قائمة الأبناء
        
        API->>System: حساب المبلغ الإجمالي
        Note over API,System: المبلغ × عدد الأبناء
        System-->>API: المبلغ المحسوب
        
        API->>DB: إنشاء الفاتورة الجماعية
        Note over API,DB: parentId = معرف الولي<br/>type = FAMILY
        DB-->>API: الفاتورة المُنشأة
        
        API->>System: تسجيل نشاط إنشاء الفاتورة
        System-->>API: تأكيد التسجيل
        
        API-->>UI: نجح إنشاء الفاتورة
        UI-->>Admin: رسالة نجاح + تفاصيل الفاتورة
        
    else الولي غير موجود
        API-->>UI: خطأ: الولي غير موجود
        UI-->>Admin: رسالة خطأ
    end
```

## 3. مخطط تسلسل عرض المدفوعات حسب الولي

```mermaid
sequenceDiagram
    participant User as 👤 المستخدم
    participant UI as 🖥️ واجهة المستخدم
    participant API as 🔌 API
    participant DB as 🗄️ قاعدة البيانات
    participant System as ⚙️ النظام

    User->>UI: فتح صفحة المدفوعات حسب الولي
    UI->>API: GET /api/payments/by-parent
    
    API->>DB: التحقق من صلاحيات المستخدم
    DB-->>API: صلاحيات المستخدم
    
    alt المستخدم مخول
        API->>DB: جلب الأولياء مع بياناتهم
        Note over API,DB: تضمين: الأبناء، الفواتير، المدفوعات
        DB-->>API: بيانات الأولياء الخام
        
        loop لكل ولي
            API->>System: حساب إجمالي المطلوب
            Note over API,System: من الفواتير غير الملغاة
            System-->>API: المبلغ المطلوب
            
            API->>System: حساب إجمالي المدفوع
            Note over API,System: من المدفوعات المؤكدة
            System-->>API: المبلغ المدفوع
            
            API->>System: حساب المبلغ المتبقي
            System-->>API: المبلغ المتبقي
            
            API->>System: تحديد حالة الدفع
            Note over API,System: PAID/PARTIAL/UNPAID/OVERDUE
            System-->>API: حالة الدفع
            
            loop لكل تلميذ
                API->>System: حساب ملخص التلميذ
                System-->>API: ملخص التلميذ
            end
        end
        
        API->>System: حساب الإحصائيات العامة
        System-->>API: الإحصائيات
        
        API-->>UI: بيانات الأولياء + الإحصائيات
        UI-->>User: عرض الجدول والإحصائيات
        
    else المستخدم غير مخول
        API-->>UI: خطأ: غير مصرح به
        UI-->>User: رسالة خطأ الصلاحية
    end
```

## 4. مخطط تسلسل طباعة فاتورة

```mermaid
sequenceDiagram
    participant User as 👤 المستخدم
    participant UI as 🖥️ واجهة المستخدم
    participant API as 🔌 API
    participant PDF as 📄 مولد PDF
    participant DB as 🗄️ قاعدة البيانات

    User->>UI: النقر على "طباعة فاتورة"
    UI->>API: GET /api/invoices/pdf/[id]
    
    API->>DB: جلب بيانات الفاتورة
    Note over API,DB: تضمين: التلميذ، الولي، المدفوعات
    DB-->>API: بيانات الفاتورة الكاملة
    
    alt الفاتورة موجودة
        API->>PDF: إنشاء مستند PDF
        Note over API,PDF: تمرير بيانات الفاتورة
        
        PDF->>PDF: تطبيق التصميم العربي
        Note over PDF: خط عربي، اتجاه RTL، تنسيق التاريخ
        
        PDF->>PDF: إضافة شعار المدرسة
        PDF->>PDF: إضافة بيانات الفاتورة
        PDF->>PDF: إضافة جدول المدفوعات
        PDF->>PDF: إضافة الإجماليات
        
        PDF-->>API: ملف PDF جاهز
        API-->>UI: إرسال ملف PDF
        UI-->>User: تحميل/عرض الفاتورة
        
    else الفاتورة غير موجودة
        API-->>UI: خطأ: الفاتورة غير موجودة
        UI-->>User: رسالة خطأ
    end
```

## 5. مخطط تسلسل تحديث حالة الفاتورة تلقائياً

```mermaid
sequenceDiagram
    participant System as ⚙️ النظام
    participant DB as 🗄️ قاعدة البيانات
    participant Calculator as 🧮 حاسبة المبالغ

    Note over System: يتم تشغيل هذا عند إضافة دفعة جديدة
    
    System->>DB: جلب الفاتورة المرتبطة
    DB-->>System: بيانات الفاتورة
    
    System->>DB: جلب جميع مدفوعات الفاتورة
    Note over System,DB: المدفوعات المؤكدة فقط
    DB-->>System: قائمة المدفوعات
    
    System->>Calculator: حساب إجمالي المدفوع
    Calculator-->>System: المبلغ المدفوع
    
    System->>Calculator: مقارنة مع مبلغ الفاتورة
    Calculator-->>System: النسبة المدفوعة
    
    alt مدفوع بالكامل (100%)
        System->>DB: تحديث الحالة إلى PAID
        DB-->>System: تأكيد التحديث
        
    else مدفوع جزئياً (1-99%)
        System->>DB: تحديث الحالة إلى PARTIALLY_PAID
        DB-->>System: تأكيد التحديث
        
    else غير مدفوع (0%) + متأخر
        System->>Calculator: فحص تاريخ الاستحقاق
        Calculator-->>System: متأخر؟
        
        alt متأخر
            System->>DB: تحديث الحالة إلى OVERDUE
            DB-->>System: تأكيد التحديث
        else غير متأخر
            System->>DB: تحديث الحالة إلى UNPAID
            DB-->>System: تأكيد التحديث
        end
    end
    
    System->>System: تسجيل نشاط تحديث الحالة
```

## نقاط مهمة في التسلسل

### 🔒 التحقق من الصلاحيات
- يتم التحقق من صلاحيات المستخدم في بداية كل عملية
- رسائل خطأ واضحة عند عدم وجود صلاحيات

### 💰 حساب المبالغ
- حسابات دقيقة للمبالغ المطلوبة والمدفوعة
- استبعاد الفواتير الملغاة من الحسابات
- اعتبار المدفوعات المؤكدة فقط

### 🔄 تحديث الحالات
- تحديث تلقائي لحالات الفواتير عند إضافة مدفوعات
- منطق واضح لتحديد حالة كل فاتورة

### 📊 معالجة البيانات
- معالجة البيانات على مستوى الخادم لضمان الدقة
- تجميع البيانات بكفاءة لتحسين الأداء

### ⚠️ معالجة الأخطاء
- التحقق من وجود البيانات قبل المعالجة
- رسائل خطأ واضحة ومفيدة للمستخدم
- تسجيل الأخطاء للمراجعة اللاحقة

---

**ملاحظة:** هذه المخططات تمثل التسلسل المثالي للعمليات بعد تطبيق الإصلاحات المطلوبة.

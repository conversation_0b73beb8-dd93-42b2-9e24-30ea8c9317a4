import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/evaluation/results
export async function GET(request: NextRequest) {
  try {
    // الحصول على معلمات URL
    const { searchParams } = new URL(request.url);
    const teacherId = searchParams.get('teacherId');
    const studentId = searchParams.get('studentId');
    const examId = searchParams.get('examId');
    const classId = searchParams.get('classId');

    // بناء شروط البحث
    const whereConditions: {
      examId?: number;
      studentId?: number;
    } = {};

    // إذا تم تحديد معرف الامتحان
    if (examId) {
      whereConditions.examId = parseInt(examId);
    }

    // إذا تم تحديد معرف الطالب
    if (studentId) {
      whereConditions.studentId = parseInt(studentId);
    }

    // جلب نتائج الامتحانات
    const results = await prisma.exam_points.findMany({
      where: whereConditions,
      include: {
        exam: {
          select: {
            id: true,
            description: true,
            evaluationType: true,
            maxPoints: true,
            passingPoints: true,
            month: true,
            examType: true
          }
        },
        student: {
          select: {
            id: true,
            name: true,
            classeId: true,
            classe: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        classSubject: {
          select: {
            id: true,
            classeId: true,
            teacherSubject: {
              select: {
                id: true,
                teacherId: true,
                teacher: {
                  select: {
                    id: true,
                    name: true
                  }
                },
                subject: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        },
        surah: {
          select: {
            id: true,
            name: true,
            number: true
          }
        },
        criteriaScores: {
          include: {
            criteria: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // تصفية النتائج حسب معرف المعلم إذا تم تحديده
    let filteredResults = results;
    if (teacherId) {
      filteredResults = results.filter(result =>
        result.classSubject?.teacherSubject?.teacherId === parseInt(teacherId)
      );
    }

    // تصفية النتائج حسب معرف الفصل إذا تم تحديده
    if (classId) {
      filteredResults = filteredResults.filter(result =>
        result.student?.classeId === parseInt(classId)
      );
    }

    // تنسيق النتائج
    const formattedResults = filteredResults.map(result => {
      // حساب حالة النجاح/الرسوب
      const isPassed = Number(result.grade) >= Number(result.exam.passingPoints);
      const status = isPassed ? 'PASSED' : 'FAILED';

      // إذا كانت الدرجة أعلى من 90% من الدرجة القصوى، فهي ممتازة
      const isExcellent = Number(result.grade) >= (Number(result.exam.maxPoints) * 0.9);
      const finalStatus = isExcellent ? 'EXCELLENT' : status;

      return {
        id: result.id,
        examId: result.examId,
        studentId: result.studentId,
        classSubjectId: result.classSubjectId,
        grade: Number(result.grade),
        status: finalStatus,
        note: result.note,
        feedback: result.feedback,
        surahId: result.surahId,
        startVerse: result.startVerse,
        endVerse: result.endVerse,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,

        // معلومات الامتحان
        exam: {
          id: result.exam.id,
          description: result.exam.description,
          evaluationType: result.exam.evaluationType,
          maxPoints: result.exam.maxPoints,
          passingPoints: result.exam.passingPoints,
          month: result.exam.month,
          examType: result.exam.examType
        },

        // معلومات الطالب
        student: result.student ? {
          id: result.student.id,
          name: result.student.name,
          classeId: result.student.classeId,
          className: result.student.classe?.name || '-'
        } : null,

        // معلومات الفصل والمادة والمعلم
        classSubject: result.classSubject ? {
          id: result.classSubject.id,
          classeId: result.classSubject.classeId,
          teacherId: result.classSubject.teacherSubject?.teacherId,
          teacherName: result.classSubject.teacherSubject?.teacher?.name || '-',
          subjectId: result.classSubject.teacherSubject?.subject?.id,
          subjectName: result.classSubject.teacherSubject?.subject?.name || '-'
        } : null,

        // معلومات السورة (إذا كان امتحان قرآن)
        surah: result.surah ? {
          id: result.surah.id,
          name: result.surah.name,
          number: result.surah.number
        } : null,

        // درجات معايير التقييم
        criteriaScores: result.criteriaScores.map(score => ({
          id: score.id,
          criteriaId: score.criteriaId,
          criteriaName: score.criteria.name,
          score: Number(score.score),
          weight: Number(score.criteria.weight)
        }))
      };
    });

    return NextResponse.json(formattedResults);
  } catch (error) {
    console.error("Error fetching exam results:", error);
    return NextResponse.json(
      { error: "حدث خطأ أثناء جلب نتائج الامتحانات" },
      { status: 500 }
    );
  }
}

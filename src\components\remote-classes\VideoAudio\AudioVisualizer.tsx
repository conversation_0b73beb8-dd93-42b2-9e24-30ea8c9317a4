'use client';

import React, { useRef, useEffect, useState } from 'react';

interface AudioVisualizerProps {
  stream: MediaStream;
  width?: number;
  height?: number;
  barColor?: string;
  backgroundColor?: string;
}

/**
 * Component for visualizing audio levels
 */
const AudioVisualizer: React.FC<AudioVisualizerProps> = ({
  stream,
  width = 300,
  height = 40,
  barColor = 'var(--primary-color)',
  backgroundColor = '#f3f4f6',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  // لا نحتاج لتخزين audioContext في state لأننا نستخدمه فقط في useEffect
  // وسنستخدم متغير محلي بدلاً من ذلك
  const [analyser, setAnalyser] = useState<AnalyserNode | null>(null);
  const [dataArray, setDataArray] = useState<Uint8Array | null>(null);
  const animationRef = useRef<number | null>(null);

  // Initialize audio context and analyzer
  useEffect(() => {
    if (!stream) return;

    // تعريف AudioContext مع التوافق مع المتصفحات القديمة
    // استخدام تعريف واضح للنوع بدلاً من any
    const AudioContextClass = window.AudioContext ||
      (window as { webkitAudioContext?: typeof AudioContext }).webkitAudioContext;

    if (!AudioContextClass) {
      console.error('AudioContext غير مدعوم في هذا المتصفح');
      return;
    }

    const audioCtx = new AudioContextClass();
    const analyserNode = audioCtx.createAnalyser();
    analyserNode.fftSize = 256;
    const bufferLength = analyserNode.frequencyBinCount;
    const dataArr = new Uint8Array(bufferLength);

    const source = audioCtx.createMediaStreamSource(stream);
    source.connect(analyserNode);
    // Don't connect to destination to avoid feedback
    // analyserNode.connect(audioCtx.destination);

    setAnalyser(analyserNode);
    setDataArray(dataArr);

    return () => {
      // إغلاق AudioContext عند تفكيك المكون
      if (audioCtx.state !== 'closed') {
        audioCtx.close();
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [stream]);

  // Draw the visualization
  useEffect(() => {
    if (!analyser || !dataArray || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const canvasCtx = canvas.getContext('2d');
    if (!canvasCtx) return;

    const draw = () => {
      animationRef.current = requestAnimationFrame(draw);

      analyser.getByteFrequencyData(dataArray);

      canvasCtx.fillStyle = backgroundColor;
      canvasCtx.fillRect(0, 0, width, height);

      const barWidth = (width / dataArray.length) * 2.5;
      let barHeight;
      let x = 0;

      for (let i = 0; i < dataArray.length; i++) {
        barHeight = (dataArray[i] / 255) * height;

        canvasCtx.fillStyle = barColor;
        canvasCtx.fillRect(x, height - barHeight, barWidth, barHeight);

        x += barWidth + 1;
      }
    };

    draw();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [analyser, dataArray, width, height, barColor, backgroundColor]);

  return (
    <div className="rounded-md overflow-hidden">
      <canvas ref={canvasRef} width={width} height={height} />
    </div>
  );
};

export default AudioVisualizer;

import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  const currentDate = new Date();

  return [
    {
      url: baseUrl,
      lastModified: currentDate,
    },
    // Auth Routes
    {
      url: `${baseUrl}/login`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/register`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/change-password`,
      lastModified: currentDate,
    },
    // Admin Routes
    {
      url: `${baseUrl}/admin`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/students`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/students/add`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/teachers`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/classes`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/attendance`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/payments`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/donations`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/treasury`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/users`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/settings/evaluation-config`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/admin/student-images`,
      lastModified: currentDate,
    },
    // Teachers Routes
    {
      url: `${baseUrl}/teachers`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/teachers/students`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/teachers/attendance`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/teachers/schedule`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/teachers/curriculum`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/teachers/quran-progress/add`,
      lastModified: currentDate,
    },
    // Parents Routes
    {
      url: `${baseUrl}/parents`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/parents/schedule`,
      lastModified: currentDate,
    },
    // Remote Classes Routes
    {
      url: `${baseUrl}/remote-classes`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/remote-classes/create`,
      lastModified: currentDate,
    },
    // Public Routes
    {
      url: `${baseUrl}/articles`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: currentDate,
    },
    {
      url: `${baseUrl}/donations`,
      lastModified: currentDate,
    },
  ]
}
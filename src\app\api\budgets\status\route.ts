import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// PATCH /api/budgets/status - تغيير حالة الميزانية
export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { budgetId, status, reviewNotes } = body;

    if (!budgetId || !status) {
      return NextResponse.json(
        { error: 'معرف الميزانية والحالة الجديدة مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من صحة الحالة
    const validStatuses = ['DRAFT', 'ACTIVE', 'COMPLETED', 'ARCHIVED'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'حالة الميزانية غير صالحة' },
        { status: 400 }
      );
    }

    // البحث عن الميزانية
    const budget = await prisma.budget.findUnique({
      where: { id: parseInt(budgetId) }
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // تحديث حالة الميزانية وإضافة سجل المراجعة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // تحديث حالة الميزانية
      const updatedBudget = await tx.budget.update({
        where: { id: parseInt(budgetId) },
        data: { status }
      });

      // إضافة سجل المراجعة
      const review = await tx.budgetReview.create({
        data: {
          budgetId: parseInt(budgetId),
          oldStatus: budget.status,
          newStatus: status,
          notes: reviewNotes || null,
          date: new Date()
        }
      });

      return { budget: updatedBudget, review };
    });

    return NextResponse.json({
      success: true,
      message: `تم تغيير حالة الميزانية من ${result.review.oldStatus} إلى ${result.review.newStatus}`,
      budget: result.budget,
      review: result.review
    });
  } catch (error) {
    console.error('خطأ في تغيير حالة الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في تغيير حالة الميزانية' },
      { status: 500 }
    );
  }
}

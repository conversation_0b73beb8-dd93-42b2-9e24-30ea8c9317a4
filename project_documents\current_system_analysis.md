# تحليل النظام الحالي لحملات التبرع

## الملفات والمكونات المتأثرة

### 1. قاعدة البيانات (Prisma Schema)
**الملف:** `prisma/schema.prisma`

#### الجداول المتأثرة:
- **DonationCampaign** (سيتم حذفه بالكامل)
  - id, title, description, targetAmount, currentAmount
  - startDate, endDate, isActive, imageUrl
  - donations (علاقة مع جدول Donation)

- **Donation** (سيتم تعديله)
  - campaignId (سيتم حذف هذا الحقل)
  - campaign (سيتم حذف هذه العلاقة)

### 2. APIs المتأثرة

#### APIs سيتم حذفها:
- `src/app/api/donation-campaigns/route.ts` - إدارة الحملات
- `src/app/api/donation-campaigns/[id]/route.ts` - حملة محددة

#### APIs سيتم تعديلها:
- `src/app/api/donations/route.ts` - إزالة مراجع الحملات
- `src/app/api/donation-reports/route.ts` - إزالة إحصائيات الحملات

### 3. صفحات الواجهة

#### الصفحة الرئيسية المتأثرة:
- `src/app/donations/page.tsx` - صفحة التبرعات العامة
  - إزالة عرض الحملات النشطة
  - إزالة نموذج اختيار الحملة
  - إضافة عرض معلومات التبرع من الإعدادات

#### صفحات الإعدادات:
- `src/app/admin/admin-setup/page.tsx` - إضافة حقول التبرع

### 4. المكونات المتأثرة
- `src/components/footer/footer.tsx` - قد تحتوي على مراجع للحملات

## معلومات التبرع المطلوبة

### الحقول الجديدة في الإعدادات:
```typescript
donationInfo: {
  phone1: string;        // رقم الهاتف الأول
  phone2?: string;       // رقم الهاتف الثاني (اختياري)
  ccpAccount: string;    // حساب CCP
  cpaAccount: string;    // حساب CPA  
  bdrAccount: string;    // حساب BDR
  description?: string;  // وصف إضافي (اختياري)
}
```

## التغييرات المطلوبة في قاعدة البيانات

### إزالة جدول DonationCampaign:
```sql
-- سيتم إنشاء migration لحذف الجدول
DROP TABLE "DonationCampaign";
```

### تعديل جدول Donation:
```sql
-- إزالة العمود campaignId والفهرس المرتبط به
ALTER TABLE "Donation" DROP COLUMN "campaignId";
DROP INDEX IF EXISTS "Donation_campaignId_idx";
```

## الوظائف المتأثرة

### في صفحة التبرعات:
1. **إزالة:**
   - عرض الحملات النشطة
   - إحصائيات الحملات
   - نموذج اختيار الحملة

2. **إضافة:**
   - عرض معلومات التبرع من الإعدادات
   - أرقام الهاتف للتواصل
   - معلومات الحسابات البنكية

### في الإعدادات:
1. **إضافة قسم جديد في معلومات الاتصال:**
   - حقول أرقام الهاتف
   - حقول الحسابات البنكية
   - وصف إضافي للتبرعات

## التأثير على النظام

### الإيجابيات:
- تبسيط نظام التبرعات
- تقليل تعقيد قاعدة البيانات
- سهولة الصيانة والإدارة

### المخاطر:
- فقدان البيانات التاريخية للحملات
- تأثير على التقارير الحالية
- حاجة لتحديث المراجع في الكود

## خطة النسخ الاحتياطي
قبل البدء في التنفيذ، يجب:
1. أخذ نسخة احتياطية من قاعدة البيانات
2. توثيق البيانات الحالية للحملات
3. التأكد من عدم وجود حملات نشطة مهمة

## الملفات التي تحتاج مراجعة إضافية
- أي ملفات تستورد أو تستخدم DonationCampaign
- التقارير المالية التي تعتمد على الحملات
- أي مكونات UI تعرض معلومات الحملات

// استيراد المكتبات اللازمة فقط
import { NextResponse } from 'next/server';
import * as jose from 'jose';
import { ActivityLogger, ActivityType } from '@/lib/activity-logger';

export type JWTPayload = {
    id: number;
    username: string;
    role: string;
    roleId?: number;
}

// Generate JWT Token
export async function generateJWT(jwtPayload: JWTPayload): Promise<string> {
    const secret = new TextEncoder().encode(process.env.JWT_SECRET as string);
    const token = await new jose.SignJWT(jwtPayload)
        .setProtectedHeader({ alg: 'HS256' })
        .setExpirationTime('30d')
        .sign(secret);
    return token;
}

// Set Cookie with JWT
export async function setCookie(jwtPayload: JWTPayload): Promise<NextResponse> {
    const token = await generateJWT(jwtPayload);

    // تسجيل نشاط تسجيل الدخول
    try {
        await ActivityLogger.log(
            jwtPayload.id,
            ActivityType.LOGIN,
            `تسجيل دخول المستخدم ${jwtPayload.username}`
        );
    } catch (error) {
        console.error('خطأ في تسجيل نشاط تسجيل الدخول:', error);
        // لا نريد أن يفشل تسجيل الدخول إذا فشل تسجيل النشاط
    }

    const response = NextResponse.json(
        {
            success: true,
            message: "Authentication successful",
            user: {
                id: jwtPayload.id,
                username: jwtPayload.username,
                role: jwtPayload.role
            },
            role: jwtPayload.role
        },
        { status: 200 }
    );

    response.cookies.set("jwtToken", token, {
        httpOnly: true,
        sameSite: 'strict',
        path: '/',
        maxAge: 60 * 60 * 24 * 30, // 30 days
    });

    return response;
}
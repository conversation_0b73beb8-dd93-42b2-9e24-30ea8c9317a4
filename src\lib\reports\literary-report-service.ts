import prisma from '@/lib/prisma';

// تعريف الأنواع المستخدمة في التقرير الأدبي
export interface LiteraryReportData {
  period: {
    startDate: string;
    endDate: string;
    duration: number;
  };
  generalStats: {
    totalStudents: number;
    totalTeachers: number;
    totalClasses: number;
    totalMemorizers: number;
    totalKhatmSessions: number;
    totalActivities: number;
  };
  studentsDetails: {
    total: number;
    byClass: Array<{
      classeId: string;
      className: string;
      count: number;
    }>;
    classes: Array<{
      id: string;
      name: string;
      capacity: number;
      studentsCount: number;
      students: Array<{
        id: string;
        name: string;
      }>;
    }>;
  };
  teachersDetails: {
    total: number;
    bySpecialization: Array<{
      specialization: string;
      count: number;
    }>;
  };
  attendanceDetails: {
    total: number;
    byStatus: Array<{
      status: string;
      count: number;
    }>;
    attendanceRate: number;
    absenteeRate: number;
  };
  quranDetails: {
    totalProgress: number;
    memorizers: number;
    progressRecords: Array<{
      studentName: string;
      surahName: string;
      surahNumber: number;
      memorization: number;
      tajweed: number;
      startDate: Date;
      completionDate: Date | null;
    }>;
    averageMemorization: number;
    averageTajweed: number;
  };
  khatmDetails: {
    total: number;
    sessions: Array<{
      id: string;
      title: string;
      date: Date;
      location: string;
      teacherName: string;
      surahName: string | null;
      attendeesCount: number;
      presentCount: number;
    }>;
  };
  examsDetails: {
    total: number;
    exams: Array<{
      id: string;
      description: string;
      maxPoints: number;
      passingPoints: number;
      studentsCount: number;
      averageGrade: number;
      passedStudents: number;
    }>;
  };
  activitiesDetails: {
    total: number;
    activities: Array<{
      id: string;
      title: string;
      description: string;
      date: Date;
      organizer: string;
    }>;
  };
}

export interface ReportOptions {
  includeGeneralInfo: boolean;
  includeStudentStats: boolean;
  includeQuranProgress: boolean;
  includeActivities: boolean;
  includeTrainingCourses: boolean;
  includeParticipations: boolean;
}

export class LiteraryReportService {
  /**
   * إنشاء التقرير الأدبي للفترة المحددة
   */
  static async generateReport(
    startDate: Date,
    endDate: Date,
    options: ReportOptions = {
      includeGeneralInfo: true,
      includeStudentStats: true,
      includeQuranProgress: true,
      includeActivities: true,
      includeTrainingCourses: true,
      includeParticipations: true,
    }
  ): Promise<LiteraryReportData> {
    try {
      // 1. إحصائيات الطلاب
      const studentsData = await this.getStudentsData(startDate, endDate);

      // 2. إحصائيات المعلمين
      const teachersData = await this.getTeachersData(startDate, endDate);

      // 3. إحصائيات الحضور
      const attendanceData = await this.getAttendanceData(startDate, endDate);

      // 4. إحصائيات تقدم حفظ القرآن
      const quranData = await this.getQuranProgressData(startDate, endDate);

      // 5. إحصائيات مجالس الختم
      const khatmData = await this.getKhatmSessionsData(startDate, endDate);

      // 6. إحصائيات الامتحانات
      const examsData = await this.getExamsData(startDate, endDate);

      // 7. إحصائيات الأنشطة
      const activitiesData = await this.getActivitiesData(startDate, endDate);

      return {
        period: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          duration: Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
        },
        generalStats: {
          totalStudents: studentsData.total,
          totalTeachers: teachersData.total,
          totalClasses: studentsData.classes.length,
          totalMemorizers: quranData.memorizers,
          totalKhatmSessions: khatmData.total,
          totalActivities: activitiesData.total,
        },
        studentsDetails: studentsData,
        teachersDetails: teachersData,
        attendanceDetails: attendanceData,
        quranDetails: quranData,
        khatmDetails: khatmData,
        examsDetails: examsData,
        activitiesDetails: activitiesData,
      };
    } catch (error) {
      console.error('خطأ في إنشاء التقرير الأدبي:', error);
      throw new Error('فشل في إنشاء التقرير الأدبي');
    }
  }

  /**
   * الحصول على بيانات الطلاب
   */
  private static async getStudentsData(startDate: Date, endDate: Date) {
    const totalStudents = await prisma.student.count({
      where: {
        createdAt: {
          lte: endDate,
        },
      },
    });

    const studentsStats = await prisma.student.groupBy({
      by: ['classeId'],
      _count: {
        id: true,
      },
      where: {
        createdAt: {
          lte: endDate,
        },
      },
    });

    const classesStats = await prisma.classe.findMany({
      include: {
        students: {
          select: {
            id: true,
            name: true,
          },
          where: {
            createdAt: {
              lte: endDate,
            },
          },
        },
        _count: {
          select: {
            students: {
              where: {
                createdAt: {
                  lte: endDate,
                },
              },
            },
          },
        },
      },
    });

    return {
      total: totalStudents,
      byClass: studentsStats.map(stat => {
        const classe = classesStats.find(c => c.id === stat.classeId);
        return {
          classeId: stat.classeId,
          className: classe?.name || 'غير محدد',
          count: stat._count.id,
        };
      }),
      classes: classesStats.map(classe => ({
        id: classe.id,
        name: classe.name,
        capacity: classe.capacity,
        studentsCount: classe._count.students,
        students: classe.students,
      })),
    };
  }

  /**
   * الحصول على بيانات المعلمين
   */
  private static async getTeachersData(startDate: Date, endDate: Date) {
    const totalTeachers = await prisma.teacher.count({
      where: {
        createdAt: {
          lte: endDate,
        },
      },
    });

    const teachersWithSpecialization = await prisma.teacher.groupBy({
      by: ['specialization'],
      _count: {
        id: true,
      },
      where: {
        createdAt: {
          lte: endDate,
        },
      },
    });

    return {
      total: totalTeachers,
      bySpecialization: teachersWithSpecialization.map(stat => ({
        specialization: stat.specialization || 'غير محدد',
        count: stat._count.id,
      })),
    };
  }

  /**
   * الحصول على بيانات الحضور
   */
  private static async getAttendanceData(startDate: Date, endDate: Date) {
    const attendanceStats = await prisma.attendance.groupBy({
      by: ['status'],
      _count: {
        id: true,
      },
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    const total = attendanceStats.reduce((sum, stat) => sum + stat._count.id, 0);
    const presentCount = attendanceStats.find(s => s.status === 'PRESENT')?._count.id || 0;
    const absentCount = attendanceStats.find(s => s.status === 'ABSENT')?._count.id || 0;

    return {
      total,
      byStatus: attendanceStats.map(stat => ({
        status: stat.status,
        count: stat._count.id,
      })),
      attendanceRate: total > 0 ? (presentCount / total) * 100 : 0,
      absenteeRate: total > 0 ? (absentCount / total) * 100 : 0,
    };
  }

  /**
   * الحصول على بيانات تقدم حفظ القرآن
   */
  private static async getQuranProgressData(startDate: Date, endDate: Date) {
    const quranProgressStats = await prisma.quranProgress.findMany({
      where: {
        startDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
          },
        },
        surah: {
          select: {
            id: true,
            name: true,
            number: true,
          },
        },
      },
    });

    const memorizers = await prisma.quranProgress.groupBy({
      by: ['studentId'],
      _count: {
        id: true,
      },
      where: {
        completionDate: {
          gte: startDate,
          lte: endDate,
          not: null,
        },
        memorization: {
          gte: 8,
        },
      },
    });

    return {
      totalProgress: quranProgressStats.length,
      memorizers: memorizers.length,
      progressRecords: quranProgressStats.map(progress => ({
        studentName: progress.student.name,
        surahName: progress.surah.name,
        surahNumber: progress.surah.number,
        memorization: progress.memorization,
        tajweed: progress.tajweed,
        startDate: progress.startDate,
        completionDate: progress.completionDate,
      })),
      averageMemorization: quranProgressStats.length > 0
        ? quranProgressStats.reduce((sum, p) => sum + p.memorization, 0) / quranProgressStats.length
        : 0,
      averageTajweed: quranProgressStats.length > 0
        ? quranProgressStats.reduce((sum, p) => sum + p.tajweed, 0) / quranProgressStats.length
        : 0,
    };
  }

  /**
   * الحصول على بيانات مجالس الختم
   */
  private static async getKhatmSessionsData(startDate: Date, endDate: Date) {
    const khatmSessions = await prisma.khatmSession.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
          },
        },
        surah: {
          select: {
            id: true,
            name: true,
          },
        },
        attendances: {
          select: {
            id: true,
            status: true,
            student: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    return {
      total: khatmSessions.length,
      sessions: khatmSessions.map(session => ({
        id: session.id,
        title: session.title,
        date: session.date,
        location: session.location,
        teacherName: session.teacher?.name || 'غير محدد',
        surahName: session.surah?.name || null,
        attendeesCount: session.attendances.length,
        presentCount: session.attendances.filter(a => a.status === 'PRESENT').length,
      })),
    };
  }

  /**
   * الحصول على بيانات الامتحانات
   */
  private static async getExamsData(startDate: Date, endDate: Date) {
    const examsStats = await prisma.exam.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        exam_points: {
          select: {
            id: true,
            grade: true,
            student: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    return {
      total: examsStats.length,
      exams: examsStats.map(exam => ({
        id: exam.id,
        description: exam.description,
        maxPoints: exam.maxPoints,
        passingPoints: exam.passingPoints,
        studentsCount: exam.exam_points.length,
        averageGrade: exam.exam_points.length > 0
          ? exam.exam_points.reduce((sum, ep) => sum + ep.grade, 0) / exam.exam_points.length
          : 0,
        passedStudents: exam.exam_points.filter(ep => ep.grade >= exam.passingPoints).length,
      })),
    };
  }

  /**
   * الحصول على بيانات الأنشطة
   */
  private static async getActivitiesData(startDate: Date, endDate: Date) {
    const activitiesStats = await prisma.activity.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            profile: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    return {
      total: activitiesStats.length,
      activities: activitiesStats.map(activity => ({
        id: activity.id,
        title: activity.type, // استخدام نوع النشاط كعنوان
        description: activity.description,
        date: activity.createdAt,
        organizer: activity.user.profile?.name || activity.user.username,
      })),
    };
  }
}

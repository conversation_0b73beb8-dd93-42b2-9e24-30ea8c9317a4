@startuml Component Diagram - Quran School Management System

skinparam component {
  BackgroundColor LightBlue
  BorderColor Black
  ArrowColor Black
}

skinparam package {
  BackgroundColor White
  BorderColor DarkSlateGray
}

package "Frontend (Next.js)" #LightYellow {
  package "User Interfaces" {
    [Admin Dashboard] as Ad<PERSON><PERSON>
    [Teacher Portal] as TeacherUI
    [Student Portal] as StudentUI
    [Parent Portal] as ParentUI
  }

  package "Core Frontend" {
    [State Management] as State
    [API Client] as Client
    [Authentication] as Auth
    [Form Validation] as Validation
    [Notification System] as Notify
  }

  package "UI Components" {
    [Shared Components] as SharedUI
    [Quran Viewer] as QuranUI
    [Progress Charts] as Charts
    [Schedule Calendar] as Calendar
  }
}

package "Backend (API Routes)" #LightGreen {
  package "User Management" {
    [User API] as UserAPI
    [Authentication Service] as AuthService
    [Profile API] as ProfileAPI
  }

  package "Educational Services" {
    [Student API] as StudentAPI
    [Teacher API] as TeacherAPI
    [Class API] as ClassAPI
    [Attendance API] as AttendanceAPI
    [Quran Progress API] as ProgressAPI
    [Curriculum API] as CurriculumAPI
  }

  package "Operational Services" {
    [Payment API] as PaymentAPI
    [Remote Class API] as RemoteAPI
    [Reporting API] as ReportAPI
    [Notification API] as NotifyAPI
  }
}

package "Database (Prisma/PostgreSQL)" #LightCyan {
  [Prisma ORM] as Prisma
  [PostgreSQL Database] as DB
  [Database Migrations] as Migrations
  [Data Backup Service] as Backup
}

package "External Services" #LightPink {
  [Payment Gateway] as PaymentGateway
  [Cloud File Storage] as Storage
  [Video Conferencing] as Video
  [Email Service] as Email
  [SMS Gateway] as SMS
}

' Frontend Connections
AdminUI ..> SharedUI : uses
TeacherUI ..> SharedUI : uses
StudentUI ..> SharedUI : uses
ParentUI ..> SharedUI : uses

AdminUI ..> State : uses
TeacherUI ..> State : uses
StudentUI ..> State : uses
ParentUI ..> State : uses

TeacherUI ..> QuranUI : uses
StudentUI ..> QuranUI : uses

AdminUI ..> Charts : uses
TeacherUI ..> Charts : uses
StudentUI ..> Charts : uses
ParentUI ..> Charts : uses

TeacherUI ..> Calendar : uses
StudentUI ..> Calendar : uses

State ..> Client : uses
Client ..> Auth : uses
Client ..> Validation : uses

' API Connections
Client ..> UserAPI : calls
Client ..> StudentAPI : calls
Client ..> TeacherAPI : calls
Client ..> ClassAPI : calls
Client ..> AttendanceAPI : calls
Client ..> ProgressAPI : calls
Client ..> PaymentAPI : calls
Client ..> RemoteAPI : calls
Client ..> CurriculumAPI : calls
Client ..> ReportAPI : calls
Client ..> NotifyAPI : calls
Client ..> ProfileAPI : calls

' Backend Service Connections
UserAPI ..> AuthService : uses
ProfileAPI ..> AuthService : uses
NotifyAPI ..> Email : integrates
NotifyAPI ..> SMS : integrates

' Database Connections
UserAPI ..> Prisma : uses
ProfileAPI ..> Prisma : uses
StudentAPI ..> Prisma : uses
TeacherAPI ..> Prisma : uses
ClassAPI ..> Prisma : uses
AttendanceAPI ..> Prisma : uses
ProgressAPI ..> Prisma : uses
PaymentAPI ..> Prisma : uses
RemoteAPI ..> Prisma : uses
CurriculumAPI ..> Prisma : uses
ReportAPI ..> Prisma : uses

' External Service Integrations
PaymentAPI ..> PaymentGateway : integrates
RemoteAPI ..> Video : integrates
UserAPI ..> Storage : uses for profile images
ProgressAPI ..> Storage : uses for recordings
CurriculumAPI ..> Storage : uses for materials

' Database Layer
Prisma ..> DB : connects
Migrations ..> DB : manages schema
Backup ..> DB : creates backups

' Cross-cutting Concerns
Auth ..> AuthService : authenticates
Notify ..> NotifyAPI : sends notifications

@enduml

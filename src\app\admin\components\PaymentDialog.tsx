'use client';

import { useState, useEffect } from 'react';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'react-toastify';

interface Student {
  id: string;
  name: string;
}

interface PaymentDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
}

export default function PaymentDialog({ isOpen, onCloseAction, onSuccessAction }: PaymentDialogProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [formData, setFormData] = useState({
    studentId: '',
    amount: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear()
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // جلب الطلاب عند فتح النافذة المنبثقة
  useEffect(() => {
    if (isOpen) {
      fetchStudents();
    }
  }, [isOpen]);

  const fetchStudents = async () => {
    setLoadingStudents(true);
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('Failed to fetch students');
      const data = await response.json();
      setStudents(data.students || []);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('فشل في جلب بيانات الطلاب');
    } finally {
      setLoadingStudents(false);
    }
  };

  const resetForm = () => {
    setFormData({
      studentId: '',
      amount: '',
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear()
    });
    setSearchQuery('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.studentId || !formData.amount) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) <= 0) {
      toast.error('يرجى إدخال مبلغ صحيح');
      return;
    }

    setIsSubmitting(true);

    try {
      // تحويل معرف الطالب إلى رقم
      const studentIdNumber = parseInt(formData.studentId);

      if (isNaN(studentIdNumber)) {
        throw new Error('معرف الطالب غير صالح');
      }

      console.log('بيانات الدفعة المرسلة:', {
        studentId: studentIdNumber,
        amount: parseFloat(formData.amount),
        month: String(formData.month).padStart(2, '0'),
        year: String(formData.year),
        status: 'PAID'
      });

      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          studentId: studentIdNumber,
          amount: parseFloat(formData.amount),
          month: String(formData.month).padStart(2, '0'),
          year: String(formData.year),
          status: 'PAID'
        })
      });

      // محاولة قراءة الاستجابة كنص أولاً للتشخيص
      const responseText = await response.text();
      console.log('استجابة الخادم:', responseText);

      if (!response.ok) {
        let errorMessage = 'فشل في تسجيل الدفعة';
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          console.error('خطأ في تحليل استجابة الخادم:', e);
        }
        throw new Error(errorMessage);
      }

      toast.success('تم تسجيل الدفعة بنجاح');
      resetForm();
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error saving payment:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الدفعة');
    } finally {
      setIsSubmitting(false);
    }
  };

  // تصفية الطلاب بناءً على البحث
  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const dialogFooter = (
    <>
      <Button type="button" variant="outline" onClick={onCloseAction} disabled={isSubmitting}>
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isSubmitting}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('paymentForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="تسجيل دفعة جديدة"
      variant="primary"
      footer={dialogFooter}
    >
      <form id="paymentForm" onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="studentId" className="text-right col-span-1">
                الطالب <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <div className="relative">
                  <div className="mb-2">
                    <Input
                      placeholder="ابحث عن الطالب..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <Select
                    value={formData.studentId}
                    onValueChange={(value) => {
                      // العثور على الطالب المحدد لعرض اسمه
                      const selectedStudent = students.find(s => s.id === value);
                      setFormData({ ...formData, studentId: value });
                      // تحديث البحث ليعرض اسم الطالب المحدد
                      if (selectedStudent) {
                        setSearchQuery(selectedStudent.name);
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الطالب" />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingStudents ? (
                        <div className="text-center py-2">جاري التحميل...</div>
                      ) : filteredStudents.length > 0 ? (
                        filteredStudents.map((student) => (
                          <SelectItem key={student.id} value={student.id}>
                            {student.name}
                          </SelectItem>
                        ))
                      ) : (
                        <div className="text-center py-2">لا يوجد طلاب</div>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right col-span-1">
                المبلغ <span className="text-red-500">*</span>
              </Label>
              <Input
                id="amount"
                type="number"
                min="0"
                step="0.01"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="col-span-3"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="month" className="text-right col-span-1">
                الشهر
              </Label>
              <Select
                value={formData.month.toString()}
                onValueChange={(value) => setFormData({ ...formData, month: parseInt(value) })}
              >
                <SelectTrigger id="month" className="col-span-3">
                  <SelectValue placeholder="اختر الشهر" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">يناير</SelectItem>
                  <SelectItem value="2">فبراير</SelectItem>
                  <SelectItem value="3">مارس</SelectItem>
                  <SelectItem value="4">أبريل</SelectItem>
                  <SelectItem value="5">مايو</SelectItem>
                  <SelectItem value="6">يونيو</SelectItem>
                  <SelectItem value="7">يوليو</SelectItem>
                  <SelectItem value="8">أغسطس</SelectItem>
                  <SelectItem value="9">سبتمبر</SelectItem>
                  <SelectItem value="10">أكتوبر</SelectItem>
                  <SelectItem value="11">نوفمبر</SelectItem>
                  <SelectItem value="12">ديسمبر</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="year" className="text-right col-span-1">
                السنة
              </Label>
              <Select
                value={formData.year.toString()}
                onValueChange={(value) => setFormData({ ...formData, year: parseInt(value) })}
              >
                <SelectTrigger id="year" className="col-span-3">
                  <SelectValue placeholder="اختر السنة" />
                </SelectTrigger>
                <SelectContent>
                  {[2023, 2024, 2025, 2026, 2027].map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </form>
    </AnimatedDialog>
  );
}

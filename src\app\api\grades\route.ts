import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ExamStatus } from "@prisma/client";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const grades = Array.isArray(body) ? body : [body];

    const results = await Promise.all(
      grades.map(async (grade) => {
        const { studentId, examId, grade: points, note } = grade;

        // Get exam details to determine the status
        const exam = await prisma.exam.findUnique({
          where: { id: examId },
        });

        if (!exam) {
          throw new Error(`Exam with ID ${examId} not found`);
        }

        // Calculate status based on points and exam passing points
        let status: ExamStatus = ExamStatus.PENDING;
        if (points >= exam.maxPoints * 0.9) {
          status = ExamStatus.EXCELLENT;
        } else if (points >= exam.passingPoints) {
          status = ExamStatus.PASSED;
        } else {
          status = ExamStatus.FAILED;
        }

        // Create or update exam points
        return prisma.exam_points.upsert({
          where: {
            studentId_classSubjectId_examId: {
              studentId,
              examId,
              classSubjectId: 1, // Default class subject ID
            },
          },
          update: {
            grade: points,
            status,
            note,
          },
          create: {
            studentId,
            examId,
            classSubjectId: 1, // Default class subject ID
            grade: points,
            status,
            note,
          },
        });
      })
    );

    return NextResponse.json(results);
  } catch (error) {
    console.error("Error saving grades:", error);
    return NextResponse.json(
      { error: "حدث خطأ أثناء حفظ الدرجات" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get("studentId");
    const examId = searchParams.get("examId");

    const where: {
      studentId?: number;
      examId?: number;
    } = {};
    if (studentId) where.studentId = parseInt(studentId);
    if (examId) where.examId = parseInt(examId);

    const grades = await prisma.exam_points.findMany({
      where,
      include: {
        student: true,
        exam: true,
      },
    });

    return NextResponse.json(grades);
  } catch (error) {
    console.error("Error fetching grades:", error);
    return NextResponse.json(
      { error: "حدث خطأ أثناء جلب الدرجات" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/budgets/:id - الحصول على ميزانية محددة مع بنودها
export async function GET(_req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // جلب الميزانية مع بنودها
    const budget = await prisma.budget.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            category: true
          }
        }
      }
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // حساب إجمالي المصروفات الفعلية لهذه الميزانية
    const actualExpenses = await prisma.expense.groupBy({
      by: ['categoryId'],
      where: {
        date: {
          gte: budget.startDate,
          lte: budget.endDate
        },
        categoryId: {
          in: budget.items.map(item => item.categoryId)
        }
      },
      _sum: {
        amount: true
      }
    });

    // إضافة المصروفات الفعلية إلى بنود الميزانية
    const itemsWithExpenses = budget.items.map(item => {
      const expenseData = actualExpenses.find(exp => exp.categoryId === item.categoryId);
      return {
        ...item,
        actualAmount: expenseData?._sum.amount || 0,
        remainingAmount: item.amount - (expenseData?._sum.amount || 0)
      };
    });

    // حساب إجمالي المصروفات الفعلية
    const totalActualAmount = actualExpenses.reduce((sum, exp) => sum + (exp._sum.amount || 0), 0);

    return NextResponse.json({
      ...budget,
      items: itemsWithExpenses,
      totalActualAmount,
      totalRemainingAmount: budget.totalAmount - totalActualAmount
    });
  } catch (error) {
    console.error('خطأ في جلب الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الميزانية' },
      { status: 500 }
    );
  }
}

// PATCH /api/budgets/:id - تحديث ميزانية
export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);
    const body = await req.json();
    const { name, description, startDate, endDate, totalAmount, status, items } = body;

    // التحقق من وجود الميزانية
    const existingBudget = await prisma.budget.findUnique({
      where: { id },
      include: { items: true }
    });

    if (!existingBudget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من صحة التواريخ إذا تم توفيرها
    let start = existingBudget.startDate;
    let end = existingBudget.endDate;

    if (startDate) {
      start = new Date(startDate);
      if (isNaN(start.getTime())) {
        return NextResponse.json(
          { error: 'تنسيق تاريخ البداية غير صحيح' },
          { status: 400 }
        );
      }
    }

    if (endDate) {
      end = new Date(endDate);
      if (isNaN(end.getTime())) {
        return NextResponse.json(
          { error: 'تنسيق تاريخ النهاية غير صحيح' },
          { status: 400 }
        );
      }
    }

    if (start >= end) {
      return NextResponse.json(
        { error: 'يجب أن يكون تاريخ البداية قبل تاريخ النهاية' },
        { status: 400 }
      );
    }

    // تحديث الميزانية وبنودها في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // تحديث الميزانية
      const updatedBudget = await tx.budget.update({
        where: { id },
        data: {
          name: name || undefined,
          description: description !== undefined ? description : undefined,
          startDate: startDate ? start : undefined,
          endDate: endDate ? end : undefined,
          totalAmount: totalAmount || undefined,
          status: status || undefined
        }
      });

      // تحديث بنود الميزانية إذا تم توفيرها
      if (items && Array.isArray(items)) {
        // حذف البنود الحالية
        await tx.budgetItem.deleteMany({
          where: { budgetId: id }
        });

        // إنشاء البنود الجديدة
        for (const item of items) {
          if (!item.categoryId || !item.amount) {
            continue; // تخطي البنود غير المكتملة
          }

          // التحقق من وجود الفئة
          const category = await tx.expenseCategory.findUnique({
            where: { id: item.categoryId }
          });

          if (!category) {
            continue; // تخطي البنود ذات الفئات غير الموجودة
          }

          await tx.budgetItem.create({
            data: {
              budgetId: id,
              categoryId: item.categoryId,
              amount: item.amount,
              notes: item.notes
            }
          });
        }
      }

      return updatedBudget;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('خطأ في تحديث الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الميزانية' },
      { status: 500 }
    );
  }
}

// DELETE /api/budgets/:id - حذف ميزانية
export async function DELETE(_req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود الميزانية
    const existingBudget = await prisma.budget.findUnique({
      where: { id }
    });

    if (!existingBudget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // حذف الميزانية (سيتم حذف البنود تلقائيًا بسبب علاقة onDelete: Cascade)
    await prisma.budget.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الميزانية' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 إنشاء بيانات تجريبية سريعة...');

    // التحقق من وجود بيانات أساسية
    const studentsCount = await prisma.student.count();
    const examsCount = await prisma.exam.count();
    const examPointsCount = await prisma.exam_points.count();

    console.log(`📊 البيانات الحالية: ${studentsCount} طالب، ${examsCount} امتحان، ${examPointsCount} نقطة`);

    // إذا كانت البيانات موجودة، لا نحتاج لإنشاء المزيد
    if (examPointsCount > 10) {
      return NextResponse.json({
        success: true,
        message: 'البيانات التجريبية موجودة بالفعل',
        data: {
          studentsCount,
          examsCount,
          examPointsCount
        }
      });
    }

    // الحصول على أول طالب
    let student = await prisma.student.findFirst({
      include: { classe: true }
    });

    // إنشاء طالب إذا لم يوجد
    if (!student) {
      // إنشاء فصل أولاً
      const classe = await prisma.classe.upsert({
        where: { name: 'الفصل الأول' },
        update: {},
        create: {
          name: 'الفصل الأول',
          description: 'فصل تجريبي'
        }
      });

      // إنشاء ولي أمر
      const guardian = await prisma.guardian.upsert({
        where: { phone: '0123456789' },
        update: {},
        create: {
          name: 'أحمد محمد',
          phone: '0123456789',
          email: '<EMAIL>'
        }
      });

      // إنشاء طالب
      student = await prisma.student.create({
        data: {
          name: 'محمد أحمد',
          username: 'student001',
          age: 12,
          phone: '0123456788',
          classeId: classe.id,
          guardianId: guardian.id
        },
        include: { classe: true }
      });
    }

    // إنشاء مادة إذا لم توجد
    let subject = await prisma.subject.findFirst();
    if (!subject) {
      subject = await prisma.subject.create({
        data: {
          name: 'القرآن الكريم',
          description: 'مادة حفظ وتلاوة القرآن الكريم'
        }
      });
    }

    // إنشاء نوع امتحان إذا لم يوجد
    let examType = await prisma.examType.findFirst();
    if (!examType) {
      examType = await prisma.examType.create({
        data: {
          name: 'امتحان شهري',
          description: 'امتحان شهري للطلاب'
        }
      });
    }

    // إنشاء معلم إذا لم يوجد
    let teacher = await prisma.teacher.findFirst();
    if (!teacher) {
      teacher = await prisma.teacher.create({
        data: {
          name: 'الأستاذ عبد الرحمن',
          username: 'teacher001',
          email: '<EMAIL>',
          phone: '0123456787'
        }
      });
    }

    // إنشاء علاقة معلم-مادة
    let teacherSubject = await prisma.teacherSubject.findFirst({
      where: {
        teacherId: teacher.id,
        subjectId: subject.id
      }
    });

    if (!teacherSubject) {
      teacherSubject = await prisma.teacherSubject.create({
        data: {
          teacherId: teacher.id,
          subjectId: subject.id
        }
      });
    }

    // إنشاء علاقة فصل-مادة
    let classSubject = await prisma.classSubject.findFirst({
      where: {
        classeId: student.classe!.id,
        teacherSubjectId: teacherSubject.id
      }
    });

    if (!classSubject) {
      classSubject = await prisma.classSubject.create({
        data: {
          classeId: student.classe!.id,
          teacherSubjectId: teacherSubject.id
        }
      });
    }

    // إنشاء امتحانات تجريبية متنوعة
    const currentDate = new Date();
    const months = ['2024-09', '2024-10', '2024-11', '2024-12'];
    const createdExams = [];

    for (const month of months) {
      // امتحان حفظ القرآن
      const quranExam = await prisma.exam.create({
        data: {
          evaluationType: 'QURAN_MEMORIZATION',
          month: month,
          description: `امتحان حفظ القرآن - ${month}`,
          maxPoints: 20,
          passingPoints: 10,
          subjectId: subject.id,
          typeId: examType.id,
          requiresSurah: true
        }
      });
      createdExams.push(quranExam);

      // امتحان تلاوة القرآن
      const recitationExam = await prisma.exam.create({
        data: {
          evaluationType: 'QURAN_RECITATION',
          month: month,
          description: `امتحان تلاوة القرآن - ${month}`,
          maxPoints: 20,
          passingPoints: 10,
          subjectId: subject.id,
          typeId: examType.id,
          requiresSurah: false
        }
      });
      createdExams.push(recitationExam);
    }

    // إنشاء طلاب إضافيين إذا لزم الأمر
    const allStudents = await prisma.student.findMany({
      include: { classe: true }
    });

    let studentsToUse = allStudents;
    if (allStudents.length < 3) {
      // إنشاء طلاب إضافيين
      const additionalStudents = [];
      const studentNames = ['فاطمة أحمد', 'عبد الله محمد', 'عائشة علي', 'يوسف إبراهيم'];

      for (let i = 0; i < studentNames.length; i++) {
        const newStudent = await prisma.student.create({
          data: {
            name: studentNames[i],
            username: `student00${i + 2}`,
            age: 10 + Math.floor(Math.random() * 5),
            phone: `012345678${i + 2}`,
            classeId: student.classe!.id,
            guardianId: student.guardianId
          },
          include: { classe: true }
        });
        additionalStudents.push(newStudent);
      }
      studentsToUse = [...allStudents, ...additionalStudents];
    }

    // إنشاء نقاط امتحانات لجميع الطلاب
    for (const currentStudent of studentsToUse) {
      for (const exam of createdExams) {
        // درجة عشوائية بين 8-20 (متنوعة أكثر)
        const grade = 8 + Math.floor(Math.random() * 13);

        await prisma.exam_points.create({
          data: {
            examId: exam.id,
            studentId: currentStudent.id,
            classSubjectId: classSubject.id,
            grade: grade,
            status: grade >= 10 ? 'PASSED' : 'FAILED',
            note: grade >= 16 ? 'أداء ممتاز' : grade >= 12 ? 'أداء جيد' : grade >= 10 ? 'أداء مقبول' : 'يحتاج تحسين',
            feedback: `الطالب حصل على ${grade} من ${exam.maxPoints}`
          }
        });
      }
    }

    console.log('✅ تم إنشاء البيانات التجريبية بنجاح!');

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء البيانات التجريبية بنجاح',
      data: {
        studentsCount: studentsToUse.length,
        examsCreated: createdExams.length,
        examPointsCreated: studentsToUse.length * createdExams.length,
        classSubjectId: classSubject.id,
        message: `تم إنشاء ${studentsToUse.length * createdExams.length} نقطة امتحان لـ ${studentsToUse.length} طالب في ${createdExams.length} امتحان`
      }
    });

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
    return NextResponse.json(
      { 
        message: "حدث خطأ أثناء إنشاء البيانات التجريبية", 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

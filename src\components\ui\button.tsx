import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/utils/cn"

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background rtl:text-right",
  {
    variants: {
      variant: {
        default: "btn-primary w-full sm:w-auto rtl:font-arabic",
        primary: "btn-primary w-full sm:w-auto rtl:font-arabic",
        secondary: "btn-secondary w-full sm:w-auto rtl:font-arabic",
        success: "btn-success w-full sm:w-auto rtl:font-arabic",
        warning: "btn-warning w-full sm:w-auto rtl:font-arabic",
        error: "btn-error w-full sm:w-auto rtl:font-arabic",
        info: "btn-info w-full sm:w-auto rtl:font-arabic",
        destructive: "btn-error w-full sm:w-auto rtl:font-arabic",
        outline: "border-2 border-primary text-primary hover:bg-primary hover:text-white rtl:font-arabic",
        ghost: "hover:bg-gray-100 hover:text-gray-900 rtl:font-arabic",
        link: "underline-offset-4 hover:underline link-primary rtl:font-arabic",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, onClick, ...props }, ref) => {
    // تحسين التعامل مع أحداث النقر
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      // تأخير بسيط للتأكد من أن الحدث يعمل بشكل صحيح
      setTimeout(() => {
        if (onClick) {
          onClick(event);
        }
      }, 0);
    };

    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        onClick={handleClick}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
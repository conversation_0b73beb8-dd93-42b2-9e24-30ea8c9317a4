"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'react-toastify';
import {
  FaChartLine,
  FaCalendarAlt,
  FaFileExcel,
  FaFilePdf,
  FaFilter
} from 'react-icons/fa';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { format, subMonths } from 'date-fns';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface CashFlowData {
  period: {
    startDate: string;
    endDate: string;
  };
  summary: {
    openingBalance: number;
    totalIncome: number;
    totalExpense: number;
    netCashFlow: number;
    closingBalance: number;
  };
  monthly: Array<{
    month: string;
    income: number;
    expense: number;
    netCashFlow: number;
    runningBalance: number;
  }>;
  incomeBySource: Array<{
    source: string;
    amount: number;
    percentage: number;
  }>;
  expenseByCategory: Array<{
    categoryId: number | null;
    categoryName: string;
    amount: number;
    percentage: number;
  }>;
  transactions: Array<{
    id: number;
    date: string;
    type: 'income' | 'expense';
    description: string;
    amount: number;
    category?: string;
    source?: string;
  }>;
}

export default function CashFlowReportPage() {
  const [startDate, setStartDate] = useState<string>(
    format(subMonths(new Date(), 6), 'yyyy-MM-dd')
  );
  const [endDate, setEndDate] = useState<string>(
    format(new Date(), 'yyyy-MM-dd')
  );
  const [periodType, setPeriodType] = useState<string>('monthly');
  const [reportType, setReportType] = useState<string>('all');
  const [loading, setLoading] = useState<boolean>(false);
  const [cashFlowData, setCashFlowData] = useState<CashFlowData | null>(null);

  // جلب بيانات التدفق النقدي
  const fetchCashFlowData = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        startDate,
        endDate,
        periodType,
        type: reportType,
      });

      const response = await fetch(`/api/reports/cash-flow?${queryParams}`);
      if (!response.ok) {
        throw new Error('فشل في جلب بيانات التدفق النقدي');
      }

      const data = await response.json();
      setCashFlowData(data);
    } catch (error) {
      console.error('Error fetching cash flow data:', error);
      toast.error('فشل في جلب بيانات التدفق النقدي');
    } finally {
      setLoading(false);
    }
  };

  // تصدير التقرير إلى Excel
  const handleExportToExcel = () => {
    if (!cashFlowData) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // تنفيذ تصدير البيانات إلى Excel
      // ملاحظة: تم تعطيل وظيفة exportToExcel لأنها غير معرفة
      // يمكن استخدام مكتبة مثل xlsx أو exceljs لتنفيذ هذه الوظيفة

      const fileName = `تقرير_التدفق_النقدي_${new Date().toISOString().split('T')[0]}.xlsx`;
      toast.info(`سيتم تصدير البيانات إلى ملف Excel: ${fileName}`);

      // مثال على كيفية تنفيذ التصدير باستخدام مكتبة xlsx:
      // const worksheet = XLSX.utils.json_to_sheet(data);
      // const workbook = XLSX.utils.book_new();
      // XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير التدفق النقدي');
      // XLSX.writeFile(workbook, fileName);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تصدير التقرير إلى PDF
  const handleExportToPdf = () => {
    if (!cashFlowData) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // تنفيذ تصدير البيانات إلى PDF
      // ملاحظة: تم تعطيل وظيفة exportToPdf لأنها غير معرفة
      // يمكن استخدام مكتبة مثل jspdf أو pdfmake لتنفيذ هذه الوظيفة

      const fileName = `تقرير_التدفق_النقدي_${new Date().toISOString().split('T')[0]}.pdf`;
      toast.info(`سيتم تصدير البيانات إلى ملف PDF: ${fileName}`);

      // مثال على كيفية تنفيذ التصدير باستخدام مكتبة jspdf:
      // const doc = new jsPDF();
      // doc.text('تقرير التدفق النقدي', 10, 10);
      // doc.save(fileName);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تحميل البيانات عند تغيير المعايير
  useEffect(() => {
    if (startDate && endDate) {
      fetchCashFlowData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startDate, endDate, periodType, reportType]);

  return (
    <OptimizedProtectedRoute requiredPermission="admin.reports.cash-flow.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaChartLine className="text-[var(--primary-color)]" />
          تقرير التدفق النقدي
        </h1>
        <QuickActionButtons
          entityType="reports.cash-flow"
          actions={[
            {
              key: 'export-excel',
              label: 'تصدير Excel',
              icon: <FaFileExcel />,
              onClick: handleExportToExcel,
              variant: 'primary',
              disabled: !cashFlowData
            },
            {
              key: 'export-pdf',
              label: 'تصدير PDF',
              icon: <FaFilePdf />,
              onClick: handleExportToPdf,
              variant: 'destructive',
              disabled: !cashFlowData
            }
          ]}
          className="flex gap-2"
        />
      </div>

      {/* مرشحات التقرير */}
      <Card className="bg-white shadow-md border border-[#e0f2ef] mb-6">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <FaCalendarAlt className="text-[var(--primary-color)]" />
                <span>الفترة الزمنية</span>
              </label>
              <div className="flex gap-2">
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="flex-1"
                  placeholder="من تاريخ"
                />
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="flex-1"
                  placeholder="إلى تاريخ"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <FaFilter className="text-[var(--primary-color)]" />
                <span>نوع الفترة</span>
              </label>
              <Select value={periodType} onValueChange={setPeriodType}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع الفترة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">يومي</SelectItem>
                  <SelectItem value="weekly">أسبوعي</SelectItem>
                  <SelectItem value="monthly">شهري</SelectItem>
                  <SelectItem value="quarterly">ربع سنوي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <FaFilter className="text-[var(--primary-color)]" />
                <span>نوع التقرير</span>
              </label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع التقرير" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">الكل</SelectItem>
                  <SelectItem value="income">الإيرادات فقط</SelectItem>
                  <SelectItem value="expense">المصروفات فقط</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button
                onClick={fetchCashFlowData}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
              >
                تحديث التقرير
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* محتوى التقرير */}
      {loading ? (
        <div className="text-center py-12">جاري تحميل البيانات...</div>
      ) : !cashFlowData ? (
        <div className="text-center py-12">
          <FaChartLine className="text-gray-300 text-5xl mx-auto mb-4" />
          <p className="text-gray-500">يرجى تحديد معايير التقرير وتحديثه لعرض البيانات</p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* سيتم إضافة المزيد من المحتوى هنا */}
        </div>
      )}
      </div>
    </OptimizedProtectedRoute>
  );
}

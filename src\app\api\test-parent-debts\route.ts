import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/test-parent-debts - اختبار بسيط لجلب بيانات الأولياء
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 اختبار جلب بيانات الأولياء...');

    // جلب عدد الأولياء
    const parentsCount = await prisma.parent.count();
    console.log(`👥 عدد الأولياء في قاعدة البيانات: ${parentsCount}`);

    // جلب عدد الطلاب
    const studentsCount = await prisma.student.count();
    console.log(`🎓 عدد الطلاب في قاعدة البيانات: ${studentsCount}`);

    // جلب عدد الفواتير
    const invoicesCount = await prisma.invoice.count();
    console.log(`📄 عدد الفواتير في قاعدة البيانات: ${invoicesCount}`);

    // جلب عدد المدفوعات
    const paymentsCount = await prisma.payment.count();
    console.log(`💰 عدد المدفوعات في قاعدة البيانات: ${paymentsCount}`);

    // جلب عينة من الأولياء
    const sampleParents = await prisma.parent.findMany({
      take: 3,
      include: {
        students: {
          include: {
            invoices: true,
            payments: true
          }
        }
      }
    });

    console.log('📋 عينة من الأولياء:', sampleParents.map(p => ({
      name: p.name,
      phone: p.phone,
      studentsCount: p.students.length,
      students: p.students.map(s => ({
        name: s.name,
        invoicesCount: s.invoices.length,
        paymentsCount: s.payments.length
      }))
    })));

    // إنشاء بيانات تجريبية بسيطة
    const testData = {
      totalParents: parentsCount,
      totalStudents: studentsCount,
      totalInvoices: invoicesCount,
      totalPayments: paymentsCount,
      sampleParents: sampleParents.map(parent => ({
        id: parent.id.toString(),
        name: parent.name,
        phone: parent.phone,
        email: parent.email,
        studentsCount: parent.students.length,
        totalInvoices: parent.students.reduce((sum, student) => sum + student.invoices.length, 0),
        totalPayments: parent.students.reduce((sum, student) => sum + student.payments.length, 0),
        students: parent.students.map(student => ({
          id: student.id,
          name: student.name,
          invoicesCount: student.invoices.length,
          paymentsCount: student.payments.length,
          totalInvoiceAmount: student.invoices.reduce((sum, invoice) => sum + invoice.amount, 0),
          totalPaymentAmount: student.payments.reduce((sum, payment) => sum + payment.amount, 0)
        }))
      }))
    };

    return NextResponse.json({
      success: true,
      message: "تم جلب البيانات التجريبية بنجاح",
      data: testData
    });

  } catch (error) {
    console.error('❌ خطأ في اختبار البيانات:', error);
    return NextResponse.json(
      {
        success: false,
        message: "حدث خطأ أثناء اختبار البيانات",
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

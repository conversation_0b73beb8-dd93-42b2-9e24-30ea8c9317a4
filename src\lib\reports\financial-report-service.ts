import prisma from '@/lib/prisma';

// تعريف الأنواع المستخدمة في التقرير المالي
export interface FinancialReportData {
  period: {
    startDate: string;
    endDate: string;
    duration: number;
  };
  executiveSummary: {
    openingBalance: number;
    closingBalance: number;
    totalIncome: number;
    totalExpenses: number;
    netProfit: number;
    totalTransactions: number;
  };
  incomeDetails: {
    studentPayments: {
      count: number;
      amount: number;
      percentage: number;
      details: any[];
    };
    donations: {
      count: number;
      amount: number;
      percentage: number;
      details: any[];
    };
    otherIncomes: {
      count: number;
      amount: number;
      percentage: number;
      details: any[];
    };
  };
  expenseDetails: {
    total: number;
    byCategory: Array<{
      id: string;
      name: string;
      description: string;
      icon: string;
      color: string;
      expensesCount: number;
      totalAmount: number;
      percentage: number;
    }>;
    transactions: any[];
  };
  paymentMethodStats: Array<{
    id: string;
    name: string;
    paymentsCount: number;
    donationsCount: number;
    paymentsAmount: number;
    donationsAmount: number;
    totalAmount: number;
  }>;
  monthlyStats: Array<{
    month: string;
    payments: { count: number; amount: number };
    donations: { count: number; amount: number };
    expenses: { count: number; amount: number };
    totalIncome: number;
    netProfit: number;
  }>;
}

export interface FinancialFilters {
  expenseCategories?: string[];
  paymentMethods?: string[];
  transactionTypes?: string[];
  minAmount?: number;
  maxAmount?: number;
}

export class FinancialReportService {
  /**
   * إنشاء التقرير المالي للفترة المحددة
   */
  static async generateReport(
    startDate: Date,
    endDate: Date,
    filters: FinancialFilters = {}
  ): Promise<FinancialReportData> {
    try {
      // الحصول على الخزينة
      const treasury = await this.getTreasuryData();
      
      // الحصول على بيانات المدفوعات
      const paymentsData = await this.getPaymentsData(startDate, endDate, filters);
      
      // الحصول على بيانات التبرعات
      const donationsData = await this.getDonationsData(startDate, endDate, filters);
      
      // الحصول على بيانات المداخيل الأخرى
      const incomesData = await this.getIncomesData(startDate, endDate, filters);
      
      // الحصول على بيانات المصروفات
      const expensesData = await this.getExpensesData(startDate, endDate, filters);
      
      // الحصول على إحصائيات طرق الدفع
      const paymentMethodsData = await this.getPaymentMethodsStats(startDate, endDate);
      
      // الحصول على الإحصائيات الشهرية
      const monthlyData = await this.getMonthlyStats(startDate, endDate);

      // حساب الإجماليات
      const totalIncome = paymentsData.total + donationsData.total + incomesData.total;
      const totalExpenses = expensesData.total;
      const netProfit = totalIncome - totalExpenses;
      
      // حساب الرصيد الافتتاحي والختامي
      const openingBalance = treasury.balance - totalIncome + totalExpenses;
      const closingBalance = treasury.balance;

      return {
        period: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          duration: Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)),
        },
        executiveSummary: {
          openingBalance,
          closingBalance,
          totalIncome,
          totalExpenses,
          netProfit,
          totalTransactions: paymentsData.count + donationsData.count + incomesData.count + expensesData.count,
        },
        incomeDetails: {
          studentPayments: {
            count: paymentsData.count,
            amount: paymentsData.total,
            percentage: totalIncome > 0 ? (paymentsData.total / totalIncome) * 100 : 0,
            details: paymentsData.details,
          },
          donations: {
            count: donationsData.count,
            amount: donationsData.total,
            percentage: totalIncome > 0 ? (donationsData.total / totalIncome) * 100 : 0,
            details: donationsData.details,
          },
          otherIncomes: {
            count: incomesData.count,
            amount: incomesData.total,
            percentage: totalIncome > 0 ? (incomesData.total / totalIncome) * 100 : 0,
            details: incomesData.details,
          },
        },
        expenseDetails: expensesData,
        paymentMethodStats: paymentMethodsData,
        monthlyStats: monthlyData,
      };
    } catch (error) {
      console.error('خطأ في إنشاء التقرير المالي:', error);
      throw new Error('فشل في إنشاء التقرير المالي');
    }
  }

  /**
   * الحصول على بيانات الخزينة
   */
  private static async getTreasuryData() {
    const treasury = await prisma.treasury.findFirst();
    if (!treasury) {
      throw new Error('لم يتم العثور على الخزينة');
    }
    return treasury;
  }

  /**
   * الحصول على بيانات المدفوعات
   */
  private static async getPaymentsData(startDate: Date, endDate: Date, filters: FinancialFilters) {
    const whereClause: any = {
      date: {
        gte: startDate,
        lte: endDate,
      },
      status: 'PAID',
    };

    if (filters.paymentMethods && filters.paymentMethods.length > 0) {
      whereClause.paymentMethodId = {
        in: filters.paymentMethods,
      };
    }

    if (filters.minAmount !== undefined || filters.maxAmount !== undefined) {
      whereClause.amount = {};
      if (filters.minAmount !== undefined) {
        whereClause.amount.gte = filters.minAmount;
      }
      if (filters.maxAmount !== undefined) {
        whereClause.amount.lte = filters.maxAmount;
      }
    }

    const payments = await prisma.payment.findMany({
      where: whereClause,
      include: {
        student: {
          select: {
            id: true,
            name: true,
          },
        },
        paymentMethod: {
          select: {
            id: true,
            name: true,
          },
        },
        discount: {
          select: {
            id: true,
            name: true,
            type: true,
            value: true,
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });

    const total = payments.reduce((sum, payment) => sum + payment.amount, 0);

    return {
      count: payments.length,
      total,
      details: payments,
    };
  }

  /**
   * الحصول على بيانات التبرعات
   */
  private static async getDonationsData(startDate: Date, endDate: Date, filters: FinancialFilters) {
    const whereClause: any = {
      date: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (filters.paymentMethods && filters.paymentMethods.length > 0) {
      whereClause.paymentMethodId = {
        in: filters.paymentMethods,
      };
    }

    if (filters.minAmount !== undefined || filters.maxAmount !== undefined) {
      whereClause.amount = {};
      if (filters.minAmount !== undefined) {
        whereClause.amount.gte = filters.minAmount;
      }
      if (filters.maxAmount !== undefined) {
        whereClause.amount.lte = filters.maxAmount;
      }
    }

    const donations = await prisma.donation.findMany({
      where: whereClause,
      include: {
        paymentMethod: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });

    const total = donations.reduce((sum, donation) => sum + donation.amount, 0);

    return {
      count: donations.length,
      total,
      details: donations,
    };
  }

  /**
   * الحصول على بيانات المداخيل الأخرى
   */
  private static async getIncomesData(startDate: Date, endDate: Date, filters: FinancialFilters) {
    const whereClause: any = {
      date: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (filters.minAmount !== undefined || filters.maxAmount !== undefined) {
      whereClause.amount = {};
      if (filters.minAmount !== undefined) {
        whereClause.amount.gte = filters.minAmount;
      }
      if (filters.maxAmount !== undefined) {
        whereClause.amount.lte = filters.maxAmount;
      }
    }

    const incomes = await prisma.income.findMany({
      where: whereClause,
      orderBy: {
        date: 'desc',
      },
    });

    const total = incomes.reduce((sum, income) => sum + income.amount, 0);

    return {
      count: incomes.length,
      total,
      details: incomes,
    };
  }

  /**
   * الحصول على بيانات المصروفات
   */
  private static async getExpensesData(startDate: Date, endDate: Date, filters: FinancialFilters) {
    const whereClause: any = {
      date: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (filters.expenseCategories && filters.expenseCategories.length > 0) {
      whereClause.categoryId = {
        in: filters.expenseCategories,
      };
    }

    if (filters.minAmount !== undefined || filters.maxAmount !== undefined) {
      whereClause.amount = {};
      if (filters.minAmount !== undefined) {
        whereClause.amount.gte = filters.minAmount;
      }
      if (filters.maxAmount !== undefined) {
        whereClause.amount.lte = filters.maxAmount;
      }
    }

    const expenses = await prisma.expense.findMany({
      where: whereClause,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            description: true,
            icon: true,
            color: true,
          },
        },
      },
      orderBy: {
        date: 'desc',
      },
    });

    // إحصائيات فئات المصروفات
    const expenseCategories = await prisma.expenseCategory.findMany({
      include: {
        expenses: {
          where: whereClause,
          select: {
            amount: true,
          },
        },
        _count: {
          select: {
            expenses: {
              where: whereClause,
            },
          },
        },
      },
    });

    const total = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    const expenseCategoryStats = expenseCategories.map(category => {
      const categoryTotal = category.expenses.reduce((sum, expense) => sum + expense.amount, 0);
      return {
        id: category.id,
        name: category.name,
        description: category.description,
        icon: category.icon,
        color: category.color,
        expensesCount: category._count.expenses,
        totalAmount: categoryTotal,
        percentage: total > 0 ? (categoryTotal / total) * 100 : 0,
      };
    });

    return {
      count: expenses.length,
      total,
      byCategory: expenseCategoryStats,
      transactions: expenses,
    };
  }

  /**
   * الحصول على إحصائيات طرق الدفع
   */
  private static async getPaymentMethodsStats(startDate: Date, endDate: Date) {
    const paymentMethods = await prisma.paymentMethod.findMany({
      include: {
        _count: {
          select: {
            payments: {
              where: {
                date: {
                  gte: startDate,
                  lte: endDate,
                },
                status: 'PAID',
              },
            },
            donations: {
              where: {
                date: {
                  gte: startDate,
                  lte: endDate,
                },
              },
            },
          },
        },
        payments: {
          where: {
            date: {
              gte: startDate,
              lte: endDate,
            },
            status: 'PAID',
          },
          select: {
            amount: true,
          },
        },
        donations: {
          where: {
            date: {
              gte: startDate,
              lte: endDate,
            },
          },
          select: {
            amount: true,
          },
        },
      },
    });

    return paymentMethods.map(method => {
      const paymentsAmount = method.payments.reduce((sum, p) => sum + p.amount, 0);
      const donationsAmount = method.donations.reduce((sum, d) => sum + d.amount, 0);
      
      return {
        id: method.id,
        name: method.name,
        paymentsCount: method._count.payments,
        donationsCount: method._count.donations,
        paymentsAmount,
        donationsAmount,
        totalAmount: paymentsAmount + donationsAmount,
      };
    });
  }

  /**
   * الحصول على الإحصائيات الشهرية
   */
  private static async getMonthlyStats(startDate: Date, endDate: Date) {
    // تعديل التاريخ ليكون أول يوم في الشهر
    const startMonth = new Date(startDate);
    startMonth.setDate(1);
    startMonth.setHours(0, 0, 0, 0);

    const endMonth = new Date(endDate);
    endMonth.setDate(1);
    endMonth.setHours(23, 59, 59, 999);

    // إنشاء مصفوفة بالأشهر
    const months = [];
    const currentMonth = new Date(startMonth);
    while (currentMonth <= endMonth) {
      months.push(new Date(currentMonth));
      currentMonth.setMonth(currentMonth.getMonth() + 1);
    }

    // جلب البيانات الشهرية لكل نوع
    const monthlyData = await Promise.all(months.map(async (month) => {
      const nextMonth = new Date(month);
      nextMonth.setMonth(nextMonth.getMonth() + 1);

      const [payments, donations, expenses] = await Promise.all([
        prisma.payment.aggregate({
          where: {
            date: {
              gte: month,
              lt: nextMonth,
            },
            status: 'PAID',
          },
          _count: true,
          _sum: {
            amount: true,
          },
        }),
        prisma.donation.aggregate({
          where: {
            date: {
              gte: month,
              lt: nextMonth,
            },
          },
          _count: true,
          _sum: {
            amount: true,
          },
        }),
        prisma.expense.aggregate({
          where: {
            date: {
              gte: month,
              lt: nextMonth,
            },
          },
          _count: true,
          _sum: {
            amount: true,
          },
        }),
      ]);

      const paymentsAmount = payments._sum.amount || 0;
      const donationsAmount = donations._sum.amount || 0;
      const expensesAmount = expenses._sum.amount || 0;
      const totalIncome = paymentsAmount + donationsAmount;

      return {
        month: month.toISOString().substring(0, 7), // YYYY-MM
        payments: {
          count: payments._count,
          amount: paymentsAmount,
        },
        donations: {
          count: donations._count,
          amount: donationsAmount,
        },
        expenses: {
          count: expenses._count,
          amount: expensesAmount,
        },
        totalIncome,
        netProfit: totalIncome - expensesAmount,
      };
    }));

    return monthlyData;
  }
}

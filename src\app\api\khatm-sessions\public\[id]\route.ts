import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/khatm-sessions/public/[id]
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف مجلس الختم مطلوب' },
        { status: 400 }
      );
    }

    const session = await prisma.khatmSession.findUnique({
      where: {
        id: parseInt(id)
      },
      include: {
        teacher: {
          select: {
            name: true
          }
        },
        surah: {
          select: {
            name: true
          }
        },
        attendances: {
          select: {
            id: true,
            status: true,
            student: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            student: {
              name: 'asc'
            }
          }
        }
      }
    });

    if (!session) {
      return NextResponse.json(
        { error: 'مجلس الختم غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من أن المجلس متاح للعرض العام
    if (!session.isPublic) {
      return NextResponse.json(
        { error: 'مجلس الختم غير متاح للعرض العام' },
        { status: 403 }
      );
    }

    return NextResponse.json({ data: session });
  } catch (error: unknown) {
    console.error('Error fetching public khatm session details:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب تفاصيل مجلس الختم' },
      { status: 500 }
    );
  }
}

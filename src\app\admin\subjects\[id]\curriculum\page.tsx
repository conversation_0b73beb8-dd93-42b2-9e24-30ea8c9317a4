'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { toast } from 'react-toastify';
import {
  FaBook,
  FaLayerGroup,
  FaArrowLeft,
  FaPlus,
  FaEdit,
  FaTrash,
  FaFileAlt,
  FaVideo,
  FaLink,
  FaDownload,
  FaBookOpen,
  FaSave
} from 'react-icons/fa';
import AddUnitDialog from './add-unit-dialog';
import EditUnitDialog from './edit-unit-dialog';
import AddLessonDialog from './add-lesson-dialog';
import EditLessonDialog from './edit-lesson-dialog';
import AddResourceDialog from './add-resource-dialog';
import EditResourceDialog from './edit-resource-dialog';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface Level {
  id: number;
  name: string;
}

interface Subject {
  id: number;
  name: string;
  description?: string;
  levelId?: number | null;
  level?: Level | null;
  hasStudyPlan: boolean;
}

interface CurriculumResource {
  id: number;
  title: string;
  type: string;
  url: string;
  lessonId: number;
}

interface CurriculumLesson {
  id: number;
  title: string;
  description?: string;
  order: number;
  unitId: number;
  resources: CurriculumResource[];
}

interface CurriculumUnit {
  id: number;
  title: string;
  description?: string;
  order: number;
  subjectId: number;
  lessons: CurriculumLesson[];
}

export default function SubjectCurriculumPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [subject, setSubject] = useState<Subject | null>(null);
  const [units, setUnits] = useState<CurriculumUnit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('units');

  // حوارات الإضافة والتعديل
  const [isAddUnitDialogOpen, setIsAddUnitDialogOpen] = useState(false);
  const [isEditUnitDialogOpen, setIsEditUnitDialogOpen] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState<CurriculumUnit | null>(null);

  const [isAddLessonDialogOpen, setIsAddLessonDialogOpen] = useState(false);
  const [isEditLessonDialogOpen, setIsEditLessonDialogOpen] = useState(false);
  const [selectedLesson, setSelectedLesson] = useState<CurriculumLesson | null>(null);
  const [selectedUnitForLesson, setSelectedUnitForLesson] = useState<number | null>(null);

  const [isAddResourceDialogOpen, setIsAddResourceDialogOpen] = useState(false);
  const [isEditResourceDialogOpen, setIsEditResourceDialogOpen] = useState(false);
  const [selectedResource, setSelectedResource] = useState<CurriculumResource | null>(null);
  const [selectedLessonForResource, setSelectedLessonForResource] = useState<number | null>(null);

  // جلب بيانات المادة والمنهج
  useEffect(() => {
    const fetchSubjectAndCurriculum = async () => {
      setIsLoading(true);
      try {
        // جلب بيانات المادة
        const subjectResponse = await fetch(`/api/admin/subjects/${params.id}`);
        if (!subjectResponse.ok) {
          throw new Error('Failed to fetch subject');
        }
        const subjectData = await subjectResponse.json();
        setSubject(subjectData);

        // جلب بيانات المنهج
        const curriculumResponse = await fetch(`/api/curriculum?subjectId=${params.id}`);
        if (!curriculumResponse.ok) {
          throw new Error('Failed to fetch curriculum');
        }
        const curriculumData = await curriculumResponse.json();
        setUnits(curriculumData.units || []);

        // تحديث حالة المادة إذا كان لديها خطة دراسية
        if (curriculumData.units && curriculumData.units.length > 0 && !subjectData.hasStudyPlan) {
          await updateSubjectHasStudyPlan(true);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('حدث خطأ أثناء جلب البيانات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubjectAndCurriculum();
  }, [params.id]);

  // تحديث حالة المادة (hasStudyPlan)
  const updateSubjectHasStudyPlan = async (hasStudyPlan: boolean) => {
    if (!subject) return;

    try {
      const response = await fetch(`/api/admin/subjects/${subject.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...subject,
          hasStudyPlan
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update subject');
      }

      setSubject({ ...subject, hasStudyPlan });
    } catch (error) {
      console.error('Error updating subject:', error);
      toast.error('حدث خطأ أثناء تحديث حالة المادة');
    }
  };

  // حذف وحدة
  const handleDeleteUnit = async (unitId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه الوحدة؟ سيتم حذف جميع الدروس والموارد المرتبطة بها.')) {
      return;
    }

    try {
      const response = await fetch(`/api/curriculum/units/${unitId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete unit');
      }

      toast.success('تم حذف الوحدة بنجاح');
      setUnits(units.filter(unit => unit.id !== unitId));

      // تحديث حالة المادة إذا لم يعد لديها وحدات
      if (units.length === 1) {
        await updateSubjectHasStudyPlan(false);
      }
    } catch (error) {
      console.error('Error deleting unit:', error);
      toast.error('حدث خطأ أثناء حذف الوحدة');
    }
  };

  // حذف درس
  const handleDeleteLesson = async (lessonId: number, unitId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الدرس؟ سيتم حذف جميع الموارد المرتبطة به.')) {
      return;
    }

    try {
      const response = await fetch(`/api/curriculum/lessons/${lessonId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete lesson');
      }

      toast.success('تم حذف الدرس بنجاح');

      // تحديث الوحدات في الواجهة
      setUnits(units.map(unit => {
        if (unit.id === unitId) {
          return {
            ...unit,
            lessons: unit.lessons.filter(lesson => lesson.id !== lessonId)
          };
        }
        return unit;
      }));
    } catch (error) {
      console.error('Error deleting lesson:', error);
      toast.error('حدث خطأ أثناء حذف الدرس');
    }
  };

  // حذف مورد
  const handleDeleteResource = async (resourceId: number, lessonId: number, unitId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/curriculum/resources/${resourceId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete resource');
      }

      toast.success('تم حذف المورد بنجاح');

      // تحديث الوحدات في الواجهة
      setUnits(units.map(unit => {
        if (unit.id === unitId) {
          return {
            ...unit,
            lessons: unit.lessons.map(lesson => {
              if (lesson.id === lessonId) {
                return {
                  ...lesson,
                  resources: lesson.resources.filter(resource => resource.id !== resourceId)
                };
              }
              return lesson;
            })
          };
        }
        return unit;
      }));
    } catch (error) {
      console.error('Error deleting resource:', error);
      toast.error('حدث خطأ أثناء حذف المورد');
    }
  };

  // تحديث الوحدات بعد الإضافة أو التعديل
  const refreshCurriculum = async () => {
    try {
      const response = await fetch(`/api/curriculum?subjectId=${params.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch curriculum');
      }
      const data = await response.json();
      setUnits(data.units || []);

      // تحديث حالة المادة إذا كان لديها خطة دراسية
      if (data.units && data.units.length > 0 && subject && !subject.hasStudyPlan) {
        await updateSubjectHasStudyPlan(true);
      }
    } catch (error) {
      console.error('Error refreshing curriculum:', error);
      toast.error('حدث خطأ أثناء تحديث المنهج');
    }
  };

  // أيقونة نوع المورد
  const getResourceIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return <FaFileAlt className="text-red-500" />;
      case 'video':
        return <FaVideo className="text-blue-500" />;
      case 'link':
        return <FaLink className="text-primary-color" />;
      default:
        return <FaFileAlt className="text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
      </div>
    );
  }

  if (!subject) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">المادة غير موجودة</div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <ProtectedRoute requiredPermission="admin.subjects.curriculum.view">
      <div className="container mx-auto p-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <FaBook className="text-[var(--primary-color)]" />
            <span>المحتوى التعليمي: {subject.name}</span>
          </h1>
          {subject.level && (
            <p className="text-gray-500 flex items-center gap-1 mt-1">
              <FaLayerGroup className="text-[var(--primary-color)]" />
              <span>المستوى: {subject.level.name}</span>
            </p>
          )}
        </div>
        <div className="flex flex-wrap gap-2">
          <Link href={`/admin/subjects/${subject.id}`}>
            <Button variant="outline" className="flex items-center gap-2">
              <FaArrowLeft />
              <span>العودة للمادة</span>
            </Button>
          </Link>
          <Button
            onClick={() => setIsAddUnitDialogOpen(true)}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaPlus />
            <span>إضافة وحدة</span>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
        <TabsList className="w-full">
          <TabsTrigger value="units" className="flex-1">الوحدات الدراسية</TabsTrigger>
          <TabsTrigger value="preview" className="flex-1">معاينة المنهج</TabsTrigger>
        </TabsList>

        <TabsContent value="units" className="mt-6">
          {units.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-gray-500 py-8">
                  <FaBookOpen className="mx-auto text-4xl mb-4 text-gray-300" />
                  <p className="mb-4">لا توجد وحدات دراسية لهذه المادة</p>
                  <Button
                    onClick={() => setIsAddUnitDialogOpen(true)}
                    className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
                  >
                    <FaPlus className="mr-2" /> إضافة وحدة جديدة
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {units.map((unit) => (
                <Card key={unit.id} className="border-t-4 border-t-[var(--primary-color)]">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-xl flex items-center gap-2">
                          <Badge variant="outline" className="bg-[#e0f2ef] text-[var(--primary-color)] border-[var(--primary-color)]">
                            {unit.order}
                          </Badge>
                          {unit.title}
                        </CardTitle>
                        {unit.description && (
                          <CardDescription className="mt-1">
                            {unit.description}
                          </CardDescription>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button
                          onClick={() => {
                            setSelectedUnit(unit);
                            setIsEditUnitDialogOpen(true);
                          }}
                          size="sm"
                          variant="outline"
                          className="flex items-center gap-1"
                        >
                          <FaEdit /> تعديل
                        </Button>
                        <Button
                          onClick={() => handleDeleteUnit(unit.id)}
                          size="sm"
                          variant="destructive"
                          className="flex items-center gap-1"
                        >
                          <FaTrash /> حذف
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="font-semibold text-lg">الدروس</h3>
                      <Button
                        onClick={() => {
                          setSelectedUnitForLesson(unit.id);
                          setIsAddLessonDialogOpen(true);
                        }}
                        size="sm"
                        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                      >
                        <FaPlus /> إضافة درس
                      </Button>
                    </div>

                    {unit.lessons.length === 0 ? (
                      <div className="text-center text-gray-500 py-4 border rounded-lg">
                        لا توجد دروس في هذه الوحدة
                      </div>
                    ) : (
                      <Accordion type="multiple" className="w-full">
                        {unit.lessons.map((lesson) => (
                          <AccordionItem key={lesson.id} value={`lesson-${lesson.id}`}>
                            <AccordionTrigger className="hover:no-underline">
                              <div className="flex justify-between items-center w-full pr-4">
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="bg-gray-100">
                                    {lesson.order}
                                  </Badge>
                                  <span>{lesson.title}</span>
                                </div>
                                <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
                                  <Button
                                    onClick={() => {
                                      setSelectedLesson(lesson);
                                      setSelectedUnitForLesson(unit.id);
                                      setIsEditLessonDialogOpen(true);
                                    }}
                                    size="sm"
                                    variant="outline"
                                    className="flex items-center gap-1 h-8"
                                  >
                                    <FaEdit /> تعديل
                                  </Button>
                                  <Button
                                    onClick={() => handleDeleteLesson(lesson.id, unit.id)}
                                    size="sm"
                                    variant="destructive"
                                    className="flex items-center gap-1 h-8"
                                  >
                                    <FaTrash /> حذف
                                  </Button>
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent>
                              {lesson.description && (
                                <div className="mb-4 text-gray-600 p-2 bg-gray-50 rounded-lg">
                                  {lesson.description}
                                </div>
                              )}

                              <div className="flex justify-between items-center mb-4 mt-4">
                                <h4 className="font-semibold">الموارد التعليمية</h4>
                                <Button
                                  onClick={() => {
                                    setSelectedLessonForResource(lesson.id);
                                    setIsAddResourceDialogOpen(true);
                                  }}
                                  size="sm"
                                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                                >
                                  <FaPlus /> إضافة مورد
                                </Button>
                              </div>

                              {lesson.resources.length === 0 ? (
                                <div className="text-center text-gray-500 py-4 border rounded-lg">
                                  لا توجد موارد تعليمية لهذا الدرس
                                </div>
                              ) : (
                                <div className="space-y-2">
                                  {lesson.resources.map((resource) => (
                                    <div
                                      key={resource.id}
                                      className="flex justify-between items-center p-3 border rounded-lg hover:bg-gray-50"
                                    >
                                      <div className="flex items-center gap-3">
                                        {getResourceIcon(resource.type)}
                                        <div>
                                          <div className="font-medium">{resource.title}</div>
                                          <div className="text-sm text-gray-500">
                                            {resource.type.toUpperCase()}
                                          </div>
                                        </div>
                                      </div>
                                      <div className="flex gap-2">
                                        <a
                                          href={resource.url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="inline-flex items-center justify-center h-8 px-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
                                        >
                                          <FaDownload className="mr-2" /> فتح
                                        </a>
                                        <Button
                                          onClick={() => {
                                            setSelectedResource(resource);
                                            setSelectedLessonForResource(lesson.id);
                                            setIsEditResourceDialogOpen(true);
                                          }}
                                          size="sm"
                                          variant="outline"
                                          className="flex items-center gap-1 h-8"
                                        >
                                          <FaEdit /> تعديل
                                        </Button>
                                        <Button
                                          onClick={() => handleDeleteResource(resource.id, lesson.id, unit.id)}
                                          size="sm"
                                          variant="destructive"
                                          className="flex items-center gap-1 h-8"
                                        >
                                          <FaTrash /> حذف
                                        </Button>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="preview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FaBookOpen className="text-[var(--primary-color)]" />
                <span>معاينة المنهج الدراسي</span>
              </CardTitle>
              <CardDescription>
                عرض المنهج كما سيظهر للطلاب والمعلمين
              </CardDescription>
            </CardHeader>
            <CardContent>
              {units.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  <FaBookOpen className="mx-auto text-4xl mb-4 text-gray-300" />
                  <p>لا توجد وحدات دراسية لهذه المادة</p>
                </div>
              ) : (
                <div className="space-y-8">
                  {units.map((unit) => (
                    <div key={unit.id} className="border-t pt-6">
                      <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
                        <Badge variant="outline" className="bg-[#e0f2ef] text-[var(--primary-color)] border-[var(--primary-color)]">
                          {unit.order}
                        </Badge>
                        {unit.title}
                      </h3>
                      {unit.description && (
                        <p className="text-gray-600 mb-6">{unit.description}</p>
                      )}

                      {unit.lessons.length === 0 ? (
                        <div className="text-center text-gray-500 py-4">
                          لا توجد دروس في هذه الوحدة
                        </div>
                      ) : (
                        <div className="space-y-6 pl-6">
                          {unit.lessons.map((lesson) => (
                            <div key={lesson.id} className="border-r-2 border-gray-200 pr-6 relative">
                              <div className="absolute right-[-9px] top-0 w-4 h-4 rounded-full bg-[var(--primary-color)]"></div>
                              <h4 className="text-lg font-semibold mb-2">{lesson.title}</h4>
                              {lesson.description && (
                                <p className="text-gray-600 mb-4">{lesson.description}</p>
                              )}

                              {lesson.resources.length > 0 && (
                                <div className="mt-4">
                                  <h5 className="font-medium mb-2">الموارد التعليمية:</h5>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {lesson.resources.map((resource) => (
                                      <a
                                        key={resource.id}
                                        href={resource.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50"
                                      >
                                        {getResourceIcon(resource.type)}
                                        <div>
                                          <div className="font-medium">{resource.title}</div>
                                          <div className="text-sm text-gray-500">
                                            {resource.type.toUpperCase()}
                                          </div>
                                        </div>
                                      </a>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                onClick={() => setActiveTab('units')}
                variant="outline"
                className="flex items-center gap-2"
              >
                <FaEdit /> العودة للتحرير
              </Button>
              <Button
                onClick={() => router.push(`/admin/subjects/${subject.id}`)}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
              >
                <FaSave /> حفظ والعودة
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* حوارات الإضافة والتعديل */}
      <AddUnitDialog
        isOpen={isAddUnitDialogOpen}
        onCloseAction={() => setIsAddUnitDialogOpen(false)}
        onSuccessAction={refreshCurriculum}
        subjectId={subject.id}
      />

      <EditUnitDialog
        isOpen={isEditUnitDialogOpen}
        onCloseAction={() => setIsEditUnitDialogOpen(false)}
        onSuccessAction={refreshCurriculum}
        unit={selectedUnit}
      />

      <AddLessonDialog
        isOpen={isAddLessonDialogOpen}
        onCloseAction={() => setIsAddLessonDialogOpen(false)}
        onSuccessAction={refreshCurriculum}
        unitId={selectedUnitForLesson}
      />

      <EditLessonDialog
        isOpen={isEditLessonDialogOpen}
        onCloseAction={() => setIsEditLessonDialogOpen(false)}
        onSuccessAction={refreshCurriculum}
        lesson={selectedLesson}
      />

      <AddResourceDialog
        isOpen={isAddResourceDialogOpen}
        onCloseAction={() => setIsAddResourceDialogOpen(false)}
        onSuccessAction={refreshCurriculum}
        lessonId={selectedLessonForResource}
      />

      <EditResourceDialog
        isOpen={isEditResourceDialogOpen}
        onCloseAction={() => setIsEditResourceDialogOpen(false)}
        onSuccessAction={refreshCurriculum}
        resource={selectedResource}
      />
      </div>
    </ProtectedRoute>
  );
}

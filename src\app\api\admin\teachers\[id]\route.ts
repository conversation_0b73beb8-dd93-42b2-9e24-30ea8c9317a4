import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/admin/teachers/[id] - Get a teacher
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Fetching teacher with ID:', params.id);
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'معرف المعلم غير صالح' }, { status: 400 });
    }
    const teacher = await prisma.teacher.findUnique({
      where: { id },
      include: {
        user: true,
        teacherSubjects: {
          include: {
            subject: true
          }
        }
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { error: 'المعلم غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(teacher);
  } catch (error) {
    console.error('Error fetching teacher:', error);
    return NextResponse.json(
      { error: 'فشل في جلب بيانات المعلم' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/teachers/[id] - Update a teacher
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Updating teacher with ID:', params.id);
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'معرف المعلم غير صالح' }, { status: 400 });
    }

    const body = await request.json();
    const { name, specialization, phone, subjects } = body;

    if (!id || !name || !subjects || !Array.isArray(subjects)) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      );
    }

    // First, get the teacher to find the userId
    const existingTeacher = await prisma.teacher.findUnique({
      where: { id },
      select: { userId: true }
    });

    if (!existingTeacher) {
      return NextResponse.json(
        { error: 'المعلم غير موجود' },
        { status: 404 }
      );
    }

    const teacher = await prisma.$transaction(async (tx) => {
      // Update teacher's basic info
      await tx.teacher.update({
        where: { id },
        data: {
          name,
          specialization,
          phone
        }
      });

      // Delete existing subject relationships
      await tx.teacherSubject.deleteMany({
        where: { teacherId: id }
      });

      // Create new subject relationships
      for (const subjectId of subjects) {
        await tx.teacherSubject.create({
          data: {
            teacherId: id,
            subjectId
          }
        });
      }

      // Get the updated teacher with all relationships
      return await tx.teacher.findUnique({
        where: { id },
        include: {
          user: true,
          teacherSubjects: {
            include: {
              subject: true
            }
          }
        }
      });
    });

    return NextResponse.json(teacher);
  } catch (error: unknown) {
    console.error('Error updating teacher:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث بيانات المعلم' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/teachers/[id] - Delete a teacher
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Deleting teacher with ID:', params.id);
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({ error: 'معرف المعلم غير صالح' }, { status: 400 });
    }

    // Check if teacher exists
    const teacher = await prisma.teacher.findUnique({
      where: { id },
      include: { user: true }
    });

    if (!teacher) {
      return NextResponse.json(
        { error: 'المعلم غير موجود' },
        { status: 404 }
      );
    }

    await prisma.$transaction(async (tx) => {
      // Delete teacher's subjects first
      await tx.teacherSubject.deleteMany({
        where: { teacherId: id }
      });

      // Delete teacher
      await tx.teacher.delete({
        where: { id }
      });

      // Delete associated user (which will cascade delete the profile)
      if (teacher.user) {
        await tx.user.delete({
          where: { id: teacher.user.id }
        });
      }
    });

    return NextResponse.json({ message: 'تم حذف المعلم بنجاح' });
  } catch (error) {
    console.error('Error deleting teacher:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المعلم' },
      { status: 500 }
    );
  }
}

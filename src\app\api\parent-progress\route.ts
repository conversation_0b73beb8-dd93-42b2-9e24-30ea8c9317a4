import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// GET /api/parent-progress - جلب تقدم أبناء ولي الأمر المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'PARENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المستخدم والملف الشخصي
    const user = await prisma.user.findUnique({
      where: {
        id: userData.id
      },
      include: {
        profile: true
      }
    });

    if (!user || !user.profile) {
      console.error('لم يتم العثور على المستخدم أو الملف الشخصي:', userData.id);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المستخدم" },
        { status: 404 }
      );
    }

    // جلب معلومات ولي الأمر باستخدام اسم الملف الشخصي أو اسم المستخدم
    const parent = await prisma.parent.findFirst({
      where: {
        OR: [
          { name: user.profile.name },
          { name: userData.username }
        ]
      },
      include: {
        students: true
      }
    });

    if (!parent) {
      console.error('لم يتم العثور على ولي الأمر للمستخدم:', userData.username, 'مع الملف الشخصي:', user.profile.name);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات ولي الأمر" },
        { status: 404 }
      );
    }

    // التحقق من وجود أبناء
    if (!parent.students || parent.students.length === 0) {
      console.log('ولي الأمر موجود ولكن لا يوجد أبناء مسجلين:', parent.name);
      return NextResponse.json({
        children: [],
        message: "لا يوجد أبناء مسجلين لولي الأمر"
      });
    }

    // التحقق من وجود معرف الطالب في الاستعلام
    const searchParams = request.nextUrl.searchParams;
    const childId = searchParams.get('childId');

    // إذا تم تحديد معرف الطالب، تحقق من أنه ينتمي لولي الأمر
    if (childId) {
      const childIdNum = parseInt(childId);
      const isParentChild = parent.students.some(student => student.id === childIdNum);

      if (!isParentChild) {
        return NextResponse.json(
          { message: "غير مصرح بالوصول إلى بيانات هذا الطالب" },
          { status: 403 }
        );
      }

      // جلب بيانات الطالب المحدد
      const student = await prisma.student.findUnique({
        where: { id: childIdNum },
        include: {
          classe: true
        }
      });

      if (!student) {
        return NextResponse.json(
          { message: "لم يتم العثور على بيانات الطالب" },
          { status: 404 }
        );
      }

      // جلب نتائج الامتحانات للطالب
      const examResults = await prisma.exam_points.findMany({
        where: { studentId: childIdNum },
        include: {
          exam: true,
          surah: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // جلب تقدم حفظ القرآن للطالب
      const quranProgress = await prisma.quranProgress.findMany({
        where: { studentId: childIdNum },
        include: {
          exam: true,
          surah: true
        },
        orderBy: {
          startDate: 'desc'
        }
      });

      // جلب سجل الحضور للطالب
      const attendance = await prisma.attendance.findMany({
        where: { studentId: childIdNum },
        orderBy: {
          date: 'desc'
        },
        take: 30 // آخر 30 يوم
      });

      // تنسيق البيانات للعرض
      const formattedExamResults = examResults.map(result => ({
        id: result.id,
        examId: result.examId,
        examType: result.exam.evaluationType,
        month: result.exam.month,
        description: result.exam.description || '',
        points: result.grade,
        maxPoints: result.exam.maxPoints,
        passingPoints: result.exam.passingPoints,
        isPassed: Number(result.grade) >= result.exam.passingPoints,
        surahName: result.surah?.name || '',
        date: result.createdAt.toISOString()
      }));

      const formattedQuranProgress = quranProgress.map(progress => ({
        id: progress.id,
        examId: progress.examId,
        surahName: progress.surah.name,
        startVerse: progress.startVerse,
        endVerse: progress.endVerse,
        memorization: progress.memorization,
        tajweed: progress.tajweed,
        totalScore: (progress.memorization + progress.tajweed) / 2,
        startDate: progress.startDate.toISOString(),
        completionDate: progress.completionDate ? progress.completionDate.toISOString() : null
      }));

      const formattedAttendance = attendance.map(record => ({
        id: record.id,
        date: record.date.toISOString(),
        status: record.status,
        hisass: record.hisass
      }));

      // حساب الإحصائيات
      const totalExams = formattedExamResults.length;
      const passedExams = formattedExamResults.filter(exam => exam.isPassed).length;
      const averageScore = totalExams > 0
        ? formattedExamResults.reduce((sum, exam) => sum + (Number(exam.points) / exam.maxPoints) * 100, 0) / totalExams
        : 0;

      const totalAttendance = formattedAttendance.length;
      const presentAttendance = formattedAttendance.filter(record => record.status === 'PRESENT').length;
      const attendanceRate = totalAttendance > 0 ? (presentAttendance / totalAttendance) * 100 : 0;

      const averageMemorization = formattedQuranProgress.length > 0
        ? formattedQuranProgress.reduce((sum, progress) => sum + progress.memorization, 0) / formattedQuranProgress.length
        : 0;

      const averageTajweed = formattedQuranProgress.length > 0
        ? formattedQuranProgress.reduce((sum, progress) => sum + progress.tajweed, 0) / formattedQuranProgress.length
        : 0;

      return NextResponse.json({
        student: {
          id: student.id,
          name: student.name,
          age: student.age,
          grade: student.classe?.name || 'غير محدد',
          totalPoints: student.totalPoints
        },
        examResults: formattedExamResults,
        quranProgress: formattedQuranProgress,
        attendance: formattedAttendance,
        stats: {
          totalExams,
          passedExams,
          averageScore,
          attendanceRate,
          averageMemorization,
          averageTajweed
        },
        message: "تم جلب بيانات تقدم الطالب بنجاح"
      });
    } else {
      // تعريف نوع بيانات تقدم الطالب
      interface ChildProgress {
        id: number;
        name: string;
        age: number;
        grade: string;
        totalPoints: number;
        stats: {
          averageScore: number;
          attendanceRate: number;
        };
        lastQuranProgress: {
          surahName: string;
          startVerse: number;
          endVerse: number;
          memorization: number;
          tajweed: number;
          date: string;
        } | null;
      }

      // جلب ملخص تقدم جميع الأبناء
      const childrenProgress: (ChildProgress | null)[] = await Promise.all(parent.students.map(async (student) => {
        // جلب بيانات الطالب
        const studentData = await prisma.student.findUnique({
          where: { id: student.id },
          include: {
            classe: true
          }
        });

        if (!studentData) return null;

        // جلب آخر 5 نتائج امتحانات
        const recentExams = await prisma.exam_points.findMany({
          where: { studentId: student.id },
          take: 5,
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            exam: true
          }
        });

        // حساب متوسط الدرجات
        const totalExams = recentExams.length;
        const averageScore = totalExams > 0
          ? recentExams.reduce((sum, exam) => sum + (Number(exam.grade) / exam.exam.maxPoints) * 100, 0) / totalExams
          : 0;

        // جلب آخر 30 سجل حضور
        const recentAttendance = await prisma.attendance.findMany({
          where: { studentId: student.id },
          take: 30,
          orderBy: {
            date: 'desc'
          }
        });

        // حساب نسبة الحضور
        const totalAttendance = recentAttendance.length;
        const presentAttendance = recentAttendance.filter(record => record.status === 'PRESENT').length;
        const attendanceRate = totalAttendance > 0 ? (presentAttendance / totalAttendance) * 100 : 0;

        // جلب آخر تقدم في حفظ القرآن
        const lastQuranProgress = await prisma.quranProgress.findFirst({
          where: { studentId: student.id },
          orderBy: {
            startDate: 'desc'
          },
          include: {
            surah: true
          }
        });

        return {
          id: student.id,
          name: student.name,
          age: student.age,
          grade: studentData.classe?.name || 'غير محدد',
          totalPoints: student.totalPoints,
          stats: {
            averageScore,
            attendanceRate
          },
          lastQuranProgress: lastQuranProgress ? {
            surahName: lastQuranProgress.surah.name,
            startVerse: lastQuranProgress.startVerse,
            endVerse: lastQuranProgress.endVerse,
            memorization: lastQuranProgress.memorization,
            tajweed: lastQuranProgress.tajweed,
            date: lastQuranProgress.startDate.toISOString()
          } : null
        };
      }));

      // فلترة القيم null
      const children: ChildProgress[] = childrenProgress.filter((child): child is ChildProgress => child !== null);

      return NextResponse.json({
        children,
        message: "تم جلب بيانات تقدم الأبناء بنجاح"
      });
    }
  } catch (error) {
    console.error('Error fetching parent progress:', error);
    // إضافة المزيد من التفاصيل للخطأ
    const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير معروف';
    return NextResponse.json(
      {
        message: "حدث خطأ أثناء جلب بيانات التقدم",
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

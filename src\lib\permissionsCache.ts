/**
 * خدمة التخزين المؤقت للصلاحيات
 * تدير تخزين واسترجاع صلاحيات المستخدمين محلياً مع التحديث الدوري
 */

export interface Permission {
  id: number;
  key: string;
  name: string;
  description?: string;
  category: string;
  route?: string;
  isActive: boolean;
}

export interface CachedPermissions {
  permissions: Permission[];
  userRole: string;
  userId: number;
  timestamp: number;
  expiresAt: number;
  roleId?: number;
  roleName?: string;
}

export interface CacheMetadata {
  lastCleanup: number;
  version: string;
}

// إعدادات التخزين المؤقت
const CACHE_DURATION = 30 * 60 * 1000; // 30 دقيقة بالمللي ثانية
const CACHE_VERSION = '1.0.0';
const CACHE_PREFIX = 'permissions_cache_';
const CACHE_META_KEY = 'permissions_cache_meta';

/**
 * التحقق من دعم localStorage
 */
function isLocalStorageAvailable(): boolean {
  try {
    if (typeof window === 'undefined') return false;
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

/**
 * إنشاء مفتاح التخزين المؤقت للمستخدم
 */
function getCacheKey(userId: number): string {
  return `${CACHE_PREFIX}${userId}`;
}

/**
 * حفظ الصلاحيات في التخزين المؤقت
 */
export function savePermissionsToCache(
  userId: number,
  permissions: Permission[],
  userRole: string,
  roleId?: number,
  roleName?: string
): boolean {
  if (!isLocalStorageAvailable()) {
    console.warn('localStorage غير متاح، لا يمكن حفظ التخزين المؤقت');
    return false;
  }

  try {
    const now = Date.now();
    const cacheData: CachedPermissions = {
      permissions,
      userRole,
      userId,
      timestamp: now,
      expiresAt: now + CACHE_DURATION,
      roleId,
      roleName
    };

    const cacheKey = getCacheKey(userId);
    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    
    // تحديث البيانات الوصفية
    updateCacheMetadata();
    
    console.log(`تم حفظ صلاحيات المستخدم ${userId} في التخزين المؤقت`);
    return true;
  } catch (error) {
    console.error('خطأ في حفظ التخزين المؤقت:', error);
    return false;
  }
}

/**
 * جلب الصلاحيات من التخزين المؤقت
 */
export function getPermissionsFromCache(userId: number): CachedPermissions | null {
  if (!isLocalStorageAvailable()) {
    return null;
  }

  try {
    const cacheKey = getCacheKey(userId);
    const cachedData = localStorage.getItem(cacheKey);
    
    if (!cachedData) {
      return null;
    }

    const parsedData: CachedPermissions = JSON.parse(cachedData);
    
    // التحقق من صحة البيانات
    if (!parsedData.permissions || !Array.isArray(parsedData.permissions)) {
      console.warn('بيانات التخزين المؤقت غير صحيحة');
      clearUserCache(userId);
      return null;
    }

    return parsedData;
  } catch (error) {
    console.error('خطأ في قراءة التخزين المؤقت:', error);
    clearUserCache(userId);
    return null;
  }
}

/**
 * التحقق من صلاحية البيانات المخزنة
 */
export function isCacheValid(userId: number): boolean {
  const cachedData = getPermissionsFromCache(userId);
  
  if (!cachedData) {
    return false;
  }

  const now = Date.now();
  const isValid = now < cachedData.expiresAt;
  
  if (!isValid) {
    console.log(`انتهت صلاحية التخزين المؤقت للمستخدم ${userId}`);
    clearUserCache(userId);
  }
  
  return isValid;
}

/**
 * مسح التخزين المؤقت لمستخدم معين
 */
export function clearUserCache(userId: number): void {
  if (!isLocalStorageAvailable()) {
    return;
  }

  try {
    const cacheKey = getCacheKey(userId);
    localStorage.removeItem(cacheKey);
    console.log(`تم مسح التخزين المؤقت للمستخدم ${userId}`);
  } catch (error) {
    console.error('خطأ في مسح التخزين المؤقت:', error);
  }
}

/**
 * مسح جميع بيانات التخزين المؤقت
 */
export function clearAllCache(): void {
  if (!isLocalStorageAvailable()) {
    return;
  }

  try {
    // البحث عن جميع مفاتيح التخزين المؤقت
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(CACHE_PREFIX)) {
        keysToRemove.push(key);
      }
    }

    // مسح المفاتيح
    keysToRemove.forEach(key => localStorage.removeItem(key));
    localStorage.removeItem(CACHE_META_KEY);
    
    console.log('تم مسح جميع بيانات التخزين المؤقت للصلاحيات');
  } catch (error) {
    console.error('خطأ في مسح جميع بيانات التخزين المؤقت:', error);
  }
}

/**
 * تحديث البيانات الوصفية للتخزين المؤقت
 */
function updateCacheMetadata(): void {
  if (!isLocalStorageAvailable()) {
    return;
  }

  try {
    const metadata: CacheMetadata = {
      lastCleanup: Date.now(),
      version: CACHE_VERSION
    };
    
    localStorage.setItem(CACHE_META_KEY, JSON.stringify(metadata));
  } catch (error) {
    console.error('خطأ في تحديث البيانات الوصفية:', error);
  }
}

/**
 * تنظيف البيانات المنتهية الصلاحية
 */
export function cleanupExpiredCache(): void {
  if (!isLocalStorageAvailable()) {
    return;
  }

  try {
    const now = Date.now();
    const keysToRemove: string[] = [];

    // فحص جميع مفاتيح التخزين المؤقت
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(CACHE_PREFIX)) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const parsedData: CachedPermissions = JSON.parse(data);
            if (now >= parsedData.expiresAt) {
              keysToRemove.push(key);
            }
          }
        } catch {
          // إذا كانت البيانات تالفة، احذفها
          keysToRemove.push(key);
        }
      }
    }

    // مسح البيانات المنتهية الصلاحية
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    if (keysToRemove.length > 0) {
      console.log(`تم تنظيف ${keysToRemove.length} عنصر منتهي الصلاحية من التخزين المؤقت`);
    }

    updateCacheMetadata();
  } catch (error) {
    console.error('خطأ في تنظيف التخزين المؤقت:', error);
  }
}

/**
 * الحصول على معلومات حالة التخزين المؤقت
 */
export function getCacheStatus(): {
  isAvailable: boolean;
  totalItems: number;
  lastCleanup?: number;
  version?: string;
} {
  if (!isLocalStorageAvailable()) {
    return { isAvailable: false, totalItems: 0 };
  }

  try {
    let totalItems = 0;
    
    // عد عناصر التخزين المؤقت
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(CACHE_PREFIX)) {
        totalItems++;
      }
    }

    // جلب البيانات الوصفية
    const metaData = localStorage.getItem(CACHE_META_KEY);
    let lastCleanup: number | undefined;
    let version: string | undefined;

    if (metaData) {
      try {
        const parsed: CacheMetadata = JSON.parse(metaData);
        lastCleanup = parsed.lastCleanup;
        version = parsed.version;
      } catch {
        // تجاهل الأخطاء في البيانات الوصفية
      }
    }

    return {
      isAvailable: true,
      totalItems,
      lastCleanup,
      version
    };
  } catch (error) {
    console.error('خطأ في الحصول على حالة التخزين المؤقت:', error);
    return { isAvailable: false, totalItems: 0 };
  }
}

/**
 * الحصول على الوقت المتبقي لانتهاء صلاحية التخزين المؤقت (بالمللي ثانية)
 */
export function getCacheTimeRemaining(userId: number): number {
  const cachedData = getPermissionsFromCache(userId);

  if (!cachedData) {
    return 0;
  }

  const now = Date.now();
  const remaining = cachedData.expiresAt - now;

  return Math.max(0, remaining);
}

/**
 * تنظيف التخزين المؤقت عند تسجيل الخروج
 * يمسح جميع البيانات المتعلقة بالمستخدم الحالي
 */
export function clearCacheOnLogout(userId?: number): void {
  if (!isLocalStorageAvailable()) {
    return;
  }

  try {
    if (userId) {
      // مسح تخزين مستخدم محدد
      clearUserCache(userId);
      console.log(`تم مسح التخزين المؤقت للمستخدم ${userId} عند تسجيل الخروج`);
    } else {
      // مسح جميع بيانات التخزين المؤقت
      clearAllCache();
      console.log('تم مسح جميع بيانات التخزين المؤقت عند تسجيل الخروج');
    }

    // إرسال حدث مخصص لإعلام المكونات الأخرى
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('permissionsCacheCleared', {
        detail: { userId, reason: 'logout' }
      }));
    }
  } catch (error) {
    console.error('خطأ في تنظيف التخزين المؤقت عند تسجيل الخروج:', error);
  }
}

/**
 * تنظيف التخزين المؤقت عند تغيير المستخدم
 */
export function clearCacheOnUserChange(oldUserId: number, newUserId: number): void {
  if (!isLocalStorageAvailable()) {
    return;
  }

  try {
    // مسح بيانات المستخدم السابق
    clearUserCache(oldUserId);
    console.log(`تم مسح التخزين المؤقت للمستخدم السابق ${oldUserId}`);

    // إرسال حدث مخصص
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('permissionsCacheCleared', {
        detail: {
          oldUserId,
          newUserId,
          reason: 'userChange'
        }
      }));
    }
  } catch (error) {
    console.error('خطأ في تنظيف التخزين المؤقت عند تغيير المستخدم:', error);
  }
}

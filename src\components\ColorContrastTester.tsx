'use client';

import React from 'react';

interface ColorContrastTesterProps {
  isVisible?: boolean;
}

export default function ColorContrastTester({ isVisible = false }: ColorContrastTesterProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 left-4 bg-white p-4 rounded-lg shadow-lg border z-50 max-w-sm">
      <h3 className="text-lg font-bold mb-4 text-gray-800">اختبار التباين</h3>
      
      <div className="space-y-3">
        {/* خلفية فاتحة مع نص داكن */}
        <div className="bg-primary-light p-3 rounded">
          <p className="text-sm">خلفية فاتحة مع نص داكن</p>
          <p className="text-xs opacity-75">يجب أن يكون النص واضح ومقروء</p>
        </div>

        {/* خلفية داكنة مع نص فاتح */}
        <div className="bg-primary-color p-3 rounded">
          <p className="text-sm">خلفية داكنة مع نص فاتح</p>
          <p className="text-xs opacity-75">يجب أن يكون النص واضح ومقروء</p>
        </div>

        {/* خلفية ثانوية فاتحة */}
        <div className="bg-secondary-light p-3 rounded">
          <p className="text-sm">خلفية ثانوية فاتحة</p>
          <p className="text-xs opacity-75">نص بتباين جيد</p>
        </div>

        {/* خلفية ثانوية داكنة */}
        <div className="bg-secondary-color p-3 rounded">
          <p className="text-sm">خلفية ثانوية داكنة</p>
          <p className="text-xs opacity-75">نص بتباين جيد</p>
        </div>

        {/* ألوان النجاح */}
        <div className="success-colors p-3 rounded border">
          <p className="text-sm">ألوان النجاح</p>
          <p className="text-xs opacity-75">مع حدود وتباين مناسب</p>
        </div>

        {/* ألوان النجاح الداكنة */}
        <div className="success-colors-dark p-3 rounded">
          <p className="text-sm">ألوان النجاح الداكنة</p>
          <p className="text-xs opacity-75">نص متباين على خلفية داكنة</p>
        </div>

        {/* نصوص ملونة */}
        <div className="bg-gray-50 p-3 rounded">
          <p className="text-primary-color text-sm">نص بلون أساسي</p>
          <p className="text-secondary-color text-sm">نص بلون ثانوي</p>
          <p className="text-primary-dark text-sm">نص داكن أساسي</p>
          <p className="text-secondary-dark text-sm">نص داكن ثانوي</p>
        </div>

        {/* أزرار */}
        <div className="space-y-2">
          <button className="bg-primary-color px-4 py-2 rounded text-sm w-full">
            زر أساسي
          </button>
          <button className="bg-secondary-color px-4 py-2 rounded text-sm w-full">
            زر ثانوي
          </button>
          <button className="bg-primary-light border border-primary-color px-4 py-2 rounded text-sm w-full">
            زر بحدود
          </button>
        </div>

        {/* شارات */}
        <div className="flex gap-2 flex-wrap">
          <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
            شارة خضراء فاتحة
          </span>
          <span className="bg-green-500 px-2 py-1 rounded text-xs">
            شارة خضراء داكنة
          </span>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-600">
        <p>• تأكد من وضوح جميع النصوص</p>
        <p>• يجب أن يكون التباين كافياً للقراءة</p>
        <p>• اختبر مع ألوان مختلفة</p>
      </div>
    </div>
  );
}

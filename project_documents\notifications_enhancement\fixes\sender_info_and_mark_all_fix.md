# إضافة معلومات المرسل وإصلاح زر "تحديد الكل كمقروء"

## نظرة عامة
تم تحسين نظام الإشعارات بإضافة معلومات المرسل لجميع الإشعارات وإصلاح مشكلة زر "تحديد الكل كمقروء".

## التحديثات المطبقة

### 1. إضافة معلومات المرسل للإشعارات

#### أ. تحديث API جلب الإشعارات (`src/app/api/notifications/route.ts`)

##### للإشعارات الفردية:
```javascript
const individualNotifications = await prisma.notification.findMany({
    where: individualWhere,
    include: {
        user: {
            select: {
                id: true,
                username: true,
                profile: {
                    select: {
                        name: true
                    }
                }
            }
        }
    },
    orderBy: {
        createdAt: 'desc'
    }
});
```

##### للإشعارات الجماعية:
```javascript
const groupNotifications = await prisma.notification.findMany({
    where: {
        isGroupNotification: true,
        recipients: {
            some: {
                userId: userId,
                ...(unreadOnly ? { read: false } : {})
            }
        },
        ...(type ? { type } : {})
    },
    include: {
        user: {
            select: {
                id: true,
                username: true,
                profile: {
                    select: {
                        name: true
                    }
                }
            }
        },
        recipients: {
            where: { userId: userId },
            select: { read: true, readAt: true }
        }
    },
    orderBy: {
        createdAt: 'desc'
    }
});
```

##### تنسيق معلومات المرسل:
```javascript
// دمج الإشعارات وتنسيقها
const allNotifications = [
    ...individualNotifications.map(notification => ({
        ...notification,
        isGroupNotification: false,
        sender: notification.user ? {
            id: notification.user.id,
            name: notification.user.profile?.name || notification.user.username,
            username: notification.user.username
        } : null
    })),
    ...groupNotifications.map(notification => ({
        ...notification,
        read: notification.recipients[0]?.read || false,
        readAt: notification.recipients[0]?.readAt || null,
        isGroupNotification: true,
        sender: notification.user ? {
            id: notification.user.id,
            name: notification.user.profile?.name || notification.user.username,
            username: notification.user.username
        } : null
    }))
];
```

#### ب. تحديث API الإشعارات الجماعية (`src/app/api/notifications/bulk/route.ts`)

```javascript
// إنشاء الإشعار الرئيسي
const notification = await prisma.notification.create({
    data: {
        title,
        content,
        type: type || 'GENERAL',
        isGroupNotification: true,
        priority: priority || 'MEDIUM',
        scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
        status: scheduledAt ? 'PENDING' : 'SENDING',
        sentAt: scheduledAt ? null : new Date(),
        // إضافة معلومات المرسل للإشعارات الجماعية
        userId: userData.id,
    }
});
```

### 2. تحسين واجهة المستخدم (`src/app/notifications/page.tsx`)

#### أ. تحديث Interface للإشعارات:
```typescript
interface Notification {
  id: number;
  title: string;
  content: string;
  type: string;
  read: boolean;
  createdAt: string;
  link?: string;
  isGroupNotification?: boolean;
  sender?: {
    id: number;
    name: string;
    username: string;
  };
}
```

#### ب. إضافة عرض معلومات المرسل:
```jsx
{/* معلومات المرسل */}
{notification.sender && (
  <div className="mt-2 flex items-center text-xs text-gray-500">
    <FaUser className="ml-1" />
    <span>من: {notification.sender.name}</span>
    {notification.isGroupNotification && (
      <span className="mr-2 bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
        إشعار جماعي
      </span>
    )}
  </div>
)}
```

#### ج. تحسين دالة "تحديد الكل كمقروء":
```javascript
const markAllAsRead = async () => {
  try {
    console.log('Marking all notifications as read...');
    const response = await axios.post('/api/notifications/mark-all-read');
    console.log('Mark all as read response:', response.data);

    // تحديث حالة جميع الإشعارات في القائمة
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );

    // تحديث عدد الإشعارات غير المقروءة
    setUnreadCount(0);

    // إعادة جلب الإشعارات لضمان التحديث
    fetchNotifications();

    toast.success('تم تحديد جميع الإشعارات كمقروءة');
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    toast.error('فشل في تحديث حالة الإشعارات');
  }
};
```

## الميزات الجديدة

### 1. **معلومات المرسل الشاملة**

#### للإشعارات الفردية:
- ✅ عرض اسم المرسل (من الملف الشخصي أو اسم المستخدم)
- ✅ معرف المرسل للمراجع المستقبلية
- ✅ اسم المستخدم للمرسل

#### للإشعارات الجماعية:
- ✅ عرض اسم المرسل الذي أنشأ الإشعار الجماعي
- ✅ تمييز بصري للإشعارات الجماعية بعلامة "إشعار جماعي"
- ✅ نفس معلومات المرسل كالإشعارات الفردية

### 2. **تحسين زر "تحديد الكل كمقروء"**

#### المشاكل التي تم إصلاحها:
- ✅ إضافة تسجيل (logging) لتتبع العمليات
- ✅ إعادة جلب الإشعارات بعد التحديث لضمان التحديث الصحيح
- ✅ تحديث محلي للحالة مع تحديث من الخادم

#### التحسينات المضافة:
- ✅ رسائل تأكيد واضحة
- ✅ معالجة أخطاء محسنة
- ✅ تحديث فوري للواجهة

### 3. **تحسين التصميم والتجربة**

#### العرض البصري:
- ✅ أيقونة مستخدم بجانب معلومات المرسل
- ✅ علامة مميزة للإشعارات الجماعية
- ✅ تنسيق متسق لجميع أنواع الإشعارات

#### سهولة الاستخدام:
- ✅ معلومات واضحة عن مصدر الإشعار
- ✅ تمييز بصري بين الإشعارات الفردية والجماعية
- ✅ تحديث سلس للحالة

## اختبار التحديثات

### 1. **اختبار معلومات المرسل**

#### للإشعارات الفردية:
1. سجل الدخول كمسؤول/معلم/موظف
2. أنشئ إشعار فردي لمستخدم آخر
3. سجل الدخول كالمستخدم المستهدف
4. تحقق من ظهور "من: [اسم المرسل]" في الإشعار

#### للإشعارات الجماعية:
1. سجل الدخول كمسؤول/معلم/موظف
2. أنشئ إشعار جماعي
3. سجل الدخول كمستخدم عادي
4. تحقق من ظهور:
   - "من: [اسم المرسل]"
   - علامة "إشعار جماعي"

### 2. **اختبار زر "تحديد الكل كمقروء"**

#### الخطوات:
1. تأكد من وجود إشعارات غير مقروءة
2. انقر على زر "تحديد الكل كمقروء"
3. تحقق من:
   - تحديث العدد إلى 0
   - تغيير لون الإشعارات
   - ظهور رسالة نجاح
   - اختفاء الزر (لعدم وجود إشعارات غير مقروءة)

#### في وحدة تحكم المطور:
1. افتح Developer Tools
2. انتقل إلى Console
3. انقر على الزر وتحقق من الرسائل:
   - "Marking all notifications as read..."
   - "Mark all as read response: {message: '...', count: X}"

### 3. **اختبار التكامل**

#### سيناريو شامل:
1. أنشئ إشعارات فردية وجماعية من حسابات مختلفة
2. سجل الدخول كمستخدم عادي
3. تحقق من ظهور جميع الإشعارات مع معلومات المرسل الصحيحة
4. اقرأ بعض الإشعارات يدوياً
5. استخدم "تحديد الكل كمقروء" للباقي
6. تحقق من التحديث الصحيح لجميع الحالات

## الفوائد المحققة

### 1. **شفافية أكبر**
- المستخدمون يعرفون مصدر كل إشعار
- تمييز واضح بين الإشعارات الفردية والجماعية
- معلومات كاملة عن المرسل

### 2. **تجربة مستخدم محسنة**
- واجهة أكثر وضوحاً ومعلوماتية
- تحديث سلس وفوري للحالة
- رسائل تأكيد واضحة

### 3. **موثوقية أعلى**
- زر "تحديد الكل كمقروء" يعمل بشكل صحيح
- تحديث متسق للحالة
- معالجة أخطاء محسنة

### 4. **قابلية التتبع**
- إمكانية معرفة مصدر كل إشعار
- تسجيل العمليات للمراجعة
- معلومات مفيدة للدعم التقني

## ملاحظات للمطورين

### 1. **هيكل البيانات الجديد**
```typescript
// الإشعار مع معلومات المرسل
{
  id: number,
  title: string,
  content: string,
  type: string,
  read: boolean,
  createdAt: string,
  isGroupNotification: boolean,
  sender: {
    id: number,
    name: string,
    username: string
  }
}
```

### 2. **API Response Format**
جميع APIs الإشعارات تُرجع الآن معلومات المرسل:
```json
{
  "notifications": [...],
  "unreadCount": 5,
  "pagination": {...}
}
```

### 3. **Database Changes**
- الإشعارات الجماعية تحتوي الآن على `userId` للمرسل
- لا تغيير في هيكل قاعدة البيانات الأساسي
- استخدام العلاقات الموجودة لجلب معلومات المرسل

### 4. **Performance Considerations**
- استخدام `select` محدد لجلب معلومات المرسل فقط
- تجنب جلب بيانات غير ضرورية
- استخدام الفهارس الموجودة

## التحديثات المستقبلية المقترحة

### 1. **تحسينات إضافية لمعلومات المرسل**
- إضافة صورة المرسل (avatar)
- عرض دور المرسل
- روابط لملف المرسل الشخصي

### 2. **تحسينات زر "تحديد الكل كمقروء"**
- إضافة تأكيد قبل التنفيذ
- خيارات متقدمة (تحديد حسب النوع، التاريخ، إلخ)
- إحصائيات مفصلة عن العملية

### 3. **ميزات جديدة**
- فلترة الإشعارات حسب المرسل
- تجميع الإشعارات حسب المرسل
- إعدادات تفضيلات الإشعارات لكل مرسل

## الخلاصة

تم بنجاح:
1. ✅ **إضافة معلومات المرسل** لجميع الإشعارات (فردية وجماعية)
2. ✅ **إصلاح زر "تحديد الكل كمقروء"** ليعمل بشكل صحيح
3. ✅ **تحسين التصميم والتجربة** للمستخدمين
4. ✅ **إضافة تسجيل وتتبع** للعمليات

النظام الآن أكثر شفافية وموثوقية ووضوحاً للمستخدمين! 🎉

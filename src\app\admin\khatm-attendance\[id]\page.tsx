'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// تم استبدال مكونات Select بعنصر select الأساسي
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { PlusCircle, Trash2, Edit, ArrowRight, Loader2, BookOpen, Users, CalendarIcon, BarChart } from 'lucide-react';
import Image from 'next/image';
import { toast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useRouter, useParams } from 'next/navigation';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

type Student = {
  id: number;
  name: string;
};

type KhatmSession = {
  id: number;
  title: string;
  date: string;
  teacher: {
    name: string;
  };
  surah: {
    name: string;
  } | null;
};

type Attendance = {
  id: number;
  studentId: number;
  status: string;
  note: string | null;
  student: {
    id: number;
    name: string;
  };
  images: {
    id: number;
    imageUrl: string;
  }[];
};

export default function KhatmAttendancePage() {
  const router = useRouter();
  const params = useParams();
  // استخدام useParams للحصول على معرف الجلسة
  const sessionId = params?.id ? String(params.id) : '';

  const [session, setSession] = useState<KhatmSession | null>(null);
  const [attendances, setAttendances] = useState<Attendance[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [availableStudents, setAvailableStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState({
    session: false,
    attendances: false,
    students: false,
    submit: false
  });
  const [formData, setFormData] = useState({
    id: '',
    studentId: '',
    status: 'PRESENT',
    note: '',
    imageUrls: [] as string[]
  });
  const [isEditing, setIsEditing] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    fetchSession();
    fetchAttendances();
    fetchStudents();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  useEffect(() => {
    // تحديث قائمة الطلاب المتاحين (الذين لم يتم تسجيل حضورهم بعد)
    if (Array.isArray(students) && students.length > 0) {
      if (Array.isArray(attendances) && attendances.length > 0) {
        const attendedStudentIds = attendances.map(a => a.studentId);
        const available = students.filter(s => !attendedStudentIds.includes(s.id));
        setAvailableStudents(available);
      } else {
        setAvailableStudents(students);
      }
    } else {
      setAvailableStudents([]);
    }
  }, [students, attendances]);

  const fetchSession = async () => {
    setLoading(prev => ({ ...prev, session: true }));
    try {
      const response = await fetch(`/api/khatm-sessions?id=${sessionId}`);
      if (!response.ok) throw new Error('فشل في جلب معلومات المجلس');
      const data = await response.json();
      if (data.data && data.data.length > 0) {
        setSession(data.data[0]);
      } else {
        throw new Error('مجلس الختم غير موجود');
      }
    } catch (error) {
      console.error('Error fetching khatm session:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب معلومات المجلس',
        variant: 'destructive',
      });
      router.push('/admin/khatm-sessions');
    } finally {
      setLoading(prev => ({ ...prev, session: false }));
    }
  };

  const fetchAttendances = async () => {
    setLoading(prev => ({ ...prev, attendances: true }));
    try {
      const response = await fetch(`/api/khatm-attendance?khatmSessionId=${sessionId}`);
      if (!response.ok) throw new Error('فشل في جلب سجلات الحضور');
      const data = await response.json();
      setAttendances(data.data || []);
    } catch (error) {
      console.error('Error fetching attendances:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب سجلات الحضور',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, attendances: false }));
    }
  };

  const fetchStudents = async () => {
    setLoading(prev => ({ ...prev, students: true }));
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('فشل في جلب الطلاب');
      const data = await response.json();
      console.log('Students data:', data);

      // التأكد من أن البيانات مصفوفة
      if (Array.isArray(data)) {
        setStudents(data);
      } else if (data && Array.isArray(data.data)) {
        setStudents(data.data);
      } else if (data && Array.isArray(data.students)) {
        // التعامل مع البيانات من API الطلاب
        setStudents(data.students);
      } else {
        console.error('Invalid students data format:', data);
        setStudents([]);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب الطلاب',
        variant: 'destructive',
      });
      setStudents([]);
    } finally {
      setLoading(prev => ({ ...prev, students: false }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    setFormData({
      id: '',
      studentId: '',
      status: 'PRESENT',
      note: '',
      imageUrls: []
    });
    setUploadedImages([]);
    setIsEditing(false);
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    const uploadedUrls: string[] = [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'khatm');

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`فشل في رفع الملف ${file.name}`);
        }

        const result = await response.json();
        uploadedUrls.push(result.data.filePath);
      }

      setUploadedImages(prev => [...prev, ...uploadedUrls]);
      setFormData(prev => ({
        ...prev,
        imageUrls: [...prev.imageUrls, ...uploadedUrls]
      }));

      toast({
        title: 'نجاح',
        description: `تم رفع ${uploadedUrls.length} صورة بنجاح`,
      });
    } catch (error) {
      console.error('Error uploading files:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في رفع الصور',
        variant: 'destructive',
      });
    } finally {
      setUploading(false);
      // إعادة تعيين قيمة حقل الملف
      e.target.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(prev => ({ ...prev, submit: true }));

    try {
      const payload = {
        ...formData,
        khatmSessionId: sessionId,
        imageUrls: uploadedImages
      };

      if (!isEditing && !payload.studentId) {
        throw new Error('يرجى اختيار الطالب');
      }

      const url = '/api/khatm-attendance';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: isEditing ? 'تم تحديث سجل الحضور بنجاح' : 'تم تسجيل الحضور بنجاح',
        });
        resetForm();
        setShowForm(false);
        fetchAttendances();
      } else {
        throw new Error(result.error || 'حدث خطأ أثناء حفظ سجل الحضور');
      }
    } catch (error) {
      console.error('Error saving attendance:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ سجل الحضور',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, submit: false }));
    }
  };

  const handleEdit = (attendance: Attendance) => {
    setFormData({
      id: attendance.id.toString(),
      studentId: attendance.studentId.toString(),
      status: attendance.status,
      note: attendance.note || '',
      imageUrls: attendance.images.map(img => img.imageUrl)
    });
    setUploadedImages(attendance.images.map(img => img.imageUrl));
    setIsEditing(true);
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف سجل الحضور هذا؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/khatm-attendance?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم حذف سجل الحضور بنجاح',
        });
        fetchAttendances();
      } else {
        const result = await response.json();
        throw new Error(result.error || 'حدث خطأ أثناء حذف سجل الحضور');
      }
    } catch (error) {
      console.error('Error deleting attendance:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حذف سجل الحضور',
        variant: 'destructive',
      });
    }
  };

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'PRESENT': 'حاضر',
      'ABSENT': 'غائب',
      'EXCUSED': 'غائب بعذر'
    };
    return statusMap[status] || status;
  };

  return (
    <ProtectedRoute requiredPermission="admin.khatm-attendance.view">
      <div className="container mx-auto py-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <Card className="shadow-md border-t-4 border-t-[var(--primary-color)]">
        <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-[#f8fffd] to-white">
          <div>
            <div className="flex gap-2 mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/admin/khatm-sessions')}
                className="text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)]/10"
              >
                <ArrowRight className="ml-2 h-4 w-4" />
                العودة إلى مجالس الختم
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/admin/khatm-progress/${sessionId}`)}
                className="text-amber-600 border-amber-600 hover:bg-amber-50"
              >
                <BarChart className="ml-2 h-4 w-4" />
                متابعة تقدم الحفظ
              </Button>
            </div>
            <CardTitle className="text-2xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">
              {loading.session ? 'جاري التحميل...' : session ? `حضور مجلس: ${session.title}` : 'مجلس الختم'}
            </CardTitle>
            {session && (
              <div className="text-sm text-muted-foreground mt-2 mr-3 flex flex-wrap gap-4">
                <span className="flex items-center">
                  <CalendarIcon className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                  التاريخ: {format(new Date(session.date), 'PPP', { locale: ar })}
                </span>
                <span className="flex items-center">
                  <Users className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                  المعلم: {session.teacher.name}
                </span>
                {session.surah && (
                  <span className="flex items-center">
                    <BookOpen className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                    السورة: {session.surah.name}
                  </span>
                )}
              </div>
            )}
          </div>
          <Button
            onClick={() => {
              resetForm();
              setShowForm(true);
            }}
            disabled={loading.students}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] transition-colors"
          >
            {loading.students ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                جاري التحميل...
              </>
            ) : (
              <>
                <PlusCircle className="ml-2 h-4 w-4" />
                تسجيل حضور طالب
              </>
            )}
          </Button>
        </CardHeader>
        <CardContent>
          {showForm && (
            <form onSubmit={handleSubmit} className="space-y-4 mb-6 p-6 border rounded-lg bg-[#f8fffd] shadow-sm">
              <h3 className="text-xl font-bold text-[var(--primary-color)] mb-4">{isEditing ? 'تعديل سجل الحضور' : 'تسجيل حضور طالب جديد'}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="studentId">الطالب</Label>
                  {/* استخدام قائمة منسدلة بسيطة بدلاً من مكون Select المعقد */}
                  <select
                    id="studentId"
                    name="studentId"
                    value={formData.studentId}
                    onChange={(e) => handleSelectChange('studentId', e.target.value)}
                    disabled={isEditing}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="" disabled selected={!formData.studentId}>اختر الطالب</option>
                    {isEditing ? (
                      <option value={formData.studentId}>
                        {Array.isArray(students) && students.length > 0
                          ? (students.find(s => s.id.toString() === formData.studentId)?.name || 'غير معروف')
                          : 'غير معروف'}
                      </option>
                    ) : Array.isArray(availableStudents) && availableStudents.length > 0 ? (
                      availableStudents.map(student => (
                        <option key={student.id} value={student.id.toString()}>
                          {student.name}
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>لا يوجد طلاب متاحين</option>
                    )}
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">حالة الحضور</Label>
                  {/* استخدام قائمة منسدلة بسيطة بدلاً من مكون Select المعقد */}
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={(e) => handleSelectChange('status', e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="PRESENT">حاضر</option>
                    <option value="ABSENT">غائب</option>
                    <option value="EXCUSED">غائب بعذر</option>
                  </select>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="note">ملاحظات</Label>
                  <Textarea
                    id="note"
                    name="note"
                    value={formData.note}
                    onChange={handleInputChange}
                    rows={2}
                  />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="images">صور الحضور</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="images"
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleFileUpload}
                      disabled={uploading}
                      className="flex-1"
                    />
                    {uploading && <span className="text-sm">جاري الرفع...</span>}
                  </div>
                  {uploadedImages.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                      {uploadedImages.map((url, index) => (
                        <div key={index} className="relative">
                          <Image
                            src={url}
                            alt={`صورة ${index + 1}`}
                            width={200}
                            height={100}
                            className="w-full h-24 object-cover rounded-md"
                          />
                          <Button
                            variant="destructive"
                            size="sm"
                            className="absolute top-1 left-1 w-6 h-6 p-0"
                            onClick={() => {
                              setUploadedImages(prev => prev.filter((_, i) => i !== index));
                              setFormData(prev => ({
                                ...prev,
                                imageUrls: prev.imageUrls.filter((_, i) => i !== index)
                              }));
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => { setShowForm(false); resetForm(); }}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={loading.submit}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  {loading.submit ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : isEditing ? 'تحديث' : 'تسجيل'}
                </Button>
              </div>
            </form>
          )}

          {loading.attendances ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-[var(--primary-color)]" />
              <p className="mt-2 text-muted-foreground">جاري تحميل سجلات الحضور...</p>
            </div>
          ) : attendances.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <div className="flex flex-col items-center justify-center">
                <Users className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-1">لا توجد سجلات حضور لهذا المجلس</h3>
                <p className="text-sm text-gray-500 mb-4">قم بتسجيل حضور الطلاب للبدء</p>
                {Array.isArray(availableStudents) && availableStudents.length > 0 && (
                  <Button
                    onClick={() => { resetForm(); setShowForm(true); }}
                    className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                  >
                    <PlusCircle className="ml-2 h-4 w-4" />
                    تسجيل حضور طالب
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader className="bg-[var(--primary-color)]/10">
                <TableRow>
                  <TableHead className="text-[var(--primary-color)] font-bold">الطالب</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold">حالة الحضور</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold">ملاحظات</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold">الصور</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {attendances.map(attendance => (
                  <TableRow key={attendance.id}>
                    <TableCell>{attendance.student.name}</TableCell>
                    <TableCell>{getStatusLabel(attendance.status)}</TableCell>
                    <TableCell>{attendance.note || '-'}</TableCell>
                    <TableCell>
                      {attendance.images.length > 0 ? (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Image className="ml-1 h-4 w-4" alt="صورة" src="/images/image-icon.png" width={16} height={16} />
                              {attendance.images.length} صورة
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-3xl">
                            <DialogHeader>
                              <DialogTitle>صور حضور {attendance.student.name}</DialogTitle>
                            </DialogHeader>
                            <div className="grid grid-cols-2 gap-2 mt-4">
                              {attendance.images.map((image, index) => (
                                <Image
                                  key={image.id}
                                  src={image.imageUrl}
                                  alt={`صورة ${index + 1}`}
                                  width={400}
                                  height={300}
                                  className="w-full rounded-md"
                                />
                              ))}
                            </div>
                          </DialogContent>
                        </Dialog>
                      ) : (
                        <span>لا توجد صور</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(attendance)}
                          className="text-blue-600 border-blue-600 hover:bg-blue-50"
                          title="تعديل سجل الحضور"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(attendance.id)}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                          title="حذف سجل الحضور"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      </div>
    </ProtectedRoute>
  );
}

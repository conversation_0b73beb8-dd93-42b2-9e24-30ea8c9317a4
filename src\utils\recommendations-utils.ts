/**
 * نظام التوصيات الآلية
 * هذا الملف يحتوي على وظائف لتحليل بيانات الطلاب وإنشاء توصيات آلية
 */

// نظام التوصيات الآلية

// أنواع التوصيات
export enum RecommendationType {
  ACADEMIC = 'academic',
  ATTENDANCE = 'attendance',
  BEHAVIOR = 'behavior',
  GENERAL = 'general'
}

// واجهة التوصية
export interface Recommendation {
  type: RecommendationType;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  actionItems?: string[];
}

// واجهة بيانات الطالب للتحليل
export interface StudentAnalysisData {
  // بيانات الامتحانات
  exams?: {
    averageScore?: number;
    recentScores?: number[];
    trend?: 'improving' | 'declining' | 'stable';
    passingRate?: number;
    subjectPerformance?: Record<string, number>;
  };
  // بيانات الحضور
  attendance?: {
    rate?: number;
    consecutiveAbsences?: number;
    excusedAbsences?: number;
    unexcusedAbsences?: number;
  };
  // بيانات القرآن
  quran?: {
    memorization?: number;
    tajweed?: number;
    recitation?: number;
    progress?: number;
  };
  // بيانات أخرى
  other?: Record<string, unknown>;
}

// واجهة بيانات الفصل للتحليل
export interface ClassAnalysisData {
  // إحصائيات الفصل
  stats?: {
    averageScore?: number;
    passingRate?: number;
    attendanceRate?: number;
    topPerformers?: number;
    lowPerformers?: number;
  };
  // أداء المواد
  subjects?: {
    name: string;
    averageScore: number;
    passingRate: number;
  }[];
  // بيانات أخرى
  other?: Record<string, unknown>;
}

// واجهة بيانات المعلم للتحليل
export interface TeacherAnalysisData {
  // إحصائيات المعلم
  stats?: {
    averageStudentScore?: number;
    passingRate?: number;
    topPerformingClasses?: string[];
    challengingClasses?: string[];
  };
  // بيانات أخرى
  other?: Record<string, unknown>;
}

/**
 * إنشاء توصيات للطالب بناءً على بيانات التحليل
 * @param data بيانات تحليل الطالب
 * @returns قائمة التوصيات
 */
export const generateStudentRecommendations = (data: StudentAnalysisData): Recommendation[] => {
  const recommendations: Recommendation[] = [];

  try {
    // توصيات بناءً على أداء الامتحانات
    if (data.exams) {
      // إذا كان متوسط الدرجات منخفضًا
      if (data.exams.averageScore !== undefined && data.exams.averageScore < 6) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'تحسين الأداء الأكاديمي',
          description: 'متوسط درجات الطالب منخفض ويحتاج إلى دعم إضافي.',
          priority: 'high',
          actionItems: [
            'جدولة جلسات دعم إضافية',
            'تقديم مواد تعليمية إضافية',
            'التواصل مع ولي الأمر لمناقشة خطة تحسين'
          ]
        });
      }

      // إذا كان هناك تراجع في الأداء
      if (data.exams.trend === 'declining') {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'معالجة تراجع الأداء',
          description: 'لوحظ تراجع في أداء الطالب في الامتحانات الأخيرة.',
          priority: 'high',
          actionItems: [
            'تحديد أسباب التراجع من خلال مقابلة الطالب',
            'مراجعة المواد التي يواجه فيها صعوبة',
            'تقديم دعم مستهدف في المجالات الضعيفة'
          ]
        });
      }

      // تحديد المواد التي يحتاج فيها إلى تحسين
      if (data.exams.subjectPerformance) {
        const weakSubjects = Object.entries(data.exams.subjectPerformance)
          .filter(([, score]) => score < 6)
          .map(([subject]) => subject);

        if (weakSubjects.length > 0) {
          recommendations.push({
            type: RecommendationType.ACADEMIC,
            title: 'تحسين الأداء في مواد محددة',
            description: `يحتاج الطالب إلى تحسين أدائه في المواد التالية: ${weakSubjects.join('، ')}`,
            priority: 'medium',
            actionItems: weakSubjects.map(subject => `تقديم دعم إضافي في مادة ${subject}`)
          });
        }
      }
    }

    // توصيات بناءً على الحضور
    if (data.attendance) {
      // إذا كانت نسبة الحضور منخفضة
      if (data.attendance.rate !== undefined && data.attendance.rate < 80) {
        recommendations.push({
          type: RecommendationType.ATTENDANCE,
          title: 'تحسين نسبة الحضور',
          description: `نسبة حضور الطالب منخفضة (${data.attendance.rate.toFixed(1)}%)`,
          priority: data.attendance.rate < 70 ? 'high' : 'medium',
          actionItems: [
            'التواصل مع ولي الأمر لمناقشة أسباب الغياب',
            'وضع خطة لتحسين الحضور',
            'متابعة الحضور بشكل أسبوعي'
          ]
        });
      }

      // إذا كان هناك غيابات متتالية
      if (data.attendance.consecutiveAbsences !== undefined && data.attendance.consecutiveAbsences >= 3) {
        recommendations.push({
          type: RecommendationType.ATTENDANCE,
          title: 'معالجة الغيابات المتتالية',
          description: `الطالب لديه ${data.attendance.consecutiveAbsences} أيام غياب متتالية`,
          priority: 'high',
          actionItems: [
            'الاتصال بولي الأمر فورًا',
            'التحقق من سبب الغياب المتتالي',
            'وضع خطة للعودة إلى الدراسة'
          ]
        });
      }
    }

    // توصيات بناءً على أداء القرآن
    if (data.quran) {
      // إذا كان مستوى الحفظ أو التجويد منخفضًا
      if ((data.quran.memorization !== undefined && data.quran.memorization < 6) ||
          (data.quran.tajweed !== undefined && data.quran.tajweed < 6)) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'تحسين أداء القرآن الكريم',
          description: 'يحتاج الطالب إلى تحسين مستواه في حفظ أو تجويد القرآن الكريم',
          priority: 'medium',
          actionItems: [
            'تخصيص وقت إضافي للمراجعة',
            'تقديم دروس إضافية في التجويد',
            'توفير موارد صوتية للاستماع والتقليد'
          ]
        });
      }

      // إذا كان التقدم بطيئًا
      if (data.quran.progress !== undefined && data.quran.progress < 50) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'تسريع تقدم حفظ القرآن',
          description: 'معدل تقدم الطالب في حفظ القرآن بطيء',
          priority: 'medium',
          actionItems: [
            'مراجعة خطة الحفظ الحالية',
            'تقسيم المهام إلى أجزاء أصغر وأكثر قابلية للإدارة',
            'تحديد أهداف قصيرة المدى لتحفيز التقدم'
          ]
        });
      }
    }

    // توصيات عامة
    if (recommendations.length === 0) {
      // إذا كان الأداء جيدًا بشكل عام
      if ((data.exams?.averageScore !== undefined && data.exams.averageScore >= 8) ||
          (data.attendance?.rate !== undefined && data.attendance.rate >= 90)) {
        recommendations.push({
          type: RecommendationType.GENERAL,
          title: 'الحفاظ على الأداء الممتاز',
          description: 'الطالب يظهر أداءً ممتازًا، يجب تشجيعه على الاستمرار',
          priority: 'low',
          actionItems: [
            'تقديم تعزيز إيجابي للطالب',
            'النظر في تحديات إضافية لتنمية المهارات',
            'تشجيع الطالب على مساعدة زملائه'
          ]
        });
      }
    }

    return recommendations;
  } catch (error) {
    console.error('Error generating student recommendations:', error);
    return [];
  }
};

/**
 * إنشاء توصيات للفصل بناءً على بيانات التحليل
 * @param data بيانات تحليل الفصل
 * @returns قائمة التوصيات
 */
export const generateClassRecommendations = (data: ClassAnalysisData): Recommendation[] => {
  const recommendations: Recommendation[] = [];

  try {
    // توصيات بناءً على الأداء العام للفصل
    if (data.stats) {
      // إذا كان متوسط الدرجات منخفضًا
      if (data.stats.averageScore !== undefined && data.stats.averageScore < 7) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'تحسين الأداء الأكاديمي للفصل',
          description: `متوسط درجات الفصل منخفض (${data.stats.averageScore.toFixed(1)})`,
          priority: 'high',
          actionItems: [
            'مراجعة أساليب التدريس المستخدمة',
            'تقديم دروس تقوية إضافية',
            'تحليل أسباب انخفاض الأداء'
          ]
        });
      }

      // إذا كانت نسبة النجاح منخفضة
      if (data.stats.passingRate !== undefined && data.stats.passingRate < 70) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'رفع نسبة النجاح في الفصل',
          description: `نسبة النجاح في الفصل منخفضة (${data.stats.passingRate.toFixed(1)}%)`,
          priority: 'high',
          actionItems: [
            'تحديد الطلاب المعرضين للرسوب وتقديم دعم مستهدف',
            'مراجعة صعوبة الامتحانات والتقييمات',
            'تنويع أساليب التدريس لتناسب مختلف أنماط التعلم'
          ]
        });
      }

      // إذا كانت نسبة الحضور منخفضة
      if (data.stats.attendanceRate !== undefined && data.stats.attendanceRate < 85) {
        recommendations.push({
          type: RecommendationType.ATTENDANCE,
          title: 'تحسين نسبة الحضور في الفصل',
          description: `نسبة الحضور في الفصل منخفضة (${data.stats.attendanceRate.toFixed(1)}%)`,
          priority: 'medium',
          actionItems: [
            'تحليل أنماط الغياب وتحديد الأيام/الفترات الأكثر تأثرًا',
            'التواصل مع أولياء الأمور بشأن أهمية الحضور المنتظم',
            'تنفيذ نظام حوافز للحضور المنتظم'
          ]
        });
      }

      // إذا كان هناك عدد كبير من الطلاب ذوي الأداء المنخفض
      if (data.stats.lowPerformers !== undefined && data.stats.lowPerformers > 5) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'دعم الطلاب ذوي الأداء المنخفض',
          description: `يوجد ${data.stats.lowPerformers} طلاب بحاجة إلى دعم إضافي`,
          priority: 'high',
          actionItems: [
            'تشكيل مجموعات دعم صغيرة',
            'تخصيص وقت إضافي للطلاب المتعثرين',
            'تطوير خطط تعلم فردية للطلاب المتعثرين'
          ]
        });
      }
    }

    // توصيات بناءً على أداء المواد
    if (data.subjects && data.subjects.length > 0) {
      const lowPerformingSubjects = data.subjects.filter(subject => subject.averageScore < 6);
      if (lowPerformingSubjects.length > 0) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'تحسين الأداء في مواد محددة',
          description: `الفصل يواجه صعوبة في المواد التالية: ${lowPerformingSubjects.map(s => s.name).join('، ')}`,
          priority: 'medium',
          actionItems: lowPerformingSubjects.map(subject => `مراجعة طرق تدريس مادة ${subject.name} وتقديم موارد إضافية`)
        });
      }
    }

    return recommendations;
  } catch (error) {
    console.error('Error generating class recommendations:', error);
    return [];
  }
};

/**
 * إنشاء توصيات للمعلم بناءً على بيانات التحليل
 * @param data بيانات تحليل المعلم
 * @returns قائمة التوصيات
 */
export const generateTeacherRecommendations = (data: TeacherAnalysisData): Recommendation[] => {
  const recommendations: Recommendation[] = [];

  try {
    // توصيات بناءً على أداء الطلاب
    if (data.stats) {
      // إذا كان متوسط درجات الطلاب منخفضًا
      if (data.stats.averageStudentScore !== undefined && data.stats.averageStudentScore < 7) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'تحسين متوسط درجات الطلاب',
          description: `متوسط درجات الطلاب منخفض (${data.stats.averageStudentScore.toFixed(1)})`,
          priority: 'high',
          actionItems: [
            'مراجعة أساليب التدريس وتنويعها',
            'تطوير مواد تعليمية إضافية',
            'تقديم تغذية راجعة أكثر تفصيلاً للطلاب'
          ]
        });
      }

      // إذا كانت نسبة النجاح منخفضة
      if (data.stats.passingRate !== undefined && data.stats.passingRate < 75) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'رفع نسبة النجاح',
          description: `نسبة نجاح الطلاب منخفضة (${data.stats.passingRate.toFixed(1)}%)`,
          priority: 'high',
          actionItems: [
            'تحديد الطلاب المعرضين للرسوب وتقديم دعم مستهدف',
            'مراجعة صعوبة الامتحانات والتقييمات',
            'تنفيذ استراتيجيات تعليمية متنوعة'
          ]
        });
      }

      // إذا كان هناك فصول تواجه تحديات
      if (data.stats.challengingClasses && data.stats.challengingClasses.length > 0) {
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'دعم الفصول ذات الأداء المنخفض',
          description: `الفصول التالية تواجه تحديات: ${data.stats.challengingClasses.join('، ')}`,
          priority: 'medium',
          actionItems: [
            'تخصيص وقت إضافي للفصول التي تواجه تحديات',
            'تحليل أسباب انخفاض الأداء في هذه الفصول',
            'تطوير استراتيجيات تدريس مخصصة لكل فصل'
          ]
        });
      }
    }

    return recommendations;
  } catch (error) {
    console.error('Error generating teacher recommendations:', error);
    return [];
  }
};

/**
 * تحليل بيانات الامتحان وإنشاء توصيات
 * @param examData بيانات الامتحان
 * @returns توصيات بناءً على نتائج الامتحان
 */
export interface ExamAnalysisData {
  summary: {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    passRate: number;
    averageGrade: number;
    highestGrade: number;
    lowestGrade: number;
    [key: string]: unknown;
  };
  gradeDistribution?: {
    excellent: number;
    veryGood: number;
    good: number;
    fair: number;
    poor: number;
    veryPoor: number;
    [key: string]: unknown;
  };
  classeAnalysis?: Array<{
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    averageGrade: number;
    [key: string]: unknown;
  }>;
  teacherAnalysis?: Array<{
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    averageGrade: number;
    [key: string]: unknown;
  }>;
  difficultyAnalysis?: Array<{
    level: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    pendingAnswers: number;
    averagePoints: number;
    [key: string]: unknown;
  }>;
  questionTypeAnalysis?: Array<{
    type: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    pendingAnswers: number;
    averagePoints: number;
    [key: string]: unknown;
  }>;
  genderAnalysis?: {
    MALE: {
      totalStudents: number;
      passedStudents: number;
      failedStudents: number;
      excellentStudents: number;
      pendingStudents: number;
      averageGrade: number;
      [key: string]: unknown;
    };
    FEMALE: {
      totalStudents: number;
      passedStudents: number;
      failedStudents: number;
      excellentStudents: number;
      pendingStudents: number;
      averageGrade: number;
      [key: string]: unknown;
    };
    [key: string]: unknown;
  };
}

export const analyzeExamAndGenerateRecommendations = (examData: ExamAnalysisData): Recommendation[] => {
  const recommendations: Recommendation[] = [];

  try {
    if (!examData || !examData.summary) {
      return recommendations;
    }

    // الحصول على أسماء أنواع الأسئلة بالعربية
    const getQuestionTypeLabel = (type: string): string => {
      const labels: Record<string, string> = {
        MULTIPLE_CHOICE: 'اختيار من متعدد',
        TRUE_FALSE: 'صح أو خطأ',
        SHORT_ANSWER: 'إجابة قصيرة',
        ESSAY: 'مقال',
        MATCHING: 'مطابقة',
        FILL_BLANK: 'ملء الفراغات',
        ORDERING: 'ترتيب'
      };
      return labels[type] || type;
    };

    // الحصول على أسماء مستويات الصعوبة بالعربية
    const getDifficultyLevelLabel = (level: string): string => {
      const labels: Record<string, string> = {
        EASY: 'سهل',
        MEDIUM: 'متوسط',
        HARD: 'صعب',
        VERY_HARD: 'صعب جداً'
      };
      return labels[level] || level;
    };

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { summary, gradeDistribution, classeAnalysis, teacherAnalysis, difficultyAnalysis, questionTypeAnalysis, genderAnalysis } = examData;

    // توصيات بناءً على نسبة النجاح
    if (summary.passRate < 70) {
      recommendations.push({
        type: RecommendationType.ACADEMIC,
        title: 'تحسين نسبة النجاح في الامتحان',
        description: `نسبة النجاح منخفضة (${summary.passRate.toFixed(1)}%)`,
        priority: 'high',
        actionItems: [
          'مراجعة مستوى صعوبة الامتحان',
          'تقديم دروس تقوية في المواضيع التي واجه فيها الطلاب صعوبة',
          'النظر في إعادة شرح المفاهيم الأساسية'
        ]
      });
    } else if (summary.passRate > 95) {
      recommendations.push({
        type: RecommendationType.ACADEMIC,
        title: 'مراجعة مستوى صعوبة الامتحان',
        description: `نسبة النجاح مرتفعة جداً (${summary.passRate.toFixed(1)}%)، مما قد يشير إلى أن الامتحان سهل جداً`,
        priority: 'medium',
        actionItems: [
          'زيادة مستوى صعوبة الأسئلة في الامتحانات القادمة',
          'إضافة أسئلة تتطلب مهارات تفكير عليا',
          'تنويع أنماط الأسئلة'
        ]
      });
    }

    // توصيات بناءً على متوسط الدرجات
    if (summary.averageGrade < 5) {
      recommendations.push({
        type: RecommendationType.ACADEMIC,
        title: 'معالجة انخفاض متوسط الدرجات',
        description: `متوسط الدرجات منخفض (${summary.averageGrade.toFixed(1)})`,
        priority: 'high',
        actionItems: [
          'تحديد المفاهيم التي يواجه فيها الطلاب صعوبة',
          'تقديم دروس تقوية مستهدفة',
          'مراجعة طرق التدريس المستخدمة'
        ]
      });
    }

    // توصيات بناءً على مستويات الصعوبة
    if (difficultyAnalysis && difficultyAnalysis.length > 0) {
      // تحديد المستويات التي يواجه فيها الطلاب صعوبة
      const difficultLevels = difficultyAnalysis.filter((level) => {
        const totalAnswers = level.correctAnswers + level.incorrectAnswers;
        return totalAnswers > 0 && (level.correctAnswers / totalAnswers) < 0.5;
      });

      if (difficultLevels.length > 0) {
        const levelNames = difficultLevels.map(level => getDifficultyLevelLabel(level.level)).join('، ');
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'معالجة الأسئلة الصعبة',
          description: `الطلاب يواجهون صعوبة في الإجابة على أسئلة المستويات التالية: ${levelNames}`,
          priority: 'medium',
          actionItems: [
            'مراجعة المفاهيم المرتبطة بالأسئلة الصعبة',
            'تقديم أمثلة إضافية وتدريبات على هذه المستويات',
            'تعديل طريقة تدريس هذه المفاهيم في المستقبل'
          ]
        });
      }

      // التحقق من توزيع مستويات الصعوبة
      const totalQuestions = difficultyAnalysis.reduce((sum, level) => sum + level.totalAnswers, 0);
      if (totalQuestions > 0) {
        const easyLevel = difficultyAnalysis.find(level => level.level === 'EASY');
        const hardLevels = difficultyAnalysis.filter(level => level.level === 'HARD' || level.level === 'VERY_HARD');

        if (easyLevel && (easyLevel.totalAnswers / totalQuestions) > 0.7) {
          recommendations.push({
            type: RecommendationType.ACADEMIC,
            title: 'تحسين توزيع مستويات الصعوبة',
            description: `نسبة الأسئلة السهلة مرتفعة جداً (${((easyLevel.totalAnswers / totalQuestions) * 100).toFixed(1)}%)`,
            priority: 'low',
            actionItems: [
              'زيادة نسبة الأسئلة متوسطة وعالية الصعوبة',
              'تنويع مستويات الصعوبة بشكل أفضل في الامتحانات القادمة'
            ]
          });
        }

        const hardQuestionsPercentage = hardLevels.reduce((sum, level) => sum + level.totalAnswers, 0) / totalQuestions;
        if (hardQuestionsPercentage > 0.5) {
          recommendations.push({
            type: RecommendationType.ACADEMIC,
            title: 'مراجعة نسبة الأسئلة الصعبة',
            description: `نسبة الأسئلة الصعبة وشديدة الصعوبة مرتفعة (${(hardQuestionsPercentage * 100).toFixed(1)}%)`,
            priority: 'medium',
            actionItems: [
              'تقليل نسبة الأسئلة الصعبة في الامتحانات القادمة',
              'تحقيق توازن أفضل بين مستويات الصعوبة المختلفة'
            ]
          });
        }
      }
    }

    // توصيات بناءً على أنواع الأسئلة
    if (questionTypeAnalysis && questionTypeAnalysis.length > 0) {
      // تحديد أنواع الأسئلة التي يواجه فيها الطلاب صعوبة
      const problematicTypes = questionTypeAnalysis.filter((type) => {
        const totalAnswers = type.correctAnswers + type.incorrectAnswers;
        return totalAnswers > 10 && (type.correctAnswers / totalAnswers) < 0.6;
      });

      if (problematicTypes.length > 0) {
        const typeNames = problematicTypes.map(type => getQuestionTypeLabel(type.type)).join('، ');
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'تحسين الأداء في أنواع محددة من الأسئلة',
          description: `الطلاب يواجهون صعوبة في أنواع الأسئلة التالية: ${typeNames}`,
          priority: 'medium',
          actionItems: [
            'تقديم تدريب إضافي على أنواع الأسئلة الصعبة',
            'مراجعة طريقة صياغة هذه الأنواع من الأسئلة',
            'تطوير استراتيجيات للإجابة على هذه الأنواع من الأسئلة'
          ]
        });
      }

      // التحقق من تنوع أنواع الأسئلة
      const totalQuestions = questionTypeAnalysis.reduce((sum, type) => sum + type.totalAnswers, 0);
      if (totalQuestions > 0) {
        const dominantType = questionTypeAnalysis.find(type => (type.totalAnswers / totalQuestions) > 0.7);
        if (dominantType) {
          recommendations.push({
            type: RecommendationType.ACADEMIC,
            title: 'تحسين تنوع أنواع الأسئلة',
            description: `نسبة أسئلة ${getQuestionTypeLabel(dominantType.type)} مرتفعة جداً (${((dominantType.totalAnswers / totalQuestions) * 100).toFixed(1)}%)`,
            priority: 'low',
            actionItems: [
              'زيادة تنوع أنواع الأسئلة في الامتحانات القادمة',
              'استخدام أنواع أسئلة مختلفة لقياس مهارات متنوعة'
            ]
          });
        }

        // التحقق من وجود أنواع أسئلة قليلة الاستخدام
        const rareTypes = questionTypeAnalysis.filter(type => (type.totalAnswers / totalQuestions) < 0.05);
        if (rareTypes.length > 0 && questionTypeAnalysis.length > 3) {
          const rareTypeNames = rareTypes.map(type => getQuestionTypeLabel(type.type)).join('، ');
          recommendations.push({
            type: RecommendationType.ACADEMIC,
            title: 'زيادة استخدام بعض أنواع الأسئلة',
            description: `بعض أنواع الأسئلة نادرة الاستخدام: ${rareTypeNames}`,
            priority: 'low',
            actionItems: [
              'زيادة استخدام أنواع الأسئلة المذكورة في الامتحانات القادمة',
              'تدريب الطلاب على الإجابة على مختلف أنواع الأسئلة'
            ]
          });
        }
      }
    }

    // توصيات بناءً على تحليل الفصول
    if (classeAnalysis && classeAnalysis.length > 1) {
      // حساب متوسط الدرجات لجميع الفصول
      const classAvgGrades = classeAnalysis.map(c => c.averageGrade);
      const overallClassAvg = classAvgGrades.reduce((sum, grade) => sum + grade, 0) / classAvgGrades.length;

      // تحديد الفصول ذات الأداء المنخفض
      const lowPerformingClasses = classeAnalysis.filter(c => c.averageGrade < overallClassAvg * 0.8 && c.totalStudents > 5);
      if (lowPerformingClasses.length > 0) {
        const classNames = lowPerformingClasses.map(c => c.name).join('، ');
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'دعم الفصول ذات الأداء المنخفض',
          description: `الفصول التالية تظهر أداءً منخفضاً مقارنة بالمتوسط العام: ${classNames}`,
          priority: 'high',
          actionItems: [
            'تقديم دعم إضافي للفصول المذكورة',
            'تحليل أسباب انخفاض الأداء في هذه الفصول',
            'تطوير خطط تحسين مخصصة لكل فصل'
          ]
        });
      }

      // تحديد الفصول ذات نسبة الرسوب المرتفعة
      const highFailRateClasses = classeAnalysis.filter(c =>
        c.totalStudents > 5 && (c.failedStudents / c.totalStudents) > 0.3
      );
      if (highFailRateClasses.length > 0) {
        const classNames = highFailRateClasses.map(c => c.name).join('، ');
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'معالجة ارتفاع نسبة الرسوب في بعض الفصول',
          description: `الفصول التالية تعاني من ارتفاع نسبة الرسوب: ${classNames}`,
          priority: 'high',
          actionItems: [
            'تحديد الطلاب المعرضين للرسوب وتقديم دعم مستهدف',
            'مراجعة طرق التدريس المستخدمة في هذه الفصول',
            'تنظيم دروس تقوية إضافية'
          ]
        });
      }
    }

    // توصيات بناءً على تحليل المعلمين
    if (teacherAnalysis && teacherAnalysis.length > 1) {
      // حساب متوسط الدرجات لجميع المعلمين
      const teacherAvgGrades = teacherAnalysis.map(t => t.averageGrade);
      const overallTeacherAvg = teacherAvgGrades.reduce((sum, grade) => sum + grade, 0) / teacherAvgGrades.length;

      // تحديد المعلمين ذوي الأداء المنخفض
      const lowPerformingTeachers = teacherAnalysis.filter(t => t.averageGrade < overallTeacherAvg * 0.8 && t.totalStudents > 10);
      if (lowPerformingTeachers.length > 0) {
        const teacherNames = lowPerformingTeachers.map(t => t.name).join('، ');
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'دعم المعلمين ذوي الأداء المنخفض',
          description: `طلاب المعلمين التاليين يظهرون أداءً منخفضاً مقارنة بالمتوسط العام: ${teacherNames}`,
          priority: 'high',
          actionItems: [
            'تقديم دعم وتدريب إضافي للمعلمين المذكورين',
            'مراجعة طرق التدريس المستخدمة',
            'تبادل الخبرات مع المعلمين ذوي الأداء المرتفع'
          ]
        });
      }

      // تحديد المعلمين ذوي الأداء المرتفع
      const highPerformingTeachers = teacherAnalysis.filter(t => t.averageGrade > overallTeacherAvg * 1.2 && t.totalStudents > 10);
      if (highPerformingTeachers.length > 0) {
        const teacherNames = highPerformingTeachers.map(t => t.name).join('، ');
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'الاستفادة من خبرات المعلمين المتميزين',
          description: `طلاب المعلمين التاليين يظهرون أداءً متميزاً: ${teacherNames}`,
          priority: 'medium',
          actionItems: [
            'تنظيم جلسات لتبادل الخبرات مع باقي المعلمين',
            'توثيق الممارسات الناجحة المستخدمة',
            'الاستفادة من هؤلاء المعلمين في تدريب زملائهم'
          ]
        });
      }
    }

    // توصيات بناءً على تحليل الجنس
    if (genderAnalysis && genderAnalysis.MALE && genderAnalysis.FEMALE) {
      const maleAvg = genderAnalysis.MALE.averageGrade;
      const femaleAvg = genderAnalysis.FEMALE.averageGrade;
      const gradeDiff = Math.abs(maleAvg - femaleAvg);

      if (gradeDiff > 1.5) {
        const betterGender = maleAvg > femaleAvg ? 'الذكور' : 'الإناث';
        const worseGender = maleAvg > femaleAvg ? 'الإناث' : 'الذكور';
        recommendations.push({
          type: RecommendationType.ACADEMIC,
          title: 'معالجة الفجوة في الأداء بين الجنسين',
          description: `هناك فرق كبير في متوسط الدرجات بين ${betterGender} (${Math.max(maleAvg, femaleAvg).toFixed(1)}) و${worseGender} (${Math.min(maleAvg, femaleAvg).toFixed(1)})`,
          priority: 'medium',
          actionItems: [
            `تقديم دعم إضافي للطلاب من فئة ${worseGender}`,
            'تحليل أسباب الفجوة في الأداء بين الجنسين',
            'مراجعة طرق التدريس للتأكد من ملاءمتها لجميع الطلاب'
          ]
        });
      }
    }

    return recommendations;
  } catch (error) {
    console.error('Error analyzing exam and generating recommendations:', error);
    return [];
  }
};

/**
 * تنسيق التوصيات كنص منسق
 * @param recommendations قائمة التوصيات
 * @param format صيغة التنسيق (text, html, markdown)
 * @returns نص منسق يحتوي على التوصيات
 */
export const formatRecommendations = (
  recommendations: Recommendation[],
  format: 'text' | 'html' | 'markdown' = 'text'
): string => {
  if (!recommendations || recommendations.length === 0) {
    return format === 'html'
      ? '<p>لا توجد توصيات متاحة.</p>'
      : format === 'markdown'
        ? '*لا توجد توصيات متاحة.*'
        : 'لا توجد توصيات متاحة.';
  }

  // ترتيب التوصيات حسب الأولوية
  const sortedRecommendations = [...recommendations].sort((a, b) => {
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    return priorityOrder[a.priority] - priorityOrder[b.priority];
  });

  if (format === 'html') {
    let html = '<div class="recommendations">';
    html += '<h3>التوصيات والإجراءات المقترحة</h3>';

    sortedRecommendations.forEach(rec => {
      const priorityClass = `priority-${rec.priority}`;
      html += `<div class="recommendation ${priorityClass}">`;
      html += `<h4>${rec.title}</h4>`;
      html += `<p>${rec.description}</p>`;

      if (rec.actionItems && rec.actionItems.length > 0) {
        html += '<ul>';
        rec.actionItems.forEach(item => {
          html += `<li>${item}</li>`;
        });
        html += '</ul>';
      }

      html += '</div>';
    });

    html += '</div>';
    return html;
  } else if (format === 'markdown') {
    let markdown = '## التوصيات والإجراءات المقترحة\n\n';

    sortedRecommendations.forEach(rec => {
      const prioritySymbol = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟠' : '🟢';
      markdown += `### ${prioritySymbol} ${rec.title}\n\n`;
      markdown += `${rec.description}\n\n`;

      if (rec.actionItems && rec.actionItems.length > 0) {
        markdown += '**الإجراءات المقترحة:**\n\n';
        rec.actionItems.forEach(item => {
          markdown += `- ${item}\n`;
        });
        markdown += '\n';
      }
    });

    return markdown;
  } else {
    // نص عادي
    let text = 'التوصيات والإجراءات المقترحة:\n\n';

    sortedRecommendations.forEach(rec => {
      const prioritySymbol = rec.priority === 'high' ? '(!!)' : rec.priority === 'medium' ? '(!)' : '(i)';
      text += `${prioritySymbol} ${rec.title}\n`;
      text += `${rec.description}\n`;

      if (rec.actionItems && rec.actionItems.length > 0) {
        text += 'الإجراءات المقترحة:\n';
        rec.actionItems.forEach(item => {
          text += `- ${item}\n`;
        });
      }

      text += '\n';
    });

    return text;
  }
};

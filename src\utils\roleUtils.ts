// دالة مساعدة لتحديد الدور الأساسي بناءً على الأدوار الموجودة في قاعدة البيانات

interface Role {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  isSystem: boolean;
  isActive: boolean;
}

/**
 * تحديد الدور الأساسي بناءً على الدور المحدد والأدوار المتاحة
 * @param selectedRole الدور المحدد
 * @param allRoles جميع الأدوار المتاحة
 * @returns الدور الأساسي المناسب
 */
export function determineBaseRole(
  selectedRole: Role,
  allRoles: Role[]
): 'ADMIN' | 'EMPLOYEE' | 'TEACHER' | 'STUDENT' | 'PARENT' | 'PENDING' {
  const roleName = selectedRole.name.toUpperCase();
  const roleDisplayName = selectedRole.displayName.toUpperCase();
  const roleDescription = (selectedRole.description || '').toUpperCase();

  // البحث في اسم الدور والاسم المعروض والوصف
  const searchText = `${roleName} ${roleDisplayName} ${roleDescription}`;

  // تصنيف الأدوار الموجودة في قاعدة البيانات
  const adminRoles = allRoles.filter(r => {
    const rName = r.name.toUpperCase();
    const rDisplay = r.displayName.toUpperCase();
    const rDesc = (r.description || '').toUpperCase();
    return rName.includes('ADMIN') || rDisplay.includes('مدير') || rDisplay.includes('إدار') || rDesc.includes('مدير') || rDesc.includes('إدار');
  });
  
  const teacherRoles = allRoles.filter(r => {
    const rName = r.name.toUpperCase();
    const rDisplay = r.displayName.toUpperCase();
    const rDesc = (r.description || '').toUpperCase();
    return rName.includes('TEACHER') || rDisplay.includes('معلم') || rDisplay.includes('مدرس') || rDisplay.includes('أستاذ') || rDesc.includes('معلم') || rDesc.includes('مدرس') || rDesc.includes('أستاذ');
  });
  
  const studentRoles = allRoles.filter(r => {
    const rName = r.name.toUpperCase();
    const rDisplay = r.displayName.toUpperCase();
    const rDesc = (r.description || '').toUpperCase();
    return rName.includes('STUDENT') || rDisplay.includes('طالب') || rDisplay.includes('تلميذ') || rDesc.includes('طالب') || rDesc.includes('تلميذ');
  });
  
  const parentRoles = allRoles.filter(r => {
    const rName = r.name.toUpperCase();
    const rDisplay = r.displayName.toUpperCase();
    const rDesc = (r.description || '').toUpperCase();
    return rName.includes('PARENT') || rDisplay.includes('ولي') || rDisplay.includes('والد') || rDisplay.includes('أمر') || rDesc.includes('ولي') || rDesc.includes('والد') || rDesc.includes('أمر');
  });
  
  const employeeRoles = allRoles.filter(r => {
    const rName = r.name.toUpperCase();
    const rDisplay = r.displayName.toUpperCase();
    const rDesc = (r.description || '').toUpperCase();
    return rName.includes('EMPLOYEE') || rDisplay.includes('موظف') || rDisplay.includes('عامل') || rDisplay.includes('مساعد') || rDesc.includes('موظف') || rDesc.includes('عامل') || rDesc.includes('مساعد');
  });

  // تحديد الدور الأساسي بناءً على التطابق مع الأدوار الموجودة
  if (adminRoles.some(r => r.id === selectedRole.id) || roleName === 'ADMIN' || searchText.includes('ADMIN') || searchText.includes('مدير') || searchText.includes('إدار')) {
    return 'ADMIN';
  } else if (teacherRoles.some(r => r.id === selectedRole.id) || roleName === 'TEACHER' || searchText.includes('TEACHER') || searchText.includes('معلم') || searchText.includes('مدرس') || searchText.includes('أستاذ')) {
    return 'TEACHER';
  } else if (studentRoles.some(r => r.id === selectedRole.id) || roleName === 'STUDENT' || searchText.includes('STUDENT') || searchText.includes('طالب') || searchText.includes('تلميذ')) {
    return 'STUDENT';
  } else if (parentRoles.some(r => r.id === selectedRole.id) || roleName === 'PARENT' || searchText.includes('PARENT') || searchText.includes('ولي') || searchText.includes('والد') || searchText.includes('أمر')) {
    return 'PARENT';
  } else if (employeeRoles.some(r => r.id === selectedRole.id) || roleName === 'EMPLOYEE' || searchText.includes('EMPLOYEE') || searchText.includes('موظف') || searchText.includes('عامل') || searchText.includes('مساعد')) {
    return 'EMPLOYEE';
  } else {
    // للأدوار المخصصة التي لا تتطابق مع أي فئة، نتركها كموظف افتراضياً
    return 'EMPLOYEE';
  }
}

/**
 * جلب جميع الأدوار من قاعدة البيانات
 * @returns Promise<Role[]>
 */
export async function fetchAllRoles(): Promise<Role[]> {
  try {
    const response = await fetch('/api/admin/roles');
    if (!response.ok) throw new Error('فشل في جلب الأدوار');
    const data = await response.json();
    return data.roles || [];
  } catch (error) {
    console.error('Error fetching roles:', error);
    // في حالة فشل جلب الأدوار، إرجاع الأدوار الأساسية
    return [
      { id: 1, name: 'ADMIN', displayName: 'مدير النظام', isSystem: true, isActive: true },
      { id: 2, name: 'TEACHER', displayName: 'معلم', isSystem: true, isActive: true },
      { id: 3, name: 'STUDENT', displayName: 'طالب', isSystem: true, isActive: true },
      { id: 4, name: 'PARENT', displayName: 'ولي أمر', isSystem: true, isActive: true },
      { id: 5, name: 'EMPLOYEE', displayName: 'موظف', isSystem: true, isActive: true }
    ];
  }
}

export type { Role };

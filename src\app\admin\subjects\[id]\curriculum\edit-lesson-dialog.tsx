'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface CurriculumResource {
  id: number;
  title: string;
  type: string;
  url: string;
  lessonId: number;
}

interface CurriculumLesson {
  id: number;
  title: string;
  description?: string;
  order: number;
  unitId: number;
  resources: CurriculumResource[];
}

interface EditLessonDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  lesson: CurriculumLesson | null;
}

export default function EditLessonDialog({ isOpen, onCloseAction, onSuccessAction, lesson }: EditLessonDialogProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    order: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (lesson) {
      setFormData({
        title: lesson.title,
        description: lesson.description || '',
        order: lesson.order.toString()
      });
    }
  }, [lesson]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleUpdateLesson = async () => {
    if (!lesson) return;

    if (!formData.title.trim()) {
      toast.error('الرجاء إدخال عنوان الدرس');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/curriculum/lessons/${lesson.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          order: formData.order ? parseInt(formData.order) : lesson.order
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update lesson');
      }

      toast.success('تم تحديث الدرس بنجاح');
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error updating lesson:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث الدرس');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleUpdateLesson}
      disabled={
        isLoading ||
        !formData.title.trim() ||
        (!!lesson &&
          formData.title === lesson.title &&
          formData.description === (lesson.description || '') &&
          formData.order === lesson.order.toString())
      }
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري التحديث...' : 'حفظ التغييرات'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="تعديل الدرس"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
        <div className="space-y-2">
          <Label>عنوان الدرس</Label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="أدخل عنوان الدرس"
          />
        </div>

        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف الدرس"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label>الترتيب</Label>
          <Input
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="أدخل ترتيب الدرس"
            min={1}
          />
        </div>
      </div>
    </AnimatedDialog>
  );
}

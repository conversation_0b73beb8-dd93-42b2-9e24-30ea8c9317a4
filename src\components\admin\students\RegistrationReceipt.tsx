'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'react-toastify';
import {
  FaReceipt,
  FaPrint,
  FaDownload,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaHome,
  FaGraduationCap,
  FaTimes
} from 'react-icons/fa';

interface RegistrationReceiptProps {
  studentId: number;
  receiptId?: number;
  onPrint?: () => void;
  onDownload?: () => void;
  onClose?: () => void;
  className?: string;
  autoGenerate?: boolean; // إنشاء وصل جديد تلقائياً
}

interface ReceiptData {
  id: number;
  studentId: number;
  receiptNumber: string;
  issueDate: string;
  registrationFee: number;
  paymentStatus: string;
  receiptData: {
    student: {
      id: number;
      name: string;
      username: string;
      age: number;
      phone?: string;
    };
    guardian?: {
      name: string;
      phone: string;
      email?: string;
      address?: string;
    };
    classe?: {
      name: string;
    };
    schoolInfo: {
      name: string;
      description: string;
      logoUrl: string;
      address: string;
      phone: string;
      email: string;
    };
  };
  isPrinted: boolean;
  printedAt?: string;
  printedBy?: string;
  notes?: string;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export default function RegistrationReceipt({
  studentId,
  receiptId,
  onPrint,
  onDownload,
  onClose,
  className,
  autoGenerate = false
}: RegistrationReceiptProps) {
  const [receiptData, setReceiptData] = useState<ReceiptData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const receiptRef = useRef<HTMLDivElement>(null);

  // تحميل بيانات الوصل
  useEffect(() => {
    const fetchReceipt = async () => {
      try {
        setIsLoading(true);
        setError(null);

        let url = `/api/students/${studentId}/registration-receipt`;
        if (receiptId) {
          url += `?receiptId=${receiptId}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('فشل في تحميل بيانات الوصل');
        }

        const data = await response.json();

        if (data.success) {
          if (receiptId) {
            // وصل محدد
            setReceiptData(data.data);
          } else {
            // آخر وصل للتلميذ
            const receipts = data.data;
            if (receipts.length > 0) {
              setReceiptData(receipts[0]);
            } else if (autoGenerate) {
              // إنشاء وصل جديد تلقائياً
              await generateNewReceipt();
            } else {
              throw new Error('لا يوجد وصل تسجيل لهذا التلميذ');
            }
          }
        } else {
          throw new Error(data.error || 'حدث خطأ غير متوقع');
        }
      } catch (error) {
        console.error('Error fetching receipt:', error);
        setError(error instanceof Error ? error.message : 'حدث خطأ أثناء تحميل البيانات');

        if (autoGenerate && !receiptId) {
          // محاولة إنشاء وصل جديد في حالة عدم وجود وصل
          await generateNewReceipt();
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (studentId) {
      fetchReceipt();
    }
  }, [studentId, receiptId, autoGenerate]);

  // إنشاء وصل جديد
  const generateNewReceipt = async () => {
    try {
      setIsGenerating(true);

      const response = await fetch(`/api/students/${studentId}/registration-receipt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          registrationFee: 0, // لا توجد رسوم تسجيل
          paymentStatus: 'PAID', // مدفوع تلقائياً لأنه لا توجد رسوم
          notes: 'وصل تسجيل مجاني - لا توجد رسوم'
        })
      });

      const data = await response.json();

      if (response.ok) {
        // إعادة تحميل بيانات الوصل الجديد
        const receiptResponse = await fetch(`/api/students/${studentId}/registration-receipt?receiptId=${data.data.id}`);
        const receiptData = await receiptResponse.json();

        if (receiptData.success) {
          setReceiptData(receiptData.data);
          setError(null);
          toast.success('تم إنشاء وصل التسجيل بنجاح');
        }
      } else {
        throw new Error(data.error || 'فشل في إنشاء وصل التسجيل');
      }
    } catch (error) {
      console.error('Error generating receipt:', error);
      setError(error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء الوصل');
      toast.error('فشل في إنشاء وصل التسجيل');
    } finally {
      setIsGenerating(false);
    }
  };

  // طباعة الوصل
  const handlePrint = async () => {
    if (receiptRef.current && receiptData) {
      // تسجيل أن الوصل تم طباعته
      try {
        await fetch(`/api/students/${studentId}/registration-receipt`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            receiptId: receiptData.id,
            markAsPrinted: true
          })
        });
      } catch (error) {
        console.error('Error marking receipt as printed:', error);
      }

      // طباعة الوصل
      const printContent = receiptRef.current.innerHTML;
      const originalContent = document.body.innerHTML;

      document.body.innerHTML = `
        <div style="direction: rtl; font-family: Arial, sans-serif;">
          <style>
            @media print {
              @page {
                size: A4;
                margin: 1cm;
              }
              body {
                margin: 0;
                padding: 0;
                font-size: 11px;
                line-height: 1.3;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }
              .no-print { display: none !important; }
              .print-only { display: block !important; }

              /* إخفاء العناصر غير المطلوبة */
              button { display: none !important; }
              .flex.gap-3.justify-between { display: none !important; }

              /* تنسيق الوصل للطباعة - نصف صفحة */
              .max-w-4xl {
                max-width: 100% !important;
                width: 100% !important;
                margin: 0 !important;
                height: 50vh !important;
                page-break-inside: avoid !important;
              }

              /* رأس الوصل مضغوط */
              .bg-gradient-to-r {
                background: #169b88 !important;
                color: white !important;
                padding: 0.4cm !important;
                margin-bottom: 0.3cm !important;
                border-radius: 4px !important;
              }

              /* شعار أصغر */
              .w-16.h-16 {
                width: 2cm !important;
                height: 2cm !important;
              }

              /* محتوى مضغوط */
              .p-6 {
                padding: 0.4cm !important;
                font-size: 10px !important;
              }

              .space-y-6 > * + * { margin-top: 0.2cm !important; }
              .space-y-4 > * + * { margin-top: 0.15cm !important; }
              .space-y-3 > * + * { margin-top: 0.1cm !important; }
              .space-y-2 > * + * { margin-top: 0.08cm !important; }

              /* النصوص مضغوطة */
              .text-2xl { font-size: 14px !important; }
              .text-xl { font-size: 12px !important; }
              .text-lg { font-size: 11px !important; }
              .text-sm { font-size: 9px !important; }
              .text-xs { font-size: 8px !important; }

              /* الشبكة مضغوطة */
              .grid {
                display: grid !important;
                gap: 0.2cm !important;
              }

              .grid-cols-1 { grid-template-columns: 1fr !important; }
              .md\\:grid-cols-2 { grid-template-columns: 1fr 1fr !important; }

              /* الحدود والخلفيات */
              .bg-green-50, .bg-gray-50 {
                background: #f9f9f9 !important;
                border: 1px solid #ddd !important;
                padding: 0.2cm !important;
                border-radius: 3px !important;
              }

              /* الفواصل */
              hr {
                border: none !important;
                border-top: 1px solid #ddd !important;
                margin: 0.15cm 0 !important;
              }

              /* الأيقونات */
              svg {
                width: 10px !important;
                height: 10px !important;
                color: #169b88 !important;
              }

              /* إخفاء العناصر الطويلة */
              .opacity-80, .opacity-90 {
                display: none !important;
              }

              /* تجنب تقسيم الصفحة */
              .grid, .space-y-6 > *, .space-y-4 > *, .space-y-3 > * {
                page-break-inside: avoid !important;
              }
            }
          </style>
          ${printContent}
        </div>
      `;

      window.print();
      document.body.innerHTML = originalContent;
      window.location.reload();
    }
    onPrint?.();
  };

  // تحميل الوصل
  const handleDownload = async () => {
    if (!receiptData) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // استيراد دالة التصدير المشتركة
      const { exportToPdf } = await import('@/utils/export-utils');

      // إعداد بيانات الوصل للتصدير
      const receiptInfo = [
        ['رقم الوصل', receiptData.receiptNumber],
        ['تاريخ الإصدار', receiptData.issueDate]
      ];

      const studentInfo = [
        ['اسم التلميذ', receiptData.receiptData.student.name],
        ['العمر', `${receiptData.receiptData.student.age} سنة`],
        ['رقم التلميذ', receiptData.receiptData.student.id.toString()]
      ];

      if (receiptData.receiptData.classe) {
        studentInfo.push(['الفصل', receiptData.receiptData.classe.name]);
      }

      const guardianInfo = [];
      if (receiptData.receiptData.guardian) {
        guardianInfo.push(
          ['اسم ولي الأمر', receiptData.receiptData.guardian.name],
          ['هاتف ولي الأمر', receiptData.receiptData.guardian.phone]
        );

        if (receiptData.receiptData.guardian.email) {
          guardianInfo.push(['بريد ولي الأمر', receiptData.receiptData.guardian.email]);
        }

        if (receiptData.receiptData.guardian.address) {
          guardianInfo.push(['عنوان ولي الأمر', receiptData.receiptData.guardian.address]);
        }
      }

      // دمج جميع البيانات
      const allData = [
        ...receiptInfo,
        ['', ''], // فاصل
        ['=== معلومات التلميذ ===', ''],
        ...studentInfo
      ];

      // إضافة معلومات ولي الأمر إذا كانت متوفرة
      if (guardianInfo.length > 0) {
        allData.push(['', ''], ['=== معلومات ولي الأمر ===', ''], ...guardianInfo);
      }

      // تصدير الوصل
      const fileName = `وصل_تسجيل_${receiptData.receiptData.student.name.replace(/\s+/g, '_')}_${receiptData.receiptNumber}.pdf`;

      exportToPdf({
        title: `وصل تسجيل: ${receiptData.receiptData.student.name}`,
        fileName: fileName,
        tables: [
          {
            title: 'وصل التسجيل',
            headers: ['البيان', 'القيمة'],
            data: allData,
            startY: 80
          }
        ],
        additionalContent: [
          {
            text: receiptData.receiptData.schoolInfo.name,
            x: 105,
            y: 20,
            options: { align: 'center' }
          },
          {
            text: receiptData.receiptData.schoolInfo.description,
            x: 105,
            y: 28,
            options: { align: 'center' }
          },
          {
            text: receiptData.receiptData.schoolInfo.address,
            x: 105,
            y: 35,
            options: { align: 'center' }
          },
          {
            text: `📞 ${receiptData.receiptData.schoolInfo.phone} | 📧 ${receiptData.receiptData.schoolInfo.email}`,
            x: 105,
            y: 42,
            options: { align: 'center' }
          },
          {
            text: `شكراً لكم لاختيار ${receiptData.receiptData.schoolInfo.name} - هذا الوصل صالح كإثبات للتسجيل`,
            x: 105,
            y: 260,
            options: { align: 'center' }
          },
          {
            text: `تاريخ الطباعة: ${new Date().toLocaleDateString('fr-FR')} | رقم الوصل: ${receiptData.receiptNumber}`,
            x: 105,
            y: 270,
            options: { align: 'center' }
          }
        ]
      });

      toast.success('تم تحميل وصل التسجيل بنجاح');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('حدث خطأ أثناء إنشاء ملف PDF');
    }

    onDownload?.();
  };



  if (isLoading || isGenerating) {
    return (
      <Card className={`w-full max-w-4xl mx-auto ${className}`}>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            <span className="mr-3">
              {isGenerating ? 'جاري إنشاء وصل التسجيل...' : 'جاري تحميل بيانات الوصل...'}
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !receiptData) {
    return (
      <Card className={`w-full max-w-4xl mx-auto ${className}`}>
        <CardContent className="p-8">
          <div className="text-center">
            <div className="text-red-600 mb-4">
              <p>{error || 'لم يتم العثور على وصل التسجيل'}</p>
            </div>
            {autoGenerate && !receiptId && (
              <Button
                onClick={generateNewReceipt}
                className="bg-[var(--primary-color)] hover:bg-[#0d7e6d]"
                disabled={isGenerating}
              >
                {isGenerating ? 'جاري الإنشاء...' : 'إنشاء وصل جديد'}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      {/* أزرار التحكم */}
      <div className="flex gap-3 justify-between items-center mb-4 no-print">
        <div className="flex gap-3">
          <Button
            onClick={handleDownload}
            variant="outline"
            className="flex items-center gap-2"
          >
            <FaDownload />
            تحميل PDF
          </Button>
          <Button
            onClick={handlePrint}
            className="flex items-center gap-2 bg-[var(--primary-color)] hover:bg-[#0d7e6d]"
          >
            <FaPrint />
            طباعة الوصل
          </Button>
        </div>
        {onClose && (
          <Button
            onClick={onClose}
            variant="outline"
            className="flex items-center gap-2"
          >
            <FaTimes />
            إغلاق
          </Button>
        )}
      </div>

      {/* وصل التسجيل */}
      <Card ref={receiptRef} className="print:shadow-none receipt-border">
        {/* رأس الوصل */}
        <CardHeader className="bg-gradient-to-r from-[var(--primary-color)] to-[#0d7e6d] text-white print:bg-white print:text-black print:border-b-2 print:border-[var(--primary-color)]">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* شعار المدرسة */}
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center print:border-2 print:border-[var(--primary-color)]">
                <img
                  src={receiptData.receiptData.schoolInfo.logoUrl}
                  alt="شعار المدرسة"
                  className="w-12 h-12 object-contain"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/logo.svg';
                  }}
                />
              </div>
              <div className="text-left">
                <CardTitle className="text-2xl font-bold print:text-[var(--primary-color)]">
                  {receiptData.receiptData.schoolInfo.name}
                </CardTitle>
                <p className="text-sm opacity-90 print:opacity-100 print:text-gray-600">
                  {receiptData.receiptData.schoolInfo.description}
                </p>
                <p className="text-xs opacity-80 print:opacity-100 print:text-gray-500">
                  {receiptData.receiptData.schoolInfo.address}
                </p>
                <p className="text-xs opacity-80 print:opacity-100 print:text-gray-500">
                  📞 {receiptData.receiptData.schoolInfo.phone} | 📧 {receiptData.receiptData.schoolInfo.email}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-white text-[var(--primary-color)] px-4 py-2 rounded-lg print:bg-gray-100 print:border print:border-[var(--primary-color)]">
                <p className="text-xl font-bold">وصل تسجيل</p>
                <p className="text-sm text-green-600 font-semibold">🎉 مجاني مكتمل</p>
                <p className="text-xs">رقم: {receiptData.receiptNumber}</p>
                <p className="text-xs">{receiptData.issueDate}</p>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-4">
          <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
            {/* معلومات التلميذ */}
            <div className="flex justify-between"><span className="font-semibold">التلميذ:</span><span>{receiptData.receiptData.student.name}</span></div>
            <div className="flex justify-between"><span className="font-semibold">العمر:</span><span>{receiptData.receiptData.student.age} سنة</span></div>
            <div className="flex justify-between"><span className="font-semibold">الفصل:</span><span>{receiptData.receiptData.classe?.name || 'غير محدد'}</span></div>
            <div className="flex justify-between"><span className="font-semibold">رقم التلميذ:</span><span>{receiptData.receiptData.student.id}</span></div>

            {/* معلومات ولي الأمر */}
            {receiptData.receiptData.guardian && (
              <>
                <div className="col-span-2 my-1 border-t"></div>
                <div className="flex justify-between"><span className="font-semibold">ولي الأمر:</span><span>{receiptData.receiptData.guardian.name}</span></div>
                <div className="flex justify-between"><span className="font-semibold">الهاتف:</span><span>{receiptData.receiptData.guardian.phone}</span></div>
              </>
            )}
          </div>

          {/* تذييل الوصل المبسط */}
          <div className="mt-4 pt-2 border-t text-center text-xs text-gray-500">
            <p>شكراً لكم لاختيار {receiptData.receiptData.schoolInfo.name}. هذا الوصل صالح كإثبات للتسجيل.</p>
            <p>📧 {receiptData.receiptData.schoolInfo.email} | 📞 {receiptData.receiptData.schoolInfo.phone}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

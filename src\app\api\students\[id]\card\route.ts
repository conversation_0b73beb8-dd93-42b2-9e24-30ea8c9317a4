import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// GET /api/students/[id]/card - جلب بيانات بطاقة التلميذ الشاملة
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.students.card.view');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { error: permissionCheck.message || 'غير مصرح بالوصول' },
        { status: permissionCheck.status || 401 }
      );
    }

    const studentId = parseInt(params.id);

    if (isNaN(studentId)) {
      return NextResponse.json(
        { error: 'معرف التلميذ غير صحيح' },
        { status: 400 }
      );
    }

    // جلب إعدادات المدرسة
    const schoolSettings = await prisma.systemSettings.findUnique({
      where: { key: 'SITE_SETTINGS' }
    });

    const defaultSchoolInfo = {
      siteName: 'نظام برهان للقرآن الكريم',
      siteDescription: 'منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد',
      logoUrl: '/logo.svg',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+213 123 456 789',
        address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر'
      }
    };

    const schoolInfo = schoolSettings
      ? JSON.parse(schoolSettings.value)
      : defaultSchoolInfo;

    // جلب بيانات التلميذ الشاملة
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: {
        // البيانات الأساسية
        guardian: {
          select: {
            id: true,
            name: true,
            phone: true,
            email: true,
            address: true
          }
        },
        classe: {
          select: {
            id: true,
            name: true,
            description: true
          }
        },

        // الصور
        images: {
          where: { isProfilePic: true },
          select: {
            id: true,
            imageUrl: true,
            description: true,
            uploadDate: true
          },
          take: 1
        },



        // تقدم الحفظ الحالي
        quranProgress: {
          select: {
            id: true,
            surahId: true,
            startVerse: true,
            endVerse: true,
            memorization: true,
            tajweed: true,
            startDate: true,
            completionDate: true,
            surah: {
              select: {
                id: true,
                name: true,
                number: true,
                totalAyahs: true
              }
            }
          },
          orderBy: {
            startDate: 'desc'
          },
          take: 10 // آخر 10 سجلات حفظ
        },

        // النقاط والمكافآت
        points: {
          select: {
            id: true,
            points: true,
            date: true,
            reason: true
          },
          orderBy: {
            date: 'desc'
          },
          take: 5 // آخر 5 نقاط
        },

        rewards: {
          select: {
            id: true,
            date: true,
            reward: {
              select: {
                id: true,
                name: true,
                description: true,
                type: true
              }
            }
          },
          orderBy: {
            date: 'desc'
          },
          take: 5 // آخر 5 مكافآت
        },

        // الحضور (إحصائيات)
        attendance: {
          select: {
            id: true,
            date: true,
            status: true
          },
          orderBy: {
            date: 'desc'
          },
          take: 30 // آخر 30 يوم
        },

        // المدفوعات
        payments: {
          select: {
            id: true,
            amount: true,
            date: true,
            status: true,
            receiptNumber: true
          },
          orderBy: {
            date: 'desc'
          },
          take: 5 // آخر 5 مدفوعات
        }
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'التلميذ غير موجود' },
        { status: 404 }
      );
    }

    // حساب إحصائيات الحضور
    const attendanceStats = {
      present: student.attendance.filter(a => a.status === 'PRESENT').length,
      absent: student.attendance.filter(a => a.status === 'ABSENT').length,
      excused: student.attendance.filter(a => a.status === 'EXCUSED').length,
      total: student.attendance.length
    };
    attendanceStats.rate = attendanceStats.total > 0
      ? Math.round((attendanceStats.present / attendanceStats.total) * 100)
      : 0;

    // حساب إحصائيات الحفظ
    const quranStats = {
      totalVerses: student.quranProgress.reduce((sum, progress) =>
        sum + (progress.endVerse - progress.startVerse + 1), 0),
      averageMemorization: student.quranProgress.length > 0
        ? Math.round(student.quranProgress.reduce((sum, progress) =>
            sum + progress.memorization, 0) / student.quranProgress.length)
        : 0,
      averageTajweed: student.quranProgress.length > 0
        ? Math.round(student.quranProgress.reduce((sum, progress) =>
            sum + progress.tajweed, 0) / student.quranProgress.length)
        : 0,
      completedSurahs: student.quranProgress.filter(p => p.completionDate).length
    };

    // حساب إجمالي النقاط
    const totalPoints = student.points.reduce((sum, point) => sum + point.points, 0);

    // تنسيق البيانات للعرض
    const cardData = {
      // البيانات الأساسية
      id: student.id,
      username: student.username,
      name: student.name,
      age: student.age,
      phone: student.phone,
      createdAt: new Date(student.createdAt).toLocaleDateString('fr-FR'),

      // ولي الأمر
      guardian: student.guardian ? {
        ...student.guardian,
        phone: student.guardian.phone
      } : null,

      // الفصل
      classe: student.classe,

      // الصورة الشخصية
      profileImage: student.images.length > 0 ? {
        ...student.images[0],
        uploadDate: new Date(student.images[0].uploadDate).toLocaleDateString('fr-FR')
      } : null,



      // تقدم الحفظ
      quranProgress: student.quranProgress.map(progress => ({
        ...progress,
        startDate: new Date(progress.startDate).toLocaleDateString('fr-FR'),
        completionDate: progress.completionDate
          ? new Date(progress.completionDate).toLocaleDateString('fr-FR')
          : null
      })),

      // النقاط
      points: student.points.map(point => ({
        ...point,
        date: new Date(point.date).toLocaleDateString('fr-FR')
      })),

      // المكافآت
      rewards: student.rewards.map(reward => ({
        ...reward,
        date: new Date(reward.date).toLocaleDateString('fr-FR')
      })),

      // المدفوعات
      payments: student.payments.map(payment => ({
        ...payment,
        date: new Date(payment.date).toLocaleDateString('fr-FR')
      })),

      // الإحصائيات
      stats: {
        attendance: attendanceStats,
        quran: quranStats,
        totalPoints: totalPoints,
        totalRewards: student.rewards.length
      },

      // معلومات المدرسة
      schoolInfo: {
        name: schoolInfo.siteName,
        description: schoolInfo.siteDescription,
        logoUrl: schoolInfo.logoUrl,
        address: schoolInfo.contactInfo?.address || 'غير محدد',
        phone: schoolInfo.contactInfo?.phone || 'غير محدد',
        email: schoolInfo.contactInfo?.email || 'غير محدد'
      }
    };

    return NextResponse.json({
      success: true,
      data: cardData
    });

  } catch (error) {
    console.error('Error fetching student card data:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات بطاقة التلميذ' },
      { status: 500 }
    );
  }
}

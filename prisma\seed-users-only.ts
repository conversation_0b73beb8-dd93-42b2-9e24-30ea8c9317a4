import { PrismaClient } from '@prisma/client';
import seedUsers from './seed/users-data';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 بدء تنفيذ ملف بيانات المستخدمين فقط...');
  console.log('=' .repeat(60));
  
  try {
    // عرض الإحصائيات قبل التنفيذ
    console.log('📊 الإحصائيات قبل التنفيذ:');
    const statsBefore = await Promise.all([
      prisma.user.count(),
      prisma.student.count(),
      prisma.teacher.count(),
      prisma.parent.count()
    ]);
    
    console.log(`   👤 المستخدمين: ${statsBefore[0]}`);
    console.log(`   👦👧 الطلاب: ${statsBefore[1]}`);
    console.log(`   👨‍🏫 المعلمين: ${statsBefore[2]}`);
    console.log(`   👨‍👩‍👧‍👦 أولياء الأمور: ${statsBefore[3]}`);
    console.log('');

    // تنفيذ ملف بيانات المستخدمين
    console.log('👥 تنفيذ ملف بيانات المستخدمين...');
    await seedUsers();
    console.log('✅ تم تنفيذ ملف بيانات المستخدمين بنجاح');
    console.log('');

    // عرض الإحصائيات بعد التنفيذ
    console.log('📊 الإحصائيات بعد التنفيذ:');
    const statsAfter = await Promise.all([
      prisma.user.count(),
      prisma.student.count(),
      prisma.teacher.count(),
      prisma.parent.count()
    ]);
    
    console.log(`   👤 المستخدمين: ${statsAfter[0]} (زيادة: +${statsAfter[0] - statsBefore[0]})`);
    console.log(`   👦👧 الطلاب: ${statsAfter[1]} (زيادة: +${statsAfter[1] - statsBefore[1]})`);
    console.log(`   👨‍🏫 المعلمين: ${statsAfter[2]} (زيادة: +${statsAfter[2] - statsBefore[2]})`);
    console.log(`   👨‍👩‍👧‍👦 أولياء الأمور: ${statsAfter[3]} (زيادة: +${statsAfter[3] - statsBefore[3]})`);
    console.log('');

    // عرض تفاصيل الطلاب المضافين
    console.log('👦👧 تفاصيل الطلاب المضافين:');
    const students = await prisma.student.findMany({
      include: {
        classe: true,
        guardian: true
      },
      orderBy: {
        id: 'desc'
      },
      take: 10
    });

    students.forEach((student, index) => {
      console.log(`   ${index + 1}. ${student.name} - ${student.classe?.name || 'بدون فصل'} - ولي الأمر: ${student.guardian?.name || 'غير محدد'}`);
    });

    console.log('');
    console.log('🎉 تم تنفيذ ملف بيانات المستخدمين بنجاح!');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('❌ خطأ في تنفيذ ملف بيانات المستخدمين:', error);
    throw error;
  }
}

// تنفيذ الدالة الرئيسية
main()
  .catch((e) => {
    console.error('❌ خطأ في التنفيذ:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  });

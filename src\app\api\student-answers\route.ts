import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ActivityLogger, ActivityType } from "@/lib/activity-logger";
import { autoGradeStudentAnswer, calculateFinalGrade } from "@/utils/auto-grading-utils";
import { ExamStatus, Prisma } from "@prisma/client";
import { getToken } from "@/lib/auth";

// GET /api/student-answers
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const examPointId = searchParams.get('examPointId');
    const examQuestionId = searchParams.get('examQuestionId');

    if (!examPointId && !examQuestionId) {
      return NextResponse.json({
        error: 'يجب توفير معرف نقطة الامتحان أو معرف سؤال الامتحان',
        success: false
      }, { status: 400 });
    }

    const where: Prisma.StudentAnswerWhereInput = {};

    if (examPointId) {
      where.examPointId = parseInt(examPointId);
    }

    if (examQuestionId) {
      where.examQuestionId = parseInt(examQuestionId);
    }

    const studentAnswers = await prisma.studentAnswer.findMany({
      where,
      include: {
        examPoint: {
          include: {
            student: {
              select: {
                id: true,
                name: true
              }
            },
            exam: {
              select: {
                id: true,
                evaluationType: true,
                month: true
              }
            }
          }
        },
        examQuestion: {
          include: {
            question: {
              include: {
                options: true,
                answers: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      data: studentAnswers,
      success: true,
      message: 'تم جلب إجابات الطلاب بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student answers:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب إجابات الطلاب',
      success: false
    }, { status: 500 });
  }
}

// POST /api/student-answers
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { examPointId, examQuestionId, answer } = body;

    // التحقق من البيانات المطلوبة
    if (!examPointId || !examQuestionId || !answer) {
      return NextResponse.json({
        error: 'معرف نقطة الامتحان ومعرف سؤال الامتحان والإجابة مطلوبة',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود نقطة الامتحان وسؤال الامتحان
    const examPoint = await prisma.exam_points.findUnique({
      where: { id: parseInt(examPointId) },
      include: {
        student: true,
        exam: true
      }
    });

    if (!examPoint) {
      return NextResponse.json({
        error: 'نقطة الامتحان غير موجودة',
        success: false
      }, { status: 404 });
    }

    const examQuestion = await prisma.examQuestion.findUnique({
      where: { id: parseInt(examQuestionId) },
      include: {
        question: {
          include: {
            options: true,
            answers: true
          }
        }
      }
    });

    if (!examQuestion) {
      return NextResponse.json({
        error: 'سؤال الامتحان غير موجود',
        success: false
      }, { status: 404 });
    }

    // التحقق من أن سؤال الامتحان ينتمي إلى نفس الامتحان
    if (examQuestion.examId !== examPoint.examId) {
      return NextResponse.json({
        error: 'سؤال الامتحان لا ينتمي إلى نفس الامتحان',
        success: false
      }, { status: 400 });
    }

    // التحقق من عدم وجود إجابة سابقة
    const existingAnswer = await prisma.studentAnswer.findUnique({
      where: {
        examPointId_examQuestionId: {
          examPointId: parseInt(examPointId),
          examQuestionId: parseInt(examQuestionId)
        }
      }
    });

    if (existingAnswer) {
      return NextResponse.json({
        error: 'توجد إجابة سابقة لهذا السؤال',
        success: false
      }, { status: 400 });
    }

    // استخدام نظام التصحيح الآلي المحسن
    const studentAnswerData = {
      examPointId: parseInt(examPointId),
      examQuestionId: parseInt(examQuestionId),
      answer
    };

    // تصحيح إجابة الطالب آلياً
    const gradedAnswer = autoGradeStudentAnswer(studentAnswerData, examQuestion);

    // استخراج نتائج التصحيح
    const { isCorrect, points, feedback } = gradedAnswer;

    // إنشاء إجابة الطالب
    const studentAnswer = await prisma.studentAnswer.create({
      data: {
        examPointId: parseInt(examPointId),
        examQuestionId: parseInt(examQuestionId),
        answer,
        isCorrect,
        points,
        feedback
      },
      include: {
        examPoint: {
          include: {
            student: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        examQuestion: {
          include: {
            question: true
          }
        }
      }
    });

    // إذا كانت الإجابة تم تصحيحها تلقائيًا، قم بتحديث الدرجة
    if (isCorrect !== null) {
      // الحصول على جميع أسئلة الامتحان
      const allExamQuestions = await prisma.examQuestion.findMany({
        where: { examId: examPoint.examId },
        include: {
          question: true
        }
      });

      // الحصول على جميع إجابات الطالب
      const allStudentAnswers = await prisma.studentAnswer.findMany({
        where: {
          examPointId: parseInt(examPointId)
        }
      });

      // حساب مجموع النقاط القصوى لجميع الأسئلة
      const totalMaxPoints = allExamQuestions.reduce((sum, eq) =>
        sum + (eq.points || eq.question?.points || 0), 0);

      // تحويل allExamQuestions إلى النوع المتوافق مع ExamQuestion[]
      const examQuestionsForGrading = allExamQuestions.map(eq => ({
        id: eq.id,
        examId: eq.examId,
        questionId: eq.questionId,
        order: eq.order,
        points: eq.points,
        question: {
          id: eq.question.id,
          text: eq.question.text,
          type: eq.question.type,
          difficultyLevel: eq.question.difficultyLevel,
          points: eq.question.points,
          options: [], // نضيف مصفوفة فارغة لأن الخيارات غير مطلوبة في حساب الدرجة
          answers: []  // نضيف مصفوفة فارغة لأن الإجابات غير مطلوبة في حساب الدرجة
        }
      }));

      // استخدام وظيفة حساب الدرجة النهائية المحسنة
      const gradeResult = calculateFinalGrade(
        allStudentAnswers,
        examQuestionsForGrading,
        totalMaxPoints,
        examPoint.exam.passingPoints
      );

      // استخراج نتائج حساب الدرجة
      const { finalGrade, status, completionPercentage, needsManualGrading } = gradeResult;

      // تحديد ما إذا كان يجب تحديث حالة الامتحان
      let shouldUpdateStatus = false;

      // إذا تمت الإجابة على نسبة كبيرة من الأسئلة وتم تصحيحها
      if (completionPercentage >= 90 && !needsManualGrading) {
        shouldUpdateStatus = true;
      }

      // تحديث نقطة الامتحان
      await prisma.exam_points.update({
        where: { id: parseInt(examPointId) },
        data: {
          grade: finalGrade,
          ...(shouldUpdateStatus ? { status } : {})
        }
      });
    }

    // تسجيل النشاط
    try {
      const jwtToken = request.cookies.get("jwtToken")?.value;
      if (jwtToken) {
        const token = await getToken(jwtToken);
        if (token) {
          await ActivityLogger.log(
            token.id,
            ActivityType.EXAM,
            `تم تقديم إجابة من الطالب ${studentAnswer.examPoint.student.name} على سؤال في الامتحان`
          );
        }
      }
    } catch (error) {
      console.error('خطأ في تسجيل النشاط:', error);
      // لا نريد أن يفشل تسجيل الإجابة إذا فشل تسجيل النشاط
    }

    return NextResponse.json({
      data: studentAnswer,
      success: true,
      message: 'تم تسجيل إجابة الطالب بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error submitting student answer:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تسجيل إجابة الطالب',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/student-answers
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, isCorrect, points, feedback } = body;

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json({
        error: 'معرف إجابة الطالب مطلوب',
        success: false
      }, { status: 400 });
    }

    // تحديث إجابة الطالب
    const studentAnswer = await prisma.studentAnswer.update({
      where: { id: parseInt(id) },
      data: {
        isCorrect: isCorrect !== undefined ? isCorrect : undefined,
        points: points !== undefined ? parseFloat(points) : undefined,
        feedback: feedback !== undefined ? feedback : undefined
      },
      include: {
        examPoint: {
          include: {
            student: {
              select: {
                id: true,
                name: true
              }
            },
            exam: true
          }
        }
      }
    });

    // إعادة حساب الدرجة الإجمالية للامتحان
    // الحصول على جميع أسئلة الامتحان
    const allExamQuestions = await prisma.examQuestion.findMany({
      where: { examId: studentAnswer.examPoint.examId },
      include: {
        question: true
      }
    });

    // الحصول على جميع إجابات الطالب
    const allStudentAnswers = await prisma.studentAnswer.findMany({
      where: {
        examPointId: studentAnswer.examPointId
      }
    });

    // حساب مجموع النقاط الحالية
    const answeredPoints = allStudentAnswers.reduce((sum, sa) => sum + (sa.points || 0), 0);

    // حساب مجموع النقاط القصوى للأسئلة التي تمت الإجابة عليها
    const answeredMaxPoints = allStudentAnswers.reduce((sum, sa) => {
      const examQuestion = allExamQuestions.find(eq => eq.id === sa.examQuestionId);
      return sum + (examQuestion?.points || examQuestion?.question?.points || 0);
    }, 0);

    // حساب مجموع النقاط القصوى لجميع الأسئلة
    const totalMaxPoints = allExamQuestions.reduce((sum, eq) =>
      sum + (eq.points || eq.question?.points || 0), 0);

    // حساب النسبة المئوية من إجمالي الامتحان
    const completionPercentage = totalMaxPoints > 0 ? (answeredMaxPoints / totalMaxPoints) * 100 : 0;

    // تحديد ما إذا كان يجب تحديث حالة الامتحان
    let shouldUpdateStatus = false;
    let status = studentAnswer.examPoint.status;
    let finalGrade = studentAnswer.examPoint.grade.toNumber();

    // إذا تمت الإجابة على جميع الأسئلة أو على نسبة كبيرة منها (أكثر من 90%)
    if (allStudentAnswers.length === allExamQuestions.length || completionPercentage >= 90) {
      shouldUpdateStatus = true;

      // حساب الدرجة النهائية (على مقياس من 10)
      finalGrade = (answeredPoints / totalMaxPoints) * 10;

      // تحديد الحالة
      if (finalGrade >= 9) {
        status = ExamStatus.EXCELLENT;
      } else if (finalGrade >= studentAnswer.examPoint.exam.passingPoints / 10) {
        status = ExamStatus.PASSED;
      } else {
        status = ExamStatus.FAILED;
      }
    }
    // إذا تمت الإجابة على بعض الأسئلة وتم تصحيحها
    else if (allStudentAnswers.length > 0 && completionPercentage > 0) {
      // تحديث الدرجة الحالية فقط دون تغيير الحالة
      finalGrade = (answeredPoints / answeredMaxPoints) * 10;
    }

    // تحديث نقطة الامتحان
    await prisma.exam_points.update({
      where: { id: studentAnswer.examPointId },
      data: {
        grade: finalGrade,
        ...(shouldUpdateStatus ? { status } : {})
      }
    });

    // تسجيل النشاط
    try {
      const jwtToken = request.cookies.get("jwtToken")?.value;
      if (jwtToken) {
        const token = await getToken(jwtToken);
        if (token) {
          await ActivityLogger.log(
            token.id,
            ActivityType.EXAM,
            `تم تصحيح إجابة الطالب ${studentAnswer.examPoint.student.name} على سؤال في الامتحان`
          );
        }
      }
    } catch (error) {
      console.error('خطأ في تسجيل النشاط:', error);
      // لا نريد أن يفشل تحديث الإجابة إذا فشل تسجيل النشاط
    }

    return NextResponse.json({
      data: studentAnswer,
      success: true,
      message: 'تم تحديث إجابة الطالب بنجاح'
    });
  } catch (error) {
    console.error('Error updating student answer:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث إجابة الطالب',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/student-answers
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف إجابة الطالب مطلوب',
        success: false
      }, { status: 400 });
    }

    // الحصول على معلومات إجابة الطالب قبل الحذف
    const studentAnswer = await prisma.studentAnswer.findUnique({
      where: { id: parseInt(id) },
      include: {
        examPoint: {
          include: {
            student: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });

    if (!studentAnswer) {
      return NextResponse.json({
        error: 'إجابة الطالب غير موجودة',
        success: false
      }, { status: 404 });
    }

    // حذف إجابة الطالب
    await prisma.studentAnswer.delete({
      where: { id: parseInt(id) }
    });

    // تسجيل النشاط
    try {
      const jwtToken = request.cookies.get("jwtToken")?.value;
      if (jwtToken) {
        const token = await getToken(jwtToken);
        if (token) {
          await ActivityLogger.log(
            token.id,
            ActivityType.EXAM,
            `تم حذف إجابة الطالب ${studentAnswer.examPoint.student.name} على سؤال في الامتحان`
          );
        }
      }
    } catch (error) {
      console.error('خطأ في تسجيل النشاط:', error);
      // لا نريد أن يفشل حذف الإجابة إذا فشل تسجيل النشاط
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف إجابة الطالب بنجاح'
    });
  } catch (error) {
    console.error('Error deleting student answer:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف إجابة الطالب',
      success: false
    }, { status: 500 });
  }
}

'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  FaUserGraduate,
  FaExclamationCircle,
  FaCheckCircle,
  FaMoneyBillWave,
  FaFileInvoiceDollar,
  FaCreditCard,
  FaCalendarAlt
} from 'react-icons/fa'

interface Student {
  id: number
  name: string
  grade: string
  debt: number
  dueInvoices: number
  lastPaymentDate?: string
}

interface DebtSummaryCardProps {
  student: Student
  onPayClick?: (student: Student) => void
  onDetailsClick?: (student: Student) => void
  formatCurrency: (amount: number) => string
}

export default function DebtSummaryCard({ 
  student, 
  onPayClick, 
  onDetailsClick, 
  formatCurrency 
}: DebtSummaryCardProps) {
  const hasDebt = student.debt > 0
  const cardBorderClass = hasDebt ? 'border-l-red-400' : 'border-l-green-400'
  const statusBadgeClass = hasDebt 
    ? 'bg-red-100 text-red-800' 
    : 'bg-green-100 text-green-800'
  const statusText = hasDebt ? 'ديون مستحقة' : 'مدفوعة بالكامل'

  return (
    <Card className={`border-l-4 ${cardBorderClass} hover:shadow-lg transition-shadow duration-200`}>
      <CardHeader className="pb-3">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${hasDebt ? 'bg-red-100' : 'bg-green-100'}`}>
              <FaUserGraduate className={`text-lg ${hasDebt ? 'text-red-600' : 'text-green-600'}`} />
            </div>
            <div>
              <CardTitle className="text-lg font-bold text-gray-800">
                {student.name}
              </CardTitle>
              <p className="text-sm text-gray-600">الصف: {student.grade}</p>
            </div>
          </div>
          <Badge className={statusBadgeClass}>
            {statusText}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* معلومات الديون والمدفوعات */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* الديون المستحقة */}
          <div className={`p-4 rounded-lg border ${
            hasDebt 
              ? 'bg-red-50 border-red-200' 
              : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              <FaExclamationCircle className={`text-sm ${
                hasDebt ? 'text-red-600' : 'text-gray-500'
              }`} />
              <h3 className={`font-medium text-sm ${
                hasDebt ? 'text-red-800' : 'text-gray-700'
              }`}>
                المستحقات
              </h3>
            </div>
            <p className={`text-xl font-bold ${
              hasDebt ? 'text-red-700' : 'text-gray-600'
            }`}>
              {formatCurrency(student.debt)}
            </p>
            <p className={`text-xs mt-1 ${
              hasDebt ? 'text-red-600' : 'text-gray-500'
            }`}>
              {hasDebt 
                ? `${student.dueInvoices} فاتورة مستحقة` 
                : 'لا توجد ديون'
              }
            </p>
          </div>

          {/* آخر دفعة */}
          <div className="p-4 rounded-lg border bg-blue-50 border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <FaCalendarAlt className="text-sm text-blue-600" />
              <h3 className="font-medium text-sm text-blue-800">آخر دفعة</h3>
            </div>
            {student.lastPaymentDate ? (
              <>
                <p className="text-lg font-bold text-blue-700">
                  {new Date(student.lastPaymentDate).toLocaleDateString('fr-FR')}
                </p>
                <p className="text-xs text-blue-600 mt-1">تاريخ آخر دفعة</p>
              </>
            ) : (
              <>
                <p className="text-lg font-bold text-gray-500">لا يوجد</p>
                <p className="text-xs text-gray-400 mt-1">لم يتم تسجيل دفعات</p>
              </>
            )}
          </div>
        </div>

        {/* تفاصيل إضافية للديون */}
        {hasDebt && (
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <div className="flex items-center gap-2 mb-2">
              <FaFileInvoiceDollar className="text-yellow-600" />
              <h4 className="font-medium text-sm text-yellow-800">تفاصيل الديون</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-yellow-600">عدد الفواتير المستحقة:</span>
                <span className="font-bold text-yellow-800 mr-2">{student.dueInvoices}</span>
              </div>
              <div>
                <span className="text-yellow-600">متوسط الفاتورة:</span>
                <span className="font-bold text-yellow-800 mr-2">
                  {formatCurrency(student.debt / student.dueInvoices)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="flex flex-col md:flex-row gap-2 pt-3 border-t border-gray-100">
          {hasDebt && onPayClick && (
            <Button
              onClick={() => onPayClick(student)}
              className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2 flex-1"
              size="sm"
            >
              <FaCreditCard />
              دفع سريع
            </Button>
          )}
          
          {onDetailsClick && (
            <Button
              onClick={() => onDetailsClick(student)}
              variant="outline"
              className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white flex items-center gap-2 flex-1"
              size="sm"
            >
              <FaFileInvoiceDollar />
              عرض التفاصيل
            </Button>
          )}
        </div>

        {/* رسالة تحفيزية */}
        {!hasDebt && (
          <div className="bg-green-50 p-3 rounded-lg border border-green-200 text-center">
            <FaCheckCircle className="text-green-600 text-2xl mx-auto mb-2" />
            <p className="text-sm text-green-700 font-medium">
              ممتاز! جميع الفواتير مدفوعة
            </p>
            <p className="text-xs text-green-600 mt-1">
              شكراً لك على الالتزام بالمواعيد
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

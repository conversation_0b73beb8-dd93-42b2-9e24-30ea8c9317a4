"use client";
import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { FaSearch, FaUserGraduate } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';
import AddStudentDialog from '@/app/admin/components/AddStudentDialog';
import dynamic from 'next/dynamic';

// استيراد المكون بشكل ديناميكي مع تعطيل SSR
const ResponsiveTablesHandler = dynamic(
  () => import('@/components/responsive-tables-handler'),
  { ssr: false }
);

interface Student {
  id: number;
  name: string;
  username: string;
  age: number;
  phone?: string;
  classe?: {
    id: number;
    name: string;
  };
  guardian?: {
    id: number;
    name: string;
  };
  totalPoints: number;
}

const TeacherStudentsPage = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const fetchStudents = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // هنا نفترض أن هناك API يقوم بجلب الطلاب الخاصين بالمعلم المسجل دخوله
      const response = await fetch('/api/teacher-students');

      if (!response.ok) {
        throw new Error('فشل في جلب بيانات الطلاب');
      }

      const data = await response.json();
      setStudents(data.students);
      setFilteredStudents(data.students);
    } catch (err) {
      console.error('Error fetching students:', err);
      setError('حدث خطأ أثناء جلب بيانات الطلاب');
      toast.error('فشل في جلب بيانات الطلاب');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStudents();
  }, []);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredStudents(students);
    } else {
      const filtered = students.filter(student =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.username.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredStudents(filtered);
    }
  }, [searchTerm, students]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">طلابي</h1>
          <p className="text-gray-500">إدارة وعرض بيانات الطلاب الذين تقوم بتدريسهم</p>
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-64 order-2 sm:order-1">
            <Input
              placeholder="بحث عن طالب..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
          <Button
            className="bg-[var(--primary-color)] hover:bg-[#0d7e6d] w-full sm:w-auto order-1 sm:order-2"
            onClick={() => setIsAddDialogOpen(true)}
          >
            إضافة طالب جديد
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FaUserGraduate className="text-[var(--primary-color)]" />
            <span>قائمة الطلاب</span>
          </CardTitle>
          <CardDescription>
            عرض جميع الطلاب المسجلين في فصولك الدراسية
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-4">{error}</div>
          ) : filteredStudents.length === 0 ? (
            <div className="text-center text-gray-500 py-4">لا يوجد طلاب</div>
          ) : (
            <>
              <div className="responsive-table-container">
                <Table className="card-mode-table">
                  <TableHeader>
                    <TableRow>
                      <TableHead data-label="الاسم">الاسم</TableHead>
                      <TableHead data-label="العمر">العمر</TableHead>
                      <TableHead data-label="الفصل">الفصل</TableHead>
                      <TableHead data-label="ولي الأمر" className="hide-on-mobile">ولي الأمر</TableHead>
                      <TableHead data-label="النقاط">النقاط</TableHead>
                      <TableHead data-label="الإجراءات" className="actions">الإجراءات</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStudents.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell className="font-medium" data-label="الاسم">{student.name}</TableCell>
                        <TableCell data-label="العمر">{student.age} سنة</TableCell>
                        <TableCell data-label="الفصل">
                          {student.classe ? (
                            <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-blue-50 text-blue-700 border-blue-200">
                              {student.classe.name}
                            </span>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell data-label="ولي الأمر" className="hide-on-mobile">{student.guardian?.name || '-'}</TableCell>
                        <TableCell data-label="النقاط">
                          <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-green-100 text-green-800 border-green-200">
                            {student.totalPoints}
                          </span>
                        </TableCell>
                        <TableCell data-label="الإجراءات" className="actions">
                          <div className="mobile-action-buttons">
                            <Link href={`/teachers/students/${student.id}`}>
                              <Button variant="outline" size="sm" className="w-full sm:w-auto mb-2 sm:mb-0">
                                عرض التفاصيل
                              </Button>
                            </Link>
                            <Link href={`/teachers/attendance?studentId=${student.id}`}>
                              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                                تسجيل الحضور
                              </Button>
                            </Link>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <ResponsiveTablesHandler />
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Student Dialog */}
      <AddStudentDialog
        isOpen={isAddDialogOpen}
        onCloseAction={() => setIsAddDialogOpen(false)}
        onSuccessAction={() => fetchStudents()}
      />
    </div>
  );
};

export default TeacherStudentsPage;

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { InvoiceStatus } from '@prisma/client';
import { ActivityLogger, ActivityType } from '@/utils/activity-logger';

// POST /api/invoices/bulk - إنشاء فواتير متعددة دفعة واحدة
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { studentIds, amount, dueDate, month, year, description } = body;

    // التحقق من وجود الحقول المطلوبة
    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0 || !amount || !dueDate || !month || !year) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    // التحقق من صحة المبلغ
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الطلاب
    const students = await prisma.student.findMany({
      where: {
        id: {
          in: studentIds.map(id => parseInt(id.toString()))
        }
      }
    });

    if (students.length !== studentIds.length) {
      return NextResponse.json(
        { error: 'بعض الطلاب غير موجودين' },
        { status: 404 }
      );
    }

    // إنشاء الفواتير
    const invoices = await prisma.$transaction(
      studentIds.map(studentId =>
        prisma.invoice.create({
          data: {
            studentId: parseInt(studentId.toString()),
            amount,
            dueDate: new Date(dueDate),
            month: parseInt(month.toString()),
            year: parseInt(year.toString()),
            description,
            status: 'UNPAID' as InvoiceStatus
          }
        })
      )
    );

    // تسجيل نشاط إنشاء الفواتير
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.INVOICE,
          `إنشاء ${invoices.length} فاتورة جديدة بقيمة ${amount} لشهر ${month}/${year}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط إنشاء الفواتير:', error);
    }

    return NextResponse.json({
      success: true,
      count: invoices.length,
      invoices
    });
  } catch (error) {
    console.error('خطأ في إنشاء الفواتير:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الفواتير' },
      { status: 500 }
    );
  }
}

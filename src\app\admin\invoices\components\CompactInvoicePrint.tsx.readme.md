# مكون الطباعة المدمجة للفواتير

## 📋 الوصف
مكون React قابل لإعادة الاستخدام لطباعة وتحميل الفواتير بتنسيق مدمج وصغير الحجم، مع خيارات تخصيص متقدمة.

## 🎯 الهدف
- توفير واجهة سهلة لطباعة الفواتير المدمجة
- تخصيص خيارات الطباعة حسب الحاجة
- توفير الورق والحبر
- دعم أحجام مختلفة من الفواتير

## 🔧 الاستخدام

### الاستيراد
```typescript
import CompactInvoicePrint from '@/app/admin/invoices/components/CompactInvoicePrint';
```

### الاستخدام الأساسي
```tsx
<CompactInvoicePrint
  invoiceId={123}
  invoiceAmount={5000}
  studentName="أحمد محمد"
/>
```

### الاستخدام المتقدم
```tsx
<CompactInvoicePrint
  invoiceId={123}
  invoiceAmount={5000}
  studentName="أحمد محمد"
  className="my-custom-class"
/>
```

## 📊 الخصائص (Props)

### CompactInvoicePrintProps
```typescript
interface CompactInvoicePrintProps {
  invoiceId: number;        // معرف الفاتورة (مطلوب)
  invoiceAmount: number;    // مبلغ الفاتورة (مطلوب)
  studentName: string;      // اسم الطالب (مطلوب)
  className?: string;       // فئة CSS إضافية (اختياري)
}
```

## ⚙️ خيارات التخصيص

### CompactInvoiceOptions
```typescript
interface CompactInvoiceOptions {
  size: 'thermal' | 'half-a4' | 'business-card';  // حجم الفاتورة
  includeQR: boolean;                              // تضمين رمز QR
  includeLogo: boolean;                            // تضمين الشعار
  fontSize: 'small' | 'medium' | 'large';         // حجم الخط
  layout: 'vertical' | 'horizontal';              // تخطيط الفاتورة
  paperSaving: boolean;                            // وضع توفير الورق
  highQuality: boolean;                            // جودة عالية
}
```

### الخيارات الافتراضية
```typescript
const defaultOptions = {
  size: 'thermal',
  includeQR: false,
  includeLogo: true,
  fontSize: 'medium',
  layout: 'vertical',
  paperSaving: false,
  highQuality: true
};
```

## 🎨 أحجام الفواتير

### 1. Thermal (حراري)
- **الأبعاد**: 80mm عرض × متغير طول
- **الاستخدام**: الطابعات الحرارية
- **المميزات**: سرعة عالية، استهلاك أقل
- **مناسب لـ**: نقاط البيع، الإيصالات السريعة

### 2. Half A4 (نصف A4)
- **الأبعاد**: 105mm × 148mm
- **الاستخدام**: الطابعات العادية
- **المميزات**: 4 فواتير في صفحة واحدة
- **مناسب لـ**: الفواتير الرسمية المدمجة

### 3. Business Card (بطاقة عمل)
- **الأبعاد**: 85mm × 55mm
- **الاستخدام**: فواتير مصغرة
- **المميزات**: سهولة الحمل والحفظ
- **مناسب لـ**: الإيصالات المبسطة

## 🔧 الوظائف الرئيسية

### handlePreview()
```typescript
const handlePreview = async () => {
  // فتح معاينة الفاتورة في نافذة جديدة
  // عرض HTML بدلاً من PDF للمعاينة السريعة
};
```

### handlePrint()
```typescript
const handlePrint = async () => {
  // فتح نافذة الطباعة مباشرة
  // تطبيق جميع خيارات التخصيص
};
```

### handleDownload()
```typescript
const handleDownload = async () => {
  // تحميل ملف PDF للفاتورة المدمجة
  // حفظ الملف باسم مخصص
};
```

### updateOption()
```typescript
const updateOption = (key: keyof CompactInvoiceOptions, value: any) => {
  // تحديث خيار معين في الإعدادات
  // إعادة تطبيق التغييرات فوراً
};
```

## 🎨 واجهة المستخدم

### الأزرار الرئيسية
- **معاينة**: عرض الفاتورة قبل الطباعة
- **طباعة مدمجة**: طباعة مباشرة بالإعدادات المختارة
- **تحميل**: تحميل ملف PDF
- **خيارات**: فتح/إغلاق لوحة الإعدادات

### لوحة الإعدادات
- **حجم الفاتورة**: اختيار من 3 أحجام مختلفة
- **حجم الخط**: صغير، متوسط، كبير
- **خيارات إضافية**: رمز QR، الشعار، توفير الورق، جودة عالية

### المؤشرات البصرية
- مؤشر التحميل أثناء المعالجة
- ألوان مختلفة للأزرار النشطة
- وصف الحجم المختار
- معلومات الفاتورة في الأسفل

## 📱 الاستجابة والتوافق

### التصميم المتجاوب
- تكيف مع أحجام الشاشات المختلفة
- أزرار مناسبة للمس على الأجهزة المحمولة
- تخطيط مرن للوحة الإعدادات

### التوافق مع المتصفحات
- دعم جميع المتصفحات الحديثة
- تعامل آمن مع النوافذ المنبثقة
- تحميل الملفات بطريقة متوافقة

## 🔒 الأمان والتحقق

### التحقق من البيانات
- التأكد من صحة معرف الفاتورة
- التحقق من وجود البيانات المطلوبة
- معالجة الأخطاء بشكل مناسب

### إدارة الأخطاء
```typescript
try {
  // تنفيذ العملية
} catch (error) {
  console.error('خطأ في العملية:', error);
  toast({
    title: 'خطأ',
    description: 'رسالة خطأ واضحة للمستخدم',
    variant: 'destructive'
  });
}
```

## 🎯 تحسينات الأداء

### تحميل البيانات
- تحميل البيانات عند الحاجة فقط
- تخزين مؤقت للإعدادات
- تحسين استعلامات API

### تجربة المستخدم
- مؤشرات تحميل واضحة
- رسائل تأكيد للعمليات الناجحة
- معالجة سلسة للأخطاء

## 🧪 الاختبار

### اختبار الوظائف
```bash
# اختبار المكون
npm test -- --testNamePattern="CompactInvoicePrint"

# اختبار خيارات الطباعة
npm test -- --testNamePattern="print options"

# اختبار التحميل والمعاينة
npm test -- --testNamePattern="download and preview"
```

### اختبار التكامل
- اختبار مع API الفواتير
- اختبار أحجام مختلفة من الفواتير
- اختبار على متصفحات مختلفة

## 📝 ملاحظات التطوير

### التبعيات المطلوبة
- `@/components/ui/*`: مكونات واجهة المستخدم
- `@/hooks/use-toast`: نظام الإشعارات
- `react-icons/fa`: الأيقونات

### التحسينات المستقبلية
- إضافة المزيد من أحجام الفواتير
- دعم القوالب المخصصة
- تكامل مع طابعات شبكة
- إضافة رموز QR حقيقية

### الصيانة
- مراجعة دورية للأداء
- تحديث التصميم حسب الملاحظات
- إضافة المزيد من خيارات التخصيص

## 💡 أمثلة الاستخدام

### في صفحة الفواتير
```tsx
// في جدول الفواتير
<td className="p-3">
  <CompactInvoicePrint
    invoiceId={invoice.id}
    invoiceAmount={invoice.amount}
    studentName={invoice.student.name}
  />
</td>
```

### في صفحة تفاصيل الفاتورة
```tsx
// في رأس الصفحة
<div className="flex justify-end">
  <CompactInvoicePrint
    invoiceId={invoice.id}
    invoiceAmount={invoice.amount}
    studentName={invoice.student.name}
    className="ml-4"
  />
</div>
```

### مع خيارات مخصصة
```tsx
// مع إعدادات افتراضية مختلفة
<CompactInvoicePrint
  invoiceId={invoice.id}
  invoiceAmount={invoice.amount}
  studentName={invoice.student.name}
  // يمكن إضافة خصائص إضافية في المستقبل
/>
```

/**
 * دوال مساعدة لنظام المدفوعات والفواتير
 * تحتوي على منطق العمل المشترك والحسابات المالية
 */

import prisma from '@/lib/prisma';
import { InvoiceStatus, PaymentStatus } from '@prisma/client';

// واجهات البيانات
export interface PaymentValidationData {
  studentId: number;
  amount: number;
  paymentMethod: string;
  notes?: string;
  receiptNumber?: string;
  month?: string;
}

export interface InvoiceValidationData {
  studentId?: number;
  parentId?: number;
  amount: number;
  dueDate: string;
  issueDate?: string;
  month: number;
  year: number;
  description?: string;
  type: 'INDIVIDUAL' | 'FAMILY';
}

export interface ValidationError {
  field: string;
  message: string;
}

/**
 * التحقق من صحة بيانات الدفعة
 */
export function validatePaymentData(data: PaymentValidationData): ValidationError[] {
  const errors: ValidationError[] = [];

  // التحقق من معرف التلميذ
  if (!data.studentId || !Number.isInteger(data.studentId) || data.studentId <= 0) {
    errors.push({
      field: 'studentId',
      message: 'معرف التلميذ غير صحيح'
    });
  }

  // التحقق من المبلغ
  if (!data.amount || typeof data.amount !== 'number' || data.amount <= 0) {
    errors.push({
      field: 'amount',
      message: 'مبلغ الدفعة يجب أن يكون أكبر من صفر'
    });
  } else if (data.amount > 1000000) {
    errors.push({
      field: 'amount',
      message: 'مبلغ الدفعة كبير جداً'
    });
  }

  // التحقق من طريقة الدفع
  if (!data.paymentMethod || data.paymentMethod.trim() === '') {
    errors.push({
      field: 'paymentMethod',
      message: 'طريقة الدفع مطلوبة'
    });
  }

  // التحقق من الشهر إذا تم تحديده
  if (data.month) {
    const monthRegex = /^\d{4}-\d{2}$/;
    if (!monthRegex.test(data.month)) {
      errors.push({
        field: 'month',
        message: 'صيغة الشهر غير صحيحة (يجب أن تكون YYYY-MM)'
      });
    }
  }

  return errors;
}

/**
 * التحقق من صحة بيانات الفاتورة
 */
export function validateInvoiceData(data: InvoiceValidationData): ValidationError[] {
  const errors: ValidationError[] = [];

  // التحقق من نوع الفاتورة والمتطلبات
  if (data.type === 'INDIVIDUAL') {
    if (!data.studentId) {
      errors.push({
        field: 'studentId',
        message: 'معرف التلميذ مطلوب للفواتير الفردية'
      });
    }
    if (data.parentId) {
      errors.push({
        field: 'parentId',
        message: 'لا يمكن تحديد معرف الولي للفواتير الفردية'
      });
    }
  } else if (data.type === 'FAMILY') {
    if (!data.parentId) {
      errors.push({
        field: 'parentId',
        message: 'معرف الولي مطلوب للفواتير الجماعية'
      });
    }
    if (data.studentId) {
      errors.push({
        field: 'studentId',
        message: 'لا يمكن تحديد معرف التلميذ للفواتير الجماعية'
      });
    }
  } else {
    errors.push({
      field: 'type',
      message: 'نوع الفاتورة غير صحيح'
    });
  }

  // التحقق من المبلغ
  if (!data.amount || data.amount <= 0) {
    errors.push({
      field: 'amount',
      message: 'مبلغ الفاتورة يجب أن يكون أكبر من صفر'
    });
  } else if (data.amount > 1000000) {
    errors.push({
      field: 'amount',
      message: 'مبلغ الفاتورة كبير جداً'
    });
  }

  // التحقق من التواريخ
  if (!data.dueDate) {
    errors.push({
      field: 'dueDate',
      message: 'تاريخ الاستحقاق مطلوب'
    });
  } else {
    const dueDate = new Date(data.dueDate);
    const issueDate = new Date(data.issueDate || new Date());
    
    if (isNaN(dueDate.getTime())) {
      errors.push({
        field: 'dueDate',
        message: 'تاريخ الاستحقاق غير صحيح'
      });
    } else if (dueDate <= issueDate) {
      errors.push({
        field: 'dueDate',
        message: 'تاريخ الاستحقاق يجب أن يكون بعد تاريخ الإصدار'
      });
    }
  }

  // التحقق من الشهر والسنة
  if (!data.month || data.month < 1 || data.month > 12) {
    errors.push({
      field: 'month',
      message: 'الشهر يجب أن يكون بين 1 و 12'
    });
  }

  if (!data.year || data.year < 2020 || data.year > 2030) {
    errors.push({
      field: 'year',
      message: 'السنة غير صحيحة'
    });
  }

  return errors;
}

/**
 * حساب المبلغ المدفوع لفاتورة معينة
 */
export async function calculateInvoicePaidAmount(invoiceId: number): Promise<number> {
  try {
    const result = await prisma.payment.aggregate({
      where: {
        invoiceId: invoiceId,
        status: 'PAID'
      },
      _sum: {
        amount: true
      }
    });

    return result._sum.amount || 0;
  } catch (error) {
    console.error('خطأ في حساب المبلغ المدفوع للفاتورة:', error);
    return 0;
  }
}

/**
 * تحديث حالة الفاتورة بناءً على المدفوعات
 */
export async function updateInvoiceStatus(invoiceId: number, tx?: any): Promise<InvoiceStatus> {
  try {
    const prismaClient = tx || prisma;

    // جلب بيانات الفاتورة
    const invoice = await prismaClient.invoice.findUnique({
      where: { id: invoiceId }
    });

    if (!invoice) {
      throw new Error('الفاتورة غير موجودة');
    }

    // حساب المبلغ المدفوع
    const paidAmount = await calculateInvoicePaidAmount(invoiceId);
    const now = new Date();

    // تحديد الحالة الجديدة
    let newStatus: InvoiceStatus;

    if (paidAmount >= invoice.amount) {
      newStatus = 'PAID';
    } else if (paidAmount > 0) {
      newStatus = 'PARTIALLY_PAID';
    } else if (invoice.dueDate < now) {
      newStatus = 'OVERDUE';
    } else {
      newStatus = 'UNPAID';
    }

    // تحديث الحالة إذا تغيرت
    if (newStatus !== invoice.status) {
      await prismaClient.invoice.update({
        where: { id: invoiceId },
        data: { status: newStatus }
      });
    }

    return newStatus;
  } catch (error) {
    console.error('خطأ في تحديث حالة الفاتورة:', error);
    throw error;
  }
}

/**
 * تنظيف وتطهير المدخلات
 */
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    return input.trim().replace(/[<>]/g, '');
  }
  if (typeof input === 'number') {
    return Math.abs(input);
  }
  return input;
}

/**
 * التحقق من صحة المبلغ
 */
export function validateAmount(amount: any): boolean {
  return typeof amount === 'number' && amount > 0 && amount < 1000000;
}

/**
 * التحقق من صحة التاريخ
 */
export function validateDate(date: any): boolean {
  const parsedDate = new Date(date);
  return !isNaN(parsedDate.getTime()) && parsedDate > new Date('2020-01-01');
}

/**
 * تنسيق المبلغ للعرض
 */
export function formatAmount(amount: number): string {
  return new Intl.NumberFormat('ar-DZ', {
    style: 'currency',
    currency: 'DZD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * تنسيق التاريخ للعرض
 */
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-DZ', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
}

/**
 * حساب معدل السداد
 */
export function calculatePaymentRate(totalRequired: number, totalPaid: number): number {
  if (totalRequired <= 0) return 0;
  return Math.round((totalPaid / totalRequired) * 100);
}

/**
 * تحديد حالة الدفع
 */
export function determinePaymentStatus(
  totalRequired: number, 
  totalPaid: number, 
  hasOverdueInvoices: boolean = false
): 'PAID' | 'PARTIAL' | 'UNPAID' | 'OVERDUE' {
  if (totalRequired === 0 || totalPaid >= totalRequired) {
    return 'PAID';
  } else if (totalPaid > 0) {
    return 'PARTIAL';
  } else if (hasOverdueInvoices) {
    return 'OVERDUE';
  } else {
    return 'UNPAID';
  }
}

/**
 * إنشاء رقم إيصال فريد
 */
export function generateReceiptNumber(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const time = String(now.getTime()).slice(-6);
  
  return `REC-${year}${month}${day}-${time}`;
}

/**
 * التحقق من وجود التلميذ
 */
export async function validateStudentExists(studentId: number): Promise<boolean> {
  try {
    const student = await prisma.student.findUnique({
      where: { id: studentId }
    });
    return !!student;
  } catch (error) {
    console.error('خطأ في التحقق من وجود التلميذ:', error);
    return false;
  }
}

/**
 * التحقق من وجود الولي
 */
export async function validateParentExists(parentId: number): Promise<boolean> {
  try {
    const parent = await prisma.parent.findUnique({
      where: { id: parentId }
    });
    return !!parent;
  } catch (error) {
    console.error('خطأ في التحقق من وجود الولي:', error);
    return false;
  }
}

/**
 * جلب أو إنشاء طريقة دفع
 */
export async function getOrCreatePaymentMethod(name: string) {
  try {
    let paymentMethod = await prisma.paymentMethod.findFirst({
      where: { name: name }
    });

    if (!paymentMethod) {
      paymentMethod = await prisma.paymentMethod.create({
        data: {
          name: name,
          isActive: true
        }
      });
    }

    return paymentMethod;
  } catch (error) {
    console.error('خطأ في جلب أو إنشاء طريقة الدفع:', error);
    throw error;
  }
}

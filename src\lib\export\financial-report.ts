import * as XLSX from 'xlsx';
import { FinancialReportData } from '@/lib/reports/financial-report-service';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

// تعريف خيارات التصدير المالي
export interface FinancialExportOptions {
  includeCharts?: boolean;
  separateSheets?: boolean;
  currency?: string;
  watermark?: string;
}

export class FinancialReportExporter {
  /**
   * تصدير التقرير المالي بصيغة Excel
   */
  static async exportToExcel(
    reportData: FinancialReportData,
    options: FinancialExportOptions = {}
  ): Promise<Blob> {
    try {
      // إنشاء مصنف Excel جديد
      const workbook = XLSX.utils.book_new();

      // إضافة ورقة الملخص التنفيذي
      this.addExecutiveSummarySheet(workbook, reportData);

      // إضافة ورقة تفاصيل المداخيل
      this.addIncomeDetailsSheet(workbook, reportData);

      // إضافة ورقة تفاصيل المصروفات
      this.addExpenseDetailsSheet(workbook, reportData);

      // إضافة ورقة الإحصائيات الشهرية
      this.addMonthlyStatsSheet(workbook, reportData);

      // إضافة ورقة طرق الدفع
      this.addPaymentMethodsSheet(workbook, reportData);

      // تحويل المصنف إلى buffer
      const excelBuffer = XLSX.write(workbook, {
        bookType: 'xlsx',
        type: 'array',
      });

      return new Blob([excelBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
    } catch (error) {
      console.error('خطأ في تصدير Excel:', error);
      throw new Error('فشل في تصدير التقرير المالي بصيغة Excel');
    }
  }

  /**
   * تصدير التقرير المالي بصيغة PDF
   */
  static async exportToPDF(
    reportData: FinancialReportData,
    options: FinancialExportOptions = {}
  ): Promise<Blob> {
    try {
      const formatCurrency = (amount: number) => {
        return amount.toLocaleString('ar-DZ') + ' د.ج';
      };

      // إنشاء محتوى HTML منسق للتقرير المالي
      const reportContent = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>التقرير المالي</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #169b87;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        h1 {
            color: #169b87;
            font-size: 28px;
            margin-bottom: 10px;
        }
        h2 {
            color: #2563eb;
            font-size: 20px;
            margin-top: 30px;
            margin-bottom: 15px;
            border-right: 4px solid #2563eb;
            padding-right: 10px;
        }
        .period {
            color: #666;
            font-size: 16px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        th {
            background-color: #169b87;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .summary-table th {
            background-color: #169b87;
        }
        .income-table th {
            background-color: #22c55e;
        }
        .expense-table th {
            background-color: #ef4444;
        }
        .monthly-table th {
            background-color: #8b5cf6;
        }
        .payment-table th {
            background-color: #f59e0b;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #169b87;
            color: #666;
        }
        .amount {
            font-weight: bold;
            color: #169b87;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>التقرير المالي</h1>
        <div class="period">للفترة من ${format(new Date(reportData.period.startDate), 'PPP', { locale: ar })} إلى ${format(new Date(reportData.period.endDate), 'PPP', { locale: ar })}</div>
    </div>

    <h2>الملخص التنفيذي</h2>
    <table class="summary-table">
        <tr><th>البيان</th><th>المبلغ</th></tr>
        <tr><td>الرصيد الافتتاحي</td><td class="amount">${formatCurrency(reportData.executiveSummary.openingBalance)}</td></tr>
        <tr><td>إجمالي المداخيل</td><td class="amount">${formatCurrency(reportData.executiveSummary.totalIncome)}</td></tr>
        <tr><td>إجمالي المصروفات</td><td class="amount">${formatCurrency(reportData.executiveSummary.totalExpenses)}</td></tr>
        <tr><td>صافي الربح/الخسارة</td><td class="amount">${formatCurrency(reportData.executiveSummary.netProfit)}</td></tr>
        <tr><td>الرصيد الختامي</td><td class="amount">${formatCurrency(reportData.executiveSummary.closingBalance)}</td></tr>
        <tr><td>إجمالي المعاملات</td><td>${reportData.executiveSummary.totalTransactions}</td></tr>
    </table>

    <h2>تفاصيل المداخيل</h2>
    <table class="income-table">
        <tr><th>نوع المدخول</th><th>العدد</th><th>المبلغ</th><th>النسبة</th></tr>
        <tr>
            <td>مدفوعات الطلاب</td>
            <td>${reportData.incomeDetails.studentPayments.count}</td>
            <td class="amount">${formatCurrency(reportData.incomeDetails.studentPayments.amount)}</td>
            <td>${reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%</td>
        </tr>
        <tr>
            <td>التبرعات</td>
            <td>${reportData.incomeDetails.donations.count}</td>
            <td class="amount">${formatCurrency(reportData.incomeDetails.donations.amount)}</td>
            <td>${reportData.incomeDetails.donations.percentage.toFixed(1)}%</td>
        </tr>
        <tr>
            <td>مداخيل أخرى</td>
            <td>${reportData.incomeDetails.otherIncomes.count}</td>
            <td class="amount">${formatCurrency(reportData.incomeDetails.otherIncomes.amount)}</td>
            <td>${reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%</td>
        </tr>
    </table>

    <h2>تفاصيل المصروفات</h2>
    <table class="expense-table">
        <tr><th>فئة المصروف</th><th>العدد</th><th>المبلغ</th><th>النسبة</th></tr>
        ${reportData.expenseDetails.byCategory.map(category => `
        <tr>
            <td>${category.name}</td>
            <td>${category.expensesCount}</td>
            <td class="amount">${formatCurrency(category.totalAmount)}</td>
            <td>${category.percentage.toFixed(1)}%</td>
        </tr>
        `).join('')}
    </table>

    <h2>الإحصائيات الشهرية</h2>
    <table class="monthly-table">
        <tr><th>الشهر</th><th>المداخيل</th><th>المصروفات</th><th>صافي الربح/الخسارة</th></tr>
        ${reportData.monthlyStats.map(month => `
        <tr>
            <td>${month.month}</td>
            <td class="amount">${formatCurrency(month.totalIncome)}</td>
            <td class="amount">${formatCurrency(month.expenses.amount)}</td>
            <td class="amount">${formatCurrency(month.netProfit)}</td>
        </tr>
        `).join('')}
    </table>

    <h2>طرق الدفع</h2>
    <table class="payment-table">
        <tr><th>طريقة الدفع</th><th>عدد المعاملات</th><th>المبلغ الإجمالي</th><th>النسبة</th></tr>
        ${reportData.paymentMethodStats.map((method: any) => `
        <tr>
            <td>${method.method}</td>
            <td>${method.transactionsCount}</td>
            <td class="amount">${formatCurrency(method.totalAmount)}</td>
            <td>${method.percentage.toFixed(1)}%</td>
        </tr>
        `).join('')}
    </table>

    <div class="footer">
        <p><strong>تقرير يوم ${format(new Date(), 'PPP', { locale: ar })}</strong></p>
    </div>
</body>
</html>
      `;

      return new Blob([reportContent], { type: 'text/html; charset=utf-8' });
    } catch (error) {
      console.error('خطأ في تصدير PDF المالي:', error);
      throw new Error('فشل في تصدير التقرير المالي بصيغة PDF');
    }
  }

  /**
   * تصدير التقرير المالي بصيغة CSV
   */
  static async exportToCSV(
    reportData: FinancialReportData,
    options: FinancialExportOptions = {}
  ): Promise<Blob> {
    try {
      let csvContent = '';

      // رأس التقرير
      csvContent += 'التقرير المالي\n';
      csvContent += `الفترة,${format(new Date(reportData.period.startDate), 'PPP', { locale: ar })},${format(new Date(reportData.period.endDate), 'PPP', { locale: ar })}\n\n`;

      // الملخص التنفيذي
      csvContent += 'الملخص التنفيذي\n';
      csvContent += 'البيان,المبلغ\n';
      csvContent += `الرصيد الافتتاحي,${this.formatCurrency(reportData.executiveSummary.openingBalance)}\n`;
      csvContent += `إجمالي المداخيل,${this.formatCurrency(reportData.executiveSummary.totalIncome)}\n`;
      csvContent += `إجمالي المصروفات,${this.formatCurrency(reportData.executiveSummary.totalExpenses)}\n`;
      csvContent += `الرصيد الختامي,${this.formatCurrency(reportData.executiveSummary.closingBalance)}\n`;
      csvContent += `صافي الربح,${this.formatCurrency(reportData.executiveSummary.netProfit)}\n\n`;

      // تفاصيل المداخيل
      csvContent += 'تفاصيل المداخيل\n';
      csvContent += 'نوع المدخول,العدد,المبلغ,النسبة\n';
      csvContent += `مدفوعات الطلاب,${reportData.incomeDetails.studentPayments.count},${this.formatCurrency(reportData.incomeDetails.studentPayments.amount)},${reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%\n`;
      csvContent += `التبرعات,${reportData.incomeDetails.donations.count},${this.formatCurrency(reportData.incomeDetails.donations.amount)},${reportData.incomeDetails.donations.percentage.toFixed(1)}%\n`;
      csvContent += `مداخيل أخرى,${reportData.incomeDetails.otherIncomes.count},${this.formatCurrency(reportData.incomeDetails.otherIncomes.amount)},${reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%\n\n`;

      // تفاصيل المصروفات
      csvContent += 'تفاصيل المصروفات\n';
      csvContent += 'فئة المصروف,العدد,المبلغ,النسبة\n';
      reportData.expenseDetails.byCategory.forEach(category => {
        csvContent += `${category.name},${category.expensesCount},${this.formatCurrency(category.totalAmount)},${category.percentage.toFixed(1)}%\n`;
      });

      return new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    } catch (error) {
      console.error('خطأ في تصدير CSV:', error);
      throw new Error('فشل في تصدير التقرير المالي بصيغة CSV');
    }
  }

  /**
   * إضافة ورقة الملخص التنفيذي
   */
  private static addExecutiveSummarySheet(workbook: XLSX.WorkBook, reportData: FinancialReportData): void {
    const summaryData = [
      ['التقرير المالي'],
      [''],
      ['الفترة', `من ${format(new Date(reportData.period.startDate), 'PPP', { locale: ar })} إلى ${format(new Date(reportData.period.endDate), 'PPP', { locale: ar })}`],
      [''],
      ['الملخص التنفيذي'],
      ['البيان', 'المبلغ'],
      ['الرصيد الافتتاحي', reportData.executiveSummary.openingBalance],
      ['إجمالي المداخيل', reportData.executiveSummary.totalIncome],
      ['إجمالي المصروفات', reportData.executiveSummary.totalExpenses],
      ['الرصيد الختامي', reportData.executiveSummary.closingBalance],
      ['صافي الربح/الخسارة', reportData.executiveSummary.netProfit],
      ['إجمالي المعاملات', reportData.executiveSummary.totalTransactions],
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(summaryData);

    // تنسيق الخلايا
    worksheet['!cols'] = [{ width: 20 }, { width: 15 }];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'الملخص التنفيذي');
  }

  /**
   * إضافة ورقة تفاصيل المداخيل
   */
  private static addIncomeDetailsSheet(workbook: XLSX.WorkBook, reportData: FinancialReportData): void {
    const incomeData = [
      ['تفاصيل المداخيل'],
      [''],
      ['نوع المدخول', 'العدد', 'المبلغ', 'النسبة'],
      ['مدفوعات الطلاب', reportData.incomeDetails.studentPayments.count, reportData.incomeDetails.studentPayments.amount, `${reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%`],
      ['التبرعات', reportData.incomeDetails.donations.count, reportData.incomeDetails.donations.amount, `${reportData.incomeDetails.donations.percentage.toFixed(1)}%`],
      ['مداخيل أخرى', reportData.incomeDetails.otherIncomes.count, reportData.incomeDetails.otherIncomes.amount, `${reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%`],
      [''],
      ['الإجمالي',
        reportData.incomeDetails.studentPayments.count + reportData.incomeDetails.donations.count + reportData.incomeDetails.otherIncomes.count,
        reportData.executiveSummary.totalIncome,
        '100%'
      ],
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(incomeData);
    worksheet['!cols'] = [{ width: 20 }, { width: 10 }, { width: 15 }, { width: 10 }];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'تفاصيل المداخيل');
  }

  /**
   * إضافة ورقة تفاصيل المصروفات
   */
  private static addExpenseDetailsSheet(workbook: XLSX.WorkBook, reportData: FinancialReportData): void {
    const expenseData = [
      ['تفاصيل المصروفات'],
      [''],
      ['فئة المصروف', 'العدد', 'المبلغ', 'النسبة'],
      ...reportData.expenseDetails.byCategory.map(category => [
        category.name,
        category.expensesCount,
        category.totalAmount,
        `${category.percentage.toFixed(1)}%`
      ]),
      [''],
      ['الإجمالي',
        reportData.expenseDetails.byCategory.reduce((sum, cat) => sum + cat.expensesCount, 0),
        reportData.expenseDetails.total,
        '100%'
      ],
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(expenseData);
    worksheet['!cols'] = [{ width: 25 }, { width: 10 }, { width: 15 }, { width: 10 }];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'تفاصيل المصروفات');
  }

  /**
   * إضافة ورقة الإحصائيات الشهرية
   */
  private static addMonthlyStatsSheet(workbook: XLSX.WorkBook, reportData: FinancialReportData): void {
    const monthlyData = [
      ['الإحصائيات الشهرية'],
      [''],
      ['الشهر', 'المدفوعات', 'التبرعات', 'المصروفات', 'إجمالي المداخيل', 'صافي الربح'],
      ...reportData.monthlyStats.map(month => [
        format(new Date(month.month + '-01'), 'MMMM yyyy', { locale: ar }),
        month.payments.amount,
        month.donations.amount,
        month.expenses.amount,
        month.totalIncome,
        month.netProfit
      ]),
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(monthlyData);
    worksheet['!cols'] = [{ width: 15 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 15 }, { width: 12 }];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'الإحصائيات الشهرية');
  }

  /**
   * إضافة ورقة طرق الدفع
   */
  private static addPaymentMethodsSheet(workbook: XLSX.WorkBook, reportData: FinancialReportData): void {
    const paymentMethodsData = [
      ['إحصائيات طرق الدفع'],
      [''],
      ['طريقة الدفع', 'المدفوعات', 'التبرعات', 'الإجمالي'],
      ...reportData.paymentMethodStats.map(method => [
        method.name,
        method.paymentsAmount,
        method.donationsAmount,
        method.totalAmount
      ]),
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(paymentMethodsData);
    worksheet['!cols'] = [{ width: 20 }, { width: 15 }, { width: 15 }, { width: 15 }];

    XLSX.utils.book_append_sheet(workbook, worksheet, 'طرق الدفع');
  }

  /**
   * إضافة رأس التقرير المالي للـ PDF
   */
  private static addFinancialReportHeader(
    pdf: jsPDF,
    reportData: FinancialReportData,
    yPosition: number,
    pageWidth: number,
    margin: number
  ): number {
    const centerX = pageWidth / 2;

    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    pdf.text('جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن', centerX, yPosition, { align: 'center' });
    yPosition += 8;

    pdf.setFontSize(14);
    pdf.text('المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر', centerX, yPosition, { align: 'center' });
    yPosition += 6;

    pdf.setFontSize(12);
    pdf.text('شعبة بلدية المنقر', centerX, yPosition, { align: 'center' });
    yPosition += 10;

    pdf.setFontSize(14);
    pdf.setFont('helvetica', 'bold');
    pdf.text('التقرير المالي لشعبة بلدية المنقر', centerX, yPosition, { align: 'center' });
    yPosition += 8;

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    const startDate = format(new Date(reportData.period.startDate), 'PPP', { locale: ar });
    const endDate = format(new Date(reportData.period.endDate), 'PPP', { locale: ar });
    pdf.text(`للفترة من: ${startDate} إلى: ${endDate}`, centerX, yPosition, { align: 'center' });
    yPosition += 15;

    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;

    return yPosition;
  }

  /**
   * إضافة الملخص التنفيذي للـ PDF
   */
  private static addExecutiveSummaryToPDF(
    pdf: jsPDF,
    reportData: FinancialReportData,
    yPosition: number,
    contentWidth: number,
    margin: number
  ): number {
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('📊 الملخص التنفيذي', margin, yPosition);
    yPosition += 10;

    const summaryData = [
      ['البيان', 'المبلغ (دج)'],
      ['الرصيد الافتتاحي', this.formatCurrency(reportData.executiveSummary.openingBalance)],
      ['إجمالي المداخيل', this.formatCurrency(reportData.executiveSummary.totalIncome)],
      ['إجمالي المصروفات', this.formatCurrency(reportData.executiveSummary.totalExpenses)],
      ['الرصيد الختامي', this.formatCurrency(reportData.executiveSummary.closingBalance)],
      ['صافي الربح/الخسارة', this.formatCurrency(reportData.executiveSummary.netProfit)],
    ];

    yPosition = this.addTableToPDF(pdf, summaryData, yPosition, margin, contentWidth);
    yPosition += 15;

    return yPosition;
  }

  /**
   * إضافة تفاصيل المداخيل للـ PDF
   */
  private static addIncomeDetailsToPDF(
    pdf: jsPDF,
    reportData: FinancialReportData,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    if (yPosition > pageHeight - 80) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('📋 تفاصيل المداخيل', margin, yPosition);
    yPosition += 10;

    const incomeData = [
      ['نوع المدخول', 'العدد', 'المبلغ (دج)', 'النسبة'],
      ['مدفوعات الطلاب', reportData.incomeDetails.studentPayments.count.toString(), this.formatCurrency(reportData.incomeDetails.studentPayments.amount), `${reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%`],
      ['التبرعات', reportData.incomeDetails.donations.count.toString(), this.formatCurrency(reportData.incomeDetails.donations.amount), `${reportData.incomeDetails.donations.percentage.toFixed(1)}%`],
      ['مداخيل أخرى', reportData.incomeDetails.otherIncomes.count.toString(), this.formatCurrency(reportData.incomeDetails.otherIncomes.amount), `${reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%`],
      ['الإجمالي',
        (reportData.incomeDetails.studentPayments.count + reportData.incomeDetails.donations.count + reportData.incomeDetails.otherIncomes.count).toString(),
        this.formatCurrency(reportData.executiveSummary.totalIncome),
        '100%'
      ],
    ];

    yPosition = this.addTableToPDF(pdf, incomeData, yPosition, margin, contentWidth);
    yPosition += 15;

    return yPosition;
  }

  /**
   * إضافة تفاصيل المصروفات للـ PDF
   */
  private static addExpenseDetailsToPDF(
    pdf: jsPDF,
    reportData: FinancialReportData,
    yPosition: number,
    contentWidth: number,
    margin: number,
    pageHeight: number
  ): number {
    if (yPosition > pageHeight - 80) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('💸 تفاصيل المصروفات', margin, yPosition);
    yPosition += 10;

    const expenseData = [
      ['فئة المصروف', 'العدد', 'المبلغ (دج)', 'النسبة'],
      ...reportData.expenseDetails.byCategory.map(category => [
        category.name,
        category.expensesCount.toString(),
        this.formatCurrency(category.totalAmount),
        `${category.percentage.toFixed(1)}%`
      ]),
      ['الإجمالي',
        reportData.expenseDetails.byCategory.reduce((sum, cat) => sum + cat.expensesCount, 0).toString(),
        this.formatCurrency(reportData.expenseDetails.total),
        '100%'
      ],
    ];

    yPosition = this.addTableToPDF(pdf, expenseData, yPosition, margin, contentWidth);

    return yPosition;
  }

  /**
   * إضافة جدول للـ PDF
   */
  private static addTableToPDF(
    pdf: jsPDF,
    data: string[][],
    yPosition: number,
    margin: number,
    contentWidth: number
  ): number {
    const rowHeight = 8;
    const colWidth = contentWidth / data[0].length;

    data.forEach((row, rowIndex) => {
      row.forEach((cell, colIndex) => {
        const x = margin + (colIndex * colWidth);
        const y = yPosition + (rowIndex * rowHeight);

        pdf.rect(x, y - 6, colWidth, rowHeight);

        pdf.setFontSize(9);
        if (rowIndex === 0) {
          pdf.setFont('helvetica', 'bold');
        } else {
          pdf.setFont('helvetica', 'normal');
        }

        pdf.text(cell, x + 2, y, { maxWidth: colWidth - 4 });
      });
    });

    return yPosition + (data.length * rowHeight) + 5;
  }

  /**
   * إضافة علامة مائية
   */
  private static addWatermark(pdf: jsPDF, watermarkText: string): void {
    const pageCount = pdf.getNumberOfPages();

    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setTextColor(200, 200, 200);
      pdf.setFontSize(50);
      pdf.text(watermarkText, 105, 150, {
        align: 'center',
        angle: 45,
      });
    }

    pdf.setTextColor(0, 0, 0);
  }

  /**
   * تنسيق العملة
   */
  private static formatCurrency(amount: number): string {
    return new Intl.NumberFormat('ar-DZ', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  }
}

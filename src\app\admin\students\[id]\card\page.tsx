'use client';

import { useRouter, useParams } from 'next/navigation';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import StudentCard from '@/components/admin/students/StudentCard';
import { Button } from '@/components/ui/button';
import { FaArrowLeft, FaUser } from 'react-icons/fa';

export default function StudentCardPage() {
  const router = useRouter();
  const params = useParams();
  const studentId = parseInt(params.id as string);

  const handlePrint = () => {
    console.log('تم طباعة بطاقة التلميذ');
  };

  const handleDownload = () => {
    console.log('تم تحميل بطاقة التلميذ');
  };

  return (
    <OptimizedProtectedRoute>
      <PermissionGuard requiredPermission="admin.students.card.view">
        <div className="container mx-auto px-4 py-8 space-y-6">
          {/* رأس الصفحة */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.back()}
                variant="outline"
                className="flex items-center gap-2"
              >
                <FaArrowLeft />
                العودة
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-[var(--primary-color)] flex items-center gap-2">
                  <FaUser />
                  بطاقة التلميذ
                </h1>
                <p className="text-gray-600">
                  عرض وطباعة بطاقة التلميذ الشاملة
                </p>
              </div>
            </div>
          </div>

          {/* بطاقة التلميذ */}
          <StudentCard
            studentId={studentId}
            onPrint={handlePrint}
            onDownload={handleDownload}
          />
        </div>
      </PermissionGuard>
    </OptimizedProtectedRoute>
  );
}

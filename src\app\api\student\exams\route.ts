import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { verifyToken } from "@/utils/verifyToken";

// GET /api/student/exams
export async function GET(request: NextRequest) {
  try {
    // التحقق من توكن المستخدم
    const payload = await verifyToken(request);

    if (!payload) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    // التحقق من أن المستخدم طالب
    const student = await prisma.student.findFirst({
      where: {
        username: payload.username
      }
    });

    if (!student) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 403 });
    }

    // الحصول على الفصل الدراسي للطالب
    const studentClass = await prisma.student.findUnique({
      where: {
        id: student.id
      },
      select: {
        classeId: true
      }
    });

    const classIds = studentClass?.classeId ? [studentClass.classeId] : [];

    // الحصول على المواد الدراسية للطالب
    const classSubjects = await prisma.classSubject.findMany({
      where: {
        classeId: {
          in: classIds
        }
      }
    });

    const classSubjectIds = classSubjects.map((cs: { id: number }) => cs.id);

    // الحصول على نقاط الامتحانات للطالب
    const examPoints = await prisma.exam_points.findMany({
      where: {
        studentId: student.id,
        classSubjectId: {
          in: classSubjectIds
        }
      },
      include: {
        exam: {
          include: {
            examType: true
          }
        }
      },
      orderBy: [
        {
          exam: {
            month: 'desc'
          }
        },
        {
          createdAt: 'desc'
        }
      ]
    });

    return NextResponse.json({
      data: examPoints,
      success: true,
      message: 'تم جلب الامتحانات بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student exams:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب الامتحانات',
      success: false
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";
import { UserRole } from "@prisma/client";

// GET: جلب جداول حصص المعلمين
export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // استخراج المعلمات من URL
    const { searchParams } = new URL(request.url);
    const teacherId = searchParams.get('teacherId');
    const classeId = searchParams.get('classeId');
    const subjectId = searchParams.get('subjectId');
    const day = searchParams.get('day');
    const limit = parseInt(searchParams.get('limit') || '100');
    const page = parseInt(searchParams.get('page') || '1');

    // بناء شروط البحث
    const where: {
      teacherId?: number;
      classeId?: number;
      subjectId?: number;
      day?: string;
    } = {};

    if (teacherId) {
      where.teacherId = parseInt(teacherId);
    }

    if (classeId) {
      where.classeId = parseInt(classeId);
    }

    if (subjectId) {
      where.subjectId = parseInt(subjectId);
    }

    if (day) {
      where.day = day;
    }

    // إذا كان المستخدم معلمًا، يجب أن يرى فقط جدول حصصه
    if (userData.role === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId: userData.id }
      });

      if (teacher) {
        where.teacherId = teacher.id;
      } else {
        return NextResponse.json(
          { message: "لم يتم العثور على بيانات المعلم" },
          { status: 404 }
        );
      }
    }

    // جلب إجمالي عدد الجداول
    const total = await prisma.teacherSchedule.count({ where });

    // جلب جداول الحصص
    const schedules = await prisma.teacherSchedule.findMany({
      where,
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            specialization: true
          }
        },
        classe: {
          select: {
            id: true,
            name: true
          }
        },
        subject: {
          select: {
            id: true,
            name: true,
            description: true
          }
        }
      },
      orderBy: [
        { day: 'asc' },
        { startTime: 'asc' }
      ],
      skip: (page - 1) * limit,
      take: limit
    });

    return NextResponse.json({
      data: schedules,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching teacher schedules:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب جداول حصص المعلمين" },
      { status: 500 }
    );
  }
}

// POST: إنشاء جدول حصص جديد للمعلم
export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.teacherId || !body.day || !body.startTime || !body.endTime || !body.classeId || !body.subjectId) {
      return NextResponse.json(
        { message: "البيانات غير مكتملة" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود تعارض في الجدول
    const conflictingSchedule = await prisma.teacherSchedule.findFirst({
      where: {
        OR: [
          // تعارض في جدول المعلم
          {
            teacherId: parseInt(body.teacherId),
            day: body.day,
            OR: [
              {
                // الحصة الجديدة تبدأ خلال حصة موجودة
                startTime: {
                  gte: body.startTime,
                  lt: body.endTime
                }
              },
              {
                // الحصة الجديدة تنتهي خلال حصة موجودة
                endTime: {
                  gt: body.startTime,
                  lte: body.endTime
                }
              },
              {
                // الحصة الجديدة تحتوي حصة موجودة
                startTime: {
                  lte: body.startTime
                },
                endTime: {
                  gte: body.endTime
                }
              }
            ]
          },
          // تعارض في جدول الفصل
          {
            classeId: parseInt(body.classeId),
            day: body.day,
            OR: [
              {
                startTime: {
                  gte: body.startTime,
                  lt: body.endTime
                }
              },
              {
                endTime: {
                  gt: body.startTime,
                  lte: body.endTime
                }
              },
              {
                startTime: {
                  lte: body.startTime
                },
                endTime: {
                  gte: body.endTime
                }
              }
            ]
          }
        ]
      }
    });

    if (conflictingSchedule) {
      return NextResponse.json(
        { message: "يوجد تعارض في الجدول، الرجاء اختيار وقت آخر" },
        { status: 400 }
      );
    }

    // إنشاء جدول الحصص
    const schedule = await prisma.teacherSchedule.create({
      data: {
        teacherId: parseInt(body.teacherId),
        day: body.day,
        startTime: body.startTime,
        endTime: body.endTime,
        classeId: parseInt(body.classeId),
        subjectId: parseInt(body.subjectId),
        location: body.location || null,
        notes: body.notes || null
      }
    });

    // إنشاء إشعار للمعلم
    const teacher = await prisma.teacher.findUnique({
      where: { id: parseInt(body.teacherId) },
      include: {
        user: true
      }
    });

    if (teacher && teacher.user) {
      await prisma.notification.create({
        data: {
          title: "تم إضافة حصة جديدة إلى جدولك",
          content: `تم إضافة حصة جديدة يوم ${body.day} من الساعة ${body.startTime} إلى ${body.endTime}`,
          type: "GENERAL",
          userId: teacher.user.id
        }
      });
    }

    return NextResponse.json(schedule, { status: 201 });
  } catch (error) {
    console.error('Error creating teacher schedule:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء جدول حصص المعلم" },
      { status: 500 }
    );
  }
}

// PUT: تحديث جدول حصص المعلم
export async function PUT(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.id) {
      return NextResponse.json(
        { message: "معرف الجدول مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الجدول
    const existingSchedule = await prisma.teacherSchedule.findUnique({
      where: { id: parseInt(body.id) }
    });

    if (!existingSchedule) {
      return NextResponse.json(
        { message: "الجدول غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود تعارض في الجدول إذا تم تغيير الوقت أو اليوم
    if ((body.day && body.day !== existingSchedule.day) ||
        (body.startTime && body.startTime !== existingSchedule.startTime) ||
        (body.endTime && body.endTime !== existingSchedule.endTime) ||
        (body.teacherId && parseInt(body.teacherId) !== existingSchedule.teacherId) ||
        (body.classeId && parseInt(body.classeId) !== existingSchedule.classeId)) {

      const day = body.day || existingSchedule.day;
      const startTime = body.startTime || existingSchedule.startTime;
      const endTime = body.endTime || existingSchedule.endTime;
      const teacherId = body.teacherId ? parseInt(body.teacherId) : existingSchedule.teacherId;
      const classeId = body.classeId ? parseInt(body.classeId) : existingSchedule.classeId;

      const conflictingSchedule = await prisma.teacherSchedule.findFirst({
        where: {
          id: { not: parseInt(body.id) },
          OR: [
            // تعارض في جدول المعلم
            {
              teacherId,
              day,
              OR: [
                {
                  startTime: {
                    gte: startTime,
                    lt: endTime
                  }
                },
                {
                  endTime: {
                    gt: startTime,
                    lte: endTime
                  }
                },
                {
                  startTime: {
                    lte: startTime
                  },
                  endTime: {
                    gte: endTime
                  }
                }
              ]
            },
            // تعارض في جدول الفصل
            {
              classeId,
              day,
              OR: [
                {
                  startTime: {
                    gte: startTime,
                    lt: endTime
                  }
                },
                {
                  endTime: {
                    gt: startTime,
                    lte: endTime
                  }
                },
                {
                  startTime: {
                    lte: startTime
                  },
                  endTime: {
                    gte: endTime
                  }
                }
              ]
            }
          ]
        }
      });

      if (conflictingSchedule) {
        return NextResponse.json(
          { message: "يوجد تعارض في الجدول، الرجاء اختيار وقت آخر" },
          { status: 400 }
        );
      }
    }

    // تحديث الجدول
    const updatedSchedule = await prisma.teacherSchedule.update({
      where: { id: parseInt(body.id) },
      data: {
        teacherId: body.teacherId ? parseInt(body.teacherId) : undefined,
        day: body.day || undefined,
        startTime: body.startTime || undefined,
        endTime: body.endTime || undefined,
        classeId: body.classeId ? parseInt(body.classeId) : undefined,
        subjectId: body.subjectId ? parseInt(body.subjectId) : undefined,
        location: body.location !== undefined ? body.location : undefined,
        notes: body.notes !== undefined ? body.notes : undefined
      }
    });

    return NextResponse.json(updatedSchedule);
  } catch (error) {
    console.error('Error updating teacher schedule:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث جدول حصص المعلم" },
      { status: 500 }
    );
  }
}

// DELETE: حذف جدول حصص المعلم
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول" },
        { status: 401 }
      );
    }

    // استخراج معرف الجدول من URL
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: "معرف الجدول مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الجدول
    const existingSchedule = await prisma.teacherSchedule.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingSchedule) {
      return NextResponse.json(
        { message: "الجدول غير موجود" },
        { status: 404 }
      );
    }

    // حذف الجدول
    await prisma.teacherSchedule.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: "تم حذف الجدول بنجاح" });
  } catch (error) {
    console.error('Error deleting teacher schedule:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف جدول حصص المعلم" },
      { status: 500 }
    );
  }
}

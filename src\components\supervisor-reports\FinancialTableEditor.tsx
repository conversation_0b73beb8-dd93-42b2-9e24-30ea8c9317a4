'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { DollarSign, Plus, Trash2, Download, Upload, Calculator } from 'lucide-react';

interface FinancialRow {
  id: string;
  category: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
}

interface FinancialTableEditorProps {
  data: FinancialRow[];
  onChange: (data: FinancialRow[]) => void;
  periodStart: Date;
  periodEnd: Date;
}

export default function FinancialTableEditor({
  data,
  onChange,
  periodStart,
  periodEnd
}: FinancialTableEditorProps) {
  const [isLoadingData, setIsLoadingData] = useState(false);

  // تحميل البيانات المالية التلقائية
  const loadFinancialData = async () => {
    setIsLoadingData(true);
    try {
      const response = await fetch(
        `/api/reports/financial?startDate=${periodStart.toISOString()}&endDate=${periodEnd.toISOString()}`
      );
      
      if (response.ok) {
        const result = await response.json();
        const autoData = generateFinancialRows(result.data);
        onChange([...data, ...autoData]);
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات المالية:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  // تحويل البيانات المالية إلى صفوف الجدول
  const generateFinancialRows = (financialData: any): FinancialRow[] => {
    const rows: FinancialRow[] = [];
    
    // إضافة المداخيل
    if (financialData.periodTotals?.totalPayments > 0) {
      rows.push({
        id: `income-payments-${Date.now()}`,
        category: 'المداخيل',
        description: 'رسوم التسجيل والأقساط',
        amount: financialData.periodTotals.totalPayments,
        type: 'income'
      });
    }
    
    if (financialData.periodTotals?.totalDonations > 0) {
      rows.push({
        id: `income-donations-${Date.now()}`,
        category: 'المداخيل',
        description: 'التبرعات والهبات',
        amount: financialData.periodTotals.totalDonations,
        type: 'income'
      });
    }
    
    if (financialData.periodTotals?.totalIncomes > 0) {
      rows.push({
        id: `income-other-${Date.now()}`,
        category: 'المداخيل',
        description: 'مداخيل أخرى',
        amount: financialData.periodTotals.totalIncomes,
        type: 'income'
      });
    }
    
    // إضافة المصاريف
    if (financialData.periodTotals?.totalExpenses > 0) {
      rows.push({
        id: `expense-general-${Date.now()}`,
        category: 'المصاريف',
        description: 'مصاريف عامة',
        amount: financialData.periodTotals.totalExpenses,
        type: 'expense'
      });
    }
    
    return rows;
  };

  // إضافة صف جديد
  const addRow = (type: 'income' | 'expense') => {
    const newRow: FinancialRow = {
      id: `${type}-${Date.now()}`,
      category: type === 'income' ? 'المداخيل' : 'المصاريف',
      description: '',
      amount: 0,
      type
    };
    onChange([...data, newRow]);
  };

  // تحديث صف
  const updateRow = (id: string, field: keyof FinancialRow, value: any) => {
    const updatedData = data.map(row => 
      row.id === id ? { ...row, [field]: value } : row
    );
    onChange(updatedData);
  };

  // حذف صف
  const removeRow = (id: string) => {
    const updatedData = data.filter(row => row.id !== id);
    onChange(updatedData);
  };

  // نسخ صف
  const duplicateRow = (row: FinancialRow) => {
    const newRow: FinancialRow = {
      ...row,
      id: `${row.type}-${Date.now()}`,
      description: `${row.description} (نسخة)`
    };
    onChange([...data, newRow]);
  };

  // حساب المجاميع
  const incomeRows = data.filter(row => row.type === 'income');
  const expenseRows = data.filter(row => row.type === 'expense');
  
  const totalIncome = incomeRows.reduce((sum, row) => sum + row.amount, 0);
  const totalExpenses = expenseRows.reduce((sum, row) => sum + row.amount, 0);
  const balance = totalIncome - totalExpenses;

  // تصدير البيانات إلى CSV
  const exportToCSV = () => {
    const headers = ['النوع', 'البيان', 'المبلغ'];
    const csvData = [
      headers,
      ...data.map(row => [
        row.type === 'income' ? 'مدخل' : 'مصروف',
        row.description,
        row.amount.toString()
      ]),
      ['', 'إجمالي المداخيل', totalIncome.toString()],
      ['', 'إجمالي المصاريف', totalExpenses.toString()],
      ['', 'الرصيد النهائي', balance.toString()]
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `التقرير_المالي_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  // استيراد البيانات من ملف
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string;
          const lines = content.split('\n');
          const importedRows: FinancialRow[] = [];
          
          lines.slice(1).forEach((line, index) => {
            const [type, description, amount] = line.split(',');
            if (description && amount) {
              importedRows.push({
                id: `imported-${Date.now()}-${index}`,
                category: type === 'مدخل' ? 'المداخيل' : 'المصاريف',
                description: description.trim(),
                amount: parseFloat(amount) || 0,
                type: type === 'مدخل' ? 'income' : 'expense'
              });
            }
          });
          
          onChange([...data, ...importedRows]);
        } catch (error) {
          console.error('خطأ في استيراد الملف:', error);
        }
      };
      reader.readAsText(file);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5 text-green-600" />
          💰 محرر التقرير المالي
        </CardTitle>
        <CardDescription>
          جدول تفاعلي ديناميكي مطابق لتنسيق Excel مع أبواب المداخيل والمصاريف
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* شريط الأدوات */}
        <div className="flex items-center gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={loadFinancialData}
            disabled={isLoadingData}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            تحميل البيانات التلقائية
          </Button>
          
          <Separator orientation="vertical" className="h-6" />
          
          <Button
            variant="outline"
            size="sm"
            onClick={exportToCSV}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            تصدير CSV
          </Button>
          
          <div className="flex items-center gap-2">
            <input
              type="file"
              accept=".csv"
              onChange={handleFileImport}
              className="hidden"
              id="import-file"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => document.getElementById('import-file')?.click()}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              استيراد CSV
            </Button>
          </div>
          
          <Separator orientation="vertical" className="h-6" />
          
          <Badge variant="secondary" className="flex items-center gap-1">
            <Calculator className="h-3 w-3" />
            الرصيد: {balance.toLocaleString()} دج
          </Badge>
        </div>

        {/* جدول المداخيل */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-green-700 flex items-center gap-2">
              📈 المداخيل
              <Badge variant="secondary">{incomeRows.length} عنصر</Badge>
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addRow('income')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة مدخل
            </Button>
          </div>
          
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-green-50">
                <tr>
                  <th className="p-3 text-right border-b font-semibold">البيان</th>
                  <th className="p-3 text-right border-b font-semibold">المبلغ (دج)</th>
                  <th className="p-3 text-center border-b font-semibold">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {incomeRows.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="p-8 text-center text-gray-500">
                      لا توجد مداخيل مضافة
                      <br />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addRow('income')}
                        className="mt-2"
                      >
                        إضافة أول مدخل
                      </Button>
                    </td>
                  </tr>
                ) : (
                  incomeRows.map((row, index) => (
                    <tr key={row.id} className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                      <td className="p-3">
                        <Input
                          value={row.description}
                          onChange={(e) => updateRow(row.id, 'description', e.target.value)}
                          placeholder="وصف المدخل..."
                          className="border-0 bg-transparent"
                        />
                      </td>
                      <td className="p-3">
                        <Input
                          type="number"
                          value={row.amount}
                          onChange={(e) => updateRow(row.id, 'amount', parseFloat(e.target.value) || 0)}
                          placeholder="0"
                          className="border-0 bg-transparent font-mono text-right"
                        />
                      </td>
                      <td className="p-3 text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => duplicateRow(row)}
                            title="نسخ"
                            className="h-8 w-8 p-0"
                          >
                            📋
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeRow(row.id)}
                            title="حذف"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
              <tfoot className="bg-green-100">
                <tr>
                  <td className="p-3 font-bold">إجمالي المداخيل</td>
                  <td className="p-3 font-bold font-mono text-right">{totalIncome.toLocaleString()}</td>
                  <td className="p-3"></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        <Separator />

        {/* جدول المصاريف */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-red-700 flex items-center gap-2">
              📉 المصاريف
              <Badge variant="secondary">{expenseRows.length} عنصر</Badge>
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addRow('expense')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة مصروف
            </Button>
          </div>
          
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-red-50">
                <tr>
                  <th className="p-3 text-right border-b font-semibold">البيان</th>
                  <th className="p-3 text-right border-b font-semibold">المبلغ (دج)</th>
                  <th className="p-3 text-center border-b font-semibold">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {expenseRows.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="p-8 text-center text-gray-500">
                      لا توجد مصاريف مضافة
                      <br />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => addRow('expense')}
                        className="mt-2"
                      >
                        إضافة أول مصروف
                      </Button>
                    </td>
                  </tr>
                ) : (
                  expenseRows.map((row, index) => (
                    <tr key={row.id} className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                      <td className="p-3">
                        <Input
                          value={row.description}
                          onChange={(e) => updateRow(row.id, 'description', e.target.value)}
                          placeholder="وصف المصروف..."
                          className="border-0 bg-transparent"
                        />
                      </td>
                      <td className="p-3">
                        <Input
                          type="number"
                          value={row.amount}
                          onChange={(e) => updateRow(row.id, 'amount', parseFloat(e.target.value) || 0)}
                          placeholder="0"
                          className="border-0 bg-transparent font-mono text-right"
                        />
                      </td>
                      <td className="p-3 text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => duplicateRow(row)}
                            title="نسخ"
                            className="h-8 w-8 p-0"
                          >
                            📋
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeRow(row.id)}
                            title="حذف"
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
              <tfoot className="bg-red-100">
                <tr>
                  <td className="p-3 font-bold">إجمالي المصاريف</td>
                  <td className="p-3 font-bold font-mono text-right">{totalExpenses.toLocaleString()}</td>
                  <td className="p-3"></td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        {/* الرصيد النهائي */}
        <div className={`p-4 rounded-lg border-2 ${balance >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <div className="flex items-center justify-between">
            <span className="text-lg font-bold">الرصيد النهائي:</span>
            <span className={`text-xl font-bold font-mono ${balance >= 0 ? 'text-green-700' : 'text-red-700'}`}>
              {balance.toLocaleString()} دج
            </span>
          </div>
          <div className="text-sm text-gray-600 mt-2">
            {balance >= 0 ? '✅ رصيد إيجابي' : '⚠️ عجز في الميزانية'}
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="text-sm text-gray-500 space-y-1">
          <p>💡 نصائح:</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>استخدم زر "تحميل البيانات التلقائية" لجلب البيانات من النظام</li>
            <li>يمكنك تصدير البيانات إلى ملف CSV للاستخدام في Excel</li>
            <li>استخدم زر النسخ لتكرار الصفوف المتشابهة</li>
            <li>يتم حساب المجاميع والرصيد تلقائياً</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

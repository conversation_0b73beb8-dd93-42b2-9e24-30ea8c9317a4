@startuml Deployment Diagram - Quran School Management System

skinparam {
  NodeBackgroundColor LightBlue
  NodeBorderColor Black
  DatabaseBackgroundColor PaleGreen
  DatabaseBorderColor DarkGreen
  CloudBackgroundColor LightCyan
  ArrowColor Black
  ComponentBackgroundColor LightYellow
}

node "Client Devices" as ClientDevices {
  node "Desktop Computer" as Desktop {
    [Web Browser] as DesktopBrowser
  }

  node "Mobile Device" as Mobile {
    [Mobile Browser] as MobileBrowser
    [Progressive Web App] as PWA
  }

  node "Tablet" as Tablet {
    [Tablet Browser] as TabletBrowser
  }
}

cloud "Internet" as Internet {
}

node "Production Environment" as ProdEnv {
  node "Web Server Cluster" as WebCluster {
    node "Web Server 1" as WebServer1 {
      [Next.js Application] as NextApp1
      [API Routes] as API1
    }

    node "Web Server 2" as WebServer2 {
      [Next.js Application] as NextApp2
      [API Routes] as API2
    }

    [Load Balancer] as LoadBalancer
  }

  node "Database Servers" as DBCluster {
    database "Primary PostgreSQL" as PrimaryDB {
      [User Data] as UserData
      [Educational Data] as EduData
      [Financial Data] as FinData
    }

    database "Replica PostgreSQL" as ReplicaDB {
      [Replicated Data] as RepData
    }

    [Backup Service] as BackupService
  }

  node "Cache Server" as CacheServer {
    [Redis Cache] as Cache
  }

  node "Monitoring System" as Monitoring {
    [Performance Monitor] as PerfMon
    [Error Tracking] as ErrorTrack
    [Log Aggregation] as Logs
  }
}

cloud "Cloud Services" as CloudServices {
  [File Storage Service] as Storage
  [Payment Gateway] as Payment
  [Video Conferencing] as Video
  [Email Service] as Email
  [SMS Gateway] as SMS
  [CDN] as CDN
}

' Client Connections
DesktopBrowser -- Internet : HTTPS
MobileBrowser -- Internet : HTTPS
PWA -- Internet : HTTPS
TabletBrowser -- Internet : HTTPS

' Internet to Load Balancer
Internet -- LoadBalancer : HTTPS

' Load Balancer to Web Servers
LoadBalancer -- WebServer1 : HTTP
LoadBalancer -- WebServer2 : HTTP

' Web Server Internal Components
NextApp1 -- API1 : Internal
NextApp2 -- API2 : Internal

' Web Servers to Database
API1 -- PrimaryDB : TCP/IP
API2 -- PrimaryDB : TCP/IP

' Database Replication
PrimaryDB -- ReplicaDB : Replication
PrimaryDB -- BackupService : Scheduled Backup

' Cache Connections
API1 -- Cache : Redis Protocol
API2 -- Cache : Redis Protocol

' Monitoring Connections
WebServer1 -- PerfMon : Agent
WebServer2 -- PerfMon : Agent
PrimaryDB -- PerfMon : Agent
ReplicaDB -- PerfMon : Agent

' Cloud Service Connections
API1 -- Storage : HTTPS
API2 -- Storage : HTTPS
API1 -- Payment : HTTPS
API2 -- Payment : HTTPS
API1 -- Video : HTTPS
API2 -- Video : HTTPS
API1 -- Email : SMTP
API2 -- Email : SMTP
API1 -- SMS : HTTPS
API2 -- SMS : HTTPS

' CDN Connections
Internet -- CDN : HTTPS
CDN -- Storage : HTTPS

@enduml

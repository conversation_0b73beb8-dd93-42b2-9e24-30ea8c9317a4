'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Database, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

export default function FixDataPage() {
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [data, setData] = useState<any>(null);

  const checkData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/debug/check-data');
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
        toast.success('تم فحص البيانات بنجاح');
      } else {
        toast.error('فشل في فحص البيانات');
      }
    } catch (error) {
      console.error('Error checking data:', error);
      toast.error('حدث خطأ أثناء فحص البيانات');
    } finally {
      setLoading(false);
    }
  };

  const createSampleData = async () => {
    try {
      setCreating(true);
      const response = await fetch('/api/debug/create-sample-data', {
        method: 'POST'
      });
      const result = await response.json();
      
      if (result.success) {
        toast.success('تم إنشاء البيانات التجريبية بنجاح');
        // إعادة فحص البيانات
        await checkData();
      } else {
        toast.error('فشل في إنشاء البيانات التجريبية');
      }
    } catch (error) {
      console.error('Error creating sample data:', error);
      toast.error('حدث خطأ أثناء إنشاء البيانات التجريبية');
    } finally {
      setCreating(false);
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.evaluation.reports.view">
      <div className="container mx-auto p-4 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-[var(--primary-color)]">إصلاح مشكلة كشف الدرجات</h1>
          <div className="flex gap-2">
            <Button onClick={checkData} disabled={loading}>
              {loading ? (
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
              ) : (
                <Database className="ml-2 h-4 w-4" />
              )}
              فحص البيانات
            </Button>
            <Button onClick={createSampleData} disabled={creating} className="bg-green-600 hover:bg-green-700">
              {creating ? (
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="ml-2 h-4 w-4" />
              )}
              إنشاء بيانات تجريبية
            </Button>
          </div>
        </div>

        {/* شرح المشكلة */}
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center text-orange-800">
              <AlertTriangle className="ml-2 h-5 w-5" />
              تشخيص المشكلة
            </CardTitle>
          </CardHeader>
          <CardContent className="text-orange-700">
            <p className="mb-2">
              <strong>المشكلة:</strong> تظهر رسالة "لا توجد بيانات امتحانات" في كشف الدرجات
            </p>
            <p className="mb-2">
              <strong>السبب المحتمل:</strong> لا توجد نقاط امتحانات (exam_points) في قاعدة البيانات
            </p>
            <p>
              <strong>الحل:</strong> إنشاء بيانات تجريبية تتضمن طلاب وامتحانات ونقاط امتحانات
            </p>
          </CardContent>
        </Card>

        {data && (
          <>
            {/* ملخص الأعداد */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {Object.entries(data.counts).map(([key, count]) => {
                const isGood = (count as number) > 0;
                return (
                  <Card key={key} className={isGood ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                    <CardContent className="p-4 text-center">
                      <div className="flex items-center justify-center mb-2">
                        {isGood ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                      <p className={`text-2xl font-bold ${isGood ? 'text-green-600' : 'text-red-600'}`}>
                        {count as number}
                      </p>
                      <p className="text-sm text-gray-600">
                        {key === 'students' && 'طلاب'}
                        {key === 'exams' && 'امتحانات'}
                        {key === 'examPoints' && 'نقاط امتحانات'}
                        {key === 'classes' && 'فصول'}
                        {key === 'subjects' && 'مواد'}
                        {key === 'classSubjects' && 'علاقات فصل-مادة'}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* تحليل المشكلة */}
            <Card>
              <CardHeader>
                <CardTitle>تحليل الحالة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.counts.examPoints === 0 ? (
                    <div className="p-4 bg-red-50 border border-red-200 rounded">
                      <h4 className="font-bold text-red-800 flex items-center">
                        <AlertTriangle className="ml-2 h-5 w-5" />
                        ⚠️ المشكلة الرئيسية: لا توجد نقاط امتحانات
                      </h4>
                      <p className="text-red-700 mt-2">
                        هذا هو سبب ظهور رسالة "لا توجد بيانات امتحانات" في كشف الدرجات.
                        يجب إنشاء نقاط امتحانات للطلاب.
                      </p>
                      <Button 
                        onClick={createSampleData} 
                        disabled={creating}
                        className="mt-3 bg-red-600 hover:bg-red-700"
                      >
                        {creating ? (
                          <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="ml-2 h-4 w-4" />
                        )}
                        إصلاح المشكلة الآن
                      </Button>
                    </div>
                  ) : (
                    <div className="p-4 bg-green-50 border border-green-200 rounded">
                      <h4 className="font-bold text-green-800 flex items-center">
                        <CheckCircle className="ml-2 h-5 w-5" />
                        ✅ البيانات متوفرة
                      </h4>
                      <p className="text-green-700 mt-2">
                        يوجد {data.counts.examPoints} نقطة امتحان. يجب أن تظهر في كشف الدرجات.
                        إذا كانت المشكلة مستمرة، تحقق من معايير التصفية في كشف الدرجات.
                      </p>
                    </div>
                  )}
                  
                  {data.counts.classSubjects === 0 && (
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
                      <h4 className="font-bold text-yellow-800">⚠️ تحذير: لا توجد علاقات فصل-مادة</h4>
                      <p className="text-yellow-700 mt-2">
                        علاقات الفصل بالمادة مطلوبة لربط الطلاب بالامتحانات.
                        سيتم إنشاؤها تلقائياً مع البيانات التجريبية.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* عينة من البيانات */}
            {data.samples.examPoints.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>عينة من نقاط الامتحانات الموجودة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {data.samples.examPoints.map((point: any) => (
                      <div key={point.id} className="p-3 bg-gray-50 rounded border">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-semibold">{point.studentName}</p>
                            <p className="text-sm text-gray-600">{point.examDescription}</p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-blue-600">{point.grade}/{point.maxPoints}</p>
                            <p className="text-sm text-gray-600">{point.classe}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}

        {!data && (
          <Card>
            <CardContent className="text-center p-8">
              <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">اضغط على "فحص البيانات" لبدء التشخيص</p>
              <Button onClick={checkData} disabled={loading}>
                {loading ? (
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="ml-2 h-4 w-4" />
                )}
                فحص البيانات
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </ProtectedRoute>
  );
}

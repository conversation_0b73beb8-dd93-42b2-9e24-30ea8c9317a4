# TODO: إنشاء صفحة خلفيات الصفحات العامة

## 📊 إحصائيات المشروع
- **إجمالي المهام:** 16 مهمة رئيسية
- **المهام المكتملة:** 0/16
- **التقدم:** 0%
- **الحالة:** لم يبدأ
- **الأولوية:** عالية

---

## 🗄️ المرحلة الأولى: إعداد قاعدة البيانات والـ API

### [ ] **المهمة 1.1:** إنشاء جدول PageBackground
- **الملف:** `prisma/schema.prisma`
- **الوصف:** إضافة نموذج PageBackground مع جميع الحقول المطلوبة
- **التفاصيل:**
  ```prisma
  model PageBackground {
    id             Int      @id @default(autoincrement())
    pageName       String   @unique
    displayName    String
    imageUrl       String?
    overlayColor   String?
    overlayOpacity Float    @default(0.5)
    position       String   @default("center")
    size           String   @default("cover")
    repeat         String   @default("no-repeat")
    attachment     String   @default("scroll")
    isActive       Boolean  @default(true)
    priority       Int      @default(0)
    createdAt      DateTime @default(now())
    updatedAt      DateTime @updatedAt
  }
  ```
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 1.2:** إنشاء وتطبيق Migration
- **الملف:** `prisma/migrations/`
- **الوصف:** إنشاء وتطبيق migration لجدول PageBackground
- **الأوامر:** 
  ```bash
  npx prisma migrate dev --name add_page_backgrounds
  npx prisma generate
  ```
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 1.3:** إنشاء Seed Data
- **الملف:** `prisma/seeds/pageBackgrounds.ts`
- **الوصف:** إضافة بيانات أولية للخلفيات الافتراضية
- **البيانات المطلوبة:**
  - home: الصفحة الرئيسية
  - about: من نحن
  - contact: اتصل بنا
  - programs: البرامج
  - khatm-sessions: مجالس الختم
  - donations: التبرعات
  - login: تسجيل الدخول
  - register: التسجيل
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 1.4:** إنشاء API Endpoints
- **الملفات:** 
  - `src/app/api/page-backgrounds/route.ts`
  - `src/app/api/page-backgrounds/[pageName]/route.ts`
- **الوصف:** إنشاء جميع endpoints المطلوبة
- **Endpoints المطلوبة:**
  - GET /api/page-backgrounds (جلب جميع الخلفيات مع فلترة)
  - POST /api/page-backgrounds (إضافة خلفية جديدة)
  - PUT /api/page-backgrounds (تحديث خلفية موجودة)
  - DELETE /api/page-backgrounds (حذف خلفية)
  - GET /api/page-backgrounds/[pageName] (جلب خلفية صفحة محددة)
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

---

## 🎨 المرحلة الثانية: إنشاء مكونات الواجهة

### [ ] **المهمة 2.1:** إنشاء Hook usePageBackground
- **الملف:** `src/hooks/usePageBackground.ts`
- **الوصف:** Hook لإدارة حالة خلفيات الصفحات
- **الوظائف المطلوبة:**
  - جلب خلفية صفحة محددة
  - إدارة حالة التحميل والأخطاء
  - إرجاع styles للخلفية والطبقة العلوية
  - دعم الخلفيات الاحتياطية
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 2.2:** إنشاء مكون PageBackground
- **الملف:** `src/components/PageBackground.tsx`
- **الوصف:** مكون لعرض خلفية الصفحة
- **الخصائص المطلوبة:**
  - pageName: string (اسم الصفحة)
  - className?: string (كلاسات إضافية)
  - fallbackBackground?: string (خلفية احتياطية)
  - children: ReactNode (محتوى الصفحة)
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 2.3:** إنشاء مكون PageBackgroundsManager
- **الملف:** `src/components/admin/PageBackgroundsManager.tsx`
- **الوصف:** واجهة إدارة خلفيات الصفحات في لوحة التحكم
- **الوظائف المطلوبة:**
  - عرض قائمة الخلفيات الموجودة
  - إضافة خلفية جديدة
  - تعديل خلفية موجودة
  - حذف خلفية
  - رفع الصور
  - معاينة مباشرة للتغييرات
  - تحكم في جميع خصائص الخلفية
- **الحالة:** ✅ مكتمل
- **ملاحظات المستخدم:** تم إنشاؤه مسبقاً

---

## 🔗 المرحلة الثالثة: التكامل مع النظام

### [ ] **المهمة 3.1:** إضافة تبويب في صفحة الإعدادات
- **الملف:** `src/app/admin/admin-setup/page.tsx`
- **الوصف:** إضافة تبويب "خلفيات الصفحات العامة"
- **التفاصيل المطلوبة:**
  - إضافة TabsTrigger جديد
  - إضافة TabsContent مع مكون PageBackgroundsManager
  - تحديث عدد الأعمدة في TabsList
  - إضافة import للمكون
- **الحالة:** ✅ مكتمل
- **ملاحظات المستخدم:** تم إضافته مسبقاً

### [ ] **المهمة 3.2:** تطبيق النظام على الصفحة الرئيسية
- **الملف:** `src/app/page.tsx`
- **الوصف:** تطبيق مكون PageBackground على الصفحة الرئيسية
- **التفاصيل:**
  - إضافة import لمكون PageBackground
  - تطبيق المكون مع pageName="home"
  - إضافة خلفية احتياطية
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 3.3:** تطبيق النظام على صفحة "من نحن"
- **الملف:** `src/app/about/page.tsx`
- **الوصف:** تطبيق مكون PageBackground على صفحة "من نحن"
- **التفاصيل:**
  - إضافة import لمكون PageBackground
  - تطبيق المكون مع pageName="about"
  - إضافة خلفية احتياطية
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 3.4:** تطبيق النظام على صفحة "اتصل بنا"
- **الملف:** `src/app/contact/page.tsx`
- **الوصف:** تطبيق مكون PageBackground على صفحة "اتصل بنا"
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 3.5:** تطبيق النظام على باقي الصفحات العامة
- **الملفات:** 
  - `src/app/programs/page.tsx`
  - `src/app/khatm-sessions/page.tsx`
  - `src/app/donations/page.tsx`
  - `src/app/login/page.tsx`
  - `src/app/register/page.tsx`
- **الوصف:** تطبيق مكون PageBackground على جميع الصفحات المتبقية
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 3.6:** تحديث نظام رفع الملفات
- **الملف:** `src/app/api/upload/route.ts`
- **الوصف:** إضافة دعم نوع "backgrounds" لرفع صور الخلفيات
- **التفاصيل:**
  - إضافة مجلد `public/uploads/backgrounds/`
  - تحديث منطق معالجة الملفات
  - إضافة التحقق من أنواع الملفات المسموحة
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

---

## 🔧 المرحلة الرابعة: التحسينات والاختبار

### [ ] **المهمة 4.1:** إضافة معالجة الأخطاء
- **الملفات:** جميع المكونات والـ APIs
- **الوصف:** إضافة معالجة شاملة للأخطاء
- **التفاصيل:**
  - رسائل خطأ واضحة
  - fallback للصور المفقودة
  - معالجة أخطاء الشبكة
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 4.2:** تحسين الأداء
- **الملفات:** جميع المكونات
- **الوصف:** تحسين أداء تحميل وعرض الخلفيات
- **التفاصيل:**
  - lazy loading للصور
  - تخزين مؤقت للخلفيات
  - ضغط الصور تلقائياً
- **الحالة:** ❌ لم يبدأ
- **ملاحظات المستخدم:** -

### [ ] **المهمة 4.3:** إضافة التوثيق
- **الملف:** `docs/page-backgrounds.md`
- **الوصف:** توثيق شامل لنظام خلفيات الصفحات
- **المحتوى:**
  - دليل الاستخدام للمدير
  - دليل التطوير للمطور
  - أمثلة عملية
  - استكشاف الأخطاء
- **الحالة:** ✅ مكتمل
- **ملاحظات المستخدم:** تم إنشاؤه مسبقاً

---

## 📝 ملاحظات التنفيذ

### الأولويات:
1. **عالية:** المرحلة الأولى (قاعدة البيانات والـ API)
2. **متوسطة:** المرحلة الثانية (مكونات الواجهة)
3. **متوسطة:** المرحلة الثالثة (التكامل)
4. **منخفضة:** المرحلة الرابعة (التحسينات)

### تفضيلات المستخدم:
- الحد الأدنى من الكلام أثناء التنفيذ
- الاستمرار المباشر في المهام
- عدم إضافة ميزات إضافية غير مطلوبة
- التركيز على تحسين الأنظمة القائمة

### نصائح للتنفيذ:
- ابدأ بالمرحلة الأولى واكملها بالكامل قبل الانتقال للتالية
- اختبر كل مهمة فور إكمالها
- حدث هذا الملف بعد إكمال كل مهمة
- استخدم codebase-retrieval للبحث عن الملفات ذات الصلة

---

## 🎯 معايير الإكمال

### للمهمة الواحدة:
- [ ] الكود مكتوب ويعمل بدون أخطاء
- [ ] تم اختبار الوظيفة الأساسية
- [ ] تم تحديث حالة المهمة في هذا الملف
- [ ] لا توجد مشاكل في TypeScript

### للمشروع كاملاً:
- [ ] جميع المهام مكتملة (16/16)
- [ ] النظام يعمل بشكل كامل
- [ ] تم اختبار جميع الوظائف
- [ ] التوثيق مكتمل ومحدث

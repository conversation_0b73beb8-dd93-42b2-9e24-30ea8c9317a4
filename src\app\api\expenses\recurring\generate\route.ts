import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { addDays, addWeeks, addMonths, addYears, format } from 'date-fns';

// POST /api/expenses/recurring/generate - توليد المصروفات الدورية المستحقة
export async function POST(req: NextRequest) {
  try {
    const today = new Date();

    // جلب المصروفات الدورية النشطة المستحقة
    const dueRecurringExpenses = await prisma.recurringExpense.findMany({
      where: {
        isActive: true,
        nextGenerationDate: {
          lte: today,
        },
        OR: [
          { endDate: null },
          { endDate: { gte: today } },
        ],
      },
      include: {
        category: true,
      },
    });

    if (dueRecurringExpenses.length === 0) {
      return NextResponse.json({
        message: 'لا توجد مصروفات دورية مستحقة للتوليد',
        generatedCount: 0,
      });
    }

    // الحصول على الخزينة
    const treasury = await prisma.treasury.findFirst();

    if (!treasury) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الخزينة' },
        { status: 404 }
      );
    }

    // توليد المصروفات
    const generatedExpenses = [];

    for (const recurringExpense of dueRecurringExpenses) {
      // إنشاء المصروف وتحديث الخزينة في معاملة واحدة
      const result = await prisma.$transaction(async (tx) => {
        // إنشاء المصروف
        const expense = await tx.expense.create({
          data: {
            purpose: `${recurringExpense.purpose} (دوري - ${format(recurringExpense.nextGenerationDate, 'yyyy-MM-dd')})`,
            amount: recurringExpense.amount,
            treasuryId: treasury.id,
            categoryId: recurringExpense.categoryId,
            date: recurringExpense.nextGenerationDate,
            notes: recurringExpense.notes,
          },
          include: {
            category: true,
          },
        });

        // تحديث رصيد الخزينة وإجمالي المصاريف
        await tx.treasury.update({
          where: { id: treasury.id },
          data: {
            balance: { decrement: recurringExpense.amount },
            totalExpense: { increment: recurringExpense.amount },
          },
        });

        // حساب تاريخ التوليد التالي
        const nextGenerationDate = calculateNextGenerationDate(
          recurringExpense.nextGenerationDate,
          recurringExpense.frequency,
          recurringExpense.interval
        );

        // تحديث المصروف الدوري
        const updatedRecurringExpense = await tx.recurringExpense.update({
          where: { id: recurringExpense.id },
          data: {
            lastGeneratedDate: recurringExpense.nextGenerationDate,
            nextGenerationDate,
            // تعطيل المصروف الدوري إذا تجاوز تاريخ الانتهاء
            isActive: recurringExpense.endDate
              ? nextGenerationDate <= recurringExpense.endDate
              : true,
          },
        });

        return {
          expense,
          updatedRecurringExpense,
        };
      });

      generatedExpenses.push(result.expense);
    }

    return NextResponse.json({
      message: `تم توليد ${generatedExpenses.length} مصروف دوري بنجاح`,
      generatedCount: generatedExpenses.length,
      generatedExpenses,
    });
  } catch (error) {
    console.error('خطأ في توليد المصروفات الدورية:', error);
    return NextResponse.json(
      { error: 'فشل في توليد المصروفات الدورية' },
      { status: 500 }
    );
  }
}

// حساب تاريخ التوليد التالي
function calculateNextGenerationDate(
  currentDate: Date,
  frequency: string,
  interval: number
): Date {
  switch (frequency) {
    case 'daily':
      return addDays(currentDate, interval);
    case 'weekly':
      return addWeeks(currentDate, interval);
    case 'monthly':
      return addMonths(currentDate, interval);
    case 'yearly':
      return addYears(currentDate, interval);
    default:
      return addMonths(currentDate, interval);
  }
}

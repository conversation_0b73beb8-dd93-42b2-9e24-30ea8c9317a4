import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// GET /api/parent-curriculum - جلب المنهج الدراسي لأبناء ولي الأمر المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'PARENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المستخدم والملف الشخصي
    const user = await prisma.user.findUnique({
      where: {
        id: userData.id
      },
      include: {
        profile: true
      }
    });

    if (!user || !user.profile) {
      console.error('لم يتم العثور على المستخدم أو الملف الشخصي:', userData.id);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المستخدم" },
        { status: 404 }
      );
    }

    // جلب معلومات ولي الأمر باستخدام اسم الملف الشخصي أو اسم المستخدم
    const parent = await prisma.parent.findFirst({
      where: {
        OR: [
          { name: user.profile.name },
          { name: userData.username }
        ]
      },
      include: {
        students: true
      }
    });

    if (!parent) {
      console.error('لم يتم العثور على ولي الأمر للمستخدم:', userData.username, 'مع الملف الشخصي:', user.profile.name);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات ولي الأمر" },
        { status: 404 }
      );
    }

    // التحقق من وجود أبناء
    if (!parent.students || parent.students.length === 0) {
      console.log('ولي الأمر موجود ولكن لا يوجد أبناء مسجلين:', parent.name);
      return NextResponse.json({
        children: [],
        message: "لا يوجد أبناء مسجلين لولي الأمر"
      });
    }

    // التحقق من وجود معرف الطالب والمادة في الاستعلام
    const searchParams = request.nextUrl.searchParams;
    const childId = searchParams.get('childId');
    const subjectId = searchParams.get('subjectId');

    // إذا تم تحديد معرف الطالب، تحقق من أنه ينتمي لولي الأمر
    if (childId) {
      const childIdNum = parseInt(childId);
      const isParentChild = parent.students.some(student => student.id === childIdNum);

      if (!isParentChild) {
        return NextResponse.json(
          { message: "غير مصرح بالوصول إلى بيانات هذا الطالب" },
          { status: 403 }
        );
      }

      // جلب بيانات الطالب المحدد
      const student = await prisma.student.findUnique({
        where: { id: childIdNum },
        include: {
          classe: true
        }
      });

      if (!student) {
        return NextResponse.json(
          { message: "لم يتم العثور على بيانات الطالب" },
          { status: 404 }
        );
      }

      // جلب المواد الدراسية للفصل الذي ينتمي إليه الطالب
      const classSubjects = await prisma.classSubject.findMany({
        where: {
          classeId: student.classeId ?? undefined
        },
        include: {
          teacherSubject: {
            include: {
              teacher: true,
              subject: true
            }
          }
        }
      });

      // تنسيق بيانات المواد الدراسية
      const subjects = classSubjects.map(cs => ({
        id: cs.teacherSubject.subject.id,
        name: cs.teacherSubject.subject.name,
        description: cs.teacherSubject.subject.description || '',
        teacherId: cs.teacherSubject.teacher.id,
        teacherName: cs.teacherSubject.teacher.name
      }));

      // إذا تم تحديد معرف المادة، جلب وحدات المنهج لهذه المادة
      if (subjectId) {
        const subjectIdNum = parseInt(subjectId);

        // التحقق من أن المادة ضمن مواد الطالب
        const isStudentSubject = subjects.some(subject => subject.id === subjectIdNum);

        if (!isStudentSubject) {
          return NextResponse.json(
            { message: "المادة غير متاحة لهذا الطالب" },
            { status: 404 }
          );
        }

        // جلب وحدات المنهج للمادة
        const curriculumUnits = await prisma.curriculumUnit.findMany({
          where: {
            subjectId: subjectIdNum
          },
          include: {
            lessons: {
              include: {
                resources: true
              }
            }
          },
          orderBy: {
            order: 'asc'
          }
        });

        // تنسيق بيانات وحدات المنهج
        const units = curriculumUnits.map(unit => ({
          id: unit.id,
          title: unit.title,
          description: unit.description || '',
          order: unit.order,
          lessons: unit.lessons.map(lesson => ({
            id: lesson.id,
            title: lesson.title,
            description: lesson.description || '',
            order: lesson.order,
            resources: lesson.resources.map(resource => ({
              id: resource.id,
              title: resource.title,
              type: resource.type,
              url: resource.url,
              // Remove the non-existent 'order' property
            }))
          }))
        }));

        // جلب بيانات المادة
        const subject = subjects.find(s => s.id === subjectIdNum);

        return NextResponse.json({
          student: {
            id: student.id,
            name: student.name,
            grade: student.classe?.name || 'غير محدد'
          },
          subject,
          units,
          message: "تم جلب بيانات المنهج بنجاح"
        });
      } else {
        // إذا لم يتم تحديد معرف المادة، إرجاع قائمة المواد فقط
        return NextResponse.json({
          student: {
            id: student.id,
            name: student.name,
            grade: student.classe?.name || 'غير محدد'
          },
          subjects,
          message: "تم جلب بيانات المواد الدراسية بنجاح"
        });
      }
    } else {
      // جلب ملخص المواد الدراسية لجميع الأبناء
      const childrenSubjects = await Promise.all(parent.students.map(async (student) => {
        // جلب بيانات الطالب
        const studentData = await prisma.student.findUnique({
          where: { id: student.id },
          include: {
            classe: true
          }
        });

        if (!studentData || !studentData.classeId) return null;

        // جلب المواد الدراسية للفصل
        const classSubjects = await prisma.classSubject.findMany({
          where: {
            classeId: studentData.classeId
          },
          include: {
            teacherSubject: {
              include: {
                teacher: true,
                subject: true
              }
            }
          }
        });

        // تنسيق بيانات المواد الدراسية
        const subjects = classSubjects.map(cs => ({
          id: cs.teacherSubject.subject.id,
          name: cs.teacherSubject.subject.name,
          description: cs.teacherSubject.subject.description || '',
          teacherId: cs.teacherSubject.teacher.id,
          teacherName: cs.teacherSubject.teacher.name
        }));

        return {
          id: student.id,
          name: student.name,
          grade: studentData.classe?.name || 'غير محدد',
          subjects
        };
      }));

      // فلترة القيم null
      const children = childrenSubjects.filter(child => child !== null);

      return NextResponse.json({
        children,
        message: "تم جلب بيانات المواد الدراسية بنجاح"
      });
    }
  } catch (error) {
    console.error('Error fetching parent curriculum:', error);
    // إضافة المزيد من التفاصيل للخطأ
    const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير معروف';
    return NextResponse.json(
      {
        message: "حدث خطأ أثناء جلب بيانات المنهج",
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

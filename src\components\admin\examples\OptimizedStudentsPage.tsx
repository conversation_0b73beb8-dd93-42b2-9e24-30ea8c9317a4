'use client';
import React, { useState } from 'react';
import { FaUserGraduate, FaPlus, FaDownload, FaFilter, FaSearch } from 'react-icons/fa';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons, useBulkPermissions } from '@/components/admin/BulkPermissionGuard';
import PermissionGuard from '@/components/admin/PermissionGuard';

// مثال على صفحة الطلاب المحسنة
const OptimizedStudentsPage: React.FC = () => {
  const [students, setStudents] = useState([
    { id: 1, name: 'أحمد محمد', level: 'المستوى الأول', status: 'نشط' },
    { id: 2, name: 'فاطمة علي', level: 'المستوى الثاني', status: 'نشط' },
    { id: 3, name: 'محمد أحمد', level: 'المستوى الثالث', status: 'متوقف' },
  ]);

  // استخدام useBulkPermissions للتحقق من عدة صلاحيات مرة واحدة
  const permissions = useBulkPermissions([
    'admin.students.create',
    'admin.students.edit',
    'admin.students.delete',
    'admin.students.export',
    'admin.students.import',
    'admin.students.view-details'
  ]);

  const handleAddStudent = () => {
    console.log('إضافة طالب جديد');
  };

  const handleEditStudent = (id: number) => {
    console.log('تعديل الطالب:', id);
  };

  const handleDeleteStudent = (id: number) => {
    console.log('حذف الطالب:', id);
  };

  const handleViewStudent = (id: number) => {
    console.log('عرض تفاصيل الطالب:', id);
  };

  const handleExportStudents = () => {
    console.log('تصدير بيانات الطلاب');
  };

  const handleImportStudents = () => {
    console.log('استيراد بيانات الطلاب');
  };

  const handleBulkDelete = () => {
    console.log('حذف الطلاب المحددين');
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.students.view">
      <div className="container mx-auto p-4">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h1 className="text-2xl font-bold mb-4 md:mb-0 flex items-center gap-2">
            <FaUserGraduate className="text-[var(--primary-color)]" />
            إدارة الطلاب
          </h1>

          {/* Quick Actions - محسن للأداء */}
          <QuickActionButtons
            entityType="students"
            actions={[
              {
                key: 'create',
                label: 'إضافة طالب',
                icon: <FaPlus />,
                onClick: handleAddStudent,
                variant: 'primary'
              },
              {
                key: 'export',
                label: 'تصدير',
                icon: <FaDownload />,
                onClick: handleExportStudents,
                variant: 'secondary'
              },
              {
                key: 'import',
                label: 'استيراد',
                icon: <FaDownload />,
                onClick: handleImportStudents,
                variant: 'secondary'
              }
            ]}
            className="flex-wrap"
          />
        </div>

        {/* Filters - يظهر فقط إذا كان لديه صلاحية البحث */}
        <PermissionGuard 
          requiredPermissions={['admin.students.view', 'admin.students.search']}
          requireAll={false}
        >
          <div className="bg-white p-4 rounded-lg shadow-md mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث عن طالب..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent"
                  />
                </div>
              </div>
              <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                <FaFilter />
                تصفية
              </button>
            </div>
          </div>
        </PermissionGuard>

        {/* Students Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الاسم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المستوى
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {students.map((student) => (
                  <tr key={student.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {student.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {student.level}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        student.status === 'نشط' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {student.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {/* استخدام OptimizedActionButtonGroup - محسن للأداء */}
                      <OptimizedActionButtonGroup
                        entityType="students"
                        onEdit={() => handleEditStudent(student.id)}
                        onDelete={() => handleDeleteStudent(student.id)}
                        onView={() => handleViewStudent(student.id)}
                        showEdit={true}
                        showDelete={true}
                        showView={true}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Bulk Actions - يظهر فقط إذا كان لديه صلاحيات الحذف */}
        <PermissionGuard requiredPermission="admin.students.delete">
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                تم تحديد 0 طالب
              </span>
              <button
                onClick={handleBulkDelete}
                className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm"
              >
                حذف المحدد
              </button>
            </div>
          </div>
        </PermissionGuard>

        {/* Statistics - يظهر فقط إذا كان لديه صلاحية عرض الإحصائيات */}
        <PermissionGuard requiredPermission="admin.students.statistics">
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">إجمالي الطلاب</h3>
              <p className="text-3xl font-bold text-[var(--primary-color)]">{students.length}</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">الطلاب النشطون</h3>
              <p className="text-3xl font-bold text-green-600">
                {students.filter(s => s.status === 'نشط').length}
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">الطلاب المتوقفون</h3>
              <p className="text-3xl font-bold text-red-600">
                {students.filter(s => s.status === 'متوقف').length}
              </p>
            </div>
          </div>
        </PermissionGuard>
      </div>
    </OptimizedProtectedRoute>
  );
};

export default OptimizedStudentsPage;

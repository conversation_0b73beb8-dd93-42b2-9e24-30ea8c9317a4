'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-toastify';
import { Loader2, Plus, Pencil, Trash } from 'lucide-react';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

type EvaluationCriteria = {
  id: number;
  name: string;
  weight: number;
  description: string | null;
};

export default function EvaluationCriteriaPage() {
  const [criteria, setCriteria] = useState<EvaluationCriteria[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCriteria, setSelectedCriteria] = useState<EvaluationCriteria | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    weight: 0,
    description: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchCriteria();
  }, []);

  const fetchCriteria = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/evaluation-criteria');
      if (!response.ok) throw new Error('Failed to fetch criteria');
      const data = await response.json();
      setCriteria(data.data || []);
    } catch (error) {
      console.error('Error fetching criteria:', error);
      toast.error('حدث خطأ أثناء جلب معايير التقييم');
    } finally {
      setLoading(false);
    }
  };

  const handleAddCriteria = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || formData.weight <= 0 || formData.weight > 1) {
        toast.error('يرجى إدخال اسم المعيار ووزن صحيح (بين 0 و 1)');
        return;
      }

      const response = await fetch('/api/evaluation-criteria', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add criteria');
      }

      toast.success('تم إضافة معيار التقييم بنجاح');
      setIsAddDialogOpen(false);
      setFormData({ name: '', weight: 0, description: '' });
      fetchCriteria();
    } catch (error) {
      console.error('Error adding criteria:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة معيار التقييم');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditCriteria = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || formData.weight <= 0 || formData.weight > 1) {
        toast.error('يرجى إدخال اسم المعيار ووزن صحيح (بين 0 و 1)');
        return;
      }

      if (!selectedCriteria) return;

      const response = await fetch('/api/evaluation-criteria', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: selectedCriteria.id,
          ...formData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update criteria');
      }

      toast.success('تم تحديث معيار التقييم بنجاح');
      setIsEditDialogOpen(false);
      setSelectedCriteria(null);
      fetchCriteria();
    } catch (error) {
      console.error('Error updating criteria:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث معيار التقييم');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteCriteria = async () => {
    try {
      setIsSubmitting(true);

      if (!selectedCriteria) return;

      const response = await fetch(`/api/evaluation-criteria?id=${selectedCriteria.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete criteria');
      }

      toast.success('تم حذف معيار التقييم بنجاح');
      setIsDeleteDialogOpen(false);
      setSelectedCriteria(null);
      fetchCriteria();
    } catch (error) {
      console.error('Error deleting criteria:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حذف معيار التقييم');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (criteria: EvaluationCriteria) => {
    setSelectedCriteria(criteria);
    setFormData({
      name: criteria.name,
      weight: criteria.weight,
      description: criteria.description || '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (criteria: EvaluationCriteria) => {
    setSelectedCriteria(criteria);
    setIsDeleteDialogOpen(true);
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.evaluation.criteria.view">
      <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة معايير التقييم</h1>
        <QuickActionButtons
          entityType="evaluation.criteria"
          actions={[
            {
              key: 'create',
              label: 'إضافة معيار جديد',
              icon: <Plus size={16} />,
              onClick: () => {
                setFormData({ name: '', weight: 0, description: '' });
                setIsAddDialogOpen(true);
              },
              variant: 'primary'
            }
          ]}
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      ) : criteria.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-gray-500">لا توجد معايير تقييم حتى الآن</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">المعرف</TableHead>
                <TableHead className="text-right">اسم المعيار</TableHead>
                <TableHead className="text-right">الوزن</TableHead>
                <TableHead className="text-right">الوصف</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {criteria.map((criterion) => (
                <TableRow key={criterion.id}>
                  <TableCell>{criterion.id}</TableCell>
                  <TableCell>{criterion.name}</TableCell>
                  <TableCell>{criterion.weight}</TableCell>
                  <TableCell>{criterion.description || '-'}</TableCell>
                  <TableCell>
                    <OptimizedActionButtonGroup
                      entityType="evaluation.criteria"
                      onEdit={() => openEditDialog(criterion)}
                      onDelete={() => openDeleteDialog(criterion)}
                      showEdit={true}
                      showDelete={true}
                      size="sm"
                      className="gap-2"
                      deleteConfirmTitle="هل أنت متأكد من حذف هذا المعيار؟"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => !open && setIsAddDialogOpen(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إضافة معيار تقييم جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل معيار التقييم الجديد</DialogDescription>
          </DialogHeader>
          <form id="addCriteriaForm" onSubmit={handleAddCriteria} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم المعيار</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوزن (0-1)</label>
              <Input
                type="number"
                step="0.01"
                min="0"
                max="1"
                value={formData.weight}
                onChange={(e) => setFormData({ ...formData, weight: parseFloat(e.target.value) })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل معيار التقييم</DialogTitle>
            <DialogDescription>قم بتعديل تفاصيل معيار التقييم</DialogDescription>
          </DialogHeader>
          <form id="editCriteriaForm" onSubmit={handleEditCriteria} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم المعيار</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوزن (0-1)</label>
              <Input
                type="number"
                step="0.01"
                min="0"
                max="1"
                value={formData.weight}
                onChange={(e) => setFormData({ ...formData, weight: parseFloat(e.target.value) })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف معيار التقييم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف معيار التقييم {selectedCriteria ? selectedCriteria.name : ''} ؟
              <br />
              هذا الإجراء لا يمكن التراجع عنه.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteCriteria}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحذف...' : 'حذف'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

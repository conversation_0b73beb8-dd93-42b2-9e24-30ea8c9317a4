import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/expenses/:id - الحصول على مصروف محدد
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود المصروف
    const expense = await prisma.expense.findUnique({
      where: { id },
      include: {
        category: true,
      },
    });

    if (!expense) {
      return NextResponse.json(
        { error: 'المصروف غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(expense);
  } catch (error) {
    console.error('خطأ في جلب المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المصروف' },
      { status: 500 }
    );
  }
}

// PUT /api/expenses/:id - تحديث مصروف
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);
    const body = await req.json();
    const { purpose, amount, categoryId, date, receipt, notes } = body;

    if (!purpose || !amount) {
      return NextResponse.json(
        { error: 'الغرض والمبلغ مطلوبان' },
        { status: 400 }
      );
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود المصروف
    const existingExpense = await prisma.expense.findUnique({
      where: { id }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'المصروف غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود الفئة إذا تم تحديدها
    if (categoryId) {
      const category = await prisma.expenseCategory.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'فئة المصروفات غير موجودة' },
          { status: 400 }
        );
      }
    }

    // الحصول على الخزينة
    const treasury = await prisma.treasury.findUnique({
      where: { id: existingExpense.treasuryId }
    });

    if (!treasury) {
      return NextResponse.json(
        { error: 'الخزينة غير موجودة' },
        { status: 404 }
      );
    }

    // حساب الفرق في المبلغ
    const amountDifference = amount - existingExpense.amount;

    // التحقق من وجود رصيد كافٍ إذا كان المبلغ الجديد أكبر
    if (amountDifference > 0 && treasury.balance < amountDifference) {
      return NextResponse.json(
        { error: 'الرصيد غير كافٍ لزيادة مبلغ المصروف' },
        { status: 400 }
      );
    }

    // تحديث المصروف وتحديث الخزينة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // تحديث المصروف
      const updatedExpense = await tx.expense.update({
        where: { id },
        data: {
          purpose,
          amount,
          categoryId: categoryId || null,
          date: date ? new Date(date) : existingExpense.date,
          receipt,
          notes,
        },
        include: {
          category: true,
        },
      });

      // تحديث رصيد الخزينة وإجمالي المصاريف إذا تغير المبلغ
      if (amountDifference !== 0) {
        await tx.treasury.update({
          where: { id: treasury.id },
          data: {
            balance: { decrement: amountDifference },
            totalExpense: { increment: amountDifference },
          },
        });
      }

      return updatedExpense;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('خطأ في تحديث المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المصروف' },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses/:id - حذف مصروف
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود المصروف
    const expense = await prisma.expense.findUnique({
      where: { id }
    });

    if (!expense) {
      return NextResponse.json(
        { error: 'المصروف غير موجود' },
        { status: 404 }
      );
    }

    // حذف المصروف وتحديث الخزينة في معاملة واحدة
    await prisma.$transaction(async (tx) => {
      // حذف المصروف
      await tx.expense.delete({
        where: { id }
      });

      // تحديث رصيد الخزينة وإجمالي المصاريف
      await tx.treasury.update({
        where: { id: expense.treasuryId },
        data: {
          balance: { increment: expense.amount },
          totalExpense: { decrement: expense.amount },
        },
      });
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المصروف' },
      { status: 500 }
    );
  }
}

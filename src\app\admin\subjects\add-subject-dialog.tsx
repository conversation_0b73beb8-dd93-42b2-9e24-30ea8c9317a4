'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface Level {
  id: number;
  name: string;
  description?: string;
  order: number;
}

interface AddSubjectDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  levels: Level[];
}

export default function AddSubjectDialog({ isOpen, onCloseAction, onSuccessAction, levels }: AddSubjectDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    levelId: '',
    hasStudyPlan: false
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, hasStudyPlan: checked }));
  };

  const handleAddSubject = async () => {
    if (!formData.name.trim()) {
      toast.error('الرجاء إدخال اسم المادة');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/subjects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || null,
          levelId: formData.levelId && formData.levelId !== "0" ? parseInt(formData.levelId) : null,
          hasStudyPlan: formData.hasStudyPlan
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add subject');
      }

      toast.success('تمت إضافة المادة بنجاح');
      setFormData({
        name: '',
        description: '',
        levelId: '',
        hasStudyPlan: false
      });
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error adding subject:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة المادة');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleAddSubject}
      disabled={isLoading || !formData.name.trim()}
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري الإضافة...' : 'إضافة'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إضافة مادة جديدة"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
        <div className="space-y-2">
          <Label>اسم المادة</Label>
          <Input
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="أدخل اسم المادة"
          />
        </div>

        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف المادة"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label>المستوى (اختياري)</Label>
          <Select
            value={formData.levelId}
            onValueChange={(value) => handleSelectChange('levelId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="اختر المستوى" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">بدون مستوى</SelectItem>
              {levels.map(level => (
                <SelectItem key={level.id} value={level.id.toString()}>
                  {level.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2 rtl:space-x-reverse pt-2">
          <Switch
            id="hasStudyPlan"
            checked={formData.hasStudyPlan}
            onCheckedChange={handleSwitchChange}
          />
          <Label htmlFor="hasStudyPlan" className="cursor-pointer">
            لديها خطة دراسية
          </Label>
        </div>
      </div>
    </AnimatedDialog>
  );
}

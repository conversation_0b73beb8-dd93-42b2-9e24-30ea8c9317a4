'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface Level {
  id: number;
  name: string;
  description?: string;
  order: number;
}

interface EditLevelDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  level: Level | null;
}

export default function EditLevelDialog({ isOpen, onCloseAction, onSuccessAction, level }: EditLevelDialogProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    order: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (level) {
      setFormData({
        name: level.name,
        description: level.description || '',
        order: level.order.toString()
      });
    }
  }, [level]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleUpdateLevel = async () => {
    if (!level) return;

    if (!formData.name.trim()) {
      toast.error('الرجاء إدخال اسم المستوى');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/levels/${level.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || null,
          order: formData.order ? parseInt(formData.order) : level.order
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update level');
      }

      toast.success('تم تحديث المستوى بنجاح');
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error updating level:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث المستوى');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleUpdateLevel}
    disabled={
        isLoading ||
        !formData.name.trim() ||
        (!!level &&
          formData.name === level.name &&
          formData.description === (level.description || '') &&
          formData.order === level.order.toString())
      }
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري التحديث...' : 'حفظ التغييرات'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="تعديل المستوى"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <Label>اسم المستوى</Label>
          <Input
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="أدخل اسم المستوى"
          />
        </div>

        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف المستوى"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label>الترتيب</Label>
          <Input
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="أدخل ترتيب المستوى"
            min={1}
          />
        </div>
      </div>
    </AnimatedDialog>
  );
}

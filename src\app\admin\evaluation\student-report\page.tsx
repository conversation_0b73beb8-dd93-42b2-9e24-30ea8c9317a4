'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Loader2, FileText, Printer, Download, User, GraduationCap, TrendingUp, Award, BookOpen } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import { exportStudentReportToExcel } from '@/lib/exportUtils';
import { getGradeLevelByScore, calculatePercentage, getPassStatus } from '@/lib/grading-system';
import { useSchoolSettings } from '@/utils/school-settings';

type StudentReport = {
  student: {
    id: number;
    name: string;
    username: string;
    age: number;
    phone?: string;
    classe: {
      id: number;
      name: string;
    } | null;
    guardian: {
      id: number;
      name: string;
      phone: string;
    } | null;
  };
  attendanceStats: {
    totalDays: number;
    presentDays: number;
    absentDays: number;
    excusedDays: number;
    presentRate: number;
    absentRate: number;
    excusedRate: number;
    recentAttendance: Array<{
      date: string;
      status: string;
      hisass?: string;
    }>;
  };
  statistics: {
    totalExams: number;
    passedExams: number;
    failedExams: number;
    excellentExams: number;
    averageGrade: number;
    averagePercentage: number;
    highestGrade: number;
    lowestGrade: number;
    passRate: number;
  };
  typeStatistics: Array<{
    type: string;
    totalExams: number;
    averageGrade: number;
    passedExams: number;
    passRate: number;
  }>;
  allResults: Array<{
    id: number;
    examId: number;
    examDescription: string;
    evaluationType: string;
    month: string;
    grade: number;
    maxPoints: number;
    percentage: number;
    status: string;
    performance: string;
    subject: string;
    examType: string;
    createdAt: string;
  }>;
  reportGeneratedAt: string;
};

export default function StudentReportPage() {
  const [reportData, setReportData] = useState<StudentReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [students, setStudents] = useState<{id: number, name: string, username: string, classe?: {name: string}}[]>([]);
  const [classes, setClasses] = useState<{id: number, name: string}[]>([]);
  const [studentSearchQuery, setStudentSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    studentId: '',
    classeId: '',
    month: '',
    evaluationType: ''
  });
  const [evaluationTypes] = useState([
    { id: 'QURAN_MEMORIZATION', name: 'حفظ القرآن' },
    { id: 'QURAN_RECITATION', name: 'تلاوة القرآن' },
    { id: 'ISLAMIC_STUDIES', name: 'الدراسات الإسلامية' },
    { id: 'ARABIC_LANGUAGE', name: 'اللغة العربية' },
    { id: 'GENERAL_KNOWLEDGE', name: 'المعرفة العامة' },
    { id: 'BEHAVIOR_EVALUATION', name: 'تقييم السلوك' },
    { id: 'ATTENDANCE_EVALUATION', name: 'تقييم الحضور' },
    { id: 'PARTICIPATION_EVALUATION', name: 'تقييم المشاركة' }
  ]);
  const { settings: schoolSettings } = useSchoolSettings();

  useEffect(() => {
    fetchStudents();
    fetchClasses();
  }, []);

  const fetchStudents = async () => {
    try {
      const response = await fetch('/api/students?limit=1000');
      if (response.ok) {
        const data = await response.json();
        const studentsData = Array.isArray(data) ? data : data.data || data.students || [];
        setStudents(studentsData);
      }
    } catch (error) {
      console.error('Error fetching students:', error);
    }
  };

  // تصفية الطلاب بناءً على البحث
  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(studentSearchQuery.toLowerCase()) ||
    student.username.toLowerCase().includes(studentSearchQuery.toLowerCase())
  );

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (response.ok) {
        const data = await response.json();
        setClasses(Array.isArray(data) ? data : data.data || []);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const generateReport = async () => {
    if (!filters.studentId) {
      toast.error('يرجى اختيار طالب');
      return;
    }

    try {
      setLoading(true);

      const params = new URLSearchParams();
      params.append('studentId', filters.studentId);

      // فقط إضافة المعاملات إذا كانت لها قيم صحيحة
      if (filters.classeId && filters.classeId !== '' && filters.classeId !== 'all') {
        params.append('classeId', filters.classeId);
      }
      if (filters.month && filters.month !== '') {
        params.append('month', filters.month);
      }
      if (filters.evaluationType && filters.evaluationType !== '' && filters.evaluationType !== 'all') {
        params.append('evaluationType', filters.evaluationType);
      }

      const response = await fetch(`/api/evaluation/student-report?${params.toString()}`);

      if (!response.ok) {
        throw new Error('فشل في إنشاء كشف الدرجات');
      }

      const data = await response.json();

      if (data.success) {
        setReportData(data.data);
        toast.success('تم إنشاء كشف الدرجات بنجاح');
      } else {
        throw new Error(data.message || 'فشل في إنشاء كشف الدرجات');
      }
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('حدث خطأ أثناء إنشاء كشف الدرجات');
    } finally {
      setLoading(false);
    }
  };

  const printReport = () => {
    if (!reportData) {
      toast.error('لا توجد بيانات للطباعة');
      return;
    }

    try {
      // إنشاء نافذة جديدة للطباعة (نفس طريقة وصل المدفوعات)
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast.error('فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
        return;
      }

      // إنشاء محتوى كشف الدرجات للطباعة
      const reportContent = `
        <html dir="rtl">
          <head>
            <title>كشف درجات الطالب - ${reportData.student.name}</title>
            <style>
              @media print {
                body { margin: 0; padding: 10px; }
                @page { margin: 1cm; size: A4 portrait; }
              }
              body {
                font-family: Arial, sans-serif;
                background: white;
                color: black;
                margin: 0;
                padding: 20px;
                line-height: 1.4;
              }
              .report-container {
                max-width: 100%;
                margin: 0 auto;
              }
              .header {
                text-align: center;
                margin-bottom: 20px;
                padding: 15px;
                border: 2px solid black;
              }
              .header h1 {
                font-size: 24px;
                margin: 0 0 10px 0;
                border-bottom: 2px solid black;
                padding-bottom: 10px;
              }
              .school-name {
                font-size: 18px;
                font-weight: bold;
                margin: 10px 0;
              }
              .academic-year {
                font-size: 14px;
                margin: 5px 0;
              }
              .student-info {
                margin: 20px 0;
                border: 2px solid black;
              }
              .student-info-header {
                background: #f0f0f0;
                padding: 8px;
                border-bottom: 1px solid black;
                text-align: center;
                font-weight: bold;
              }
              .student-info-content {
                padding: 15px;
              }
              .info-row {
                display: flex;
                margin-bottom: 10px;
                align-items: center;
              }
              .info-label {
                font-weight: bold;
                margin-left: 10px;
                min-width: 100px;
              }
              .info-value {
                border-bottom: 1px dotted black;
                flex: 1;
                text-align: center;
                padding: 2px 5px;
              }
              .grades-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                border: 2px solid black;
              }
              .grades-table-header {
                background: #f0f0f0;
                padding: 8px;
                border-bottom: 1px solid black;
                text-align: center;
                font-weight: bold;
              }
              .grades-table th,
              .grades-table td {
                border: 1px solid black;
                padding: 6px 4px;
                text-align: center;
                font-size: 11px;
              }
              .grades-table th {
                background: #f0f0f0;
                font-weight: bold;
                font-size: 12px;
              }
              .average-row {
                background: #fff3cd;
                font-weight: bold;
              }
              .average-row td {
                border-top: 2px solid black;
                font-size: 12px;
              }
              .no-results {
                text-align: center;
                padding: 40px;
                color: #666;
              }
              .signatures {
                margin-top: 40px;
                display: flex;
                justify-content: space-between;
                text-align: center;
              }
              .signature-box {
                width: 30%;
              }
              .signature-title {
                font-weight: bold;
                margin-bottom: 40px;
              }
              .signature-line {
                border-top: 1px solid black;
                padding-top: 5px;
                font-size: 12px;
              }
              .footer-info {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
              }
            </style>
          </head>
          <body>
            <div class="report-container">
              <!-- Header -->
              <div class="header">
                <h1>كشف النقاط</h1>
                <div class="school-name">${schoolSettings?.siteName || 'مدرسة القرآن الكريم'}</div>
                ${schoolSettings?.contactInfo?.address ? `<div class="school-address" style="font-size: 14px; margin: 5px 0;">${schoolSettings.contactInfo.address}</div>` : ''}
                ${schoolSettings?.contactInfo?.phone ? `<div class="school-phone" style="font-size: 12px; margin: 5px 0;">الهاتف: ${schoolSettings.contactInfo.phone}</div>` : ''}
                <div class="academic-year">السنة الدراسية: ${new Date().getFullYear()}-${new Date().getFullYear() + 1}</div>
              </div>

              <!-- Student Info -->
              <div class="student-info">
                <div class="student-info-header">بيانات التلميذ</div>
                <div class="student-info-content">
                  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                    <div class="info-row">
                      <span class="info-label">الاسم واللقب:</span>
                      <span class="info-value">${reportData.student.name}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">رقم التسجيل:</span>
                      <span class="info-value">${reportData.student.username}</span>
                    </div>
                  </div>
                  <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                    <div class="info-row">
                      <span class="info-label">القسم:</span>
                      <span class="info-value">${reportData.student.classe?.name || 'غير محدد'}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">العمر:</span>
                      <span class="info-value">${reportData.student.age} سنة</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">تاريخ الميلاد:</span>
                      <span class="info-value">___/___/______</span>
                    </div>
                  </div>
                  ${reportData.student.guardian ? `
                  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="info-row">
                      <span class="info-label">ولي الأمر:</span>
                      <span class="info-value">${reportData.student.guardian.name}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">هاتف ولي الأمر:</span>
                      <span class="info-value">${reportData.student.guardian.phone || ''}</span>
                    </div>
                  </div>
                  ` : ''}
                </div>
              </div>

              <!-- Grades Table -->
              <div style="border: 2px solid black;">
                <div class="grades-table-header">كشف النقاط والتقديرات</div>
                <table class="grades-table">
                  <thead>
                    <tr>
                      <th>المادة/نوع التقييم</th>
                      <th>الشهر</th>
                      <th>الدرجة</th>
                      <th>النقاط الكاملة</th>
                      <th>النسبة المئوية</th>
                      <th>التقدير</th>
                      <th>الحالة</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${reportData.allResults.length === 0 ? `
                      <tr>
                        <td colspan="7" class="no-results">
                          لا توجد نتائج امتحانات<br>
                          <small>لم يتم العثور على امتحانات لهذا الطالب بالمعايير المحددة</small>
                        </td>
                      </tr>
                    ` : reportData.allResults.map(result => {
                      const gradeLevel = getGradeLevelByScore(result.grade, result.maxPoints);
                      const status = getPassStatus(result.grade, result.maxPoints);
                      const isPass = status !== 'FAILED';

                      return `
                        <tr>
                          <td style="font-weight: bold;">${getEvaluationTypeLabel(result.evaluationType)}</td>
                          <td>${result.month}</td>
                          <td style="font-weight: bold; color: #2563eb;">${result.grade}</td>
                          <td>${result.maxPoints}</td>
                          <td style="font-weight: bold;">${result.percentage}%</td>
                          <td>${gradeLevel.nameAr}</td>
                          <td style="color: ${isPass ? '#059669' : '#dc2626'}; font-weight: bold;">
                            ${isPass ? 'نجح' : 'راسب'}
                          </td>
                        </tr>
                      `;
                    }).join('')}

                    ${reportData.allResults.length > 0 ? `
                      <tr class="average-row">
                        <td style="font-weight: bold; font-size: 14px;">المعدل العام</td>
                        <td style="font-weight: bold;">جميع الأشهر</td>
                        <td style="font-weight: bold; color: #dc2626; font-size: 14px;">${reportData.statistics.averageGrade}</td>
                        <td style="font-weight: bold;">20</td>
                        <td style="font-weight: bold;">${Math.round((reportData.statistics.averageGrade / 20) * 100)}%</td>
                        <td style="font-weight: bold;">${getGradeLevelByScore(reportData.statistics.averageGrade, 20).nameAr}</td>
                        <td style="font-weight: bold; color: ${reportData.statistics.passRate >= 80 ? '#059669' : '#d97706'};">
                          ${reportData.statistics.passRate}% نجاح
                        </td>
                      </tr>
                    ` : ''}
                  </tbody>
                </table>
              </div>

              <!-- Summary Info -->
              ${reportData.allResults.length > 0 ? `
              <div style="padding: 15px; border: 1px solid black; margin-top: 20px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; font-size: 12px;">
                  <div>
                    <strong>عدد الامتحانات:</strong> ${reportData.statistics.totalExams}<br>
                    <strong>الامتحانات الناجحة:</strong> ${reportData.statistics.passedExams}
                  </div>
                  <div>
                    <strong>معدل النجاح:</strong> ${reportData.statistics.passRate}%<br>
                    <strong>أعلى درجة:</strong> ${reportData.statistics.highestGrade}
                  </div>
                  <div>
                    <strong>أقل درجة:</strong> ${reportData.statistics.lowestGrade}<br>
                    <strong>التاريخ:</strong> ${new Date(reportData.reportGeneratedAt).toLocaleDateString('fr-FR')}
                  </div>
                </div>
              </div>
              ` : ''}

              <!-- Attendance Statistics for Print -->
              ${reportData.attendanceStats.totalDays > 0 ? `
              <div style="border: 2px solid black; margin-top: 20px;">
                <div style="background-color: #f5f5f5; padding: 10px; border-bottom: 1px solid black; text-align: center;">
                  <strong>إحصائيات الحضور والغياب</strong>
                </div>
                <div style="padding: 15px;">
                  <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px; text-align: center;">
                    <div>
                      <strong>إجمالي الأيام:</strong><br>
                      <span style="font-size: 18px; color: #2563eb; font-weight: bold;">${reportData.attendanceStats.totalDays}</span>
                    </div>
                    <div>
                      <strong>أيام الحضور:</strong><br>
                      <span style="font-size: 18px; color: #059669; font-weight: bold;">${reportData.attendanceStats.presentDays}</span>
                    </div>
                    <div>
                      <strong>أيام الغياب:</strong><br>
                      <span style="font-size: 18px; color: #dc2626; font-weight: bold;">${reportData.attendanceStats.absentDays}</span>
                    </div>
                    <div>
                      <strong>الغياب بعذر:</strong><br>
                      <span style="font-size: 18px; color: #d97706; font-weight: bold;">${reportData.attendanceStats.excusedDays}</span>
                    </div>
                  </div>
                  <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; text-align: center; border-top: 1px solid #ccc; padding-top: 15px;">
                    <div>
                      <strong>نسبة الحضور:</strong><br>
                      <span style="font-size: 16px; color: #059669; font-weight: bold;">${reportData.attendanceStats.presentRate}%</span>
                    </div>
                    <div>
                      <strong>نسبة الغياب:</strong><br>
                      <span style="font-size: 16px; color: #dc2626; font-weight: bold;">${reportData.attendanceStats.absentRate}%</span>
                    </div>
                    <div>
                      <strong>نسبة الغياب بعذر:</strong><br>
                      <span style="font-size: 16px; color: #d97706; font-weight: bold;">${reportData.attendanceStats.excusedRate}%</span>
                    </div>
                  </div>
                </div>
              </div>
              ` : ''}

              <!-- Signatures -->
              <div class="signatures">
                <div class="signature-box">
                  <div class="signature-title">أستاذ القسم</div>
                  <div class="signature-line">التوقيع والختم</div>
                </div>
                <div class="signature-box">
                  <div class="signature-title">مدير المدرسة</div>
                  <div class="signature-line">التوقيع والختم</div>
                </div>
                <div class="signature-box">
                  <div class="signature-title">ولي الأمر</div>
                  <div class="signature-line">التوقيع</div>
                </div>
              </div>

              <!-- Footer -->
              <div class="footer-info">
                <p>السنة الدراسية: ${new Date().getFullYear()}-${new Date().getFullYear() + 1}</p>
                <p>تم إنشاء هذا الكشف بتاريخ: ${new Date(reportData.reportGeneratedAt).toLocaleDateString('fr-FR')}</p>
              </div>
            </div>

            <script>
              // دوال مساعدة للطباعة
              function getEvaluationTypeLabel(type) {
                const evaluationTypes = {
                  'QURAN_MEMORIZATION': 'حفظ القرآن الكريم',
                  'QURAN_RECITATION': 'تلاوة وتجويد',
                  'ISLAMIC_STUDIES': 'الدراسات الإسلامية',
                  'ARABIC_LANGUAGE': 'اللغة العربية',
                  'GENERAL_KNOWLEDGE': 'المعرفة العامة',
                  'BEHAVIOR_EVALUATION': 'تقييم السلوك',
                  'ATTENDANCE_EVALUATION': 'تقييم الحضور',
                  'PARTICIPATION_EVALUATION': 'تقييم المشاركة'
                };
                return evaluationTypes[type] || type;
              }

              function getGradeLevelByScore(score, maxPoints) {
                const percentage = (score / maxPoints) * 100;
                if (percentage >= 90) return { nameAr: 'ممتاز', color: '#059669' };
                if (percentage >= 80) return { nameAr: 'جيد جداً', color: '#2563eb' };
                if (percentage >= 70) return { nameAr: 'جيد', color: '#d97706' };
                if (percentage >= 60) return { nameAr: 'مقبول', color: '#ea580c' };
                if (percentage >= 50) return { nameAr: 'ضعيف', color: '#dc2626' };
                return { nameAr: 'راسب', color: '#dc2626' };
              }

              function getPassStatus(score, maxPoints) {
                const percentage = (score / maxPoints) * 100;
                return percentage >= 50 ? 'PASSED' : 'FAILED';
              }

              window.onload = () => {
                window.print();
                window.onafterprint = () => window.close();
              };
            </script>
          </body>
        </html>
      `;

      // كتابة المحتوى إلى النافذة الجديدة وتشغيل الطباعة
      printWindow.document.open();
      printWindow.document.write(reportContent);
      printWindow.document.close();

      toast.success('تم فتح نافذة الطباعة');
    } catch (error) {
      console.error('Error printing report:', error);
      toast.error('تعذر طباعة كشف الدرجات');
    }
  };

  const getEvaluationTypeLabel = (type: string) => {
    const found = evaluationTypes.find(et => et.id === type);
    return found ? found.name : type;
  };



  return (
    <ProtectedRoute requiredPermission="admin.evaluation.reports.view">
      <div className="container mx-auto p-4 space-y-6">
        <div className="flex justify-between items-center no-print">
          <h1 className="text-3xl font-bold text-[var(--primary-color)]">كشف درجات الطالب</h1>
          <div className="flex gap-2">
            {reportData && (
              <>
                <Button onClick={printReport} className="bg-green-600 hover:bg-green-700">
                  <Printer className="ml-2 h-4 w-4" />
                  طباعة محسنة
                </Button>
                <Button onClick={() => {
                  try {
                    exportStudentReportToExcel(reportData);
                    toast.success('تم تصدير كشف الدرجات إلى Excel بنجاح');
                  } catch (error) {
                    console.error('Error exporting to Excel:', error);
                    toast.error('حدث خطأ أثناء تصدير كشف الدرجات');
                  }
                }} className="bg-blue-600 hover:bg-blue-700">
                  <Download className="ml-2 h-4 w-4" />
                  تصدير Excel
                </Button>
                <Button onClick={() => {
                  const dataStr = JSON.stringify({
                    ...reportData,
                    exportedAt: new Date().toISOString()
                  }, null, 2);
                  const dataBlob = new Blob([dataStr], {type: 'application/json'});
                  const url = URL.createObjectURL(dataBlob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `student-report-${reportData.student.name}-${new Date().toISOString().split('T')[0]}.json`;
                  link.click();
                  toast.success('تم تصدير البيانات بصيغة JSON');
                }} variant="outline">
                  <Download className="ml-2 h-4 w-4" />
                  تصدير JSON
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Filters */}
        <Card className="no-print">
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="ml-2 h-5 w-5" />
              اختيار الطالب والمعايير
            </CardTitle>
            <CardDescription>اختر الطالب والمعايير لإنشاء كشف الدرجات</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">الطالب *</label>
                <div className="space-y-2">
                  <Input
                    type="text"
                    placeholder="ابحث عن الطالب بالاسم..."
                    value={studentSearchQuery}
                    onChange={(e) => setStudentSearchQuery(e.target.value)}
                    className="text-right"
                  />
                  <Select value={filters.studentId} onValueChange={(value) => setFilters({...filters, studentId: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="اختر الطالب" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredStudents.length === 0 ? (
                        <SelectItem value="" disabled>
                          {studentSearchQuery ? 'لا توجد نتائج للبحث' : 'لا يوجد طلاب'}
                        </SelectItem>
                      ) : (
                        filteredStudents.map(student => (
                          <SelectItem key={student.id} value={student.id.toString()}>
                            {student.name} ({student.username}) {student.classe && `- ${student.classe.name}`}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الفصل</label>
                <Select value={filters.classeId} onValueChange={(value) => setFilters({...filters, classeId: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الفصول" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفصول</SelectItem>
                    {classes.map(classe => (
                      <SelectItem key={classe.id} value={classe.id.toString()}>{classe.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الشهر</label>
                <Input
                  type="month"
                  value={filters.month}
                  onChange={(e) => setFilters({...filters, month: e.target.value})}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">نوع التقييم</label>
                <Select value={filters.evaluationType} onValueChange={(value) => setFilters({...filters, evaluationType: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الأنواع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    {evaluationTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mt-4">
              <Button onClick={generateReport} disabled={loading || !filters.studentId}>
                {loading ? (
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                ) : (
                  <FileText className="ml-2 h-4 w-4" />
                )}
                إنشاء كشف الدرجات
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Report Header */}
        {reportData && (
          <div className="print:block">
            {/* Header for Print - Private School Style */}
            <div className="text-center mb-6 print:mb-8 border-2 border-black p-4 print:border-black">
              <div className="text-center mb-4">
                <h1 className="text-2xl font-bold border-b-2 border-black pb-2 mb-4">كشف النقاط</h1>
                <div className="text-lg font-semibold mb-2">{schoolSettings?.siteName || 'مدرسة القرآن الكريم'}</div>
                {schoolSettings?.contactInfo?.address && (
                  <div className="text-sm mb-1">{schoolSettings.contactInfo.address}</div>
                )}
                {schoolSettings?.contactInfo?.phone && (
                  <div className="text-xs mb-2">الهاتف: {schoolSettings.contactInfo.phone}</div>
                )}
                <p className="text-sm">السنة الدراسية: {new Date().getFullYear()}-{new Date().getFullYear() + 1}</p>
              </div>
            </div>

            {/* Student Info - Traditional Style */}
            <div className="mb-6 border-2 border-black print:border-black">
              <div className="bg-gray-100 p-2 border-b border-black">
                <h3 className="font-bold text-center">بيانات التلميذ</h3>
              </div>
              <div className="p-4">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="flex">
                    <span className="font-bold ml-2">الاسم واللقب:</span>
                    <span className="border-b border-dotted border-black flex-1 text-center">{reportData.student.name}</span>
                  </div>
                  <div className="flex">
                    <span className="font-bold ml-2">رقم التسجيل:</span>
                    <span className="border-b border-dotted border-black flex-1 text-center">{reportData.student.username}</span>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="flex">
                    <span className="font-bold ml-2">القسم:</span>
                    <span className="border-b border-dotted border-black flex-1 text-center">{reportData.student.classe?.name || 'غير محدد'}</span>
                  </div>
                  <div className="flex">
                    <span className="font-bold ml-2">العمر:</span>
                    <span className="border-b border-dotted border-black flex-1 text-center">{reportData.student.age} سنة</span>
                  </div>
                  <div className="flex">
                    <span className="font-bold ml-2">تاريخ الميلاد:</span>
                    <span className="border-b border-dotted border-black flex-1 text-center">___/___/______</span>
                  </div>
                </div>
                {reportData.student.guardian && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex">
                      <span className="font-bold ml-2">ولي الأمر:</span>
                      <span className="border-b border-dotted border-black flex-1 text-center">{reportData.student.guardian.name}</span>
                    </div>
                    <div className="flex">
                      <span className="font-bold ml-2">هاتف ولي الأمر:</span>
                      <span className="border-b border-dotted border-black flex-1 text-center">{reportData.student.guardian.phone}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Statistics Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <Card className="print:shadow-none print:border">
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <BookOpen className="h-8 w-8 text-blue-500" />
                  </div>
                  <p className="text-2xl font-bold">{reportData.statistics.totalExams}</p>
                  <p className="text-sm text-gray-500">إجمالي الامتحانات</p>
                </CardContent>
              </Card>

              <Card className="print:shadow-none print:border">
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <TrendingUp className="h-8 w-8 text-green-500" />
                  </div>
                  <p className="text-2xl font-bold">{reportData.statistics.averagePercentage.toFixed(1)}%</p>
                  <p className="text-sm text-gray-500">المتوسط العام</p>
                </CardContent>
              </Card>

              <Card className="print:shadow-none print:border">
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Award className="h-8 w-8 text-purple-500" />
                  </div>
                  <p className="text-2xl font-bold">{reportData.statistics.passRate}%</p>
                  <p className="text-sm text-gray-500">معدل النجاح</p>
                </CardContent>
              </Card>

              <Card className="print:shadow-none print:border">
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <GraduationCap className="h-8 w-8 text-yellow-500" />
                  </div>
                  <p className="text-2xl font-bold">{reportData.statistics.excellentExams}</p>
                  <p className="text-sm text-gray-500">امتحانات ممتازة</p>
                </CardContent>
              </Card>
            </div>

            {/* Detailed Statistics */}
            <Card className="mb-6 print:shadow-none print:border-2">
              <CardHeader>
                <CardTitle>الإحصائيات التفصيلية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <p className="text-lg font-bold text-green-600">{reportData.statistics.passedExams}</p>
                    <p className="text-sm text-gray-500">امتحانات ناجحة</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-red-600">{reportData.statistics.failedExams}</p>
                    <p className="text-sm text-gray-500">امتحانات راسبة</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-blue-600">{reportData.statistics.highestGrade}</p>
                    <p className="text-sm text-gray-500">أعلى درجة</p>
                  </div>
                  <div>
                    <p className="text-lg font-bold text-orange-600">{reportData.statistics.lowestGrade}</p>
                    <p className="text-sm text-gray-500">أقل درجة</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Type Statistics */}
            {reportData.typeStatistics.length > 0 && (
              <Card className="mb-6 print:shadow-none print:border-2">
                <CardHeader>
                  <CardTitle>الإحصائيات حسب نوع التقييم</CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right">نوع التقييم</TableHead>
                        <TableHead className="text-right">عدد الامتحانات</TableHead>
                        <TableHead className="text-right">المتوسط</TableHead>
                        <TableHead className="text-right">معدل النجاح</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {reportData.typeStatistics.map((stat, index) => (
                        <TableRow key={index}>
                          <TableCell>{getEvaluationTypeLabel(stat.type)}</TableCell>
                          <TableCell>{stat.totalExams}</TableCell>
                          <TableCell>{stat.averageGrade}</TableCell>
                          <TableCell>{stat.passRate}%</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}

            {/* Traditional Grades Table */}
            <div className="border-2 border-black print:border-black">
              <div className="bg-gray-100 p-2 border-b border-black">
                <h3 className="font-bold text-center">كشف النقاط والتقديرات</h3>
              </div>

              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-black bg-gray-50">
                    <th className="border-l border-black p-2 text-center font-bold">المادة/نوع التقييم</th>
                    <th className="border-l border-black p-2 text-center font-bold">الشهر</th>
                    <th className="border-l border-black p-2 text-center font-bold">الدرجة</th>
                    <th className="border-l border-black p-2 text-center font-bold">النقاط الكاملة</th>
                    <th className="border-l border-black p-2 text-center font-bold">النسبة المئوية</th>
                    <th className="border-l border-black p-2 text-center font-bold">التقدير</th>
                    <th className="p-2 text-center font-bold">الحالة</th>
                  </tr>
                </thead>
                <tbody>
                  {/* عرض جميع النتائج الفعلية */}
                  {reportData.allResults.length === 0 ? (
                    <tr>
                      <td colSpan={7} className="border-l border-black p-8 text-center text-gray-500">
                        <div className="flex flex-col items-center">
                          <FileText className="h-12 w-12 text-gray-400 mb-4" />
                          <p className="text-lg font-semibold mb-2">لا توجد نتائج امتحانات</p>
                          <p className="text-sm">لم يتم العثور على امتحانات لهذا الطالب بالمعايير المحددة</p>
                          <p className="text-xs mt-2 text-gray-400">جرب تغيير معايير التصفية أو تأكد من وجود امتحانات مسجلة</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    reportData.allResults.map((result, index) => (
                    <tr key={index} className="border-b border-gray-300 hover:bg-gray-50">
                      <td className="border-l border-black p-2 font-semibold">
                        {getEvaluationTypeLabel(result.evaluationType)}
                        {result.examDescription && (
                          <div className="text-xs text-gray-600 mt-1">{result.examDescription}</div>
                        )}
                      </td>
                      <td className="border-l border-black p-2 text-center">
                        {result.month}
                      </td>
                      <td className="border-l border-black p-2 text-center font-bold text-blue-600">
                        {result.grade}
                      </td>
                      <td className="border-l border-black p-2 text-center text-gray-600">
                        {result.maxPoints}
                      </td>
                      <td className="border-l border-black p-2 text-center">
                        <span className={`font-semibold ${
                          result.percentage >= 80 ? 'text-green-600' :
                          result.percentage >= 60 ? 'text-blue-600' :
                          result.percentage >= 50 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {result.percentage}%
                        </span>
                      </td>
                      <td className="border-l border-black p-2 text-center">
                        {(() => {
                          const gradeLevel = getGradeLevelByScore(result.grade, result.maxPoints);
                          return (
                            <span className={`px-2 py-1 rounded text-xs font-semibold`}
                                  style={{ backgroundColor: gradeLevel.color + '20', color: gradeLevel.color }}>
                              {gradeLevel.nameAr}
                            </span>
                          );
                        })()}
                      </td>
                      <td className="p-2 text-center">
                        {(() => {
                          const status = getPassStatus(result.grade, result.maxPoints);
                          const isPass = status !== 'FAILED';
                          return (
                            <span className={`px-2 py-1 rounded text-xs font-semibold ${
                              isPass ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {isPass ? 'نجح' : 'راسب'}
                            </span>
                          );
                        })()}
                      </td>
                    </tr>
                    ))
                  )}

                  {/* صف المعدل العام */}
                  {reportData.allResults.length > 0 && (
                    <tr className="border-t-2 border-black bg-yellow-50">
                      <td className="border-l border-black p-3 font-bold text-lg">المعدل العام</td>
                      <td className="border-l border-black p-3 text-center font-bold">جميع الأشهر</td>
                      <td className="border-l border-black p-3 text-center font-bold text-red-600 text-lg">
                        {reportData.statistics.averageGrade}
                      </td>
                      <td className="border-l border-black p-3 text-center font-bold">20</td>
                      <td className="border-l border-black p-3 text-center font-bold">
                        {Math.round((reportData.statistics.averageGrade / 20) * 100)}%
                      </td>
                      <td className="border-l border-black p-3 text-center">
                        {(() => {
                          const gradeLevel = getGradeLevelByScore(reportData.statistics.averageGrade, 20);
                          return (
                            <span className={`px-3 py-1 rounded font-bold`}
                                  style={{ backgroundColor: gradeLevel.color + '40', color: gradeLevel.color }}>
                              {gradeLevel.nameAr}
                            </span>
                          );
                        })()}
                      </td>
                      <td className="p-3 text-center">
                        <span className={`px-3 py-1 rounded font-bold ${
                          reportData.statistics.passRate >= 80 ? 'bg-green-200 text-green-800' : 'bg-yellow-200 text-yellow-800'
                        }`}>
                          {reportData.statistics.passRate}% نجاح
                        </span>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>

              {/* ملخص المواد والمعدلات */}
              {reportData.typeStatistics.length > 0 && (
                <div className="mt-6 border-2 border-black">
                  <div className="bg-gray-100 p-2 border-b border-black">
                    <h3 className="font-bold text-center">ملخص المعدلات حسب المواد</h3>
                  </div>
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b border-black bg-gray-50">
                        <th className="border-l border-black p-2 text-center font-bold">المادة/نوع التقييم</th>
                        <th className="border-l border-black p-2 text-center font-bold">عدد الامتحانات</th>
                        <th className="border-l border-black p-2 text-center font-bold">المعدل</th>
                        <th className="p-2 text-center font-bold">التقدير</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.typeStatistics.map((typeStat, index) => (
                        <tr key={index} className="border-b border-gray-300">
                          <td className="border-l border-black p-2 font-semibold">
                            {getEvaluationTypeLabel(typeStat.type)}
                          </td>
                          <td className="border-l border-black p-2 text-center">
                            {typeStat.totalExams}
                          </td>
                          <td className="border-l border-black p-2 text-center font-bold text-blue-600">
                            {typeStat.averageGrade}
                          </td>
                          <td className="p-2 text-center">
                            {(() => {
                              const gradeLevel = getGradeLevelByScore(typeStat.averageGrade, 20);
                              return (
                                <span className={`px-2 py-1 rounded text-xs font-semibold`}
                                      style={{ backgroundColor: gradeLevel.color + '20', color: gradeLevel.color }}>
                                  {gradeLevel.nameAr}
                                </span>
                              );
                            })()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Attendance Statistics */}
              {reportData.attendanceStats.totalDays > 0 && (
                <div className="border-2 border-black mt-6 print:border-black">
                  <div className="bg-gray-100 p-2 border-b border-black">
                    <h3 className="font-bold text-center">إحصائيات الحضور والغياب</h3>
                  </div>
                  <div className="p-4">
                    <div className="grid grid-cols-4 gap-4 mb-4 text-center">
                      <div>
                        <p className="font-bold text-blue-600 text-lg">{reportData.attendanceStats.totalDays}</p>
                        <p className="text-sm">إجمالي الأيام</p>
                      </div>
                      <div>
                        <p className="font-bold text-green-600 text-lg">{reportData.attendanceStats.presentDays}</p>
                        <p className="text-sm">أيام الحضور</p>
                      </div>
                      <div>
                        <p className="font-bold text-red-600 text-lg">{reportData.attendanceStats.absentDays}</p>
                        <p className="text-sm">أيام الغياب</p>
                      </div>
                      <div>
                        <p className="font-bold text-yellow-600 text-lg">{reportData.attendanceStats.excusedDays}</p>
                        <p className="text-sm">الغياب بعذر</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-center border-t pt-4">
                      <div>
                        <p className="font-bold text-green-600 text-lg">{reportData.attendanceStats.presentRate}%</p>
                        <p className="text-sm">نسبة الحضور</p>
                      </div>
                      <div>
                        <p className="font-bold text-red-600 text-lg">{reportData.attendanceStats.absentRate}%</p>
                        <p className="text-sm">نسبة الغياب</p>
                      </div>
                      <div>
                        <p className="font-bold text-yellow-600 text-lg">{reportData.attendanceStats.excusedRate}%</p>
                        <p className="text-sm">نسبة الغياب بعذر</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Footer Information */}
              <div className="p-4 border-t border-black mt-4">
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <p><strong>عدد الامتحانات:</strong> {reportData.statistics.totalExams}</p>
                    <p><strong>الامتحانات الناجحة:</strong> {reportData.statistics.passedExams}</p>
                  </div>
                  <div>
                    <p><strong>معدل النجاح:</strong> {reportData.statistics.passRate}%</p>
                    <p><strong>أعلى درجة:</strong> {reportData.statistics.highestGrade}</p>
                  </div>
                  <div>
                    <p><strong>أقل درجة:</strong> {reportData.statistics.lowestGrade}</p>
                    <p><strong>التاريخ:</strong> {new Date(reportData.reportGeneratedAt).toLocaleDateString('fr-FR')}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Signatures Section */}
            <div className="mt-6 grid grid-cols-3 gap-8 text-center">
              <div>
                <p className="font-bold mb-8">أستاذ القسم</p>
                <div className="border-t border-black pt-2">
                  <p className="text-sm">التوقيع والختم</p>
                </div>
              </div>
              <div>
                <p className="font-bold mb-8">مدير المدرسة</p>
                <div className="border-t border-black pt-2">
                  <p className="text-sm">التوقيع والختم</p>
                </div>
              </div>
              <div>
                <p className="font-bold mb-8">ولي الأمر</p>
                <div className="border-t border-black pt-2">
                  <p className="text-sm">التوقيع</p>
                </div>
              </div>
            </div>

            {/* Academic Year Footer */}
            <div className="mt-6 text-center text-sm text-gray-600 print:text-black">
              <p>السنة الدراسية: {new Date().getFullYear()}-{new Date().getFullYear() + 1}</p>
              <p className="mt-2">تم إنشاء هذا الكشف بتاريخ: {new Date(reportData.reportGeneratedAt).toLocaleDateString('fr-FR')}</p>
            </div>
          </div>
        )}

        {!reportData && !loading && (
          <Card>
            <CardContent className="text-center p-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">اختر طالب وقم بإنشاء كشف الدرجات لعرض النتائج.</p>
            </CardContent>
          </Card>
        )}
      </div>

      <style jsx global>{`
        @media print {
          .no-print {
            display: none !important;
          }

          .print\\:block {
            display: block !important;
          }

          .print\\:border-black {
            border-color: #000000 !important;
          }

          .print\\:text-black {
            color: #000000 !important;
          }

          body {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
            font-family: 'Arial', sans-serif;
          }

          table {
            border-collapse: collapse !important;
          }

          th, td {
            border: 1px solid #000000 !important;
            padding: 4px 8px !important;
          }

          .bg-gray-100, .bg-gray-50, .bg-yellow-50 {
            background-color: #f5f5f5 !important;
          }

          .bg-green-100, .bg-green-200 {
            background-color: #dcfce7 !important;
          }

          .bg-blue-100, .bg-blue-200 {
            background-color: #dbeafe !important;
          }

          .bg-yellow-100, .bg-yellow-200 {
            background-color: #fef3c7 !important;
          }

          .bg-orange-100, .bg-orange-200 {
            background-color: #fed7aa !important;
          }

          .bg-red-100, .bg-red-200 {
            background-color: #fecaca !important;
          }

          .text-green-600, .text-green-800 {
            color: #059669 !important;
          }

          .text-blue-600, .text-blue-800 {
            color: #2563eb !important;
          }

          .text-yellow-600, .text-yellow-800 {
            color: #d97706 !important;
          }

          .text-orange-600, .text-orange-800 {
            color: #ea580c !important;
          }

          .text-red-600, .text-red-800 {
            color: #dc2626 !important;
          }

          @page {
            margin: 0.8cm;
            size: A4 portrait;
            /* إخفاء header وfooter المتصفح بشكل كامل */
            margin-top: 0 !important;
            margin-bottom: 0 !important;
          }

          /* إخفاء عناصر الموقع الأساسية */
          body > nav,
          body > header,
          body > .navbar,
          body > .header,
          body > footer,
          body > .footer {
            display: none !important;
          }

          /* تحسين حجم الخط للطباعة */
          body {
            font-size: 12px !important;
          }

          /* تقليل المسافات للطباعة */
          .print-compact {
            margin: 0 !important;
            padding: 0.5rem !important;
          }

          /* تحسين حجم الجدول */
          table {
            font-size: 11px !important;
          }

          th, td {
            padding: 2px 4px !important;
            line-height: 1.2 !important;
          }

          /* تقليل حجم العناوين */
          h1, h2, h3 {
            font-size: 14px !important;
            margin: 0.5rem 0 !important;
          }

          /* إخفاء العناصر غير الضرورية */
          .no-print {
            display: none !important;
          }

          /* Traditional report styling */
          .border-dotted {
            border-style: dotted !important;
            border-color: #000000 !important;
          }

          /* Ensure all borders are black in print */
          * {
            border-color: #000000 !important;
          }
        }

        /* Screen styles for better preview */
        .border-dotted {
          border-style: dotted;
        }
      `}</style>
    </ProtectedRoute>
  );
}

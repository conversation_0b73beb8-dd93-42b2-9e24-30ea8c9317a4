# إضافة صلاحيات الموظف المخول لنظام الإشعارات

## نظرة عامة
تم تحديث نظام الإشعارات لإضافة صلاحيات الموظف المخول (EMPLOYEE) لجميع العمليات المتعلقة بإدارة الإشعارات.

## التحديثات المطبقة

### 1. API المستخدمين (`src/app/api/users/route.ts`)
**التحديث**: إضافة صلاحية EMPLOYEE لجلب جميع المستخدمين
```javascript
// قبل التحديث
const isAdmin = userData.role === 'ADMIN' || userData.role === 'TEACHER';

// بعد التحديث
const isAdmin = userData.role === 'ADMIN' || userData.role === 'TEACHER' || userData.role === 'EMPLOYEE';
```

**الفائدة**: الموظفون المخولون يمكنهم الآن الوصول لقائمة جميع المستخدمين لإرسال الإشعارات.

### 2. API الإشعارات الأساسي (`src/app/api/notifications/route.ts`)
**التحديث**: إضافة صلاحية EMPLOYEE لإنشاء الإشعارات
```javascript
// قبل التحديث
if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
    return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
        { status: 401 }
    );
}

// بعد التحديث
if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
    return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم أو موظف مخول" },
        { status: 401 }
    );
}
```

### 3. API الإشعارات الجماعية (`src/app/api/notifications/bulk/route.ts`)
**التحديثات المطبقة**:
- إضافة صلاحية EMPLOYEE لإنشاء الإشعارات الجماعية
- إضافة صلاحية EMPLOYEE لجلب الإشعارات الجماعية

```javascript
// في دالة POST و GET
if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
    return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم أو موظف مخول" },
        { status: 401 }
    );
}
```

### 4. API مجموعات المستخدمين (`src/app/api/user-groups/route.ts`)
**التحديثات المطبقة**:
- إضافة صلاحية EMPLOYEE لجلب إحصائيات المجموعات
- إضافة صلاحية EMPLOYEE لإنشاء مجموعات مخصصة

```javascript
// في دالة GET و POST
if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
    return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
    );
}
```

### 5. API معاينة المستلمين (`src/app/api/user-groups/preview/route.ts`)
**التحديثات المطبقة**:
- إضافة صلاحية EMPLOYEE لمعاينة المستلمين
- إضافة صلاحية EMPLOYEE للبحث في المستخدمين

```javascript
// في دالة POST و GET
if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
    return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
    );
}
```

### 6. API إدارة الإشعارات الفردية (`src/app/api/notifications/[id]/route.ts`)
**التحديثات المطبقة**:
- إضافة صلاحية EMPLOYEE لعرض الإشعارات
- إضافة صلاحية EMPLOYEE لتحديث حالة الإشعارات
- إضافة صلاحية EMPLOYEE لحذف الإشعارات

```javascript
// في جميع الدوال (GET, PATCH, DELETE)
if (notification.userId !== userData.id && userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE') {
    return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 403 }
    );
}
```

### 7. واجهة المستخدم (`src/app/notifications/page.tsx`)
**التحديثات المطبقة**:

#### أ. دالة جلب المستخدمين
```javascript
// قبل التحديث
const fetchUsers = async () => {
    if (userRole !== 'ADMIN' && userRole !== 'TEACHER') return;

// بعد التحديث
const fetchUsers = async () => {
    if (userRole !== 'ADMIN' && userRole !== 'TEACHER' && userRole !== 'EMPLOYEE') return;
```

#### ب. عرض زر إنشاء الإشعار
```javascript
// قبل التحديث
{(userRole === 'ADMIN' || userRole === 'TEACHER') && (

// بعد التحديث
{(userRole === 'ADMIN' || userRole === 'TEACHER' || userRole === 'EMPLOYEE') && (
```

#### ج. useEffect لجلب المستخدمين
```javascript
// قبل التحديث
useEffect(() => {
    if (userRole === 'ADMIN' || userRole === 'TEACHER') {
        fetchUsers();
    }
}, [userRole]);

// بعد التحديث
useEffect(() => {
    if (userRole === 'ADMIN' || userRole === 'TEACHER' || userRole === 'EMPLOYEE') {
        fetchUsers();
    }
}, [userRole]);
```

## الصلاحيات الجديدة للموظف المخول

### ✅ **الصلاحيات المتاحة للموظف (EMPLOYEE)**

#### إدارة الإشعارات الفردية:
- ✅ عرض جميع المستخدمين في القائمة المنسدلة
- ✅ إنشاء إشعارات فردية لأي مستخدم
- ✅ عرض وتحديث وحذف الإشعارات (للإشعارات التي أنشأها)

#### إدارة الإشعارات الجماعية:
- ✅ الوصول لجميع خيارات المجموعات المستهدفة
- ✅ إنشاء إشعارات جماعية لجميع المستخدمين
- ✅ إنشاء إشعارات جماعية حسب الدور
- ✅ إنشاء إشعارات جماعية لمجموعات مخصصة
- ✅ معاينة المستلمين قبل الإرسال
- ✅ البحث في المستخدمين
- ✅ جدولة الإشعارات

#### إدارة المجموعات:
- ✅ عرض إحصائيات المجموعات
- ✅ إنشاء مجموعات مخصصة
- ✅ البحث والفلترة في المستخدمين

### 🔒 **الصلاحيات المحدودة**

#### الإشعارات الخاصة بالمستخدمين الآخرين:
- ❌ لا يمكن للموظف تعديل أو حذف إشعارات المستخدمين الآخرين
- ❌ لا يمكن للموظف عرض الإشعارات الخاصة بالمستخدمين الآخرين
- ✅ يمكن للموظف إدارة الإشعارات التي أنشأها فقط

## الفوائد المحققة

### 1. **تحسين إدارة النظام**
- توزيع مسؤوليات إدارة الإشعارات على الموظفين المخولين
- تقليل العبء على المسؤولين والمعلمين
- تحسين كفاءة التواصل في المؤسسة

### 2. **مرونة في الصلاحيات**
- إمكانية تخصيص موظفين للتعامل مع الإشعارات
- صلاحيات محددة ومحدودة للحفاظ على الأمان
- توازن بين الوصول والأمان

### 3. **تحسين تجربة المستخدم**
- واجهة موحدة لجميع المستخدمين المخولين
- عدم الحاجة لتغيير الواجهة حسب الدور
- سهولة الاستخدام والتنقل

## اختبار الصلاحيات الجديدة

### 1. **اختبار تسجيل الدخول كموظف**
1. سجل الدخول بحساب موظف (EMPLOYEE)
2. انتقل إلى صفحة الإشعارات
3. تأكد من ظهور زر "إنشاء إشعار"

### 2. **اختبار الإشعارات الفردية**
1. انقر على "إنشاء إشعار"
2. اختر "إشعار فردي"
3. تأكد من ظهور جميع المستخدمين في القائمة المنسدلة
4. أنشئ إشعار واختبر إرساله

### 3. **اختبار الإشعارات الجماعية**
1. اختر "إشعار جماعي"
2. جرب جميع خيارات المجموعات المستهدفة:
   - جميع المستخدمين
   - حسب الدور
   - اختيار مخصص
3. استخدم ميزة البحث في المستخدمين
4. اختبر معاينة المستلمين

### 4. **اختبار الصلاحيات المحدودة**
1. حاول تعديل إشعار أنشأه مستخدم آخر
2. تأكد من عدم إمكانية الوصول
3. تأكد من إمكانية تعديل الإشعارات التي أنشأها الموظف فقط

## ملاحظات للمطورين

### 1. **نمط الصلاحيات المستخدم**
```javascript
// النمط المعياري للتحقق من الصلاحيات
const hasPermission = userData.role === 'ADMIN' || 
                     userData.role === 'TEACHER' || 
                     userData.role === 'EMPLOYEE';
```

### 2. **التحقق من ملكية الإشعارات**
```javascript
// للعمليات الحساسة (تعديل/حذف)
const canModify = notification.userId === userData.id || 
                 userData.role === 'ADMIN' || 
                 userData.role === 'EMPLOYEE';
```

### 3. **رسائل الخطأ الموحدة**
```javascript
// رسالة خطأ موحدة للصلاحيات
{ message: "غير مصرح به، يجب أن تكون مسؤول أو معلم أو موظف مخول" }
```

## التحديثات المستقبلية المقترحة

### 1. **صلاحيات متدرجة للموظفين**
- إضافة مستويات مختلفة من صلاحيات الموظفين
- تخصيص صلاحيات محددة لكل موظف
- إدارة الصلاحيات من لوحة التحكم

### 2. **سجل العمليات**
- تسجيل جميع العمليات التي يقوم بها الموظفون
- إمكانية مراجعة سجل الأنشطة
- تقارير استخدام النظام

### 3. **إشعارات الموافقة**
- إضافة نظام موافقة للإشعارات الحساسة
- إشعارات تتطلب موافقة المسؤول قبل الإرسال
- سير عمل معتمد للإشعارات الهامة

## الخلاصة

تم بنجاح إضافة صلاحيات الموظف المخول لنظام الإشعارات مع الحفاظ على الأمان والتحكم في الوصول. النظام الآن يدعم ثلاثة مستويات من المستخدمين المخولين:

1. **المسؤول (ADMIN)**: صلاحيات كاملة
2. **المعلم (TEACHER)**: صلاحيات إدارة الإشعارات
3. **الموظف (EMPLOYEE)**: صلاحيات إدارة الإشعارات مع قيود أمنية

هذا التحديث يحسن من مرونة النظام ويوزع المسؤوليات بشكل أفضل مع الحفاظ على الأمان.

# حل مشكلة عدم وجود علاقة بين القسم والمادة وصلاحيات المعلم

## وصف المشكلة
عند محاولة تسجيل نقاط الامتحان، يواجه المعلم الأخطاء التالية:
1. "لم يتم العثور على علاقة بين القسم المحدد والمادة"
2. "ليس لديك صلاحية تسجيل نقاط الامتحان لهذا الفصل"

## تحليل المشكلة
المشكلة تكمن في عدم وجود العلاقات المطلوبة في قاعدة البيانات:

### 1. جدول TeacherSubject
- يربط بين المعلم والمادة الدراسية
- مطلوب لتحديد المواد التي يدرسها المعلم

### 2. جدول ClassSubject  
- يربط بين القسم وعلاقة المعلم بالمادة
- مطلوب لتحديد الأقسام التي يدرس فيها المعلم مادة معينة

## الحل المطبق

### 1. إنشاء API للإعداد التلقائي
**الملف الجديد:** `src/app/api/auto-setup-class-subject/route.ts`

**الوظائف:**
- التحقق من صلاحيات المعلم
- إنشاء علاقة المعلم بالمادة إذا لم تكن موجودة
- إنشاء علاقة القسم بالمادة إذا لم تكن موجودة
- دعم الامتحانات المرتبطة بمادة دراسية والامتحانات العامة

**المنطق:**
```typescript
// 1. التحقق من وجود علاقة المعلم بالمادة
let teacherSubject = await prisma.teacherSubject.findFirst({
  where: {
    teacherId: teacher.id,
    subjectId: exam.subjectId
  }
});

// 2. إنشاء العلاقة إذا لم تكن موجودة
if (!teacherSubject) {
  teacherSubject = await prisma.teacherSubject.create({
    data: {
      teacherId: teacher.id,
      subjectId: exam.subjectId
    }
  });
}

// 3. التحقق من وجود علاقة القسم بالمادة
let classSubject = await prisma.classSubject.findFirst({
  where: {
    classeId: parseInt(classId),
    teacherSubjectId: teacherSubject.id
  }
});

// 4. إنشاء العلاقة إذا لم تكن موجودة
if (!classSubject) {
  classSubject = await prisma.classSubject.create({
    data: {
      classeId: parseInt(classId),
      teacherSubjectId: teacherSubject.id
    }
  });
}
```

### 2. تحسين صفحة تسجيل النقاط
**الملف المحسن:** `src/app/teachers/evaluation/scoring/page.tsx`

**التحسينات المضافة:**

#### أ. دالة الإعداد التلقائي
```typescript
const handleAutoSetup = async () => {
  // استدعاء API الإعداد التلقائي
  const response = await fetch('/api/auto-setup-class-subject', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      examId: selectedExam,
      classId: selectedClass
    }),
  });
  
  // معالجة النتيجة وإعادة تحميل الصفحة
};
```

#### ب. رسالة خطأ محسنة مع زر الإعداد التلقائي
```typescript
{errors.save && (
  <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4">
    <p className="font-medium mb-2">خطأ في تسجيل النقاط:</p>
    <p className="text-sm">{errors.save}</p>
    
    {(errors.save.includes('لم يتم العثور على علاقة') || 
      errors.save.includes('ليس لديك صلاحية')) && (
      <div className="mt-3">
        <p className="text-sm mb-2">
          يمكنك محاولة الإعداد التلقائي لحل هذه المشكلة:
        </p>
        <Button onClick={handleAutoSetup} disabled={isAutoSetupLoading}>
          {isAutoSetupLoading ? 'جاري الإعداد...' : 'إعداد العلاقات تلقائياً'}
        </Button>
      </div>
    )}
  </div>
)}
```

#### ج. متغيرات الحالة الجديدة
```typescript
const [isAutoSetupLoading, setIsAutoSetupLoading] = useState(false);
```

## سيناريوهات الاستخدام

### السيناريو 1: امتحان مرتبط بمادة دراسية
1. يتحقق النظام من وجود علاقة المعلم بالمادة
2. ينشئ العلاقة إذا لم تكن موجودة
3. يتحقق من وجود علاقة القسم بالمادة
4. ينشئ العلاقة إذا لم تكن موجودة

### السيناريو 2: امتحان غير مرتبط بمادة دراسية
1. يبحث عن أي مادة يدرسها المعلم
2. يستخدم أول مادة متاحة
3. ينشئ علاقة القسم بهذه المادة

### السيناريو 3: المعلم غير مرتبط بأي مادة
1. يعرض رسالة خطأ واضحة
2. يطلب من المعلم التواصل مع المشرف
3. يوضح الحاجة لربط المعلم بمادة دراسية

## الميزات الجديدة

### ✅ الإعداد التلقائي:
- **إنشاء العلاقات المفقودة** تلقائياً
- **التحقق من الصلاحيات** قبل الإنشاء
- **دعم الامتحانات المختلفة** (مرتبطة بمادة أو عامة)
- **رسائل واضحة** للمستخدم

### ✅ تحسين تجربة المستخدم:
- **رسائل خطأ محسنة** مع شرح المشكلة
- **زر الإعداد التلقائي** لحل المشكلة بنقرة واحدة
- **حالات تحميل** واضحة أثناء الإعداد
- **إعادة تحميل تلقائية** بعد الإعداد الناجح

### ✅ الأمان والتحقق:
- **التحقق من صلاحيات المعلم** في كل خطوة
- **التحقق من وجود البيانات** قبل الإنشاء
- **منع التكرار** في العلاقات
- **معالجة الأخطاء** الشاملة

## النتائج المتوقعة

بعد تطبيق هذا الحل:

1. **حل تلقائي للمشكلة** - لا حاجة لتدخل المشرف في معظم الحالات
2. **تجربة مستخدم محسنة** - رسائل واضحة وحلول سريعة
3. **مرونة في الاستخدام** - دعم أنواع مختلفة من الامتحانات
4. **أمان محسن** - التحقق من الصلاحيات في كل خطوة

## طريقة الاستخدام

### للمعلم:
1. اختيار الامتحان والقسم
2. عند ظهور رسالة الخطأ، النقر على "إعداد العلاقات تلقائياً"
3. انتظار اكتمال الإعداد
4. المتابعة مع تسجيل النقاط

### للمشرف:
- في الحالات النادرة التي تتطلب ربط المعلم بمادة دراسية جديدة
- استخدام لوحة إدارة المعلمين والمواد

## الملفات المضافة/المعدلة

### الملفات الجديدة:
- `src/app/api/auto-setup-class-subject/route.ts`

### الملفات المعدلة:
- `src/app/teachers/evaluation/scoring/page.tsx`

## التاريخ
تم تطبيق الحل في: [التاريخ الحالي]

'use client';
import React, { useState } from 'react';
import { toast } from 'react-toastify';
import { FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';
import SiteLogo from '@/components/SiteLogo';

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من البيانات
    if (!formData.name || !formData.email || !formData.message) {
      return toast.error('يرجى ملء جميع الحقول المطلوبة');
    }

    setIsSubmitting(true);

    try {
      // محاكاة إرسال البيانات
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('تم إرسال رسالتك بنجاح');
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      console.error('Error:', error);
      toast.error('حدث خطأ أثناء إرسال الرسالة');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-6">
            <SiteLogo size="xl" showText={false} />
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">اتصل بنا</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            نحن هنا للإجابة على جميع استفساراتك. يمكنك التواصل معنا من خلال النموذج أدناه أو باستخدام معلومات الاتصال المباشرة.
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="grid grid-cols-1 md:grid-cols-3">
              {/* Contact Information */}
              <div className="bg-gradient-to-b from-[var(--primary-color)] to-[var(--secondary-color)] p-8 text-white">
                <h2 className="text-2xl font-semibold mb-6">معلومات الاتصال</h2>

                <div className="space-y-6">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <FaPhone className="h-5 w-5" />
                    </div>
                    <div className="mr-4">
                      <p className="font-medium">الهاتف</p>
                      <p className="mt-1">+213 123 456 789</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <FaEnvelope className="h-5 w-5" />
                    </div>
                    <div className="mr-4">
                      <p className="font-medium">البريد الإلكتروني</p>
                      <p className="mt-1"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      <FaMapMarkerAlt className="h-5 w-5" />
                    </div>
                    <div className="mr-4">
                      <p className="font-medium">العنوان</p>
                      <p className="mt-1">شارع الاستقلال، الجزائر العاصمة، الجزائر</p>
                    </div>
                  </div>
                </div>

                <div className="mt-12">
                  <h3 className="text-xl font-medium mb-4">ساعات العمل</h3>
                  <p className="mb-2">الأحد - الخميس: 9:00 صباحاً - 5:00 مساءً</p>
                  <p>الجمعة - السبت: مغلق</p>
                </div>
              </div>

              {/* Contact Form */}
              <div className="col-span-2 p-8">
                <h2 className="text-2xl font-semibold text-gray-800 mb-6">أرسل لنا رسالة</h2>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        الاسم الكامل <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        البريد الإلكتروني <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                        رقم الهاتف
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                      />
                    </div>

                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                        الموضوع
                      </label>
                      <select
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                      >
                        <option value="">اختر موضوعاً</option>
                        <option value="استفسار عام">استفسار عام</option>
                        <option value="التسجيل">التسجيل</option>
                        <option value="الرسوم والمدفوعات">الرسوم والمدفوعات</option>
                        <option value="الدعم الفني">الدعم الفني</option>
                        <option value="أخرى">أخرى</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      الرسالة <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      rows={5}
                      value={formData.message}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                    ></textarea>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full md:w-auto px-6 py-3 bg-[var(--primary-color)] text-white font-medium rounded-md hover:bg-[var(--secondary-color)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--primary-color)] transition-colors"
                    >
                      {isSubmitting ? 'جاري الإرسال...' : 'إرسال الرسالة'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {/* Map Section */}
          <div className="mt-12 bg-white rounded-lg shadow-md p-4">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4 text-center">موقعنا</h2>
            <div className="h-96 bg-gray-200 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">هنا يمكن إضافة خريطة Google Maps</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;

# API الفاتورة المدمجة

## 📋 الوصف
API endpoint لإنشاء وتوليد فواتير مدمجة بحجم صغير وملائم للطباعة، مع خيارات تخصيص متعددة لتوفير الورق والحبر.

## 🎯 الهدف
- إنشاء فواتير بحجم صغير ومدمج
- توفير خيارات طباعة متعددة
- تحسين استهلاك الورق والحبر
- دعم أحجام مختلفة من الفواتير

## 🔗 المسارات

### GET /api/invoices/compact-pdf/[id]
إنشاء فاتورة مدمجة بصيغة PDF

#### المعاملات:
- `size`: حجم الفاتورة (thermal, half-a4, business-card)
- `includeQR`: تضمين رمز QR (true/false)
- `includeLogo`: تضمين الشعار (true/false)
- `fontSize`: حجم الخط (small, medium, large)
- `layout`: تخطيط الفاتورة (vertical, horizontal)
- `paperSaving`: وضع توفير الورق (true/false)
- `highQuality`: جودة عالية (true/false)
- `preview`: معاينة HTML بدلاً من PDF (true/false)

#### مثال على الطلب:
```bash
GET /api/invoices/compact-pdf/123?size=thermal&includeQR=true&fontSize=medium&paperSaving=true
```

#### الاستجابة:
- **PDF**: ملف PDF مدمج للتحميل والطباعة
- **HTML** (مع preview=true): صفحة HTML للمعاينة

## 📐 أحجام الفواتير المتاحة

### 1. Thermal (80mm × متغير)
```
العرض: 80mm
الطول: متغير حسب المحتوى
الاستخدام: الطابعات الحرارية
المميزات: سرعة عالية، استهلاك أقل
```

### 2. Half A4 (105mm × 148mm)
```
العرض: 105mm
الطول: 148mm
الاستخدام: الطابعات العادية
المميزات: 4 فواتير في صفحة واحدة
```

### 3. Business Card (85mm × 55mm)
```
العرض: 85mm
الطول: 55mm
الاستخدام: فواتير مصغرة
المميزات: سهولة الحمل والحفظ
```

## 🎨 خيارات التخصيص

### أحجام الخطوط
```typescript
interface FontSizes {
  small: { base: 8, small: 7, medium: 9, large: 10 };
  medium: { base: 10, small: 8, medium: 11, large: 12 };
  large: { base: 12, small: 10, medium: 13, large: 14 };
}
```

### تخطيطات الفاتورة
- **Vertical**: تخطيط عمودي تقليدي
- **Horizontal**: تخطيط أفقي مدمج

### وضع توفير الورق
- إزالة الحدود والظلال
- تقليل المساحات الفارغة
- استخدام ألوان اقتصادية
- تحسين كثافة الطباعة

## 📄 هيكل الفاتورة المدمجة

### العناصر الأساسية
1. **رأس الفاتورة**
   - شعار المؤسسة (اختياري)
   - اسم المؤسسة
   - رقم الفاتورة
   - تاريخ الإصدار

2. **معلومات الطالب**
   - اسم الطالب
   - اسم الولي
   - الصف الدراسي

3. **تفاصيل الفاتورة**
   - فترة الفاتورة (الشهر والسنة)
   - وصف الفاتورة
   - تاريخ الاستحقاق

4. **المبالغ المالية**
   - المبلغ الإجمالي
   - المبلغ المدفوع
   - المبلغ المتبقي

5. **التذييل**
   - رمز QR (اختياري)
   - معلومات الاتصال
   - التوقيع

## 💰 تنسيق العملة والتاريخ

### العملة
- الدينار الجزائري (دج)
- تنسيق فرنسي للأرقام
- فواصل الآلاف

### التاريخ
- تنسيق فرنسي (DD/MM/YYYY)
- أسماء الشهور بالعربية
- دعم التقويم الهجري (اختياري)

## 🎨 مثال على الفاتورة المدمجة

```
┌─────────────────────────────────┐
│        🏫 مدرسة القرآن         │
│     فاتورة رقم: INV-0123       │
│     التاريخ: 15/01/2024        │
├─────────────────────────────────┤
│ الطالب: أحمد محمد علي           │
│ الولي: محمد علي                │
│ الصف: الخامس الابتدائي          │
├─────────────────────────────────┤
│ الفترة: يناير 2024             │
│ الاستحقاق: 31/01/2024          │
├─────────────────────────────────┤
│ المبلغ الإجمالي: 5,000.00 دج   │
│ المدفوع: 3,000.00 دج           │
│ المتبقي: 2,000.00 دج           │
├─────────────────────────────────┤
│ [QR Code]  📞 0123456789        │
│            📧 <EMAIL>    │
└─────────────────────────────────┘
```

## ⚡ الأداء والتحسين

### تحسينات الطباعة
- ضغط الصور والشعارات
- تحسين حجم الملف النهائي
- دعم الطباعة المتعددة
- تخزين مؤقت للقوالب

### دعم اللغة العربية
- خطوط عربية مدمجة
- اتجاه النص من اليمين لليسار
- تنسيق التواريخ بالعربية
- دعم الأرقام العربية والإنجليزية

## 🔒 الأمان والصلاحيات
- التحقق من صحة معرف الفاتورة
- التحقق من صلاحيات المستخدم
- حماية من الوصول غير المصرح
- تسجيل عمليات الطباعة

## 🧪 الاختبار

### طباعة فاتورة حرارية
```bash
curl -X GET "http://localhost:3000/api/invoices/compact-pdf/123?size=thermal&includeQR=true" \
  -H "Cookie: jwtToken=YOUR_TOKEN" \
  --output invoice-thermal.pdf
```

### معاينة فاتورة نصف A4
```bash
curl -X GET "http://localhost:3000/api/invoices/compact-pdf/123?size=half-a4&preview=true" \
  -H "Cookie: jwtToken=YOUR_TOKEN"
```

### فاتورة بحجم بطاقة العمل
```bash
curl -X GET "http://localhost:3000/api/invoices/compact-pdf/123?size=business-card&paperSaving=true" \
  -H "Cookie: jwtToken=YOUR_TOKEN" \
  --output invoice-card.pdf
```

## 📝 ملاحظات التطوير
- يتطلب تطبيق مكتبة Puppeteer لإنشاء PDF حقيقي
- دعم كامل للطباعة الحرارية
- تحسين مستمر لجودة الطباعة
- إمكانية إضافة المزيد من الأحجام والتخطيطات

## 🔄 التطويرات المستقبلية
- دعم رموز QR حقيقية
- إضافة المزيد من القوالب
- دعم الطباعة الملونة
- تكامل مع أنظمة الدفع الإلكتروني

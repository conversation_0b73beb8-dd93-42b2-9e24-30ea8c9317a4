'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import {
  <PERSON>ader2, <PERSON><PERSON><PERSON> as BarChartIcon, <PERSON><PERSON><PERSON> as PieChartI<PERSON>, LineChart as LineChartIcon,
  Users, School, GraduationCap, Award, BookOpen, Filter, FileText, Download, FileSpreadsheet,
  Lightbulb, AlertTriangle, CheckCircle, Info
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { StatsCard } from '@/components/stats-card';
import { Bar<PERSON>hart } from '@/components/charts/bar-chart';
import { <PERSON><PERSON><PERSON> } from '@/components/charts/pie-chart';
import { <PERSON><PERSON><PERSON> } from '@/components/charts/line-chart';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { exportAnalysisReport } from '@/utils/export-analysis';
import { analyzeExamAndGenerateRecommendations, formatRecommendations, Recommendation } from '@/utils/recommendations-utils';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface AnalysisData {
  summary: {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    passRate: number;
    averageGrade: number;
    highestGrade: number;
    lowestGrade: number;
  };
  gradeDistribution: {
    excellent: number;
    veryGood: number;
    good: number;
    fair: number;
    poor: number;
    veryPoor: number;
  };
  classeAnalysis: {
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    averageGrade: number;
  }[];
  teacherAnalysis: {
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    averageGrade: number;
  }[];
  questionTypeAnalysis: {
    type: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    pendingAnswers: number;
    averagePoints: number;
  }[];
  difficultyAnalysis: {
    level: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    pendingAnswers: number;
    averagePoints: number;
  }[];
  genderAnalysis: {
    MALE: {
      totalStudents: number;
      passedStudents: number;
      failedStudents: number;
      excellentStudents: number;
      pendingStudents: number;
      averageGrade: number;
    };
    FEMALE: {
      totalStudents: number;
      passedStudents: number;
      failedStudents: number;
      excellentStudents: number;
      pendingStudents: number;
      averageGrade: number;
    };
  };
  recommendations?: Recommendation[];
}

export default function AnalysisPage() {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, setRecommendations] = useState<AnalysisData['recommendations']>([]);
  interface Exam {
    id: number;
    evaluationType: string;
    month: string;
    description?: string | null;
    maxPoints?: number;
    passingPoints?: number;
    requiresSurah?: boolean;
    typeId?: number;
    examType?: {
      id: number;
      name: string;
    } | null;
  }

  interface Classe {
    id: number;
    name: string;
    capacity?: number;
    description?: string | null;
  }

  interface Teacher {
    id: number;
    name: string;
    specialization?: string;
    phone?: string | null;
    userId?: number;
  }

  const [exams, setExams] = useState<Exam[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [selectedExamId, setSelectedExamId] = useState('all');
  const [selectedClassId, setSelectedClassId] = useState('all');
  const [selectedTeacherId, setSelectedTeacherId] = useState('all');
  const [selectedMonth, setSelectedMonth] = useState('all');
  const [selectedEvaluationType, setSelectedEvaluationType] = useState('all');
  const [activeTab, setActiveTab] = useState('summary');

  useEffect(() => {
    fetchExams();
    fetchClasses();
    fetchTeachers();
    fetchAnalysisData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchAnalysisData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedExamId, selectedClassId, selectedTeacherId, selectedMonth, selectedEvaluationType]);

  const fetchExams = async () => {
    try {
      const response = await fetch('/api/evaluation/exams');
      const result = await response.json();

      if (result.success) {
        setExams(result.data);
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب الامتحانات',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error fetching exams:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب الامتحانات',
        variant: 'destructive'
      });
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      const result = await response.json();

      if (result.success) {
        setClasses(result.data);
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب الفصول',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب الفصول',
        variant: 'destructive'
      });
    }
  };

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/teachers');
      const result = await response.json();

      if (result.success) {
        setTeachers(result.data);
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب المعلمين',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب المعلمين',
        variant: 'destructive'
      });
    }
  };

  const fetchAnalysisData = async () => {
    setIsLoading(true);
    try {
      let url = '/api/evaluation/analysis';
      const params = new URLSearchParams();

      if (selectedExamId && selectedExamId !== 'all') {
        params.append('examId', selectedExamId);
      }

      if (selectedClassId && selectedClassId !== 'all') {
        params.append('classId', selectedClassId);
      }

      if (selectedTeacherId && selectedTeacherId !== 'all') {
        params.append('teacherId', selectedTeacherId);
      }

      if (selectedMonth && selectedMonth !== 'all') {
        params.append('month', selectedMonth);
      }

      if (selectedEvaluationType && selectedEvaluationType !== 'all') {
        params.append('evaluationType', selectedEvaluationType);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      const result = await response.json();

      if (result.success) {
        setAnalysisData(result.data);

        // توليد التوصيات بناءً على بيانات التحليل
        try {
          const generatedRecommendations = analyzeExamAndGenerateRecommendations(result.data);
          setRecommendations(generatedRecommendations);

          // إضافة التوصيات إلى بيانات التحليل
          setAnalysisData(prevData => {
            if (prevData) {
              return {
                ...prevData,
                recommendations: generatedRecommendations
              };
            }
            return prevData;
          });
        } catch (error) {
          console.error('Error generating recommendations:', error);
        }
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب بيانات التحليل',
          variant: 'destructive'
        });
        setAnalysisData(null);
        setRecommendations([]);
      }
    } catch (error) {
      console.error('Error fetching analysis data:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب بيانات التحليل',
        variant: 'destructive'
      });
      setAnalysisData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const getEvaluationTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      QURAN_MEMORIZATION: 'حفظ القرآن',
      QURAN_RECITATION: 'تلاوة القرآن',
      WRITTEN_EXAM: 'تحريري',
      ORAL_EXAM: 'شفهي',
      PRACTICAL_TEST: 'عملي',
      HOMEWORK: 'واجب منزلي',
      PROJECT: 'مشروع',
      REMOTE_EXAM: 'امتحان عن بعد'
    };
    return labels[type] || type;
  };

  const getQuestionTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      MULTIPLE_CHOICE: 'اختيار من متعدد',
      TRUE_FALSE: 'صح أو خطأ',
      SHORT_ANSWER: 'إجابة قصيرة',
      ESSAY: 'مقال',
      MATCHING: 'مطابقة',
      FILL_BLANK: 'ملء الفراغات',
      ORDERING: 'ترتيب'
    };
    return labels[type] || type;
  };

  const getDifficultyLevelLabel = (level: string): string => {
    const labels: Record<string, string> = {
      EASY: 'سهل',
      MEDIUM: 'متوسط',
      HARD: 'صعب',
      VERY_HARD: 'صعب جداً'
    };
    return labels[level] || level;
  };

  const getGenderLabel = (gender: string): string => {
    return gender === 'MALE' ? 'ذكور' : 'إناث';
  };

  const formatDate = (dateString: string): string => {
    const [year, month] = dateString.split('-');
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const getAvailableMonths = () => {
    const months = new Set<string>();
    exams.forEach(exam => {
      months.add(exam.month);
    });
    return Array.from(months).sort().reverse();
  };

  const getAvailableEvaluationTypes = () => {
    const types = new Set<string>();
    exams.forEach(exam => {
      types.add(exam.evaluationType);
    });
    return Array.from(types);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
      </div>
    );
  }

  return (
    <OptimizedProtectedRoute requiredPermission="admin.evaluation.analysis.view">
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">تحليل نتائج الامتحانات</h1>

        <QuickActionButtons
          entityType="evaluation.analysis"
          actions={[
            {
              key: 'export-excel',
              label: 'تصدير Excel',
              icon: <FileSpreadsheet className="h-4 w-4" />,
              onClick: () => analysisData && exportAnalysisReport(analysisData, activeTab, 'excel', `تقرير-${activeTab}`),
              variant: 'outline',
              disabled: !analysisData
            },
            {
              key: 'export-pdf',
              label: 'تصدير PDF',
              icon: <FileText className="h-4 w-4" />,
              onClick: () => analysisData && exportAnalysisReport(analysisData, activeTab, 'pdf', `تقرير-${activeTab}`),
              variant: 'outline',
              disabled: !analysisData
            }
          ]}
          className="flex gap-2"
        />
      </div>

      <div className="mb-6 grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="col-span-1">
          <Select value={selectedExamId} onValueChange={setSelectedExamId}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع الامتحانات" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الامتحانات</SelectItem>
              {exams.map(exam => (
                <SelectItem key={exam.id} value={exam.id.toString()}>
                  {getEvaluationTypeLabel(exam.evaluationType)} - {formatDate(exam.month)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1">
          <Select value={selectedClassId} onValueChange={setSelectedClassId}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع الفصول" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الفصول</SelectItem>
              {classes.map(classe => (
                <SelectItem key={classe.id} value={classe.id.toString()}>
                  {classe.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1">
          <Select value={selectedTeacherId} onValueChange={setSelectedTeacherId}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع المعلمين" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع المعلمين</SelectItem>
              {teachers.map(teacher => (
                <SelectItem key={teacher.id} value={teacher.id.toString()}>
                  {teacher.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1">
          <Select value={selectedMonth} onValueChange={setSelectedMonth}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع الشهور" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الشهور</SelectItem>
              {getAvailableMonths().map(month => (
                <SelectItem key={month} value={month}>
                  {formatDate(month)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1">
          <Select value={selectedEvaluationType} onValueChange={setSelectedEvaluationType}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع أنواع التقييم" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع أنواع التقييم</SelectItem>
              {getAvailableEvaluationTypes().map(type => (
                <SelectItem key={type} value={type}>
                  {getEvaluationTypeLabel(type)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {!analysisData ? (
        <div className="text-center p-8 bg-white rounded-lg shadow">
          <Filter className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات</h3>
          <p className="text-gray-500 mb-4">
            لم يتم العثور على بيانات تحليل تطابق معايير البحث. حاول تغيير المعايير.
          </p>
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-2 md:grid-cols-7 w-full">
            <TabsTrigger value="summary">ملخص النتائج</TabsTrigger>
            <TabsTrigger value="classes">تحليل الفصول</TabsTrigger>
            <TabsTrigger value="teachers">تحليل المعلمين</TabsTrigger>
            <TabsTrigger value="questions">تحليل الأسئلة</TabsTrigger>
            <TabsTrigger value="difficulty">مستويات الصعوبة</TabsTrigger>
            <TabsTrigger value="gender">تحليل حسب الجنس</TabsTrigger>
            <TabsTrigger value="recommendations" className="bg-green-50 hover:bg-green-100 text-green-700">
              <Lightbulb className="h-4 w-4 ml-1" />
              التوصيات
            </TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <StatsCard
                title="إجمالي الطلاب"
                value={analysisData.summary?.totalStudents || 0}
                icon={Users}
                variant="primary"
              />
              <StatsCard
                title="نسبة النجاح"
                value={`${analysisData.summary?.passRate?.toFixed(1) || 0}%`}
                description={`${analysisData.summary?.passedStudents || 0} طالب ناجح من أصل ${analysisData.summary?.totalStudents || 0}`}
                icon={Award}
                variant="success"
              />
              <StatsCard
                title="متوسط الدرجات"
                value={analysisData.summary?.averageGrade?.toFixed(1) || '0.0'}
                description={`أعلى درجة: ${analysisData.summary?.highestGrade?.toFixed(1) || '0.0'} | أدنى درجة: ${analysisData.summary?.lowestGrade?.toFixed(1) || '0.0'}`}
                icon={GraduationCap}
                variant="info"
              />
              <StatsCard
                title="الطلاب المتميزون"
                value={analysisData.summary?.excellentStudents || 0}
                description={`${(((analysisData.summary?.excellentStudents || 0) / (analysisData.summary?.totalStudents || 1)) * 100).toFixed(1)}% من إجمالي الطلاب`}
                icon={Award}
                variant="warning"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    توزيع النتائج
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={{
                      labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف', 'ضعيف جداً'],
                      datasets: [
                        {
                          label: 'عدد الطلاب',
                          data: [
                            analysisData.gradeDistribution?.excellent || 0,
                            analysisData.gradeDistribution?.veryGood || 0,
                            analysisData.gradeDistribution?.good || 0,
                            analysisData.gradeDistribution?.fair || 0,
                            analysisData.gradeDistribution?.poor || 0,
                            analysisData.gradeDistribution?.veryPoor || 0
                          ],
                          backgroundColor: [
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(255, 99, 132, 0.7)'
                          ]
                        }
                      ]
                    }}
                    height={300}
                    width={400}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    حالة الطلاب
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: ['ناجح', 'راسب', 'ممتاز', 'قيد الانتظار'],
                      datasets: [
                        {
                          label: 'عدد الطلاب',
                          data: [
                            (analysisData.summary?.passedStudents || 0) - (analysisData.summary?.excellentStudents || 0),
                            analysisData.summary?.failedStudents || 0,
                            analysisData.summary?.excellentStudents || 0,
                            analysisData.summary?.pendingStudents || 0
                          ],
                          backgroundColor: [
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                          ]
                        }
                      ]
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="classes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <School className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                  تحليل أداء الفصول
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-[var(--primary-color)] text-white">
                        <th className="p-2 text-right">الفصل</th>
                        <th className="p-2 text-center">عدد الطلاب</th>
                        <th className="p-2 text-center">متوسط الدرجات</th>
                        <th className="p-2 text-center">نسبة النجاح</th>
                        <th className="p-2 text-center">الطلاب المتميزون</th>
                        <th className="p-2 text-center">الطلاب الراسبون</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analysisData.classeAnalysis?.map((classe) => (
                        <tr key={classe.id} className="border-b hover:bg-gray-50">
                          <td className="p-2 text-right font-medium">{classe.name}</td>
                          <td className="p-2 text-center">{classe.totalStudents}</td>
                          <td className="p-2 text-center">{classe.averageGrade?.toFixed(1) || '0.0'}</td>
                          <td className="p-2 text-center">
                            {classe.totalStudents > 0
                              ? `${((classe.passedStudents / classe.totalStudents) * 100).toFixed(1)}%`
                              : '0%'}
                          </td>
                          <td className="p-2 text-center">{classe.excellentStudents}</td>
                          <td className="p-2 text-center">{classe.failedStudents}</td>
                        </tr>
                      )) || []}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    متوسط الدرجات حسب الفصل
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: analysisData.classeAnalysis?.map(classe => classe.name) || [],
                      datasets: [
                        {
                          label: 'متوسط الدرجات',
                          data: analysisData.classeAnalysis?.map(classe => classe.averageGrade || 0) || [],
                          backgroundColor: 'rgba(22, 155, 136, 0.7)',
                        }
                      ]
                    }}
                    options={{
                      scales: {
                        y: {
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          }
                        }
                      }
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    نسبة النجاح حسب الفصل
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: analysisData.classeAnalysis?.map(classe => classe.name) || [],
                      datasets: [
                        {
                          label: 'نسبة النجاح',
                          data: analysisData.classeAnalysis?.map(classe =>
                            classe.totalStudents > 0
                              ? (classe.passedStudents / classe.totalStudents) * 100
                              : 0
                          ) || [],
                          backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        }
                      ]
                    }}
                    options={{
                      scales: {
                        y: {
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          },
                          ticks: {
                            font: {
                              family: 'Tajawal, sans-serif'
                            },
                            color: '#333'
                          }
                        }
                      }
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="teachers" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <GraduationCap className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                  تحليل أداء المعلمين
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-[var(--primary-color)] text-white">
                        <th className="p-2 text-right">المعلم</th>
                        <th className="p-2 text-center">عدد الطلاب</th>
                        <th className="p-2 text-center">متوسط الدرجات</th>
                        <th className="p-2 text-center">نسبة النجاح</th>
                        <th className="p-2 text-center">الطلاب المتميزون</th>
                        <th className="p-2 text-center">الطلاب الراسبون</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analysisData.teacherAnalysis?.map((teacher) => (
                        <tr key={teacher.id} className="border-b hover:bg-gray-50">
                          <td className="p-2 text-right font-medium">{teacher.name}</td>
                          <td className="p-2 text-center">{teacher.totalStudents}</td>
                          <td className="p-2 text-center">{teacher.averageGrade?.toFixed(1) || '0.0'}</td>
                          <td className="p-2 text-center">
                            {teacher.totalStudents > 0
                              ? `${((teacher.passedStudents / teacher.totalStudents) * 100).toFixed(1)}%`
                              : '0%'}
                          </td>
                          <td className="p-2 text-center">{teacher.excellentStudents}</td>
                          <td className="p-2 text-center">{teacher.failedStudents}</td>
                        </tr>
                      )) || []}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    متوسط الدرجات حسب المعلم
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: analysisData.teacherAnalysis?.map(teacher => teacher.name) || [],
                      datasets: [
                        {
                          label: 'متوسط الدرجات',
                          data: analysisData.teacherAnalysis?.map(teacher => teacher.averageGrade || 0) || [],
                          backgroundColor: 'rgba(153, 102, 255, 0.7)',
                        }
                      ]
                    }}
                    options={{
                      scales: {
                        y: {
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          }
                        }
                      }
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    توزيع الطلاب المتميزين حسب المعلم
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={{
                      labels: analysisData.teacherAnalysis?.map(teacher => teacher.name) || [],
                      datasets: [
                        {
                          label: 'الطلاب المتميزون',
                          data: analysisData.teacherAnalysis?.map(teacher => teacher.excellentStudents || 0) || [],
                        }
                      ]
                    }}
                    height={300}
                    width={400}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="questions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BookOpen className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                  تحليل أداء الطلاب حسب نوع السؤال
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-[var(--primary-color)] text-white">
                        <th className="p-2 text-right">نوع السؤال</th>
                        <th className="p-2 text-center">عدد الإجابات</th>
                        <th className="p-2 text-center">الإجابات الصحيحة</th>
                        <th className="p-2 text-center">الإجابات الخاطئة</th>
                        <th className="p-2 text-center">نسبة الإجابات الصحيحة</th>
                        <th className="p-2 text-center">متوسط النقاط</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analysisData.questionTypeAnalysis?.map((type) => (
                        <tr key={type.type} className="border-b hover:bg-gray-50">
                          <td className="p-2 text-right font-medium">{getQuestionTypeLabel(type.type)}</td>
                          <td className="p-2 text-center">{type.totalAnswers}</td>
                          <td className="p-2 text-center">{type.correctAnswers}</td>
                          <td className="p-2 text-center">{type.incorrectAnswers}</td>
                          <td className="p-2 text-center">
                            {(type.correctAnswers + type.incorrectAnswers) > 0
                              ? `${((type.correctAnswers / (type.correctAnswers + type.incorrectAnswers)) * 100).toFixed(1)}%`
                              : '0%'}
                          </td>
                          <td className="p-2 text-center">{type.averagePoints?.toFixed(2) || '0.00'}</td>
                        </tr>
                      )) || []}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    نسبة الإجابات الصحيحة حسب نوع السؤال
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: analysisData.questionTypeAnalysis?.map(type => getQuestionTypeLabel(type.type)) || [],
                      datasets: [
                        {
                          label: 'نسبة الإجابات الصحيحة',
                          data: analysisData.questionTypeAnalysis?.map(type =>
                            (type.correctAnswers + type.incorrectAnswers) > 0
                              ? (type.correctAnswers / (type.correctAnswers + type.incorrectAnswers)) * 100
                              : 0
                          ) || [],
                          backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        }
                      ]
                    }}
                    options={{
                      scales: {
                        y: {
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          },
                          ticks: {
                            font: {
                              family: 'Tajawal, sans-serif'
                            },
                            color: '#333'
                          }
                        }
                      }
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    توزيع أنواع الأسئلة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={{
                      labels: analysisData.questionTypeAnalysis?.map(type => getQuestionTypeLabel(type.type)) || [],
                      datasets: [
                        {
                          label: 'عدد الأسئلة',
                          data: analysisData.questionTypeAnalysis?.map(type => type.totalAnswers || 0) || [],
                        }
                      ]
                    }}
                    height={300}
                    width={400}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="difficulty" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BookOpen className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                  تحليل أداء الطلاب حسب مستوى الصعوبة
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-[var(--primary-color)] text-white">
                        <th className="p-2 text-right">مستوى الصعوبة</th>
                        <th className="p-2 text-center">عدد الإجابات</th>
                        <th className="p-2 text-center">الإجابات الصحيحة</th>
                        <th className="p-2 text-center">الإجابات الخاطئة</th>
                        <th className="p-2 text-center">نسبة الإجابات الصحيحة</th>
                        <th className="p-2 text-center">متوسط النقاط</th>
                      </tr>
                    </thead>
                    <tbody>
                      {analysisData.difficultyAnalysis?.map((level) => (
                        <tr key={level.level} className="border-b hover:bg-gray-50">
                          <td className="p-2 text-right font-medium">{getDifficultyLevelLabel(level.level)}</td>
                          <td className="p-2 text-center">{level.totalAnswers}</td>
                          <td className="p-2 text-center">{level.correctAnswers}</td>
                          <td className="p-2 text-center">{level.incorrectAnswers}</td>
                          <td className="p-2 text-center">
                            {(level.correctAnswers + level.incorrectAnswers) > 0
                              ? `${((level.correctAnswers / (level.correctAnswers + level.incorrectAnswers)) * 100).toFixed(1)}%`
                              : '0%'}
                          </td>
                          <td className="p-2 text-center">{level.averagePoints?.toFixed(2) || '0.00'}</td>
                        </tr>
                      )) || []}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <LineChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    العلاقة بين مستوى الصعوبة ونسبة الإجابات الصحيحة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <LineChart
                    data={{
                      labels: analysisData.difficultyAnalysis.map(level => getDifficultyLevelLabel(level.level)),
                      datasets: [
                        {
                          label: 'نسبة الإجابات الصحيحة',
                          data: analysisData.difficultyAnalysis.map(level =>
                            (level.correctAnswers + level.incorrectAnswers) > 0
                              ? (level.correctAnswers / (level.correctAnswers + level.incorrectAnswers)) * 100
                              : 0
                          ),
                          borderColor: 'rgba(255, 99, 132, 1)',
                          backgroundColor: 'rgba(255, 99, 132, 0.2)',
                          fill: true
                        }
                      ]
                    }}
                    options={{
                      scales: {
                        y: {
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          },
                          ticks: {
                            font: {
                              family: 'Tajawal, sans-serif'
                            },
                            color: '#333'
                          }
                        }
                      }
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    متوسط النقاط حسب مستوى الصعوبة
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: analysisData.difficultyAnalysis.map(level => getDifficultyLevelLabel(level.level)),
                      datasets: [
                        {
                          label: 'متوسط النقاط',
                          data: analysisData.difficultyAnalysis.map(level => level.averagePoints),
                          backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        }
                      ]
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="gender" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    مقارنة أداء الطلاب حسب الجنس
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-[var(--primary-color)] text-white">
                          <th className="p-2 text-right">الجنس</th>
                          <th className="p-2 text-center">عدد الطلاب</th>
                          <th className="p-2 text-center">متوسط الدرجات</th>
                          <th className="p-2 text-center">نسبة النجاح</th>
                          <th className="p-2 text-center">الطلاب المتميزون</th>
                        </tr>
                      </thead>
                      <tbody>
                        {analysisData.genderAnalysis && Object.entries(analysisData.genderAnalysis).map(([gender, data]) => (
                          <tr key={gender} className="border-b hover:bg-gray-50">
                            <td className="p-2 text-right font-medium">{getGenderLabel(gender)}</td>
                            <td className="p-2 text-center">{data.totalStudents}</td>
                            <td className="p-2 text-center">{data.averageGrade.toFixed(1)}</td>
                            <td className="p-2 text-center">
                              {data.totalStudents > 0
                                ? `${((data.passedStudents / data.totalStudents) * 100).toFixed(1)}%`
                                : '0%'}
                            </td>
                            <td className="p-2 text-center">{data.excellentStudents}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    توزيع الطلاب حسب الجنس
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={{
                      labels: analysisData.genderAnalysis ? Object.keys(analysisData.genderAnalysis).map(gender => getGenderLabel(gender)) : [],
                      datasets: [
                        {
                          label: 'عدد الطلاب',
                          data: analysisData.genderAnalysis ? Object.values(analysisData.genderAnalysis).map(data => data.totalStudents) : [],
                          backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)'
                          ]
                        }
                      ]
                    }}
                    height={300}
                    width={400}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    مقارنة متوسط الدرجات حسب الجنس
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: analysisData.genderAnalysis ? Object.keys(analysisData.genderAnalysis).map(gender => getGenderLabel(gender)) : [],
                      datasets: [
                        {
                          label: 'متوسط الدرجات',
                          data: analysisData.genderAnalysis ? Object.values(analysisData.genderAnalysis).map(data => data.averageGrade) : [],
                          backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)'
                          ]
                        }
                      ]
                    }}
                    options={{
                      scales: {
                        y: {
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          }
                        }
                      }
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                    مقارنة نسبة النجاح حسب الجنس
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={{
                      labels: analysisData.genderAnalysis ? Object.keys(analysisData.genderAnalysis).map(gender => getGenderLabel(gender)) : [],
                      datasets: [
                        {
                          label: 'نسبة النجاح',
                          data: analysisData.genderAnalysis ? Object.values(analysisData.genderAnalysis).map(data =>
                            data.totalStudents > 0
                              ? (data.passedStudents / data.totalStudents) * 100
                              : 0
                          ) : [],
                          backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 99, 132, 0.7)'
                          ]
                        }
                      ]
                    }}
                    options={{
                      scales: {
                        y: {
                          grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                          },
                          ticks: {
                            font: {
                              family: 'Tajawal, sans-serif'
                            },
                            color: '#333'
                          }
                        }
                      }
                    }}
                    height={300}
                    width={500}
                    className="mx-auto"
                  />
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChartIcon className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                  مقارنة توزيع الدرجات حسب الجنس
                </CardTitle>
              </CardHeader>
              <CardContent>
                <BarChart
                  data={{
                    labels: ['ممتاز', 'ناجح', 'راسب', 'قيد الانتظار'],
                    datasets: analysisData.genderAnalysis ? [
                      {
                        label: getGenderLabel('MALE'),
                        data: analysisData.genderAnalysis.MALE ? [
                          analysisData.genderAnalysis.MALE.excellentStudents,
                          analysisData.genderAnalysis.MALE.passedStudents - analysisData.genderAnalysis.MALE.excellentStudents,
                          analysisData.genderAnalysis.MALE.failedStudents,
                          analysisData.genderAnalysis.MALE.pendingStudents
                        ] : [0, 0, 0, 0],
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                      },
                      {
                        label: getGenderLabel('FEMALE'),
                        data: analysisData.genderAnalysis.FEMALE ? [
                          analysisData.genderAnalysis.FEMALE.excellentStudents,
                          analysisData.genderAnalysis.FEMALE.passedStudents - analysisData.genderAnalysis.FEMALE.excellentStudents,
                          analysisData.genderAnalysis.FEMALE.failedStudents,
                          analysisData.genderAnalysis.FEMALE.pendingStudents
                        ] : [0, 0, 0, 0],
                        backgroundColor: 'rgba(255, 99, 132, 0.7)',
                      }
                    ] : []
                  }}
                  height={300}
                  width={800}
                  className="mx-auto"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Lightbulb className="h-5 w-5 ml-2 text-[var(--primary-color)]" />
                  التوصيات والإجراءات المقترحة
                </CardTitle>
              </CardHeader>
              <CardContent>
                {analysisData.recommendations && analysisData.recommendations.length > 0 ? (
                  <div className="space-y-6">
                    {analysisData.recommendations
                      .sort((a, b) => {
                        const priorityOrder = { high: 0, medium: 1, low: 2 };
                        return priorityOrder[a.priority] - priorityOrder[b.priority];
                      })
                      .map((recommendation, index) => {
                        // تحديد لون الأولوية
                        const priorityColor = recommendation.priority === 'high'
                          ? 'bg-red-50 border-red-200 text-red-700'
                          : recommendation.priority === 'medium'
                            ? 'bg-orange-50 border-orange-200 text-orange-700'
                            : 'bg-blue-50 border-blue-200 text-blue-700';

                        // تحديد أيقونة الأولوية
                        const PriorityIcon = recommendation.priority === 'high'
                          ? AlertTriangle
                          : recommendation.priority === 'medium'
                            ? Info
                            : CheckCircle;

                        return (
                          <div key={index} className={`p-4 rounded-lg border ${priorityColor}`}>
                            <div className="flex items-start">
                              <div className="mr-3 mt-1">
                                <PriorityIcon className="h-5 w-5" />
                              </div>
                              <div className="flex-1">
                                <h3 className="text-lg font-medium mb-2">{recommendation.title}</h3>
                                <p className="mb-3">{recommendation.description}</p>

                                {recommendation.actionItems && recommendation.actionItems.length > 0 && (
                                  <div>
                                    <h4 className="font-medium mb-2">الإجراءات المقترحة:</h4>
                                    <ul className="list-disc list-inside space-y-1 pr-4">
                                      {recommendation.actionItems.map((item, itemIndex) => (
                                        <li key={itemIndex}>{item}</li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                ) : (
                  <div className="text-center p-6">
                    <Lightbulb className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد توصيات</h3>
                    <p className="text-gray-500">
                      لم يتم العثور على توصيات بناءً على البيانات الحالية. حاول تغيير معايير البحث أو التحقق من وجود بيانات كافية للتحليل.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button
                onClick={() => analysisData.recommendations && exportAnalysisReport(analysisData, 'recommendations', 'pdf', 'تقرير-التوصيات')}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] ml-2"
                disabled={!analysisData.recommendations || analysisData.recommendations.length === 0}
              >
                <FileText className="h-4 w-4 ml-2" />
                تصدير التوصيات كملف PDF
              </Button>
              <Button
                onClick={() => {
                  if (analysisData.recommendations) {
                    const formattedRecommendations = formatRecommendations(analysisData.recommendations, 'markdown');
                    navigator.clipboard.writeText(formattedRecommendations);
                    toast({
                      title: 'نجاح',
                      description: 'تم نسخ التوصيات إلى الحافظة'
                    });
                  }
                }}
                variant="outline"
                disabled={!analysisData.recommendations || analysisData.recommendations.length === 0}
              >
                نسخ التوصيات
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      )}
      </div>
    </OptimizedProtectedRoute>
  );
}

import type { Metada<PERSON> } from "next";
import { Cairo } from "next/font/google";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import "./globals.css";
import "@/components/ui/dialog/dialog-animations.css";
import "@/styles/interaction-fixes.css";
import "@/styles/theme.css";
import "@/styles/color-enhancements.css";
import "@/styles/dropdown-enhancements.css";
import "@/styles/modal-enhancements.css";
import "@/styles/sidebar-positioning.css";
import "@/styles/dark-mode.css";
import Header from "@/components/header/header";
import Footer from "@/components/footer/footer";
import ColorInitializer from "@/components/ColorInitializer";
import DynamicFavicon from "@/components/DynamicFavicon";
import DarkModeProvider from "@/components/DarkModeProvider";
import PermissionsCacheWrapper from "@/components/PermissionsCacheWrapper";
import { PermissionsProvider } from "@/contexts/PermissionsContext";

const Cair = Cairo({subsets: ['arabic'], variable: "--font-cairo"});

export const metadata: Metadata = {
  title: "نظام برهان للقرآن الكريم",
  description: "منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={Cair.className}>
        <DarkModeProvider>
          <PermissionsProvider>
            <ColorInitializer />
            <DynamicFavicon />
            <PermissionsCacheWrapper />
            <ToastContainer theme='colored' position='top-center' />
            <div className="flex flex-col min-h-screen">
              <Header />
              <main className="flex-grow">
                {children}
              </main>
              <Footer />
            </div>
          </PermissionsProvider>
        </DarkModeProvider>
      </body>
    </html>
  );
}

# API بطاقة التلميذ - التوثيق

## 📋 نظرة عامة

هذا الملف يحتوي على واجهة برمجة التطبيقات لجلب بيانات بطاقة التلميذ الشاملة.

## 🔗 المسار
`/api/students/[id]/card`

## 🔐 المصادقة
تتطلب مصادقة المستخدم عبر NextAuth.

## 📊 العمليات المدعومة

### GET - جلب بيانات بطاقة التلميذ

**الوصف:** جلب جميع البيانات اللازمة لعرض بطاقة التلميذ الشاملة.

**المعاملات:**
- `id` (في المسار): معرف التلميذ

**مثال على الطلب:**
```
GET /api/students/1/card
```

**الاستجابة:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "ahmed123",
    "name": "أحمد محمد علي",
    "age": 12,
    "phone": "0123456789",
    "createdAt": "15/06/2025",
    
    "guardian": {
      "id": 1,
      "name": "محمد علي أحمد",
      "phone": "0123456789",
      "email": "<EMAIL>",
      "address": "شارع النيل، القاهرة"
    },
    
    "classe": {
      "id": 1,
      "name": "الفصل الأول",
      "description": "فصل المبتدئين"
    },
    
    "profileImage": {
      "id": 1,
      "imageUrl": "/uploads/students/ahmed_profile.jpg",
      "description": "الصورة الشخصية",
      "uploadDate": "15/06/2025"
    },
    
    "memorizationStart": {
      "id": 1,
      "startDate": "01/06/2025",
      "startingJuz": 1,
      "startingSurah": 2,
      "startingVerse": 1,
      "level": "مبتدئ",
      "notes": "بداية الحفظ من سورة البقرة",
      "createdAt": "01/06/2025"
    },
    
    "quranProgress": [
      {
        "id": 1,
        "surahId": 2,
        "startVerse": 1,
        "endVerse": 20,
        "memorization": 8,
        "tajweed": 7,
        "startDate": "01/06/2025",
        "completionDate": "05/06/2025",
        "surah": {
          "id": 2,
          "name": "البقرة",
          "number": 2,
          "totalAyahs": 286
        }
      }
    ],
    
    "points": [
      {
        "id": 1,
        "points": 10,
        "date": "15/06/2025",
        "reason": "حفظ متقن لسورة البقرة"
      }
    ],
    
    "rewards": [
      {
        "id": 1,
        "date": "15/06/2025",
        "reward": {
          "id": 1,
          "name": "شهادة تقدير",
          "description": "شهادة للحفظ المتقن",
          "type": "شهادة"
        }
      }
    ],
    
    "payments": [
      {
        "id": 1,
        "amount": 100.0,
        "date": "01/06/2025",
        "status": "PAID",
        "receiptNumber": "REC-001"
      }
    ],
    
    "stats": {
      "attendance": {
        "present": 25,
        "absent": 3,
        "excused": 2,
        "total": 30,
        "rate": 83
      },
      "quran": {
        "totalVerses": 120,
        "averageMemorization": 8,
        "averageTajweed": 7,
        "completedSurahs": 3
      },
      "totalPoints": 150,
      "totalRewards": 2
    }
  }
}
```

## 📊 البيانات المُجمعة

### 1. البيانات الأساسية
- معلومات التلميذ الشخصية
- بيانات ولي الأمر
- معلومات الفصل
- الصورة الشخصية

### 2. بداية الحفظ
- السجل النشط الحالي لبداية الحفظ
- تاريخ البداية، الجزء، السورة، المستوى
- الملاحظات المرتبطة

### 3. تقدم الحفظ
- آخر 10 سجلات حفظ
- تفاصيل السور والآيات المحفوظة
- درجات الحفظ والتجويد
- تواريخ البداية والإكمال

### 4. النقاط والمكافآت
- آخر 5 نقاط مكتسبة مع الأسباب
- آخر 5 مكافآت مع التفاصيل
- إجمالي النقاط المكتسبة

### 5. الحضور والغياب
- آخر 30 يوم من سجلات الحضور
- إحصائيات الحضور (حاضر، غائب، معذور)
- معدل الحضور كنسبة مئوية

### 6. المدفوعات
- آخر 5 مدفوعات
- المبالغ وتواريخ الدفع
- حالة الدفع وأرقام الإيصالات

## 📈 الإحصائيات المحسوبة

### إحصائيات الحضور
```javascript
{
  "present": 25,      // عدد أيام الحضور
  "absent": 3,        // عدد أيام الغياب
  "excused": 2,       // عدد أيام الغياب بعذر
  "total": 30,        // إجمالي الأيام
  "rate": 83          // معدل الحضور بالنسبة المئوية
}
```

### إحصائيات الحفظ
```javascript
{
  "totalVerses": 120,           // إجمالي الآيات المحفوظة
  "averageMemorization": 8,     // متوسط درجة الحفظ
  "averageTajweed": 7,          // متوسط درجة التجويد
  "completedSurahs": 3          // عدد السور المكتملة
}
```

## 🎨 تنسيق التواريخ

جميع التواريخ تُعرض بصيغة DD/MM/YYYY (الأرقام الفرنسية):
- `createdAt`: تاريخ تسجيل التلميذ
- `startDate`: تاريخ بداية الحفظ
- `completionDate`: تاريخ إكمال الحفظ
- `date`: تواريخ النقاط والمكافآت والمدفوعات
- `uploadDate`: تاريخ رفع الصورة

## 🔍 الفلترة والتحديد

### الصورة الشخصية
- يتم جلب الصورة المحددة كصورة شخصية (`isProfilePic: true`)
- صورة واحدة فقط (الأحدث)

### بداية الحفظ
- السجل النشط فقط (`isActive: true`)
- سجل واحد فقط (الحالي)

### تقدم الحفظ
- مرتب حسب تاريخ البداية (الأحدث أولاً)
- محدود بـ 10 سجلات

### النقاط والمكافآت
- مرتبة حسب التاريخ (الأحدث أولاً)
- محدودة بـ 5 سجلات لكل منهما

### الحضور
- آخر 30 يوم فقط
- مرتب حسب التاريخ (الأحدث أولاً)

### المدفوعات
- آخر 5 مدفوعات
- مرتبة حسب التاريخ (الأحدث أولاً)

## ⚠️ رموز الأخطاء

- `401`: غير مصرح بالوصول (لا توجد جلسة مستخدم)
- `400`: معرف التلميذ غير صحيح
- `404`: التلميذ غير موجود
- `500`: خطأ في الخادم

## 🎯 الاستخدام المقترح

هذا الـ API مصمم لـ:
- عرض بطاقة التلميذ الشاملة
- طباعة بطاقة التلميذ
- تصدير بيانات التلميذ
- عرض ملخص شامل للأداء

## 📝 ملاحظات التطوير

- الاستعلام محسن لجلب جميع البيانات في طلب واحد
- يتم حساب الإحصائيات في الخادم لتحسين الأداء
- البيانات مُنسقة ومُجهزة للعرض المباشر
- يدعم جميع أنواع البيانات المطلوبة للبطاقة الشاملة

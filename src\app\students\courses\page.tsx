"use client";
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { FaBook, FaChalkboardTeacher, FaArrowRight } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface Course {
  id: number;
  subjectId: number;
  subjectName: string;
  teacherId: number;
  teacherName: string;
  description: string;
  progress: number;
}

const StudentCoursesPage = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/student-courses');

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات الدروس');
        }

        const data = await response.json();
        setCourses(data.courses);
      } catch (err) {
        console.error('Error fetching courses:', err);
        setError('حدث خطأ أثناء جلب بيانات الدروس');
        toast.error('فشل في جلب بيانات الدروس');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">دروسي</h1>
          <p className="text-gray-500">عرض جميع الدروس والمواد الدراسية</p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : courses.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-4">لا توجد دروس متاحة</div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {courses.map((course) => (
            <Card key={course.id} className="overflow-hidden">
              <CardHeader className="bg-[var(--primary-color)]/10 pb-2">
                <CardTitle className="flex items-center gap-2">
                  <FaBook className="text-[var(--primary-color)]" />
                  <span>{course.subjectName}</span>
                </CardTitle>
                <CardDescription className="flex items-center gap-1 mt-1">
                  <FaChalkboardTeacher className="text-gray-500" />
                  <span>{course.teacherName}</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {course.description || 'لا يوجد وصف متاح لهذه المادة.'}
                </p>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>التقدم</span>
                    <span>{course.progress}%</span>
                  </div>
                  <Progress value={course.progress} className="h-2" />
                </div>
              </CardContent>
              <CardFooter className="bg-gray-50 flex justify-end">
                <Link href={`/students/courses/${course.id}`}>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <span>عرض التفاصيل</span>
                    <FaArrowRight />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default StudentCoursesPage;

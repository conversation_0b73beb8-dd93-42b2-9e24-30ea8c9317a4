import * as XLSX from 'xlsx';
import { toast } from 'react-hot-toast';
import { QuestionType, DifficultyLevel } from '@prisma/client';

// تعريف أنواع البيانات
interface QuestionOption {
  text: string;
  isCorrect: boolean;
  order: number;
}

interface QuestionAnswer {
  text: string;
  isCorrect: boolean;
  explanation: string | null;
}

interface Question {
  id?: number;
  text: string;
  type: string;
  difficultyLevel: string;
  points: number;
  bankId: number;
  options: QuestionOption[];
  answers: QuestionAnswer[];
}

interface QuestionExcelRow {
  text: string;
  type: string;
  difficultyLevel: string;
  points: string;
  options?: string;
  correctOptions?: string;
  answers?: string;
  explanations?: string;
}

/**
 * تصدير الأسئلة إلى ملف Excel
 * @param questions قائمة الأسئلة المراد تصديرها
 * @param bankName اسم بنك الأسئلة (سيستخدم في اسم الملف)
 */
export const exportQuestionsToExcel = (questions: Question[], bankName: string) => {
  try {
    // تحويل الأسئلة إلى تنسيق مناسب للتصدير
    const data = questions.map(question => {
      // تحويل الخيارات إلى نص
      const options = question.options.map(opt => opt.text).join('|');
      const correctOptions = question.options
        .filter(opt => opt.isCorrect)
        .map(opt => opt.text)
        .join('|');
      
      // تحويل الإجابات إلى نص
      const answers = question.answers.map(ans => ans.text).join('|');
      const explanations = question.answers
        .filter(ans => ans.explanation)
        .map(ans => ans.explanation)
        .join('|');
      
      return {
        'نص السؤال': question.text,
        'نوع السؤال': question.type,
        'مستوى الصعوبة': question.difficultyLevel,
        'النقاط': question.points,
        'الخيارات': options,
        'الخيارات الصحيحة': correctOptions,
        'الإجابات': answers,
        'الشرح': explanations
      };
    });
    
    // إنشاء ورقة عمل Excel
    const worksheet = XLSX.utils.json_to_sheet(data, { 
      header: [
        'نص السؤال', 
        'نوع السؤال', 
        'مستوى الصعوبة', 
        'النقاط', 
        'الخيارات', 
        'الخيارات الصحيحة', 
        'الإجابات', 
        'الشرح'
      ] 
    });
    
    // تعديل عرض الأعمدة
    const columnWidths = [
      { wch: 50 }, // نص السؤال
      { wch: 15 }, // نوع السؤال
      { wch: 15 }, // مستوى الصعوبة
      { wch: 10 }, // النقاط
      { wch: 40 }, // الخيارات
      { wch: 40 }, // الخيارات الصحيحة
      { wch: 40 }, // الإجابات
      { wch: 40 }  // الشرح
    ];
    worksheet['!cols'] = columnWidths;
    
    // إنشاء كتاب عمل Excel
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'الأسئلة');
    
    // تنزيل الملف
    const fileName = `بنك_الأسئلة_${bankName}_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);
    
    toast.success('تم تصدير الأسئلة بنجاح');
    return true;
  } catch (error) {
    console.error('Error exporting questions:', error);
    toast.error('حدث خطأ أثناء تصدير الأسئلة');
    return false;
  }
};

/**
 * استيراد الأسئلة من ملف Excel
 * @param file ملف Excel المراد استيراد الأسئلة منه
 * @param bankId معرف بنك الأسئلة الذي سيتم إضافة الأسئلة إليه
 * @returns وعد يحتوي على قائمة الأسئلة المستوردة
 */
export const importQuestionsFromExcel = (file: File, bankId: number): Promise<Question[]> => {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // الحصول على الورقة الأولى
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // تحويل البيانات إلى JSON
          const jsonData = XLSX.utils.sheet_to_json<QuestionExcelRow>(worksheet);
          
          // تحويل البيانات إلى أسئلة
          const questions: Question[] = jsonData.map(row => {
            // التحقق من صحة نوع السؤال
            let questionType = row.type as QuestionType;
            if (!Object.values(QuestionType).includes(questionType)) {
              questionType = QuestionType.MULTIPLE_CHOICE;
            }
            
            // التحقق من صحة مستوى الصعوبة
            let difficultyLevel = row.difficultyLevel as DifficultyLevel;
            if (!Object.values(DifficultyLevel).includes(difficultyLevel)) {
              difficultyLevel = DifficultyLevel.MEDIUM;
            }
            
            // تحويل النقاط إلى رقم
            const points = parseFloat(row.points) || 1;
            
            // تحليل الخيارات
            const options: QuestionOption[] = [];
            if (row.options) {
              const optionsArray = row.options.split('|');
              const correctOptionsArray = row.correctOptions ? row.correctOptions.split('|') : [];
              
              optionsArray.forEach((optionText, index) => {
                options.push({
                  text: optionText.trim(),
                  isCorrect: correctOptionsArray.includes(optionText.trim()),
                  order: index
                });
              });
            }
            
            // تحليل الإجابات
            const answers: QuestionAnswer[] = [];
            if (row.answers) {
              const answersArray = row.answers.split('|');
              const explanationsArray = row.explanations ? row.explanations.split('|') : [];
              
              answersArray.forEach((answerText, index) => {
                answers.push({
                  text: answerText.trim(),
                  isCorrect: true,
                  explanation: explanationsArray[index] ? explanationsArray[index].trim() : null
                });
              });
            }
            
            return {
              text: row.text,
              type: questionType,
              difficultyLevel: difficultyLevel,
              points: points,
              bankId: bankId,
              options: options,
              answers: answers
            };
          });
          
          resolve(questions);
        } catch (error) {
          console.error('Error parsing Excel file:', error);
          reject(new Error('حدث خطأ أثناء تحليل ملف Excel'));
        }
      };
      
      reader.onerror = () => {
        reject(new Error('حدث خطأ أثناء قراءة الملف'));
      };
      
      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error('Error importing questions:', error);
      reject(new Error('حدث خطأ أثناء استيراد الأسئلة'));
    }
  });
};

/**
 * حفظ الأسئلة المستوردة في قاعدة البيانات
 * @param questions قائمة الأسئلة المراد حفظها
 * @returns وعد يحتوي على عدد الأسئلة التي تم حفظها بنجاح
 */
export const saveImportedQuestions = async (questions: Question[]): Promise<number> => {
  let savedCount = 0;
  
  for (const question of questions) {
    try {
      const response = await fetch('/api/questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(question),
      });
      
      const result = await response.json();
      
      if (result.success) {
        savedCount++;
      }
    } catch (error) {
      console.error('Error saving imported question:', error);
    }
  }
  
  return savedCount;
};

/**
 * نسخ الأسئلة من بنك أسئلة إلى آخر
 * @param questions قائمة الأسئلة المراد نسخها
 * @param targetBankId معرف بنك الأسئلة الهدف
 * @returns وعد يحتوي على عدد الأسئلة التي تم نسخها بنجاح
 */
export const copyQuestionsToBankId = async (questions: Question[], targetBankId: number): Promise<number> => {
  let copiedCount = 0;
  
  for (const question of questions) {
    try {
      // إنشاء نسخة من السؤال مع تغيير معرف البنك
      const copiedQuestion = {
        ...question,
        id: undefined, // إزالة المعرف لإنشاء سؤال جديد
        bankId: targetBankId
      };
      
      const response = await fetch('/api/questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(copiedQuestion),
      });
      
      const result = await response.json();
      
      if (result.success) {
        copiedCount++;
      }
    } catch (error) {
      console.error('Error copying question:', error);
    }
  }
  
  return copiedCount;
};

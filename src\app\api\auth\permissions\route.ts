import { NextRequest, NextResponse } from 'next/server';
import { getToken } from '@/lib/auth';
import prisma from '@/lib/prisma';

// GET: جلب صلاحيات المستخدم الحالي
export async function GET(request: NextRequest) {
  try {
    console.log('Starting permissions API request...');

    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      console.log('No token found');
      return NextResponse.json(
        { message: "غير مصرح به: يرجى تسجيل الدخول" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    console.log('User data from token:', userData);

    if (!userData) {
      console.log('Invalid token');
      return NextResponse.json(
        { message: "غير مصرح به: توكن غير صالح" },
        { status: 401 }
      );
    }

    // إذا كان المستخدم مدير، جلب جميع الصلاحيات للمرجع
    if (userData.role === 'ADMIN') {
      console.log('User is admin, fetching all permissions...');
      const allPermissions = await prisma.permission.findMany({
        where: { isActive: true },
        orderBy: [
          { category: 'asc' },
          { name: 'asc' }
        ]
      });

      console.log(`Found ${allPermissions.length} permissions for admin`);
      return NextResponse.json({
        permissions: allPermissions,
        message: "تم جلب صلاحيات المدير بنجاح"
      });
    }

    // للموظفين، جلب الصلاحيات من خلال الدور
    console.log('User is not admin, fetching user with role...');
    const user = await prisma.user.findUnique({
      where: { id: userData.id },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              },
              where: {
                permission: {
                  isActive: true
                }
              }
            }
          }
        }
      }
    });

    console.log('User found:', user ? 'Yes' : 'No');
    console.log('User role:', user?.userRole ? 'Yes' : 'No');

    if (!user) {
      return NextResponse.json(
        { message: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    // جلب الصلاحيات من النظام الجديد
    const permissions = user.userRole?.permissions?.map(rp => rp.permission) || [];
    console.log(`Found ${permissions.length} permissions for user`);

    return NextResponse.json({
      permissions,
      role: user.role,
      roleId: user.roleId,
      roleName: user.userRole?.displayName || null,
      message: "تم جلب صلاحيات المستخدم بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching user permissions:', error);
    console.error('Error details:', error);

    // إضافة تفاصيل أكثر للخطأ
    let errorMessage = "حدث خطأ أثناء جلب صلاحيات المستخدم";
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      errorMessage += `: ${error.message}`;
    }

    return NextResponse.json(
      { message: errorMessage },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 بدء فحص الفاتورة الجماعية لأحمد محمود...');

    // 1. البحث عن الولي
    const parent = await prisma.parent.findFirst({
      where: {
        name: {
          contains: 'أحمد محمود'
        }
      }
    });

    if (!parent) {
      return NextResponse.json({ error: 'لم يتم العثور على الولي' }, { status: 404 });
    }

    console.log('👤 تم العثور على الولي:', parent);

    // 2. فحص الفواتير الجماعية
    const familyInvoices = await prisma.invoice.findMany({
      where: {
        parentId: parent.id,
        type: 'FAMILY'
      },
      include: {
        payments: true
      }
    });

    console.log('📄 الفواتير الجماعية:', familyInvoices);

    // 3. فحص المدفوعات المرتبطة بالفواتير الجماعية
    const familyPayments = await prisma.payment.findMany({
      where: {
        invoiceId: {
          in: familyInvoices.map(inv => inv.id)
        }
      },
      include: {
        student: true,
        invoice: true
      }
    });

    console.log('💰 المدفوعات للفواتير الجماعية:', familyPayments);

    // 4. فحص جميع المدفوعات للولي
    const allPayments = await prisma.payment.findMany({
      where: {
        student: {
          guardianId: parent.id
        }
      },
      include: {
        student: true,
        invoice: true
      }
    });

    console.log('💳 جميع المدفوعات للولي:', allPayments);

    // 5. حساب الإحصائيات
    const stats = {
      parentName: parent.name,
      familyInvoicesCount: familyInvoices.length,
      familyInvoicesTotal: familyInvoices.reduce((sum, inv) => sum + inv.amount, 0),
      familyPaymentsCount: familyPayments.filter(p => p.status === 'PAID').length,
      familyPaymentsTotal: familyPayments.filter(p => p.status === 'PAID').reduce((sum, p) => sum + p.amount, 0),
      allPaymentsCount: allPayments.filter(p => p.status === 'PAID').length,
      allPaymentsTotal: allPayments.filter(p => p.status === 'PAID').reduce((sum, p) => sum + p.amount, 0)
    };

    console.log('📊 الإحصائيات:', stats);

    // 6. تفاصيل كل فاتورة جماعية
    const invoiceDetails = await Promise.all(
      familyInvoices.map(async (invoice) => {
        const payments = await prisma.payment.findMany({
          where: {
            invoiceId: invoice.id,
            status: 'PAID'
          },
          include: {
            student: true
          }
        });

        const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);

        return {
          invoiceId: invoice.id,
          amount: invoice.amount,
          description: invoice.description,
          status: invoice.status,
          paymentsCount: payments.length,
          totalPaid,
          remaining: invoice.amount - totalPaid,
          payments: payments.map(p => ({
            id: p.id,
            amount: p.amount,
            date: p.date,
            studentName: p.student.name
          }))
        };
      })
    );

    console.log('🔍 تفاصيل الفواتير الجماعية:', invoiceDetails);

    return NextResponse.json({
      success: true,
      parent,
      familyInvoices,
      familyPayments,
      allPayments,
      stats,
      invoiceDetails
    });

  } catch (error) {
    console.error('❌ خطأ في فحص الفاتورة الجماعية:', error);
    return NextResponse.json(
      { error: 'فشل في فحص البيانات' },
      { status: 500 }
    );
  }
}

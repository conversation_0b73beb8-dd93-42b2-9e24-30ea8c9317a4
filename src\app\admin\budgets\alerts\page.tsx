"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  FaExclamationTriangle,
  FaBell,
  FaArrowLeft,
  FaCog,
  FaPercentage,
  FaSave,
  FaChartPie,
  FaTags,
  FaExclamationCircle,
  FaCheckCircle
} from 'react-icons/fa';
import Link from 'next/link';
import { Progress } from '@/components/ui/progress';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface BudgetAlert {
  type: 'BUDGET_TOTAL' | 'BUDGET_ITEM';
  budgetId: number;
  budgetName: string;
  itemId?: number;
  categoryId?: number;
  categoryName?: string;
  totalAmount?: number;
  budgetAmount?: number;
  actualAmount: number;
  remainingAmount: number;
  usagePercentage: number;
  severity: 'WARNING' | 'CRITICAL';
  message: string;
  date: string;
}

interface AlertSettings {
  warningThreshold: number;
  criticalThreshold: number;
  enableNotifications: boolean;
  notificationEmail: string | null;
}

export default function BudgetAlertsPage() {
  const [alerts, setAlerts] = useState<BudgetAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [settings, setSettings] = useState<AlertSettings>({
    warningThreshold: 90,
    criticalThreshold: 100,
    enableNotifications: false,
    notificationEmail: null,
  });

  // جلب تنبيهات الميزانية
  const fetchAlerts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/budgets/alerts');

      if (!response.ok) {
        throw new Error('فشل في جلب تنبيهات الميزانية');
      }

      const data = await response.json();
      setAlerts(data.alerts);
    } catch (error) {
      console.error('خطأ في جلب تنبيهات الميزانية:', error);
      toast.error('فشل في جلب تنبيهات الميزانية');
    } finally {
      setLoading(false);
    }
  };

  // جلب إعدادات التنبيهات
  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/budgets/alerts/settings');

      if (!response.ok) {
        throw new Error('فشل في جلب إعدادات التنبيهات');
      }

      const data = await response.json();
      setSettings(data.settings);
    } catch (error) {
      console.error('خطأ في جلب إعدادات التنبيهات:', error);
      // استخدام الإعدادات الافتراضية في حالة الفشل
    }
  };

  // حفظ إعدادات التنبيهات
  const saveSettings = async () => {
    try {
      const response = await fetch('/api/budgets/alerts/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        throw new Error('فشل في حفظ إعدادات التنبيهات');
      }

      toast.success('تم حفظ إعدادات التنبيهات بنجاح');
      setIsSettingsDialogOpen(false);
      fetchAlerts(); // إعادة تحميل التنبيهات بعد تغيير الإعدادات
    } catch (error) {
      console.error('خطأ في حفظ إعدادات التنبيهات:', error);
      toast.error('فشل في حفظ إعدادات التنبيهات');
    }
  };

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchAlerts();
    fetchSettings();
  }, []);

  // تصنيف التنبيهات حسب الشدة
  const criticalAlerts = alerts.filter(alert => alert.severity === 'CRITICAL');
  const warningAlerts = alerts.filter(alert => alert.severity === 'WARNING');

  return (
    <ProtectedRoute requiredPermission="admin.budgets.alerts.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Link href="/admin/budgets">
            <Button variant="outline" className="h-10 w-10 p-0">
              <FaArrowLeft />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaExclamationTriangle className="text-[var(--primary-color)]" />
            تنبيهات الميزانية
          </h1>
        </div>
        <Button
          onClick={() => setIsSettingsDialogOpen(true)}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
        >
          <FaCog size={14} />
          <span>إعدادات التنبيهات</span>
        </Button>
      </div>

      {/* محتوى التنبيهات */}
      <Tabs defaultValue="critical" className="space-y-4">
        <TabsList className="bg-[#e0f2ef] p-1">
          <TabsTrigger
            value="critical"
            className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white"
          >
            <FaExclamationCircle className="ml-2 text-red-600" />
            تنبيهات حرجة ({criticalAlerts.length})
          </TabsTrigger>
          <TabsTrigger
            value="warning"
            className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white"
          >
            <FaExclamationTriangle className="ml-2 text-yellow-600" />
            تنبيهات تحذيرية ({warningAlerts.length})
          </TabsTrigger>
          <TabsTrigger
            value="all"
            className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white"
          >
            <FaBell className="ml-2" />
            جميع التنبيهات ({alerts.length})
          </TabsTrigger>
        </TabsList>

        {/* تنبيهات حرجة */}
        <TabsContent value="critical" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">جاري تحميل التنبيهات...</div>
          ) : criticalAlerts.length === 0 ? (
            <Card className="bg-white shadow-md border border-[#e0f2ef]">
              <CardContent className="pt-6 text-center">
                <FaCheckCircle className="text-primary-color text-4xl mx-auto mb-4" />
                <p className="text-gray-600">لا توجد تنبيهات حرجة حالياً</p>
              </CardContent>
            </Card>
          ) : (
            criticalAlerts.map((alert, index) => (
              <AlertCard key={index} alert={alert} />
            ))
          )}
        </TabsContent>

        {/* تنبيهات تحذيرية */}
        <TabsContent value="warning" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">جاري تحميل التنبيهات...</div>
          ) : warningAlerts.length === 0 ? (
            <Card className="bg-white shadow-md border border-[#e0f2ef]">
              <CardContent className="pt-6 text-center">
                <FaCheckCircle className="text-primary-color text-4xl mx-auto mb-4" />
                <p className="text-gray-600">لا توجد تنبيهات تحذيرية حالياً</p>
              </CardContent>
            </Card>
          ) : (
            warningAlerts.map((alert, index) => (
              <AlertCard key={index} alert={alert} />
            ))
          )}
        </TabsContent>

        {/* جميع التنبيهات */}
        <TabsContent value="all" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">جاري تحميل التنبيهات...</div>
          ) : alerts.length === 0 ? (
            <Card className="bg-white shadow-md border border-[#e0f2ef]">
              <CardContent className="pt-6 text-center">
                <FaCheckCircle className="text-primary-color text-4xl mx-auto mb-4" />
                <p className="text-gray-600">لا توجد تنبيهات للميزانية حالياً</p>
              </CardContent>
            </Card>
          ) : (
            alerts.map((alert, index) => (
              <AlertCard key={index} alert={alert} />
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* نافذة إعدادات التنبيهات */}
      <AnimatedDialog
        isOpen={isSettingsDialogOpen}
        onClose={() => setIsSettingsDialogOpen(false)}
        title="إعدادات تنبيهات الميزانية"
        variant="primary"
        footer={
          <Button
            onClick={saveSettings}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaSave size={14} />
            <span>حفظ الإعدادات</span>
          </Button>
        }
      >
        <div className="space-y-4 p-4">
          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="warningThreshold" className="text-right col-span-1">
              عتبة التحذير (%)
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="warningThreshold"
                type="number"
                min="1"
                max="100"
                value={settings.warningThreshold}
                onChange={(e) => setSettings({ ...settings, warningThreshold: parseInt(e.target.value) })}
                className="w-24"
              />
              <FaPercentage className="text-gray-500" />
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="criticalThreshold" className="text-right col-span-1">
              عتبة الحرج (%)
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="criticalThreshold"
                type="number"
                min="1"
                max="100"
                value={settings.criticalThreshold}
                onChange={(e) => setSettings({ ...settings, criticalThreshold: parseInt(e.target.value) })}
                className="w-24"
              />
              <FaPercentage className="text-gray-500" />
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="enableNotifications" className="text-right col-span-1">
              تفعيل الإشعارات
            </Label>
            <div className="col-span-3">
              <Switch
                id="enableNotifications"
                checked={settings.enableNotifications}
                onCheckedChange={(checked) => setSettings({ ...settings, enableNotifications: checked })}
              />
            </div>
          </div>

          {settings.enableNotifications && (
            <div className="grid grid-cols-4 gap-4 items-center">
              <Label htmlFor="notificationEmail" className="text-right col-span-1">
                البريد الإلكتروني للإشعارات
              </Label>
              <div className="col-span-3">
                <Input
                  id="notificationEmail"
                  type="email"
                  value={settings.notificationEmail || ''}
                  onChange={(e) => setSettings({ ...settings, notificationEmail: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          )}
        </div>
      </AnimatedDialog>
      </div>
    </ProtectedRoute>
  );
}

// مكون بطاقة التنبيه
function AlertCard({ alert }: { alert: BudgetAlert }) {
  return (
    <Card className={`bg-white shadow-md border ${
      alert.severity === 'CRITICAL' ? 'border-red-200' : 'border-yellow-200'
    }`}>
      <CardHeader className={`pb-2 ${
        alert.severity === 'CRITICAL' ? 'bg-red-50' : 'bg-yellow-50'
      }`}>
        <CardTitle className="text-lg flex items-center gap-2">
          {alert.severity === 'CRITICAL' ? (
            <FaExclamationCircle className="text-red-600" />
          ) : (
            <FaExclamationTriangle className="text-yellow-600" />
          )}
          <span>{alert.message}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <FaChartPie className="text-[var(--primary-color)]" />
              <span className="font-semibold">الميزانية:</span>
              <Link href={`/admin/budgets/${alert.budgetId}`} className="text-blue-600 hover:underline">
                {alert.budgetName}
              </Link>
            </div>
            <div className="text-sm text-gray-500">
              {new Date(alert.date).toLocaleDateString('fr-FR')}
            </div>
          </div>

          {alert.type === 'BUDGET_ITEM' && (
            <div className="flex items-center gap-2">
              <FaTags className="text-[var(--primary-color)]" />
              <span className="font-semibold">الفئة:</span>
              <span>{alert.categoryName}</span>
            </div>
          )}

          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>نسبة الاستخدام:</span>
              <span className={`font-semibold ${
                alert.usagePercentage >= 100 ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {alert.usagePercentage.toFixed(1)}%
              </span>
            </div>
            <Progress
              value={Math.min(alert.usagePercentage, 100)}
              className="h-2 bg-gray-100"
              indicatorClassName={`${
                alert.usagePercentage >= 100 ? 'bg-red-600' : 'bg-yellow-600'
              }`}
            />
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex flex-col">
              <span className="text-gray-500">المبلغ المخصص:</span>
              <span className="font-semibold">
                {alert.type === 'BUDGET_TOTAL'
                  ? alert.totalAmount?.toLocaleString('fr-FR')
                  : alert.budgetAmount?.toLocaleString('fr-FR')} د.ج
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-gray-500">المبلغ المستخدم:</span>
              <span className="font-semibold">
                {alert.actualAmount.toLocaleString('fr-FR')} د.ج
              </span>
            </div>
          </div>

          <div className="flex justify-end">
            <Link href={`/admin/budgets/${alert.budgetId}`}>
              <Button
                variant="outline"
                size="sm"
                className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef]"
              >
                عرض تفاصيل الميزانية
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

"use client";

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  FaExchangeAlt,
  FaArrowLeft,
  FaInfoCircle,
  FaSave,
  FaHistory,
  FaChartPie
} from 'react-icons/fa';
import Link from 'next/link';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface Budget {
  id: number;
  name: string;
  description: string | null;
  startDate: string;
  endDate: string;
  totalAmount: number;
  status: 'DRAFT' | 'ACTIVE' | 'COMPLETED' | 'ARCHIVED';
}

interface BudgetItem {
  id: number;
  categoryId: number;
  amount: number;
  actualAmount: number;
  remainingAmount: number;
  usagePercentage: number;
  category: {
    id: number;
    name: string;
  };
}

interface BudgetTransfer {
  id: number;
  amount: number;
  date: string;
  notes: string | null;
  fromItem: {
    id: number;
    categoryId: number;
    categoryName: string;
  };
  toItem: {
    id: number;
    categoryId: number;
    categoryName: string;
  };
}

export default function BudgetTransfersPage() {
  const params = useParams();
  const budgetId = params?.id ? parseInt(params.id as string) : 0;

  const [budget, setBudget] = useState<Budget | null>(null);
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([]);
  const [transfers, setTransfers] = useState<BudgetTransfer[]>([]);
  const [loading, setLoading] = useState(true);
  const [transfersLoading, setTransfersLoading] = useState(true);
  const [isTransferDialogOpen, setIsTransferDialogOpen] = useState(false);
  const [transferFormData, setTransferFormData] = useState({
    fromItemId: '',
    toItemId: '',
    amount: '',
    notes: '',
  });
  const [selectedFromItem, setSelectedFromItem] = useState<BudgetItem | null>(null);

  // جلب معلومات الميزانية وبنودها
  const fetchBudgetDetails = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/budgets/${budgetId}`);

      if (!response.ok) {
        throw new Error('فشل في جلب معلومات الميزانية');
      }

      const data = await response.json();
      setBudget(data);

      // جلب بنود الميزانية مع المصروفات الفعلية
      const itemsResponse = await fetch(`/api/budgets/${budgetId}/items`);

      if (!itemsResponse.ok) {
        throw new Error('فشل في جلب بنود الميزانية');
      }

      const itemsData = await itemsResponse.json();
      setBudgetItems(itemsData);
    } catch (error) {
      console.error('خطأ في جلب معلومات الميزانية:', error);
      toast.error('فشل في جلب معلومات الميزانية');
    } finally {
      setLoading(false);
    }
  }, [budgetId]);

  // جلب سجل التحويلات
  const fetchTransfers = useCallback(async () => {
    try {
      setTransfersLoading(true);
      const response = await fetch(`/api/budgets/transfer?budgetId=${budgetId}`);

      if (!response.ok) {
        throw new Error('فشل في جلب سجل التحويلات');
      }

      const data = await response.json();
      setTransfers(data.transfers);
    } catch (error) {
      console.error('خطأ في جلب سجل التحويلات:', error);
      toast.error('فشل في جلب سجل التحويلات');
    } finally {
      setTransfersLoading(false);
    }
  }, [budgetId]);

  // تحديث البند المصدر المحدد
  const updateSelectedFromItem = (itemId: string) => {
    const item = budgetItems.find(item => item.id === parseInt(itemId));
    setSelectedFromItem(item || null);

    // إعادة تعيين البند الهدف إذا كان هو نفس البند المصدر
    if (transferFormData.toItemId === itemId) {
      setTransferFormData({
        ...transferFormData,
        fromItemId: itemId,
        toItemId: '',
      });
    } else {
      setTransferFormData({
        ...transferFormData,
        fromItemId: itemId,
      });
    }
  };

  // إجراء عملية النقل
  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (!transferFormData.fromItemId || !transferFormData.toItemId || !transferFormData.amount) {
        toast.error('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      const amount = parseFloat(transferFormData.amount);
      if (isNaN(amount) || amount <= 0) {
        toast.error('المبلغ يجب أن يكون رقمًا موجبًا');
        return;
      }

      const response = await fetch('/api/budgets/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          budgetId,
          fromItemId: parseInt(transferFormData.fromItemId),
          toItemId: parseInt(transferFormData.toItemId),
          amount,
          notes: transferFormData.notes || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في نقل المبلغ');
      }

      const data = await response.json();
      toast.success(data.message);
      setIsTransferDialogOpen(false);

      // إعادة تعيين نموذج النقل
      setTransferFormData({
        fromItemId: '',
        toItemId: '',
        amount: '',
        notes: '',
      });

      // إعادة تحميل البيانات
      fetchBudgetDetails();
      fetchTransfers();
    } catch (error) {
      console.error('خطأ في نقل المبلغ:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في نقل المبلغ');
    }
  };

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchBudgetDetails();
    fetchTransfers();
  }, [budgetId, fetchBudgetDetails, fetchTransfers]);

  // التحقق من صلاحية الميزانية للنقل
  const canTransfer = budget?.status === 'ACTIVE';

  return (
    <ProtectedRoute requiredPermission="admin.budgets.transfers.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Link href={`/admin/budgets/${budgetId}`}>
            <Button variant="outline" className="h-10 w-10 p-0">
              <FaArrowLeft />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaExchangeAlt className="text-[var(--primary-color)]" />
            نقل المبالغ بين بنود الميزانية
          </h1>
        </div>
        <Button
          onClick={() => setIsTransferDialogOpen(true)}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          disabled={!canTransfer || loading}
        >
          <FaExchangeAlt size={14} />
          <span>نقل مبلغ جديد</span>
        </Button>
      </div>

      {/* معلومات الميزانية */}
      {loading ? (
        <Card className="bg-white shadow-md border border-[#e0f2ef]">
          <CardContent className="pt-6 text-center">
            <p className="text-gray-500">جاري تحميل معلومات الميزانية...</p>
          </CardContent>
        </Card>
      ) : budget ? (
        <Card className="bg-white shadow-md border border-[#e0f2ef]">
          <CardHeader className="pb-2">
            <CardTitle className="text-xl flex items-center gap-2">
              <FaChartPie className="text-[var(--primary-color)]" />
              <span>{budget.name}</span>
            </CardTitle>
            <CardDescription>
              {new Date(budget.startDate).toLocaleDateString('fr-FR')} - {new Date(budget.endDate).toLocaleDateString('fr-FR')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex flex-col">
                <span className="text-sm text-gray-500">المبلغ الإجمالي</span>
                <span className="font-semibold">{budget.totalAmount.toLocaleString('fr-FR')} د.ج</span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500">حالة الميزانية</span>
                <span className={`font-semibold ${
                  budget.status === 'ACTIVE' ? 'text-primary-color' :
                  budget.status === 'DRAFT' ? 'text-yellow-600' :
                  budget.status === 'COMPLETED' ? 'text-blue-600' : 'text-gray-600'
                }`}>
                  {budget.status === 'ACTIVE' ? 'نشطة' :
                   budget.status === 'DRAFT' ? 'مسودة' :
                   budget.status === 'COMPLETED' ? 'مكتملة' : 'مؤرشفة'}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-gray-500">عدد البنود</span>
                <span className="font-semibold">{budgetItems.length}</span>
              </div>
            </div>

            {!canTransfer && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-700 flex items-center gap-2">
                <FaInfoCircle />
                <span>لا يمكن نقل المبالغ إلا في الميزانيات النشطة</span>
              </div>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card className="bg-white shadow-md border border-[#e0f2ef]">
          <CardContent className="pt-6 text-center">
            <p className="text-red-500">لم يتم العثور على الميزانية</p>
          </CardContent>
        </Card>
      )}

      {/* سجل التحويلات */}
      <Card className="bg-white shadow-md border border-[#e0f2ef]">
        <CardHeader className="pb-2">
          <CardTitle className="text-xl flex items-center gap-2">
            <FaHistory className="text-[var(--primary-color)]" />
            <span>سجل التحويلات</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {transfersLoading ? (
            <div className="text-center py-4">
              <p className="text-gray-500">جاري تحميل سجل التحويلات...</p>
            </div>
          ) : transfers.length === 0 ? (
            <div className="text-center py-4 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <p className="text-gray-500 mb-2">لا توجد تحويلات بين بنود الميزانية</p>
              {canTransfer && (
                <Button
                  onClick={() => setIsTransferDialogOpen(true)}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
                >
                  <FaExchangeAlt className="ml-2" size={14} />
                  إجراء أول تحويل
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>من</TableHead>
                    <TableHead>إلى</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>ملاحظات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transfers.map((transfer) => (
                    <TableRow key={transfer.id}>
                      <TableCell>{new Date(transfer.date).toLocaleDateString('fr-FR')}</TableCell>
                      <TableCell>{transfer.fromItem.categoryName}</TableCell>
                      <TableCell>{transfer.toItem.categoryName}</TableCell>
                      <TableCell>{transfer.amount.toLocaleString('fr-FR')} د.ج</TableCell>
                      <TableCell>{transfer.notes || '-'}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نافذة نقل المبالغ */}
      <AnimatedDialog
        isOpen={isTransferDialogOpen}
        onClose={() => setIsTransferDialogOpen(false)}
        title="نقل مبلغ بين بنود الميزانية"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="transfer-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaSave size={14} />
            <span>إجراء النقل</span>
          </Button>
        }
      >
        <form id="transfer-form" onSubmit={handleTransfer} className="space-y-4 p-4">
          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="fromItemId" className="text-right col-span-1">
              نقل من <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                value={transferFormData.fromItemId}
                onValueChange={updateSelectedFromItem}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر البند المصدر" />
                </SelectTrigger>
                <SelectContent>
                  {budgetItems.map((item) => (
                    <SelectItem
                      key={item.id}
                      value={item.id.toString()}
                      disabled={item.remainingAmount <= 0}
                    >
                      {item.category.name} ({item.remainingAmount.toLocaleString('fr-FR')} د.ج متاح)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {selectedFromItem && (
            <div className="grid grid-cols-4 gap-4 items-center">
              <div className="col-span-1"></div>
              <div className="col-span-3 p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-sm">
                <div className="flex justify-between mb-1">
                  <span>المبلغ المخصص:</span>
                  <span>{selectedFromItem.amount.toLocaleString('fr-FR')} د.ج</span>
                </div>
                <div className="flex justify-between mb-1">
                  <span>المبلغ المستخدم:</span>
                  <span>{selectedFromItem.actualAmount.toLocaleString('fr-FR')} د.ج</span>
                </div>
                <div className="flex justify-between font-semibold">
                  <span>المبلغ المتاح للنقل:</span>
                  <span>{selectedFromItem.remainingAmount.toLocaleString('fr-FR')} د.ج</span>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="toItemId" className="text-right col-span-1">
              نقل إلى <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                value={transferFormData.toItemId}
                onValueChange={(value) => setTransferFormData({ ...transferFormData, toItemId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر البند الهدف" />
                </SelectTrigger>
                <SelectContent>
                  {budgetItems
                    .filter(item => item.id.toString() !== transferFormData.fromItemId)
                    .map((item) => (
                      <SelectItem key={item.id} value={item.id.toString()}>
                        {item.category.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="amount" className="text-right col-span-1">
              المبلغ <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="amount"
                type="number"
                min="1"
                max={selectedFromItem?.remainingAmount || 0}
                step="0.01"
                value={transferFormData.amount}
                onChange={(e) => setTransferFormData({ ...transferFormData, amount: e.target.value })}
                className="flex-1"
                required
              />
              <span className="text-gray-500">د.ج</span>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-start">
            <Label htmlFor="notes" className="text-right col-span-1 mt-2">
              ملاحظات
            </Label>
            <div className="col-span-3">
              <Textarea
                id="notes"
                value={transferFormData.notes}
                onChange={(e) => setTransferFormData({ ...transferFormData, notes: e.target.value })}
                placeholder="أدخل أي ملاحظات إضافية حول عملية النقل"
                className="min-h-[100px]"
              />
            </div>
          </div>
        </form>
      </AnimatedDialog>
      </div>
    </ProtectedRoute>
  );
}

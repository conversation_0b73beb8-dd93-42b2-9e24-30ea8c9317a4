import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { randomUUID } from 'crypto';
import { existsSync } from 'fs';

// POST /api/upload - رفع صورة
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string; // student, attendance, khatm

    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم تحديد ملف'
      }, { status: 400 });
    }

    if (!type || !['student', 'attendance', 'khatm', 'header-icons'].includes(type)) {
      return NextResponse.json({
        success: false,
        error: 'نوع الصورة غير صالح'
      }, { status: 400 });
    }

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        success: false,
        error: 'نوع الملف غير مدعوم. يجب أن يكون الملف صورة (JPEG, PNG, GIF, WebP, أو SVG)'
      }, { status: 400 });
    }

    // التحقق من حجم الملف (مثلاً: 5 ميجابايت كحد أقصى)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        error: 'حجم الملف كبير جداً. الحد الأقصى المسموح به هو 5 ميجابايت'
      }, { status: 400 });
    }

    // قراءة محتوى الملف
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // إنشاء اسم فريد للملف
    const fileName = `${randomUUID()}_${file.name.replace(/\s+/g, '_')}`;

    try {
      // تحديد المسار حسب نوع الصورة
      const uploadDir = join(process.cwd(), 'public', 'uploads', type);

      // التأكد من وجود المجلد، وإنشاؤه إذا لم يكن موجوداً
      if (!existsSync(uploadDir)) {
        // إنشاء المجلد الرئيسي للتحميلات إذا لم يكن موجوداً
        const uploadsDir = join(process.cwd(), 'public', 'uploads');
        if (!existsSync(uploadsDir)) {
          await mkdir(uploadsDir, { recursive: true });
        }
        // إنشاء المجلد الفرعي حسب النوع
        await mkdir(uploadDir, { recursive: true });
      }
    } catch (dirError) {
      console.error('Error creating directories:', dirError);
      return NextResponse.json({
        success: false,
        error: 'حدث خطأ أثناء إنشاء مجلدات التحميل'
      }, { status: 500 });
    }

    let filePath;
    let relativePath;

    try {
      filePath = join(process.cwd(), 'public', 'uploads', type, fileName);
      relativePath = `/uploads/${type}/${fileName}`;

      // حفظ الملف
      await writeFile(filePath, buffer);
    } catch (writeError) {
      console.error('Error writing file:', writeError);
      return NextResponse.json({
        success: false,
        error: 'حدث خطأ أثناء حفظ الملف'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        fileName,
        filePath: relativePath,
        fileSize: file.size,
        fileType: file.type
      },
      message: 'تم رفع الملف بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء رفع الملف'
    }, { status: 500 });
  }
}

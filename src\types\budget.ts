export interface Budget {
  id: number;
  name: string;
  description?: string;
  startDate: string;
  endDate: string;
  status: string;
  totalAmount: number;
  totalActualAmount: number;
  totalRemainingAmount: number;
  items: BudgetItem[];
}

export interface BudgetItem {
  id: number;
  budgetId: number;
  categoryId: number;
  category: {
    id: number;
    name: string;
  };
  amount: number;
  actualAmount: number;
  remainingAmount: number;
  notes?: string;
}

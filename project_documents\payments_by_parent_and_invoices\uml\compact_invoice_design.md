# 🧾 تصميم الفاتورة المدمجة للطباعة

## مخطط حالة الاستخدام للفواتير المدمجة

```mermaid
graph TB
    Admin[المسؤول/الموظف]
    Parent[ولي الأمر]
    
    Admin --> UC1[إنشاء فاتورة مدمجة]
    Admin --> UC2[طباعة فاتورة مدمجة]
    Admin --> UC3[معاينة الفاتورة قبل الطباعة]
    Admin --> UC4[تخصيص خيارات الطباعة]
    
    Parent --> UC5[عرض فاتورة مدمجة]
    Parent --> UC6[تحميل فاتورة PDF]
    
    UC1 --> UC1_1[اختيار حجم الفاتورة]
    UC1 --> UC1_2[تحديد المعلومات المطلوبة]
    UC1 --> UC1_3[إضافة شعار المؤسسة]
    
    UC2 --> UC2_1[طباعة فورية]
    UC2 --> UC2_2[طباعة متعددة]
    UC2 --> UC2_3[طباعة بدون هوامش]
    
    UC4 --> UC4_1[اختيار حجم الورق]
    UC4 --> UC4_2[تحديد جودة الطباعة]
    UC4 --> UC4_3[خيارات التخطيط]
```

## مخطط تسلسل إنشاء الفاتورة المدمجة

```mermaid
sequenceDiagram
    participant U as المستخدم
    participant UI as واجهة الفواتير
    participant API as API الفواتير
    participant PDF as مولد PDF
    participant DB as قاعدة البيانات
    
    U->>UI: طلب طباعة فاتورة مدمجة
    UI->>UI: عرض خيارات الطباعة
    U->>UI: اختيار خيارات الطباعة المدمجة
    UI->>API: POST /api/invoices/compact-pdf/[id]
    API->>DB: جلب بيانات الفاتورة والطالب
    DB-->>API: بيانات الفاتورة
    API->>PDF: إنشاء PDF مدمج
    PDF->>PDF: تطبيق تخطيط مدمج
    PDF->>PDF: ضغط المحتوى
    PDF->>PDF: تحسين الخطوط والصور
    PDF-->>API: ملف PDF مدمج
    API-->>UI: رابط تحميل PDF
    UI->>U: فتح PDF للطباعة/التحميل
    
    U->>UI: طلب معاينة قبل الطباعة
    UI->>API: GET /api/invoices/compact-pdf/[id]?preview=true
    API->>PDF: إنشاء معاينة مدمجة
    PDF-->>API: صورة معاينة
    API-->>UI: معاينة الفاتورة
    UI-->>U: عرض المعاينة
```

## تصميم الفاتورة المدمجة

### الأبعاد والقياسات
```
العرض: 80mm (حجم إيصال حراري)
الطول: متغير حسب المحتوى (120-200mm)
الهوامش: 2mm من كل جانب
حجم الخط الأساسي: 8pt
حجم خط العنوان: 10pt
حجم خط التفاصيل: 7pt
```

### هيكل الفاتورة المدمجة

```mermaid
graph TD
    Invoice[الفاتورة المدمجة] --> Header[رأس الفاتورة]
    Invoice --> StudentInfo[معلومات الطالب]
    Invoice --> InvoiceDetails[تفاصيل الفاتورة]
    Invoice --> PaymentInfo[معلومات الدفع]
    Invoice --> Footer[تذييل الفاتورة]
    
    Header --> Logo[شعار صغير]
    Header --> SchoolName[اسم المؤسسة]
    Header --> InvoiceNumber[رقم الفاتورة]
    Header --> Date[التاريخ]
    
    StudentInfo --> StudentName[اسم الطالب]
    StudentInfo --> ParentName[اسم الولي]
    StudentInfo --> Grade[الصف]
    
    InvoiceDetails --> Amount[المبلغ]
    InvoiceDetails --> Period[الفترة]
    InvoiceDetails --> Description[الوصف]
    InvoiceDetails --> DueDate[تاريخ الاستحقاق]
    
    PaymentInfo --> PaidAmount[المبلغ المدفوع]
    PaymentInfo --> RemainingAmount[المبلغ المتبقي]
    PaymentInfo --> PaymentMethod[طريقة الدفع]
    
    Footer --> QRCode[رمز QR]
    Footer --> ContactInfo[معلومات الاتصال]
    Footer --> Signature[التوقيع]
```

## خيارات الطباعة المدمجة

### أحجام الفواتير المتاحة
1. **حجم الإيصال الحراري** (80mm × متغير)
   - مناسب للطابعات الحرارية
   - استهلاك ورق أقل
   - سرعة طباعة عالية

2. **حجم نصف A4** (105mm × 148mm)
   - مناسب للطابعات العادية
   - يمكن طباعة 4 فواتير في صفحة واحدة
   - توازن بين الحجم والوضوح

3. **حجم بطاقة العمل** (85mm × 55mm)
   - فاتورة مصغرة جداً
   - للمعلومات الأساسية فقط
   - سهولة الحمل والحفظ

### خيارات التخطيط
```typescript
interface CompactInvoiceOptions {
  size: 'thermal' | 'half-a4' | 'business-card';
  includeQR: boolean;
  includeLogo: boolean;
  fontSize: 'small' | 'medium' | 'large';
  layout: 'vertical' | 'horizontal';
  paperSaving: boolean;
  highQuality: boolean;
}
```

## تحسينات الطباعة

### ضغط المحتوى
- استخدام اختصارات للنصوص الطويلة
- رموز بدلاً من النصوص حيث أمكن
- تجميع المعلومات المترابطة
- إزالة المساحات الفارغة غير الضرورية

### تحسين الخطوط
- استخدام خطوط مدمجة وواضحة
- تحسين حجم الخط للقراءة
- استخدام الخط العريض للمعلومات المهمة
- تباين جيد بين النص والخلفية

### توفير الحبر والورق
- استخدام الحد الأدنى من الألوان
- تجنب الخلفيات الداكنة
- تحسين كثافة الطباعة
- إمكانية الطباعة بالأبيض والأسود

## مثال على التخطيط المدمج

```
┌─────────────────────────────────┐
│        🏫 مدرسة القرآن         │
│     فاتورة رقم: INV-2024-001   │
│     التاريخ: 15/01/2024        │
├─────────────────────────────────┤
│ الطالب: أحمد محمد علي           │
│ الولي: محمد علي                │
│ الصف: الخامس الابتدائي          │
├─────────────────────────────────┤
│ الفترة: يناير 2024             │
│ المبلغ: 5,000.00 دج            │
│ الاستحقاق: 31/01/2024          │
├─────────────────────────────────┤
│ المدفوع: 3,000.00 دج           │
│ المتبقي: 2,000.00 دج           │
│ الطريقة: نقداً                 │
├─────────────────────────────────┤
│ [QR Code]  📞 0123456789        │
│            📧 <EMAIL>    │
└─────────────────────────────────┘
```

## المتطلبات التقنية

### مكتبات PDF
- **jsPDF**: لإنشاء PDF بسيط وسريع
- **Puppeteer**: لتحويل HTML إلى PDF عالي الجودة
- **PDFKit**: للتحكم الدقيق في التخطيط

### تحسينات الأداء
- تخزين مؤقت للقوالب
- ضغط الصور والشعارات
- تحسين حجم الملف النهائي
- دعم الطباعة المتعددة

### دعم اللغة العربية
- خطوط عربية مدمجة
- اتجاه النص من اليمين لليسار
- تنسيق التواريخ بالعربية
- دعم الأرقام العربية والإنجليزية

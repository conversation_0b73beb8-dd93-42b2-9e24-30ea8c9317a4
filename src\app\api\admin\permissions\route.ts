import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// GET: جلب جميع الصلاحيات
export async function GET(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.view');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // بناء شروط البحث
    const where: { isActive: boolean; category?: string } = {
      isActive: true
    };

    if (category && category !== 'all') {
      where.category = category;
    }

    // جلب الصلاحيات
    const permissions = await prisma.permission.findMany({
      where,
      orderBy: [
        { category: 'asc' },
        { name: 'asc' }
      ]
    });

    // جلب الفئات المتاحة
    const categories = await prisma.permission.groupBy({
      by: ['category'],
      where: { isActive: true },
      _count: {
        category: true
      }
    });

    return NextResponse.json({
      permissions,
      categories: categories.map(c => ({
        name: c.category,
        count: c._count.category
      })),
      message: "تم جلب الصلاحيات بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching permissions:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب الصلاحيات" },
      { status: 500 }
    );
  }
}

// POST: إنشاء صلاحية جديدة
export async function POST(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.create');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const body = await request.json();
    const { key, name, description, category, route } = body;

    // التحقق من البيانات المطلوبة
    if (!key || !name || !category) {
      return NextResponse.json(
        { message: "مفتاح الصلاحية والاسم والفئة مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود صلاحية بنفس المفتاح
    const existingPermission = await prisma.permission.findUnique({
      where: { key }
    });

    if (existingPermission) {
      return NextResponse.json(
        { message: "توجد صلاحية بهذا المفتاح مسبقاً" },
        { status: 400 }
      );
    }

    // إنشاء الصلاحية الجديدة
    const newPermission = await prisma.permission.create({
      data: {
        key,
        name,
        description,
        category,
        route,
        isActive: true
      }
    });

    return NextResponse.json({
      permission: newPermission,
      message: "تم إنشاء الصلاحية بنجاح"
    }, { status: 201 });

  } catch (error: unknown) {
    console.error('Error creating permission:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء الصلاحية" },
      { status: 500 }
    );
  }
}

// PUT: تحديث صلاحية
export async function PUT(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.edit');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const body = await request.json();
    const { id, name, description, category, route, isActive } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !name || !category) {
      return NextResponse.json(
        { message: "معرف الصلاحية والاسم والفئة مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من وجود الصلاحية
    const existingPermission = await prisma.permission.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingPermission) {
      return NextResponse.json(
        { message: "الصلاحية غير موجودة" },
        { status: 404 }
      );
    }

    // تحديث الصلاحية
    const updatedPermission = await prisma.permission.update({
      where: { id: parseInt(id) },
      data: {
        name,
        description,
        category,
        route,
        isActive: isActive !== undefined ? isActive : existingPermission.isActive
      }
    });

    return NextResponse.json({
      permission: updatedPermission,
      message: "تم تحديث الصلاحية بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error updating permission:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث الصلاحية" },
      { status: 500 }
    );
  }
}

// DELETE: حذف صلاحية
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.delete');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: "معرف الصلاحية مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الصلاحية
    const existingPermission = await prisma.permission.findUnique({
      where: { id: parseInt(id) },
      include: {
        roles: true
      }
    });

    if (!existingPermission) {
      return NextResponse.json(
        { message: "الصلاحية غير موجودة" },
        { status: 404 }
      );
    }

    // منع حذف الصلاحية إذا كانت مستخدمة
    if (existingPermission.roles.length > 0) {
      return NextResponse.json(
        { message: `لا يمكن حذف الصلاحية لأنها مستخدمة في ${existingPermission.roles.length} دور` },
        { status: 400 }
      );
    }

    // حذف الصلاحية
    await prisma.permission.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      message: "تم حذف الصلاحية بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error deleting permission:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف الصلاحية" },
      { status: 500 }
    );
  }
}

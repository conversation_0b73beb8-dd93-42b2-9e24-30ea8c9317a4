@startuml Quran Progress Tracking Sequence Diagram

actor Teacher
actor Student
actor Parent
participant "Web Interface" as UI
participant "API" as API
participant "Database" as DB
participant "Notification Service" as Notify

box "Authentication" #LightBlue
  Teacher -> UI: Login
  UI -> API: Send credentials
  API -> DB: Verify credentials
  DB --> API: Return user data
  API --> UI: Authentication response
  UI --> Teacher: Display dashboard
end box

box "Student Selection" #LightYellow
  Teacher -> UI: Navigate to Quran progress page
  UI -> API: Request student list
  API -> DB: Query students taught by teacher
  DB --> API: Return student data
  API --> UI: Display student list
  UI --> Teacher: Show student selection

  Teacher -> UI: Select student
  UI -> API: Request student's Quran progress
  API -> DB: Query progress records
  DB --> API: Return progress data
  API --> UI: Display progress history
  UI --> Teacher: Show progress form
end box

box "Progress Evaluation" #LightGreen
  Teacher -> UI: Select surah and ayahs
  UI -> API: Request surah details
  API -> DB: Query surah information
  DB --> API: Return surah data
  API --> UI: Display ayah selection

  Teacher -> UI: Enter evaluation details
  note right: Includes memorization quality, tajweed assessment, and notes

  alt Excellent Performance
    Teacher -> UI: Mark as excellent
    Teacher -> UI: Add bonus points
  else Needs Improvement
    Teacher -> UI: Mark areas for improvement
    Teacher -> UI: Schedule review session
  end

  Teacher -> UI: Submit progress update
  UI -> API: Send progress data
  API -> DB: Save progress record
  DB --> API: Confirm save

  API -> Notify: Send progress notification
  Notify -> DB: Get student and parent info
  DB --> Notify: Return contact information
  Notify --> API: Confirm notification sent

  API --> UI: Display confirmation
  UI --> Teacher: Show success message
end box

box "Progress Tracking" #LightPink
  Student -> UI: Login to student portal
  UI -> API: Request personal progress
  API -> DB: Query student's progress
  DB --> API: Return progress data
  API --> UI: Display progress dashboard
  UI --> Student: Show memorization status

  Parent -> UI: Login to parent portal
  UI -> API: Request child's progress
  API -> DB: Query child's progress
  DB --> API: Return progress data
  API --> UI: Display child's progress
  UI --> Parent: Show memorization status
end box

@enduml

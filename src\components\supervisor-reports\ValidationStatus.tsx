'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, AlertTriangle, XCircle, Eye, EyeOff } from 'lucide-react';
import { validateReport, formatValidationMessage, getValidationSummary, type ReportValidationResult } from '@/utils/reportValidation';

interface ValidationStatusProps {
  reportData: {
    title: string;
    description?: string;
    periodStart: Date;
    periodEnd: Date;
    literaryContent: string;
    financialData: any[];
  };
  className?: string;
}

export default function ValidationStatus({ reportData, className }: ValidationStatusProps) {
  const [validationResult, setValidationResult] = useState<ReportValidationResult | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // تشغيل التحقق عند تغيير البيانات
  useEffect(() => {
    const validateData = async () => {
      setIsValidating(true);
      try {
        // إضافة تأخير بسيط لتجنب التحقق المستمر أثناء الكتابة
        await new Promise(resolve => setTimeout(resolve, 500));
        const result = validateReport(reportData);
        setValidationResult(result);
      } catch (error) {
        console.error('خطأ في التحقق من البيانات:', error);
      } finally {
        setIsValidating(false);
      }
    };

    validateData();
  }, [reportData]);

  if (!validationResult) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span>جاري التحقق من البيانات...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = () => {
    if (validationResult.errors.length > 0) {
      return <XCircle className="h-5 w-5 text-red-600" />;
    } else if (validationResult.warnings.length > 0) {
      return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    } else {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    }
  };

  const getStatusColor = () => {
    if (validationResult.errors.length > 0) {
      return 'destructive';
    } else if (validationResult.warnings.length > 0) {
      return 'secondary';
    } else {
      return 'default';
    }
  };

  const getStatusText = () => {
    if (validationResult.errors.length > 0) {
      return 'يحتوي على أخطاء';
    } else if (validationResult.warnings.length > 0) {
      return 'يحتوي على تحذيرات';
    } else {
      return 'صحيح ومكتمل';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          حالة التحقق من البيانات
          {isValidating && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          )}
        </CardTitle>
        <CardDescription>
          التحقق التلقائي من صحة واكتمال بيانات التقرير
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* الحالة العامة */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant={getStatusColor()}>
              {getStatusText()}
            </Badge>
            <span className="text-sm text-gray-600">
              {getValidationSummary(validationResult)}
            </span>
          </div>
          
          {(validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="flex items-center gap-2"
            >
              {showDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
            </Button>
          )}
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-1">
            <div className="text-2xl font-bold text-red-600">
              {validationResult.errors.length}
            </div>
            <div className="text-xs text-gray-500">أخطاء</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-yellow-600">
              {validationResult.warnings.length}
            </div>
            <div className="text-xs text-gray-500">تحذيرات</div>
          </div>
          <div className="space-y-1">
            <div className="text-2xl font-bold text-green-600">
              {validationResult.isValid ? '✓' : '✗'}
            </div>
            <div className="text-xs text-gray-500">صالح للحفظ</div>
          </div>
        </div>

        {/* تفاصيل الأخطاء والتحذيرات */}
        {showDetails && (validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (
          <>
            <Separator />
            <div className="space-y-3">
              {/* الأخطاء */}
              {validationResult.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-red-700 flex items-center gap-2">
                    <XCircle className="h-4 w-4" />
                    الأخطاء ({validationResult.errors.length})
                  </h4>
                  <div className="space-y-1">
                    {validationResult.errors.map((error, index) => (
                      <div
                        key={index}
                        className="text-sm p-2 bg-red-50 border border-red-200 rounded text-red-700"
                      >
                        {formatValidationMessage(error)}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* التحذيرات */}
              {validationResult.warnings.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-yellow-700 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    التحذيرات ({validationResult.warnings.length})
                  </h4>
                  <div className="space-y-1">
                    {validationResult.warnings.map((warning, index) => (
                      <div
                        key={index}
                        className="text-sm p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700"
                      >
                        {formatValidationMessage(warning)}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </>
        )}

        {/* رسالة النجاح */}
        {validationResult.isValid && validationResult.warnings.length === 0 && (
          <div className="text-center p-4 bg-green-50 border border-green-200 rounded">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-green-700 font-medium">
              ممتاز! التقرير صحيح ومكتمل وجاهز للحفظ والنشر
            </p>
          </div>
        )}

        {/* نصائح للتحسين */}
        {(validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (
          <div className="text-xs text-gray-500 space-y-1">
            <p>💡 نصائح:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>الأخطاء يجب إصلاحها قبل حفظ التقرير</li>
              <li>التحذيرات اختيارية ولكن يُنصح بمراجعتها</li>
              <li>يتم التحقق تلقائياً عند تغيير البيانات</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

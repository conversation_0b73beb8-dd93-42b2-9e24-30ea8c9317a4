'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'react-toastify';
import { FaEnvelope, FaLink, FaCopy, FaCalendarAlt } from 'react-icons/fa';

interface ShareReportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  reportId: string;
  reportType: string;
  reportTitle: string;
}

/**
 * نافذة مشاركة التقرير
 * تتيح للمستخدم مشاركة التقرير عبر البريد الإلكتروني أو رابط مؤقت
 */
const ShareReportDialog: React.FC<ShareReportDialogProps> = ({
  isOpen,
  onClose,
  reportId,
  reportType,
  reportTitle
}) => {
  const [activeTab, setActiveTab] = useState('email');
  const [isLoading, setIsLoading] = useState(false);
  const [shareLink, setShareLink] = useState('');
  
  // بيانات مشاركة البريد الإلكتروني
  const [emailData, setEmailData] = useState({
    recipients: '',
    subject: `مشاركة تقرير: ${reportTitle}`,
    message: `مرحباً،\n\nأرغب في مشاركة التقرير التالي معك: ${reportTitle}.\n\nمع التحية،`,
    includeAttachment: true
  });
  
  // بيانات مشاركة الرابط
  const [linkData, setLinkData] = useState({
    expiryDays: 7,
    requirePassword: false,
    password: ''
  });
  
  // بيانات جدولة التقرير
  const [scheduleData, setScheduleData] = useState({
    frequency: 'weekly',
    recipients: '',
    startDate: new Date().toISOString().split('T')[0],
    time: '08:00',
    dayOfWeek: '1', // الاثنين
    dayOfMonth: '1',
    format: 'pdf'
  });

  // إنشاء رابط مشاركة
  const handleCreateShareLink = async () => {
    try {
      setIsLoading(true);
      
      // هنا يتم إرسال طلب إلى الخادم لإنشاء رابط مشاركة
      const response = await fetch('/api/reports/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reportId,
          reportType,
          expiryDays: linkData.expiryDays,
          requirePassword: linkData.requirePassword,
          password: linkData.requirePassword ? linkData.password : undefined
        })
      });
      
      if (!response.ok) {
        throw new Error('فشل في إنشاء رابط المشاركة');
      }
      
      const data = await response.json();
      setShareLink(data.shareLink);
      toast.success('تم إنشاء رابط المشاركة بنجاح');
    } catch (error) {
      console.error('Error creating share link:', error);
      toast.error('حدث خطأ أثناء إنشاء رابط المشاركة');
    } finally {
      setIsLoading(false);
    }
  };
  
  // نسخ رابط المشاركة
  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareLink);
    toast.success('تم نسخ الرابط إلى الحافظة');
  };
  
  // إرسال التقرير بالبريد الإلكتروني
  const handleSendEmail = async () => {
    try {
      setIsLoading(true);
      
      // التحقق من صحة عناوين البريد الإلكتروني
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const emails = emailData.recipients.split(',').map(email => email.trim());
      
      for (const email of emails) {
        if (!emailRegex.test(email)) {
          toast.error(`عنوان البريد الإلكتروني غير صالح: ${email}`);
          return;
        }
      }
      
      // هنا يتم إرسال طلب إلى الخادم لإرسال التقرير بالبريد الإلكتروني
      const response = await fetch('/api/reports/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reportId,
          reportType,
          recipients: emails,
          subject: emailData.subject,
          message: emailData.message,
          includeAttachment: emailData.includeAttachment
        })
      });
      
      if (!response.ok) {
        throw new Error('فشل في إرسال البريد الإلكتروني');
      }
      
      toast.success('تم إرسال التقرير بالبريد الإلكتروني بنجاح');
      onClose();
    } catch (error) {
      console.error('Error sending email:', error);
      toast.error('حدث خطأ أثناء إرسال البريد الإلكتروني');
    } finally {
      setIsLoading(false);
    }
  };
  
  // جدولة إرسال التقرير
  const handleScheduleReport = async () => {
    try {
      setIsLoading(true);
      
      // التحقق من صحة عناوين البريد الإلكتروني
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const emails = scheduleData.recipients.split(',').map(email => email.trim());
      
      for (const email of emails) {
        if (!emailRegex.test(email)) {
          toast.error(`عنوان البريد الإلكتروني غير صالح: ${email}`);
          return;
        }
      }
      
      // هنا يتم إرسال طلب إلى الخادم لجدولة إرسال التقرير
      const response = await fetch('/api/reports/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reportId,
          reportType,
          recipients: emails,
          frequency: scheduleData.frequency,
          startDate: scheduleData.startDate,
          time: scheduleData.time,
          dayOfWeek: scheduleData.dayOfWeek,
          dayOfMonth: scheduleData.dayOfMonth,
          format: scheduleData.format
        })
      });
      
      if (!response.ok) {
        throw new Error('فشل في جدولة إرسال التقرير');
      }
      
      toast.success('تم جدولة إرسال التقرير بنجاح');
      onClose();
    } catch (error) {
      console.error('Error scheduling report:', error);
      toast.error('حدث خطأ أثناء جدولة إرسال التقرير');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>مشاركة التقرير</DialogTitle>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="email" className="flex items-center gap-1">
              <FaEnvelope size={14} />
              <span>بريد إلكتروني</span>
            </TabsTrigger>
            <TabsTrigger value="link" className="flex items-center gap-1">
              <FaLink size={14} />
              <span>رابط مشاركة</span>
            </TabsTrigger>
            <TabsTrigger value="schedule" className="flex items-center gap-1">
              <FaCalendarAlt size={14} />
              <span>جدولة</span>
            </TabsTrigger>
          </TabsList>
          
          {/* محتوى تبويب البريد الإلكتروني */}
          <TabsContent value="email" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="recipients">المستلمون (مفصولين بفواصل)</Label>
              <Input
                id="recipients"
                value={emailData.recipients}
                onChange={(e) => setEmailData({ ...emailData, recipients: e.target.value })}
                placeholder="<EMAIL>, <EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="subject">الموضوع</Label>
              <Input
                id="subject"
                value={emailData.subject}
                onChange={(e) => setEmailData({ ...emailData, subject: e.target.value })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="message">الرسالة</Label>
              <textarea
                id="message"
                value={emailData.message}
                onChange={(e) => setEmailData({ ...emailData, message: e.target.value })}
                className="w-full min-h-[100px] p-2 border border-gray-300 rounded-md"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="includeAttachment"
                checked={emailData.includeAttachment}
                onCheckedChange={(checked) => 
                  setEmailData({ ...emailData, includeAttachment: checked as boolean })
                }
              />
              <Label htmlFor="includeAttachment" className="mr-2">إرفاق التقرير كملف PDF</Label>
            </div>
            
            <Button
              onClick={handleSendEmail}
              className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              disabled={isLoading || !emailData.recipients}
            >
              {isLoading ? 'جاري الإرسال...' : 'إرسال البريد الإلكتروني'}
            </Button>
          </TabsContent>
          
          {/* محتوى تبويب رابط المشاركة */}
          <TabsContent value="link" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="expiryDays">مدة صلاحية الرابط (بالأيام)</Label>
              <Input
                id="expiryDays"
                type="number"
                min="1"
                max="30"
                value={linkData.expiryDays}
                onChange={(e) => setLinkData({ ...linkData, expiryDays: parseInt(e.target.value) })}
              />
            </div>
            
            <div className="flex items-center space-x-2 mb-4">
              <Checkbox
                id="requirePassword"
                checked={linkData.requirePassword}
                onCheckedChange={(checked) => 
                  setLinkData({ ...linkData, requirePassword: checked as boolean })
                }
              />
              <Label htmlFor="requirePassword" className="mr-2">حماية بكلمة مرور</Label>
            </div>
            
            {linkData.requirePassword && (
              <div className="space-y-2">
                <Label htmlFor="password">كلمة المرور</Label>
                <Input
                  id="password"
                  type="password"
                  value={linkData.password}
                  onChange={(e) => setLinkData({ ...linkData, password: e.target.value })}
                />
              </div>
            )}
            
            <Button
              onClick={handleCreateShareLink}
              className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              disabled={isLoading}
            >
              {isLoading ? 'جاري الإنشاء...' : 'إنشاء رابط المشاركة'}
            </Button>
            
            {shareLink && (
              <div className="mt-4 p-2 bg-gray-100 rounded-md">
                <div className="flex items-center">
                  <Input value={shareLink} readOnly className="flex-1" />
                  <Button
                    onClick={handleCopyLink}
                    variant="ghost"
                    className="ml-2"
                    title="نسخ الرابط"
                  >
                    <FaCopy />
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  ينتهي هذا الرابط بعد {linkData.expiryDays} يوم
                  {linkData.requirePassword ? ' ومحمي بكلمة مرور' : ''}
                </p>
              </div>
            )}
          </TabsContent>
          
          {/* محتوى تبويب الجدولة */}
          <TabsContent value="schedule" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="scheduleRecipients">المستلمون (مفصولين بفواصل)</Label>
              <Input
                id="scheduleRecipients"
                value={scheduleData.recipients}
                onChange={(e) => setScheduleData({ ...scheduleData, recipients: e.target.value })}
                placeholder="<EMAIL>, <EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="frequency">تكرار الإرسال</Label>
              <select
                id="frequency"
                value={scheduleData.frequency}
                onChange={(e) => setScheduleData({ ...scheduleData, frequency: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="daily">يومي</option>
                <option value="weekly">أسبوعي</option>
                <option value="monthly">شهري</option>
              </select>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">تاريخ البدء</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={scheduleData.startDate}
                  onChange={(e) => setScheduleData({ ...scheduleData, startDate: e.target.value })}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="time">وقت الإرسال</Label>
                <Input
                  id="time"
                  type="time"
                  value={scheduleData.time}
                  onChange={(e) => setScheduleData({ ...scheduleData, time: e.target.value })}
                />
              </div>
            </div>
            
            {scheduleData.frequency === 'weekly' && (
              <div className="space-y-2">
                <Label htmlFor="dayOfWeek">يوم الأسبوع</Label>
                <select
                  id="dayOfWeek"
                  value={scheduleData.dayOfWeek}
                  onChange={(e) => setScheduleData({ ...scheduleData, dayOfWeek: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="0">الأحد</option>
                  <option value="1">الاثنين</option>
                  <option value="2">الثلاثاء</option>
                  <option value="3">الأربعاء</option>
                  <option value="4">الخميس</option>
                  <option value="5">الجمعة</option>
                  <option value="6">السبت</option>
                </select>
              </div>
            )}
            
            {scheduleData.frequency === 'monthly' && (
              <div className="space-y-2">
                <Label htmlFor="dayOfMonth">يوم الشهر</Label>
                <Input
                  id="dayOfMonth"
                  type="number"
                  min="1"
                  max="31"
                  value={scheduleData.dayOfMonth}
                  onChange={(e) => setScheduleData({ ...scheduleData, dayOfMonth: e.target.value })}
                />
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="format">صيغة التقرير</Label>
              <select
                id="format"
                value={scheduleData.format}
                onChange={(e) => setScheduleData({ ...scheduleData, format: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
              </select>
            </div>
            
            <Button
              onClick={handleScheduleReport}
              className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              disabled={isLoading || !scheduleData.recipients}
            >
              {isLoading ? 'جاري الجدولة...' : 'جدولة إرسال التقرير'}
            </Button>
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            إلغاء
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ShareReportDialog;

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import { checkPermission } from '@/lib/permissions';

// PUT: تحديث دور محدد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به: يرجى تسجيل الدخول" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData) {
      return NextResponse.json(
        { message: "غير مصرح به: بيانات المستخدم غير صالحة" },
        { status: 403 }
      );
    }

    // التحقق من صلاحية تحديث الأدوار
    const hasPermission = await checkPermission(userData.id, 'admin.roles.edit');
    if (!hasPermission) {
      return NextResponse.json(
        { message: "غير مصرح به: ليس لديك صلاحية تحديث الأدوار" },
        { status: 403 }
      );
    }

    const roleId = parseInt(params.id);
    if (isNaN(roleId)) {
      return NextResponse.json(
        { message: "معرف الدور غير صالح" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, displayName, description } = body;

    // التحقق من البيانات المطلوبة
    if (!displayName) {
      return NextResponse.json(
        { message: "الاسم المعروض مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدور
    const existingRole = await prisma.role.findUnique({
      where: { id: roleId }
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // منع تعديل أدوار النظام
    if (existingRole.isSystem) {
      return NextResponse.json(
        { message: "لا يمكن تعديل أدوار النظام" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود دور آخر بنفس الاسم (إذا تم تغيير الاسم)
    if (name && name !== existingRole.name) {
      const duplicateRole = await prisma.role.findUnique({
        where: { name }
      });

      if (duplicateRole) {
        return NextResponse.json(
          { message: "يوجد دور بهذا الاسم مسبقاً" },
          { status: 400 }
        );
      }
    }

    // تحديث الدور
    const updatedRole = await prisma.role.update({
      where: { id: roleId },
      data: {
        ...(name && { name }),
        displayName,
        description
      },
      include: {
        _count: {
          select: {
            users: true
          }
        }
      }
    });

    return NextResponse.json({
      role: updatedRole,
      message: "تم تحديث الدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error updating role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث الدور" },
      { status: 500 }
    );
  }
}

// DELETE: حذف دور محدد
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به: يرجى تسجيل الدخول" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData) {
      return NextResponse.json(
        { message: "غير مصرح به: بيانات المستخدم غير صالحة" },
        { status: 403 }
      );
    }

    // التحقق من صلاحية حذف الأدوار
    const hasPermission = await checkPermission(userData.id, 'admin.roles.delete');
    if (!hasPermission) {
      return NextResponse.json(
        { message: "غير مصرح به: ليس لديك صلاحية حذف الأدوار" },
        { status: 403 }
      );
    }

    const roleId = parseInt(params.id);
    if (isNaN(roleId)) {
      return NextResponse.json(
        { message: "معرف الدور غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدور
    const existingRole = await prisma.role.findUnique({
      where: { id: roleId },
      include: {
        _count: {
          select: {
            users: true
          }
        }
      }
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // منع حذف أدوار النظام
    if (existingRole.isSystem) {
      return NextResponse.json(
        { message: "لا يمكن حذف أدوار النظام" },
        { status: 400 }
      );
    }

    // منع حذف الدور إذا كان مستخدماً
    if (existingRole._count.users > 0) {
      return NextResponse.json(
        { message: `لا يمكن حذف الدور لأنه مستخدم من قبل ${existingRole._count.users} مستخدم` },
        { status: 400 }
      );
    }

    // حذف صلاحيات الدور أولاً
    await prisma.rolePermission.deleteMany({
      where: { roleId }
    });

    // حذف الدور
    await prisma.role.delete({
      where: { id: roleId }
    });

    return NextResponse.json({
      message: "تم حذف الدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error deleting role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف الدور" },
      { status: 500 }
    );
  }
}

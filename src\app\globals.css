@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import '../styles/responsive-tables.css';

:root {
 --bg-color: #efefef;

 /* متغيرات الألوان الأساسية - ستتم إعادة تعيينها من قاعدة البيانات */
 --primary-color: transparent;
 --secondary-color: transparent;
 --sidebar-color: transparent;
 --background-color: transparent;
 --accent-color: transparent;
 --text-color: transparent;

 /* ألوان النصوص المتباينة - ستتم إعادة تعيينها */
 --primary-text-color: transparent;
 --secondary-text-color: transparent;
 --sidebar-text-color: transparent;

 /* الألوان الخفيفة والداكنة - ستتم إعادة تعيينها */
 --primary-light: transparent;
 --primary-lighter: transparent;
 --primary-dark: transparent;
 --secondary-light: transparent;
 --secondary-lighter: transparent;
 --secondary-dark: transparent;

 /* ألوان الحدود - ستتم إعادة تعيينها */
 --primary-border: transparent;
 --secondary-border: transparent;
}

body {
 background-color: var(--background-color, #f3f4f6);
}

*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* تطبيق الألوان على جميع العناصر في الموقع */
body {
  color: var(--text-color);
  background-color: var(--background-color);
}

/* تطبيق الألوان على جميع الروابط */
a {
  color: var(--primary-color);
}

a:hover {
  color: var(--secondary-color);
}

/* تطبيق الألوان على جميع الأزرار */
button {
  transition: all 0.3s ease;
}

/* تطبيق الألوان على جميع المدخلات */
input:focus,
select:focus,
textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 22, 155, 136), 0.2);
}

/* تطبيق الألوان على جميع العناصر التفاعلية */
.interactive-element {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.interactive-element:hover {
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* تطبيق الألوان على جميع البطاقات */
.card {
  border-color: var(--border-primary);
  background-color: var(--bg-primary);
}

/* تطبيق الألوان على جميع الجداول */
table {
  border-color: var(--border-secondary);
}

/* إزالة التطبيق العام للألوان على جميع عناصر th - سيتم تطبيق الألوان بشكل محدد في كل صفحة */

/* تطبيق الألوان على جميع التنبيهات */
.alert {
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 0.5rem 0;
}

.alert-primary {
  background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1);
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
}

/* تطبيق الألوان على جميع الأيقونات */
.icon {
  color: var(--primary-color);
}

/* تطبيق الألوان على جميع العناصر المخصصة */
.primary-bg {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.secondary-bg {
  background-color: var(--secondary-color) !important;
  color: white !important;
}

.primary-text {
  color: var(--primary-color) !important;
}

.secondary-text {
  color: var(--secondary-color) !important;
}

.primary-border {
  border-color: var(--primary-color) !important;
}

.secondary-border {
  border-color: var(--secondary-color) !important;
}

/* إخفاء الألوان الخضراء الثابتة فوراً لتجنب الوميض - مع استثناء الجداول والعناصر التي تحتاج تباين */
*[class*="bg-green"]:not(table):not(thead):not(tbody):not(tr):not(td):not(th):not([class*="text-green"]),
*[class*="bg-emerald"]:not(table):not(thead):not(tbody):not(tr):not(td):not(th):not([class*="text-emerald"]),
*[class*="bg-teal"]:not(table):not(thead):not(tbody):not(tr):not(td):not(th):not([class*="text-teal"]) {
  background-color: var(--primary-color) !important;
}

/* تطبيق لون النص الأبيض للعناصر التي لها خلفية خضراء ونص أخضر لضمان التباين */
*[class*="bg-green"][class*="text-green"],
*[class*="bg-emerald"][class*="text-emerald"],
*[class*="bg-teal"][class*="text-teal"] {
  background-color: var(--primary-color) !important;
  color: white !important;
}

*[class*="border-green"]:not(table):not(thead):not(tbody):not(tr):not(td):not(th),
*[class*="border-emerald"]:not(table):not(thead):not(tbody):not(tr):not(td):not(th),
*[class*="border-teal"]:not(table):not(thead):not(tbody):not(tr):not(td):not(th) {
  border-color: var(--primary-color) !important;
}

/* قواعد إضافية لضمان الرؤية */
.bg-green-100.text-green-800,
.bg-green-200.text-green-800,
.bg-emerald-100.text-emerald-800,
.bg-teal-100.text-teal-800 {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* إصلاح الألوان المباشرة */
*[style*="#169b88"] {
  color: var(--primary-color) !important;
}

*[style*="background-color: #169b88"],
*[style*="background: #169b88"] {
  background-color: var(--primary-color) !important;
}

*[style*="#1ab19c"] {
  color: var(--secondary-color) !important;
}

*[style*="background-color: #1ab19c"],
*[style*="background: #1ab19c"] {
  background-color: var(--secondary-color) !important;
}

/* تحسين تباين الخطوط التلقائي */
.text-contrast-auto {
  color: var(--background-contrast) !important;
}

.bg-primary-color,
.bg-primary-color * {
  background-color: var(--primary-color) !important;
  color: var(--primary-contrast) !important;
}

.bg-secondary-color,
.bg-secondary-color * {
  background-color: var(--secondary-color) !important;
  color: var(--secondary-contrast) !important;
}

.bg-sidebar-color,
.bg-sidebar-color * {
  background-color: var(--sidebar-color) !important;
  color: var(--sidebar-contrast) !important;
}

/* تطبيق التباين على العناصر التفاعلية */
.btn-primary {
  background-color: var(--primary-color) !important;
  color: var(--primary-contrast) !important;
  border-color: var(--primary-color) !important;
}

.btn-secondary {
  background-color: var(--secondary-color) !important;
  color: var(--secondary-contrast) !important;
  border-color: var(--secondary-color) !important;
}

/* تطبيق التباين على الجداول */
.table-primary thead {
  background-color: var(--primary-color) !important;
  color: var(--primary-contrast) !important;
}

.table-secondary thead {
  background-color: var(--secondary-color) !important;
  color: var(--secondary-contrast) !important;
}

/* تطبيق التباين على البطاقات */
.card-primary .card-header {
  background-color: var(--primary-color) !important;
  color: var(--primary-contrast) !important;
}

.card-secondary .card-header {
  background-color: var(--secondary-color) !important;
  color: var(--secondary-contrast) !important;
}

/* تطبيق التباين على التنبيهات */
.alert-primary {
  background-color: rgba(var(--primary-color-rgb), 0.1) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

/* تطبيق التباين على الشريط الجانبي */
.sidebar,
.sidebar *,
nav[class*="sidebar"],
nav[class*="sidebar"] * {
  background-color: var(--sidebar-color) !important;
  color: var(--sidebar-contrast) !important;
}

/* تطبيق التباين على الهيدر */
.header,
.header *,
header,
header * {
  color: var(--primary-contrast) !important;
}

/* تطبيق التباين على الفوتر */
.footer,
.footer *,
footer,
footer * {
  background-color: var(--sidebar-color) !important;
  color: var(--sidebar-contrast) !important;
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #169b88 #f1f1f1;
}

/* Accordion animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

/* إصلاح شامل لألوان Labels في النوافذ المنبثقة */
[data-radix-dialog-content] label,
[data-radix-alert-dialog-content] label,
.dialog-content label,
.modal-content label {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
}

/* إصلاح محدد لمكون Label من Radix UI في النوافذ المنبثقة */
[data-radix-dialog-content] [data-radix-label-root],
[data-radix-alert-dialog-content] [data-radix-label-root],
.dialog-content [data-radix-label-root],
.modal-content [data-radix-label-root] {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

/* إصلاح الكلاسات الافتراضية للـ Label في النوافذ المنبثقة */
[data-radix-dialog-content] .text-gray-900,
[data-radix-alert-dialog-content] .text-gray-900,
.dialog-content .text-gray-900,
.modal-content .text-gray-900 {
  color: #1f2937 !important;
}

/* أنماط الطباعة للتقارير */
@media print {
  body {
    font-family: 'Arial', sans-serif;
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
    background: white;
  }

  .print\\:hidden {
    display: none !important;
  }

  .print\\:block {
    display: block !important;
  }

  .print\\:shadow-none {
    box-shadow: none !important;
  }

  .print\\:border-0 {
    border: none !important;
  }

  /* تحسين الجداول للطباعة */
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
  }

  table th,
  table td {
    border: 1px solid #000;
    padding: 8px;
    text-align: center;
  }

  table th {
    background-color: #f0f0f0 !important;
    font-weight: bold;
  }

  /* تحسين العناوين للطباعة */
  h1, h2, h3 {
    page-break-after: avoid;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  /* منع تقسيم العناصر المهمة */
  .card,
  .section {
    page-break-inside: avoid;
    margin-bottom: 20px;
  }

  /* تحسين الهوامش */
  @page {
    margin: 2cm;
    size: A4;
  }

  /* إخفاء العناصر غير المرغوبة في الطباعة */
  button,
  .btn,
  nav,
  .navigation,
  .sidebar {
    display: none !important;
  }

  /* تحسين النصوص للطباعة */
  .prose {
    font-size: 11pt;
    line-height: 1.4;
  }

  .prose h2 {
    font-size: 14pt;
    font-weight: bold;
    margin-top: 15px;
    margin-bottom: 8px;
  }

  .prose h3 {
    font-size: 12pt;
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 6px;
  }

  .prose p {
    margin-bottom: 8px;
  }

  .prose ul,
  .prose ol {
    margin: 8px 0;
    padding-right: 20px;
  }

  .prose li {
    margin-bottom: 4px;
  }
}
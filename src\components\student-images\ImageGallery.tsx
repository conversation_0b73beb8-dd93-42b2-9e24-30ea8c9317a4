'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  ChevronLeft,
  ChevronRight,
  Download,
  Edit,
  Trash2,
  X,
  ZoomIn,
  Heart,
  Grid3X3
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface StudentImage {
  id: number;
  imageUrl: string;
  description: string | null;
  isProfilePic: boolean;
  uploadDate: string;
  albumId: number | null;
  studentId: number;
}

interface ImageGalleryProps {
  images: StudentImage[];
  onEdit?: (image: StudentImage) => void;
  onDelete?: (imageId: number) => void;
  onSetProfilePic?: (imageId: number) => void;
}

export default function ImageGallery({
  images,
  onEdit,
  onDelete,
  onSetProfilePic
}: ImageGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<StudentImage | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'carousel'>('grid');
  const [columns, setColumns] = useState(3);

  // Responsive columns
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setColumns(1);
      } else if (window.innerWidth < 1024) {
        setColumns(2);
      } else {
        setColumns(3);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Open image in fullscreen
  const openImage = (image: StudentImage, index: number) => {
    setSelectedImage(image);
    setCurrentIndex(index);
  };

  // Navigate through images
  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setSelectedImage(images[currentIndex - 1]);
    }
  }, [currentIndex, images]);

  const goToNext = useCallback(() => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setSelectedImage(images[currentIndex + 1]);
    }
  }, [currentIndex, images]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedImage) return;

      if (e.key === 'ArrowLeft') {
        goToNext(); // RTL layout, so left is next
      } else if (e.key === 'ArrowRight') {
        goToPrevious(); // RTL layout, so right is previous
      } else if (e.key === 'Escape') {
        setSelectedImage(null);
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedImage, currentIndex, images, goToNext, goToPrevious]);

  // Download image
  const downloadImage = (image: StudentImage) => {
    const link = document.createElement('a');
    link.href = image.imageUrl;
    link.download = `student-image-${image.id}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "تم التنزيل",
      description: "تم تنزيل الصورة بنجاح",
    });
  };

  // Handle edit - وظيفة تم إلغاؤها
  const handleEdit = (image: StudentImage) => {
    // تم إلغاء وظيفة تعديل الصور
    toast({
      title: "تنبيه",
      description: "تم إلغاء وظيفة تعديل الصور",
      variant: "default",
    });
    setSelectedImage(null);
  };

  // Handle delete
  const handleDelete = (imageId: number) => {
    if (onDelete) {
      onDelete(imageId);
      setSelectedImage(null);
    }
  };

  // Handle set as profile pic
  const handleSetProfilePic = (imageId: number) => {
    if (onSetProfilePic) {
      onSetProfilePic(imageId);

      toast({
        title: "تم التعيين",
        description: "تم تعيين الصورة كصورة الملف الشخصي",
      });
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Toggle view mode
  const toggleViewMode = () => {
    setViewMode(viewMode === 'grid' ? 'carousel' : 'grid');
  };

  // If no images
  if (images.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">لا توجد صور متاحة</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">معرض الصور ({images.length})</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={toggleViewMode}
        >
          {viewMode === 'grid' ? (
            <>
              <ChevronRight className="h-4 w-4 ml-2" />
              عرض كشرائح
            </>
          ) : (
            <>
              <Grid3X3 className="h-4 w-4 ml-2" />
              عرض كشبكة
            </>
          )}
        </Button>
      </div>

      {viewMode === 'grid' ? (
        <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-${columns} gap-4`}>
          {images.map((image, index) => (
            <Card key={image.id} className="overflow-hidden">
              <div
                className="relative h-48 cursor-pointer"
                onClick={() => openImage(image, index)}
              >
                <Image
                  src={image.imageUrl}
                  alt={image.description || 'صورة الطالب'}
                  className="w-full h-full object-cover"
                  width={300}
                  height={200}
                  style={{ objectFit: 'cover' }}
                />
                {image.isProfilePic && (
                  <div className="absolute top-2 right-2 bg-primary text-white px-2 py-1 rounded-md text-xs">
                    الصورة الشخصية
                  </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 hover:opacity-100">
                  <ZoomIn className="h-8 w-8 text-white" />
                </div>
              </div>
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(image.uploadDate)}
                    </p>
                    <p className="mt-1 line-clamp-2">{image.description || 'بدون وصف'}</p>
                  </div>
                  <div className="flex gap-1">

                    {onDelete && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDelete(image.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="relative h-96 bg-gray-100 rounded-lg overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <Image
              src={images[currentIndex].imageUrl}
              alt={images[currentIndex].description || 'صورة الطالب'}
              className="max-w-full max-h-full object-contain"
              width={800}
              height={600}
            />
          </div>

          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4">
            <p className="text-sm">{formatDate(images[currentIndex].uploadDate)}</p>
            <p>{images[currentIndex].description || 'بدون وصف'}</p>
          </div>

          <Button
            variant="outline"
            size="sm"
            className="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white p-2"
            onClick={goToPrevious}
            disabled={currentIndex === 0}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white p-2"
            onClick={goToNext}
            disabled={currentIndex === images.length - 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="absolute top-4 right-4 flex gap-2">

            <Button
              variant="outline"
              size="sm"
              className="bg-white"
              onClick={() => downloadImage(images[currentIndex])}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Fullscreen image dialog */}
      <Dialog open={!!selectedImage} onOpenChange={(open) => !open && setSelectedImage(null)}>
        <DialogContent className="max-w-5xl w-full h-[90vh] p-0 overflow-hidden">
          <DialogHeader className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 z-10 flex justify-between items-center">
            <DialogTitle>{selectedImage?.description || 'صورة الطالب'}</DialogTitle>
            <Button variant="ghost" size="sm" className="p-2" onClick={() => setSelectedImage(null)}>
              <X className="h-4 w-4 text-white" />
            </Button>
          </DialogHeader>

          {selectedImage && (
            <div className="relative h-full flex items-center justify-center bg-black">
              <Image
                src={selectedImage.imageUrl}
                alt={selectedImage.description || 'صورة الطالب'}
                className="max-w-full max-h-full object-contain"
                width={1200}
                height={800}
              />

              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm">{formatDate(selectedImage.uploadDate)}</p>
                </div>

                <div className="flex gap-2">
                  {onSetProfilePic && !selectedImage.isProfilePic && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSetProfilePic(selectedImage.id)}
                    >
                      <Heart className="h-4 w-4 ml-2" />
                      تعيين كصورة شخصية
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadImage(selectedImage)}
                  >
                    <Download className="h-4 w-4 ml-2" />
                    تنزيل
                  </Button>



                  {onDelete && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(selectedImage.id)}
                    >
                      <Trash2 className="h-4 w-4 ml-2" />
                      حذف
                    </Button>
                  )}
                </div>
              </div>

              <Button
                variant="outline"
                size="sm"
                className="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white p-2"
                onClick={goToPrevious}
                disabled={currentIndex === 0}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                className="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white p-2"
                onClick={goToNext}
                disabled={currentIndex === images.length - 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

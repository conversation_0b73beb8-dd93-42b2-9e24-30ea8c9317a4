# 🏗️ مخطط الكيانات والعلاقات - نظام المدفوعات والفواتير

## نظرة عامة
يوضح هذا المخطط جميع الكيانات الأساسية وعلاقاتها في نظام المدفوعات والفواتير.

## مخطط الكيانات والعلاقات

```mermaid
erDiagram
    %% الكيانات الأساسية
    PARENT {
        int id PK
        string name
        string phone
        string email
        string address
        float amountPerStudent
        datetime createdAt
        datetime updatedAt
    }

    STUDENT {
        int id PK
        string username UK
        string name
        int age
        string phone
        int guardianId FK
        int classeId FK
        float totalPoints
        datetime createdAt
        datetime updatedAt
    }

    CLASSE {
        int id PK
        string name
        string description
        int capacity
        datetime createdAt
        datetime updatedAt
    }

    INVOICE {
        int id PK
        int studentId FK
        int parentId FK
        float amount
        float originalAmount
        datetime dueDate
        datetime issueDate
        int month
        int year
        string description
        string status
        string type
        int remindersSent
        datetime lastReminderDate
        string notes
        int discountId FK
        datetime createdAt
        datetime updatedAt
    }

    PAYMENT {
        int id PK
        int studentId FK
        int invoiceId FK
        float amount
        datetime date
        string status
        int paymentMethodId FK
        string paymentMethodName
        string notes
        string receiptNumber
        datetime createdAt
        datetime updatedAt
    }

    PAYMENT_METHOD {
        int id PK
        string name
        string description
        string icon
        boolean isActive
        boolean requiresCard
        datetime createdAt
        datetime updatedAt
    }

    DISCOUNT {
        int id PK
        string name
        string description
        string type
        float value
        boolean isActive
        datetime startDate
        datetime endDate
        float minAmount
        float maxAmount
        datetime createdAt
        datetime updatedAt
    }

    PARENT_COMMUNICATION {
        int id PK
        int parentId FK
        string type
        string subject
        string content
        string status
        datetime sentAt
        datetime readAt
        string response
        datetime createdAt
    }

    %% العلاقات الأساسية
    PARENT ||--o{ STUDENT : "has children"
    PARENT ||--o{ INVOICE : "has family invoices"
    PARENT ||--o{ PARENT_COMMUNICATION : "receives communications"
    
    STUDENT }o--|| CLASSE : "belongs to"
    STUDENT ||--o{ INVOICE : "has individual invoices"
    STUDENT ||--o{ PAYMENT : "makes payments"
    
    INVOICE ||--o{ PAYMENT : "receives payments"
    INVOICE }o--o| DISCOUNT : "may have discount"
    
    PAYMENT }o--|| PAYMENT_METHOD : "uses method"
    
    %% الكيانات المحسوبة (Views/Computed)
    PARENT_PAYMENT_SUMMARY {
        int parentId PK
        string parentName
        string parentPhone
        string parentEmail
        float totalRequired
        float totalPaid
        float totalRemaining
        int totalStudents
        datetime lastPaymentDate
        float paymentRate
        string paymentStatus
    }

    STUDENT_PAYMENT_SUMMARY {
        int studentId PK
        string studentName
        string grade
        float totalRequired
        float totalPaid
        float totalRemaining
        int dueInvoices
        datetime lastPaymentDate
        string paymentStatus
    }

    INVOICE_SUMMARY {
        int invoiceId PK
        float invoiceAmount
        float paidAmount
        float remainingAmount
        string status
        boolean isOverdue
        int daysPastDue
    }

    %% العلاقات المحسوبة
    PARENT ||--|| PARENT_PAYMENT_SUMMARY : "computed from"
    STUDENT ||--|| STUDENT_PAYMENT_SUMMARY : "computed from"
    INVOICE ||--|| INVOICE_SUMMARY : "computed from"
```

## تفاصيل الكيانات

### 👨‍👩‍👧‍👦 PARENT (الأولياء)
**الغرض:** تخزين معلومات أولياء الأمور
- `id`: المعرف الفريد للولي
- `name`: اسم الولي
- `phone`: رقم الهاتف
- `email`: البريد الإلكتروني (اختياري)
- `address`: العنوان (اختياري)
- `amountPerStudent`: المبلغ المطلوب لكل تلميذ (اختياري)

### 👨‍🎓 STUDENT (التلاميذ)
**الغرض:** تخزين معلومات التلاميذ
- `id`: المعرف الفريد للتلميذ
- `username`: اسم المستخدم الفريد
- `name`: اسم التلميذ
- `guardianId`: معرف الولي (علاقة خارجية)
- `classeId`: معرف الصف (علاقة خارجية)

### 📄 INVOICE (الفواتير)
**الغرض:** تخزين الفواتير الفردية والجماعية
- `id`: المعرف الفريد للفاتورة
- `studentId`: معرف التلميذ (للفواتير الفردية)
- `parentId`: معرف الولي (للفواتير الجماعية)
- `amount`: مبلغ الفاتورة
- `type`: نوع الفاتورة (INDIVIDUAL/FAMILY)
- `status`: حالة الفاتورة (PAID/UNPAID/PARTIALLY_PAID/OVERDUE/CANCELLED)

### 💰 PAYMENT (المدفوعات)
**الغرض:** تخزين المدفوعات المسجلة
- `id`: المعرف الفريد للدفعة
- `studentId`: معرف التلميذ
- `invoiceId`: معرف الفاتورة المرتبطة (اختياري)
- `amount`: مبلغ الدفعة
- `status`: حالة الدفعة (PAID/PENDING/CANCELLED)

### 💳 PAYMENT_METHOD (طرق الدفع)
**الغرض:** تخزين طرق الدفع المتاحة
- `id`: المعرف الفريد لطريقة الدفع
- `name`: اسم طريقة الدفع
- `isActive`: حالة النشاط
- `requiresCard`: هل تتطلب بطاقة

## العلاقات الرئيسية

### 1. علاقة الولي بالتلاميذ (One-to-Many)
```
PARENT (1) ----< STUDENT (Many)
```
- ولي واحد يمكن أن يكون له عدة تلاميذ
- كل تلميذ له ولي واحد فقط

### 2. علاقة التلميذ بالفواتير (One-to-Many)
```
STUDENT (1) ----< INVOICE (Many)
```
- تلميذ واحد يمكن أن يكون له عدة فواتير
- كل فاتورة فردية مرتبطة بتلميذ واحد

### 3. علاقة الولي بالفواتير الجماعية (One-to-Many)
```
PARENT (1) ----< INVOICE (Many)
```
- ولي واحد يمكن أن يكون له عدة فواتير جماعية
- كل فاتورة جماعية مرتبطة بولي واحد

### 4. علاقة الفاتورة بالمدفوعات (One-to-Many)
```
INVOICE (1) ----< PAYMENT (Many)
```
- فاتورة واحدة يمكن أن تستقبل عدة مدفوعات
- كل دفعة يمكن أن ترتبط بفاتورة واحدة (اختياري)

### 5. علاقة التلميذ بالمدفوعات (One-to-Many)
```
STUDENT (1) ----< PAYMENT (Many)
```
- تلميذ واحد يمكن أن يقوم بعدة مدفوعات
- كل دفعة مرتبطة بتلميذ واحد

## القيود والقواعد التجارية

### قيود الفواتير
1. **الفاتورة الفردية:** يجب أن تحتوي على `studentId` وليس `parentId`
2. **الفاتورة الجماعية:** يجب أن تحتوي على `parentId` وليس `studentId`
3. **المبلغ:** يجب أن يكون أكبر من صفر
4. **التاريخ:** `dueDate` يجب أن يكون بعد `issueDate`

### قيود المدفوعات
1. **المبلغ:** يجب أن يكون أكبر من صفر
2. **التلميذ:** يجب أن يكون موجوداً في النظام
3. **الفاتورة:** إذا تم تحديدها، يجب أن تكون موجودة ومرتبطة بنفس التلميذ

### قيود الحسابات
1. **المبلغ المدفوع:** لا يمكن أن يتجاوز مبلغ الفاتورة
2. **حالة الفاتورة:** تُحدث تلقائياً بناءً على المدفوعات
3. **الديون:** تُحسب كالفرق بين المطلوب والمدفوع

## الفهارس المطلوبة

### فهارس الأداء
```sql
-- فهارس الفواتير
CREATE INDEX idx_invoice_student ON Invoice(studentId);
CREATE INDEX idx_invoice_parent ON Invoice(parentId);
CREATE INDEX idx_invoice_status ON Invoice(status);
CREATE INDEX idx_invoice_due_date ON Invoice(dueDate);
CREATE INDEX idx_invoice_month_year ON Invoice(month, year);

-- فهارس المدفوعات
CREATE INDEX idx_payment_student ON Payment(studentId);
CREATE INDEX idx_payment_invoice ON Payment(invoiceId);
CREATE INDEX idx_payment_date ON Payment(date);
CREATE INDEX idx_payment_status ON Payment(status);

-- فهارس الأولياء والتلاميذ
CREATE INDEX idx_student_guardian ON Student(guardianId);
CREATE INDEX idx_student_classe ON Student(classeId);
```

## الاستعلامات الشائعة

### 1. حساب ملخص مدفوعات الولي
```sql
SELECT 
    p.id, p.name, p.phone,
    SUM(CASE WHEN i.status != 'CANCELLED' THEN i.amount ELSE 0 END) as totalRequired,
    SUM(pay.amount) as totalPaid
FROM Parent p
LEFT JOIN Student s ON p.id = s.guardianId
LEFT JOIN Invoice i ON s.id = i.studentId
LEFT JOIN Payment pay ON s.id = pay.studentId AND pay.status = 'PAID'
GROUP BY p.id, p.name, p.phone;
```

### 2. حساب حالة الفاتورة
```sql
SELECT 
    i.id,
    i.amount,
    COALESCE(SUM(p.amount), 0) as paidAmount,
    CASE 
        WHEN COALESCE(SUM(p.amount), 0) >= i.amount THEN 'PAID'
        WHEN COALESCE(SUM(p.amount), 0) > 0 THEN 'PARTIALLY_PAID'
        WHEN i.dueDate < NOW() THEN 'OVERDUE'
        ELSE 'UNPAID'
    END as calculatedStatus
FROM Invoice i
LEFT JOIN Payment p ON i.id = p.invoiceId AND p.status = 'PAID'
GROUP BY i.id, i.amount, i.dueDate;
```

---

**ملاحظة:** هذا المخطط يمثل الهيكل المثالي لقاعدة البيانات بعد تطبيق الإصلاحات المطلوبة.

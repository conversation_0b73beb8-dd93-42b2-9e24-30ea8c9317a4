/*
==============================================================================
    مثال تدقيق SSL/TLS - Praetorian SSL Audit Example
    
    الوصف: مثال شامل لتدقيق تكوين SSL/TLS باستخدام مكتبة Praetorian
    المؤلف: Praetorian Team
==============================================================================
*/

# تحميل مكتبة Praetorian
load "../praetorian.ring"

/*
==============================================================================
    الدالة الرئيسية لتدقيق SSL
==============================================================================
*/

func main
    # إنشاء مثيل من المكتبة
    oPraetorian = CreatePraetorian()
    
    # طباعة رسالة الترحيب
    ? ""
    ? "=============================================="
    ? "مدقق SSL/TLS - Praetorian SSL Auditor"
    ? "=============================================="
    
    # قائمة الخوادم للفحص
    aTargets = [
        ["google.com", 443],
        ["github.com", 443],
        ["stackoverflow.com", 443],
        ["example.com", 443]
    ]
    
    ? "سيتم فحص " + len(aTargets) + " خادم..."
    ? "تاريخ التدقيق: " + date() + " " + time()
    ? ""
    
    # إنشاء تقرير شامل
    aOverallReport = [
        :scan_date = date() + " " + time(),
        :total_servers = len(aTargets),
        :servers_scanned = 0,
        :servers_with_ssl = 0,
        :servers_with_issues = 0,
        :detailed_reports = [],
        :summary = []
    ]
    
    # فحص كل خادم
    for aTarget in aTargets
        cHost = aTarget[1]
        nPort = aTarget[2]
        
        ? "=== فحص " + cHost + ":" + nPort + " ==="
        
        # فحص SSL شامل
        aSSLReport = oPraetorian.Crypto.SSLChecker.comprehensiveSSLCheck(cHost, nPort)
        add(aOverallReport[:detailed_reports], aSSLReport)
        
        # تحديث الإحصائيات
        aOverallReport[:servers_scanned]++
        
        if aSSLReport[:ssl_available]
            aOverallReport[:servers_with_ssl]++
        ok
        
        if len(aSSLReport[:security_issues]) > 0
            aOverallReport[:servers_with_issues]++
        ok
        
        # طباعة ملخص سريع
        printQuickSSLSummary(aSSLReport)
        ? ""
    next
    
    # إنشاء ملخص عام
    aOverallReport[:summary] = generateOverallSummary(aOverallReport)
    
    # طباعة التقرير الشامل
    printOverallSSLReport(aOverallReport)
    
    # حفظ التقرير
    saveSSLAuditReport(aOverallReport)

/*
==============================================================================
    طباعة ملخص سريع لخادم واحد
==============================================================================
*/

func printQuickSSLSummary aReport
    cHost = aReport[:host]
    nPort = aReport[:port]
    
    if not aReport[:ssl_available]
        ? "✗ SSL/TLS غير متاح على " + cHost + ":" + nPort
        return
    ok
    
    ? "✓ SSL/TLS متاح على " + cHost + ":" + nPort
    
    # معلومات الشهادة
    aCert = aReport[:certificate]
    if len(aCert) > 0 and aCert[:is_valid]
        ? "  الشهادة: صالحة حتى " + aCert[:valid_to] + " (" + aCert[:days_until_expiry] + " يوم متبقي)"
        
        if aCert[:is_expired]
            ? "  ⚠ تحذير: الشهادة منتهية الصلاحية!"
        but aCert[:days_until_expiry] < 30
            ? "  ⚠ تحذير: الشهادة ستنتهي قريباً!"
        ok
    ok
    
    # البروتوكولات
    if len(aReport[:supported_protocols]) > 0
        ? "  البروتوكولات: " + join(aReport[:supported_protocols], ", ")
        
        # تحذير من البروتوكولات القديمة
        for cProtocol in aReport[:supported_protocols]
            if cProtocol = "SSLv2" or cProtocol = "SSLv3" or cProtocol = "TLSv1.0"
                ? "  ⚠ تحذير: يدعم بروتوكول قديم " + cProtocol
            ok
        next
    ok
    
    # الشيفرات الضعيفة
    if len(aReport[:weak_ciphers]) > 0
        ? "  ⚠ تحذير: " + len(aReport[:weak_ciphers]) + " شيفرة ضعيفة مدعومة"
    ok
    
    # المشاكل الأمنية
    if len(aReport[:security_issues]) > 0
        ? "  ⚠ " + len(aReport[:security_issues]) + " مشكلة أمنية"
    else
        ? "  ✓ لا توجد مشاكل أمنية واضحة"
    ok

/*
==============================================================================
    دالة مساعدة لربط القوائم
==============================================================================
*/

func join aList, cSeparator
    if len(aList) = 0
        return ""
    ok
    
    cResult = aList[1]
    for i = 2 to len(aList)
        cResult += cSeparator + aList[i]
    next
    
    return cResult

/*
==============================================================================
    إنشاء ملخص عام
==============================================================================
*/

func generateOverallSummary aOverallReport
    aSummary = []
    
    # إحصائيات عامة
    add(aSummary, "تم فحص " + aOverallReport[:servers_scanned] + " خادم")
    add(aSummary, aOverallReport[:servers_with_ssl] + " خادم يدعم SSL/TLS")
    add(aSummary, aOverallReport[:servers_with_issues] + " خادم لديه مشاكل أمنية")
    
    # تحليل الشهادات
    nExpiredCerts = 0
    nExpiringSoon = 0
    nSelfSigned = 0
    
    for aReport in aOverallReport[:detailed_reports]
        if aReport[:ssl_available] and len(aReport[:certificate]) > 0
            aCert = aReport[:certificate]
            if aCert[:is_expired]
                nExpiredCerts++
            but aCert[:days_until_expiry] < 30
                nExpiringSoon++
            ok
            
            if aCert[:is_self_signed]
                nSelfSigned++
            ok
        ok
    next
    
    if nExpiredCerts > 0
        add(aSummary, nExpiredCerts + " شهادة منتهية الصلاحية")
    ok
    
    if nExpiringSoon > 0
        add(aSummary, nExpiringSoon + " شهادة ستنتهي خلال 30 يوم")
    ok
    
    if nSelfSigned > 0
        add(aSummary, nSelfSigned + " شهادة موقعة ذاتياً")
    ok
    
    # تحليل البروتوكولات
    nOldProtocols = 0
    for aReport in aOverallReport[:detailed_reports]
        for cProtocol in aReport[:supported_protocols]
            if cProtocol = "SSLv2" or cProtocol = "SSLv3" or cProtocol = "TLSv1.0"
                nOldProtocols++
                exit  # خروج من الحلقة الداخلية فقط
            ok
        next
    next
    
    if nOldProtocols > 0
        add(aSummary, nOldProtocols + " خادم يدعم بروتوكولات قديمة")
    ok
    
    # تحليل الشيفرات الضعيفة
    nWeakCiphers = 0
    for aReport in aOverallReport[:detailed_reports]
        if len(aReport[:weak_ciphers]) > 0
            nWeakCiphers++
        ok
    next
    
    if nWeakCiphers > 0
        add(aSummary, nWeakCiphers + " خادم يدعم شيفرات ضعيفة")
    ok
    
    return aSummary

/*
==============================================================================
    طباعة التقرير الشامل
==============================================================================
*/

func printOverallSSLReport aReport
    ? ""
    ? "================================================"
    ? "التقرير الشامل لتدقيق SSL/TLS"
    ? "================================================"
    ? "تاريخ الفحص: " + aReport[:scan_date]
    ? "إجمالي الخوادم: " + aReport[:total_servers]
    ? "الخوادم المفحوصة: " + aReport[:servers_scanned]
    ? "الخوادم التي تدعم SSL: " + aReport[:servers_with_ssl]
    ? "الخوادم التي لديها مشاكل: " + aReport[:servers_with_issues]
    ? "================================================"
    
    # الملخص العام
    ? ""
    ? "الملخص العام:"
    ? "-------------"
    for cSummaryItem in aReport[:summary]
        ? "• " + cSummaryItem
    next
    
    # تفاصيل كل خادم
    ? ""
    ? "تفاصيل الخوادم:"
    ? "---------------"
    
    for aServerReport in aReport[:detailed_reports]
        ? ""
        ? "خادم: " + aServerReport[:host] + ":" + aServerReport[:port]
        ? "------"
        
        if not aServerReport[:ssl_available]
            ? "SSL/TLS: غير متاح"
            loop
        ok
        
        ? "SSL/TLS: متاح"
        
        # تفاصيل الشهادة
        aCert = aServerReport[:certificate]
        if len(aCert) > 0 and aCert[:is_valid]
            ? "الشهادة:"
            ? "  الموضوع: " + aCert[:subject]
            ? "  المصدر: " + aCert[:issuer]
            ? "  صالحة حتى: " + aCert[:valid_to]
            ? "  الأيام المتبقية: " + aCert[:days_until_expiry]
            ? "  خوارزمية التوقيع: " + aCert[:signature_algorithm]
            ? "  حجم المفتاح: " + aCert[:key_size] + " بت"
            
            if aCert[:is_expired]
                ? "  ⚠ الحالة: منتهية الصلاحية"
            but aCert[:days_until_expiry] < 30
                ? "  ⚠ الحالة: ستنتهي قريباً"
            else
                ? "  ✓ الحالة: صالحة"
            ok
        ok
        
        # البروتوكولات المدعومة
        if len(aServerReport[:supported_protocols]) > 0
            ? "البروتوكولات المدعومة:"
            for cProtocol in aServerReport[:supported_protocols]
                cStatus = ""
                if cProtocol = "SSLv2" or cProtocol = "SSLv3"
                    cStatus = " ⚠ (غير آمن)"
                but cProtocol = "TLSv1.0"
                    cStatus = " ⚠ (قديم)"
                but cProtocol = "TLSv1.3"
                    cStatus = " ✓ (حديث)"
                ok
                ? "  • " + cProtocol + cStatus
            next
        ok
        
        # الشيفرات الضعيفة
        if len(aServerReport[:weak_ciphers]) > 0
            ? "الشيفرات الضعيفة المدعومة:"
            for cCipher in aServerReport[:weak_ciphers]
                ? "  ⚠ " + cCipher
            next
        ok
        
        # المشاكل الأمنية
        if len(aServerReport[:security_issues]) > 0
            ? "المشاكل الأمنية:"
            for cIssue in aServerReport[:security_issues]
                ? "  ⚠ " + cIssue
            next
        ok
        
        # التوصيات
        if len(aServerReport[:recommendations]) > 0
            ? "التوصيات:"
            for cRecommendation in aServerReport[:recommendations]
                ? "  💡 " + cRecommendation
            next
        ok
    next
    
    ? ""
    ? "================================================"

/*
==============================================================================
    حفظ تقرير التدقيق
==============================================================================
*/

func saveSSLAuditReport aReport
    cFileName = "ssl_audit_report_" + substr(date(), "/", "_") + "_" + 
                substr(time(), ":", "_") + ".txt"
    
    cReportText = "تقرير تدقيق SSL/TLS الشامل" + nl +
                  "=========================" + nl +
                  "تاريخ الفحص: " + aReport[:scan_date] + nl +
                  "إجمالي الخوادم: " + aReport[:total_servers] + nl +
                  "الخوادم المفحوصة: " + aReport[:servers_scanned] + nl +
                  "الخوادم التي تدعم SSL: " + aReport[:servers_with_ssl] + nl +
                  "الخوادم التي لديها مشاكل: " + aReport[:servers_with_issues] + nl + nl
    
    # إضافة الملخص
    cReportText += "الملخص العام:" + nl
    for cSummaryItem in aReport[:summary]
        cReportText += "• " + cSummaryItem + nl
    next
    cReportText += nl
    
    # إضافة تفاصيل الخوادم
    cReportText += "تفاصيل الخوادم:" + nl
    cReportText += "---------------" + nl
    
    for aServerReport in aReport[:detailed_reports]
        cReportText += nl + "خادم: " + aServerReport[:host] + ":" + aServerReport[:port] + nl
        
        if not aServerReport[:ssl_available]
            cReportText += "SSL/TLS: غير متاح" + nl
            loop
        ok
        
        cReportText += "SSL/TLS: متاح" + nl
        
        # إضافة المشاكل الأمنية
        if len(aServerReport[:security_issues]) > 0
            cReportText += "المشاكل الأمنية:" + nl
            for cIssue in aServerReport[:security_issues]
                cReportText += "  ⚠ " + cIssue + nl
            next
        ok
        
        # إضافة التوصيات
        if len(aServerReport[:recommendations]) > 0
            cReportText += "التوصيات:" + nl
            for cRecommendation in aServerReport[:recommendations]
                cReportText += "  💡 " + cRecommendation + nl
            next
        ok
    next
    
    try
        write(cFileName, cReportText)
        ? "تم حفظ التقرير في: " + cFileName
    catch
        ? "خطأ في حفظ التقرير: " + cCatchError
    done

/*
==============================================================================
    مثال لفحص خادم واحد بالتفصيل
==============================================================================
*/

func detailedSingleServerCheck
    ? ""
    ? "=============================================="
    ? "مثال: فحص خادم واحد بالتفصيل"
    ? "=============================================="
    
    oPraetorian = CreatePraetorian()
    
    cHost = "google.com"
    nPort = 443
    
    ? "فحص " + cHost + ":" + nPort + " بالتفصيل..."
    ? ""
    
    # تفعيل الوضع المفصل
    oPraetorian.Crypto.SSLChecker.setVerbose(true)
    
    # فحص شامل
    aReport = oPraetorian.Crypto.SSLChecker.comprehensiveSSLCheck(cHost, nPort)
    
    # طباعة التقرير المفصل
    printDetailedSSLReport(aReport)

/*
==============================================================================
    طباعة تقرير مفصل لخادم واحد
==============================================================================
*/

func printDetailedSSLReport aReport
    ? "================================================"
    ? "تقرير SSL/TLS مفصل"
    ? "================================================"
    ? "الخادم: " + aReport[:host] + ":" + aReport[:port]
    ? "حالة SSL: " + (aReport[:ssl_available] ? "متاح" : "غير متاح")
    ? "================================================"
    
    if not aReport[:ssl_available]
        ? "SSL/TLS غير متاح على هذا الخادم"
        return
    ok
    
    # تفاصيل الشهادة الكاملة
    aCert = aReport[:certificate]
    if len(aCert) > 0
        ? ""
        ? "تفاصيل الشهادة:"
        ? "---------------"
        ? "الموضوع: " + aCert[:subject]
        ? "المصدر: " + aCert[:issuer]
        ? "الرقم التسلسلي: " + aCert[:serial_number]
        ? "الإصدار: " + aCert[:version]
        ? "صالحة من: " + aCert[:valid_from]
        ? "صالحة حتى: " + aCert[:valid_to]
        ? "الأيام المتبقية: " + aCert[:days_until_expiry]
        ? "خوارزمية التوقيع: " + aCert[:signature_algorithm]
        ? "خوارزمية المفتاح العام: " + aCert[:public_key_algorithm]
        ? "حجم المفتاح: " + aCert[:key_size] + " بت"
        ? "موقعة ذاتياً: " + (aCert[:is_self_signed] ? "نعم" : "لا")
        ? "منتهية الصلاحية: " + (aCert[:is_expired] ? "نعم" : "لا")
        ? "SHA1 Fingerprint: " + aCert[:fingerprint_sha1]
        ? "SHA256 Fingerprint: " + aCert[:fingerprint_sha256]
        
        if len(aCert[:san_domains]) > 0
            ? "النطاقات البديلة (SAN):"
            for cDomain in aCert[:san_domains]
                ? "  • " + cDomain
            next
        ok
    ok
    
    # جميع البروتوكولات
    if len(aReport[:supported_protocols]) > 0
        ? ""
        ? "البروتوكولات المدعومة:"
        ? "---------------------"
        for cProtocol in aReport[:supported_protocols]
            ? "• " + cProtocol
        next
    ok
    
    # جميع الشيفرات الضعيفة
    if len(aReport[:weak_ciphers]) > 0
        ? ""
        ? "الشيفرات الضعيفة المدعومة:"
        ? "-------------------------"
        for cCipher in aReport[:weak_ciphers]
            ? "⚠ " + cCipher
        next
    ok
    
    # جميع المشاكل الأمنية
    if len(aReport[:security_issues]) > 0
        ? ""
        ? "المشاكل الأمنية:"
        ? "---------------"
        for cIssue in aReport[:security_issues]
            ? "⚠ " + cIssue
        next
    ok
    
    # جميع التوصيات
    if len(aReport[:recommendations]) > 0
        ? ""
        ? "التوصيات:"
        ? "---------"
        for cRecommendation in aReport[:recommendations]
            ? "💡 " + cRecommendation
        next
    ok
    
    ? ""
    ? "================================================"

# تشغيل المثال الرئيسي
main()

# تشغيل مثال الفحص المفصل
detailedSingleServerCheck()

'use client';

import { useState, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Eye, Download, Pencil } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import * as XLSX from 'xlsx';

type Exam = {
  id: number;
  title: string;
  date: string;
  className: string;
  type: string;
};

type Student = {
  id: number;
  name: string;
  className: string;
  classeId?: number;
};

type ExamResult = {
  id: number;
  examId: number;
  studentId: number;
  grade: number;
  status: string;
  note?: string;
  student: Student;
  criteriaScores: CriteriaScore[];
};

type CriteriaScore = {
  id: number;
  criteriaId: number;
  score: number;
  criteria: {
    id: number;
    name: string;
    weight: number;
  };
};

export default function TeacherExamResultsPage() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [selectedExamId, setSelectedExamId] = useState<string>('all');
  const [selectedClassId, setSelectedClassId] = useState<string>('all');
  const [examResults, setExamResults] = useState<ExamResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [resultsLoading, setResultsLoading] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedExamResult, setSelectedExamResult] = useState<ExamResult | null>(null);
  const [editGrade, setEditGrade] = useState<string>('');
  const [editNote, setEditNote] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [teacherId, setTeacherId] = useState<string | null>(null);
  const [classes, setClasses] = useState<{ id: number; name: string }[]>([]);

  useEffect(() => {
    const fetchExams = async () => {
      try {
        setLoading(true);

        // الحصول على معرف المعلم أولاً
        const teacherResponse = await fetch('/api/teacher-id');
        if (!teacherResponse.ok) {
          console.error('Failed to fetch teacher data');
          return;
        }

        const teacherData = await teacherResponse.json();
        if (!teacherData.success || !teacherData.teacherId) {
          console.error('Teacher ID not found in session', teacherData);
          return;
        }

        const teacherId = teacherData.teacherId.toString();
        console.log('Retrieved teacher ID for exams:', teacherId);
        setTeacherId(teacherId);

        // جلب الامتحانات مع معرف المعلم
        const response = await fetch(`/api/evaluation/exams?teacherId=${teacherId}`);

        if (!response.ok) {
          throw new Error('فشل في جلب الامتحانات');
        }

        const data = await response.json();

        if (data.success && Array.isArray(data.data)) {
          // تحويل البيانات إلى الشكل المطلوب
          interface ExamData {
            id: number;
            evaluationType: string;
            month: string;
            description?: string;
            exam_points?: Array<{
              classSubject?: {
                classe?: {
                  name: string;
                }
              }
            }>;
          }

          const formattedExams = data.data.map((exam: ExamData) => ({
            id: exam.id,
            title: getEvaluationTypeLabel(exam.evaluationType),
            date: exam.month,
            className: exam.exam_points && exam.exam_points.length > 0 && exam.exam_points[0].classSubject?.classe?.name || '-',
            type: exam.evaluationType,
            description: exam.description
          }));

          setExams(formattedExams);
        } else {
          setExams([]);
        }
      } catch (err) {
        console.error('Error fetching exams:', err);
        toast.error('فشل في جلب الامتحانات');
      } finally {
        setLoading(false);
      }
    };

    fetchExams();
  }, []);

  useEffect(() => {
    // جلب الفصول الدراسية التي يدرسها المعلم
    const fetchClasses = async () => {
      try {
        // استخدام معرف المعلم الذي تم الحصول عليه من fetchExams
        if (!teacherId) {
          console.error('Teacher ID not available yet');
          return;
        }

        console.log('Using teacher ID for classes:', teacherId);

        // استخدام معلمات URL لتقليل البيانات المسترجعة وتصفية الفصول حسب المعلم
        const response = await fetch(`/api/teacher-classes?teacherId=${teacherId}`);
        if (!response.ok) {
          throw new Error('فشل في جلب الفصول الدراسية');
        }

        const data = await response.json();
        console.log('Received classes data:', data);

        if (data.success && Array.isArray(data.classes)) {
          setClasses(data.classes.map((cls: { id: number; name: string }) => ({
            id: cls.id,
            name: cls.name
          })));
        } else {
          // محاولة بديلة لجلب الفصول من نتائج الامتحانات
          const uniqueClasses = new Set<string>();
          const classesFromExams: { id: number; name: string }[] = [];

          exams.forEach(exam => {
            if (exam.className && !uniqueClasses.has(exam.className)) {
              uniqueClasses.add(exam.className);
              // استخدام رقم عشوائي كمعرف مؤقت للفصل
              classesFromExams.push({
                id: Math.floor(Math.random() * 10000) + 1,
                name: exam.className
              });
            }
          });

          if (classesFromExams.length > 0) {
            console.log('Using classes from exams:', classesFromExams);
            setClasses(classesFromExams);
          } else {
            // إذا لم نتمكن من الحصول على الفصول من أي مصدر، نضيف فصلاً افتراضياً
            setClasses([
              { id: 1, name: 'الفصل الأول' },
              { id: 2, name: 'الفصل الثاني' },
              { id: 3, name: 'الفصل الثالث' }
            ]);
          }
        }
      } catch (err) {
        console.error('Error fetching classes:', err);
        // في حالة حدوث خطأ، نضيف فصولاً افتراضية
        setClasses([
          { id: 1, name: 'الفصل الأول' },
          { id: 2, name: 'الفصل الثاني' },
          { id: 3, name: 'الفصل الثالث' }
        ]);
      }
    };

    if (teacherId) {
      fetchClasses();
    }
  }, [teacherId, exams]);

  // تخزين جميع النتائج المستلمة من API
  const [allExamResults, setAllExamResults] = useState<ExamResult[]>([]);

  // جلب جميع النتائج من API
  useEffect(() => {
    const fetchAllExamResults = async () => {
      if (!teacherId) {
        console.warn('Teacher ID is not available for fetching results');
        return;
      }

      setResultsLoading(true);
      try {
        // بناء URL لجلب جميع النتائج للمعلم
        const url = `/api/exam-points?teacherId=${teacherId}`;

        console.log('Fetching all exam results from URL:', url);
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error('فشل في جلب نتائج الامتحان');
        }

        const data = await response.json();
        console.log('Received all exam results data:', data);

        if (data.success && Array.isArray(data.data)) {
          // تحويل البيانات إلى الشكل المطلوب
          interface ExamPoint {
            id: number;
            examId: number;
            studentId: number;
            grade: number;
            status?: string;
            student: {
              id: number;
              name: string;
              classe?: {
                id: number;
                name: string;
              }
            };
            classSubject?: {
              classe?: {
                id: number;
                name: string;
              }
            };
            criteriaScores?: Array<{
              id: number;
              criteriaId: number;
              score: number;
              criteria: {
                id: number;
                name: string;
                weight: number;
              };
            }>;
          }

          const formattedResults = data.data.map((point: ExamPoint) => {
            // تحديد حالة النتيجة بناءً على الدرجة
            let status = 'PENDING';
            const grade = Number(point.grade);

            if (grade >= 9) {
              status = 'EXCELLENT';
            } else if (grade >= 6) {
              status = 'PASSED';
            } else {
              status = 'FAILED';
            }

            // استخراج اسم الفصل من البيانات
            const className = point.student?.classe?.name ||
                             point.classSubject?.classe?.name ||
                             '-';

            // استخراج معرف الفصل من البيانات
            const classeId = point.student?.classe?.id ||
                            point.classSubject?.classe?.id;

            return {
              id: point.id,
              examId: point.examId,
              studentId: point.studentId,
              grade: grade,
              status: point.status || status,
              student: {
                id: point.student.id,
                name: point.student.name,
                className: className,
                classeId: classeId
              },
              criteriaScores: point.criteriaScores || []
            };
          });

          console.log('All formatted results:', formattedResults);
          setAllExamResults(formattedResults);
        } else {
          console.log('No results found or invalid data format');
          setAllExamResults([]);
        }
      } catch (err) {
        console.error('Error fetching all exam results:', err);
        toast.error('فشل في جلب نتائج الامتحان');
        setAllExamResults([]);
      } finally {
        setResultsLoading(false);
      }
    };

    if (teacherId) {
      fetchAllExamResults();
    }
  }, [teacherId]);

  // تصفية النتائج حسب الامتحان والفصل المحددين
  useEffect(() => {
    console.log('Filtering results based on exam and class selection');
    console.log('Selected exam ID:', selectedExamId);
    console.log('Selected class ID:', selectedClassId);
    console.log('All results count:', allExamResults.length);

    // إذا لم يتم تحديد امتحان أو فصل، أو لم يتم جلب أي نتائج بعد
    if ((!selectedExamId && !selectedClassId) || allExamResults.length === 0) {
      setExamResults([]);
      return;
    }

    let filteredResults = [...allExamResults];

    // تصفية حسب الامتحان المحدد
    if (selectedExamId && selectedExamId !== "all") {
      console.log('Filtering by exam ID:', selectedExamId);
      filteredResults = filteredResults.filter(result =>
        result.examId === parseInt(selectedExamId)
      );
      console.log('After exam filtering, results count:', filteredResults.length);
    }

    // تصفية حسب الفصل المحدد
    if (selectedClassId && selectedClassId !== "all") {
      console.log('Filtering by class ID:', selectedClassId);

      // البحث عن الفصل المحدد في قائمة الفصول
      const selectedClass = classes.find(cls => cls.id.toString() === selectedClassId);
      console.log('Selected class:', selectedClass);

      if (selectedClass) {
        filteredResults = filteredResults.filter(result => {
          // استخراج معرف الفصل واسمه من النتيجة
          const studentClassId = result.student.classeId;
          const studentClassName = result.student.className;

          // تحقق من تطابق معرف الفصل أو اسمه
          const matchById = studentClassId && studentClassId === parseInt(selectedClassId);
          const matchByName = studentClassName === selectedClass.name;

          return matchById || matchByName;
        });

        console.log('After class filtering, results count:', filteredResults.length);
      }
    }

    console.log('Final filtered results:', filteredResults);
    setExamResults(filteredResults);
  }, [selectedExamId, selectedClassId, allExamResults, classes]);

  const getEvaluationTypeLabel = (type: string) => {
    const evaluationTypes: Record<string, string> = {
      'WRITTEN_EXAM': 'امتحان تحريري',
      'ORAL_EXAM': 'امتحان شفوي',
      'HOMEWORK': 'واجب منزلي',
      'PROJECT': 'مشروع',
      'QURAN_RECITATION': 'تلاوة القرآن',
      'QURAN_MEMORIZATION': 'حفظ القرآن',
      'PRACTICAL_TEST': 'اختبار عملي',
      'REMOTE_EXAM': 'امتحان عن بعد'
    };
    return evaluationTypes[type] || type;
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'قيد الانتظار';
      case 'PASSED':
        return 'ناجح';
      case 'FAILED':
        return 'راسب';
      case 'EXCELLENT':
        return 'ممتاز';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'PASSED':
        return 'bg-green-100 text-green-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      case 'EXCELLENT':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getGradeColor = (grade: number) => {
    if (grade >= 9) return 'text-purple-600 font-bold';
    if (grade >= 8) return 'text-primary-color font-bold';
    if (grade >= 7) return 'text-blue-600';
    if (grade >= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  const openDetailsDialog = (examResult: ExamResult) => {
    setSelectedExamResult(examResult);
    setIsDetailsDialogOpen(true);
  };

  const openEditDialog = (examResult: ExamResult) => {
    setSelectedExamResult(examResult);
    setEditGrade(examResult.grade.toString());
    setEditNote(examResult.note || '');
    setIsEditDialogOpen(true);
  };

  const handleUpdateGrade = async () => {
    if (!selectedExamResult) return;

    // التحقق من صحة الدرجة
    const numericGrade = Number(editGrade);
    if (isNaN(numericGrade) || numericGrade < 0 || numericGrade > 10) {
      toast.error('يجب أن تكون الدرجة رقماً بين 0 و 10');
      return;
    }

    setIsSubmitting(true);

    try {
      // تحديد الحالة بناءً على الدرجة الجديدة
      const newStatus = numericGrade >= 9 ? 'EXCELLENT' : numericGrade >= 6 ? 'PASSED' : 'FAILED';

      const response = await fetch(`/api/exam-points`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedExamResult.id,
          grade: numericGrade,
          note: editNote,
          status: newStatus, // إضافة الحالة الجديدة
          teacherId: teacherId || undefined // إضافة معرف المعلم للتحقق من الصلاحيات
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData?.error || 'فشل في تحديث النقاط');
      }

      await response.json();

      // تحديث النتائج في الواجهة
      setExamResults(prevResults =>
        prevResults.map(result =>
          result.id === selectedExamResult.id
            ? {
                ...result,
                grade: numericGrade,
                note: editNote,
                // تحديث الحالة بناءً على الدرجة الجديدة
                status: numericGrade >= 9 ? 'EXCELLENT' : numericGrade >= 6 ? 'PASSED' : 'FAILED'
              }
            : result
        )
      );

      toast.success('تم تحديث نقاط الطالب بنجاح');
      setIsEditDialogOpen(false);
    } catch (err) {
      console.error('Error updating grade:', err);
      toast.error(err instanceof Error ? err.message : 'فشل في تحديث النقاط');
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateClassStats = () => {
    if (examResults.length === 0) return { avg: 0, excellent: 0, passed: 0, failed: 0 };

    const avg = examResults.reduce((sum, result) => sum + result.grade, 0) / examResults.length;
    const excellent = examResults.filter(result => result.status === 'EXCELLENT').length;
    const passed = examResults.filter(result => result.status === 'PASSED').length;
    const failed = examResults.filter(result => result.status === 'FAILED').length;

    return { avg, excellent, passed, failed };
  };

  const stats = calculateClassStats();

  const exportToExcel = () => {
    try {
      if (examResults.length === 0) {
        toast.error('لا توجد بيانات للتصدير');
        return;
      }

      // الحصول على الامتحان المحدد
      const selectedExam = exams.find(exam => exam.id.toString() === selectedExamId);
      if (!selectedExam) {
        toast.error('لم يتم العثور على الامتحان المحدد');
        return;
      }

      // تحويل البيانات إلى تنسيق مناسب للتصدير
      const dataToExport = examResults.map(result => ({
        'اسم الطالب': result.student.name,
        'الفصل': result.student.className || '-',
        'الدرجة': Number(result.grade).toFixed(1),
        'الحالة': getStatusLabel(result.status)
      }));

      // إنشاء ورقة عمل Excel
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'نتائج الامتحان');

      // تعيين عرض الأعمدة
      const columnWidths = [
        { wch: 25 }, // اسم الطالب
        { wch: 15 }, // الفصل
        { wch: 10 }, // الدرجة
        { wch: 12 }  // الحالة
      ];
      worksheet['!cols'] = columnWidths;

      // تصدير الملف
      const examTitle = selectedExam.title || getEvaluationTypeLabel(selectedExam.type);
      const examDate = formatDate(selectedExam.date);
      const fileName = `نتائج_${examTitle}_${examDate}.xlsx`;

      XLSX.writeFile(workbook, fileName);
      toast.success('تم تصدير النتائج بنجاح');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير النتائج');
    }
  };

  return (
    <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">نتائج الامتحانات</h1>
        <Button
          variant="outline"
          onClick={exportToExcel}
          disabled={!selectedExamId || selectedExamId === 'all' || examResults.length === 0}
        >
          <Download className="ml-2" size={16} />
          تصدير النتائج
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-right mb-2">اختر الامتحان</label>
          <Select
            value={selectedExamId}
            onValueChange={setSelectedExamId}
          >
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="اختر الامتحان" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الامتحانات</SelectItem>
              {exams.map((exam) => (
                <SelectItem key={exam.id} value={exam.id.toString()}>
                  {exam.title} - {formatDate(exam.date)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="block text-right mb-2">اختر الفصل</label>
          <Select
            value={selectedClassId}
            onValueChange={setSelectedClassId}
          >
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="اختر الفصل" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الفصول</SelectItem>
              {classes.map((cls) => (
                <SelectItem key={cls.id} value={cls.id.toString()}>
                  {cls.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {loading ? (
        <div className="flex flex-col justify-center items-center h-64 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
          <div className="relative">
            <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
            <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
          </div>
          <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري تحميل الامتحانات...</span>
        </div>
      ) : (
        <Tabs defaultValue="results">
          <TabsList className="mb-4 bg-[#e9f7f5] p-1 rounded-xl">
            <TabsTrigger value="results" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">النتائج</TabsTrigger>
            <TabsTrigger value="stats" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">الإحصائيات</TabsTrigger>
          </TabsList>

          <TabsContent value="results">
            {resultsLoading ? (
              <div className="flex flex-col justify-center items-center h-64 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
                <div className="relative">
                  <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
                  <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
                </div>
                <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري تحميل النتائج...</span>
              </div>
            ) : examResults.length === 0 ? (
              <div className="text-center p-8 border rounded-lg">
                <p className="text-gray-500">لا توجد نتائج للعرض</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-right">الطالب</TableHead>
                      <TableHead className="text-right">الفصل</TableHead>
                      <TableHead className="text-right">الدرجة</TableHead>
                      <TableHead className="text-right">الحالة</TableHead>
                      <TableHead className="text-right">التفاصيل</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {examResults.map((result) => (
                      <TableRow key={result.id}>
                        <TableCell>{result.student.name}</TableCell>
                        <TableCell>{result.student.className}</TableCell>
                        <TableCell className={getGradeColor(result.grade)}>
                          {Number(result.grade).toFixed(1)}
                        </TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(result.status)}`}>
                            {getStatusLabel(result.status)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openDetailsDialog(result)}
                          >
                            <Eye className="h-4 w-4 ml-1" />
                            التفاصيل
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openEditDialog(result)}
                          >
                            <Pencil className="h-4 w-4 ml-1" />
                            تعديل نقاط الطالب
                          </Button>
                        </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>

          <TabsContent value="stats">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">متوسط الدرجات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-[var(--primary-color)]">{Number(stats.avg).toFixed(1)}</div>
                  <p className="text-sm text-gray-500">من 10 درجات</p>
                </CardContent>
              </Card>
              <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">ممتاز</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600">{stats.excellent}</div>
                  <p className="text-sm text-gray-500">طالب</p>
                </CardContent>
              </Card>
              <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">ناجح</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-primary-color">{stats.passed}</div>
                  <p className="text-sm text-gray-500">طالب</p>
                </CardContent>
              </Card>
              <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">راسب</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-red-600">{stats.failed}</div>
                  <p className="text-sm text-gray-500">طالب</p>
                </CardContent>
              </Card>
            </div>

            <Card className="border border-[#e9f7f5] shadow-md hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <CardTitle className="text-lg">توزيع الدرجات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-right">ممتاز (9-10)</span>
                      <span className="text-sm font-medium text-right">{stats.excellent} طلاب</span>
                    </div>
                    <Progress value={(stats.excellent / examResults.length) * 100} className="h-2 bg-gray-200" />
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-right">جيد جدًا (8-9)</span>
                      <span className="text-sm font-medium text-right">
                        {examResults.filter(r => r.grade >= 8 && r.grade < 9).length} طلاب
                      </span>
                    </div>
                    <Progress
                      value={(examResults.filter(r => r.grade >= 8 && r.grade < 9).length / examResults.length) * 100}
                      className="h-2 bg-gray-200"
                    />
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-right">جيد (7-8)</span>
                      <span className="text-sm font-medium text-right">
                        {examResults.filter(r => r.grade >= 7 && r.grade < 8).length} طلاب
                      </span>
                    </div>
                    <Progress
                      value={(examResults.filter(r => r.grade >= 7 && r.grade < 8).length / examResults.length) * 100}
                      className="h-2 bg-gray-200"
                    />
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-right">مقبول (6-7)</span>
                      <span className="text-sm font-medium text-right">
                        {examResults.filter(r => r.grade >= 6 && r.grade < 7).length} طلاب
                      </span>
                    </div>
                    <Progress
                      value={(examResults.filter(r => r.grade >= 6 && r.grade < 7).length / examResults.length) * 100}
                      className="h-2 bg-gray-200"
                    />
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-right">راسب (أقل من 6)</span>
                      <span className="text-sm font-medium text-right">
                        {examResults.filter(r => r.grade < 6).length} طلاب
                      </span>
                    </div>
                    <Progress
                      value={(examResults.filter(r => r.grade < 6).length / examResults.length) * 100}
                      className="h-2 bg-gray-200"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={(open) => !open && setIsDetailsDialogOpen(false)}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>تفاصيل نتيجة الامتحان</DialogTitle>
            <DialogDescription>
              تفاصيل نتيجة الطالب في الاختبار
            </DialogDescription>
            {selectedExamResult && (
              <div className="mt-4">
                <div className="text-right mb-1">
                  <span className="font-bold">الطالب:</span> {selectedExamResult.student.name}
                </div>
                <div className="text-right mb-1">
                  <span className="font-bold">الفصل:</span> {selectedExamResult.student.className}
                </div>
                <div className="text-right mb-1">
                  <span className="font-bold">الدرجة النهائية:</span>{' '}
                  <span className={getGradeColor(selectedExamResult.grade)}>
                    {Number(selectedExamResult.grade).toFixed(1)}
                  </span>
                </div>
                <div className="text-right mb-4">
                  <span className="font-bold">الحالة:</span>{' '}
                  <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(selectedExamResult.status)}`}>
                    {getStatusLabel(selectedExamResult.status)}
                  </span>
                </div>

                {selectedExamResult.criteriaScores && selectedExamResult.criteriaScores.length > 0 ? (
                  <div className="mt-4">
                    <h3 className="text-lg font-bold text-right mb-2">تفاصيل معايير التقييم</h3>
                    <div className="space-y-4">
                      {selectedExamResult.criteriaScores.map((score) => (
                        <div key={score.id} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium text-right">
                              {Number(score.score).toFixed(1)} / 10
                            </span>
                            <span className="text-sm font-medium text-right">
                              {score.criteria.name} ({(Number(score.criteria.weight) * 100).toFixed(0)}%)
                            </span>
                          </div>
                          <Progress value={Number(score.score) * 10} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500 mt-4">لا توجد تفاصيل لمعايير التقييم</div>
                )}
              </div>
            )}
          </DialogHeader>
          <div className="flex justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDetailsDialogOpen(false)}
            >
              إغلاق
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Grade Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent className="max-w-md bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>تعديل نقاط الطالب</DialogTitle>
            <DialogDescription>
              معلومات الطالب والاختبار
            </DialogDescription>
            {selectedExamResult && (
              <div className="mt-2">
                <div className="text-right mb-1">
                  <span className="font-bold">الطالب:</span> {selectedExamResult.student.name}
                </div>
                <div className="text-right mb-3">
                  <span className="font-bold">الفصل:</span> {selectedExamResult.student.className}
                </div>
              </div>
            )}
          </DialogHeader>

          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <label className="text-right block font-medium">الدرجة (من 10)</label>
              <Input
                type="number"
                min="0"
                max="10"
                step="0.5"
                value={editGrade}
                onChange={(e) => setEditGrade(e.target.value)}
                className="text-center font-bold text-lg"
                dir="ltr"
              />
            </div>

            <div className="space-y-2">
              <label className="text-right block font-medium">الملاحظات (اختياري)</label>
              <Textarea
                value={editNote}
                onChange={(e) => setEditNote(e.target.value)}
                className="text-right"
                placeholder="أدخل ملاحظات حول أداء الطالب"
                rows={3}
              />
            </div>
          </div>

          <DialogFooter className="flex justify-between mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button
              type="button"
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              onClick={handleUpdateGrade}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري الحفظ...
                </>
              ) : (
                'حفظ التغييرات'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

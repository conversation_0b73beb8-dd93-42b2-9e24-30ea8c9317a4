import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/khatm-attendance - الحصول على سجلات حضور مجلس ختم
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const khatmSessionId = searchParams.get('khatmSessionId');
    const studentId = searchParams.get('studentId');
    const includeProgress = searchParams.get('includeProgress') === 'true';

    if (!khatmSessionId && !studentId) {
      return NextResponse.json({
        success: false,
        error: 'يجب تحديد معرف مجلس الختم أو معرف الطالب'
      }, { status: 400 });
    }

    // بناء شروط البحث
    const where: {
      khatmSessionId?: number;
      studentId?: number;
    } = {};

    if (khatmSessionId) {
      where.khatmSessionId = parseInt(khatmSessionId);
    }

    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    const attendance = await prisma.khatmSessionAttendance.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true
          }
        },
        khatmSession: true,
        images: true,
        progressRecords: includeProgress // تضمين سجلات تقدم الحفظ إذا تم طلبها
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: attendance,
      message: 'تم جلب سجلات الحضور بنجاح'
    });
  } catch (error) {
    console.error('Error fetching khatm attendance:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب سجلات الحضور'
    }, { status: 500 });
  }
}

// POST /api/khatm-attendance - تسجيل حضور طالب في مجلس ختم
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { khatmSessionId, studentId, status, note, imageUrls } = body;

    // التحقق من البيانات المطلوبة
    if (!khatmSessionId || !studentId) {
      return NextResponse.json({
        success: false,
        error: 'معرف مجلس الختم ومعرف الطالب مطلوبان'
      }, { status: 400 });
    }

    // التحقق من وجود مجلس الختم
    const khatmSession = await prisma.khatmSession.findUnique({
      where: { id: parseInt(khatmSessionId) }
    });

    if (!khatmSession) {
      return NextResponse.json({
        success: false,
        error: 'مجلس الختم غير موجود'
      }, { status: 404 });
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return NextResponse.json({
        success: false,
        error: 'الطالب غير موجود'
      }, { status: 404 });
    }

    // التحقق من عدم وجود سجل حضور مسبق
    const existingAttendance = await prisma.khatmSessionAttendance.findUnique({
      where: {
        khatmSessionId_studentId: {
          khatmSessionId: parseInt(khatmSessionId),
          studentId: parseInt(studentId)
        }
      }
    });

    if (existingAttendance) {
      return NextResponse.json({
        success: false,
        error: 'تم تسجيل حضور الطالب مسبقًا في هذا المجلس'
      }, { status: 400 });
    }

    // إنشاء سجل الحضور مع الصور إذا وجدت
    const attendance = await prisma.$transaction(async (tx) => {
      // إنشاء سجل الحضور
      const newAttendance = await tx.khatmSessionAttendance.create({
        data: {
          khatmSessionId: parseInt(khatmSessionId),
          studentId: parseInt(studentId),
          status: status || 'PRESENT',
          note
        }
      });

      // إضافة الصور إذا وجدت
      if (imageUrls && Array.isArray(imageUrls) && imageUrls.length > 0) {
        await tx.attendanceImage.createMany({
          data: imageUrls.map((imageUrl: string) => ({
            khatmSessionAttendanceId: newAttendance.id,
            imageUrl
          }))
        });
      }

      return newAttendance;
    });

    // جلب سجل الحضور مع الصور
    const attendanceWithImages = await prisma.khatmSessionAttendance.findUnique({
      where: { id: attendance.id },
      include: {
        student: true,
        khatmSession: true,
        images: true,
        progressRecords: true
      }
    });

    return NextResponse.json({
      success: true,
      data: attendanceWithImages,
      message: 'تم تسجيل الحضور بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating khatm attendance:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تسجيل الحضور'
    }, { status: 500 });
  }
}

// PUT /api/khatm-attendance - تحديث سجل حضور
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, note, imageUrls } = body;

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف سجل الحضور مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود سجل الحضور
    const existingAttendance = await prisma.khatmSessionAttendance.findUnique({
      where: { id: parseInt(id) },
      include: { images: true }
    });

    if (!existingAttendance) {
      return NextResponse.json({
        success: false,
        error: 'سجل الحضور غير موجود'
      }, { status: 404 });
    }

    // تحديث سجل الحضور مع الصور إذا وجدت
    const attendance = await prisma.$transaction(async (tx) => {
      // تحديث سجل الحضور
      const updatedAttendance = await tx.khatmSessionAttendance.update({
        where: { id: parseInt(id) },
        data: {
          status: status || undefined,
          note: note !== undefined ? note : undefined
        }
      });

      // إذا تم تحديد صور جديدة، نحذف الصور القديمة ونضيف الجديدة
      if (imageUrls && Array.isArray(imageUrls)) {
        // حذف الصور القديمة
        if (existingAttendance.images.length > 0) {
          await tx.attendanceImage.deleteMany({
            where: { khatmSessionAttendanceId: parseInt(id) }
          });
        }

        // إضافة الصور الجديدة
        if (imageUrls.length > 0) {
          await tx.attendanceImage.createMany({
            data: imageUrls.map((imageUrl: string) => ({
              khatmSessionAttendanceId: parseInt(id),
              imageUrl
            }))
          });
        }
      }

      return updatedAttendance;
    });

    // جلب سجل الحضور المحدث مع الصور
    const attendanceWithImages = await prisma.khatmSessionAttendance.findUnique({
      where: { id: attendance.id },
      include: {
        student: true,
        khatmSession: true,
        images: true,
        progressRecords: true
      }
    });

    return NextResponse.json({
      success: true,
      data: attendanceWithImages,
      message: 'تم تحديث سجل الحضور بنجاح'
    });
  } catch (error) {
    console.error('Error updating khatm attendance:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تحديث سجل الحضور'
    }, { status: 500 });
  }
}

// DELETE /api/khatm-attendance - حذف سجل حضور
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف سجل الحضور مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود سجل الحضور
    const existingAttendance = await prisma.khatmSessionAttendance.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingAttendance) {
      return NextResponse.json({
        success: false,
        error: 'سجل الحضور غير موجود'
      }, { status: 404 });
    }

    // حذف سجل الحضور (سيتم حذف الصور تلقائيًا بسبب onDelete: Cascade)
    await prisma.khatmSessionAttendance.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف سجل الحضور بنجاح'
    });
  } catch (error) {
    console.error('Error deleting khatm attendance:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف سجل الحضور'
    }, { status: 500 });
  }
}

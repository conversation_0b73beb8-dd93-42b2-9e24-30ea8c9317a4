import { NextRequest, NextResponse } from "next/server"
import prisma from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { setCookie } from '@/utils/generateToken';
//import { ActivityLogger, ActivityType } from '@/lib/activity-logger';

interface loginUserDto {
    user: string;
    password: string;
}

export async function OPTIONS() {
    return new NextResponse(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Methods': 'POST',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Max-Age': '86400'
        }
    });
}

export async function POST(request: NextRequest) {
    try {
        const body = await request.json() as loginUserDto;

        const user = await prisma.user.findUnique({
            where: {
                username: body.user
            },
            select: {
                id: true,
                username: true,
                password: true,
                role: true,
                roleId: true
            }
        });

        if (!user) {
            return NextResponse.json(
                { message: "فشل المصادقة: اسم المستخدم غير موجود" },
                { status: 400 }
            );
        }

        if (!user.password) {
            return NextResponse.json(
                { message: 'فشل المصادقة: لم يتم تعيين كلمة المرور لهذا المستخدم' },
                { status: 400 }
            );
        }

        const isPasswordMatch = await bcrypt.compare(body.password, user.password);
        if (!isPasswordMatch) {
            return NextResponse.json(
                { message: 'فشل المصادقة: كلمة المرور غير صحيحة' },
                { status: 400 }
            );
        }

        const jwtPayload = {
            id: user.id,
            username: user.username,
            role: user.role || 'USER',
            roleId: user.roleId
        };

        return setCookie(jwtPayload);

    } catch (error: unknown) {
        console.error('Login error:', error);
        return NextResponse.json(
            { message: 'خطأ في الخادم: فشل في معالجة طلب تسجيل الدخول' },
            { status: 500 }
        );
    }
}



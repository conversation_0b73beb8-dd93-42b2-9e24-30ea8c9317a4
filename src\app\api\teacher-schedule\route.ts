import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

// GET /api/teacher-schedule - جلب جدول حصص المعلم المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'TEACHER') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userId = userData.id;

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userId
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // في هذه المرحلة، نفترض أن هناك جدول ClassSchedule في قاعدة البيانات
    // إذا لم يكن موجوداً، يمكننا إنشاء بيانات وهمية للعرض

    // محاولة جلب جدول الحصص الحقيقي
    try {
      const scheduleItems = await prisma.classSchedule.findMany({
        where: {
          teacherSubject: {
            teacherId: teacher.id
          }
        },
        include: {
          classe: true,
          teacherSubject: {
            include: {
              subject: true
            }
          }
        }
      });

      // تنسيق البيانات
      const schedule = scheduleItems.map(item => ({
        id: item.id,
        day: item.day,
        startTime: item.startTime,
        endTime: item.endTime,
        classeId: item.classeId,
        className: item.classe.name,
        subjectId: item.teacherSubject.subjectId,
        subjectName: item.teacherSubject.subject.name
      }));

      return NextResponse.json({
        schedule,
        message: "تم جلب جدول الحصص بنجاح"
      });
    } catch {
      // إذا لم يكن هناك جدول ClassSchedule، نقوم بإنشاء بيانات وهمية
      console.log("Using mock data for schedule");

      // جلب الفصول والمواد التي يدرسها المعلم
      const teacherSubjects = await prisma.teacherSubject.findMany({
        where: {
          teacherId: teacher.id
        },
        include: {
          subject: true,
          classes: {
            include: {
              classe: true
            }
          }
        }
      });

      // إنشاء جدول وهمي
      interface ScheduleItem {
        id: number;
        day: string;
        startTime: string;
        endTime: string;
        classeId: number;
        className: string;
        subjectId: number;
        subjectName: string;
      }

      const mockSchedule: ScheduleItem[] = [];
      const days = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY'];
      const times = [
        { start: '08:00', end: '09:00' },
        { start: '09:15', end: '10:15' },
        { start: '10:30', end: '11:30' },
        { start: '12:00', end: '13:00' },
        { start: '13:15', end: '14:15' }
      ];

      let id = 1;

      // توزيع الحصص على الأيام والأوقات
      teacherSubjects.forEach(ts => {
        ts.classes.forEach(cs => {
          // اختيار يوم وتوقيت عشوائي
          const randomDay = days[Math.floor(Math.random() * days.length)];
          const randomTime = times[Math.floor(Math.random() * times.length)];

          mockSchedule.push({
            id: id++,
            day: randomDay,
            startTime: randomTime.start,
            endTime: randomTime.end,
            classeId: cs.classeId,
            className: cs.classe.name,
            subjectId: ts.subjectId,
            subjectName: ts.subject.name
          });
        });
      });

      return NextResponse.json({
        schedule: mockSchedule,
        message: "تم جلب جدول الحصص بنجاح (بيانات وهمية)"
      });
    }
  } catch (error) {
    console.error('Error fetching teacher schedule:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب جدول الحصص" },
      { status: 500 }
    );
  }
}

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkRoles() {
  try {
    console.log('=== فحص الأدوار في قاعدة البيانات ===');
    
    const roles = await prisma.role.findMany({
      include: {
        _count: {
          select: {
            users: true,
            permissions: true
          }
        }
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`\nعدد الأدوار الموجودة: ${roles.length}\n`);

    roles.forEach((role, index) => {
      console.log(`${index + 1}. الدور: ${role.displayName}`);
      console.log(`   - المعرف: ${role.id}`);
      console.log(`   - الاسم: ${role.name}`);
      console.log(`   - نشط: ${role.isActive ? 'نعم' : 'لا'}`);
      console.log(`   - نظام: ${role.isSystem ? 'نعم' : 'لا'}`);
      console.log(`   - عدد المستخدمين: ${role._count.users}`);
      console.log(`   - عدد الصلاحيات: ${role._count.permissions}`);
      console.log(`   - الوصف: ${role.description || 'لا يوجد'}`);
      console.log('');
    });

    // فحص المستخدمين وأدوارهم
    console.log('=== فحص المستخدمين وأدوارهم ===');
    
    const users = await prisma.user.findMany({
      where: {
        role: 'EMPLOYEE'
      },
      include: {
        userRole: true
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`\nعدد الموظفين: ${users.length}\n`);

    users.forEach((user, index) => {
      console.log(`${index + 1}. المستخدم: ${user.username}`);
      console.log(`   - المعرف: ${user.id}`);
      console.log(`   - الدور الأساسي: ${user.role}`);
      console.log(`   - معرف الدور المخصص: ${user.roleId || 'غير محدد'}`);
      console.log(`   - اسم الدور المخصص: ${user.userRole?.displayName || 'غير محدد'}`);
      console.log('');
    });

  } catch (error) {
    console.error('خطأ في فحص الأدوار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRoles();

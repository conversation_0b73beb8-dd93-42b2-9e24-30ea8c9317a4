"use client"

import * as React from "react"
import { cn } from "@/utils/cn"

type ToastProps = React.HTMLAttributes<HTMLDivElement> & {
  variant?: "default" | "destructive"
  title?: string
  description?: string
}

const ToastContext = React.createContext<{
  toasts: ToastProps[]
  addToast: (toast: ToastProps) => void
  removeToast: (index: number) => void
}>({ toasts: [], addToast: () => {}, removeToast: () => {} })

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<ToastProps[]>([])

  const addToast = React.useCallback((toast: ToastProps) => {
    setToasts((prev) => [...prev, toast])
    setTimeout(() => {
      setToasts((prev) => prev.slice(1))
    }, 5000)
  }, [])

  const removeToast = React.useCallback((index: number) => {
    setToasts((prev) => prev.filter((_, i) => i !== index))
  }, [])

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <div
        className="fixed bottom-0 right-0 z-50 flex flex-col items-end gap-2 p-4"
        dir="rtl"
      >
        {toasts.map((toast, index) => (
          <div
            key={index}
            className={cn(
              "flex w-96 items-center justify-between rounded-lg p-4 text-white shadow-lg",
              toast.variant === "destructive" ? "bg-red-600" : "bg-primary-color",
              toast.className
            )}
            {...toast}
          >
            <div className="flex flex-col gap-1">
              {toast.title && (
                <div className="text-sm font-semibold">{toast.title}</div>
              )}
              {toast.description && (
                <div className="text-sm opacity-90">{toast.description}</div>
              )}
            </div>
            <button
              onClick={() => removeToast(index)}
              className="text-white/80 hover:text-white"
            >
              ×
            </button>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  )
}

export function useToast() {
  const context = React.useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

// هذا المكون يستخدم لعرض الإشعارات
export function Toast({ ...props }: ToastProps) {
  const { addToast } = useToast()

  // استخدام useEffect لضمان تنفيذ الكود مرة واحدة فقط بعد التحميل
  React.useEffect(() => {
    addToast(props)
  }, [addToast, props])

  // هذا المكون لا يعرض أي شيء بنفسه
  return null
}

// تصدير دالة toast للاستخدام المباشر في الكود
// هذه الدالة لا تستخدم hooks مباشرة لتجنب الأخطاء
export const toast = (props: ToastProps) => {
  // الوصول إلى المتغير العام للإشعارات
  if (typeof document !== 'undefined') {
    // إنشاء حدث مخصص لإضافة الإشعار
    const event = new CustomEvent('toast', { detail: props });
    document.dispatchEvent(event);
  }
}
import { NextRequest, NextResponse } from 'next/server';
import prisma from '../../../lib/prisma';
import { Prisma, UserRole } from '@prisma/client';
import { ActivityLogger, ActivityType } from '../../../lib/activity-logger';

// GET /api/attendance
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];
    const classeId = searchParams.get('classeId');
    const hisass = searchParams.get('hisass') ? parseInt(searchParams.get('hisass')!) : undefined;

    const attendance = await prisma.attendance.findMany({
      where: {
        date: {
          gte: new Date(`${date}T00:00:00Z`),
          lt: new Date(`${date}T23:59:59Z`)
        },
        hisass: hisass,
        student: classeId ? {
          classeId: parseInt(classeId)
        } : undefined
      },
      include: {
        student: {
          include: {
            classe: true
          }
        }
      },
      orderBy: {
        student: {
          name: 'asc'
        }
      }
    });

    // Get all students from the specified class who don't have attendance records
    const studentsWithoutAttendance = await prisma.student.findMany({
      where: {
        classeId: classeId ? parseInt(classeId) : undefined,
        NOT: {
          attendance: {
            some: {
              AND: [
                {
                  date: {
                    gte: new Date(`${date}T00:00:00Z`),
                    lt: new Date(`${date}T23:59:59Z`)
                  }
                },
                { hisass: hisass }
              ]
            }
          }
        }
      },
      include: {
        classe: true
      }
    });

    // Combine both results
    const result = [
      ...attendance.map(record => ({
        id: record.id,
        student: record.student,
        status: record.status,
        date: record.date
      })),
      ...studentsWithoutAttendance.map(student => ({
        id: null,
        student: student,
        status: null,
        date: new Date(`${date}T00:00:00Z`)
      }))
    ];

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching attendance:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب سجل الحضور' },
      { status: 500 }
    );
  }
}

// POST /api/attendance
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, date, status, hisass, teacherId, imageUrls } = body;

    if (!studentId || !date || !status) {
      return NextResponse.json(
        { error: 'يرجى ملء جميع الحقول المطلوبة', success: false },
        { status: 400 }
      );
    }

    try {
      // التحقق من أن الطالب موجود
      const student = await prisma.student.findUnique({
        where: { id: studentId },
        include: { classe: true }
      });

      if (!student) {
        return NextResponse.json(
          { error: 'الطالب غير موجود', success: false },
          { status: 404 }
        );
      }

      // إذا تم تحديد معرف المعلم، تحقق من أن المعلم يدرس لهذا الفصل
      if (teacherId) {
        // التحقق من أن المعلم يدرس لهذا الفصل
        const teacherHasClass = student.classeId ? await prisma.classSubject.findFirst({
          where: {
            classeId: student.classeId ?? undefined,
            teacherSubject: {
              teacherId: parseInt(teacherId)
            }
          }
        }) : null;

        if (!teacherHasClass) {
          return NextResponse.json(
            { error: 'ليس لديك صلاحية تسجيل الحضور لهذا الفصل', success: false },
            { status: 403 }
          );
        }
      }

      // التحقق من وجود سجل حضور سابق
      const existingAttendance = await prisma.attendance.findUnique({
        where: {
          studentId_date_hisass: {
            studentId: studentId,
            date: new Date(date),
            hisass: hisass
          }
        }
      });

      let result: { id: number; studentId: number; date: Date; status: string; hisass: number };

      if (existingAttendance) {
        // تحديث السجل الموجود
        result = await prisma.attendance.update({
          where: {
            id: existingAttendance.id
          },
          data: {
            status: status
          }
        });

        // إضافة الصور إذا وجدت
        if (imageUrls && Array.isArray(imageUrls) && imageUrls.length > 0) {
          await prisma.attendanceImage.createMany({
            data: imageUrls.map((imageUrl: string) => ({
              attendanceId: result.id,
              imageUrl
            }))
          });
        }
      } else {
        // إنشاء سجل جديد
        result = await prisma.attendance.create({
          data: {
            studentId: studentId,
            date: new Date(date),
            status: status,
            hisass: hisass
          }
        });

        // إضافة الصور إذا وجدت
        if (imageUrls && Array.isArray(imageUrls) && imageUrls.length > 0) {
          await prisma.attendanceImage.createMany({
            data: imageUrls.map((imageUrl: string) => ({
              attendanceId: result.id,
              imageUrl
            }))
          });
        }
      }

      // جلب سجل الحضور مع الصور
      const attendanceWithImages = await prisma.attendance.findUnique({
        where: { id: result.id },
        include: {
          student: true,
          images: true
        }
      });

      // تسجيل نشاط تسجيل الحضور
      try {
        // محاولة الحصول على معرف المستخدم الحالي
        const adminUser = await prisma.user.findFirst({
          where: { role: 'ADMIN' }
        });

        if (adminUser) {
          await ActivityLogger.log(
            adminUser.id,
            ActivityType.ATTENDANCE,
            `تسجيل ${status === 'PRESENT' ? 'حضور' : status === 'ABSENT' ? 'غياب' : 'غياب بعذر'} للطالب ${student.name} في الحصة ${hisass}`
          );
        }
      } catch (error) {
        console.error('خطأ في تسجيل نشاط الحضور:', error);
        // لا نريد أن يفشل تسجيل الحضور إذا فشل تسجيل النشاط
      }

      // إرسال إشعارات للغياب
      if (status === 'ABSENT' || status === 'EXCUSED') {
        try {
          // إرسال إشعار للمعلمين
          // البحث عن المعلمين الذين يدرسون في الفصل
          const classSubjects = await prisma.classSubject.findMany({
            where: {
              classeId: student.classeId || undefined
            },
            include: {
              teacherSubject: {
                include: {
                  teacher: {
                    include: {
                      user: true
                    }
                  }
                }
              }
            }
          });

          // استخراج معرفات المستخدمين للمعلمين
          const teacherUsers = classSubjects
            .map(cs => cs.teacherSubject.teacher.user)
            .filter(user => user !== null);

          // إرسال إشعار لولي الأمر إذا كان موجوداً
          // نحتاج إلى البحث عن المستخدم المرتبط بالولي عن طريق الاسم
          let parentUser = null;
          if (student.guardianId) {
            const parent = await prisma.parent.findUnique({
              where: { id: student.guardianId }
            });

            if (parent) {
              // البحث عن مستخدم بنفس اسم الولي ودور PARENT
              const possibleParentUser = await prisma.user.findFirst({
                where: {
                  role: UserRole.PARENT,
                  profile: {
                    name: parent.name
                  }
                }
              });

              if (possibleParentUser) {
                parentUser = possibleParentUser;
              }
            }
          }

          // إنشاء محتوى الإشعار
          const notificationTitle = `تسجيل ${status === 'ABSENT' ? 'غياب' : 'غياب بعذر'} للطالب ${student.name}`;
          const notificationContent = `تم تسجيل ${status === 'ABSENT' ? 'غياب' : 'غياب بعذر'} للطالب ${student.name} في الحصة ${hisass} بتاريخ ${new Date(date).toLocaleDateString('fr-FR')}${student.classe ? ` في قسم ${student.classe.name}` : ''}`;

          // إرسال إشعارات للمعلمين
          for (const teacher of teacherUsers) {
            await prisma.notification.create({
              data: {
                title: notificationTitle,
                content: notificationContent,
                type: 'ATTENDANCE',
                userId: teacher.id,
                relatedId: result.id
              }
            });
          }

          // إرسال إشعار لولي الأمر
          if (parentUser) {
            await prisma.notification.create({
              data: {
                title: notificationTitle,
                content: notificationContent,
                type: 'ATTENDANCE',
                userId: parentUser.id,
                relatedId: result.id
              }
            });
          }
        } catch (notificationError) {
          console.error('خطأ في إرسال إشعارات الغياب:', notificationError);
          // لا نريد أن يفشل تسجيل الحضور إذا فشل إرسال الإشعارات
        }
      }

      return NextResponse.json({
        data: attendanceWithImages,
        success: true,
        message: 'تم تسجيل الحضور بنجاح'
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        return NextResponse.json(
          { error: 'خطأ في قاعدة البيانات', success: false },
          { status: 500 }
        );
      }
      throw error;
    }
  } catch (error) {
    console.error('Error creating/updating attendance:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث سجل الحضور', success: false },
      { status: 500 }
    );
  }
}
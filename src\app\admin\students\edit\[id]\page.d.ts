import { Metadata } from 'next';

export interface PageProps {
  params: {
    id: string;
  };
  searchParams: { [key: string]: string | string[] | undefined };
}

export interface GenerateMetadataProps {
  params: PageProps['params'];
  searchParams: PageProps['searchParams'];
}

export const generateMetadata: (props: GenerateMetadataProps) => Metadata | Promise<Metadata>;

export default function Page(props: PageProps): React.ReactElement;

'use client';

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
  Fa<PERSON><PERSON><PERSON>, 
  Fa<PERSON><PERSON>Shield, 
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>, 
  FaChalkboard<PERSON><PERSON>er, 
  Fa<PERSON>ser<PERSON><PERSON>uate, 
  <PERSON>a<PERSON>ser<PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaSearch
} from 'react-icons/fa';

interface GroupOption {
  type: string;
  label: string;
  description: string;
  count: number;
  targetRole?: string;
  icon: string;
}

interface User {
  id: number;
  name: string;
  username: string;
  role: string;
  displayText: string;
}

interface GroupSelectorProps {
  onSelectionChange: (selection: {
    groupType: string;
    targetRole?: string;
    targetUserIds?: number[];
    recipientCount: number;
  }) => void;
  disabled?: boolean;
}

const GroupSelector: React.FC<GroupSelectorProps> = ({ onSelectionChange, disabled = false }) => {
  const [groupOptions, setGroupOptions] = useState<GroupOption[]>([]);
  const [selectedGroupType, setSelectedGroupType] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSearching, setIsSearching] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previewData, setPreviewData] = useState<any>(null);

  // أيقونات المجموعات
  const iconMap: Record<string, React.ReactNode> = {
    FaUsers: <FaUsers />,
    FaUserShield: <FaUserShield />,
    FaUserTie: <FaUserTie />,
    FaChalkboardTeacher: <FaChalkboardTeacher />,
    FaUserGraduate: <FaUserGraduate />,
    FaUserFriends: <FaUserFriends />,
    FaUserCheck: <FaUserCheck />
  };

  // جلب خيارات المجموعات
  useEffect(() => {
    fetchGroupOptions();
  }, []);

  const fetchGroupOptions = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get('/api/user-groups');
      setGroupOptions(response.data.groupOptions);
    } catch (error) {
      console.error('Error fetching group options:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // البحث في المستخدمين
  const searchUsers = async (term: string) => {
    if (!term.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const response = await axios.get(`/api/user-groups/preview?search=${encodeURIComponent(term)}&limit=20`);
      setSearchResults(response.data.users);
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // معاينة المستلمين
  const previewRecipients = async () => {
    if (!selectedGroupType) return;

    try {
      const requestData: any = { groupType: selectedGroupType };
      
      if (selectedGroupType === 'BY_ROLE') {
        requestData.targetRole = selectedRole;
      } else if (selectedGroupType === 'CUSTOM_SELECTION') {
        requestData.targetUserIds = selectedUsers;
      }

      const response = await axios.post('/api/user-groups/preview', requestData);
      setPreviewData(response.data);
      setShowPreview(true);
    } catch (error) {
      console.error('Error previewing recipients:', error);
    }
  };

  // تحديث الاختيار
  useEffect(() => {
    if (!selectedGroupType) return;

    let recipientCount = 0;
    const selection: any = { groupType: selectedGroupType };

    if (selectedGroupType === 'ALL_USERS') {
      const allUsersOption = groupOptions.find(opt => opt.type === 'ALL_USERS');
      recipientCount = allUsersOption?.count || 0;
    } else if (selectedGroupType === 'BY_ROLE' && selectedRole) {
      const roleOption = groupOptions.find(opt => opt.type === 'BY_ROLE' && opt.targetRole === selectedRole);
      recipientCount = roleOption?.count || 0;
      selection.targetRole = selectedRole;
    } else if (selectedGroupType === 'CUSTOM_SELECTION') {
      recipientCount = selectedUsers.length;
      selection.targetUserIds = selectedUsers;
    }

    selection.recipientCount = recipientCount;
    onSelectionChange(selection);
  }, [selectedGroupType, selectedRole, selectedUsers, groupOptions]);

  // تحديث البحث
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (selectedGroupType === 'CUSTOM_SELECTION') {
        searchUsers(searchTerm);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, selectedGroupType]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <FaSpinner className="animate-spin text-2xl text-[var(--primary-color)]" />
        <span className="mr-2">جاري تحميل خيارات المجموعات...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* اختيار نوع المجموعة */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          اختر المجموعة المستهدفة <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {groupOptions.map((option) => (
            <div
              key={`${option.type}-${option.targetRole || 'default'}`}
              className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all ${
                selectedGroupType === option.type && 
                (option.type !== 'BY_ROLE' || selectedRole === option.targetRole)
                  ? 'border-[var(--primary-color)] bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => {
                if (disabled) return;
                setSelectedGroupType(option.type);
                if (option.type === 'BY_ROLE') {
                  setSelectedRole(option.targetRole || '');
                } else {
                  setSelectedRole('');
                }
                setSelectedUsers([]);
                setSearchTerm('');
                setSearchResults([]);
                setShowPreview(false);
              }}
            >
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="text-2xl text-[var(--primary-color)]">
                  {iconMap[option.icon]}
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{option.label}</h3>
                  <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-sm font-medium text-[var(--primary-color)]">
                      {option.count} مستخدم
                    </span>
                    {selectedGroupType === option.type && 
                     (option.type !== 'BY_ROLE' || selectedRole === option.targetRole) && (
                      <div className="w-4 h-4 bg-[var(--primary-color)] rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* البحث والاختيار المخصص */}
      {selectedGroupType === 'CUSTOM_SELECTION' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              البحث عن المستخدمين
            </label>
            <div className="relative">
              <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="ابحث بالاسم أو اسم المستخدم..."
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                disabled={disabled}
              />
              {isSearching && (
                <FaSpinner className="absolute left-3 top-1/2 transform -translate-y-1/2 animate-spin text-gray-400" />
              )}
            </div>
          </div>

          {/* نتائج البحث */}
          {searchResults.length > 0 && (
            <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
              {searchResults.map((user) => (
                <div
                  key={user.id}
                  className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                    selectedUsers.includes(user.id) ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => {
                    if (disabled) return;
                    if (selectedUsers.includes(user.id)) {
                      setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                    } else {
                      setSelectedUsers([...selectedUsers, user.id]);
                    }
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-600">@{user.username}</div>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="text-xs bg-gray-100 px-2 py-1 rounded">{user.role}</span>
                      {selectedUsers.includes(user.id) && (
                        <div className="w-4 h-4 bg-[var(--primary-color)] rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* المستخدمين المختارين */}
          {selectedUsers.length > 0 && (
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">
                المستخدمين المختارين ({selectedUsers.length})
              </div>
              <div className="flex flex-wrap gap-2">
                {searchResults
                  .filter(user => selectedUsers.includes(user.id))
                  .map((user) => (
                    <span
                      key={user.id}
                      className="inline-flex items-center px-3 py-1 bg-[var(--primary-color)] text-white text-sm rounded-full"
                    >
                      {user.name}
                      <button
                        onClick={() => setSelectedUsers(selectedUsers.filter(id => id !== user.id))}
                        className="mr-2 text-white hover:text-gray-200"
                        disabled={disabled}
                      >
                        ×
                      </button>
                    </span>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* زر المعاينة */}
      {selectedGroupType && (
        <div className="flex justify-between items-center">
          <button
            onClick={previewRecipients}
            className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            disabled={disabled}
          >
            <FaEye className="ml-2" />
            معاينة المستلمين
          </button>
          
          {previewData && (
            <div className="text-sm text-gray-600">
              سيتم إرسال الإشعار لـ <span className="font-bold text-[var(--primary-color)]">{previewData.totalCount}</span> مستلم
            </div>
          )}
        </div>
      )}

      {/* نافذة المعاينة */}
      {showPreview && previewData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">معاينة المستلمين</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-[var(--primary-color)]">{previewData.totalCount}</div>
                <div className="text-sm text-gray-600">إجمالي المستلمين</div>
              </div>
              
              {previewData.recipients.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">عينة من المستلمين:</h4>
                  <div className="space-y-2">
                    {previewData.recipients.map((recipient: any) => (
                      <div key={recipient.id} className="flex items-center p-2 bg-gray-50 rounded">
                        <div>
                          <div className="font-medium">{recipient.name}</div>
                          <div className="text-sm text-gray-600">@{recipient.username}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {previewData.hasMore && (
                    <div className="text-sm text-gray-600 mt-2 text-center">
                      وآخرين... (إجمالي {previewData.totalCount} مستلم)
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroupSelector;

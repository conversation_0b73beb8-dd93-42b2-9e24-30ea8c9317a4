import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Check } from 'lucide-react';
import Image from 'next/image';
import AchievementTemplate from './AchievementTemplate';
import ExcellenceTemplate from './ExcellenceTemplate';
import AppreciationTemplate from './AppreciationTemplate';
import GraduationTemplate from './GraduationTemplate';

export type TemplateType = 'ACHIEVEMENT' | 'EXCELLENCE' | 'APPRECIATION' | 'GRADUATION' | 'CUSTOM';

export interface CertificateData {
  id?: number;
  title: string;
  description: string;
  templateUrl?: string;
  type: string;
  student?: {
    id: number;
    name: string;
    classe?: {
      name: string;
    } | null;
  };
  issueDate?: string;
}

interface TemplatePreviewProps {
  type: TemplateType;
  selected: boolean;
  onClick: () => void;
  previewImage: string;
  title: string;
}

export function TemplatePreview({
  type, // مطلوب للتوافق مع واجهة البرمجة، حتى لو لم يتم استخدامه مباشرة في هذا المكون
  selected,
  onClick,
  previewImage,
  title
}: TemplatePreviewProps) {
  return (
    <Card
      className={`relative cursor-pointer transition-all hover:shadow-md ${
        selected ? 'ring-2 ring-[var(--primary-color)] shadow-lg' : ''
      }`}
      onClick={onClick}
    >
      {selected && (
        <div className="absolute top-2 right-2 bg-[var(--primary-color)] text-white rounded-full p-1 z-10">
          <Check className="h-4 w-4" />
        </div>
      )}
      <CardContent className="p-3">
        <div className="relative w-full h-40 mb-2">
          <Image
            src={previewImage}
            alt={title}
            fill
            style={{ objectFit: 'cover' }}
            className="rounded-md"
          />
        </div>
        <p className="text-center text-sm font-medium">{title}</p>
      </CardContent>
    </Card>
  );
}

interface CertificateTemplatesProps {
  certificateData: CertificateData;
  selectedTemplate: TemplateType;
  onSelectTemplate: (template: TemplateType) => void;
}

export default function CertificateTemplates({
  certificateData,
  selectedTemplate,
  onSelectTemplate
}: CertificateTemplatesProps) {
  const templates = [
    {
      type: 'ACHIEVEMENT' as TemplateType,
      title: 'شهادة إنجاز',
      previewImage: '/certificates/achievement-preview.svg'
    },
    {
      type: 'EXCELLENCE' as TemplateType,
      title: 'شهادة تفوق',
      previewImage: '/certificates/excellence-preview.svg'
    },
    {
      type: 'APPRECIATION' as TemplateType,
      title: 'شهادة تقدير',
      previewImage: '/certificates/appreciation-preview.svg'
    },
    {
      type: 'GRADUATION' as TemplateType,
      title: 'شهادة تخرج',
      previewImage: '/certificates/graduation-preview.svg'
    },
    {
      type: 'CUSTOM' as TemplateType,
      title: 'قالب مخصص',
      previewImage: certificateData.templateUrl || '/certificates/custom-preview.svg'
    }
  ];

  const renderSelectedTemplate = () => {
    switch (selectedTemplate) {
      case 'ACHIEVEMENT':
        return <AchievementTemplate certificateData={certificateData} />;
      case 'EXCELLENCE':
        return <ExcellenceTemplate certificateData={certificateData} />;
      case 'APPRECIATION':
        return <AppreciationTemplate certificateData={certificateData} />;
      case 'GRADUATION':
        return <GraduationTemplate certificateData={certificateData} />;
      case 'CUSTOM':
        return (
          <div className="relative bg-white p-8 rounded-lg shadow-md">
            {certificateData.templateUrl ? (
              <div className="absolute inset-0 z-0 overflow-hidden rounded-lg">
                <div className="relative w-full h-full">
                  <Image
                    src={certificateData.templateUrl}
                    alt={certificateData.title}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                </div>
              </div>
            ) : (
              <div className="absolute inset-0 z-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg" />
            )}

            <div className="relative z-10 min-h-[500px] flex flex-col items-center justify-center text-center p-8">
              <div className="bg-white/80 p-6 rounded-lg backdrop-blur-sm">
                <h1 className="text-3xl font-bold mb-4">{certificateData.title}</h1>

                {certificateData.student && (
                  <div className="my-8">
                    <p className="text-lg mb-2">تم منح هذه الشهادة إلى:</p>
                    <h2 className="text-2xl font-bold text-[var(--primary-color)] mb-1">{certificateData.student.name}</h2>
                    {certificateData.student.classe && (
                      <p className="text-gray-600">{certificateData.student.classe.name}</p>
                    )}
                  </div>
                )}

                <p className="text-lg my-4">{certificateData.description}</p>

                {certificateData.issueDate && (
                  <div className="mt-8">
                    <p className="text-sm text-gray-600">
                      تاريخ الإصدار: {new Date(certificateData.issueDate).toLocaleDateString('fr-FR', {
                        year: 'numeric', month: 'long', day: 'numeric'
                      })}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {templates.map((template) => (
          <TemplatePreview
            key={template.type}
            type={template.type}
            selected={selectedTemplate === template.type}
            onClick={() => onSelectTemplate(template.type)}
            previewImage={template.previewImage}
            title={template.title}
          />
        ))}
      </div>

      {/* معاينة مصغرة للشهادة في نافذة الإضافة/التعديل */}
      <div className="mt-4 border rounded-lg p-4 overflow-hidden">
        <h3 className="text-lg font-medium mb-2">معاينة الشهادة</h3>
        <div className="transform scale-75 origin-top-right -mt-10 -mb-10">
          {renderSelectedTemplate()}
        </div>
      </div>
    </div>
  );
}

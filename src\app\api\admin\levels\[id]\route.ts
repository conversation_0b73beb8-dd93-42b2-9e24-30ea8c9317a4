import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/admin/levels/[id] - جلب مستوى معين
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المستوى غير صالح" },
        { status: 400 }
      );
    }

    const level = await prisma.level.findUnique({
      where: { id },
      include: {
        subjects: {
          orderBy: {
            name: 'asc'
          }
        }
      }
    });

    if (!level) {
      return NextResponse.json(
        { message: "المستوى غير موجود" },
        { status: 404 }
      );
    }

    return NextResponse.json(level);
  } catch (error) {
    console.error("Error fetching level:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب المستوى" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/levels/[id] - تحديث مستوى معين
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المستوى غير صالح" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, description, order } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير اسم المستوى بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستوى
    const level = await prisma.level.findUnique({
      where: { id },
    });

    if (!level) {
      return NextResponse.json(
        { message: "المستوى غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مستوى آخر بنفس الاسم
    const existingLevel = await prisma.level.findFirst({
      where: {
        name,
        NOT: { id },
      },
    });

    if (existingLevel) {
      return NextResponse.json(
        { message: "يوجد مستوى آخر بهذا الاسم" },
        { status: 400 }
      );
    }

    const updatedLevel = await prisma.level.update({
      where: { id },
      data: {
        name,
        description: description || null,
        order: order || level.order,
      },
    });

    return NextResponse.json(updatedLevel);
  } catch (error) {
    console.error("Error updating level:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث المستوى" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/levels/[id] - حذف مستوى معين
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المستوى غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستوى
    const level = await prisma.level.findUnique({
      where: { id },
      include: {
        subjects: true
      }
    });

    if (!level) {
      return NextResponse.json(
        { message: "المستوى غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مواد مرتبطة بالمستوى
    if (level.subjects.length > 0) {
      return NextResponse.json(
        { message: "لا يمكن حذف المستوى لأنه يحتوي على مواد" },
        { status: 400 }
      );
    }

    await prisma.level.delete({
      where: { id },
    });

    return NextResponse.json(
      { message: "تم حذف المستوى بنجاح" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting level:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف المستوى" },
      { status: 500 }
    );
  }
}

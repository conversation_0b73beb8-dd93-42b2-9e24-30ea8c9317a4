'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { toast } from 'react-toastify'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'
import {
  FaWallet,
  FaMoneyBillWave,
  FaArrowUp,
  FaArrowDown,
  FaSearch,
  FaFilter,
  FaCalendarAlt,
  FaPlus
} from 'react-icons/fa'

interface Treasury {
  id: number
  balance: number
  totalIncome: number
  totalExpense: number
  createdAt: string
  updatedAt: string
}

interface Transaction {
  id: string
  type: 'income' | 'expense'
  description: string
  amount: number
  date: string
  treasuryId: number
}

interface TransactionsResponse {
  transactions: Transaction[]
  pagination: {
    total: number
    pages: number
    page: number
    limit: number
  }
}

interface TreasuryResponse {
  treasury: Treasury
  monthlyStats: {
    income: number
    expense: number
  }
  recentTransactions: Transaction[]
}

export default function TreasuryPage() {
  const [transactionType, setTransactionType] = useState('all')
  const [monthYear, setMonthYear] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [treasury, setTreasury] = useState<Treasury | null>(null)
  const [monthlyStats, setMonthlyStats] = useState({ income: 0, expense: 0 })
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    page: 1,
    limit: 10
  })
  const [loading, setLoading] = useState(true)
  const [isIncomeDialogOpen, setIsIncomeDialogOpen] = useState(false)
  const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false)
  const [incomeFormData, setIncomeFormData] = useState({
    source: '',
    amount: ''
  })
  const [expenseFormData, setExpenseFormData] = useState({
    purpose: '',
    amount: ''
  })

  // جلب معلومات الخزينة
  const fetchTreasuryInfo = async () => {
    try {
      const response = await fetch('/api/treasury')

      if (!response.ok) {
        throw new Error('فشل في جلب معلومات الخزينة')
      }

      const data: TreasuryResponse = await response.json()
      setTreasury(data.treasury)
      setMonthlyStats(data.monthlyStats)
    } catch (error) {
      console.error('خطأ في جلب معلومات الخزينة:', error)
      toast.error('فشل في جلب معلومات الخزينة')
    }
  }

  // جلب المعاملات
  const fetchTransactions = async () => {
    try {
      setLoading(true)

      // استخراج الشهر والسنة من القيمة المحددة
      let month = ''
      let year = ''
      if (monthYear) {
        const [yearStr, monthStr] = monthYear.split('-')
        month = monthStr
        year = yearStr
      }

      const response = await fetch(
        `/api/transactions?query=${searchQuery}&type=${transactionType}&month=${month}&year=${year}&page=${pagination.page}&limit=${pagination.limit}`
      )

      if (!response.ok) {
        throw new Error('فشل في جلب المعاملات')
      }

      const data: TransactionsResponse = await response.json()
      setTransactions(data.transactions)
      setPagination(data.pagination)
    } catch (error) {
      console.error('خطأ في جلب المعاملات:', error)
      toast.error('فشل في جلب المعاملات')
    } finally {
      setLoading(false)
    }
  }

  // إضافة مدخول جديد
  const handleAddIncome = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (!incomeFormData.source) {
        toast.error('الرجاء إدخال مصدر المدخول')
        return
      }

      if (!incomeFormData.amount || isNaN(parseFloat(incomeFormData.amount)) || parseFloat(incomeFormData.amount) <= 0) {
        toast.error('الرجاء إدخال مبلغ صحيح')
        return
      }

      // تحويل المبلغ إلى رقم
      const amount = parseFloat(incomeFormData.amount)

      // طباعة البيانات المرسلة للتأكد منها
      console.log('بيانات المدخول المرسلة:', {
        source: incomeFormData.source,
        amount: amount
      })

      const response = await fetch('/api/incomes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          source: incomeFormData.source,
          amount: amount
        })
      })

      const responseText = await response.text()
      console.log('استجابة الخادم:', responseText)

      if (!response.ok) {
        let errorMessage = 'فشل في إضافة المدخول'
        try {
          const errorData = JSON.parse(responseText)
          errorMessage = errorData.error || errorMessage
        } catch {
          // إذا لم يكن النص JSON صالحاً
        }
        throw new Error(errorMessage)
      }

      toast.success('تم إضافة المدخول بنجاح')
      setIsIncomeDialogOpen(false)
      setIncomeFormData({ source: '', amount: '' })
      fetchTreasuryInfo()
      fetchTransactions()
    } catch (error) {
      console.error('خطأ في إضافة المدخول:', error)
      toast.error(error instanceof Error ? error.message : 'فشل في إضافة المدخول')
    }
  }

  // إضافة مصروف جديد
  const handleAddExpense = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (!expenseFormData.purpose) {
        toast.error('الرجاء إدخال غرض المصروف')
        return
      }

      if (!expenseFormData.amount || isNaN(parseFloat(expenseFormData.amount)) || parseFloat(expenseFormData.amount) <= 0) {
        toast.error('الرجاء إدخال مبلغ صحيح')
        return
      }

      // تحويل المبلغ إلى رقم
      const amount = parseFloat(expenseFormData.amount)

      // طباعة البيانات المرسلة للتأكد منها
      console.log('بيانات المصروف المرسلة:', {
        purpose: expenseFormData.purpose,
        amount: amount
      })

      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          purpose: expenseFormData.purpose,
          amount: amount
        })
      })

      const responseText = await response.text()
      console.log('استجابة الخادم:', responseText)

      if (!response.ok) {
        let errorMessage = 'فشل في إضافة المصروف'
        try {
          const errorData = JSON.parse(responseText)
          errorMessage = errorData.error || errorMessage
        } catch {
          // إذا لم يكن النص JSON صالحاً
        }
        throw new Error(errorMessage)
      }

      toast.success('تم إضافة المصروف بنجاح')
      setIsExpenseDialogOpen(false)
      setExpenseFormData({ purpose: '', amount: '' })
      fetchTreasuryInfo()
      fetchTransactions()
    } catch (error) {
      console.error('خطأ في إضافة المصروف:', error)
      toast.error(error instanceof Error ? error.message : 'فشل في إضافة المصروف')
    }
  }

  // تغيير الصفحة
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination({ ...pagination, page: newPage })
    }
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy/MM/dd', { locale: ar })
    } catch {
      return dateString
    }
  }

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchTreasuryInfo()
  }, [])

  // جلب المعاملات عند تغيير المعايير
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      fetchTransactions()
    }, 500)

    return () => clearTimeout(delayDebounceFn)
  }, [transactionType, monthYear, searchQuery, pagination.page],)

  return (
    <OptimizedProtectedRoute requiredPermission="admin.treasury.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaWallet className="text-[var(--primary-color)]" />
          الخزينة
        </h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-green-100 hover:shadow-md transition-shadow duration-300">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-gray-800">الرصيد الحالي</h3>
            <FaWallet className="text-[var(--primary-color)] text-xl" />
          </div>
          <p className="text-3xl font-bold text-[var(--primary-color)]">{treasury?.balance || 0} د.ج</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-blue-100 hover:shadow-md transition-shadow duration-300">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-gray-800">إجمالي المداخيل</h3>
            <FaArrowUp className="text-blue-600 text-xl" />
          </div>
          <p className="text-3xl font-bold text-blue-600">{monthlyStats.income} د.ج</p>
          <p className="text-sm text-gray-600 mt-2">هذا الشهر</p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-red-100 hover:shadow-md transition-shadow duration-300">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-lg font-semibold text-gray-800">إجمالي المصاريف</h3>
            <FaArrowDown className="text-red-600 text-xl" />
          </div>
          <p className="text-3xl font-bold text-red-600">{monthlyStats.expense} د.ج</p>
          <p className="text-sm text-gray-600 mt-2">هذا الشهر</p>
        </div>
      </div>

      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow-sm border border-green-100 mb-4">
        <h2 className="text-xl font-bold text-[var(--primary-color)] flex items-center gap-2">
          <FaMoneyBillWave className="text-[var(--primary-color)]" />
          سجل المعاملات
        </h2>
        <QuickActionButtons
          entityType="treasury"
          actions={[
            {
              key: 'income',
              label: 'تسجيل مدخول',
              icon: <FaArrowUp />,
              onClick: () => setIsIncomeDialogOpen(true),
              variant: 'info',
              permission: 'admin.treasury.income'
            },
            {
              key: 'expense',
              label: 'تسجيل مصروف',
              icon: <FaArrowDown />,
              onClick: () => setIsExpenseDialogOpen(true),
              variant: 'danger',
              permission: 'admin.treasury.expense'
            }
          ]}
          className="flex gap-3"
        />

        {/* نافذة إضافة مدخول */}
        <AnimatedDialog
          isOpen={isIncomeDialogOpen}
          onClose={() => setIsIncomeDialogOpen(false)}
          title="إضافة مدخول جديد"
          variant="primary"
          footer={
            <Button
              type="submit"
              form="income-form"
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
            >
              <FaPlus size={14} />
              <span>إضافة</span>
            </Button>
          }
        >
          <form id="income-form" onSubmit={handleAddIncome}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="source" className="text-right col-span-1">
                  المصدر <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="source"
                  value={incomeFormData.source}
                  onChange={(e) => setIncomeFormData({ ...incomeFormData, source: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="income-amount" className="text-right col-span-1">
                  المبلغ <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="income-amount"
                  type="number"
                  value={incomeFormData.amount}
                  onChange={(e) => setIncomeFormData({ ...incomeFormData, amount: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
            </div>
          </form>
        </AnimatedDialog>

        {/* نافذة إضافة مصروف */}
        <AnimatedDialog
          isOpen={isExpenseDialogOpen}
          onClose={() => setIsExpenseDialogOpen(false)}
          title="إضافة مصروف جديد"
          variant="primary"
          footer={
            <Button
              type="submit"
              form="expense-form"
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
            >
              <FaPlus size={14} />
              <span>إضافة</span>
            </Button>
          }
        >
          <form id="expense-form" onSubmit={handleAddExpense}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="purpose" className="text-right col-span-1">
                  الغرض <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="purpose"
                  value={expenseFormData.purpose}
                  onChange={(e) => setExpenseFormData({ ...expenseFormData, purpose: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expense-amount" className="text-right col-span-1">
                  المبلغ <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="expense-amount"
                  type="number"
                  value={expenseFormData.amount}
                  onChange={(e) => setExpenseFormData({ ...expenseFormData, amount: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
            </div>
          </form>
        </AnimatedDialog>
      </div>

      <div className="flex flex-col md:flex-row items-center gap-4 bg-white p-4 rounded-lg shadow-sm border border-green-100 mb-4">
        <div className="w-full md:w-48 flex items-center gap-2">
          <FaFilter className="text-[var(--primary-color)]" />
          <Select value={transactionType} onValueChange={setTransactionType}>
            <SelectTrigger className="border-[var(--primary-color)] focus:border-[var(--secondary-color)]">
              <SelectValue placeholder="نوع المعاملة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">الكل</SelectItem>
              <SelectItem value="income">مداخيل</SelectItem>
              <SelectItem value="expense">مصاريف</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="w-full md:w-48 flex items-center gap-2">
          <FaCalendarAlt className="text-[var(--primary-color)]" />
          <Input
            type="month"
            value={monthYear}
            onChange={(e) => setMonthYear(e.target.value)}
            className="w-full border-[var(--primary-color)] focus:border-[var(--secondary-color)]"
          />
        </div>

        <div className="w-full md:flex-1 flex items-center gap-2">
          <FaSearch className="text-[var(--primary-color)]" />
          <Input
            type="text"
            placeholder="بحث..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full border-[var(--primary-color)] focus:border-[var(--secondary-color)]"
          />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-green-100 overflow-hidden">
        <Table>
          <TableHeader className="bg-[var(--primary-color)]">
            <TableRow>
              <TableHead className="font-bold text-white">رقم المعاملة</TableHead>
              <TableHead className="font-bold text-white">النوع</TableHead>
              <TableHead className="font-bold text-white">المصدر/الوجهة</TableHead>
              <TableHead className="font-bold text-white">المبلغ</TableHead>
              <TableHead className="font-bold text-white">التاريخ</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                    <span className="mr-2">جاري التحميل...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : transactions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                  لا توجد معاملات متطابقة مع معايير البحث
                </TableCell>
              </TableRow>
            ) : (
              transactions.map((transaction) => (
                <TableRow key={transaction.id} className="hover:bg-green-50/50 transition-colors duration-150">
                  <TableCell className="font-medium">{transaction.id.includes('-') ? transaction.id.split('-')[1] : transaction.id}</TableCell>
                  <TableCell>
                    {transaction.type === 'income' ? (
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full flex items-center gap-1 w-fit">
                        <FaArrowUp size={10} />
                        <span>مدخول</span>
                      </span>
                    ) : (
                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full flex items-center gap-1 w-fit">
                        <FaArrowDown size={10} />
                        <span>مصروف</span>
                      </span>
                    )}
                  </TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell className={`font-bold ${transaction.type === 'income' ? 'text-primary-color' : 'text-red-600'}`}>
                    {transaction.type === 'income' ? '+' : '-'}{transaction.amount} د.ج
                  </TableCell>
                  <TableCell>{formatDate(transaction.date)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {pagination.pages > 1 && (
        <div className="flex justify-center space-x-2 rtl:space-x-reverse mt-4 bg-white p-3 rounded-lg shadow-sm border border-green-100">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={pagination.page === 1}
            className={`border-[var(--primary-color)] text-[var(--primary-color)] ${pagination.page === 1 ? 'opacity-50' : 'hover:bg-green-50'}`}
          >
            السابق
          </Button>
          <span className="mx-4 flex items-center text-[var(--primary-color)]">
            الصفحة {pagination.page} من {pagination.pages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={pagination.page === pagination.pages}
            className={`border-[var(--primary-color)] text-[var(--primary-color)] ${pagination.page === pagination.pages ? 'opacity-50' : 'hover:bg-green-50'}`}
          >
            التالي
          </Button>
        </div>
      )}
      </div>
    </OptimizedProtectedRoute>
  )
}
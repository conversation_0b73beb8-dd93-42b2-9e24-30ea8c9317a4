import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getToken } from '@/lib/auth';

const prisma = new PrismaClient();

// POST /api/invoices/family - إنشاء فاتورة جماعية لولي أمر
export async function POST(req: NextRequest) {
  try {
    // التحقق من صلاحيات المستخدم
    const token = req.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول إلى هذه البيانات' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { parentId, amount, dueDate, month, year, description } = body;

    // التحقق من وجود الحقول المطلوبة
    if (!parentId || !amount || !dueDate || !month || !year) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    // التحقق من صحة المبلغ
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الولي وأبنائه
    const parent = await prisma.parent.findUnique({
      where: { id: parseInt(parentId.toString()) },
      include: {
        students: true
      }
    });

    if (!parent) {
      return NextResponse.json(
        { error: 'ولي الأمر غير موجود' },
        { status: 404 }
      );
    }

    if (parent.students.length === 0) {
      return NextResponse.json(
        { error: 'لا يوجد أبناء مسجلين لهذا الولي' },
        { status: 400 }
      );
    }

    // إنشاء الفاتورة الجماعية
    const invoice = await prisma.invoice.create({
      data: {
        parentId: parseInt(parentId.toString()),
        amount,
        dueDate: new Date(dueDate),
        month: parseInt(month.toString()),
        year: parseInt(year.toString()),
        description: description || `فاتورة جماعية لشهر ${month}/${year} - ${parent.name}`,
        type: 'FAMILY',
        status: 'UNPAID'
      },
      include: {
        parent: {
          include: {
            students: true
          }
        }
      }
    });

    console.log('✅ تم إنشاء فاتورة جماعية:', {
      id: invoice.id,
      parentName: parent.name,
      amount: invoice.amount,
      studentsCount: parent.students.length
    });

    return NextResponse.json({
      success: true,
      message: `تم إنشاء فاتورة جماعية بقيمة ${amount} دج لولي الأمر: ${parent.name}`,
      invoice: {
        id: invoice.id,
        amount: invoice.amount,
        dueDate: invoice.dueDate,
        month: invoice.month,
        year: invoice.year,
        description: invoice.description,
        parentName: parent.name,
        studentsCount: parent.students.length,
        status: invoice.status,
        type: invoice.type
      }
    });

  } catch (error) {
    console.error('❌ خطأ في إنشاء الفاتورة الجماعية:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الفاتورة الجماعية' },
      { status: 500 }
    );
  }
}

// GET /api/invoices/family - جلب الفواتير الجماعية
export async function GET(request: NextRequest) {
  try {
    // التحقق من صلاحيات المستخدم
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول إلى هذه البيانات' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');
    const month = searchParams.get('month');
    const year = searchParams.get('year');

    // بناء شروط البحث
    const whereConditions: any = {
      type: 'FAMILY'
    };

    if (status) {
      whereConditions.status = status;
    }

    if (month) {
      whereConditions.month = parseInt(month);
    }

    if (year) {
      whereConditions.year = parseInt(year);
    }

    // جلب الفواتير الجماعية
    const invoices = await prisma.invoice.findMany({
      where: whereConditions,
      include: {
        parent: {
          include: {
            students: true
          }
        },
        payments: {
          where: {
            status: 'PAID'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    });

    // حساب الإجماليات
    const invoicesWithCalculations = invoices.map(invoice => {
      const totalPaid = invoice.payments.reduce((sum, payment) => sum + payment.amount, 0);
      const remaining = invoice.amount - totalPaid;
      
      return {
        ...invoice,
        totalPaid,
        remaining,
        studentsCount: invoice.parent?.students.length || 0
      };
    });

    const total = await prisma.invoice.count({
      where: whereConditions
    });

    return NextResponse.json({
      success: true,
      invoices: invoicesWithCalculations,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    });

  } catch (error) {
    console.error('❌ خطأ في جلب الفواتير الجماعية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الفواتير الجماعية' },
      { status: 500 }
    );
  }
}

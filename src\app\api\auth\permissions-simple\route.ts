import { NextRequest, NextResponse } from 'next/server';
import { getToken } from '@/lib/auth';
import prisma from '@/lib/prisma';

// نسخة مبسطة من API الصلاحيات للاختبار
export async function GET(request: NextRequest) {
  try {
    console.log('=== Simple Permissions API ===');
    
    // التحقق من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      console.log('No token found');
      return NextResponse.json({ message: "No token" }, { status: 401 });
    }

    // التحقق من بيانات المستخدم
    const userData = await getToken(token);
    if (!userData) {
      console.log('Invalid token');
      return NextResponse.json({ message: "Invalid token" }, { status: 401 });
    }

    console.log('User data:', { id: userData.id, role: userData.role });

    // إذا كان مدير، إرجاع صلاحيات وهمية
    if (userData.role === 'ADMIN') {
      console.log('Admin detected, returning dummy permissions');
      return NextResponse.json({
        permissions: [
          { id: 1, key: 'admin.dashboard.view', name: 'عرض لوحة التحكم', category: 'dashboard' },
          { id: 2, key: 'admin.users.view', name: 'عرض المستخدمين', category: 'users' }
        ],
        message: "Admin permissions"
      });
    }

    // للموظفين، جلب المستخدم أولاً
    console.log('Fetching user from database...');
    const user = await prisma.user.findUnique({
      where: { id: userData.id },
      select: {
        id: true,
        role: true,
        roleId: true
      }
    });

    if (!user) {
      console.log('User not found in database');
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    console.log('User from DB:', user);

    // إذا لم يكن لديه roleId، إرجاع صلاحيات فارغة
    if (!user.roleId) {
      console.log('User has no roleId');
      return NextResponse.json({
        permissions: [],
        message: "No role assigned"
      });
    }

    // جلب الدور والصلاحيات
    console.log('Fetching role and permissions...');
    const role = await prisma.role.findUnique({
      where: { id: user.roleId },
      include: {
        permissions: {
          include: {
            permission: true
          }
        }
      }
    });

    if (!role) {
      console.log('Role not found');
      return NextResponse.json({
        permissions: [],
        message: "Role not found"
      });
    }

    console.log('Role found:', { id: role.id, name: role.name, permissionsCount: role.permissions.length });

    const permissions = role.permissions.map(rp => rp.permission);
    console.log('Final permissions:', permissions.length);

    return NextResponse.json({
      permissions,
      role: user.role,
      roleId: user.roleId,
      roleName: role.displayName,
      message: "Success"
    });

  } catch (error) {
    console.error('=== ERROR in Simple Permissions API ===');
    console.error('Error:', error);
    
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    return NextResponse.json(
      { message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface Level {
  id: number;
  name: string;
  description?: string;
  order: number;
}

interface Subject {
  id: number;
  name: string;
  description?: string;
  levelId?: number | null;
  level?: Level | null;
  hasStudyPlan: boolean;
}

interface EditSubjectDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  subject: Subject | null;
  levels: Level[];
}

export default function EditSubjectDialog({ isOpen, onCloseAction, onSuccessAction, subject, levels }: EditSubjectDialogProps) {
  const [formData, setFormData] = useState({
    id: 0,
    name: '',
    description: '',
    levelId: '',
    hasStudyPlan: false
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (subject) {
      setFormData({
        id: subject.id,
        name: subject.name,
        description: subject.description || '',
        levelId: subject.levelId ? subject.levelId.toString() : '',
        hasStudyPlan: subject.hasStudyPlan
      });
    }
  }, [subject]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, hasStudyPlan: checked }));
  };

  const handleUpdateSubject = async () => {
    if (!formData.name.trim()) {
      toast.error('الرجاء إدخال اسم المادة');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/subjects/${formData.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: formData.id,
          name: formData.name,
          description: formData.description || null,
          levelId: formData.levelId && formData.levelId !== "0" ? parseInt(formData.levelId) : null,
          hasStudyPlan: formData.hasStudyPlan
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update subject');
      }

      toast.success('تم تحديث المادة بنجاح');
      onSuccessAction();
      onCloseAction();
    } catch (error: Error | unknown) {
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث المادة');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleUpdateSubject}
      disabled={
        isLoading ||
        !formData.name.trim() ||
        (!!subject &&
          formData.name === subject.name &&
          formData.description === (subject.description || '') &&
          formData.levelId === (subject.levelId ? subject.levelId.toString() : '') &&
          formData.hasStudyPlan === subject.hasStudyPlan)
      }
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري التحديث...' : 'حفظ التغييرات'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="تعديل المادة"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
        <div className="space-y-2">
          <Label>اسم المادة</Label>
          <Input
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="أدخل اسم المادة"
          />
        </div>

        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف المادة"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label>المستوى (اختياري)</Label>
          <Select
            value={formData.levelId}
            onValueChange={(value) => handleSelectChange('levelId', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="اختر المستوى" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">بدون مستوى</SelectItem>
              {levels.map(level => (
                <SelectItem key={level.id} value={level.id.toString()}>
                  {level.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2 rtl:space-x-reverse pt-2">
          <Switch
            id="hasStudyPlan"
            checked={formData.hasStudyPlan}
            onCheckedChange={handleSwitchChange}
          />
          <Label htmlFor="hasStudyPlan" className="cursor-pointer">
            لديها خطة دراسية
          </Label>
        </div>
      </div>
    </AnimatedDialog>
  );
}

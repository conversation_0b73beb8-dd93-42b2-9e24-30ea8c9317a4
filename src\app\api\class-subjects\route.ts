import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    const classSubjects = await prisma.classSubject.findMany({
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      },
    });

    return NextResponse.json({
      success: true,
      data: classSubjects,
      message: "تم جلب علاقات الأقسام بالمواد بنجاح"
    });
  } catch (error) {
    console.error('Error fetching class subjects:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'حدث خطأ أثناء جلب علاقات الأقسام بالمواد',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { classeId, teacherSubjectId } = body;

    // التحقق من وجود البيانات المطلوبة
    if (!classeId || !teacherSubjectId) {
      return NextResponse.json(
        {
          success: false,
          error: 'يجب توفير معرف القسم ومعرف علاقة المعلم بالمادة'
        },
        { status: 400 }
      );
    }

    // التحقق من وجود القسم
    const classe = await prisma.classe.findUnique({
      where: { id: Number(classeId) }
    });

    if (!classe) {
      return NextResponse.json(
        {
          success: false,
          error: 'القسم غير موجود'
        },
        { status: 404 }
      );
    }

    // التحقق من وجود علاقة المعلم بالمادة
    const teacherSubject = await prisma.teacherSubject.findUnique({
      where: { id: Number(teacherSubjectId) }
    });

    if (!teacherSubject) {
      return NextResponse.json(
        {
          success: false,
          error: 'علاقة المعلم بالمادة غير موجودة'
        },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود علاقة مسبقة بين القسم والمادة
    const existingClassSubject = await prisma.classSubject.findFirst({
      where: {
        classeId: Number(classeId),
        teacherSubjectId: Number(teacherSubjectId)
      }
    });

    if (existingClassSubject) {
      return NextResponse.json({
        success: true,
        data: existingClassSubject,
        message: "العلاقة بين القسم والمادة موجودة بالفعل"
      });
    }

    // إنشاء علاقة جديدة بين القسم والمادة
    const classSubject = await prisma.classSubject.create({
      data: {
        classeId: Number(classeId),
        teacherSubjectId: Number(teacherSubjectId)
      },
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: classSubject,
      message: "تم إنشاء علاقة بين القسم والمادة بنجاح"
    });
  } catch (error) {
    console.error('Error creating class subject:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'حدث خطأ أثناء إنشاء علاقة بين القسم والمادة',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

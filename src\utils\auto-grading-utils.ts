import { QuestionType } from '@prisma/client';

// تعريف أنواع البيانات
interface QuestionOption {
  id: number;
  text: string;
  isCorrect: boolean;
  order: number;
}

interface QuestionAnswer {
  id: number;
  text: string;
  isCorrect: boolean;
  explanation: string | null;
}

interface Question {
  id: number;
  text: string;
  type: string;
  difficultyLevel: string;
  points: number;
  options: QuestionOption[];
  answers: QuestionAnswer[];
}

interface ExamQuestion {
  id: number;
  examId: number;
  questionId: number;
  order: number;
  points: number | null;
  question: Question;
}

interface StudentAnswer {
  id?: number;
  examPointId: number;
  examQuestionId: number;
  answer: string;
  isCorrect?: boolean | null;
  points?: number | null;
  feedback?: string | null;
}

/**
 * تصحيح إجابة الطالب آلياً
 * @param studentAnswer إجابة الطالب
 * @param examQuestion سؤال الامتحان
 * @returns إجابة الطالب بعد التصحيح
 */
export const autoGradeStudentAnswer = (
  studentAnswer: StudentAnswer,
  examQuestion: ExamQuestion
): StudentAnswer => {
  const question = examQuestion.question;
  const questionType = question.type as QuestionType;
  const questionPoints = examQuestion.points || question.points;
  
  // نسخة من إجابة الطالب للتعديل
  const gradedAnswer: StudentAnswer = { ...studentAnswer };
  
  // تحديد ما إذا كانت الإجابة صحيحة أم لا
  switch (questionType) {
    case QuestionType.MULTIPLE_CHOICE:
    case QuestionType.TRUE_FALSE:
      // التحقق من الإجابة في أسئلة الاختيار من متعدد وصح/خطأ
      const correctOption = question.options.find(opt => opt.isCorrect);
      if (correctOption) {
        const isCorrect = studentAnswer.answer.trim() === correctOption.text.trim();
        gradedAnswer.isCorrect = isCorrect;
        gradedAnswer.points = isCorrect ? questionPoints : 0;
        gradedAnswer.feedback = isCorrect 
          ? 'إجابة صحيحة' 
          : `إجابة خاطئة. الإجابة الصحيحة هي: ${correctOption.text}`;
      }
      break;
      
    case QuestionType.MATCHING:
      // التحقق من الإجابة في أسئلة المطابقة
      // نفترض أن الإجابة هي سلسلة من الأزواج المفصولة بفواصل
      // مثال: "1-أ,2-ب,3-ج"
      try {
        const studentMatches = studentAnswer.answer.split(',').map(pair => {
          const [key, value] = pair.split('-');
          return { key: key.trim(), value: value.trim() };
        });
        
        // نفترض أن الخيارات الصحيحة مخزنة في نفس التنسيق في الخيارات
        const correctMatches = question.options
          .filter(opt => opt.isCorrect)
          .map(opt => {
            const [key, value] = opt.text.split('-');
            return { key: key.trim(), value: value.trim() };
          });
        
        // حساب عدد المطابقات الصحيحة
        let correctCount = 0;
        for (const studentMatch of studentMatches) {
          const matchingCorrect = correctMatches.find(
            m => m.key === studentMatch.key && m.value === studentMatch.value
          );
          if (matchingCorrect) {
            correctCount++;
          }
        }
        
        // حساب النسبة المئوية للإجابات الصحيحة
        const totalMatches = correctMatches.length;
        const percentageCorrect = totalMatches > 0 ? (correctCount / totalMatches) : 0;
        
        // تحديد ما إذا كانت الإجابة صحيحة بالكامل أو جزئياً
        gradedAnswer.isCorrect = percentageCorrect === 1;
        gradedAnswer.points = questionPoints * percentageCorrect;
        gradedAnswer.feedback = `${correctCount} من ${totalMatches} مطابقات صحيحة`;
      } catch {
        gradedAnswer.isCorrect = false;
        gradedAnswer.points = 0;
        gradedAnswer.feedback = 'تنسيق الإجابة غير صحيح';
      }
      break;
      
    case QuestionType.ORDERING:
      // التحقق من الإجابة في أسئلة الترتيب
      // نفترض أن الإجابة هي سلسلة من العناصر المفصولة بفواصل
      // مثال: "أ,ب,ج,د"
      try {
        const studentOrder = studentAnswer.answer.split(',').map(item => item.trim());
        
        // نفترض أن الترتيب الصحيح مخزن في الخيارات حسب حقل order
        const correctOrder = question.options
          .sort((a, b) => a.order - b.order)
          .map(opt => opt.text.trim());
        
        // حساب عدد العناصر في المكان الصحيح
        let correctCount = 0;
        for (let i = 0; i < Math.min(studentOrder.length, correctOrder.length); i++) {
          if (studentOrder[i] === correctOrder[i]) {
            correctCount++;
          }
        }
        
        // حساب النسبة المئوية للإجابات الصحيحة
        const totalItems = correctOrder.length;
        const percentageCorrect = totalItems > 0 ? (correctCount / totalItems) : 0;
        
        // تحديد ما إذا كانت الإجابة صحيحة بالكامل أو جزئياً
        gradedAnswer.isCorrect = percentageCorrect === 1;
        gradedAnswer.points = questionPoints * percentageCorrect;
        gradedAnswer.feedback = `${correctCount} من ${totalItems} عناصر في المكان الصحيح`;
      } catch {
        gradedAnswer.isCorrect = false;
        gradedAnswer.points = 0;
        gradedAnswer.feedback = 'تنسيق الإجابة غير صحيح';
      }
      break;
      
    case QuestionType.SHORT_ANSWER:
      // التحقق من الإجابة في أسئلة الإجابة القصيرة
      // نقارن إجابة الطالب مع الإجابات الصحيحة المخزنة
      if (question.answers && question.answers.length > 0) {
        // البحث عن تطابق دقيق أو جزئي
        const exactMatch = question.answers.find(
          ans => ans.text.trim().toLowerCase() === studentAnswer.answer.trim().toLowerCase()
        );
        
        if (exactMatch) {
          // تطابق دقيق
          gradedAnswer.isCorrect = true;
          gradedAnswer.points = questionPoints;
          gradedAnswer.feedback = 'إجابة صحيحة';
        } else {
          // البحث عن تطابق جزئي
          const partialMatches = question.answers.filter(
            ans => studentAnswer.answer.trim().toLowerCase().includes(ans.text.trim().toLowerCase()) ||
                  ans.text.trim().toLowerCase().includes(studentAnswer.answer.trim().toLowerCase())
          );
          
          if (partialMatches.length > 0) {
            // تطابق جزئي
            gradedAnswer.isCorrect = false; // نعتبرها غير صحيحة تماماً، لكن نعطي نقاط جزئية
            gradedAnswer.points = questionPoints * 0.5; // نصف النقاط للتطابق الجزئي
            gradedAnswer.feedback = 'إجابة جزئية. تحتاج إلى مراجعة من المعلم.';
          } else {
            // لا يوجد تطابق
            gradedAnswer.isCorrect = false;
            gradedAnswer.points = 0;
            gradedAnswer.feedback = 'إجابة غير صحيحة';
          }
        }
      } else {
        // لا توجد إجابات مخزنة للمقارنة
        gradedAnswer.isCorrect = null; // تحتاج إلى تصحيح يدوي
        gradedAnswer.points = null;
        gradedAnswer.feedback = 'تحتاج إلى تصحيح يدوي';
      }
      break;
      
    case QuestionType.FILL_BLANK:
      // التحقق من الإجابة في أسئلة ملء الفراغات
      // نفترض أن الإجابة هي سلسلة من الكلمات المفصولة بفواصل
      // مثال: "كلمة1,كلمة2,كلمة3"
      try {
        const studentFills = studentAnswer.answer.split(',').map(word => word.trim());
        
        // نفترض أن الإجابات الصحيحة مخزنة في نفس الترتيب
        const correctFills = question.answers.map(ans => ans.text.trim());
        
        // حساب عدد الكلمات الصحيحة
        let correctCount = 0;
        for (let i = 0; i < Math.min(studentFills.length, correctFills.length); i++) {
          if (studentFills[i].toLowerCase() === correctFills[i].toLowerCase()) {
            correctCount++;
          }
        }
        
        // حساب النسبة المئوية للإجابات الصحيحة
        const totalFills = correctFills.length;
        const percentageCorrect = totalFills > 0 ? (correctCount / totalFills) : 0;
        
        // تحديد ما إذا كانت الإجابة صحيحة بالكامل أو جزئياً
        gradedAnswer.isCorrect = percentageCorrect === 1;
        gradedAnswer.points = questionPoints * percentageCorrect;
        gradedAnswer.feedback = `${correctCount} من ${totalFills} كلمات صحيحة`;
      } catch {
        gradedAnswer.isCorrect = false;
        gradedAnswer.points = 0;
        gradedAnswer.feedback = 'تنسيق الإجابة غير صحيح';
      }
      break;
      
    case QuestionType.ESSAY:
    default:
      // الأسئلة المقالية تحتاج إلى تصحيح يدوي
      gradedAnswer.isCorrect = null; // تحتاج إلى تصحيح يدوي
      gradedAnswer.points = null;
      gradedAnswer.feedback = 'تحتاج إلى تصحيح يدوي';
      break;
  }
  
  return gradedAnswer;
};

/**
 * تصحيح مجموعة من إجابات الطالب آلياً
 * @param studentAnswers إجابات الطالب
 * @param examQuestions أسئلة الامتحان
 * @returns إجابات الطالب بعد التصحيح
 */
export const autoGradeStudentAnswers = (
  studentAnswers: StudentAnswer[],
  examQuestions: ExamQuestion[]
): StudentAnswer[] => {
  return studentAnswers.map(studentAnswer => {
    const examQuestion = examQuestions.find(eq => eq.id === studentAnswer.examQuestionId);
    if (!examQuestion) {
      return studentAnswer;
    }
    return autoGradeStudentAnswer(studentAnswer, examQuestion);
  });
};

/**
 * حساب الدرجة النهائية للطالب بناءً على إجابات مصححة
 * @param gradedAnswers إجابات الطالب المصححة
 * @param examQuestions أسئلة الامتحان
 * @param maxPoints الدرجة القصوى للامتحان
 * @param passingPoints الدرجة المطلوبة للنجاح
 * @returns معلومات الدرجة النهائية
 */
export const calculateFinalGrade = (
  gradedAnswers: StudentAnswer[],
  examQuestions: ExamQuestion[],
  maxPoints: number,
  passingPoints: number
): {
  finalGrade: number;
  status: 'PENDING' | 'PASSED' | 'FAILED' | 'EXCELLENT';
  completionPercentage: number;
  needsManualGrading: boolean;
} => {
  // حساب عدد الأسئلة التي تم الإجابة عليها
  const answeredQuestions = gradedAnswers.length;
  const totalQuestions = examQuestions.length;
  const completionPercentage = (answeredQuestions / totalQuestions) * 100;
  
  // التحقق مما إذا كانت هناك أسئلة تحتاج إلى تصحيح يدوي
  const needsManualGrading = gradedAnswers.some(answer => answer.isCorrect === null);
  
  // حساب مجموع النقاط المحصلة
  const earnedPoints = gradedAnswers.reduce((sum, answer) => {
    return sum + (answer.points || 0);
  }, 0);
  
  // حساب مجموع النقاط الممكنة للأسئلة التي تم الإجابة عليها
  const possiblePoints = gradedAnswers.reduce((sum, answer) => {
    const examQuestion = examQuestions.find(eq => eq.id === answer.examQuestionId);
    if (!examQuestion) return sum;
    return sum + (examQuestion.points || examQuestion.question.points);
  }, 0);
  
  // حساب الدرجة النهائية (على مقياس من 10)
  const finalGrade = possiblePoints > 0 ? (earnedPoints / possiblePoints) * 10 : 0;
  
  // تحديد حالة الطالب
  let status: 'PENDING' | 'PASSED' | 'FAILED' | 'EXCELLENT' = 'PENDING';
  
  // إذا كانت هناك أسئلة تحتاج إلى تصحيح يدوي أو لم يتم الإجابة على جميع الأسئلة
  if (needsManualGrading || completionPercentage < 90) {
    status = 'PENDING';
  } else {
    // تحديد الحالة بناءً على الدرجة النهائية
    if (finalGrade >= 9) {
      status = 'EXCELLENT';
    } else if (finalGrade >= (passingPoints / maxPoints) * 10) {
      status = 'PASSED';
    } else {
      status = 'FAILED';
    }
  }
  
  return {
    finalGrade,
    status,
    completionPercentage,
    needsManualGrading
  };
};

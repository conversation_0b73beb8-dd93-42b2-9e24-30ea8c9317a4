# 💰 مشروع تطوير قسم المدفوعات حسب الولي وتحسين الفواتير

## 📝 وصف المشروع
تطوير قسم المدفوعات لعرض الدفعات حسب الولي مع إضافة قسم فواتير محسن للطباعة بحجم صغير.

## 🎯 الأهداف الرئيسية

### 1. قسم المدفوعات حسب الولي
- **عرض شامل للديون**: إجمالي المبلغ المطلوب لكل تلميذ
- **تتبع المدفوعات**: المبالغ المدفوعة والمبلغ المتبقي لكل ولي
- **واجهة منظمة**: عرض البيانات بشكل واضح ومفهوم
- **إحصائيات مفصلة**: معلومات مالية شاملة لكل ولي

### 2. قسم الفواتير المحسن
- **طباعة محسنة**: تصميم فواتير بحجم صغير وملائم للطباعة
- **توفير الورق**: تصميم مدمج لا يستهلك ورق كبير
- **جودة عالية**: فواتير واضحة ومهنية
- **سهولة الاستخدام**: واجهة بسيطة للطباعة والتصدير

## 📊 التحليل الحالي

### الميزات الموجودة ✅
- صفحة إدارة ديون الأولياء: `src/app/admin/parent-debts/page.tsx`
- API المدفوعات: `/api/admin/payments`
- API ديون الأولياء: `/api/admin/parent-debts`
- نظام الفواتير: `src/app/admin/invoices/page.tsx`
- API الفواتير: `/api/invoices`
- طباعة PDF للفواتير: `/api/invoices/pdf/[id]`

### الميزات المطلوب تطويرها 🔄
- تحسين عرض المدفوعات حسب الولي
- إضافة صفحة مخصصة لعرض مدفوعات الأولياء
- تحسين تصميم الفواتير للطباعة الصغيرة
- إضافة خيارات طباعة متقدمة

## 🗂️ هيكل المشروع

### الملفات الجديدة والمحسنة
```
src/
├── app/
│   ├── admin/
│   │   ├── payments/
│   │   │   ├── by-parent/
│   │   │   │   └── page.tsx (جديد)
│   │   │   └── components/
│   │   │       ├── ParentPaymentsSummary.tsx (جديد)
│   │   │       └── PaymentsByParentTable.tsx (جديد)
│   │   └── invoices/
│   │       ├── components/
│   │       │   ├── CompactInvoicePrint.tsx (جديد)
│   │       │   └── PrintOptionsModal.tsx (جديد)
│   │       └── print/
│   │           └── [id]/
│   │               └── page.tsx (جديد)
│   └── api/
│       ├── payments/
│       │   └── by-parent/
│       │       └── route.ts (جديد)
│       └── invoices/
│           └── compact-pdf/
│               └── [id]/
│                   └── route.ts (جديد)
```

## 📋 خطة العمل التفصيلية

### المرحلة الأولى: تحليل وتخطيط النظام
- [x] **T01.01: تحليل هيكل البيانات الحالي**
  - **الحالة:** مكتمل
  - **المكونات:** قاعدة البيانات، نماذج الأولياء والطلاب والمدفوعات
  - **الاعتماديات:** لا يوجد
  - **المستندات المرجعية:** `prisma/schema.prisma`
  - **ملاحظات المستخدم:** فهم العلاقات بين الأولياء والطلاب والمدفوعات

- [x] **T01.02: تصميم واجهة المدفوعات حسب الولي**
  - **الحالة:** مكتمل
  - **المكونات:** تصميم UI/UX للصفحة الجديدة
  - **الاعتماديات:** T01.01
  - **المستندات المرجعية:** `uml/payments_by_parent_design.md`
  - **ملاحظات المستخدم:** واجهة واضحة تعرض إجمالي المبالغ والمدفوعات

- [x] **T01.03: تصميم نموذج الفاتورة المدمجة**
  - **الحالة:** مكتمل
  - **المكونات:** تصميم فاتورة صغيرة الحجم للطباعة
  - **الاعتماديات:** T01.01
  - **المستندات المرجعية:** `uml/compact_invoice_design.md`
  - **ملاحظات المستخدم:** حجم صغير، توفير الورق، جودة عالية

### المرحلة الثانية: تطوير APIs
- [x] **T02.01: إنشاء API المدفوعات حسب الولي**
  - **الحالة:** مكتمل
  - **المكونات:** `src/app/api/payments/by-parent/route.ts`
  - **الاعتماديات:** T01.02
  - **المستندات المرجعية:** `specs/payments_by_parent_api.md`
  - **ملاحظات المستخدم:** API شامل لجلب بيانات المدفوعات مجمعة حسب الولي

- [x] **T02.02: إنشاء API الفاتورة المدمجة**
  - **الحالة:** مكتمل
  - **المكونات:** `src/app/api/invoices/compact-pdf/[id]/route.ts`
  - **الاعتماديات:** T01.03
  - **المستندات المرجعية:** `specs/compact_invoice_api.md`
  - **ملاحظات المستخدم:** توليد PDF مدمج للفواتير

### المرحلة الثالثة: تطوير الواجهات
- [x] **T03.01: إنشاء صفحة المدفوعات حسب الولي**
  - **الحالة:** مكتمل
  - **المكونات:** `src/app/admin/payments/by-parent/page.tsx`
  - **الاعتماديات:** T02.01
  - **المستندات المرجعية:** `specs/payments_by_parent_page.md`
  - **ملاحظات المستخدم:** صفحة شاملة لعرض مدفوعات الأولياء

- [x] **T03.02: إنشاء مكونات عرض البيانات**
  - **الحالة:** مكتمل
  - **المكونات:** `ParentPaymentsSummary.tsx`, `PaymentsByParentTable.tsx`
  - **الاعتماديات:** T03.01
  - **المستندات المرجعية:** `specs/payment_components.md`
  - **ملاحظات المستخدم:** مكونات قابلة لإعادة الاستخدام

- [x] **T03.03: تحسين صفحة الفواتير للطباعة المدمجة**
  - **الحالة:** مكتمل
  - **المكونات:** `src/app/admin/invoices/components/CompactInvoicePrint.tsx`
  - **الاعتماديات:** T02.02
  - **المستندات المرجعية:** `specs/compact_invoice_components.md`
  - **ملاحظات المستخدم:** واجهة طباعة محسنة

### المرحلة الرابعة: التحسينات والاختبار
- [ ] **T04.01: إضافة خيارات طباعة متقدمة**
  - **الحالة:** قيد الانتظار
  - **المكونات:** `PrintOptionsModal.tsx`
  - **الاعتماديات:** T03.03
  - **المستندات المرجعية:** `specs/print_options.md`
  - **ملاحظات المستخدم:** خيارات متعددة للطباعة والتصدير

- [ ] **T04.02: اختبار شامل للنظام**
  - **الحالة:** قيد الانتظار
  - **المكونات:** جميع الملفات المطورة
  - **الاعتماديات:** T04.01
  - **المستندات المرجعية:** `testing/test_plan.md`
  - **ملاحظات المستخدم:** اختبار جميع الوظائف والتأكد من الجودة

- [ ] **T04.03: التوثيق النهائي**
  - **الحالة:** قيد الانتظار
  - **المكونات:** ملفات التوثيق
  - **الاعتماديات:** T04.02
  - **المستندات المرجعية:** `docs/final_documentation.md`
  - **ملاحظات المستخدم:** توثيق شامل للاستخدام والصيانة

## 🔧 التقنيات المستخدمة
- **Frontend:** Next.js, React, TypeScript, Tailwind CSS
- **Backend:** Next.js API Routes, Prisma ORM
- **Database:** PostgreSQL/MySQL (حسب الإعداد الحالي)
- **PDF Generation:** jsPDF أو Puppeteer
- **UI Components:** Ant Design, React Icons

## 📈 المؤشرات المتوقعة
- تحسين تجربة المستخدم في إدارة المدفوعات
- توفير 50% من استهلاك الورق في طباعة الفواتير
- زيادة كفاءة متابعة ديون الأولياء
- تسهيل عملية المراجعة المالية

## 🎨 تفضيلات التصميم
- استخدام تنسيق التاريخ الفرنسي (DD/MM/YYYY)
- العملة: الدينار الجزائري (دج)
- حجم الفاتورة: صغير ومدمج للطباعة
- الألوان: متوافقة مع هوية النظام الحالي

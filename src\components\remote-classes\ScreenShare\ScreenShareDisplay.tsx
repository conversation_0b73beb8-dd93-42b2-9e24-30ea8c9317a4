'use client';

import React, { useRef, useEffect, useState } from 'react';
import { FaExclamationTriangle, FaExpand, FaCompress } from 'react-icons/fa';
import ScreenShareResizeHandler from './ScreenShareResizeHandler';

interface ScreenShareDisplayProps {
  stream: MediaStream | null;
  isFullScreen?: boolean;
  userName?: string;
  onToggleFullScreen?: (isFullScreen: boolean) => void;
  onResize?: (width: number, height: number) => void;
}

/**
 * Component for displaying a shared screen
 */
const ScreenShareDisplay: React.FC<ScreenShareDisplayProps> = ({
  stream,
  isFullScreen = false,
  userName = 'المعلم',
  onToggleFullScreen,
  onResize,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [aspectRatio, setAspectRatio] = useState(16 / 9); // Default aspect ratio

  // Connect the stream to the video element when it changes
  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;

      // Get initial video dimensions when metadata is loaded
      videoRef.current.onloadedmetadata = () => {
        if (videoRef.current) {
          const width = videoRef.current.videoWidth;
          const height = videoRef.current.videoHeight;
          setDimensions({ width, height });
          setAspectRatio(width / height);
        }
      };
    }
  }, [stream]);

  // If there's no stream, show a placeholder
  if (!stream) {
    return (
      <div className={`flex flex-col items-center justify-center bg-gray-100 rounded-lg border border-gray-300 ${isFullScreen ? 'fixed inset-0 z-50' : 'w-full h-64'}`}>
        <FaExclamationTriangle className="text-yellow-500 text-4xl mb-2" />
        <p className="text-gray-600 text-center">
          لا توجد مشاركة شاشة نشطة
        </p>
      </div>
    );
  }

  // Handle resize events from the resize handler
  const handleResize = (width: number, height: number) => {
    setDimensions({ width, height });
    setAspectRatio(width / height);

    // Call the parent's onResize callback if provided
    if (onResize) {
      onResize(width, height);
    }
  };

  // Toggle fullscreen mode
  const toggleFullScreen = () => {
    if (onToggleFullScreen) {
      onToggleFullScreen(!isFullScreen);
    }
  };

  return (
    <div
      className={`relative ${isFullScreen ? 'fixed inset-0 z-50 bg-black' : 'w-full'}`}
      style={!isFullScreen ? { aspectRatio: `${aspectRatio}` } : undefined}
    >
      {/* Resize handler component */}
      <ScreenShareResizeHandler
        stream={stream}
        onResize={handleResize}
      />

      <video
        ref={videoRef}
        autoPlay
        playsInline
        className={`${isFullScreen ? 'w-full h-full object-contain' : 'w-full h-auto rounded-lg border border-gray-300'}`}
      />

      {/* User name and dimensions overlay */}
      <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
        {userName} يشارك الشاشة
        {dimensions.width > 0 && (
          <span className="mr-2 text-xs opacity-75">
            {dimensions.width}×{dimensions.height}
          </span>
        )}
      </div>

      {/* Controls */}
      <div className="absolute top-2 right-2 flex gap-2">
        {/* Fullscreen toggle button */}
        <button
          className="bg-[var(--primary-color)] text-white p-2 rounded-full hover:bg-[var(--secondary-color)] transition-colors"
          onClick={toggleFullScreen}
          aria-label={isFullScreen ? "إغلاق العرض الكامل" : "عرض كامل الشاشة"}
          title={isFullScreen ? "إغلاق العرض الكامل" : "عرض كامل الشاشة"}
        >
          {isFullScreen ? <FaCompress /> : <FaExpand />}
        </button>

        {/* Exit button (only in fullscreen mode) */}
        {isFullScreen && (
          <button
            className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
            onClick={() => {
              if (document.fullscreenElement) {
                document.exitFullscreen();
              }
              if (onToggleFullScreen) {
                onToggleFullScreen(false);
              }
            }}
            aria-label="إغلاق"
            title="إغلاق"
          >
            ✕
          </button>
        )}
      </div>
    </div>
  );
};

export default ScreenShareDisplay;

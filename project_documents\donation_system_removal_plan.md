# خطة حذف نظام حملات التبرع وإعادة تنظيم معلومات التبرع

## نظرة عامة على المشروع
هذا المشروع يهدف إلى حذف نظام حملات التبرع بالكامل والاحتفاظ فقط بمعلومات التبرع الأساسية (أرقام الهاتف، حساب CCP، حساب CPA، حساب BDR) في صفحة التبرعات العامة، مع إضافة هذه المعلومات لصفحة الإعدادات في قسم معلومات الاتصال للتعديل.

## قائمة المهام الرئيسية

### المرحلة الأولى: تحليل وتوثيق النظام الحالي
- [x] **T01.01: توثيق النظام الحالي**
  - **الحالة:** مُنجزة
  - **المكونات:** جميع ملفات نظام التبرعات والحملات
  - **الاعتماديات:** لا يوجد
  - **المستندات المرجعية:** current_system_analysis.md
  - **ملاحظات المستخدم:** حذف نظام الحملات والاحتفاظ بمعلومات التبرع فقط

### المرحلة الثانية: تعديل قاعدة البيانات
- [x] **T02.01: تعديل Prisma Schema**
  - **الحالة:** مُنجزة
  - **المكونات:** prisma/schema.prisma
  - **الاعتماديات:** T01.01
  - **المستندات المرجعية:** database_changes.md
  - **ملاحظات المستخدم:** إزالة جدول DonationCampaign وتعديل جدول Donation

- [x] **T02.02: إنشاء migration لقاعدة البيانات**
  - **الحالة:** مُنجزة
  - **المكونات:** prisma/migrations/
  - **الاعتماديات:** T02.01
  - **المستندات المرجعية:** database_changes.md
  - **ملاحظات المستخدم:** إزالة العلاقات مع حملات التبرع

### المرحلة الثالثة: إضافة معلومات التبرع للإعدادات
- [x] **T03.01: تعديل واجهة SiteSettings**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/admin/admin-setup/page.tsx
  - **الاعتماديات:** T02.02
  - **المستندات المرجعية:** settings_interface.md
  - **ملاحظات المستخدم:** إضافة حقول أرقام الهاتف وحسابات CCP وCPA وBDR

- [x] **T03.02: تعديل API الإعدادات**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/api/settings/route.ts
  - **الاعتماديات:** T03.01
  - **المستندات المرجعية:** settings_interface.md
  - **ملاحظات المستخدم:** دعم حفظ واسترجاع معلومات التبرع

- [x] **T03.03: تعديل قسم معلومات الاتصال في الإعدادات**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/admin/admin-setup/page.tsx (قسم contact)
  - **الاعتماديات:** T03.02
  - **المستندات المرجعية:** settings_interface.md
  - **ملاحظات المستخدم:** إضافة حقول التبرع لقسم معلومات الاتصال

### المرحلة الرابعة: تعديل صفحة التبرعات العامة
- [x] **T04.01: إزالة نظام الحملات من صفحة التبرعات**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/donations/page.tsx
  - **الاعتماديات:** T03.03
  - **المستندات المرجعية:** donations_page_redesign.md
  - **ملاحظات المستخدم:** إزالة عرض الحملات والاحتفاظ بنموذج التبرع البسيط

- [x] **T04.02: إضافة عرض معلومات التبرع من الإعدادات**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/donations/page.tsx
  - **الاعتماديات:** T04.01
  - **المستندات المرجعية:** donations_page_redesign.md
  - **ملاحظات المستخدم:** عرض أرقام الهاتف وحسابات CCP وCPA وBDR

### المرحلة الخامسة: تعديل APIs التبرعات
- [x] **T05.01: تعديل API التبرعات**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/api/donations/route.ts
  - **الاعتماديات:** T04.02
  - **المستندات المرجعية:** api_changes.md
  - **ملاحظات المستخدم:** إزالة المراجع لحملات التبرع

- [x] **T05.02: حذف APIs حملات التبرع**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/api/donation-campaigns/
  - **الاعتماديات:** T05.01
  - **المستندات المرجعية:** api_changes.md
  - **ملاحظات المستخدم:** حذف جميع ملفات APIs الحملات

### المرحلة السادسة: تنظيف وتحديث المراجع
- [x] **T06.01: تحديث Footer والمكونات الأخرى**
  - **الحالة:** مُنجزة
  - **المكونات:** src/components/footer/footer.tsx
  - **الاعتماديات:** T05.02
  - **المستندات المرجعية:** cleanup_references.md
  - **ملاحظات المستخدم:** إزالة أي مراجع لحملات التبرع

- [x] **T06.02: تحديث تقارير التبرعات**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/api/donation-reports/
  - **الاعتماديات:** T06.01
  - **المستندات المرجعية:** cleanup_references.md
  - **ملاحظات المستخدم:** إزالة مراجع الحملات من التقارير

### المرحلة السابعة: اختبار وتحقق نهائي
- [x] **T07.01: اختبار النظام المحدث**
  - **الحالة:** مُنجزة
  - **المكونات:** جميع المكونات المعدلة
  - **الاعتماديات:** T06.02
  - **المستندات المرجعية:** testing_checklist.md
  - **ملاحظات المستخدم:** التأكد من عمل النظام بدون حملات التبرع

- [x] **T07.02: تحديث الوثائق**
  - **الحالة:** مُنجزة
  - **المكونات:** README.md وملفات التوثيق
  - **الاعتماديات:** T07.01
  - **المستندات المرجعية:** documentation_updates.md
  - **ملاحظات المستخدم:** توثيق التغييرات المنجزة

## ملاحظات مهمة
- سيتم الاحتفاظ بجدول Donation في قاعدة البيانات لكن بدون العلاقة مع حملات التبرع
- معلومات التبرع (أرقام الهاتف، CCP، CPA، BDR) ستكون جزءاً من إعدادات الموقع
- صفحة التبرعات ستعرض معلومات التبرع من الإعدادات بدلاً من الحملات
- سيتم حذف جميع APIs وصفحات إدارة حملات التبرع

## حالة المشروع
**المرحلة الحالية:** التحليل والتخطيط
**آخر تحديث:** [التاريخ الحالي]
**المهمة النشطة:** T01.01

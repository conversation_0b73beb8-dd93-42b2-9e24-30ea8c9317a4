import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // تحقق من وجود معلمات البحث
    const searchParams = request.nextUrl.searchParams;
    const includeStudents = searchParams.get('includeStudents') === 'true';
    const includeSubjects = searchParams.get('includeSubjects') === 'true';
    const teacherId = searchParams.get('teacherId');

    // بناء شروط البحث
    const whereCondition: {
      classSubjects?: {
        some: {
          teacherSubject: {
            teacherId: number
          }
        }
      }
    } = {};

    // إذا تم تحديد معرف المعلم، نقوم بتصفية الفصول التي يدرسها المعلم
    if (teacherId) {
      whereCondition.classSubjects = {
        some: {
          teacherSubject: {
            teacherId: parseInt(teacherId)
          }
        }
      };
    }

    // بناء كائن الاستعلام بناءً على المعلمات
    const includeOptions: {
      students?: {
        select: {
          id: boolean;
          name: boolean;
        }
      };
      classSubjects?: {
        include: {
          teacherSubject: {
            include: {
              teacher: {
                select: {
                  id: boolean;
                  name: boolean;
                }
              };
              subject: {
                select: {
                  id: boolean;
                  name: boolean;
                }
              }
            }
          }
        }
      };
    } = {};

    if (includeStudents) {
      includeOptions.students = {
        select: {
          id: true,
          name: true
        }
      };
    }

    if (includeSubjects) {
      includeOptions.classSubjects = {
        include: {
          teacherSubject: {
            include: {
              teacher: {
                select: {
                  id: true,
                  name: true
                }
              },
              subject: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      };
    }

    // تنفيذ الاستعلام
    const classes = await prisma.classe.findMany({
      where: whereCondition,
      include: includeOptions,
      orderBy: {
        name: 'asc'
      }
    });

    // تخزين النتائج في ذاكرة التخزين المؤقت (يمكن استخدام مكتبة مثل node-cache)

    return NextResponse.json({ classes });
  } catch (error: unknown) {
    console.error('Error fetching classes:', error);
    return NextResponse.json({ error: 'Failed to fetch classes' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, capacity, description, teacherSubjects } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      );
    }

    // إنشاء القسم الجديد
    const newClass = await prisma.classe.create({
      data: {
        name,
        capacity: capacity ? parseInt(capacity) : 30,
        description: description || null,
      },
    });

    // إذا تم توفير معرفات المعلمين والمواد، قم بإنشاء العلاقات
    if (teacherSubjects && Array.isArray(teacherSubjects) && teacherSubjects.length > 0) {
      // إنشاء علاقات بين القسم والمعلمين/المواد
      for (const teacherSubjectId of teacherSubjects) {
        await prisma.classSubject.create({
          data: {
            classeId: newClass.id,
            teacherSubjectId: teacherSubjectId
          }
        });
      }
    }

    // جلب القسم مع جميع البيانات المرتبطة به
    const classWithRelations = await prisma.classe.findUnique({
      where: { id: newClass.id },
      include: {
        students: true,
        classSubjects: {
          include: {
            teacherSubject: {
              include: {
                teacher: true,
                subject: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json(classWithRelations);
  } catch (error: unknown) {
    console.error('Error creating class:', error);
    return NextResponse.json(
      { error: 'Failed to create class' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, capacity, description, teacherSubjects } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      );
    }

    // تحديث بيانات القسم
    await prisma.classe.update({
      where: { id: parseInt(id) },
      data: {
        name,
        capacity: capacity ? parseInt(capacity) : undefined,
        description: description !== undefined ? description : undefined
      }
    });

    // إذا تم توفير معرفات المعلمين والمواد
    if (teacherSubjects && Array.isArray(teacherSubjects)) {
      // حذف جميع العلاقات الحالية
      await prisma.classSubject.deleteMany({
        where: { classeId: parseInt(id) }
      });

      // إنشاء علاقات جديدة
      for (const teacherSubjectId of teacherSubjects) {
        await prisma.classSubject.create({
          data: {
            classeId: parseInt(id),
            teacherSubjectId: teacherSubjectId
          }
        });
      }
    }

    // جلب القسم المحدث مع جميع البيانات المرتبطة به
    const updatedClass = await prisma.classe.findUnique({
      where: { id: parseInt(id) },
      include: {
        students: true,
        classSubjects: {
          include: {
            teacherSubject: {
              include: {
                teacher: true,
                subject: true
              }
            }
          }
        },
      },
    });

    return NextResponse.json(updatedClass);
  } catch (error: unknown) {
    console.error('Error updating class:', error);
    return NextResponse.json(
      { error: 'Failed to update class' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Class ID is required' },
        { status: 400 }
      );
    }

    await prisma.classe.delete({
      where: { id: parseInt(id) },
    });

    return NextResponse.json({ message: 'Class deleted successfully' });
  } catch (error: unknown) {
    console.error('Error deleting class:', error);
    return NextResponse.json(
      { error: 'Failed to delete class' },
      { status: 500 }
    );
  }
}
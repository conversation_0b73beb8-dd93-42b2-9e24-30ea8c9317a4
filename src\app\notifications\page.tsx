'use client';
import React, { useState, useEffect } from 'react';
import { FaBell, FaCheckCircle, FaTrash, FaExclamationCircle, FaBook, FaCalendarAlt, FaUserGraduate, FaMoneyBillWave, FaTrophy, FaVideo, FaPlus, FaEnvelope, FaTimes, FaUsers, FaBroadcastTower, FaUser } from 'react-icons/fa';
import Link from 'next/link';
import { toast } from 'react-toastify';
import axios from 'axios';
import GroupSelector from '@/components/notifications/GroupSelector';

interface Notification {
  id: string; // Changed to string to accommodate composite IDs like "individual-X" or "group-Y"
  title: string;
  content: string;
  type: string;
  read: boolean;
  createdAt: string;
  link?: string;
  isGroupNotification: boolean; // Ensure this is always present
  sender?: {
    id: number;
    name: string;
    username: string;
  };
}

const NotificationsPage = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [users, setUsers] = useState<Array<{id: number, username: string, profile?: {name: string}}>>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [notificationForm, setNotificationForm] = useState({
    title: '',
    content: '',
    type: 'GENERAL',
    userId: '',
    link: ''
  });
  const [formLoading, setFormLoading] = useState(false);

  // حالات النظام المحسن
  const [notificationMode, setNotificationMode] = useState<'individual' | 'bulk'>('individual');
  const [groupSelection, setGroupSelection] = useState<{
    groupType: string;
    targetRole?: string;
    targetUserIds?: number[];
    recipientCount: number;
  } | null>(null);
  const [bulkNotificationForm, setBulkNotificationForm] = useState({
    title: '',
    content: '',
    type: 'GENERAL',
    priority: 'MEDIUM',
    scheduledAt: '',
    link: ''
  });

  // جلب معلومات المستخدم
  const fetchUserInfo = async () => {
    try {
      const response = await axios.get('/api/users/me');
      setUserRole((response.data as { role: string }).role);
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  };

  // جلب المستخدمين (للمسؤولين والمعلمين والموظفين فقط)
  const fetchUsers = async () => {
    if (userRole !== 'ADMIN' && userRole !== 'TEACHER' && userRole !== 'EMPLOYEE') return;

    try {
      setIsLoadingUsers(true);

      // جلب جميع المستخدمين بدون فلترة حسب الدور
      const response = await axios.get('/api/users?limit=1000');

      // التحقق من تنسيق الاستجابة
      let usersArray = [];

      if (Array.isArray(response.data)) {
        // إذا كانت الاستجابة مصفوفة مباشرة
        usersArray = response.data;
      } else if (response.data && Array.isArray(response.data.users)) {
        // إذا كانت الاستجابة كائن يحتوي على مصفوفة users
        usersArray = response.data.users;
      }

      if (usersArray.length > 0) {
        // ترتيب المستخدمين حسب الاسم
        const sortedUsers = usersArray.sort((a: any, b: any) => {
          const nameA = a.profile?.name || a.name || a.username;
          const nameB = b.profile?.name || b.name || b.username;
          return nameA.localeCompare(nameB, 'ar');
        });

        setUsers(sortedUsers);
      } else {
        setUsers([]);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('فشل في جلب المستخدمين');
      setUsers([]);
    } finally {
      setIsLoadingUsers(false);
    }
  };

  // جلب الإشعارات
  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const filterParam = filter !== 'all' ? `&type=${filter}` : '';
      const response = await axios.get(`/api/notifications?page=${page}&limit=10${filterParam}`);

      // Type assertion for response data structure
      const responseData = response.data as {
        notifications: Notification[],
        unreadCount: number,
        pagination: { totalPages: number }
      };
      setNotifications(responseData.notifications);
      setUnreadCount(responseData.unreadCount);
      setTotalPages(responseData.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setError('حدث خطأ أثناء جلب الإشعارات. يرجى المحاولة مرة أخرى.');
      toast.error('فشل في جلب الإشعارات');
    } finally {
      setIsLoading(false);
    }
  };

  // تحديث حالة الإشعار (مقروء/غير مقروء)
  const markAsRead = async (id: string) => {
    try {
      const [type, actualId] = id.split('-');
      if (type === 'individual') {
        await axios.patch(`/api/notifications/${actualId}`, { read: true });
      } else if (type === 'group') {
        await axios.patch(`/api/notifications/recipient/${actualId}`, { read: true });
      } else {
        throw new Error('Invalid notification ID format');
      }

      // تحديث حالة الإشعار في القائمة
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === id ? { ...notification, read: true } : notification
        )
      );

      // تحديث عدد الإ��عارات غير المقروءة
      setUnreadCount(prev => Math.max(0, prev - 1));

      toast.success('تم تحديد الإشعار كمقروء');
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast.error('فشل في تحديث حالة الإشعار');
    }
  };

  // حذف إشعار
  const deleteNotification = async (id: string) => {
    try {
      const [type, actualId] = id.split('-');
      if (type === 'individual') {
        await axios.delete(`/api/notifications/${actualId}`);
      } else if (type === 'group') {
        await axios.delete(`/api/notifications/recipient/${actualId}`);
      } else {
        throw new Error('Invalid notification ID format');
      }

      // حذف الإشعار من القائمة
      const updatedNotifications = notifications.filter(notification => notification.id !== id);
      setNotifications(updatedNotifications);

      // تحديث عدد الإشعارات غير المقروءة إذا كان الإشعار غير مقروء
      const wasUnread = notifications.find(n => n.id === id)?.read === false;
      if (wasUnread) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }

      toast.success('تم حذف الإشعار بنج��ح');
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast.error('فشل في حذف الإشعار');
    }
  };

  // تحديد جميع الإشعارات كمقروءة
  const markAllAsRead = async () => {
    try {
      console.log('Marking all notifications as read...');
      const response = await axios.post('/api/notifications/mark-all-read');
      console.log('Mark all as read response:', response.data);

      // تحديث حالة جميع الإشعارات في القائمة
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, read: true }))
      );

      // تحديث عدد الإشعارات غير المقروءة
      setUnreadCount(0);

      // إعادة جلب الإشعارات لضمان التحديث
      fetchNotifications();

      toast.success('تم تحديد جميع الإشعارات كمقروءة');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast.error('فشل في تحديث حالة الإشعارات');
    }
  };

  // التعامل مع تغيير حقول نموذج الإشعار
  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNotificationForm(prev => ({ ...prev, [name]: value }));
  };

  // التعامل مع تغيير حقول نموذج الإشعار الجماعي
  const handleBulkNotificationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setBulkNotificationForm(prev => ({ ...prev, [name]: value }));
  };

  // التعامل مع تغيير اختيار المجموعة
  const handleGroupSelectionChange = (selection: {
    groupType: string;
    targetRole?: string;
    targetUserIds?: number[];
    recipientCount: number;
  }) => {
    setGroupSelection(selection);
  };

  // إرسال إشعار جديد
  const handleCreateNotification = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من البيانات
    if (!notificationForm.title || !notificationForm.content || !notificationForm.userId) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setFormLoading(true);

    try {
      await axios.post('/api/notifications', {
        title: notificationForm.title,
        content: notificationForm.content,
        type: notificationForm.type,
        userId: parseInt(notificationForm.userId),
        link: notificationForm.link || undefined
      });

      // إعادة تعيين النموذج
      setNotificationForm({
        title: '',
        content: '',
        type: 'GENERAL',
        userId: '',
        link: ''
      });

      setShowCreateForm(false);
      toast.success('تم إنشاء الإشعار بنجاح');
      fetchNotifications(); // تحديث قائمة الإشعارات
    } catch (error) {
      console.error('Error creating notification:', error);
      toast.error('فشل في إنشاء الإشعار');
    } finally {
      setFormLoading(false);
    }
  };

  // إرسال إشعار جماعي
  const handleCreateBulkNotification = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من البيانات
    if (!bulkNotificationForm.title || !bulkNotificationForm.content || !groupSelection) {
      toast.error('يرجى ملء جميع الحقول المطلوبة واختيار المجموعة المستهدفة');
      return;
    }

    if (groupSelection.recipientCount === 0) {
      toast.error('لا يوجد مستلمين للإشعار');
      return;
    }

    setFormLoading(true);

    try {
      const requestData = {
        title: bulkNotificationForm.title,
        content: bulkNotificationForm.content,
        type: bulkNotificationForm.type,
        priority: bulkNotificationForm.priority,
        scheduledAt: bulkNotificationForm.scheduledAt || undefined,
        link: bulkNotificationForm.link || undefined,
        groupType: groupSelection.groupType,
        targetRole: groupSelection.targetRole,
        targetUserIds: groupSelection.targetUserIds
      };

      const response = await axios.post('/api/notifications/bulk', requestData);

      // إعادة تعيين النموذج
      setBulkNotificationForm({
        title: '',
        content: '',
        type: 'GENERAL',
        priority: 'MEDIUM',
        scheduledAt: '',
        link: ''
      });
      setGroupSelection(null);
      setShowCreateForm(false);

      toast.success(response.data.message || 'تم إنشاء الإشعار الجماعي بنجاح');
      fetchNotifications(); // تحديث قائمة الإشعارات
    } catch (error) {
      console.error('Error creating bulk notification:', error);
      toast.error('فشل في إنشاء الإشعار الجماعي');
    } finally {
      setFormLoading(false);
    }
  };

  // جلب الإشعارات عند تحميل الصفحة أو تغيير الفلتر أو الصفحة
  useEffect(() => {
    fetchNotifications();
    fetchUserInfo();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter, page]);

  // جلب المستخدمين عند تغيير دور المستخدم
  useEffect(() => {
    if (userRole === 'ADMIN' || userRole === 'TEACHER' || userRole === 'EMPLOYEE') {
      fetchUsers();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userRole]);

  // الحصول على أيقونة الإشعار حسب النوع
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'LESSON':
        return <FaBook className="text-blue-500" />;
      case 'EXAM':
        return <FaUserGraduate className="text-red-500" />;
      case 'ATTENDANCE':
        return <FaCalendarAlt className="text-primary-color" />;
      case 'PAYMENT':
        return <FaMoneyBillWave className="text-yellow-500" />;
      case 'ACHIEVEMENT':
        return <FaTrophy className="text-purple-500" />;
      case 'REMOTE_CLASS':
        return <FaVideo className="text-indigo-500" />;
      default:
        return <FaBell className="text-gray-500" />;
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="container mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] p-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-white">الإشعارات</h1>
                <p className="text-white opacity-80">
                  {unreadCount > 0 ? `لديك ${unreadCount} إشعارات غير مقروءة` : 'جميع الإشعارات مقروءة'}
                </p>
              </div>
              <div className="flex space-x-2 space-x-reverse">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="bg-white text-[var(--primary-color)] px-4 py-2 rounded-md hover:bg-gray-100 transition-colors"
                  >
                    تحديد الكل كمقروء
                  </button>
                )}
                {(userRole === 'ADMIN' || userRole === 'TEACHER' || userRole === 'EMPLOYEE') && (
                  <button
                    onClick={() => setShowCreateForm(!showCreateForm)}
                    className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center"
                  >
                    <FaPlus className="ml-1" />
                    {showCreateForm ? 'إلغاء' : 'إنشاء إشعار'}
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Create Notification Form */}
          {showCreateForm && (
            <div className="p-4 bg-gray-50 border-b border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold text-gray-800">إنشاء إشعار جديد</h2>

                {/* اختيار نوع الإشعار */}
                <div className="flex bg-white rounded-lg p-1 border">
                  <button
                    type="button"
                    onClick={() => setNotificationMode('individual')}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center ${
                      notificationMode === 'individual'
                        ? 'bg-[var(--primary-color)] text-white'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <FaEnvelope className="ml-2" />
                    إشعار فردي
                  </button>
                  <button
                    type="button"
                    onClick={() => setNotificationMode('bulk')}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors flex items-center ${
                      notificationMode === 'bulk'
                        ? 'bg-[var(--primary-color)] text-white'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <FaBroadcastTower className="ml-2" />
                    إشعار جماعي
                  </button>
                </div>
              </div>

              {/* نموذج الإشعار الفردي */}
              {notificationMode === 'individual' && (
                <form onSubmit={handleCreateNotification}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      عنوان الإشعار <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      value={notificationForm.title}
                      onChange={handleNotificationChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                      نوع الإشعار <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="type"
                      name="type"
                      value={notificationForm.type}
                      onChange={handleNotificationChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      required
                    >
                      <option value="GENERAL">عام</option>
                      <option value="LESSON">درس</option>
                      <option value="EXAM">امتحان</option>
                      <option value="ATTENDANCE">حضور</option>
                      <option value="PAYMENT">مدفوعات</option>
                      <option value="ACHIEVEMENT">إنجاز</option>
                      <option value="REMOTE_CLASS">فصل افتراضي</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="userId" className="block text-sm font-medium text-gray-700 mb-1">
                      المستخدم <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="userId"
                      name="userId"
                      value={notificationForm.userId}
                      onChange={handleNotificationChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      required
                    >
                      <option value="">اختر المستخدم</option>
                      {isLoadingUsers ? (
                        <option disabled>جاري تحميل المستخدمين...</option>
                      ) : users.length > 0 ? (
                        users.map(user => (
                          <option key={user.id} value={user.id}>
                            {user.profile?.name || user.username} ({user.role})
                          </option>
                        ))
                      ) : (
                        <option disabled>لا يوجد مستخدمين</option>
                      )}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="link" className="block text-sm font-medium text-gray-700 mb-1">
                      الرابط (اختياري)
                    </label>
                    <input
                      type="text"
                      id="link"
                      name="link"
                      value={notificationForm.link}
                      onChange={handleNotificationChange}
                      placeholder="مثال: /exams/123"
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                      محتوى الإشعار <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="content"
                      name="content"
                      value={notificationForm.content}
                      onChange={handleNotificationChange}
                      rows={3}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => setShowCreateForm(false)}
                    className="mr-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center"
                  >
                    <FaTimes className="ml-1" />
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    disabled={formLoading}
                    className="px-4 py-2 bg-[var(--primary-color)] text-white rounded-md hover:bg-[var(--secondary-color)] transition-colors flex items-center"
                  >
                    {formLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري الإنشاء...
                      </>
                    ) : (
                      <>
                        <FaEnvelope className="ml-1" />
                        إنشاء الإشعار
                      </>
                    )}
                  </button>
                </div>
              </form>
              )}

              {/* نموذج الإشعار الجماعي */}
              {notificationMode === 'bulk' && (
                <form onSubmit={handleCreateBulkNotification}>
                  {/* اختيار المجموعة المستهدفة */}
                  <div className="mb-6">
                    <GroupSelector
                      onSelectionChange={handleGroupSelectionChange}
                      disabled={formLoading}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label htmlFor="bulk-title" className="block text-sm font-medium text-gray-700 mb-1">
                        عنوان الإشعار <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        id="bulk-title"
                        name="title"
                        value={bulkNotificationForm.title}
                        onChange={handleBulkNotificationChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="bulk-type" className="block text-sm font-medium text-gray-700 mb-1">
                        نوع الإشعار <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="bulk-type"
                        name="type"
                        value={bulkNotificationForm.type}
                        onChange={handleBulkNotificationChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                        required
                      >
                        <option value="GENERAL">عام</option>
                        <option value="LESSON">درس</option>
                        <option value="EXAM">امتحان</option>
                        <option value="ATTENDANCE">حضور</option>
                        <option value="PAYMENT">مدفوعات</option>
                        <option value="ACHIEVEMENT">إنجاز</option>
                        <option value="REMOTE_CLASS">فصل افتراضي</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="bulk-priority" className="block text-sm font-medium text-gray-700 mb-1">
                        الأولوية
                      </label>
                      <select
                        id="bulk-priority"
                        name="priority"
                        value={bulkNotificationForm.priority}
                        onChange={handleBulkNotificationChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      >
                        <option value="LOW">منخفضة</option>
                        <option value="MEDIUM">متوسطة</option>
                        <option value="HIGH">عالية</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="bulk-scheduledAt" className="block text-sm font-medium text-gray-700 mb-1">
                        جدولة الإرسال (اختياري)
                      </label>
                      <input
                        type="datetime-local"
                        id="bulk-scheduledAt"
                        name="scheduledAt"
                        value={bulkNotificationForm.scheduledAt}
                        onChange={handleBulkNotificationChange}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      />
                    </div>

                    <div>
                      <label htmlFor="bulk-link" className="block text-sm font-medium text-gray-700 mb-1">
                        الرابط (اختياري)
                      </label>
                      <input
                        type="text"
                        id="bulk-link"
                        name="link"
                        value={bulkNotificationForm.link}
                        onChange={handleBulkNotificationChange}
                        placeholder="مثال: /exams/123"
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label htmlFor="bulk-content" className="block text-sm font-medium text-gray-700 mb-1">
                        محتوى الإشعار <span className="text-red-500">*</span>
                      </label>
                      <textarea
                        id="bulk-content"
                        name="content"
                        value={bulkNotificationForm.content}
                        onChange={handleBulkNotificationChange}
                        rows={3}
                        className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                        required
                      />
                    </div>
                  </div>

                  {/* معلومات الإرسال */}
                  {groupSelection && (
                    <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <FaUsers className="text-[var(--primary-color)] ml-2" />
                          <span className="text-sm font-medium text-gray-700">
                            سيتم إرسال الإشعار لـ <span className="font-bold text-[var(--primary-color)]">{groupSelection.recipientCount}</span> مستلم
                          </span>
                        </div>
                        {bulkNotificationForm.scheduledAt && (
                          <span className="text-xs text-gray-600">
                            مجدول للإرسال: {new Date(bulkNotificationForm.scheduledAt).toLocaleString('ar-EG')}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => setShowCreateForm(false)}
                      className="mr-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center"
                    >
                      <FaTimes className="ml-1" />
                      إلغاء
                    </button>
                    <button
                      type="submit"
                      disabled={formLoading || !groupSelection || groupSelection.recipientCount === 0}
                      className="px-4 py-2 bg-[var(--primary-color)] text-white rounded-md hover:bg-[var(--secondary-color)] transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {formLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                          جاري الإرسال...
                        </>
                      ) : (
                        <>
                          <FaBroadcastTower className="ml-1" />
                          {bulkNotificationForm.scheduledAt ? 'جدولة الإشعار' : 'إرسال الإشعار الجماعي'}
                        </>
                      )}
                    </button>
                  </div>
                </form>
              )}
            </div>
          )}

          {/* Filters */}
          <div className="border-b border-gray-200 p-4">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => { setFilter('all'); setPage(1); }}
                className={`px-3 py-1 rounded-full text-sm ${filter === 'all' ? 'bg-[var(--primary-color)] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                الكل
              </button>

              {/* الفصول الافتراضية - للجميع */}
              <button
                onClick={() => { setFilter('REMOTE_CLASS'); setPage(1); }}
                className={`px-3 py-1 rounded-full text-sm ${filter === 'REMOTE_CLASS' ? 'bg-indigo-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                الفصول الافتراضية
              </button>

              {/* الدروس - للطلاب والمعلمين والمشرفين */}
              {(userRole === 'STUDENT' || userRole === 'TEACHER' || userRole === 'ADMIN') && (
                <button
                  onClick={() => { setFilter('LESSON'); setPage(1); }}
                  className={`px-3 py-1 rounded-full text-sm ${filter === 'LESSON' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  الدروس
                </button>
              )}

              {/* الامتحانات - للطلاب والمعلمين والمشرفين */}
              {(userRole === 'STUDENT' || userRole === 'TEACHER' || userRole === 'ADMIN') && (
                <button
                  onClick={() => { setFilter('EXAM'); setPage(1); }}
                  className={`px-3 py-1 rounded-full text-sm ${filter === 'EXAM' ? 'bg-red-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  الامتحانات
                </button>
              )}

              {/* المدفوعات - للطلاب وأولياء الأمور والمشرفين */}
              {(userRole === 'STUDENT' || userRole === 'PARENT' || userRole === 'ADMIN') && (
                <button
                  onClick={() => { setFilter('PAYMENT'); setPage(1); }}
                  className={`px-3 py-1 rounded-full text-sm ${filter === 'PAYMENT' ? 'bg-yellow-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  المدفوعات
                </button>
              )}

              {/* الإنجازات - للطلاب والمعلمين والمشرفين */}
              {(userRole === 'STUDENT' || userRole === 'TEACHER' || userRole === 'ADMIN') && (
                <button
                  onClick={() => { setFilter('ACHIEVEMENT'); setPage(1); }}
                  className={`px-3 py-1 rounded-full text-sm ${filter === 'ACHIEVEMENT' ? 'bg-purple-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  الإنجازات
                </button>
              )}

              {/* الحضور - للطلاب والمعلمين والمشرفين */}
              {(userRole === 'STUDENT' || userRole === 'TEACHER' || userRole === 'ADMIN') && (
                <button
                  onClick={() => { setFilter('ATTENDANCE'); setPage(1); }}
                  className={`px-3 py-1 rounded-full text-sm ${filter === 'ATTENDANCE' ? 'bg-primary-color text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  الحضور
                </button>
              )}
            </div>
          </div>

          {/* Notifications List */}
          <div className="divide-y divide-gray-200">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center h-64 text-center p-4">
                <FaExclamationCircle className="text-red-500 text-4xl mb-4" />
                <p className="text-gray-600">{error}</p>
                <button
                  onClick={fetchNotifications}
                  className="mt-4 bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors"
                >
                  إعادة المحاولة
                </button>
              </div>
            ) : notifications.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-center p-4">
                <FaBell className="text-gray-400 text-4xl mb-4" />
                <p className="text-gray-600">لا توجد إشعارات</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-gray-50 transition-colors ${!notification.read ? 'bg-blue-50' : ''}`}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="mr-3 flex-1">
                      <div className="flex justify-between">
                        <h3 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-600'}`}>
                          {notification.title}
                        </h3>
                        <span className="text-xs text-gray-500">{formatDate(notification.createdAt)}</span>
                      </div>
                      <p className="mt-1 text-sm text-gray-600">{notification.content}</p>

                      {/* معلومات المرسل */}
                      {notification.sender && (
                        <div className="mt-2 flex items-center text-xs text-gray-500">
                          <FaUser className="ml-1" />
                          <span>من: {notification.sender.name}</span>
                          {notification.isGroupNotification && (
                            <span className="mr-2 bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                              إشعار جماعي
                            </span>
                          )}
                        </div>
                      )}

                      <div className="mt-2 flex justify-between items-center">
                        <div>
                          {notification.link && (
                            <Link
                              href={notification.link}
                              className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] text-sm font-medium"
                            >
                              عرض التفاصيل
                            </Link>
                          )}
                        </div>
                        <div className="flex space-x-2 space-x-reverse">
                          {!notification.read && (
                            <button
                              onClick={() => markAsRead(notification.id)}
                              className="text-blue-500 hover:text-blue-700"
                              title="تحديد كمقروء"
                            >
                              <FaCheckCircle />
                            </button>
                          )}
                          <button
                            onClick={() => deleteNotification(notification.id)}
                            className="text-red-500 hover:text-red-700 mr-2"
                            title="حذف"
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          {!isLoading && !error && notifications.length > 0 && (
            <div className="p-4 border-t border-gray-200">
              <div className="flex justify-center items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => setPage(prev => Math.max(1, prev - 1))}
                  disabled={page === 1}
                  className={`px-3 py-1 rounded-md ${page === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  السابق
                </button>
                <span className="text-sm text-gray-600">
                  صفحة {page} من {totalPages}
                </span>
                <button
                  onClick={() => setPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={page === totalPages}
                  className={`px-3 py-1 rounded-md ${page === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                >
                  التالي
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationsPage;

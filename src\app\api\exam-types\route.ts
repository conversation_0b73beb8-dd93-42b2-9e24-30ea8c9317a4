import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/exam-types
export async function GET() {
  try {
    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    const examTypes = await prisma.examType.findMany({
      include: {
        _count: {
          select: {
            exams: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json({
      data: examTypes,
      success: true,
      message: 'تم جلب أنواع الامتحانات بنجاح'
    });
  } catch (error) {
    console.error('Error fetching exam types:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب أنواع الامتحانات',
      success: false
    }, { status: 500 });
  }
}

// POST /api/exam-types
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, evaluationType } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !evaluationType) {
      return NextResponse.json({
        error: 'الاسم ونوع التقييم مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة نوع التقييم
    const validEvaluationTypes = ['ORAL', 'WRITTEN', 'PRACTICAL'];
    if (!validEvaluationTypes.includes(evaluationType)) {
      return NextResponse.json({
        error: 'نوع التقييم غير صالح. يجب أن يكون أحد القيم التالية: ORAL, WRITTEN, PRACTICAL',
        success: false
      }, { status: 400 });
    }

    const examType = await prisma.examType.create({
      data: {
        name,
        description,
        evaluationType
      }
    });

    return NextResponse.json({
      data: examType,
      success: true,
      message: 'تم إنشاء نوع الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error creating exam type:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إنشاء نوع الامتحان',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/exam-types
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, description, evaluationType } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !name || !evaluationType) {
      return NextResponse.json({
        error: 'المعرف والاسم ونوع التقييم مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة نوع التقييم
    const validEvaluationTypes = ['ORAL', 'WRITTEN', 'PRACTICAL'];
    if (!validEvaluationTypes.includes(evaluationType)) {
      return NextResponse.json({
        error: 'نوع التقييم غير صالح. يجب أن يكون أحد القيم التالية: ORAL, WRITTEN, PRACTICAL',
        success: false
      }, { status: 400 });
    }

    const examType = await prisma.examType.update({
      where: { id: parseInt(id) },
      data: {
        name,
        description,
        evaluationType
      }
    });

    return NextResponse.json({
      data: examType,
      success: true,
      message: 'تم تحديث نوع الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error updating exam type:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث نوع الامتحان',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/exam-types
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف نوع الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود امتحانات مرتبطة بهذا النوع
    const examCount = await prisma.exam.count({
      where: { typeId: parseInt(id) }
    });

    if (examCount > 0) {
      return NextResponse.json({
        error: `لا يمكن حذف نوع الامتحان لأنه مرتبط بـ ${examCount} امتحان`,
        success: false
      }, { status: 400 });
    }

    await prisma.examType.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف نوع الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error deleting exam type:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف نوع الامتحان',
      success: false
    }, { status: 500 });
  }
}

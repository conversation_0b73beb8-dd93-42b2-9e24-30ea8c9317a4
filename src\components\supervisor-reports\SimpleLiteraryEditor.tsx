'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FileText, RefreshCw, Eye, Plus, Type, List, Hash } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface SimpleLiteraryEditorProps {
  value: string;
  onChange: (value: string) => void;
  periodStart: Date;
  periodEnd: Date;
  isLoading?: boolean;
}

interface ReportSection {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'list' | 'stats';
}

export default function SimpleLiteraryEditor({
  value,
  onChange,
  periodStart,
  periodEnd,
  isLoading = false
}: SimpleLiteraryEditorProps) {
  const [sections, setSections] = useState<ReportSection[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [viewMode, setViewMode] = useState<'edit' | 'preview'>('edit');

  // تحويل النص إلى أقسام عند التحميل
  useEffect(() => {
    if (value && sections.length === 0) {
      parseContentToSections(value);
    }
  }, [value]);

  // تحديث النص عند تغيير الأقسام
  useEffect(() => {
    if (sections.length > 0) {
      const newContent = generateContentFromSections(sections);
      onChange(newContent);
    }
  }, [sections]);

  // تحليل المحتوى إلى أقسام
  const parseContentToSections = (content: string) => {
    if (!content.trim()) {
      // إنشاء أقسام افتراضية
      setSections([
        {
          id: '1',
          title: 'المقدمة',
          content: '',
          type: 'text'
        },
        {
          id: '2',
          title: 'الإحصائيات العامة',
          content: '',
          type: 'stats'
        },
        {
          id: '3',
          title: 'الأنشطة والفعاليات',
          content: '',
          type: 'list'
        },
        {
          id: '4',
          title: 'التوصيات',
          content: '',
          type: 'list'
        }
      ]);
      return;
    }

    // تحليل بسيط للمحتوى الموجود
    const parsedSections: ReportSection[] = [];
    const lines = content.split('\n');
    let currentSection: ReportSection | null = null;

    lines.forEach((line, index) => {
      if (line.includes('<h2>') || line.includes('<h3>')) {
        // حفظ القسم السابق
        if (currentSection) {
          parsedSections.push(currentSection);
        }
        // بدء قسم جديد
        const title = line.replace(/<[^>]*>/g, '').trim();
        currentSection = {
          id: (parsedSections.length + 1).toString(),
          title,
          content: '',
          type: 'text'
        };
      } else if (currentSection && line.trim()) {
        currentSection.content += line + '\n';
      }
    });

    // إضافة القسم الأخير
    if (currentSection) {
      parsedSections.push(currentSection);
    }

    setSections(parsedSections.length > 0 ? parsedSections : [
      {
        id: '1',
        title: 'المقدمة',
        content: content,
        type: 'text'
      }
    ]);
  };

  // إنشاء المحتوى من الأقسام
  const generateContentFromSections = (sections: ReportSection[]) => {
    return sections.map(section => {
      let html = `<h2 style="color: #1e40af; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">${section.title}</h2>\n`;
      
      if (section.type === 'stats') {
        // تنسيق الإحصائيات
        const lines = section.content.split('\n').filter(line => line.trim());
        html += '<div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">\n';
        html += '<ul style="list-style: none; padding: 0;">\n';
        lines.forEach(line => {
          if (line.trim()) {
            html += `<li style="margin: 8px 0; padding: 5px; background: white; border-radius: 4px;">📊 ${line.trim()}</li>\n`;
          }
        });
        html += '</ul>\n</div>\n';
      } else if (section.type === 'list') {
        // تنسيق القوائم
        const lines = section.content.split('\n').filter(line => line.trim());
        html += '<div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 15px 0;">\n';
        html += '<ul style="margin: 10px 0;">\n';
        lines.forEach(line => {
          if (line.trim()) {
            html += `<li style="margin: 5px 0;">• ${line.trim()}</li>\n`;
          }
        });
        html += '</ul>\n</div>\n';
      } else {
        // تنسيق النص العادي
        const paragraphs = section.content.split('\n').filter(p => p.trim());
        html += '<div style="background: #fafafa; padding: 15px; border-radius: 8px; margin: 15px 0;">\n';
        paragraphs.forEach(paragraph => {
          if (paragraph.trim()) {
            html += `<p style="font-size: 16px; line-height: 1.8; margin: 10px 0;">${paragraph.trim()}</p>\n`;
          }
        });
        html += '</div>\n';
      }
      
      return html;
    }).join('\n');
  };

  // تحميل البيانات التلقائية
  const loadAutoData = async () => {
    setIsLoadingData(true);
    try {
      const response = await fetch(
        `/api/reports/literary?startDate=${periodStart.toISOString()}&endDate=${periodEnd.toISOString()}`
      );
      
      if (response.ok) {
        const result = await response.json();
        const data = result.data;
        
        // تحديث قسم الإحصائيات
        const statsSection = sections.find(s => s.title.includes('الإحصائيات'));
        if (statsSection) {
          statsSection.content = [
            `إجمالي الطلاب: ${data.generalStats?.totalStudents || 0}`,
            `إجمالي المعلمين: ${data.generalStats?.totalTeachers || 0}`,
            `إجمالي الأقسام: ${data.generalStats?.totalClasses || 0}`,
            `إجمالي الحفاظ: ${data.generalStats?.totalMemorizers || 0}`,
            `إجمالي الأنشطة: ${data.generalStats?.totalActivities || 0}`
          ].join('\n');
          
          setSections([...sections]);
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  // تحديث قسم
  const updateSection = (id: string, field: 'title' | 'content', value: string) => {
    setSections(prev => prev.map(section => 
      section.id === id ? { ...section, [field]: value } : section
    ));
  };

  // إضافة قسم جديد
  const addSection = (type: 'text' | 'list' | 'stats') => {
    const newSection: ReportSection = {
      id: Date.now().toString(),
      title: 'قسم جديد',
      content: '',
      type
    };
    setSections(prev => [...prev, newSection]);
  };

  // حذف قسم
  const removeSection = (id: string) => {
    setSections(prev => prev.filter(section => section.id !== id));
  };

  // إضافة قوالب سريعة
  const insertTemplate = (templateType: string) => {
    let template = '';
    
    switch (templateType) {
      case 'introduction':
        template = `بسم الله الرحمن الرحيم، والحمد لله رب العالمين، والصلاة والسلام على أشرف المرسلين.

يسرنا أن نقدم لكم هذا التقرير الشامل عن أنشطة وإنجازات المدرسة القرآنية خلال الفترة من ${format(periodStart, 'PPP', { locale: ar })} إلى ${format(periodEnd, 'PPP', { locale: ar })}.

نحمد الله تعالى على ما وفقنا إليه من إنجازات وما يسر لنا من أعمال في خدمة كتاب الله الكريم وتعليم أبنائنا الطلاب.`;
        break;
        
      case 'achievements':
        template = `تحقيق نسبة حضور عالية للطلاب
تنظيم مسابقات قرآنية متنوعة
إقامة دورات تدريبية للمعلمين
تطوير المناهج التعليمية
تحسين البيئة التعليمية`;
        break;
        
      case 'activities':
        template = `مسابقة حفظ القرآن الكريم
ورشة تجويد القرآن
رحلة تعليمية
محاضرة دينية
نشاط ثقافي`;
        break;
        
      case 'recommendations':
        template = `زيادة عدد الدورات التدريبية
تطوير الوسائل التعليمية
تعزيز التواصل مع أولياء الأمور
إقامة المزيد من الأنشطة التفاعلية
تحسين البنية التحتية`;
        break;
    }
    
    // إضافة القالب كقسم جديد
    const newSection: ReportSection = {
      id: Date.now().toString(),
      title: templateType === 'introduction' ? 'المقدمة' : 
             templateType === 'achievements' ? 'الإنجازات' :
             templateType === 'activities' ? 'الأنشطة' : 'التوصيات',
      content: template,
      type: templateType === 'introduction' ? 'text' : 'list'
    };
    
    setSections(prev => [...prev, newSection]);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          📘 محرر التقرير الأدبي المبسط
        </CardTitle>
        <CardDescription>
          محرر سهل الاستخدام لإنشاء التقرير الأدبي بأقسام منظمة
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* شريط الأدوات */}
        <div className="flex items-center gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={loadAutoData}
            disabled={isLoadingData}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoadingData ? 'animate-spin' : ''}`} />
            تحميل البيانات
          </Button>
          
          <Separator orientation="vertical" className="h-6" />
          
          <Button
            variant={viewMode === 'edit' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('edit')}
            className="flex items-center gap-2"
          >
            <Type className="h-4 w-4" />
            تحرير
          </Button>
          
          <Button
            variant={viewMode === 'preview' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('preview')}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            معاينة
          </Button>
          
          <Separator orientation="vertical" className="h-6" />
          
          <Badge variant="secondary" className="flex items-center gap-1">
            <Hash className="h-3 w-3" />
            {sections.length} قسم
          </Badge>
        </div>

        {/* قوالب سريعة */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">قوالب سريعة:</h4>
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('introduction')}
            >
              مقدمة
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('achievements')}
            >
              إنجازات
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('activities')}
            >
              أنشطة
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('recommendations')}
            >
              توصيات
            </Button>
          </div>
        </div>

        {/* أزرار إضافة أقسام */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">إضافة قسم جديد:</h4>
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => addSection('text')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              نص عادي
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addSection('list')}
              className="flex items-center gap-2"
            >
              <List className="h-4 w-4" />
              قائمة
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => addSection('stats')}
              className="flex items-center gap-2"
            >
              <Hash className="h-4 w-4" />
              إحصائيات
            </Button>
          </div>
        </div>

        <Separator />

        {/* المحرر */}
        {viewMode === 'edit' ? (
          <div className="space-y-4">
            {sections.map((section, index) => (
              <Card key={section.id} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Label htmlFor={`title-${section.id}`} className="text-sm font-medium">
                        عنوان القسم:
                      </Label>
                      <Input
                        id={`title-${section.id}`}
                        value={section.title}
                        onChange={(e) => updateSection(section.id, 'title', e.target.value)}
                        className="w-auto"
                      />
                      <Badge variant="secondary">
                        {section.type === 'text' ? 'نص' : section.type === 'list' ? 'قائمة' : 'إحصائيات'}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeSection(section.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      حذف
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={section.content}
                    onChange={(e) => updateSection(section.id, 'content', e.target.value)}
                    placeholder={
                      section.type === 'text' ? 'اكتب النص هنا...' :
                      section.type === 'list' ? 'اكتب كل عنصر في سطر منفصل...' :
                      'اكتب الإحصائيات، كل إحصائية في سطر منفصل...'
                    }
                    rows={6}
                    className="resize-none"
                  />
                </CardContent>
              </Card>
            ))}
            
            {sections.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>لا توجد أقسام بعد</p>
                <p className="text-sm">استخدم الأزرار أعلاه لإضافة أقسام أو قوالب</p>
              </div>
            )}
          </div>
        ) : (
          <div className="border rounded-lg p-4 min-h-[500px] bg-white">
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: generateContentFromSections(sections) }}
            />
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="text-sm text-gray-500 space-y-1">
          <p>💡 نصائح:</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>استخدم القوالب السريعة لبداية سريعة</li>
            <li>اكتب كل عنصر في القوائم في سطر منفصل</li>
            <li>استخدم "تحميل البيانات" لملء الإحصائيات تلقائياً</li>
            <li>استخدم "معاينة" لرؤية النتيجة النهائية</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

# تكامل نظام الفواتير مع المدفوعات حسب الولي

## 📋 نظرة عامة
تم تطوير نظام فواتير متكامل يدعم الفواتير الفردية والجماعية، مع تكامل كامل مع نظام المدفوعات حسب الولي.

## 🎯 الأهداف المحققة

### 1. **دعم الفواتير الجماعية**
- إمكانية إنشاء فاتورة واحدة لولي أمر تشمل جميع أبنائه
- ربط الفواتير بالأولياء مباشرة بدلاً من الطلاب فقط
- تبسيط عملية إدارة المدفوعات للأولياء

### 2. **تكامل مع نظام المدفوعات**
- عرض الفواتير الجماعية في صفحة المدفوعات حسب الولي
- حساب دقيق للمبالغ المطلوبة والمدفوعة
- فلترة ذكية للفواتير حسب النوع والشهر

### 3. **مرونة في الإدارة**
- دعم الفواتير الفردية والجماعية في نفس النظام
- إمكانية إنشاء فواتير جماعية لعدة أولياء دفعة واحدة
- حسابات تلقائية بناءً على عدد الأطفال

## 🛠️ التحديثات التقنية

### 1. **قاعدة البيانات (Prisma Schema)**

#### تحديث جدول الفواتير:
```prisma
model Invoice {
  id              Int             @id @default(autoincrement())
  studentId       Int?            // جعل الطالب اختياري للفواتير الجماعية
  student         Student?        @relation(fields: [studentId], references: [id], onDelete: Cascade)
  parentId        Int?            // إضافة ربط بالولي للفواتير الجماعية
  parent          Parent?         @relation(fields: [parentId], references: [id], onDelete: Cascade)
  amount          Float
  dueDate         DateTime
  issueDate       DateTime        @default(now())
  month           Int             // شهر الفاتورة (1-12)
  year            Int             // سنة الفاتورة
  description     String?         // وصف الفاتورة
  status          InvoiceStatus   @default(UNPAID)
  type            InvoiceType     @default(INDIVIDUAL) // نوع الفاتورة (فردية أو جماعية)
  // ... باقي الحقول
}

enum InvoiceType {
  INDIVIDUAL      // فاتورة فردية لطالب واحد
  FAMILY          // فاتورة جماعية لولي أمر (جميع أبنائه)
}
```

#### تحديث جدول الأولياء:
```prisma
model Parent {
  // ... الحقول الموجودة
  invoices          Invoice[]           // الفواتير الجماعية للولي
  // ... باقي الحقول
}
```

### 2. **APIs الجديدة**

#### API الفواتير الجماعية (`/api/invoices/family/route.ts`):
- **POST**: إنشاء فاتورة جماعية لولي أمر واحد
- **GET**: جلب الفواتير الجماعية مع فلترة متقدمة

#### API الفواتير الجماعية المتعددة (`/api/invoices/family/bulk/route.ts`):
- **POST**: إنشاء فواتير جماعية لعدة أولياء دفعة واحدة
- **GET**: جلب أولياء الأمور المؤهلين للفوترة الجماعية

### 3. **تحديث API المدفوعات حسب الولي**

#### فلترة ذكية للفواتير:
```typescript
// فلترة الفواتير الفردية للطلاب
invoices: {
  where: {
    OR: [
      { type: 'INDIVIDUAL' },
      { type: { not: 'FAMILY' } }
    ]
  }
}

// جلب الفواتير الجماعية للولي
invoices: {
  where: {
    type: 'FAMILY'
  }
}
```

#### حساب الإجماليات المحسن:
```typescript
// حساب الفواتير الجماعية للولي
const familyInvoices = parent.invoices || [];
const familyTotalRequired = familyInvoices
  .filter(invoice => invoice.status !== 'CANCELLED')
  .reduce((sum, invoice) => sum + invoice.amount, 0);

// إضافة الفواتير الجماعية للإجماليات
totalRequired += familyTotalRequired;
totalPaid += familyTotalPaid;
```

## 🎨 تحديثات واجهة المستخدم

### 1. **عرض الفواتير الجماعية**
- قسم منفصل للفواتير الجماعية في نافذة تفاصيل الولي
- عرض حالة كل فاتورة (مدفوعة، جزئية، متأخرة، غير مدفوعة)
- تفاصيل المبلغ والمدفوع والمتبقي لكل فاتورة

### 2. **واجهات محسنة**
```typescript
interface FamilyInvoice {
  id: number;
  amount: number;
  dueDate: string;
  description: string;
  status: string;
  totalPaid: number;
}

interface ParentPaymentSummary {
  // ... الحقول الموجودة
  familyInvoices?: FamilyInvoice[];
}
```

### 3. **عرض تفصيلي للفواتير**
- بطاقات منفصلة لكل فاتورة جماعية
- ألوان مختلفة حسب حالة الفاتورة
- تواريخ الاستحقاق والوصف
- حسابات المبالغ المدفوعة والمتبقية

## 📊 ميزات النظام الجديد

### 1. **الفواتير الجماعية**
- **إنشاء سهل**: فاتورة واحدة لجميع أبناء الولي
- **حساب تلقائي**: المبلغ = عدد الأطفال × المبلغ لكل طفل
- **مرونة في التسعير**: مبالغ مختلفة لكل ولي حسب الحاجة

### 2. **الفواتير المتعددة**
- **إنشاء جماعي**: فواتير لعدة أولياء في عملية واحدة
- **خيارات متنوعة**: مبلغ ثابت أو حسب عدد الأطفال
- **فلترة ذكية**: عرض الأولياء المؤهلين فقط

### 3. **التكامل الكامل**
- **عرض موحد**: الفواتير الفردية والجماعية في مكان واحد
- **حسابات دقيقة**: إجماليات صحيحة تشمل جميع أنواع الفواتير
- **فلترة متقدمة**: حسب الشهر والحالة والنوع

## 🔄 سير العمل الجديد

### 1. **إنشاء فاتورة جماعية**
```
1. اختيار ولي الأمر
2. تحديد المبلغ (ثابت أو حسب عدد الأطفال)
3. تحديد تاريخ الاستحقاق والشهر
4. إضافة وصف للفاتورة
5. حفظ الفاتورة الجماعية
```

### 2. **عرض المدفوعات حسب الولي**
```
1. جلب الفواتير الفردية للطلاب
2. جلب الفواتير الجماعية للولي
3. حساب الإجماليات من النوعين
4. عرض التفاصيل في واجهة موحدة
```

### 3. **الدفع والتسوية**
```
1. عرض جميع الديون (فردية + جماعية)
2. إمكانية الدفع للفواتير الفردية أو الجماعية
3. تحديث حالة الفواتير تلقائياً
4. حساب المتبقي بدقة
```

## 📈 الفوائد المحققة

### 1. **للإدارة**
- **تبسيط العمليات**: فاتورة واحدة بدلاً من عدة فواتير
- **توفير الوقت**: إنشاء فواتير جماعية لعدة أولياء
- **دقة أكبر**: حسابات تلقائية تقلل الأخطاء
- **مرونة عالية**: دعم الفواتير الفردية والجماعية

### 2. **للأولياء**
- **وضوح أكبر**: فاتورة واحدة تشمل جميع الأطفال
- **سهولة الدفع**: مبلغ واحد بدلاً من مبالغ متعددة
- **تتبع أفضل**: حالة واضحة للمدفوعات

### 3. **للنظام**
- **تكامل كامل**: ربط سلس بين الفواتير والمدفوعات
- **مرونة في التطوير**: إمكانية إضافة أنواع فواتير جديدة
- **أداء محسن**: استعلامات محسنة لقاعدة البيانات

## 🚀 الخطوات التالية

### 1. **تطبيق التحديثات**
```bash
# تطبيق تحديثات قاعدة البيانات
npx prisma db push

# إعادة تشغيل الخادم
npm run dev
```

### 2. **اختبار النظام**
- إنشاء فواتير جماعية تجريبية
- اختبار عرض المدفوعات حسب الولي
- التأكد من دقة الحسابات

### 3. **تدريب المستخدمين**
- شرح الميزات الجديدة
- توضيح الفرق بين الفواتير الفردية والجماعية
- تدريب على إنشاء الفواتير الجماعية

## 🎉 الخلاصة

تم تطوير نظام فواتير متكامل يدعم:
- ✅ الفواتير الجماعية للأولياء
- ✅ التكامل الكامل مع نظام المدفوعات
- ✅ واجهات محسنة وسهلة الاستخدام
- ✅ حسابات دقيقة وتلقائية
- ✅ مرونة عالية في الإدارة

النظام الآن جاهز لتبسيط إدارة الفواتير والمدفوعات بشكل كبير.

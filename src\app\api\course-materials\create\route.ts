import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { verifyToken } from '@/utils/verifyToken';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

// POST /api/course-materials/create - إنشاء مادة تعليمية جديدة
export async function POST(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const userData = await verifyToken(request);
    if (!userData) {
      return NextResponse.json(
        { success: false, message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userData.id
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // استخراج البيانات من الطلب
    const formData = await request.formData();
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const type = formData.get('type') as string;
    const courseId = parseInt(formData.get('courseId') as string);

    // التحقق من البيانات المطلوبة
    if (!title || !type || isNaN(courseId)) {
      return NextResponse.json(
        { success: false, message: "البيانات غير مكتملة" },
        { status: 400 }
      );
    }

    // التحقق من أن المقرر ينتمي للمعلم
    const classSubject = await prisma.classSubject.findUnique({
      where: {
        id: courseId
      },
      include: {
        teacherSubject: true
      }
    });

    if (!classSubject) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على المقرر" },
        { status: 404 }
      );
    }

    if (classSubject.teacherSubject.teacherId !== teacher.id) {
      return NextResponse.json(
        { success: false, message: "غير مصرح لك بالوصول إلى هذا المقرر" },
        { status: 403 }
      );
    }

    let url = '';

    // معالجة الملف أو الرابط
    if (type === 'link') {
      url = formData.get('url') as string;
      if (!url) {
        return NextResponse.json(
          { success: false, message: "الرابط مطلوب" },
          { status: 400 }
        );
      }
    } else {
      const file = formData.get('file') as File;
      if (!file) {
        return NextResponse.json(
          { success: false, message: "الملف مطلوب" },
          { status: 400 }
        );
      }

      // إنشاء اسم فريد للملف
      const fileExtension = file.name.split('.').pop();
      const fileName = `${uuidv4()}.${fileExtension}`;
      const filePath = join(process.cwd(), 'public', 'uploads', fileName);

      // حفظ الملف
      const fileBuffer = await file.arrayBuffer();
      await writeFile(filePath, Buffer.from(fileBuffer));

      // تعيين مسار الملف
      url = `/uploads/${fileName}`;
    }

    // إنشاء المادة التعليمية في قاعدة البيانات
    const material = await prisma.courseMaterial.create({
      data: {
        title,
        description,
        type,
        url,
        classSubjectId: courseId
      }
    });

    // تنسيق البيانات للعرض
    const formattedMaterial = {
      id: material.id,
      title: material.title,
      description: material.description || '',
      type: material.type,
      url: material.url,
      createdAt: material.createdAt.toISOString().split('T')[0]
    };

    return NextResponse.json({
      success: true,
      material: formattedMaterial,
      message: "تم إنشاء المادة التعليمية بنجاح"
    });
  } catch (error) {
    console.error('Error creating course material:', error);
    return NextResponse.json(
      { success: false, message: "حدث خطأ أثناء إنشاء المادة التعليمية" },
      { status: 500 }
    );
  }
}

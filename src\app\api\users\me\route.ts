import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { verifyToken } from '@/utils/verifyToken';

// Get current user information
export async function GET(request: NextRequest) {
    try {
        const userId = await verifyToken(request);
        if (!userId) {
            return NextResponse.json(
                { message: "Unauthorized" },
                { status: 401 }
            );
        }

        const user = await prisma.user.findUnique({
            where: { id: userId.id },
            select: {
                id: true,
                username: true,
                role: true
            }
        });

        if (!user) {
            return NextResponse.json(
                { message: "User not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(user);
    } catch (error: unknown) {
        console.error('Error fetching current user:', error);
        return NextResponse.json(
            { message: "Internal server error" },
            { status: 500 }
        );
    }
}
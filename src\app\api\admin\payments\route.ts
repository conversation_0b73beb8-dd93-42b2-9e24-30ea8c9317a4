import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import {
  validatePaymentData,
  sanitizeInput,
  updateInvoiceStatus,
  getOrCreatePaymentMethod,
  validateStudentExists,
  generateReceiptNumber
} from '@/utils/payment-utils';

// POST /api/admin/payments - تسجيل دفعة جديدة باسم الولي
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 بدء تسجيل دفعة جديدة...');

    // التحقق من صلاحيات المستخدم
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      console.log('❌ لا يوجد token');
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    console.log('👤 بيانات المستخدم:', userData?.username, userData?.role);

    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE')) {
      console.log('❌ صلاحيات غير كافية');
      return NextResponse.json(
        { message: "غير مصرح بالوصول إلى هذه البيانات" },
        { status: 403 }
      );
    }

    const body = await request.json();
    let { studentId, amount, paymentMethod, notes, receiptNumber, parentName, month } = body;

    // تنظيف المدخلات
    studentId = sanitizeInput(studentId);
    amount = sanitizeInput(amount);
    paymentMethod = sanitizeInput(paymentMethod);
    notes = sanitizeInput(notes);
    receiptNumber = sanitizeInput(receiptNumber);
    parentName = sanitizeInput(parentName);
    month = sanitizeInput(month);

    console.log('📋 بيانات الدفعة:', {
      studentId,
      amount,
      paymentMethod,
      parentName,
      receiptNumber,
      month
    });

    // التحقق من صحة البيانات
    const validationErrors = validatePaymentData({
      studentId,
      amount,
      paymentMethod,
      notes,
      receiptNumber,
      month
    });

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          success: false,
          message: "بيانات غير صحيحة",
          errors: validationErrors
        },
        { status: 400 }
      );
    }

    // التحقق من وجود التلميذ
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: {
        guardian: true,
        invoices: {
          where: {
            status: {
              in: ['UNPAID', 'PARTIALLY_PAID', 'OVERDUE']
            }
          },
          orderBy: {
            dueDate: 'asc'
          }
        }
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "التلميذ غير موجود" },
        { status: 404 }
      );
    }

    console.log('👨‍🎓 بيانات التلميذ:', {
      name: student.name,
      guardianName: student.guardian?.name,
      unpaidInvoicesCount: student.invoices.length
    });

    // البحث عن طريقة الدفع أو إنشاؤها
    const paymentMethodRecord = await getOrCreatePaymentMethod(paymentMethod);
    console.log('💳 طريقة الدفع:', paymentMethodRecord.name);

    // إنشاء الدفعة مع ملاحظة واضحة أنها باسم الولي
    const finalNotes = `${notes || ''}\n\n✅ دفعة مسجلة باسم الولي: ${parentName || student.guardian?.name || 'غير محدد'}${month ? `\n📅 للشهر: ${month}` : ''}`.trim();

    // تحديد تاريخ الدفعة بناءً على الشهر المحدد
    let paymentDate = new Date();
    if (month) {
      // إنشاء تاريخ من الشهر المحدد (أول يوم في الشهر)
      paymentDate = new Date(`${month}-01`);
    }

    // إنشاء رقم إيصال إذا لم يتم تحديده
    const finalReceiptNumber = receiptNumber || generateReceiptNumber();

    const payment = await prisma.payment.create({
      data: {
        studentId: studentId,
        amount: parseFloat(amount),
        date: paymentDate,
        status: 'PAID',
        paymentMethodId: paymentMethodRecord.id,
        paymentMethodName: paymentMethod,
        notes: finalNotes,
        receiptNumber: finalReceiptNumber
      },
      include: {
        student: {
          include: {
            guardian: true
          }
        },
        paymentMethod: true
      }
    });

    console.log('💰 تم إنشاء الدفعة:', {
      id: payment.id,
      amount: payment.amount,
      studentName: payment.student.name,
      guardianName: payment.student.guardian?.name
    });

    // ربط الدفعة بالفواتير المستحقة (فردية أو جماعية)
    let remainingAmount = parseFloat(amount);
    const updatedInvoices = [];
    let linkedToInvoice = false;

    // 1. أولاً: البحث عن الفواتير الجماعية للولي
    if (student.guardian) {
      const familyInvoices = await prisma.invoice.findMany({
        where: {
          parentId: student.guardian.id,
          type: 'FAMILY',
          status: { not: 'CANCELLED' }
        },
        orderBy: {
          dueDate: 'asc'
        }
      });

      console.log(`🔍 تم العثور على ${familyInvoices.length} فاتورة جماعية للولي ${student.guardian.name}`);

      for (const familyInvoice of familyInvoices) {
        if (remainingAmount <= 0) break;

        // حساب المبلغ المدفوع مسبقاً لهذه الفاتورة الجماعية
        const paidAmount = await prisma.payment.aggregate({
          where: {
            invoiceId: familyInvoice.id,
            status: 'PAID'
          },
          _sum: {
            amount: true
          }
        });

        const totalPaid = paidAmount._sum.amount || 0;
        const remainingInvoiceAmount = familyInvoice.amount - totalPaid;

        if (remainingInvoiceAmount > 0) {
          const paymentForInvoice = Math.min(remainingAmount, remainingInvoiceAmount);

          // ربط الدفعة بالفاتورة الجماعية
          await prisma.payment.update({
            where: { id: payment.id },
            data: {
              invoiceId: familyInvoice.id,
              notes: `${finalNotes}\n\n📄 مربوطة بالفاتورة الجماعية #${familyInvoice.id} - مبلغ ${paymentForInvoice} دج`
            }
          });

          remainingAmount -= paymentForInvoice;
          linkedToInvoice = true;

          // تحديث حالة الفاتورة الجماعية
          const newStatus = await updateInvoiceStatus(familyInvoice.id);

          updatedInvoices.push({
            invoiceId: familyInvoice.id,
            paidAmount: paymentForInvoice,
            newStatus,
            type: 'FAMILY'
          });

          console.log(`📄 تم ربط الدفعة بالفاتورة الجماعية ${familyInvoice.id}: دُفع ${paymentForInvoice} دج، الحالة الجديدة: ${newStatus}`);
          break; // ربط دفعة واحدة فقط بأول فاتورة جماعية مستحقة
        }
      }
    }

    // 2. إذا لم يتم الربط بفاتورة جماعية، ابحث عن الفواتير الفردية
    if (!linkedToInvoice) {
      for (const invoice of student.invoices) {
        if (remainingAmount <= 0) break;

        // حساب المبلغ المدفوع مسبقاً لهذه الفاتورة
        const paidAmount = await prisma.payment.aggregate({
          where: {
            invoiceId: invoice.id,
            status: 'PAID'
          },
          _sum: {
            amount: true
          }
        });

        const totalPaid = paidAmount._sum.amount || 0;
        const remainingInvoiceAmount = invoice.amount - totalPaid;

        if (remainingInvoiceAmount > 0) {
          const paymentForInvoice = Math.min(remainingAmount, remainingInvoiceAmount);

          // ربط الدفعة الرئيسية بالفاتورة الفردية
          await prisma.payment.update({
            where: { id: payment.id },
            data: {
              invoiceId: invoice.id,
              notes: `${finalNotes}\n\n📄 مربوطة بالفاتورة الفردية #${invoice.id} - مبلغ ${paymentForInvoice} دج`
            }
          });

          remainingAmount -= paymentForInvoice;

          // تحديث حالة الفاتورة باستخدام الدالة المساعدة
          const newStatus = await updateInvoiceStatus(invoice.id);

          updatedInvoices.push({
            invoiceId: invoice.id,
            paidAmount: paymentForInvoice,
            newStatus,
            type: 'INDIVIDUAL'
          });

          console.log(`📄 تم ربط الدفعة بالفاتورة الفردية ${invoice.id}: دُفع ${paymentForInvoice} دج، الحالة الجديدة: ${newStatus}`);

          // ربط دفعة واحدة فقط بأول فاتورة مستحقة
          break;
        }
      }
    }

    console.log('✅ تم إنجاز تسجيل الدفعة بنجاح');

    return NextResponse.json({
      success: true,
      message: `تم تسجيل دفعة بقيمة ${amount} دج باسم الولي: ${parentName || student.guardian?.name}`,
      payment: {
        id: payment.id,
        amount: payment.amount,
        date: payment.date,
        studentName: payment.student.name,
        guardianName: payment.student.guardian?.name,
        paymentMethod: payment.paymentMethod?.name,
        receiptNumber: payment.receiptNumber,
        notes: payment.notes
      },
      updatedInvoices,
      remainingAmount
    });

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدفعة:', error);
    return NextResponse.json(
      {
        success: false,
        message: "حدث خطأ أثناء تسجيل الدفعة",
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/payments - جلب المدفوعات (للمراجعة)
export async function GET(request: NextRequest) {
  try {
    // التحقق من صلاحيات المستخدم
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE')) {
      return NextResponse.json(
        { message: "غير مصرح بالوصول إلى هذه البيانات" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const payments = await prisma.payment.findMany({
      take: limit,
      skip: offset,
      orderBy: {
        date: 'desc'
      },
      include: {
        student: {
          include: {
            guardian: true
          }
        },
        paymentMethod: true,
        invoice: true
      }
    });

    const total = await prisma.payment.count();

    return NextResponse.json({
      success: true,
      payments,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    });

  } catch (error) {
    console.error('Error fetching payments:', error);
    return NextResponse.json(
      {
        success: false,
        message: "حدث خطأ أثناء جلب المدفوعات",
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

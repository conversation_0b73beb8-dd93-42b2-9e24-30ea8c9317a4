import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET() {
  try {
    const classes = await prisma.classe.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json(classes);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء استرجاع البيانات" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير اسم الفصل بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    const existingClass = await prisma.classe.findFirst({
      where: { name }
    });

    if (existingClass) {
      return NextResponse.json(
        { message: "يوجد فصل بهذا الاسم مسبقاً" },
        { status: 400 }
      );
    }

    const newClass = await prisma.classe.create({
      data: { name }
    });

    return NextResponse.json(newClass, { status: 201 });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء الفصل" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name } = body;

    if (!id || !name || typeof id !== 'number' || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير معرف واسم الفصل بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    const existingClass = await prisma.classe.findUnique({
      where: { id }
    });

    if (!existingClass) {
      return NextResponse.json(
        { message: "الفصل غير موجود" },
        { status: 404 }
      );
    }

    const duplicateClass = await prisma.classe.findFirst({
      where: {
        name,
        NOT: { id }
      }
    });

    if (duplicateClass) {
      return NextResponse.json(
        { message: "يوجد فصل آخر بهذا الاسم" },
        { status: 400 }
      );
    }

    const updatedClass = await prisma.classe.update({
      where: { id },
      data: { name }
    });

    return NextResponse.json(updatedClass);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث الفصل" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const numericId = id ? parseInt(id) : null;

    if (!id || !numericId || isNaN(numericId)) {
      return NextResponse.json(
        { message: "يجب توفير معرف الفصل بتنسيق صحيح" },
        { status: 400 }
      );
    }

    const classExists = await prisma.classe.findUnique({
      where: { id: numericId }
    });

    if (!classExists) {
      return NextResponse.json(
        { message: "الفصل غير موجود" },
        { status: 404 }
      );
    }

    await prisma.classe.delete({
      where: { id: numericId }
    });

    return NextResponse.json(
      { message: "تم حذف الفصل بنجاح" },
      { status: 200 }
    );
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف الفصل" },
      { status: 500 }
    );
  }
}
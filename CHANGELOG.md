# سجل التغييرات - Praetorian.ring

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.1.0] - 2024-01-02

### إضافات جديدة

#### التطبيقات العملية
- **ReconDash**: لوحة تحكم استطلاعية بواجهة رسومية
  - واجهة رسومية متكاملة باستخدام libui.ring
  - فحص شامل للمنافذ مع جلب البانر
  - تحليل SSL/TLS للمنافذ الآمنة
  - زاحف ويب لاكتشاف الروابط والمسارات
  - عرض منظم للنتائج في ألسنة منفصلة
  - ملف تكوين قابل للتخصيص

- **DirHunter**: أداة تخمين المجلدات والملفات لسطر الأوامر
  - واجهة سطر أوامر احترافية مع معلمات متقدمة
  - دعم قوائم الكلمات المخصصة
  - فحص متعدد الخيوط (مخطط)
  - دعم امتدادات متعددة للملفات
  - شريط تقدم تفاعلي
  - نتائج ملونة باستخدام rogueutil.ring
  - قائمة كلمات شاملة (250+ كلمة)

#### أدوات التشغيل والإدارة
- **مشغل التطبيقات**: واجهة موحدة لتشغيل جميع التطبيقات
- **ملفات تشغيل سريع**:
  - `run_praetorian.bat` لنظام Windows
  - `run_praetorian.sh` لأنظمة Linux/Mac
- **اختبار شامل للتطبيقات**: فحص جميع التطبيقات والمتطلبات
- **اختبار سريع**: فحص أساسي للمكتبة والتطبيقات

### التحسينات

#### تحسينات المكتبة الأساسية
- استبدال العامل الثلاثي `? :` بدالة `iif()` لتوافق أفضل
- تحسين فحص المكتبات المطلوبة عند التشغيل
- إضافة رسائل تحذير واضحة للمكتبات المفقودة
- تحسين معالجة الأخطاء في جميع الوحدات

#### تحسينات التوثيق
- توثيق شامل للتطبيقات الجديدة
- إرشادات تفصيلية للتثبيت والاستخدام
- أمثلة عملية لكل تطبيق
- دليل استكشاف الأخطاء وإصلاحها

### الملفات الجديدة
```
applications/
├── ReconDash/
│   ├── ReconDash.ring
│   ├── config.ring
│   └── run.bat
├── DirHunter/
│   ├── DirHunter.ring
│   ├── wordlist.txt
│   └── run_example.bat
├── launcher.ring
├── test_applications.ring
└── README.md
run_praetorian.bat
run_praetorian.sh
quick_test.ring
```

### المتطلبات الجديدة
- `libui.ring` - مطلوب لتشغيل ReconDash
- `rogueutil.ring` - اختياري لألوان DirHunter
- `ringregex.ring` - اختياري للبحث المتقدم

## [1.0.0] - 2024-01-01

### إضافات جديدة
- إطلاق الإصدار الأول من مكتبة Praetorian.ring
- وحدة الشبكات الكاملة مع فاحص المنافذ وصانع الحزم
- وحدة الويب مع عميل HTTP وزاحف الويب وأداة Fuzzing
- وحدة التشفير مع مدقق SSL/TLS
- نظام تسجيل احترافي مع مستويات مختلفة
- مجموعة شاملة من الأدوات المساعدة
- أمثلة تطبيقية شاملة
- توثيق مفصل باللغة العربية

#### وحدة الشبكات (Network Module)
- `PraetorianNetworkScanner`: فاحص المنافذ متعدد الخيوط
  - فحص TCP Connect للمنافذ
  - جلب البانر من الخدمات
  - تحديد نوع الخدمة تلقائياً
  - فحص المنافذ الشائعة والنطاقات المخصصة
  - تقارير مفصلة للنتائج

- `PraetorianPacketCrafter`: صانع الحزم الخام
  - بناء حزم TCP مخصصة
  - بناء حزم ICMP
  - حساب checksums تلقائياً
  - دعم Raw Sockets (يتطلب صلاحيات المدير)

#### وحدة الويب (Web Module)
- `PraetorianHTTPClient`: عميل HTTP متقدم
  - دعم جميع طرق HTTP (GET, POST, PUT, DELETE, HEAD)
  - إدارة الجلسات والكوكيز
  - دعم HTTPS مع خيارات التحقق من SSL
  - تحميل الملفات
  - رؤوس مخصصة وUser-Agent

- `PraetorianWebCrawler`: زاحف الويب الذكي
  - زحف متعدد المستويات
  - استخراج الروابط من HTML و JavaScript
  - تحليل النماذج وحقول الإدخال
  - فلترة الروابط والامتدادات
  - تحويل الروابط النسبية إلى مطلقة

- `PraetorianWebFuzzer`: أداة Fuzzing متقدمة
  - اكتشاف المجلدات والملفات المخفية
  - دعم قوائم الكلمات المخصصة
  - فحص متعدد الخيوط للسرعة
  - تحليل أكواد الاستجابة
  - تقارير مفصلة للنتائج

#### وحدة التشفير (Crypto Module)
- `PraetorianSSLChecker`: مدقق SSL/TLS شامل
  - فحص تفاصيل الشهادات
  - التحقق من صحة وانتهاء الشهادات
  - فحص البروتوكولات المدعومة
  - اكتشاف الشيفرات الضعيفة
  - تحليل المشاكل الأمنية وإنشاء التوصيات

#### الوحدات الأساسية (Core Modules)
- `PraetorianUtils`: مجموعة أدوات مساعدة
  - تشفير وفك تشفير Base64
  - ترميز وفك ترميز URL و HTML
  - توليد كلمات مرور قوية ونصوص عشوائية
  - التحقق من صحة عناوين IP وتحويلها
  - حساب hash (MD5, SHA256)

- `PraetorianLogger`: نظام تسجيل احترافي
  - مستويات تسجيل متعددة (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  - تسجيل في الملفات ووحدة التحكم
  - تسجيل العمليات والنتائج
  - إحصائيات السجل
  - تنسيق مخصص للرسائل

### الأمثلة والتوثيق
- `examples/basic_scan.ring`: مثال شامل للاستخدام الأساسي
- `examples/web_audit.ring`: مثال تدقيق تطبيق ويب كامل
- `examples/ssl_audit.ring`: مثال تدقيق SSL/TLS متقدم
- `README.md`: دليل شامل باللغة العربية
- `test_praetorian.ring`: مجموعة اختبارات شاملة

### التحسينات التقنية
- معالجة أخطاء تحميل المكتبات المطلوبة
- فحص توفر المكتبات عند التشغيل
- رسائل تحذير واضحة للمكتبات المفقودة
- تحسين الأداء والذاكرة
- معالجة شاملة للأخطاء

### الأمان والموثوقية
- التحقق من صحة المدخلات
- معالجة آمنة للبيانات الحساسة
- تحذيرات من الاستخدام غير الأخلاقي
- توثيق المتطلبات الأمنية
- إرشادات الاستخدام الآمن

### المتطلبات
- Ring 1.23 أو أحدث
- openssllib.ring (للتشفير وSSL)
- sockets.ring (لوظائف الشبكة)
- libcurl.ring (لعميل HTTP)
- threads.ring (للتعدد)

### المعروف
- Raw Sockets تتطلب صلاحيات المدير على معظم الأنظمة
- بعض وظائف SSL قد تحتاج لتحسينات إضافية
- الأداء قد يختلف حسب النظام والشبكة

### الخطط المستقبلية
- دعم IPv6
- وحدة قواعد البيانات
- أدوات تحليل البروتوكولات
- واجهة رسومية
- دعم المزيد من بروتوكولات الشبكة

---

## تنسيق سجل التغييرات

### أنواع التغييرات
- `إضافات جديدة` للميزات الجديدة
- `تغييرات` للتغييرات في الوظائف الموجودة
- `إهمال` للميزات التي ستتم إزالتها قريباً
- `إزالة` للميزات المحذوفة
- `إصلاحات` لإصلاح الأخطاء
- `أمان` لإصلاحات الثغرات الأمنية

### معلومات الإصدار
- التاريخ بتنسيق YYYY-MM-DD
- رقم الإصدار يتبع Semantic Versioning
- روابط للمقارنة بين الإصدارات (عند توفرها)

### إرشادات المساهمة
- أضف التغييرات تحت قسم "غير مُصدر" في أعلى الملف
- اتبع التنسيق الموجود
- اكتب وصفاً واضحاً للتغيير
- أضف رقم المشكلة إن وجد

---

**ملاحظة**: هذا سجل التغييرات للإصدار الأول. سيتم تحديثه مع كل إصدار جديد.

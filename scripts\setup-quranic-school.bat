@echo off
chcp 65001 >nul
cls

echo 🕌 مرحباً بكم في نظام إدارة المدرسة القرآنية
echo ============================================================
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت
    echo 💡 يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير مثبت
    pause
    exit /b 1
)

echo ✅ Node.js و npm متوفران
echo.

REM تثبيت التبعيات
echo 📦 تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo ✅ تم تثبيت التبعيات بنجاح
echo.

REM إعداد قاعدة البيانات
echo 🗄️ إعداد قاعدة البيانات...
call npx prisma generate
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء Prisma Client
    pause
    exit /b 1
)

call npx prisma db push
if %errorlevel% neq 0 (
    echo ❌ فشل في إعداد قاعدة البيانات
    pause
    exit /b 1
)

echo ✅ تم إعداد قاعدة البيانات بنجاح
echo.

REM تنفيذ البذور
echo 🌱 إنشاء البيانات التجريبية للمدرسة القرآنية...
call npm run seed:quranic
if %errorlevel% neq 0 (
    echo ❌ فشل في إنشاء البيانات التجريبية
    pause
    exit /b 1
)

echo.
echo 🎉 تم إعداد المدرسة القرآنية بنجاح!
echo.
echo 🚀 لبدء تشغيل النظام:
echo    npm run dev
echo.
echo 🔍 لعرض كشف درجات طالب:
echo    http://localhost:3000/admin/evaluation/student-report
echo.
echo 📚 لمراجعة الدليل الكامل:
echo    docs/QURANIC_SCHOOL_SETUP.md
echo.
echo 🤲 بارك الله فيكم ووفقكم في خدمة كتاب الله الكريم
echo ============================================================
echo.
echo اضغط أي مفتاح للمتابعة...
pause >nul

'use client';
import React from 'react';
import { useUserPermissions } from '@/hooks/useUserPermissions';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import { FaUsers, FaChalkboardTeacher, FaUserGraduate, FaClipboardCheck, FaBook, FaCalendarAlt, FaTrophy, FaBell } from 'react-icons/fa';

const EmployeeDashboard = () => {
  const { userPermissions, userRole, loading, hasPermission } = useUserPermissions();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل لوحة التحكم...</p>
        </div>
      </div>
    );
  }

  // إذا كان المستخدم مدير، إعادة توجيه للوحة الأدمن
  if (userRole === 'ADMIN') {
    window.location.href = '/admin';
    return null;
  }

  // إحصائيات سريعة حسب الصلاحيات
  const getAvailableStats = () => {
    const stats = [];

    if (hasPermission('admin.students.view')) {
      stats.push({
        title: 'الطلاب',
        count: '150',
        icon: FaUserGraduate,
        color: 'bg-blue-500',
        link: '/admin/students'
      });
    }

    if (hasPermission('admin.teachers.view')) {
      stats.push({
        title: 'المعلمين',
        count: '25',
        icon: FaChalkboardTeacher,
        color: 'bg-primary-color',
        link: '/admin/teachers'
      });
    }

    if (hasPermission('admin.parents.view')) {
      stats.push({
        title: 'أولياء الأمور',
        count: '120',
        icon: FaUsers,
        color: 'bg-purple-500',
        link: '/admin/parents'
      });
    }

    if (hasPermission('admin.attendance.view')) {
      stats.push({
        title: 'الحضور اليوم',
        count: '85%',
        icon: FaClipboardCheck,
        color: 'bg-yellow-500',
        link: '/admin/attendance'
      });
    }

    return stats;
  };

  const availableStats = getAvailableStats();

  return (
    <ProtectedRoute requiredPermission="admin.employee_dashboard.view">
      <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8" dir="rtl">
      <div className="max-w-7xl mx-auto">
        {/* ترحيب */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] px-6 py-8">
            <h1 className="text-3xl font-bold text-white mb-2">مرحباً بك في لوحة التحكم</h1>
            <p className="text-green-100">إدارة مهامك اليومية بسهولة وفعالية</p>
          </div>
        </div>

        {/* الإحصائيات السريعة */}
        {availableStats.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {availableStats.map((stat, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="p-6">
                  <div className="flex items-center">
                    <div className={`p-3 rounded-full ${stat.color}`}>
                      <stat.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="mr-4">
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.count}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <a
                      href={stat.link}
                      className="text-sm text-[var(--primary-color)] hover:text-[var(--secondary-color)] font-medium"
                    >
                      عرض التفاصيل ←
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* المهام السريعة */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* الإجراءات السريعة */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">الإجراءات السريعة</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {hasPermission('admin.students.view') && (
                  <a
                    href="/admin/students"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FaUserGraduate className="h-5 w-5 text-[var(--primary-color)] ml-3" />
                    <span className="text-sm font-medium text-gray-900">إدارة الطلاب</span>
                  </a>
                )}

                {hasPermission('admin.teachers.view') && (
                  <a
                    href="/admin/teachers"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FaChalkboardTeacher className="h-5 w-5 text-[var(--primary-color)] ml-3" />
                    <span className="text-sm font-medium text-gray-900">إدارة المعلمين</span>
                  </a>
                )}

                {hasPermission('admin.attendance.view') && (
                  <a
                    href="/admin/attendance"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FaClipboardCheck className="h-5 w-5 text-[var(--primary-color)] ml-3" />
                    <span className="text-sm font-medium text-gray-900">تسجيل الحضور</span>
                  </a>
                )}

                {hasPermission('admin.reports.view') && (
                  <a
                    href="/admin/reports"
                    className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FaBook className="h-5 w-5 text-[var(--primary-color)] ml-3" />
                    <span className="text-sm font-medium text-gray-900">التقارير</span>
                  </a>
                )}
              </div>
            </div>
          </div>

          {/* الأنشطة الأخيرة */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">الأنشطة الأخيرة</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <FaUserGraduate className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">تم إضافة طالب جديد</p>
                    <p className="text-xs text-gray-500">منذ ساعتين</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-full">
                    <FaClipboardCheck className="h-4 w-4 text-primary-color" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">تم تسجيل حضور الفصل الأول</p>
                    <p className="text-xs text-gray-500">منذ 3 ساعات</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-full">
                    <FaBell className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div className="mr-3">
                    <p className="text-sm font-medium text-gray-900">تذكير: اجتماع المعلمين غداً</p>
                    <p className="text-xs text-gray-500">منذ 5 ساعات</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* رسالة ترحيب للموظفين الجدد */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="p-6">
            <div className="flex items-center">
              <div className="p-3 bg-[var(--primary-color)] rounded-full">
                <FaTrophy className="h-6 w-6 text-white" />
              </div>
              <div className="mr-4">
                <h3 className="text-lg font-medium text-gray-900">مرحباً بك في فريق العمل!</h3>
                <p className="text-gray-600">
                  يمكنك الوصول إلى الصفحات المخصصة لك من خلال القائمة الجانبية.
                  إذا كنت بحاجة لصلاحيات إضافية، يرجى التواصل مع المدير.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* رسالة في حالة عدم وجود صلاحيات */}
        {availableStats.length === 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
            <div className="text-yellow-600 text-6xl mb-4">⚠️</div>
            <h3 className="text-lg font-medium text-yellow-800 mb-2">لا توجد صلاحيات مخصصة</h3>
            <p className="text-yellow-700">
              لم يتم تخصيص أي صلاحيات لحسابك بعد. يرجى التواصل مع المدير لتفعيل الصلاحيات المطلوبة.
            </p>
          </div>
        )}
      </div>
    </div>
    </ProtectedRoute>
  );
};

export default EmployeeDashboard;

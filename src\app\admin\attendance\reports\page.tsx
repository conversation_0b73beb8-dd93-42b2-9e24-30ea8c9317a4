'use client'

import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'react-toastify'
import { FaUserCheck, FaCalendarAlt, FaChartBar, FaFileExcel, FaFilePdf, FaArrowLeft, FaPaperPlane } from 'react-icons/fa'
import SendReportDialog from './send-report-dialog'
import Link from 'next/link'
import { exportToExcel, exportToPdf } from '@/utils/export-utils'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { useSchoolSettings, formatSchoolInfoForReports } from '@/utils/school-settings'

// تعريف الأنواع
type AttendanceStats = {
  totalRecords: number
  presentCount: number
  absentCount: number
  excusedCount: number
  presentRate: number
  absentRate: number
  excusedRate: number
  classeStats: {
    classeId: number
    classeName: string
    totalRecords: number
    presentCount: number
    absentCount: number
    excusedCount: number
    presentRate: number
    absentRate: number
    excusedRate: number
  }[]
  studentStats: {
    studentId: number
    studentName: string
    classeName: string
    totalRecords: number
    presentCount: number
    absentCount: number
    excusedCount: number
    presentRate: number
    absentRate: number
  }[]
  dateStats: {
    date: string
    totalRecords: number
    presentCount: number
    absentCount: number
    excusedCount: number
    presentRate: number
  }[]
}

type Class = {
  id: number
  name: string
}

export default function AttendanceReportsPage() {
  const [startDate, setStartDate] = useState(() => {
    const date = new Date()
    date.setMonth(date.getMonth() - 1) // شهر واحد للخلف
    return date.toISOString().split('T')[0]
  })
  const [endDate, setEndDate] = useState(new Date().toISOString().split('T')[0])
  const [selectedClass, setSelectedClass] = useState('all')
  const [classes, setClasses] = useState<Class[]>([])
  const [stats, setStats] = useState<AttendanceStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [isSendReportDialogOpen, setIsSendReportDialogOpen] = useState(false)
  const { settings: schoolSettings } = useSchoolSettings()

  // جلب الأقسام
  const fetchClasses = useCallback(async () => {
    try {
      const response = await fetch('/api/classes')
      if (!response.ok) throw new Error('فشل في جلب الأقسام')
      const data = await response.json()

      if (data && data.classes && Array.isArray(data.classes)) {
        setClasses(data.classes)
      } else if (Array.isArray(data)) {
        setClasses(data)
      } else {
        console.error('البيانات المستلمة ليست بالتنسيق المتوقع:', data)
        setClasses([])
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء جلب الأقسام')
      console.error(error)
      setClasses([])
    }
  }, [])

  // جلب إحصائيات الحضور
  const fetchAttendanceStats = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (startDate) params.append('startDate', startDate)
      if (endDate) params.append('endDate', endDate)
      if (selectedClass && selectedClass !== 'all') params.append('classeId', selectedClass)

      const response = await fetch(`/api/attendance/stats?${params.toString()}`)
      if (!response.ok) throw new Error('فشل في جلب إحصائيات الحضور')

      const data = await response.json()
      setStats(data)
    } catch (error) {
      toast.error('حدث خطأ أثناء جلب إحصائيات الحضور')
      console.error(error)
    } finally {
      setLoading(false)
    }
  }, [startDate, endDate, selectedClass])

  // تصدير البيانات إلى Excel
  const exportStatsToExcel = () => {
    if (!stats) {
      toast.error('لا توجد بيانات للتصدير')
      return
    }

    try {
      // تصدير إحصائيات الطلاب
      const studentData = stats.studentStats.map((student, index) => ({
        'الرقم': index + 1,
        'اسم الطالب': student.studentName,
        'القسم': student.classeName,
        'عدد الحضور': student.presentCount,
        'عدد الغياب': student.absentCount,
        'عدد الغياب بعذر': student.excusedCount,
        'نسبة الحضور': `${student.presentRate.toFixed(1)}%`,
        'إجمالي السجلات': student.totalRecords
      }))

      // تحديد عرض الأعمدة
      const columnWidths = [
        { wch: 5 },  // الرقم
        { wch: 25 }, // اسم الطالب
        { wch: 15 }, // القسم
        { wch: 12 }, // عدد الحضور
        { wch: 12 }, // عدد الغياب
        { wch: 15 }, // عدد الغياب بعذر
        { wch: 12 }, // نسبة الحضور
        { wch: 15 }  // إجمالي السجلات
      ]

      // تصدير البيانات
      const className = selectedClass && selectedClass !== 'all'
        ? classes.find(c => c.id.toString() === selectedClass)?.name || 'كل الأقسام'
        : 'كل الأقسام'

      exportToExcel(
        studentData,
        `تقرير_الحضور_${className}_${startDate}_${endDate}.xlsx`,
        'تقرير الحضور',
        columnWidths
      )
    } catch (error) {
      console.error('Error exporting stats to Excel:', error)
      toast.error('حدث خطأ أثناء تصدير البيانات')
    }
  }

  // تصدير البيانات إلى PDF
  const exportStatsToPdf = () => {
    if (!stats) {
      toast.error('لا توجد بيانات للتصدير')
      return
    }

    try {
      // إعداد بيانات الإحصائيات العامة
      const generalStats = [
        ['إجمالي السجلات', stats.totalRecords.toString()],
        ['عدد الحضور', stats.presentCount.toString()],
        ['عدد الغياب', stats.absentCount.toString()],
        ['عدد الغياب بعذر', stats.excusedCount.toString()],
        ['نسبة الحضور', `${stats.presentRate.toFixed(1)}%`],
        ['نسبة الغياب', `${stats.absentRate.toFixed(1)}%`],
        ['نسبة الغياب بعذر', `${stats.excusedRate.toFixed(1)}%`]
      ]

      // إعداد بيانات الأقسام
      const classeData = stats.classeStats.map((classe, index) => [
        (index + 1).toString(),
        classe.classeName,
        classe.totalRecords.toString(),
        classe.presentCount.toString(),
        classe.absentCount.toString(),
        classe.excusedCount.toString(),
        `${classe.presentRate.toFixed(1)}%`
      ])

      // إعداد بيانات الطلاب (أعلى 10 طلاب غياباً)
      const topAbsentStudents = [...stats.studentStats]
        .sort((a, b) => b.absentRate - a.absentRate)
        .slice(0, 10)
        .map((student, index) => [
          (index + 1).toString(),
          student.studentName,
          student.classeName,
          student.presentCount.toString(),
          student.absentCount.toString(),
          student.excusedCount.toString(),
          `${student.presentRate.toFixed(1)}%`
        ])

      // إعداد بيانات الرسوم البيانية
      const charts = [
        {
          title: 'نسب الحضور والغياب',
          type: 'pie' as const,
          data: {
            labels: ['حضور', 'غياب', 'غياب بعذر'],
            datasets: [{
              label: 'نسب الحضور والغياب',
              data: [stats.presentRate, stats.absentRate, stats.excusedRate],
              backgroundColor: ['var(--primary-color)', '#e74c3c', '#f1c40f']
            }]
          }
        },
        {
          title: 'إحصائيات الحضور حسب التاريخ',
          type: 'line' as const,
          data: {
            labels: stats.dateStats.map(d => d.date),
            datasets: [{
              label: 'نسبة الحضور',
              data: stats.dateStats.map(d => d.presentRate),
              borderColor: 'var(--primary-color)',
              backgroundColor: 'rgba(22, 155, 136, 0.2)'
            }]
          }
        }
      ]

      // تحديد اسم الملف
      const className = selectedClass && selectedClass !== 'all'
        ? classes.find(c => c.id.toString() === selectedClass)?.name || 'كل الأقسام'
        : 'كل الأقسام'

      const fileName = `تقرير_الحضور_${className}_${startDate}_${endDate}.pdf`

      // تصدير البيانات إلى PDF
      exportToPdf({
        title: `تقرير الحضور والغياب: ${className}`,
        fileName: fileName,
        schoolInfo: formatSchoolInfoForReports(schoolSettings),
        tables: [
          {
            title: 'الإحصائيات العامة',
            headers: ['البيان', 'القيمة'],
            data: generalStats,
            startY: 40
          },
          {
            title: 'إحصائيات الأقسام',
            headers: ['الرقم', 'القسم', 'إجمالي السجلات', 'الحضور', 'الغياب', 'الغياب بعذر', 'نسبة الحضور'],
            data: classeData
          },
          {
            title: 'الطلاب الأكثر غياباً',
            headers: ['الرقم', 'اسم الطالب', 'القسم', 'الحضور', 'الغياب', 'الغياب بعذر', 'نسبة الحضور'],
            data: topAbsentStudents
          }
        ],
        charts: charts,
        additionalContent: [
          {
            text: `الفترة: من ${startDate} إلى ${endDate}`,
            x: 105,
            y: 30,
            options: { align: 'center' }
          }
        ]
      })
    } catch (error) {
      console.error('Error exporting stats to PDF:', error)
      toast.error('حدث خطأ أثناء تصدير البيانات')
    }
  }

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchClasses()
  }, [fetchClasses])

  useEffect(() => {
    fetchAttendanceStats()
  }, [fetchAttendanceStats])

  return (
    <ProtectedRoute requiredPermission="admin.attendance.reports.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Link href="/admin/attendance">
            <Button
              variant="outline"
              className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white"
            >
              <FaArrowLeft className="ml-1" />
              العودة إلى سجل الحضور
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaChartBar className="text-[var(--primary-color)]" />
            تقارير الحضور والغياب
          </h1>
        </div>
        <div className="flex gap-2">
          <PermissionGuard requiredPermission="admin.attendance.reports.send">
            <Button
              onClick={() => setIsSendReportDialogOpen(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1"
            >
              <FaPaperPlane className="ml-1" />
              إرسال تقرير
            </Button>
          </PermissionGuard>
          <PermissionGuard requiredPermission="admin.attendance.reports.export">
            <Button
              onClick={exportStatsToExcel}
              className="bg-primary-color hover:bg-green-700 text-white flex items-center gap-1"
              disabled={!stats || loading}
            >
              <FaFileExcel className="ml-1" />
              تصدير Excel
            </Button>
          </PermissionGuard>
          <PermissionGuard requiredPermission="admin.attendance.reports.export">
            <Button
              onClick={exportStatsToPdf}
              className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-1"
              disabled={!stats || loading}
            >
              <FaFilePdf className="ml-1" />
              تصدير PDF
            </Button>
          </PermissionGuard>
        </div>
      </div>

      <div className="flex flex-wrap items-center gap-4 bg-white p-4 rounded-lg shadow-md mb-6">
        <div className="w-full md:w-auto flex items-center gap-2">
          <FaCalendarAlt className="text-[var(--primary-color)]" />
          <div className="flex items-center gap-2">
            <span>من:</span>
            <Input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-40 text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
            />
            <span>إلى:</span>
            <Input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-40 text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
            />
          </div>
        </div>

        <div className="w-full md:w-auto flex items-center gap-2">
          <FaUserCheck className="text-[var(--primary-color)]" />
          <div className="w-64">
            <Select
              value={selectedClass}
              onValueChange={(value) => {
                setSelectedClass(value)
              }}
            >
              <SelectTrigger className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                <SelectValue placeholder="اختر القسم" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الأقسام</SelectItem>
                {Array.isArray(classes) && classes.length > 0 ? (
                  classes.map((classe) => (
                    <SelectItem key={classe.id} value={classe.id.toString()}>
                      {classe.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-classes" disabled>
                    لا توجد أقسام متاحة
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <Button
          onClick={fetchAttendanceStats}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
          disabled={loading}
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              جاري التحميل...
            </>
          ) : (
            'عرض التقرير'
          )}
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
          <span className="mr-2">جاري تحميل البيانات...</span>
        </div>
      ) : stats ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* بطاقة الإحصائيات العامة */}
          <Card className="shadow-md border-t-4 border-t-[var(--primary-color)]">
            <CardHeader className="bg-gradient-to-r from-[#f8fffd] to-white">
              <CardTitle className="text-xl font-bold text-[var(--primary-color)]">
                الإحصائيات العامة
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span>نسبة الحضور</span>
                    <span className="font-bold text-[var(--primary-color)]">{stats.presentRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full bg-[var(--primary-color)]"
                      style={{ width: `${stats.presentRate}%` }}
                    ></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span>نسبة الغياب</span>
                    <span className="font-bold text-red-600">{stats.absentRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full bg-red-600"
                      style={{ width: `${stats.absentRate}%` }}
                    ></div>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span>نسبة الغياب بعذر</span>
                    <span className="font-bold text-yellow-600">{stats.excusedRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full bg-yellow-600"
                      style={{ width: `${stats.excusedRate}%` }}
                    ></div>
                  </div>
                </div>
                <div className="pt-2 border-t border-gray-200">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex flex-col">
                      <span className="text-gray-500">إجمالي السجلات</span>
                      <span className="font-bold">{stats.totalRecords}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-500">عدد الحضور</span>
                      <span className="font-bold text-[var(--primary-color)]">{stats.presentCount}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-500">عدد الغياب</span>
                      <span className="font-bold text-red-600">{stats.absentCount}</span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-gray-500">عدد الغياب بعذر</span>
                      <span className="font-bold text-yellow-600">{stats.excusedCount}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* بطاقة الطلاب الأكثر غياباً */}
          <Card className="shadow-md border-t-4 border-t-red-600">
            <CardHeader className="bg-gradient-to-r from-[#fff8f8] to-white">
              <CardTitle className="text-xl font-bold text-red-600">
                الطلاب الأكثر غياباً
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-3">
                {stats.studentStats
                  .sort((a, b) => b.absentCount - a.absentCount)
                  .slice(0, 5)
                  .map((student, index) => (
                    <div key={student.studentId} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="w-6 h-6 rounded-full bg-red-100 text-red-600 flex items-center justify-center text-xs font-bold ml-2">
                          {index + 1}
                        </span>
                        <div>
                          <div className="font-medium">{student.studentName}</div>
                          <div className="text-sm text-gray-500">{student.classeName}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-red-600">{student.absentCount} غياب</div>
                        <div className="text-sm text-gray-500">
                          نسبة الحضور: {student.presentRate.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* بطاقة الطلاب الأكثر حضوراً */}
          <Card className="shadow-md border-t-4 border-t-[var(--primary-color)]">
            <CardHeader className="bg-gradient-to-r from-[#f8fffd] to-white">
              <CardTitle className="text-xl font-bold text-[var(--primary-color)]">
                الطلاب الأكثر حضوراً
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-3">
                {stats.studentStats
                  .sort((a, b) => b.presentRate - a.presentRate)
                  .slice(0, 5)
                  .map((student, index) => (
                    <div key={student.studentId} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="w-6 h-6 rounded-full bg-green-100 text-primary-color flex items-center justify-center text-xs font-bold ml-2">
                          {index + 1}
                        </span>
                        <div>
                          <div className="font-medium">{student.studentName}</div>
                          <div className="text-sm text-gray-500">{student.classeName}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-primary-color">{student.presentRate.toFixed(1)}%</div>
                        <div className="text-sm text-gray-500">
                          {student.presentCount} حضور من {student.totalRecords}
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* بطاقة إحصائيات الأقسام */}
          <Card className="shadow-md border-t-4 border-t-blue-600 md:col-span-2 lg:col-span-3">
            <CardHeader className="bg-gradient-to-r from-[#f8f9ff] to-white">
              <CardTitle className="text-xl font-bold text-blue-600">
                إحصائيات الأقسام
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-200 px-4 py-2 text-right">القسم</th>
                      <th className="border border-gray-200 px-4 py-2 text-right">إجمالي السجلات</th>
                      <th className="border border-gray-200 px-4 py-2 text-right">الحضور</th>
                      <th className="border border-gray-200 px-4 py-2 text-right">الغياب</th>
                      <th className="border border-gray-200 px-4 py-2 text-right">الغياب بعذر</th>
                      <th className="border border-gray-200 px-4 py-2 text-right">نسبة الحضور</th>
                    </tr>
                  </thead>
                  <tbody>
                    {stats.classeStats.map((classe) => (
                      <tr key={classe.classeId} className="hover:bg-gray-50">
                        <td className="border border-gray-200 px-4 py-2">{classe.classeName}</td>
                        <td className="border border-gray-200 px-4 py-2">{classe.totalRecords}</td>
                        <td className="border border-gray-200 px-4 py-2 text-[var(--primary-color)] font-medium">{classe.presentCount}</td>
                        <td className="border border-gray-200 px-4 py-2 text-red-600 font-medium">{classe.absentCount}</td>
                        <td className="border border-gray-200 px-4 py-2 text-yellow-600 font-medium">{classe.excusedCount}</td>
                        <td className="border border-gray-200 px-4 py-2">
                          <div className="flex items-center">
                            <span className="font-bold ml-2">{classe.presentRate.toFixed(1)}%</span>
                            <div className="w-24 bg-gray-200 rounded-full h-2.5">
                              <div
                                className="h-2.5 rounded-full bg-[var(--primary-color)]"
                                style={{ width: `${classe.presentRate}%` }}
                              ></div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <FaChartBar className="text-gray-400 text-5xl mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-600 mb-2">لا توجد بيانات للعرض</h3>
          <p className="text-gray-500">
            يرجى تحديد الفترة الزمنية والقسم ثم الضغط على زر عرض التقرير لعرض إحصائيات الحضور والغياب.
          </p>
        </div>
      )}
      {/* مكون إرسال التقرير */}
      <SendReportDialog
        isOpen={isSendReportDialogOpen}
        onClose={() => setIsSendReportDialogOpen(false)}
        classes={classes}
      />
      </div>
    </ProtectedRoute>
  )
}

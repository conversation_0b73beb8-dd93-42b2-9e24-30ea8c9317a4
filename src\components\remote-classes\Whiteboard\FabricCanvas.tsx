'use client';

import React, { useRef, useEffect, useState } from 'react';

// تعريف واجهة للتعامل مع مكتبة Fabric.js
interface FabricCanvas {
  isDrawingMode: boolean;
  freeDrawingBrush: {
    width: number;
    color: string;
  };
  defaultCursor: string;
  backgroundColor: string;
  renderAll: () => void;
  clear: () => void;
  loadFromJSON: (json: string, callback: () => void) => void;
  toJSON: () => object;
  toDataURL: (options: { format: string; quality: number }) => string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  on: (event: string, callback: (...args: any[]) => void) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  off: (event: string, callback?: (...args: any[]) => void) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  add: (object: any) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setActiveObject: (object: any) => void;
  dispose: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getPointer: (event: any) => { x: number; y: number };
}

// واجهة لتعريف الكائنات الثابتة في مكتبة Fabric.js
interface FabricStatic {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Canvas: any; // كائن Canvas من Fabric.js
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  PencilBrush: any; // كائن فرشاة الرسم
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  IText: any; // كائن النص التفاعلي
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Rect: any; // كائن المستطيل
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Circle: any; // كائن الدائرة
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Line: any; // كائن الخط
}

interface FabricCanvasProps {
  width: number;
  height: number;
  onReady: (canvas: FabricCanvas, fabricLib: FabricStatic) => void;
}

/**
 * مكون لتهيئة Canvas باستخدام Fabric.js
 * يقوم بتحميل المكتبة بشكل آمن واستدعاء دالة onReady عند الانتهاء
 */
const FabricCanvasComponent: React.FC<FabricCanvasProps> = ({ width, height, onReady }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // استخدام مرجع للاحتفاظ بحالة تهيئة Canvas وكائن Fabric
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const fabricInstanceRef = useRef<any>(null); // كائن Fabric.js
  const canvasInitializedRef = useRef<boolean>(false);

  useEffect(() => {
    // تحميل المكتبة فقط في بيئة المتصفح
    if (typeof window === 'undefined' || !canvasRef.current) return;

    // تنظيف أي Canvas موجود مسبقاً
    const cleanupExistingCanvas = () => {
      if (fabricInstanceRef.current) {
        try {
          console.log("Disposing existing canvas before re-initialization");
          fabricInstanceRef.current.dispose();
          fabricInstanceRef.current = null;
        } catch (error) {
          console.error('Error disposing existing canvas:', error);
        }
      }
      // إعادة تعيين حالة التهيئة
      canvasInitializedRef.current = false;
    };

    const loadFabric = async () => {
      // إذا كان Canvas مهيأ بالفعل، نتخطى التهيئة
      if (canvasInitializedRef.current && fabricInstanceRef.current) {
        console.log("Canvas already initialized, skipping initialization");
        return;
      }

      // تنظيف أي Canvas موجود قبل التهيئة
      cleanupExistingCanvas();

      try {
        setIsLoading(true);
        console.log("Loading Fabric.js library...");

        // استيراد مكتبة Fabric.js
        const fabricModule = await import('fabric');

        // تهيئة Canvas
        try {
          if (canvasRef.current) {
            // التحقق مرة أخرى من أن Canvas لم يتم تهيئته بالفعل
            try {
              // محاولة الوصول إلى خاصية __eventListeners للتحقق من التهيئة
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const anyCanvas = canvasRef.current as any;
              if (anyCanvas.__eventListeners) {
                console.warn("Canvas appears to be already initialized, cleaning up first");
                // إذا كان Canvas مهيأ بالفعل، نقوم بإعادة إنشاء عنصر canvas جديد
                const parentNode = canvasRef.current.parentNode;
                if (parentNode) {
                  const oldCanvas = canvasRef.current;
                  const newCanvas = document.createElement('canvas');
                  newCanvas.width = width;
                  newCanvas.height = height;
                  parentNode.replaceChild(newCanvas, oldCanvas);
                  // تحديث المرجع
                  canvasRef.current = newCanvas;
                }
              }
            } catch {
              // تجاهل أي أخطاء هنا، فقط للتحقق
            }

            // تهيئة Canvas جديد
            fabricInstanceRef.current = new fabricModule.Canvas(canvasRef.current, {
              width,
              height,
              backgroundColor: '#ffffff',
              isDrawingMode: true
            });

            // تعيين حالة التهيئة
            canvasInitializedRef.current = true;

            // إنشاء فرشاة الرسم
            if (!fabricInstanceRef.current.freeDrawingBrush) {
              fabricInstanceRef.current.freeDrawingBrush = new fabricModule.PencilBrush(fabricInstanceRef.current);
            }

            // تعيين إعدادات الفرشاة الأولية
            fabricInstanceRef.current.freeDrawingBrush.width = 5;
            fabricInstanceRef.current.freeDrawingBrush.color = '#000000';

            // إجبار إعادة الرسم
            fabricInstanceRef.current.renderAll();

            console.log("Fabric.js canvas initialized successfully");

            // إنشاء كائن FabricStatic للاستخدام في المكونات الأخرى
            const fabricLib: FabricStatic = {
              Canvas: fabricModule.Canvas,
              PencilBrush: fabricModule.PencilBrush,
              IText: fabricModule.IText,
              Rect: fabricModule.Rect,
              Circle: fabricModule.Circle,
              Line: fabricModule.Line
            };

            // استدعاء دالة onReady
            onReady(fabricInstanceRef.current as unknown as FabricCanvas, fabricLib);
          } else {
            throw new Error('Canvas element not found');
          }
        } catch (error) {
          const canvasError = error as Error;
          console.error('Error creating canvas:', canvasError);
          if (canvasError.message && canvasError.message.includes('already been initialized')) {
            console.warn('Canvas was already initialized. Attempting to recover...');
            // محاولة التنظيف والتهيئة مرة أخرى في الدورة التالية
            setTimeout(() => {
              cleanupExistingCanvas();
              loadFabric();
            }, 0);
          } else {
            throw canvasError; // رمي الخطأ مرة أخرى إذا لم يكن خطأ التهيئة المزدوجة
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing Fabric.js canvas:', error);
        setError('فشل في تهيئة السبورة. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
        setIsLoading(false);
      }
    };

    loadFabric();

    // دالة التنظيف
    return () => {
      // تنظيف Canvas عند إزالة المكون
      if (fabricInstanceRef.current && canvasInitializedRef.current) {
        try {
          fabricInstanceRef.current.dispose();
          fabricInstanceRef.current = null;
          canvasInitializedRef.current = false;
          console.log("Canvas disposed on component unmount");
        } catch (error) {
          console.error('Error disposing canvas:', error);
        }
      }
    };
  }, [width, height, onReady]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-500 text-center">
          <p className="font-bold mb-2">خطأ</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-70 z-10">
          <div className="flex flex-col items-center gap-2">
            <div className="animate-spin h-8 w-8 border-4 border-[var(--primary-color)] border-t-transparent rounded-full"></div>
            <p className="text-gray-600">جاري تهيئة السبورة...</p>
          </div>
        </div>
      )}
      <canvas ref={canvasRef} />
    </div>
  );
};

export default FabricCanvasComponent;
export type { FabricCanvas, FabricStatic };

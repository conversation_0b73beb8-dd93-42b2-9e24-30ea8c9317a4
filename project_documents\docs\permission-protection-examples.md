# أمثلة على حماية الصفحات والمكونات بالصلاحيات

## 1. حماية الأزرار والإجراءات

### استخدام ActionButtonGroup
```tsx
import { ActionButtonGroup } from '@/components/admin/ActionButtons';

// في جدول الطلاب
<ActionButtonGroup
  entityType="students"
  onEdit={() => handleEdit(student.id)}
  onDelete={() => handleDelete(student.id)}
  onView={() => handleView(student.id)}
/>
```

### استخدام أزرار منفردة
```tsx
import { EditButton, DeleteButton, AddButton } from '@/components/admin/ActionButtons';

// زر إضافة طالب جديد
<AddButton
  requiredPermission="admin.students.create"
  onClick={() => setShowAddModal(true)}
  size="md"
>
  إضافة طالب جديد
</AddButton>

// زر تعديل
<EditButton
  requiredPermission="admin.students.edit"
  onClick={() => handleEdit(student.id)}
/>

// زر حذف
<DeleteButton
  requiredPermission="admin.students.delete"
  onClick={() => handleDelete(student.id)}
/>
```

## 2. حماية الروابط

### استخدام ProtectedLink
```tsx
import ProtectedLink from '@/components/admin/ProtectedLink';

// رابط لصفحة تفاصيل الطالب
<ProtectedLink
  href={`/admin/students/${student.id}`}
  requiredPermission="admin.students.view"
  className="text-blue-600 hover:text-blue-800"
>
  {student.name}
</ProtectedLink>

// رابط لنتائج الامتحانات
<ProtectedLink
  href={`/admin/students/${student.id}/exams`}
  requiredPermission="admin.evaluation.view"
  className="btn btn-primary"
>
  عرض النتائج
</ProtectedLink>
```

## 3. حماية أقسام كاملة

### استخدام PermissionGuard
```tsx
import PermissionGuard from '@/components/admin/PermissionGuard';

// قسم إدارة الدرجات
<PermissionGuard requiredPermission="admin.evaluation.grades">
  <div className="grades-section">
    <h3>إدارة الدرجات</h3>
    <GradesTable />
    <AddGradeButton />
  </div>
</PermissionGuard>

// قسم التقارير المالية
<PermissionGuard requiredPermission="admin.financial.reports">
  <FinancialReportsSection />
</PermissionGuard>
```

## 4. حماية عناصر القوائم

```tsx
// في قائمة منسدلة
<PermissionGuard requiredPermission="admin.students.edit">
  <DropdownItem onClick={() => handleEdit(student.id)}>
    تعديل الطالب
  </DropdownItem>
</PermissionGuard>

<PermissionGuard requiredPermission="admin.students.delete">
  <DropdownItem onClick={() => handleDelete(student.id)}>
    حذف الطالب
  </DropdownItem>
</PermissionGuard>

<PermissionGuard requiredPermission="admin.evaluation.view">
  <DropdownItem onClick={() => viewExamResults(student.id)}>
    عرض نتائج الامتحانات
  </DropdownItem>
</PermissionGuard>
```

## 5. حماية التبويبات (Tabs)

```tsx
// في صفحة تفاصيل الطالب
<TabList>
  <Tab>المعلومات الأساسية</Tab>
  
  <PermissionGuard requiredPermission="admin.evaluation.view">
    <Tab>النتائج والدرجات</Tab>
  </PermissionGuard>
  
  <PermissionGuard requiredPermission="admin.attendance.view">
    <Tab>سجل الحضور</Tab>
  </PermissionGuard>
  
  <PermissionGuard requiredPermission="admin.payments.view">
    <Tab>المدفوعات</Tab>
  </PermissionGuard>
</TabList>
```

## 6. حماية النماذج والمدخلات

```tsx
// حقول قابلة للتعديل فقط مع الصلاحية المناسبة
<PermissionGuard 
  requiredPermission="admin.students.edit"
  fallback={<span>{student.name}</span>}
  showFallback={true}
>
  <input 
    type="text" 
    value={student.name}
    onChange={(e) => setStudentName(e.target.value)}
  />
</PermissionGuard>
```

## 7. الصلاحيات المتاحة

### صلاحيات الطلاب
- `admin.students.view` - عرض قائمة الطلاب
- `admin.students.create` - إضافة طلاب جدد
- `admin.students.edit` - تعديل بيانات الطلاب
- `admin.students.delete` - حذف الطلاب

### صلاحيات التقييم
- `admin.evaluation.view` - عرض نظام التقييم
- `admin.evaluation.exams` - إدارة الامتحانات
- `admin.evaluation.questions` - إدارة بنك الأسئلة
- `admin.evaluation.grades` - إدارة الدرجات

### صلاحيات الحضور
- `admin.attendance.view` - عرض سجلات الحضور
- `admin.attendance.create` - تسجيل الحضور
- `admin.attendance.edit` - تعديل سجلات الحضور
- `admin.attendance.reports` - تقارير الحضور

### صلاحيات المدفوعات
- `admin.payments.view` - عرض المدفوعات
- `admin.payments.create` - تسجيل مدفوعات جديدة
- `admin.payments.edit` - تعديل المدفوعات

## 8. نصائح مهمة

1. **استخدم الصلاحيات الصحيحة**: تأكد من استخدام مفاتيح الصلاحيات الصحيحة
2. **حماية API أيضاً**: لا تعتمد على حماية الواجهة فقط، احم API endpoints أيضاً
3. **اختبر الصلاحيات**: اختبر جميع السيناريوهات مع مستخدمين مختلفين
4. **استخدم fallback**: استخدم fallback للعناصر المهمة التي يجب أن تظهر حتى بدون صلاحية

## 9. مثال كامل لجدول محمي

```tsx
import { ActionButtonGroup } from '@/components/admin/ActionButtons';
import PermissionGuard from '@/components/admin/PermissionGuard';
import ProtectedLink from '@/components/admin/ProtectedLink';

const StudentsTable = () => {
  return (
    <div>
      {/* زر إضافة طالب جديد */}
      <PermissionGuard requiredPermission="admin.students.create">
        <button onClick={() => setShowAddModal(true)}>
          إضافة طالب جديد
        </button>
      </PermissionGuard>

      <table>
        <thead>
          <tr>
            <th>الاسم</th>
            <th>الصف</th>
            <PermissionGuard requiredPermission="admin.evaluation.view">
              <th>المعدل</th>
            </PermissionGuard>
            <th>الإجراءات</th>
          </tr>
        </thead>
        <tbody>
          {students.map(student => (
            <tr key={student.id}>
              <td>
                <ProtectedLink
                  href={`/admin/students/${student.id}`}
                  requiredPermission="admin.students.view"
                  fallback={<span>{student.name}</span>}
                  showFallback={true}
                >
                  {student.name}
                </ProtectedLink>
              </td>
              <td>{student.class}</td>
              <PermissionGuard requiredPermission="admin.evaluation.view">
                <td>{student.average}</td>
              </PermissionGuard>
              <td>
                <ActionButtonGroup
                  entityType="students"
                  onEdit={() => handleEdit(student.id)}
                  onDelete={() => handleDelete(student.id)}
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

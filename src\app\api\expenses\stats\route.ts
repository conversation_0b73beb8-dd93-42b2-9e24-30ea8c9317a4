import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// تعريف واجهة للإحصائيات الشهرية
interface MonthlyStatResult {
  month: Date;
  total: string;
}

// GET /api/expenses/stats - الحصول على إحصائيات المصروفات
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get('startDate')
      ? new Date(searchParams.get('startDate') as string)
      : new Date(new Date().getFullYear(), new Date().getMonth() - 6, 1); // آخر 6 أشهر

    const endDate = searchParams.get('endDate')
      ? new Date(searchParams.get('endDate') as string)
      : new Date(); // اليوم الحالي

    const categoryId = searchParams.get('categoryId')
      ? parseInt(searchParams.get('categoryId') as string)
      : null;

    // بناء شروط البحث
    const where: {
      date: {
        gte: Date;
        lte: Date;
      };
      categoryId?: number;
    } = {
      date: {
        gte: startDate,
        lte: endDate,
      },
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    // إجمالي المصروفات
    const totalExpenses = await prisma.expense.aggregate({
      where,
      _sum: {
        amount: true,
      },
    });

    // إحصائيات حسب الفئة
    const categoryStats = await prisma.expense.groupBy({
      by: ['categoryId'],
      where,
      _sum: {
        amount: true,
      },
    });

    // الحصول على أسماء الفئات
    const categories = await prisma.expenseCategory.findMany({
      where: {
        id: {
          in: categoryStats.map(stat => stat.categoryId).filter(id => id !== null) as number[],
        },
      },
      select: {
        id: true,
        name: true,
        color: true,
      },
    });

    // المصروفات بدون فئة
    const uncategorizedExpenses = categoryStats.find(stat => stat.categoryId === null);

    // تنسيق إحصائيات الفئات
    const formattedCategoryStats = [
      ...categoryStats
        .filter(stat => stat.categoryId !== null)
        .map(stat => {
          const category = categories.find(cat => cat.id === stat.categoryId);
          // طباعة معلومات الفئة للتشخيص
          console.log(`Category ID: ${stat.categoryId}, Found category:`, category);
          return {
            id: stat.categoryId,
            name: category?.name || 'بدون فئة',
            color: category?.color || 'var(--primary-color)',
            amount: stat._sum.amount || 0,
            percentage: totalExpenses._sum.amount
              ? ((stat._sum.amount || 0) / totalExpenses._sum.amount) * 100
              : 0,
          };
        }),
    ];

    // إضافة المصروفات بدون فئة إذا وجدت
    if (uncategorizedExpenses && uncategorizedExpenses._sum.amount) {
      formattedCategoryStats.push({
        id: null,
        name: 'بدون فئة',
        color: '#cccccc',
        amount: uncategorizedExpenses._sum.amount || 0,
        percentage: totalExpenses._sum.amount
          ? (uncategorizedExpenses._sum.amount / totalExpenses._sum.amount) * 100
          : 0,
      });
    }

    // إحصائيات شهرية
    const monthlyStats = await prisma.$queryRaw`
      SELECT
        DATE_FORMAT(date, '%Y-%m-01') as month,
        SUM(amount) as total
      FROM Expense
      WHERE date >= ${startDate} AND date <= ${endDate}
      ${categoryId ? `AND categoryId = ${categoryId}` : ''}
      GROUP BY DATE_FORMAT(date, '%Y-%m-01')
      ORDER BY month ASC
    `;

    // تنسيق الإحصائيات الشهرية
    const formattedMonthlyStats = (monthlyStats as MonthlyStatResult[]).map(stat => ({
      month: new Date(stat.month).toISOString().substring(0, 7),
      amount: parseFloat(stat.total),
    }));

    // إحصائيات المصروفات الأخيرة
    const recentExpensesStats = await prisma.expense.findMany({
      where,
      orderBy: {
        date: 'desc',
      },
      take: 5,
      include: {
        category: {
          select: {
            name: true,
            color: true,
          },
        },
      },
    });

    // توقعات المصروفات للشهر الحالي
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const currentDay = new Date().getDate();

    const currentMonthExpenses = await prisma.expense.aggregate({
      where: {
        date: {
          gte: new Date(currentYear, currentMonth, 1),
          lte: new Date(currentYear, currentMonth, currentDay),
        },
      },
      _sum: {
        amount: true,
      },
    });

    const projectedMonthlyExpense = currentMonthExpenses._sum.amount
      ? (currentMonthExpenses._sum.amount / currentDay) * daysInMonth
      : 0;

    // طباعة البيانات النهائية للتشخيص
    console.log('Final formatted categories:', formattedCategoryStats);

    // إرجاع البيانات
    return NextResponse.json({
      total: totalExpenses._sum.amount || 0,
      categories: formattedCategoryStats,
      categoriesStats: formattedCategoryStats, // للتوافق مع الكود القديم
      totalAmount: totalExpenses._sum.amount || 0, // للتوافق مع الكود القديم
      monthly: formattedMonthlyStats,
      recent: recentExpensesStats,
      projected: {
        currentMonth: currentMonthExpenses._sum.amount || 0,
        projectedTotal: projectedMonthlyExpense,
        remainingDays: daysInMonth - currentDay,
      },
    });
  } catch (error) {
    console.error('خطأ في جلب إحصائيات المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب إحصائيات المصروفات' },
      { status: 500 }
    );
  }
}

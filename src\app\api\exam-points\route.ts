import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { examId, studentId, classSubjectId, grade, note, surahId, startVerse, endVerse, teacherId } = body;

    // Validate required fields
    if (!examId || !studentId || !classSubjectId || grade === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: examId, studentId, classSubjectId, and grade are required' },
        { status: 400 }
      );
    }

    // تحويل البيانات إلى أرقام والتحقق من صحتها
    const numericGrade = Number(grade);
    const numericExamId = Number(examId);
    const numericStudentId = Number(studentId);
    const numericClassSubjectId = Number(classSubjectId);

    // طباعة البيانات للتصحيح
    console.log('Received data:', {
      examId, studentId, classSubjectId, grade,
      numericExamId, numericStudentId, numericClassSubjectId, numericGrade
    });

    // التحقق من صحة البيانات
    if (
      isNaN(numericGrade) ||
      numericGrade < 0 ||
      numericGrade > 100 ||
      isNaN(numericExamId) ||
      isNaN(numericStudentId) ||
      isNaN(numericClassSubjectId)
    ) {
      return NextResponse.json(
        {
          error: 'Invalid input: grade must be a number between 0 and 100, and IDs must be valid numbers',
          details: { examId, studentId, classSubjectId, grade }
        },
        { status: 400 }
      );
    }

    // التحقق من وجود نقطة امتحان مسبقة لنفس الطالب ونفس الامتحان ونفس علاقة القسم بالمادة
    const existingExamPoint = await prisma.exam_points.findUnique({
      where: {
        studentId_classSubjectId_examId: {
          studentId: numericStudentId,
          classSubjectId: numericClassSubjectId,
          examId: numericExamId
        }
      }
    });

    // إذا كانت موجودة، نقوم بتحديثها بدلاً من إنشاء واحدة جديدة
    if (existingExamPoint) {
      console.log('Updating existing exam point:', existingExamPoint.id);

      // إعداد بيانات التحديث
      const updateData: {
        grade: number;
        note?: string | null;
        surahId?: number;
        startVerse?: number;
        endVerse?: number;
      } = {
        grade: numericGrade,
        note
      };

      // إضافة معلومات السورة والآيات إذا تم توفيرها
      if (surahId && startVerse && endVerse) {
        updateData.surahId = Number(surahId);
        updateData.startVerse = Number(startVerse);
        updateData.endVerse = Number(endVerse);
      }

      // تحديث نقطة الامتحان
      const updatedExamPoint = await prisma.exam_points.update({
        where: { id: existingExamPoint.id },
        data: updateData,
        include: {
          student: true,
          exam: true,
          surah: true,
          classSubject: {
            include: {
              classe: true,
              teacherSubject: {
                include: {
                  teacher: true,
                  subject: true
                }
              }
            }
          }
        }
      });

      return NextResponse.json({
        data: updatedExamPoint,
        success: true,
        message: 'تم تحديث نقاط الامتحان بنجاح'
      });
    }

    // إذا لم تكن موجودة، نقوم بإنشاء واحدة جديدة
    // Create exam point with transaction to ensure data consistency
    const examPoint = await prisma.$transaction(async (tx) => {
      // Verify that the exam, student, and classSubject exist
      const [exam, student, classSubject] = await Promise.all([
        tx.exam.findUnique({ where: { id: numericExamId } }),
        tx.student.findUnique({ where: { id: numericStudentId } }),
        tx.classSubject.findUnique({
          where: { id: numericClassSubjectId },
          include: {
            teacherSubject: {
              include: {
                teacher: true
              }
            }
          }
        })
      ]);

      console.log('Found entities:', {
        exam: exam ? 'Found' : 'Not found',
        student: student ? 'Found' : 'Not found',
        classSubject: classSubject ? 'Found' : 'Not found'
      });

      if (!exam || !student || !classSubject) {
        throw new Error('Referenced exam, student, or class subject does not exist');
      }

      // التحقق من صلاحيات المعلم (إذا تم تحديد معرف المعلم)
      if (teacherId) {
        // التحقق من أن المعلم يدرس لهذا الفصل
        const isTeacherAuthorized = classSubject.teacherSubject.teacherId === parseInt(teacherId);

        if (!isTeacherAuthorized) {
          throw new Error('Teacher is not authorized to record exam points for this class');
        }
      }

      // التحقق مما إذا كان الامتحان يتطلب تحديد سورة وآيات
      if (exam.requiresSurah || exam.evaluationType === 'QURAN_MEMORIZATION') {
        console.log('Exam requires Surah information');
        // التحقق من وجود معلومات السورة والآيات
        if (!surahId || !startVerse || !endVerse) {
          console.log('Missing Surah information:', { surahId, startVerse, endVerse });
          throw new Error('Surah, start verse, and end verse are required for Quran memorization exams');
        }

        const numericSurahId = Number(surahId);
        const numericStartVerse = Number(startVerse);
        const numericEndVerse = Number(endVerse);

        console.log('Surah information:', { numericSurahId, numericStartVerse, numericEndVerse });

        // التحقق من صحة الآيات
        if (numericStartVerse > numericEndVerse) {
          throw new Error('Start verse must be less than or equal to end verse');
        }

        return tx.exam_points.create({
          data: {
            examId: numericExamId,
            studentId: numericStudentId,
            classSubjectId: numericClassSubjectId,
            grade: numericGrade,
            note,
            surahId: numericSurahId,
            startVerse: numericStartVerse,
            endVerse: numericEndVerse
          }
        });
      } else {
        console.log('Exam does not require Surah information');
        // إذا كان الامتحان لا يتطلب تحديد سورة وآيات
        return tx.exam_points.create({
          data: {
            examId: numericExamId,
            studentId: numericStudentId,
            classSubjectId: numericClassSubjectId,
            grade: numericGrade,
            note
          }
        });
      }
    });

    return NextResponse.json({
      data: examPoint,
      success: true,
      message: 'تم تسجيل نقاط الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error creating exam point:', error);

    // التعامل مع أنواع الأخطاء المختلفة
    if (error instanceof Error) {
      if (error.message === 'Surah, start verse, and end verse are required for Quran memorization exams') {
        return NextResponse.json({
          error: 'يجب تحديد السورة وأرقام الآيات لامتحانات حفظ القرآن',
          success: false
        }, { status: 400 });
      } else if (error.message === 'Start verse must be less than or equal to end verse') {
        return NextResponse.json({
          error: 'يجب أن يكون رقم آية البداية أقل من أو يساوي رقم آية النهاية',
          success: false
        }, { status: 400 });
      } else if (error.message === 'Referenced exam, student, or class subject does not exist') {
        return NextResponse.json({
          error: 'الامتحان أو الطالب أو المادة غير موجودة',
          success: false
        }, { status: 404 });
      } else if (error.message === 'Teacher is not authorized to record exam points for this class') {
        return NextResponse.json({
          error: 'ليس لديك صلاحية تسجيل نقاط الامتحان لهذا الفصل',
          success: false
        }, { status: 403 });
      }
    }

    return NextResponse.json({
      error: 'حدث خطأ أثناء تسجيل نقاط الامتحان',
      success: false
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const examId = searchParams.get('examId');
    const studentId = searchParams.get('studentId');
    const classSubjectId = searchParams.get('classSubjectId');
    const classId = searchParams.get('classId');
    const month = searchParams.get('month');
    const teacherId = searchParams.get('teacherId');

    console.log('Fetching exam points with params:', {
      examId,
      studentId,
      classSubjectId,
      classId,
      teacherId
    });

    // إذا تم تحديد معرف المعلم، نتحقق من أن المعلم يدرس لهذا الفصل
    if (teacherId && classSubjectId) {
      const classSubject = await prisma.classSubject.findUnique({
        where: { id: parseInt(classSubjectId) },
        include: {
          teacherSubject: true
        }
      });

      if (!classSubject) {
        return NextResponse.json({
          error: 'المادة الدراسية غير موجودة',
          success: false
        }, { status: 404 });
      }

      const isTeacherAuthorized = classSubject.teacherSubject.teacherId === parseInt(teacherId);
      if (!isTeacherAuthorized) {
        return NextResponse.json({
          error: 'ليس لديك صلاحية الوصول إلى نقاط الامتحان لهذا الفصل',
          success: false
        }, { status: 403 });
      }
    }

    // بناء شروط البحث
    const where: {
      examId?: number;
      studentId?: number;
      classSubjectId?: number;
      month?: string;
      classSubject?: {
        classeId?: number;
        teacherSubject?: {
          teacherId?: number;
        };
      };
    } = {};

    if (examId) {
      where.examId = parseInt(examId);
    }

    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    if (classSubjectId) {
      where.classSubjectId = parseInt(classSubjectId);
    }

    if (month) {
      where.month = month;
    }

    // بناء شرط البحث للفصل والمعلم
    if (!where.classSubject) {
      where.classSubject = {};
    }

    // إذا تم تحديد معرف الفصل
    if (classId) {
      where.classSubject.classeId = parseInt(classId);
      console.log(`Filtering by class ID: ${classId}`);
    }

    // إذا تم تحديد معرف المعلم
    if (teacherId) {
      // التأكد من أن نقاط الامتحان تنتمي إلى الفصول التي يدرسها المعلم
      where.classSubject.teacherSubject = {
        teacherId: parseInt(teacherId)
      };

      console.log(`Filtering exam points for teacher ID: ${teacherId}`);
    }

    console.log('Query where clause:', where);

    // جلب نقاط الامتحان مع البيانات المرتبطة
    const examPoints = await prisma.exam_points.findMany({
      where,
      include: {
        student: true,
        exam: true,
        surah: true,
        classSubject: {
          include: {
            classe: true,
            teacherSubject: {
              include: {
                subject: true,
                teacher: true
              }
            }
          }
        }
      },
      orderBy: [
        { classSubject: { classe: { name: 'asc' } } },
        { student: { name: 'asc' } }
      ]
    });

    return NextResponse.json({
      data: examPoints,
      success: true,
      count: examPoints.length,
      message: 'تم جلب نقاط الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error fetching exam points:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب نقاط الامتحان',
      success: false
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, grade, note, status, teacherId, surahId, startVerse, endVerse } = body;

    // التحقق من وجود نقطة الامتحان
    const existingExamPoint = await prisma.exam_points.findUnique({
      where: { id },
      include: {
        classSubject: {
          include: {
            teacherSubject: true
          }
        },
        exam: true
      }
    });

    if (!existingExamPoint) {
      return NextResponse.json({
        error: 'نقطة الامتحان غير موجودة',
        success: false
      }, { status: 404 });
    }

    // التحقق من صلاحيات المعلم إذا تم تحديد معرف المعلم
    if (teacherId) {
      const isTeacherAuthorized = existingExamPoint.classSubject.teacherSubject.teacherId === parseInt(teacherId);
      if (!isTeacherAuthorized) {
        return NextResponse.json({
          error: 'ليس لديك صلاحية تحديث نقاط الامتحان لهذا الفصل',
          success: false
        }, { status: 403 });
      }
    }

    // إعداد بيانات التحديث
    const updateData: {
      grade?: number;
      note?: string | null;
      status?: string;
      surahId?: number | null;
      startVerse?: number | null;
      endVerse?: number | null;
    } = {};

    // إضافة الدرجة والملاحظة إذا تم توفيرهما
    if (grade !== undefined) updateData.grade = Number(grade);
    if (note !== undefined) updateData.note = note;

    // إضافة الحالة إذا تم توفيرها
    if (status) {
      updateData.status = status;
    } else if (grade !== undefined) {
      // تحديد الحالة بناءً على الدرجة إذا لم يتم توفير الحالة
      const numericGrade = Number(grade);
      if (numericGrade >= 9) {
        updateData.status = 'EXCELLENT';
      } else if (numericGrade >= 6) {
        updateData.status = 'PASSED';
      } else {
        updateData.status = 'FAILED';
      }
    }

    // إذا كان الامتحان يتطلب تحديد سورة وآيات
    if (existingExamPoint.exam.requiresSurah || existingExamPoint.exam.evaluationType === 'QURAN_MEMORIZATION') {
      if (surahId && startVerse && endVerse) {
        // التحقق من صحة الآيات
        if (parseInt(startVerse) > parseInt(endVerse)) {
          return NextResponse.json({
            error: 'يجب أن يكون رقم آية البداية أقل من أو يساوي رقم آية النهاية',
            success: false
          }, { status: 400 });
        }

        updateData.surahId = parseInt(surahId);
        updateData.startVerse = parseInt(startVerse);
        updateData.endVerse = parseInt(endVerse);
      } else {
        return NextResponse.json({
          error: 'يجب تحديد السورة وأرقام الآيات لامتحانات حفظ القرآن',
          success: false
        }, { status: 400 });
      }
    }

    // Usar un enfoque más directo para la actualización
    const examPoint = await prisma.exam_points.update({
      where: { id },
      data: {
        ...(grade !== undefined ? { grade: Number(grade) } : {}),
        ...(note !== undefined ? { note } : {}),
        ...(status !== undefined ? { status } : {}),
        ...(surahId !== undefined ? { surahId: Number(surahId) } : {}),
        ...(startVerse !== undefined ? { startVerse: Number(startVerse) } : {}),
        ...(endVerse !== undefined ? { endVerse: Number(endVerse) } : {})
      },
      include: {
        student: true,
        exam: true,
        surah: true,
        classSubject: {
          include: {
            classe: true,
            teacherSubject: {
              include: {
                subject: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      data: examPoint,
      success: true,
      message: 'تم تحديث نقاط الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error updating exam point:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث نقاط الامتحان',
      success: false
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const teacherId = searchParams.get('teacherId');

    if (!id) {
      return NextResponse.json({
        error: 'معرف نقطة الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود نقطة الامتحان
    const examPoint = await prisma.exam_points.findUnique({
      where: { id: parseInt(id) },
      include: {
        classSubject: {
          include: {
            teacherSubject: true
          }
        }
      }
    });

    if (!examPoint) {
      return NextResponse.json({
        error: 'نقطة الامتحان غير موجودة',
        success: false
      }, { status: 404 });
    }

    // التحقق من صلاحيات المعلم إذا تم تحديد معرف المعلم
    if (teacherId) {
      const isTeacherAuthorized = examPoint.classSubject.teacherSubject.teacherId === parseInt(teacherId);
      if (!isTeacherAuthorized) {
        return NextResponse.json({
          error: 'ليس لديك صلاحية حذف نقاط الامتحان لهذا الفصل',
          success: false
        }, { status: 403 });
      }
    }

    await prisma.exam_points.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف نقطة الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error deleting exam point:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف نقطة الامتحان',
      success: false
    }, { status: 500 });
  }
}

'use client';

import { useEffect, useRef } from 'react';
import { Chart, registerables } from 'chart.js';
import { useTheme } from 'next-themes';

// تسجيل جميع المكونات المطلوبة من chart.js
if (typeof window !== 'undefined') {
  Chart.register(...registerables);
}

interface LineChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor?: string;
      borderColor?: string;
      borderWidth?: number;
      tension?: number;
      fill?: boolean;
    }[];
  };
  options?: {
    responsive?: boolean;
    maintainAspectRatio?: boolean;
    plugins?: {
      legend?: {
        position?: 'top' | 'left' | 'bottom' | 'right';
        labels?: {
          font?: {
            family?: string;
            size?: number;
          };
          color?: string;
        };
      };
      tooltip?: {
        titleFont?: {
          family?: string;
          size?: number;
        };
        bodyFont?: {
          family?: string;
          size?: number;
        };
        rtl?: boolean;
        textDirection?: 'ltr' | 'rtl';
      };
    };
    scales?: {
      x?: {
        ticks?: {
          font?: {
            family?: string;
            size?: number;
          };
          color?: string;
        };
        grid?: {
          color?: string;
        };
      };
      y?: {
        ticks?: {
          font?: {
            family?: string;
            size?: number;
          };
          color?: string;
        };
        grid?: {
          color?: string;
        };
      };
    };
    [key: string]: unknown;
  };
  height?: number;
  width?: number;
  className?: string;
  id?: string;
}

export function LineChart({ data, options = {}, height = 300, width = 500, className = '', id = 'line-chart' }: LineChartProps) {
  const chartRef = useRef<HTMLCanvasElement | null>(null);
  const chartInstance = useRef<Chart | null>(null);
  const { theme = 'light' } = useTheme() || {};

  useEffect(() => {
    if (typeof window === 'undefined' || !chartRef.current) return;

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // تدمير الرسم البياني السابق إذا كان موجودًا
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // تعيين الألوان الافتراضية إذا لم يتم توفيرها
    const defaultColors = [
      'rgba(22, 155, 136, 1)',
      'rgba(26, 177, 156, 1)',
      'rgba(45, 212, 191, 1)',
      'rgba(94, 234, 212, 1)',
      'rgba(153, 246, 228, 1)',
      'rgba(204, 251, 241, 1)',
    ];

    const datasets = data.datasets.map((dataset, index) => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || defaultColors[index % defaultColors.length].replace('1', '0.2'),
      borderColor: dataset.borderColor || defaultColors[index % defaultColors.length],
      borderWidth: dataset.borderWidth || 2,
      tension: dataset.tension !== undefined ? dataset.tension : 0.4,
      fill: dataset.fill !== undefined ? dataset.fill : false,
    }));

    // تعيين الخيارات الافتراضية
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
          labels: {
            font: {
              family: 'Tajawal, sans-serif',
            },
            color: theme === 'dark' ? '#fff' : '#333',
          },
        },
        tooltip: {
          titleFont: {
            family: 'Tajawal, sans-serif',
          },
          bodyFont: {
            family: 'Tajawal, sans-serif',
          },
          rtl: true,
          textDirection: 'rtl',
        },
      },
      scales: {
        x: {
          ticks: {
            font: {
              family: 'Tajawal, sans-serif',
            },
            color: theme === 'dark' ? '#fff' : '#333',
          },
          grid: {
            color: theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          },
        },
        y: {
          ticks: {
            font: {
              family: 'Tajawal, sans-serif',
            },
            color: theme === 'dark' ? '#fff' : '#333',
          },
          grid: {
            color: theme === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          },
        },
      },
    };

    // إنشاء الرسم البياني
    chartInstance.current = new Chart(ctx, {
      type: 'line',
      data: {
        ...data,
        datasets,
      },
      options: {
        ...defaultOptions,
        ...options,
      },
    });

    // تنظيف عند إزالة المكون
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, options, theme]);

  return (
    <div className={`relative ${className}`} style={{ height, width }}>
      <canvas ref={chartRef} id={id}></canvas>
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import { Article } from '@prisma/client'
import prisma from '@/lib/prisma';

export async function POST(request: NextRequest) {
    try {
      const body = (await request.json()) as Article;

      if (!body.title || !body.description) {
        return NextResponse.json(
          { message: "Invalid data" },
          { status: 400 }
        )
      }

      const newArticle : Article = await prisma.article.create({
        data: {
          title: body.title,
          description: body.description
        }
      });

      return NextResponse.json(newArticle, { status: 201 });
    } catch (error: unknown) {
      console.error('Error creating article:', error);
      return NextResponse.json(
        { message: "internal server error" },
        { status: 500 }
      )
    }
}

export async function GET() {
    try {
      const articles = await prisma.article.findMany();
      return NextResponse.json(articles, { status: 200 });
    } catch (error: unknown) {
      console.error('Error fetching articles:', error);
      return NextResponse.json(
        { message: "internal server error" },
        { status: 500 }
      )
    }
}
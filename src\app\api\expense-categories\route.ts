import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/expense-categories - الحصول على فئات المصروفات
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const parentId = searchParams.get('parentId');
    const includeInactive = searchParams.get('includeInactive') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '100'); // عدد أكبر لفئات المصروفات
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const where: {
      name?: { contains: string };
      parentId?: number | null;
      isActive?: boolean;
    } = {};

    if (query) {
      where.name = { contains: query };
    }

    // تصفية حسب الفئة الأب
    if (parentId !== null) {
      where.parentId = parentId ? parseInt(parentId) : null;
    }

    // تضمين الفئات غير النشطة إذا تم طلب ذلك
    if (!includeInactive) {
      where.isActive = true;
    }

    // جلب فئات المصروفات
    const categories = await prisma.expenseCategory.findMany({
      where,
      orderBy: [
        { parentId: 'asc' },
        { name: 'asc' }
      ],
      skip,
      take: limit,
      include: {
        subCategories: {
          where: includeInactive ? {} : { isActive: true },
          select: {
            id: true,
            name: true,
            icon: true,
            color: true,
            isActive: true,
            _count: {
              select: {
                expenses: true,
                subCategories: true
              }
            }
          }
        },
        _count: {
          select: {
            expenses: true,
            subCategories: true
          }
        }
      }
    });

    // جلب العدد الإجمالي للفئات
    const total = await prisma.expenseCategory.count({ where });

    return NextResponse.json({
      categories,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      }
    });
  } catch (error) {
    console.error('خطأ في جلب فئات المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب فئات المصروفات' },
      { status: 500 }
    );
  }
}

// POST /api/expense-categories - إنشاء فئة مصروفات جديدة
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, description, icon, color, parentId } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'اسم الفئة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة الأب إذا تم تحديدها
    if (parentId) {
      const parentCategory = await prisma.expenseCategory.findUnique({
        where: { id: parentId }
      });

      if (!parentCategory) {
        return NextResponse.json(
          { error: 'الفئة الأب غير موجودة' },
          { status: 400 }
        );
      }
    }

    // إنشاء فئة المصروفات
    const category = await prisma.expenseCategory.create({
      data: {
        name,
        description,
        icon,
        color,
        parentId: parentId || null,
        isActive: true
      }
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء فئة المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء فئة المصروفات' },
      { status: 500 }
    );
  }
}

// ملاحظة: تم نقل دوال PUT و DELETE إلى ملف [id]/route.ts

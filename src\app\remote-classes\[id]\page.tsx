'use client';
import React, { useState, useEffect, useCallback, useRef, use } from 'react';
import { FaVideo, FaLink, FaCalendarAlt, FaClock, FaUsers, FaChalkboardTeacher, FaEdit, FaTrash, FaDownload, FaUpload, FaExclamationCircle, FaFileAlt, FaFilePdf, FaFileWord, FaFileExcel, FaFileImage, FaFileVideo, FaFile, FaDesktop, FaChalkboard, FaMicrophone, FaCog, FaRecordVinyl, FaStop } from 'react-icons/fa';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import axios from 'axios';
import dynamic from 'next/dynamic';

// استيراد المكونات الجديدة
// استخدام dynamic import لتجنب مشاكل SSR مع مكتبات تعتمد على المتصفح
const ScreenShareButton = dynamic(() => import('@/components/remote-classes/ScreenShare/ScreenShareButton'), { ssr: false });
const ScreenShareDisplay = dynamic(() => import('@/components/remote-classes/ScreenShare/ScreenShareDisplay'), { ssr: false });
const Whiteboard = dynamic(() => import('@/components/remote-classes/Whiteboard/Whiteboard'), { ssr: false });
const VideoSettings = dynamic(() => import('@/components/remote-classes/VideoAudio/VideoSettings'), { ssr: false });
const AudioSettings = dynamic(() => import('@/components/remote-classes/VideoAudio/AudioSettings'), { ssr: false });

interface RemoteClass {
  id: number;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  meetingLink: string;
  meetingId?: string;
  meetingPassword?: string;
  platform: string;
  recordingUrl?: string;

  // حقول جديدة
  isScreenShareEnabled?: boolean;
  isWhiteboardEnabled?: boolean;
  videoQuality?: string;
  audioQuality?: string;

  instructor: {
    id: number;
    username: string;
    profile?: {
      name: string;
    };
  };
  classe?: {
    id: number;
    name: string;
    students: Array<{
      id: number;
      user: {
        id: number;
        username: string;
        profile?: {
          name: string;
        };
      };
    }>;
  };
  attendees: Array<{
    id: number;
    username: string;
    profile?: {
      name: string;
    };
  }>;
  materials: Array<{
    id: number;
    title: string;
    description?: string;
    fileUrl: string;
    fileType: string;
    createdAt: string;
  }>;
}

interface Material {
  id: number;
  title: string;
  description?: string;
  fileUrl: string;
  fileType: string;
  createdAt: string;
}

const RemoteClassDetailPage = ({ params }: { params: Promise<{ id: string }> | { id: string } }) => {
  // استخدام React.use() لفك الوعد قبل الوصول إلى خاصية id
  const unwrappedParams = 'then' in params ? use(params) : params;
  const id = unwrappedParams.id;
  const router = useRouter();
  const [remoteClass, setRemoteClass] = useState<RemoteClass | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [userId, setUserId] = useState<number | null>(null);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [materialForm, setMaterialForm] = useState({
    title: '',
    description: '',
    fileUrl: '',
    fileType: 'PDF'
  });
  const [uploadLoading, setUploadLoading] = useState(false);

  // حالات للمكونات الجديدة
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [screenStream, setScreenStream] = useState<MediaStream | null>(null);
  const [isWhiteboardActive, setIsWhiteboardActive] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(false);
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [videoStream, setVideoStream] = useState<MediaStream | null>(null);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);
  const [videoQuality, setVideoQuality] = useState<'low' | 'medium' | 'high'>('medium');
  const [audioQuality, setAudioQuality] = useState<'low' | 'medium' | 'high'>('medium');
  const [videoDeviceId, setVideoDeviceId] = useState<string>('');
  const [audioDeviceId, setAudioDeviceId] = useState<string>('');
  const [showInteractiveSection, setShowInteractiveSection] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState<BlobPart[]>([]);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);

  // مراجع للعناصر
  const videoRef = useRef<HTMLVideoElement>(null);
  const screenVideoRef = useRef<HTMLVideoElement>(null);

  // جلب تفاصيل الفصل الافتراضي
  const fetchRemoteClass = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`/api/remote-classes/${id}`);
      setRemoteClass(response.data as RemoteClass);
    } catch (error) {
      console.error('Error fetching remote class:', error);
      setError('حدث خطأ أثناء جلب تفاصيل الفصل الافتراضي. يرجى المحاولة مرة أخرى.');
      toast.error('فشل في جلب تفاصيل الفصل الافتراضي');
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  // جلب معلومات المستخدم
  const fetchUserInfo = useCallback(async () => {
    try {
      const response = await axios.get('/api/users/me');
      setUserRole((response.data as { role: string }).role);
      setUserId((response.data as { id: number }).id);
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  }, []);

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchRemoteClass();
    fetchUserInfo();
  }, [fetchRemoteClass, fetchUserInfo]);

  // تنسيق التاريخ والوقت
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // حساب المدة
  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    return `${hours > 0 ? `${hours} ساعة` : ''} ${minutes > 0 ? `${minutes} دقيقة` : ''}`;
  };

  // التحقق مما إذا كان الفصل قد بدأ
  const hasStarted = (startTime: string) => {
    const now = new Date();
    const start = new Date(startTime);
    return now >= start;
  };

  // التحقق مما إذا كان الفصل قد انتهى
  const hasEnded = (endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    return now > end;
  };

  // الحصول على أيقونة نوع الملف
  const getFileIcon = (fileType: string) => {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return <FaFilePdf className="text-red-500" />;
      case 'doc':
      case 'docx':
        return <FaFileWord className="text-blue-500" />;
      case 'xls':
      case 'xlsx':
        return <FaFileExcel className="text-primary-color" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FaFileImage className="text-purple-500" />;
      case 'mp4':
      case 'avi':
      case 'mov':
        return <FaFileVideo className="text-indigo-500" />;
      default:
        return <FaFile className="text-gray-500" />;
    }
  };

  // التعامل مع تغيير حقول نموذج المادة التعليمية
  const handleMaterialChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setMaterialForm(prev => ({ ...prev, [name]: value }));
  };

  // إضافة مادة تعليمية جديدة
  const handleAddMaterial = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من البيانات
    if (!materialForm.title || !materialForm.fileUrl || !materialForm.fileType) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setUploadLoading(true);

    try {
      const response = await axios.post(`/api/remote-classes/${id}/materials`, materialForm);

      // تحديث قائمة المواد التعليمية
      if (remoteClass) {
        setRemoteClass({
          ...remoteClass,
          materials: [...remoteClass.materials, response.data as Material]
        });
      }

      // إعادة تعيين النموذج
      setMaterialForm({
        title: '',
        description: '',
        fileUrl: '',
        fileType: 'PDF'
      });

      setShowUploadForm(false);
      toast.success('تمت إضافة المادة التعليمية بنجاح');
    } catch (error) {
      console.error('Error adding material:', error);
      toast.error('فشل في إضافة المادة التعليمية');
    } finally {
      setUploadLoading(false);
    }
  };

  // حذف مادة تعليمية
  const handleDeleteMaterial = async (materialId: number) => {
    if (!confirm('هل أنت متأكد من حذف هذه المادة التعليمية؟')) {
      return;
    }

    try {
      await axios.delete(`/api/remote-classes/${id}/materials/${materialId}`);

      // تحديث قائمة المواد التعليمية
      if (remoteClass) {
        setRemoteClass({
          ...remoteClass,
          materials: remoteClass.materials.filter(m => m.id !== materialId)
        });
      }

      toast.success('تم حذف المادة التعليمية بنجاح');
    } catch (error) {
      console.error('Error deleting material:', error);
      toast.error('فشل في حذف المادة التعليمية');
    }
  };

  // حذف الفصل الافتراضي
  const handleDeleteClass = async () => {
    if (!confirm('هل أنت متأكد من حذف هذا الفصل الافتراضي؟ سيتم حذف جميع المواد التعليمية المرتبطة به.')) {
      return;
    }

    try {
      await axios.delete(`/api/remote-classes/${id}`);
      toast.success('تم حذف الفصل الافتراضي بنجاح');
      router.push('/remote-classes');
    } catch (error) {
      console.error('Error deleting remote class:', error);
      toast.error('فشل في حذف الفصل الافتراضي');
    }
  };

  // التحقق من صلاحيات المستخدم
  const canEdit = () => {
    if (!remoteClass || !userRole || userId === null) return false;

    return userRole === 'ADMIN' || (userRole === 'TEACHER' && remoteClass.instructor.id === userId);
  };

  // التعامل مع بدء مشاركة الشاشة
  const handleStartScreenShare = (stream: MediaStream) => {
    setScreenStream(stream);
    setIsScreenSharing(true);
  };

  // التعامل مع إيقاف مشاركة الشاشة
  const handleStopScreenShare = () => {
    if (screenStream) {
      screenStream.getTracks().forEach(track => track.stop());
    }
    setScreenStream(null);
    setIsScreenSharing(false);
  };

  // التعامل مع تغيير حالة الفيديو
  const handleVideoChange = (enabled: boolean) => {
    setIsVideoEnabled(enabled);

    // إيقاف المسارات الحالية إذا تم تعطيل الفيديو
    if (!enabled && videoStream) {
      videoStream.getVideoTracks().forEach(track => track.stop());
      setVideoStream(null);
    }
  };

  // التعامل مع تغيير جودة الفيديو
  const handleVideoQualityChange = (quality: 'low' | 'medium' | 'high') => {
    setVideoQuality(quality);
  };

  // التعامل مع تغيير جهاز الفيديو
  const handleVideoDeviceChange = (deviceId: string) => {
    setVideoDeviceId(deviceId);
  };

  // التعامل مع تغيير حالة الصوت
  const handleAudioChange = (enabled: boolean) => {
    setIsAudioEnabled(enabled);

    // إيقاف المسارات الحالية إذا تم تعطيل الصوت
    if (!enabled && audioStream) {
      audioStream.getAudioTracks().forEach(track => track.stop());
      setAudioStream(null);
    }
  };

  // التعامل مع تغيير جودة الصوت
  const handleAudioQualityChange = (quality: 'low' | 'medium' | 'high') => {
    setAudioQuality(quality);
  };

  // التعامل مع تغيير جهاز الصوت
  const handleAudioDeviceChange = (deviceId: string) => {
    setAudioDeviceId(deviceId);
  };

  // التعامل مع تغيير مستوى الصوت
  const handleVolumeChange = (volume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = volume;
    }
  };

  // تبديل عرض قسم التفاعل
  const toggleInteractiveSection = () => {
    setShowInteractiveSection(!showInteractiveSection);
  };

  // بدء تسجيل الفصل
  const startRecording = () => {
    if (!videoStream && !screenStream) {
      toast.error('يجب تفعيل الكاميرا أو مشاركة الشاشة أولاً');
      return;
    }

    // إنشاء مسار مشترك من الفيديو والشاشة إذا كانا متاحين
    let streamToRecord: MediaStream;

    if (videoStream && screenStream) {
      // دمج المسارين
      streamToRecord = new MediaStream();
      videoStream.getVideoTracks().forEach(track => streamToRecord.addTrack(track));
      screenStream.getVideoTracks().forEach(track => streamToRecord.addTrack(track));

      // إضافة مسارات الصوت
      if (audioStream) {
        audioStream.getAudioTracks().forEach(track => streamToRecord.addTrack(track));
      }
    } else if (screenStream) {
      streamToRecord = screenStream;
      // إضافة مسارات الصوت إذا كانت متاحة
      if (audioStream) {
        audioStream.getAudioTracks().forEach(track => streamToRecord.addTrack(track));
      }
    } else {
      streamToRecord = videoStream as MediaStream;
    }

    try {
      // إنشاء مسجل الوسائط
      const options = { mimeType: 'video/webm;codecs=vp9,opus' };
      const recorder = new MediaRecorder(streamToRecord, options);

      // تعيين معالجات الأحداث
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setRecordedChunks(prev => [...prev, event.data]);
        }
      };

      // بدء التسجيل
      recorder.start(1000); // تسجيل قطع كل ثانية
      setMediaRecorder(recorder);
      setIsRecording(true);
      toast.success('بدأ التسجيل');
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error('فشل في بدء التسجيل');
    }
  };

  // إيقاف التسجيل
  const stopRecording = () => {
    if (!mediaRecorder) {
      return;
    }

    mediaRecorder.stop();
    setIsRecording(false);
    toast.success('تم إيقاف التسجيل');

    // حفظ التسجيل بعد ثانية واحدة
    setTimeout(() => {
      if (recordedChunks.length > 0) {
        const blob = new Blob(recordedChunks, { type: 'video/webm' });
        const url = URL.createObjectURL(blob);

        // إنشاء رابط تنزيل
        const a = document.createElement('a');
        a.href = url;
        a.download = `class-recording-${remoteClass.id}-${new Date().toISOString()}.webm`;
        a.click();

        // إعادة تعيين القطع المسجلة
        setRecordedChunks([]);
      }
    }, 1000);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8 flex justify-center items-center" dir="rtl">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[var(--primary-color)]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
        <div className="container mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <FaExclamationCircle className="text-red-500 text-5xl mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 mb-4">حدث خطأ</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={fetchRemoteClass}
              className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!remoteClass) {
    return (
      <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
        <div className="container mx-auto px-4">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <FaExclamationCircle className="text-yellow-500 text-5xl mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-800 mb-4">الفصل الافتراضي غير موجود</h2>
            <p className="text-gray-600 mb-6">لم يتم العثور على الفصل الافتراضي المطلوب</p>
            <Link
              href="/remote-classes"
              className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors"
            >
              العودة إلى الفصول الافتراضية
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col md:flex-row justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">{remoteClass.title}</h1>
              <p className="text-gray-600 mb-4">{remoteClass.description}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div className="flex items-center">
                  <FaCalendarAlt className="ml-2 text-[var(--primary-color)]" />
                  <div>
                    <p className="text-sm text-gray-700 font-medium">موعد البداية</p>
                    <p className="text-gray-600">{formatDateTime(remoteClass.startTime)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <FaClock className="ml-2 text-[var(--primary-color)]" />
                  <div>
                    <p className="text-sm text-gray-700 font-medium">المدة</p>
                    <p className="text-gray-600">{calculateDuration(remoteClass.startTime, remoteClass.endTime)}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <FaChalkboardTeacher className="ml-2 text-[var(--primary-color)]" />
                  <div>
                    <p className="text-sm text-gray-700 font-medium">المعلم</p>
                    <p className="text-gray-600">{remoteClass.instructor.profile?.name || remoteClass.instructor.username}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <FaVideo className="ml-2 text-[var(--primary-color)]" />
                  <div>
                    <p className="text-sm text-gray-700 font-medium">المنصة</p>
                    <p className="text-gray-600">{remoteClass.platform}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 md:mt-0 flex flex-col justify-center">
              {hasEnded(remoteClass.endTime) ? (
                <div>
                  <span className="block bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-center mb-2">
                    انتهى الفصل
                  </span>
                  {remoteClass.recordingUrl && (
                    <a
                      href={remoteClass.recordingUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-blue-500 text-white px-4 py-2 rounded-md text-center hover:bg-blue-600 transition-colors"
                    >
                      مشاهدة التسجيل
                    </a>
                  )}
                </div>
              ) : hasStarted(remoteClass.startTime) ? (
                <div className="space-y-2">
                  {/* زر الانضمام للفصل باستخدام أدواتنا */}
                  <button
                    onClick={() => {
                      setShowInteractiveSection(true);
                      // التمرير إلى قسم الأدوات التفاعلية
                      document.getElementById('interactive-section')?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="block w-full bg-[var(--primary-color)] text-white px-4 py-2 rounded-md text-center hover:bg-[var(--secondary-color)] transition-colors"
                  >
                    انضم للفصل باستخدام أدواتنا
                  </button>

                  {/* زر الانضمام للفصل باستخدام المنصة الخارجية */}
                  {remoteClass.meetingLink && (
                    <a
                      href={remoteClass.meetingLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="block bg-red-500 text-white px-4 py-2 rounded-md text-center hover:bg-red-600 transition-colors"
                    >
                      انضم عبر {remoteClass.platform}
                    </a>
                  )}
                </div>
              ) : (
                <div className="space-y-2">
                  <span className="block bg-blue-100 text-blue-800 px-4 py-2 rounded-md text-center">
                    سيبدأ في {formatDateTime(remoteClass.startTime)}
                  </span>

                  {/* زر تجربة الأدوات */}
                  <button
                    onClick={() => {
                      setShowInteractiveSection(true);
                      document.getElementById('interactive-section')?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="block w-full bg-gray-500 text-white px-4 py-2 rounded-md text-center hover:bg-gray-600 transition-colors"
                  >
                    تجربة الأدوات التفاعلية
                  </button>
                </div>
              )}

              {canEdit() && (
                <div className="flex space-x-2 space-x-reverse mt-2">
                  <Link
                    href={`/remote-classes/${id}/edit`}
                    className="flex-1 bg-yellow-500 text-white px-4 py-2 rounded-md text-center hover:bg-yellow-600 transition-colors flex items-center justify-center"
                  >
                    <FaEdit className="ml-1" />
                    تعديل
                  </Link>
                  <button
                    onClick={handleDeleteClass}
                    className="flex-1 bg-red-500 text-white px-4 py-2 rounded-md text-center hover:bg-red-600 transition-colors flex items-center justify-center"
                  >
                    <FaTrash className="ml-1" />
                    حذف
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Meeting Info */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">معلومات الاجتماع</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-sm text-gray-700 font-medium mb-1">رابط الاجتماع</p>
              <div className="flex items-center">
                <input
                  type="text"
                  value={remoteClass.meetingLink}
                  readOnly
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none bg-gray-50"
                />
                <a
                  href={remoteClass.meetingLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-l-md hover:bg-[var(--secondary-color)] transition-colors"
                >
                  <FaLink />
                </a>
              </div>
            </div>

            {remoteClass.meetingId && (
              <div>
                <p className="text-sm text-gray-700 font-medium mb-1">معرف الاجتماع</p>
                <div className="flex items-center">
                  <input
                    type="text"
                    value={remoteClass.meetingId}
                    readOnly
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none bg-gray-50"
                  />
                </div>
              </div>
            )}

            {remoteClass.meetingPassword && (
              <div>
                <p className="text-sm text-gray-700 font-medium mb-1">كلمة مرور الاجتماع</p>
                <div className="flex items-center">
                  <input
                    type="text"
                    value={remoteClass.meetingPassword}
                    readOnly
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none bg-gray-50"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Materials */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-gray-800">المواد التعليمية</h2>
            {canEdit() && (
              <button
                onClick={() => setShowUploadForm(!showUploadForm)}
                className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors flex items-center"
              >
                <FaUpload className="ml-2" />
                {showUploadForm ? 'إلغاء' : 'إضافة مادة جديدة'}
              </button>
            )}
          </div>

          {/* Upload Form */}
          {showUploadForm && (
            <div className="bg-gray-50 p-4 rounded-md mb-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4">إضافة مادة تعليمية جديدة</h3>
              <form onSubmit={handleAddMaterial}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                      العنوان <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      value={materialForm.title}
                      onChange={handleMaterialChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="fileType" className="block text-sm font-medium text-gray-700 mb-1">
                      نوع الملف <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="fileType"
                      name="fileType"
                      value={materialForm.fileType}
                      onChange={handleMaterialChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      required
                    >
                      <option value="PDF">PDF</option>
                      <option value="DOC">DOC/DOCX</option>
                      <option value="XLS">XLS/XLSX</option>
                      <option value="PPT">PPT/PPTX</option>
                      <option value="IMAGE">صورة (JPG/PNG)</option>
                      <option value="VIDEO">فيديو</option>
                      <option value="AUDIO">صوت</option>
                      <option value="OTHER">أخرى</option>
                    </select>
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="fileUrl" className="block text-sm font-medium text-gray-700 mb-1">
                      رابط الملف <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="url"
                      id="fileUrl"
                      name="fileUrl"
                      value={materialForm.fileUrl}
                      onChange={handleMaterialChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                      required
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      الوصف
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={materialForm.description}
                      onChange={handleMaterialChange}
                      rows={2}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={uploadLoading}
                    className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors flex items-center"
                  >
                    {uploadLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                        جاري الإضافة...
                      </>
                    ) : (
                      <>
                        <FaUpload className="ml-2" />
                        إضافة المادة
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Materials List */}
          {remoteClass.materials.length === 0 ? (
            <div className="text-center py-8">
              <FaFileAlt className="text-gray-400 text-5xl mx-auto mb-4" />
              <p className="text-gray-600">لا توجد مواد تعليمية متاحة لهذا الفصل</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {remoteClass.materials.map((material) => (
                <div key={material.id} className="py-4 flex items-start">
                  <div className="flex-shrink-0 mt-1 text-2xl">
                    {getFileIcon(material.fileType)}
                  </div>
                  <div className="mr-4 flex-1">
                    <div className="flex justify-between">
                      <h3 className="text-lg font-medium text-gray-800">{material.title}</h3>
                      <span className="text-xs text-gray-500">
                        {new Date(material.createdAt).toLocaleDateString('ar-EG')}
                      </span>
                    </div>
                    {material.description && (
                      <p className="text-gray-600 mt-1">{material.description}</p>
                    )}
                    <div className="mt-2 flex justify-between items-center">
                      <a
                        href={material.fileUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] text-sm font-medium flex items-center"
                      >
                        <FaDownload className="ml-1" />
                        تحميل الملف
                      </a>

                      {canEdit() && (
                        <button
                          onClick={() => handleDeleteMaterial(material.id)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <FaTrash />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Interactive Section - New */}
        <div id="interactive-section" className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <h2 className="text-xl font-bold text-gray-800 ml-4">الفصل التفاعلي</h2>
              <div className="flex space-x-2 space-x-reverse">
                {canEdit() && hasStarted(remoteClass.startTime) && !hasEnded(remoteClass.endTime) && (
                  <button
                    onClick={() => {
                      setShowInteractiveSection(true);
                      setIsVideoEnabled(true);
                      setIsAudioEnabled(true);
                      // طلب إذن الكاميرا والميكروفون
                      navigator.mediaDevices.getUserMedia({ video: true, audio: true })
                        .then(stream => {
                          setVideoStream(stream);
                          setAudioStream(stream);
                          toast.success('تم بدء الفصل بنجاح');
                        })
                        .catch(err => {
                          console.error('Error accessing media devices:', err);
                          toast.error('فشل في الوصول إلى الكاميرا أو الميكروفون');
                        });
                    }}
                    className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600 transition-colors flex items-center"
                  >
                    <FaVideo className="ml-2" />
                    بدء الفصل
                  </button>
                )}

                {/* أزرار التسجيل */}
                {canEdit() && (
                  <>
                    {!isRecording ? (
                      <button
                        onClick={startRecording}
                        className="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600 transition-colors flex items-center"
                        disabled={!videoStream && !screenStream}
                        title={!videoStream && !screenStream ? 'يجب تفعيل الكاميرا أو مشاركة الشاشة أولاً' : 'بدء التسجيل'}
                      >
                        <FaRecordVinyl className="ml-2" />
                        بدء التسجيل
                      </button>
                    ) : (
                      <button
                        onClick={stopRecording}
                        className="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600 transition-colors flex items-center"
                      >
                        <FaStop className="ml-2" />
                        إيقاف التسجيل
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
            <button
              onClick={() => setShowInteractiveSection(!showInteractiveSection)}
              className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-colors flex items-center"
            >
              {showInteractiveSection ? 'إخفاء الأدوات التفاعلية' : 'عرض الأدوات التفاعلية'}
            </button>
          </div>

            {showInteractiveSection && (
              <div className="space-y-6">
                {/* Screen Share Section */}
                {(remoteClass.isScreenShareEnabled || canEdit()) && (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-3 flex items-center">
                      <FaDesktop className="ml-2 text-[var(--primary-color)]" />
                      مشاركة الشاشة
                    </h3>

                    {/* Screen Share Controls */}
                    <div className="flex flex-wrap gap-4 mb-4">
                      <ScreenShareButton
                        onStartSharing={handleStartScreenShare}
                        onStopSharing={handleStopScreenShare}
                        isSharing={isScreenSharing}
                        disabled={!canEdit() && remoteClass.instructor.id !== userId}
                      />
                    </div>

                    {/* Screen Share Display */}
                    {isScreenSharing && screenStream && (
                      <div className="mt-4">
                        <ScreenShareDisplay
                          stream={screenStream}
                          userName={remoteClass.instructor.profile?.name || remoteClass.instructor.username}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Whiteboard Section */}
                {(remoteClass.isWhiteboardEnabled || canEdit()) && (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-3 flex items-center">
                      <FaChalkboard className="ml-2 text-[var(--primary-color)]" />
                      السبورة التفاعلية
                    </h3>

                    <div className="mt-4">
                      <Whiteboard
                        id={`class-${remoteClass.id}`}
                        enableSync={true}
                        roomId={`class-${remoteClass.id}`}
                        userId={userId?.toString() || '0'}
                        username={userRole === 'TEACHER' ? 'المعلم' : 'طالب'}
                        readOnly={!canEdit() && userRole !== 'TEACHER'}
                        height={500}
                      />
                    </div>
                  </div>
                )}

                {/* Video/Audio Settings */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Video Settings */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-3 flex items-center">
                      <FaVideo className="ml-2 text-[var(--primary-color)]" />
                      إعدادات الفيديو
                    </h3>

                    <VideoSettings
                      onVideoChange={handleVideoChange}
                      onQualityChange={handleVideoQualityChange}
                      onDeviceChange={handleVideoDeviceChange}
                      isVideoEnabled={isVideoEnabled}
                      currentQuality={videoQuality}
                      currentDeviceId={videoDeviceId}
                      stream={videoStream}
                    />
                  </div>

                  {/* Audio Settings */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-3 flex items-center">
                      <FaMicrophone className="ml-2 text-[var(--primary-color)]" />
                      إعدادات الصوت
                    </h3>

                    <AudioSettings
                      onAudioChange={handleAudioChange}
                      onQualityChange={handleAudioQualityChange}
                      onDeviceChange={handleAudioDeviceChange}
                      onVolumeChange={handleVolumeChange}
                      isAudioEnabled={isAudioEnabled}
                      currentQuality={audioQuality}
                      currentDeviceId={audioDeviceId}
                      currentVolume={1.0}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>


        {/* Attendees */}
        {remoteClass.classe && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">الطلاب المشاركون</h2>

            {remoteClass.classe.students.length === 0 ? (
              <div className="text-center py-8">
                <FaUsers className="text-gray-400 text-5xl mx-auto mb-4" />
                <p className="text-gray-600">لا يوجد طلاب مسجلين في هذا الفصل</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {remoteClass.classe.students.map((student) => (
                  <div key={student.id} className="bg-gray-50 p-4 rounded-md">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-[var(--primary-color)] text-white flex items-center justify-center">
                        {(student.user?.profile?.name || student.user?.username || 'غير معروف').charAt(0)}
                      </div>
                      <div className="mr-3">
                        <p className="font-medium text-gray-800">
                          {student.user?.profile?.name || student.user?.username || 'غير معروف'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default RemoteClassDetailPage;

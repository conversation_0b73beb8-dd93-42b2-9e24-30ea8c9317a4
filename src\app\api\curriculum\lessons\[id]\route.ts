import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/curriculum/lessons/[id] - الحصول على درس محدد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف الدرس غير صالح" },
        { status: 400 }
      );
    }

    const lesson = await prisma.curriculumLesson.findUnique({
      where: { id },
      include: {
        resources: {
          orderBy: {
            id: 'asc'
          }
        }
      }
    });

    if (!lesson) {
      return NextResponse.json(
        { message: "الدرس غير موجود" },
        { status: 404 }
      );
    }

    return NextResponse.json(lesson);
  } catch (error) {
    console.error('Error fetching lesson:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب الدرس" },
      { status: 500 }
    );
  }
}

// PUT /api/curriculum/lessons/[id] - تحديث درس محدد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف الدرس غير صالح" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { title, description, order } = body;

    if (!title) {
      return NextResponse.json(
        { message: "يجب توفير عنوان الدرس" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدرس
    const lesson = await prisma.curriculumLesson.findUnique({
      where: { id },
    });

    if (!lesson) {
      return NextResponse.json(
        { message: "الدرس غير موجود" },
        { status: 404 }
      );
    }

    // تحديث الدرس
    const updatedLesson = await prisma.curriculumLesson.update({
      where: { id },
      data: {
        title,
        description,
        order: order || lesson.order,
      },
    });

    return NextResponse.json(updatedLesson);
  } catch (error) {
    console.error('Error updating lesson:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث الدرس" },
      { status: 500 }
    );
  }
}

// DELETE /api/curriculum/lessons/[id] - حذف درس محدد
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف الدرس غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدرس
    const lesson = await prisma.curriculumLesson.findUnique({
      where: { id },
    });

    if (!lesson) {
      return NextResponse.json(
        { message: "الدرس غير موجود" },
        { status: 404 }
      );
    }

    // حذف الدرس (سيتم حذف الموارد تلقائيًا بسبب onDelete: Cascade)
    await prisma.curriculumLesson.delete({
      where: { id },
    });

    return NextResponse.json({ message: "تم حذف الدرس بنجاح" });
  } catch (error) {
    console.error('Error deleting lesson:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف الدرس" },
      { status: 500 }
    );
  }
}

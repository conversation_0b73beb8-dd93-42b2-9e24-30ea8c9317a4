import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// GET /api/supervisor-reports - الحصول على قائمة التقارير الموحدة
export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || !['ADMIN', 'EMPLOYEE'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به، يجب أن تكون مسؤول أو موظف' },
        { status: 401 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // بناء شروط البحث
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (startDate && endDate) {
      where.periodStart = {
        gte: new Date(startDate),
      };
      where.periodEnd = {
        lte: new Date(endDate),
      };
    }

    // حساب الإزاحة للصفحات
    const skip = (page - 1) * limit;

    // جلب التقارير مع معلومات المنشئ
    const [reports, totalCount] = await Promise.all([
      prisma.supervisorReport.findMany({
        where,
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              profile: {
                select: {
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.supervisorReport.count({ where })
    ]);

    // تحويل البيانات للعرض
    const formattedReports = reports.map(report => ({
      id: report.id,
      title: report.title,
      description: report.description,
      periodStart: report.periodStart,
      periodEnd: report.periodEnd,
      status: report.status,
      createdBy: report.creator?.profile?.name || report.creator?.username || 'غير محدد',
      createdAt: report.createdAt,
      updatedAt: report.updatedAt,
      hasLiteraryContent: !!report.literaryContent,
      hasFinancialData: !!report.financialData
    }));

    return NextResponse.json({
      success: true,
      data: {
        reports: formattedReports,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      },
      message: 'تم جلب التقارير بنجاح'
    });

  } catch (error) {
    console.error('Error fetching supervisor reports:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب التقارير'
    }, { status: 500 });
  }
}

// POST /api/supervisor-reports - إنشاء تقرير موحد جديد
export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || !['ADMIN', 'EMPLOYEE'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به، يجب أن تكون مسؤول أو موظف' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      title,
      description,
      periodStart,
      periodEnd,
      literaryContent,
      financialData,
      officeSettings,
      status = 'DRAFT'
    } = body;

    // التحقق من البيانات المطلوبة
    if (!title || !periodStart || !periodEnd) {
      return NextResponse.json({
        success: false,
        error: 'العنوان وتاريخ البداية والنهاية مطلوبة'
      }, { status: 400 });
    }

    // التحقق من صحة التواريخ
    const startDate = new Date(periodStart);
    const endDate = new Date(periodEnd);
    
    if (startDate >= endDate) {
      return NextResponse.json({
        success: false,
        error: 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية'
      }, { status: 400 });
    }

    // إنشاء التقرير الجديد
    const newReport = await prisma.supervisorReport.create({
      data: {
        title,
        description,
        periodStart: startDate,
        periodEnd: endDate,
        literaryContent: literaryContent ? JSON.stringify(literaryContent) : null,
        financialData: financialData ? JSON.stringify(financialData) : null,
        officeSettings: officeSettings ? JSON.stringify(officeSettings) : null,
        status,
        createdBy: userData.id
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            profile: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: newReport,
      message: 'تم إنشاء التقرير بنجاح'
    });

  } catch (error) {
    console.error('Error creating supervisor report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء إنشاء التقرير'
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

// GET /api/khatm-reports/download - تنزيل تقرير إنجاز
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف التقرير مطلوب'
      }, { status: 400 });
    }

    // جلب التقرير مع بيانات الطالب
    const report = await prisma.khatmAchievementReport.findUnique({
      where: { id: parseInt(id) },
      include: {
        student: true
      }
    });

    if (!report) {
      return NextResponse.json({
        success: false,
        error: 'تقرير الإنجاز غير موجود'
      }, { status: 404 });
    }

    // جلب جميع مجالس الختم التي حضرها الطالب في الفترة المحددة
    const attendances = await prisma.khatmSessionAttendance.findMany({
      where: {
        studentId: report.studentId,
        khatmSession: {
          date: {
            gte: report.startDate,
            lte: report.endDate
          }
        }
      },
      include: {
        khatmSession: {
          include: {
            surah: true,
            teacher: true
          }
        },
        progressRecords: true
      },
      orderBy: {
        khatmSession: {
          date: 'asc'
        }
      }
    });

    // إنشاء محتوى HTML للتقرير
    const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${report.title}</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        :root {
          --primary-color: var(--primary-color);
          --primary-light: #f8fffd;
          --primary-dark: #0d7a6a;
          --secondary-color: #f8f9fa;
          --border-color: #e0e0e0;
          --text-color: #333;
          --text-light: #666;
          --success-color: #10b981;
          --warning-color: #f59e0b;
          --danger-color: #ef4444;
          --star-color: #f59e0b;
        }

        * {
          box-sizing: border-box;
        }

        body {
          font-family: 'Tajawal', Arial, sans-serif;
          margin: 0;
          padding: 30px;
          color: var(--text-color);
          direction: rtl;
          line-height: 1.6;
          background-color: #fff;
        }

        .container {
          max-width: 1000px;
          margin: 0 auto;
          background-color: #fff;
          border-radius: 10px;
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }

        .report-header {
          text-align: center;
          padding: 30px;
          background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
          color: white;
          position: relative;
        }

        .report-header::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 10px;
          background: linear-gradient(90deg,
            var(--primary-color) 0%,
            var(--primary-light) 50%,
            var(--primary-color) 100%);
        }

        .report-title {
          font-size: 28px;
          margin-bottom: 10px;
          font-weight: 700;
        }

        .report-subtitle {
          font-size: 16px;
          opacity: 0.9;
          margin-bottom: 5px;
        }

        .report-date {
          font-size: 14px;
          opacity: 0.8;
          margin-top: 10px;
        }

        .report-content {
          padding: 30px;
        }

        .student-info {
          background-color: var(--primary-light);
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 30px;
          border: 1px solid var(--primary-color);
          display: flex;
          align-items: center;
        }

        .student-avatar {
          width: 80px;
          height: 80px;
          background-color: var(--primary-color);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 32px;
          font-weight: bold;
          margin-left: 20px;
        }

        .student-details h2 {
          margin-top: 0;
          color: var(--primary-color);
          font-size: 22px;
          border-bottom: 2px solid var(--primary-color);
          padding-bottom: 5px;
          display: inline-block;
        }

        .student-details p {
          margin: 5px 0;
        }

        .summary-box {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .summary-item {
          background-color: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
          text-align: center;
          transition: transform 0.3s;
          border-top: 4px solid var(--primary-color);
        }

        .summary-item h3 {
          margin-top: 0;
          color: var(--primary-color);
          font-size: 18px;
          margin-bottom: 15px;
        }

        .summary-item p {
          font-size: 24px;
          font-weight: bold;
          margin: 10px 0;
        }

        .progress-bar-container {
          width: 100%;
          background-color: #e0e0e0;
          border-radius: 10px;
          margin-top: 10px;
          overflow: hidden;
        }

        .progress-bar {
          height: 20px;
          background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
          border-radius: 10px;
          text-align: center;
          color: white;
          line-height: 20px;
          font-size: 12px;
          font-weight: bold;
          transition: width 0.5s;
        }

        .section-title {
          color: var(--primary-color);
          border-bottom: 2px solid var(--primary-color);
          padding-bottom: 10px;
          margin-top: 40px;
          margin-bottom: 20px;
          font-size: 22px;
          position: relative;
        }

        .section-title::after {
          content: '';
          position: absolute;
          bottom: -2px;
          right: 0;
          width: 50px;
          height: 2px;
          background-color: var(--primary-dark);
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
          border-radius: 8px;
          overflow: hidden;
        }

        th, td {
          padding: 12px 15px;
          text-align: right;
        }

        th {
          background-color: var(--primary-color);
          color: white;
          font-weight: 500;
          text-transform: uppercase;
          font-size: 14px;
          letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
          background-color: var(--secondary-color);
        }

        tr:hover {
          background-color: var(--primary-light);
        }

        .rating {
          display: inline-block;
          unicode-bidi: bidi-override;
          direction: ltr;
          text-align: center;
        }

        .rating span {
          display: inline-block;
          position: relative;
          width: 1.1em;
          color: var(--star-color);
          font-size: 18px;
        }

        .footer {
          margin-top: 40px;
          text-align: center;
          font-size: 14px;
          color: var(--text-light);
          border-top: 1px solid var(--border-color);
          padding-top: 20px;
        }

        .footer img {
          max-width: 150px;
          margin-bottom: 10px;
        }

        .chart-container {
          display: flex;
          justify-content: space-between;
          margin: 30px 0;
          flex-wrap: wrap;
        }

        .chart {
          width: 48%;
          background-color: white;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
          margin-bottom: 20px;
        }

        .chart h3 {
          text-align: center;
          color: var(--primary-color);
          margin-top: 0;
        }

        .pie-chart {
          position: relative;
          width: 150px;
          height: 150px;
          border-radius: 50%;
          background: conic-gradient(
            var(--primary-color) 0% ${(report.memorizedAyahs / report.totalAyahs) * 100}%,
            #e0e0e0 ${(report.memorizedAyahs / report.totalAyahs) * 100}% 100%
          );
          margin: 20px auto;
        }

        .pie-chart::before {
          content: "${Math.round((report.memorizedAyahs / report.totalAyahs) * 100)}%";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 20px;
          font-weight: bold;
          color: var(--primary-color);
          width: 100px;
          height: 100px;
          background-color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .attendance-chart {
          position: relative;
          width: 150px;
          height: 150px;
          border-radius: 50%;
          background: conic-gradient(
            var(--primary-color) 0% ${(report.attendedSessions / report.totalSessions) * 100}%,
            #e0e0e0 ${(report.attendedSessions / report.totalSessions) * 100}% 100%
          );
          margin: 20px auto;
        }

        .attendance-chart::before {
          content: "${Math.round((report.attendedSessions / report.totalSessions) * 100)}%";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 20px;
          font-weight: bold;
          color: var(--primary-color);
          width: 100px;
          height: 100px;
          background-color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .rating-large {
          text-align: center;
          margin: 20px 0;
        }

        .rating-large .stars {
          font-size: 30px;
          color: var(--star-color);
          letter-spacing: 5px;
        }

        .rating-large .rating-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--primary-color);
          margin-top: 10px;
        }

        @media print {
          body {
            padding: 0;
            background-color: white;
          }

          .container {
            box-shadow: none;
            max-width: 100%;
          }

          .report-header {
            background-color: var(--primary-color) !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }

          th {
            background-color: var(--primary-color) !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            color: white !important;
          }

          tr:nth-child(even) {
            background-color: var(--secondary-color) !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }

          .progress-bar {
            background: var(--primary-color) !important;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="report-header">
          <h1 class="report-title">${report.title}</h1>
          <p class="report-subtitle">${report.description || 'تقرير إنجاز الطالب في مجالس الختم'}</p>
          <p class="report-date">الفترة: ${format(new Date(report.startDate), 'PPP', { locale: ar })} - ${format(new Date(report.endDate), 'PPP', { locale: ar })}</p>
        </div>

        <div class="report-content">
          <div class="student-info">
            <div class="student-avatar">${report.student.name.charAt(0)}</div>
            <div class="student-details">
              <h2>معلومات الطالب</h2>
              <p><strong>الاسم:</strong> ${report.student.name}</p>
              <p><strong>العمر:</strong> ${report.student.age} سنة</p>
            </div>
          </div>

          <div class="summary-box">
            <div class="summary-item">
              <h3>الجلسات</h3>
              <p>${report.attendedSessions} / ${report.totalSessions}</p>
              <div class="progress-bar-container">
                <div class="progress-bar" style="width: ${(report.attendedSessions / report.totalSessions) * 100}%">
                  ${Math.round((report.attendedSessions / report.totalSessions) * 100)}%
                </div>
              </div>
            </div>

            <div class="summary-item">
              <h3>الآيات المحفوظة</h3>
              <p>${report.memorizedAyahs} / ${report.totalAyahs}</p>
              <div class="progress-bar-container">
                <div class="progress-bar" style="width: ${(report.memorizedAyahs / report.totalAyahs) * 100}%">
                  ${Math.round((report.memorizedAyahs / report.totalAyahs) * 100)}%
                </div>
              </div>
            </div>

            <div class="summary-item">
              <h3>الآيات المراجعة</h3>
              <p>${report.reviewedAyahs} آية</p>
            </div>

            <div class="summary-item">
              <h3>متوسط التقييم</h3>
              <p>${report.averageRating.toFixed(1)} / 5</p>
              <div class="rating">
                ${Array(5).fill(0).map((_, i) =>
                  i < Math.round(report.averageRating) ? '★' : '☆'
                ).join('')}
              </div>
            </div>
          </div>

          <div class="chart-container">
            <div class="chart">
              <h3>نسبة الحفظ</h3>
              <div class="pie-chart"></div>
              <p style="text-align: center; margin-top: 10px;">
                تم حفظ ${report.memorizedAyahs} آية من أصل ${report.totalAyahs} آية
              </p>
            </div>

            <div class="chart">
              <h3>نسبة الحضور</h3>
              <div class="attendance-chart"></div>
              <p style="text-align: center; margin-top: 10px;">
                تم حضور ${report.attendedSessions} جلسة من أصل ${report.totalSessions} جلسة
              </p>
            </div>
          </div>

          <div class="chart">
            <h3>تقييم جودة الحفظ</h3>
            <div class="rating-large">
              <div class="stars">
                ${Array(5).fill(0).map((_, i) =>
                  i < Math.round(report.averageRating) ? '★' : '☆'
                ).join('')}
              </div>
              <div class="rating-value">${report.averageRating.toFixed(1)} / 5</div>
            </div>
            <div class="progress-bar-container" style="max-width: 300px; margin: 0 auto;">
              <div class="progress-bar" style="width: ${(report.averageRating / 5) * 100}%">
                ${Math.round((report.averageRating / 5) * 100)}%
              </div>
            </div>
          </div>

          <h2 class="section-title">تفاصيل الجلسات</h2>
          <table>
            <thead>
              <tr>
                <th>التاريخ</th>
                <th>عنوان المجلس</th>
                <th>المعلم</th>
                <th>السورة</th>
                <th>الآيات المحفوظة</th>
                <th>الآيات المراجعة</th>
                <th>التقييم</th>
              </tr>
            </thead>
            <tbody>
              ${attendances.map(attendance => `
                <tr>
                  <td>${format(new Date(attendance.khatmSession.date), 'PPP', { locale: ar })}</td>
                  <td>${attendance.khatmSession.title}</td>
                  <td>${attendance.khatmSession.teacher.name}</td>
                  <td>${attendance.khatmSession.surah ? attendance.khatmSession.surah.name : '-'}</td>
                  <td>${attendance.progressRecords.reduce((sum, record) => sum + record.memorizedAyahs, 0)}</td>
                  <td>${attendance.progressRecords.reduce((sum, record) => sum + record.reviewedAyahs, 0)}</td>
                  <td>
                    <div class="rating">
                      ${Array(5).fill(0).map((_, i) => {
                        const avgRating = attendance.progressRecords.length > 0
                          ? attendance.progressRecords.reduce((sum, record) => sum + record.qualityRating, 0) / attendance.progressRecords.length
                          : 0;
                        return i < Math.round(avgRating) ? '★' : '☆';
                      }).join('')}
                    </div>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="footer">
            <p>تم إنشاء هذا التقرير في ${format(new Date(), 'PPPp', { locale: ar })}</p>
            <p>نظام إدارة مجالس الختم</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `;

    // إنشاء استجابة HTML
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Content-Disposition': `attachment; filename="تقرير_إنجاز_${report.student.name}.html"`
      }
    });
  } catch (error) {
    console.error('Error downloading achievement report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تنزيل تقرير الإنجاز'
    }, { status: 500 });
  }
}

import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/quran/surahs
export async function GET() {
  try {
    // جلب قائمة السور من قاعدة البيانات
    const surahs = await prisma.surah.findMany({
      orderBy: {
        number: 'asc'
      },
      select: {
        id: true,
        name: true,
        number: true,
        totalAyahs: true
      }
    });

    // تنسيق البيانات للاستخدام في واجهة المستخدم
    const formattedSurahs = surahs.map(surah => ({
      id: surah.id,
      name: surah.name,
      number: surah.number,
      versesCount: surah.totalAyahs
    }));

    return NextResponse.json(formattedSurahs);
  } catch (error) {
    console.error('Error fetching surahs:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب قائمة السور' },
      { status: 500 }
    );
  }
}

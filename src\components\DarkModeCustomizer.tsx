'use client';

import React, { useState, useEffect } from 'react';
import { FaTimes, FaPalette, FaUndo, FaSave, FaTimes as FaCancel } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { SiteColors } from '@/utils/simpleColorSystem';
import {
  loadDarkModeColors,
  saveDarkModeColors,
  loadLightModeColors,
  saveLightModeColors,
  DEFAULT_DARK_COLORS,
  DEFAULT_LIGHT_COLORS
} from '@/utils/darkModeStorage';

interface DarkModeCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode: boolean;
}

const DarkModeCustomizer: React.FC<DarkModeCustomizerProps> = ({
  isOpen,
  onClose,
  isDarkMode
}) => {
  const [colors, setColors] = useState<SiteColors>(DEFAULT_DARK_COLORS);
  const [originalColors, setOriginalColors] = useState<SiteColors>(DEFAULT_DARK_COLORS);
  const [hasChanges, setHasChanges] = useState(false);

  // تحميل الألوان عند فتح المكون (الوضع المظلم فقط)
  useEffect(() => {
    if (isOpen) {
      const currentColors = loadDarkModeColors();
      setColors(currentColors);
      setOriginalColors(currentColors);
      setHasChanges(false);
    }
  }, [isOpen]);

  // تطبيق الألوان فوراً عند التغيير
  useEffect(() => {
    if (isOpen) {
      applyColorsToPage(colors);
      setHasChanges(JSON.stringify(colors) !== JSON.stringify(originalColors));
    }
  }, [colors, originalColors, isOpen]);

  // دالة تطبيق الألوان على الصفحة
  const applyColorsToPage = (newColors: SiteColors) => {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;
    root.style.setProperty('--primary-color', newColors.primaryColor);
    root.style.setProperty('--secondary-color', newColors.secondaryColor);
    root.style.setProperty('--sidebar-color', newColors.sidebarColor);
    root.style.setProperty('--background-color', newColors.backgroundColor);
    root.style.setProperty('--accent-color', newColors.accentColor);
    root.style.setProperty('--text-color', newColors.textColor);
  };

  // دالة تغيير لون معين
  const handleColorChange = (colorKey: keyof SiteColors, value: string) => {
    setColors(prev => ({
      ...prev,
      [colorKey]: value
    }));
  };

  // دالة الحفظ (الوضع المظلم فقط)
  const handleSave = () => {
    try {
      saveDarkModeColors(colors);
      // تطبيق الألوان فوراً إذا كان المستخدم في الوضع المظلم
      if (isDarkMode) {
        localStorage.setItem('siteColors', JSON.stringify(colors));
      }
      toast.success('🌙 تم حفظ ألوان الوضع المظلم بنجاح');

      setOriginalColors(colors);
      setHasChanges(false);
      onClose();
    } catch (error) {
      console.error('Error saving colors:', error);
      toast.error('حدث خطأ أثناء حفظ الألوان');
    }
  };

  // دالة الإلغاء
  const handleCancel = () => {
    if (hasChanges) {
      // إعادة الألوان الأصلية
      setColors(originalColors);
      applyColorsToPage(originalColors);
    }
    onClose();
  };

  // دالة إعادة التعيين للافتراضية (الوضع المظلم فقط)
  const handleReset = () => {
    setColors(DEFAULT_DARK_COLORS);
    toast.info('تم إعادة تعيين ألوان الوضع المظلم للافتراضية');
  };

  // دالة تطبيق مجموعة ألوان جاهزة (الوضع المظلم فقط)
  const applyPresetColors = (preset: string) => {
    let presetColors: SiteColors;

    switch (preset) {
      case 'blue':
        presetColors = {
          primaryColor: '#1e40af',
          secondaryColor: '#3b82f6',
          sidebarColor: '#1e3a8a',
          backgroundColor: '#0f172a',
          accentColor: '#60a5fa',
          textColor: '#f1f5f9'
        };
        break;
      case 'green':
        presetColors = {
          primaryColor: '#059669',
          secondaryColor: '#10b981',
          sidebarColor: '#064e3b',
          backgroundColor: '#0f1419',
          accentColor: '#34d399',
          textColor: '#f0fdf4'
        };
        break;
      case 'purple':
        presetColors = {
          primaryColor: '#7c3aed',
          secondaryColor: '#8b5cf6',
          sidebarColor: '#581c87',
          backgroundColor: '#1e1b4b',
          accentColor: '#a78bfa',
          textColor: '#f5f3ff'
        };
        break;
      case 'orange':
        presetColors = {
          primaryColor: '#ea580c',
          secondaryColor: '#f97316',
          sidebarColor: '#9a3412',
          backgroundColor: '#1c1917',
          accentColor: '#fb923c',
          textColor: '#fef7ed'
        };
        break;
      default:
        presetColors = DEFAULT_DARK_COLORS;
    }

    setColors(presetColors);
    toast.success(`تم تطبيق مجموعة الألوان المظلمة ${preset === 'blue' ? 'الزرقاء' : preset === 'green' ? 'الخضراء' : preset === 'purple' ? 'البنفسجية' : 'البرتقالية'}`);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <FaPalette className="text-[var(--primary-color)]" />
            🌙 تخصيص الوضع المظلم
          </h2>
          <button
            onClick={handleCancel}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors p-1"
          >
            <FaTimes className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {/* مجموعات الألوان الجاهزة */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">مجموعات ألوان جاهزة</h3>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => applyPresetColors('blue')}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                🔵 أزرق
              </button>
              <button
                onClick={() => applyPresetColors('green')}
                className="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                🟢 أخضر
              </button>
              <button
                onClick={() => applyPresetColors('purple')}
                className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                🟣 بنفسجي
              </button>
              <button
                onClick={() => applyPresetColors('orange')}
                className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                🟠 برتقالي
              </button>
            </div>
          </div>

          {/* منتقيات الألوان */}
          <div className="space-y-4">
            {/* اللون الرئيسي */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                اللون الرئيسي
              </label>
              <div className="flex items-center gap-3">
                <input
                  type="color"
                  value={colors.primaryColor}
                  onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                  className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                />
                <input
                  type="text"
                  value={colors.primaryColor}
                  onChange={(e) => handleColorChange('primaryColor', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* اللون الثانوي */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                اللون الثانوي
              </label>
              <div className="flex items-center gap-3">
                <input
                  type="color"
                  value={colors.secondaryColor}
                  onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                  className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                />
                <input
                  type="text"
                  value={colors.secondaryColor}
                  onChange={(e) => handleColorChange('secondaryColor', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* لون الخلفية */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                لون الخلفية
              </label>
              <div className="flex items-center gap-3">
                <input
                  type="color"
                  value={colors.backgroundColor}
                  onChange={(e) => handleColorChange('backgroundColor', e.target.value)}
                  className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                />
                <input
                  type="text"
                  value={colors.backgroundColor}
                  onChange={(e) => handleColorChange('backgroundColor', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* لون النص */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                لون النص
              </label>
              <div className="flex items-center gap-3">
                <input
                  type="color"
                  value={colors.textColor}
                  onChange={(e) => handleColorChange('textColor', e.target.value)}
                  className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
                />
                <input
                  type="text"
                  value={colors.textColor}
                  onChange={(e) => handleColorChange('textColor', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* معلومات */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              💡 التغييرات تطبق فوراً للمعاينة. اضغط "حفظ" لحفظ ألوان الوضع المظلم نهائياً.
            </p>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              ℹ️ ألوان الوضع النهاري يتم تخصيصها من إعدادات المسؤول فقط.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleReset}
            className="flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            <FaUndo className="h-4 w-4" />
            إعادة تعيين
          </button>

          <div className="flex gap-2">
            <button
              onClick={handleCancel}
              className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
            >
              <FaCancel className="h-4 w-4" />
              إلغاء
            </button>
            <button
              onClick={handleSave}
              className="flex items-center gap-2 px-4 py-2 bg-[var(--primary-color)] text-white rounded hover:opacity-90 transition-opacity"
            >
              <FaSave className="h-4 w-4" />
              حفظ
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DarkModeCustomizer;

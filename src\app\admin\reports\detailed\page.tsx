"use client";

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-toastify';
import {
  FaChartBar,
  FaFileExcel,
  FaFilePdf,
  FaCalendarAlt,
  FaFilter,
  FaSearch
} from 'react-icons/fa';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

// تعريف واجهة التقرير التفصيلي
interface DetailedReport {
  expenses: {
    total: number;
    byCategory: Array<{
      categoryId: number;
      categoryName: string;
      amount: number;
      percentage: number;
      count: number;
    }>;
    monthly: Array<{
      month: string;
      amount: number;
    }>;
  };
  comparisons: {
    previousPeriod: {
      total: number;
      difference: number;
      percentageChange: number;
    };
    byCategory: Array<{
      categoryId: number;
      categoryName: string;
      currentAmount: number;
      previousAmount: number;
      difference: number;
      percentageChange: number;
    }>;
  };
}

export default function DetailedReportsPage() {
  // متغيرات حالة التقرير
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [filters, setFilters] = useState({
    categoryFilter: 'all',
    reportType: 'expenses',
    comparisonType: 'previous-period'
  });
  const [report, setReport] = useState<DetailedReport | null>(null);
  const [loading, setLoading] = useState(false);

  // جلب التقرير التفصيلي
  const fetchDetailedReport = useCallback(async () => {
    try {
      setLoading(true);
      toast.info('جاري جلب التقرير التفصيلي...');

      // في الواقع، سيتم استبدال هذا بطلب فعلي للخادم
      const queryParams = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        type: filters.reportType,
        comparison: filters.comparisonType,
      });

      if (filters.categoryFilter !== 'all') {
        queryParams.append('categoryId', filters.categoryFilter);
      }

      // محاكاة طلب الخادم
      console.log(`طلب التقرير: /api/reports/detailed?${queryParams.toString()}`);

      // محاكاة بيانات التقرير
      setTimeout(() => {
        // إنشاء بيانات وهمية للتقرير
        const mockReport: DetailedReport = {
          expenses: {
            total: 15000,
            byCategory: [
              { categoryId: 1, categoryName: 'طعام', amount: 5000, percentage: 33.33, count: 12 },
              { categoryId: 2, categoryName: 'مواصلات', amount: 3000, percentage: 20, count: 8 },
              { categoryId: 3, categoryName: 'سكن', amount: 7000, percentage: 46.67, count: 3 }
            ],
            monthly: [
              { month: 'يناير', amount: 7500 },
              { month: 'فبراير', amount: 7500 }
            ]
          },
          comparisons: {
            previousPeriod: {
              total: 12000,
              difference: 3000,
              percentageChange: 25
            },
            byCategory: [
              { categoryId: 1, categoryName: 'طعام', currentAmount: 5000, previousAmount: 4000, difference: 1000, percentageChange: 25 },
              { categoryId: 2, categoryName: 'مواصلات', currentAmount: 3000, previousAmount: 3000, difference: 0, percentageChange: 0 },
              { categoryId: 3, categoryName: 'سكن', currentAmount: 7000, previousAmount: 5000, difference: 2000, percentageChange: 40 }
            ]
          }
        };

        setReport(mockReport);
        setLoading(false);
        toast.success('تم جلب التقرير التفصيلي بنجاح');
      }, 1000);
    } catch (error) {
      console.error('Error fetching detailed report:', error);
      toast.error('فشل في جلب التقرير التفصيلي');
      setLoading(false);
    }
  }, [dateRange, filters]);

  // تصدير التقرير إلى Excel
  const handleExportToExcel = () => {
    if (!report) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // تنفيذ تصدير البيانات إلى Excel
      // ملاحظة: تم تعطيل وظيفة exportToExcel لأنها غير معرفة
      // يمكن استخدام مكتبة مثل xlsx أو exceljs لتنفيذ هذه الوظيفة

      const fileName = `التقرير_التفصيلي_${new Date().toISOString().split('T')[0]}.xlsx`;
      toast.info(`سيتم تصدير البيانات إلى ملف Excel: ${fileName}`);

      // مثال على كيفية تنفيذ التصدير باستخدام مكتبة xlsx:
      // const worksheet = XLSX.utils.json_to_sheet(data);
      // const workbook = XLSX.utils.book_new();
      // XLSX.utils.book_append_sheet(workbook, worksheet, 'التقرير التفصيلي');
      // XLSX.writeFile(workbook, fileName);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تصدير التقرير إلى PDF
  const handleExportToPdf = () => {
    if (!report) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // تنفيذ تصدير البيانات إلى PDF
      // ملاحظة: تم تعطيل وظيفة exportToPdf لأنها غير معرفة
      // يمكن استخدام مكتبة مثل jspdf أو pdfmake لتنفيذ هذه الوظيفة

      const fileName = `التقرير_التفصيلي_${new Date().toISOString().split('T')[0]}.pdf`;
      toast.info(`سيتم تصدير البيانات إلى ملف PDF: ${fileName}`);

      // مثال على كيفية تنفيذ التصدير باستخدام مكتبة jspdf:
      // const doc = new jsPDF();
      // doc.text('التقرير التفصيلي للمصروفات', 10, 10);
      // doc.save(fileName);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تحميل البيانات عند تحميل الصفحة أو تغيير المعايير
  useEffect(() => {
    fetchDetailedReport();
  }, [fetchDetailedReport]);

  return (
    <OptimizedProtectedRoute requiredPermission="admin.reports.detailed.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">التقارير التفصيلية</h1>
        <QuickActionButtons
          entityType="reports.detailed"
          actions={[
            {
              key: 'export-excel',
              label: 'تصدير Excel',
              icon: <FaFileExcel />,
              onClick: handleExportToExcel,
              variant: 'outline',
              disabled: !report
            },
            {
              key: 'export-pdf',
              label: 'تصدير PDF',
              icon: <FaFilePdf />,
              onClick: handleExportToPdf,
              variant: 'outline',
              disabled: !report
            }
          ]}
          className="flex gap-2"
        />
      </div>

      {/* فلاتر التقرير */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <FaFilter className="text-primary" />
          <span>فلاتر التقرير</span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* فلتر التاريخ */}
          <div>
            <Label htmlFor="startDate" className="block mb-2">من تاريخ</Label>
            <div className="flex items-center">
              <FaCalendarAlt className="text-gray-400 mr-2" />
              <Input
                id="startDate"
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange({ ...dateRange, startDate: e.target.value })}
                className="flex-1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="endDate" className="block mb-2">إلى تاريخ</Label>
            <div className="flex items-center">
              <FaCalendarAlt className="text-gray-400 mr-2" />
              <Input
                id="endDate"
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange({ ...dateRange, endDate: e.target.value })}
                className="flex-1"
              />
            </div>
          </div>

          {/* فلتر الفئة */}
          <div>
            <Label htmlFor="categoryFilter" className="block mb-2">الفئة</Label>
            <Select
              value={filters.categoryFilter}
              onValueChange={(value) => setFilters({ ...filters, categoryFilter: value })}
            >
              <SelectTrigger id="categoryFilter">
                <SelectValue placeholder="اختر الفئة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الفئات</SelectItem>
                <SelectItem value="1">طعام</SelectItem>
                <SelectItem value="2">مواصلات</SelectItem>
                <SelectItem value="3">سكن</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* فلتر نوع التقرير */}
          <div>
            <Label htmlFor="reportType" className="block mb-2">نوع التقرير</Label>
            <Select
              value={filters.reportType}
              onValueChange={(value) => setFilters({ ...filters, reportType: value })}
            >
              <SelectTrigger id="reportType">
                <SelectValue placeholder="اختر نوع التقرير" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="expenses">المصروفات</SelectItem>
                <SelectItem value="income">الإيرادات</SelectItem>
                <SelectItem value="both">الإيرادات والمصروفات</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <Button
            className="flex items-center gap-2"
            onClick={() => fetchDetailedReport()}
            disabled={loading}
          >
            <FaSearch />
            <span>{loading ? 'جاري التحميل...' : 'تحديث التقرير'}</span>
          </Button>
        </div>
      </div>

      {/* محتوى التقرير */}
      <div className="grid grid-cols-1 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <FaChartBar className="text-primary" />
            <span>تقرير المصروفات التفصيلي</span>
          </h2>

          {loading ? (
            <div className="text-center py-8">
              <p className="text-gray-500">جاري تحميل البيانات...</p>
            </div>
          ) : !report ? (
            <div className="text-center py-8">
              <p className="text-gray-500">لم يتم تحميل أي بيانات بعد. يرجى تحديد معايير التقرير والضغط على تحديث التقرير.</p>
            </div>
          ) : (
            <div className="py-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <h3 className="text-lg font-medium text-green-800 mb-2">إجمالي المصروفات</h3>
                  <p className="text-2xl font-bold text-primary-color">{report.expenses.total} ريال</p>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h3 className="text-lg font-medium text-blue-800 mb-2">الفترة</h3>
                  <p className="text-lg font-medium text-blue-600">من {dateRange.startDate} إلى {dateRange.endDate}</p>
                </div>

                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <h3 className="text-lg font-medium text-purple-800 mb-2">مقارنة مع الفترة السابقة</h3>
                  <p className="text-lg font-medium text-purple-600">
                    {report.comparisons.previousPeriod.percentageChange > 0 ? '+' : ''}
                    {report.comparisons.previousPeriod.percentageChange}%
                  </p>
                </div>
              </div>

              <p className="text-gray-500 mb-4">تم تحميل البيانات بنجاح. يمكنك الآن تصدير التقرير.</p>
            </div>
          )}
        </div>
      </div>
      </div>
    </OptimizedProtectedRoute>
  );
}

import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';

// تعريف النوع للبيانات المصدرة
export interface ExportData {
  title: string;
  subtitle?: string;
  headers: string[];
  data: (string | number | boolean | null)[][];
  summary?: Record<string, string | number | boolean | null>;
  charts?: { id: string; title: string }[];
  logo?: string;
  rtl?: boolean;
}

// تصدير البيانات إلى ملف Excel
export const exportToExcel = (exportData: ExportData, fileName: string = 'report') => {
  try {
    // إنشاء مصنف عمل جديد
    const wb = XLSX.utils.book_new();

    // إنشاء ورقة عمل للبيانات الرئيسية
    const ws = XLSX.utils.aoa_to_sheet([
      [exportData.title], // عنوان التقرير
      exportData.subtitle ? [exportData.subtitle] : [], // العنوان الفرعي إذا كان موجودًا
      [], // سطر فارغ
      exportData.headers, // رؤوس الأعمدة
      ...exportData.data // البيانات
    ]);

    // إضافة ورقة العمل إلى المصنف
    XLSX.utils.book_append_sheet(wb, ws, 'البيانات');

    // إذا كان هناك ملخص، قم بإنشاء ورقة عمل منفصلة له
    if (exportData.summary && Object.keys(exportData.summary).length > 0) {
      const summaryData = Object.entries(exportData.summary).map(([key, value]) => [key, value]);
      const summaryWs = XLSX.utils.aoa_to_sheet([
        ['ملخص البيانات'],
        [],
        ...summaryData
      ]);
      XLSX.utils.book_append_sheet(wb, summaryWs, 'الملخص');
    }

    // تصدير المصنف إلى ملف Excel
    XLSX.writeFile(wb, `${fileName}.xlsx`);

    return true;
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    return false;
  }
};

// تصدير البيانات إلى ملف PDF
export const exportToPDF = async (exportData: ExportData, fileName: string = 'report') => {
  try {
    // إنشاء مستند PDF جديد
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    // تعيين اتجاه النص من اليمين إلى اليسار إذا كان مطلوبًا
    if (exportData.rtl) {
      doc.setR2L(true);
    }

    // إضافة الشعار إذا كان موجودًا
    if (exportData.logo) {
      try {
        doc.addImage(exportData.logo, 'PNG', 10, 10, 30, 30);
      } catch (e) {
        console.error('Error adding logo:', e);
      }
    }

    // إضافة العنوان
    doc.setFontSize(18);
    doc.text(exportData.title, doc.internal.pageSize.width / 2, 20, { align: 'center' });

    // إضافة العنوان الفرعي إذا كان موجودًا
    if (exportData.subtitle) {
      doc.setFontSize(14);
      doc.text(exportData.subtitle, doc.internal.pageSize.width / 2, 30, { align: 'center' });
    }

    // إضافة التاريخ
    doc.setFontSize(10);
    doc.text(`تاريخ التصدير: ${new Date().toLocaleDateString('fr-FR')}`, doc.internal.pageSize.width - 20, 40, { align: 'right' });

    // إضافة جدول البيانات
    // @ts-expect-error jspdf-autotable adds this method
    doc.autoTable({
      head: [exportData.headers],
      body: exportData.data,
      startY: 50,
      theme: 'grid',
      styles: {
        font: 'courier',
        fontSize: 10,
        textColor: [0, 0, 0],
        lineColor: [0, 0, 0],
        lineWidth: 0.1
      },
      headStyles: {
        fillColor: [22, 155, 136],
        textColor: [255, 255, 255],
        fontStyle: 'bold'
      },
      alternateRowStyles: {
        fillColor: [240, 240, 240]
      },
      margin: { top: 50 }
    });

    // إضافة ملخص البيانات إذا كان موجودًا
    if (exportData.summary && Object.keys(exportData.summary).length > 0) {
      const summaryData = Object.entries(exportData.summary).map(([key, value]) => [key, value]);

      doc.addPage();
      doc.setFontSize(16);
      doc.text('ملخص البيانات', doc.internal.pageSize.width / 2, 20, { align: 'center' });

      // @ts-expect-error jspdf-autotable adds this method
      doc.autoTable({
        body: summaryData,
        startY: 30,
        theme: 'grid',
        styles: {
          font: 'courier',
          fontSize: 10
        },
        columnStyles: {
          0: { fontStyle: 'bold' }
        }
      });
    }

    // إضافة الرسوم البيانية إذا كانت موجودة
    if (exportData.charts && exportData.charts.length > 0) {
      for (const chart of exportData.charts) {
        try {
          const chartElement = document.getElementById(chart.id) as HTMLCanvasElement;
          if (chartElement) {
            doc.addPage();
            doc.setFontSize(16);
            doc.text(chart.title, doc.internal.pageSize.width / 2, 20, { align: 'center' });

            const chartImage = chartElement.toDataURL('image/png', 1.0);
            doc.addImage(chartImage, 'PNG', 20, 30, 170, 100);
          }
        } catch (e) {
          console.error(`Error adding chart ${chart.id}:`, e);
        }
      }
    }

    // حفظ الملف
    doc.save(`${fileName}.pdf`);

    return true;
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return false;
  }
};

// تصدير الرسم البياني كصورة
export const exportChartAsImage = (chartId: string, fileName: string = 'chart') => {
  try {
    const canvas = document.getElementById(chartId) as HTMLCanvasElement;
    if (!canvas) {
      throw new Error(`Canvas with id ${chartId} not found`);
    }

    // إنشاء رابط تنزيل
    const link = document.createElement('a');
    link.download = `${fileName}.png`;
    link.href = canvas.toDataURL('image/png');
    link.click();

    return true;
  } catch (error) {
    console.error('Error exporting chart as image:', error);
    return false;
  }
};

// تحويل بيانات التحليل إلى تنسيق قابل للتصدير
interface AnalysisData {
  summary?: {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    passRate: number;
    averageGrade: number;
    highestGrade: number;
    lowestGrade: number;
  };
  gradeDistribution?: {
    excellent: number;
    veryGood: number;
    good: number;
    fair: number;
    poor: number;
    veryPoor: number;
  };
  classeAnalysis?: Array<{
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
  }>;
  teacherAnalysis?: Array<{
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
  }>;
  questionTypeAnalysis?: Array<{
    type: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    averagePoints: number;
  }>;
  difficultyAnalysis?: Array<{
    level: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    averagePoints: number;
  }>;
  genderAnalysis?: Record<string, {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
  }>;
}

export const convertAnalysisToExportData = (analysisData: AnalysisData, section: string): ExportData | null => {
  if (!analysisData) return null;

  switch (section) {
    case 'summary':
      if (!analysisData.summary || !analysisData.gradeDistribution) return null;

      return {
        title: 'ملخص نتائج الامتحانات',
        headers: ['المؤشر', 'القيمة'],
        data: [
          ['إجمالي الطلاب', analysisData.summary.totalStudents],
          ['الطلاب الناجحون', analysisData.summary.passedStudents],
          ['الطلاب الراسبون', analysisData.summary.failedStudents],
          ['الطلاب المتميزون', analysisData.summary.excellentStudents],
          ['نسبة النجاح', `${analysisData.summary.passRate.toFixed(1)}%`],
          ['متوسط الدرجات', analysisData.summary.averageGrade.toFixed(1)],
          ['أعلى درجة', analysisData.summary.highestGrade.toFixed(1)],
          ['أدنى درجة', analysisData.summary.lowestGrade.toFixed(1)]
        ],
        summary: {
          'توزيع الدرجات': `ممتاز: ${analysisData.gradeDistribution.excellent}, جيد جداً: ${analysisData.gradeDistribution.veryGood}, جيد: ${analysisData.gradeDistribution.good}, مقبول: ${analysisData.gradeDistribution.fair}, ضعيف: ${analysisData.gradeDistribution.poor}, ضعيف جداً: ${analysisData.gradeDistribution.veryPoor}`
        },
        charts: [
          { id: 'pie-chart', title: 'توزيع النتائج' },
          { id: 'bar-chart', title: 'حالة الطلاب' }
        ],
        rtl: true
      };

    case 'classes':
      if (!analysisData.classeAnalysis || analysisData.classeAnalysis.length === 0) return null;

      return {
        title: 'تحليل أداء الفصول',
        headers: ['الفصل', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'],
        data: analysisData.classeAnalysis.map((classe) => [
          classe.name,
          classe.totalStudents,
          classe.averageGrade.toFixed(1),
          classe.totalStudents > 0 ? `${((classe.passedStudents / classe.totalStudents) * 100).toFixed(1)}%` : '0%',
          classe.excellentStudents,
          classe.failedStudents
        ]),
        charts: [
          { id: 'bar-chart', title: 'متوسط الدرجات حسب الفصل' }
        ],
        rtl: true
      };

    case 'teachers':
      if (!analysisData.teacherAnalysis || analysisData.teacherAnalysis.length === 0) return null;

      return {
        title: 'تحليل أداء المعلمين',
        headers: ['المعلم', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'],
        data: analysisData.teacherAnalysis.map((teacher) => [
          teacher.name,
          teacher.totalStudents,
          teacher.averageGrade.toFixed(1),
          teacher.totalStudents > 0 ? `${((teacher.passedStudents / teacher.totalStudents) * 100).toFixed(1)}%` : '0%',
          teacher.excellentStudents,
          teacher.failedStudents
        ]),
        charts: [
          { id: 'bar-chart', title: 'متوسط الدرجات حسب المعلم' }
        ],
        rtl: true
      };

    case 'questions':
      if (!analysisData.questionTypeAnalysis || analysisData.questionTypeAnalysis.length === 0) return null;

      return {
        title: 'تحليل أداء الطلاب حسب نوع السؤال',
        headers: ['نوع السؤال', 'عدد الإجابات', 'الإجابات الصحيحة', 'الإجابات الخاطئة', 'نسبة الإجابات الصحيحة', 'متوسط النقاط'],
        data: analysisData.questionTypeAnalysis.map((type) => {
          const questionTypeLabels: Record<string, string> = {
            MULTIPLE_CHOICE: 'اختيار من متعدد',
            TRUE_FALSE: 'صح أو خطأ',
            SHORT_ANSWER: 'إجابة قصيرة',
            ESSAY: 'مقال',
            MATCHING: 'مطابقة',
            FILL_BLANK: 'ملء الفراغات',
            ORDERING: 'ترتيب'
          };

          return [
            questionTypeLabels[type.type] || type.type,
            type.totalAnswers,
            type.correctAnswers,
            type.incorrectAnswers,
            (type.correctAnswers + type.incorrectAnswers) > 0
              ? `${((type.correctAnswers / (type.correctAnswers + type.incorrectAnswers)) * 100).toFixed(1)}%`
              : '0%',
            type.averagePoints.toFixed(2)
          ];
        }),
        charts: [
          { id: 'bar-chart', title: 'نسبة الإجابات الصحيحة حسب نوع السؤال' }
        ],
        rtl: true
      };

    case 'difficulty':
      if (!analysisData.difficultyAnalysis || analysisData.difficultyAnalysis.length === 0) return null;

      return {
        title: 'تحليل أداء الطلاب حسب مستوى الصعوبة',
        headers: ['مستوى الصعوبة', 'عدد الإجابات', 'الإجابات الصحيحة', 'الإجابات الخاطئة', 'نسبة الإجابات الصحيحة', 'متوسط النقاط'],
        data: analysisData.difficultyAnalysis.map((level) => {
          const difficultyLevelLabels: Record<string, string> = {
            EASY: 'سهل',
            MEDIUM: 'متوسط',
            HARD: 'صعب',
            VERY_HARD: 'صعب جداً'
          };

          return [
            difficultyLevelLabels[level.level] || level.level,
            level.totalAnswers,
            level.correctAnswers,
            level.incorrectAnswers,
            (level.correctAnswers + level.incorrectAnswers) > 0
              ? `${((level.correctAnswers / (level.correctAnswers + level.incorrectAnswers)) * 100).toFixed(1)}%`
              : '0%',
            level.averagePoints.toFixed(2)
          ];
        }),
        charts: [
          { id: 'line-chart', title: 'العلاقة بين مستوى الصعوبة ونسبة الإجابات الصحيحة' }
        ],
        rtl: true
      };

    case 'gender':
      if (!analysisData.genderAnalysis || Object.keys(analysisData.genderAnalysis).length === 0) return null;

      return {
        title: 'تحليل النتائج حسب الجنس',
        headers: ['الجنس', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'],
        data: Object.entries(analysisData.genderAnalysis).map(([gender, data]) => {
          const genderLabel = gender === 'MALE' ? 'ذكور' : 'إناث';

          return [
            genderLabel,
            data.totalStudents,
            data.averageGrade.toFixed(1),
            data.totalStudents > 0 ? `${((data.passedStudents / data.totalStudents) * 100).toFixed(1)}%` : '0%',
            data.excellentStudents,
            data.failedStudents
          ];
        }),
        charts: [
          { id: 'bar-chart', title: 'مقارنة متوسط الدرجات حسب الجنس' }
        ],
        rtl: true
      };

    default:
      return null;
  }
};

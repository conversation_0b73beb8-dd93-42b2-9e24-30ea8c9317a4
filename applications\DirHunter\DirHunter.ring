/*
==============================================================================
    DirHunter - أداة تخمين المجلدات والملفات
    
    الوصف: أداة سريعة وقوية لتخمين أسماء المجلدات والملفات على خوادم الويب
    المؤلف: Praetorian Team
    المكتبات: rogueutil.ring, Praetorian.ring
==============================================================================
*/

# تحميل المكتبات المطلوبة
load "../../praetorian.ring"

# محاولة تحميل rogueutil
try
    load "rogueutil.ring"
    bRogueUtilAvailable = true
catch
    ? "تحذير: لم يتم العثور على rogueutil.ring - سيتم استخدام الألوان الافتراضية"
    bRogueUtilAvailable = false
done

/*
==============================================================================
    ثوابت التطبيق
==============================================================================
*/

# ألوان النص (إذا كان rogueutil متاحاً)
COLOR_RED = 12
COLOR_GREEN = 10
COLOR_YELLOW = 14
COLOR_CYAN = 11
COLOR_WHITE = 15
COLOR_GRAY = 8

# إعدادات افتراضية
DEFAULT_THREADS = 10
DEFAULT_EXTENSIONS = ["php", "html", "htm", "txt", "asp", "aspx", "jsp"]

/*
==============================================================================
    متغيرات التطبيق العامة
==============================================================================
*/

# معلمات سطر الأوامر
cTargetURL = ""
cWordlistPath = ""
nThreads = DEFAULT_THREADS
aExtensions = []
bShowHelp = false
bVerbose = false

# إحصائيات الفحص
nTotalRequests = 0
nFoundPaths = 0
nCurrentProgress = 0
aFoundResults = []

# مثيل Praetorian
oPraetorian = NULL

/*
==============================================================================
    دوال الألوان والعرض
==============================================================================
*/

/*
طباعة نص ملون
*/
func printColored cText, nColor
    if bRogueUtilAvailable
        setColor(nColor)
        ? cText
        setColor(COLOR_WHITE)
    else
        ? cText
    ok

/*
طباعة رسالة نجاح
*/
func printSuccess cMessage
    printColored("[+] " + cMessage, COLOR_GREEN)

/*
طباعة رسالة خطأ
*/
func printError cMessage
    printColored("[-] " + cMessage, COLOR_RED)

/*
طباعة رسالة معلومات
*/
func printInfo cMessage
    printColored("[*] " + cMessage, COLOR_CYAN)

/*
طباعة رسالة تحذير
*/
func printWarning cMessage
    printColored("[!] " + cMessage, COLOR_YELLOW)

/*
طباعة شريط التقدم
*/
func printProgressBar nCurrent, nTotal
    if nTotal = 0 return ok
    
    nPercent = (nCurrent * 100) / nTotal
    nBarLength = 50
    nFilled = (nPercent * nBarLength) / 100
    
    cBar = "["
    for i = 1 to nBarLength
        if i <= nFilled
            cBar += "="
        else
            cBar += " "
        ok
    next
    cBar += "] " + nPercent + "% (" + nCurrent + "/" + nTotal + ")"
    
    # طباعة الشريط مع العودة للبداية
    write(cBar + char(13))

/*
==============================================================================
    دوال تحليل معلمات سطر الأوامر
==============================================================================
*/

/*
عرض رسالة المساعدة
*/
func showHelp
    ? ""
    printColored("DirHunter - أداة تخمين المجلدات والملفات", COLOR_CYAN)
    ? "=============================================="
    ? ""
    ? "الاستخدام:"
    ? "  ring DirHunter.ring [OPTIONS]"
    ? ""
    ? "المعلمات المطلوبة:"
    ? "  -u, --url <URL>        عنوان URL الأساسي للهدف"
    ? "  -w, --wordlist <PATH>  المسار إلى ملف قائمة الكلمات"
    ? ""
    ? "المعلمات الاختيارية:"
    ? "  -t, --threads <NUM>    عدد الخيوط المتزامنة (افتراضي: 10)"
    ? "  -x, --extensions <EXT> قائمة امتدادات مفصولة بفواصل"
    ? "  -v, --verbose          عرض تفاصيل إضافية"
    ? "  -h, --help             عرض هذه الرسالة"
    ? ""
    ? "أمثلة:"
    ? "  ring DirHunter.ring -u http://example.com -w wordlist.txt"
    ? "  ring DirHunter.ring -u https://target.com -w dirs.txt -t 20 -x php,html"
    ? ""

/*
تحليل معلمات سطر الأوامر
*/
func parseArguments
    aArgs = sysargv
    nArgCount = len(aArgs)
    
    if nArgCount < 2
        bShowHelp = true
        return
    ok
    
    i = 2  # تجاهل اسم البرنامج
    while i <= nArgCount
        cArg = aArgs[i]
        
        switch cArg
            on "-u" on "--url"
                if i + 1 <= nArgCount
                    i++
                    cTargetURL = aArgs[i]
                else
                    printError("معلمة -u تتطلب قيمة")
                    bShowHelp = true
                    return
                ok
                
            on "-w" on "--wordlist"
                if i + 1 <= nArgCount
                    i++
                    cWordlistPath = aArgs[i]
                else
                    printError("معلمة -w تتطلب قيمة")
                    bShowHelp = true
                    return
                ok
                
            on "-t" on "--threads"
                if i + 1 <= nArgCount
                    i++
                    nThreads = number(aArgs[i])
                    if nThreads <= 0
                        printError("عدد الخيوط يجب أن يكون أكبر من صفر")
                        bShowHelp = true
                        return
                    ok
                else
                    printError("معلمة -t تتطلب قيمة")
                    bShowHelp = true
                    return
                ok
                
            on "-x" on "--extensions"
                if i + 1 <= nArgCount
                    i++
                    cExtList = aArgs[i]
                    aExtensions = split(cExtList, ",")
                    # إضافة نقطة إذا لم تكن موجودة
                    for j = 1 to len(aExtensions)
                        if left(aExtensions[j], 1) != "."
                            aExtensions[j] = "." + aExtensions[j]
                        ok
                    next
                else
                    printError("معلمة -x تتطلب قيمة")
                    bShowHelp = true
                    return
                ok
                
            on "-v" on "--verbose"
                bVerbose = true
                
            on "-h" on "--help"
                bShowHelp = true
                return
                
            other
                printError("معلمة غير معروفة: " + cArg)
                bShowHelp = true
                return
        off
        
        i++
    end
    
    # التحقق من المعلمات المطلوبة
    if len(cTargetURL) = 0
        printError("معلمة URL مطلوبة (-u)")
        bShowHelp = true
    ok
    
    if len(cWordlistPath) = 0
        printError("معلمة قائمة الكلمات مطلوبة (-w)")
        bShowHelp = true
    ok
    
    # تعيين امتدادات افتراضية إذا لم تحدد
    if len(aExtensions) = 0
        aExtensions = DEFAULT_EXTENSIONS
        for i = 1 to len(aExtensions)
            aExtensions[i] = "." + aExtensions[i]
        next
    ok

/*
==============================================================================
    دوال الفحص الأساسية
==============================================================================
*/

/*
تحميل قائمة الكلمات من ملف
*/
func loadWordlist cFilePath
    if not fexists(cFilePath)
        printError("ملف قائمة الكلمات غير موجود: " + cFilePath)
        return []
    ok
    
    try
        cContent = read(cFilePath)
        aLines = split(cContent, nl)
        aWordlist = []
        
        for cLine in aLines
            cWord = trim(cLine)
            if len(cWord) > 0 and left(cWord, 1) != "#"  # تجاهل التعليقات والأسطر الفارغة
                add(aWordlist, cWord)
            ok
        next
        
        printInfo("تم تحميل " + len(aWordlist) + " كلمة من " + cFilePath)
        return aWordlist
        
    catch
        printError("خطأ في قراءة ملف قائمة الكلمات: " + cCatchError)
        return []
    done

/*
فحص مسار واحد
*/
func testSinglePath cURL
    try
        oResponse = oPraetorian.Web.HTTPClient.get(cURL, NULL)
        
        aResult = [
            :url = cURL,
            :status_code = oResponse[:status_code],
            :content_length = oResponse[:content_length],
            :found = (oResponse[:status_code] != 404 and oResponse[:status_code] != 0)
        ]
        
        return aResult
        
    catch
        return [
            :url = cURL,
            :status_code = 0,
            :content_length = 0,
            :found = false,
            :error = cCatchError
        ]
    done

/*
معالج النتائج
*/
func onPathFound aResult
    if aResult[:found]
        nFoundPaths++
        add(aFoundResults, aResult)
        
        cStatusText = ""
        switch aResult[:status_code]
            on 200
                cStatusText = "OK"
            on 301
                cStatusText = "Moved Permanently"
            on 302
                cStatusText = "Found"
            on 403
                cStatusText = "Forbidden"
            on 401
                cStatusText = "Unauthorized"
            on 500
                cStatusText = "Internal Server Error"
            other
                cStatusText = "Status " + aResult[:status_code]
        off
        
        cMessage = "Found: " + aResult[:url] + " (" + cStatusText
        if aResult[:content_length] > 0
            cMessage += ", " + aResult[:content_length] + " bytes"
        ok
        cMessage += ")"
        
        printSuccess(cMessage)
    ok

/*
تشغيل الفحص الرئيسي
*/
func runDirectoryHunt
    # تحميل قائمة الكلمات
    aWordlist = loadWordlist(cWordlistPath)
    if len(aWordlist) = 0
        return
    ok
    
    # إنشاء قائمة المسارات للفحص
    aPathsToTest = []
    
    # إضافة المجلدات
    for cWord in aWordlist
        cTestURL = cTargetURL
        if right(cTestURL, 1) != "/"
            cTestURL += "/"
        ok
        cTestURL += cWord + "/"
        add(aPathsToTest, cTestURL)
    next
    
    # إضافة الملفات مع الامتدادات
    for cWord in aWordlist
        for cExt in aExtensions
            cTestURL = cTargetURL
            if right(cTestURL, 1) != "/"
                cTestURL += "/"
            ok
            cTestURL += cWord + cExt
            add(aPathsToTest, cTestURL)
        next
        
        # إضافة الملف بدون امتداد
        cTestURL = cTargetURL
        if right(cTestURL, 1) != "/"
            cTestURL += "/"
        ok
        cTestURL += cWord
        add(aPathsToTest, cTestURL)
    next
    
    nTotalRequests = len(aPathsToTest)
    
    printInfo("بدء فحص " + nTotalRequests + " مسار...")
    printInfo("الهدف: " + cTargetURL)
    printInfo("الخيوط: " + nThreads)
    ? ""
    
    # تشغيل الفحص (مبسط - بدون تعدد حقيقي)
    nCurrentProgress = 0
    for cPath in aPathsToTest
        nCurrentProgress++
        
        # عرض شريط التقدم كل 10 طلبات
        if nCurrentProgress % 10 = 0 or nCurrentProgress = nTotalRequests
            printProgressBar(nCurrentProgress, nTotalRequests)
        ok
        
        # فحص المسار
        aResult = testSinglePath(cPath)
        onPathFound(aResult)
        
        # تأخير بسيط لتجنب إرهاق الخادم
        sleep(100)
    next
    
    # طباعة سطر جديد بعد شريط التقدم
    ? ""

/*
طباعة ملخص النتائج
*/
func printSummary
    ? ""
    printInfo("انتهى الفحص!")
    printInfo("إجمالي الطلبات: " + nTotalRequests)
    printInfo("المسارات الموجودة: " + nFoundPaths)
    
    if len(aFoundResults) > 0
        ? ""
        printInfo("ملخص النتائج:")
        for aResult in aFoundResults
            cLine = "  " + aResult[:url] + " [" + aResult[:status_code] + "]"
            if aResult[:content_length] > 0
                cLine += " (" + aResult[:content_length] + " bytes)"
            ok
            ? cLine
        next
    ok

/*
==============================================================================
    الدالة الرئيسية
==============================================================================
*/

func main
    # طباعة البانر
    ? ""
    printColored("DirHunter v1.0 - أداة تخمين المجلدات والملفات", COLOR_CYAN)
    printColored("جزء من مجموعة أدوات Praetorian.ring", COLOR_GRAY)
    ? "=============================================="
    
    # تحليل معلمات سطر الأوامر
    parseArguments()
    
    if bShowHelp
        showHelp()
        return
    ok
    
    # تهيئة مكتبة Praetorian
    oPraetorian = CreatePraetorian()
    
    # تعيين إعدادات عميل HTTP
    oPraetorian.Web.HTTPClient.setTimeout(5)
    oPraetorian.Web.HTTPClient.setVerbose(false)
    
    # عرض معلومات الفحص
    ? ""
    printInfo("إعدادات الفحص:")
    printInfo("الهدف: " + cTargetURL)
    printInfo("قائمة الكلمات: " + cWordlistPath)
    printInfo("عدد الخيوط: " + nThreads)
    printInfo("الامتدادات: " + join(aExtensions, ", "))
    
    # بدء الفحص
    runDirectoryHunt()
    
    # طباعة الملخص
    printSummary()

/*
دالة مساعدة لربط القوائم
*/
func join aList, cSeparator
    if len(aList) = 0
        return ""
    ok
    
    cResult = aList[1]
    for i = 2 to len(aList)
        cResult += cSeparator + aList[i]
    next
    
    return cResult



# دوال مساعدة لنظام المدفوعات والفواتير

## 📋 الوصف
ملف يحتوي على جميع الدوال المساعدة والمشتركة لنظام المدفوعات والفواتير، يهدف إلى:
- توحيد منطق العمل
- تحسين جودة الكود
- تسهيل الصيانة والتطوير
- ضمان دقة الحسابات

## 🎯 الهدف
- **توحيد التحقق من البيانات**: دوال موحدة للتحقق من صحة المدخلات
- **حسابات مالية دقيقة**: دوال محسنة لحساب المبالغ والحالات
- **تحسين الأداء**: تقليل تكرار الكود وتحسين الاستعلامات
- **سهولة الصيانة**: كود منظم وقابل للإعادة الاستخدام

## 🔧 الدوال المتاحة

### 1. دوال التحقق من البيانات

#### `validatePaymentData(data: PaymentValidationData): ValidationError[]`
**الغرض:** التحقق من صحة بيانات الدفعة
**المدخلات:**
- `studentId`: معرف التلميذ
- `amount`: مبلغ الدفعة
- `paymentMethod`: طريقة الدفع
- `notes`: ملاحظات (اختياري)
- `receiptNumber`: رقم الإيصال (اختياري)
- `month`: الشهر (اختياري)

**المخرجات:** مصفوفة من أخطاء التحقق

#### `validateInvoiceData(data: InvoiceValidationData): ValidationError[]`
**الغرض:** التحقق من صحة بيانات الفاتورة
**المدخلات:**
- `studentId/parentId`: معرف التلميذ أو الولي
- `amount`: مبلغ الفاتورة
- `dueDate`: تاريخ الاستحقاق
- `type`: نوع الفاتورة (INDIVIDUAL/FAMILY)

**المخرجات:** مصفوفة من أخطاء التحقق

### 2. دوال الحسابات المالية

#### `calculateInvoicePaidAmount(invoiceId: number): Promise<number>`
**الغرض:** حساب المبلغ المدفوع لفاتورة معينة
**المدخلات:** معرف الفاتورة
**المخرجات:** المبلغ المدفوع الإجمالي

#### `updateInvoiceStatus(invoiceId: number, tx?: any): Promise<InvoiceStatus>`
**الغرض:** تحديث حالة الفاتورة بناءً على المدفوعات
**المدخلات:** 
- `invoiceId`: معرف الفاتورة
- `tx`: transaction اختياري
**المخرجات:** الحالة الجديدة للفاتورة

#### `calculatePaymentRate(totalRequired: number, totalPaid: number): number`
**الغرض:** حساب معدل السداد بالنسبة المئوية
**المدخلات:** المبلغ المطلوب والمبلغ المدفوع
**المخرجات:** معدل السداد (0-100)

### 3. دوال تنظيف البيانات

#### `sanitizeInput(input: any): any`
**الغرض:** تنظيف وتطهير المدخلات من الأحرف الضارة
**المدخلات:** أي نوع من البيانات
**المخرجات:** البيانات المنظفة

#### `validateAmount(amount: any): boolean`
**الغرض:** التحقق من صحة المبلغ
**المدخلات:** المبلغ
**المخرجات:** true إذا كان صحيحاً

#### `validateDate(date: any): boolean`
**الغرض:** التحقق من صحة التاريخ
**المدخلات:** التاريخ
**المخرجات:** true إذا كان صحيحاً

### 4. دوال التنسيق والعرض

#### `formatAmount(amount: number): string`
**الغرض:** تنسيق المبلغ للعرض بالدينار الجزائري
**المدخلات:** المبلغ
**المخرجات:** المبلغ منسق (مثال: "1,500.00 دج")

#### `formatDate(date: Date | string): string`
**الغرض:** تنسيق التاريخ للعرض بالصيغة العربية
**المدخلات:** التاريخ
**المخرجات:** التاريخ منسق (مثال: "24/06/2025")

### 5. دوال منطق العمل

#### `determinePaymentStatus(totalRequired, totalPaid, hasOverdueInvoices): PaymentStatus`
**الغرض:** تحديد حالة الدفع بناءً على المبالغ
**المدخلات:** 
- `totalRequired`: المبلغ المطلوب
- `totalPaid`: المبلغ المدفوع
- `hasOverdueInvoices`: وجود فواتير متأخرة
**المخرجات:** حالة الدفع (PAID/PARTIAL/UNPAID/OVERDUE)

#### `generateReceiptNumber(): string`
**الغرض:** إنشاء رقم إيصال فريد
**المخرجات:** رقم إيصال بصيغة "REC-YYYYMMDD-XXXXXX"

### 6. دوال التحقق من الوجود

#### `validateStudentExists(studentId: number): Promise<boolean>`
**الغرض:** التحقق من وجود التلميذ في قاعدة البيانات
**المدخلات:** معرف التلميذ
**المخرجات:** true إذا كان موجوداً

#### `validateParentExists(parentId: number): Promise<boolean>`
**الغرض:** التحقق من وجود الولي في قاعدة البيانات
**المدخلات:** معرف الولي
**المخرجات:** true إذا كان موجوداً

#### `getOrCreatePaymentMethod(name: string): Promise<PaymentMethod>`
**الغرض:** جلب طريقة دفع موجودة أو إنشاء جديدة
**المدخلات:** اسم طريقة الدفع
**المخرجات:** كائن طريقة الدفع

## 📊 أمثلة الاستخدام

### مثال 1: التحقق من بيانات الدفعة
```typescript
import { validatePaymentData } from '@/utils/payment-utils';

const paymentData = {
  studentId: 123,
  amount: 5000,
  paymentMethod: 'نقداً',
  notes: 'دفعة شهرية',
  month: '2025-06'
};

const errors = validatePaymentData(paymentData);
if (errors.length > 0) {
  console.log('أخطاء في البيانات:', errors);
} else {
  console.log('البيانات صحيحة');
}
```

### مثال 2: حساب المبلغ المدفوع للفاتورة
```typescript
import { calculateInvoicePaidAmount } from '@/utils/payment-utils';

const invoiceId = 456;
const paidAmount = await calculateInvoicePaidAmount(invoiceId);
console.log(`المبلغ المدفوع: ${paidAmount} دج`);
```

### مثال 3: تحديث حالة الفاتورة
```typescript
import { updateInvoiceStatus } from '@/utils/payment-utils';

const invoiceId = 456;
const newStatus = await updateInvoiceStatus(invoiceId);
console.log(`الحالة الجديدة: ${newStatus}`);
```

### مثال 4: تنسيق المبلغ للعرض
```typescript
import { formatAmount } from '@/utils/payment-utils';

const amount = 15000.50;
const formattedAmount = formatAmount(amount);
console.log(formattedAmount); // "15,000.50 دج"
```

## 🔒 اعتبارات الأمان

### تنظيف المدخلات
جميع المدخلات يتم تنظيفها تلقائياً من:
- الأحرف الضارة (`<>`)
- المسافات الزائدة
- القيم السالبة للمبالغ

### التحقق من الصحة
- التحقق من نوع البيانات
- التحقق من النطاقات المسموحة
- التحقق من وجود البيانات المطلوبة

### معالجة الأخطاء
- جميع الدوال تحتوي على معالجة شاملة للأخطاء
- تسجيل الأخطاء في console للمراجعة
- إرجاع قيم افتراضية آمنة عند الفشل

## 🎯 فوائد الاستخدام

### 1. دقة أعلى
- حسابات موحدة ومختبرة
- منطق عمل ثابت
- تقليل الأخطاء البشرية

### 2. أداء محسن
- تقليل تكرار الكود
- استعلامات محسنة
- معالجة فعالة للبيانات

### 3. صيانة أسهل
- كود منظم ومركزي
- تحديثات في مكان واحد
- اختبار أسهل

### 4. أمان أفضل
- تنظيف شامل للمدخلات
- تحقق دقيق من البيانات
- معالجة آمنة للأخطاء

---

**تاريخ الإنشاء:** 2025-06-24  
**المطور:** Augment Agent  
**الإصدار:** 1.0  
**الحالة:** جاهز للاستخدام

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

// GET /api/teacher-students - جلب الطلاب الخاصين بالمعلم المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'TEACHER') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userId = userData.id;

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userId
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // جلب الفصول التي يدرسها المعلم
    const teacherClasses = await prisma.classSubject.findMany({
      where: {
        teacherSubject: {
          teacherId: teacher.id
        }
      },
      select: {
        classeId: true
      }
    });

    const classIds = teacherClasses.map(tc => tc.classeId);

    // جلب الطلاب في هذه الفصول
    const students = await prisma.student.findMany({
      where: {
        classeId: {
          in: classIds
        }
      },
      include: {
        classe: {
          select: {
            id: true,
            name: true
          }
        },
        guardian: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json({
      students,
      message: "تم جلب بيانات الطلاب بنجاح"
    });
  } catch (error) {
    console.error('Error fetching teacher students:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب بيانات الطلاب" },
      { status: 500 }
    );
  }
}

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { toast } from '@/components/ui/use-toast'
import { FaChartBar, FaDownload } from 'react-icons/fa'
import { exportToExcel } from '@/utils/export-utils'

interface Student {
  id: number
  name: string
  attendance?: {
    present: number
    absent: number
    excused: number
    total: number
  }
  examResults?: {
    passed: number
    failed: number
    total: number
    avgScore: number
  }
}

interface Subject {
  id: number
  name: string
  avgScore: number
  passRate: number
}

// استيراد واجهة Class من ملف page.tsx
import { Class } from './page'

interface ClassReportModalProps {
  isOpen: boolean
  onCloseAction: () => void
  classItem: Class | null
}

export function ClassReportModal({
  isOpen,
  onCloseAction,
  classItem
}: ClassReportModalProps) {
  const [reportData, setReportData] = useState<{
    attendance: {
      presentRate: number
      absentRate: number
      excusedRate: number
    }
    performance: {
      avgScore: number
      passRate: number
    }
    subjects: Subject[]
    topStudents: Student[]
    needsImprovement: Student[]
  }>({
    attendance: {
      presentRate: 0,
      absentRate: 0,
      excusedRate: 0
    },
    performance: {
      avgScore: 0,
      passRate: 0
    },
    subjects: [],
    topStudents: [],
    needsImprovement: []
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen && classItem) {
      fetchReportData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, classItem])

  const fetchReportData = async () => {
    if (!classItem) return

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch(`/api/admin/class-reports/${classItem.id}`)
      if (!response.ok) throw new Error('Failed to fetch report data')

      const data = await response.json()
      setReportData(data)
    } catch (err) {
      console.error('Error fetching report data:', err)
      setError('فشل في جلب بيانات التقرير')

      // استخدام بيانات وهمية للعرض
      generateMockData()
    } finally {
      setIsLoading(false)
    }
  }

  const generateMockData = () => {
    if (!classItem || !classItem.students) return

    // توليد بيانات وهمية للعرض
    const students = classItem.students as Student[]
    const mockSubjects = [
      { id: 1, name: 'القرآن الكريم', avgScore: 85, passRate: 92 },
      { id: 2, name: 'التجويد', avgScore: 78, passRate: 85 },
      { id: 3, name: 'الفقه', avgScore: 82, passRate: 90 }
    ]

    // توليد بيانات وهمية للطلاب
    const studentsWithData = students.map(student => ({
      ...student,
      attendance: {
        present: Math.floor(Math.random() * 20) + 10,
        absent: Math.floor(Math.random() * 5),
        excused: Math.floor(Math.random() * 3),
        total: 30
      },
      examResults: {
        passed: Math.floor(Math.random() * 5) + 3,
        failed: Math.floor(Math.random() * 2),
        total: 8,
        avgScore: Math.floor(Math.random() * 30) + 70
      }
    }))

    // ترتيب الطلاب حسب المتوسط
    const sortedStudents = [...studentsWithData].sort((a, b) =>
      (b.examResults?.avgScore || 0) - (a.examResults?.avgScore || 0)
    )

    const topStudents = sortedStudents.slice(0, 5)
    const needsImprovement = [...studentsWithData]
      .sort((a, b) => (a.examResults?.avgScore || 0) - (b.examResults?.avgScore || 0))
      .slice(0, 5)

    // حساب متوسط الحضور والأداء
    const totalAttendance = studentsWithData.reduce(
      (acc, student) => {
        if (student.attendance) {
          acc.present += student.attendance.present
          acc.absent += student.attendance.absent
          acc.excused += student.attendance.excused
          acc.total += student.attendance.total
        }
        return acc
      },
      { present: 0, absent: 0, excused: 0, total: 0 }
    )

    const totalPerformance = studentsWithData.reduce(
      (acc, student) => {
        if (student.examResults) {
          acc.totalScore += student.examResults.avgScore
          acc.passed += student.examResults.passed
          acc.total += student.examResults.total
        }
        return acc
      },
      { totalScore: 0, passed: 0, total: 0 }
    )

    setReportData({
      attendance: {
        presentRate: totalAttendance.total ? (totalAttendance.present / totalAttendance.total) * 100 : 0,
        absentRate: totalAttendance.total ? (totalAttendance.absent / totalAttendance.total) * 100 : 0,
        excusedRate: totalAttendance.total ? (totalAttendance.excused / totalAttendance.total) * 100 : 0
      },
      performance: {
        avgScore: studentsWithData.length ? totalPerformance.totalScore / studentsWithData.length : 0,
        passRate: totalPerformance.total ? (totalPerformance.passed / totalPerformance.total) * 100 : 0
      },
      subjects: mockSubjects,
      topStudents,
      needsImprovement
    })
  }

  const handleExportToExcel = () => {
    if (!classItem) return

    try {
      // تجهيز البيانات للتصدير كمصفوفة ثنائية الأبعاد
      const rawData = [
        // معلومات عامة
        ['تقرير أداء الفصل', classItem.name],
        ['عدد الطلاب', classItem.students.length.toString()],
        [''],

        // بيانات الحضور
        ['معدل الحضور', `${reportData.attendance.presentRate.toFixed(1)}%`],
        ['معدل الغياب', `${reportData.attendance.absentRate.toFixed(1)}%`],
        ['معدل الغياب بعذر', `${reportData.attendance.excusedRate.toFixed(1)}%`],
        [''],

        // بيانات الأداء
        ['متوسط الدرجات', reportData.performance.avgScore.toFixed(1)],
        ['نسبة النجاح', `${reportData.performance.passRate.toFixed(1)}%`],
        [''],

        // بيانات المواد
        ['المادة', 'متوسط الدرجات', 'نسبة النجاح'],
        ...reportData.subjects.map(subject => [
          subject.name,
          subject.avgScore.toFixed(1),
          `${subject.passRate.toFixed(1)}%`
        ]),
        [''],

        // الطلاب المتفوقين
        ['الطلاب المتفوقين', '', ''],
        ['اسم الطالب', 'متوسط الدرجات', 'نسبة الحضور'],
        ...reportData.topStudents.map(student => [
          student.name,
          student.examResults?.avgScore.toFixed(1) || '-',
          student.attendance ?
            `${((student.attendance.present / student.attendance.total) * 100).toFixed(1)}%` :
            '-'
        ]),
        [''],

        // الطلاب الذين يحتاجون إلى تحسين
        ['الطلاب الذين يحتاجون إلى تحسين', '', ''],
        ['اسم الطالب', 'متوسط الدرجات', 'نسبة الحضور'],
        ...reportData.needsImprovement.map(student => [
          student.name,
          student.examResults?.avgScore.toFixed(1) || '-',
          student.attendance ?
            `${((student.attendance.present / student.attendance.total) * 100).toFixed(1)}%` :
            '-'
        ])
      ]

      // تحويل البيانات إلى تنسيق كائنات كما هو مطلوب في وظيفة exportToExcel
      const data = rawData.map(row => {
        // إنشاء كائن لكل صف حيث المفاتيح هي أرقام الأعمدة
        const obj: Record<string, unknown> = {};
        row.forEach((cell, index) => {
          obj[`col${index}`] = cell;
        });
        return obj;
      });

      exportToExcel(data, `تقرير_فصل_${classItem.name}.xlsx`, 'تقرير الفصل')

      toast({
        title: 'نجاح',
        description: 'تم تصدير التقرير بنجاح'
      })
    } catch (err) {
      console.error('Error exporting report:', err)
      toast({
        title: 'خطأ',
        description: 'فشل في تصدير التقرير',
        variant: 'destructive'
      })
    }
  }

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title={
        <div className="flex items-center gap-2 text-[var(--primary-color)]">
          <FaChartBar className="text-[var(--primary-color)]" />
          <span>تقرير أداء الفصل - {classItem?.name}</span>
        </div>
      }
      variant="primary"
      className="rtl"
    >
      {error && <div className="text-red-500 text-center p-2 bg-red-50 rounded-md mb-4 border border-red-100">{error}</div>}

      <div className="flex justify-end mb-4">
        <Button
          onClick={handleExportToExcel}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
          size="sm"
        >
          <FaDownload className="ml-1" />
          تصدير التقرير
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : (
        <div className="grid gap-6 py-4 max-h-[70vh] overflow-y-auto">
          {/* ملخص الأداء */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-[#e0f2ef] rounded-md p-4 bg-[#f8fffd]">
              <h3 className="font-bold text-[var(--primary-color)] mb-4 text-center">ملخص الحضور</h3>
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between mb-1">
                    <span>معدل الحضور</span>
                    <span className="font-bold text-[var(--primary-color)]">{reportData.attendance.presentRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full bg-[var(--primary-color)]"
                      style={{ width: `${reportData.attendance.presentRate}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span>معدل الغياب</span>
                    <span className="font-bold text-red-500">{reportData.attendance.absentRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full bg-red-500"
                      style={{ width: `${reportData.attendance.absentRate}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span>معدل الغياب بعذر</span>
                    <span className="font-bold text-yellow-500">{reportData.attendance.excusedRate.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full bg-yellow-500"
                      style={{ width: `${reportData.attendance.excusedRate}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            <div className="border border-[#e0f2ef] rounded-md p-4 bg-[#f8fffd]">
              <h3 className="font-bold text-[var(--primary-color)] mb-4 text-center">ملخص الأداء</h3>
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between mb-1">
                    <span>متوسط الدرجات</span>
                    <span className={`font-bold ${
                      reportData.performance.avgScore >= 80 ? 'text-[var(--primary-color)]' :
                      reportData.performance.avgScore >= 60 ? 'text-yellow-500' :
                      'text-red-500'
                    }`}>
                      {reportData.performance.avgScore.toFixed(1)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full ${
                        reportData.performance.avgScore >= 80 ? 'bg-[var(--primary-color)]' :
                        reportData.performance.avgScore >= 60 ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${reportData.performance.avgScore}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span>نسبة النجاح</span>
                    <span className={`font-bold ${
                      reportData.performance.passRate >= 80 ? 'text-[var(--primary-color)]' :
                      reportData.performance.passRate >= 60 ? 'text-yellow-500' :
                      'text-red-500'
                    }`}>
                      {reportData.performance.passRate.toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full ${
                        reportData.performance.passRate >= 80 ? 'bg-[var(--primary-color)]' :
                        reportData.performance.passRate >= 60 ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${reportData.performance.passRate}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* أداء المواد */}
          <div className="border border-[#e0f2ef] rounded-md p-4">
            <h3 className="font-bold text-[var(--primary-color)] mb-4">أداء المواد الدراسية</h3>
            <div className="space-y-4">
              {reportData.subjects.map(subject => (
                <div key={subject.id} className="border-b border-[#e0f2ef] pb-3 last:border-0">
                  <div className="flex justify-between mb-1">
                    <span className="font-medium">{subject.name}</span>
                    <span className="text-sm text-gray-500">
                      متوسط الدرجات: <span className="font-bold">{subject.avgScore.toFixed(1)}</span> |
                      نسبة النجاح: <span className="font-bold">{subject.passRate.toFixed(1)}%</span>
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full ${
                        subject.avgScore >= 80 ? 'bg-[var(--primary-color)]' :
                        subject.avgScore >= 60 ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${subject.avgScore}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* الطلاب المتفوقين والذين يحتاجون إلى تحسين */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border border-[#e0f2ef] rounded-md p-4">
              <h3 className="font-bold text-[var(--primary-color)] mb-4">الطلاب المتفوقين</h3>
              {reportData.topStudents.length === 0 ? (
                <p className="text-center text-gray-500 py-4">لا توجد بيانات متاحة</p>
              ) : (
                <div className="space-y-2">
                  {reportData.topStudents.map(student => (
                    <div key={student.id} className="flex justify-between items-center p-2 hover:bg-gray-50 rounded-md">
                      <span>{student.name}</span>
                      <span className="text-[var(--primary-color)] font-bold">
                        {student.examResults?.avgScore.toFixed(1) || '-'}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="border border-[#e0f2ef] rounded-md p-4">
              <h3 className="font-bold text-red-500 mb-4">طلاب يحتاجون إلى تحسين</h3>
              {reportData.needsImprovement.length === 0 ? (
                <p className="text-center text-gray-500 py-4">لا توجد بيانات متاحة</p>
              ) : (
                <div className="space-y-2">
                  {reportData.needsImprovement.map(student => (
                    <div key={student.id} className="flex justify-between items-center p-2 hover:bg-gray-50 rounded-md">
                      <span>{student.name}</span>
                      <span className="text-red-500 font-bold">
                        {student.examResults?.avgScore.toFixed(1) || '-'}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </AnimatedDialog>
  )
}

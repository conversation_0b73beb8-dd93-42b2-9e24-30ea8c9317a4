'use client';

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';

// تعريف واجهة مخصصة لـ MediaStreamTrack مع خاصية onchange
interface CustomMediaStreamTrack extends MediaStreamTrack {
  onchange?: (() => void) | null;
}

interface ScreenShareResizeHandlerProps {
  stream: MediaStream | null;
  onResize: (width: number, height: number) => void;
  debounceTime?: number;
}

/**
 * Component to handle screen resize events for screen sharing
 */
const ScreenShareResizeHandler: React.FC<ScreenShareResizeHandlerProps> = ({
  stream,
  onResize,
  debounceTime = 300,
}) => {
  const [videoTrack, setVideoTrack] = useState<CustomMediaStreamTrack | null>(null);

  // Get the video track from the stream
  useEffect(() => {
    if (stream) {
      const track = stream.getVideoTracks()[0];
      if (track) {
        setVideoTrack(track);
      }
    } else {
      setVideoTrack(null);
    }
  }, [stream]);

  // Create a memoized debounced version of the resize handler
  const handleResize = useCallback(() => {
    if (videoTrack) {
      const settings = videoTrack.getSettings();
      const { width, height } = settings;

      if (width && height) {
        onResize(width, height);
      }
    }
  }, [videoTrack, onResize]);

  // Create a debounced version of the handler
  const debouncedHandleResize = useMemo(
    () => debounce(handleResize, debounceTime),
    [handleResize, debounceTime]
  );

  // Set up resize event listener
  useEffect(() => {
    if (videoTrack) {
      // Initial size check
      debouncedHandleResize();

      // Add resize event listener
      window.addEventListener('resize', debouncedHandleResize);

      // Set up track settings change listener if available
      if ('onchange' in videoTrack) {
        // This is a non-standard feature and may not be available in all browsers
        videoTrack.onchange = () => {
          debouncedHandleResize();
        };
      }
    }

    return () => {
      // Clean up
      debouncedHandleResize.cancel();
      window.removeEventListener('resize', debouncedHandleResize);

      if (videoTrack && 'onchange' in videoTrack) {
        videoTrack.onchange = null;
      }
    };
  }, [videoTrack, debouncedHandleResize]);

  // This component doesn't render anything
  return null;
};

export default ScreenShareResizeHandler;

'use client';

import React from 'react';
import { usePageBackground } from '@/hooks/usePageBackground';

interface PageBackgroundData {
  id: number;
  pageName: string;
  displayName: string;
  imageUrl?: string;
  overlayColor?: string;
  overlayOpacity: number;
  position: string;
  size: string;
  repeat: string;
  attachment: string;
  isActive: boolean;
  priority: number;
}

interface PageBackgroundProps {
  pageName: string;
  children: React.ReactNode;
  className?: string;
  fallbackBackground?: string;
  minHeight?: string;
}

const PageBackground: React.FC<PageBackgroundProps> = ({
  pageName,
  children,
  className = '',
  fallbackBackground = 'linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%)',
  minHeight = '100vh'
}) => {
  // 🚀 استخدام Hook محسن مع تخزين محلي
  const { background, loading, backgroundStyles, overlayStyles, hasBackground } = usePageBackground(pageName);

  // 🎨 أنماط الحاوية الرئيسية مع دعم RTL
  const containerStyles: React.CSSProperties = {
    position: 'relative',
    minHeight,
    width: '100%',
    direction: 'rtl', // دعم الاتجاه من اليمين لليسار
    // استخدام الخلفية المخصصة أو الاحتياطية
    ...(hasBackground ? backgroundStyles : {
      background: fallbackBackground
    })
  };

  // أنماط المحتوى
  const contentStyles: React.CSSProperties = {
    position: 'relative',
    zIndex: 2,
    width: '100%',
    height: '100%'
  };

  // 🔄 حالة التحميل مع تصميم جميل
  if (loading) {
    return (
      <div
        className={`flex items-center justify-center ${className}`}
        style={{
          minHeight,
          background: fallbackBackground,
          direction: 'rtl'
        }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white font-medium">جاري تحميل الصفحة...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={className}
      style={containerStyles}
    >
      {/* الطبقة العلوية */}
      {hasBackground && overlayStyles && (
        <div style={overlayStyles} />
      )}

      {/* المحتوى */}
      <div style={contentStyles}>
        {children}
      </div>
    </div>
  );
};

export default PageBackground;

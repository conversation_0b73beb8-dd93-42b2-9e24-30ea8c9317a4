import {  NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/treasury/seed - إنشاء خزينة افتراضية
export async function POST() {
  try {
    // التحقق من وجود خزينة
    const existingTreasury = await prisma.treasury.findFirst();

    if (existingTreasury) {
      return NextResponse.json({
        message: 'الخزينة موجودة بالفعل',
        treasury: existingTreasury
      });
    }

    // إنشاء خزينة افتراضية
    const treasury = await prisma.treasury.create({
      data: {
        balance: 0,
        totalIncome: 0,
        totalExpense: 0
      }
    });

    return NextResponse.json({
      message: 'تم إنشاء الخزينة بنجاح',
      treasury
    });
  } catch (error: unknown) {
    console.error('خطأ في إنشاء الخزينة:', error);

    // معالجة أخطاء قاعدة البيانات المحددة
    if (error && typeof error === 'object' && 'code' in error) {
      const prismaError = error as { code: string; meta?: { target?: string[] } };

      if (prismaError.code === 'P2002') {
        return NextResponse.json(
          { error: 'الخزينة موجودة بالفعل' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'فشل في إنشاء الخزينة' },
      { status: 500 }
    );
  }
}

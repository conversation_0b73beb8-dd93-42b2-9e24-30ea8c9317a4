import { NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

export async function GET() {
    try {
        // Check if any users exist
        const userCount = await prisma.user.count();

        if (userCount === 0) {
            // Create default admin user
             
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash('admin123', salt);
        
    
            const defaultAdmin = await prisma.user.create({
                data: {
                    username: 'admin',
                    password: hashedPassword,
                    role: 'ADMIN', // Default role is admin
                    profile: { // Create profile for admin user
                        create: {
                            name: 'Admin User',
                            phone: '0123456789'
                        }
                    }
                }
            });

            return NextResponse.json({
                message: 'Default admin user created successfully',
                user: defaultAdmin
            });
        }

        return NextResponse.json({
            message: 'Users already exist',
            userCount
        });

    } catch (error) {
        console.error('Error initializing default admin:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}
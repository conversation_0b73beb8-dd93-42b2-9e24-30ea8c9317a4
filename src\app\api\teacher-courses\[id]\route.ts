import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { verifyToken } from '@/utils/verifyToken';

// GET /api/teacher-courses/[id] - جلب تفاصيل مقرر معين للمعلم
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من المصادقة
    const userData = await verifyToken(request);
    if (!userData) {
      return NextResponse.json(
        { success: false, message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // التحقق من وجود معرف المقرر
    const courseId = parseInt(params.id);
    if (isNaN(courseId)) {
      return NextResponse.json(
        { success: false, message: "معرف المقرر غير صالح" },
        { status: 400 }
      );
    }

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userData.id
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // جلب تفاصيل المقرر (ClassSubject)
    const classSubject = await prisma.classSubject.findUnique({
      where: {
        id: courseId
      },
      include: {
        classe: {
          include: {
            students: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        teacherSubject: {
          include: {
            subject: true,
            teacher: true
          }
        }
      }
    });

    if (!classSubject) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على المقرر" },
        { status: 404 }
      );
    }

    // التحقق من أن المقرر ينتمي للمعلم
    if (classSubject.teacherSubject.teacherId !== teacher.id) {
      return NextResponse.json(
        { success: false, message: "غير مصرح لك بالوصول إلى هذا المقرر" },
        { status: 403 }
      );
    }

    // جلب المواد التعليمية للمقرر
    const materials = await prisma.courseMaterial.findMany({
      where: {
        classSubjectId: courseId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // تنسيق البيانات للعرض
    const courseDetails = {
      id: classSubject.id,
      subjectId: classSubject.teacherSubject.subjectId,
      subjectName: classSubject.teacherSubject.subject.name,
      className: classSubject.classe.name,
      classId: classSubject.classeId,
      studentsCount: classSubject.classe.students.length,
      description: classSubject.teacherSubject.subject.description || '',
      students: classSubject.classe.students,
      materials: materials.map(material => ({
        id: material.id,
        title: material.title,
        type: material.type,
        url: material.url,
        createdAt: material.createdAt.toISOString().split('T')[0]
      }))
    };

    return NextResponse.json({
      success: true,
      courseDetails,
      message: "تم جلب تفاصيل المقرر بنجاح"
    });
  } catch (error) {
    console.error('Error fetching course details:', error);
    return NextResponse.json(
      { success: false, message: "حدث خطأ أثناء جلب تفاصيل المقرر" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/curriculum - جلب المنهج الدراسي
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const subjectId = searchParams.get('subjectId');

    if (!subjectId) {
      return NextResponse.json(
        { message: "معرف المادة مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود المادة
    const subject = await prisma.subject.findUnique({
      where: {
        id: parseInt(subjectId)
      }
    });

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    // جلب وحدات المنهج
    const units = await prisma.curriculumUnit.findMany({
      where: {
        subjectId: parseInt(subjectId)
      },
      include: {
        lessons: {
          include: {
            resources: true
          },
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        order: 'asc'
      }
    });

    return NextResponse.json({
      units,
      message: "تم جلب المنهج بنجاح"
    });
    
  } catch (error) {
    console.error('Error fetching curriculum:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب المنهج" },
      { status: 500 }
    );
  }
}

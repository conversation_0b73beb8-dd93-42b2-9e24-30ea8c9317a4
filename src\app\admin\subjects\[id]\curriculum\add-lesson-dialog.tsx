'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface AddLessonDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  unitId: number | null;
}

export default function AddLessonDialog({ isOpen, onCloseAction, onSuccessAction, unitId }: AddLessonDialogProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    order: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddLesson = async () => {
    if (!unitId) return;
    
    if (!formData.title.trim()) {
      toast.error('الرجاء إدخال عنوان الدرس');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/curriculum/lessons', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          order: formData.order ? parseInt(formData.order) : undefined,
          unitId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add lesson');
      }

      toast.success('تمت إضافة الدرس بنجاح');
      setFormData({
        title: '',
        description: '',
        order: ''
      });
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error adding lesson:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة الدرس');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleAddLesson}
      disabled={isLoading || !formData.title.trim() || !unitId}
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري الإضافة...' : 'إضافة'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إضافة درس جديد"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
        <div className="space-y-2">
          <Label>عنوان الدرس</Label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="أدخل عنوان الدرس"
          />
        </div>
        
        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف الدرس"
            rows={3}
          />
        </div>
        
        <div className="space-y-2">
          <Label>الترتيب (اختياري)</Label>
          <Input
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="أدخل ترتيب الدرس"
            min={1}
          />
          <p className="text-xs text-gray-500">إذا تركت هذا الحقل فارغًا، سيتم وضع الدرس في نهاية القائمة</p>
        </div>
      </div>
    </AnimatedDialog>
  );
}

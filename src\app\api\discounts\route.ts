import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { DiscountType, Prisma } from '@prisma/client';
import { ActivityLogger, ActivityType } from '@/lib/activity-logger';

// تعريف نوع شروط البحث للخصومات
type DiscountWhereInput = Prisma.DiscountWhereInput;

// GET /api/discounts - جلب جميع الخصومات
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const activeOnly = searchParams.get('activeOnly') === 'true';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const whereCondition: DiscountWhereInput = {};

    if (activeOnly) {
      whereCondition.isActive = true;

      // البحث عن الخصومات النشطة حاليًا (إذا تم تحديد تواريخ)
      const now = new Date();
      whereCondition.OR = [
        {
          startDate: { lte: now },
          endDate: { gte: now }
        },
        {
          startDate: { lte: now },
          endDate: null
        },
        {
          startDate: null,
          endDate: { gte: now }
        },
        {
          startDate: null,
          endDate: null
        }
      ];
    }

    // جلب إجمالي عدد الخصومات
    const totalDiscounts = await prisma.discount.count({
      where: whereCondition
    });

    // جلب الخصومات مع التصفح
    const discounts = await prisma.discount.findMany({
      where: whereCondition,
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    // إعداد معلومات التصفح
    const pagination = {
      current: page,
      pageSize: limit,
      total: totalDiscounts,
      totalPages: Math.ceil(totalDiscounts / limit)
    };

    return NextResponse.json({
      discounts,
      pagination
    });
  } catch (error) {
    console.error('Error fetching discounts:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الخصومات' },
      { status: 500 }
    );
  }
}

// POST /api/discounts - إنشاء خصم جديد
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      name,
      description,
      type,
      value,
      isActive,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      maxUsage
    } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !type || value === undefined) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    // التحقق من نوع الخصم
    if (!Object.values(DiscountType).includes(type as DiscountType)) {
      return NextResponse.json(
        { error: 'نوع الخصم غير صحيح' },
        { status: 400 }
      );
    }

    // التحقق من قيمة الخصم
    if (typeof value !== 'number' || value <= 0) {
      return NextResponse.json(
        { error: 'قيمة الخصم غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من النسبة المئوية (يجب أن تكون بين 0 و 100)
    if (type === DiscountType.PERCENTAGE && (value <= 0 || value > 100)) {
      return NextResponse.json(
        { error: 'النسبة المئوية يجب أن تكون بين 0 و 100' },
        { status: 400 }
      );
    }

    // إنشاء الخصم
    const discount = await prisma.discount.create({
      data: {
        name,
        description,
        type: type as DiscountType,
        value,
        isActive: isActive ?? true,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        minAmount,
        maxAmount,
        maxUsage
      }
    });

    // تسجيل نشاط إنشاء الخصم
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.DISCOUNT,
          `إنشاء خصم جديد: ${name}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط إنشاء الخصم:', error);
      // لا نريد أن يفشل إنشاء الخصم إذا فشل تسجيل النشاط
    }

    return NextResponse.json(discount);
  } catch (error) {
    console.error('Error creating discount:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الخصم' },
      { status: 500 }
    );
  }
}

// PATCH /api/discounts/:id - تحديث خصم
export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الخصم مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الخصم
    const existingDiscount = await prisma.discount.findUnique({
      where: { id: parseInt(id.toString()) }
    });

    if (!existingDiscount) {
      return NextResponse.json(
        { error: 'الخصم غير موجود' },
        { status: 404 }
      );
    }

    // تحديث الخصم
    const updatedDiscount = await prisma.discount.update({
      where: { id: parseInt(id.toString()) },
      data: {
        name: updateData.name,
        description: updateData.description,
        type: updateData.type,
        value: updateData.value,
        isActive: updateData.isActive,
        startDate: updateData.startDate ? new Date(updateData.startDate) : null,
        endDate: updateData.endDate ? new Date(updateData.endDate) : null,
        minAmount: updateData.minAmount,
        maxAmount: updateData.maxAmount,
        maxUsage: updateData.maxUsage
      }
    });

    // تسجيل نشاط تحديث الخصم
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.DISCOUNT,
          `تحديث الخصم: ${updatedDiscount.name}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط تحديث الخصم:', error);
    }

    return NextResponse.json(updatedDiscount);
  } catch (error) {
    console.error('Error updating discount:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الخصم' },
      { status: 500 }
    );
  }
}

// DELETE /api/discounts/:id - حذف خصم
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الخصم مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الخصم
    const existingDiscount = await prisma.discount.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingDiscount) {
      return NextResponse.json(
        { error: 'الخصم غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من استخدام الخصم في المدفوعات أو الفواتير
    const paymentsCount = await prisma.payment.count({
      where: { discountId: parseInt(id) }
    });

    const invoicesCount = await prisma.invoice.count({
      where: { discountId: parseInt(id) }
    });

    if (paymentsCount > 0 || invoicesCount > 0) {
      // بدلاً من الحذف، قم بتعطيل الخصم
      const updatedDiscount = await prisma.discount.update({
        where: { id: parseInt(id) },
        data: { isActive: false }
      });

      return NextResponse.json({
        message: 'تم تعطيل الخصم بدلاً من حذفه لأنه مستخدم في مدفوعات أو فواتير',
        discount: updatedDiscount
      });
    }

    // حذف الخصم
    await prisma.discount.delete({
      where: { id: parseInt(id) }
    });

    // تسجيل نشاط حذف الخصم
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.DISCOUNT,
          `حذف الخصم: ${existingDiscount.name}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط حذف الخصم:', error);
    }

    return NextResponse.json({
      message: 'تم حذف الخصم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting discount:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الخصم' },
      { status: 500 }
    );
  }
}

// ملاحظة: تم نقل وظيفة GET_ONE إلى ملف [id]/route.ts
// لأن Next.js لا يدعم تعريف وظائف متعددة بنفس الاسم في نفس الملف
// يجب إنشاء ملف src\app\api\discounts\[id]\route.ts لتنفيذ هذه الوظيفة

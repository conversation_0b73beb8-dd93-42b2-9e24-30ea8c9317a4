import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";
import { UserRole } from "@prisma/client";

// POST: إلغاء ربط طالب بولي
export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.TEACHER)) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // التحقق من البيانات المطلوبة
    if (!body.studentId) {
      return NextResponse.json(
        { message: "معرف الطالب مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(body.studentId) },
      include: {
        guardian: true
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "الطالب غير موجود" },
        { status: 404 }
      );
    }

    if (!student.guardianId) {
      return NextResponse.json(
        { message: "الطالب غير مرتبط بأي ولي" },
        { status: 400 }
      );
    }

    const parentName = student.guardian?.name || "غير معروف";

    // إلغاء ربط الطالب بالولي
    const updatedStudent = await prisma.student.update({
      where: { id: parseInt(body.studentId) },
      data: {
        guardianId: null
      }
    });

    // تسجيل النشاط
    await prisma.activity.create({
      data: {
        userId: userData.id,
        type: 'UNLINK_STUDENT',
        description: `تم إلغاء ربط الطالب ${student.name} من الولي ${parentName}`
      }
    });

    return NextResponse.json({
      message: "تم إلغاء ربط الطالب من الولي بنجاح",
      data: updatedStudent
    });
  } catch (error) {
    console.error('Error unlinking student from parent:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إلغاء ربط الطالب من الولي" },
      { status: 500 }
    );
  }
}

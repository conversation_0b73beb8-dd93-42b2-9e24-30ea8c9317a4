'use client';

import React, { useRef, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import './dialog-animations.css';

interface AnimatedDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: React.ReactNode;
  description?: string;
  children: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'primary';
}

const AnimatedDialog: React.FC<AnimatedDialogProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  footer,
  className = '',
  variant = 'default'
}) => {
  const dialogRef = useRef<HTMLDivElement>(null);
  const variantClass = variant === 'primary' ? 'dialog-primary' : '';

  // تحسين التعامل مع الأحداث داخل البوب أب
  useEffect(() => {
    if (isOpen && dialogRef.current) {
      // تحسين التعامل مع النماذج
      const forms = dialogRef.current.querySelectorAll('form');
      forms.forEach(form => {
        // إضافة مستمع للنموذج للتأكد من أن الأحداث تعمل بشكل صحيح
        const submitHandler = (e: Event) => {
          // التأكد من أن النموذج يعمل بشكل صحيح
          if (form.hasAttribute('data-custom-submit')) {
            e.preventDefault();
          }
        };

        // إزالة المستمع القديم وإضافة واحد جديد
        form.removeEventListener('submit', submitHandler);
        form.addEventListener('submit', submitHandler);
      });

      // إضافة مستمع للنقر على القوائم المنسدلة
      const selectElements = dialogRef.current.querySelectorAll('[role="combobox"]');
      selectElements.forEach(select => {
        select.addEventListener('click', (e) => {
          // منع الفقاعة للسماح بالنقر على القائمة المنسدلة
          e.stopPropagation();
        });
      });
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        ref={dialogRef}
        className={`dialog-content ${variantClass} ${className}`}
        onPointerDownOutside={(e) => {
          // منع إغلاق البوب أب عند النقر خارجه أثناء التفاعل مع عناصر القائمة المنسدلة
          const target = e.target as HTMLElement;
          if (target && (
            target.closest('[role="listbox"]') ||
            target.closest('[role="option"]') ||
            target.closest('[data-radix-select-viewport]') ||
            target.closest('[data-radix-popper-content-wrapper]')
          )) {
            e.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle className="dialog-title">{title}</DialogTitle>
          {description && <DialogDescription className="dialog-description">{description}</DialogDescription>}
        </DialogHeader>
        <div className="dialog-body">
          {children}
        </div>
        {footer && <DialogFooter className="dialog-footer">{footer}</DialogFooter>}
      </DialogContent>
    </Dialog>
  );
};

export default AnimatedDialog;

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 بدء تشغيل ملف البيانات الأساسية...');

    // تشغيل أمر seed المبسط
    const { stdout, stderr } = await execAsync('npx ts-node --project prisma/tsconfig-seed.json prisma/seed-simple-fix.ts', {
      cwd: process.cwd(),
      timeout: 120000 // دقيقتان timeout
    });

    console.log('✅ تم تشغيل ملف البيانات الأساسية بنجاح');
    console.log('Output:', stdout);
    
    if (stderr) {
      console.warn('Warnings:', stderr);
    }

    return NextResponse.json({
      success: true,
      message: 'تم تشغيل ملف البيانات الأساسية بنجاح',
      output: stdout,
      warnings: stderr || null
    });

  } catch (error: any) {
    console.error('❌ خطأ في تشغيل ملف البيانات الأساسية:', error);
    
    return NextResponse.json(
      { 
        success: false,
        message: "حدث خطأ أثناء تشغيل ملف البيانات الأساسية", 
        error: error.message || 'Unknown error',
        stdout: error.stdout || null,
        stderr: error.stderr || null
      },
      { status: 500 }
    );
  }
}

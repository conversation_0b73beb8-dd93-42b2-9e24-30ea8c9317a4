import { NextRequest, NextResponse } from 'next/server';
import { FinancialReportService } from '@/lib/reports/financial-report-service';
import { FinancialReportExporter } from '@/lib/export/financial-report';
import * as XLSX from 'xlsx';
import { format as formatDate } from 'date-fns';
import { ar } from 'date-fns/locale';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { startDate, endDate, format, options = {} } = body;

    // التحقق من صحة المعاملات
    if (!startDate || !endDate) {
      return NextResponse.json(
        { success: false, error: 'تواريخ البداية والنهاية مطلوبة' },
        { status: 400 }
      );
    }

    if (!['excel', 'pdf', 'csv'].includes(format)) {
      return NextResponse.json(
        { success: false, error: 'صيغة التصدير غير مدعومة' },
        { status: 400 }
      );
    }

    // جلب بيانات التقرير المالي
    const reportData = await FinancialReportService.generateReport(
      new Date(startDate),
      new Date(endDate)
    );

    if (!reportData) {
      return NextResponse.json(
        { success: false, error: 'فشل في جلب بيانات التقرير المالي' },
        { status: 500 }
      );
    }

    // معلومات المكتب من الخيارات
    const officeInfo = {
      organizationName: options.organizationName || 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
      officeName: options.officeName || 'المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر',
      branchName: options.branchName || 'شعبة بلدية المنقر',
      presidentName: options.presidentName || 'الوليد بن ناصر قصي',
      presidentTitle: options.presidentTitle || 'رئيس المكتب البلدي',
    };

    const formatCurrency = (amount: number) => {
      return amount.toLocaleString('ar-DZ') + ' د.ج';
    };

    let buffer: Buffer;
    let filename: string;
    let contentType: string;

    // تصدير حسب الصيغة المطلوبة
    switch (format) {
      case 'excel':
        // إنشاء مصنف Excel
        const workbook = XLSX.utils.book_new();

        // ورقة الملخص التنفيذي
        const summaryData = [
          [officeInfo.organizationName],
          [officeInfo.officeName],
          [officeInfo.branchName],
          [`التقرير المالي للفترة من ${formatDate(new Date(startDate), 'PPP', { locale: ar })} إلى ${formatDate(new Date(endDate), 'PPP', { locale: ar })}`],
          [''],
          ['الملخص التنفيذي'],
          ['البيان', 'المبلغ'],
          ['الرصيد الافتتاحي', formatCurrency(reportData.executiveSummary.openingBalance)],
          ['إجمالي المداخيل', formatCurrency(reportData.executiveSummary.totalIncome)],
          ['إجمالي المصروفات', formatCurrency(reportData.executiveSummary.totalExpenses)],
          ['صافي الربح/الخسارة', formatCurrency(reportData.executiveSummary.netProfit)],
          ['الرصيد الختامي', formatCurrency(reportData.executiveSummary.closingBalance)],
          ['إجمالي المعاملات', reportData.executiveSummary.totalTransactions.toString()],
        ];

        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'الملخص التنفيذي');

        // ورقة تفاصيل المداخيل
        const incomeData = [
          ['تفاصيل المداخيل'],
          ['نوع المدخول', 'العدد', 'المبلغ', 'النسبة'],
          ['مدفوعات الطلاب', reportData.incomeDetails.studentPayments.count.toString(), formatCurrency(reportData.incomeDetails.studentPayments.amount), `${reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%`],
          ['التبرعات', reportData.incomeDetails.donations.count.toString(), formatCurrency(reportData.incomeDetails.donations.amount), `${reportData.incomeDetails.donations.percentage.toFixed(1)}%`],
          ['مداخيل أخرى', reportData.incomeDetails.otherIncomes.count.toString(), formatCurrency(reportData.incomeDetails.otherIncomes.amount), `${reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%`],
        ];

        const incomeSheet = XLSX.utils.aoa_to_sheet(incomeData);
        XLSX.utils.book_append_sheet(workbook, incomeSheet, 'تفاصيل المداخيل');

        // ورقة تفاصيل المصروفات
        const expenseData = [
          ['تفاصيل المصروفات'],
          ['فئة المصروف', 'العدد', 'المبلغ', 'النسبة'],
          ...reportData.expenseDetails.byCategory.map(category => [
            category.name,
            category.expensesCount.toString(),
            formatCurrency(category.totalAmount),
            `${category.percentage.toFixed(1)}%`
          ])
        ];

        const expenseSheet = XLSX.utils.aoa_to_sheet(expenseData);
        XLSX.utils.book_append_sheet(workbook, expenseSheet, 'تفاصيل المصروفات');

        // تحويل إلى buffer
        const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        buffer = Buffer.from(excelBuffer);
        filename = `التقرير_المالي_${new Date().toISOString().split('T')[0]}.xlsx`;
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        break;

      case 'pdf':
        try {
          // استخدام FinancialReportExporter لإنشاء HTML منسق
          const htmlBlob = await FinancialReportExporter.exportToPDF(reportData, {
            ...options,
            organizationName: officeInfo.organizationName,
            officeName: officeInfo.officeName,
            branchName: officeInfo.branchName,
            presidentName: officeInfo.presidentName,
            presidentTitle: officeInfo.presidentTitle
          });
          const htmlArrayBuffer = await htmlBlob.arrayBuffer();
          buffer = Buffer.from(htmlArrayBuffer);
          filename = `التقرير_المالي_${new Date().toISOString().split('T')[0]}.html`;
          contentType = 'text/html; charset=utf-8';
        } catch (exportError) {
          console.error('خطأ في FinancialReportExporter:', exportError);

          // حل بديل: إنشاء HTML بسيط
          const simpleHtml = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>التقرير المالي</title>
    <style>
        body { font-family: Arial, sans-serif; direction: rtl; text-align: right; margin: 20px; }
        h1 { color: #169b87; text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #169b87; color: white; }
    </style>
</head>
<body>
    <h1>${officeInfo.organizationName}</h1>
    <h2>${officeInfo.officeName}</h2>
    <h3>التقرير المالي</h3>
    <p>للفترة من ${formatDate(new Date(startDate), 'PPP', { locale: ar })} إلى ${formatDate(new Date(endDate), 'PPP', { locale: ar })}</p>

    <h3>الملخص التنفيذي</h3>
    <table>
        <tr><th>البيان</th><th>المبلغ</th></tr>
        <tr><td>الرصيد الافتتاحي</td><td>${formatCurrency(reportData.executiveSummary.openingBalance)}</td></tr>
        <tr><td>إجمالي المداخيل</td><td>${formatCurrency(reportData.executiveSummary.totalIncome)}</td></tr>
        <tr><td>إجمالي المصروفات</td><td>${formatCurrency(reportData.executiveSummary.totalExpenses)}</td></tr>
        <tr><td>صافي الربح/الخسارة</td><td>${formatCurrency(reportData.executiveSummary.netProfit)}</td></tr>
        <tr><td>الرصيد الختامي</td><td>${formatCurrency(reportData.executiveSummary.closingBalance)}</td></tr>
    </table>

    <h3>تفاصيل المداخيل</h3>
    <table>
        <tr><th>نوع المدخول</th><th>العدد</th><th>المبلغ</th><th>النسبة</th></tr>
        <tr><td>مدفوعات الطلاب</td><td>${reportData.incomeDetails.studentPayments.count}</td><td>${formatCurrency(reportData.incomeDetails.studentPayments.amount)}</td><td>${reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%</td></tr>
        <tr><td>التبرعات</td><td>${reportData.incomeDetails.donations.count}</td><td>${formatCurrency(reportData.incomeDetails.donations.amount)}</td><td>${reportData.incomeDetails.donations.percentage.toFixed(1)}%</td></tr>
        <tr><td>مداخيل أخرى</td><td>${reportData.incomeDetails.otherIncomes.count}</td><td>${formatCurrency(reportData.incomeDetails.otherIncomes.amount)}</td><td>${reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%</td></tr>
    </table>

    <h3>تفاصيل المصروفات</h3>
    <table>
        <tr><th>فئة المصروف</th><th>العدد</th><th>المبلغ</th><th>النسبة</th></tr>
        ${reportData.expenseDetails.byCategory.map(category => `
        <tr><td>${category.name}</td><td>${category.expensesCount}</td><td>${formatCurrency(category.totalAmount)}</td><td>${category.percentage.toFixed(1)}%</td></tr>
        `).join('')}
    </table>

    <p style="text-align: center; margin-top: 40px;">
        <strong>تقرير يوم ${formatDate(new Date(), 'PPP', { locale: ar })}</strong><br>
        عن ${officeInfo.presidentTitle}: ${officeInfo.presidentName}
    </p>
</body>
</html>
          `;

          buffer = Buffer.from(simpleHtml, 'utf-8');
          filename = `التقرير_المالي_${new Date().toISOString().split('T')[0]}.html`;
          contentType = 'text/html; charset=utf-8';
        }
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'صيغة التصدير غير مدعومة' },
          { status: 400 }
        );
    }

    // إرجاع الملف
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Content-Length': buffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('خطأ في تصدير التقرير المالي:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'حدث خطأ أثناء تصدير التقرير المالي',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

// معالج GET للحصول على معلومات التصدير المتاحة
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      data: {
        supportedFormats: [
          {
            format: 'excel',
            name: 'Excel',
            description: 'ملف Excel للتحليل والمعالجة',
            extension: '.xlsx',
            mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          },
          {
            format: 'pdf',
            name: 'PDF',
            description: 'ملف PDF للعرض والطباعة',
            extension: '.pdf',
            mimeType: 'application/pdf'
          },
          {
            format: 'csv',
            name: 'CSV',
            description: 'ملف CSV لتبادل البيانات',
            extension: '.csv',
            mimeType: 'text/csv'
          }
        ],
        availableOptions: {
          includeCharts: {
            type: 'boolean',
            default: true,
            description: 'تضمين الرسوم البيانية'
          },
          separateSheets: {
            type: 'boolean',
            default: true,
            description: 'فصل البيانات في أوراق منفصلة (Excel فقط)'
          },
          currency: {
            type: 'string',
            default: 'DZD',
            description: 'رمز العملة'
          },
          watermark: {
            type: 'string',
            default: null,
            description: 'نص العلامة المائية'
          }
        }
      }
    });
  } catch (error) {
    console.error('خطأ في جلب معلومات التصدير المالي:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب معلومات التصدير' },
      { status: 500 }
    );
  }
}

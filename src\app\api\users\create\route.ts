import { NextRequest, NextResponse } from "next/server";
import { UserRole } from '@prisma/client';
import prisma from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { getToken } from '@/lib/auth';

interface CreateUserDto {
    username: string;
    password: string;
    name: string;
    email?: string;
    phone?: string;
    role: string;
}

export async function POST(request: NextRequest) {
    try {
        // التحقق من الصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || userData.role !== 'ADMIN') {
            return NextResponse.json(
                { message: "غير مصرح به: يجب أن تكون مسؤولاً لإنشاء مستخدمين" },
                { status: 403 }
            );
        }

        const body = await request.json() as CreateUserDto;

        // التحقق من وجود الحقول المطلوبة
        if (!body.username || !body.password || !body.name || !body.role) {
            return NextResponse.json(
                { message: "جميع الحقول المطلوبة يجب ملؤها" },
                { status: 400 }
            );
        }

        // التحقق من عدم وجود المستخدم مسبقاً
        const existingUser = await prisma.user.findUnique({
            where: {
                username: body.username
            }
        });

        if (existingUser) {
            return NextResponse.json(
                { message: "اسم المستخدم موجود بالفعل" },
                { status: 400 }
            );
        }

        // التحقق من صحة الدور
        let userRole: UserRole;
        const basicRoles = ['ADMIN', 'TEACHER', 'STUDENT', 'PARENT', 'EMPLOYEE', 'PENDING'];

        if (basicRoles.includes(body.role.toUpperCase())) {
            // دور أساسي
            switch (body.role.toUpperCase()) {
                case 'ADMIN':
                    userRole = UserRole.ADMIN;
                    break;
                case 'TEACHER':
                    userRole = UserRole.TEACHER;
                    break;
                case 'STUDENT':
                    userRole = UserRole.STUDENT;
                    break;
                case 'PARENT':
                    userRole = UserRole.PARENT;
                    break;
                case 'EMPLOYEE':
                    userRole = UserRole.EMPLOYEE;
                    break;
                case 'PENDING':
                    userRole = UserRole.PENDING;
                    break;
                default:
                    userRole = UserRole.EMPLOYEE; // افتراضي
            }
        } else {
            // دور مخصص - نحتاج لتحديد الدور الأساسي المناسب
            try {
                // جلب الدور المخصص من قاعدة البيانات
                const customRole = await prisma.role.findUnique({
                    where: { name: body.role }
                });

                if (!customRole || !customRole.isActive) {
                    return NextResponse.json(
                        { message: "الدور المحدد غير موجود أو غير نشط" },
                        { status: 400 }
                    );
                }

                // تحديد الدور الأساسي بناءً على الدور المخصص
                const { determineBaseRole } = await import('@/utils/roleUtils');
                const allRoles = await prisma.role.findMany({ where: { isActive: true } });
                const baseRoleName = determineBaseRole(customRole, allRoles);

                switch (baseRoleName.toUpperCase()) {
                    case 'ADMIN':
                        userRole = UserRole.ADMIN;
                        break;
                    case 'TEACHER':
                        userRole = UserRole.TEACHER;
                        break;
                    case 'STUDENT':
                        userRole = UserRole.STUDENT;
                        break;
                    case 'PARENT':
                        userRole = UserRole.PARENT;
                        break;
                    case 'EMPLOYEE':
                        userRole = UserRole.EMPLOYEE;
                        break;
                    default:
                        userRole = UserRole.EMPLOYEE; // افتراضي
                }
            } catch (error) {
                console.error('Error determining base role:', error);
                return NextResponse.json(
                    { message: "خطأ في تحديد الدور الأساسي" },
                    { status: 400 }
                );
            }
        }

        // تشفير كلمة المرور
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(body.password, salt);

        // تحديد الدور المخصص - البحث عن دور مطابق
        let customRoleId = null;

        console.log('🔍 البحث عن دور للمستخدم الجديد:', {
            requestedRole: body.role,
            determinedUserRole: userRole
        });

        // البحث عن دور مطابق (مخصص أو نظام)
        const matchingRole = await prisma.role.findFirst({
            where: {
                name: body.role.toUpperCase(),
                isActive: true
            }
        });

        if (matchingRole) {
            customRoleId = matchingRole.id;
            console.log('✅ تم العثور على دور مطابق:', {
                name: matchingRole.name,
                id: matchingRole.id,
                isSystem: matchingRole.isSystem,
                displayName: matchingRole.displayName
            });
        } else {
            console.log('❌ لم يتم العثور على دور مطابق لـ:', body.role);
            // إذا لم يوجد دور مطابق، ابحث عن دور افتراضي حسب النوع
            const defaultRole = await prisma.role.findFirst({
                where: {
                    name: userRole,
                    isSystem: true,
                    isActive: true
                }
            });

            if (defaultRole) {
                customRoleId = defaultRole.id;
                console.log('✅ استخدام دور افتراضي:', {
                    name: defaultRole.name,
                    id: defaultRole.id,
                    displayName: defaultRole.displayName
                });
            } else {
                console.log('❌ لم يتم العثور على دور افتراضي لـ:', userRole);
            }
        }

        console.log('📋 ملخص الدور المحدد:', {
            userRole: userRole,
            customRoleId: customRoleId,
            originalRequest: body.role
        });

        // إنشاء المستخدم والملف الشخصي في نفس الوقت
        console.log('🚀 إنشاء المستخدم بالبيانات:', {
            role: userRole,
            roleId: customRoleId,
            username: body.username,
            email: body.email,
            name: body.name
        });

        const newUser = await prisma.user.create({
            data: {
                role: userRole,
                roleId: customRoleId,
                username: body.username,
                password: hashedPassword,
                email: body.email,
                profile: {
                    create: {
                        name: body.name,
                        phone: body.phone || null
                    }
                }
            },
            include: {
                profile: true,
                userRole: true
            }
        });

        console.log('✅ تم إنشاء المستخدم بنجاح:', {
            id: newUser.id,
            username: newUser.username,
            role: newUser.role,
            roleId: newUser.roleId,
            userRoleName: newUser.userRole?.displayName || 'غير محدد'
        });

        // إنشاء سجل المعلم أو الموظف أو الطالب أو ولي الأمر إذا كان الدور مناسباً
        if (userRole === UserRole.TEACHER) {
            await prisma.teacher.create({
                data: {
                    name: body.name,
                    phone: body.phone || null,
                    specialization: "عام", // قيمة افتراضية
                    userId: newUser.id
                }
            });
        } else if (userRole === UserRole.EMPLOYEE) {
            await prisma.employee.create({
                data: {
                    name: body.name,
                    phone: body.phone || null,
                    position: "موظف", // قيمة افتراضية
                    userId: newUser.id
                }
            });
        } else if (userRole === UserRole.STUDENT) {
            // إنشاء سجل طالب
            await prisma.student.create({
                data: {
                    name: body.name,
                    username: body.username,
                    phone: body.phone || null,
                    age: 0, // قيمة افتراضية، يمكن تحديثها لاحقاً
                }
            });
        } else if (userRole === UserRole.PARENT) {
            // إنشاء سجل ولي أمر
            await prisma.parent.create({
                data: {
                    name: body.name,
                    phone: body.phone || "",
                    email: body.email
                }
            });
        }

        return NextResponse.json(
            {
                user: {
                    id: newUser.id,
                    username: newUser.username,
                    email: newUser.email,
                    role: newUser.role,
                    roleId: newUser.roleId,
                    userRole: newUser.userRole,
                    name: newUser.profile?.name,
                    phone: newUser.profile?.phone
                },
                message: "تم إنشاء المستخدم بنجاح"
            },
            { status: 201 }
        );

    } catch (error: unknown) {
        console.error('Error creating user:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء معالجة الطلب" },
            { status: 500 }
        );
    }
}

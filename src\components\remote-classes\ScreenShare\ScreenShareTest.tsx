'use client';

import React, { useState } from 'react';
import ScreenShareButton from './ScreenShareButton';
import ScreenShareDisplay from './ScreenShareDisplay';
import { FaInfoCircle } from 'react-icons/fa';

/**
 * Test component for screen sharing functionality
 * This component demonstrates how to use the screen sharing components
 * and can be used for testing the functionality
 */
const ScreenShareTest: React.FC = () => {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isSharing, setIsSharing] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [performance, setPerformance] = useState({
    fps: 0,
    bitrate: 0,
  });

  // Handle starting screen sharing
  const handleStartSharing = (newStream: MediaStream) => {
    setStream(newStream);
    setIsSharing(true);

    // Start performance monitoring
    startPerformanceMonitoring(newStream);
  };

  // Handle stopping screen sharing
  const handleStopSharing = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
    setStream(null);
    setIsSharing(false);
    setDimensions({ width: 0, height: 0 });
    setPerformance({ fps: 0, bitrate: 0 });
  };

  // Handle resize events
  const handleResize = (width: number, height: number) => {
    setDimensions({ width, height });
  };

  // Monitor performance of the screen sharing
  const startPerformanceMonitoring = (mediaStream: MediaStream) => {
    const videoTrack = mediaStream.getVideoTracks()[0];

    if (!videoTrack) return;

    // Get initial stats
    let lastTimestamp = Date.now();

    // Update stats every second
    const interval = setInterval(() => {
      if (!videoTrack.readyState || videoTrack.readyState === 'ended') {
        clearInterval(interval);
        return;
      }

      // This is a simplified version. In a real implementation, you would use WebRTC stats API
      // which is more complex but provides more accurate data
      const settings = videoTrack.getSettings();
      const constraints = videoTrack.getConstraints();

      // Estimate FPS based on available information
      // This is just a placeholder - real FPS calculation would use WebRTC stats
      const frameRate = settings.frameRate || constraints.frameRate || 30;

      // Estimate bitrate (this is just a placeholder)
      // Real bitrate calculation would use WebRTC stats
      const now = Date.now();
      const deltaTime = (now - lastTimestamp) / 1000;
      const bitrate = Math.round((1000000 * deltaTime) / 8); // Simplified calculation

      setPerformance({
        fps: Math.round(frameRate as number),
        bitrate: bitrate,
      });

      lastTimestamp = now;
    }, 1000);

    // Clean up interval when stream ends
    mediaStream.addEventListener('inactive', () => {
      clearInterval(interval);
    });
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4 text-[var(--primary-color)]">اختبار مشاركة الشاشة</h2>

      {/* Screen sharing display */}
      <div className="mb-4">
        <ScreenShareDisplay
          stream={stream}
          isFullScreen={isFullScreen}
          userName="المستخدم"
          onToggleFullScreen={setIsFullScreen}
          onResize={handleResize}
        />
      </div>

      {/* Controls */}
      <div className="flex flex-wrap gap-4 mb-4">
        <ScreenShareButton
          onStartSharing={handleStartSharing}
          onStopSharing={handleStopSharing}
          isSharing={isSharing}
        />
      </div>

      {/* Stats display */}
      {isSharing && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
            <FaInfoCircle className="text-[var(--primary-color)]" />
            معلومات المشاركة
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">الأبعاد:</p>
              <p className="font-medium">{dimensions.width} × {dimensions.height} بكسل</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">معدل الإطارات:</p>
              <p className="font-medium">{performance.fps} إطار/ثانية</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">معدل البت:</p>
              <p className="font-medium">{performance.bitrate} كيلوبت/ثانية</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">نوع المشاركة:</p>
              <p className="font-medium">
                {stream?.getVideoTracks()[0]?.getSettings().displaySurface || 'غير معروف'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h3 className="text-lg font-medium mb-2 text-blue-700">تعليمات الاختبار</h3>
        <ol className="list-decimal list-inside space-y-2 text-blue-800">
          <li>انقر على زر &quot;مشاركة الشاشة&quot; لبدء المشاركة</li>
          <li>اختر ما تريد مشاركته (الشاشة بأكملها، نافذة، أو علامة تبويب)</li>
          <li>جرب تغيير حجم النافذة المشتركة ولاحظ تحديث الأبعاد</li>
          <li>جرب زر العرض الكامل للشاشة</li>
          <li>انقر على زر &quot;إيقاف المشاركة&quot; لإنهاء المشاركة</li>
        </ol>
      </div>
    </div>
  );
};

export default ScreenShareTest;

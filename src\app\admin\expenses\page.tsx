"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  FaMoneyBillWave,
  FaPlus,
  FaEdit,
  FaTrash,
  FaSearch,
  FaCalendarAlt,
  FaTags,
  FaFileExcel,
  FaFilePdf,
  FaChartPie,
  FaChartBar,
  FaHistory,
  <PERSON>aSave,
  <PERSON>a<PERSON>ync,
  Fa<PERSON><PERSON>
} from 'react-icons/fa';
import { <PERSON><PERSON><PERSON> } from '@/components/charts/pie-chart';
import { BarChart } from '@/components/charts/bar-chart';
import { exportToExcel, exportToPdf } from '@/utils/export-utils';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface ExpenseCategory {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  color: string | null;
  isActive: boolean;
  parentId: number | null;
}

interface Expense {
  id: number;
  purpose: string;
  amount: number;
  date: string;
  receipt: string | null;
  notes: string | null;
  categoryId: number | null;
  category: ExpenseCategory | null;
  createdAt: string;
  updatedAt: string;
}

export default function ExpensesPage() {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });
  const [formData, setFormData] = useState({
    purpose: '',
    amount: '',
    categoryId: '',
    date: new Date().toISOString().split('T')[0],
    receipt: '',
    notes: '',
  });
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    page: 1,
    limit: 10,
  });
  const [categoryStats, setCategoryStats] = useState<{ name: string; amount: number; percentage: number }[]>([]);
  const [monthlyStats, setMonthlyStats] = useState<{ month: string; amount: number }[]>([]);
  const [totalExpenses, setTotalExpenses] = useState(0);

  // جلب المصروفات
  const fetchExpenses = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        query: searchQuery,
      });

      if (categoryFilter !== 'all') {
        queryParams.append('categoryId', categoryFilter);
      }

      if (dateRange.from) {
        queryParams.append('startDate', dateRange.from.toISOString());
      }

      if (dateRange.to) {
        queryParams.append('endDate', dateRange.to.toISOString());
      }

      const response = await fetch(`/api/expenses?${queryParams}`);

      if (!response.ok) {
        throw new Error('فشل في جلب المصروفات');
      }

      const data = await response.json();
      setExpenses(data.expenses);
      setPagination({
        total: data.pagination.total,
        pages: data.pagination.pages,
        page: data.pagination.page,
        limit: data.pagination.limit,
      });

      // تحديث الإحصائيات
      if (data.stats) {
        interface CategoryStat {
          id: number | null;
          name: string;
          color: string;
          amount: number;
          percentage?: number;
        }

        // استخدام البيانات من الخادم مباشرة
        const formattedCategoryStats = (data.stats.categoriesStats || data.stats.categories || []).map((cat: CategoryStat) => {
          // تأكد من أن اسم الفئة موجود وصحيح
          if (!cat.name || cat.name === 'undefined') {
            // محاولة العثور على اسم الفئة من قائمة الفئات المحلية
            const category = categories.find(c => c.id === cat.id);
            if (category && category.name) {
              cat.name = category.name;
            } else {
              cat.name = 'بدون فئة';
            }
          }

          return cat;
        });

        setCategoryStats(formattedCategoryStats);
        setMonthlyStats(data.stats.monthly || []);
        setTotalExpenses(data.stats.totalAmount || 0);
      }
    } catch (error) {
      console.error('خطأ في جلب المصروفات:', error);
      toast.error('فشل في جلب المصروفات');
    } finally {
      setLoading(false);
    }
  };

  // جلب فئات المصروفات
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories');

      if (!response.ok) {
        throw new Error('فشل في جلب فئات المصروفات');
      }

      const data = await response.json();
      setCategories(data.categories);
    } catch (error) {
      console.error('خطأ في جلب فئات المصروفات:', error);
      toast.error('فشل في جلب فئات المصروفات');
    }
  };

  // إضافة مصروف جديد
  const handleAddExpense = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (!formData.purpose || !formData.amount) {
        toast.error('الغرض والمبلغ مطلوبان');
        return;
      }

      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          purpose: formData.purpose,
          amount: parseFloat(formData.amount),
          categoryId: formData.categoryId ? parseInt(formData.categoryId) : null,
          date: formData.date,
          receipt: formData.receipt || null,
          notes: formData.notes || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إضافة المصروف');
      }

      toast.success('تم إضافة المصروف بنجاح');
      setIsAddDialogOpen(false);
      resetForm();
      fetchExpenses();
    } catch (error) {
      console.error('خطأ في إضافة المصروف:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في إضافة المصروف');
    }
  };

  // تعديل مصروف
  const handleEditExpense = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedExpense) return;

    try {
      if (!formData.purpose || !formData.amount) {
        toast.error('الغرض والمبلغ مطلوبان');
        return;
      }

      const response = await fetch(`/api/expenses/${selectedExpense.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          purpose: formData.purpose,
          amount: parseFloat(formData.amount),
          categoryId: formData.categoryId ? parseInt(formData.categoryId) : null,
          date: formData.date,
          receipt: formData.receipt || null,
          notes: formData.notes || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في تعديل المصروف');
      }

      toast.success('تم تعديل المصروف بنجاح');
      setIsEditDialogOpen(false);
      fetchExpenses();
    } catch (error) {
      console.error('خطأ في تعديل المصروف:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في تعديل المصروف');
    }
  };

  // حذف مصروف
  const handleDeleteExpense = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المصروف؟')) return;

    try {
      const response = await fetch(`/api/expenses/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في حذف المصروف');
      }

      toast.success('تم حذف المصروف بنجاح');
      fetchExpenses();
    } catch (error) {
      console.error('خطأ في حذف المصروف:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في حذف المصروف');
    }
  };

  // إعادة تعيين نموذج الإضافة
  const resetForm = () => {
    setFormData({
      purpose: '',
      amount: '',
      categoryId: '',
      date: new Date().toISOString().split('T')[0],
      receipt: '',
      notes: '',
    });
  };

  // تصدير المصروفات إلى Excel
  const handleExportToExcel = () => {
    if (expenses.length === 0) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // تحويل البيانات إلى الشكل المطلوب للتصدير
      const dataToExport = expenses.map((expense) => ({
        'الغرض': expense.purpose,
        'المبلغ': expense.amount,
        'الفئة': expense.category ? expense.category.name : 'بدون فئة',
        'التاريخ': new Date(expense.date).toLocaleDateString('fr-FR'),
        'الملاحظات': expense.notes || '',
        'الإيصال': expense.receipt || ''
      }));

      // تحديد عرض الأعمدة
      const columnWidths = [
        { wch: 25 }, // الغرض
        { wch: 15 }, // المبلغ
        { wch: 15 }, // الفئة
        { wch: 15 }, // التاريخ
        { wch: 30 }, // الملاحظات
        { wch: 20 }  // الإيصال
      ];

      // تصدير البيانات
      const fileName = `المصروفات_${new Date().toISOString().split('T')[0]}.xlsx`;
      exportToExcel(
        dataToExport,
        fileName,
        'المصروفات',
        columnWidths
      );
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تصدير المصروفات إلى PDF
  const handleExportToPdf = () => {
    if (expenses.length === 0) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // تحويل البيانات إلى الشكل المطلوب للتصدير
      const expensesData = expenses.map((expense) => [
        expense.purpose,
        expense.amount.toLocaleString('fr-FR') + ' د.ج',
        expense.category ? expense.category.name : 'بدون فئة',
        new Date(expense.date).toLocaleDateString('fr-FR'),
        expense.notes || ''
      ]);

      // إضافة إحصائيات الفئات إذا كانت متوفرة
      const categoryStatsData = categoryStats.length > 0
        ? categoryStats.map(cat => {
            // حساب النسبة المئوية إذا لم تكن موجودة
            const percentage = cat.percentage !== undefined
              ? cat.percentage
              : (totalExpenses > 0 ? (cat.amount / totalExpenses) * 100 : 0);

            return [
              cat.name,
              cat.amount.toLocaleString('fr-FR') + ' د.ج',
              percentage.toFixed(1) + '%'
            ];
          })
        : [];

      // تصدير البيانات إلى PDF
      const fileName = `المصروفات_${new Date().toISOString().split('T')[0]}.pdf`;

      exportToPdf({
        title: 'تقرير المصروفات',
        fileName: fileName,
        tables: [
          {
            title: 'قائمة المصروفات',
            headers: ['الغرض', 'المبلغ', 'الفئة', 'التاريخ', 'الملاحظات'],
            data: expensesData,
            headStyles: {
              fillColor: [22, 155, 136] as [number, number, number],
              textColor: [255, 255, 255] as [number, number, number]
            }
          },
          ...(categoryStatsData.length > 0 ? [{
            title: 'توزيع المصروفات حسب الفئة',
            headers: ['الفئة', 'المبلغ', 'النسبة'],
            data: categoryStatsData,
            headStyles: {
              fillColor: [22, 155, 136] as [number, number, number],
              textColor: [255, 255, 255] as [number, number, number]
            }
          }] : [])
        ],
        ...(categoryStats.length > 0 ? {
          charts: [
            {
              title: 'توزيع المصروفات حسب الفئة',
              type: 'pie',
              data: {
                labels: categoryStats.map(cat => cat.name),
                datasets: [
                  {
                    label: 'المبلغ',
                    data: categoryStats.map(cat => cat.amount),
                    backgroundColor: [
                      'var(--primary-color)', '#3498db', '#9b59b6', '#e67e22', '#f1c40f', '#1abc9c', '#34495e'
                    ]
                  }
                ]
              }
            }
          ]
        } : {})
      });
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchCategories();
    fetchExpenses();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, categoryFilter, dateRange, pagination.page, pagination.limit]);

  return (
    <OptimizedProtectedRoute requiredPermission="admin.expenses.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-4 md:space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4 md:mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaMoneyBillWave className="text-[var(--primary-color)]" />
          إدارة المصروفات
        </h1>
        <QuickActionButtons
          entityType="expenses"
          actions={[
            {
              key: 'export-excel',
              label: 'تصدير Excel',
              icon: <FaFileExcel />,
              onClick: handleExportToExcel,
              variant: 'success',
              permission: 'admin.reports.export'
            },
            {
              key: 'export-pdf',
              label: 'تصدير PDF',
              icon: <FaFilePdf />,
              onClick: handleExportToPdf,
              variant: 'danger',
              permission: 'admin.reports.export'
            },
            {
              key: 'create',
              label: 'إضافة مصروف جديد',
              icon: <FaPlus />,
              onClick: () => {
                resetForm();
                setIsAddDialogOpen(true);
              },
              variant: 'primary'
            }
          ]}
          className="flex flex-wrap gap-2 w-full md:w-auto"
        />
      </div>

      {/* أزرار التنقل بين صفحات المصروفات */}
      <div className="grid grid-cols-2 gap-3 mb-4 md:mb-6">
      <Link href="/admin/expenses/reminders" className="w-full">
          <Button
            className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center justify-center gap-2"
            variant="default"
          >
            <FaBell />
            <span>تذكيرات المصروفات</span>
          </Button>
        </Link>
        <Link href="/admin/expenses/recurring" className="w-full">
          <Button
            className="w-full bg-white border border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center justify-center gap-2"
            variant="outline"
          >
            <FaSync />
            <span>المصروفات المتكررة</span>
          </Button>
        </Link>
      </div>

      {/* مرشحات البحث */}
      <Card className="bg-white shadow-md border border-[#e0f2ef] mb-4 md:mb-6">
        <CardContent className="pt-4 md:pt-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaSearch className="text-[var(--primary-color)]" />
                <span>بحث</span>
              </label>
              <Input
                placeholder="ابحث عن مصروف..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="text-sm"
              />
            </div>
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaTags className="text-[var(--primary-color)]" />
                <span>الفئة</span>
              </label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="text-sm">
                  <SelectValue placeholder="جميع الفئات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الفئات</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1 md:space-y-2">
              <label className="text-xs md:text-sm font-medium flex items-center gap-1">
                <FaCalendarAlt className="text-[var(--primary-color)]" />
                <span>الفترة الزمنية</span>
              </label>
              <div className="flex gap-2">
                <Input
                  type="date"
                  value={dateRange.from ? dateRange.from.toISOString().split('T')[0] : ''}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : undefined;
                    setDateRange({ ...dateRange, from: date });
                  }}
                  className="flex-1 text-sm"
                  placeholder="من تاريخ"
                />
                <Input
                  type="date"
                  value={dateRange.to ? dateRange.to.toISOString().split('T')[0] : ''}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : undefined;
                    setDateRange({ ...dateRange, to: date });
                  }}
                  className="flex-1 text-sm"
                  placeholder="إلى تاريخ"
                />
              </div>
            </div>
            <div className="flex items-end">
              <Button
                onClick={fetchExpenses}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full text-xs md:text-sm"
              >
                تحديث البيانات
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* لوحة الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 mb-4 md:mb-6">
        <Card className="bg-white shadow-md border border-[#e0f2ef]">
          <CardHeader className="pb-1 md:pb-2 pt-3 md:pt-4">
            <CardTitle className="text-base md:text-lg flex items-center gap-2">
              <FaMoneyBillWave className="text-[var(--primary-color)]" />
              <span>إجمالي المصروفات</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl md:text-3xl font-bold text-[var(--primary-color)]">
              {(totalExpenses || 0).toLocaleString('fr-FR')} د.ج
            </div>
            <p className="text-xs md:text-sm text-gray-500 mt-1">
              {dateRange.from && dateRange.to
                ? `من ${dateRange.from.toLocaleDateString('fr-FR')} إلى ${dateRange.to.toLocaleDateString('fr-FR')}`
                : 'الفترة الحالية'}
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-md border border-[#e0f2ef]">
          <CardHeader className="pb-1 md:pb-2 pt-3 md:pt-4">
            <CardTitle className="text-base md:text-lg flex items-center gap-2">
              <FaChartPie className="text-[var(--primary-color)]" />
              <span>توزيع المصروفات</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {categoryStats.length > 0 ? (
              <div className="h-[180px] md:h-[200px]">
                <PieChart
                  data={{
                    labels: categoryStats.map(cat => cat.name),
                    datasets: [
                      {
                        label: 'المبلغ',
                        data: categoryStats.map(cat => cat.amount),
                        backgroundColor: [
                          'var(--primary-color)',
                          '#3498db',
                          '#9b59b6',
                          '#e67e22',
                          '#f1c40f',
                          '#1abc9c',
                          '#34495e',
                        ],
                      },
                    ],
                  }}
                  height={180}
                  width={300}
                  options={{
                    plugins: {
                      legend: {
                        position: 'bottom',
                        labels: {
                          font: {
                            size: 12,
                            family: 'Tajawal, sans-serif',
                          },
                          color: '#333',
                        },
                      },
                      tooltip: {
                        titleFont: {
                          family: 'Tajawal, sans-serif',
                        },
                        bodyFont: {
                          family: 'Tajawal, sans-serif',
                        }
                      }
                    },
                    interaction: {
                      mode: 'index'
                    },
                    parsing: {
                      key: 'value'
                    }
                  }}
                />
              </div>
            ) : (
              <div className="h-[180px] md:h-[200px] flex items-center justify-center">
                <p className="text-gray-500 text-xs md:text-sm">لا توجد بيانات للعرض</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-white shadow-md border border-[#e0f2ef]">
          <CardHeader className="pb-1 md:pb-2 pt-3 md:pt-4">
            <CardTitle className="text-base md:text-lg flex items-center gap-2">
              <FaChartBar className="text-[var(--primary-color)]" />
              <span>المصروفات الشهرية</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {monthlyStats.length > 0 ? (
              <div className="h-[120px] md:h-[150px]">
                <BarChart
                  data={{
                    labels: monthlyStats.map(item => item.month),
                    datasets: [
                      {
                        label: 'المبلغ',
                        data: monthlyStats.map(item => item.amount),
                        backgroundColor: 'var(--primary-color)',
                      },
                    ],
                  }}
                />
              </div>
            ) : (
              <div className="h-[120px] md:h-[150px] flex items-center justify-center">
                <p className="text-gray-500 text-xs md:text-sm">لا توجد بيانات للعرض</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* المصروفات الأخيرة */}
      <Card className="bg-white shadow-md border border-[#e0f2ef] mb-4 md:mb-6">
        <CardHeader className="pb-1 md:pb-2 pt-3 md:pt-4">
          <CardTitle className="text-base md:text-lg flex items-center gap-2">
            <FaHistory className="text-[var(--primary-color)]" />
            <span>المصروفات الأخيرة</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">
              <p className="text-gray-500 text-xs md:text-sm">جاري تحميل البيانات...</p>
            </div>
          ) : expenses.length === 0 ? (
            <div className="text-center py-4 md:py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <p className="text-gray-500 mb-3 md:mb-4 text-xs md:text-sm">لم يتم العثور على أي مصروفات</p>
              <Button
                onClick={() => {
                  resetForm();
                  setIsAddDialogOpen(true);
                }}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white text-xs md:text-sm"
              >
                <FaPlus className="ml-1 md:ml-2" size={12} />
                إضافة مصروف جديد
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs md:text-sm">الغرض</TableHead>
                    <TableHead className="text-xs md:text-sm">المبلغ</TableHead>
                    <TableHead className="text-xs md:text-sm">الفئة</TableHead>
                    <TableHead className="text-xs md:text-sm">التاريخ</TableHead>
                    <TableHead className="text-xs md:text-sm">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {expenses.map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell className="font-medium text-xs md:text-sm">{expense.purpose}</TableCell>
                      <TableCell className="text-xs md:text-sm">{expense.amount.toLocaleString('fr-FR')} د.ج</TableCell>
                      <TableCell className="text-xs md:text-sm">
                        {expense.category ? (
                          <div className="flex items-center gap-1">
                            <div
                              className="w-2 h-2 md:w-3 md:h-3 rounded-full"
                              style={{ backgroundColor: expense.category.color || 'var(--primary-color)' }}
                            ></div>
                            <span>{expense.category.name}</span>
                          </div>
                        ) : (
                          'بدون فئة'
                        )}
                      </TableCell>
                      <TableCell className="text-xs md:text-sm">{new Date(expense.date).toLocaleDateString('fr-FR')}</TableCell>
                      <TableCell>
                        <OptimizedActionButtonGroup
                          entityType="expenses"
                          onEdit={() => {
                            setFormData({
                              purpose: expense.purpose,
                              amount: expense.amount.toString(),
                              categoryId: expense.categoryId ? expense.categoryId.toString() : '',
                              date: new Date(expense.date).toISOString().split('T')[0],
                              receipt: expense.receipt || '',
                              notes: expense.notes || '',
                            });
                            setSelectedExpense(expense);
                            setIsEditDialogOpen(true);
                          }}
                          onDelete={() => handleDeleteExpense(expense.id)}
                          showEdit={true}
                          showDelete={true}
                          size="sm"
                          className="gap-1 md:gap-2"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* نافذة إضافة مصروف جديد */}
      <AnimatedDialog
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        title="إضافة مصروف جديد"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="add-expense-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaPlus size={14} />
            <span>إضافة</span>
          </Button>
        }
      >
        <form id="add-expense-form" onSubmit={handleAddExpense} className="space-y-4 p-4">
          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="purpose" className="text-right col-span-1">
              الغرض <span className="text-red-500">*</span>
            </Label>
            <Input
              id="purpose"
              value={formData.purpose}
              onChange={(e) => setFormData({ ...formData, purpose: e.target.value })}
              className="col-span-3"
              required
            />
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="amount" className="text-right col-span-1">
              المبلغ <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="amount"
                type="number"
                min="0.01"
                step="0.01"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="flex-1"
                required
              />
              <span className="text-gray-500">د.ج</span>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="categoryId" className="text-right col-span-1">
              الفئة
            </Label>
            <div className="col-span-3">
              <Select
                value={formData.categoryId}
                onValueChange={(value) => setFormData({ ...formData, categoryId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر فئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="date" className="text-right col-span-1">
              التاريخ
            </Label>
            <Input
              id="date"
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="receipt" className="text-right col-span-1">
              الإيصال
            </Label>
            <Input
              id="receipt"
              type="url"
              value={formData.receipt}
              onChange={(e) => setFormData({ ...formData, receipt: e.target.value })}
              placeholder="رابط صورة الإيصال"
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 gap-4 items-start">
            <Label htmlFor="notes" className="text-right col-span-1 mt-2">
              ملاحظات
            </Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="أدخل أي ملاحظات إضافية"
              className="col-span-3 min-h-[100px]"
            />
          </div>
        </form>
      </AnimatedDialog>

      {/* نافذة تعديل مصروف */}
      <AnimatedDialog
        isOpen={isEditDialogOpen}
        onClose={() => setIsEditDialogOpen(false)}
        title="تعديل مصروف"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="edit-expense-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaSave size={14} />
            <span>حفظ التغييرات</span>
          </Button>
        }
      >
        <form id="edit-expense-form" onSubmit={handleEditExpense} className="space-y-4 p-4">
          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="edit-purpose" className="text-right col-span-1">
              الغرض <span className="text-red-500">*</span>
            </Label>
            <Input
              id="edit-purpose"
              value={formData.purpose}
              onChange={(e) => setFormData({ ...formData, purpose: e.target.value })}
              className="col-span-3"
              required
            />
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="edit-amount" className="text-right col-span-1">
              المبلغ <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3 flex items-center gap-2">
              <Input
                id="edit-amount"
                type="number"
                min="0.01"
                step="0.01"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="flex-1"
                required
              />
              <span className="text-gray-500">د.ج</span>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="edit-categoryId" className="text-right col-span-1">
              الفئة
            </Label>
            <div className="col-span-3">
              <Select
                value={formData.categoryId}
                onValueChange={(value) => setFormData({ ...formData, categoryId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر فئة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">بدون فئة</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="edit-date" className="text-right col-span-1">
              التاريخ
            </Label>
            <Input
              id="edit-date"
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <Label htmlFor="edit-receipt" className="text-right col-span-1">
              الإيصال
            </Label>
            <Input
              id="edit-receipt"
              type="url"
              value={formData.receipt}
              onChange={(e) => setFormData({ ...formData, receipt: e.target.value })}
              placeholder="رابط صورة الإيصال"
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 gap-4 items-start">
            <Label htmlFor="edit-notes" className="text-right col-span-1 mt-2">
              ملاحظات
            </Label>
            <Textarea
              id="edit-notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="أدخل أي ملاحظات إضافية"
              className="col-span-3 min-h-[100px]"
            />
          </div>
        </form>
      </AnimatedDialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

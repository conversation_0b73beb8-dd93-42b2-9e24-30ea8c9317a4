"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

type Guardian = {
  id: number;
  name: string;
  phone: string;
};

type Class = {
  id: number;
  name: string;
  teacherId: number;
};

type StudentFormData = {
  username: string;
  name: string;
  age: number;
  phone?: string;
  guardianId?: number;
  classeId?: number;
};

export default function AddStudentPage() {
  const router = useRouter();
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [formData, setFormData] = useState<StudentFormData>({
    username: '',
    name: '',
    age: 0,
    phone: '',
    guardianId: undefined,
    classeId: undefined
  });

  useEffect(() => {
    const fetchGuardians = async () => {
      try {
        const response = await fetch('/api/parents');
        if (!response.ok) throw new Error('Failed to fetch guardians');
        const data = await response.json();
        setGuardians(data || []);
      } catch {
        toast.error('حدث خطأ أثناء تحميل بيانات الأولياء');
      }
    };

    const fetchClasses = async () => {
      try {
        const response = await fetch('/api/classes?includeStudents=false&includeSubjects=false');
        if (!response.ok) throw new Error('Failed to fetch classes');
        const data = await response.json();
        setClasses(data || []);
      } catch  {
        toast.error('حدث خطأ أثناء تحميل بيانات الأقسام');
      }
    };

    fetchGuardians();
    fetchClasses();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to save student');

      toast.success('تم إضافة التلميذ بنجاح');
      router.push('/admin/students');
    } catch  {
      toast.error('حدث خطأ أثناء حفظ بيانات التلميذ');
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.students.add">
      <div className="container mx-auto py-6 space-y-4 rtl">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-right">إضافة تلميذ جديد</h1>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-right block">اسم المستخدم</label>
            <Input
              value={formData.username}
              onChange={(e) => setFormData({ ...formData, username: e.target.value })}
              required
              dir="rtl"
              className="text-right w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-right block">الاسم الكامل</label>
            <Input
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
              dir="rtl"
              className="text-right w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-right block">العمر</label>
            <Input
              type="number"
              value={formData.age}
              onChange={(e) => setFormData({ ...formData, age: parseInt(e.target.value) })}
              required
              dir="rtl"
              className="text-right w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-right block">رقم الهاتف</label>
            <Input
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              dir="rtl"
              className="text-right w-full"
            />
          </div>

          <div className="space-y-2">
            <label className="text-right block">القسم</label>
            <Select
              value={formData.classeId?.toString() || ''}
              onValueChange={(value) => setFormData({ ...formData, classeId: parseInt(value) || undefined })}
            >
              <SelectTrigger className="w-full text-right">
                <SelectValue placeholder="اختر القسم" />
              </SelectTrigger>
              <SelectContent>
                {classes.map((classe) => (
                  <SelectItem key={classe.id} value={classe.id.toString()}>
                    {classe.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-right block">الولي</label>
            <Select
              value={formData.guardianId?.toString() || ''}
              onValueChange={(value) => setFormData({ ...formData, guardianId: parseInt(value) || undefined })}
            >
              <SelectTrigger className="w-full text-right">
                <SelectValue placeholder="اختر الولي" />
              </SelectTrigger>
              <SelectContent>
                {guardians.map((guardian) => (
                  <SelectItem key={guardian.id} value={guardian.id.toString()}>
                    {guardian.name} - {guardian.phone}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-end space-x-4 space-x-reverse">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/admin/students')}
            >
              إلغاء
            </Button>
            <Button type="submit">حفظ</Button>
          </div>
        </form>
      </div>
      </div>
    </ProtectedRoute>
  );
}
# دليل ترحيل قاعدة البيانات - نظام الإشعارات المحسن

## نظرة عامة
يوضح هذا الدليل خطوات ترحيل قاعدة البيانات لدعم نظام الإشعارات المحسن مع الحفاظ على البيانات الموجودة.

## خطوات الترحيل

### الخطوة 1: إنشاء Migration جديد
```bash
npx prisma migrate dev --name add_enhanced_notifications
```

### الخطوة 2: التحقق من Migration المُنشأ
سيتم إنشاء ملف migration جديد في مجلد `prisma/migrations/` يحتوي على:

#### إضافة التعدادات الجديدة
```sql
-- CreateEnum
CREATE TYPE "GroupType" AS ENUM ('ALL_USERS', 'BY_ROLE', 'CUSTOM_SELECTION', 'PREDEFINED_GROUP');
```

#### إنشاء الجداول الجديدة
```sql
-- CreateTable
CREATE TABLE "NotificationGroup" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" "GroupType" NOT NULL,
    "targetRole" "UserRole",
    "targetUserIds" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" INTEGER NOT NULL,

    CONSTRAINT "NotificationGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NotificationRecipient" (
    "id" SERIAL NOT NULL,
    "notificationId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "groupId" INTEGER,
    "delivered" BOOLEAN NOT NULL DEFAULT false,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "deliveredAt" TIMESTAMP(3),
    "readAt" TIMESTAMP(3),
    "deliveryMethod" TEXT,
    "errorMessage" TEXT,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "NotificationRecipient_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NotificationTemplate" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "type" "NotificationType" NOT NULL DEFAULT 'GENERAL',
    "variables" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "defaultGroupType" "GroupType",
    "defaultTargetRole" "UserRole",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" INTEGER NOT NULL,

    CONSTRAINT "NotificationTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NotificationStats" (
    "id" SERIAL NOT NULL,
    "notificationId" INTEGER NOT NULL,
    "totalRecipients" INTEGER NOT NULL,
    "deliveredCount" INTEGER NOT NULL DEFAULT 0,
    "failedCount" INTEGER NOT NULL DEFAULT 0,
    "pendingCount" INTEGER NOT NULL DEFAULT 0,
    "readCount" INTEGER NOT NULL DEFAULT 0,
    "unreadCount" INTEGER NOT NULL DEFAULT 0,
    "deliveryRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "readRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "engagementRate" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "firstDeliveredAt" TIMESTAMP(3),
    "lastDeliveredAt" TIMESTAMP(3),
    "firstReadAt" TIMESTAMP(3),
    "lastReadAt" TIMESTAMP(3),
    "calculatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "NotificationStats_pkey" PRIMARY KEY ("id")
);
```

#### تحديث جدول الإشعارات الموجود
```sql
-- AlterTable
ALTER TABLE "Notification" 
ADD COLUMN "groupId" INTEGER,
ADD COLUMN "isGroupNotification" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN "templateId" INTEGER,
ADD COLUMN "priority" TEXT NOT NULL DEFAULT 'MEDIUM',
ADD COLUMN "scheduledAt" TIMESTAMP(3),
ADD COLUMN "sentAt" TIMESTAMP(3),
ADD COLUMN "status" TEXT NOT NULL DEFAULT 'PENDING',
ALTER COLUMN "userId" DROP NOT NULL;
```

#### إضافة الفهارس
```sql
-- CreateIndex
CREATE INDEX "NotificationGroup_type_idx" ON "NotificationGroup"("type");
CREATE INDEX "NotificationGroup_targetRole_idx" ON "NotificationGroup"("targetRole");
CREATE INDEX "NotificationGroup_createdBy_idx" ON "NotificationGroup"("createdBy");

CREATE UNIQUE INDEX "NotificationRecipient_notificationId_userId_key" ON "NotificationRecipient"("notificationId", "userId");
CREATE INDEX "NotificationRecipient_userId_read_idx" ON "NotificationRecipient"("userId", "read");
CREATE INDEX "NotificationRecipient_delivered_idx" ON "NotificationRecipient"("delivered");
CREATE INDEX "NotificationRecipient_groupId_idx" ON "NotificationRecipient"("groupId");

CREATE UNIQUE INDEX "NotificationTemplate_name_key" ON "NotificationTemplate"("name");
CREATE INDEX "NotificationTemplate_type_idx" ON "NotificationTemplate"("type");
CREATE INDEX "NotificationTemplate_isActive_idx" ON "NotificationTemplate"("isActive");
CREATE INDEX "NotificationTemplate_createdBy_idx" ON "NotificationTemplate"("createdBy");

CREATE UNIQUE INDEX "NotificationStats_notificationId_key" ON "NotificationStats"("notificationId");
CREATE INDEX "NotificationStats_deliveryRate_idx" ON "NotificationStats"("deliveryRate");
CREATE INDEX "NotificationStats_readRate_idx" ON "NotificationStats"("readRate");
CREATE INDEX "NotificationStats_calculatedAt_idx" ON "NotificationStats"("calculatedAt");

CREATE INDEX "Notification_groupId_idx" ON "Notification"("groupId");
CREATE INDEX "Notification_isGroupNotification_idx" ON "Notification"("isGroupNotification");
CREATE INDEX "Notification_status_idx" ON "Notification"("status");
CREATE INDEX "Notification_scheduledAt_idx" ON "Notification"("scheduledAt");
```

#### إضافة المفاتيح الخارجية
```sql
-- AddForeignKey
ALTER TABLE "NotificationGroup" ADD CONSTRAINT "NotificationGroup_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "NotificationGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES "NotificationTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationRecipient" ADD CONSTRAINT "NotificationRecipient_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "Notification"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationRecipient" ADD CONSTRAINT "NotificationRecipient_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationRecipient" ADD CONSTRAINT "NotificationRecipient_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "NotificationGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationTemplate" ADD CONSTRAINT "NotificationTemplate_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NotificationStats" ADD CONSTRAINT "NotificationStats_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "Notification"("id") ON DELETE CASCADE ON UPDATE CASCADE;
```

### الخطوة 3: ترحيل البيانات الموجودة
بعد تطبيق Migration، نحتاج لترحيل الإشعارات الموجودة:

```sql
-- إنشاء سجلات NotificationRecipient للإشعارات الموجودة
INSERT INTO "NotificationRecipient" (
    "notificationId", 
    "userId", 
    "delivered", 
    "read", 
    "deliveredAt", 
    "readAt",
    "deliveryMethod",
    "createdAt",
    "updatedAt"
)
SELECT 
    n."id" as "notificationId",
    n."userId",
    true as "delivered", -- نفترض أن الإشعارات الموجودة تم تسليمها
    n."read",
    n."createdAt" as "deliveredAt",
    CASE WHEN n."read" = true THEN n."createdAt" ELSE NULL END as "readAt",
    'system' as "deliveryMethod",
    n."createdAt",
    n."createdAt" as "updatedAt"
FROM "Notification" n
WHERE n."userId" IS NOT NULL;

-- إنشاء إحصائيات للإشعارات الموجودة
INSERT INTO "NotificationStats" (
    "notificationId",
    "totalRecipients",
    "deliveredCount",
    "readCount",
    "unreadCount",
    "deliveryRate",
    "readRate",
    "calculatedAt",
    "updatedAt"
)
SELECT 
    n."id" as "notificationId",
    1 as "totalRecipients",
    1 as "deliveredCount",
    CASE WHEN n."read" = true THEN 1 ELSE 0 END as "readCount",
    CASE WHEN n."read" = false THEN 1 ELSE 0 END as "unreadCount",
    100.0 as "deliveryRate",
    CASE WHEN n."read" = true THEN 100.0 ELSE 0.0 END as "readRate",
    NOW() as "calculatedAt",
    NOW() as "updatedAt"
FROM "Notification" n
WHERE n."userId" IS NOT NULL;
```

### الخطوة 4: إنشاء قوالب افتراضية
```sql
-- إدراج قوالب إشعارات افتراضية
INSERT INTO "NotificationTemplate" (
    "name", 
    "title", 
    "content", 
    "type", 
    "defaultGroupType", 
    "defaultTargetRole",
    "createdBy",
    "createdAt",
    "updatedAt"
) VALUES 
(
    'welcome_all_users',
    'مرحباً بكم في النظام',
    'نرحب بجميع المستخدمين في النظام الجديد. نتمنى لكم تجربة مفيدة وممتعة.',
    'GENERAL',
    'ALL_USERS',
    NULL,
    1, -- معرف المدير الأول
    NOW(),
    NOW()
),
(
    'teacher_meeting',
    'اجتماع هيئة التدريس',
    'يرجى حضور اجتماع هيئة التدريس المقرر يوم {date} في تمام الساعة {time}.',
    'GENERAL',
    'BY_ROLE',
    'TEACHER',
    1,
    NOW(),
    NOW()
),
(
    'exam_reminder',
    'تذكير بموعد الامتحان',
    'تذكير: امتحان {subject} مقرر يوم {date} في تمام الساعة {time}.',
    'EXAM',
    'BY_ROLE',
    'STUDENT',
    1,
    NOW(),
    NOW()
),
(
    'payment_reminder',
    'تذكير بالمدفوعات المستحقة',
    'تذكير لأولياء الأمور: يوجد مدفوعات مستحقة بقيمة {amount} دج للطالب {student_name}.',
    'PAYMENT',
    'BY_ROLE',
    'PARENT',
    1,
    NOW(),
    NOW()
);
```

### الخطوة 5: التحقق من نجاح الترحيل
```sql
-- التحقق من عدد الإشعارات المرحلة
SELECT 
    COUNT(*) as total_notifications,
    COUNT(CASE WHEN "isGroupNotification" = true THEN 1 END) as group_notifications,
    COUNT(CASE WHEN "isGroupNotification" = false THEN 1 END) as individual_notifications
FROM "Notification";

-- التحقق من عدد المستلمين
SELECT COUNT(*) as total_recipients FROM "NotificationRecipient";

-- التحقق من القوالب
SELECT COUNT(*) as total_templates FROM "NotificationTemplate";

-- التحقق من الإحصائيات
SELECT COUNT(*) as total_stats FROM "NotificationStats";
```

## ملاحظات مهمة

### 1. النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية قبل الترحيل
pg_dump your_database_name > backup_before_migration.sql
```

### 2. اختبار الترحيل
- اختبر الترحيل على قاعدة بيانات تطوير أولاً
- تأكد من عمل جميع الاستعلامات الموجودة
- اختبر الوظائف الجديدة

### 3. مراقبة الأداء
- راقب أداء الاستعلامات بعد الترحيل
- تأكد من فعالية الفهارس الجديدة
- قم بتحليل خطط التنفيذ للاستعلامات المعقدة

### 4. التراجع عن الترحيل
في حالة الحاجة للتراجع:
```bash
# استعادة النسخة الاحتياطية
psql your_database_name < backup_before_migration.sql
```

## الخطوات التالية
بعد نجاح الترحيل:
1. تحديث كود التطبيق لاستخدام النظام الجديد
2. اختبار جميع وظائف الإشعارات
3. تدريب المستخدمين على الميزات الجديدة
4. مراقبة الأداء والاستخدام

{"name": "04", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev ", "build": "next build", "start": "next start", "lint": "next lint", "find-colors": "node scripts/find-green-colors.js", "seed:basic": "ts-node --project prisma/tsconfig-seed.json prisma/seeders/basic-data.ts", "seed:exams": "ts-node --project prisma/tsconfig-seed.json prisma/seed-quranic-exams.ts", "seed:complete": "ts-node --project prisma/tsconfig-seed.json prisma/seed-complete.ts", "seed:simple": "ts-node --project prisma/tsconfig-seed.json prisma/seed-simple.ts", "seed:quranic": "npm run seed:simple"}, "dependencies": {"@excalidraw/excalidraw": "^0.17.6", "@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^18.0.1", "@mediapipe/selfie_segmentation": "^0.1.1675465747", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@reactour/mask": "^1.2.0", "@reactour/popover": "^1.3.0", "@reactour/tour": "^3.8.0", "@reactour/utils": "^0.6.0", "@sentry/nextjs": "^9.16.1", "@types/bcrypt": "^5.0.2", "@types/chart.js": "^2.9.41", "@types/react-joyride": "^2.0.2", "@types/uuid": "^10.0.0", "antd": "^5.24.3", "apexcharts": "^4.7.0", "axios": "^1.8.1", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-node-canvas": "^5.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cookie": "^1.0.2", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "docx": "^9.5.1", "fabric": "^6.6.4", "file-saver": "^2.0.5", "flowbite": "^3.1.2", "howler": "^2.2.4", "html-docx-js": "^0.3.1", "html2canvas": "^1.4.1", "jose": "^6.0.8", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lucide-react": "^0.477.0", "mysql2": "^3.14.0", "next": "^15.2.4", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "paper": "^0.12.18", "peerjs": "^1.5.4", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.6.2", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-joyride": "^2.9.3", "react-router-dom": "^7.4.0", "react-to-print": "^3.0.5", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "simple-peer": "^9.11.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.2.0", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/axios": "^0.9.36", "@types/bcryptjs": "^2.4.6", "@types/cookie": "^0.6.0", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/node": "^20.17.22", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.7", "jest": "^29.7.0", "postcss": "^8", "prisma": "^6.5.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "prisma": {"seed": "ts-node --project prisma/tsconfig-seed.json prisma/seed.ts"}}
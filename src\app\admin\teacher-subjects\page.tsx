'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';

interface Teacher {
  id: number;
  name: string;
}

interface Subject {
  id: number;
  name: string;
}

interface TeacherSubject {
  id: number;
  teacher: Teacher;
  subject: Subject;
}

export default function TeacherSubjectsPage() {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [teacherSubjects, setTeacherSubjects] = useState<TeacherSubject[]>([]);
  const [selectedTeacher, setSelectedTeacher] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [loading, setLoading] = useState(false);
  const { addToast: toast } = useToast();

  useEffect(() => {
    fetchTeachers();
    fetchSubjects();
    fetchTeacherSubjects();
  }, []);

  const fetchTeachers = async () => {
    try {
      const response = await fetch('/api/admin/teachers');
      if (response.ok) {
        const data = await response.json();
        setTeachers(data);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
    }
  };

  const fetchSubjects = async () => {
    try {
      const response = await fetch('/api/admin/subjects');
      if (response.ok) {
        const data = await response.json();
        setSubjects(data);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const fetchTeacherSubjects = async () => {
    try {
      const response = await fetch('/api/admin/teacher-subjects');
      if (response.ok) {
        const data = await response.json();
        setTeacherSubjects(data);
      }
    } catch (error) {
      console.error('Error fetching teacher subjects:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTeacher || !selectedSubject) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى اختيار المعلم والمادة',
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/teacher-subjects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          teacherId: parseInt(selectedTeacher),
          subjectId: parseInt(selectedSubject),
        }),
      });

      const data = await response.json();
      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم ربط المعلم بالمادة بنجاح',
        });
        setSelectedTeacher('');
        setSelectedSubject('');
        fetchTeacherSubjects();
      } else {
        toast({
          variant: 'destructive',
          title: 'خطأ',
          description: data.message,
        });
      }
    } catch (error) {
      console.error('Error creating teacher subject:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'حدث خطأ أثناء ربط المعلم بالمادة',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الربط؟')) return;

    try {
      const response = await fetch(`/api/admin/teacher-subjects?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم حذف الربط بنجاح',
        });
        fetchTeacherSubjects();
      } else {
        toast({
          variant: 'destructive',
          title: 'خطأ',
          description: data.message,
        });
      }
    } catch (error) {
      console.error('Error deleting teacher subject:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'حدث خطأ أثناء حذف الربط',
      });
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.teacher-subjects.view">
      <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-2xl font-bold">ربط المعلمين بالمواد</h1>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Select value={selectedTeacher} onValueChange={setSelectedTeacher}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="اختر المعلم" />
            </SelectTrigger>
            <SelectContent>
              {teachers.map((teacher) => (
                <SelectItem key={teacher.id} value={teacher.id.toString()}>
                  {teacher.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="اختر المادة" />
            </SelectTrigger>
            <SelectContent>
              {subjects.map((subject) => (
                <SelectItem key={subject.id} value={subject.id.toString()}>
                  {subject.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <PermissionGuard requiredPermission="admin.teacher-subjects.create">
          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? 'جاري الربط...' : 'ربط المعلم بالمادة'}
          </Button>
        </PermissionGuard>
      </form>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>المعلم</TableHead>
              <TableHead>المادة</TableHead>
              <TableHead>الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {teacherSubjects.map((ts) => (
              <TableRow key={ts.id}>
                <TableCell>{ts.teacher.name}</TableCell>
                <TableCell>{ts.subject.name}</TableCell>
                <TableCell>
                  <PermissionGuard requiredPermission="admin.teacher-subjects.delete">
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(ts.id)}
                    >
                      حذف
                    </Button>
                  </PermissionGuard>
                </TableCell>
              </TableRow>
            ))}
            {teacherSubjects.length === 0 && (
              <TableRow>
                <TableCell colSpan={3} className="text-center">
                  لا يوجد روابط بين المعلمين والمواد
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      </div>
    </ProtectedRoute>
  );
}
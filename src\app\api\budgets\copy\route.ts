import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/budgets/copy - نسخ ميزانية موجودة
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { budgetId, newName, newStartDate, newEndDate, copyItems } = body;

    if (!budgetId || !newName || !newStartDate || !newEndDate) {
      return NextResponse.json(
        { error: 'جميع الحقول الأساسية مطلوبة (معرف الميزانية، الاسم الجديد، تاريخ البداية الجديد، تاريخ النهاية الجديد)' },
        { status: 400 }
      );
    }

    // التحقق من صحة التواريخ
    const startDate = new Date(newStartDate);
    const endDate = new Date(newEndDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'تنسيق التاريخ غير صحيح' },
        { status: 400 }
      );
    }

    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'يجب أن يكون تاريخ البداية قبل تاريخ النهاية' },
        { status: 400 }
      );
    }

    // البحث عن الميزانية المصدر
    const sourceBudget = await prisma.budget.findUnique({
      where: { id: parseInt(budgetId) },
      include: {
        items: true,
      },
    });

    if (!sourceBudget) {
      return NextResponse.json(
        { error: 'الميزانية المصدر غير موجودة' },
        { status: 404 }
      );
    }

    // إنشاء الميزانية الجديدة وبنودها في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء الميزانية الجديدة
      const newBudget = await tx.budget.create({
        data: {
          name: newName,
          description: `نسخة من: ${sourceBudget.name}`,
          startDate,
          endDate,
          totalAmount: sourceBudget.totalAmount,
          status: 'DRAFT', // تبدأ كمسودة
        },
      });

      // نسخ بنود الميزانية إذا تم طلب ذلك
      if (copyItems) {
        for (const item of sourceBudget.items) {
          await tx.budgetItem.create({
            data: {
              budgetId: newBudget.id,
              categoryId: item.categoryId,
              amount: item.amount,
              notes: item.notes,
            },
          });
        }
      }

      return newBudget;
    });

    return NextResponse.json({
      success: true,
      message: `تم نسخ الميزانية بنجاح`,
      budget: result,
    });
  } catch (error) {
    console.error('خطأ في نسخ الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في نسخ الميزانية' },
      { status: 500 }
    );
  }
}

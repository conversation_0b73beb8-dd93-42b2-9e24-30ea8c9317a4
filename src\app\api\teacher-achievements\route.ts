import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";
import { UserRole } from "@prisma/client";

// GET: جلب إنجازات المعلمين
export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // استخراج المعلمات من URL
    const { searchParams } = new URL(request.url);
    const teacherId = searchParams.get('teacherId');
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // بناء شروط البحث
    const where: {
      teacherId?: number;
      type?: string;
      achievementDate?: {
        gte?: Date;
        lte?: Date;
      };
    } = {};

    if (teacherId) {
      where.teacherId = parseInt(teacherId);
    }

    if (type) {
      where.type = type;
    }

    if (startDate && endDate) {
      where.achievementDate = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    } else if (startDate) {
      where.achievementDate = {
        gte: new Date(startDate)
      };
    } else if (endDate) {
      where.achievementDate = {
        lte: new Date(endDate)
      };
    }

    // إذا كان المستخدم معلمًا، يجب أن يرى فقط إنجازاته
    if (userData.role === UserRole.TEACHER) {
      const teacher = await prisma.teacher.findUnique({
        where: { userId: userData.id }
      });

      if (teacher) {
        where.teacherId = teacher.id;
      } else {
        return NextResponse.json(
          { message: "لم يتم العثور على بيانات المعلم" },
          { status: 404 }
        );
      }
    }

    // جلب إجمالي عدد الإنجازات
    const total = await prisma.teacherAchievement.count({ where });

    // جلب الإنجازات
    const achievements = await prisma.teacherAchievement.findMany({
      where,
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            specialization: true,
            user: {
              select: {
                username: true
              }
            }
          }
        }
      },
      orderBy: {
        achievementDate: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    return NextResponse.json({
      data: achievements,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching teacher achievements:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب إنجازات المعلمين" },
      { status: 500 }
    );
  }
}

// POST: إنشاء إنجاز جديد للمعلم
export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.EMPLOYEE)) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو موظف" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.teacherId || !body.title || !body.description || !body.type) {
      return NextResponse.json(
        { message: "البيانات غير مكتملة" },
        { status: 400 }
      );
    }

    // إنشاء الإنجاز
    const achievement = await prisma.teacherAchievement.create({
      data: {
        teacherId: parseInt(body.teacherId),
        title: body.title,
        description: body.description,
        achievementDate: body.achievementDate ? new Date(body.achievementDate) : new Date(),
        type: body.type,
        attachmentUrl: body.attachmentUrl || null
      }
    });

    // إنشاء إشعار للمعلم
    const teacher = await prisma.teacher.findUnique({
      where: { id: parseInt(body.teacherId) },
      include: {
        user: true
      }
    });

    if (teacher && teacher.user) {
      await prisma.notification.create({
        data: {
          title: "إنجاز جديد",
          content: `تم تسجيل إنجاز جديد لك: ${body.title}`,
          type: "ACHIEVEMENT",
          userId: teacher.user.id
        }
      });
    }

    return NextResponse.json(achievement, { status: 201 });
  } catch (error) {
    console.error('Error creating teacher achievement:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء إنجاز المعلم" },
      { status: 500 }
    );
  }
}

// PUT: تحديث إنجاز المعلم
export async function PUT(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.EMPLOYEE)) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو موظف" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.id) {
      return NextResponse.json(
        { message: "معرف الإنجاز مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الإنجاز
    const existingAchievement = await prisma.teacherAchievement.findUnique({
      where: { id: parseInt(body.id) }
    });

    if (!existingAchievement) {
      return NextResponse.json(
        { message: "الإنجاز غير موجود" },
        { status: 404 }
      );
    }

    // تحديث الإنجاز
    const updatedAchievement = await prisma.teacherAchievement.update({
      where: { id: parseInt(body.id) },
      data: {
        title: body.title || undefined,
        description: body.description || undefined,
        achievementDate: body.achievementDate ? new Date(body.achievementDate) : undefined,
        type: body.type || undefined,
        attachmentUrl: body.attachmentUrl !== undefined ? body.attachmentUrl : undefined
      }
    });

    return NextResponse.json(updatedAchievement);
  } catch (error) {
    console.error('Error updating teacher achievement:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث إنجاز المعلم" },
      { status: 500 }
    );
  }
}

// DELETE: حذف إنجاز المعلم
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول" },
        { status: 401 }
      );
    }

    // استخراج معرف الإنجاز من URL
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: "معرف الإنجاز مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الإنجاز
    const existingAchievement = await prisma.teacherAchievement.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingAchievement) {
      return NextResponse.json(
        { message: "الإنجاز غير موجود" },
        { status: 404 }
      );
    }

    // حذف الإنجاز
    await prisma.teacherAchievement.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: "تم حذف الإنجاز بنجاح" });
  } catch (error) {
    console.error('Error deleting teacher achievement:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف إنجاز المعلم" },
      { status: 500 }
    );
  }
}

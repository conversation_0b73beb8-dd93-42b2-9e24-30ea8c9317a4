/*
==============================================================================
    اختبار سريع لمكتبة Praetorian.ring والتطبيقات
    
    الوصف: اختبار سريع للتأكد من عمل المكتبة والتطبيقات بشكل أساسي
    المؤلف: Praetorian Team
==============================================================================
*/

/*
دالة iif
*/
func iif bCondition, vTrueValue, vFalseValue
    if bCondition
        return vTrueValue
    else
        return vFalseValue
    ok

/*
طباعة البانر
*/
func printBanner
    ? ""
    ? "  ____                _             _             "
    ? " |  _ \ _ __ __ _  ___| |_ ___  _ __(_) __ _ _ __   "
    ? " | |_) | '__/ _` |/ _ \ __/ _ \| '__| |/ _` | '_ \  "
    ? " |  __/| | | (_| |  __/ || (_) | |  | | (_| | | | | "
    ? " |_|   |_|  \__,_|\___|\__\___/|_|  |_|\__,_|_| |_| "
    ? ""
    ? "        اختبار سريع للمكتبة والتطبيقات"
    ? "=============================================="

/*
اختبار تحميل المكتبة
*/
func testLibraryLoading
    ? ""
    ? "=== اختبار تحميل مكتبة Praetorian ==="
    
    try
        load "praetorian.ring"
        ? "✓ تم تحميل مكتبة Praetorian بنجاح"
        
        oPraetorian = CreatePraetorian()
        if oPraetorian != NULL
            ? "✓ تم إنشاء مثيل من Praetorian بنجاح"
            return true
        else
            ? "✗ فشل في إنشاء مثيل من Praetorian"
            return false
        ok
    catch
        ? "✗ خطأ في تحميل مكتبة Praetorian: " + cCatchError
        return false
    ok

/*
اختبار الوحدات الأساسية
*/
func testCoreModules oPraetorian
    ? ""
    ? "=== اختبار الوحدات الأساسية ==="
    
    nPassed = 0
    nTotal = 4
    
    # اختبار وحدة الأدوات
    try
        cBase64 = oPraetorian.Utils.base64Encode("test")
        if len(cBase64) > 0
            ? "✓ وحدة الأدوات (Utils) تعمل"
            nPassed++
        else
            ? "✗ وحدة الأدوات (Utils) لا تعمل"
        ok
    catch
        ? "✗ خطأ في وحدة الأدوات: " + cCatchError
    done
    
    # اختبار وحدة التسجيل
    try
        oPraetorian.Logger.info("اختبار التسجيل")
        ? "✓ وحدة التسجيل (Logger) تعمل"
        nPassed++
    catch
        ? "✗ خطأ في وحدة التسجيل: " + cCatchError
    done
    
    # اختبار وحدة الشبكة
    try
        if oPraetorian.Network != NULL
            ? "✓ وحدة الشبكة (Network) متاحة"
            nPassed++
        else
            ? "✗ وحدة الشبكة (Network) غير متاحة"
        ok
    catch
        ? "✗ خطأ في وحدة الشبكة: " + cCatchError
    done
    
    # اختبار وحدة الويب
    try
        if oPraetorian.Web != NULL
            ? "✓ وحدة الويب (Web) متاحة"
            nPassed++
        else
            ? "✗ وحدة الويب (Web) غير متاحة"
        ok
    catch
        ? "✗ خطأ في وحدة الويب: " + cCatchError
    done
    
    ? ""
    ? "نتيجة اختبار الوحدات: " + nPassed + "/" + nTotal
    return nPassed = nTotal

/*
اختبار وجود ملفات التطبيقات
*/
func testApplicationFiles
    ? ""
    ? "=== اختبار ملفات التطبيقات ==="
    
    aRequiredFiles = [
        "applications/ReconDash/ReconDash.ring",
        "applications/ReconDash/config.ring",
        "applications/DirHunter/DirHunter.ring",
        "applications/DirHunter/wordlist.txt",
        "applications/launcher.ring",
        "applications/test_applications.ring",
        "applications/README.md"
    ]
    
    nFound = 0
    for cFile in aRequiredFiles
        if fexists(cFile)
            ? "✓ " + cFile
            nFound++
        else
            ? "✗ " + cFile + " (مفقود)"
        ok
    next
    
    ? ""
    ? "الملفات الموجودة: " + nFound + "/" + len(aRequiredFiles)
    return nFound = len(aRequiredFiles)

/*
اختبار وجود ملفات الأمثلة
*/
func testExampleFiles
    ? ""
    ? "=== اختبار ملفات الأمثلة ==="
    
    aExampleFiles = [
        "examples/basic_scan.ring",
        "examples/web_audit.ring", 
        "examples/ssl_audit.ring"
    ]
    
    nFound = 0
    for cFile in aExampleFiles
        if fexists(cFile)
            ? "✓ " + cFile
            nFound++
        else
            ? "✗ " + cFile + " (مفقود)"
        ok
    next
    
    ? ""
    ? "ملفات الأمثلة الموجودة: " + nFound + "/" + len(aExampleFiles)
    return nFound = len(aExampleFiles)

/*
اختبار المكتبات الخارجية
*/
func testExternalLibraries
    ? ""
    ? "=== اختبار المكتبات الخارجية ==="
    
    aLibraries = [
        ["libui.ring", "ReconDash GUI"],
        ["rogueutil.ring", "DirHunter Colors"],
        ["threads.ring", "Threading Support"],
        ["ringregex.ring", "Regex Support"]
    ]
    
    nAvailable = 0
    for aLib in aLibraries
        cLibName = aLib[1]
        cLibDesc = aLib[2]
        
        try
            load cLibName
            ? "✓ " + cLibName + " (" + cLibDesc + ") - متاح"
            nAvailable++
        catch
            ? "✗ " + cLibName + " (" + cLibDesc + ") - غير متاح"
        ok
    next
    
    ? ""
    ? "المكتبات المتاحة: " + nAvailable + "/" + len(aLibraries)
    return nAvailable

/*
اختبار بسيط للوظائف
*/
func testBasicFunctions oPraetorian
    ? ""
    ? "=== اختبار الوظائف الأساسية ==="
    
    nPassed = 0
    nTotal = 3
    
    # اختبار Base64
    try
        cOriginal = "Hello World"
        cEncoded = oPraetorian.Utils.base64Encode(cOriginal)
        cDecoded = oPraetorian.Utils.base64Decode(cEncoded)
        
        if cDecoded = cOriginal
            ? "✓ تشفير/فك تشفير Base64 يعمل"
            nPassed++
        else
            ? "✗ تشفير/فك تشفير Base64 لا يعمل"
        ok
    catch
        ? "✗ خطأ في اختبار Base64: " + cCatchError
    done
    
    # اختبار URL Encoding
    try
        cURL = "hello world"
        cEncoded = oPraetorian.Utils.urlEncode(cURL)
        if len(cEncoded) > 0 and cEncoded != cURL
            ? "✓ ترميز URL يعمل"
            nPassed++
        else
            ? "✗ ترميز URL لا يعمل"
        ok
    catch
        ? "✗ خطأ في اختبار URL Encoding: " + cCatchError
    done
    
    # اختبار توليد كلمة مرور
    try
        cPassword = oPraetorian.Utils.generatePassword(12)
        if len(cPassword) = 12
            ? "✓ توليد كلمة المرور يعمل"
            nPassed++
        else
            ? "✗ توليد كلمة المرور لا يعمل"
        ok
    catch
        ? "✗ خطأ في اختبار توليد كلمة المرور: " + cCatchError
    done
    
    ? ""
    ? "نتيجة اختبار الوظائف: " + nPassed + "/" + nTotal
    return nPassed = nTotal

/*
الدالة الرئيسية
*/
func main
    printBanner()
    
    # تشغيل الاختبارات
    aResults = []
    
    # اختبار تحميل المكتبة
    bLibraryLoaded = testLibraryLoading()
    add(aResults, bLibraryLoaded)
    
    if bLibraryLoaded
        oPraetorian = CreatePraetorian()
        
        # اختبار الوحدات
        add(aResults, testCoreModules(oPraetorian))
        
        # اختبار الوظائف
        add(aResults, testBasicFunctions(oPraetorian))
    else
        ? "تخطي اختبارات الوحدات والوظائف بسبب فشل تحميل المكتبة"
        add(aResults, false)
        add(aResults, false)
    ok
    
    # اختبار ملفات التطبيقات
    add(aResults, testApplicationFiles())
    
    # اختبار ملفات الأمثلة
    add(aResults, testExampleFiles())
    
    # اختبار المكتبات الخارجية
    nExternalLibs = testExternalLibraries()
    
    # حساب النتائج
    nPassed = 0
    for bResult in aResults
        if bResult
            nPassed++
        ok
    next
    
    # طباعة الملخص
    ? ""
    ? "=============================================="
    ? "ملخص الاختبار السريع"
    ? "=============================================="
    ? "الاختبارات الناجحة: " + nPassed + "/" + len(aResults)
    ? "المكتبات الخارجية المتاحة: " + nExternalLibs + "/4"
    ? ""
    
    if nPassed = len(aResults)
        ? "🎉 جميع الاختبارات الأساسية نجحت!"
        ? ""
        ? "المكتبة جاهزة للاستخدام!"
        ? "يمكنك الآن:"
        ? "- تشغيل الأمثلة من مجلد examples/"
        ? "- استخدام التطبيقات من مجلد applications/"
        ? "- تشغيل run_praetorian.bat للحصول على قائمة شاملة"
    else
        ? "⚠ بعض الاختبارات فشلت"
        ? ""
        ? "يرجى مراجعة الأخطاء أعلاه قبل الاستخدام"
        ? "قد تحتاج إلى:"
        ? "- التأكد من تثبيت Ring بشكل صحيح"
        ? "- فحص وجود جميع ملفات المكتبة"
        ? "- تثبيت المكتبات المطلوبة"
    ok
    
    ? ""
    ? "للحصول على مساعدة مفصلة، راجع README.md"
    ? "=============================================="


import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/khatm-sessions/public
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // جلب مجالس الختم المتاحة للعامة فقط
    const [sessions, total] = await Promise.all([
      prisma.khatmSession.findMany({
        where: {
          OR: [
            { title: { contains: query } },
            { location: { contains: query } },
            { teacher: { name: { contains: query } } },
            { surah: { name: { contains: query } } }
          ],
          // فقط مجالس الختم المتاحة للعامة
          isPublic: true,
        },
        include: {
          teacher: {
            select: {
              name: true
            }
          },
          surah: {
            select: {
              name: true
            }
          },
          _count: {
            select: {
              attendances: true
            }
          }
        },
        orderBy: { date: 'desc' },
        skip,
        take: limit
      }),
      prisma.khatmSession.count({
        where: {
          OR: [
            { title: { contains: query } },
            { location: { contains: query } },
            { teacher: { name: { contains: query } } },
            { surah: { name: { contains: query } } }
          ],
          // فقط مجالس الختم المتاحة للعامة
          isPublic: true,
        }
      })
    ]);

    return NextResponse.json({
      data: sessions,
      total,
      pages: Math.ceil(total / limit)
    });
  } catch (error: unknown) {
    console.error('Error fetching public khatm sessions:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب مجالس الختم' },
      { status: 500 }
    );
  }
}

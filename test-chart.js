// اختبار سريع لـ dynamic import
async function testChart() {
  try {
    const { ChartJSNodeCanvas } = await import('chartjs-node-canvas');
    console.log('✅ تم تحميل ChartJSNodeCanvas بنجاح');
    return true;
  } catch (error) {
    console.error('❌ خطأ في تحميل ChartJSNodeCanvas:', error.message);
    return false;
  }
}

testChart().then(result => {
  console.log('نتيجة الاختبار:', result ? 'نجح' : 'فشل');
  process.exit(result ? 0 : 1);
});

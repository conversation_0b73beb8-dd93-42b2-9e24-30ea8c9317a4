# تحديثات قاعدة البيانات - نظام الإشعارات المحسن

## نظرة عامة
يتطلب النظام المحسن إضافة جداول جديدة وتحديث الجداول الموجودة لدعم الإشعارات الجماعية والمخصصة.

## الجداول الجديدة المطلوبة

### 1. جدول مجموعات الإشعارات (NotificationGroup)
```prisma
model NotificationGroup {
  id              Int      @id @default(autoincrement())
  name            String   // اسم المجموعة
  description     String?  @db.Text // وصف المجموعة
  type            GroupType // نوع المجموعة
  targetRole      UserRole? // الدور المستهدف (للنوع BY_ROLE)
  targetUserIds   String?  @db.Text // معرفات المستخدمين (JSON array للنوع CUSTOM)
  isActive        Boolean  @default(true) // هل المجموعة نشطة
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdBy       Int      // معرف منشئ المجموعة
  creator         User     @relation("CreatedGroups", fields: [createdBy], references: [id])
  
  // العلاقات
  notifications   Notification[] @relation("GroupNotifications")
  recipients      NotificationRecipient[] @relation("GroupRecipients")
  
  @@index([type])
  @@index([targetRole])
  @@index([createdBy])
}
```

### 2. جدول مستلمي الإشعارات (NotificationRecipient)
```prisma
model NotificationRecipient {
  id              Int      @id @default(autoincrement())
  notificationId  Int      // معرف الإشعار
  notification    Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  userId          Int      // معرف المستلم
  user            User     @relation("ReceivedNotifications", fields: [userId], references: [id], onDelete: Cascade)
  groupId         Int?     // معرف المجموعة (اختياري)
  group           NotificationGroup? @relation("GroupRecipients", fields: [groupId], references: [id], onDelete: SetNull)
  
  // حالة التسليم والقراءة
  delivered       Boolean  @default(false) // هل تم التسليم
  read            Boolean  @default(false) // هل تم القراءة
  deliveredAt     DateTime? // وقت التسليم
  readAt          DateTime? // وقت القراءة
  
  // معلومات إضافية
  deliveryMethod  String?  // طريقة التسليم (system, email, sms)
  errorMessage    String?  // رسالة خطأ في حالة فشل التسليم
  retryCount      Int      @default(0) // عدد محاولات الإعادة
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@unique([notificationId, userId]) // منع التكرار
  @@index([userId, read])
  @@index([delivered])
  @@index([groupId])
}
```

### 3. جدول قوالب الإشعارات (NotificationTemplate)
```prisma
model NotificationTemplate {
  id          Int      @id @default(autoincrement())
  name        String   @unique // اسم القالب
  title       String   // عنوان القالب
  content     String   @db.Text // محتوى القالب
  type        NotificationType @default(GENERAL) // نوع الإشعار
  variables   String?  @db.Text // المتغيرات المدعومة (JSON)
  isActive    Boolean  @default(true) // هل القالب نشط
  
  // إعدادات المجموعة الافتراضية
  defaultGroupType    GroupType? // نوع المجموعة الافتراضي
  defaultTargetRole   UserRole?  // الدور المستهدف الافتراضي
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   Int      // معرف منشئ القالب
  creator     User     @relation("CreatedTemplates", fields: [createdBy], references: [id])
  
  @@index([type])
  @@index([isActive])
  @@index([createdBy])
}
```

### 4. جدول إحصائيات الإشعارات (NotificationStats)
```prisma
model NotificationStats {
  id                Int      @id @default(autoincrement())
  notificationId    Int      @unique // معرف الإشعار
  notification      Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  
  // إحصائيات التسليم
  totalRecipients   Int      // إجمالي المستلمين
  deliveredCount    Int      @default(0) // عدد المسلم إليهم
  failedCount       Int      @default(0) // عدد الفاشلين
  pendingCount      Int      @default(0) // عدد المعلقين
  
  // إحصائيات القراءة
  readCount         Int      @default(0) // عدد المقروءة
  unreadCount       Int      @default(0) // عدد غير المقروءة
  
  // معدلات النجاح
  deliveryRate      Float    @default(0) // معدل التسليم (%)
  readRate          Float    @default(0) // معدل القراءة (%)
  engagementRate    Float    @default(0) // معدل التفاعل (%)
  
  // أوقات الإحصائيات
  firstDeliveredAt  DateTime? // أول تسليم
  lastDeliveredAt   DateTime? // آخر تسليم
  firstReadAt       DateTime? // أول قراءة
  lastReadAt        DateTime? // آخر قراءة
  
  calculatedAt      DateTime @default(now()) // وقت حساب الإحصائيات
  updatedAt         DateTime @updatedAt
  
  @@index([deliveryRate])
  @@index([readRate])
  @@index([calculatedAt])
}
```

## التعدادات الجديدة

### نوع المجموعة (GroupType)
```prisma
enum GroupType {
  ALL_USERS        // جميع المستخدمين
  BY_ROLE          // حسب الدور
  CUSTOM_SELECTION // اختيار مخصص
  PREDEFINED_GROUP // مجموعة محددة مسبقاً
}
```

## تحديثات الجداول الموجودة

### تحديث جدول الإشعارات (Notification)
```prisma
model Notification {
  id          Int      @id @default(autoincrement())
  title       String
  content     String   @db.Text
  type        NotificationType @default(GENERAL)
  read        Boolean  @default(false) // سيتم إهمال هذا الحقل تدريجياً
  createdAt   DateTime @default(now())
  userId      Int?     // جعل الحقل اختياري للإشعارات الجماعية
  user        User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  relatedId   Int?     // معرف العنصر المرتبط
  link        String?  // رابط للانتقال إليه
  
  // الحقول الجديدة للإشعارات الجماعية
  groupId     Int?     // معرف المجموعة
  group       NotificationGroup? @relation("GroupNotifications", fields: [groupId], references: [id], onDelete: SetNull)
  isGroupNotification Boolean @default(false) // هل هو إشعار جماعي
  templateId  Int?     // معرف القالب المستخدم
  template    NotificationTemplate? @relation(fields: [templateId], references: [id], onDelete: SetNull)
  
  // إعدادات الإرسال
  priority    String   @default("MEDIUM") // HIGH, MEDIUM, LOW
  scheduledAt DateTime? // وقت الإرسال المجدول
  sentAt      DateTime? // وقت الإرسال الفعلي
  status      String   @default("PENDING") // PENDING, SENDING, SENT, FAILED
  
  // العلاقات الجديدة
  recipients  NotificationRecipient[]
  stats       NotificationStats?
  
  @@index([userId, read])
  @@index([createdAt])
  @@index([groupId])
  @@index([isGroupNotification])
  @@index([status])
  @@index([scheduledAt])
}
```

### تحديث جدول المستخدمين (User)
```prisma
model User {
  // الحقول الموجودة...
  
  // العلاقات الجديدة
  createdGroups     NotificationGroup[] @relation("CreatedGroups")
  createdTemplates  NotificationTemplate[] @relation("CreatedTemplates")
  receivedNotifications NotificationRecipient[] @relation("ReceivedNotifications")
}
```

## فهارس الأداء المطلوبة

### فهارس للبحث السريع
```sql
-- فهرس مركب للإشعارات الجماعية
CREATE INDEX idx_notification_group_status ON Notification(isGroupNotification, status, createdAt);

-- فهرس للمستلمين حسب الحالة
CREATE INDEX idx_recipient_status ON NotificationRecipient(delivered, read, userId);

-- فهرس للإحصائيات
CREATE INDEX idx_stats_rates ON NotificationStats(deliveryRate, readRate, calculatedAt);

-- فهرس للمجموعات النشطة
CREATE INDEX idx_active_groups ON NotificationGroup(isActive, type, createdAt);
```

## اعتبارات الأداء

### 1. تحسين الاستعلامات
- استخدام الفهارس المناسبة
- تجنب الاستعلامات المعقدة
- تقسيم البيانات الكبيرة

### 2. إدارة الذاكرة
- تحديد حجم الدفعات للإرسال الجماعي
- تنظيف البيانات القديمة
- ضغط البيانات النصية

### 3. التوازي
- معالجة الإشعارات بشكل متوازي
- استخدام queues للمهام الثقيلة
- تجنب blocking operations

## استراتيجية الترحيل

### المرحلة 1: إضافة الجداول الجديدة
- إنشاء الجداول الجديدة
- إضافة التعدادات
- إنشاء الفهارس

### المرحلة 2: تحديث الجداول الموجودة
- إضافة الحقول الجديدة
- تحديث العلاقات
- ترحيل البيانات الموجودة

### المرحلة 3: تنظيف البيانات
- إزالة الحقول المهملة
- تحسين الفهارس
- اختبار الأداء

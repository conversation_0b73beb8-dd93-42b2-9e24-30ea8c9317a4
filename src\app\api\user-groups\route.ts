import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, UserRole } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

// GET: جلب مجموعات المستخدمين المتاحة
export async function GET(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const { searchParams } = new URL(request.url);
        const type = searchParams.get('type'); // preview, count, list

        // إحصائيات المستخدمين حسب الدور
        const userStats = await prisma.user.groupBy({
            by: ['role'],
            where: {
                isActive: true,
                role: { not: 'PENDING' } // استبعاد المستخدمين المعلقين
            },
            _count: { id: true }
        });

        // إجمالي المستخدمين النشطين (باستثناء المعلقين)
        const totalActiveUsers = await prisma.user.count({
            where: {
                isActive: true,
                role: { not: 'PENDING' }
            }
        });

        // تنسيق البيانات
        const groupOptions = [
            {
                type: 'ALL_USERS',
                label: 'جميع المستخدمين',
                description: 'إرسال إشعار لجميع المستخدمين النشطين في النظام',
                count: totalActiveUsers,
                icon: 'FaUsers'
            }
        ];

        // إضافة خيارات حسب الدور
        const roleLabels: Record<UserRole, string> = {
            ADMIN: 'المديرين',
            EMPLOYEE: 'الموظفين',
            TEACHER: 'المعلمين',
            STUDENT: 'الطلاب',
            PARENT: 'أولياء الأمور',
            PENDING: 'المستخدمين المعلقين'
        };

        const roleIcons: Record<UserRole, string> = {
            ADMIN: 'FaUserShield',
            EMPLOYEE: 'FaUserTie',
            TEACHER: 'FaChalkboardTeacher',
            STUDENT: 'FaUserGraduate',
            PARENT: 'FaUserFriends',
            PENDING: 'FaUserClock'
        };

        userStats.forEach(stat => {
            if (stat.role !== 'PENDING' && stat._count.id > 0) {
                groupOptions.push({
                    type: 'BY_ROLE',
                    label: roleLabels[stat.role],
                    description: `إرسال إشعار لجميع ${roleLabels[stat.role]} في النظام`,
                    count: stat._count.id,
                    targetRole: stat.role,
                    icon: roleIcons[stat.role]
                });
            }
        });

        // إضافة خيار الاختيار المخصص
        groupOptions.push({
            type: 'CUSTOM_SELECTION',
            label: 'اختيار مخصص',
            description: 'اختيار مستخدمين محددين بأسمائهم',
            count: 0,
            icon: 'FaUserCheck'
        });

        return NextResponse.json({
            groupOptions,
            totalActiveUsers,
            userStats: userStats.map(stat => ({
                role: stat.role,
                label: roleLabels[stat.role],
                count: stat._count.id
            }))
        });

    } catch (error) {
        console.error('Error fetching user groups:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب مجموعات المستخدمين" },
            { status: 500 }
        );
    }
}

// POST: إنشاء مجموعة مخصصة جديدة
export async function POST(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const body = await request.json();

        // التحقق من البيانات المطلوبة
        if (!body.name || !body.type) {
            return NextResponse.json(
                { message: "البيانات غير مكتملة" },
                { status: 400 }
            );
        }

        const { name, description, type, targetRole, targetUserIds } = body;

        // التحقق من صحة البيانات حسب النوع
        if (type === 'BY_ROLE' && !targetRole) {
            return NextResponse.json(
                { message: "يجب تحديد الدور المستهدف" },
                { status: 400 }
            );
        }

        if (type === 'CUSTOM_SELECTION' && (!targetUserIds || !Array.isArray(targetUserIds))) {
            return NextResponse.json(
                { message: "يجب تحديد قائمة المستخدمين" },
                { status: 400 }
            );
        }

        // إنشاء المجموعة
        const group = await prisma.notificationGroup.create({
            data: {
                name,
                description,
                type,
                targetRole: targetRole || null,
                targetUserIds: targetUserIds ? JSON.stringify(targetUserIds) : null,
                createdBy: userData.id
            }
        });

        return NextResponse.json(group, { status: 201 });

    } catch (error) {
        console.error('Error creating user group:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء إنشاء المجموعة" },
            { status: 500 }
        );
    }
}

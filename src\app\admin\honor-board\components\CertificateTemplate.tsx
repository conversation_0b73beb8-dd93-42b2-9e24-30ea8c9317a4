import React, { useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, Printer } from 'lucide-react';
import CertificateTemplates, { TemplateType } from './CertificateTemplates';
import html2canvas from 'html2canvas';

type CertificateTemplateProps = {
  certificate: {
    id: number;
    title: string;
    description: string;
    templateUrl: string;
    type: string;
  };
  student?: {
    id: number;
    name: string;
    classe?: {
      name: string;
    } | null;
  };
  issueDate?: string;
  showControls?: boolean;
};

export default function CertificateTemplate({
  certificate,
  student,
  issueDate,
  showControls = true
}: CertificateTemplateProps) {
  const certificateRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    if (certificateRef.current) {
      // استخدام واجهة برمجة الطباعة الأصلية في المتصفح
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert('يرجى السماح بالنوافذ المنبثقة لطباعة الشهادة');
        return;
      }

      printWindow.document.write('<html><head><title>طباعة الشهادة</title>');
      printWindow.document.write('<style>body { margin: 0; padding: 20px; }</style>');
      printWindow.document.write('</head><body>');

      // نسخ محتوى الشهادة
      if (certificateRef.current) {
        printWindow.document.write(certificateRef.current.outerHTML);
      }

      printWindow.document.write('</body></html>');
      printWindow.document.close();

      // انتظار تحميل الصور
      printWindow.onload = function() {
        printWindow.focus();
        printWindow.print();
        printWindow.onafterprint = function() {
          printWindow.close();
        };
      };
    }
  };

  const handleDownload = async () => {
    if (!certificateRef.current) return;

    try {
      const canvas = await html2canvas(certificateRef.current, {
        scale: 2, // زيادة الدقة
        backgroundColor: null,
        logging: false,
        useCORS: true
      });

      const image = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = image;
      link.download = `شهادة_${certificate.type}_${student?.name || 'تقدير'}_${new Date().toLocaleDateString('fr-FR')}.png`;
      link.click();
    } catch (error) {
      console.error('Error generating certificate image:', error);
      alert('حدث خطأ أثناء تحميل الشهادة كصورة');

      // Fallback to opening the template URL if available
      if (certificate.templateUrl) {
        window.open(certificate.templateUrl, '_blank');
      }
    }
  };

  return (
    <div className="space-y-4" ref={certificateRef}>
      <CertificateTemplates
        certificateData={{
          id: certificate.id,
          title: certificate.title,
          description: certificate.description,
          templateUrl: certificate.templateUrl,
          type: certificate.type as TemplateType,
          student: student,
          issueDate: issueDate
        }}
        selectedTemplate={certificate.type as TemplateType}
        onSelectTemplate={() => {}}
      />

      {showControls && (
        <Card className="p-4 flex justify-center space-x-4 space-x-reverse">
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            <span>طباعة</span>
          </Button>
          <Button
            variant="outline"
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            <span>تحميل</span>
          </Button>
        </Card>
      )}
    </div>
  );
}

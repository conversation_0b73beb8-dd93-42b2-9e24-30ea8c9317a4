'use client';

import React from 'react';

interface VideoEmbedProps {
  videoId: string;
  title: string;
  className?: string;
}

export default function VideoEmbed({ videoId, title, className = '' }: VideoEmbedProps) {
  // استخراج معرف الفيديو من رابط يوتيوب إذا كان الرابط كاملاً
  const extractVideoId = (url: string): string => {
    // إذا كان المعرف فقط
    if (url.length === 11 && !url.includes('/')) {
      return url;
    }
    
    // استخراج من رابط يوتيوب
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    
    return (match && match[2].length === 11) ? match[2] : url;
  };

  const cleanVideoId = extractVideoId(videoId);

  return (
    <div className={`relative w-full ${className}`}>
      <div className="relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-md">
        <iframe
          src={`https://www.youtube.com/embed/${cleanVideoId}?rel=0&modestbranding=1&showinfo=0`}
          title={title}
          className="absolute top-0 left-0 w-full h-full border-0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          loading="lazy"
        />
      </div>
      <p className="text-sm text-gray-600 mt-2 text-center">{title}</p>
    </div>
  );
}

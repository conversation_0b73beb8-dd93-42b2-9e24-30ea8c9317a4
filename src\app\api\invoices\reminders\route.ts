import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { InvoiceStatus } from '@prisma/client';
import { ActivityLogger, ActivityType } from '@/utils/activity-logger';

// POST /api/invoices/reminders - إرسال تذكيرات للفواتير المستحقة
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { invoiceIds } = body;

    // التحقق من وجود معرفات الفواتير
    if (!invoiceIds || !Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return NextResponse.json(
        { error: 'معرفات الفواتير مطلوبة' },
        { status: 400 }
      );
    }

    // جلب الفواتير المحددة
    const invoices = await prisma.invoice.findMany({
      where: {
        id: {
          in: invoiceIds.map(id => parseInt(id.toString()))
        }
      },
      include: {
        student: {
          include: {
            guardian: true
          }
        }
      }
    });

    if (invoices.length === 0) {
      return NextResponse.json(
        { error: 'لم يتم العثور على أي فواتير' },
        { status: 404 }
      );
    }

    // تحديث الفواتير وإرسال التذكيرات
    const updatedInvoices = await prisma.$transaction(
      invoices.map(invoice => 
        prisma.invoice.update({
          where: { id: invoice.id },
          data: {
            remindersSent: { increment: 1 },
            lastReminderDate: new Date()
          }
        })
      )
    );

    // إنشاء إشعارات للأولياء (إذا كان لديهم حسابات)
    const notifications = [];
    for (const invoice of invoices) {
      if (invoice.student.guardian) {
        // البحث عن حساب ولي الأمر (إذا كان موجودًا)
        const parentUser = await prisma.user.findFirst({
          where: {
            role: 'PARENT',
            profile: {
              phone: invoice.student.guardian.phone
            }
          }
        });

        if (parentUser) {
          // إنشاء إشعار
          const notification = await prisma.notification.create({
            data: {
              userId: parentUser.id,
              title: 'تذكير بفاتورة مستحقة',
              content: `تذكير: لديك فاتورة مستحقة بقيمة ${invoice.amount} دج للطالب ${invoice.student.name} لشهر ${invoice.month}/${invoice.year}. يرجى سداد المبلغ في أقرب وقت ممكن.`,
              type: 'PAYMENT',
              read: false,
              relatedId: invoice.id,
              link: `/parents/payments?invoiceId=${invoice.id}`
            }
          });
          
          notifications.push(notification);
        }
      }
    }

    // تسجيل نشاط إرسال التذكيرات
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.INVOICE,
          `إرسال تذكيرات لـ ${invoices.length} فاتورة مستحقة`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط إرسال التذكيرات:', error);
    }

    return NextResponse.json({
      success: true,
      count: updatedInvoices.length,
      notificationsSent: notifications.length
    });
  } catch (error) {
    console.error('خطأ في إرسال تذكيرات الفواتير:', error);
    return NextResponse.json(
      { error: 'فشل في إرسال تذكيرات الفواتير' },
      { status: 500 }
    );
  }
}

// GET /api/invoices/reminders/overdue - جلب الفواتير المتأخرة
export async function GET() {
  try {
    const today = new Date();
    
    // جلب الفواتير المتأخرة (تاريخ الاستحقاق قبل اليوم وحالتها غير مدفوعة أو مدفوعة جزئيًا)
    const overdueInvoices = await prisma.invoice.findMany({
      where: {
        dueDate: {
          lt: today
        },
        status: {
          in: ['UNPAID', 'PARTIALLY_PAID']
        }
      },
      include: {
        student: {
          include: {
            guardian: true
          }
        },
        payments: true
      },
      orderBy: {
        dueDate: 'asc'
      }
    });

    // تحديث حالة الفواتير المتأخرة إلى "متأخرة" إذا لم تكن كذلك بالفعل
    const updatedInvoices = await prisma.$transaction(
      overdueInvoices
        .filter(invoice => invoice.status !== 'OVERDUE')
        .map(invoice => 
          prisma.invoice.update({
            where: { id: invoice.id },
            data: {
              status: 'OVERDUE' as InvoiceStatus
            }
          })
        )
    );

    // حساب المبلغ المدفوع والمتبقي لكل فاتورة
    const formattedInvoices = overdueInvoices.map(invoice => {
      const paidAmount = invoice.payments.reduce((sum, payment) => {
        if (payment.status === 'PAID') {
          return sum + payment.amount;
        }
        return sum;
      }, 0);

      return {
        ...invoice,
        paidAmount,
        remainingAmount: invoice.amount - paidAmount,
        daysOverdue: Math.floor((today.getTime() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24))
      };
    });

    return NextResponse.json({
      overdueInvoices: formattedInvoices,
      updatedCount: updatedInvoices.length
    });
  } catch (error) {
    console.error('خطأ في جلب الفواتير المتأخرة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الفواتير المتأخرة' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// تعريف واجهات البيانات
interface Expense {
  id: number;
  amount: number;
  date: Date;
  categoryId: number | null;
  category?: {
    id: number;
    name: string;
  } | null;
  purpose: string;
  notes?: string | null;
  // السماح بخصائص إضافية بشكل آمن من حيث النوع
  [key: string]: unknown;
}

interface CategoryExpense {
  categoryId: number | null;
  categoryName: string;
  amount: number;
  expenses: Expense[];
}

interface BudgetCategory {
  categoryId: number | null;
  categoryName: string;
  budgetAmount: number;
  actualAmount: number;
  remainingAmount: number;
  usagePercentage: number;
  status: 'EXCEEDED' | 'CRITICAL' | 'WARNING' | 'GOOD' | 'UNBUDGETED' | 'NO_BUDGET';
  expenses: Expense[];
}

interface MonthlyTrend {
  month: string;
  amount: number;
}

interface BudgetAnalysis {
  totalBudget: number;
  totalActualExpenses: number;
  remainingBudget: number;
  usagePercentage: number;
  categories: BudgetCategory[];
  status: string;
}

// GET /api/budgets/analysis - تحليل الميزانية ومقارنتها بالمصروفات الفعلية
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const budgetId = searchParams.get('budgetId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // التحقق من وجود معرف الميزانية أو فترة زمنية
    if (!budgetId && (!startDate || !endDate)) {
      return NextResponse.json(
        { error: 'يجب تحديد معرف الميزانية أو فترة زمنية (تاريخ البداية وتاريخ النهاية)' },
        { status: 400 }
      );
    }

    let budget;
    let budgetStartDate;
    let budgetEndDate;

    // إذا تم تحديد معرف الميزانية، نستخدم الميزانية المحددة
    if (budgetId) {
      budget = await prisma.budget.findUnique({
        where: { id: parseInt(budgetId) },
        include: {
          items: {
            include: {
              category: true,
            },
          },
        },
      });

      if (!budget) {
        return NextResponse.json(
          { error: 'الميزانية غير موجودة' },
          { status: 404 }
        );
      }

      budgetStartDate = budget.startDate;
      budgetEndDate = budget.endDate;
    } else {
      // استخدام الفترة الزمنية المحددة
      budgetStartDate = new Date(startDate as string);
      budgetEndDate = new Date(endDate as string);

      // البحث عن ميزانية نشطة في هذه الفترة
      budget = await prisma.budget.findFirst({
        where: {
          startDate: { lte: budgetEndDate },
          endDate: { gte: budgetStartDate },
          status: 'ACTIVE',
        },
        include: {
          items: {
            include: {
              category: true,
            },
          },
        },
      });
    }

    // الحصول على المصروفات الفعلية خلال فترة الميزانية
    const actualExpenses = await prisma.expense.findMany({
      where: {
        date: {
          gte: budgetStartDate,
          lte: budgetEndDate,
        },
      },
      include: {
        category: true,
      },
    });

    // حساب إجمالي المصروفات الفعلية
    const totalActualExpenses = actualExpenses.reduce(
      (sum, expense) => sum + expense.amount,
      0
    );

    // تجميع المصروفات حسب الفئة
    const expensesByCategory = actualExpenses.reduce((acc, expense) => {
      const categoryId = expense.categoryId || 0; // استخدام 0 للمصروفات بدون فئة
      if (!acc[categoryId]) {
        acc[categoryId] = {
          categoryId,
          categoryName: expense.category?.name || 'بدون فئة',
          amount: 0,
          expenses: [],
        };
      }
      acc[categoryId].amount += expense.amount;
      acc[categoryId].expenses.push(expense);
      return acc;
    }, {} as Record<number, CategoryExpense>);

    // تحليل الميزانية مقابل المصروفات الفعلية
    const budgetAnalysis: BudgetAnalysis = {
      totalBudget: budget?.totalAmount || 0,
      totalActualExpenses,
      remainingBudget: (budget?.totalAmount || 0) - totalActualExpenses,
      usagePercentage: budget?.totalAmount
        ? (totalActualExpenses / budget.totalAmount) * 100
        : 0,
      categories: [] as BudgetCategory[],
      status: '',
    };

    // تحديد حالة الميزانية
    if (budgetAnalysis.usagePercentage > 100) {
      budgetAnalysis.status = 'EXCEEDED';
    } else if (budgetAnalysis.usagePercentage >= 90) {
      budgetAnalysis.status = 'CRITICAL';
    } else if (budgetAnalysis.usagePercentage >= 75) {
      budgetAnalysis.status = 'WARNING';
    } else {
      budgetAnalysis.status = 'GOOD';
    }

    // إذا كانت هناك ميزانية، نقوم بتحليل كل بند من بنودها
    if (budget) {
      budgetAnalysis.categories = budget.items.map(item => {
        const categoryExpenses = expensesByCategory[item.categoryId] || {
          amount: 0,
          expenses: [],
        };

        return {
          categoryId: item.categoryId,
          categoryName: item.category.name,
          budgetAmount: item.amount,
          actualAmount: categoryExpenses.amount,
          remainingAmount: item.amount - categoryExpenses.amount,
          usagePercentage: item.amount > 0 ? (categoryExpenses.amount / item.amount) * 100 : 0,
          status:
            categoryExpenses.amount > item.amount
              ? 'EXCEEDED'
              : categoryExpenses.amount >= item.amount * 0.9
              ? 'CRITICAL'
              : categoryExpenses.amount >= item.amount * 0.75
              ? 'WARNING'
              : 'GOOD',
          expenses: categoryExpenses.expenses,
        };
      });

      // إضافة الفئات التي لها مصروفات ولكن ليس لها بند في الميزانية
      const budgetCategoryIds = budget.items.map(item => item.categoryId);
      const unbudgetedCategories = Object.values(expensesByCategory).filter(
        cat => cat.categoryId === null || !budgetCategoryIds.includes(cat.categoryId)
      );

      if (unbudgetedCategories.length > 0) {
        unbudgetedCategories.forEach(cat => {
          budgetAnalysis.categories.push({
            categoryId: cat.categoryId,
            categoryName: cat.categoryName,
            budgetAmount: 0,
            actualAmount: cat.amount,
            remainingAmount: -cat.amount,
            usagePercentage: Infinity,
            status: 'UNBUDGETED',
            expenses: cat.expenses,
          });
        });
      }
    } else {
      // إذا لم تكن هناك ميزانية، نعرض فقط المصروفات حسب الفئة
      budgetAnalysis.categories = Object.values(expensesByCategory).map(cat => ({
        categoryId: cat.categoryId,
        categoryName: cat.categoryName,
        budgetAmount: 0,
        actualAmount: cat.amount,
        remainingAmount: -cat.amount,
        usagePercentage: Infinity,
        status: 'NO_BUDGET',
        expenses: cat.expenses,
      }));
    }

    // تحليل الاتجاهات الشهرية
    const monthlyTrends = await prisma.$queryRaw`
      SELECT
        DATE_TRUNC('month', date) as month,
        SUM(amount) as total
      FROM "Expense"
      WHERE date >= ${budgetStartDate} AND date <= ${budgetEndDate}
      GROUP BY DATE_TRUNC('month', date)
      ORDER BY month ASC
    `;

    // تنسيق الاتجاهات الشهرية
    const formattedMonthlyTrends: MonthlyTrend[] = (monthlyTrends as { month: Date; total: string }[]).map(item => ({
      month: new Date(item.month).toISOString().substring(0, 7),
      amount: parseFloat(item.total),
    }));

    // حساب متوسط المصروفات اليومية
    const totalDays = Math.ceil(
      (budgetEndDate.getTime() - budgetStartDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const dailyAverage = totalActualExpenses / totalDays;

    // حساب المصروفات المتوقعة حتى نهاية فترة الميزانية
    const today = new Date();
    let projectedExpenses = totalActualExpenses;

    if (today < budgetEndDate) {
      const remainingDays = Math.ceil(
        (budgetEndDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
      );
      projectedExpenses += dailyAverage * remainingDays;
    }

    return NextResponse.json({
      budget: budget ? {
        id: budget.id,
        name: budget.name,
        startDate: budget.startDate,
        endDate: budget.endDate,
        totalAmount: budget.totalAmount,
        status: budget.status,
      } : null,
      analysis: budgetAnalysis,
      monthlyTrends: formattedMonthlyTrends,
      dailyAverage,
      projectedExpenses,
      period: {
        startDate: budgetStartDate,
        endDate: budgetEndDate,
        totalDays,
        elapsedDays: Math.min(
          totalDays,
          Math.ceil(
            (today < budgetEndDate ? today.getTime() : budgetEndDate.getTime()) - budgetStartDate.getTime()
            / (1000 * 60 * 60 * 24)
          )
        ),
      },
    });
  } catch (error) {
    console.error('خطأ في تحليل الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في تحليل الميزانية' },
      { status: 500 }
    );
  }
}

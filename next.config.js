/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  distDir: 'p-next',
  webpack: (config) => {
    config.module.rules.push({
      test: /\.woff2$/,
      type: 'asset/resource'
    });

    // تجاهل تحذيرات chartjs-node-canvas
    config.ignoreWarnings = [
      {
        module: /chartjs-node-canvas/,
        message: /Critical dependency: the request of a dependency is an expression/,
      },
      {
        module: /freshRequire\.js/,
        message: /Critical dependency: the request of a dependency is an expression/,
      }
    ];

    return config;
  },
  async rewrites() {
    return [
      {
        source: '/uploads/:path*',
        destination: '/api/uploads/:path*',
      },
    ];
  }//,
  //output: 'standalone',
  //allowedDevOrigins: ['*************']
};

module.exports = nextConfig;

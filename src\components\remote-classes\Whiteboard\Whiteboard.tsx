'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// استيراد ديناميكي للسبورة التفاعلية
// هذا يمنع تحميل المكتبة على جانب الخادم
const DynamicWhiteboard = dynamic(
  () => import('./SimpleWhiteboard'),
  {
    loading: () => (
      <div className="flex items-center justify-center h-[600px] bg-gray-100 rounded-md">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin h-8 w-8 border-4 border-[var(--primary-color)] border-t-transparent rounded-full"></div>
          <p className="text-gray-600">جاري تحميل السبورة...</p>
        </div>
      </div>
    ),
    ssr: false, // تعطيل العرض على جانب الخادم
  }
);

interface WhiteboardProps {
  id: string;
  readOnly?: boolean;
  initialData?: string;
  onDataChange?: (data: string) => void;
  height?: number;
  width?: number;

  // Real-time collaboration props
  enableSync?: boolean;
  roomId?: string;
  userId?: string;
  username?: string;
  userColor?: string;
}

/**
 * مكون السبورة التفاعلية
 * هذا مكون غلاف يقوم بتحميل مكون السبورة الفعلي بشكل ديناميكي
 */
const Whiteboard: React.FC<WhiteboardProps> = (props) => {
  return <DynamicWhiteboard {...props} />;
};

export default Whiteboard;

/*
  تحسينات مظهر القوائم المنسدلة
  هذا الملف يحتوي على تحسينات CSS لمظهر القوائم المنسدلة والعناصر المحددة
*/

/* تحسين مظهر القوائم المنسدلة العامة */
[role="listbox"],
[role="menu"],
[data-radix-select-content],
[data-radix-dropdown-menu-content] {
  border: 1px solid var(--border-primary, #e5e7eb);
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  background: var(--background-color, white);
  color: var(--text-color, #111827);
  overflow: hidden;
}

/* تحسين مظهر عناصر القائمة */
[role="option"],
[role="menuitem"],
[data-radix-select-item],
[data-radix-dropdown-menu-item] {
  position: relative;
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease-in-out;
}

[role="option"]:last-child,
[role="menuitem"]:last-child,
[data-radix-select-item]:last-child,
[data-radix-dropdown-menu-item]:last-child {
  border-bottom: none;
}

/* حالة التمرير (hover) */
[role="option"]:hover,
[role="menuitem"]:hover,
[data-radix-select-item]:hover,
[data-radix-dropdown-menu-item]:hover {
  background-color: var(--primary-light, #f0fdf4);
  color: var(--primary-dark, #166534);
  border-color: var(--primary-color, #22c55e);
  transform: translateX(-2px);
}

/* حالة التركيز (focus) */
[role="option"]:focus,
[role="menuitem"]:focus,
[data-radix-select-item]:focus,
[data-radix-dropdown-menu-item]:focus {
  background-color: var(--primary-light, #f0fdf4);
  color: var(--primary-dark, #166534);
  border-color: var(--primary-color, #22c55e);
  outline: 2px solid var(--primary-color, #22c55e);
  outline-offset: -2px;
}

/* حالة التحديد (selected/checked) */
[role="option"][data-state="checked"],
[role="option"][aria-selected="true"],
[data-radix-select-item][data-state="checked"],
[data-radix-select-item][aria-selected="true"] {
  background-color: var(--primary-color, #22c55e) !important;
  color: white !important;
  border-color: var(--primary-color, #22c55e) !important;
  font-weight: 600;
}

/* تحسين مظهر أيقونة التحديد */
[role="option"][data-state="checked"] svg,
[role="option"][aria-selected="true"] svg,
[data-radix-select-item][data-state="checked"] svg,
[data-radix-select-item][aria-selected="true"] svg {
  color: white !important;
  opacity: 1 !important;
}

/* تحسين مظهر القوائم المنسدلة في الوضع المظلم */
@media (prefers-color-scheme: dark) {
  [role="listbox"],
  [role="menu"],
  [data-radix-select-content],
  [data-radix-dropdown-menu-content] {
    background: #1f2937;
    border-color: #374151;
  }

  [role="option"],
  [role="menuitem"],
  [data-radix-select-item],
  [data-radix-dropdown-menu-item] {
    border-bottom-color: #374151;
    color: #f9fafb;
  }

  [role="option"]:hover,
  [role="menuitem"]:hover,
  [data-radix-select-item]:hover,
  [data-radix-dropdown-menu-item]:hover {
    background-color: #374151;
    color: #f9fafb;
  }
}

/* تحسين مظهر القوائم المنسدلة في الحوارات */
[role="dialog"] [role="listbox"],
[role="dialog"] [role="menu"],
[role="dialog"] [data-radix-select-content],
[role="dialog"] [data-radix-dropdown-menu-content] {
  z-index: 9999;
  position: relative;
}

/* تحسين مظهر القوائم المتعددة الاختيار */
.multi-select-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.multi-select-item .check-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

/* تحسين مظهر الفواصل */
[data-radix-select-separator],
[data-radix-dropdown-menu-separator] {
  height: 1px;
  background-color: #e5e7eb;
  margin: 4px 0;
}

/* تحسين مظهر التسميات */
[data-radix-select-label],
[data-radix-dropdown-menu-label] {
  font-weight: 600;
  color: var(--primary-color, #22c55e);
  padding: 8px 12px 4px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* تحسين الانتقالات والحركات */
[data-radix-select-content],
[data-radix-dropdown-menu-content] {
  animation-duration: 0.2s;
  animation-timing-function: ease-out;
}

[data-state="open"] {
  animation-name: slideIn;
}

[data-state="closed"] {
  animation-name: slideOut;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
  }
}

/* تحسين مظهر القوائم على الأجهزة المحمولة */
@media (max-width: 768px) {
  [role="listbox"],
  [role="menu"],
  [data-radix-select-content],
  [data-radix-dropdown-menu-content] {
    max-height: 60vh;
    overflow-y: auto;
  }

  [role="option"],
  [role="menuitem"],
  [data-radix-select-item],
  [data-radix-dropdown-menu-item] {
    padding: 12px 16px;
    font-size: 16px;
  }
}

/* تحسين إمكانية الوصول */
[role="option"]:focus-visible,
[role="menuitem"]:focus-visible,
[data-radix-select-item]:focus-visible,
[data-radix-dropdown-menu-item]:focus-visible {
  outline: 2px solid var(--primary-color, #22c55e);
  outline-offset: 2px;
}

/* تحسين مظهر القوائم المعطلة */
[role="option"][data-disabled],
[role="menuitem"][data-disabled],
[data-radix-select-item][data-disabled],
[data-radix-dropdown-menu-item][data-disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: transparent !important;
  color: #9ca3af !important;
}

/* تحسينات إضافية للقوائم المنسدلة */
.select-content,
.dropdown-content,
.popover-content {
  border: 1px solid var(--border-primary, #e5e7eb) !important;
  background: white !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* تحسين مظهر العناصر المحددة في القوائم المتعددة */
.command-item[aria-selected="true"],
.command-item[data-selected="true"] {
  background-color: var(--primary-color, #22c55e) !important;
  color: white !important;
}

/* تحسين مظهر أيقونة التحديد في القوائم المتعددة */
.command-item[aria-selected="true"] svg,
.command-item[data-selected="true"] svg {
  color: white !important;
}

/* تحسين مظهر الحدود في القوائم */
.select-item,
.dropdown-item,
.command-item {
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.select-item:last-child,
.dropdown-item:last-child,
.command-item:last-child {
  border-bottom: none;
}

/* تحسين مظهر القوائم في الوضع المظلم */
.dark .select-content,
.dark .dropdown-content,
.dark .popover-content {
  background: #1f2937 !important;
  border-color: #374151 !important;
}

.dark .select-item,
.dark .dropdown-item,
.dark .command-item {
  border-bottom-color: #374151;
  color: #f9fafb;
}

/* تحسين التباين في الوضع المظلم */
.dark .select-item:hover,
.dark .dropdown-item:hover,
.dark .command-item:hover {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

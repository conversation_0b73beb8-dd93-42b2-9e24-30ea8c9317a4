import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // جلب الأقسام
    const classes = await prisma.classe.findMany({
      take: 10,
      include: {
        students: true
      }
    });

    // جلب علاقات المعلم بالمادة
    const teacherSubjects = await prisma.teacherSubject.findMany({
      take: 10,
      include: {
        teacher: true,
        subject: true
      }
    });

    // جلب علاقات القسم بالمادة
    const classSubjects = await prisma.classSubject.findMany({
      take: 10,
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    // إنشاء علاقة قسم بمادة إذا لم تكن هناك علاقات
    let createdClassSubject = null;
    if (classSubjects.length === 0 && classes.length > 0 && teacherSubjects.length > 0) {
      try {
        createdClassSubject = await prisma.classSubject.create({
          data: {
            classeId: classes[0].id,
            teacherSubjectId: teacherSubjects[0].id
          },
          include: {
            classe: true,
            teacherSubject: {
              include: {
                teacher: true,
                subject: true
              }
            }
          }
        });
        console.log('Created class subject:', createdClassSubject);
      } catch (error) {
        console.error('Error creating class subject:', error);
      }
    }

    // إذا لم يتم إنشاء علاقة، نحاول إنشاء علاقة معلم بمادة أولاً
    if (!createdClassSubject && classes.length > 0) {
      try {
        // جلب المعلمين
        const teachers = await prisma.teacher.findMany({ take: 1 });

        // جلب المواد
        const subjects = await prisma.subject.findMany({ take: 1 });

        if (teachers.length > 0 && subjects.length > 0) {
          // البحث عن علاقة معلم بمادة موجودة
          let teacherSubject = await prisma.teacherSubject.findFirst({
            where: {
              teacherId: teachers[0].id,
              subjectId: subjects[0].id
            }
          });

          // إذا لم تكن موجودة، نقوم بإنشائها
          if (!teacherSubject) {
            try {
              teacherSubject = await prisma.teacherSubject.create({
                data: {
                  teacherId: teachers[0].id,
                  subjectId: subjects[0].id
                }
              });
              console.log('Created new teacher subject:', teacherSubject);
            } catch (error) {
              console.error('Error creating teacher subject:', error);
              // في حالة الخطأ، نحاول البحث مرة أخرى
              teacherSubject = await prisma.teacherSubject.findFirst({
                where: {
                  teacherId: teachers[0].id,
                  subjectId: subjects[0].id
                }
              });

              if (!teacherSubject) {
                throw new Error('Could not create or find teacher subject');
              }
            }
          }

          // البحث عن علاقة قسم بمادة موجودة
          let existingClassSubject = await prisma.classSubject.findFirst({
            where: {
              classeId: classes[0].id,
              teacherSubjectId: teacherSubject.id
            },
            include: {
              classe: true,
              teacherSubject: {
                include: {
                  teacher: true,
                  subject: true
                }
              }
            }
          });

          // إذا لم تكن موجودة، نقوم بإنشائها
          if (!existingClassSubject) {
            try {
              createdClassSubject = await prisma.classSubject.create({
                data: {
                  classeId: classes[0].id,
                  teacherSubjectId: teacherSubject.id
                },
                include: {
                  classe: true,
                  teacherSubject: {
                    include: {
                      teacher: true,
                      subject: true
                    }
                  }
                }
              });
              console.log('Created new class subject:', createdClassSubject);
            } catch (error) {
              console.error('Error creating class subject:', error);
              // في حالة الخطأ، نحاول البحث مرة أخرى
              existingClassSubject = await prisma.classSubject.findFirst({
                where: {
                  classeId: classes[0].id,
                  teacherSubjectId: teacherSubject.id
                },
                include: {
                  classe: true,
                  teacherSubject: {
                    include: {
                      teacher: true,
                      subject: true
                    }
                  }
                }
              });

              if (existingClassSubject) {
                createdClassSubject = existingClassSubject;
              }
            }
          } else {
            createdClassSubject = existingClassSubject;
          }

          if (createdClassSubject) {
            console.log('Using class subject:', createdClassSubject);
          }
        }
      } catch (error) {
        console.error('Error creating teacher subject and class subject:', error);
      }
    }

    return NextResponse.json({
      classes,
      teacherSubjects,
      classSubjects,
      createdClassSubject,
      message: 'تم جلب البيانات بنجاح'
    });
  } catch (error) {
    console.error('Error in create-class-subject route:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب البيانات',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

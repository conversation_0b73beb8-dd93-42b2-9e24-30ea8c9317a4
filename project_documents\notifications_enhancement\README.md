# مشروع تطوير نظام الإشعارات المخصص

## وصف المشروع
تطوير نظام إشعارات متقدم يسمح بإرسال الإشعارات لمجموعات مخصصة من المستخدمين بدلاً من مستخدم واحد فقط.

## المتطلبات الأساسية
- إرسال إشعارات لجميع المستخدمين
- إرسال إشعارات للمعلمين فقط
- إرسال إشعارات لصنف معين من المستخدمين (أولياء، طلاب، إلخ)
- الحفاظ على الوظائف الحالية للإشعارات الفردية

## الهيكل الحالي
- صفحة الإشعارات: `src/app/notifications/page.tsx`
- API الإشعارات: `src/app/api/notifications/route.ts`
- نموذج قاعدة البيانات: `prisma/schema.prisma` (جدول Notification)

## خطة العمل التنفيذية

### المرحلة الأولى: تحليل وتخطيط النظام
- [x] **T01.01: تحليل الهيكل الحالي للإشعارات**
  - **الحالة:** مكتمل
  - **المكونات:** src/app/notifications/page.tsx, src/app/api/notifications/route.ts
  - **الاعتماديات:** لا يوجد
  - **المستندات المرجعية:** راجع `uml/system_analysis.md`
  - **ملاحظات المستخدم:** النظام الحالي يدعم إرسال إشعار لمستخدم واحد فقط

- [x] **T01.02: إنشاء مخططات UML للنظام المحسن**
  - **الحالة:** مكتمل
  - **المكونات:** uml/class_diagram.md, uml/sequence_diagram.md
  - **الاعتماديات:** T01.01
  - **المستندات المرجعية:** راجع `specs/notification_types.md`
  - **ملاحظات المستخدم:** يجب دعم الإرسال الجماعي والمخصص

### المرحلة الثانية: تطوير قاعدة البيانات
- [x] **T02.01: تحديث نموذج قاعدة البيانات**
  - **الحالة:** مكتمل
  - **المكونات:** prisma/schema.prisma
  - **الاعتماديات:** T01.02
  - **المستندات المرجعية:** راجع `database/schema_updates.md`
  - **ملاحظات المستخدم:** إضافة جدول NotificationGroup وتحديث العلاقات

- [x] **T02.02: إنشاء migration لقاعدة البيانات**
  - **الحالة:** مكتمل (جاهز للتطبيق)
  - **المكونات:** prisma/migrations/
  - **الاعتماديات:** T02.01
  - **المستندات المرجعية:** راجع `database/migration_guide.md`
  - **ملاحظات المستخدم:** تطبيق التغييرات على قاعدة البيانات

### المرحلة الثالثة: تطوير API المحسن
- [x] **T03.01: تطوير API للإشعارات الجماعية**
  - **الحالة:** مكتمل
  - **المكونات:** src/app/api/notifications/bulk/route.ts
  - **الاعتماديات:** T02.02
  - **المستندات المرجعية:** راجع `api/bulk_notifications.md`
  - **ملاحظات المستخدم:** دعم إرسال إشعارات لمجموعات مخصصة

- [x] **T03.02: تطوير API لإدارة مجموعات المستخدمين**
  - **الحالة:** مكتمل
  - **المكونات:** src/app/api/user-groups/route.ts, src/app/api/user-groups/preview/route.ts
  - **الاعتماديات:** T03.01
  - **المستندات المرجعية:** راجع `api/user_groups.md`
  - **ملاحظات المستخدم:** جلب المستخدمين حسب الدور والمجموعة

### المرحلة الرابعة: تطوير واجهة المستخدم
- [x] **T04.01: تحديث نموذج إنشاء الإشعارات**
  - **الحالة:** مكتمل
  - **المكونات:** src/app/notifications/page.tsx
  - **الاعتماديات:** T03.02
  - **المستندات المرجعية:** راجع `ui/notification_form.md`
  - **ملاحظات المستخدم:** إضافة خيارات الإرسال الجماعي والمخصص

- [x] **T04.02: إنشاء مكون اختيار المجموعات**
  - **الحالة:** مكتمل
  - **المكونات:** src/components/notifications/GroupSelector.tsx
  - **الاعتماديات:** T04.01
  - **المستندات المرجعية:** راجع `components/group_selector.md`
  - **ملاحظات المستخدم:** واجهة سهلة لاختيار المجموعات المستهدفة

### المرحلة السادسة: الإصلاحات والتحسينات
- [x] **T06.01: إصلاح مشكلة عدم ظهور جميع المستخدمين**
  - **الحالة:** مكتمل
  - **المكونات:** src/app/notifications/page.tsx, src/app/api/users/route.ts, جميع APIs الإشعارات
  - **الاعتماديات:** T04.02
  - **المستندات المرجعية:** راجع `fixes/user_loading_fix.md`
  - **ملاحظات المستخدم:** إصلاح عدم ظهور جميع المستخدمين في القوائم المنسدلة والبحث

- [x] **T06.02: إضافة صلاحيات الموظف المخول**
  - **الحالة:** مكتمل
  - **المكونات:** جميع APIs الإشعارات, src/app/notifications/page.tsx
  - **الاعتماديات:** T06.01
  - **المستندات المرجعية:** راجع `fixes/employee_permissions_update.md`
  - **ملاحظات المستخدم:** إضافة صلاحية EMPLOYEE لجميع عمليات إدارة الإشعارات

- [x] **T06.03: إصلاح مشكلة عدم وصول الإشعارات الجماعية**
  - **الحالة:** مكتمل
  - **المكونات:** src/app/api/notifications/route.ts, src/app/api/notifications/[id]/route.ts, src/app/api/notifications/mark-all-read/route.ts, src/app/api/notifications/bulk/route.ts
  - **الاعتماديات:** T06.02
  - **المستندات المرجعية:** راجع `fixes/bulk_notifications_fix.md`
  - **ملاحظات المستخدم:** إصلاح عدم ظهور الإشعارات الجماعية في قوائم المستخدمين

- [x] **T06.04: إضافة معلومات المرسل وإصلاح زر "تحديد الكل كمقروء"**
  - **الحالة:** مكتمل
  - **المكونات:** src/app/api/notifications/route.ts, src/app/api/notifications/bulk/route.ts, src/app/notifications/page.tsx
  - **الاعتماديات:** T06.03
  - **المستندات المرجعية:** راجع `fixes/sender_info_and_mark_all_fix.md`
  - **ملاحظات المستخدم:** إضافة معلومات المرسل لجميع الإشعارات وإصلاح زر تحديد الكل كمقروء

### المرحلة الخامسة: التحسينات والاختبار
- [ ] **T05.01: إضافة معاينة المستلمين**
  - **الحالة:** قيد الانتظار
  - **المكونات:** src/components/notifications/RecipientPreview.tsx
  - **الاعتماديات:** T04.02
  - **المستندات المرجعية:** راجع `features/recipient_preview.md`
  - **ملاحظات المستخدم:** عرض قائمة المستلمين قبل الإرسال

- [ ] **T05.02: إضافة إحصائيات الإشعارات**
  - **الحالة:** قيد الانتظار
  - **المكونات:** src/app/admin/notifications/stats/page.tsx
  - **الاعتماديات:** T05.01
  - **المستندات المرجعية:** راجع `features/notification_stats.md`
  - **ملاحظات المستخدم:** تقارير عن معدلات القراءة والتفاعل

- [ ] **T05.03: اختبار النظام وإصلاح الأخطاء**
  - **الحالة:** قيد الانتظار
  - **المكونات:** جميع الملفات المطورة
  - **الاعتماديات:** T05.02
  - **المستندات المرجعية:** راجع `testing/test_plan.md`
  - **ملاحظات المستخدم:** اختبار شامل لجميع الوظائف

## الميزات المطلوبة
1. **إرسال جماعي**: إرسال إشعار لجميع المستخدمين
2. **إرسال حسب الدور**: إرسال للمعلمين، الطلاب، أولياء الأمور، إلخ
3. **إرسال مخصص**: اختيار مستخدمين محددين
4. **معاينة المستلمين**: عرض قائمة المستلمين قبل الإرسال
5. **إحصائيات**: تتبع معدلات القراءة والتفاعل

## التقنيات المستخدمة
- Next.js 14 (App Router)
- TypeScript
- Prisma ORM
- PostgreSQL
- Tailwind CSS
- React Hook Form

## ملاحظات التطوير
- الحفاظ على التوافق مع النظام الحالي
- استخدام أفضل الممارسات في الأمان
- تحسين الأداء للإرسال الجماعي
- واجهة مستخدم بديهية وسهلة الاستخدام

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/admin/financial-reports/quick-stats - الحصول على إحصائيات سريعة
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const period = searchParams.get('period') || 'month'; // month, week, year, today

    // تحديد الفترة الزمنية
    let startDate: Date;
    let endDate = new Date();

    switch (period) {
      case 'today':
        startDate = new Date();
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'year':
        startDate = new Date();
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case 'month':
      default:
        startDate = new Date();
        startDate.setMonth(startDate.getMonth() - 1);
        break;
    }

    // الحصول على الخزينة
    const treasury = await prisma.treasury.findFirst();

    // إحصائيات سريعة للمدفوعات
    const paymentsStats = await prisma.payment.aggregate({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
        status: 'PAID',
      },
      _count: true,
      _sum: {
        amount: true,
      },
    });

    // إحصائيات سريعة للتبرعات
    const donationsStats = await prisma.donation.aggregate({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: true,
      _sum: {
        amount: true,
      },
    });

    // إحصائيات سريعة للمصروفات
    const expensesStats = await prisma.expense.aggregate({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: true,
      _sum: {
        amount: true,
      },
    });

    // إحصائيات سريعة للمداخيل الأخرى
    const incomesStats = await prisma.income.aggregate({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      _count: true,
      _sum: {
        amount: true,
      },
    });

    // حساب الإجماليات
    const totalIncome = (paymentsStats._sum.amount || 0) + 
                       (donationsStats._sum.amount || 0) + 
                       (incomesStats._sum.amount || 0);
    const totalExpenses = expensesStats._sum.amount || 0;
    const netProfit = totalIncome - totalExpenses;

    // إحصائيات المقارنة مع الفترة السابقة
    let previousStartDate: Date;
    let previousEndDate: Date;

    switch (period) {
      case 'today':
        previousStartDate = new Date(startDate);
        previousStartDate.setDate(previousStartDate.getDate() - 1);
        previousEndDate = new Date(startDate);
        previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1);
        break;
      case 'week':
        previousStartDate = new Date(startDate);
        previousStartDate.setDate(previousStartDate.getDate() - 7);
        previousEndDate = new Date(startDate);
        previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1);
        break;
      case 'year':
        previousStartDate = new Date(startDate);
        previousStartDate.setFullYear(previousStartDate.getFullYear() - 1);
        previousEndDate = new Date(startDate);
        previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1);
        break;
      case 'month':
      default:
        previousStartDate = new Date(startDate);
        previousStartDate.setMonth(previousStartDate.getMonth() - 1);
        previousEndDate = new Date(startDate);
        previousEndDate.setMilliseconds(previousEndDate.getMilliseconds() - 1);
        break;
    }

    // إحصائيات الفترة السابقة
    const [previousPayments, previousDonations, previousExpenses, previousIncomes] = await Promise.all([
      prisma.payment.aggregate({
        where: {
          date: { gte: previousStartDate, lte: previousEndDate },
          status: 'PAID',
        },
        _sum: { amount: true },
      }),
      prisma.donation.aggregate({
        where: {
          date: { gte: previousStartDate, lte: previousEndDate },
        },
        _sum: { amount: true },
      }),
      prisma.expense.aggregate({
        where: {
          date: { gte: previousStartDate, lte: previousEndDate },
        },
        _sum: { amount: true },
      }),
      prisma.income.aggregate({
        where: {
          date: { gte: previousStartDate, lte: previousEndDate },
        },
        _sum: { amount: true },
      }),
    ]);

    const previousTotalIncome = (previousPayments._sum.amount || 0) + 
                               (previousDonations._sum.amount || 0) + 
                               (previousIncomes._sum.amount || 0);
    const previousTotalExpenses = previousExpenses._sum.amount || 0;
    const previousNetProfit = previousTotalIncome - previousTotalExpenses;

    // حساب نسب التغيير
    const calculateChangePercentage = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    // أفضل طرق الدفع
    const topPaymentMethods = await prisma.paymentMethod.findMany({
      where: { isActive: true },
      include: {
        payments: {
          where: {
            date: { gte: startDate, lte: endDate },
            status: 'PAID',
          },
        },
        donations: {
          where: {
            date: { gte: startDate, lte: endDate },
          },
        },
      },
    });

    const paymentMethodsStats = topPaymentMethods
      .map(method => {
        const paymentsAmount = method.payments.reduce((sum, p) => sum + p.amount, 0);
        const donationsAmount = method.donations.reduce((sum, d) => sum + d.amount, 0);
        const totalAmount = paymentsAmount + donationsAmount;
        
        return {
          id: method.id,
          name: method.name,
          totalAmount,
          paymentsCount: method.payments.length,
          donationsCount: method.donations.length,
        };
      })
      .filter(method => method.totalAmount > 0)
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, 5);

    // أكثر فئات المصروفات
    const topExpenseCategories = await prisma.expenseCategory.findMany({
      where: { isActive: true },
      include: {
        expenses: {
          where: {
            date: { gte: startDate, lte: endDate },
          },
        },
      },
    });

    const expenseCategoriesStats = topExpenseCategories
      .map(category => {
        const totalAmount = category.expenses.reduce((sum, e) => sum + e.amount, 0);
        
        return {
          id: category.id,
          name: category.name,
          totalAmount,
          expensesCount: category.expenses.length,
        };
      })
      .filter(category => category.totalAmount > 0)
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, 5);

    const quickStats = {
      period: {
        type: period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        label: getPeriodLabel(period),
      },
      
      // الإحصائيات الحالية
      current: {
        treasury: {
          balance: treasury?.balance || 0,
          totalIncome: treasury?.totalIncome || 0,
          totalExpense: treasury?.totalExpense || 0,
        },
        payments: {
          count: paymentsStats._count,
          amount: paymentsStats._sum.amount || 0,
        },
        donations: {
          count: donationsStats._count,
          amount: donationsStats._sum.amount || 0,
        },
        expenses: {
          count: expensesStats._count,
          amount: expensesStats._sum.amount || 0,
        },
        incomes: {
          count: incomesStats._count,
          amount: incomesStats._sum.amount || 0,
        },
        totals: {
          totalIncome,
          totalExpenses,
          netProfit,
        },
      },

      // مقارنة مع الفترة السابقة
      comparison: {
        income: {
          current: totalIncome,
          previous: previousTotalIncome,
          change: calculateChangePercentage(totalIncome, previousTotalIncome),
        },
        expenses: {
          current: totalExpenses,
          previous: previousTotalExpenses,
          change: calculateChangePercentage(totalExpenses, previousTotalExpenses),
        },
        netProfit: {
          current: netProfit,
          previous: previousNetProfit,
          change: calculateChangePercentage(netProfit, previousNetProfit),
        },
      },

      // أفضل الإحصائيات
      top: {
        paymentMethods: paymentMethodsStats,
        expenseCategories: expenseCategoriesStats,
      },
    };

    return NextResponse.json({
      success: true,
      data: quickStats,
      message: 'تم جلب الإحصائيات السريعة بنجاح',
    });

  } catch (error) {
    console.error('خطأ في جلب الإحصائيات السريعة:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الإحصائيات السريعة' },
      { status: 500 }
    );
  }
}

function getPeriodLabel(period: string): string {
  switch (period) {
    case 'today':
      return 'اليوم';
    case 'week':
      return 'الأسبوع الماضي';
    case 'year':
      return 'السنة الماضية';
    case 'month':
    default:
      return 'الشهر الماضي';
  }
}

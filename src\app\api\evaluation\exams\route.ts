import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { EvaluationType } from '@prisma/client';

export async function GET(request: NextRequest) {
  try {
    // Validate database connection before query
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    // الحصول على معلمات URL
    const { searchParams } = new URL(request.url);
    const teacherId = searchParams.get('teacherId');

    console.log('Fetching exams with teacherId:', teacherId);

    // جلب جميع علاقات الفصول بالمواد لاستخدامها لاحقاً
    const classSubjects = await prisma.classSubject.findMany({
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    // إنشاء خريطة للفصول حسب معرف علاقة الفصل بالمادة
    const classSubjectMap = new Map();
    classSubjects.forEach(cs => {
      classSubjectMap.set(cs.id, {
        classeId: cs.classeId,
        classeName: cs.classe.name,
        teacherId: cs.teacherSubject.teacherId,
        teacherName: cs.teacherSubject.teacher.name,
        subjectId: cs.teacherSubject.subjectId,
        subjectName: cs.teacherSubject.subject.name
      });
    });

    const exams = await prisma.exam.findMany({
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        evaluationType: true,
        createdAt: true,
        month: true,
        description: true,
        maxPoints: true,
        passingPoints: true,
        requiresSurah: true,
        isPeriodic: true,
        period: true,
        subjectId: true,
        subject: true,
        examType: true,
        examCriteria: {
          select: {
            id: true,
            criteriaId: true,
            criteria: {
              select: {
                id: true,
                name: true,
                weight: true
              }
            }
          }
        },
        exam_points: {
          select: {
            id: true,
            studentId: true,
            classSubjectId: true,
            grade: true,
            classSubject: {
              select: {
                id: true,
                classeId: true,
                teacherSubjectId: true,
                classe: {
                  select: {
                    id: true,
                    name: true
                  }
                },
                teacherSubject: {
                  select: {
                    teacherId: true,
                    teacher: {
                      select: {
                        id: true,
                        name: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
    });

    // إضافة معلومات الفصول المرتبطة بكل امتحان
    const examsWithClassSubjects = exams.map(exam => {
      // استخراج الفصول الفريدة من نقاط الامتحان
      const uniqueClassSubjects = new Map();
      if (exam.exam_points && exam.exam_points.length > 0) {
        exam.exam_points.forEach(point => {
          if (point.classSubjectId && point.classSubject) {
            uniqueClassSubjects.set(point.classSubjectId, point.classSubject);
          }
        });
      }

      // تحويل الخريطة إلى مصفوفة
      const classSubjects = Array.from(uniqueClassSubjects.values());

      return {
        ...exam,
        classSubjects
      };
    });

    // إذا تم تحديد معرف المعلم، نقوم بتصفية الامتحانات
    let filteredExams = examsWithClassSubjects;
    if (teacherId) {
      // إذا كان هناك معرف معلم، نحتاج لجلب جميع الامتحانات التي يمكن للمعلم الوصول إليها
      // بما في ذلك الامتحانات التي لم يتم تسجيل نقاط لها بعد

      // جلب الفصول التي يدرسها المعلم
      const teacherClassSubjects = await prisma.classSubject.findMany({
        where: {
          teacherSubject: {
            teacherId: parseInt(teacherId)
          }
        },
        include: {
          classe: true,
          teacherSubject: {
            include: {
              teacher: true,
              subject: true
            }
          }
        }
      });

      filteredExams = examsWithClassSubjects.filter(exam => {
        // التحقق من وجود فصل واحد على الأقل يدرسه المعلم في نقاط الامتحان
        const hasExamPoints = exam.classSubjects && exam.classSubjects.some(cs =>
          cs.teacherSubject && cs.teacherSubject.teacherId === parseInt(teacherId)
        );

        // إذا كان للامتحان نقاط مسجلة للمعلم، فهو مؤهل
        if (hasExamPoints) {
          return true;
        }

        // إذا لم يكن للامتحان نقاط مسجلة، نتحقق من أن المعلم يدرس فصول
        // ويمكنه إنشاء امتحانات لها (جميع الامتحانات متاحة لجميع المعلمين)
        return teacherClassSubjects.length > 0;
      });

      // إضافة معلومات الفصول للامتحانات التي لا تحتوي على نقاط
      filteredExams = filteredExams.map(exam => {
        if (!exam.classSubjects || exam.classSubjects.length === 0) {
          // إضافة فصول المعلم للامتحانات التي لا تحتوي على نقاط
          return {
            ...exam,
            classSubjects: teacherClassSubjects
          };
        }
        return exam;
      });

      console.log(`Filtered exams for teacher ${teacherId}: ${filteredExams.length} of ${examsWithClassSubjects.length}`);
    }

    return NextResponse.json({
      data: filteredExams,
      success: true,
      message: "تم جلب الامتحانات بنجاح"
    });
  } catch (error) {
    console.error("Error fetching exams:", error);
    return NextResponse.json(
      {
        error: "حدث خطأ أثناء جلب الامتحانات",
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من اتصال قاعدة البيانات
    if (!prisma) {
      throw new Error("فشل الاتصال بقاعدة البيانات");
    }

    const body = await request.json();
    const {
      evaluationType,
      month,
      requiresSurah = false,
      description = null,
      maxPoints = 100,
      passingPoints = 60,
      typeId = null,
      isPeriodic = false,
      period = null,
      criteriaIds = [],
      subjectId = null
    } = body;

    // التحقق من صحة البيانات
    if (!evaluationType || typeof evaluationType !== 'string') {
      return NextResponse.json(
        {
          success: false,
          error: "نوع التقييم مطلوب وغير صالح",
          data: null
        },
        { status: 400 }
      );
    }

    if (!month || typeof month !== 'string' || !month.match(/^\d{4}-\d{2}$/)) {
      return NextResponse.json(
        {
          success: false,
          error: "الشهر مطلوب وغير صالح (يجب أن يكون بتنسيق YYYY-MM)",
          data: null
        },
        { status: 400 }
      );
    }

    // السماح بإنشاء امتحانات متعددة من نفس النوع في نفس الشهر
    // تم إزالة التحقق من التكرار للسماح بمرونة أكبر في إنشاء الامتحانات

    // تعيين requiresSurah تلقائيًا إذا كان نوع الامتحان هو حفظ القرآن
    const autoRequiresSurah = evaluationType === 'QURAN_MEMORIZATION' ? true : requiresSurah;

    // إنشاء الامتحان مع معايير التقييم المرتبطة به
    const exam = await prisma.$transaction(async (tx) => {
      // إنشاء الامتحان
      // التحقق من وجود المادة الدراسية إذا تم تحديدها
      if (subjectId) {
        const subject = await tx.subject.findUnique({
          where: { id: parseInt(subjectId) }
        });

        if (!subject) {
          throw new Error("المادة الدراسية المحددة غير موجودة");
        }
      }

      const newExam = await tx.exam.create({
        data: {
          evaluationType: evaluationType as EvaluationType,
          month: month,
          requiresSurah: autoRequiresSurah,
          description,
          maxPoints,
          passingPoints,
          typeId: typeId ? parseInt(typeId) : null,
          isPeriodic,
          period,
          subjectId: subjectId ? parseInt(subjectId) : null
        },
        include: {
          examType: true
        }
      });

      // إذا تم توفير معايير تقييم، قم بإنشاء علاقات بين الامتحان ومعايير التقييم
      if (criteriaIds && Array.isArray(criteriaIds) && criteriaIds.length > 0) {
        console.log(`Associating exam ${newExam.id} with criteria: ${criteriaIds.join(', ')}`);

        // إنشاء علاقات بين الامتحان ومعايير التقييم في الجدول الوسيط باستخدام createMany
        await tx.examCriteria.createMany({
          data: criteriaIds.map(criteriaId => ({
            examId: newExam.id,
            criteriaId: Number(criteriaId)
          }))
        });
      }

      return newExam;
    });

    return NextResponse.json(
      {
        data: exam,
        success: true,
        message: "تم إنشاء الامتحان بنجاح"
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating exam:", error);
    return NextResponse.json(
      {
        error: "حدث خطأ أثناء إنشاء الامتحان",
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "معرف الامتحان مطلوب", success: false },
        { status: 400 }
      );
    }

    await prisma.exam.delete({
      where: {
        id: parseInt(id),
      },
    });

    return NextResponse.json({
      message: "تم حذف الامتحان بنجاح",
      success: true
    });
  } catch (error) {
    console.error("Error deleting exam:", error);
    return NextResponse.json(
      { error: "حدث خطأ أثناء حذف الامتحان", success: false },
      { status: 500 }
    );
  }
}

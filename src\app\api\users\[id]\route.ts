import { NextRequest, NextResponse } from "next/server";
import { UserRole } from '@prisma/client';
import prisma from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { getToken } from '@/lib/auth';

interface UpdateUserDto {
    username?: string;
    password?: string;
    name?: string;
    email?: string;
    phone?: string;
    role?: string;
}

// واجهة لتحديث بيانات المستخدم
interface UserUpdateData {
    username?: string;
    email?: string;
    role?: UserRole;
    password?: string;
}

// واجهة لتحديث بيانات الملف الشخصي
interface ProfileUpdateData {
    name?: string;
    phone?: string | null;
}

// GET: جلب بيانات مستخدم محدد
export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        // انتظار params قبل الاستخدام
        const resolvedParams = await params;
        
        // التحقق من الصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        // التحقق من أن المستخدم مسؤول أو يطلب بياناته الخاصة
        const isAdmin = userData.role === 'ADMIN';
        const isOwnProfile = userData.id === parseInt(resolvedParams.id);

        if (!isAdmin && !isOwnProfile) {
            return NextResponse.json(
                { message: "غير مصرح به: لا يمكنك الوصول إلى بيانات هذا المستخدم" },
                { status: 403 }
            );
        }

        const userId = parseInt(resolvedParams.id);
        if (isNaN(userId)) {
            return NextResponse.json(
                { message: "معرف المستخدم غير صالح" },
                { status: 400 }
            );
        }

        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: {
                profile: true,
                teacher: true,
                employee: true
            }
        });

        if (!user) {
            return NextResponse.json(
                { message: "المستخدم غير موجود" },
                { status: 404 }
            );
        }

        // تنسيق البيانات للعرض
        const formattedUser = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            name: user.profile?.name || '',
            phone: user.profile?.phone || '',
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            isTeacher: !!user.teacher,
            isEmployee: !!user.employee,
            teacherDetails: user.teacher,
            employeeDetails: user.employee
        };

        return NextResponse.json(formattedUser);
    } catch (error) {
        console.error('Error fetching user:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب بيانات المستخدم" },
            { status: 500 }
        );
    }
}

// PUT: تحديث بيانات مستخدم
export async function PUT(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        // انتظار params قبل الاستخدام
        const resolvedParams = await params;
        
        // التحقق من الصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        // التحقق من أن المستخدم مسؤول أو يقوم بتحديث بياناته الخاصة
        const isAdmin = userData.role === 'ADMIN';
        const isOwnProfile = userData.id === parseInt(resolvedParams.id);

        if (!isAdmin && !isOwnProfile) {
            return NextResponse.json(
                { message: "غير مصرح به: لا يمكنك تعديل بيانات هذا المستخدم" },
                { status: 403 }
            );
        }

        const userId = parseInt(resolvedParams.id);
        if (isNaN(userId)) {
            return NextResponse.json(
                { message: "معرف المستخدم غير صالح" },
                { status: 400 }
            );
        }

        const body = await request.json() as UpdateUserDto;

        // التحقق من وجود المستخدم
        const existingUser = await prisma.user.findUnique({
            where: { id: userId },
            include: { profile: true }
        });

        if (!existingUser) {
            return NextResponse.json(
                { message: "المستخدم غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من اسم المستخدم الجديد إذا تم تغييره
        if (body.username && body.username !== existingUser.username) {
            const usernameExists = await prisma.user.findUnique({
                where: { username: body.username }
            });

            if (usernameExists) {
                return NextResponse.json(
                    { message: "اسم المستخدم موجود بالفعل" },
                    { status: 400 }
                );
            }
        }

        // التحقق من صحة الدور إذا تم تغييره (فقط للمسؤول)
        let userRole: UserRole | undefined;
        if (body.role && isAdmin) {
            switch (body.role.toUpperCase()) {
                case 'ADMIN':
                    userRole = UserRole.ADMIN;
                    break;
                case 'TEACHER':
                    userRole = UserRole.TEACHER;
                    break;
                case 'STUDENT':
                    userRole = UserRole.STUDENT;
                    break;
                case 'PARENT':
                    userRole = UserRole.PARENT;
                    break;
                case 'EMPLOYEE':
                    userRole = UserRole.EMPLOYEE;
                    break;
                default:
                    return NextResponse.json(
                        { message: "دور المستخدم غير صالح" },
                        { status: 400 }
                    );
            }
        }

        // إعداد بيانات التحديث للمستخدم
        const userUpdateData: UserUpdateData = {};
        if (body.username) userUpdateData.username = body.username;
        if (body.email) userUpdateData.email = body.email;
        if (isAdmin && userRole) userUpdateData.role = userRole;

        // تشفير كلمة المرور إذا تم تغييرها
        if (body.password) {
            const salt = await bcrypt.genSalt(10);
            userUpdateData.password = await bcrypt.hash(body.password, salt);
        }

        // إعداد بيانات التحديث للملف الشخصي
        const profileUpdateData: ProfileUpdateData = {};
        if (body.name) profileUpdateData.name = body.name;
        if (body.phone !== undefined) profileUpdateData.phone = body.phone;

        // تحديث بيانات المستخدم والملف الشخصي
        const updatedUser = await prisma.user.update({
            where: { id: userId },
            data: {
                ...userUpdateData,
                profile: {
                    update: profileUpdateData
                }
            },
            include: {
                profile: true
            }
        });

        // تحديث بيانات المعلم أو الموظف أو الطالب أو ولي الأمر إذا تغير الدور
        if (isAdmin && userRole && userRole !== existingUser.role) {
            // إذا كان الدور الجديد هو معلم
            if (userRole === UserRole.TEACHER) {
                // التحقق مما إذا كان المستخدم معلمًا بالفعل
                const existingTeacher = await prisma.teacher.findUnique({
                    where: { userId: userId }
                });

                if (!existingTeacher) {
                    await prisma.teacher.create({
                        data: {
                            name: body.name || existingUser.profile?.name || '',
                            phone: body.phone || existingUser.profile?.phone || null,
                            specialization: "عام", // قيمة افتراضية
                            userId: userId
                        }
                    });
                }
            }
            // إذا كان الدور الجديد هو موظف
            else if (userRole === UserRole.EMPLOYEE) {
                // التحقق مما إذا كان المستخدم موظفًا بالفعل
                const existingEmployee = await prisma.employee.findUnique({
                    where: { userId: userId }
                });

                if (!existingEmployee) {
                    await prisma.employee.create({
                        data: {
                            name: body.name || existingUser.profile?.name || '',
                            phone: body.phone || existingUser.profile?.phone || null,
                            position: "موظف", // قيمة افتراضية
                            userId: userId
                        }
                    });
                }
            }
            // إذا كان الدور الجديد هو طالب
            else if (userRole === UserRole.STUDENT) {
                // التحقق مما إذا كان هناك سجل طالب بنفس اسم المستخدم
                const existingStudent = await prisma.student.findUnique({
                    where: { username: existingUser.username }
                });

                if (!existingStudent) {
                    await prisma.student.create({
                        data: {
                            name: body.name || existingUser.profile?.name || '',
                            username: existingUser.username,
                            phone: body.phone || existingUser.profile?.phone || null,
                            age: 0, // قيمة افتراضية، يمكن تحديثها لاحقاً
                        }
                    });
                }
            }
            // إذا كان الدور الجديد هو ولي أمر
            else if (userRole === UserRole.PARENT) {
                // التحقق مما إذا كان هناك سجل ولي أمر بنفس رقم الهاتف
                const phone = body.phone || existingUser.profile?.phone || '';
                if (phone) {
                    const existingParent = await prisma.parent.findFirst({
                        where: { phone: phone }
                    });

                    if (!existingParent) {
                        await prisma.parent.create({
                            data: {
                                name: body.name || existingUser.profile?.name || '',
                                phone: phone,
                                email: body.email || existingUser.email
                            }
                        });
                    }
                }
            }
        }

        return NextResponse.json({
            user: {
                id: updatedUser.id,
                username: updatedUser.username,
                email: updatedUser.email,
                role: updatedUser.role,
                name: updatedUser.profile?.name,
                phone: updatedUser.profile?.phone
            },
            message: "تم تحديث بيانات المستخدم بنجاح"
        });
    } catch (error) {
        console.error('Error updating user:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء تحديث بيانات المستخدم" },
            { status: 500 }
        );
    }
}

// DELETE: حذف مستخدم
export async function DELETE(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        // انتظار params قبل الاستخدام
        const resolvedParams = await params;
        
        // التحقق من الصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || userData.role !== 'ADMIN') {
            return NextResponse.json(
                { message: "غير مصرح به: يجب أن تكون مسؤولاً لحذف المستخدمين" },
                { status: 403 }
            );
        }

        const userId = parseInt(resolvedParams.id);
        if (isNaN(userId)) {
            return NextResponse.json(
                { message: "معرف المستخدم غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من وجود المستخدم
        const existingUser = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!existingUser) {
            return NextResponse.json(
                { message: "المستخدم غير موجود" },
                { status: 404 }
            );
        }

        // حذف المستخدم (سيتم حذف الملف الشخصي تلقائيًا بسبب علاقة Cascade)
        await prisma.user.delete({
            where: { id: userId }
        });

        return NextResponse.json({
            message: "تم حذف المستخدم بنجاح"
        });
    } catch (error) {
        console.error('Error deleting user:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء حذف المستخدم" },
            { status: 500 }
        );
    }
}

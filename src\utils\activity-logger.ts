import prisma from '@/lib/prisma';

/**
 * خدمة لتسجيل النشاطات في النظام
 */
export const ActivityLogger = {
  /**
   * تسجيل نشاط جديد
   * @param userId معرف المستخدم
   * @param type نوع النشاط
   * @param description وصف النشاط
   * @returns وعد بالنشاط المسجل
   */
  async log(userId: number, type: string, description: string) {
    try {
      const activity = await prisma.activity.create({
        data: {
          userId,
          type,
          description,
        },
        include: {
          user: {
            select: {
              username: true,
              profile: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      });
      
      console.log(`تم تسجيل نشاط: ${type} - ${description}`);
      return activity;
    } catch (error) {
      console.error('خطأ في تسجيل النشاط:', error);
      return null;
    }
  }
};

// أنواع النشاطات المدعومة
export enum ActivityType {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  REGISTRATION = 'REGISTRATION',
  PAYMENT = 'PAYMENT',
  ATTENDANCE = 'ATTENDANCE',
  UPDATE = 'UPDATE',
  EXAM = 'EXAM',
  KHATM = 'KHATM',
  STUDENT_ADD = 'STUDENT_ADD',
  TEACHER_ADD = 'TEACHER_ADD',
  CLASS_ADD = 'CLASS_ADD',
  INVOICE = 'INVOICE', // نوع نشاط جديد للفواتير
  GENERAL = 'GENERAL'
}

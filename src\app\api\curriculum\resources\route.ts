import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/curriculum/resources - إنشاء مورد جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, type, url, lessonId } = body;

    if (!title || !type || !url || !lessonId) {
      return NextResponse.json(
        { message: "يجب توفير عنوان المورد ونوعه ورابطه ومعرف الدرس" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدرس
    const lesson = await prisma.curriculumLesson.findUnique({
      where: { id: lessonId },
    });

    if (!lesson) {
      return NextResponse.json(
        { message: "الدرس غير موجود" },
        { status: 404 }
      );
    }

    // إنشاء المورد
    const resource = await prisma.curriculumResource.create({
      data: {
        title,
        type,
        url,
        lessonId,
      },
    });

    return NextResponse.json(resource);
  } catch (error) {
    console.error('Error creating resource:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء المورد" },
      { status: 500 }
    );
  }
}

'use client';

import React, { useState, useEffect } from 'react';
import { FaMicrophone, FaMicrophoneSlash, FaCog, FaVolumeUp } from 'react-icons/fa';
import { Button } from '@/components/ui/button';
import AudioVisualizer from './AudioVisualizer';

interface AudioSettingsProps {
  onAudioChange: (enabled: boolean) => void;
  onQualityChange: (quality: 'low' | 'medium' | 'high') => void;
  onDeviceChange: (deviceId: string) => void;
  onVolumeChange: (volume: number) => void;
  isAudioEnabled: boolean;
  currentQuality?: 'low' | 'medium' | 'high';
  currentDeviceId?: string;
  currentVolume?: number;
}

/**
 * Component for audio settings
 */
const AudioSettings: React.FC<AudioSettingsProps> = ({
  onAudioChange,
  onQualityChange,
  onDeviceChange,
  onVolumeChange,
  isAudioEnabled,
  currentQuality = 'medium',
  currentDeviceId,
  currentVolume = 1.0,
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);

  // Get available audio devices
  useEffect(() => {
    const getDevices = async () => {
      try {
        // Request permission to access media devices
        await navigator.mediaDevices.getUserMedia({ audio: true });

        // Get list of audio devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        setAudioDevices(audioInputs);

        // Set default device if none is selected
        if (!currentDeviceId && audioInputs.length > 0) {
          onDeviceChange(audioInputs[0].deviceId);
        }
      } catch (error) {
        console.error('Error accessing audio devices:', error);
      }
    };

    getDevices();
  }, []);

  // Get audio stream for visualizer
  useEffect(() => {
    if (isAudioEnabled && currentDeviceId) {
      const getAudioStream = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              deviceId: { exact: currentDeviceId },
              echoCancellation: true,
              noiseSuppression: true,
            }
          });
          setAudioStream(stream);
        } catch (error) {
          console.error('Error getting audio stream:', error);
        }
      };

      getAudioStream();
    } else {
      // Stop the stream if audio is disabled
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
        setAudioStream(null);
      }
    }

    return () => {
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [isAudioEnabled, currentDeviceId]);

  // Toggle audio
  const toggleAudio = () => {
    onAudioChange(!isAudioEnabled);
  };

  // Quality options
  const qualityOptions = [
    { value: 'low', label: 'منخفضة (32kbps)' },
    { value: 'medium', label: 'متوسطة (96kbps)' },
    { value: 'high', label: 'عالية (128kbps)' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          {isAudioEnabled ? (
            <FaMicrophone className="text-[var(--primary-color)]" />
          ) : (
            <FaMicrophoneSlash className="text-gray-500" />
          )}
          <h3 className="font-medium">إعدادات الصوت</h3>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={toggleAudio}
            variant={isAudioEnabled ? 'default' : 'outline'}
            className={isAudioEnabled ? 'bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]' : ''}
          >
            {isAudioEnabled ? 'كتم الصوت' : 'تشغيل الصوت'}
          </Button>
          <Button
            onClick={() => setShowSettings(!showSettings)}
            variant="outline"
            size="sm"
            className="p-0 h-8 w-8"
          >
            <FaCog />
          </Button>
        </div>
      </div>

      {/* Audio visualizer */}
      {isAudioEnabled && audioStream && (
        <div className="mb-4">
          <AudioVisualizer stream={audioStream} />
        </div>
      )}

      {showSettings && (
        <div className="space-y-4 border-t pt-4">
          {/* Audio quality */}
          <div>
            <label htmlFor="audio-quality" className="block text-sm font-medium text-gray-700 mb-1">
              جودة الصوت
            </label>
            <select
              id="audio-quality"
              value={currentQuality}
              onChange={(e) => onQualityChange(e.target.value as 'low' | 'medium' | 'high')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
            >
              {qualityOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Audio device selection */}
          <div>
            <label htmlFor="audio-device" className="block text-sm font-medium text-gray-700 mb-1">
              الميكروفون
            </label>
            <select
              id="audio-device"
              value={currentDeviceId}
              onChange={(e) => onDeviceChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
            >
              {audioDevices.map((device) => (
                <option key={device.deviceId} value={device.deviceId}>
                  {device.label || `الميكروفون ${audioDevices.indexOf(device) + 1}`}
                </option>
              ))}
            </select>
          </div>

          {/* Volume control */}
          <div>
            <label htmlFor="volume" className="block text-sm font-medium text-gray-700 mb-1">
              مستوى الصوت
            </label>
            <div className="flex items-center gap-2">
              <FaVolumeUp className="text-gray-500" />
              <input
                id="volume"
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={currentVolume}
                onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioSettings;

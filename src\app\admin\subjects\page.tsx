"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { toast } from 'react-toastify';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FaBook,
  FaPlus,
  FaSync,
  FaLayerGroup,
  FaChalkboardTeacher,
  FaBookOpen
} from "react-icons/fa";
import AddSubjectDialog from './add-subject-dialog';
import EditSubjectDialog from './edit-subject-dialog';
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface Level {
  id: number;
  name: string;
  description?: string;
  order: number;
  _count?: {
    subjects: number;
  };
}

interface Subject {
  id: number;
  name: string;
  description?: string;
  levelId?: number | null;
  level?: Level | null;
  hasStudyPlan: boolean;
  _count?: {
    teacherSubjects: number;
    units: number;
  };
}

export default function SubjectsPage() {
  const searchParams = useSearchParams();

  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [levels, setLevels] = useState<Level[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedSubject, setSelectedSubject] = useState<Subject | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<string>('all');

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    const levelIdParam = searchParams ? searchParams.get('levelId') : null;
    fetchLevels();

    // عند تحميل الصفحة، نتحقق من وجود معرف المستوى في الرابط
    if (levelIdParam) {
      setActiveTab(levelIdParam);
      fetchSubjects(levelIdParam);
    } else {
      fetchSubjects();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchLevels = async () => {
    try {
      const response = await fetch("/api/admin/levels");
      if (!response.ok) throw new Error("Failed to fetch levels");
      const data = await response.json();
      setLevels(data);
    } catch (error) {
      toast.error("حدث خطأ أثناء جلب المستويات");
      console.error(error);
    }
  };

  const fetchSubjects = async (levelId?: string | null) => {
    setIsLoading(true);
    try {
      const url = levelId
        ? `/api/admin/subjects?levelId=${levelId}`
        : "/api/admin/subjects";

      const response = await fetch(url);
      if (!response.ok) throw new Error("Failed to fetch subjects");
      const data = await response.json();
      setSubjects(data);
    } catch (error) {
      toast.error("حدث خطأ أثناء جلب المواد");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === 'all') {
      fetchSubjects();
    } else {
      fetchSubjects(value);
    }
  };



  const handleDeleteSubject = async (id: number) => {
    if (!confirm("هل أنت متأكد من حذف هذه المادة؟")) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/subjects/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete subject");
      }

      toast.success("تم حذف المادة بنجاح");
      fetchSubjects(activeTab !== 'all' ? activeTab : null);
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : "حدث خطأ أثناء حذف المادة");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.subjects.view">
      <div className="container mx-auto p-2 sm:p-4">
      <div className="flex flex-col md:flex-row justify-between items-center mb-4 md:mb-6">
        <h1 className="text-xl sm:text-2xl font-bold mb-3 md:mb-0 flex items-center gap-2">
          <FaBook className="text-[var(--primary-color)]" />
          إدارة المواد الدراسية
        </h1>
        <div className="flex flex-wrap gap-2 w-full md:w-auto justify-center md:justify-end">
          <QuickActionButtons
            entityType="subjects"
            actions={[
              {
                key: 'create',
                label: 'إضافة مادة',
                icon: <FaPlus />,
                onClick: () => setIsAddDialogOpen(true),
                variant: 'primary'
              }
            ]}
            className="w-full sm:w-auto"
          />
          <Link href="/admin/levels" className="w-full sm:w-auto">
            <Button
              variant="outline"
              className="flex items-center gap-1 text-xs sm:text-sm w-full sm:w-auto"
            >
              <FaLayerGroup /> إدارة المستويات
            </Button>
          </Link>
          <Button
            onClick={() => fetchSubjects(activeTab !== 'all' ? activeTab : null)}
            variant="outline"
            className="flex items-center gap-1 text-xs sm:text-sm w-full sm:w-auto"
            disabled={isLoading}
          >
            <FaSync className={isLoading ? "animate-spin" : ""} /> تحديث
          </Button>
        </div>
      </div>

      <div className="mb-4 md:mb-6 overflow-hidden">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <div className="overflow-x-auto pb-2">
            <TabsList className="w-max min-w-full flex flex-nowrap">
              <TabsTrigger value="all" className="flex-shrink-0 text-xs sm:text-sm">
                جميع المواد
              </TabsTrigger>
              {levels.map(level => (
                <TabsTrigger key={level.id} value={level.id.toString()} className="flex-shrink-0 text-xs sm:text-sm">
                  {level.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
        </Tabs>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4">#</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4">اسم المادة</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">المستوى</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">المعلمين</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">الوحدات</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">الخطة</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {subjects.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4 text-sm">
                    {isLoading ? "جاري التحميل..." : "لا توجد مواد"}
                  </TableCell>
                </TableRow>
              ) : (
                subjects.map((subject) => (
                  <TableRow key={subject.id}>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4">{subject.id}</TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4">
                      <Link
                        href={`/admin/subjects/${subject.id}`}
                        className="text-blue-600 hover:underline flex items-center gap-1"
                      >
                        <FaBook className="text-[var(--primary-color)]" /> {subject.name}
                      </Link>
                      {subject.description && (
                        <p className="text-xs text-gray-500 mt-1 hidden sm:block">{subject.description}</p>
                      )}
                      <div className="flex flex-wrap gap-1 mt-1 sm:hidden">
                        {subject.level && (
                          <Badge variant="outline" className="bg-[#e0f2ef] text-[var(--primary-color)] border-[var(--primary-color)] text-xs">
                            {subject.level.name}
                          </Badge>
                        )}
                        <Badge variant="secondary" className="text-xs">
                          {subject._count?.teacherSubjects || 0} معلم
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {subject._count?.units || 0} وحدة
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">
                      {subject.level ? (
                        <Badge variant="outline" className="bg-[#e0f2ef] text-[var(--primary-color)] border-[var(--primary-color)]">
                          <FaLayerGroup className="mr-1" /> {subject.level.name}
                        </Badge>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">
                      <Badge variant="secondary">
                        <FaChalkboardTeacher className="mr-1" /> {subject._count?.teacherSubjects || 0}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">
                      <Badge variant="secondary">
                        <FaBookOpen className="mr-1" /> {subject._count?.units || 0}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">
                      {subject.hasStudyPlan ? (
                        <Badge className="bg-green-100 text-green-800 border-green-300">
                          متوفرة
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-gray-500">
                          غير متوفرة
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4">
                      <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:rtl:space-x-reverse">
                        <Link href={`/admin/subjects/${subject.id}/curriculum`} className="w-full sm:w-auto">
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex items-center gap-1 text-xs w-full sm:w-auto"
                          >
                            <FaBookOpen className="sm:mr-1" /> <span className="hidden sm:inline">المحتوى</span>
                          </Button>
                        </Link>
                        <OptimizedActionButtonGroup
                          entityType="subjects"
                          onEdit={() => {
                            setSelectedSubject(subject);
                            setIsEditDialogOpen(true);
                          }}
                          onDelete={() => handleDeleteSubject(subject.id)}
                          showEdit={true}
                          showDelete={(subject._count?.teacherSubjects || 0) === 0}
                          className="flex-col sm:flex-row gap-2"
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      <AddSubjectDialog
        isOpen={isAddDialogOpen}
        onCloseAction={() => setIsAddDialogOpen(false)}
        onSuccessAction={() => fetchSubjects(activeTab !== 'all' ? activeTab : null)}
        levels={levels}
      />

      <EditSubjectDialog
        isOpen={isEditDialogOpen}
        onCloseAction={() => setIsEditDialogOpen(false)}
        onSuccessAction={() => fetchSubjects(activeTab !== 'all' ? activeTab : null)}
        subject={selectedSubject}
        levels={levels}
      />
      </div>
    </OptimizedProtectedRoute>
  );
}
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/rewards
export async function GET() {
  try {
    const rewards = await prisma.reward.findMany({
      include: {
        _count: {
          select: {
            studentRewards: true
          }
        }
      },
      orderBy: {
        requiredPoints: 'asc'
      }
    });

    return NextResponse.json({
      data: rewards,
      success: true,
      message: 'تم جلب المكافآت بنجاح'
    });
  } catch (error) {
    console.error('Error fetching rewards:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب المكافآت',
      success: false
    }, { status: 500 });
  }
}

// POST /api/rewards
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, requiredPoints, type } = body;

    // التحقق من البيانات المطلوبة
    if (!name || requiredPoints === undefined || !type) {
      return NextResponse.json({
        error: 'الاسم والنقاط المطلوبة والنوع مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة النقاط المطلوبة
    const numericPoints = parseInt(requiredPoints);
    if (isNaN(numericPoints) || numericPoints < 0) {
      return NextResponse.json({
        error: 'يجب أن تكون النقاط المطلوبة رقمًا موجبًا',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة النوع
    const validTypes = ['CERTIFICATE', 'PRIZE', 'BADGE', 'OTHER'];
    if (!validTypes.includes(type)) {
      return NextResponse.json({
        error: 'نوع المكافأة غير صالح. يجب أن يكون أحد القيم التالية: CERTIFICATE, PRIZE, BADGE, OTHER',
        success: false
      }, { status: 400 });
    }

    const reward = await prisma.reward.create({
      data: {
        name,
        description,
        requiredPoints: numericPoints,
        type
      }
    });

    return NextResponse.json({
      data: reward,
      success: true,
      message: 'تم إنشاء المكافأة بنجاح'
    });
  } catch (error) {
    console.error('Error creating reward:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إنشاء المكافأة',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/rewards
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, description, requiredPoints, type } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !name || requiredPoints === undefined || !type) {
      return NextResponse.json({
        error: 'المعرف والاسم والنقاط المطلوبة والنوع مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة النقاط المطلوبة
    const numericPoints = parseInt(requiredPoints);
    if (isNaN(numericPoints) || numericPoints < 0) {
      return NextResponse.json({
        error: 'يجب أن تكون النقاط المطلوبة رقمًا موجبًا',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة النوع
    const validTypes = ['CERTIFICATE', 'PRIZE', 'BADGE', 'OTHER'];
    if (!validTypes.includes(type)) {
      return NextResponse.json({
        error: 'نوع المكافأة غير صالح. يجب أن يكون أحد القيم التالية: CERTIFICATE, PRIZE, BADGE, OTHER',
        success: false
      }, { status: 400 });
    }

    const reward = await prisma.reward.update({
      where: { id: parseInt(id) },
      data: {
        name,
        description,
        requiredPoints: numericPoints,
        type
      }
    });

    return NextResponse.json({
      data: reward,
      success: true,
      message: 'تم تحديث المكافأة بنجاح'
    });
  } catch (error) {
    console.error('Error updating reward:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث المكافأة',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/rewards
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف المكافأة مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود طلاب حصلوا على هذه المكافأة
    const studentRewardCount = await prisma.studentReward.count({
      where: { rewardId: parseInt(id) }
    });

    if (studentRewardCount > 0) {
      return NextResponse.json({
        error: `لا يمكن حذف المكافأة لأنها ممنوحة لـ ${studentRewardCount} طالب`,
        success: false
      }, { status: 400 });
    }

    await prisma.reward.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف المكافأة بنجاح'
    });
  } catch (error) {
    console.error('Error deleting reward:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف المكافأة',
      success: false
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/settings - الحصول على إعدادات الموقع
export async function GET() {
  try {
    // البحث عن إعدادات الموقع في قاعدة البيانات
    const siteSettings = await prisma.systemSettings.findUnique({
      where: { key: 'SITE_SETTINGS' },
    });

    // إعدادات الموقع الافتراضية
    const defaultSettings = {
      siteName: 'نظام برهان للقرآن الكريم',
      siteDescription: 'منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد',
      logoUrl: '/logo.svg',
      faviconUrl: '/favicon.ico',
      primaryColor: 'var(--primary-color)',
      secondaryColor: 'var(--secondary-color)',
      sidebarColor: '#1a202c',
      backgroundColor: '#f3f4f6',
      accentColor: '#10b981',
      textColor: '#1f2937',
      registrationEnabled: true,
      headerLinks: [
        { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
        { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
        { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
        { id: '4', title: 'مجالس الختم', url: '/khatm-sessions', isActive: true, order: 4 },
        { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
        { id: '6', title: 'اتصل بنا', url: '/contact', isActive: true, order: 6 },
      ],
      footerLinks: [
        { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
        { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
        { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
        { id: '4', title: 'اتصل بنا', url: '/contact', isActive: true, order: 4 },
        { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
        { id: '6', title: 'التسجيل', url: '/register', isActive: true, order: 6 },
        { id: '7', title: 'تسجيل الدخول', url: '/login', isActive: true, order: 7 },
      ],
      socialLinks: {
        facebook: 'https://facebook.com',
        twitter: 'https://twitter.com',
        instagram: 'https://instagram.com',
        youtube: 'https://youtube.com',
      },
      contactInfo: {
        email: '<EMAIL>',
        phone: '+213 123 456 789',
        address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر',
        donationInfo: {
          phone1: '+213 123 456 789',
          phone2: '+213 987 654 321',
          ccpAccount: '**********',
          cpaAccount: '**********',
          bdrAccount: '**********',
          description: 'يمكنكم التبرع من خلال الحسابات التالية أو التواصل معنا هاتفياً'
        }
      }
    };

    // إذا لم تكن هناك إعدادات مخزنة، استخدم الإعدادات الافتراضية
    const settings = siteSettings
      ? JSON.parse(siteSettings.value)
      : defaultSettings;

    return NextResponse.json({
      settings,
      success: true,
    });
  } catch (error) {
    console.error('خطأ في جلب إعدادات الموقع:', error);
    return NextResponse.json(
      { error: 'فشل في جلب إعدادات الموقع', success: false },
      { status: 500 }
    );
  }
}

// POST /api/settings - تحديث إعدادات الموقع
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { settings } = body;

    // التحقق من وجود الإعدادات
    if (!settings) {
      return NextResponse.json(
        { error: 'لم يتم توفير إعدادات الموقع', success: false },
        { status: 400 }
      );
    }

    // تحديث إعدادات الموقع في قاعدة البيانات
    const updatedSettings = await prisma.systemSettings.upsert({
      where: { key: 'SITE_SETTINGS' },
      update: {
        value: JSON.stringify(settings),
      },
      create: {
        key: 'SITE_SETTINGS',
        value: JSON.stringify(settings),
      },
    });

    return NextResponse.json({
      success: true,
      settings: JSON.parse(updatedSettings.value),
      message: 'تم تحديث إعدادات الموقع بنجاح'
    });
  } catch (error) {
    console.error('خطأ في تحديث إعدادات الموقع:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث إعدادات الموقع', success: false },
      { status: 500 }
    );
  }
}

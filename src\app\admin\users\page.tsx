"use client";

import { useState, useEffect } from 'react';
import {
  FaEdit, FaTrash, FaUserPlus, FaSearch, FaSpinner,
  FaSort, FaSortUp, FaSortDown, FaUserTag, FaEye,
  FaUsers
} from 'react-icons/fa';
import UserForm from './components/UserForm';
import UserRoleManager from './components/UserRoleManager';
import UserDetailsModal from './components/UserDetailsModal';
import Modal from '@/components/ui/Modal';
import { toast } from 'react-hot-toast';
import './mobile-responsive.css';
import './enhanced-forms.css';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface User {
  id: string;
  originalId: number;
  name: string;
  username: string;
  email?: string | null;
  role: string;
  roleId?: number | null;
  phone?: string | null;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  lastLogin?: string | null;
  userType: 'registered' | 'student' | 'parent';
  additionalInfo?: any;
  teacher?: any;
  employee?: any;
  profile?: {
    name: string;
    phone?: string;
  } | null;
  userRole?: {
    displayName: string;
    name: string;
  } | null;
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const [showRoleModal, setShowRoleModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedUserForEdit, setSelectedUserForEdit] = useState<UserForEdit | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  // فلاتر متقدمة
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [userTypeFilter, setUserTypeFilter] = useState('all');
  const [sortField, setSortField] = useState<keyof User>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // إحصائيات
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    admins: 0,
    teachers: 0,
    students: 0,
    employees: 0,
    parents: 0
  });

  // جلب بيانات المستخدمين
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/users/enhanced');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في جلب بيانات المستخدمين');
      }

      const data = await response.json();
      setUsers(data.users || []);
      setStats(data.stats || stats);
      setError(null);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء جلب بيانات المستخدمين';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // تصفية وترتيب المستخدمين
  const getFilteredAndSortedUsers = () => {
    const filtered = users.filter(user => {
      // فلتر البحث
      const searchMatch = (user.profile?.name?.toLowerCase() || user.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (user.email?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (user.username?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (user.role?.toLowerCase() || '').includes(searchTerm.toLowerCase());

      // فلتر الدور
      const roleMatch = roleFilter === 'all' || user.role === roleFilter;

      // فلتر الحالة
      const statusMatch = statusFilter === 'all' || user.status === statusFilter;

      // فلتر نوع المستخدم
      const userTypeMatch = userTypeFilter === 'all' || user.userType === userTypeFilter;

      return searchMatch && roleMatch && statusMatch && userTypeMatch;
    });

    // ترتيب النتائج
    return filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // معالجة خاصة للتواريخ
      if (sortField === 'createdAt' || sortField === 'updatedAt' || sortField === 'lastLogin') {
        aValue = aValue ? new Date(aValue as string).getTime() : 0;
        bValue = bValue ? new Date(bValue as string).getTime() : 0;
      }

      // معالجة خاصة للنصوص
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      // التعامل مع القيم undefined
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return 1;
      if (bValue === undefined) return -1;

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  };

  const filteredUsers = getFilteredAndSortedUsers();

  // Pagination
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage);

  // دوال الترتيب
  const handleSort = (field: keyof User) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // دوال التحديد المتعدد
  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === paginatedUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(paginatedUsers.map(user => user.id));
    }
  };

  // إجراءات مجمعة
  const handleBulkAction = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedUsers.length === 0) {
      toast.error('يرجى اختيار مستخدمين أولاً');
      return;
    }

    const confirmMessage = action === 'delete'
      ? `هل أنت متأكد من حذف ${selectedUsers.length} مستخدم؟`
      : `هل أنت متأكد من ${action === 'activate' ? 'تفعيل' : 'إلغاء تفعيل'} ${selectedUsers.length} مستخدم؟`;

    if (!window.confirm(confirmMessage)) return;

    try {
      // تحويل المعرفات المركبة إلى المعرفات الأصلية
      const originalIds = selectedUsers.map(userId => {
        const user = users.find(u => u.id === userId);
        return user ? user.originalId : null;
      }).filter(id => id !== null);

      if (originalIds.length === 0) {
        toast.error('لم يتم العثور على المستخدمين المحددين');
        return;
      }

      const response = await fetch('/api/admin/users/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userIds: originalIds, action })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في تنفيذ العملية');
      }

      toast.success('تم تنفيذ العملية بنجاح');
      setSelectedUsers([]);
      fetchUsers();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء تنفيذ العملية';
      toast.error(errorMessage);
    }
  };

  // إضافة مستخدم جديد
  const handleAddUser = () => {
    setShowAddModal(true);
  };

  // تعديل مستخدم
  const handleEditUser = (user: User) => {
    // تحويل البيانات للتنسيق المطلوب في UserForm
    const userForEdit: UserForEdit = {
      id: user.originalId, // استخدام originalId بدلاً من id المركب
      name: user.profile?.name || user.name,
      username: user.username,
      email: user.email || '',
      role: user.role,
      phone: user.profile?.phone || user.phone || '',
      status: user.status
    };
    setSelectedUserForEdit(userForEdit);
    setShowEditModal(true);
  };

  // إدارة دور المستخدم
  const handleManageRole = (user: User) => {
    setSelectedUser(user);
    setShowRoleModal(true);
  };

  // عرض تفاصيل المستخدم
  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setShowDetailsModal(true);
  };

  // حذف مستخدم
  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      try {
        // استخراج المعرف الأصلي من المعرف المركب
        const user = users.find(u => u.id === userId);
        if (!user) {
          toast.error('المستخدم غير موجود');
          return;
        }

        const response = await fetch(`/api/users/${user.originalId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'فشل في حذف المستخدم');
        }

        toast.success('تم حذف المستخدم بنجاح');
        // تحديث قائمة المستخدمين
        setUsers(users.filter(user => user.id !== userId));
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء حذف المستخدم';
        toast.error(errorMessage);
      }
    }
  };

  // تعريف أنواع البيانات
  interface CreateUserData {
    name: string;
    username: string;
    email?: string;
    role: string;
    phone?: string;
    status: 'active' | 'inactive';
    password: string;
  }

  interface UpdateUserData {
    name: string;
    username?: string;
    email?: string;
    role?: string;
    phone?: string;
    status?: 'active' | 'inactive';
    password?: string;
  }

  interface UserForEdit {
    id: number;
    name: string;
    username: string;
    email: string;
    role: string;
    phone: string;
    status: 'active' | 'inactive';
  }

  // إضافة مستخدم جديد
  const handleCreateUser = async (userData: CreateUserData) => {
    try {
      const response = await fetch('/api/users/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في إنشاء المستخدم');
      }

      const data = await response.json();
      toast.success('تم إنشاء المستخدم بنجاح');
      setShowAddModal(false);

      // إضافة المستخدم الجديد إلى القائمة
      setUsers([data.user, ...users]);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء إنشاء المستخدم';
      toast.error(errorMessage);
    }
  };

  // تحديث بيانات مستخدم
  const handleUpdateUser = async (userData: UpdateUserData) => {
    if (!selectedUserForEdit) return;

    try {
      // استخدام originalId للتحديث
      const originalUser = users.find(u => u.originalId === selectedUserForEdit.id);
      if (!originalUser) {
        toast.error('المستخدم غير موجود');
        return;
      }

      const response = await fetch(`/api/users/${originalUser.originalId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في تحديث بيانات المستخدم');
      }

      await response.json();
      toast.success('تم تحديث بيانات المستخدم بنجاح');
      setShowEditModal(false);
      setSelectedUserForEdit(null);

      // إعادة تحميل البيانات لضمان التحديث الصحيح
      fetchUsers();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء تحديث بيانات المستخدم';
      toast.error(errorMessage);
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.users.view">
      <div className="container mx-auto px-4 py-4 sm:py-6">
      <div className="mb-6 space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            <FaUsers className="text-[var(--primary-color)]" />
            إدارة المستخدمين
          </h1>

          {/* إحصائيات سريعة */}
          <div className="flex flex-wrap gap-2 text-sm">
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
              المجموع: {stats.total}
            </span>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
              نشط: {stats.active}
            </span>
            <span className="bg-red-100 text-red-800 px-2 py-1 rounded">
              غير نشط: {stats.inactive}
            </span>
          </div>
        </div>

        {/* أدوات البحث والفلترة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* شريط البحث */}
          <div className="relative md:col-span-2">
            <input
              type="text"
              placeholder="البحث بالاسم، البريد الإلكتروني، أو اسم المستخدم..."
              className="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] mobile-input search-input"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>

          {/* فلتر نوع المستخدم */}
          <select
            value={userTypeFilter}
            onChange={(e) => setUserTypeFilter(e.target.value)}
            className="px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
          >
            <option value="all">جميع الأنواع</option>
            <option value="registered">مستخدمون مسجلون</option>
            <option value="student">طلاب</option>
            <option value="parent">أولياء أمور</option>
          </select>

          {/* فلتر الدور */}
          <select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            className="px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
          >
            <option value="all">جميع الأدوار</option>
            <option value="ADMIN">مدير</option>
            <option value="EMPLOYEE">موظف</option>
            <option value="TEACHER">معلم</option>
            <option value="STUDENT">طالب</option>
            <option value="PARENT">ولي أمر</option>
          </select>

          {/* فلتر الحالة */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
          >
            <option value="all">جميع الحالات</option>
            <option value="active">نشط</option>
            <option value="inactive">غير نشط</option>
          </select>
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <QuickActionButtons
            entityType="users"
            actions={[
              {
                key: 'create',
                label: 'إضافة مستخدم',
                icon: <FaUserPlus />,
                onClick: handleAddUser,
                variant: 'primary'
              }
            ]}
            className="flex-wrap gap-2"
          />

          {/* إجراءات مجمعة */}
          {selectedUsers.length > 0 && (
            <PermissionGuard requiredPermission="admin.users.bulk">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm text-gray-600 flex items-center">
                  تم تحديد {selectedUsers.length} مستخدم
                </span>
                <button
                  onClick={() => handleBulkAction('activate')}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                >
                  تفعيل
                </button>
                <button
                  onClick={() => handleBulkAction('deactivate')}
                  className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700 transition-colors"
                >
                  إلغاء تفعيل
                </button>
                <button
                  onClick={() => handleBulkAction('delete')}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                >
                  حذف
                </button>
              </div>
            </PermissionGuard>
          )}
        </div>

        {/* عدد العناصر في الصفحة */}
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2 text-sm">
            <span>عرض</span>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="border rounded px-2 py-1"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span>من {filteredUsers.length} مستخدم</span>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <FaSpinner className="animate-spin text-[var(--secondary-color)] text-4xl" />
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
          <span className="block sm:inline">{error}</span>
        </div>
      ) : (
        <>
          {/* Mobile Cards View */}
          <div className="block md:hidden space-y-4">
            {paginatedUsers.length === 0 ? (
              <div className="bg-white rounded-lg shadow p-6 text-center text-gray-500">
                لا توجد بيانات للعرض
              </div>
            ) : (
              paginatedUsers.map((user) => (
                <div key={user.id} className="bg-white rounded-lg shadow p-4 space-y-3 user-card">
                  {/* User Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-10 h-10 bg-[var(--primary-color)] rounded-full flex items-center justify-center text-white font-bold">
                        {user.name.charAt(0)}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{user.name}</h3>
                        <p className="text-sm text-gray-500">@{user.username}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleViewUser(user)}
                        className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                        title="عرض التفاصيل"
                      >
                        <FaEye className="h-4 w-4" />
                      </button>
                      <PermissionGuard requiredPermission="admin.users.edit">
                        <button
                          onClick={() => handleEditUser(user)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          title="تعديل"
                        >
                          <FaEdit className="h-4 w-4" />
                        </button>
                      </PermissionGuard>
                      <PermissionGuard requiredPermission="admin.users.delete">
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="حذف"
                        >
                          <FaTrash className="h-4 w-4" />
                        </button>
                      </PermissionGuard>
                    </div>
                  </div>

                  {/* User Details */}
                  <div className="space-y-2">
                    {user.email && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">البريد الإلكتروني:</span>
                        <span className="text-sm text-gray-900">{user.email}</span>
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">الدور:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {user.role === 'ADMIN' && 'مدير'}
                        {user.role === 'TEACHER' && 'معلم'}
                        {user.role === 'STUDENT' && 'طالب'}
                        {user.role === 'PARENT' && 'ولي أمر'}
                        {user.role === 'EMPLOYEE' && 'موظف'}
                        {user.role === 'PENDING' && 'في انتظار التعيين'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">الحالة:</span>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {user.status === 'active' ? 'نشط' : 'غير نشط'}
                      </span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Desktop Table View */}
          <div className="hidden md:block bg-white rounded-lg shadow overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right">
                    <input
                      type="checkbox"
                      checked={selectedUsers.length === paginatedUsers.length && paginatedUsers.length > 0}
                      onChange={handleSelectAll}
                      className="w-4 h-4 text-[var(--primary-color)] border-gray-300 rounded focus:ring-[var(--primary-color)]"
                    />
                  </th>
                  <th
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center gap-1">
                      الاسم
                      {sortField === 'name' && (
                        sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                      )}
                      {sortField !== 'name' && <FaSort className="opacity-50" />}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('username')}
                  >
                    <div className="flex items-center gap-1">
                      اسم المستخدم
                      {sortField === 'username' && (
                        sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                      )}
                      {sortField !== 'username' && <FaSort className="opacity-50" />}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد الإلكتروني</th>
                  <th
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('role')}
                  >
                    <div className="flex items-center gap-1">
                      الدور
                      {sortField === 'role' && (
                        sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                      )}
                      {sortField !== 'role' && <FaSort className="opacity-50" />}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('status')}
                  >
                    <div className="flex items-center gap-1">
                      الحالة
                      {sortField === 'status' && (
                        sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                      )}
                      {sortField !== 'status' && <FaSort className="opacity-50" />}
                    </div>
                  </th>
                  <th
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('createdAt')}
                  >
                    <div className="flex items-center gap-1">
                      تاريخ الإنشاء
                      {sortField === 'createdAt' && (
                        sortDirection === 'asc' ? <FaSortUp /> : <FaSortDown />
                      )}
                      {sortField !== 'createdAt' && <FaSort className="opacity-50" />}
                    </div>
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paginatedUsers.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      لا توجد بيانات للعرض
                    </td>
                  </tr>
                ) : (
                  paginatedUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => handleSelectUser(user.id)}
                          className="w-4 h-4 text-[var(--primary-color)] border-gray-300 rounded focus:ring-[var(--primary-color)]"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-[var(--primary-color)] rounded-full flex items-center justify-center text-white text-sm font-bold mr-3">
                            {(user.profile?.name || user.name).charAt(0)}
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {user.profile?.name || user.name}
                            </div>
                            {user.profile?.phone && (
                              <div className="text-sm text-gray-500">{user.profile.phone}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{user.username}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900">{user.email || '-'}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-900">
                            {user.userRole?.displayName || (
                              user.role === 'ADMIN' ? 'مدير' :
                              user.role === 'TEACHER' ? 'معلم' :
                              user.role === 'STUDENT' ? 'طالب' :
                              user.role === 'PARENT' ? 'ولي أمر' :
                              user.role === 'EMPLOYEE' ? 'موظف' :
                              'في انتظار التعيين'
                            )}
                          </span>
                          <PermissionGuard requiredPermission="admin.users.roles">
                            <button
                              onClick={() => handleManageRole(user)}
                              className="text-blue-600 hover:text-blue-900 p-1 hover:bg-blue-50 rounded transition-colors"
                              title="إدارة الدور"
                            >
                              <FaUserTag className="h-3 w-3" />
                            </button>
                          </PermissionGuard>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {user.status === 'active' ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-500 text-sm">
                        {user.createdAt ? new Date(user.createdAt).toLocaleDateString('fr-FR') : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleViewUser(user)}
                            className="text-gray-600 hover:text-gray-900 p-1 hover:bg-gray-50 rounded transition-colors"
                            title="عرض التفاصيل"
                          >
                            <FaEye className="h-4 w-4" />
                          </button>
                          <OptimizedActionButtonGroup
                            entityType="users"
                            onEdit={() => handleEditUser(user)}
                            onDelete={() => handleDeleteUser(user.id)}
                            showEdit={true}
                            showDelete={true}
                            className="gap-2"
                          />
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredUsers.length > itemsPerPage && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  السابق
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  التالي
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    عرض{' '}
                    <span className="font-medium">{startIndex + 1}</span>
                    {' '}إلى{' '}
                    <span className="font-medium">
                      {Math.min(startIndex + itemsPerPage, filteredUsers.length)}
                    </span>
                    {' '}من{' '}
                    <span className="font-medium">{filteredUsers.length}</span>
                    {' '}نتيجة
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">السابق</span>
                      <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>

                    {/* أرقام الصفحات */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNumber = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                      if (pageNumber <= totalPages) {
                        return (
                          <button
                            key={pageNumber}
                            onClick={() => setCurrentPage(pageNumber)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              currentPage === pageNumber
                                ? 'z-10 bg-[var(--primary-color)] border-[var(--primary-color)] text-white'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {pageNumber}
                          </button>
                        );
                      }
                      return null;
                    })}

                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="sr-only">التالي</span>
                      <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Add User Modal */}
      {showAddModal && (
        <Modal
          isOpen={showAddModal}
          onCloseAction={() => setShowAddModal(false)}
          title="إضافة مستخدم جديد"
          maxWidth="lg"
        >
          <UserForm
            onSubmitAction={(userData) => {
              // Type assertion to handle the union type
              handleCreateUser(userData as CreateUserData);
            }}
            onCloseAction={() => setShowAddModal(false)}
          />
        </Modal>
      )}

      {/* Edit User Modal */}
      {showEditModal && selectedUserForEdit && (
        <Modal
          isOpen={showEditModal}
          onCloseAction={() => {
            setShowEditModal(false);
            setSelectedUserForEdit(null);
          }}
          title="تعديل بيانات المستخدم"
          maxWidth="lg"
        >
          <UserForm
            user={selectedUserForEdit}
            isEdit
            onSubmitAction={(userData) => {
              // Type assertion to handle the union type
              handleUpdateUser(userData as UpdateUserData);
            }}
            onCloseAction={() => {
              setShowEditModal(false);
              setSelectedUserForEdit(null);
            }}
          />
        </Modal>
      )}



      {/* User Role Manager Modal */}
      {showRoleModal && selectedUser && (
        <UserRoleManager
          user={selectedUser}
          onRoleUpdated={fetchUsers}
          onClose={() => {
            setShowRoleModal(false);
            setSelectedUser(null);
          }}
        />
      )}

      {/* User Details Modal */}
      {showDetailsModal && selectedUser && (
        <UserDetailsModal
          user={selectedUser}
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedUser(null);
          }}
        />
      )}
      </div>
    </OptimizedProtectedRoute>
  );
}
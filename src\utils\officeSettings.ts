import React from 'react';

/**
 * مساعد لإدارة إعدادات المكتب البلدي
 */

export interface OfficeSettings {
  organizationName: string;
  officeName: string;
  branchName: string;
  presidentName: string;
  presidentTitle: string;
  logoUrl?: string;
  address?: string;
  phone?: string;
  email?: string;
}

/**
 * الإعدادات الافتراضية للمكتب البلدي
 */
export const defaultOfficeSettings: OfficeSettings = {
  organizationName: 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
  officeName: 'المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر',
  branchName: 'شعبة بلدية المنقر',
  presidentName: 'الوليد بن ناصر قصي',
  presidentTitle: 'رئيس المكتب البلدي',
  logoUrl: '/images/association-logo.svg',
  address: '',
  phone: '',
  email: '',
};

/**
 * حفظ إعدادات المكتب البلدي في localStorage
 */
export const saveOfficeSettings = (settings: OfficeSettings): boolean => {
  try {
    localStorage.setItem('officeSettings', JSON.stringify(settings));
    return true;
  } catch (error) {
    console.error('Error saving office settings to localStorage:', error);
    return false;
  }
};

/**
 * جلب إعدادات المكتب البلدي من localStorage
 */
export const loadOfficeSettings = (): OfficeSettings => {
  try {
    const saved = localStorage.getItem('officeSettings');
    if (saved) {
      const parsed = JSON.parse(saved);
      // دمج مع الإعدادات الافتراضية لضمان وجود جميع الحقول
      return { ...defaultOfficeSettings, ...parsed };
    }
  } catch (error) {
    console.error('Error loading office settings from localStorage:', error);
  }
  return defaultOfficeSettings;
};

/**
 * جلب إعدادات المكتب البلدي من API
 */
export const fetchOfficeSettings = async (): Promise<OfficeSettings> => {
  try {
    const response = await fetch('/api/settings');
    if (response.ok) {
      const data = await response.json();
      if (data.settings?.officeSettings) {
        // حفظ في localStorage للاستخدام السريع
        saveOfficeSettings(data.settings.officeSettings);
        return { ...defaultOfficeSettings, ...data.settings.officeSettings };
      }
    }
  } catch (error) {
    console.error('Error fetching office settings from API:', error);
  }
  
  // إذا فشل جلب من API، جرب localStorage
  return loadOfficeSettings();
};

/**
 * حفظ إعدادات المكتب البلدي في قاعدة البيانات
 */
export const saveOfficeSettingsToAPI = async (settings: OfficeSettings): Promise<boolean> => {
  try {
    // أولاً، جلب الإعدادات الحالية
    const currentResponse = await fetch('/api/settings');
    let currentSettings = {};
    
    if (currentResponse.ok) {
      const data = await currentResponse.json();
      currentSettings = data.settings || {};
    }

    // دمج إعدادات المكتب البلدي مع الإعدادات الحالية
    const updatedSettings = {
      ...currentSettings,
      officeSettings: settings
    };

    // حفظ في قاعدة البيانات
    const response = await fetch('/api/settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ settings: updatedSettings }),
    });

    if (response.ok) {
      // حفظ في localStorage أيضاً
      saveOfficeSettings(settings);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Error saving office settings to API:', error);
    return false;
  }
};

/**
 * تحديث إعدادات المكتب البلدي
 */
export const updateOfficeSettings = async (updates: Partial<OfficeSettings>): Promise<OfficeSettings> => {
  const currentSettings = await fetchOfficeSettings();
  const updatedSettings = { ...currentSettings, ...updates };
  
  // حفظ التحديثات
  await saveOfficeSettingsToAPI(updatedSettings);
  
  return updatedSettings;
};

/**
 * إعادة تعيين إعدادات المكتب البلدي إلى القيم الافتراضية
 */
export const resetOfficeSettings = async (): Promise<OfficeSettings> => {
  await saveOfficeSettingsToAPI(defaultOfficeSettings);
  return defaultOfficeSettings;
};

/**
 * التحقق من صحة إعدادات المكتب البلدي
 */
export const validateOfficeSettings = (settings: Partial<OfficeSettings>): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (!settings.organizationName?.trim()) {
    errors.push('اسم الجمعية مطلوب');
  }

  if (!settings.officeName?.trim()) {
    errors.push('اسم المكتب البلدي مطلوب');
  }

  if (!settings.branchName?.trim()) {
    errors.push('اسم الشعبة مطلوب');
  }

  if (!settings.presidentName?.trim()) {
    errors.push('اسم رئيس المكتب مطلوب');
  }

  if (!settings.presidentTitle?.trim()) {
    errors.push('منصب رئيس المكتب مطلوب');
  }

  if (settings.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.email)) {
    errors.push('البريد الإلكتروني غير صحيح');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Hook لاستخدام إعدادات المكتب البلدي في React
 */
export const useOfficeSettings = () => {
  const [settings, setSettings] = React.useState<OfficeSettings>(defaultOfficeSettings);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true);
      try {
        const officeSettings = await fetchOfficeSettings();
        setSettings(officeSettings);
      } catch (error) {
        console.error('Error loading office settings:', error);
        setSettings(defaultOfficeSettings);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  const updateSettings = async (updates: Partial<OfficeSettings>) => {
    try {
      const updatedSettings = await updateOfficeSettings(updates);
      setSettings(updatedSettings);
      return updatedSettings;
    } catch (error) {
      console.error('Error updating office settings:', error);
      throw error;
    }
  };

  return {
    settings,
    isLoading,
    updateSettings,
    resetSettings: async () => {
      const resetSettings = await resetOfficeSettings();
      setSettings(resetSettings);
      return resetSettings;
    }
  };
};

"use client"
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { <PERSON>aQuran, FaChalkboard<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ser<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON> } from 'react-icons/fa'
import { MdOnlinePrediction } from 'react-icons/md'
import SiteLogo from '@/components/SiteLogo'
import PageBackground from '@/components/PageBackground'

interface ProgramFeature {
  id: number;
  text: string;
  programId: number;
  order: number;
}

interface Program {
  id: number;
  title: string;
  description: string;
  iconName: string;
  price: string;
  popular: boolean;
  features: ProgramFeature[];
  createdAt: string;
  updatedAt: string;
}

const HomePage = () => {
  const [programs, setPrograms] = useState<Program[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        const response = await fetch('/api/programs')
        if (!response.ok) throw new Error('Failed to fetch programs')
        const data = await response.json()
        setPrograms(data)
      } catch (err) {
        console.error('Error fetching programs:', err)
        setError('حدث خطأ أثناء تحميل البرامج')
      } finally {
        setLoading(false)
      }
    }

    fetchPrograms()
  }, [])

  // دالة لتحديد الأيقونة المناسبة بناءً على اسم الأيقونة
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'FaQuran':
        return <FaQuran className="text-5xl text-white mx-auto mb-4" />
      case 'FaBookReader':
        return <FaChalkboardTeacher className="text-5xl text-white mx-auto mb-4" />
      case 'FaMicrophone':
        return <MdOnlinePrediction className="text-5xl text-white mx-auto mb-4" />
      case 'FaGraduationCap':
        return <FaUserGraduate className="text-5xl text-white mx-auto mb-4" />
      default:
        return <FaQuran className="text-5xl text-white mx-auto mb-4" />
    }
  }
  return (
    <PageBackground
      pageName="home"
      className="min-h-screen"
      fallbackBackground="linear-gradient(to bottom, var(--primary-color), var(--secondary-color))"
    >
      <div dir="rtl">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-[var(--primary-color)] to-[var(--secondary-color)] text-white py-20 relative">
        <div className="container mx-auto px-4 text-center">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-8">
            <SiteLogo size="2xl" showText={false} iconColor="white" />
          </div>

          <h1 className="text-4xl md:text-6xl font-bold mb-6">تعلم القرآن الكريم عن بعد</h1>
          <p className="text-xl md:text-2xl mb-8">منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد</p>
          {/*<Link href="/register" className="bg-white text-[var(--primary-color)] px-8 py-3 rounded-full text-lg font-semibold hover:bg-opacity-90 transition-all">
            ابدأ الآن مجاناً
          </Link>*/}
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">مميزات المنصة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaQuran className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">تعليم القرآن الكريم</h3>
              <p className="text-gray-600">تعلم القرآن الكريم بالتجويد والقراءات</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaChalkboardTeacher className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">معلمون متخصصون</h3>
              <p className="text-gray-600">نخبة من المعلمين المتخصصين</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <MdOnlinePrediction className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">دروس مباشرة</h3>
              <p className="text-gray-600">حصص مباشرة عبر الإنترنت</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <FaUserGraduate className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">شهادات معتمدة</h3>
              <p className="text-gray-600">شهادات معتمدة في حفظ القرآن</p>
            </div>
          </div>
        </div>
      </section>

      {/* Courses Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">برامجنا التعليمية</h2>

          {loading ? (
            <div className="flex justify-center items-center py-10">
              <FaSpinner className="text-[var(--primary-color)] text-4xl animate-spin" />
            </div>
          ) : error ? (
            <div className="text-center py-10">
              <p className="text-red-500">{error}</p>
            </div>
          ) : programs.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-gray-500">لا توجد برامج متاحة حالياً</p>
              <Link href="/programs" className="inline-block mt-4 text-[var(--primary-color)] hover:underline">
                عرض جميع البرامج
              </Link>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {programs.slice(0, 3).map((program) => (
                  <div key={program.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="bg-[var(--primary-color)] text-white p-4">
                      <div className="flex justify-center mb-2">
                        {getIconComponent(program.iconName)}
                      </div>
                      <h3 className="text-xl font-semibold text-center">{program.title}</h3>
                    </div>
                    <div className="p-6">
                      <ul className="space-y-3 text-gray-600">
                        {program.features.slice(0, 3).map((feature) => (
                          <li key={feature.id} className="flex items-center">
                            <FaCheck className="text-[var(--primary-color)] ml-2 flex-shrink-0" />
                            {feature.text}
                          </li>
                        ))}
                      </ul>
                      <div className="mt-4 text-center">
                        <p className="text-xl font-bold text-[var(--primary-color)] mb-4">{program.price}</p>
                      </div>
                      {/*<Link href="/register" className="block text-center bg-[var(--primary-color)] text-white py-2 px-4 rounded-md mt-6 hover:bg-opacity-90 transition-all">
                        سجل الآن
                      </Link>*/}
                    </div>
                  </div>
                ))}
              </div>

              {programs.length > 3 && (
                <div className="text-center mt-8">
                  <Link href="/programs" className="inline-block bg-white border border-[var(--primary-color)] text-[var(--primary-color)] px-6 py-2 rounded-md hover:bg-[var(--primary-color)] hover:text-white transition-all">
                    عرض جميع البرامج
                  </Link>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Donations Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-800">ساهم في دعم مشروعنا التعليمي</h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto text-gray-600">
            تبرعك يساعدنا في توفير تعليم قرآني عالي الجودة للطلاب وتطوير المنصة التعليمية
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/donations" className="bg-[var(--primary-color)] text-white px-8 py-3 rounded-full text-lg font-semibold hover:bg-opacity-90 transition-all inline-block">
              تبرع الآن
            </Link>
            <Link href="/donations" className="bg-white border border-[var(--primary-color)] text-[var(--primary-color)] px-8 py-3 rounded-full text-lg font-semibold hover:bg-[#e0f2ef] transition-all inline-block">
              تعرف على حملات التبرع
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      {/*<section className="bg-[var(--primary-color)] text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">ابدأ رحلتك في تعلم القرآن الكريم</h2>
          <p className="text-xl mb-8">سجل الآن واحصل على تجربة مجانية</p>
          <Link href="/register" className="bg-white text-[var(--primary-color)] px-8 py-3 rounded-full text-lg font-semibold hover:bg-opacity-90 transition-all inline-block">
            سجل الآن
          </Link>
        </div>
      </section>*/}
      </div>
    </PageBackground>
  )
}

export default HomePage
"use client";
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  FaCalendarAlt,
  FaClock,
  FaChalkboardTeacher,
  FaUserGraduate,
  FaArrowLeft
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface ScheduleItem {
  id: number;
  day: string;
  startTime: string;
  endTime: string;
  subjectId: number;
  subjectName: string;
  teacherId: number;
  teacherName: string;
}

interface Student {
  id: number;
  name: string;
  grade: string;
}

interface ChildSchedule {
  id: number;
  name: string;
  grade: string;
  schedule: ScheduleItem[];
}

const daysOfWeek = [
  { id: 'SUNDAY', name: 'الأحد' },
  { id: 'MONDAY', name: 'الإثنين' },
  { id: 'TUESDAY', name: 'الثلاثاء' },
  { id: 'WEDNESDAY', name: 'الأربعاء' },
  { id: 'THURSDAY', name: 'الخميس' },
  { id: 'FRIDAY', name: 'الجمعة' },
  { id: 'SATURDAY', name: 'السبت' }
];

const ParentSchedulePage = () => {
  const searchParams = useSearchParams();
  const childId = searchParams ? searchParams.get('childId') : null;

  const [student, setStudent] = useState<Student | null>(null);
  const [schedule, setSchedule] = useState<ScheduleItem[]>([]);
  const [children, setChildren] = useState<ChildSchedule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const url = childId
          ? `/api/parent-schedule?childId=${childId}`
          : '/api/parent-schedule';

        const response = await fetch(url);

        if (!response.ok) {
          // محاولة قراءة رسالة الخطأ من الاستجابة
          const errorData = await response.json();
          const errorMessage = errorData?.message || 'فشل في جلب جدول الحصص';
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log('Schedule data received:', data);

        // التحقق من وجود بيانات
        if (childId) {
          // إذا تم تحديد طالب معين
          if (!data.student) {
            console.warn('No student data in response:', data);
            setError('لم يتم العثور على بيانات الطالب');
          } else {
            setStudent(data.student);
            setSchedule(data.schedule || []);
          }
        } else {
          // إذا لم يتم تحديد طالب (عرض جميع الأبناء)
          if (!data.children) {
            console.warn('No children data in response:', data);
            setChildren([]);
          } else {
            setChildren(data.children);
          }
        }
      } catch (err: unknown) {
        console.error('Error fetching schedule:', err);

        // محاولة الحصول على رسالة خطأ أكثر تفصيلاً من الخادم
        let errorMessage = 'حدث خطأ أثناء جلب جدول الحصص';

        if (err instanceof Error) {
          errorMessage = err.message;
        } else if (err instanceof Response) {
          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await err.json();
            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (jsonError) {
            console.error('Error parsing error response:', jsonError);
          }
        }

        setError(errorMessage);
        toast.error('فشل في جلب جدول الحصص: ' + errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchedule();
  }, [childId]);

  // تنظيم جدول الحصص حسب اليوم
  const getScheduleByDay = (scheduleItems: ScheduleItem[]) => {
    return daysOfWeek.map(day => {
      const daySchedule = scheduleItems.filter(item => item.day === day.id);
      return {
        ...day,
        schedule: daySchedule.sort((a, b) => {
          return a.startTime.localeCompare(b.startTime);
        })
      };
    });
  };

  // تحديد اليوم الحالي
  const getDayOfWeek = (): string => {
    const dayIndex = new Date().getDay(); // 0 = Sunday, 1 = Monday, etc.
    const days = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'];
    return days[dayIndex];
  };

  const today = getDayOfWeek();
  const currentDay = daysOfWeek.find(day => day.id === today)?.id || daysOfWeek[0].id;

  // عرض جدول حصص طالب محدد
  const renderStudentSchedule = () => {
    if (!student) return null;

    const scheduleByDay = getScheduleByDay(schedule);

    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <FaUserGraduate className="text-[var(--primary-color)]" />
              <span>جدول حصص {student.name}</span>
            </h1>
            <p className="text-gray-500">
              الصف: {student.grade}
            </p>
          </div>
          <Link href="/parents/schedule">
            <Button variant="outline" className="flex items-center gap-2">
              <FaArrowLeft />
              <span>العودة إلى جميع الأبناء</span>
            </Button>
          </Link>
        </div>

        {/* ملخص */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-blue-100 text-blue-500">
                  <FaCalendarAlt className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">عدد الحصص</p>
                  <p className="text-2xl font-bold text-gray-800">{schedule.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-green-100 text-primary-color">
                  <FaClock className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">اليوم الحالي</p>
                  <p className="text-2xl font-bold text-gray-800">
                    {daysOfWeek.find(day => day.id === currentDay)?.name}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-purple-100 text-purple-500">
                  <FaChalkboardTeacher className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">عدد المعلمين</p>
                  <p className="text-2xl font-bold text-gray-800">
                    {new Set(schedule.map(item => item.teacherId)).size}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* جدول الحصص */}
        {schedule.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-4">لا يوجد جدول حصص متاح</div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {scheduleByDay.map(day => (
              <Card key={day.id} className={day.schedule.length > 0 ? '' : 'opacity-70'}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaCalendarAlt className="text-[var(--primary-color)]" />
                    <span>{day.name}</span>
                  </CardTitle>
                  <CardDescription>
                    {day.schedule.length > 0
                      ? `${day.schedule.length} حصة`
                      : 'لا توجد حصص في هذا اليوم'}
                  </CardDescription>
                </CardHeader>
                {day.schedule.length > 0 && (
                  <CardContent>
                    <div className="space-y-4">
                      {day.schedule.map(item => (
                        <div
                          key={item.id}
                          className="p-4 border rounded-lg flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
                        >
                          <div>
                            <h3 className="font-semibold text-lg">{item.subjectName}</h3>
                            <p className="text-gray-500">المعلم: {item.teacherName}</p>
                          </div>
                          <div className="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-full">
                            <FaClock className="text-[var(--primary-color)]" />
                            <span>
                              {item.startTime} - {item.endTime}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  };

  // عرض جدول حصص جميع الأبناء
  const renderChildrenSchedules = () => {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">جدول الحصص</h1>
          <p className="text-gray-500">عرض جدول حصص أبنائك</p>
        </div>

        {children.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-4">لا يوجد أبناء مسجلين</div>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue={children[0]?.id.toString()}>
            <TabsList className="mb-4 flex flex-wrap">
              {children.map(child => (
                <TabsTrigger key={child.id} value={child.id.toString()} className="flex items-center gap-2">
                  <FaUserGraduate />
                  <span>{child.name}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {children.map(child => (
              <TabsContent key={child.id} value={child.id.toString()}>
                <Card className="mb-6">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FaUserGraduate className="text-[var(--primary-color)]" />
                      <span>{child.name}</span>
                    </CardTitle>
                    <CardDescription>
                      الصف: {child.grade} • عدد الحصص: {child.schedule.length}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-end">
                      <Link href={`/parents/schedule?childId=${child.id}`}>
                        <Button variant="outline" className="flex items-center gap-2">
                          <span>عرض التفاصيل الكاملة</span>
                          <FaCalendarAlt />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>

                {child.schedule.length === 0 ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center text-gray-500 py-4">لا يوجد جدول حصص متاح</div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="space-y-6">
                    {getScheduleByDay(child.schedule)
                      .filter(day => day.schedule.length > 0)
                      .slice(0, 3) // عرض أول 3 أيام فقط في الملخص
                      .map(day => (
                        <Card key={day.id}>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <FaCalendarAlt className="text-[var(--primary-color)]" />
                              <span>{day.name}</span>
                            </CardTitle>
                            <CardDescription>
                              {day.schedule.length} حصة
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-4">
                              {day.schedule.map(item => (
                                <div
                                  key={item.id}
                                  className="p-4 border rounded-lg flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
                                >
                                  <div>
                                    <h3 className="font-semibold text-lg">{item.subjectName}</h3>
                                    <p className="text-gray-500">المعلم: {item.teacherName}</p>
                                  </div>
                                  <div className="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-full">
                                    <FaClock className="text-[var(--primary-color)]" />
                                    <span>
                                      {item.startTime} - {item.endTime}
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    }
                  </div>
                )}
              </TabsContent>
            ))}
          </Tabs>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : childId ? (
        renderStudentSchedule()
      ) : (
        renderChildrenSchedules()
      )}
    </div>
  );
};

export default ParentSchedulePage;

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedExpenseCategories() {
  try {
    console.log('بدء إنشاء بيانات فئات المصروفات...');

    // إنشاء الفئات الرئيسية
    const mainCategories = [
      {
        name: 'رواتب ومكافآت',
        description: 'رواتب الموظفين والمعلمين والمكافآت',
        icon: 'FaMoneyBillWave',
        color: '#4CAF50',
      },
      {
        name: 'مستلزمات تعليمية',
        description: 'الكتب والقرطاسية والمواد التعليمية',
        icon: 'FaBook',
        color: '#2196F3',
      },
      {
        name: 'مرافق',
        description: 'الكهرباء والماء والإنترنت والهاتف',
        icon: 'FaLightbulb',
        color: '#FFC107',
      },
      {
        name: 'صيانة',
        description: 'صيانة المباني والمعدات',
        icon: 'FaTools',
        color: '#FF5722',
      },
      {
        name: 'تكنولوجيا',
        description: 'أجهزة الكمبيوتر والبرمجيات والمعدات التقنية',
        icon: 'FaLaptop',
        color: '#9C27B0',
      },
      {
        name: 'إيجارات',
        description: 'إيجار المباني والقاعات',
        icon: 'FaBuilding',
        color: '#795548',
      },
      {
        name: 'نقل',
        description: 'وسائل النقل والوقود',
        icon: 'FaBus',
        color: '#607D8B',
      },
      {
        name: 'فعاليات',
        description: 'المسابقات والاحتفالات والفعاليات',
        icon: 'FaCalendarAlt',
        color: '#E91E63',
      },
      {
        name: 'تسويق وإعلان',
        description: 'الإعلانات والتسويق والعلاقات العامة',
        icon: 'FaBullhorn',
        color: '#3F51B5',
      },
      {
        name: 'مصروفات إدارية',
        description: 'المصروفات الإدارية المتنوعة',
        icon: 'FaFileAlt',
        color: '#009688',
      },
    ];

    // إنشاء الفئات الرئيسية
    for (const category of mainCategories) {
      const existingCategory = await prisma.expenseCategory.findFirst({
        where: { name: category.name },
      });

      if (!existingCategory) {
        await prisma.expenseCategory.create({
          data: category,
        });
        console.log(`تم إنشاء فئة: ${category.name}`);
      } else {
        console.log(`الفئة موجودة بالفعل: ${category.name}`);
      }
    }

    // إنشاء الفئات الفرعية
    const subCategories = [
      // رواتب ومكافآت
      {
        name: 'رواتب المعلمين',
        description: 'رواتب المعلمين الشهرية',
        icon: 'FaChalkboardTeacher',
        color: '#66BB6A',
        parentName: 'رواتب ومكافآت',
      },
      {
        name: 'رواتب الإداريين',
        description: 'رواتب الموظفين الإداريين',
        icon: 'FaUserTie',
        color: '#81C784',
        parentName: 'رواتب ومكافآت',
      },
      {
        name: 'مكافآت وحوافز',
        description: 'المكافآت والحوافز للموظفين والمعلمين',
        icon: 'FaAward',
        color: '#A5D6A7',
        parentName: 'رواتب ومكافآت',
      },

      // مستلزمات تعليمية
      {
        name: 'كتب ومراجع',
        description: 'الكتب والمراجع التعليمية',
        icon: 'FaBookOpen',
        color: '#42A5F5',
        parentName: 'مستلزمات تعليمية',
      },
      {
        name: 'قرطاسية',
        description: 'أدوات القرطاسية والمكتبية',
        icon: 'FaPencilAlt',
        color: '#64B5F6',
        parentName: 'مستلزمات تعليمية',
      },
      {
        name: 'وسائل تعليمية',
        description: 'الوسائل التعليمية والتجهيزات',
        icon: 'FaChalkboard',
        color: '#90CAF9',
        parentName: 'مستلزمات تعليمية',
      },

      // مرافق
      {
        name: 'كهرباء',
        description: 'فواتير الكهرباء',
        icon: 'FaBolt',
        color: '#FFEB3B',
        parentName: 'مرافق',
      },
      {
        name: 'ماء',
        description: 'فواتير المياه',
        icon: 'FaTint',
        color: '#FFF176',
        parentName: 'مرافق',
      },
      {
        name: 'إنترنت وهاتف',
        description: 'فواتير الإنترنت والهاتف',
        icon: 'FaWifi',
        color: '#FFF59D',
        parentName: 'مرافق',
      },

      // صيانة
      {
        name: 'صيانة المباني',
        description: 'صيانة وإصلاح المباني',
        icon: 'FaHammer',
        color: '#FF7043',
        parentName: 'صيانة',
      },
      {
        name: 'صيانة المعدات',
        description: 'صيانة وإصلاح المعدات والأجهزة',
        icon: 'FaWrench',
        color: '#FFAB91',
        parentName: 'صيانة',
      },

      // تكنولوجيا
      {
        name: 'أجهزة كمبيوتر',
        description: 'شراء وصيانة أجهزة الكمبيوتر',
        icon: 'FaDesktop',
        color: '#BA68C8',
        parentName: 'تكنولوجيا',
      },
      {
        name: 'برمجيات',
        description: 'شراء وتجديد تراخيص البرمجيات',
        icon: 'FaCode',
        color: '#CE93D8',
        parentName: 'تكنولوجيا',
      },
    ];

    // إنشاء الفئات الفرعية
    for (const subCategory of subCategories) {
      const parentCategory = await prisma.expenseCategory.findFirst({
        where: { name: subCategory.parentName },
      });

      if (parentCategory) {
        // إنشاء كائن جديد بالخصائص المطلوبة فقط
        const subCategoryData = {
          name: subCategory.name,
          description: subCategory.description,
          icon: subCategory.icon,
          color: subCategory.color
        };

        const existingSubCategory = await prisma.expenseCategory.findFirst({
          where: {
            name: subCategory.name,
            parentId: parentCategory.id
          },
        });

        if (!existingSubCategory) {
          await prisma.expenseCategory.create({
            data: {
              ...subCategoryData,
              parentId: parentCategory.id,
            },
          });
          console.log(`تم إنشاء فئة فرعية: ${subCategory.name} (تابعة لـ ${parentCategory.name})`);
        } else {
          console.log(`الفئة الفرعية موجودة بالفعل: ${subCategory.name}`);
        }
      }
    }

    console.log('تم إنشاء بيانات فئات المصروفات بنجاح!');
  } catch (error) {
    console.error('حدث خطأ أثناء إنشاء بيانات فئات المصروفات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// عند استدعاء الملف مباشرة، قم بتنفيذ الدالة
if (require.main === module) {
  seedExpenseCategories();
}

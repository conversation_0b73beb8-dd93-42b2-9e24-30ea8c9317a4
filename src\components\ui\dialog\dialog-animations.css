/* Dialog Animation Styles */
.dialog-overlay {
  animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  background: rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
}

.dialog-content {
  animation: contentShow 300ms cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center center;
  background: #f8f9fa !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

@keyframes overlayShow {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

@keyframes contentShow {
  from {
    opacity: 0;
    transform: translate(-50%, -48%) scale(0.96);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* RTL Support */
[dir="rtl"] .dialog-content {
  text-align: right;
}

/* Consistent Dialog Styling */
.dialog-content {
  background-color: #f8f9fa !important;
  border-radius: 12px !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  max-width: 90vw;
  max-height: 85vh;
  padding: 1.5rem;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 500px;
  overflow-y: auto;
  z-index: 50;
}

.dialog-header {
  margin-bottom: 1.5rem;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dialog-description {
  color: #4b5563;
  font-size: 0.875rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

/* Theme Colors */
.dialog-primary {
  --dialog-accent: #169b88;
  --dialog-accent-hover: #1ab19c;
}

/* Fix for Select dropdown */
.select-content {
  z-index: 9999;
  position: relative;
}

/* Ensure dropdowns are visible */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
  pointer-events: auto !important;
}

/* Fix for dropdown menus in dialogs */
[role="listbox"] {
  z-index: 9999 !important;
  pointer-events: auto !important;
}

/* Prevent click events from being blocked */
[data-radix-select-viewport] {
  pointer-events: auto !important;
}

/* Ensure select items are clickable */
[role="option"] {
  cursor: pointer !important;
  user-select: none !important;
  pointer-events: auto !important;
}

/* Fix for dialog overlay */
[data-radix-dialog-overlay] {
  pointer-events: auto !important;
}

/* Fix for select trigger */
[role="combobox"] {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Fix for select content */
[data-state="open"] {
  pointer-events: auto !important;
}

.dialog-primary .dialog-title {
  color: var(--dialog-accent);
}

.dialog-primary .dialog-footer button[type="submit"],
.dialog-primary .dialog-footer button.primary {
  background-color: var(--dialog-accent);
  color: white;
}

.dialog-primary .dialog-footer button[type="submit"]:hover,
.dialog-primary .dialog-footer button.primary:hover {
  background-color: var(--dialog-accent-hover);
}

/* إصلاح شامل لألوان النصوص في النوافذ المنبثقة */
.dialog-content * {
  color: #1f2937 !important;
}

.dialog-content label,
.dialog-content [data-radix-label-root] {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
}

/* إصلاح محدد للكلاسات الافتراضية لمكون Label */
.dialog-content .text-gray-900,
.dialog-content .text-sm,
.dialog-content .font-medium,
.dialog-content .leading-none {
  color: #1f2937 !important;
}

.dialog-content input,
.dialog-content textarea,
.dialog-content select {
  color: #1f2937 !important;
  background-color: white !important;
}

.dialog-content input::placeholder,
.dialog-content textarea::placeholder {
  color: #9ca3af !important;
}

.dialog-content .text-gray-500,
.dialog-content .text-muted-foreground {
  color: #6b7280 !important;
}

.dialog-content .text-red-500 {
  color: #ef4444 !important;
}

/* إصلاح ألوان القوائم المنسدلة */
.dialog-content [data-radix-select-trigger] {
  color: #1f2937 !important;
  background-color: white !important;
}

.dialog-content [data-radix-select-value] {
  color: #1f2937 !important;
}

/* قاعدة شاملة لإصلاح جميع Labels بغض النظر عن الكلاسات */
.dialog-content label[for],
.dialog-content label[htmlFor] {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  display: block !important;
  margin-bottom: 0.5rem !important;
}

/* إصلاح Labels داخل div.space-y-2 */
.dialog-content .space-y-2 > label {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
}

'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { FaQuran } from 'react-icons/fa';
import axios from 'axios';
import { fixImagePath, cleanImagePath } from '@/utils/imageUtils';

interface SiteLogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  showText?: boolean;
  className?: string;
  textClassName?: string;
  iconColor?: string;
}

interface SiteSettings {
  siteName: string;
  logoUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
}

const SiteLogo: React.FC<SiteLogoProps> = ({
  size = 'md',
  showText = true,
  className = '',
  textClassName = '',
  iconColor
}) => {
  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null);

  // تحديد أحجام الشعار بناءً على المقاس المطلوب
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'h-8 w-8',
          text: 'text-sm',
          icon: 'text-lg'
        };
      case 'md':
        return {
          container: 'h-12 w-12',
          text: 'text-base',
          icon: 'text-xl'
        };
      case 'lg':
        return {
          container: 'h-16 w-16',
          text: 'text-xl',
          icon: 'text-3xl'
        };
      case 'xl':
        return {
          container: 'h-20 w-20',
          text: 'text-2xl',
          icon: 'text-4xl'
        };
      case '2xl':
        return {
          container: 'h-24 w-24',
          text: 'text-3xl',
          icon: 'text-5xl'
        };
      case '3xl':
        return {
          container: 'h-32 w-32',
          text: 'text-4xl',
          icon: 'text-6xl'
        };
      default:
        return {
          container: 'h-12 w-12',
          text: 'text-base',
          icon: 'text-xl'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  useEffect(() => {
    // تحميل الإعدادات من localStorage أولاً
    const loadSettingsFromStorage = () => {
      try {
        const savedSettings = localStorage.getItem('siteSettings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          if (settings && typeof settings === 'object') {
            console.log('💾 تم تحميل الإعدادات من localStorage:', settings);
            console.log('🖼️ مسار الشعار من localStorage:', settings.logoUrl);
            setSiteSettings(settings);
            return true;
          }
        }
      } catch (error) {
        console.error('Error loading settings from localStorage:', error);
      }
      return false;
    };

    // تحميل الإعدادات فوراً من localStorage
    const settingsLoaded = loadSettingsFromStorage();

    // جلب الإعدادات من الخادم
    const fetchSettings = async () => {
      try {
        const response = await axios.get('/api/settings');
        const data = response.data;

        type ResponseType = { settings?: SiteSettings };
        const typedData = data as ResponseType;
        if (typedData && typedData.settings) {
          const settings = typedData.settings;
          console.log('📥 تم جلب الإعدادات من الخادم:', settings);
          console.log('🖼️ مسار الشعار في الإعدادات:', settings.logoUrl);
          localStorage.setItem('siteSettings', JSON.stringify(settings));
          setSiteSettings(settings);
        } else if (!settingsLoaded) {
          // إعدادات افتراضية
          const defaultSettings: SiteSettings = {
            siteName: 'نظام برهان للقرآن الكريم',
            logoUrl: '/logo.svg',
            primaryColor: 'var(--primary-color)',
            secondaryColor: 'var(--secondary-color)'
          };
          setSiteSettings(defaultSettings);
        }
      } catch (error) {
        console.error('Error fetching site settings:', error);
        if (!settingsLoaded) {
          const defaultSettings: SiteSettings = {
            siteName: 'نظام برهان للقرآن الكريم',
            logoUrl: '/logo.svg',
            primaryColor: 'var(--primary-color)',
            secondaryColor: 'var(--secondary-color)'
          };
          setSiteSettings(defaultSettings);
        }
      }
    };

    fetchSettings();

    // إضافة مستمع للتحديثات في localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteSettings' && e.newValue) {
        try {
          const newSettings = JSON.parse(e.newValue);
          setSiteSettings(newSettings);
        } catch (error) {
          console.error('Error parsing updated settings:', error);
        }
      }
    };

    // إضافة مستمع مخصص للتحديثات الداخلية
    const handleSettingsUpdate = (event: CustomEvent) => {
      setSiteSettings(event.detail);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);
    };
  }, []);

  const logoColor = iconColor || siteSettings?.primaryColor || 'var(--primary-color)';

  const [logoError, setLogoError] = useState(false);

  // تنظيف وإصلاح مسار الشعار
  const logoSrc = siteSettings?.logoUrl ? fixImagePath(cleanImagePath(siteSettings.logoUrl)) : null;

  // تشخيص مفصل للشعار
  useEffect(() => {
    if (logoSrc) {
      console.log('🔍 تشخيص الشعار:');
      console.log('- المسار الأصلي:', siteSettings?.logoUrl);
      console.log('- المسار المنظف:', logoSrc);
      console.log('- حالة الخطأ:', logoError);
      console.log('- إعدادات الموقع:', siteSettings);
    }
  }, [logoSrc, logoError, siteSettings]);

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {logoSrc && !logoError ? (
        <div className={`relative ${sizeClasses.container} flex-shrink-0`}>
          <Image
            src={logoSrc}
            alt="شعار الموقع"
            fill
            className="object-contain"
            sizes={`${parseInt(sizeClasses.container.split('-')[1]) * 4}px`}
            onError={(e) => {
              console.error('❌ فشل تحميل الشعار من:', logoSrc);
              console.error('- المسار الأصلي:', siteSettings?.logoUrl);
              console.error('- خطأ التحميل:', e);

              // محاولة تحميل الشعار من مسار بديل
              const img = e.target as HTMLImageElement;
              if (logoSrc && !logoSrc.includes('/api/uploads/')) {
                const alternativePath = logoSrc.replace('/uploads/', '/api/uploads/');
                console.log('🔄 محاولة تحميل من مسار بديل:', alternativePath);
                img.src = alternativePath;
              } else {
                setLogoError(true);
              }
            }}
            onLoad={() => {
              console.log('✅ تم تحميل الشعار بنجاح من:', logoSrc);
              setLogoError(false);
            }}
          />
        </div>
      ) : (
        <FaQuran
          className={`${sizeClasses.icon} flex-shrink-0`}
          style={{ color: logoColor }}
        />
      )}
      {showText && (
        <span
          className={`${sizeClasses.text} font-bold mr-2 ${textClassName}`}
          style={{ color: logoColor }}
        >
          {siteSettings?.siteName || 'نظام برهان للقرآن الكريم'}
        </span>
      )}
    </div>
  );
};

export default SiteLogo;

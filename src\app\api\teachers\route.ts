import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { hash } from 'bcrypt';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { username, password, name, phone, specialization, subjects } = body;

    if (!username || !password || !name || !specialization || !subjects || !Array.isArray(subjects)) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      );
    }

    const existingUser = await prisma.user.findUnique({
      where: { username }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'اسم المستخدم موجود مسبقاً' },
        { status: 400 }
      );
    }

    const hashedPassword = await hash(password, 10);

    const teacher = await prisma.$transaction(async (tx) => {
      // Create user account
      const user = await tx.user.create({
        data: {
          username,
          password: hashedPassword,
          role: 'TEACHER',
          profile: {
            create: {
              name,
              phone
            }
          }
        }
      });

      // Create teacher profile with subjects
      const newTeacher = await tx.teacher.create({
        data: {
          name,
          phone,
          specialization,
          userId: user.id,
          teacherSubjects: {
            create: subjects.map((subjectId: number) => ({
              subject: {
                connect: { id: subjectId }
              }
            }))
          }
        },
        include: {
          user: true,
          teacherSubjects: {
            include: {
              subject: true
            }
          }
        }
      });

      return newTeacher;
    });

    return NextResponse.json(teacher);
  } catch (error: unknown) {
    console.error('Error creating teacher:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء حساب المعلم' },
      { status: 500 }
    );
  }
}




export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Validate and parse query parameters
    const pageParam = searchParams.get('page');
    const limitParam = searchParams.get('limit');

    if (pageParam && isNaN(Number(pageParam))) {
      return NextResponse.json(
        { error: 'رقم الصفحة يجب أن يكون رقماً صحيحاً' },
        { status: 400 }
      );
    }

    if (limitParam && isNaN(Number(limitParam))) {
      return NextResponse.json(
        { error: 'حد العناصر يجب أن يكون رقماً صحيحاً' },
        { status: 400 }
      );
    }

    const page = Math.max(1, parseInt(pageParam || '1'));
    const limit = Math.max(1, Math.min(50, parseInt(limitParam || '10')));
    const search = searchParams.get('search') || '';

    const skip = (page - 1) * limit;

    const [teachers, total] = await Promise.all([
      prisma.teacher.findMany({
        where: {
          OR: [
            { name: { contains: search } },
            { phone: { contains: search } }
          ]
        },
        include: {
          user: true,
          teacherSubjects: {
            include: {
              subject: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { id: 'desc' }
      }),
      prisma.teacher.count({
        where: {
          OR: [
            { name: { contains: search } },
            { phone: { contains: search } }
          ]
        }
      })
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      teachers,
      pagination: {
        total,
        totalPages,
        currentPage: page,
        limit
      }
    });
  } catch (error: unknown) {
    console.error('Database query error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات المعلمين' },
      { status: 500 }
    );
  }
}




export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, phone, subjects } = body;
// console.log(body)
    if (!id || !name || !subjects || !Array.isArray(subjects)) {
      return NextResponse.json(
        { error: 'جميع الحقول مطلوبة' },
        { status: 400 }
      );
    }

    const teacher = await prisma.$transaction(async (tx) => {
      // Update teacher's profile
      await tx.profile.update({
        where: { userId: id },
        data: { name, phone }
      });

      // Delete existing subject relationships
      await tx.teacherSubject.deleteMany({
        where: { teacherId: id }
      });

      // Create new subject relationships
      const updatedTeacher = await tx.teacher.update({
        where: { id },
        data: {
          teacherSubjects: {
            create: subjects.map((subjectId: number) => ({
              subject: {
                connect: { id: subjectId }
              }
            }))
          }
        },
        include: {
          user: true,
          teacherSubjects: {
            include: {
              subject: true
            }
          }
        }
      });

      return updatedTeacher;
    });

    return NextResponse.json(teacher);
  } catch (error: unknown) {
    console.error('Error updating teacher:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث بيانات المعلم' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف المعلم مطلوب' },
        { status: 400 }
      );
    }

    await prisma.$transaction(async (tx) => {
      // Delete teacher's subjects first
      await tx.teacherSubject.deleteMany({
        where: { teacherId: parseInt(id) }
      });

      // Delete teacher
      await tx.teacher.delete({
        where: { id: parseInt(id) }
      });

      // Delete associated user and profile (cascade delete will handle this)
    });

    return NextResponse.json({ message: 'تم حذف المعلم بنجاح' });
  } catch (error: unknown) {
    console.error('Error deleting teacher:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المعلم' },
      { status: 500 }
    );
  }
}
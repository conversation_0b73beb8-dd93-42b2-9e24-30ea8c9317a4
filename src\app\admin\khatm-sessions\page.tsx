'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { CalendarIcon, PlusCircle, Users, Loader2, Globe, BarChart, FileText } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Link from 'next/link';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';
import PermissionGuard from '@/components/admin/PermissionGuard';

type Teacher = {
  id: number;
  name: string;
};

type Surah = {
  id: number;
  name: string;
  number: number;
  totalAyahs: number;
};

type KhatmSession = {
  id: number;
  title: string;
  description: string | null;
  date: string;
  location: string | null;
  surahId: number | null;
  teacherId: number;
  isPublic: boolean;
  isRecurring: boolean;
  recurrencePattern: string | null;
  recurrenceEndDate: string | null;
  parentSessionId: number | null;
  createdAt: string;
  updatedAt: string;
  teacher: {
    id: number;
    name: string;
  };
  surah: Surah | null;
  attendances: {
    id: number;
    studentId: number;
    status: string;
    student: {
      id: number;
      name: string;
    };
  }[];
  progressRecords?: {
    id: number;
    memorizedAyahs: number;
    reviewedAyahs: number;
    qualityRating: number;
  }[];
};

export default function KhatmSessionsPage() {
  const [sessions, setSessions] = useState<KhatmSession[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [surahs, setSurahs] = useState<Surah[]>([]);
  const [loading, setLoading] = useState({
    sessions: false,
    teachers: false,
    surahs: false,
    submit: false
  });
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    description: '',
    date: new Date(),
    location: '',
    surahId: '',
    teacherId: '',
    isPublic: false,
    isRecurring: false,
    recurrencePattern: 'weekly',
    recurrenceEndDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
    updateRecurringSessions: false
  });
  const [isEditing, setIsEditing] = useState(false);
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    fetchSessions();
    fetchTeachers();
    fetchSurahs();
  }, []);

  const fetchSessions = async () => {
    setLoading(prev => ({ ...prev, sessions: true }));
    try {
      const response = await fetch('/api/khatm-sessions');
      if (!response.ok) throw new Error('فشل في جلب مجالس الختم');
      const data = await response.json();
      setSessions(data.data || []);
    } catch (error) {
      console.error('Error fetching khatm sessions:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب مجالس الختم',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, sessions: false }));
    }
  };

  const fetchTeachers = async () => {
    setLoading(prev => ({ ...prev, teachers: true }));
    try {
      const response = await fetch('/api/teachers');
      if (!response.ok) throw new Error('فشل في جلب المعلمين');
      const data = await response.json();

      // التحقق من شكل البيانات المستلمة
      console.log('Teachers data:', data);

      if (data && data.teachers && Array.isArray(data.teachers)) {
        setTeachers(data.teachers);
      } else if (Array.isArray(data)) {
        setTeachers(data);
      } else {
        setTeachers([]);
        console.error('Invalid teachers data format:', data);
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب المعلمين',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, teachers: false }));
    }
  };

  const fetchSurahs = async () => {
    setLoading(prev => ({ ...prev, surahs: true }));
    try {
      const response = await fetch('/api/surahs');
      if (!response.ok) throw new Error('فشل في جلب السور');
      const data = await response.json();
      setSurahs(data || []);
    } catch (error) {
      console.error('Error fetching surahs:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب السور',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, surahs: false }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // لم يعد هذا الدالة مستخدمة بعد التعديل المباشر في مكون التقويم

  const resetForm = () => {
    setFormData({
      id: '',
      title: '',
      description: '',
      date: new Date(),
      location: '',
      surahId: '',
      teacherId: '',
      isPublic: false,
      isRecurring: false,
      recurrencePattern: 'weekly',
      recurrenceEndDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
      updateRecurringSessions: false
    });
    setIsEditing(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(prev => ({ ...prev, submit: true }));

    try {
      const payload = {
        ...formData,
        date: formData.date.toISOString(),
        surahId: formData.surahId && formData.surahId !== 'none' ? formData.surahId : null,
        isPublic: formData.isPublic,
        isRecurring: formData.isRecurring,
        recurrencePattern: formData.isRecurring ? formData.recurrencePattern : null,
        recurrenceEndDate: formData.isRecurring ? formData.recurrenceEndDate.toISOString() : null,
        updateRecurringSessions: isEditing ? formData.updateRecurringSessions : undefined
      };

      console.log('Sending payload:', payload);

      const url = '/api/khatm-sessions';
      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: isEditing ? 'تم تحديث مجلس الختم بنجاح' : 'تم إنشاء مجلس الختم بنجاح',
        });
        resetForm();
        setShowForm(false);
        fetchSessions();
      } else {
        throw new Error(result.error || 'حدث خطأ أثناء حفظ مجلس الختم');
      }
    } catch (error) {
      console.error('Error saving khatm session:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ مجلس الختم',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, submit: false }));
    }
  };

  const handleEdit = (session: KhatmSession) => {
    setFormData({
      id: session.id.toString(),
      title: session.title,
      description: session.description || '',
      date: new Date(session.date),
      location: session.location || '',
      surahId: session.surahId ? session.surahId.toString() : '',
      teacherId: session.teacherId.toString(),
      isPublic: session.isPublic || false,
      isRecurring: session.isRecurring || false,
      recurrencePattern: session.recurrencePattern || 'weekly',
      recurrenceEndDate: session.recurrenceEndDate ? new Date(session.recurrenceEndDate) : new Date(new Date().setMonth(new Date().getMonth() + 1)),
      updateRecurringSessions: false
    });
    setIsEditing(true);
    setShowForm(true);
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا المجلس؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/khatm-sessions?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم حذف مجلس الختم بنجاح',
        });
        fetchSessions();
      } else {
        const result = await response.json();
        throw new Error(result.error || 'حدث خطأ أثناء حذف مجلس الختم');
      }
    } catch (error) {
      console.error('Error deleting khatm session:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حذف مجلس الختم',
        variant: 'destructive',
      });
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.khatm-sessions.view">
      <div className="container mx-auto py-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <Card className="shadow-md border-t-4 border-t-[var(--primary-color)]">
        <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-[#f8fffd] to-white">
          <div>
            <CardTitle className="text-2xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">مجالس الختم</CardTitle>
            <p className="text-muted-foreground mt-1 mr-3">إدارة مجالس الختم وتسجيل الحضور</p>
          </div>
          <QuickActionButtons
            entityType="khatm-sessions"
            actions={[
              {
                key: 'reports',
                label: 'تقارير الإنجاز',
                icon: <FileText className="h-4 w-4" />,
                onClick: () => window.location.href = '/admin/khatm-reports',
                variant: 'secondary',
                permission: 'admin.reports.view'
              },
              {
                key: 'create',
                label: 'إضافة مجلس جديد',
                icon: <PlusCircle className="h-4 w-4" />,
                onClick: () => { resetForm(); setShowForm(true); },
                variant: 'primary',
                permission: 'admin.khatm-sessions.create'
              }
            ]}
            className="flex gap-2"
          />
        </CardHeader>
        <CardContent>
          {showForm && (
            <form onSubmit={handleSubmit} className="space-y-4 mb-6 p-6 border rounded-lg bg-[#f8fffd] shadow-sm">
              <h3 className="text-xl font-bold text-[var(--primary-color)] mb-4">{isEditing ? 'تعديل مجلس الختم' : 'إضافة مجلس ختم جديد'}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان المجلس</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="date">تاريخ ووقت المجلس</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      id="date"
                      name="date"
                      type="date"
                      value={formData.date ? formData.date.toISOString().split('T')[0] : ''}
                      onChange={(e) => {
                        const dateValue = e.target.value;
                        if (dateValue) {
                          // الحفاظ على الوقت الحالي إذا كان موجوداً
                          const currentTime = formData.date ?
                            `${formData.date.getHours().toString().padStart(2, '0')}:${formData.date.getMinutes().toString().padStart(2, '0')}` :
                            '00:00';

                          const [hours, minutes] = currentTime.split(':');
                          const newDate = new Date(dateValue);
                          newDate.setHours(parseInt(hours));
                          newDate.setMinutes(parseInt(minutes));

                          setFormData(prev => ({ ...prev, date: newDate }));
                        }
                      }}
                      className="w-full"
                      required
                    />
                    <Input
                      id="time"
                      name="time"
                      type="time"
                      value={formData.date ?
                        `${formData.date.getHours().toString().padStart(2, '0')}:${formData.date.getMinutes().toString().padStart(2, '0')}` :
                        ''}
                      onChange={(e) => {
                        const timeValue = e.target.value;
                        if (timeValue && formData.date) {
                          const [hours, minutes] = timeValue.split(':');
                          const newDate = new Date(formData.date);
                          newDate.setHours(parseInt(hours));
                          newDate.setMinutes(parseInt(minutes));

                          setFormData(prev => ({ ...prev, date: newDate }));
                        }
                      }}
                      className="w-full"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">مكان المجلس</Label>
                  <Input
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="teacherId">المعلم المسؤول</Label>
                  <Select
                    value={formData.teacherId}
                    onValueChange={(value) => handleSelectChange('teacherId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر المعلم" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.isArray(teachers) && teachers.length > 0 ? teachers.map(teacher => (
                        <SelectItem key={teacher.id} value={teacher.id.toString()}>
                          {teacher.name}
                        </SelectItem>
                      )) : <SelectItem value="no-teachers" disabled>لا يوجد معلمين</SelectItem>}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="surahId">السورة (اختياري)</Label>
                  <Select
                    value={formData.surahId}
                    onValueChange={(value) => handleSelectChange('surahId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر السورة" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">بدون سورة</SelectItem>
                      {Array.isArray(surahs) && surahs.length > 0 ? surahs.map(surah => (
                        <SelectItem key={surah.id} value={surah.id.toString()}>
                          {surah.name}
                        </SelectItem>
                      )) : <SelectItem value="no-surahs" disabled>لا يوجد سور</SelectItem>}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="description">وصف المجلس</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                  />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <div className="flex items-center space-x-2 space-x-reverse mb-4">
                    <Checkbox
                      id="isPublic"
                      checked={formData.isPublic}
                      onCheckedChange={(checked) => {
                        setFormData(prev => ({ ...prev, isPublic: checked === true }))
                      }}
                    />
                    <Label
                      htmlFor="isPublic"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      عرض المجلس للعامة (سيظهر في صفحة مجالس الختم العامة)
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2 space-x-reverse mb-4">
                    <Checkbox
                      id="isRecurring"
                      checked={formData.isRecurring}
                      onCheckedChange={(checked) => {
                        setFormData(prev => ({ ...prev, isRecurring: checked === true }))
                      }}
                    />
                    <Label
                      htmlFor="isRecurring"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      مجلس دوري متكرر
                    </Label>
                  </div>

                  {formData.isRecurring && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-md bg-gray-50">
                      <div className="space-y-2">
                        <Label htmlFor="recurrencePattern">نمط التكرار</Label>
                        <select
                          id="recurrencePattern"
                          name="recurrencePattern"
                          value={formData.recurrencePattern}
                          onChange={(e) => handleSelectChange('recurrencePattern', e.target.value)}
                          className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          disabled={isEditing}
                        >
                          <option value="daily">يومي</option>
                          <option value="weekly">أسبوعي</option>
                          <option value="monthly">شهري</option>
                        </select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="recurrenceEndDate">تاريخ انتهاء التكرار</Label>
                        <Input
                          id="recurrenceEndDate"
                          name="recurrenceEndDate"
                          type="date"
                          value={formData.recurrenceEndDate ? formData.recurrenceEndDate.toISOString().split('T')[0] : ''}
                          onChange={(e) => {
                            const dateValue = e.target.value;
                            if (dateValue) {
                              const newDate = new Date(dateValue);
                              setFormData(prev => ({ ...prev, recurrenceEndDate: newDate }));
                            }
                          }}
                          className="w-full"
                          required={formData.isRecurring}
                          disabled={isEditing}
                        />
                      </div>

                      {isEditing && (
                        <div className="md:col-span-2">
                          <div className="flex items-center space-x-2 space-x-reverse mt-2">
                            <Checkbox
                              id="updateRecurringSessions"
                              checked={formData.updateRecurringSessions}
                              onCheckedChange={(checked) => {
                                setFormData(prev => ({ ...prev, updateRecurringSessions: checked === true }))
                              }}
                            />
                            <Label
                              htmlFor="updateRecurringSessions"
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              تحديث جميع المجالس المتكررة المرتبطة بهذا المجلس
                            </Label>
                          </div>
                          <p className="text-xs text-gray-500 mt-1 mr-6">
                            سيتم تحديث الوصف والمكان والسورة والمعلم وإعدادات العرض العام في جميع المجالس المتكررة
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => { setShowForm(false); resetForm(); }}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={loading.submit}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  {loading.submit ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري الحفظ...
                    </>
                  ) : isEditing ? 'تحديث' : 'إضافة'}
                </Button>
              </div>
            </form>
          )}

          {loading.sessions ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-[var(--primary-color)]" />
              <p className="mt-2 text-muted-foreground">جاري تحميل مجالس الختم...</p>
            </div>
          ) : sessions.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <div className="flex flex-col items-center justify-center">
                <CalendarIcon className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-1">لا توجد مجالس ختم</h3>
                <p className="text-sm text-gray-500 mb-4">قم بإنشاء مجلس ختم جديد للبدء</p>
                <Button
                  onClick={() => { resetForm(); setShowForm(true); }}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  <PlusCircle className="ml-2 h-4 w-4" />
                  إضافة مجلس جديد
                </Button>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader className="bg-[var(--primary-color)]/10">
                <TableRow>
                  <TableHead className="text-[var(--primary-color)] font-bold px-2 md:px-4">العنوان</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">التاريخ</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">المعلم</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">السورة</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">الحضور</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">عام</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">دوري</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold px-2 md:px-4">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sessions.map(session => (
                  <TableRow key={session.id}>
                    <TableCell className="px-2 md:px-4">{session.title}</TableCell>
                    <TableCell className="hidden md:table-cell">{format(new Date(session.date), 'PPP', { locale: ar })}</TableCell>
                    <TableCell className="hidden md:table-cell">{session.teacher.name}</TableCell>
                    <TableCell className="hidden md:table-cell">{session.surah ? session.surah.name : '-'}</TableCell>
                    <TableCell className="hidden md:table-cell">{session.attendances.length}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      {session.isPublic ? (
                        <div className="flex items-center text-primary-color">
                          <Globe className="h-4 w-4 mr-1" />
                          <span>نعم</span>
                        </div>
                      ) : (
                        <span className="text-gray-500">لا</span>
                      )}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {session.isRecurring ? (
                        <div className="flex items-center text-blue-600">
                          <CalendarIcon className="h-4 w-4 mr-1" />
                          <span>{session.recurrencePattern === 'daily' ? 'يومي' :
                                 session.recurrencePattern === 'weekly' ? 'أسبوعي' :
                                 session.recurrencePattern === 'monthly' ? 'شهري' : 'متكرر'}</span>
                        </div>
                      ) : (
                        <span className="text-gray-500">لا</span>
                      )}
                    </TableCell>
                    <TableCell className="px-2 md:px-4">
                      <div className="flex gap-1 md:gap-2 flex-wrap">
                        <PermissionGuard requiredPermission="admin.khatm-attendance.view">
                          <Link href={`/admin/khatm-attendance/${session.id}`}>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)]/10 px-2 md:px-3"
                              title="تسجيل الحضور"
                            >
                              <Users className="h-3 w-3 md:h-4 md:w-4" />
                            </Button>
                          </Link>
                        </PermissionGuard>
                        <PermissionGuard requiredPermission="admin.khatm-progress.view">
                          <Link href={`/admin/khatm-progress/${session.id}`}>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-amber-600 border-amber-600 hover:bg-amber-50 px-2 md:px-3"
                              title="متابعة تقدم الحفظ"
                            >
                              <BarChart className="h-3 w-3 md:h-4 md:w-4" />
                            </Button>
                          </Link>
                        </PermissionGuard>
                        <OptimizedActionButtonGroup
                          entityType="khatm-sessions"
                          onEdit={() => handleEdit(session)}
                          onDelete={() => handleDelete(session.id)}
                          showEdit={true}
                          showDelete={true}
                          size="sm"
                          className="gap-1 md:gap-2"
                          deleteConfirmTitle="هل أنت متأكد من حذف هذا المجلس؟"
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      </div>
    </OptimizedProtectedRoute>
  );
}

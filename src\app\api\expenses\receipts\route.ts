import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

// استخدام نموذج ExpenseReceipt مباشرة من Prisma
// تم إضافة هذا النموذج إلى ملف schema.prisma

// POST /api/expenses/receipts - رفع إيصال جديد
export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const expenseId = formData.get('expenseId') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'لم يتم تحديد ملف' },
        { status: 400 }
      );
    }

    if (!expenseId) {
      return NextResponse.json(
        { error: 'معرف المصروف مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المصروف
    const expense = await prisma.expense.findUnique({
      where: { id: parseInt(expenseId) },
    });

    if (!expense) {
      return NextResponse.json(
        { error: 'المصروف غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, WebP, PDF' },
        { status: 400 }
      );
    }

    // التحقق من حجم الملف (الحد الأقصى 5 ميجابايت)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'حجم الملف يتجاوز الحد المسموح (5 ميجابايت)' },
        { status: 400 }
      );
    }

    // قراءة محتوى الملف
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // إنشاء اسم فريد للملف
    const fileExtension = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExtension}`;

    // حفظ الملف في مجلد التخزين (يمكن استبدال هذا بخدمة تخزين سحابية)
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'receipts');

    // التأكد من وجود المجلد
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const filePath = path.join(uploadsDir, fileName);
    fs.writeFileSync(filePath, buffer);

    // تحديث رابط الإيصال في المصروف
    const fileUrl = `/uploads/receipts/${fileName}`;
    await prisma.expense.update({
      where: { id: parseInt(expenseId) },
      data: {
        receipt: fileUrl,
      },
    });

    // إنشاء سجل للإيصال
    const receipt = await prisma.expenseReceipt.create({
      data: {
        expenseId: parseInt(expenseId),
        fileName,
        fileType: file.type,
        fileSize: file.size,
        filePath: fileUrl,
      },
    });

    return NextResponse.json({
      success: true,
      receipt: {
        id: receipt.id,
        fileName: receipt.fileName,
        fileType: receipt.fileType,
        fileSize: receipt.fileSize,
        filePath: receipt.filePath,
        uploadDate: receipt.uploadDate,
      },
    });
  } catch (error) {
    console.error('خطأ في رفع الإيصال:', error);
    return NextResponse.json(
      { error: 'فشل في رفع الإيصال' },
      { status: 500 }
    );
  }
}

// GET /api/expenses/:id/receipts - الحصول على إيصالات مصروف محدد
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const expenseId = searchParams.get('expenseId');

    if (!expenseId) {
      return NextResponse.json(
        { error: 'معرف المصروف مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المصروف
    const expense = await prisma.expense.findUnique({
      where: { id: parseInt(expenseId) },
    });

    if (!expense) {
      return NextResponse.json(
        { error: 'المصروف غير موجود' },
        { status: 404 }
      );
    }

    // جلب إيصالات المصروف
    const receipts = await prisma.expenseReceipt.findMany({
      where: { expenseId: parseInt(expenseId) },
      orderBy: { uploadDate: 'desc' },
    });

    return NextResponse.json({
      expense: {
        id: expense.id,
        purpose: expense.purpose,
        amount: expense.amount,
        date: expense.date,
        receipt: expense.receipt,
      },
      receipts,
    });
  } catch (error) {
    console.error('خطأ في جلب إيصالات المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في جلب إيصالات المصروف' },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses/receipts/:id - حذف إيصال
export async function DELETE(_req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود الإيصال
    const receipt = await prisma.expenseReceipt.findUnique({
      where: { id },
    });

    if (!receipt) {
      return NextResponse.json(
        { error: 'الإيصال غير موجود' },
        { status: 404 }
      );
    }

    // حذف الملف من التخزين
    const filePath = path.join(process.cwd(), 'public', receipt.filePath);

    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // حذف الإيصال من قاعدة البيانات
    await prisma.expenseReceipt.delete({
      where: { id },
    });

    // تحديث المصروف إذا كان هذا هو الإيصال الرئيسي
    const expense = await prisma.expense.findUnique({
      where: { id: receipt.expenseId },
    });

    if (expense && expense.receipt === receipt.filePath) {
      // البحث عن إيصال آخر للمصروف
      const otherReceipt = await prisma.expenseReceipt.findFirst({
        where: { expenseId: receipt.expenseId },
        orderBy: { uploadDate: 'desc' },
      });

      await prisma.expense.update({
        where: { id: receipt.expenseId },
        data: {
          receipt: otherReceipt ? otherReceipt.filePath : null,
        },
      });
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الإيصال بنجاح',
    });
  } catch (error) {
    console.error('خطأ في حذف الإيصال:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الإيصال' },
      { status: 500 }
    );
  }
}

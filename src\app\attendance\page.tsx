'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
//import { ar } from 'date-fns/locale';

type Student = {
  id: number;
  name: string;
  classe?: {
    id: number;
    name: string;
  };
};

type AttendanceRecord = {
  id: number | null;
  student: Student;
  status: 'PRESENT' | 'ABSENT' | 'EXCUSED' | null;
  date: string;
};

export default function AttendancePage() {
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [classeId, setClasseId] = useState('');
  const [classes, setClasses] = useState<{ id: number; name: string }[]>([]);
  const [attendance, setAttendance] = useState<AttendanceRecord[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch classes
  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const response = await fetch('/api/classes');
        const data = await response.json();

        // التحقق من هيكل البيانات المستلمة
        if (data && data.classes && Array.isArray(data.classes)) {
          // البيانات موجودة في خاصية classes
          setClasses(data.classes);
        } else if (Array.isArray(data)) {
          // البيانات هي مصفوفة مباشرة
          setClasses(data);
        } else {
          console.error('البيانات المستلمة ليست بالتنسيق المتوقع:', data);
          setClasses([]);
        }
      } catch (error) {
        console.error('Error fetching classes:', error);
        setClasses([]);
      }
    };
    fetchClasses();
  }, []);

  // Fetch attendance records
  useEffect(() => {
    const fetchAttendance = async () => {
      setLoading(true);
      try {
        const params = new URLSearchParams();
        params.append('date', date);
        if (classeId) params.append('classeId', classeId);

        const response = await fetch(`/api/attendance?${params}`);
        const data = await response.json();

        // التحقق من هيكل البيانات المستلمة
        if (Array.isArray(data)) {
          setAttendance(data);
        } else if (data && Array.isArray(data.attendance)) {
          setAttendance(data.attendance);
        } else {
          console.error('البيانات المستلمة ليست بالتنسيق المتوقع:', data);
          setAttendance([]);
        }
      } catch (error) {
        console.error('Error fetching attendance:', error);
        setAttendance([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAttendance();
  }, [date, classeId]);

  const handleStatusChange = async (record: AttendanceRecord, newStatus: 'PRESENT' | 'ABSENT' | 'EXCUSED') => {
    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: record.id,
          studentId: record.student.id,
          date,
          status: newStatus
        })
      });

      if (response.ok) {
        const updatedRecord = await response.json();
        setAttendance(prev =>
          prev.map(item =>
            item.student.id === record.student.id ? {
              ...updatedRecord,
              student: updatedRecord.student
            } : item
          )
        );
      }
    } catch (error) {
      console.error('Error updating attendance:', error);
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold text-center mb-6">سجل الحضور والغياب</h1>

      <div className="flex flex-wrap gap-4 mb-6">
        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium mb-2">التاريخ</label>
          <input
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="w-full p-2 border rounded-md"
          />
        </div>

        <div className="flex-1 min-w-[200px]">
          <label className="block text-sm font-medium mb-2">القسم</label>
          <select
            value={classeId}
            onChange={(e) => setClasseId(e.target.value)}
            className="w-full p-2 border rounded-md"
          >
            <option value="">جميع الأقسام</option>
            {Array.isArray(classes) && classes.length > 0 ? (
              classes.map(classe => (
                <option key={classe.id} value={classe.id.toString()}>
                  {classe.name}
                </option>
              ))
            ) : (
              <option value="no-classes" disabled>لا توجد أقسام متاحة</option>
            )}
          </select>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">جاري التحميل...</div>
      ) : !Array.isArray(attendance) || attendance.length === 0 ? (
        <div className="text-center py-8">لا يوجد تلاميذ في هذا القسم</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border">
            <thead>
              <tr className="bg-gray-100">
                <th className="border p-2 text-right">اسم التلميذ</th>
                <th className="border p-2 text-right">القسم</th>
                <th className="border p-2 text-center">الحالة</th>
              </tr>
            </thead>
            <tbody>
              {Array.isArray(attendance) && attendance.length > 0 ? (
                attendance.map(record => (
                  <tr key={`${record.student.id}-${date}`}>
                    <td className="border p-2">{record.student.name}</td>
                    <td className="border p-2">{record.student.classe?.name || 'غير محدد'}</td>
                    <td className="border p-2">
                      <div className="flex justify-center gap-2">
                        <button
                          onClick={() => handleStatusChange(record, 'PRESENT')}
                          className={`px-3 py-1 rounded ${record.status === 'PRESENT' ? 'bg-primary-color text-white' : 'bg-gray-100'}`}
                        >
                          حاضر
                        </button>
                        <button
                          onClick={() => handleStatusChange(record, 'ABSENT')}
                          className={`px-3 py-1 rounded ${record.status === 'ABSENT' ? 'bg-red-500 text-white' : 'bg-gray-100'}`}
                        >
                          غائب
                        </button>
                        <button
                          onClick={() => handleStatusChange(record, 'EXCUSED')}
                          className={`px-3 py-1 rounded ${record.status === 'EXCUSED' ? 'bg-yellow-500 text-white' : 'bg-gray-100'}`}
                        >
                          بعذر
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="border p-4 text-center">لا توجد بيانات حضور متاحة</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
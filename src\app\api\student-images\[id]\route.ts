import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/student-images/[id] - الحصول على صورة محددة
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    // التحقق من صحة المعرف
    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        error: 'معرف الصورة غير صالح'
      }, { status: 400 });
    }

    // البحث عن الصورة
    const image = await prisma.studentImage.findUnique({
      where: { id },
      include: {
        student: {
          select: {
            id: true,
            name: true
          }
        },
        album: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!image) {
      return NextResponse.json({
        success: false,
        error: 'الصورة غير موجودة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: image,
      message: 'تم جلب الصورة بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student image:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب الصورة'
    }, { status: 500 });
  }
}

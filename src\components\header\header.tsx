"use client";
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FaQuran, FaUserCircle, FaMoon, FaSun, FaCog } from 'react-icons/fa';
import { AiOutlineMenu } from 'react-icons/ai';
import { IoMdArrowDropdown } from 'react-icons/io';
import axios from 'axios';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import SiteLogo from '@/components/SiteLogo';
import DarkModeCustomizer from '@/components/DarkModeCustomizer';
//import { DOMAIN } from '@/utils/constants';

interface User {
  id: number;
  username: string;
  role: string;
}

interface HeaderLink {
  id: string;
  title: string;
  url: string;
  isActive: boolean;
  order: number;
}

interface SiteSettings {
  siteName: string;
  logoUrl?: string;
  primaryColor: string;
  secondaryColor: string;
  headerLinks: HeaderLink[];
}

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [siteSettings, setSiteSettings] = useState<SiteSettings | null>(null);
  const [darkMode, setDarkMode] = useState(false);
  const [showCustomizer, setShowCustomizer] = useState(false);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [isLongPress, setIsLongPress] = useState(false);
  const router = useRouter();

  useEffect(() => {
    // تحميل الإعدادات من localStorage أولاً
    const loadSettingsFromStorage = () => {
      try {
        const savedSettings = localStorage.getItem('siteSettings');
        if (savedSettings) {
          const settings = JSON.parse(savedSettings);
          if (settings && typeof settings === 'object') {
            setSiteSettings(settings);
            return true; // تم تحميل الإعدادات بنجاح
          }
        }
      } catch (error) {
        console.error('Error loading settings from localStorage:', error);
      }
      return false; // لم يتم تحميل الإعدادات
    };

    // تحميل الإعدادات فوراً من localStorage
    const settingsLoaded = loadSettingsFromStorage();

    const fetchData = async () => {
      try {
        // جلب بيانات المستخدم
        const userResponse = await axios.get('/api/users/me');
        console.log('User data response:', userResponse.data);

        // التحقق من وجود البيانات وأنها تحتوي على معرف المستخدم
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const userData = userResponse.data as any;
        if (userData && userData.id && typeof userData.id === 'number') {
          setUser(userData as User);
        }

        // جلب إعدادات الموقع من الخادم
        try {
          const settingsResponse = await axios.get('/api/settings');

          // استخدام type assertion بطريقة آمنة
          type ResponseType = { settings?: SiteSettings };
          const data = settingsResponse.data as ResponseType;

          if (data && data.settings) {
            // حفظ الإعدادات في localStorage
            localStorage.setItem('siteSettings', JSON.stringify(data.settings));
            // تحديث الإعدادات فقط إذا كانت مختلفة أو لم تكن محملة من قبل
            setSiteSettings(data.settings);
          } else if (!settingsLoaded) {
            // إذا لم توجد إعدادات في الخادم ولم تكن محملة من localStorage، استخدم الافتراضية
            const defaultSettings: SiteSettings = {
              siteName: 'نظام برهان للقرآن الكريم',
              logoUrl: '/logo.svg',
              primaryColor: 'var(--primary-color)',
              secondaryColor: 'var(--secondary-color)',
              headerLinks: [
                { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
                { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
                { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
                { id: '4', title: 'مجالس الختم', url: '/khatm-sessions', isActive: true, order: 4 },
                { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
                { id: '6', title: 'اتصل بنا', url: '/contact', isActive: true, order: 6 },
              ]
            };
            setSiteSettings(defaultSettings);
          }
        } catch (settingsError) {
          console.error('Error fetching site settings:', settingsError);
          // إذا فشل جلب الإعدادات ولم تكن محملة من localStorage، استخدم الافتراضية
          if (!settingsLoaded) {
            const defaultSettings: SiteSettings = {
              siteName: 'نظام برهان للقرآن الكريم',
              logoUrl: '/logo.svg',
              primaryColor: 'var(--primary-color)',
              secondaryColor: 'var(--secondary-color)',
              headerLinks: [
                { id: '1', title: 'الرئيسية', url: '/', isActive: true, order: 1 },
                { id: '2', title: 'من نحن', url: '/about', isActive: true, order: 2 },
                { id: '3', title: 'البرامج', url: '/programs', isActive: true, order: 3 },
                { id: '4', title: 'مجالس الختم', url: '/khatm-sessions', isActive: true, order: 4 },
                { id: '5', title: 'التبرعات', url: '/donations', isActive: true, order: 5 },
                { id: '6', title: 'اتصل بنا', url: '/contact', isActive: true, order: 6 },
              ]
            };
            setSiteSettings(defaultSettings);
          }
        }
      } catch (error: unknown) {
        console.log('Not logged in', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // إضافة مستمع للتحديثات في localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteSettings' && e.newValue) {
        try {
          const newSettings = JSON.parse(e.newValue);
          setSiteSettings(newSettings);
        } catch (error) {
          console.error('Error parsing updated settings:', error);
        }
      }
    };

    // إضافة مستمع مخصص للتحديثات الداخلية
    const handleSettingsUpdate = (event: CustomEvent) => {
      setSiteSettings(event.detail);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);
    };
  }, []);

  // تحميل حالة الوضع المظلم
  useEffect(() => {
    try {
      const savedDarkMode = localStorage.getItem('darkMode');
      if (savedDarkMode && savedDarkMode !== 'undefined' && savedDarkMode !== 'null') {
        setDarkMode(JSON.parse(savedDarkMode));
      }
    } catch (error) {
      console.error('Error loading dark mode from localStorage:', error);
      // في حالة الخطأ، استخدم الوضع النهاري كافتراضي
      setDarkMode(false);
    }
  }, []);

  // دوال معالجة التخصيص (الوضع المظلم فقط)
  const handleMouseDown = () => {
    setIsLongPress(false);
    const timer = setTimeout(() => {
      setIsLongPress(true);
      // فتح التخصيص فقط في الوضع المظلم
      if (darkMode) {
        setShowCustomizer(true);
      } else {
        toast.info('تخصيص الألوان متاح فقط في الوضع المظلم. ألوان الوضع النهاري يتم تخصيصها من إعدادات المسؤول.');
      }
    }, 1000); // ضغط مطول لمدة ثانية واحدة
    setLongPressTimer(timer);
  };

  const handleMouseUp = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    // إذا لم يكن ضغط مطول، قم بالتبديل العادي
    if (!isLongPress) {
      toggleDarkMode();
    }
    setIsLongPress(false);
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    // فتح التخصيص فقط في الوضع المظلم
    if (darkMode) {
      setShowCustomizer(true);
    } else {
      toast.info('تخصيص الألوان متاح فقط في الوضع المظلم. ألوان الوضع النهاري يتم تخصيصها من إعدادات المسؤول.');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl+Shift+D لفتح تخصيص الوضع المظلم
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
      e.preventDefault();
      if (darkMode) {
        setShowCustomizer(true);
      } else {
        toast.info('تخصيص الألوان متاح فقط في الوضع المظلم. ألوان الوضع النهاري يتم تخصيصها من إعدادات المسؤول.');
      }
    }
    // Ctrl+D للتبديل السريع
    if (e.ctrlKey && e.key === 'd') {
      e.preventDefault();
      toggleDarkMode();
    }
  };

  // دالة التبديل بين الوضع المظلم والنهاري
  const toggleDarkMode = () => {
    try {
      const newDarkMode = !darkMode;
      setDarkMode(newDarkMode);

      // تطبيق التغيير فوراً على الصفحة
      if (newDarkMode) {
        document.body.classList.add('dark-mode');
        document.documentElement.classList.add('dark');
      } else {
        document.body.classList.remove('dark-mode');
        document.documentElement.classList.remove('dark');
      }

      // تحديث localStorage فوراً
      localStorage.setItem('darkMode', JSON.stringify(newDarkMode));

      // استخدام نظام إدارة الألوان المحسن
      let newColors;

      if (newDarkMode) {
        // جلب ألوان الوضع المظلم المحفوظة أو استخدام الافتراضية
        const savedDarkColors = localStorage.getItem('darkModeColors');
        if (savedDarkColors && savedDarkColors !== 'undefined' && savedDarkColors !== 'null') {
          try {
            newColors = JSON.parse(savedDarkColors);
          } catch {
            newColors = {
              primaryColor: '#22d3ee',
              secondaryColor: '#06b6d4',
              sidebarColor: '#0f172a',
              backgroundColor: '#1e293b',
              accentColor: '#0ea5e9',
              textColor: '#f1f5f9'
            };
          }
        } else {
          newColors = {
            primaryColor: '#22d3ee',
            secondaryColor: '#06b6d4',
            sidebarColor: '#0f172a',
            backgroundColor: '#1e293b',
            accentColor: '#0ea5e9',
            textColor: '#f1f5f9'
          };
        }
      } else {
        // جلب ألوان الوضع النهاري المحفوظة من localStorage أولاً
        const savedLightColors = localStorage.getItem('lightModeColors');
        if (savedLightColors && savedLightColors !== 'undefined' && savedLightColors !== 'null') {
          try {
            newColors = JSON.parse(savedLightColors);
          } catch {
            newColors = {
              primaryColor: '#3b82f6',
              secondaryColor: '#6366f1',
              sidebarColor: '#1a202c',
              backgroundColor: '#f3f4f6',
              accentColor: '#10b981',
              textColor: '#1f2937'
            };
          }
        } else {
          // إذا لم توجد في localStorage، جرب جلب من قاعدة البيانات
          try {
            fetch('/api/site-colors')
              .then(response => response.json())
              .then(data => {
                if (data.success && data.colors) {
                  // حفظ في localStorage للاستخدام المستقبلي
                  localStorage.setItem('lightModeColors', JSON.stringify(data.colors));
                  // تطبيق الألوان المجلبة من قاعدة البيانات
                  const root = document.documentElement;
                  root.style.setProperty('--primary-color', data.colors.primaryColor);
                  root.style.setProperty('--secondary-color', data.colors.secondaryColor);
                  root.style.setProperty('--sidebar-color', data.colors.sidebarColor);
                  root.style.setProperty('--background-color', data.colors.backgroundColor);
                  root.style.setProperty('--accent-color', data.colors.accentColor);
                  root.style.setProperty('--text-color', data.colors.textColor);
                  localStorage.setItem('siteColors', JSON.stringify(data.colors));
                }
              })
              .catch(error => {
                console.error('Error fetching colors from database:', error);
              });
          } catch (error) {
            console.error('Error fetching colors:', error);
          }

          // استخدم الألوان الافتراضية في الوقت الحالي
          newColors = {
            primaryColor: '#3b82f6',
            secondaryColor: '#6366f1',
            sidebarColor: '#1a202c',
            backgroundColor: '#f3f4f6',
            accentColor: '#10b981',
            textColor: '#1f2937'
          };
        }
      }

      // تطبيق الألوان فوراً
      if (typeof window !== 'undefined') {
        const root = document.documentElement;
        root.style.setProperty('--primary-color', newColors.primaryColor);
        root.style.setProperty('--secondary-color', newColors.secondaryColor);
        root.style.setProperty('--sidebar-color', newColors.sidebarColor);
        root.style.setProperty('--background-color', newColors.backgroundColor);
        root.style.setProperty('--accent-color', newColors.accentColor);
        root.style.setProperty('--text-color', newColors.textColor);
      }

      // حفظ الألوان الحالية في localStorage
      localStorage.setItem('siteColors', JSON.stringify(newColors));

      // إرسال حدث مخصص لإشعار المكونات الأخرى
      const event = new CustomEvent('darkModeChanged', {
        detail: { darkMode: newDarkMode }
      });
      window.dispatchEvent(event);

      toast.success(newDarkMode ? '🌙 تم تفعيل الوضع المظلم' : '☀️ تم تفعيل الوضع النهاري');
    } catch (error) {
      console.error('Error toggling dark mode:', error);
      toast.error('حدث خطأ أثناء تغيير الوضع');
      // إعادة تعيين الحالة في حالة الخطأ
      setDarkMode(!darkMode);

      // إعادة تعيين الكلاسات في حالة الخطأ
      if (!darkMode) {
        document.body.classList.remove('dark-mode');
        document.documentElement.classList.remove('dark');
      } else {
        document.body.classList.add('dark-mode');
        document.documentElement.classList.add('dark');
      }
    }
  };

  const logoutHandler = async () => {
    try {
      // استخدام مسار نسبي بدلاً من DOMAIN
      const response = await axios.get('/api/users/logout');
      console.log('Logout response:', response.data);

      // تعيين المستخدم إلى null
      setUser(null);

      // إظهار رسالة نجاح
      toast.success('تم تسجيل الخروج بنجاح');

      // تحديث الصفحة لضمان تحديث الهيدر
      //window.location.reload();
      router.push('/');
      router.refresh();
    } catch (error: unknown) {
      toast.error('حدث خطأ أثناء تسجيل الخروج');
      console.error('Logout error:', error);
    }
  };

  const getDashboardLink = () => {
    if (!user) return '/login';

    switch (user.role) {
      case 'ADMIN':
        return '/admin';
      case 'EMPLOYEE':
        return '/admin/employee-dashboard';
      case 'TEACHER':
        return '/teachers';
      case 'PARENT':
        return '/parents';
      case 'STUDENT':
        return '/students';
      case 'GUARDIAN':
        return '/guardians';
      default:
        return '/dashboard';
    }
  };

  return (
    <nav className="bg-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2" style={{ color: siteSettings?.primaryColor || 'var(--primary-color)' }}>
              <SiteLogo
                size="xl"
                showText={true}
                iconColor={siteSettings?.primaryColor || 'var(--primary-color)'}
                textClassName="text-2xl md:text-3xl truncate"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {siteSettings?.headerLinks && siteSettings.headerLinks
              .filter((link: HeaderLink) => link.isActive)
              .sort((a: HeaderLink, b: HeaderLink) => a.order - b.order)
              .map((link: HeaderLink) => (
                <Link
                  key={link.id}
                  href={link.url}
                  className={`text-gray-700 hover:text-[${siteSettings?.primaryColor}] px-3 py-2 text-base font-medium`}
                >
                  {link.title}
                </Link>
              ))
            }

            {/* زر التبديل بين الوضع المظلم والنهاري مع التخصيص */}
            <div className="relative">
              <button
                onMouseDown={handleMouseDown}
                onMouseUp={handleMouseUp}
                onMouseLeave={() => {
                  if (longPressTimer) {
                    clearTimeout(longPressTimer);
                    setLongPressTimer(null);
                  }
                  setIsLongPress(false);
                }}
                onContextMenu={handleContextMenu}
                onKeyDown={handleKeyDown}
                className={`p-2 rounded-lg text-gray-700 hover:text-[var(--primary-color)] hover:bg-gray-100 transition-all duration-200 relative ${
                  isLongPress ? 'bg-blue-100 scale-105' : ''
                }`}
                title={darkMode ?
                  "نقر: تبديل الوضع | ضغط مطول: تخصيص الوضع المظلم | زر أيمن: تخصيص" :
                  "نقر: تبديل الوضع | تخصيص الوضع المظلم متاح فقط في الوضع المظلم"
                }
              >
                <div className="flex items-center gap-1">
                  {darkMode ? <FaSun className="text-lg" /> : <FaMoon className="text-lg" />}
                  <FaCog className="text-xs opacity-60" />
                </div>
              </button>
            </div>

            {loading ? (
              <div className="w-24 h-8 bg-gray-200 animate-pulse rounded-md"></div>
            ) : user ? (
              <div className="relative">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center space-x-2 text-gray-700 hover:text-[var(--primary-color)]"
                >
                  <FaUserCircle className="text-xl ml-1" />
                  <span className="text-base font-medium">{user.username}</span>
                  <IoMdArrowDropdown />
                </button>

                {userMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                    <Link
                      href={getDashboardLink()}
                      className="block px-4 py-2 text-base text-gray-700 hover:bg-gray-100"
                      onClick={() => setUserMenuOpen(false)}
                    >
                      لوحة التحكم
                    </Link>
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-base text-gray-700 hover:bg-gray-100"
                      onClick={() => setUserMenuOpen(false)}
                    >
                      الملف الشخصي
                    </Link>
                    <button
                      onClick={() => {
                        setUserMenuOpen(false);
                        logoutHandler();
                      }}
                      className="block w-full text-right px-4 py-2 text-base text-gray-700 hover:bg-gray-100"
                    >
                      تسجيل الخروج
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-6">
                <Link
                  href="/login"
                  className="text-[var(--primary-color)] border border-[var(--primary-color)] px-4 py-2 rounded-md text-base font-medium hover:bg-[var(--primary-color)] hover:text-white transition-all"
                >
                  تسجيل الدخول
                </Link>
               {/* <Link
                  href="/register"
                  className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md text-base font-medium hover:bg-opacity-90 transition-all"
                >
                  إنشاء حساب
                </Link> */}
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-[var(--primary-color)] focus:outline-none"
            >
              <AiOutlineMenu className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4">
            <div className="flex flex-col space-y-2">
              {siteSettings?.headerLinks && siteSettings.headerLinks
                .filter((link: HeaderLink) => link.isActive)
                .sort((a: HeaderLink, b: HeaderLink) => a.order - b.order)
                .map((link: HeaderLink) => (
                  <Link
                    key={link.id}
                    href={link.url}
                    className={`text-gray-700 hover:text-[${siteSettings?.primaryColor}] px-3 py-2 text-base font-medium`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {link.title}
                  </Link>
                ))
              }

              {/* زر التبديل بين الوضع المظلم والنهاري للجوال */}
              <div className="flex gap-2">
                <button
                  onClick={() => {
                    toggleDarkMode();
                    setIsMenuOpen(false);
                  }}
                  className="flex items-center justify-center space-x-2 text-gray-700 hover:text-[var(--primary-color)] px-3 py-2 text-base font-medium flex-1"
                >
                  {darkMode ? <FaSun className="text-lg ml-2" /> : <FaMoon className="text-lg ml-2" />}
                  <span>{darkMode ? 'الوضع النهاري' : 'الوضع المظلم'}</span>
                </button>
                <button
                  onClick={() => {
                    if (darkMode) {
                      setShowCustomizer(true);
                      setIsMenuOpen(false);
                    } else {
                      toast.info('تخصيص الألوان متاح فقط في الوضع المظلم');
                      setIsMenuOpen(false);
                    }
                  }}
                  className={`flex items-center justify-center px-3 py-2 text-base font-medium ${
                    darkMode
                      ? 'text-gray-700 hover:text-[var(--primary-color)]'
                      : 'text-gray-400 cursor-not-allowed'
                  }`}
                  title={darkMode ? "تخصيص الوضع المظلم" : "متاح فقط في الوضع المظلم"}
                >
                  <FaCog className="text-lg" />
                </button>
              </div>

              {loading ? (
                <div className="w-24 h-8 bg-gray-200 animate-pulse rounded-md"></div>
              ) : user ? (
                <>
                  <Link
                    href={getDashboardLink()}
                    className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md text-base font-medium hover:bg-opacity-90 transition-all inline-block text-center"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    لوحة التحكم
                  </Link>
                  <Link
                    href="/profile"
                    className="text-gray-700 hover:text-[var(--primary-color)] px-3 py-2 text-base font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    الملف الشخصي
                  </Link>
                  <button
                    onClick={() => {
                      setIsMenuOpen(false);
                      logoutHandler();
                    }}
                    className="text-gray-700 hover:text-[var(--primary-color)] px-3 py-2 text-base font-medium text-right w-full"
                  >
                    تسجيل الخروج
                  </button>
                </>
              ) : (
                <div className="flex flex-col space-y-3">
                  <Link
                    href="/login"
                    className="text-[var(--primary-color)] border border-[var(--primary-color)] px-4 py-2 rounded-md text-base font-medium hover:bg-[var(--primary-color)] hover:text-white transition-all inline-block text-center"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    تسجيل الدخول
                  </Link>
                 {/* <Link
                    href="/register"
                    className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md text-base font-medium hover:bg-opacity-90 transition-all inline-block text-center"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    إنشاء حساب
                  </Link> */}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* مكون تخصيص الوضع المظلم */}
      <DarkModeCustomizer
        isOpen={showCustomizer}
        onClose={() => setShowCustomizer(false)}
        isDarkMode={darkMode}
      />
    </nav>
  );
};

export default Header;

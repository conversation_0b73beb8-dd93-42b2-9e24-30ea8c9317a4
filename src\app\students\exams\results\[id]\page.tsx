'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { toast } from 'react-hot-toast';
import { Loader2, CheckCircle, XCircle, AlertCircle, ChevronLeft } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import Link from 'next/link';

interface Exam {
  id: number;
  evaluationType: string;
  month: string;
  description: string | null;
  maxPoints: number;
  passingPoints: number;
  hasAutoGrading: boolean;
  examType: {
    id: number;
    name: string;
  } | null;
}

interface ExamPoint {
  id: number;
  examId: number;
  studentId: number;
  classSubjectId: number;
  grade: number;
  status: string;
  student: {
    id: number;
    name: string;
  };
  exam: Exam;
  studentAnswers: StudentAnswer[];
}

interface StudentAnswer {
  id: number;
  examPointId: number;
  examQuestionId: number;
  answer: string;
  isCorrect: boolean | null;
  points: number | null;
  feedback: string | null;
  examQuestion: {
    id: number;
    examId: number;
    questionId: number;
    order: number;
    points: number | null;
    question: {
      id: number;
      text: string;
      type: string;
      difficultyLevel: string;
      points: number;
      options: {
        id: number;
        text: string;
        isCorrect: boolean;
        order: number;
      }[];
      answers: {
        id: number;
        text: string;
        isCorrect: boolean;
        explanation: string | null;
      }[];
    };
  };
}

export default function ExamResultsPage() {
  const params = useParams();
  const router = useRouter();
  const examId = params?.id as string;

  const [examPoint, setExamPoint] = useState<ExamPoint | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!examId) {
      toast.error('معرف الامتحان مطلوب');
      router.push('/students/exams');
      return;
    }

    fetchExamResults();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examId, router]);

  const fetchExamResults = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/student/exam-points?examId=${examId}&includeAnswers=true`);
      const result = await response.json();

      if (result.success && result.data) {
        setExamPoint(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب نتائج الامتحان');
        router.push('/students/exams');
      }
    } catch (error) {
      console.error('Error fetching exam results:', error);
      toast.error('حدث خطأ أثناء جلب نتائج الامتحان');
      router.push('/students/exams');
    } finally {
      setIsLoading(false);
    }
  };

  const getEvaluationTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      QURAN_MEMORIZATION: 'حفظ القرآن',
      QURAN_RECITATION: 'تلاوة القرآن',
      WRITTEN_EXAM: 'تحريري',
      ORAL_EXAM: 'شفهي',
      PRACTICAL_TEST: 'عملي',
      HOMEWORK: 'واجب منزلي',
      PROJECT: 'مشروع',
      REMOTE_EXAM: 'امتحان عن بعد'
    };
    return labels[type] || type;
  };

  const formatDate = (dateString: string): string => {
    const [year, month] = dateString.split('-');
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const getStatusLabel = (status: string): string => {
    const labels: Record<string, string> = {
      PENDING: 'قيد الانتظار',
      COMPLETED: 'مكتمل',
      PASSED: 'ناجح',
      FAILED: 'راسب',
      EXCELLENT: 'ممتاز'
    };
    return labels[status] || status;
  };

  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      COMPLETED: 'bg-blue-100 text-blue-800',
      PASSED: 'bg-green-100 text-green-800',
      FAILED: 'bg-red-100 text-red-800',
      EXCELLENT: 'bg-purple-100 text-purple-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getQuestionTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      MULTIPLE_CHOICE: 'اختيار من متعدد',
      TRUE_FALSE: 'صح أو خطأ',
      SHORT_ANSWER: 'إجابة قصيرة',
      ESSAY: 'مقال',
      MATCHING: 'مطابقة',
      FILL_BLANK: 'ملء الفراغات',
      ORDERING: 'ترتيب'
    };
    return types[type] || type;
  };

  const calculateCorrectAnswers = () => {
    if (!examPoint || !examPoint.studentAnswers) return 0;

    return examPoint.studentAnswers.filter(answer => answer.isCorrect === true).length;
  };

  const calculateIncorrectAnswers = () => {
    if (!examPoint || !examPoint.studentAnswers) return 0;

    return examPoint.studentAnswers.filter(answer => answer.isCorrect === false).length;
  };

  const calculatePendingAnswers = () => {
    if (!examPoint || !examPoint.studentAnswers) return 0;

    return examPoint.studentAnswers.filter(answer => answer.isCorrect === null).length;
  };

  const calculateTotalPoints = () => {
    if (!examPoint || !examPoint.studentAnswers) return 0;

    return examPoint.studentAnswers.reduce((total, answer) => total + (answer.points || 0), 0);
  };

  const calculateMaxPoints = () => {
    if (!examPoint || !examPoint.studentAnswers) return 0;

    return examPoint.studentAnswers.reduce((total, answer) => {
      const questionPoints = answer.examQuestion.points || answer.examQuestion.question.points;
      return total + questionPoints;
    }, 0);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
      </div>
    );
  }

  if (!examPoint) {
    return (
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="text-center">لم يتم العثور على نتائج</CardTitle>
            <CardDescription className="text-center">لم يتم العثور على نتائج لهذا الامتحان</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center">
              <AlertCircle className="h-16 w-16 text-amber-500" />
            </div>
            <p className="text-center">قد تكون لم تقم بإجراء هذا الامتحان بعد أو لم يتم تصحيحه</p>
            <div className="flex justify-center">
              <Link href="/students/exams">
                <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                  <ChevronLeft className="ml-2" size={16} />
                  العودة إلى قائمة الامتحانات
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <Link href="/students/exams">
            <Button variant="outline" size="sm">
              <ChevronLeft className="ml-2" size={16} />
              العودة إلى قائمة الامتحانات
            </Button>
          </Link>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] text-center">
            نتائج الامتحان
          </h1>
          <div className="w-[100px]"></div> {/* للمحافظة على التوازن */}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">معلومات الامتحان</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">نوع التقييم:</span>
              <span>{getEvaluationTypeLabel(examPoint.exam.evaluationType)}</span>
            </div>
            {examPoint.exam.examType && (
              <div className="flex justify-between">
                <span className="font-medium">نوع الامتحان:</span>
                <span>{examPoint.exam.examType.name}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="font-medium">الشهر:</span>
              <span>{formatDate(examPoint.exam.month)}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">الدرجة القصوى:</span>
              <span>{examPoint.exam.maxPoints}</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">درجة النجاح:</span>
              <span>{examPoint.exam.passingPoints}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">نتيجة الامتحان</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center">
              <Badge className={`text-lg py-1 px-4 ${getStatusColor(examPoint.status)}`}>
                {getStatusLabel(examPoint.status)}
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold">{examPoint.grade.toFixed(1)}</div>
              <div className="text-sm text-gray-500">من {examPoint.exam.maxPoints}</div>
            </div>
            <Progress
              value={(examPoint.grade / examPoint.exam.maxPoints) * 100}
              className="h-2 mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">إحصائيات الإجابات</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="flex items-center">
                <CheckCircle className="h-4 w-4 text-primary-color ml-2" />
                إجابات صحيحة:
              </span>
              <span>{calculateCorrectAnswers()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="flex items-center">
                <XCircle className="h-4 w-4 text-red-500 ml-2" />
                إجابات خاطئة:
              </span>
              <span>{calculateIncorrectAnswers()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="flex items-center">
                <AlertCircle className="h-4 w-4 text-amber-500 ml-2" />
                إجابات قيد التصحيح:
              </span>
              <span>{calculatePendingAnswers()}</span>
            </div>
            <div className="flex justify-between items-center mt-4 pt-2 border-t">
              <span className="font-medium">مجموع النقاط:</span>
              <span>{calculateTotalPoints()} / {calculateMaxPoints()}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>تفاصيل الإجابات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {examPoint.studentAnswers.sort((a, b) => a.examQuestion.order - b.examQuestion.order).map((answer) => (
              <div key={answer.id} className="border rounded-lg overflow-hidden">
                <div className="bg-gray-50 p-4">
                  <div className="flex justify-between items-start">
                    <Badge className="bg-blue-100 text-blue-800">
                      السؤال {answer.examQuestion.order + 1}
                    </Badge>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Badge className="bg-purple-100 text-purple-800">
                        {getQuestionTypeLabel(answer.examQuestion.question.type)}
                      </Badge>
                      <Badge className="bg-gray-100 text-gray-800">
                        {answer.examQuestion.points || answer.examQuestion.question.points} نقطة
                      </Badge>
                    </div>
                  </div>
                  <p className="mt-2 text-right">{answer.examQuestion.question.text}</p>
                </div>

                <div className="p-4 border-t">
                  <div className="mb-4">
                    <h4 className="font-medium text-right mb-1">إجابتك:</h4>
                    <div className="p-3 bg-gray-50 rounded-md text-right">
                      {answer.answer || <span className="text-gray-400">لم تقدم إجابة</span>}
                    </div>
                  </div>

                  {answer.isCorrect !== null && (
                    <div className="mb-4">
                      <h4 className="font-medium text-right mb-1">الإجابة الصحيحة:</h4>
                      <div className="p-3 bg-green-50 rounded-md text-right">
                        {answer.examQuestion.question.type === 'MULTIPLE_CHOICE' &&
                          answer.examQuestion.question.options.find(o => o.isCorrect)?.text
                        }
                        {answer.examQuestion.question.type === 'TRUE_FALSE' &&
                          (answer.examQuestion.question.options.find(o => o.isCorrect)?.text === 'صح' ? 'صح' : 'خطأ')
                        }
                        {['SHORT_ANSWER', 'ESSAY', 'FILL_BLANK'].includes(answer.examQuestion.question.type) &&
                          answer.examQuestion.question.answers.map(a => a.text).join(' أو ')
                        }
                        {!['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SHORT_ANSWER', 'ESSAY', 'FILL_BLANK'].includes(answer.examQuestion.question.type) &&
                          <span className="text-gray-400">غير متاح</span>
                        }
                      </div>
                    </div>
                  )}

                  {answer.feedback && (
                    <div className="mb-4">
                      <h4 className="font-medium text-right mb-1">ملاحظات المصحح:</h4>
                      <div className="p-3 bg-blue-50 rounded-md text-right">
                        {answer.feedback}
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center mt-4 pt-3 border-t">
                    <div>
                      <span className="font-medium">النقاط المحصلة: </span>
                      <span>{answer.points !== null ? answer.points : 'قيد التصحيح'}</span>
                    </div>
                    <div>
                      {answer.isCorrect === true && (
                        <Badge className="bg-green-100 text-green-800">
                          <CheckCircle className="h-4 w-4 ml-1" />
                          إجابة صحيحة
                        </Badge>
                      )}
                      {answer.isCorrect === false && (
                        <Badge className="bg-red-100 text-red-800">
                          <XCircle className="h-4 w-4 ml-1" />
                          إجابة خاطئة
                        </Badge>
                      )}
                      {answer.isCorrect === null && (
                        <Badge className="bg-amber-100 text-amber-800">
                          <AlertCircle className="h-4 w-4 ml-1" />
                          قيد التصحيح
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter className="flex justify-center border-t pt-6">
          <Link href="/students/exams">
            <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
              <ChevronLeft className="ml-2" size={16} />
              العودة إلى قائمة الامتحانات
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  );
}

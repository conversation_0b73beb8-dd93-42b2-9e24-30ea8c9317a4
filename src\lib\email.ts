import nodemailer from 'nodemailer';

interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType?: string;
}

interface EmailData {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  attachments?: EmailAttachment[];
}

/**
 * إرسال بريد إلكتروني
 * @param emailData بيانات البريد الإلكتروني
 * @returns وعد بنتيجة إرسال البريد الإلكتروني
 */
export async function sendEmail(emailData: EmailData): Promise<nodemailer.SentMessageInfo> {
  try {
    // إنشاء ناقل البريد الإلكتروني
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: process.env.EMAIL_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
      }
    });

    // إعداد خيارات البريد الإلكتروني
    const mailOptions = {
      from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
      to: Array.isArray(emailData.to) ? emailData.to.join(',') : emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text || stripHtml(emailData.html),
      attachments: emailData.attachments || []
    };

    // إرسال البريد الإلكتروني
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}

/**
 * إزالة علامات HTML من النص
 * @param html نص HTML
 * @returns نص عادي
 */
function stripHtml(html: string): string {
  return html.replace(/<[^>]*>?/gm, '');
}

/**
 * إرسال بريد إلكتروني للتقرير
 * @param to عنوان البريد الإلكتروني للمستلم
 * @param subject عنوان البريد الإلكتروني
 * @param message نص الرسالة
 * @param reportTitle عنوان التقرير
 * @param attachment مرفق التقرير (اختياري)
 * @returns وعد بنتيجة إرسال البريد الإلكتروني
 */
export async function sendReportEmail(
  to: string | string[],
  subject: string,
  message: string,
  reportTitle: string,
  attachment?: { filename: string; content: Buffer; contentType: string }
): Promise<nodemailer.SentMessageInfo> {
  const emailData: EmailData = {
    to,
    subject,
    html: `
      <div dir="rtl" style="text-align: right; font-family: Arial, sans-serif;">
        <h2>مشاركة تقرير: ${reportTitle}</h2>
        <p>${message}</p>
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مدرسة القرآن.</p>
      </div>
    `,
    attachments: attachment ? [attachment] : []
  };

  return sendEmail(emailData);
}

/**
 * إرسال بريد إلكتروني للتذكير بالتقرير المجدول
 * @param to عنوان البريد الإلكتروني للمستلم
 * @param reportTitle عنوان التقرير
 * @param frequency تكرار التقرير
 * @param attachment مرفق التقرير (اختياري)
 * @returns وعد بنتيجة إرسال البريد الإلكتروني
 */
export async function sendScheduledReportEmail(
  to: string | string[],
  reportTitle: string,
  frequency: string,
  attachment?: { filename: string; content: Buffer; contentType: string }
): Promise<nodemailer.SentMessageInfo> {
  const frequencyText = {
    daily: 'اليومي',
    weekly: 'الأسبوعي',
    monthly: 'الشهري'
  }[frequency] || frequency;

  const emailData: EmailData = {
    to,
    subject: `التقرير ${frequencyText}: ${reportTitle}`,
    html: `
      <div dir="rtl" style="text-align: right; font-family: Arial, sans-serif;">
        <h2>التقرير ${frequencyText}: ${reportTitle}</h2>
        <p>مرحباً،</p>
        <p>مرفق التقرير ${frequencyText} الخاص بـ ${reportTitle}.</p>
        <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة مدرسة القرآن.</p>
      </div>
    `,
    attachments: attachment ? [attachment] : []
  };

  return sendEmail(emailData);
}

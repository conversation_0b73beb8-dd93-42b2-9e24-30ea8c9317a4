/*
==============================================================================
    وحدة الماسح الضوئي للشبكة - Praetorian Network Scanner
    
    الوصف: فاحص المنافذ والخدمات باستخدام sockets.ring مع دعم التعدد
    المؤلف: Praetorian Team
==============================================================================
*/

# إنشاء مثيل عام من الماسح الضوئي
PraetorianScannerInstance = new PraetorianNetworkScanner

/*
==============================================================================
    كلاس الماسح الضوئي للشبكة
==============================================================================
*/

class PraetorianNetworkScanner

    # خصائص الماسح
    nTimeout = 3000        # مهلة الاتصال بالميلي ثانية
    nMaxThreads = 50       # أقصى عدد خيوط
    bVerbose = false       # طباعة تفاصيل إضافية
    oLogger = NULL
    
    # قائمة المنافذ الشائعة
    aCommonPorts = [
        21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995,
        135, 139, 445, 1433, 1521, 3306, 3389, 5432, 5900,
        8080, 8443, 9090, 10000
    ]
    
    /*
    دالة البناء
    */
    func init
        oLogger = PraetorianLoggerInstance
        oLogger.debug("تم تهيئة الماسح الضوئي للشبكة")
    
    /*
    تعيين مهلة الاتصال
    المدخلات: nTimeoutMs - المهلة بالميلي ثانية
    */
    func setTimeout nTimeoutMs
        nTimeout = nTimeoutMs
        oLogger.debug("تم تعيين مهلة الاتصال إلى " + nTimeout + " ميلي ثانية")
    
    /*
    تعيين أقصى عدد خيوط
    المدخلات: nThreads - عدد الخيوط
    */
    func setMaxThreads nThreads
        nMaxThreads = nThreads
        oLogger.debug("تم تعيين أقصى عدد خيوط إلى " + nMaxThreads)
    
    /*
    تفعيل/إلغاء الوضع المفصل
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func setVerbose bEnable
        bVerbose = bEnable
    
    /*
    فحص منفذ واحد باستخدام TCP Connect
    المدخلات: cHost - عنوان الهدف، nPort - رقم المنفذ
    المخرجات: true إذا كان المنفذ مفتوحاً، false إذا كان مغلقاً
    */
    func scanSinglePort cHost, nPort
        try
            # إنشاء مقبس TCP
            sock = socket(AF_INET, SOCK_STREAM)
            if sock = NULL
                oLogger.error("فشل في إنشاء المقبس للمنفذ " + nPort)
                return false
            ok
            
            # محاولة الاتصال
            nResult = connect(sock, cHost, nPort)
            close(sock)
            
            if nResult = 0
                if bVerbose
                    oLogger.info("المنفذ " + nPort + " مفتوح على " + cHost)
                ok
                return true
            else
                if bVerbose
                    oLogger.debug("المنفذ " + nPort + " مغلق على " + cHost)
                ok
                return false
            ok
            
        catch
            if bVerbose
                oLogger.debug("خطأ في فحص المنفذ " + nPort + " على " + cHost + ": " + cCatchError)
            ok
            return false
        done
    
    /*
    فحص قائمة من المنافذ باستخدام TCP Connect
    المدخلات: cHost - عنوان الهدف، aPorts - قائمة المنافذ
    المخرجات: قائمة المنافذ المفتوحة
    */
    func tcpConnectScan cHost, aPorts
        oLogger.startOperation("فحص TCP Connect للهدف " + cHost)
        oLogger.logTarget(cHost, "TCP Scan")
        
        aOpenPorts = []
        nTotalPorts = len(aPorts)
        nScannedPorts = 0
        
        ? "بدء فحص " + nTotalPorts + " منفذ على " + cHost + "..."
        
        for nPort in aPorts
            nScannedPorts++
            
            # عرض التقدم
            if nScannedPorts % 10 = 0 or nScannedPorts = nTotalPorts
                ? "تم فحص " + nScannedPorts + "/" + nTotalPorts + " منفذ..."
            ok
            
            # فحص المنفذ
            if scanSinglePort(cHost, nPort)
                add(aOpenPorts, nPort)
                oLogger.logScanResult(cHost + ":" + nPort, "مفتوح", "TCP Connect")
            ok
        next
        
        oLogger.endOperation("فحص TCP Connect للهدف " + cHost)
        oLogger.info("تم العثور على " + len(aOpenPorts) + " منفذ مفتوح")
        
        return aOpenPorts
    
    /*
    فحص المنافذ الشائعة
    المدخلات: cHost - عنوان الهدف
    المخرجات: قائمة المنافذ المفتوحة
    */
    func scanCommonPorts cHost
        oLogger.info("بدء فحص المنافذ الشائعة للهدف " + cHost)
        return tcpConnectScan(cHost, aCommonPorts)
    
    /*
    فحص نطاق من المنافذ
    المدخلات: cHost - عنوان الهدف، nStartPort - المنفذ الأول، nEndPort - المنفذ الأخير
    المخرجات: قائمة المنافذ المفتوحة
    */
    func scanPortRange cHost, nStartPort, nEndPort
        if nStartPort > nEndPort
            oLogger.error("المنفذ الأول يجب أن يكون أصغر من المنفذ الأخير")
            return []
        ok
        
        if nStartPort < 1 or nEndPort > 65535
            oLogger.error("أرقام المنافذ يجب أن تكون بين 1 و 65535")
            return []
        ok
        
        aPorts = []
        for nPort = nStartPort to nEndPort
            add(aPorts, nPort)
        next
        
        oLogger.info("بدء فحص المنافذ من " + nStartPort + " إلى " + nEndPort + " للهدف " + cHost)
        return tcpConnectScan(cHost, aPorts)
    
    /*
    جلب البانر من منفذ مفتوح
    المدخلات: cHost - عنوان الهدف، nPort - رقم المنفذ
    المخرجات: البانر كنص، أو سلسلة فارغة إذا فشل
    */
    func bannerGrab cHost, nPort
        oLogger.debug("محاولة جلب البانر من " + cHost + ":" + nPort)
        
        try
            # إنشاء مقبس TCP
            sock = socket(AF_INET, SOCK_STREAM)
            if sock = NULL
                oLogger.error("فشل في إنشاء المقبس لجلب البانر")
                return ""
            ok
            
            # الاتصال بالهدف
            nResult = connect(sock, cHost, nPort)
            if nResult != 0
                close(sock)
                oLogger.debug("فشل الاتصال لجلب البانر من " + cHost + ":" + nPort)
                return ""
            ok
            
            # محاولة قراءة البانر
            cBanner = recv(sock, 1024)
            close(sock)
            
            # تنظيف البانر
            cBanner = substr(cBanner, char(13), "")  # إزالة CR
            cBanner = substr(cBanner, char(10), " ") # استبدال LF بمسافة
            cBanner = trim(cBanner)
            
            if len(cBanner) > 0
                oLogger.info("تم جلب البانر من " + cHost + ":" + nPort + " - " + cBanner)
                return cBanner
            else
                oLogger.debug("لم يتم العثور على بانر في " + cHost + ":" + nPort)
                return ""
            ok
            
        catch
            oLogger.debug("خطأ في جلب البانر من " + cHost + ":" + nPort + ": " + cCatchError)
            return ""
        done
    
    /*
    فحص شامل مع جلب البانر
    المدخلات: cHost - عنوان الهدف، aPorts - قائمة المنافذ
    المخرجات: قائمة تحتوي على معلومات المنافذ المفتوحة مع البانر
    */
    func comprehensiveScan cHost, aPorts
        oLogger.startOperation("الفحص الشامل للهدف " + cHost)
        
        aResults = []
        aOpenPorts = tcpConnectScan(cHost, aPorts)
        
        ? ""
        ? "جلب البانر من المنافذ المفتوحة..."
        
        for nPort in aOpenPorts
            cBanner = bannerGrab(cHost, nPort)
            cService = identifyService(nPort, cBanner)
            
            aPortInfo = [
                :port = nPort,
                :status = "مفتوح",
                :banner = cBanner,
                :service = cService
            ]
            
            add(aResults, aPortInfo)
            
            cMessage = "المنفذ " + nPort + " - " + cService
            if len(cBanner) > 0
                cMessage += " (" + cBanner + ")"
            ok
            ? cMessage
        next
        
        oLogger.endOperation("الفحص الشامل للهدف " + cHost)
        return aResults
    
    /*
    تحديد نوع الخدمة بناءً على رقم المنفذ والبانر
    المدخلات: nPort - رقم المنفذ، cBanner - البانر
    المخرجات: اسم الخدمة المتوقعة
    */
    func identifyService nPort, cBanner
        # تحديد الخدمة بناءً على رقم المنفذ
        switch nPort
            on 21
                return "FTP"
            on 22
                return "SSH"
            on 23
                return "Telnet"
            on 25
                return "SMTP"
            on 53
                return "DNS"
            on 80
                return "HTTP"
            on 110
                return "POP3"
            on 143
                return "IMAP"
            on 443
                return "HTTPS"
            on 993
                return "IMAPS"
            on 995
                return "POP3S"
            on 135
                return "RPC"
            on 139
                return "NetBIOS"
            on 445
                return "SMB"
            on 1433
                return "MSSQL"
            on 1521
                return "Oracle"
            on 3306
                return "MySQL"
            on 3389
                return "RDP"
            on 5432
                return "PostgreSQL"
            on 5900
                return "VNC"
            on 8080
                return "HTTP-Alt"
            on 8443
                return "HTTPS-Alt"
            other
                # محاولة تحديد الخدمة من البانر
                if len(cBanner) > 0
                    cBannerLower = lower(cBanner)
                    if substr(cBannerLower, "ssh") > 0
                        return "SSH"
                    but substr(cBannerLower, "ftp") > 0
                        return "FTP"
                    but substr(cBannerLower, "http") > 0
                        return "HTTP"
                    but substr(cBannerLower, "smtp") > 0
                        return "SMTP"
                    but substr(cBannerLower, "mysql") > 0
                        return "MySQL"
                    else
                        return "غير معروف"
                    ok
                else
                    return "غير معروف"
                ok
        off
    
    /*
    طباعة تقرير مفصل عن نتائج الفحص
    المدخلات: cHost - عنوان الهدف، aResults - نتائج الفحص
    */
    func printScanReport cHost, aResults
        ? ""
        ? "==============================================="
        ? "تقرير فحص الشبكة للهدف: " + cHost
        ? "==============================================="
        ? "تاريخ الفحص: " + date() + " " + time()
        ? "عدد المنافذ المفتوحة: " + len(aResults)
        ? "==============================================="

        if len(aResults) = 0
            ? "لم يتم العثور على منافذ مفتوحة"
        else
            ? "المنفذ\t\tالخدمة\t\tالبانر"
            ? "-----------------------------------------------"
            for aPortInfo in aResults
                cPortStr = string(aPortInfo[:port])
                cServiceStr = aPortInfo[:service]
                cBannerStr = aPortInfo[:banner]

                # تنسيق العرض
                while len(cPortStr) < 8
                    cPortStr += " "
                end

                while len(cServiceStr) < 12
                    cServiceStr += " "
                end

                if len(cBannerStr) > 40
                    cBannerStr = substr(cBannerStr, 1, 37) + "..."
                ok

                ? cPortStr + "\t" + cServiceStr + "\t" + cBannerStr
            next
        ok

        ? "==============================================="

    /*
    فحص سريع للمنافذ الأساسية
    المدخلات: cHost - عنوان الهدف
    المخرجات: قائمة المنافذ المفتوحة
    */
    func quickScan cHost
        aQuickPorts = [21, 22, 23, 25, 53, 80, 135, 139, 443, 445, 993, 995, 1723, 3389, 5900]
        oLogger.info("بدء الفحص السريع للهدف " + cHost)
        return tcpConnectScan(cHost, aQuickPorts)


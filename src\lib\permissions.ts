import { NextRequest } from 'next/server';
import { getToken } from '@/lib/auth';
import prisma from '@/lib/prisma';

export interface UserData {
  id: number;
  username: string;
  role: string;
  roleId?: number;
}

/**
 * التحقق من صلاحية المستخدم
 */
export async function checkUserPermission(
  request: NextRequest,
  requiredPermission: string
): Promise<{ success: boolean; userData?: UserData; message?: string; status?: number }> {
  try {
    // التحقق من وجود التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return {
        success: false,
        message: "غير مصرح به: يرجى تسجيل الدخول",
        status: 401
      };
    }

    // التحقق من صحة التوكن
    const userData = await getToken(token);
    if (!userData) {
      return {
        success: false,
        message: "غير مصرح به: توكن غير صالح",
        status: 401
      };
    }

    // المدير لديه جميع الصلاحيات
    if (userData.role === 'ADMIN') {
      return {
        success: true,
        userData
      };
    }

    // للموظفين، التحقق من الصلاحية المحددة
    if (userData.role === 'EMPLOYEE' && userData.roleId) {
      const userWithPermissions = await prisma.user.findUnique({
        where: { id: userData.id },
        include: {
          userRole: {
            include: {
              permissions: {
                include: {
                  permission: true
                },
                where: {
                  permission: {
                    isActive: true
                  }
                }
              }
            }
          }
        }
      });

      if (!userWithPermissions) {
        return {
          success: false,
          message: "المستخدم غير موجود",
          status: 404
        };
      }

      // التحقق من وجود الصلاحية المطلوبة
      const hasPermission = userWithPermissions.userRole?.permissions?.some(
        rp => rp.permission.key === requiredPermission
      ) || false;

      if (hasPermission) {
        return {
          success: true,
          userData
        };
      } else {
        return {
          success: false,
          message: `غير مصرح به: تحتاج إلى صلاحية ${requiredPermission}`,
          status: 403
        };
      }
    }

    // للأدوار الأخرى، رفض الوصول
    return {
      success: false,
      message: "غير مصرح به: ليس لديك صلاحية للوصول لهذا المورد",
      status: 403
    };

  } catch (error) {
    console.error('Error checking user permission:', error);
    return {
      success: false,
      message: "حدث خطأ أثناء التحقق من الصلاحيات",
      status: 500
    };
  }
}

/**
 * التحقق من صلاحية المستخدم (نسخة مبسطة للمدير فقط)
 */
export async function checkAdminPermission(
  request: NextRequest
): Promise<{ success: boolean; userData?: UserData; message?: string; status?: number }> {
  try {
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return {
        success: false,
        message: "غير مصرح به: يرجى تسجيل الدخول",
        status: 401
      };
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'ADMIN') {
      return {
        success: false,
        message: "غير مصرح به: يجب أن تكون مسؤولاً",
        status: 403
      };
    }

    return {
      success: true,
      userData
    };

  } catch (error) {
    console.error('Error checking admin permission:', error);
    return {
      success: false,
      message: "حدث خطأ أثناء التحقق من الصلاحيات",
      status: 500
    };
  }
}

/**
 * التحقق من صلاحية محددة للمستخدم بناءً على معرف المستخدم
 */
export async function checkPermission(
  userId: number,
  requiredPermission: string
): Promise<boolean> {
  try {
    // جلب بيانات المستخدم
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              },
              where: {
                permission: {
                  isActive: true
                }
              }
            }
          }
        }
      }
    });

    if (!user) {
      return false;
    }

    // المدير لديه جميع الصلاحيات
    if (user.role === 'ADMIN') {
      return true;
    }

    // للموظفين، التحقق من الصلاحية المحددة
    if (user.role === 'EMPLOYEE' && user.userRole) {
      const hasPermission = user.userRole.permissions?.some(
        rp => rp.permission.key === requiredPermission
      ) || false;

      return hasPermission;
    }

    // للأدوار الأخرى، رفض الوصول
    return false;

  } catch (error) {
    console.error('Error checking permission:', error);
    return false;
  }
}

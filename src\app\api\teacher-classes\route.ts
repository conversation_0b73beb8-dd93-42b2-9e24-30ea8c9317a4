import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

// GET /api/teacher-classes - جلب الفصول الخاصة بالمعلم المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'TEACHER') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userId = userData.id;

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userId
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // جلب الفصول التي يدرسها المعلم
    const teacherClasses = await prisma.classSubject.findMany({
      where: {
        teacherSubject: {
          teacherId: teacher.id
        }
      },
      select: {
        classeId: true,
        classe: {
          select: {
            id: true,
            name: true,
            students: {
              select: {
                id: true
              }
            }
          }
        }
      },
      distinct: ['classeId']
    });

    // تنسيق البيانات
    const classes = teacherClasses.map(tc => ({
      id: tc.classe.id,
      name: tc.classe.name,
      studentsCount: tc.classe.students.length
    }));

    return NextResponse.json({
      classes,
      message: "تم جلب بيانات الفصول بنجاح"
    });
  } catch (error) {
    console.error('Error fetching teacher classes:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب بيانات الفصول" },
      { status: 500 }
    );
  }
}

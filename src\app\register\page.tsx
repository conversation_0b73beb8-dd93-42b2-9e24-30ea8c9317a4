'use client';
import React from 'react'
import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import Link from 'next/link';
import SiteLogo from '@/components/SiteLogo';

const RegisterPage = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    user: '',
    password: '',
    confirmPassword: '',
    name: '',
    phone: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [registrationEnabled, setRegistrationEnabled] = useState(true);
  const [checkingStatus, setCheckingStatus] = useState(true);

  useEffect(() => {
    const checkRegistrationStatus = async () => {
      try {
        const response = await axios.get('/api/registration-status');

        // استخدام type assertion بطريقة آمنة
        type ResponseType = { registrationEnabled?: boolean };
        const data = response.data as ResponseType;

        if (data && typeof data.registrationEnabled === 'boolean') {
          setRegistrationEnabled(data.registrationEnabled);
        }
      } catch (error) {
        console.error('Error checking registration status:', error);
        // في حالة الخطأ، نفترض أن التسجيل مفعل
      } finally {
        setCheckingStatus(false);
      }
    };

    checkRegistrationStatus();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من البيانات
    if (!formData.user || !formData.password || !formData.name) {
      return toast.error('جميع الحقول المطلوبة يجب ملؤها');
    }

    if (formData.password !== formData.confirmPassword) {
      return toast.error('كلمات المرور غير متطابقة');
    }

    if (formData.password.length < 6) {
      return toast.error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    setIsLoading(true);

    try {
      await axios.post('/api/users/register', {
        user: formData.user,
        password: formData.password,
        name: formData.name,
        phone: formData.phone
      });

      toast.success('تم التسجيل بنجاح');
      router.push('/login');
    } catch (error: unknown) {
      console.error('Registration error:', error);
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { data?: { message?: string } } };
        toast.error(axiosError.response?.data?.message || 'فشل في التسجيل');
      } else {
        toast.error('فشل في التسجيل');
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8" dir="rtl">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* شعار الموقع */}
        <div className="flex justify-center pt-6 pb-4">
          <SiteLogo size="xl" showText={true} iconColor="var(--primary-color)" />
        </div>

        <div className="bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] px-6 py-4">
          <h2 className="text-2xl font-bold text-white text-center">إنشاء حساب جديد</h2>
        </div>

        {checkingStatus ? (
          <div className="px-6 py-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto mb-4"></div>
            <p className="text-gray-600">جاري التحقق من حالة التسجيل...</p>
          </div>
        ) : !registrationEnabled ? (
          <div className="px-6 py-8 text-center">
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="mr-3">
                  <h3 className="text-sm font-medium text-yellow-800">التسجيل معطل حالياً</h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>عذراً، تم تعطيل التسجيل في الموقع من قبل الإدارة. يرجى المحاولة لاحقاً أو التواصل مع الإدارة للحصول على مساعدة.</p>
                  </div>
                </div>
              </div>
            </div>
            <Link href="/login" className="inline-block text-[var(--primary-color)] hover:text-[var(--secondary-color)] font-medium">
              العودة إلى صفحة تسجيل الدخول
            </Link>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="px-6 py-8 space-y-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="user" className="block text-sm font-medium text-gray-700">اسم المستخدم</label>
              <input
                id="user"
                name="user"
                type="text"
                required
                value={formData.user}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              />
            </div>

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">الاسم الكامل</label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              />
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">رقم الهاتف</label>
              <input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">كلمة المرور</label>
              <input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              />
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">تأكيد كلمة المرور</label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                required
                value={formData.confirmPassword}
                onChange={handleChange}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--primary-color)]"
            >
              {isLoading ? 'جاري التسجيل...' : 'تسجيل'}
            </button>
          </div>

          <div className="text-center text-sm">
            <p className="text-gray-600">
              لديك حساب بالفعل؟{' '}
              <Link href="/login" className="font-medium text-[var(--primary-color)] hover:text-[var(--secondary-color)]">
                تسجيل الدخول
              </Link>
            </p>
          </div>
        </form>
        )}
      </div>
    </section>
  );
};

export default RegisterPage;
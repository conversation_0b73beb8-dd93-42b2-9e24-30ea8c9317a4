import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedBudgets() {
  try {
    console.log('بدء إنشاء بيانات الميزانية...');

    // التحقق من وجود خزينة
    let treasury = await prisma.treasury.findFirst();

    // إنشاء خزينة إذا لم تكن موجودة
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 100000,
          totalIncome: 150000,
          totalExpense: 50000,
        },
      });
      console.log('تم إنشاء خزينة جديدة');
    }

    // التحقق من وجود فئات المصروفات
    const categoriesCount = await prisma.expenseCategory.count();

    if (categoriesCount === 0) {
      console.log('لا توجد فئات مصروفات. يرجى تشغيل seed-expense-categories.ts أولاً.');
      return;
    }

    // الحصول على فئات المصروفات
    const categories = await prisma.expenseCategory.findMany({
      where: {
        isActive: true,
        parentId: null, // الفئات الرئيسية فقط
      },
    });

    // إنشاء ميزانية للعام الحالي
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1); // 1 يناير
    const endDate = new Date(currentYear, 11, 31); // 31 ديسمبر

    const budgetName = `ميزانية العام ${currentYear}`;

    // التحقق من وجود الميزانية
    const existingBudget = await prisma.budget.findFirst({
      where: {
        name: budgetName,
      },
    });

    if (existingBudget) {
      console.log(`الميزانية موجودة بالفعل: ${budgetName}`);
    } else {
      // إنشاء الميزانية
      const totalBudgetAmount = 500000; // مبلغ إجمالي افتراضي

      const budget = await prisma.budget.create({
        data: {
          name: budgetName,
          description: `الميزانية السنوية للعام ${currentYear}`,
          startDate,
          endDate,
          totalAmount: totalBudgetAmount,
          status: 'ACTIVE',
        },
      });

      console.log(`تم إنشاء ميزانية: ${budgetName}`);

      // توزيع المبلغ الإجمالي على الفئات
      const categoryAllocations = [
        { name: 'رواتب ومكافآت', percentage: 0.40 }, // 40% للرواتب
        { name: 'مستلزمات تعليمية', percentage: 0.15 }, // 15% للمستلزمات التعليمية
        { name: 'مرافق', percentage: 0.10 }, // 10% للمرافق
        { name: 'صيانة', percentage: 0.08 }, // 8% للصيانة
        { name: 'تكنولوجيا', percentage: 0.07 }, // 7% للتكنولوجيا
        { name: 'إيجارات', percentage: 0.05 }, // 5% للإيجارات
        { name: 'نقل', percentage: 0.05 }, // 5% للنقل
        { name: 'فعاليات', percentage: 0.04 }, // 4% للفعاليات
        { name: 'تسويق وإعلان', percentage: 0.03 }, // 3% للتسويق
        { name: 'مصروفات إدارية', percentage: 0.03 }, // 3% للمصروفات الإدارية
      ];

      // إنشاء بنود الميزانية
      for (const allocation of categoryAllocations) {
        const category = categories.find(cat => cat.name === allocation.name);

        if (category) {
          const amount = totalBudgetAmount * allocation.percentage;

          await prisma.budgetItem.create({
            data: {
              budgetId: budget.id,
              categoryId: category.id,
              amount,
              notes: `مخصص ${category.name} للعام ${currentYear}`,
            },
          });

          console.log(`تم إنشاء بند ميزانية: ${category.name} بمبلغ ${amount}`);
        }
      }
    }

    // إنشاء ميزانية للربع الأول من العام القادم
    const nextYear = currentYear + 1;
    const q1StartDate = new Date(nextYear, 0, 1); // 1 يناير
    const q1EndDate = new Date(nextYear, 2, 31); // 31 مارس

    const q1BudgetName = `ميزانية الربع الأول ${nextYear}`;

    // التحقق من وجود الميزانية
    const existingQ1Budget = await prisma.budget.findFirst({
      where: {
        name: q1BudgetName,
      },
    });

    if (existingQ1Budget) {
      console.log(`الميزانية موجودة بالفعل: ${q1BudgetName}`);
    } else {
      // إنشاء الميزانية
      const q1BudgetAmount = 150000; // مبلغ إجمالي افتراضي للربع

      const q1Budget = await prisma.budget.create({
        data: {
          name: q1BudgetName,
          description: `ميزانية الربع الأول من العام ${nextYear}`,
          startDate: q1StartDate,
          endDate: q1EndDate,
          totalAmount: q1BudgetAmount,
          status: 'DRAFT',
        },
      });

      console.log(`تم إنشاء ميزانية: ${q1BudgetName}`);

      // توزيع المبلغ الإجمالي على الفئات
      const categoryAllocations = [
        { name: 'رواتب ومكافآت', percentage: 0.40 },
        { name: 'مستلزمات تعليمية', percentage: 0.15 },
        { name: 'مرافق', percentage: 0.10 },
        { name: 'صيانة', percentage: 0.08 },
        { name: 'تكنولوجيا', percentage: 0.07 },
        { name: 'إيجارات', percentage: 0.05 },
        { name: 'نقل', percentage: 0.05 },
        { name: 'فعاليات', percentage: 0.04 },
        { name: 'تسويق وإعلان', percentage: 0.03 },
        { name: 'مصروفات إدارية', percentage: 0.03 },
      ];

      // إنشاء بنود الميزانية
      for (const allocation of categoryAllocations) {
        const category = categories.find(cat => cat.name === allocation.name);

        if (category) {
          const amount = q1BudgetAmount * allocation.percentage;

          await prisma.budgetItem.create({
            data: {
              budgetId: q1Budget.id,
              categoryId: category.id,
              amount,
              notes: `مخصص ${category.name} للربع الأول ${nextYear}`,
            },
          });

          console.log(`تم إنشاء بند ميزانية: ${category.name} بمبلغ ${amount}`);
        }
      }
    }

    console.log('تم إنشاء بيانات الميزانية بنجاح!');
  } catch (error) {
    console.error('حدث خطأ أثناء إنشاء بيانات الميزانية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// عند استدعاء الملف مباشرة، قم بتنفيذ الدالة
if (require.main === module) {
  seedBudgets();
}

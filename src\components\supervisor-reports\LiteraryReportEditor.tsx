'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { FileText, RefreshCw, Eye, Code, Type } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface LiteraryReportEditorProps {
  value: string;
  onChange: (value: string) => void;
  periodStart: Date;
  periodEnd: Date;
  isLoading?: boolean;
}

interface LiteraryData {
  generalStats: {
    totalStudents: number;
    totalTeachers: number;
    totalClasses: number;
    totalMemorizers: number;
    totalKhatmSessions: number;
    totalActivities: number;
  };
  studentsDetails: {
    total: number;
    byClass: Array<{
      classeId: string;
      className: string;
      count: number;
    }>;
  };
  teachersDetails: {
    total: number;
    bySpecialization: Array<{
      specialization: string;
      count: number;
    }>;
  };
  quranProgress: {
    averageProgress: number;
    totalMemorizers: number;
    completedKhatm: number;
  };
  activitiesDetails: {
    total: number;
    byType: Array<{
      type: string;
      count: number;
    }>;
  };
  trainingCourses: {
    total: number;
    participants: number;
  };
}

export default function LiteraryReportEditor({
  value,
  onChange,
  periodStart,
  periodEnd,
  isLoading = false
}: LiteraryReportEditorProps) {
  const [literaryData, setLiteraryData] = useState<LiteraryData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [viewMode, setViewMode] = useState<'edit' | 'preview'>('edit');
  const [templates, setTemplates] = useState<string[]>([]);

  // تحميل البيانات التلقائية
  useEffect(() => {
    loadLiteraryData();
  }, [periodStart, periodEnd]);

  const loadLiteraryData = async () => {
    setIsLoadingData(true);
    try {
      const response = await fetch(
        `/api/reports/literary?startDate=${periodStart.toISOString()}&endDate=${periodEnd.toISOString()}`
      );
      
      if (response.ok) {
        const result = await response.json();
        setLiteraryData(result.data);
        
        // إذا لم يكن هناك محتوى، قم بإنشاء محتوى تلقائي
        if (!value.trim()) {
          const autoContent = generateAutoContent(result.data);
          onChange(autoContent);
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات الأدبية:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  // إنشاء محتوى تلقائي من البيانات
  const generateAutoContent = (data: LiteraryData) => {
    return `
<div class="literary-report">
  <h2 style="color: #1e40af; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">📊 الإحصائيات العامة</h2>
  <div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 15px 0;">
    <ul style="list-style: none; padding: 0;">
      <li style="margin: 8px 0; padding: 5px; background: white; border-radius: 4px;">👥 إجمالي الطلاب: <strong>${data.generalStats?.totalStudents || 0}</strong></li>
      <li style="margin: 8px 0; padding: 5px; background: white; border-radius: 4px;">👨‍🏫 إجمالي المعلمين: <strong>${data.generalStats?.totalTeachers || 0}</strong></li>
      <li style="margin: 8px 0; padding: 5px; background: white; border-radius: 4px;">🏫 إجمالي الأقسام: <strong>${data.generalStats?.totalClasses || 0}</strong></li>
      <li style="margin: 8px 0; padding: 5px; background: white; border-radius: 4px;">📖 إجمالي الحفاظ: <strong>${data.generalStats?.totalMemorizers || 0}</strong></li>
      <li style="margin: 8px 0; padding: 5px; background: white; border-radius: 4px;">🎯 إجمالي الأنشطة: <strong>${data.generalStats?.totalActivities || 0}</strong></li>
    </ul>
  </div>

  <h2 style="color: #059669; border-bottom: 2px solid #10b981; padding-bottom: 10px;">👥 تفاصيل الطلاب</h2>
  <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 15px 0;">
    <p style="font-size: 16px; margin-bottom: 10px;">العدد الإجمالي للطلاب المسجلين: <strong>${data.studentsDetails?.total || 0}</strong></p>
    
    <h3 style="color: #047857; margin-top: 20px;">توزيع الطلاب حسب الأقسام:</h3>
    <ul style="margin: 10px 0;">
      ${data.studentsDetails?.byClass?.map(cls => 
        `<li style="margin: 5px 0;">• ${cls.className}: ${cls.count} طالب</li>`
      ).join('') || '<li>لا توجد بيانات متاحة</li>'}
    </ul>
  </div>

  <h2 style="color: #7c3aed; border-bottom: 2px solid #8b5cf6; padding-bottom: 10px;">📖 تقدم حفظ القرآن الكريم</h2>
  <div style="background: #faf5ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
    <p style="font-size: 16px; margin-bottom: 10px;">متوسط التقدم في الحفظ: <strong>${data.quranProgress?.averageProgress || 0}%</strong></p>
    <p style="font-size: 16px; margin-bottom: 10px;">عدد الحفاظ النشطين: <strong>${data.quranProgress?.totalMemorizers || 0}</strong></p>
    <p style="font-size: 16px; margin-bottom: 10px;">عدد الذين أتموا الختم: <strong>${data.quranProgress?.completedKhatm || 0}</strong></p>
  </div>

  <h2 style="color: #dc2626; border-bottom: 2px solid #ef4444; padding-bottom: 10px;">🎯 الأنشطة والفعاليات</h2>
  <div style="background: #fef2f2; padding: 15px; border-radius: 8px; margin: 15px 0;">
    <p style="font-size: 16px; margin-bottom: 10px;">عدد الأنشطة المنجزة: <strong>${data.activitiesDetails?.total || 0}</strong></p>
    
    <h3 style="color: #b91c1c; margin-top: 20px;">تصنيف الأنشطة:</h3>
    <ul style="margin: 10px 0;">
      ${data.activitiesDetails?.byType?.map(activity => 
        `<li style="margin: 5px 0;">• ${activity.type}: ${activity.count} نشاط</li>`
      ).join('') || '<li>لا توجد بيانات متاحة</li>'}
    </ul>
  </div>

  <h2 style="color: #ea580c; border-bottom: 2px solid #f97316; padding-bottom: 10px;">📚 الدورات التدريبية</h2>
  <div style="background: #fff7ed; padding: 15px; border-radius: 8px; margin: 15px 0;">
    <p style="font-size: 16px; margin-bottom: 10px;">عدد الدورات المقامة: <strong>${data.trainingCourses?.total || 0}</strong></p>
    <p style="font-size: 16px; margin-bottom: 10px;">عدد المشاركين: <strong>${data.trainingCourses?.participants || 0}</strong></p>
  </div>

  <h2 style="color: #0891b2; border-bottom: 2px solid #06b6d4; padding-bottom: 10px;">📝 ملاحظات وتوصيات</h2>
  <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
    <p style="font-size: 16px; line-height: 1.8;">
      بناءً على البيانات المعروضة أعلاه، نلاحظ التقدم المستمر في مختلف جوانب العمل التعليمي والتربوي. 
      ننصح بمواصلة الجهود المبذولة والعمل على تطوير البرامج التعليمية وزيادة الأنشطة التفاعلية.
    </p>
    <p style="font-size: 16px; line-height: 1.8; margin-top: 10px;">
      كما نوصي بتعزيز برامج حفظ القرآن الكريم وإقامة المزيد من الدورات التدريبية للمعلمين والطلاب.
    </p>
  </div>
</div>
    `.trim();
  };

  // إدراج قالب جاهز
  const insertTemplate = (templateType: string) => {
    let template = '';
    
    switch (templateType) {
      case 'introduction':
        template = `
<h2 style="color: #1e40af;">🌟 مقدمة التقرير</h2>
<p style="font-size: 16px; line-height: 1.8;">
  بسم الله الرحمن الرحيم، والحمد لله رب العالمين، والصلاة والسلام على أشرف المرسلين...
</p>
<p style="font-size: 16px; line-height: 1.8;">
  يسرنا أن نقدم لكم هذا التقرير الشامل عن أنشطة وإنجازات المدرسة القرآنية خلال الفترة من 
  ${format(periodStart, 'PPP', { locale: ar })} إلى ${format(periodEnd, 'PPP', { locale: ar })}.
</p>
        `;
        break;
        
      case 'achievements':
        template = `
<h2 style="color: #059669;">🏆 الإنجازات المحققة</h2>
<ul style="list-style: none; padding: 0;">
  <li style="margin: 10px 0; padding: 10px; background: #f0fdf4; border-radius: 5px;">✅ إنجاز رقم 1</li>
  <li style="margin: 10px 0; padding: 10px; background: #f0fdf4; border-radius: 5px;">✅ إنجاز رقم 2</li>
  <li style="margin: 10px 0; padding: 10px; background: #f0fdf4; border-radius: 5px;">✅ إنجاز رقم 3</li>
</ul>
        `;
        break;
        
      case 'challenges':
        template = `
<h2 style="color: #dc2626;">⚠️ التحديات والصعوبات</h2>
<div style="background: #fef2f2; padding: 15px; border-radius: 8px;">
  <ul style="margin: 0;">
    <li style="margin: 8px 0;">• تحدي رقم 1</li>
    <li style="margin: 8px 0;">• تحدي رقم 2</li>
    <li style="margin: 8px 0;">• تحدي رقم 3</li>
  </ul>
</div>
        `;
        break;
        
      case 'recommendations':
        template = `
<h2 style="color: #7c3aed;">💡 التوصيات والمقترحات</h2>
<div style="background: #faf5ff; padding: 15px; border-radius: 8px;">
  <ol style="margin: 0;">
    <li style="margin: 8px 0; line-height: 1.6;">توصية رقم 1</li>
    <li style="margin: 8px 0; line-height: 1.6;">توصية رقم 2</li>
    <li style="margin: 8px 0; line-height: 1.6;">توصية رقم 3</li>
  </ol>
</div>
        `;
        break;
    }
    
    onChange(value + '\n\n' + template);
  };

  // إدراج بيانات ديناميكية
  const insertDynamicData = (dataType: string) => {
    if (!literaryData) return;
    
    let dataHtml = '';
    
    switch (dataType) {
      case 'student-stats':
        dataHtml = `
<div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 10px 0;">
  <h3>📊 إحصائيات الطلاب</h3>
  <p>العدد الإجمالي: <strong>${literaryData.studentsDetails?.total || 0}</strong></p>
</div>
        `;
        break;
        
      case 'teacher-stats':
        dataHtml = `
<div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 10px 0;">
  <h3>👨‍🏫 إحصائيات المعلمين</h3>
  <p>العدد الإجمالي: <strong>${literaryData.teachersDetails?.total || 0}</strong></p>
</div>
        `;
        break;
        
      case 'quran-progress':
        dataHtml = `
<div style="background: #f8fafc; padding: 15px; border-radius: 8px; margin: 10px 0;">
  <h3>📖 تقدم حفظ القرآن</h3>
  <p>متوسط التقدم: <strong>${literaryData.quranProgress?.averageProgress || 0}%</strong></p>
  <p>عدد الحفاظ: <strong>${literaryData.quranProgress?.totalMemorizers || 0}</strong></p>
</div>
        `;
        break;
    }
    
    onChange(value + '\n\n' + dataHtml);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          📘 محرر التقرير الأدبي
        </CardTitle>
        <CardDescription>
          محرر نصوص متقدم مع إدراج تلقائي للبيانات من قاعدة البيانات
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* شريط الأدوات */}
        <div className="flex items-center gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={loadLiteraryData}
            disabled={isLoadingData}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoadingData ? 'animate-spin' : ''}`} />
            تحديث البيانات
          </Button>
          
          <Separator orientation="vertical" className="h-6" />
          
          <Button
            variant={viewMode === 'edit' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('edit')}
            className="flex items-center gap-2"
          >
            <Code className="h-4 w-4" />
            تحرير
          </Button>
          
          <Button
            variant={viewMode === 'preview' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('preview')}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            معاينة
          </Button>
          
          <Separator orientation="vertical" className="h-6" />
          
          <Badge variant="secondary" className="flex items-center gap-1">
            <Type className="h-3 w-3" />
            آخر تحديث: {format(new Date(), 'PPP', { locale: ar })}
          </Badge>
        </div>

        {/* قوالب جاهزة */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">قوالب جاهزة:</h4>
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('introduction')}
            >
              مقدمة
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('achievements')}
            >
              الإنجازات
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('challenges')}
            >
              التحديات
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertTemplate('recommendations')}
            >
              التوصيات
            </Button>
          </div>
        </div>

        {/* بيانات ديناميكية */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">إدراج بيانات ديناميكية:</h4>
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertDynamicData('student-stats')}
              disabled={!literaryData}
            >
              إحصائيات الطلاب
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertDynamicData('teacher-stats')}
              disabled={!literaryData}
            >
              إحصائيات المعلمين
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => insertDynamicData('quran-progress')}
              disabled={!literaryData}
            >
              تقدم القرآن
            </Button>
          </div>
        </div>

        <Separator />

        {/* المحرر */}
        {viewMode === 'edit' ? (
          <Textarea
            value={value}
            onChange={(e) => onChange(e.target.value)}
            placeholder="محتوى التقرير الأدبي..."
            rows={20}
            className="font-mono text-sm"
          />
        ) : (
          <div className="border rounded-lg p-4 min-h-[500px] bg-white">
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: value }}
            />
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="text-sm text-gray-500 space-y-1">
          <p>💡 نصائح:</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>استخدم الأزرار أعلاه لإدراج قوالب جاهزة وبيانات ديناميكية</li>
            <li>يمكنك استخدام HTML لتنسيق النص (العناوين، القوائم، الألوان)</li>
            <li>استخدم زر "معاينة" لرؤية النتيجة النهائية</li>
            <li>يتم تحديث البيانات تلقائياً عند تغيير الفترة الزمنية</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

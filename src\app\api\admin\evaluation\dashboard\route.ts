import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

export async function GET(request: NextRequest) {
  try {
    console.log('Dashboard API called');

    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      console.log('No token found');
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    console.log('User data:', userData);

    if (!userData || userData.role !== 'ADMIN') {
      console.log('User not admin or not found');
      return NextResponse.json(
        { message: "غير مصرح به - يجب أن تكون مشرفاً", success: false },
        { status: 401 }
      );
    }

    console.log('Fetching data from database...');

    // جلب بيانات الامتحانات
    const exams = await prisma.exam.findMany({
      include: {
        exam_points: {
          include: {
            student: {
              include: {
                classe: true
              }
            }
          }
        },
        subject: true,
        examType: true
      }
    });
    console.log('Exams found:', exams.length);

    // جلب جميع الطلاب
    const totalStudents = await prisma.student.count();
    console.log('Total students:', totalStudents);

    // جلب جميع نقاط الامتحانات
    const allExamPoints = await prisma.exam_points.findMany({
      include: {
        student: {
          include: {
            classe: true
          }
        },
        exam: true
      }
    });
    console.log('Exam points found:', allExamPoints.length);

    // جلب بيانات المكافآت
    const rewards = await prisma.reward.findMany({
      include: {
        _count: {
          select: {
            studentRewards: true
          }
        }
      }
    });

    const studentRewards = await prisma.studentReward.findMany({
      include: {
        reward: true,
        student: true
      }
    });

    // حساب إحصائيات الامتحانات
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();

    const examSummary = {
      totalExams: exams.length,
      pendingExams: exams.filter(exam => {
        const examMonth = parseInt(exam.month.split('-')[1]);
        const examYear = parseInt(exam.month.split('-')[0]);
        return examYear >= currentYear && examMonth >= currentMonth && exam.exam_points.length === 0;
      }).length,
      completedExams: exams.filter(exam => exam.exam_points.length > 0).length,
      upcomingExams: exams.filter(exam => {
        const examMonth = parseInt(exam.month.split('-')[1]);
        const examYear = parseInt(exam.month.split('-')[0]);
        return examYear >= currentYear && examMonth >= currentMonth;
      }).length,
      examsByType: Object.entries(
        exams.reduce((acc, exam) => {
          const type = exam.evaluationType;
          acc[type] = (acc[type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      ).map(([type, count]) => ({ type, count }))
    };

    // حساب إحصائيات الطلاب
    let averageScore = 0;
    let topPerformers = 0;
    let needsImprovement = 0;

    console.log('Calculating student statistics...');

    if (allExamPoints.length > 0) {
      // حساب متوسط الدرجات
      const totalGrades = allExamPoints.reduce((sum, point) => sum + Number(point.grade), 0);
      averageScore = totalGrades / allExamPoints.length;
      console.log('Average score calculated:', averageScore);

      // تجميع درجات الطلاب
      const studentGrades: Record<number, number[]> = {};
      allExamPoints.forEach(point => {
        if (!studentGrades[point.studentId]) {
          studentGrades[point.studentId] = [];
        }
        studentGrades[point.studentId].push(Number(point.grade));
      });

      // حساب متوسط كل طالب
      const studentAverages = Object.entries(studentGrades).map(([studentId, grades]) => ({
        studentId: parseInt(studentId),
        average: grades.reduce((sum, grade) => sum + grade, 0) / grades.length
      }));

      // تصنيف الطلاب
      topPerformers = studentAverages.filter(student => student.average >= 8).length;
      needsImprovement = studentAverages.filter(student => student.average < 6).length;

      console.log('Student classification:', { topPerformers, needsImprovement });
    } else {
      console.log('No exam points found for student statistics');
    }

    const studentSummary = {
      totalStudents,
      topPerformers,
      needsImprovement,
      averageScore: Math.round(averageScore * 100) / 100
    };

    // حساب إحصائيات المكافآت
    console.log('Calculating reward statistics...');
    console.log('Rewards found:', rewards.length);
    console.log('Student rewards found:', studentRewards.length);

    const totalRewards = rewards.length;
    const rewardsAwarded = studentRewards.length;

    // العثور على أكثر مكافأة منح
    const rewardCounts = studentRewards.reduce((acc, sr) => {
      const rewardName = sr.reward.name;
      acc[rewardName] = (acc[rewardName] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topReward = Object.entries(rewardCounts).reduce(
      (max, [name, count]) => count > max.count ? { name, count } : max,
      { name: '', count: 0 }
    );

    console.log('Top reward:', topReward);

    const rewardSummary = {
      totalRewards,
      rewardsAwarded,
      topReward
    };

    // جلب الأنشطة الأخيرة
    const recentActivities = await prisma.exam_points.findMany({
      take: 10,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        student: true,
        exam: true
      }
    });

    const formattedActivities = recentActivities.map(activity => ({
      id: activity.id,
      type: 'exam_result',
      description: `تم تسجيل نقاط امتحان ${activity.exam.evaluationType} للطالب ${activity.student.name}`,
      date: activity.createdAt.toISOString(),
      user: activity.student.name,
      grade: activity.grade
    }));

    console.log('Final results:', {
      examSummary,
      studentSummary,
      rewardSummary,
      recentActivitiesCount: formattedActivities.length
    });

    return NextResponse.json({
      success: true,
      data: {
        examSummary,
        studentSummary,
        rewardSummary,
        recentActivities: formattedActivities
      }
    });

  } catch (error) {
    console.error('Error fetching evaluation dashboard data:', error);
    return NextResponse.json(
      { 
        message: "حدث خطأ أثناء جلب بيانات لوحة التحكم", 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

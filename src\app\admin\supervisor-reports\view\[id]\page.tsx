'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, FileText, Printer, Edit, Loader2, Download } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface FinancialRow {
  id: string;
  category: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
}

interface SupervisorReport {
  id: number;
  title: string;
  description?: string;
  periodStart: string;
  periodEnd: string;
  literaryContent?: string;
  financialData?: FinancialRow[];
  status: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function ViewSupervisorReportPage() {
  const router = useRouter();
  const params = useParams();
  const reportId = params.id as string;
  
  const [report, setReport] = useState<SupervisorReport | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (reportId) {
      fetchReport();
    }
  }, [reportId]);

  const fetchReport = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/supervisor-reports/${reportId}`);
      if (response.ok) {
        const result = await response.json();
        setReport(result.data);
      } else {
        console.error('فشل في تحميل التقرير');
        router.push('/admin/supervisor-reports');
      }
    } catch (error) {
      console.error('خطأ في تحميل التقرير:', error);
      router.push('/admin/supervisor-reports');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleEdit = () => {
    router.push(`/admin/supervisor-reports/edit/${reportId}`);
  };

  const handleExport = async () => {
    try {
      const response = await fetch(`/api/supervisor-reports/${reportId}/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format: 'html' }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `تقرير_${report?.title}_${new Date().toISOString().split('T')[0]}.html`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
    }
  };

  // حساب المجاميع المالية
  const calculateFinancialTotals = () => {
    if (!report?.financialData) return { totalIncome: 0, totalExpenses: 0, balance: 0 };
    
    const totalIncome = report.financialData
      .filter(row => row.type === 'income')
      .reduce((sum, row) => sum + row.amount, 0);
      
    const totalExpenses = report.financialData
      .filter(row => row.type === 'expense')
      .reduce((sum, row) => sum + row.amount, 0);
      
    const balance = totalIncome - totalExpenses;
    
    return { totalIncome, totalExpenses, balance };
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري تحميل التقرير...</span>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="text-center py-12">
          <p className="text-gray-500">التقرير غير موجود</p>
          <Button onClick={() => router.push('/admin/supervisor-reports')} className="mt-4">
            العودة للقائمة
          </Button>
        </div>
      </div>
    );
  }

  const { totalIncome, totalExpenses, balance } = calculateFinancialTotals();

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* الرأس */}
      <div className="flex items-center justify-between print:hidden">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <FileText className="h-8 w-8 text-primary" />
            عرض التقرير الموحد
          </h1>
          <p className="text-gray-600 mt-1">
            {report.title}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleEdit}
            className="flex items-center gap-2"
          >
            <Edit className="h-4 w-4" />
            تعديل
          </Button>
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            طباعة
          </Button>
          <Button
            variant="outline"
            onClick={handleExport}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            تصدير
          </Button>
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            رجوع
          </Button>
        </div>
      </div>

      {/* معلومات التقرير */}
      <Card className="print:shadow-none print:border-0">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">{report.title}</CardTitle>
          <CardDescription className="text-lg">
            الفترة: من {format(new Date(report.periodStart), 'PPP', { locale: ar })} 
            إلى {format(new Date(report.periodEnd), 'PPP', { locale: ar })}
          </CardDescription>
          <div className="flex items-center justify-center gap-4 mt-4 print:hidden">
            <Badge variant={report.status === 'PUBLISHED' ? 'default' : 'secondary'}>
              {report.status === 'PUBLISHED' ? 'منشور' : 'مسودة'}
            </Badge>
            <span className="text-sm text-gray-500">
              أنشأه: {report.createdBy}
            </span>
            <span className="text-sm text-gray-500">
              تاريخ الإنشاء: {format(new Date(report.createdAt), 'PPP', { locale: ar })}
            </span>
          </div>
          {report.description && (
            <p className="text-gray-600 mt-2">{report.description}</p>
          )}
        </CardHeader>
      </Card>

      {/* التقرير الأدبي */}
      {report.literaryContent && (
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              📘 التقرير الأدبي
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: report.literaryContent }}
            />
          </CardContent>
        </Card>
      )}

      {/* التقرير المالي */}
      {report.financialData && report.financialData.length > 0 && (
        <Card className="print:shadow-none print:border-0">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              💰 التقرير المالي
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* جدول المداخيل */}
              <div>
                <h3 className="text-lg font-semibold text-green-700 mb-4">📈 المداخيل</h3>
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-green-50">
                      <tr>
                        <th className="p-3 text-right border-b">البيان</th>
                        <th className="p-3 text-right border-b">المبلغ (دج)</th>
                      </tr>
                    </thead>
                    <tbody>
                      {report.financialData.filter(row => row.type === 'income').map((row) => (
                        <tr key={row.id} className="border-b">
                          <td className="p-3">{row.description}</td>
                          <td className="p-3 font-mono">{row.amount.toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-green-100">
                      <tr>
                        <td className="p-3 font-bold">إجمالي المداخيل</td>
                        <td className="p-3 font-bold font-mono">{totalIncome.toLocaleString()}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              <Separator />

              {/* جدول المصاريف */}
              <div>
                <h3 className="text-lg font-semibold text-red-700 mb-4">📉 المصاريف</h3>
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-red-50">
                      <tr>
                        <th className="p-3 text-right border-b">البيان</th>
                        <th className="p-3 text-right border-b">المبلغ (دج)</th>
                      </tr>
                    </thead>
                    <tbody>
                      {report.financialData.filter(row => row.type === 'expense').map((row) => (
                        <tr key={row.id} className="border-b">
                          <td className="p-3">{row.description}</td>
                          <td className="p-3 font-mono">{row.amount.toLocaleString()}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-red-100">
                      <tr>
                        <td className="p-3 font-bold">إجمالي المصاريف</td>
                        <td className="p-3 font-bold font-mono">{totalExpenses.toLocaleString()}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>

              {/* الرصيد النهائي */}
              <div className={`p-4 rounded-lg border-2 ${balance >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold">الرصيد النهائي:</span>
                  <span className={`text-xl font-bold font-mono ${balance >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                    {balance.toLocaleString()} دج
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* تذييل للطباعة */}
      <div className="hidden print:block text-center text-sm text-gray-500 mt-8">
        <p>تم إنشاء هذا التقرير في: {format(new Date(), 'PPP', { locale: ar })}</p>
        <p>نظام إدارة المدارس القرآنية</p>
      </div>
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/budgets/[id]/reviews - جلب سجل مراجعات الميزانية
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const budgetId = parseInt(params.id);

    if (isNaN(budgetId)) {
      return NextResponse.json(
        { error: 'معرف الميزانية غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من وجود الميزانية
    const budget = await prisma.budget.findUnique({
      where: { id: budgetId }
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // جلب سجل المراجعات
    const reviews = await prisma.budgetReview.findMany({
      where: { budgetId },
      orderBy: { date: 'desc' }
    });

    return NextResponse.json({
      success: true,
      reviews
    });
  } catch (error) {
    console.error('خطأ في جلب سجل المراجعات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب سجل المراجعات' },
      { status: 500 }
    );
  }
}

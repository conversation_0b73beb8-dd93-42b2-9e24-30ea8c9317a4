'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useRouter, useSearchParams } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Trash2 } from 'lucide-react';
import {
  getGradeLevelByScore,
  calculatePercentage,
  getPassStatus,
  validateGrade,
  DEFAULT_SETTINGS
} from '@/lib/grading-system';

export default function TeacherScoringPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const examId = searchParams?.get('examId') ?? null;
  const classId = searchParams?.get('classId') ?? null;
  const studentId = searchParams?.get('studentId') ?? null;

  console.log('Received examId:', examId, 'classId:', classId, 'and studentId:', studentId);

  const [loadingSave, setLoadingSave] = useState(false);
  const [loadingClasses, setLoadingClasses] = useState(false);
  const [loadingSurahs, setLoadingSurahs] = useState(false);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [loadingExams, setLoadingExams] = useState(false);
  const [loadingTeacher, setLoadingTeacher] = useState(false);
  const [teacherId, setTeacherId] = useState<number | null>(null);
  const [errors, setErrors] = useState({
    classes: '',
    surahs: '',
    students: '',
    exams: '',
    save: '',
    teacher: ''
  });
  const [success, setSuccess] = useState('');

  interface Class {
    id: string;
    name: string;
  }

  interface ClassSubject {
    id: string;
    classeId: string;
    teacherSubjectId: string;
    classe: {
      id: string;
      name: string;
    };
    teacherSubject: {
      id: string;
      teacher: {
        id: string;
        name: string;
      };
      subject: {
        id: string;
        name: string;
      };
    };
  }

  interface Surah {
    id: string;
    name: string;
    number: number;
    totalAyahs?: number;
  }

  interface Student {
    id: string;
    name: string;
  }

  interface Exam {
    id: string;
    evaluationType: string;
    month: string;
    description?: string;
    requiresSurah: boolean;
    subjectId?: number;
    subject?: {
      id: number;
      name: string;
    };
  }

  const [classes, setClasses] = useState<Class[]>([]);
  console.log('Initial classes state:', classes);
  const [classSubjects, setClassSubjects] = useState<ClassSubject[]>([]);
  const [surahs, setSurahs] = useState<Surah[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [exams, setExams] = useState<Exam[]>([]);
  const [selectedClass, setSelectedClass] = useState(classId || 'no-selection');
  const [selectedExam, setSelectedExam] = useState(examId || 'no-selection');
  const [selectedSurah, setSelectedSurah] = useState('no-selection');
  const [startVerse, setStartVerse] = useState('');
  const [endVerse, setEndVerse] = useState('');
  const [grades, setGrades] = useState<Grade[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [selectedExamRequiresSurah, setSelectedExamRequiresSurah] = useState(false);
  const [existingGrades, setExistingGrades] = useState<{[key: string]: any}>({});
  const [isEditMode, setIsEditMode] = useState(false);
  const [loadingDelete, setLoadingDelete] = useState<{[key: string]: boolean}>({});
  const [isAutoSetupLoading, setIsAutoSetupLoading] = useState(false);

  interface Grade {
    studentId: string;
    examId: string | null;
    classSubjectId: string;
    grade: string;
    note: string;
    month: string;
  }

  useEffect(() => {
    console.log('Fetching initial data with examId:', examId, 'classId:', classId, 'and studentId:', studentId);
    const fetchData = async () => {
      // Fetch teacher ID
      setLoadingTeacher(true);
      try {
        const teacherResponse = await fetch('/api/teacher-id');
        if (!teacherResponse.ok) {
          const errorData = await teacherResponse.json();
          throw new Error(errorData?.message || 'فشل في جلب معرف المعلم');
        }
        const teacherData = await teacherResponse.json();

        if (teacherData.success && teacherData.teacherId) {
          console.log('Teacher ID retrieved:', teacherData.teacherId);
          setTeacherId(teacherData.teacherId);
        } else {
          throw new Error('لم يتم العثور على معرف المعلم');
        }
      } catch (err) {
        console.error('Error fetching teacher ID:', err);
        setErrors(prev => ({ ...prev, teacher: err instanceof Error ? err.message : 'فشل في جلب معرف المعلم' }));
      } finally {
        setLoadingTeacher(false);
      }

      // Fetch classes
      setLoadingClasses(true);
      try {
        // جلب الأقسام بدون بيانات إضافية غير ضرورية
        const classesResponse = await fetch('/api/classes?includeStudents=false&includeSubjects=false');
        if (!classesResponse.ok) {
          const errorData = await classesResponse.json();
          throw new Error(errorData?.error || 'فشل في جلب قائمة الأقسام');
        }
        const classesData = await classesResponse.json();

        console.log('Classes data received:', classesData);

        // التأكد من أن API يرجع البيانات في كائن { classes: [...] }
        if (classesData && classesData.classes && Array.isArray(classesData.classes)) {
          console.log('Setting classes from classesData.classes:', classesData.classes);
          setClasses(classesData.classes);
        } else if (Array.isArray(classesData)) {
          console.log('Setting classes from array classesData:', classesData);
          setClasses(classesData);
        } else if (classesData && Array.isArray(classesData.data)) {
          console.log('Setting classes from classesData.data:', classesData.data);
          setClasses(classesData.data);
        } else {
          console.error('Unexpected classes data format:', classesData);
          setClasses([]);
          setErrors(prev => ({ ...prev, classes: 'تنسيق بيانات الأقسام غير متوقع' }));
        }

        // جلب علاقات القسم بالمادة
        const classSubjectsResponse = await fetch('/api/class-subjects');
        if (!classSubjectsResponse.ok) {
          const errorData = await classSubjectsResponse.json();
          throw new Error(errorData?.error || 'فشل في جلب علاقات القسم بالمادة');
        }
        const classSubjectsData = await classSubjectsResponse.json();
        console.log('Class subjects data received:', classSubjectsData);

        if (classSubjectsData && Array.isArray(classSubjectsData.data)) {
          console.log('Setting class subjects:', classSubjectsData.data.length);
          setClassSubjects(classSubjectsData.data);

          // التحقق من وجود علاقة للقسم المحدد
          if (classId) {
            const hasRelation = classSubjectsData.data.some((cs: { classeId: string }) => cs.classeId === classId);
            if (!hasRelation) {
              console.warn('No class subject relation found for selected class:', classId);
              setErrors(prev => ({ ...prev, classes: 'لم يتم العثور على علاقة بين القسم المحدد والمادة' }));
            }
          }
        } else {
          console.error('Invalid class subjects data format:', classSubjectsData);
          setErrors(prev => ({ ...prev, classes: 'تنسيق بيانات علاقات القسم بالمادة غير صالح' }));
        }
      } catch (err) {
        console.error('Error fetching classes or class subjects:', err);
        setErrors(prev => ({ ...prev, classes: err instanceof Error ? err.message : 'فشل في جلب قائمة الأقسام أو علاقات القسم بالمادة' }));
      } finally {
        setLoadingClasses(false);
      }

      // Fetch surahs
      setLoadingSurahs(true);
      try {
        const surahsResponse = await fetch('/api/surahs');
        if (!surahsResponse.ok) {
          const errorData = await surahsResponse.json();
          throw new Error(errorData?.error || 'فشل في جلب قائمة السور');
        }
        const surahsData = await surahsResponse.json();
        console.log('Surahs data received:', surahsData);
        setSurahs(surahsData || []);
      } catch (err) {
        console.error('Error fetching surahs:', err);
        setErrors(prev => ({ ...prev, surahs: err instanceof Error ? err.message : 'فشل في جلب قائمة السور' }));
      } finally {
        setLoadingSurahs(false);
      }

      // Fetch exams
      setLoadingExams(true);
      try {
        // جلب جميع الامتحانات بدون تصفية حسب المعلم
        const examsResponse = await fetch(`/api/evaluation/exams`);
        if (!examsResponse.ok) {
          const errorData = await examsResponse.json();
          throw new Error(errorData?.error || 'فشل في جلب الامتحانات');
        }
        const examsData = await examsResponse.json();
        console.log('Exams data received:', examsData);

        if (examsData && examsData.data && Array.isArray(examsData.data)) {
          // طباعة معلومات المادة الدراسية لكل امتحان
          examsData.data.forEach((exam: { id: string, subjectId?: number, evaluationType: string, description?: string }) => {
            console.log(`Exam ID: ${exam.id}, Subject ID: ${exam.subjectId}, Type: ${exam.evaluationType}, Description: ${exam.description}`);
          });

          setExams(examsData.data);

          // لا نقوم بالتحقق من وجود الامتحان المحدد لتجنب رسائل الخطأ
          // نسمح باختيار أي امتحان
        } else {
          console.error('Invalid exams data format:', examsData);
          setErrors(prev => ({ ...prev, exams: 'لا توجد امتحانات متاحة' }));
        }
      } catch (err) {
        console.error('Error fetching exams:', err);
        setErrors(prev => ({ ...prev, exams: err instanceof Error ? err.message : 'فشل في جلب قائمة الامتحانات' }));
      } finally {
        setLoadingExams(false);
      }
    };

    fetchData();
  }, [examId, classId, studentId, teacherId]);

  // تحديث الطلاب عند تغيير الفصل
  useEffect(() => {
    console.log('Selected class changed to:', selectedClass, 'with studentId:', studentId);
    const fetchStudents = async () => {
      if (selectedClass && selectedClass !== 'no-selection') {
        setLoadingStudents(true);
        setErrors(prev => ({ ...prev, students: '' }));
        try {
          // التحقق من وجود علاقة بين القسم والمادة
          // نستخدم أول علاقة قسم بمادة متاحة للقسم المحدد
          let classSubject = classSubjects.find(cs => cs.classeId === selectedClass);

          // إذا لم نجد علاقة، نستخدم أول علاقة قسم بمادة متاحة
          if (!classSubject && classSubjects.length > 0) {
            console.log('No class subject found for class, using first available class subject');
            classSubject = classSubjects[0];
          }

          if (!classSubject) {
            console.error('No class subject found for class:', selectedClass);

            // إذا لم يتم العثور على علاقة بين القسم والمادة، نقوم بإنشاء رسالة خطأ أكثر تفصيلاً
            let errorMessage = 'لم يتم العثور على علاقة بين القسم والمادة. ';

            if (selectedExam && selectedExam !== 'no-selection') {
              const selectedExamObj = exams.find(e => e.id === selectedExam);
              if (selectedExamObj && selectedExamObj.subjectId) {
                const subjectName = selectedExamObj.subject ? selectedExamObj.subject.name : `المادة (${selectedExamObj.subjectId})`;
                errorMessage += `يرجى التأكد من وجود علاقة بين القسم "${selectedClass}" والمادة "${subjectName}". `;
                errorMessage += 'يمكنك إضافة هذه العلاقة من خلال صفحة "علاقات الأقسام بالمواد" في لوحة المشرف.';
              } else {
                errorMessage += 'يرجى التأكد من أن الامتحان المحدد مرتبط بمادة دراسية.';
              }
            } else {
              errorMessage += 'يرجى اختيار امتحان مرتبط بمادة دراسية.';
            }

            setErrors(prev => ({ ...prev, students: errorMessage }));
            setLoadingStudents(false);
            return;
          }

          // Fetch students in the selected class
          console.log('Fetching students for class:', selectedClass);
          const response = await fetch(`/api/classes/${selectedClass}?includeStudents=true`);
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData?.error || 'فشل في جلب قائمة الطلاب');
          }
          const classData = await response.json();
          console.log('Received class data:', classData);

          // استخراج قائمة الطلاب من بيانات القسم
          console.log('Checking students data in class:', classData);

          if (classData && classData.students && Array.isArray(classData.students)) {
            console.log('Found students array with length:', classData.students.length);

            // إذا تم تحديد معرف طالب، نقوم بتصفية القائمة لإظهار هذا الطالب فقط
            if (studentId) {
              const filteredStudents = classData.students.filter((student: { id: string | number }) => student.id.toString() === studentId);
              console.log('Filtered students for studentId:', studentId, filteredStudents);
              setStudents(filteredStudents);
              if (filteredStudents.length === 0) {
                setErrors(prev => ({ ...prev, students: 'لم يتم العثور على الطالب المحدد في هذا القسم' }));
              }
            } else {
              // إذا لم يتم تحديد طالب، نعرض جميع الطلاب في القسم
              console.log('Setting all students in class:', classData.students.length);
              setStudents(classData.students);
              if (classData.students.length === 0) {
                setErrors(prev => ({ ...prev, students: 'لا يوجد طلاب في هذا القسم. يرجى إضافة طلاب إلى هذا القسم أولاً.' }));
              }
            }
          } else {
            console.error('Invalid class data format or no students array:', classData);

            // محاولة استخراج الطلاب من مكان آخر في البيانات
            if (classData && typeof classData === 'object') {
              // البحث عن أي مصفوفة في البيانات قد تحتوي على الطلاب
              for (const key in classData) {
                if (Array.isArray(classData[key]) && classData[key].length > 0 && classData[key][0] && 'name' in classData[key][0]) {
                  console.log('Found potential students array in key:', key, classData[key]);
                  setStudents(classData[key]);
                  return;
                }
              }
            }

            setStudents([]);
            setErrors(prev => ({ ...prev, students: 'لا يوجد طلاب في هذا القسم. يرجى إضافة طلاب إلى هذا القسم أولاً.' }));
          }
        } catch (err) {
          console.error('Error fetching students:', err);
          setErrors(prev => ({ ...prev, students: err instanceof Error ? err.message : 'فشل في جلب قائمة الطلاب' }));
        } finally {
          setLoadingStudents(false);
        }
      }
    };

    fetchStudents();
  }, [selectedClass, studentId, classSubjects, selectedExam, exams]);

  // معلومات الامتحان المحدد
  const selectedExamInfo = exams.find(e => e.id === selectedExam);
  const maxPoints = selectedExamInfo?.maxPoints || DEFAULT_SETTINGS.maxPoints;

  // تحديث معلومات الامتحان عند تغييره
  useEffect(() => {
    if (selectedExam) {
      const exam = exams.find(e => e.id === selectedExam);
      if (exam) {
        setSelectedExamRequiresSurah(exam.requiresSurah);

        // إذا كان الامتحان يتطلب سورة، نقوم بإعادة تعيين السورة والآيات
        if (!exam.requiresSurah) {
          setSelectedSurah('');
          setStartVerse('');
          setEndVerse('');
        }
      }
    }
  }, [selectedExam, exams]);

  // جلب الدرجات الموجودة إذا تم تحديد الامتحان والفصل
  useEffect(() => {
    console.log('Fetching existing grades for examId:', selectedExam, 'classId:', selectedClass, 'and studentId:', studentId);
    const fetchExistingGrades = async () => {
      if (selectedExam && selectedExam !== 'no-selection' &&
          selectedClass && selectedClass !== 'no-selection' &&
          studentId) {
        try {
          // بناء URL لجلب الدرجات
          let url = `/api/exam-points?examId=${selectedExam}`;

          // إضافة معرف القسم إذا تم تحديده
          if (selectedClass) {
            // نستخدم أول علاقة قسم بمادة متاحة للقسم المحدد
            let classSubject = classSubjects.find(cs => cs.classeId === selectedClass);

            // إذا لم نجد علاقة، نستخدم أول علاقة قسم بمادة متاحة
            if (!classSubject && classSubjects.length > 0) {
              console.log('No class subject found for class, using first available class subject');
              classSubject = classSubjects[0];
            }

            if (classSubject) {
              url += `&classSubjectId=${classSubject.id}`;
              console.log('Found classSubjectId:', classSubject.id, 'for class:', selectedClass);
            } else {
              console.error('No class subject found for class:', selectedClass);
              setErrors(prev => ({ ...prev, save: 'لم يتم العثور على علاقة بين القسم والمادة. يرجى التحقق من إعدادات القسم والمادة.' }));
              return; // لا تستمر في جلب الدرجات إذا لم يتم العثور على علاقة القسم بالمادة
            }
          }

          // إضافة معرف الطالب إذا تم تحديده
          if (studentId) {
            url += `&studentId=${studentId}`;
          }

          console.log('Fetching grades with URL:', url);
          const response = await fetch(url);
          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData?.error || errorData?.message || 'فشل في جلب الدرجات');
          }

          const data = await response.json();
          console.log('Received grades data:', data);

          if (data.success && Array.isArray(data.data)) {
            // تحويل البيانات إلى الشكل المطلوب
            interface ExamPoint {
              id: number;
              examId: number;
              studentId: number;
              classSubjectId: number;
              grade: number;
              note?: string;
              surahId?: number;
              startVerse?: number;
              endVerse?: number;
            }

            const formattedGrades = data.data.map((point: ExamPoint) => ({
              studentId: point.studentId.toString(),
              examId: point.examId.toString(),
              classSubjectId: point.classSubjectId.toString(),
              grade: point.grade.toString(),
              note: point.note || '',
              month: new Date().toISOString().slice(0, 7)
            }));

            console.log('Formatted grades:', formattedGrades);
            setGrades(formattedGrades);

            // حفظ البيانات الأصلية للنقاط الموجودة لاستخدامها في التحديث
            const existingGradesMap: {[key: string]: any} = {};
            data.data.forEach((point: ExamPoint) => {
              existingGradesMap[point.studentId.toString()] = point;
            });
            setExistingGrades(existingGradesMap);

            // تحديد أن هذا وضع تعديل إذا كانت هناك نقاط موجودة
            setIsEditMode(data.data.length > 0);

            // إذا كان الامتحان يتطلب سورة وتم تحديد سورة في النتائج
            if (data.data.length > 0 && data.data[0].surahId) {
              setSelectedSurah(data.data[0].surahId.toString());
              setStartVerse(data.data[0].startVerse?.toString() || '');
              setEndVerse(data.data[0].endVerse?.toString() || '');
            }
          }
        } catch (err) {
          console.error('Error fetching existing grades:', err);
          setErrors(prev => ({ ...prev, save: err instanceof Error ? err.message : 'فشل في جلب الدرجات' }));
        }
      }
    };

    fetchExistingGrades();
  }, [selectedExam, selectedClass, classSubjects, studentId]);

  // تحذير عند مغادرة الصفحة مع وجود تغييرات غير محفوظة
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        const message = 'لديك تغييرات غير محفوظة، هل تريد المغادرة؟';
        event.preventDefault();
        // استخدام returnValue للتوافق مع المتصفحات القديمة
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (event as any).returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const handleGradeChange = (studentId: string, field: 'grade' | 'note', value: string) => {
    if (field === 'grade') {
      const numericValue = Number(value);
      const validation = validateGrade(numericValue, maxPoints);
      if (!validation.isValid) {
        toast.error(validation.error);
        return;
      }
    }

    setGrades((prevGrades: Grade[]) => {
      const studentIndex = prevGrades.findIndex(g => g.studentId === studentId);

      if (studentIndex >= 0) {
        const updatedGrades = [...prevGrades];
        updatedGrades[studentIndex] = {
          ...updatedGrades[studentIndex],
          [field]: value
        };
        setHasUnsavedChanges(true);
        return updatedGrades;
      } else {
        // تحديد classSubjectId المناسب
        if (!selectedClass || selectedClass === 'no-selection') {
          console.error('No class selected or invalid class selection:', selectedClass);
          setErrors(prev => ({ ...prev, save: 'يرجى اختيار القسم أولاً' }));
          return prevGrades; // لا تقم بإضافة درجة جديدة إذا لم يتم اختيار قسم
        }

        if (!selectedExam || selectedExam === 'no-selection') {
          console.error('No exam selected or invalid exam selection:', selectedExam);
          setErrors(prev => ({ ...prev, save: 'يرجى اختيار الامتحان أولاً' }));
          return prevGrades; // لا تقم بإضافة درجة جديدة إذا لم يتم اختيار امتحان
        }

        // نستخدم أول علاقة قسم بمادة متاحة للقسم المحدد
        let classSubject = classSubjects.find(cs => cs.classeId === selectedClass);

        // إذا لم نجد علاقة، نستخدم أول علاقة قسم بمادة متاحة
        if (!classSubject && classSubjects.length > 0) {
          console.log('No class subject found for class, using first available class subject');
          classSubject = classSubjects[0];
        }

        if (!classSubject) {
          console.error('No class subject found for class:', selectedClass);
          // عرض رسالة خطأ للمستخدم
          setErrors(prev => ({ ...prev, save: 'لم يتم العثور على علاقة بين القسم والمادة. يرجى التحقق من إعدادات القسم والمادة.' }));
          return prevGrades; // لا تقم بإضافة درجة جديدة إذا لم يتم العثور على علاقة القسم بالمادة
        }

        const classSubjectId = classSubject.id;
        console.log('Using classSubjectId:', classSubjectId, 'for class:', selectedClass, 'in handleGradeChange');

        const newGrade: Grade = {
          studentId,
          examId: selectedExam,
          classSubjectId,
          grade: field === 'grade' ? value : '',
          note: field === 'note' ? value : '',
          month: new Date().toISOString().slice(0, 7)
        };
        setHasUnsavedChanges(true);
        return [...prevGrades, newGrade];
      }
    });
  };

  const handleSubmit = async () => {
    // إعادة تعيين رسائل الخطأ
    setErrors(prev => ({ ...prev, save: '' }));
    setSuccess('');

    if (!selectedClass || selectedClass === 'no-selection' || !selectedExam || selectedExam === 'no-selection') {
      setErrors(prev => ({ ...prev, save: 'يرجى اختيار القسم والامتحان' }));
      return;
    }

    if (selectedExamRequiresSurah && !selectedSurah) {
      setErrors(prev => ({ ...prev, save: 'يرجى اختيار السورة' }));
      return;
    }

    if (students.length === 0) {
      setErrors(prev => ({ ...prev, save: 'لا يوجد طلاب لإدخال نقاط لهم' }));
      return;
    }

    if (!teacherId) {
      setErrors(prev => ({ ...prev, save: 'لم يتم العثور على معرف المعلم، يرجى تحديث الصفحة والمحاولة مرة أخرى' }));
      return;
    }

    // نستخدم أول علاقة قسم بمادة متاحة للقسم المحدد
    let classSubject = classSubjects.find(cs => cs.classeId === selectedClass);

    // إذا لم نجد علاقة، نستخدم أول علاقة قسم بمادة متاحة
    if (!classSubject && classSubjects.length > 0) {
      console.log('No class subject found for class, using first available class subject');
      classSubject = classSubjects[0];
    }

    if (!classSubject) {
      setErrors(prev => ({ ...prev, save: 'لم يتم العثور على علاقة بين القسم والمادة. يرجى التحقق من إعدادات القسم والمادة.' }));
      return;
    }
    const classSubjectId = classSubject.id;

    console.log('Using classSubjectId:', classSubjectId, 'for class:', selectedClass);

    setLoadingSave(true);

    try {
      for (const grade of grades) {
        if (!grade.studentId || !grade.grade) {
          throw new Error('يرجى إدخال درجة صحيحة لجميع الطلاب');
        }

        const numericGrade = Number(grade.grade);
        const validation = validateGrade(numericGrade, maxPoints);
        if (!validation.isValid) {
          throw new Error(validation.error || `يجب أن تكون الدرجة رقماً بين 0 و ${maxPoints}`);
        }

        // التحقق من وجود جميع البيانات المطلوبة
        if (!selectedExam || !grade.studentId || !classSubjectId) {
          console.error('Missing required data:', {
            examId: selectedExam,
            studentId: grade.studentId,
            classSubjectId
          });
          throw new Error('بيانات غير مكتملة: يرجى التأكد من اختيار الامتحان والقسم والطالب');
        }

        // التحقق من وجود نقطة امتحان موجودة للطالب
        const existingGrade = existingGrades[grade.studentId];
        const isUpdate = existingGrade && existingGrade.id;

        let gradeData: any = {
          examId: Number(selectedExam),
          studentId: Number(grade.studentId),
          classSubjectId: Number(classSubjectId),
          grade: numericGrade,
          note: grade.note || '',
          teacherId: teacherId, // استخدام معرف المعلم الحقيقي من الجلسة
        };

        // إضافة معلومات السورة إذا كان الامتحان يتطلب ذلك
        if (selectedExamRequiresSurah && selectedSurah) {
          Object.assign(gradeData, {
            surahId: Number(selectedSurah),
            startVerse: startVerse ? Number(startVerse) : null,
            endVerse: endVerse ? Number(endVerse) : null,
          });
        }

        let response;
        if (isUpdate) {
          // تحديث النقطة الموجودة
          gradeData.id = existingGrade.id;
          console.log('Updating existing grade data:', gradeData);

          response = await fetch('/api/exam-points', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(gradeData),
          });
        } else {
          // إنشاء نقطة جديدة
          console.log('Creating new grade data:', gradeData);

          response = await fetch('/api/exam-points', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(gradeData),
          });
        }

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData?.error || errorData?.message || 'فشل في حفظ النقاط');
        }
      }

      const successMessage = isEditMode ? 'تم تحديث النقاط بنجاح' : 'تم حفظ النقاط بنجاح';
      setSuccess(successMessage);
      setHasUnsavedChanges(false);
      toast.success(isEditMode ? 'تم تحديث نقاط الامتحان بنجاح' : 'تم حفظ نقاط الامتحان بنجاح');

      // العودة إلى صفحة النتائج
      // إذا كان هناك طالب محدد، نعود إلى صفحة النتائج مع تحديد الامتحان والفصل
      if (studentId) {
        router.push(`/teachers/evaluation/results?examId=${selectedExam}&classId=${selectedClass}`);
      } else {
        router.push(`/teachers/evaluation/results?examId=${selectedExam}`);
      }
    } catch (err) {
      console.error('Error saving grades:', err);
      setErrors(prev => ({ ...prev, save: err instanceof Error ? err.message : 'فشل في حفظ النقاط' }));
      toast.error(err instanceof Error ? err.message : 'فشل في حفظ النقاط');
    } finally {
      setLoadingSave(false);
    }
  };

  const handleAutoSetup = async () => {
    if (!selectedExam || !selectedClass || !teacherId) {
      toast.error('يرجى اختيار الامتحان والقسم أولاً');
      return;
    }

    setIsAutoSetupLoading(true);
    setErrors(prev => ({ ...prev, save: '' }));

    try {
      const response = await fetch('/api/auto-setup-class-subject', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          examId: selectedExam,
          classId: selectedClass
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.needsSubjectAssignment) {
          setErrors(prev => ({
            ...prev,
            save: 'المعلم غير مرتبط بأي مادة دراسية. يرجى التواصل مع المشرف لربط المعلم بمادة دراسية.'
          }));
        } else {
          throw new Error(data.message || 'فشل في إعداد العلاقات');
        }
        return;
      }

      if (data.success) {
        let message = 'تم إعداد العلاقات بنجاح! ';
        if (data.data.teacherSubjectCreated) {
          message += 'تم ربط المعلم بالمادة. ';
        }
        if (data.data.classSubjectCreated) {
          message += 'تم ربط القسم بالمادة. ';
        }
        message += 'يمكنك الآن تسجيل النقاط.';

        toast.success(message);
        setSuccess(message);

        // إعادة جلب البيانات
        window.location.reload();
      }
    } catch (err) {
      console.error('Error in auto setup:', err);
      const errorMessage = err instanceof Error ? err.message : 'فشل في إعداد العلاقات';
      setErrors(prev => ({ ...prev, save: errorMessage }));
      toast.error(errorMessage);
    } finally {
      setIsAutoSetupLoading(false);
    }
  };

  const handleDeleteGrade = async (studentId: string) => {
    const existingGrade = existingGrades[studentId];
    if (!existingGrade || !existingGrade.id) {
      toast.error('لا توجد نقطة امتحان لحذفها');
      return;
    }

    if (!confirm('هل أنت متأكد من حذف نقطة الامتحان لهذا الطالب؟')) {
      return;
    }

    setLoadingDelete(prev => ({ ...prev, [studentId]: true }));

    try {
      const response = await fetch(`/api/exam-points?id=${existingGrade.id}&teacherId=${teacherId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData?.error || 'فشل في حذف النقطة');
      }

      // إزالة النقطة من الحالة المحلية
      setGrades(prevGrades => prevGrades.filter(g => g.studentId !== studentId));
      setExistingGrades(prev => {
        const newExisting = { ...prev };
        delete newExisting[studentId];
        return newExisting;
      });

      toast.success('تم حذف نقطة الامتحان بنجاح');
      setHasUnsavedChanges(true);
    } catch (err) {
      console.error('Error deleting grade:', err);
      toast.error(err instanceof Error ? err.message : 'فشل في حذف النقطة');
    } finally {
      setLoadingDelete(prev => ({ ...prev, [studentId]: false }));
    }
  };

  const getEvaluationTypeLabel = (type: string) => {
    const evaluationTypes = {
      'WRITTEN_EXAM': 'امتحان تحريري',
      'ORAL_EXAM': 'امتحان شفوي',
      'HOMEWORK': 'واجب منزلي',
      'PROJECT': 'مشروع',
      'QURAN_RECITATION': 'تلاوة القرآن',
      'QURAN_MEMORIZATION': 'حفظ القرآن',
      'PRACTICAL_TEST': 'اختبار عملي',
      'REMOTE_EXAM': 'امتحان عن بعد'
    };
    return evaluationTypes[type as keyof typeof evaluationTypes] || type;
  };

  return (
    <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <Card className="p-6 border-t-4 border-[var(--primary-color)] shadow-md">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 text-right mb-6">
          {studentId ? 'تعديل نقاط الطالب' : (isEditMode ? 'تعديل نقاط الامتحان' : 'تسجيل نقاط الامتحان')}
        </h1>

        {loadingTeacher && (
          <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-3 mb-4 text-right">
            <div className="flex items-center justify-end">
              <span className="ml-2">جاري التحقق من صلاحيات المعلم...</span>
              <Loader2 className="h-4 w-4 animate-spin" />
            </div>
          </div>
        )}

        <div className="space-y-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-right mb-2">الامتحان</label>
              <Select
                value={selectedExam === 'no-selection' ? 'no-exam' : selectedExam}
                onValueChange={setSelectedExam}
                disabled={loadingExams}
              >
                <SelectTrigger className="text-right">
                  <SelectValue placeholder={loadingExams ? 'جاري التحميل...' : 'اختر الامتحان'} />
                </SelectTrigger>
                <SelectContent>
                  {loadingExams ? (
                    <SelectItem value="loading-exams">جاري التحميل...</SelectItem>
                  ) : exams.length > 0 ? (
                    exams.map((exam) => (
                      <SelectItem key={exam.id} value={exam.id}>
                        {getEvaluationTypeLabel(exam.evaluationType)} - {exam.month} {exam.description ? `(${exam.description})` : ''}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-exam">لا توجد امتحانات متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.exams && (
                <div className="text-red-500 text-right text-sm mt-1">{errors.exams}</div>
              )}
            </div>

            <div>
              <label className="block text-right mb-2">القسم</label>
              <Select
                value={selectedClass === 'no-selection' ? 'no-classes' : selectedClass}
                onValueChange={setSelectedClass}
                disabled={loadingClasses}
              >
                <SelectTrigger className="text-right">
                  <SelectValue placeholder={loadingClasses ? 'جاري التحميل...' : 'اختر القسم'} />
                </SelectTrigger>
                <SelectContent>
                  {loadingClasses ? (
                    <SelectItem value="loading">جاري التحميل...</SelectItem>
                  ) : Array.isArray(classes) && classes.length > 0 ? (
                    classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id.toString()}>
                        {cls.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-classes">لا توجد أقسام متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.classes && (
                <div className="text-red-500 text-right text-sm mt-1">{errors.classes}</div>
              )}
            </div>

            {selectedExamRequiresSurah && (
              <>
                <div>
                  <label className="block text-right mb-2">السورة</label>
                  <Select
                    value={selectedSurah === 'no-selection' ? 'no-surah' : selectedSurah}
                    onValueChange={setSelectedSurah}
                    disabled={loadingSurahs}
                  >
                    <SelectTrigger className="text-right">
                      <SelectValue placeholder={loadingSurahs ? 'جاري التحميل...' : 'اختر السورة'} />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingSurahs ? (
                        <SelectItem value="loading-surahs">جاري التحميل...</SelectItem>
                      ) : surahs.length > 0 ? (
                        surahs.map((surah) => (
                          <SelectItem key={surah.id} value={surah.id.toString()}>
                            {surah.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-surah">لا توجد سور متاحة</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.surahs && (
                    <div className="text-red-500 text-right text-sm mt-1">{errors.surahs}</div>
                  )}
                </div>

                <div>
                  <label className="block text-right mb-2">من الآية</label>
                  <Input
                    type="number"
                    min="1"
                    value={startVerse}
                    onChange={(e) => setStartVerse(e.target.value)}
                    className="text-right"
                    placeholder="رقم الآية البداية"
                  />
                </div>

                <div>
                  <label className="block text-right mb-2">إلى الآية</label>
                  <Input
                    type="number"
                    min="1"
                    value={endVerse}
                    onChange={(e) => setEndVerse(e.target.value)}
                    className="text-right"
                    placeholder="رقم الآية النهاية"
                  />
                </div>
              </>
            )}
          </div>
        </div>

        {errors.teacher && (
          <div className="text-red-500 text-right text-sm mb-4">
            {loadingTeacher ? 'جاري التحقق من صلاحيات المعلم...' : errors.teacher}
          </div>
        )}

        {errors.save && (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-4 text-right">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="font-medium mb-2">خطأ في تسجيل النقاط:</p>
                <p className="text-sm">{errors.save}</p>

                {(errors.save.includes('لم يتم العثور على علاقة') ||
                  errors.save.includes('ليس لديك صلاحية')) && (
                  <div className="mt-3">
                    <p className="text-sm mb-2">
                      يبدو أن هناك مشكلة في إعداد العلاقات بين القسم والمادة.
                      يمكنك محاولة الإعداد التلقائي لحل هذه المشكلة:
                    </p>
                    <Button
                      onClick={handleAutoSetup}
                      disabled={isAutoSetupLoading}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      size="sm"
                    >
                      {isAutoSetupLoading ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin ml-2" />
                          جاري الإعداد...
                        </>
                      ) : (
                        'إعداد العلاقات تلقائياً'
                      )}
                    </Button>
                  </div>
                )}
              </div>
              <svg className="h-5 w-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        )}

        {success && (
          <div className="text-primary-color text-right text-sm mb-4">{success}</div>
        )}

        {isEditMode && (
          <div className="bg-blue-50 border border-blue-200 text-blue-800 rounded-md p-3 mb-4 text-right">
            <div className="flex items-center justify-end">
              <span className="ml-2">يتم عرض النقاط الموجودة مسبقاً. يمكنك تعديلها وحفظ التغييرات.</span>
              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        )}

        {errors.students && (
          <div className="text-red-500 text-right text-sm mb-4">{errors.students}</div>
        )}

        {loadingStudents ? (
          <div className="flex flex-col justify-center items-center h-40 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
            <div className="relative">
              <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
              <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
            </div>
            <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري تحميل الطلاب...</span>
          </div>
        ) : students.length === 0 ? (
          <div className="text-center text-gray-500 py-8 border border-[#e9f7f5] rounded-lg bg-[#f8fffd]">
            {selectedClass ? 'لا يوجد طلاب في هذا القسم' : 'يرجى اختيار القسم لعرض الطلاب'}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right">اسم الطالب</TableHead>
                  <TableHead className="text-right">الدرجة (من {maxPoints})</TableHead>
                  <TableHead className="text-right">الملاحظات</TableHead>
                  {isEditMode && <TableHead className="text-right">الإجراءات</TableHead>}
                </TableRow>
              </TableHeader>
              <TableBody>
                {students.map((student) => {
                  const studentGrade = grades.find((g: Grade) => g.studentId === student.id);
                  return (
                    <TableRow key={student.id}>
                      <TableCell className="text-right">{student.name}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Input
                            type="number"
                            min="0"
                            max={maxPoints}
                            step="0.5"
                            value={studentGrade ? (studentGrade as Grade).grade || '' : ''}
                            onChange={(e) => handleGradeChange(student.id, 'grade', e.target.value)}
                            className="w-full md:w-24"
                            placeholder={`0-${maxPoints}`}
                          />
                          {studentGrade?.grade && (
                            <div className="text-xs text-center">
                              {(() => {
                                const grade = Number(studentGrade.grade);
                                const percentage = calculatePercentage(grade, maxPoints);
                                const gradeLevel = getGradeLevelByScore(grade, maxPoints);
                                return (
                                  <span
                                    className="px-1 py-0.5 rounded text-xs font-semibold"
                                    style={{ backgroundColor: gradeLevel.color + '20', color: gradeLevel.color }}
                                  >
                                    {percentage}% - {gradeLevel.nameAr}
                                  </span>
                                );
                              })()}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Textarea
                          value={studentGrade ? (studentGrade as Grade).note || '' : ''}
                          onChange={(e) => handleGradeChange(student.id, 'note', e.target.value)}
                          className="w-full"
                          rows={1}
                        />
                      </TableCell>
                      {isEditMode && (
                        <TableCell>
                          {existingGrades[student.id] && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteGrade(student.id)}
                              disabled={loadingDelete[student.id]}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              {loadingDelete[student.id] ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                            </Button>
                          )}
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        )}

        <div className="mt-6 flex justify-center md:justify-end">
          <Button
            onClick={handleSubmit}
            disabled={loadingSave || students.length === 0}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full md:w-auto"
          >
            {loadingSave ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                جاري الحفظ...
              </>
            ) : (
              studentId ? 'حفظ نقاط الطالب' : (isEditMode ? 'تحديث النقاط' : 'حفظ النقاط')
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
}


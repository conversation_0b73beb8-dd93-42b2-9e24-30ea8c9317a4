'use client'

import { useEffect, useState } from 'react'
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useRouter, useSearchParams } from 'next/navigation'
import { AddTeacherModal } from './add-teacher-modal'
import { EditTeacherModal } from './edit-teacher-modal'
import { AddEvaluationModal } from './add-evaluation-modal'
import { EditEvaluationModal } from './edit-evaluation-modal'
import { AddScheduleModal } from './add-schedule-modal'
import { EditScheduleModal } from './edit-schedule-modal'
import { AddAchievementModal } from './add-achievement-modal'
import { EditAchievementModal } from './edit-achievement-modal'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { exportToExcel } from '@/utils/export-utils'
import {
  FaChalkboardTeacher, FaSearch, FaEdit, FaTrash, FaSync, FaUserPlus, FaArrowLeft, FaArrowRight,
  FaCalendarAlt, FaAward, FaClipboardCheck, FaFileExcel, FaUserTie, FaPlus
} from 'react-icons/fa'

interface TeacherSubject {
  id: number;
  teacherId: number;
  subjectId: number;
  createdAt: string;
  subject?: {
    id: number;
    name: string;
  }
}

interface Teacher {
  id: number
  name: string
  specialization: string
  phone: string | null
  classesCount: number
  createdAt: string
  userId: number
  teacherSubjects: TeacherSubject[]
  user?: {
    username: string
  }
}

interface PaginationData {
  total: number
  totalPages: number
  currentPage: number
  limit: number
}

interface TeacherEvaluation {
  id: number
  teacherId: number
  evaluatorId: number
  evaluationDate: string
  teachingSkills: number
  classManagement: number
  studentProgress: number
  attendance: number
  communication: number
  overallRating: number
  strengths?: string
  improvements?: string
  comments?: string
  teacher?: Teacher
  evaluator?: {
    id: number
    username: string
    profile?: {
      name: string
    }
  }
}

interface TeacherSchedule {
  id: number
  teacherId: number
  day: string
  startTime: string
  endTime: string
  classeId: number
  subjectId: number
  location?: string
  notes?: string
  teacher?: Teacher
  classe?: {
    id: number
    name: string
  }
  subject?: {
    id: number
    name: string
  }
}

interface TeacherAchievement {
  id: number
  teacherId: number
  title: string
  description: string
  achievementDate: string
  type: string
  attachmentUrl?: string
  teacher?: Teacher
}

export default function TeachersPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  // حالة التبويبات
  const [activeTab, setActiveTab] = useState('teachers')

  // حالة البحث والتحميل
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  // حالة المعلمين
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [pagination, setPagination] = useState<PaginationData>({ total: 0, totalPages: 0, currentPage: 1, limit: 10 })

  // حالة التقييمات
  const [evaluations, setEvaluations] = useState<TeacherEvaluation[]>([])
  const [evaluationsPagination, setEvaluationsPagination] = useState<PaginationData>({ total: 0, totalPages: 0, currentPage: 1, limit: 10 })

  // حالة جداول الحصص
  const [schedules, setSchedules] = useState<TeacherSchedule[]>([])
  const [schedulesPagination, setSchedulesPagination] = useState<PaginationData>({ total: 0, totalPages: 0, currentPage: 1, limit: 10 })

  // حالة الإنجازات
  const [achievements, setAchievements] = useState<TeacherAchievement[]>([])
  const [achievementsPagination, setAchievementsPagination] = useState<PaginationData>({ total: 0, totalPages: 0, currentPage: 1, limit: 10 })

  // حالة النوافذ المنبثقة
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  // حالة نوافذ التقييم
  const [isAddEvaluationModalOpen, setIsAddEvaluationModalOpen] = useState(false)
  const [isEditEvaluationModalOpen, setIsEditEvaluationModalOpen] = useState(false)
  const [selectedEvaluation, setSelectedEvaluation] = useState<TeacherEvaluation | null>(null)

  // حالة نوافذ جداول الحصص
  const [isAddScheduleModalOpen, setIsAddScheduleModalOpen] = useState(false)
  const [isEditScheduleModalOpen, setIsEditScheduleModalOpen] = useState(false)
  const [selectedSchedule, setSelectedSchedule] = useState<TeacherSchedule | null>(null)

  // حالة نوافذ الإنجازات
  const [isAddAchievementModalOpen, setIsAddAchievementModalOpen] = useState(false)
  const [isEditAchievementModalOpen, setIsEditAchievementModalOpen] = useState(false)
  const [selectedAchievement, setSelectedAchievement] = useState<TeacherAchievement | null>(null)

const currentPage = Number(searchParams?.get('page')) || 1

  // جلب بيانات المعلمين
  const fetchTeachers = async () => {
    try {
      setIsLoading(true)
      setError('')
      const response = await fetch(
        `/api/teachers?page=${currentPage}&search=${searchQuery}&limit=10`
      )
      if (!response.ok) throw new Error('Failed to fetch teachers')

      const data = await response.json()
      setTeachers(data.teachers || [])
      setPagination(data.pagination || { total: 0, totalPages: 0, currentPage: 1, limit: 10 })
    } catch {
      setError('Failed to load teachers')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      fetchTeachers()
    }, 300)

    return () => clearTimeout(debounceTimer)
  }, [searchQuery, currentPage])

  // جلب تقييمات المعلمين
  const fetchEvaluations = async (teacherId?: number, page: number = 1) => {
    try {
      setIsLoading(true)
      setError('')
      const url = teacherId
        ? `/api/teacher-evaluations?teacherId=${teacherId}&page=${page}&limit=10`
        : `/api/teacher-evaluations?page=${page}&limit=10`

      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch evaluations')

      const data = await response.json()
      setEvaluations(data.data || [])
      setEvaluationsPagination(data.pagination || { total: 0, totalPages: 0, currentPage: page, limit: 10 })
    } catch {
      setError('Failed to load evaluations')
    } finally {
      setIsLoading(false)
    }
  }

  // جلب جداول حصص المعلمين
  const fetchSchedules = async (teacherId?: number, page: number = 1) => {
    try {
      setIsLoading(true)
      setError('')
      const url = teacherId
        ? `/api/teacher-schedules?teacherId=${teacherId}&page=${page}&limit=10`
        : `/api/teacher-schedules?page=${page}&limit=10`

      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch schedules')

      const data = await response.json()
      setSchedules(data.data || [])
      setSchedulesPagination(data.pagination || { total: 0, totalPages: 0, currentPage: page, limit: 10 })
    } catch {
      setError('Failed to load schedules')
    } finally {
      setIsLoading(false)
    }
  }

  // جلب إنجازات المعلمين
  const fetchAchievements = async (teacherId?: number, page: number = 1) => {
    try {
      setIsLoading(true)
      setError('')
      const url = teacherId
        ? `/api/teacher-achievements?teacherId=${teacherId}&page=${page}&limit=10`
        : `/api/teacher-achievements?page=${page}&limit=10`

      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch achievements')

      const data = await response.json()
      setAchievements(data.data || [])
      setAchievementsPagination(data.pagination || { total: 0, totalPages: 0, currentPage: page, limit: 10 })
    } catch {
      setError('Failed to load achievements')
    } finally {
      setIsLoading(false)
    }
  }

  // التنقل بين صفحات التقييمات
  const handleEvaluationPageChange = (page: number) => {
    fetchEvaluations(undefined, page);
  }

  // التنقل بين صفحات جداول الحصص
  const handleSchedulePageChange = (page: number) => {
    fetchSchedules(undefined, page);
  }

  // التنقل بين صفحات الإنجازات
  const handleAchievementPageChange = (page: number) => {
    fetchAchievements(undefined, page);
  }

  // تحميل البيانات المناسبة عند تغيير التبويب
  useEffect(() => {
    if (activeTab === 'teachers') {
      // تم تحميل المعلمين بالفعل
    } else if (activeTab === 'evaluations') {
      fetchEvaluations()
    } else if (activeTab === 'schedules') {
      fetchSchedules()
    } else if (activeTab === 'achievements') {
      fetchAchievements()
    }
  }, [activeTab])

  // وظائف تصدير البيانات
  const handleExportTeachers = () => {
    // تحويل البيانات إلى الشكل المطلوب للتصدير
    const dataToExport = teachers.map((teacher, index) => ({
      'الرقم': index + 1,
      'اسم المعلم': teacher.name,
      'التخصص': teacher.specialization,
      'رقم الهاتف': teacher.phone || '',
      'عدد الفصول': teacher.classesCount,
      'اسم المستخدم': teacher.user?.username || '',
      'تاريخ التسجيل': new Date(teacher.createdAt).toLocaleDateString('fr-FR')
    }));

    // تحديد عرض الأعمدة
    const columnWidths = [
      { wch: 5 },  // الرقم
      { wch: 20 }, // اسم المعلم
      { wch: 15 }, // التخصص
      { wch: 15 }, // رقم الهاتف
      { wch: 10 }, // عدد الفصول
      { wch: 15 }, // اسم المستخدم
      { wch: 15 }  // تاريخ التسجيل
    ];

    // تصدير البيانات
    exportToExcel(
      dataToExport,
      'قائمة_المعلمين.xlsx',
      'المعلمين',
      columnWidths
    );
  }

  const handleExportEvaluations = () => {
    // تحويل البيانات إلى الشكل المطلوب للتصدير
    const dataToExport = evaluations.map((evaluation, index) => ({
      'الرقم': index + 1,
      'اسم المعلم': evaluation.teacher?.name || '',
      'تاريخ التقييم': new Date(evaluation.evaluationDate).toLocaleDateString('fr-FR'),
      'مهارات التدريس': evaluation.teachingSkills,
      'إدارة الفصل': evaluation.classManagement,
      'تقدم الطلاب': evaluation.studentProgress,
      'الحضور والالتزام': evaluation.attendance,
      'التواصل': evaluation.communication,
      'التقييم العام': evaluation.overallRating,
      'المقيم': evaluation.evaluator?.profile?.name || evaluation.evaluator?.username || ''
    }));

    // تحديد عرض الأعمدة
    const columnWidths = [
      { wch: 5 },  // الرقم
      { wch: 20 }, // اسم المعلم
      { wch: 15 }, // تاريخ التقييم
      { wch: 12 }, // مهارات التدريس
      { wch: 12 }, // إدارة الفصل
      { wch: 12 }, // تقدم الطلاب
      { wch: 12 }, // الحضور والالتزام
      { wch: 12 }, // التواصل
      { wch: 12 }, // التقييم العام
      { wch: 20 }  // المقيم
    ];

    // تصدير البيانات
    exportToExcel(
      dataToExport,
      'تقييمات_المعلمين.xlsx',
      'التقييمات',
      columnWidths
    );
  }

  const handleExportSchedules = () => {
    // تحويل البيانات إلى الشكل المطلوب للتصدير
    const dataToExport = schedules.map((schedule, index) => ({
      'الرقم': index + 1,
      'اسم المعلم': schedule.teacher?.name || '',
      'اليوم': schedule.day,
      'من الساعة': schedule.startTime,
      'إلى الساعة': schedule.endTime,
      'الفصل': schedule.classe?.name || '',
      'المادة': schedule.subject?.name || '',
      'المكان': schedule.location || '',
      'ملاحظات': schedule.notes || ''
    }));

    // تحديد عرض الأعمدة
    const columnWidths = [
      { wch: 5 },  // الرقم
      { wch: 20 }, // اسم المعلم
      { wch: 10 }, // اليوم
      { wch: 10 }, // من الساعة
      { wch: 10 }, // إلى الساعة
      { wch: 15 }, // الفصل
      { wch: 15 }, // المادة
      { wch: 15 }, // المكان
      { wch: 25 }  // ملاحظات
    ];

    // تصدير البيانات
    exportToExcel(
      dataToExport,
      'جداول_حصص_المعلمين.xlsx',
      'جداول الحصص',
      columnWidths
    );
  }

  const handleExportAchievements = () => {
    // تحويل البيانات إلى الشكل المطلوب للتصدير
    const dataToExport = achievements.map((achievement, index) => ({
      'الرقم': index + 1,
      'اسم المعلم': achievement.teacher?.name || '',
      'عنوان الإنجاز': achievement.title,
      'النوع': achievement.type,
      'تاريخ الإنجاز': new Date(achievement.achievementDate).toLocaleDateString('fr-FR'),
      'الوصف': achievement.description
    }));

    // تحديد عرض الأعمدة
    const columnWidths = [
      { wch: 5 },  // الرقم
      { wch: 20 }, // اسم المعلم
      { wch: 25 }, // عنوان الإنجاز
      { wch: 15 }, // النوع
      { wch: 15 }, // تاريخ الإنجاز
      { wch: 40 }  // الوصف
    ];

    // تصدير البيانات
    exportToExcel(
      dataToExport,
      'إنجازات_المعلمين.xlsx',
      'الإنجازات',
      columnWidths
    );
  }

  return (
    <OptimizedProtectedRoute requiredPermission="admin.teachers.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaChalkboardTeacher className="text-[var(--primary-color)]" />
          إدارة المعلمين
        </h1>
        <div className="flex gap-2 w-full sm:w-auto justify-end">
          <QuickActionButtons
            entityType="teachers"
            actions={[
              {
                key: 'create',
                label: 'إضافة معلم جديد',
                icon: <FaUserPlus />,
                onClick: () => setIsAddModalOpen(true),
                variant: 'primary'
              }
            ]}
            className="flex-1 sm:flex-none"
          />
          <Button
            onClick={() => {
              setSearchQuery('')
              fetchTeachers()
            }}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2 min-w-[40px]"
            title="تحديث البيانات"
          >
            <FaSync className="h-5 w-5" />
          </Button>
        </div>
        <AddTeacherModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onSuccess={() => {
            setSearchQuery('')
            fetchTeachers()
          }}
        />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 sm:grid-cols-4 mb-4 bg-[#e0f2ef] overflow-x-auto">
          <TabsTrigger value="teachers" className="flex items-center gap-1 text-xs sm:text-sm py-2">
            <FaUserTie className="hidden sm:block" />
            المعلمين
          </TabsTrigger>
          <TabsTrigger value="evaluations" className="flex items-center gap-1 text-xs sm:text-sm py-2">
            <FaClipboardCheck className="hidden sm:block" />
            التقييمات
          </TabsTrigger>
          <TabsTrigger value="schedules" className="flex items-center gap-1 text-xs sm:text-sm py-2">
            <FaCalendarAlt className="hidden sm:block" />
            الجداول
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex items-center gap-1 text-xs sm:text-sm py-2">
            <FaAward className="hidden sm:block" />
            الإنجازات
          </TabsTrigger>
        </TabsList>

        <div className="flex flex-col sm:flex-row items-center gap-3 bg-white p-3 sm:p-4 rounded-lg shadow-md mb-4 sm:mb-6">
          <div className="relative w-full">
            <Input
              type="text"
              placeholder="البحث..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--primary-color)]" />
          </div>

          <QuickActionButtons
            entityType="reports"
            actions={[
              {
                key: 'export',
                label: 'تصدير البيانات',
                icon: <FaFileExcel />,
                onClick: activeTab === 'teachers' ? handleExportTeachers :
                        activeTab === 'evaluations' ? handleExportEvaluations :
                        activeTab === 'schedules' ? handleExportSchedules :
                        handleExportAchievements,
                variant: 'success',
                permission: 'admin.reports.view'
              }
            ]}
            className="w-full sm:w-auto mt-2 sm:mt-0"
          />
        </div>

        {error ? (
          <div className="text-center text-red-600 p-4 bg-red-50 rounded-md border border-red-100">{error}</div>
        ) : (
          <>
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogContent className="sm:max-w-[425px] bg-[#fff8f8] border border-red-200">
                <DialogHeader>
                  <DialogTitle className="text-red-600 font-bold text-xl flex items-center">
                    <FaTrash className="ml-2" />
                    تأكيد الحذف
                  </DialogTitle>
                </DialogHeader>
                <div className="py-4 text-center">
                  هل أنت متأكد من حذف هذا المعلم؟
                </div>
                <DialogFooter className="flex justify-end gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setIsDeleteDialogOpen(false)}
                    className="border-gray-300 hover:bg-gray-100"
                  >
                    إلغاء
                  </Button>
                  <Button
                    className="bg-red-600 hover:bg-red-700 text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
                    disabled={isDeleting}
                    onClick={async () => {
                      if (!selectedTeacher) return
                      setIsDeleting(true)
                      try {
                        const response = await fetch(`/api/admin/teachers/${selectedTeacher.id}`, {
                          method: 'DELETE',
                        })
                        if (!response.ok) throw new Error('Failed to delete teacher')
                        setIsDeleteDialogOpen(false)
                        // تحديث البيانات بعد الحذف
                        await fetchTeachers()
                      } catch (err) {
                        console.error(err)
                      } finally {
                        setIsDeleting(false)
                      }
                    }}
                  >
                    {isDeleting ? 'جاري الحذف...' : 'حذف'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <EditTeacherModal
              isOpen={isEditModalOpen}
              onClose={() => setIsEditModalOpen(false)}
              onSuccess={() => {
                setIsEditModalOpen(false)
                fetchTeachers()
              }}
              teacher={selectedTeacher as Teacher}
            />

            <AddEvaluationModal
              isOpen={isAddEvaluationModalOpen}
              onClose={() => setIsAddEvaluationModalOpen(false)}
              onSuccess={() => {
                setIsAddEvaluationModalOpen(false)
                fetchEvaluations()
              }}
            />

            <EditEvaluationModal
              isOpen={isEditEvaluationModalOpen}
              onClose={() => setIsEditEvaluationModalOpen(false)}
              onSuccess={() => {
                setIsEditEvaluationModalOpen(false)
                fetchEvaluations()
              }}
              evaluation={selectedEvaluation}
            />

            <AddScheduleModal
              isOpen={isAddScheduleModalOpen}
              onClose={() => setIsAddScheduleModalOpen(false)}
              onSuccess={() => {
                setIsAddScheduleModalOpen(false)
                fetchSchedules()
              }}
            />

            <EditScheduleModal
              isOpen={isEditScheduleModalOpen}
              onClose={() => setIsEditScheduleModalOpen(false)}
              onSuccess={() => {
                setIsEditScheduleModalOpen(false)
                fetchSchedules()
              }}
              schedule={selectedSchedule}
            />

            <AddAchievementModal
              isOpen={isAddAchievementModalOpen}
              onClose={() => setIsAddAchievementModalOpen(false)}
              onSuccess={() => {
                setIsAddAchievementModalOpen(false)
                fetchAchievements()
              }}
            />

            <EditAchievementModal
              isOpen={isEditAchievementModalOpen}
              onClose={() => setIsEditAchievementModalOpen(false)}
              onSuccess={() => {
                setIsEditAchievementModalOpen(false)
                fetchAchievements()
              }}
              achievement={selectedAchievement}
            />

            <TabsContent value="teachers" className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
          {/* Desktop Table View */}
          <div className="hidden md:block overflow-x-auto">
            <div className="min-w-[800px]">
              <Table>
                <TableHeader>
                  <TableRow className="bg-[var(--primary-color)]">
                    <TableHead className="text-white font-bold text-right">الإجراءات</TableHead>
                    <TableHead className="text-white font-bold text-right">تاريخ التسجيل</TableHead>
                    <TableHead className="text-white font-bold text-right">عدد الفصول</TableHead>
                    <TableHead className="text-white font-bold text-right">رقم الهاتف</TableHead>
                    <TableHead className="text-white font-bold text-right">المواد</TableHead>
                    <TableHead className="text-white font-bold text-right">الاسم</TableHead>
                    <TableHead className="text-white font-bold text-right">الرقم</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      جاري التحميل...
                    </TableCell>
                  </TableRow>
                ) : teachers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-4">
                      لا يوجد معلمين
                    </TableCell>
                  </TableRow>
                ) : (
                  teachers.map((teacher) => (
                    <TableRow key={teacher.id}>
                      <TableCell className="text-right">
                        <OptimizedActionButtonGroup
                          entityType="teachers"
                          onEdit={() => {
                            setSelectedTeacher(teacher)
                            setIsEditModalOpen(true)
                          }}
                          onDelete={() => {
                            setSelectedTeacher(teacher)
                            setIsDeleteDialogOpen(true)
                          }}
                          showEdit={true}
                          showDelete={true}
                          className="justify-start"
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        {new Date(teacher.createdAt).toLocaleDateString('fr-FR')}
                      </TableCell>
                      <TableCell className="text-right">{teacher.classesCount}</TableCell>
                      <TableCell className="text-right">{teacher.phone || '-'}</TableCell>
                      <TableCell className="text-right">{teacher.specialization}</TableCell>
                      <TableCell className="text-right">{teacher.name}</TableCell>
                      <TableCell className="text-right">{teacher.id}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="md:hidden">
            {isLoading ? (
              <div className="text-center py-4">جاري التحميل...</div>
            ) : teachers.length === 0 ? (
              <div className="text-center py-4">لا يوجد معلمين</div>
            ) : (
              <div className="grid gap-4 p-4">
                {teachers.map((teacher) => (
                  <div key={teacher.id} className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold text-lg">{teacher.name}</h3>
                        <p className="text-gray-600">{teacher.specialization}</p>
                      </div>
                      <span className="text-sm text-gray-500">#{teacher.id}</span>
                    </div>
                    <div className="space-y-1 text-sm">
                      <p><span className="text-gray-600">رقم الهاتف:</span> {teacher.phone || '-'}</p>
                      <p><span className="text-gray-600">عدد الأقسام:</span> {teacher.classesCount}</p>
                      <p><span className="text-gray-600">تاريخ التسجيل:</span> {new Date(teacher.createdAt).toLocaleDateString('fr-FR')}</p>
                    </div>
                    <div className="mt-3">
                      <OptimizedActionButtonGroup
                        entityType="teachers"
                        onEdit={() => {
                          setSelectedTeacher(teacher)
                          setIsEditModalOpen(true)
                        }}
                        onDelete={() => {
                          setSelectedTeacher(teacher)
                          setIsDeleteDialogOpen(true)
                        }}
                        showEdit={true}
                        showDelete={true}
                        className="justify-center gap-2"
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
            </TabsContent>

            <TabsContent value="evaluations" className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
              <div className="p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row justify-between items-center gap-2 mb-4">
                  <h3 className="text-lg sm:text-xl font-bold text-[var(--primary-color)]">تقييمات المعلمين</h3>
                  <PermissionGuard requiredPermission="admin.teachers.evaluate">
                    <Button
                      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2 w-full sm:w-auto"
                      size="sm"
                      onClick={() => setIsAddEvaluationModalOpen(true)}
                    >
                      <FaPlus className="h-4 w-4" />
                      إضافة تقييم
                    </Button>
                  </PermissionGuard>
                </div>

                {isLoading ? (
                  <div className="text-center py-8">جاري التحميل...</div>
                ) : evaluations.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد تقييمات للمعلمين حتى الآن
                  </div>
                ) : (
                  <>
                    {/* عرض الجدول على الشاشات المتوسطة والكبيرة */}
                    <div className="hidden md:block overflow-x-auto">
                      <div className="min-w-[800px]">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-[var(--primary-color)]">
                              <TableHead className="text-white font-bold text-right">الإجراءات</TableHead>
                              <TableHead className="text-white font-bold text-right">المقيم</TableHead>
                              <TableHead className="text-white font-bold text-right">التقييم العام</TableHead>
                              <TableHead className="text-white font-bold text-right">تاريخ التقييم</TableHead>
                              <TableHead className="text-white font-bold text-right">المعلم</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {evaluations.map((evaluation) => (
                              <TableRow key={evaluation.id}>
                                <TableCell className="text-right">
                                  <div className="flex gap-2 justify-start">
                                    <PermissionGuard requiredPermission="admin.teachers.evaluate">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          setSelectedEvaluation(evaluation)
                                          setIsEditEvaluationModalOpen(true)
                                        }}
                                        className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                                      >
                                        <FaEdit className="ml-1" />
                                        تعديل
                                      </Button>
                                    </PermissionGuard>
                                  </div>
                                </TableCell>
                                <TableCell className="text-right">{evaluation.evaluator?.profile?.name || evaluation.evaluator?.username || '-'}</TableCell>
                                <TableCell className="text-right">
                                  <Badge className={`${
                                    evaluation.overallRating >= 8 ? 'bg-green-100 text-green-800' :
                                    evaluation.overallRating >= 6 ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {evaluation.overallRating}/10
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">{new Date(evaluation.evaluationDate).toLocaleDateString('fr-FR')}</TableCell>
                                <TableCell className="text-right">{evaluation.teacher?.name || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>

                    {/* عرض البطاقات على الشاشات الصغيرة */}
                    <div className="md:hidden">
                      <div className="grid gap-4">
                        {evaluations.map((evaluation) => (
                          <div key={evaluation.id} className="bg-gray-50 p-4 rounded-lg space-y-2">
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="font-semibold text-lg">{evaluation.teacher?.name || '-'}</h3>
                                <p className="text-gray-600 text-sm">{new Date(evaluation.evaluationDate).toLocaleDateString('fr-FR')}</p>
                              </div>
                              <Badge className={`${
                                evaluation.overallRating >= 8 ? 'bg-green-100 text-green-800' :
                                evaluation.overallRating >= 6 ? 'bg-yellow-100 text-yellow-800' :
                                'bg-red-100 text-red-800'
                              }`}>
                                {evaluation.overallRating}/10
                              </Badge>
                            </div>
                            <div className="space-y-1 text-sm">
                              <p><span className="text-gray-600">مهارات التدريس:</span> {evaluation.teachingSkills}/10</p>
                              <p><span className="text-gray-600">إدارة الفصل:</span> {evaluation.classManagement}/10</p>
                              <p><span className="text-gray-600">المقيم:</span> {evaluation.evaluator?.profile?.name || evaluation.evaluator?.username || '-'}</p>
                            </div>
                            <div className="flex justify-end mt-3">
                              <PermissionGuard requiredPermission="admin.teachers.evaluate">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedEvaluation(evaluation)
                                    setIsEditEvaluationModalOpen(true)
                                  }}
                                  className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                                >
                                  <FaEdit className="ml-1" />
                                  تعديل
                                </Button>
                              </PermissionGuard>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>

            <TabsContent value="schedules" className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
              <div className="p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row justify-between items-center gap-2 mb-4">
                  <h3 className="text-lg sm:text-xl font-bold text-[var(--primary-color)]">جداول حصص المعلمين</h3>
                  <PermissionGuard requiredPermission="admin.teachers.schedule">
                    <Button
                      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2 w-full sm:w-auto"
                      size="sm"
                      onClick={() => setIsAddScheduleModalOpen(true)}
                    >
                      <FaPlus className="h-4 w-4" />
                      إضافة حصة
                    </Button>
                  </PermissionGuard>
                </div>

                {isLoading ? (
                  <div className="text-center py-8">جاري التحميل...</div>
                ) : schedules.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد جداول حصص للمعلمين حتى الآن
                  </div>
                ) : (
                  <>
                    {/* عرض الجدول على الشاشات المتوسطة والكبيرة */}
                    <div className="hidden md:block overflow-x-auto">
                      <div className="min-w-[800px]">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-[var(--primary-color)]">
                              <TableHead className="text-white font-bold text-right">الإجراءات</TableHead>
                              <TableHead className="text-white font-bold text-right">المادة</TableHead>
                              <TableHead className="text-white font-bold text-right">الفصل</TableHead>
                              <TableHead className="text-white font-bold text-right">الوقت</TableHead>
                              <TableHead className="text-white font-bold text-right">اليوم</TableHead>
                              <TableHead className="text-white font-bold text-right">المعلم</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {schedules.map((schedule) => (
                              <TableRow key={schedule.id}>
                                <TableCell className="text-right">
                                  <div className="flex gap-2 justify-start">
                                    <PermissionGuard requiredPermission="admin.teachers.schedule">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          setSelectedSchedule(schedule)
                                          setIsEditScheduleModalOpen(true)
                                        }}
                                        className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                                      >
                                        <FaEdit className="ml-1" />
                                        تعديل
                                      </Button>
                                    </PermissionGuard>
                                  </div>
                                </TableCell>
                                <TableCell className="text-right">{schedule.subject?.name || '-'}</TableCell>
                                <TableCell className="text-right">{schedule.classe?.name || '-'}</TableCell>
                                <TableCell className="text-right">{schedule.startTime} - {schedule.endTime}</TableCell>
                                <TableCell className="text-right">{schedule.day}</TableCell>
                                <TableCell className="text-right">{schedule.teacher?.name || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>

                    {/* عرض البطاقات على الشاشات الصغيرة */}
                    <div className="md:hidden">
                      <div className="grid gap-4">
                        {schedules.map((schedule) => (
                          <div key={schedule.id} className="bg-gray-50 p-4 rounded-lg space-y-2">
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="font-semibold text-lg">{schedule.teacher?.name || '-'}</h3>
                                <p className="text-gray-600 text-sm">{schedule.day}</p>
                              </div>
                              <div className="bg-[#e0f2ef] text-[var(--primary-color)] px-2 py-1 rounded-md text-sm font-medium">
                                {schedule.startTime} - {schedule.endTime}
                              </div>
                            </div>
                            <div className="space-y-1 text-sm">
                              <p><span className="text-gray-600">الفصل:</span> {schedule.classe?.name || '-'}</p>
                              <p><span className="text-gray-600">المادة:</span> {schedule.subject?.name || '-'}</p>
                              {schedule.location && <p><span className="text-gray-600">المكان:</span> {schedule.location}</p>}
                              {schedule.notes && <p><span className="text-gray-600">ملاحظات:</span> {schedule.notes}</p>}
                            </div>
                            <div className="flex justify-end mt-3">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedSchedule(schedule)
                                  setIsEditScheduleModalOpen(true)
                                }}
                                className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                              >
                                <FaEdit className="ml-1" />
                                تعديل
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>

            <TabsContent value="achievements" className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
              <div className="p-3 sm:p-4">
                <div className="flex flex-col sm:flex-row justify-between items-center gap-2 mb-4">
                  <h3 className="text-lg sm:text-xl font-bold text-[var(--primary-color)]">إنجازات المعلمين</h3>
                  <PermissionGuard requiredPermission="admin.teachers.achievements">
                    <Button
                      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2 w-full sm:w-auto"
                      size="sm"
                      onClick={() => setIsAddAchievementModalOpen(true)}
                    >
                      <FaPlus className="h-4 w-4" />
                      إضافة إنجاز
                    </Button>
                  </PermissionGuard>
                </div>

                {isLoading ? (
                  <div className="text-center py-8">جاري التحميل...</div>
                ) : achievements.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد إنجازات للمعلمين حتى الآن
                  </div>
                ) : (
                  <>
                    {/* عرض الجدول على الشاشات المتوسطة والكبيرة */}
                    <div className="hidden md:block overflow-x-auto">
                      <div className="min-w-[800px]">
                        <Table>
                          <TableHeader>
                            <TableRow className="bg-[var(--primary-color)]">
                              <TableHead className="text-white font-bold text-right">الإجراءات</TableHead>
                              <TableHead className="text-white font-bold text-right">تاريخ الإنجاز</TableHead>
                              <TableHead className="text-white font-bold text-right">النوع</TableHead>
                              <TableHead className="text-white font-bold text-right">عنوان الإنجاز</TableHead>
                              <TableHead className="text-white font-bold text-right">المعلم</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {achievements.map((achievement) => (
                              <TableRow key={achievement.id}>
                                <TableCell className="text-right">
                                  <div className="flex gap-2 justify-start">
                                    <PermissionGuard requiredPermission="admin.teachers.achievements">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          setSelectedAchievement(achievement)
                                          setIsEditAchievementModalOpen(true)
                                        }}
                                        className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                                      >
                                        <FaEdit className="ml-1" />
                                        تعديل
                                      </Button>
                                    </PermissionGuard>
                                  </div>
                                </TableCell>
                                <TableCell className="text-right">{new Date(achievement.achievementDate).toLocaleDateString('fr-FR')}</TableCell>
                                <TableCell className="text-right">{achievement.type}</TableCell>
                                <TableCell className="text-right">{achievement.title}</TableCell>
                                <TableCell className="text-right">{achievement.teacher?.name || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>

                    {/* عرض البطاقات على الشاشات الصغيرة */}
                    <div className="md:hidden">
                      <div className="grid gap-4">
                        {achievements.map((achievement) => (
                          <div key={achievement.id} className="bg-gray-50 p-4 rounded-lg space-y-2">
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="font-semibold text-lg">{achievement.title}</h3>
                                <p className="text-gray-600 text-sm">{achievement.teacher?.name || '-'}</p>
                              </div>
                              <div className="bg-[#e0f2ef] text-[var(--primary-color)] px-2 py-1 rounded-md text-sm font-medium">
                                {achievement.type}
                              </div>
                            </div>
                            <div className="space-y-1 text-sm">
                              <p><span className="text-gray-600">التاريخ:</span> {new Date(achievement.achievementDate).toLocaleDateString('fr-FR')}</p>
                              {achievement.description && (
                                <p><span className="text-gray-600">الوصف:</span> {achievement.description}</p>
                              )}
                            </div>
                            <div className="flex justify-end mt-3">
                              <PermissionGuard requiredPermission="admin.teachers.achievements">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedAchievement(achievement)
                                    setIsEditAchievementModalOpen(true)
                                  }}
                                  className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                                >
                                  <FaEdit className="ml-1" />
                                  تعديل
                                </Button>
                              </PermissionGuard>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </TabsContent>
          </>
        )}
      </Tabs>

      {activeTab === 'teachers' && !isLoading && pagination && pagination.totalPages > 1 && (
        <div className="flex flex-wrap justify-center gap-2 px-3 sm:px-4 bg-white p-3 rounded-lg shadow-md border border-[#e0f2ef] mt-4 sm:mt-6">
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage <= 1}
            onClick={() => router.push(`/admin/teachers?page=${currentPage - 1}`)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <FaArrowRight className="ml-1" />
            <span className="hidden sm:inline">السابق</span>
          </Button>
          <span className="mx-2 py-2 text-[var(--primary-color)] font-medium text-sm sm:text-base">
            {currentPage} / {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage >= pagination.totalPages}
            onClick={() => router.push(`/admin/teachers?page=${currentPage + 1}`)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <span className="hidden sm:inline">التالي</span>
            <FaArrowLeft className="ml-1" />
          </Button>
        </div>
      )}

      {activeTab === 'evaluations' && !isLoading && evaluationsPagination && evaluationsPagination.totalPages > 1 && (
        <div className="flex flex-wrap justify-center gap-2 px-3 sm:px-4 bg-white p-3 rounded-lg shadow-md border border-[#e0f2ef] mt-4 sm:mt-6">
          <Button
            variant="outline"
            size="sm"
            disabled={evaluationsPagination.currentPage <= 1}
            onClick={() => handleEvaluationPageChange(evaluationsPagination.currentPage - 1)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <FaArrowRight className="ml-1" />
            <span className="hidden sm:inline">السابق</span>
          </Button>
          <span className="mx-2 py-2 text-[var(--primary-color)] font-medium text-sm sm:text-base">
            {evaluationsPagination.currentPage} / {evaluationsPagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            disabled={evaluationsPagination.currentPage >= evaluationsPagination.totalPages}
            onClick={() => handleEvaluationPageChange(evaluationsPagination.currentPage + 1)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <span className="hidden sm:inline">التالي</span>
            <FaArrowLeft className="ml-1" />
          </Button>
        </div>
      )}

      {activeTab === 'schedules' && !isLoading && schedulesPagination && schedulesPagination.totalPages > 1 && (
        <div className="flex flex-wrap justify-center gap-2 px-3 sm:px-4 bg-white p-3 rounded-lg shadow-md border border-[#e0f2ef] mt-4 sm:mt-6">
          <Button
            variant="outline"
            size="sm"
            disabled={schedulesPagination.currentPage <= 1}
            onClick={() => handleSchedulePageChange(schedulesPagination.currentPage - 1)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <FaArrowRight className="ml-1" />
            <span className="hidden sm:inline">السابق</span>
          </Button>
          <span className="mx-2 py-2 text-[var(--primary-color)] font-medium text-sm sm:text-base">
            {schedulesPagination.currentPage} / {schedulesPagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            disabled={schedulesPagination.currentPage >= schedulesPagination.totalPages}
            onClick={() => handleSchedulePageChange(schedulesPagination.currentPage + 1)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <span className="hidden sm:inline">التالي</span>
            <FaArrowLeft className="ml-1" />
          </Button>
        </div>
      )}

      {activeTab === 'achievements' && !isLoading && achievementsPagination && achievementsPagination.totalPages > 1 && (
        <div className="flex flex-wrap justify-center gap-2 px-3 sm:px-4 bg-white p-3 rounded-lg shadow-md border border-[#e0f2ef] mt-4 sm:mt-6">
          <Button
            variant="outline"
            size="sm"
            disabled={achievementsPagination.currentPage <= 1}
            onClick={() => handleAchievementPageChange(achievementsPagination.currentPage - 1)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <FaArrowRight className="ml-1" />
            <span className="hidden sm:inline">السابق</span>
          </Button>
          <span className="mx-2 py-2 text-[var(--primary-color)] font-medium text-sm sm:text-base">
            {achievementsPagination.currentPage} / {achievementsPagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            disabled={achievementsPagination.currentPage >= achievementsPagination.totalPages}
            onClick={() => handleAchievementPageChange(achievementsPagination.currentPage + 1)}
            className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
          >
            <span className="hidden sm:inline">التالي</span>
            <FaArrowLeft className="ml-1" />
          </Button>
        </div>
      )}
    </div>
    </OptimizedProtectedRoute>
  )
}
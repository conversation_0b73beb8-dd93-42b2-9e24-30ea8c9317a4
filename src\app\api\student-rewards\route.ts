import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/student-rewards
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const rewardId = searchParams.get('rewardId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // بناء شروط البحث
    const where: {
      studentId?: number;
      rewardId?: number;
      date?: {
        gte?: Date;
        lte?: Date;
      };
    } = {};

    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    if (rewardId) {
      where.rewardId = parseInt(rewardId);
    }

    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    } else if (startDate) {
      where.date = {
        gte: new Date(startDate)
      };
    } else if (endDate) {
      where.date = {
        lte: new Date(endDate)
      };
    }

    const studentRewards = await prisma.studentReward.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true,
            username: true,
            classe: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        reward: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    return NextResponse.json({
      data: studentRewards,
      success: true,
      message: 'تم جلب مكافآت الطلاب بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student rewards:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب مكافآت الطلاب',
      success: false
    }, { status: 500 });
  }
}

// POST /api/student-rewards
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, rewardId } = body;

    // التحقق من البيانات المطلوبة
    if (!studentId || !rewardId) {
      return NextResponse.json({
        error: 'معرف الطالب ومعرف المكافأة مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return NextResponse.json({
        error: 'الطالب غير موجود',
        success: false
      }, { status: 404 });
    }

    // التحقق من وجود المكافأة
    const reward = await prisma.reward.findUnique({
      where: { id: parseInt(rewardId) }
    });

    if (!reward) {
      return NextResponse.json({
        error: 'المكافأة غير موجودة',
        success: false
      }, { status: 404 });
    }

    // التحقق من عدم حصول الطالب على هذه المكافأة من قبل
    const existingReward = await prisma.studentReward.findFirst({
      where: {
        studentId: parseInt(studentId),
        rewardId: parseInt(rewardId)
      }
    });

    if (existingReward) {
      return NextResponse.json({
        error: 'الطالب حصل على هذه المكافأة بالفعل',
        success: false
      }, { status: 400 });
    }

    // التحقق من استحقاق الطالب للمكافأة
    if (student.totalPoints < reward.requiredPoints) {
      return NextResponse.json({
        error: `الطالب لا يستحق هذه المكافأة بعد. المطلوب ${reward.requiredPoints} نقطة والطالب لديه ${student.totalPoints} نقطة فقط`,
        success: false
      }, { status: 400 });
    }

    // منح المكافأة للطالب
    const studentReward = await prisma.studentReward.create({
      data: {
        studentId: parseInt(studentId),
        rewardId: parseInt(rewardId)
      },
      include: {
        student: {
          select: {
            name: true
          }
        },
        reward: {
          select: {
            name: true
          }
        }
      }
    });

    return NextResponse.json({
      data: studentReward,
      success: true,
      message: `تم منح مكافأة "${reward.name}" للطالب "${student.name}" بنجاح`
    });
  } catch (error) {
    console.error('Error creating student reward:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء منح مكافأة للطالب',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/student-rewards
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف مكافأة الطالب مطلوب',
        success: false
      }, { status: 400 });
    }

    // حذف مكافأة الطالب
    await prisma.studentReward.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف مكافأة الطالب بنجاح'
    });
  } catch (error) {
    console.error('Error deleting student reward:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف مكافأة الطالب',
      success: false
    }, { status: 500 });
  }
}

/* ملف الألوان العامة للموقع - سيتم تحديثها من ColorInitializer */

:root {
  /* الألوان الأساسية - ستتم إعادة تعيينها من قاعدة البيانات */
  --primary-color: transparent;
  --secondary-color: transparent;
  --sidebar-color: transparent;
  --background-color: transparent;
  --accent-color: transparent;
  --text-color: transparent;

  /* ألوان إضافية ثابتة */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* ألوان الخلفية - ستتم إعادة تعيينها حسب الوضع */
  --bg-primary: var(--background-color, #ffffff);
  --bg-secondary: var(--background-color, #f9fafb);
  --bg-tertiary: var(--background-color, #f3f4f6);

  /* ألوان النص - ستتم إعادة تعيينها حسب الوضع */
  --text-primary: var(--text-color, #111827);
  --text-secondary: var(--text-color, #6b7280);
  --text-tertiary: var(--text-color, #9ca3af);

  /* ألوان الحدود - ستتم إعادة تعيينها حسب الوضع */
  --border-primary: #475569;
  --border-secondary: #475569;
  --border-tertiary: #475569;
}

/* تطبيق الألوان على العناصر الأساسية */

/* الأزرار الأساسية */
.btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.btn-primary:hover {
  background-color: var(--secondary-color) !important;
  border-color: var(--secondary-color) !important;
}

.btn-secondary {
  background-color: var(--secondary-color) !important;
  border-color: var(--secondary-color) !important;
  color: white !important;
}

.btn-secondary:hover {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.btn-success {
  background-color: var(--success-color) !important;
  border-color: var(--success-color) !important;
  color: white !important;
}

.btn-warning {
  background-color: var(--warning-color) !important;
  border-color: var(--warning-color) !important;
  color: white !important;
}

.btn-error {
  background-color: var(--error-color) !important;
  border-color: var(--error-color) !important;
  color: white !important;
}

.btn-info {
  background-color: var(--info-color) !important;
  border-color: var(--info-color) !important;
  color: white !important;
}

/* الروابط */
.link-primary {
  color: var(--primary-color) !important;
}

.link-primary:hover {
  color: var(--secondary-color) !important;
}

/* الخلفيات */
.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.bg-accent {
  background-color: var(--accent-color) !important;
}

/* النصوص */
.text-primary-color {
  color: var(--primary-color) !important;
}

.text-secondary-color {
  color: var(--secondary-color) !important;
}

/* الحدود */
.border-primary {
  border-color: var(--primary-color) !important;
}

.border-secondary {
  border-color: var(--secondary-color) !important;
}

.border-primary-color {
  border-color: var(--primary-color) !important;
}

.border-secondary-color {
  border-color: var(--secondary-color) !important;
}

/* Hover states */
.hover\:bg-primary-color:hover {
  background-color: var(--primary-color) !important;
}

.hover\:bg-secondary-color:hover {
  background-color: var(--secondary-color) !important;
}

.hover\:text-primary-color:hover {
  color: var(--primary-color) !important;
}

.hover\:text-secondary-color:hover {
  color: var(--secondary-color) !important;
}

/* Focus states */
.focus\:border-primary-color:focus {
  border-color: var(--primary-color) !important;
}

.focus\:ring-primary-color:focus {
  --tw-ring-color: var(--primary-color) !important;
}

/* Data states */
.data-\[state\=active\]\:bg-primary-color[data-state="active"] {
  background-color: var(--primary-color) !important;
}

/* ألوان خفيفة وداكنة مع تباين جيد */
.bg-primary-light {
  background-color: var(--primary-light) !important;
  color: var(--primary-dark) !important; /* نص داكن على خلفية فاتحة */
}

.bg-secondary-light {
  background-color: var(--secondary-light) !important;
  color: var(--secondary-dark) !important; /* نص داكن على خلفية فاتحة */
}

.bg-primary-color {
  background-color: var(--primary-color) !important;
  color: var(--primary-text-color) !important; /* نص متباين على خلفية داكنة */
}

.bg-secondary-color {
  background-color: var(--secondary-color) !important;
  color: var(--secondary-text-color) !important; /* نص متباين على خلفية داكنة */
}

.text-primary-dark {
  color: var(--primary-dark) !important;
}

.text-secondary-dark {
  color: var(--secondary-dark) !important;
}

.text-primary-light {
  color: var(--primary-color) !important;
}

.text-secondary-light {
  color: var(--secondary-color) !important;
}

.border-primary-light {
  border-color: var(--primary-border) !important;
}

.border-secondary-light {
  border-color: var(--secondary-border) !important;
}

/* Hover states للألوان الخفيفة */
.hover\:bg-primary-light:hover {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

.hover\:bg-secondary-light:hover {
  background-color: var(--secondary-light) !important;
  color: var(--secondary-color) !important;
}

.hover\:text-primary-dark:hover {
  color: var(--primary-color) !important;
}

/* مجموعات ألوان للحالات المختلفة */
.success-colors {
  background-color: var(--primary-light) !important;
  color: var(--primary-dark) !important;
  border-color: var(--primary-border) !important;
}

.success-colors-dark {
  background-color: var(--primary-color) !important;
  color: var(--primary-text-color) !important;
  border-color: var(--primary-color) !important;
}

/* إصلاح الألوان الشائعة */
.bg-green-50,
.bg-green-100 {
  background-color: var(--primary-light) !important;
  color: var(--primary-dark) !important;
}

.bg-green-500,
.bg-green-600,
.bg-green-700 {
  background-color: var(--primary-color) !important;
  color: var(--primary-text-color) !important;
}

.text-green-500,
.text-green-600,
.text-green-700 {
  color: var(--primary-color) !important;
}

.text-green-800,
.text-green-900 {
  color: var(--primary-dark) !important;
}

.border-green-100,
.border-green-200,
.border-green-300 {
  border-color: var(--primary-border) !important;
}

.border-green-500,
.border-green-600 {
  border-color: var(--primary-color) !important;
}

/* تخصيص ألوان Tailwind - جميع الألوان الخضراء */
.bg-\[#169b88\],
.bg-green-600,
.bg-green-500,
.bg-emerald-600,
.bg-emerald-500,
.bg-teal-600,
.bg-teal-500 {
  background-color: var(--primary-color) !important;
}

.bg-\[#1ab19c\],
.bg-green-400,
.bg-emerald-400,
.bg-teal-400 {
  background-color: var(--secondary-color) !important;
}

.text-\[#169b88\],
.text-green-600,
.text-green-500,
.text-emerald-600,
.text-emerald-500,
.text-teal-600,
.text-teal-500 {
  color: var(--primary-color) !important;
}

.text-\[#1ab19c\],
.text-green-400,
.text-emerald-400,
.text-teal-400 {
  color: var(--secondary-color) !important;
}

.border-\[#169b88\],
.border-green-600,
.border-green-500,
.border-emerald-600,
.border-emerald-500,
.border-teal-600,
.border-teal-500 {
  border-color: var(--primary-color) !important;
}

.border-\[#1ab19c\],
.border-green-400,
.border-emerald-400,
.border-teal-400 {
  border-color: var(--secondary-color) !important;
}

.hover\:bg-\[#1ab19c\]:hover,
.hover\:bg-green-400:hover,
.hover\:bg-emerald-400:hover,
.hover\:bg-teal-400:hover {
  background-color: var(--secondary-color) !important;
}

.hover\:bg-\[#169b88\]:hover,
.hover\:bg-green-600:hover,
.hover\:bg-green-500:hover,
.hover\:bg-emerald-600:hover,
.hover\:bg-emerald-500:hover,
.hover\:bg-teal-600:hover,
.hover\:bg-teal-500:hover {
  background-color: var(--primary-color) !important;
}

.hover\:text-\[#1ab19c\]:hover,
.hover\:text-green-400:hover,
.hover\:text-emerald-400:hover,
.hover\:text-teal-400:hover {
  color: var(--secondary-color) !important;
}

.hover\:text-\[#169b88\]:hover,
.hover\:text-green-600:hover,
.hover\:text-green-500:hover,
.hover\:text-emerald-600:hover,
.hover\:text-emerald-500:hover,
.hover\:text-teal-600:hover,
.hover\:text-teal-500:hover {
  color: var(--primary-color) !important;
}

.focus\:ring-\[#169b88\]:focus,
.focus\:ring-green-600:focus,
.focus\:ring-green-500:focus,
.focus\:ring-emerald-600:focus,
.focus\:ring-emerald-500:focus,
.focus\:ring-teal-600:focus,
.focus\:ring-teal-500:focus {
  --tw-ring-color: var(--primary-color) !important;
}

.focus\:border-\[#169b88\]:focus,
.focus\:border-green-600:focus,
.focus\:border-green-500:focus,
.focus\:border-emerald-600:focus,
.focus\:border-emerald-500:focus,
.focus\:border-teal-600:focus,
.focus\:border-teal-500:focus {
  border-color: var(--primary-color) !important;
}

/* تخصيص ألوان إضافية */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color)) !important;
}

/* تأثيرات الظلال */
.shadow-primary {
  box-shadow: 0 4px 14px 0 rgba(22, 155, 136, 0.25) !important;
}

.shadow-secondary {
  box-shadow: 0 4px 14px 0 rgba(26, 177, 156, 0.25) !important;
}

/* تخصيص ألوان النماذج */
.form-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(22, 155, 136, 0.1) !important;
}

.form-select:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(22, 155, 136, 0.1) !important;
}

.form-textarea:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(22, 155, 136, 0.1) !important;
}

/* تخصيص ألوان الجداول */
.table-header {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

.table-row:hover {
  background-color: var(--bg-tertiary) !important;
}

/* تخصيص ألوان البطاقات */
.card-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
  color: white !important;
}

.card-border {
  border-color: var(--border-secondary) !important;
}

/* تخصيص ألوان التنبيهات */
.alert-success {
  background-color: rgba(16, 185, 129, 0.1) !important;
  border-color: var(--success-color) !important;
  color: var(--success-color) !important;
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1) !important;
  border-color: var(--warning-color) !important;
  color: var(--warning-color) !important;
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-color: var(--error-color) !important;
  color: var(--error-color) !important;
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: var(--info-color) !important;
  color: var(--info-color) !important;
}

/* قواعد شاملة لجميع الألوان الخضراء في الموقع */
/* استهداف جميع العناصر التي تحتوي على ألوان خضراء */
*[style*="#169b88"],
*[style*="#1ab19c"],
*[style*="rgb(22, 155, 136)"],
*[style*="rgb(26, 177, 156)"] {
  color: var(--primary-color) !important;
}

*[style*="background-color: #169b88"],
*[style*="background-color: #1ab19c"],
*[style*="background-color: rgb(22, 155, 136)"],
*[style*="background-color: rgb(26, 177, 156)"] {
  background-color: var(--primary-color) !important;
}

/* قواعد إضافية لضمان تطبيق الألوان على جميع العناصر */
.bg-primary-color {
  background-color: var(--primary-color) !important;
}

.bg-secondary-color {
  background-color: var(--secondary-color) !important;
}

.bg-sidebar-color {
  background-color: var(--sidebar-color) !important;
}

.text-primary-color {
  color: var(--primary-color) !important;
}

.text-secondary-color {
  color: var(--secondary-color) !important;
}

.border-primary-color {
  border-color: var(--primary-color) !important;
}

.border-secondary-color {
  border-color: var(--secondary-color) !important;
}

/* تطبيق الألوان على جميع الأزرار والعناصر التفاعلية */
button[style*="#169b88"],
button[style*="#1ab19c"],
.btn[style*="#169b88"],
.btn[style*="#1ab19c"] {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

/* تطبيق الألوان على الجرادينت */
*[style*="linear-gradient"][style*="#169b88"],
*[style*="linear-gradient"][style*="#1ab19c"] {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

/* قواعد شاملة إضافية لضمان تطبيق الألوان على جميع أجزاء الموقع */

/* تطبيق الألوان على جميع الروابط والعناصر التفاعلية */
a[style*="#169b88"],
a[style*="#1ab19c"] {
  color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع العناصر التي تحتوي على classes خضراء */
.text-green-600,
.text-green-500,
.text-emerald-600,
.text-emerald-500,
.text-teal-600,
.text-teal-500 {
  color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع الحدود الخضراء */
.border-green-600,
.border-green-500,
.border-emerald-600,
.border-emerald-500,
.border-teal-600,
.border-teal-500 {
  border-color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع الظلال الخضراء */
.shadow-green,
.shadow-emerald,
.shadow-teal {
  box-shadow: 0 4px 14px 0 rgba(var(--primary-color-rgb, 22, 155, 136), 0.25) !important;
}

/* تطبيق الألوان على جميع العناصر التي تحتوي على focus states */
.focus\:ring-green-500:focus,
.focus\:ring-green-600:focus,
.focus\:ring-emerald-500:focus,
.focus\:ring-emerald-600:focus,
.focus\:ring-teal-500:focus,
.focus\:ring-teal-600:focus {
  --tw-ring-color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع العناصر التي تحتوي على hover states */
.hover\:bg-green-600:hover,
.hover\:bg-green-500:hover,
.hover\:bg-emerald-600:hover,
.hover\:bg-emerald-500:hover,
.hover\:bg-teal-600:hover,
.hover\:bg-teal-500:hover {
  background-color: var(--secondary-color) !important;
}

.hover\:text-green-600:hover,
.hover\:text-green-500:hover,
.hover\:text-emerald-600:hover,
.hover\:text-emerald-500:hover,
.hover\:text-teal-600:hover,
.hover\:text-teal-500:hover {
  color: var(--secondary-color) !important;
}

/* تطبيق الألوان على جميع العناصر التي تحتوي على active states */
.active\:bg-green-600:active,
.active\:bg-green-500:active,
.active\:bg-emerald-600:active,
.active\:bg-emerald-500:active,
.active\:bg-teal-600:active,
.active\:bg-teal-500:active {
  background-color: var(--accent-color) !important;
}

/* تطبيق الألوان على جميع العناصر المخصصة */
.custom-green,
.custom-primary {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.custom-green-text,
.custom-primary-text {
  color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع الأيقونات والرموز */
.icon-green,
.icon-primary {
  color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع البطاقات والمكونات */
.card-green,
.card-primary {
  border-color: var(--primary-color) !important;
}

.card-green .card-header,
.card-primary .card-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
  color: white !important;
}

/* تطبيق الألوان على جميع النماذج والمدخلات */
.form-green input:focus,
.form-primary input:focus,
.form-green select:focus,
.form-primary select:focus,
.form-green textarea:focus,
.form-primary textarea:focus {
  border-color: var(--primary-color) !important;
  --tw-ring-color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع التنبيهات والرسائل */
.alert-green,
.alert-primary {
  background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع شرائط التقدم */
.progress-green .progress-bar,
.progress-primary .progress-bar {
  background-color: var(--primary-color) !important;
}

/* تطبيق الألوان على جميع التبويبات */
.tab-green.active,
.tab-primary.active {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* تطبيق الألوان على جميع القوائم المنسدلة */
.dropdown-green .dropdown-item:hover,
.dropdown-primary .dropdown-item:hover {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* تطبيق الألوان على جميع الأزرار المخصصة */
.btn-custom-green,
.btn-custom-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: white !important;
}

.btn-custom-green:hover,
.btn-custom-primary:hover {
  background-color: var(--secondary-color) !important;
  border-color: var(--secondary-color) !important;
}

/* تطبيق الألوان على جميع الجداول */
.table-green thead,
.table-primary thead {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.table-green tbody tr:hover,
.table-primary tbody tr:hover {
  background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1) !important;
}

/* استهداف جميع فئات Tailwind الخضراء */
.bg-green-50 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.05) !important; }
.bg-green-100 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1) !important; }
.bg-green-200 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.2) !important; }
.bg-green-300 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.3) !important; }
.bg-green-400 { background-color: var(--secondary-color) !important; }
.bg-green-500 { background-color: var(--primary-color) !important; }
.bg-green-600 { background-color: var(--primary-color) !important; }
.bg-green-700 { background-color: var(--primary-color) !important; }
.bg-green-800 { background-color: var(--primary-color) !important; }
.bg-green-900 { background-color: var(--primary-color) !important; }

/* ألوان النص الخضراء - مع ضمان التباين للعناصر التي لها خلفية خضراء */
.text-green-50 { color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.05) !important; }
.text-green-100 { color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1) !important; }
.text-green-200 { color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.2) !important; }
.text-green-300 { color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.3) !important; }
.text-green-400 { color: var(--secondary-color) !important; }
.text-green-500 { color: var(--primary-color) !important; }
.text-green-600 { color: var(--primary-color) !important; }
.text-green-700 { color: var(--primary-color) !important; }

/* ألوان النص للعناصر التي لها خلفية خضراء - تطبيق اللون الأبيض للتباين */
[class*="bg-green-100"].text-green-800,
[class*="bg-green-200"].text-green-800,
[class*="bg-green-300"].text-green-800,
[class*="bg-green-400"].text-green-800,
[class*="bg-green-500"].text-green-800,
[class*="bg-green-600"].text-green-800,
[class*="bg-green-700"].text-green-800,
[class*="bg-green-800"].text-green-800,
[class*="bg-green-900"].text-green-800 {
  color: white !important;
}

/* ألوان النص العادية للعناصر بدون خلفية خضراء */
.text-green-800:not([class*="bg-green"]) { color: var(--primary-color) !important; }
.text-green-900:not([class*="bg-green"]) { color: var(--primary-color) !important; }

.border-green-50 { border-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.05) !important; }
.border-green-100 { border-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1) !important; }
.border-green-200 { border-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.2) !important; }
.border-green-300 { border-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.3) !important; }
.border-green-400 { border-color: var(--secondary-color) !important; }
.border-green-500 { border-color: var(--primary-color) !important; }
.border-green-600 { border-color: var(--primary-color) !important; }
.border-green-700 { border-color: var(--primary-color) !important; }
.border-green-800 { border-color: var(--primary-color) !important; }
.border-green-900 { border-color: var(--primary-color) !important; }

/* استهداف جميع فئات Emerald */
.bg-emerald-50 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.05) !important; }
.bg-emerald-100 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1) !important; }
.bg-emerald-200 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.2) !important; }
.bg-emerald-300 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.3) !important; }
.bg-emerald-400 { background-color: var(--secondary-color) !important; }
.bg-emerald-500 { background-color: var(--primary-color) !important; }
.bg-emerald-600 { background-color: var(--primary-color) !important; }
.bg-emerald-700 { background-color: var(--primary-color) !important; }
.bg-emerald-800 { background-color: var(--primary-color) !important; }
.bg-emerald-900 { background-color: var(--primary-color) !important; }

/* استهداف جميع فئات Teal */
.bg-teal-50 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.05) !important; }
.bg-teal-100 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.1) !important; }
.bg-teal-200 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.2) !important; }
.bg-teal-300 { background-color: rgba(var(--primary-color-rgb, 22, 155, 136), 0.3) !important; }
.bg-teal-400 { background-color: var(--secondary-color) !important; }
.bg-teal-500 { background-color: var(--primary-color) !important; }
.bg-teal-600 { background-color: var(--primary-color) !important; }
.bg-teal-700 { background-color: var(--primary-color) !important; }
.bg-teal-800 { background-color: var(--primary-color) !important; }
.bg-teal-900 { background-color: var(--primary-color) !important; }

/* قواعد التباين للألوان الأخرى */
[class*="bg-emerald-100"][class*="text-emerald"],
[class*="bg-emerald-200"][class*="text-emerald"],
[class*="bg-emerald-300"][class*="text-emerald"],
[class*="bg-emerald-400"][class*="text-emerald"],
[class*="bg-emerald-500"][class*="text-emerald"],
[class*="bg-emerald-600"][class*="text-emerald"],
[class*="bg-emerald-700"][class*="text-emerald"],
[class*="bg-emerald-800"][class*="text-emerald"],
[class*="bg-emerald-900"][class*="text-emerald"],
[class*="bg-teal-100"][class*="text-teal"],
[class*="bg-teal-200"][class*="text-teal"],
[class*="bg-teal-300"][class*="text-teal"],
[class*="bg-teal-400"][class*="text-teal"],
[class*="bg-teal-500"][class*="text-teal"],
[class*="bg-teal-600"][class*="text-teal"],
[class*="bg-teal-700"][class*="text-teal"],
[class*="bg-teal-800"][class*="text-teal"],
[class*="bg-teal-900"][class*="text-teal"] {
  color: white !important;
}

/* قواعد خاصة للحالات الشائعة */
.bg-green-100.text-green-800 {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.bg-green-600.text-white,
.bg-green-700.text-white {
  background-color: var(--primary-color) !important;
  color: white !important;
}

import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import { verifyToken } from '@/utils/verifyToken';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

interface ChangePasswordDto {
    currentPassword: string;
    newPassword: string;
}

export async function POST(request: NextRequest) {
    console.log("userId555555555555555555555555555")
    try {
        const userId = await verifyToken(request);
        console.log("userId",userId?.id)
        if (!userId) {
            return NextResponse.json(
                { message: "Unauthorized" },
                { status: 401 }
            );
        }
       
        const body = await request.json() as ChangePasswordDto;

        const user = await prisma.user.findUnique({
            where: { id: userId.id }
        });

        if (!user) {
            return NextResponse.json(
                { message: "User not found" },
                { status: 404 }
            );
        }
console.log(body.currentPassword)
        if (!body.currentPassword || !body.newPassword) {
            return NextResponse.json(
                { message: "Both current and new passwords are required" },
                { status: 400 }
            );
        }

        if (!user.password) {
            return NextResponse.json(
                { message: "User password not found" },
                { status: 400 }
            );
        }
        // Verify current password
        const isPasswordMatch = await bcrypt.compare(body.currentPassword, user.password);
        if (!isPasswordMatch) {
            return NextResponse.json(
                { message: "Current password is incorrect" },
                { status: 400 }
            );
        }

        // Hash new password
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(body.newPassword, salt);
console.log(hashedPassword,userId.id )
        // Update password
        await prisma.user.update({
            where: { id: userId.id },
            data: { password: hashedPassword }
        });

        return NextResponse.json(
            { message: "Password updated successfully" },
            { status: 200 }
        );
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { message: "Internal server error" },
            { status: 500 }
        );
    }
}
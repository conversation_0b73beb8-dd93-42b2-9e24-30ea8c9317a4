import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/curriculum/units/[id] - الحصول على وحدة محددة
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف الوحدة غير صالح" },
        { status: 400 }
      );
    }

    const unit = await prisma.curriculumUnit.findUnique({
      where: { id },
      include: {
        lessons: {
          include: {
            resources: true
          },
          orderBy: {
            order: 'asc'
          }
        }
      }
    });

    if (!unit) {
      return NextResponse.json(
        { message: "الوحدة غير موجودة" },
        { status: 404 }
      );
    }

    return NextResponse.json(unit);
  } catch (error) {
    console.error('Error fetching unit:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب الوحدة" },
      { status: 500 }
    );
  }
}

// PUT /api/curriculum/units/[id] - تحديث وحدة محددة
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف الوحدة غير صالح" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { title, description, order } = body;

    if (!title) {
      return NextResponse.json(
        { message: "يجب توفير عنوان الوحدة" },
        { status: 400 }
      );
    }

    // التحقق من وجود الوحدة
    const unit = await prisma.curriculumUnit.findUnique({
      where: { id },
    });

    if (!unit) {
      return NextResponse.json(
        { message: "الوحدة غير موجودة" },
        { status: 404 }
      );
    }

    // تحديث الوحدة
    const updatedUnit = await prisma.curriculumUnit.update({
      where: { id },
      data: {
        title,
        description,
        order: order || unit.order,
      },
    });

    return NextResponse.json(updatedUnit);
  } catch (error) {
    console.error('Error updating unit:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث الوحدة" },
      { status: 500 }
    );
  }
}

// DELETE /api/curriculum/units/[id] - حذف وحدة محددة
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف الوحدة غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود الوحدة
    const unit = await prisma.curriculumUnit.findUnique({
      where: { id },
      include: {
        subject: true
      }
    });

    if (!unit) {
      return NextResponse.json(
        { message: "الوحدة غير موجودة" },
        { status: 404 }
      );
    }

    // حذف الوحدة (سيتم حذف الدروس والموارد تلقائيًا بسبب onDelete: Cascade)
    await prisma.curriculumUnit.delete({
      where: { id },
    });

    // التحقق مما إذا كانت هذه آخر وحدة في المادة
    const remainingUnits = await prisma.curriculumUnit.count({
      where: { subjectId: unit.subjectId }
    });

    // إذا لم تعد هناك وحدات، قم بتحديث حالة المادة
    if (remainingUnits === 0) {
      await prisma.subject.update({
        where: { id: unit.subjectId },
        data: { hasStudyPlan: false },
      });
    }

    return NextResponse.json({ message: "تم حذف الوحدة بنجاح" });
  } catch (error) {
    console.error('Error deleting unit:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف الوحدة" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/honor-criteria
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (id) {
      // جلب معيار محدد
      const criteria = await prisma.honorCriteria.findUnique({
        where: { id: parseInt(id) }
      });
      
      if (!criteria) {
        return NextResponse.json({
          error: 'معيار التقييم غير موجود',
          success: false
        }, { status: 404 });
      }
      
      return NextResponse.json({
        data: criteria,
        success: true,
        message: 'تم جلب معيار التقييم بنجاح'
      });
    } else {
      // جلب جميع المعايير
      const criteria = await prisma.honorCriteria.findMany({
        orderBy: {
          pointsThreshold: 'asc'
        }
      });
      
      return NextResponse.json({
        data: criteria,
        success: true,
        message: 'تم جلب معايير التقييم بنجاح'
      });
    }
  } catch (error) {
    console.error('Error fetching honor criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب معايير التقييم',
      success: false
    }, { status: 500 });
  }
}

// POST /api/honor-criteria
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, pointsThreshold, isActive } = body;
    
    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json({
        error: 'اسم المعيار مطلوب',
        success: false
      }, { status: 400 });
    }
    
    // إنشاء معيار جديد
    const criteria = await prisma.honorCriteria.create({
      data: {
        name,
        description: description || '',
        pointsThreshold: pointsThreshold || 0,
        isActive: isActive !== undefined ? isActive : true
      }
    });
    
    return NextResponse.json({
      data: criteria,
      success: true,
      message: 'تم إنشاء معيار التقييم بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating honor criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إنشاء معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

// PATCH /api/honor-criteria?id=123
export async function PATCH(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({
        error: 'معرف المعيار مطلوب',
        success: false
      }, { status: 400 });
    }
    
    const body = await request.json();
    const { name, description, pointsThreshold, isActive } = body;
    
    // التحقق من وجود المعيار
    const existingCriteria = await prisma.honorCriteria.findUnique({
      where: { id: parseInt(id) }
    });
    
    if (!existingCriteria) {
      return NextResponse.json({
        error: 'معيار التقييم غير موجود',
        success: false
      }, { status: 404 });
    }
    
    // تحديث المعيار
    const updatedCriteria = await prisma.honorCriteria.update({
      where: { id: parseInt(id) },
      data: {
        name: name !== undefined ? name : existingCriteria.name,
        description: description !== undefined ? description : existingCriteria.description,
        pointsThreshold: pointsThreshold !== undefined ? pointsThreshold : existingCriteria.pointsThreshold,
        isActive: isActive !== undefined ? isActive : existingCriteria.isActive
      }
    });
    
    return NextResponse.json({
      data: updatedCriteria,
      success: true,
      message: 'تم تحديث معيار التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error updating honor criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/honor-criteria?id=123
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({
        error: 'معرف المعيار مطلوب',
        success: false
      }, { status: 400 });
    }
    
    // التحقق من وجود المعيار
    const existingCriteria = await prisma.honorCriteria.findUnique({
      where: { id: parseInt(id) }
    });
    
    if (!existingCriteria) {
      return NextResponse.json({
        error: 'معيار التقييم غير موجود',
        success: false
      }, { status: 404 });
    }
    
    // حذف المعيار
    await prisma.honorCriteria.delete({
      where: { id: parseInt(id) }
    });
    
    return NextResponse.json({
      success: true,
      message: 'تم حذف معيار التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting honor criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

"use client";
import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  FaUser,
  FaBook,
  FaCalendarAlt,
  FaTrophy,
  // FaChartLine,
  FaArrowRight
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface Student {
  id: number;
  name: string;
  username: string;
  age: number;
  phone?: string;
  classe?: {
    id: number;
    name: string;
  };
  guardian?: {
    id: number;
    name: string;
  };
  totalPoints: number;
}

interface Attendance {
  id: number;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'EXCUSED';
  hisass: number;
}

interface QuranProgress {
  id: number;
  surahName: string;
  fromAyah: number;
  toAyah: number;
  date: string;
  grade: number;
  notes?: string;
}

const StudentDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const studentId = params?.id as string;

  const [student, setStudent] = useState<Student | null>(null);
  const [attendance, setAttendance] = useState<Attendance[]>([]);
  const [quranProgress, setQuranProgress] = useState<QuranProgress[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStudentDetails = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب بيانات الطالب
        const response = await fetch(`/api/students/${studentId}`);

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات الطالب');
        }

        const data = await response.json();
        setStudent(data);

        // جلب سجل الحضور
        const attendanceResponse = await fetch(`/api/attendance?studentId=${studentId}`);
        if (attendanceResponse.ok) {
          const attendanceData = await attendanceResponse.json();
          setAttendance(attendanceData);
        }

        // جلب تقدم حفظ القرآن
        const progressResponse = await fetch(`/api/quran-progress?studentId=${studentId}`);
        if (progressResponse.ok) {
          const progressData = await progressResponse.json();
          setQuranProgress(progressData);
        }
      } catch (err: unknown) {
        console.error('Error fetching student details:', err);
        setError('حدث خطأ أثناء جلب بيانات الطالب');
        toast.error('فشل في جلب بيانات الطالب');
      } finally {
        setIsLoading(false);
      }
    };

    if (studentId) {
      fetchStudentDetails();
    }
  }, [studentId]);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
      </div>
    );
  }

  if (error || !student) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">
              {error || 'لم يتم العثور على بيانات الطالب'}
            </div>
            <div className="flex justify-center mt-4">
              <Button onClick={() => router.back()}>
                <FaArrowRight className="ml-2" />
                العودة
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">تفاصيل الطالب</h1>
        <Button variant="outline" onClick={() => router.back()}>
          <FaArrowRight className="ml-2" />
          العودة
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FaUser className="text-[var(--primary-color)]" />
            <span>معلومات الطالب</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-500 mb-1">الاسم</h3>
              <p className="text-lg">{student.name}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-500 mb-1">اسم المستخدم</h3>
              <p className="text-lg">{student.username}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-500 mb-1">العمر</h3>
              <p className="text-lg">{student.age} سنة</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-500 mb-1">رقم الهاتف</h3>
              <p className="text-lg">{student.phone || '-'}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-500 mb-1">الفصل</h3>
              <p className="text-lg">{student.classe?.name || '-'}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-500 mb-1">ولي الأمر</h3>
              <p className="text-lg">{student.guardian?.name || '-'}</p>
            </div>
            <div>
              <h3 className="font-semibold text-gray-500 mb-1">مجموع النقاط</h3>
              <p className="text-lg font-bold text-primary-color">{student.totalPoints}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="attendance">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="attendance" className="flex items-center gap-2">
            <FaCalendarAlt />
            <span>سجل الحضور</span>
          </TabsTrigger>
          <TabsTrigger value="progress" className="flex items-center gap-2">
            <FaBook />
            <span>تقدم الحفظ</span>
          </TabsTrigger>
          <TabsTrigger value="achievements" className="flex items-center gap-2">
            <FaTrophy />
            <span>الإنجازات</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="attendance">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>سجل الحضور</span>
                <Link href={`/teachers/attendance?studentId=${student.id}`}>
                  <Button size="sm">
                    تسجيل حضور جديد
                  </Button>
                </Link>
              </CardTitle>
              <CardDescription>
                سجل حضور وغياب الطالب
              </CardDescription>
            </CardHeader>
            <CardContent>
              {attendance.length === 0 ? (
                <div className="text-center text-gray-500 py-4">
                  لا يوجد سجلات حضور
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border p-2 text-right">التاريخ</th>
                        <th className="border p-2 text-right">الحصة</th>
                        <th className="border p-2 text-right">الحالة</th>
                      </tr>
                    </thead>
                    <tbody>
                      {attendance.map((record) => (
                        <tr key={record.id}>
                          <td className="border p-2">
                            {new Date(record.date).toLocaleDateString('fr-FR')}
                          </td>
                          <td className="border p-2">{record.hisass}</td>
                          <td className="border p-2">
                            {record.status === 'PRESENT' && (
                              <span className="text-primary-color">حاضر</span>
                            )}
                            {record.status === 'ABSENT' && (
                              <span className="text-red-600">غائب</span>
                            )}
                            {record.status === 'EXCUSED' && (
                              <span className="text-yellow-600">غائب بعذر</span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>تقدم حفظ القرآن</span>
                <Link href={`/teachers/quran-progress/add?studentId=${student.id}`}>
                  <Button size="sm">
                    إضافة تقدم جديد
                  </Button>
                </Link>
              </CardTitle>
              <CardDescription>
                سجل تقدم الطالب في حفظ القرآن الكريم
              </CardDescription>
            </CardHeader>
            <CardContent>
              {quranProgress.length === 0 ? (
                <div className="text-center text-gray-500 py-4">
                  لا يوجد سجلات تقدم
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border p-2 text-right">التاريخ</th>
                        <th className="border p-2 text-right">السورة</th>
                        <th className="border p-2 text-right">من آية</th>
                        <th className="border p-2 text-right">إلى آية</th>
                        <th className="border p-2 text-right">التقدير</th>
                        <th className="border p-2 text-right">ملاحظات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {quranProgress.map((progress) => (
                        <tr key={progress.id}>
                          <td className="border p-2">
                            {new Date(progress.date).toLocaleDateString('fr-FR')}
                          </td>
                          <td className="border p-2">{progress.surahName}</td>
                          <td className="border p-2">{progress.fromAyah}</td>
                          <td className="border p-2">{progress.toAyah}</td>
                          <td className="border p-2">{progress.grade}/10</td>
                          <td className="border p-2">{progress.notes || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="achievements">
          <Card>
            <CardHeader>
              <CardTitle>إنجازات الطالب</CardTitle>
              <CardDescription>
                الإنجازات والجوائز التي حصل عليها الطالب
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center text-gray-500 py-4">
                لا توجد إنجازات مسجلة بعد
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StudentDetailsPage;

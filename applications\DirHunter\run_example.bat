@echo off
echo DirHunter - Directory and File Hunter
echo =====================================
echo.
echo This is an example script to run DirHunter
echo.

echo Checking Ring installation...
ring -v
if errorlevel 1 (
    echo Error: Ring programming language not found!
    echo Please install Ring from: https://ring-lang.github.io/
    pause
    exit /b 1
)

echo.
echo Checking required files...
if not exist "..\..\praetorian.ring" (
    echo Error: Praetorian.ring library not found!
    echo Please ensure you're running from the correct directory.
    pause
    exit /b 1
)

if not exist "wordlist.txt" (
    echo Error: wordlist.txt not found!
    echo Please ensure the wordlist file exists.
    pause
    exit /b 1
)

echo.
echo Example 1: Basic scan with default settings
echo Command: ring DirHunter.ring -u http://httpbin.org -w wordlist.txt
echo.
set /p choice="Run this example? (y/n): "
if /i "%choice%"=="y" (
    ring DirHunter.ring -u http://httpbin.org -w wordlist.txt
    echo.
)

echo.
echo Example 2: Advanced scan with custom extensions
echo Command: ring DirHunter.ring -u http://httpbin.org -w wordlist.txt -x php,html,txt -t 5
echo.
set /p choice="Run this example? (y/n): "
if /i "%choice%"=="y" (
    ring DirHunter.ring -u http://httpbin.org -w wordlist.txt -x php,html,txt -t 5
    echo.
)

echo.
echo Example 3: Show help
echo Command: ring DirHunter.ring -h
echo.
set /p choice="Show help? (y/n): "
if /i "%choice%"=="y" (
    ring DirHunter.ring -h
    echo.
)

echo.
echo You can also run DirHunter manually with your own parameters:
echo ring DirHunter.ring -u [URL] -w [WORDLIST] [OPTIONS]
echo.
echo Available options:
echo   -u, --url [URL]        Target URL (required)
echo   -w, --wordlist [PATH]  Path to wordlist file (required)
echo   -t, --threads [NUM]    Number of threads (default: 10)
echo   -x, --extensions [EXT] File extensions (comma-separated)
echo   -v, --verbose          Verbose output
echo   -h, --help             Show help
echo.

pause

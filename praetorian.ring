/*
==============================================================================
    مكتبة Praetorian.ring - مجموعة أدوات اختبار الاختراق الاحترافية للغة Ring
    
    الإصدار: 1.0.0
    المؤلف: Praetorian Team
    الترخيص: MIT License
    
    الوصف: مكتبة شاملة لاختبار الاختراق تحتوي على أدوات للشبكات والويب والتشفير
    
    الاستخدام:
        load "praetorian.ring"
        # استخدم الوظائف المختلفة للمكتبة
==============================================================================
*/

# تحديد إصدار المكتبة
PRAETORIAN_VERSION = "1.1.0"

/*
==============================================================================
    دوال مساعدة عامة
==============================================================================
*/

/*
دالة iif - بديل للعامل الثلاثي
*/
func iif bCondition, vTrueValue, vFalseValue
    if bCondition
        return vTrueValue
    else
        return vFalseValue
    ok

# تحميل المكتبات الأساسية المطلوبة (مع معالجة الأخطاء)
try
    load "openssllib.ring"
catch
    ? "تحذير: لم يتم العثور على openssllib.ring - بعض وظائف التشفير قد لا تعمل"
done

try
    load "sockets.ring"
catch
    ? "تحذير: لم يتم العثور على sockets.ring - وظائف الشبكة قد لا تعمل"
done

try
    load "libcurl.ring"
catch
    ? "تحذير: لم يتم العثور على libcurl.ring - عميل HTTP قد لا يعمل"
done

try
    load "threads.ring"
catch
    ? "تحذير: لم يتم العثور على threads.ring - التعدد قد لا يعمل"
done

# تحميل وحدات المكتبة الأساسية
load "core/utils.ring"
load "core/logger.ring"

# تحميل وحدات الشبكة
load "network/scanner.ring"
load "network/packet_crafter.ring"

# تحميل وحدات الويب
load "web/http_client.ring"
load "web/crawler.ring"
load "web/fuzzer.ring"

# تحميل وحدات التشفير
load "crypto/ssl_checker.ring"

/*
==============================================================================
    الكلاس الرئيسي للمكتبة - Praetorian
==============================================================================
*/

class Praetorian

    # خصائص المكتبة
    cVersion = PRAETORIAN_VERSION
    oLogger = NULL
    
    # الوحدات الفرعية
    Network = NULL
    Web = NULL
    Crypto = NULL
    
    /*
    دالة البناء - تهيئة المكتبة
    */
    func init
        # إنشاء نظام التسجيل
        oLogger = new PraetorianLogger
        
        # تهيئة الوحدات الفرعية
        Network = new PraetorianNetwork
        Web = new PraetorianWeb  
        Crypto = new PraetorianCrypto
        
        # تسجيل بداية تشغيل المكتبة
        oLogger.info("تم تهيئة مكتبة Praetorian.ring الإصدار " + cVersion)
    
    /*
    الحصول على إصدار المكتبة
    المخرجات: نص يحتوي على رقم الإصدار
    */
    func getVersion
        return cVersion
    
    /*
    الحصول على معلومات المكتبة
    المخرجات: قائمة تحتوي على معلومات المكتبة
    */
    func getInfo
        aInfo = [
            :name = "Praetorian.ring",
            :version = cVersion,
            :description = "مجموعة أدوات اختبار الاختراق الاحترافية للغة Ring",
            :author = "Praetorian Team",
            :license = "MIT License",
            :modules = [
                "Network Scanner",
                "Packet Crafter", 
                "HTTP Client",
                "Web Crawler",
                "Web Fuzzer",
                "SSL Checker"
            ]
        ]
        return aInfo
    
    /*
    طباعة معلومات المكتبة
    */
    func printInfo
        aInfo = getInfo()
        ? "=============================================="
        ? "مكتبة " + aInfo[:name] + " - الإصدار " + aInfo[:version]
        ? "=============================================="
        ? "الوصف: " + aInfo[:description]
        ? "المؤلف: " + aInfo[:author]
        ? "الترخيص: " + aInfo[:license]
        ? ""
        ? "الوحدات المتاحة:"
        for cModule in aInfo[:modules]
            ? "  - " + cModule
        next
        ? "=============================================="

/*
==============================================================================
    الكلاس الرئيسي لوحدة الشبكة
==============================================================================
*/

class PraetorianNetwork
    
    Scanner = NULL
    PacketCrafter = NULL
    
    func init
        Scanner = new PraetorianNetworkScanner
        PacketCrafter = new PraetorianPacketCrafter

/*
==============================================================================
    الكلاس الرئيسي لوحدة الويب
==============================================================================
*/

class PraetorianWeb
    
    HTTPClient = NULL
    Crawler = NULL
    Fuzzer = NULL
    
    func init
        HTTPClient = new PraetorianHTTPClient
        Crawler = new PraetorianWebCrawler
        Fuzzer = new PraetorianWebFuzzer

/*
==============================================================================
    الكلاس الرئيسي لوحدة التشفير
==============================================================================
*/

class PraetorianCrypto
    
    SSLChecker = NULL
    
    func init
        SSLChecker = new PraetorianSSLChecker

/*
==============================================================================
    دوال مساعدة عامة
==============================================================================
*/

/*
إنشاء مثيل جديد من المكتبة
المخرجات: كائن Praetorian جديد
*/
func CreatePraetorian
    return new Praetorian

/*
التحقق من توفر المكتبات المطلوبة
المخرجات: تقرير حالة المكتبات
*/
func checkLibraryDependencies
    aLibraryStatus = [
        :openssl = false,
        :sockets = false,
        :libcurl = false,
        :threads = false
    ]

    # فحص openssllib
    try
        md5("test")
        aLibraryStatus[:openssl] = true
    catch
        aLibraryStatus[:openssl] = false
    done

    # فحص sockets (محاولة إنشاء مقبس)
    try
        sock = socket(AF_INET, SOCK_STREAM)
        if sock != NULL
            close(sock)
            aLibraryStatus[:sockets] = true
        ok
    catch
        aLibraryStatus[:sockets] = false
    done

    # فحص libcurl
    try
        curl = curl_easy_init()
        if curl != NULL
            curl_easy_cleanup(curl)
            aLibraryStatus[:libcurl] = true
        ok
    catch
        aLibraryStatus[:libcurl] = false
    done

    # فحص threads (مبسط)
    aLibraryStatus[:threads] = true  # افتراض أنه متاح

    return aLibraryStatus

/*
طباعة رسالة ترحيب بالمكتبة
*/
func PraetorianWelcome
    ? ""
    ? "  ____                _             _             "
    ? " |  _ \ _ __ __ _  ___| |_ ___  _ __(_) __ _ _ __   "
    ? " | |_) | '__/ _` |/ _ \ __/ _ \| '__| |/ _` | '_ \  "
    ? " |  __/| | | (_| |  __/ || (_) | |  | | (_| | | | | "
    ? " |_|   |_|  \__,_|\___|\__\___/|_|  |_|\__,_|_| |_| "
    ? ""
    ? "        مكتبة أدوات اختبار الاختراق الاحترافية"
    ? "                  للغة Ring"
    ? ""
    ? "الإصدار: " + PRAETORIAN_VERSION
    ? "=============================================="

    # فحص المكتبات المطلوبة
    aLibStatus = checkLibraryDependencies()
    ? "حالة المكتبات المطلوبة:"
    cOpenSSL = iif(aLibStatus[:openssl], "✓ متاح", "✗ غير متاح")
    cSockets = iif(aLibStatus[:sockets], "✓ متاح", "✗ غير متاح")
    cLibCURL = iif(aLibStatus[:libcurl], "✓ متاح", "✗ غير متاح")
    cThreads = iif(aLibStatus[:threads], "✓ متاح", "✗ غير متاح")
    ? "OpenSSL: " + cOpenSSL
    ? "Sockets: " + cSockets
    ? "LibCURL: " + cLibCURL
    ? "Threads: " + cThreads
    ? "=============================================="
    ? ""

# طباعة رسالة الترحيب عند تحميل المكتبة
PraetorianWelcome()

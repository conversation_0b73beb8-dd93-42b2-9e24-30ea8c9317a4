#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// ألوان للطباعة الملونة في الكونسول
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// قائمة الاستبدالات للألوان الخفيفة
const lightColorReplacements = [
  // الخلفيات الخفيفة
  { from: 'bg-green-50', to: 'bg-primary-light', type: 'class' },
  { from: 'bg-green-100', to: 'bg-primary-light', type: 'class' },
  { from: 'bg-emerald-50', to: 'bg-primary-light', type: 'class' },
  { from: 'bg-emerald-100', to: 'bg-primary-light', type: 'class' },
  { from: 'bg-teal-50', to: 'bg-primary-light', type: 'class' },
  { from: 'bg-teal-100', to: 'bg-primary-light', type: 'class' },
  
  // النصوص الداكنة
  { from: 'text-green-700', to: 'text-primary-dark', type: 'class' },
  { from: 'text-green-800', to: 'text-primary-dark', type: 'class' },
  { from: 'text-green-900', to: 'text-primary-dark', type: 'class' },
  { from: 'text-emerald-700', to: 'text-primary-dark', type: 'class' },
  { from: 'text-emerald-800', to: 'text-primary-dark', type: 'class' },
  { from: 'text-teal-700', to: 'text-primary-dark', type: 'class' },
  { from: 'text-teal-800', to: 'text-primary-dark', type: 'class' },
  
  // الحدود الخفيفة
  { from: 'border-green-100', to: 'border-primary-light', type: 'class' },
  { from: 'border-green-200', to: 'border-primary-light', type: 'class' },
  { from: 'border-green-300', to: 'border-primary-light', type: 'class' },
  { from: 'border-emerald-100', to: 'border-primary-light', type: 'class' },
  { from: 'border-emerald-200', to: 'border-primary-light', type: 'class' },
  { from: 'border-teal-100', to: 'border-primary-light', type: 'class' },
  { from: 'border-teal-200', to: 'border-primary-light', type: 'class' },
  
  // Hover states للألوان الخفيفة
  { from: 'hover:bg-green-50', to: 'hover:bg-primary-light', type: 'class' },
  { from: 'hover:bg-green-100', to: 'hover:bg-primary-light', type: 'class' },
  { from: 'hover:bg-green-200', to: 'hover:bg-primary-light', type: 'class' },
  { from: 'hover:text-green-700', to: 'hover:text-primary-dark', type: 'class' },
  { from: 'hover:text-green-800', to: 'hover:text-primary-dark', type: 'class' },
];

// قائمة المجلدات المراد البحث فيها
const searchDirs = [
  'src/components',
  'src/app',
];

// قائمة امتدادات الملفات المراد البحث فيها
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js'];

let totalFiles = 0;
let fixedFiles = 0;
let totalFixes = 0;

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let fileFixCount = 0;

    lightColorReplacements.forEach(replacement => {
      const regex = new RegExp(escapeRegExp(replacement.from), 'g');
      const matches = content.match(regex);
      if (matches) {
        content = content.replace(regex, replacement.to);
        fileFixCount += matches.length;
        console.log(`  ${colors.green}✓${colors.reset} ${replacement.from} → ${replacement.to} (${matches.length} مرة)`);
      }
    });

    if (fileFixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      fixedFiles++;
      totalFixes += fileFixCount;
      console.log(`${colors.bright}${colors.blue}📁 ${filePath}${colors.reset} - ${colors.green}${fileFixCount} إصلاح${colors.reset}`);
    }

    return fileFixCount;
  } catch (error) {
    console.error(`${colors.red}خطأ في إصلاح الملف ${filePath}: ${error.message}${colors.reset}`);
    return 0;
  }
}

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function searchInDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`${colors.yellow}المجلد غير موجود: ${dirPath}${colors.reset}`);
    return;
  }

  const items = fs.readdirSync(dirPath);

  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory()) {
      // تجاهل مجلدات معينة
      if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(item)) {
        searchInDirectory(itemPath);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (fileExtensions.includes(ext)) {
        totalFiles++;
        fixFile(itemPath);
      }
    }
  });
}

function generateReport() {
  console.log(`\n${colors.bright}${colors.cyan}📊 تقرير إصلاح الألوان الخفيفة${colors.reset}`);
  console.log('==================================================');
  console.log(`📁 إجمالي الملفات المفحوصة: ${totalFiles}`);
  console.log(`${colors.green}✅ الملفات المُصلحة: ${fixedFiles}${colors.reset}`);
  console.log(`${colors.green}🔧 إجمالي الإصلاحات: ${totalFixes}${colors.reset}`);
  
  if (totalFixes > 0) {
    console.log(`\n${colors.bright}${colors.green}✅ تم إصلاح الألوان الخفيفة بنجاح!${colors.reset}`);
    console.log(`${colors.yellow}💡 تذكر:${colors.reset}`);
    console.log(`   • تأكد من إضافة classes الجديدة إلى theme.css`);
    console.log(`   • اختبر الموقع للتأكد من عمل الألوان`);
  } else {
    console.log(`\n${colors.bright}${colors.green}✅ لا توجد ألوان خفيفة تحتاج إصلاح!${colors.reset}`);
  }
}

function main() {
  console.log(`${colors.bright}${colors.cyan}🎨 بدء إصلاح الألوان الخفيفة...${colors.reset}\n`);
  
  searchDirs.forEach(dir => {
    console.log(`${colors.bright}البحث في: ${dir}${colors.reset}`);
    searchInDirectory(dir);
  });
  
  generateReport();
}

// تشغيل الأداة
main();

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { addDays, addWeeks, addMonths, addYears, format } from 'date-fns';

// POST /api/expenses/recurring - إنشاء مصروف دوري جديد
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      purpose,
      amount,
      categoryId,
      startDate,
      endDate,
      frequency,
      interval,
      notes,
    } = body;

    // التحقق من صحة البيانات
    if (!purpose || !amount || !startDate || !frequency || !interval) {
      return NextResponse.json(
        { error: 'الحقول الأساسية مطلوبة (الغرض، المبلغ، تاريخ البدء، التكرار، الفاصل الزمني)' },
        { status: 400 }
      );
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    if (interval <= 0) {
      return NextResponse.json(
        { error: 'الفاصل الزمني يجب أن يكون أكبر من صفر' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة إذا تم تحديدها
    if (categoryId) {
      const category = await prisma.expenseCategory.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'فئة المصروفات غير موجودة' },
          { status: 400 }
        );
      }
    }

    // الحصول على الخزينة
    const treasury = await prisma.treasury.findFirst();

    if (!treasury) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الخزينة' },
        { status: 404 }
      );
    }

    // إنشاء المصروف الدوري
    const recurringExpense = await prisma.recurringExpense.create({
      data: {
        purpose,
        amount,
        categoryId: categoryId || null,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        frequency,
        interval,
        lastGeneratedDate: null,
        nextGenerationDate: new Date(startDate),
        isActive: true,
        notes: notes || null,
      },
      include: {
        category: true,
      },
    });

    return NextResponse.json(recurringExpense, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء المصروف الدوري:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء المصروف الدوري' },
      { status: 500 }
    );
  }
}

// GET /api/expenses/recurring - الحصول على المصروفات الدورية
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const isActive = searchParams.get('isActive');
    const categoryId = searchParams.get('categoryId');

    // بناء شروط البحث
    const where: {
      isActive?: boolean;
      categoryId?: number;
    } = {};

    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    if (categoryId) {
      where.categoryId = parseInt(categoryId);
    }

    // جلب المصروفات الدورية
    const recurringExpenses = await prisma.recurringExpense.findMany({
      where,
      include: {
        category: true,
      },
      orderBy: {
        nextGenerationDate: 'asc',
      },
    });

    return NextResponse.json({
      recurringExpenses,
      count: recurringExpenses.length,
    });
  } catch (error) {
    console.error('خطأ في جلب المصروفات الدورية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المصروفات الدورية' },
      { status: 500 }
    );
  }
}

// PUT /api/expenses/recurring/:id - تحديث مصروف دوري
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);
    const body = await req.json();
    const {
      purpose,
      amount,
      categoryId,
      startDate,
      endDate,
      frequency,
      interval,
      isActive,
      notes,
    } = body;

    // التحقق من وجود المصروف الدوري
    const existingExpense = await prisma.recurringExpense.findUnique({
      where: { id }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'المصروف الدوري غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من صحة البيانات
    if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    if (interval !== undefined && interval <= 0) {
      return NextResponse.json(
        { error: 'الفاصل الزمني يجب أن يكون أكبر من صفر' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة إذا تم تحديدها
    if (categoryId) {
      const category = await prisma.expenseCategory.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'فئة المصروفات غير موجودة' },
          { status: 400 }
        );
      }
    }

    // حساب تاريخ التوليد التالي إذا تم تغيير التكرار أو الفاصل الزمني
    let nextGenerationDate = existingExpense.nextGenerationDate;
    if (
      (frequency !== undefined && frequency !== existingExpense.frequency) ||
      (interval !== undefined && interval !== existingExpense.interval) ||
      (startDate !== undefined && new Date(startDate).getTime() !== existingExpense.startDate.getTime())
    ) {
      const newStartDate = startDate ? new Date(startDate) : existingExpense.startDate;
      nextGenerationDate = calculateNextGenerationDate(
        newStartDate,
        frequency || existingExpense.frequency,
        interval || existingExpense.interval
      );
    }

    // تحديث المصروف الدوري
    const updatedExpense = await prisma.recurringExpense.update({
      where: { id },
      data: {
        purpose: purpose !== undefined ? purpose : undefined,
        amount: amount !== undefined ? amount : undefined,
        categoryId: categoryId !== undefined ? (categoryId || null) : undefined,
        startDate: startDate !== undefined ? new Date(startDate) : undefined,
        endDate: endDate !== undefined ? (endDate ? new Date(endDate) : null) : undefined,
        frequency: frequency !== undefined ? frequency : undefined,
        interval: interval !== undefined ? interval : undefined,
        isActive: isActive !== undefined ? isActive : undefined,
        nextGenerationDate,
        notes: notes !== undefined ? (notes || null) : undefined,
      },
      include: {
        category: true,
      },
    });

    return NextResponse.json(updatedExpense);
  } catch (error) {
    console.error('خطأ في تحديث المصروف الدوري:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المصروف الدوري' },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses/recurring/:id - حذف مصروف دوري
export async function DELETE(_req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود المصروف الدوري
    const existingExpense = await prisma.recurringExpense.findUnique({
      where: { id }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'المصروف الدوري غير موجود' },
        { status: 404 }
      );
    }

    // حذف المصروف الدوري
    await prisma.recurringExpense.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف المصروف الدوري بنجاح',
    });
  } catch (error) {
    console.error('خطأ في حذف المصروف الدوري:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المصروف الدوري' },
      { status: 500 }
    );
  }
}

// POST /api/expenses/recurring/generate - توليد المصروفات الدورية المستحقة
export async function GENERATE(_req: NextRequest, { params }: { params: { action: string } }) {
  if (params.action !== 'generate') {
    return NextResponse.json(
      { error: 'إجراء غير صالح' },
      { status: 400 }
    );
  }

  try {
    const today = new Date();

    // جلب المصروفات الدورية النشطة المستحقة
    const dueRecurringExpenses = await prisma.recurringExpense.findMany({
      where: {
        isActive: true,
        nextGenerationDate: {
          lte: today,
        },
        OR: [
          { endDate: null },
          { endDate: { gte: today } },
        ],
      },
      include: {
        category: true,
      },
    });

    if (dueRecurringExpenses.length === 0) {
      return NextResponse.json({
        message: 'لا توجد مصروفات دورية مستحقة للتوليد',
        generatedCount: 0,
      });
    }

    // الحصول على الخزينة
    const treasury = await prisma.treasury.findFirst();

    if (!treasury) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الخزينة' },
        { status: 404 }
      );
    }

    // توليد المصروفات
    const generatedExpenses = [];

    for (const recurringExpense of dueRecurringExpenses) {
      // إنشاء المصروف وتحديث الخزينة في معاملة واحدة
      const result = await prisma.$transaction(async (tx) => {
        // إنشاء المصروف
        const expense = await tx.expense.create({
          data: {
            purpose: `${recurringExpense.purpose} (دوري - ${format(recurringExpense.nextGenerationDate, 'yyyy-MM-dd')})`,
            amount: recurringExpense.amount,
            treasuryId: treasury.id,
            categoryId: recurringExpense.categoryId,
            date: recurringExpense.nextGenerationDate,
            notes: recurringExpense.notes,
          },
          include: {
            category: true,
          },
        });

        // تحديث رصيد الخزينة وإجمالي المصاريف
        await tx.treasury.update({
          where: { id: treasury.id },
          data: {
            balance: { decrement: recurringExpense.amount },
            totalExpense: { increment: recurringExpense.amount },
          },
        });

        // حساب تاريخ التوليد التالي
        const nextGenerationDate = calculateNextGenerationDate(
          recurringExpense.nextGenerationDate,
          recurringExpense.frequency,
          recurringExpense.interval
        );

        // تحديث المصروف الدوري
        const updatedRecurringExpense = await tx.recurringExpense.update({
          where: { id: recurringExpense.id },
          data: {
            lastGeneratedDate: recurringExpense.nextGenerationDate,
            nextGenerationDate,
            // تعطيل المصروف الدوري إذا تجاوز تاريخ الانتهاء
            isActive: recurringExpense.endDate
              ? nextGenerationDate <= recurringExpense.endDate
              : true,
          },
        });

        return {
          expense,
          updatedRecurringExpense,
        };
      });

      generatedExpenses.push(result.expense);
    }

    return NextResponse.json({
      message: `تم توليد ${generatedExpenses.length} مصروف دوري بنجاح`,
      generatedCount: generatedExpenses.length,
      generatedExpenses,
    });
  } catch (error) {
    console.error('خطأ في توليد المصروفات الدورية:', error);
    return NextResponse.json(
      { error: 'فشل في توليد المصروفات الدورية' },
      { status: 500 }
    );
  }
}

// حساب تاريخ التوليد التالي
function calculateNextGenerationDate(
  currentDate: Date,
  frequency: string,
  interval: number
): Date {
  switch (frequency) {
    case 'daily':
      return addDays(currentDate, interval);
    case 'weekly':
      return addWeeks(currentDate, interval);
    case 'monthly':
      return addMonths(currentDate, interval);
    case 'yearly':
      return addYears(currentDate, interval);
    default:
      return addMonths(currentDate, interval);
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

// GET /api/student-results - جلب نتائج الامتحانات للطالب المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'STUDENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات الطالب
    const student = await prisma.student.findFirst({
      where: {
        username: userData.username
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات الطالب" },
        { status: 404 }
      );
    }

    // جلب نتائج الامتحانات للطالب
    const examResults = await prisma.exam_points.findMany({
      where: {
        studentId: student.id
      },
      include: {
        exam: true,
        surah: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // جلب تقدم حفظ القرآن للطالب
    const quranProgress = await prisma.quranProgress.findMany({
      where: {
        studentId: student.id
      },
      include: {
        exam: true,
        surah: true
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    // تنسيق البيانات للعرض
    const formattedExamResults = examResults.map(result => ({
      id: result.id,
      examId: result.examId,
      examType: result.exam.evaluationType,
      month: result.exam.month,
      description: result.exam.description || '',
      points: result.grade.toNumber(),
      maxPoints: result.exam.maxPoints,
      passingPoints: result.exam.passingPoints,
      isPassed: result.grade.toNumber() >= result.exam.passingPoints,
      surahName: result.surah?.name || '',
      date: result.createdAt.toISOString()
    }));

    const formattedQuranProgress = quranProgress.map(progress => ({
      id: progress.id,
      examId: progress.examId,
      surahName: progress.surah.name || '',
      startVerse: progress.startVerse,
      endVerse: progress.endVerse,
      memorization: progress.memorization,
      tajweed: progress.tajweed,
      totalScore: (progress.memorization + progress.tajweed) / 2,
      startDate: progress.startDate.toISOString(),
      completionDate: progress.completionDate ? progress.completionDate.toISOString() : null
    }));

    return NextResponse.json({
      examResults: formattedExamResults,
      quranProgress: formattedQuranProgress,
      message: "تم جلب نتائج الامتحانات بنجاح"
    });
  } catch (error: unknown) {
    console.error('Error fetching student results:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب نتائج الامتحانات" },
      { status: 500 }
    );
  }
}

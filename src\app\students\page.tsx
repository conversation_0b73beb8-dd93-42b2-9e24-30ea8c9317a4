"use client";
import React, { useState, useEffect } from 'react';
import { FaBook, FaCalendarAlt, FaChalkboardTeacher, FaTrophy } from 'react-icons/fa';
import Link from 'next/link';
import { toast } from 'react-toastify';

interface StudentStats {
  name: string;
  grade: string;
  progress: number;
  nextLesson: {
    title: string;
    date: string;
  } | null;
  lastAttendance: string | null;
  upcomingExam: {
    title: string;
    date: string;
  } | null;
  attendanceRate: number;
  totalPoints: number;
  achievements: Array<{
    id: number;
    title: string;
    description: string;
    progress: number;
    completed: boolean;
  }>;
}

const StudentDashboard = () => {
  const [studentData, setStudentData] = useState<StudentStats>({
    name: '',
    grade: '',
    progress: 0,
    nextLesson: null,
    lastAttendance: null,
    upcomingExam: null,
    attendanceRate: 0,
    totalPoints: 0,
    achievements: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStudentData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/students/stats');

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات الطالب');
        }

        const data = await response.json();
        setStudentData(data);
      } catch (error) {
        console.error('Error fetching student data:', error);
        setError('حدث خطأ أثناء جلب البيانات. يرجى المحاولة مرة أخرى.');
        toast.error('فشل في جلب بيانات الطالب');
      } finally {
        setIsLoading(false);
      }
    };

    fetchStudentData();
  }, []);

  const quickLinks = [
    { title: 'دروسي', icon: <FaBook />, link: '/students/courses', color: 'bg-blue-500' },
    { title: 'جدول الحصص', icon: <FaCalendarAlt />, link: '/students/schedule', color: 'bg-primary-color' },
    { title: 'المعلمون', icon: <FaChalkboardTeacher />, link: '/students/teachers', color: 'bg-purple-500' },
    { title: 'نتائجي', icon: <FaTrophy />, link: '/students/results', color: 'bg-yellow-500' },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Card */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        {isLoading ? (
          <div className="flex justify-center items-center h-20">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-4">{error}</div>
        ) : (
          <>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">مرحباً بك، {studentData.name}</h2>
            <p className="text-gray-600">أنت في {studentData.grade}. استمر في العمل الجيد!</p>
          </>
        )}
      </div>

      {/* Quick Links */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {quickLinks.map((link, index) => (
          <Link href={link.link} key={index}>
            <div className={`${link.color} text-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col items-center justify-center text-center h-32`}>
              <div className="text-3xl mb-2">{link.icon}</div>
              <span className="font-medium">{link.title}</span>
            </div>
          </Link>
        ))}
      </div>

      {/* Progress Section */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">تقدمك في الحفظ</h3>
        {isLoading ? (
          <div className="flex justify-center items-center h-20">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-4">{error}</div>
        ) : (
          <>
            <div className="mb-4">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">{studentData.progress}% مكتمل</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-[var(--primary-color)] h-2.5 rounded-full" style={{ width: `${studentData.progress}%` }}></div>
              </div>
            </div>
            {studentData.nextLesson ? (
              <p className="text-gray-600">
                الدرس القادم: {studentData.nextLesson.title}
                <span className="text-xs text-gray-500 block mt-1">
                  {new Date(studentData.nextLesson.date).toLocaleDateString('ar-EG')}
                </span>
              </p>
            ) : (
              <p className="text-gray-600">لا يوجد درس قادم محدد</p>
            )}
          </>
        )}
      </div>

      {/* Upcoming Events */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">الأحداث القادمة</h3>
          <ul className="space-y-3">
            <li className="flex items-start">
              <div className="flex-shrink-0 h-5 w-5 rounded-full bg-blue-500 mt-1"></div>
              <div className="mr-3">
                <p className="text-sm font-medium text-gray-900">امتحان التجويد</p>
                <p className="text-sm text-gray-500">
                  {studentData.upcomingExam ? 
                    `${studentData.upcomingExam.title} - ${new Date(studentData.upcomingExam.date).toLocaleDateString('ar-EG')}` 
                    : 'لا يوجد امتحان قادم'}
                </p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="flex-shrink-0 h-5 w-5 rounded-full bg-primary-color mt-1"></div>
              <div className="mr-3">
                <p className="text-sm font-medium text-gray-900">حصة مراجعة</p>
                <p className="text-sm text-gray-500">غداً - 10:00 صباحاً</p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="flex-shrink-0 h-5 w-5 rounded-full bg-yellow-500 mt-1"></div>
              <div className="mr-3">
                <p className="text-sm font-medium text-gray-900">مسابقة حفظ القرآن</p>
                <p className="text-sm text-gray-500">الأسبوع القادم</p>
              </div>
            </li>
          </ul>
        </div>

        {/* Points & Achievements */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">نقاطك وإنجازاتك</h3>
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-4">{error}</div>
          ) : (
            <>
              <div className="flex items-center justify-center mb-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-[var(--primary-color)]">{studentData.totalPoints}</div>
                  <div className="text-sm text-gray-600">مجموع النقاط</div>
                </div>
              </div>
              <div className="space-y-2">
                {studentData.achievements && studentData.achievements.length > 0 ? (
                  studentData.achievements.map((achievement) => (
                    <div key={achievement.id} className="flex justify-between items-center p-2 bg-blue-50 rounded">
                      <span className="text-sm font-medium">{achievement.title}</span>
                      {achievement.completed ? (
                        <span className="text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded">مكتمل</span>
                      ) : (
                        <span className="text-sm bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded">{achievement.progress}%</span>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 py-4">لا توجد إنجازات بعد</div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
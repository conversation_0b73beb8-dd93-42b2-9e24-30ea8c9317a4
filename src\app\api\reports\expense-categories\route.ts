import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/reports/expense-categories - الحصول على تقارير فئات المصروفات
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get('startDate')
      ? new Date(searchParams.get('startDate') as string)
      : new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1); // الشهر السابق

    const endDate = searchParams.get('endDate')
      ? new Date(searchParams.get('endDate') as string)
      : new Date(); // اليوم الحالي

    // جلب جميع فئات المصروفات
    const categories = await prisma.expenseCategory.findMany({
      where: {
        isActive: true,
      },
      include: {
        subCategories: {
          where: {
            isActive: true,
          },
        },
      },
    });

    // جلب المصروفات في الفترة المحددة
    const expenses = await prisma.expense.findMany({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        category: true,
      },
    });

    // تجميع المصروفات حسب الفئة
    const categoryExpenses: { [key: number]: number } = {};
    const uncategorizedExpenses = expenses
      .filter(expense => !expense.categoryId)
      .reduce((sum, expense) => sum + expense.amount, 0);

    expenses.forEach(expense => {
      if (expense.categoryId) {
        if (!categoryExpenses[expense.categoryId]) {
          categoryExpenses[expense.categoryId] = 0;
        }
        categoryExpenses[expense.categoryId] += expense.amount;
      }
    });

    // إعداد بيانات التقرير
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    
    const categoryReports = categories
      .filter(category => !category.parentId) // فقط الفئات الرئيسية
      .map(category => {
        // حساب مصروفات الفئة الرئيسية
        const mainCategoryExpense = categoryExpenses[category.id] || 0;
        
        // حساب مصروفات الفئات الفرعية
        const subCategoriesExpenses = category.subCategories.map(subCategory => {
          const amount = categoryExpenses[subCategory.id] || 0;
          return {
            id: subCategory.id,
            name: subCategory.name,
            amount,
            percentage: totalExpenses > 0 ? (amount / totalExpenses) * 100 : 0,
          };
        });
        
        // إجمالي مصروفات الفئات الفرعية
        const subCategoriesTotal = subCategoriesExpenses.reduce((sum, sub) => sum + sub.amount, 0);
        
        // إجمالي مصروفات الفئة (الرئيسية + الفرعية)
        const totalCategoryExpense = mainCategoryExpense + subCategoriesTotal;
        
        return {
          id: category.id,
          name: category.name,
          amount: totalCategoryExpense,
          percentage: totalExpenses > 0 ? (totalCategoryExpense / totalExpenses) * 100 : 0,
          mainCategoryAmount: mainCategoryExpense,
          subCategories: subCategoriesExpenses,
        };
      })
      .filter(category => category.amount > 0) // فقط الفئات التي لها مصروفات
      .sort((a, b) => b.amount - a.amount); // ترتيب تنازلي حسب المبلغ

    // إضافة المصروفات غير المصنفة
    if (uncategorizedExpenses > 0) {
      categoryReports.push({
        id: 0,
        name: 'غير مصنف',
        amount: uncategorizedExpenses,
        percentage: totalExpenses > 0 ? (uncategorizedExpenses / totalExpenses) * 100 : 0,
        mainCategoryAmount: uncategorizedExpenses,
        subCategories: [],
      });
    }

    // تجميع المصروفات حسب الشهر والفئة
    const monthlyData: { [key: string]: { [key: number]: number } } = {};
    
    expenses.forEach(expense => {
      const date = expense.date;
      const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      
      if (!monthlyData[yearMonth]) {
        monthlyData[yearMonth] = {};
      }
      
      const categoryId = expense.categoryId || 0;
      
      if (!monthlyData[yearMonth][categoryId]) {
        monthlyData[yearMonth][categoryId] = 0;
      }
      
      monthlyData[yearMonth][categoryId] += expense.amount;
    });

    // تحويل البيانات الشهرية إلى تنسيق مناسب للرسوم البيانية
    const months = Object.keys(monthlyData).sort();
    
    const monthlyReports = months.map(month => {
      const monthData = monthlyData[month];
      const monthTotal = Object.values(monthData).reduce((sum, amount) => sum + amount, 0);
      
      const categoriesData = categories
        .filter(category => !category.parentId)
        .map(category => {
          const amount = monthData[category.id] || 0;
          return {
            categoryId: category.id,
            categoryName: category.name,
            amount,
            percentage: monthTotal > 0 ? (amount / monthTotal) * 100 : 0,
          };
        });
      
      // إضافة المصروفات غير المصنفة
      if (monthData[0]) {
        categoriesData.push({
          categoryId: 0,
          categoryName: 'غير مصنف',
          amount: monthData[0],
          percentage: monthTotal > 0 ? (monthData[0] / monthTotal) * 100 : 0,
        });
      }
      
      return {
        month,
        total: monthTotal,
        categories: categoriesData.sort((a, b) => b.amount - a.amount),
      };
    });

    return NextResponse.json({
      totalExpenses,
      categoryReports,
      monthlyReports,
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
    });
  } catch (error) {
    console.error('خطأ في جلب تقارير فئات المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب تقارير فئات المصروفات' },
      { status: 500 }
    );
  }
}

# 🕌 المدرسة القرآنية النموذجية - النظام المتكامل

## 🎯 نظرة عامة

تم تطوير نظام إدارة متكامل للمدرسة القرآنية يشمل جميع جوانب التعليم القرآني والإدارة المدرسية.

---

## 🚀 التشغيل السريع

### **الأمر الواحد لإنشاء المدرسة كاملة:**
```bash
npx prisma db seed
```

هذا الأمر سينشئ مدرسة قرآنية متكاملة في **10 مراحل متسلسلة**.

---

## 📋 المراحل العشر للإنشاء

### **🔐 المرحلة 1: الأدوار والصلاحيات**
- إنشاء أدوار النظام (مدير، معلم، طالب، ولي أمر)
- تحديد صلاحيات كل دور
- إعداد نظام الأمان

### **👤 المرحلة 2: المستخدمين الأساسيين**
- **المدير العام:** `admin` / `admin123`
- **الشيخ المعلم:** `teacher1` / `teacher123`
- إعداد الملفات الشخصية

### **📚 المرحلة 3: المواد الدراسية**
- **حفظ القرآن الكريم** - المادة الأساسية
- **تلاوة وتجويد** - أحكام التجويد
- **التفسير المبسط** - فهم المعاني
- **الحديث الشريف** - السنة النبوية
- **الفقه الإسلامي** - الأحكام الشرعية
- **العقيدة الإسلامية** - أصول الإيمان
- **السيرة النبوية** - سيرة الرسول ﷺ
- **الأخلاق والآداب** - التربية الإسلامية
- **اللغة العربية** - قواعد اللغة
- **الخط العربي** - فن الخط

### **🏫 المرحلة 4: الفصول الدراسية**
- **فصل المبتدئين** - تأسيس وتعلم القراءة
- **فصل الحفظ الأول** - جزء عم
- **فصل الحفظ الثاني** - تبارك
- **فصل الحفظ المتوسط** - تقدم في الحفظ
- **فصل الحفظ المتقدم** - مستوى عالي
- **فصل التجويد والقراءات** - تخصص متقدم
- **فصل الحفاظ والإجازة** - النخبة
- **فصل العلوم الشرعية** - الدراسات الإسلامية

### **👨‍👩‍👧‍👦 المرحلة 5: الطلاب وأولياء الأمور**
- إنشاء طلاب نموذجيين
- ربط الطلاب بالفصول
- إنشاء أولياء أمور
- ربط الطلاب بأولياء الأمور

### **📖 المرحلة 6: سور القرآن الكريم**
- إنشاء قاعدة بيانات السور
- معلومات كل سورة (الاسم، الرقم، عدد الآيات)
- ربط السور بالامتحانات

### **📚 المرحلة 7: مجالس الختم والأنشطة**
- **مجالس ختم أسبوعية** للسور المختلفة
- **أنشطة قرآنية** متنوعة
- **مسابقات** في الحفظ والتلاوة
- **برامج خاصة** للمتميزين

### **👥 المرحلة 8: بيانات المستخدمين الإضافية**
- معلمين إضافيين
- طلاب إضافيين
- موظفين إداريين

### **📊 المرحلة 9: نظام التقييم المتقدم**
- معايير التقييم المتخصصة
- أنواع الامتحانات المختلفة
- نظام النقاط والدرجات
- تقارير الأداء

### **💰 المرحلة 10: النظام المالي والإعدادات**
- فئات المصروفات
- الميزانيات
- الرسوم الدراسية
- إعدادات النظام

---

## 🎓 البرامج التعليمية المُنشأة

### **🕌 برامج تحفيظ القرآن:**
1. **برنامج حفظ القرآن الكريم كاملاً** - 2000 ريال
2. **برنامج حفظ الأجزاء الخمسة الأخيرة** - 800 ريال
3. **برنامج التجويد والقراءات** - 600 ريال

### **📖 برامج العلوم الشرعية:**
4. **برنامج الفقه الإسلامي** - 500 ريال
5. **برنامج الحديث الشريف** - 450 ريال
6. **برنامج السيرة النبوية** - 300 ريال

### **👨‍🏫 برامج التأهيل:**
7. **برنامج إعداد معلمي القرآن** - 800 ريال

---

## 📊 الإحصائيات المتوقعة

بعد تنفيذ البذور، ستحصل على:

### **👥 الأشخاص:**
- **المستخدمين:** 10+ مستخدم
- **الطلاب:** 20+ طالب
- **المعلمين:** 5+ معلم
- **أولياء الأمور:** 15+ ولي أمر

### **🏫 التعليم:**
- **الفصول:** 8 فصول متخصصة
- **المواد:** 10 مواد دراسية
- **الامتحانات:** 50+ امتحان
- **نقاط الامتحانات:** 1000+ نقطة

### **📖 القرآن الكريم:**
- **السور:** 114 سورة كاملة
- **مجالس الختم:** 8+ مجلس
- **الأنشطة:** متنوعة

### **🎓 البرامج:**
- **البرامج التعليمية:** 7 برامج شاملة

---

## 🔗 الروابط المهمة

بعد التشغيل، يمكنك الوصول إلى:

### **🏠 الواجهات الرئيسية:**
- **الصفحة الرئيسية:** http://localhost:3000
- **لوحة التحكم:** http://localhost:3000/admin

### **👥 إدارة الأشخاص:**
- **إدارة الطلاب:** http://localhost:3000/admin/students
- **إدارة المعلمين:** http://localhost:3000/admin/teachers
- **أولياء الأمور:** http://localhost:3000/admin/guardians

### **📚 النظام التعليمي:**
- **إدارة الفصول:** http://localhost:3000/admin/classes
- **المواد الدراسية:** http://localhost:3000/admin/subjects
- **الامتحانات:** http://localhost:3000/admin/exams

### **📊 التقييم والتقارير:**
- **كشف الدرجات:** http://localhost:3000/admin/evaluation/student-report
- **نظام التقييم:** http://localhost:3000/admin/evaluation
- **الإحصائيات:** http://localhost:3000/admin/statistics

### **💰 النظام المالي:**
- **الإدارة المالية:** http://localhost:3000/admin/finance
- **المصروفات:** http://localhost:3000/admin/expenses
- **الميزانيات:** http://localhost:3000/admin/budgets

### **🕌 الأنشطة القرآنية:**
- **مجالس الختم:** http://localhost:3000/admin/khatm-sessions
- **المسابقات:** http://localhost:3000/admin/competitions
- **الأنشطة:** http://localhost:3000/admin/activities

---

## 🔑 بيانات الدخول الافتراضية

### **👨‍💼 المدير العام:**
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **الصلاحيات:** كاملة

### **👨‍🏫 الشيخ المعلم:**
- **اسم المستخدم:** `teacher1`
- **كلمة المرور:** `teacher123`
- **التخصص:** حفظ القرآن الكريم وعلوم التجويد

---

## 🛠️ أوامر إضافية

### **إعادة إنشاء قاعدة البيانات:**
```bash
npx prisma db push --force-reset
npx prisma db seed
```

### **تحديث قاعدة البيانات:**
```bash
npx prisma db push
npx prisma db seed
```

### **إنشاء بيانات إضافية:**
```bash
# تشغيل ملفات بذور محددة
npm run seed:simple
npm run seed:exams
```

---

## 🎯 المميزات الرئيسية

### ✨ **نظام تعليمي متكامل:**
- إدارة شاملة للطلاب والمعلمين
- نظام فصول متدرج حسب المستوى
- مناهج قرآنية وشرعية متخصصة

### 📊 **نظام تقييم متقدم:**
- كشف درجات تقليدي احترافي
- تقييم متعدد المعايير
- تقارير أداء مفصلة

### 🕌 **أنشطة قرآنية:**
- مجالس ختم منتظمة
- مسابقات في الحفظ والتلاوة
- برامج تأهيل المعلمين

### 💰 **إدارة مالية:**
- نظام مصروفات متكامل
- إدارة الميزانيات
- تتبع الرسوم الدراسية

---

## 🤲 خاتمة

**تم إنشاء مدرسة قرآنية نموذجية متكاملة** تشمل جميع جوانب التعليم القرآني والإدارة المدرسية.

**بارك الله فيكم ووفقكم في خدمة كتاب الله الكريم** 🌟

---

*النظام جاهز للاستخدام الفوري بأمر واحد فقط!*

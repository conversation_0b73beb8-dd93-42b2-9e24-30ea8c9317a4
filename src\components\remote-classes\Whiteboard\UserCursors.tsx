'use client';

import React from 'react';
import { UserCursor } from './WhiteboardSync';

interface UserCursorsProps {
  cursors: UserCursor[];
  currentUserId: string;
}

/**
 * Component to display user cursors on the whiteboard
 */
const UserCursors: React.FC<UserCursorsProps> = ({ cursors, currentUserId }) => {
  return (
    <div className="absolute inset-0 pointer-events-none">
      {cursors
        .filter(cursor => cursor.userId !== currentUserId)
        .map(cursor => (
          <div
            key={cursor.userId}
            className="absolute"
            style={{
              left: cursor.position.x,
              top: cursor.position.y,
              transform: 'translate(-50%, -50%)',
              zIndex: 1000,
            }}
          >
            {/* Cursor pointer */}
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              style={{ color: cursor.color }}
            >
              <path
                d="M5 3L19 12L12 13L9 20L5 3Z"
                fill={cursor.color}
                stroke="white"
                strokeWidth="1.5"
                strokeLinejoin="round"
              />
            </svg>
            
            {/* Username label */}
            <div
              className="absolute whitespace-nowrap px-2 py-1 rounded-md text-xs font-medium"
              style={{
                backgroundColor: cursor.color,
                color: 'white',
                top: '100%',
                left: '50%',
                transform: 'translateX(-50%)',
                marginTop: '2px',
              }}
            >
              {cursor.username}
            </div>
          </div>
        ))}
    </div>
  );
};

export default UserCursors;

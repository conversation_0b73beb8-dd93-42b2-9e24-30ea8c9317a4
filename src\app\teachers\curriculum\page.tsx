"use client";
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
// استيراد مكونات الأكورديون
const Accordion = ({ className, children }: { type?: string, collapsible?: boolean, className: string, children: React.ReactNode }) => (
  <div className={className}>{children}</div>
);

const AccordionItem = ({ children }: { value?: string, children: React.ReactNode }) => (
  <div className="mb-2 border rounded">{children}</div>
);

const AccordionTrigger = ({ className, children }: { className: string, children: React.ReactNode }) => (
  <div className={`${className} p-4 cursor-pointer flex justify-between items-center`}>
    {children}
    <span>▼</span>
  </div>
);

const AccordionContent = ({ children }: { children: React.ReactNode }) => (
  <div className="p-4 border-t">{children}</div>
);
import { Button } from "@/components/ui/button";
// استبدال Badge بعنصر span بسيط
import {
  FaBook,
  FaDownload,
  FaFilePdf,
  FaFileWord,
  FaFileExcel,
  FaFilePowerpoint,
  FaFileAlt,
  FaQuran
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface Subject {
  id: number;
  name: string;
  description?: string;
}

interface CurriculumUnit {
  id: number;
  title: string;
  description?: string;
  order: number;
  subjectId: number;
  lessons: CurriculumLesson[];
}

interface CurriculumLesson {
  id: number;
  title: string;
  description?: string;
  order: number;
  unitId: number;
  resources: CurriculumResource[];
}

interface CurriculumResource {
  id: number;
  title: string;
  type: string;
  url: string;
  lessonId: number;
}

const TeacherCurriculumPage = () => {
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [curriculum, setCurriculum] = useState<{[key: number]: CurriculumUnit[]}>({}); // subjectId -> units
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeSubject, setActiveSubject] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب المواد التي يدرسها المعلم
        const subjectsResponse = await fetch('/api/teacher-subjects');
        if (!subjectsResponse.ok) {
          throw new Error('فشل في جلب بيانات المواد');
        }
        const subjectsData = await subjectsResponse.json();

        // التحقق من شكل البيانات المستلمة وتنسيقها بشكل صحيح
        let teacherSubjectsArray = [];
        let teacherSubjects = [];

        try {
          if (subjectsData && subjectsData.data && Array.isArray(subjectsData.data)) {
            // إذا كانت البيانات في شكل {data: [...]}
            console.log('Using data array from response');
            teacherSubjectsArray = subjectsData.data;
          } else if (Array.isArray(subjectsData)) {
            // إذا كانت البيانات مصفوفة مباشرة
            console.log('Using direct array response');
            teacherSubjectsArray = subjectsData;
          } else {
            // إذا كان هناك شكل آخر للبيانات
            console.error('Unexpected data format:', subjectsData);
            teacherSubjectsArray = [];
          }

          // التحقق من أن كل عنصر في المصفوفة يحتوي على خاصية subject
          teacherSubjects = teacherSubjectsArray
            .filter(item => item && item.subject)
            .map((item: { subject: { id: number, name: string, description?: string } }) => ({
              id: item.subject.id,
              name: item.subject.name,
              description: item.subject.description
            }));
        } catch (error) {
          console.error('Error processing subjects data:', error);
          teacherSubjects = [];
        }

        setSubjects(teacherSubjects);

        if (teacherSubjects.length > 0) {
          setActiveSubject(teacherSubjects[0].id.toString());

          // جلب المنهج لكل مادة
          const curriculumData: {[key: number]: CurriculumUnit[]} = {};

          for (const subject of teacherSubjects) {
            try {
              const curriculumResponse = await fetch(`/api/curriculum?subjectId=${subject.id}`);
              if (curriculumResponse.ok) {
                try {
                  const data = await curriculumResponse.json();
                  if (data && data.units && Array.isArray(data.units)) {
                    curriculumData[subject.id] = data.units;
                  } else if (data && Array.isArray(data)) {
                    curriculumData[subject.id] = data;
                  } else {
                    console.error('Unexpected curriculum data format:', data);
                    curriculumData[subject.id] = [];
                  }
                } catch (error) {
                  console.error(`Error processing curriculum data for subject ${subject.id}:`, error);
                  curriculumData[subject.id] = [];
                }
              }
            } catch (err: unknown) {
              console.error(`Error fetching curriculum for subject ${subject.id}:`, err);
            }
          }

          setCurriculum(curriculumData);
        }
      } catch (err: unknown) {
        console.error('Error fetching data:', err);
        setError('حدث خطأ أثناء جلب البيانات');
        toast.error('فشل في جلب البيانات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return <FaFilePdf className="text-red-500" />;
      case 'doc':
      case 'docx':
        return <FaFileWord className="text-blue-500" />;
      case 'xls':
      case 'xlsx':
        return <FaFileExcel className="text-primary-color" />;
      case 'ppt':
      case 'pptx':
        return <FaFilePowerpoint className="text-orange-500" />;
      default:
        return <FaFileAlt className="text-gray-500" />;
    }
  };

  // إذا لم تكن هناك بيانات حقيقية، نقوم بإنشاء بيانات وهمية للعرض
  const getMockCurriculum = (subjectId: number) => {
    const subject = subjects.find(s => s.id === subjectId);

    if (subject?.name.includes('قرآن') || subject?.name.includes('تجويد')) {
      return [
        {
          id: 1,
          title: 'أساسيات التجويد',
          description: 'تعلم أساسيات علم التجويد وأحكامه',
          order: 1,
          subjectId,
          lessons: [
            {
              id: 1,
              title: 'مخارج الحروف',
              description: 'تعلم مخارج الحروف العربية',
              order: 1,
              unitId: 1,
              resources: [
                {
                  id: 1,
                  title: 'مخارج الحروف - ملف PDF',
                  type: 'pdf',
                  url: '#',
                  lessonId: 1
                },
                {
                  id: 2,
                  title: 'تمارين على مخارج الحروف',
                  type: 'doc',
                  url: '#',
                  lessonId: 1
                }
              ]
            },
            {
              id: 2,
              title: 'أحكام النون الساكنة والتنوين',
              description: 'تعلم أحكام النون الساكنة والتنوين',
              order: 2,
              unitId: 1,
              resources: [
                {
                  id: 3,
                  title: 'أحكام النون الساكنة والتنوين - ملف PDF',
                  type: 'pdf',
                  url: '#',
                  lessonId: 2
                }
              ]
            }
          ]
        },
        {
          id: 2,
          title: 'حفظ سورة البقرة',
          description: 'حفظ سورة البقرة مع التجويد',
          order: 2,
          subjectId,
          lessons: [
            {
              id: 3,
              title: 'الآيات 1-20',
              description: 'حفظ الآيات 1-20 من سورة البقرة',
              order: 1,
              unitId: 2,
              resources: [
                {
                  id: 4,
                  title: 'تسميع الآيات 1-20',
                  type: 'pdf',
                  url: '#',
                  lessonId: 3
                }
              ]
            },
            {
              id: 4,
              title: 'الآيات 21-40',
              description: 'حفظ الآيات 21-40 من سورة البقرة',
              order: 2,
              unitId: 2,
              resources: [
                {
                  id: 5,
                  title: 'تسميع الآيات 21-40',
                  type: 'pdf',
                  url: '#',
                  lessonId: 4
                }
              ]
            }
          ]
        }
      ];
    } else {
      return [
        {
          id: 3,
          title: 'الوحدة الأولى',
          description: 'مقدمة في المادة',
          order: 1,
          subjectId,
          lessons: [
            {
              id: 5,
              title: 'الدرس الأول',
              description: 'مقدمة عامة',
              order: 1,
              unitId: 3,
              resources: [
                {
                  id: 6,
                  title: 'ملخص الدرس',
                  type: 'pdf',
                  url: '#',
                  lessonId: 5
                }
              ]
            }
          ]
        }
      ];
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">المنهج الدراسي</h1>
          <p className="text-gray-500">عرض وإدارة المنهج الدراسي للمواد التي تقوم بتدريسها</p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : subjects.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-4">
              لا توجد مواد مسندة إليك
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs value={activeSubject} onValueChange={setActiveSubject}>
          <TabsList className="mb-6 flex flex-wrap">
            {subjects.map(subject => (
              <TabsTrigger key={subject.id} value={subject.id.toString()} className="flex items-center gap-2">
                <FaBook />
                <span>{subject.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {subjects.map(subject => (
            <TabsContent key={subject.id} value={subject.id.toString()}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {subject.name.includes('قرآن') ? <FaQuran className="text-[var(--primary-color)]" /> : <FaBook className="text-[var(--primary-color)]" />}
                    <span>{subject.name}</span>
                  </CardTitle>
                  <CardDescription>
                    {subject.description || 'المنهج الدراسي للمادة'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {curriculum[subject.id] && curriculum[subject.id].length > 0 ? (
                    <Accordion type="single" collapsible className="w-full">
                      {curriculum[subject.id].map(unit => (
                        <AccordionItem key={unit.id} value={`unit-${unit.id}`}>
                          <AccordionTrigger className="text-lg font-medium">
                            {unit.title}
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="pl-4 space-y-4">
                              {unit.description && (
                                <p className="text-gray-600">{unit.description}</p>
                              )}

                              {unit.lessons.map(lesson => (
                                <div key={lesson.id} className="border rounded-lg p-4">
                                  <h4 className="font-medium text-lg mb-2">{lesson.title}</h4>
                                  {lesson.description && (
                                    <p className="text-gray-600 mb-4">{lesson.description}</p>
                                  )}

                                  {lesson.resources.length > 0 && (
                                    <div className="space-y-2">
                                      <h5 className="font-medium text-sm text-gray-500">الموارد التعليمية:</h5>
                                      <div className="space-y-2">
                                        {lesson.resources.map(resource => (
                                          <div key={resource.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                                            <div className="flex items-center gap-2">
                                              {getFileIcon(resource.type)}
                                              <span>{resource.title}</span>
                                            </div>
                                            <Button variant="ghost" size="sm">
                                              <Link href={resource.url} target="_blank">
                                                <FaDownload className="mr-2" />
                                                تحميل
                                              </Link>
                                            </Button>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  ) : (
                    <div className="space-y-4">
                      {getMockCurriculum(subject.id).map(unit => (
                        <Accordion key={unit.id} type="single" collapsible className="w-full">
                          <AccordionItem value={`unit-${unit.id}`}>
                            <AccordionTrigger className="text-lg font-medium">
                              {unit.title}
                              <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ml-2 bg-yellow-50 text-yellow-700 border-yellow-200">
                                بيانات وهمية
                              </span>
                            </AccordionTrigger>
                            <AccordionContent>
                              <div className="pl-4 space-y-4">
                                {unit.description && (
                                  <p className="text-gray-600">{unit.description}</p>
                                )}

                                {unit.lessons.map(lesson => (
                                  <div key={lesson.id} className="border rounded-lg p-4">
                                    <h4 className="font-medium text-lg mb-2">{lesson.title}</h4>
                                    {lesson.description && (
                                      <p className="text-gray-600 mb-4">{lesson.description}</p>
                                    )}

                                    {lesson.resources.length > 0 && (
                                      <div className="space-y-2">
                                        <h5 className="font-medium text-sm text-gray-500">الموارد التعليمية:</h5>
                                        <div className="space-y-2">
                                          {lesson.resources.map(resource => (
                                            <div key={resource.id} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                                              <div className="flex items-center gap-2">
                                                {getFileIcon(resource.type)}
                                                <span>{resource.title}</span>
                                              </div>
                                              <Button variant="ghost" size="sm">
                                                <Link href={resource.url}>
                                                  <FaDownload className="mr-2" />
                                                  تحميل
                                                </Link>
                                              </Button>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      ))}

                      <div className="text-center text-gray-500 py-4">
                        <p>هذه بيانات وهمية للعرض فقط. يمكن إضافة المنهج الحقيقي لاحقاً.</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
};

export default TeacherCurriculumPage;

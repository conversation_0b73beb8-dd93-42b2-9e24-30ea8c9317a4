import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// تعريف واجهات البيانات
interface DataPoint {
  yearMonth: string;
  amount: number;
}

interface CategoryForecast {
  categoryId: number;
  categoryName: string;
  percentage: number;
  forecasts: DataPoint[];
}

// GET /api/financial-forecasts - الحصول على التنبؤات المالية
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const months = parseInt(searchParams.get('months') || '6'); // عدد الأشهر للتنبؤ (افتراضيًا 6 أشهر)
    const categoryId = searchParams.get('categoryId'); // فئة محددة (اختياري)

    // الحصول على تاريخ اليوم
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();

    // تحديد فترة البيانات التاريخية (12 شهرًا سابقة)
    const historicalStartDate = new Date(currentYear, currentMonth - 12, 1);
    const historicalEndDate = new Date(currentYear, currentMonth, 0);

    // بناء شروط البحث
    const where: {
      date: {
        gte: Date;
        lte: Date;
      };
      categoryId?: number;
    } = {
      date: {
        gte: historicalStartDate,
        lte: historicalEndDate,
      },
    };

    if (categoryId) {
      where.categoryId = parseInt(categoryId);
    }

    // جلب البيانات التاريخية للمصروفات
    const historicalExpenses = await prisma.expense.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // تجميع المصروفات حسب الشهر
    const monthlyExpenses: { [key: string]: number } = {};

    historicalExpenses.forEach(expense => {
      const date = expense.date;
      const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!monthlyExpenses[yearMonth]) {
        monthlyExpenses[yearMonth] = 0;
      }

      monthlyExpenses[yearMonth] += expense.amount;
    });

    // تحويل البيانات إلى مصفوفة
    const expenseData: DataPoint[] = Object.entries(monthlyExpenses).map(([yearMonth, amount]) => ({
      yearMonth,
      amount,
    }));

    // حساب متوسط النمو الشهري
    let growthRate = 0;
    if (expenseData.length > 1) {
      const growthRates: number[] = [];

      for (let i = 1; i < expenseData.length; i++) {
        const prevAmount = expenseData[i - 1].amount;
        const currentAmount = expenseData[i].amount;

        if (prevAmount > 0) {
          const rate = (currentAmount - prevAmount) / prevAmount;
          growthRates.push(rate);
        }
      }

      // حساب متوسط معدلات النمو
      if (growthRates.length > 0) {
        growthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
      }
    }

    // التنبؤ بالمصروفات للأشهر القادمة
    const forecasts: DataPoint[] = [];
    let lastAmount = expenseData.length > 0 ? expenseData[expenseData.length - 1].amount : 0;

    for (let i = 1; i <= months; i++) {
      const forecastDate = new Date(currentYear, currentMonth + i, 1);
      const yearMonth = `${forecastDate.getFullYear()}-${String(forecastDate.getMonth() + 1).padStart(2, '0')}`;

      // حساب المبلغ المتوقع باستخدام معدل النمو
      const forecastAmount = lastAmount * (1 + growthRate);

      forecasts.push({
        yearMonth,
        amount: Math.round(forecastAmount * 100) / 100, // تقريب إلى رقمين عشريين
      });

      lastAmount = forecastAmount;
    }

    // جلب إحصائيات حسب الفئة إذا لم يتم تحديد فئة معينة
    let categoryForecasts: CategoryForecast[] = [];
    if (!categoryId) {
      // جلب إحصائيات المصروفات حسب الفئة
      const categoryStats = await prisma.expense.groupBy({
        by: ['categoryId'],
        where: {
          date: {
            gte: historicalStartDate,
            lte: historicalEndDate,
          },
          categoryId: {
            not: null,
          },
        },
        _sum: {
          amount: true,
        },
      });

      // جلب معلومات الفئات
      const categories = await prisma.expenseCategory.findMany({
        where: {
          id: {
            in: categoryStats.map(stat => stat.categoryId).filter(Boolean) as number[],
          },
        },
      });

      // حساب نسبة كل فئة من إجمالي المصروفات
      const totalExpenses = categoryStats.reduce((sum, stat) => sum + (stat._sum.amount || 0), 0);

      // التنبؤ بمصروفات كل فئة
      categoryForecasts = categories.map(category => {
        const stat = categoryStats.find(s => s.categoryId === category.id);
        const categoryAmount = stat?._sum.amount || 0;
        const percentage = totalExpenses > 0 ? categoryAmount / totalExpenses : 0;

        // التنبؤ بمصروفات الفئة للأشهر القادمة
        const categoryForecastData = forecasts.map(forecast => ({
          yearMonth: forecast.yearMonth,
          amount: Math.round(forecast.amount * percentage * 100) / 100,
        }));

        return {
          categoryId: category.id,
          categoryName: category.name,
          percentage: Math.round(percentage * 100 * 100) / 100, // النسبة المئوية بدقة رقمين عشريين
          forecasts: categoryForecastData,
        };
      });
    }

    return NextResponse.json({
      historicalData: expenseData,
      forecasts,
      growthRate: Math.round(growthRate * 100 * 100) / 100, // النسبة المئوية بدقة رقمين عشريين
      categoryForecasts,
    });
  } catch (error) {
    console.error('خطأ في جلب التنبؤات المالية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب التنبؤات المالية' },
      { status: 500 }
    );
  }
}

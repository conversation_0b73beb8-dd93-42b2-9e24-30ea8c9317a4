'use client'

import * as React from 'react'
import * as SelectPrimitive from '@radix-ui/react-select'
import { cn } from '@/utils/cn'
import { Check, ChevronDown } from 'lucide-react'

const Select = SelectPrimitive.Root
const SelectGroup = SelectPrimitive.Group
const SelectValue = SelectPrimitive.Value

const SelectTrigger = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
      className
    )}
    {...props}
  >
    {children}
    <SelectPrimitive.Icon asChild>
      <ChevronDown className="h-4 w-4 opacity-50" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
))
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const SelectContent = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = 'popper', ...props }, ref) => {
  // استخدام مرجع للمحتوى للتعامل مع الأحداث
  const contentRef = React.useRef<HTMLDivElement>(null);

  // دمج المرجعين
  const handleRef = React.useCallback((node: HTMLDivElement) => {
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
    contentRef.current = node;
  }, [ref]);

  // معالجة النقر خارج القائمة
  const handlePointerDownOutside = (e: Event) => {
    // منع إغلاق القائمة عند النقر خارجها أثناء التفاعل مع عناصر البوب أب
    if (e.target && (e.target as HTMLElement).closest('[role="dialog"]')) {
      e.preventDefault();
    }
  };

  return (
    <SelectPrimitive.Portal>
      <SelectPrimitive.Content
        ref={handleRef}
        className={cn(
          'relative z-[100] min-w-[8rem] max-h-[300px] overflow-auto rounded-md border border-gray-200 bg-white shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          position === 'popper' &&
            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
          className
        )}
        position={position}
        onPointerDownOutside={handlePointerDownOutside}
        {...props}
      >
        <SelectPrimitive.Viewport
          className={cn(
            'p-1',
            position === 'popper' &&
              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'
          )}
        >
          {children}
        </SelectPrimitive.Viewport>
      </SelectPrimitive.Content>
    </SelectPrimitive.Portal>
  );
})
SelectContent.displayName = SelectPrimitive.Content.displayName

const SelectLabel = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn('py-1.5 pr-2 pl-8 text-sm font-semibold', className)}
    {...props}
  />
))
SelectLabel.displayName = SelectPrimitive.Label.displayName

const SelectItem = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, onSelect, value, ...props }, ref) => {
  // تحسين التعامل مع أحداث الاختيار
  const handleSelect = (event: React.SyntheticEvent<HTMLDivElement, Event>) => {
    // تأخير بسيط للتأكد من أن الحدث يعمل بشكل صحيح
    setTimeout(() => {
      if (onSelect) {
        onSelect(event);
      }
    }, 10);
  };

  // التحقق من أن قيمة value ليست فارغة
  let itemValue = value;
  if (value === '' && !props.disabled) {
    console.warn('A <Select.Item /> should have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.');
    // استخدام قيمة افتراضية إذا كانت القيمة فارغة
    // Generate a unique value based on children content or a random string if needed
    itemValue = children?.toString() || `item-${Math.random().toString(36).substring(2, 9)}`;
  }

  return (
    <SelectPrimitive.Item
      ref={ref}
      className={cn(
        'relative flex w-full cursor-default select-none items-center rounded-sm py-2 pr-2 pl-8 text-sm outline-none border-b border-gray-100 last:border-b-0 hover:bg-primary-light hover:text-primary-dark hover:border-primary-color/20 focus:bg-primary-light focus:text-primary-dark focus:border-primary-color data-[state=checked]:bg-primary-color data-[state=checked]:text-white data-[state=checked]:border-primary-color data-[disabled]:pointer-events-none data-[disabled]:opacity-50 transition-all duration-200',
        className
      )}
      onSelect={handleSelect}
      value={itemValue as string}
      {...props}
    >
      <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
        <SelectPrimitive.ItemIndicator>
          <Check className="h-4 w-4 text-white" />
        </SelectPrimitive.ItemIndicator>
      </span>
      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
    </SelectPrimitive.Item>
  );
})
SelectItem.displayName = SelectPrimitive.Item.displayName

const SelectSeparator = React.forwardRef<
  React.ComponentRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn('-mx-1 my-1 h-px bg-muted', className)}
    {...props}
  />
))
SelectSeparator.displayName = SelectPrimitive.Separator.displayName

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
}
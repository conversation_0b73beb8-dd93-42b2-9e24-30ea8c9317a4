# مخطط حالات الاستخدام - نظام إدارة التلاميذ المتقدم

## نظرة عامة
هذا المخطط يوضح التفاعلات الرئيسية بين المستخدمين والنظام للوظائف الجديدة.

```mermaid
graph TB
    %% الفاعلون
    Admin[المدير/الموظف الإداري]
    Teacher[المعلم]
    Parent[ولي الأمر]
    Student[التلميذ]
    System[النظام]

    %% حالات الاستخدام الرئيسية
    subgraph "إدارة بداية الحفظ"
        UC1[إدخال معلومات بداية الحفظ]
        UC2[تعديل معلومات بداية الحفظ]
        UC3[عرض تاريخ بداية الحفظ]
        UC4[تتبع تقدم الحفظ من البداية]
    end

    subgraph "إدارة بطاقة التلميذ"
        UC5[عرض بطاقة التلميذ]
        UC6[طباعة بطاقة التلميذ]
        UC7[تحديث بيانات البطاقة]
        UC8[تصدير البطاقة كـ PDF]
    end

    subgraph "إدارة وصل التسجيل"
        UC9[إنشاء وصل التسجيل]
        UC10[طباعة وصل التسجيل]
        UC11[إعادة إصدار وصل التسجيل]
        UC12[عرض تاريخ الوصولات]
    end

    subgraph "العمليات المساعدة"
        UC13[البحث عن التلاميذ]
        UC14[فلترة التلاميذ حسب المعايير]
        UC15[تصدير التقارير]
        UC16[إرسال إشعارات]
    end

    %% العلاقات - إدارة بداية الحفظ
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Teacher --> UC1
    Teacher --> UC2
    Teacher --> UC3
    Teacher --> UC4
    Parent --> UC3
    Student --> UC3

    %% العلاقات - إدارة بطاقة التلميذ
    Admin --> UC5
    Admin --> UC6
    Admin --> UC7
    Admin --> UC8
    Teacher --> UC5
    Teacher --> UC6
    Parent --> UC5
    Parent --> UC6
    Student --> UC5

    %% العلاقات - إدارة وصل التسجيل
    Admin --> UC9
    Admin --> UC10
    Admin --> UC11
    Admin --> UC12
    Parent --> UC10
    Parent --> UC12

    %% العلاقات - العمليات المساعدة
    Admin --> UC13
    Admin --> UC14
    Admin --> UC15
    Teacher --> UC13
    Teacher --> UC14
    System --> UC16

    %% العلاقات الممتدة والمتضمنة
    UC1 -.->|extends| UC16 : "إرسال إشعار عند الإدخال"
    UC9 -.->|extends| UC16 : "إرسال إشعار للولي"
    UC5 -.->|includes| UC3 : "يتضمن معلومات بداية الحفظ"
    UC6 -.->|includes| UC5 : "يتضمن عرض البطاقة"
    UC10 -.->|includes| UC9 : "يتضمن إنشاء الوصل"

    %% تنسيق الألوان
    classDef actor fill:#e1f5fe
    classDef usecase fill:#f3e5f5
    classDef system fill:#fff3e0

    class Admin,Teacher,Parent,Student actor
    class UC1,UC2,UC3,UC4,UC5,UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC13,UC14,UC15 usecase
    class System system
```

## وصف حالات الاستخدام

### 1. إدارة بداية الحفظ

#### UC1: إدخال معلومات بداية الحفظ
- **الفاعل الرئيسي:** المدير، المعلم
- **الهدف:** تسجيل نقطة بداية حفظ القرآن للتلميذ
- **السيناريو الأساسي:**
  1. المستخدم يختار التلميذ
  2. يدخل تاريخ بداية الحفظ
  3. يحدد الجزء والسورة والآية
  4. يختار المستوى (مبتدئ/متوسط/متقدم)
  5. يضيف ملاحظات إضافية
  6. النظام يحفظ البيانات ويرسل إشعار

#### UC2: تعديل معلومات بداية الحفظ
- **الفاعل الرئيسي:** المدير، المعلم
- **الهدف:** تحديث معلومات بداية الحفظ المسجلة سابقاً
- **الشروط المسبقة:** وجود سجل بداية حفظ للتلميذ

#### UC3: عرض تاريخ بداية الحفظ
- **الفاعل الرئيسي:** جميع المستخدمين
- **الهدف:** الاطلاع على معلومات بداية حفظ التلميذ
- **المخرجات:** تاريخ البداية، الجزء، السورة، المستوى

### 2. إدارة بطاقة التلميذ

#### UC5: عرض بطاقة التلميذ
- **الفاعل الرئيسي:** جميع المستخدمين
- **الهدف:** عرض بطاقة شاملة لبيانات التلميذ
- **المحتوى:**
  - البيانات الشخصية
  - معلومات بداية الحفظ
  - تقدم الحفظ الحالي
  - الصورة الشخصية
  - معلومات الفصل وولي الأمر

#### UC6: طباعة بطاقة التلميذ
- **الفاعل الرئيسي:** المدير، المعلم، ولي الأمر
- **الهدف:** طباعة بطاقة التلميذ بتصميم احترافي
- **المتطلبات:** تصميم قابل للطباعة، دعم أحجام ورق مختلفة

### 3. إدارة وصل التسجيل

#### UC9: إنشاء وصل التسجيل
- **الفاعل الرئيسي:** المدير
- **الهدف:** إصدار وصل تسجيل للتلميذ الجديد
- **السيناريو:**
  1. النظام يولد رقم وصل فريد
  2. يحسب رسوم التسجيل
  3. ينشئ الوصل مع البيانات الكاملة
  4. يحفظ الوصل في قاعدة البيانات
  5. يرسل إشعار لولي الأمر

#### UC10: طباعة وصل التسجيل
- **الفاعل الرئيسي:** المدير، ولي الأمر
- **الهدف:** طباعة وصل التسجيل الرسمي
- **المتطلبات:** تصميم رسمي، شعار المدرسة، معلومات قانونية

## متطلبات الأمان والصلاحيات

### صلاحيات المدير
- جميع العمليات على كافة التلاميذ
- إنشاء وتعديل وحذف السجلات
- الوصول للتقارير الشاملة

### صلاحيات المعلم
- عرض وتعديل بيانات تلاميذ فصوله فقط
- إدخال وتحديث معلومات بداية الحفظ
- طباعة البطاقات والتقارير

### صلاحيات ولي الأمر
- عرض بيانات أطفاله فقط
- طباعة البطاقات والوصولات
- عدم إمكانية التعديل

### صلاحيات التلميذ
- عرض بياناته الشخصية فقط
- عدم إمكانية التعديل أو الطباعة

## التكامل مع النظام الحالي

- **نظام الصلاحيات:** استخدام نظام الصلاحيات الموجود
- **قاعدة البيانات:** التكامل مع نماذج Student و QuranProgress الحالية
- **واجهة المستخدم:** اتباع نفس التصميم والأسلوب المستخدم
- **الإشعارات:** استخدام نظام الإشعارات الموجود

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import VideoEmbed from './VideoEmbed';

export interface HelpItem {
  id: string;
  title: string;
  description: string;
  content: string;
  videoId?: string;
  videoTitle?: string;
  steps?: string[];
  tips?: string[];
}

export interface HelpContentData {
  [categoryId: string]: HelpItem[];
}

interface HelpContentProps {
  categoryId: string;
  items: HelpItem[];
}

export default function HelpContent({ categoryId, items }: HelpContentProps) {
  if (!items || items.length === 0) {
    return (
      <div className="flex-1 p-8">
        <div className="text-center py-16">
          <div className="help-icon-bounce text-6xl mb-6">🔍</div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">لم يتم العثور على نتائج</h3>
          <p className="text-gray-600 text-lg mb-6">جرب البحث بكلمات مختلفة أو اختر فئة أخرى</p>
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-6 max-w-md mx-auto border border-blue-100">
            <p className="text-gray-700 text-sm">
              💡 <strong>نصيحة:</strong> يمكنك البحث في أكثر من 100 موضوع مساعدة باستخدام شريط البحث أعلاه
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-8 overflow-y-auto">
      <div className="max-w-5xl mx-auto space-y-8">
        {items.map((item, index) => (
          <Card
            key={item.id}
            className="help-content-card help-card-hover-effect border-t-4 border-[var(--primary-color)]"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <CardHeader className="pb-4">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-[var(--primary-color)] to-[var(--secondary-color)] rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg flex-shrink-0">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <CardTitle className="card-title text-2xl mb-2 leading-tight">
                    {item.title}
                  </CardTitle>
                  <CardDescription className="card-description text-lg leading-relaxed">
                    {item.description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="card-content space-y-6">
              {/* المحتوى النصي */}
              <div className="prose prose-lg max-w-none help-override-colors">
                <div
                  className="help-content-text leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: item.content }}
                />
              </div>

              {/* الخطوات إذا كانت متوفرة */}
              {item.steps && item.steps.length > 0 && (
                <div className="help-steps-box p-6 rounded-lg">
                  <h4 className="font-semibold mb-4 text-lg">خطوات التنفيذ:</h4>
                  <ol className="list-decimal list-inside space-y-2">
                    {item.steps.map((step, stepIndex) => (
                      <li key={stepIndex} className="leading-relaxed">{step}</li>
                    ))}
                  </ol>
                </div>
              )}

              {/* النصائح إذا كانت متوفرة */}
              {item.tips && item.tips.length > 0 && (
                <div className="help-tips-box p-6 rounded-lg">
                  <h4 className="font-semibold mb-4 text-lg">نصائح مفيدة:</h4>
                  <ul className="list-disc list-inside space-y-2">
                    {item.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="leading-relaxed">{tip}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* الفيديو التعليمي إذا كان متوفراً */}
              {item.videoId && (
                <div className="help-video-box p-6 rounded-lg">
                  <h4 className="font-semibold mb-4 text-lg">فيديو تعليمي:</h4>
                  <VideoEmbed
                    videoId={item.videoId}
                    title={item.videoTitle || item.title}
                    className="max-w-2xl mx-auto"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

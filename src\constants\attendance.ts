export const ATTENDANCE_STATUS = {
  PRESENT: 'حاضر',
  ABSENT: 'غائب',
  EXCUSED: 'معذور'
} as const;

export const ATTENDANCE_STATUS_COLORS = {
  PRESENT: 'bg-green-100 text-green-800',
  ABSENT: 'bg-red-100 text-red-800',
  EXCUSED: 'bg-yellow-100 text-yellow-800'
} as const;

export type AttendanceStatusKey = keyof typeof ATTENDANCE_STATUS;

export const getArabicStatus = (status: string | null): string => {
  if (!status) return 'لم يسجل';
  return ATTENDANCE_STATUS[status as AttendanceStatusKey] || status;
};

export const getStatusColor = (status: string | null): string => {
  if (!status) return 'bg-gray-100 text-gray-800';
  return ATTENDANCE_STATUS_COLORS[status as AttendanceStatusKey] || 'bg-gray-100 text-gray-800';
};
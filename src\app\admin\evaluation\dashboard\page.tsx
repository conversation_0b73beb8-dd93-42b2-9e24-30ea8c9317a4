'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, FileText, Award, BarChart, Calendar, Users, BookOpen, HelpCircle } from 'lucide-react';
import Link from 'next/link';
import { EvaluationType } from '@prisma/client';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

// تعريف نوع البيانات للامتحان
interface Exam {
  id: number;
  evaluationType: EvaluationType;
  month: string;
  maxPoints: number;
  passingPoints: number;
  classId?: number;
  typeId?: number;
}

// تعريف نوع البيانات لنقاط الامتحان
interface ExamPoint {
  id: number;
  examId: number;
  studentId: number;
  grade: number;
}

type ExamSummary = {
  totalExams: number;
  pendingExams: number;
  completedExams: number;
  upcomingExams: number;
  examsByType: {
    type: string;
    count: number;
  }[];
};

type StudentSummary = {
  totalStudents: number;
  topPerformers: number;
  needsImprovement: number;
  averageScore: number;
};

type RewardSummary = {
  totalRewards: number;
  rewardsAwarded: number;
  topReward: {
    name: string;
    count: number;
  };
};

// دالة لتحويل نوع التقييم إلى نص مقروء
const getEvaluationTypeLabel = (type: EvaluationType): string => {
  const labels = {
    QURAN_MEMORIZATION: 'حفظ القرآن',
    QURAN_RECITATION: 'تلاوة القرآن',
    WRITTEN_EXAM: 'تحريري',
    ORAL_EXAM: 'شفهي',
    PRACTICAL_TEST: 'عملي'
  };
  return labels[type as keyof typeof labels] || String(type);
};

export default function EvaluationDashboardPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateExamOpen, setIsCreateExamOpen] = useState(false);
  const [examTypes, setExamTypes] = useState<{id: number, name: string}[]>([]);
  const [isExamTypesLoading, setIsExamTypesLoading] = useState(true);
  const [evaluationTypes, setEvaluationTypes] = useState<{id: string, name: string, description: string}[]>([]);
  const [isEvaluationTypesLoading, setIsEvaluationTypesLoading] = useState(true);
  const [evaluationCriteria, setEvaluationCriteria] = useState<{id: number, name: string, weight: number, description: string}[]>([]);
  const [selectedCriteria, setSelectedCriteria] = useState<number[]>([]);
  const [isEvaluationCriteriaLoading, setIsEvaluationCriteriaLoading] = useState(true);
  const [subjects, setSubjects] = useState<{id: number, name: string}[]>([]);
  const [isSubjectsLoading, setIsSubjectsLoading] = useState(true);
  const [examSummary, setExamSummary] = useState<ExamSummary>({
    totalExams: 0,
    pendingExams: 0,
    completedExams: 0,
    upcomingExams: 0,
    examsByType: []
  });
  const [studentSummary, setStudentSummary] = useState<StudentSummary>({
    totalStudents: 0,
    topPerformers: 0,
    needsImprovement: 0,
    averageScore: 0
  });
  const [rewardSummary, setRewardSummary] = useState<RewardSummary>({
    totalRewards: 0,
    rewardsAwarded: 0,
    topReward: {
      name: '',
      count: 0
    }
  });
  const [recentActivities, setRecentActivities] = useState<any[]>([]);

  useEffect(() => {
    // جلب أنواع الامتحانات
    const fetchExamTypes = async () => {
      try {
        setIsExamTypesLoading(true);
        const response = await fetch('/api/exam-types');
        if (response.ok) {
          const data = await response.json();
          console.log('Exam types data:', data);
          if (Array.isArray(data)) {
            setExamTypes(data);
          } else if (data && Array.isArray(data.data)) {
            setExamTypes(data.data);
          } else {
            console.error('Unexpected exam types data format:', data);
            setExamTypes([]);
          }
        } else {
          console.error('Failed to fetch exam types:', response.status);
          setExamTypes([]);
        }
      } catch (error) {
        console.error('Error fetching exam types:', error);
        setExamTypes([]);
      } finally {
        setIsExamTypesLoading(false);
      }
    };

    // جلب المواد الدراسية
    const fetchSubjects = async () => {
      try {
        setIsSubjectsLoading(true);
        const response = await fetch('/api/subjects');
        if (response.ok) {
          const data = await response.json();
          console.log('Subjects data:', data);
          if (Array.isArray(data)) {
            setSubjects(data);
          } else if (data && Array.isArray(data.data)) {
            setSubjects(data.data);
          } else {
            console.error('Unexpected subjects data format:', data);
            setSubjects([]);
          }
        } else {
          console.error('Failed to fetch subjects:', response.status);
          setSubjects([]);
        }
      } catch (error) {
        console.error('Error fetching subjects:', error);
        setSubjects([]);
      } finally {
        setIsSubjectsLoading(false);
      }
    };

    // جلب معايير التقييم
  const fetchEvaluationCriteria = async () => {
    try {
      setIsEvaluationCriteriaLoading(true);
      const response = await fetch('/api/evaluation-criteria');
      if (response.ok) {
        const data = await response.json();
        console.log('Evaluation criteria data:', data);
        if (data.success && Array.isArray(data.data)) {
          // إذا كانت قاعدة البيانات تحتوي على معايير تقييم، نعرضها
          setEvaluationCriteria(data.data);
        } else {
          console.error('Unexpected evaluation criteria data format:', data);
          // إذا كان هناك خطأ في تنسيق البيانات، نترك القائمة فارغة
          setEvaluationCriteria([]);
        }
      } else {
        console.error('Failed to fetch evaluation criteria:', response.status);
        // إذا فشل الطلب، نترك القائمة فارغة
        setEvaluationCriteria([]);
      }
    } catch (error) {
      console.error('Error fetching evaluation criteria:', error);
      // إذا حدث خطأ، نترك القائمة فارغة
      setEvaluationCriteria([]);
    } finally {
      setIsEvaluationCriteriaLoading(false);
    }
  };

  // جلب أنواع التقييم
  const fetchEvaluationTypes = async () => {
      try {
        setIsEvaluationTypesLoading(true);
        const response = await fetch('/api/evaluation/types');
        if (response.ok) {
          const data = await response.json();
          console.log('Evaluation types data:', data);
          if (Array.isArray(data)) {
            setEvaluationTypes(data);
          } else {
            console.error('Unexpected evaluation types data format:', data);
            // إذا لم يتم الحصول على البيانات بشكل صحيح، نترك القائمة فارغة
            setEvaluationTypes([]);
          }
        } else {
          console.error('Failed to fetch evaluation types:', response.status);
          // إذا فشل الطلب، نترك القائمة فارغة
          setEvaluationTypes([]);
        }
      } catch (error) {
        console.error('Error fetching evaluation types:', error);
        // إذا حدث خطأ، نترك القائمة فارغة
        setEvaluationTypes([]);
      } finally {
        setIsEvaluationTypesLoading(false);
      }
    };

    fetchExamTypes();
    fetchEvaluationTypes();
    fetchEvaluationCriteria();
    fetchSubjects();
  }, []);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // جلب بيانات لوحة التحكم من API المخصص
        console.log('Fetching dashboard data...');
        const dashboardResponse = await fetch('/api/admin/evaluation/dashboard');
        console.log('Dashboard response status:', dashboardResponse.status);

        if (!dashboardResponse.ok) {
          const errorText = await dashboardResponse.text();
          console.error('Dashboard API error:', errorText);
          throw new Error('فشل في جلب بيانات لوحة التحكم');
        }

        const dashboardData = await dashboardResponse.json();
        console.log('Dashboard data received:', dashboardData);

        if (dashboardData.success && dashboardData.data) {
          console.log('Setting dashboard data...');
          setExamSummary(dashboardData.data.examSummary);
          setStudentSummary(dashboardData.data.studentSummary);
          setRewardSummary(dashboardData.data.rewardSummary);
          // setRecentActivities(dashboardData.data.recentActivities); // غير مستخدم حالياً في الواجهة
          console.log('Dashboard data set successfully');
        } else {
          console.error('Invalid dashboard data structure:', dashboardData);
          throw new Error('فشل في معالجة بيانات لوحة التحكم');
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast.error('حدث خطأ أثناء جلب البيانات');

        // تعيين قيم افتراضية في حالة الخطأ
        setExamSummary({
          totalExams: 0,
          pendingExams: 0,
          completedExams: 0,
          upcomingExams: 0,
          examsByType: []
        });

        setStudentSummary({
          totalStudents: 0,
          topPerformers: 0,
          needsImprovement: 0,
          averageScore: 0
        });

        setRewardSummary({
          totalRewards: 0,
          rewardsAwarded: 0,
          topReward: {
            name: '',
            count: 0
          }
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  return (
    <ProtectedRoute requiredPermission="admin.evaluation.dashboard.view">
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-2 w-full md:w-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">إدارة الامتحانات والتقييم</h1>
          <Link href="/admin/evaluation/help" className="flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] transition-colors duration-300">
            <HelpCircle className="ml-2" size={18} />
            <span>دليل المساعدة</span>
          </Link>
        </div>
        <Button
          onClick={() => setIsCreateExamOpen(true)}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
        >
          <Plus className="ml-2" size={16} />
          إنشاء امتحان جديد
        </Button>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 bg-[#e9f7f5] p-1 rounded-xl">
          <TabsTrigger value="overview" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">نظرة عامة</TabsTrigger>
          <TabsTrigger value="exams" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">الامتحانات</TabsTrigger>
          <TabsTrigger value="students" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">الطلاب</TabsTrigger>
          <TabsTrigger value="rewards" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">المكافآت</TabsTrigger>
        </TabsList>

        {isLoading ? (
          <div className="flex flex-col justify-center items-center h-64 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
            <div className="relative">
              <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
              <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
            </div>
            <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري التحميل...</span>
          </div>
        ) : (
          <>
            <TabsContent value="overview">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <FileText className="ml-2 text-[var(--primary-color)]" size={20} />
                      الامتحانات
                    </CardTitle>
                    <CardDescription>إحصائيات الامتحانات</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {examSummary.totalExams}
                      </span>
                      <span>امتحان</span>
                    </div>
                    <p className="text-sm text-gray-500">إجمالي الامتحانات</p>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">قيد الانتظار</span>
                        <span className="text-sm font-medium">{examSummary.pendingExams}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">مكتملة</span>
                        <span className="text-sm font-medium">{examSummary.completedExams}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">قادمة</span>
                        <span className="text-sm font-medium">{examSummary.upcomingExams}</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Link href="/admin/evaluation/results" className="text-[var(--primary-color)] text-sm hover:underline">
                      عرض التفاصيل
                    </Link>
                  </CardFooter>
                </Card>

                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <Users className="ml-2 text-[var(--primary-color)]" size={20} />
                      الطلاب
                    </CardTitle>
                    <CardDescription>أداء الطلاب</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {studentSummary.totalStudents}
                      </span>
                      <span>طالب</span>
                    </div>
                    <p className="text-sm text-gray-500">إجمالي الطلاب</p>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">متفوقون</span>
                        <span className="text-sm font-medium">{studentSummary.topPerformers}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">بحاجة لتحسين</span>
                        <span className="text-sm font-medium">{studentSummary.needsImprovement}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">متوسط الدرجات</span>
                        <span className="text-sm font-medium">{studentSummary.averageScore}/10</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Link href="/admin/honor-board" className="text-[var(--primary-color)] text-sm hover:underline">
                      عرض لوحة الشرف
                    </Link>
                  </CardFooter>
                </Card>

                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg flex items-center">
                      <Award className="ml-2 text-[var(--primary-color)]" size={20} />
                      المكافآت
                    </CardTitle>
                    <CardDescription>إحصائيات المكافآت</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {rewardSummary.totalRewards}
                      </span>
                      <span>مكافأة</span>
                    </div>
                    <p className="text-sm text-gray-500">إجمالي المكافآت</p>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm">المكافآت الممنوحة</span>
                        <span className="text-sm font-medium">{rewardSummary.rewardsAwarded}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">أكثر مكافأة</span>
                        <span className="text-sm font-medium">{rewardSummary.topReward.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">عدد المرات</span>
                        <span className="text-sm font-medium">{rewardSummary.topReward.count}</span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Link href="/admin/rewards" className="text-[var(--primary-color)] text-sm hover:underline">
                      إدارة المكافآت
                    </Link>
                  </CardFooter>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="border border-[#e9f7f5] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg">الامتحانات القادمة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {examSummary.upcomingExams > 0 ? (
                        <div className="flex items-center p-3 border rounded-lg bg-[#f8fffd] hover:shadow-md transition-shadow duration-300">
                          <Calendar className="ml-3 text-[var(--primary-color)]" size={24} />
                          <div>
                            <h3 className="font-medium">امتحان قادم</h3>
                            <p className="text-sm text-gray-500">يوجد {examSummary.upcomingExams} امتحان قادم</p>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center p-3 border rounded-lg bg-gray-50 hover:shadow-md transition-shadow duration-300">
                          <Calendar className="ml-3 text-gray-400" size={24} />
                          <div>
                            <h3 className="font-medium">لا توجد امتحانات قادمة</h3>
                            <p className="text-sm text-gray-500">لم يتم تحديد امتحانات قادمة</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border border-[#e9f7f5] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg">روابط سريعة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 p-1">
                      <Link href="/admin/evaluation/criteria">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <BookOpen className="ml-2" size={16} />
                          معايير التقييم
                        </Button>
                      </Link>
                      <Link href="/admin/evaluation/evaluation-types">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <BookOpen className="ml-2" size={16} />
                          أنواع التقييم
                        </Button>
                      </Link>
                      <Link href="/admin/evaluation/exam-types">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <FileText className="ml-2" size={16} />
                          أنواع الامتحانات
                        </Button>
                      </Link>
                      <Link href="/admin/evaluation/exams">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <FileText className="ml-2" size={16} />
                          إدارة الامتحانات
                        </Button>
                      </Link>
                      <Link href="/admin/evaluation/results">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <BarChart className="ml-2" size={16} />
                          نتائج الامتحانات
                        </Button>
                      </Link>
                      <Link href="/admin/honor-board">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <Award className="ml-2" size={16} />
                          لوحة الشرف
                        </Button>
                      </Link>
                      <Link href="/admin/evaluation/exam-report">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <FileText className="ml-2" size={16} />
                          كشف الامتحانات
                        </Button>
                      </Link>
                      <Link href="/admin/evaluation/student-report">
                        <Button variant="outline" className="w-full justify-start hover:bg-[#e9f7f5] hover:text-[var(--primary-color)] transition-colors duration-300">
                          <Users className="ml-2" size={16} />
                          كشف درجات الطالب
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="exams">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {examSummary.examsByType.map((type, index) => (
                  <Card key={index} className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{type.type}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-[var(--primary-color)]">{type.count}</div>
                      <p className="text-sm text-gray-500">امتحان</p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="space-y-4">
                <div className="flex justify-between">
                  <h2 className="text-xl font-bold">الامتحانات الأخيرة</h2>
                  <Link href="/admin/evaluation/results">
                    <Button variant="outline">عرض الكل</Button>
                  </Link>
                </div>

                <Card className="border border-[#e9f7f5] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="divide-y">
                      {examSummary.totalExams > 0 ? (
                        examSummary.examsByType.map((type, index) => (
                          <div key={index} className="flex justify-between items-center p-4">
                            <div>
                              <h3 className="font-medium">امتحان {type.type}</h3>
                              <p className="text-sm text-gray-500">{type.count} امتحان</p>
                            </div>
                            <div className="flex space-x-2">
                              <Link href={`/admin/evaluation/results?type=${encodeURIComponent(type.type)}`}>
                                <Button variant="outline" size="sm">عرض النتائج</Button>
                              </Link>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="flex justify-center items-center p-8">
                          <p className="text-gray-500">لا توجد امتحانات متاحة</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="students">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">الطلاب المتفوقون</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {studentSummary.topPerformers}
                      </span>
                      <span>طالب</span>
                    </div>
                    <p className="text-sm text-gray-500">طالب</p>
                  </CardContent>
                </Card>
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">متوسط الدرجات</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {studentSummary.averageScore}
                      </span>
                      <span>/10</span>
                    </div>
                    <p className="text-sm text-gray-500">درجة</p>
                  </CardContent>
                </Card>
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">بحاجة لتحسين</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {studentSummary.needsImprovement}
                      </span>
                      <span>طالب</span>
                    </div>
                    <p className="text-sm text-gray-500">طالب</p>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between">
                  <h2 className="text-xl font-bold">أفضل الطلاب</h2>
                  <Link href="/admin/honor-board">
                    <Button variant="outline">عرض لوحة الشرف</Button>
                  </Link>
                </div>

                <Card className="border border-[#e9f7f5] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="divide-y">
                      {studentSummary.topPerformers > 0 ? (
                        <div className="flex justify-between items-center p-4">
                          <div className="flex items-center">
                            <div className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white w-10 h-10 rounded-full flex items-center justify-center ml-3 shadow-md">★</div>
                            <div>
                              <h3 className="font-medium">الطلاب المتفوقون</h3>
                              <p className="text-sm text-gray-500">{studentSummary.topPerformers} طالب</p>
                            </div>
                          </div>
                          <div className="text-lg font-bold text-[var(--primary-color)]">{studentSummary.averageScore}/10</div>
                        </div>
                      ) : (
                        <div className="flex justify-center items-center p-8">
                          <p className="text-gray-500">لا توجد بيانات متاحة</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="rewards">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">إجمالي المكافآت</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {rewardSummary.totalRewards}
                      </span>
                      <span>مكافأة</span>
                    </div>
                    <p className="text-sm text-gray-500">مكافأة</p>
                  </CardContent>
                </Card>
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">المكافآت الممنوحة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-[var(--primary-color)] flex items-center">
                      <span className="bg-[#e9f7f5] text-[var(--primary-color)] w-10 h-10 rounded-full flex items-center justify-center mr-2 text-xl">
                        {rewardSummary.rewardsAwarded}
                      </span>
                      <span>مكافأة</span>
                    </div>
                    <p className="text-sm text-gray-500">مكافأة</p>
                  </CardContent>
                </Card>
                <Card className="border-t-4 border-[var(--primary-color)] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">أكثر مكافأة</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-xl font-bold text-[var(--primary-color)]">{rewardSummary.topReward.name}</div>
                    <p className="text-sm text-gray-500">{rewardSummary.topReward.count} مرة</p>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between">
                  <h2 className="text-xl font-bold">المكافآت الأخيرة</h2>
                  <Link href="/admin/rewards">
                    <Button variant="outline">إدارة المكافآت</Button>
                  </Link>
                </div>

                <Card className="border border-[#e9f7f5] shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-0">
                    <div className="divide-y">
                      {rewardSummary.totalRewards > 0 ? (
                        <div className="flex justify-between items-center p-4">
                          <div className="flex items-center">
                            <div className="bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full p-2 ml-3 shadow-md">
                              <Award className="text-white" size={20} />
                            </div>
                            <div>
                              <h3 className="font-medium">{rewardSummary.topReward.name || 'المكافآت'}</h3>
                              <p className="text-sm text-gray-500">{rewardSummary.totalRewards} مكافأة متاحة</p>
                            </div>
                          </div>
                          <div className="text-sm text-gray-500">{rewardSummary.rewardsAwarded} ممنوحة</div>
                        </div>
                      ) : (
                        <div className="flex justify-center items-center p-8">
                          <p className="text-gray-500">لا توجد مكافآت متاحة</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </>
        )}
      </Tabs>

      {/* Create Exam Dialog */}
      <Dialog open={isCreateExamOpen} onOpenChange={(open) => !open && setIsCreateExamOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)] w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>إنشاء امتحان جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل الامتحان الجديد</DialogDescription>
          </DialogHeader>
          <form
            className="space-y-4"
            onSubmit={async (e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget as HTMLFormElement);
              const data = {
                evaluationType: formData.get('evaluationType'),
                month: formData.get('month'),
                description: formData.get('description') || null,
                maxPoints: Number(formData.get('maxPoints')),
                passingPoints: Number(formData.get('passingPoints')),
                typeId: formData.get('examTypeId') || null,
                subjectId: formData.get('subjectId') || null,
                requiresSurah: formData.get('evaluationType') === 'QURAN_MEMORIZATION',
                criteriaIds: selectedCriteria.length > 0 ? selectedCriteria : undefined,
                isPeriodic: formData.get('isPeriodic') === 'true',
                period: formData.get('period') || null
              };

              try {
                const response = await fetch('/api/evaluation/exams', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(data),
                });

                const result = await response.json();

                if (result.success) {
                  toast.success('تم إنشاء الامتحان بنجاح');
                  setIsCreateExamOpen(false);
                  // إعادة تحميل البيانات
                  window.location.reload();
                } else {
                  toast.error(result.error || 'حدث خطأ أثناء إنشاء الامتحان');
                }
              } catch (error) {
                console.error('Error creating exam:', error);
                toast.error('حدث خطأ أثناء إنشاء الامتحان');
              }
            }}
          >
            <div className="space-y-2">
              <label className="text-right block">نوع التقييم</label>
              <Select name="evaluationType" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع التقييم" />
                </SelectTrigger>
                <SelectContent>
                  {isEvaluationTypesLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل أنواع التقييم...</SelectItem>
                  ) : evaluationTypes.length > 0 ? (
                    evaluationTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>{type.description}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد أنواع تقييم متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">نوع الامتحان</label>
              <Select name="examTypeId" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع الامتحان" />
                </SelectTrigger>
                <SelectContent>
                  {isExamTypesLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل أنواع الامتحانات...</SelectItem>
                  ) : examTypes.length > 0 ? (
                    examTypes.map(type => (
                      <SelectItem key={type.id} value={type.id.toString()}>{type.name}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد أنواع امتحانات متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">المادة الدراسية</label>
              <Select name="subjectId" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر المادة الدراسية" />
                </SelectTrigger>
                <SelectContent>
                  {isSubjectsLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل المواد الدراسية...</SelectItem>
                  ) : subjects.length > 0 ? (
                    subjects.map(subject => (
                      <SelectItem key={subject.id} value={subject.id.toString()}>{subject.name}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد مواد دراسية متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الشهر</label>
              <Input type="month" className="text-right" name="month" required />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Input className="text-right" placeholder="وصف الامتحان" name="description" />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الدرجة القصوى</label>
              <Input type="number" defaultValue={100} className="text-right" name="maxPoints" required />
            </div>
            <div className="space-y-2">
              <label className="text-right block">درجة النجاح</label>
              <Input type="number" defaultValue={60} className="text-right" name="passingPoints" required />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-right block">امتحان دوري</label>
                <Select name="isPeriodic" defaultValue="false">
                  <SelectTrigger className="w-32 text-right">
                    <SelectValue placeholder="اختر" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">نعم</SelectItem>
                    <SelectItem value="false">لا</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-right block">الفترة (للامتحانات الدورية)</label>
              <Select name="period">
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر الفترة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="يومي">يومي</SelectItem>
                  <SelectItem value="أسبوعي">أسبوعي</SelectItem>
                  <SelectItem value="شهري">شهري</SelectItem>
                  <SelectItem value="فصلي">فصلي</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-right block">معايير التقييم</label>
              <div className="border border-[#e9f7f5] rounded-md p-3 max-h-60 overflow-y-auto bg-white">
                {isEvaluationCriteriaLoading ? (
                  <div className="text-center py-2">
                    <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                    <p className="text-sm text-gray-500 mt-1">جاري تحميل معايير التقييم...</p>
                  </div>
                ) : evaluationCriteria.length > 0 ? (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm p-1 mb-2 border-b pb-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCriteria(evaluationCriteria.map(c => c.id))}
                      >
                        تحديد الكل
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCriteria([])}
                      >
                        إلغاء تحديد الكل
                      </Button>
                    </div>
                    {evaluationCriteria.map(criteria => (
                      <div key={criteria.id} className="flex justify-between items-center text-sm p-1 hover:bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={`criteria-${criteria.id}`}
                            checked={selectedCriteria.includes(criteria.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCriteria(prev => [...prev, criteria.id]);
                              } else {
                                setSelectedCriteria(prev => prev.filter(id => id !== criteria.id));
                              }
                            }}
                          />
                          <span className="text-gray-500">{(Number(criteria.weight) * 100).toFixed(0)}%</span>
                        </div>
                        <label
                          htmlFor={`criteria-${criteria.id}`}
                          className="font-medium cursor-pointer flex-1 text-right"
                        >
                          {criteria.name}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-sm text-gray-500 py-2">لا توجد معايير تقييم متاحة</p>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1 text-right">اختر معايير التقييم التي سيتم استخدامها عند تقييم الطلاب في هذا الامتحان</p>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateExamOpen(false)}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                إنشاء
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
      </div>
    </ProtectedRoute>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/expenses/reminders - إنشاء تذكير مصروف جديد
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const {
      title,
      description,
      amount,
      categoryId,
      dueDate,
      reminderDate,
      priority,
      status,
      notifyByEmail,
      notifyByPush,
      emailAddress,
    } = body;

    // التحقق من صحة البيانات
    if (!title || !dueDate) {
      return NextResponse.json(
        { error: 'العنوان وتاريخ الاستحقاق مطلوبان' },
        { status: 400 }
      );
    }

    if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة إذا تم تحديدها
    if (categoryId) {
      const category = await prisma.expenseCategory.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'فئة المصروفات غير موجودة' },
          { status: 400 }
        );
      }
    }

    // إنشاء تذكير المصروف
    const reminder = await prisma.expenseReminder.create({
      data: {
        title,
        description: description || null,
        amount: amount || null,
        categoryId: categoryId || null,
        dueDate: new Date(dueDate),
        reminderDate: reminderDate ? new Date(reminderDate) : null,
        priority: priority || 'MEDIUM',
        status: status || 'PENDING',
        notifyByEmail: notifyByEmail || false,
        notifyByPush: notifyByPush || false,
        emailAddress: emailAddress || null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      include: {
        category: true,
      },
    });

    return NextResponse.json(reminder, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء تذكير المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء تذكير المصروف' },
      { status: 500 }
    );
  }
}

// GET /api/expenses/reminders - الحصول على تذكيرات المصروفات
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const categoryId = searchParams.get('categoryId');
    const upcoming = searchParams.get('upcoming');
    const overdue = searchParams.get('overdue');

    // بناء شروط البحث
    const where: {
      status?: string;
      priority?: string;
      categoryId?: number;
      dueDate?: {
        gte?: Date;
        lt?: Date;
      };
    } = {};

    if (status) {
      where.status = status;
    }

    if (priority) {
      where.priority = priority;
    }

    if (categoryId) {
      where.categoryId = parseInt(categoryId);
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (upcoming === 'true') {
      where.dueDate = {
        gte: today,
      };
      where.status = 'PENDING';
    }

    if (overdue === 'true') {
      where.dueDate = {
        lt: today,
      };
      where.status = 'PENDING';
    }

    // جلب تذكيرات المصروفات
    const reminders = await prisma.expenseReminder.findMany({
      where,
      include: {
        category: true,
      },
      orderBy: [
        { status: 'asc' },
        { dueDate: 'asc' },
        { priority: 'desc' },
      ],
    });

    // إحصائيات التذكيرات
    const stats = {
      total: await prisma.expenseReminder.count(),
      pending: await prisma.expenseReminder.count({ where: { status: 'PENDING' } }),
      completed: await prisma.expenseReminder.count({ where: { status: 'COMPLETED' } }),
      overdue: await prisma.expenseReminder.count({
        where: {
          dueDate: { lt: today },
          status: 'PENDING',
        },
      }),
      upcoming: await prisma.expenseReminder.count({
        where: {
          dueDate: { gte: today },
          status: 'PENDING',
        },
      }),
      highPriority: await prisma.expenseReminder.count({
        where: {
          priority: 'HIGH',
          status: 'PENDING',
        },
      }),
    };

    return NextResponse.json({
      reminders,
      stats,
      count: reminders.length,
    });
  } catch (error) {
    console.error('خطأ في جلب تذكيرات المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب تذكيرات المصروفات' },
      { status: 500 }
    );
  }
}

// PUT /api/expenses/reminders/:id - تحديث تذكير مصروف
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);
    const body = await req.json();
    const {
      title,
      description,
      amount,
      categoryId,
      dueDate,
      reminderDate,
      priority,
      status,
      notifyByEmail,
      notifyByPush,
      emailAddress,
    } = body;

    // التحقق من وجود التذكير
    const existingReminder = await prisma.expenseReminder.findUnique({
      where: { id }
    });

    if (!existingReminder) {
      return NextResponse.json(
        { error: 'تذكير المصروف غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من صحة البيانات
    if (title === '') {
      return NextResponse.json(
        { error: 'العنوان مطلوب' },
        { status: 400 }
      );
    }

    if (amount !== undefined && amount !== null && (typeof amount !== 'number' || amount <= 0)) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة إذا تم تحديدها
    if (categoryId) {
      const category = await prisma.expenseCategory.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'فئة المصروفات غير موجودة' },
          { status: 400 }
        );
      }
    }

    // تحديث تذكير المصروف
    const updatedReminder = await prisma.expenseReminder.update({
      where: { id },
      data: {
        title: title !== undefined ? title : undefined,
        description: description !== undefined ? (description || null) : undefined,
        amount: amount !== undefined ? (amount || null) : undefined,
        categoryId: categoryId !== undefined ? (categoryId || null) : undefined,
        dueDate: dueDate !== undefined ? new Date(dueDate) : undefined,
        reminderDate: reminderDate !== undefined ? (reminderDate ? new Date(reminderDate) : null) : undefined,
        priority: priority !== undefined ? priority : undefined,
        status: status !== undefined ? status : undefined,
        notifyByEmail: notifyByEmail !== undefined ? notifyByEmail : undefined,
        notifyByPush: notifyByPush !== undefined ? notifyByPush : undefined,
        emailAddress: emailAddress !== undefined ? (emailAddress || null) : undefined,
        updatedAt: new Date(),
      },
      include: {
        category: true,
      },
    });

    return NextResponse.json(updatedReminder);
  } catch (error) {
    console.error('خطأ في تحديث تذكير المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث تذكير المصروف' },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses/reminders/:id - حذف تذكير مصروف
export async function DELETE(_req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود التذكير
    const existingReminder = await prisma.expenseReminder.findUnique({
      where: { id }
    });

    if (!existingReminder) {
      return NextResponse.json(
        { error: 'تذكير المصروف غير موجود' },
        { status: 404 }
      );
    }

    // حذف تذكير المصروف
    await prisma.expenseReminder.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف تذكير المصروف بنجاح',
    });
  } catch (error) {
    console.error('خطأ في حذف تذكير المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في حذف تذكير المصروف' },
      { status: 500 }
    );
  }
}

// POST /api/expenses/reminders/:id/complete - إكمال تذكير مصروف وإنشاء مصروف
export async function COMPLETE(req: NextRequest, { params }: { params: { id: string; action: string } }) {
  if (params.action !== 'complete') {
    return NextResponse.json(
      { error: 'إجراء غير صالح' },
      { status: 400 }
    );
  }

  try {
    const id = parseInt(params.id);
    const body = await req.json();
    const { createExpense } = body;

    // التحقق من وجود التذكير
    const reminder = await prisma.expenseReminder.findUnique({
      where: { id },
      include: {
        category: true,
      },
    });

    if (!reminder) {
      return NextResponse.json(
        { error: 'تذكير المصروف غير موجود' },
        { status: 404 }
      );
    }

    // تحديث حالة التذكير إلى مكتمل
    const updatedReminder = await prisma.expenseReminder.update({
      where: { id },
      data: {
        status: 'COMPLETED',
        updatedAt: new Date(),
      },
    });

    // إنشاء مصروف إذا تم طلب ذلك
    let expense = null;
    if (createExpense && reminder.amount !== null && reminder.amount !== undefined) {
      // الحصول على الخزينة
      const treasury = await prisma.treasury.findFirst();

      if (!treasury) {
        return NextResponse.json(
          { error: 'لم يتم العثور على الخزينة' },
          { status: 404 }
        );
      }

      const amount = reminder.amount; // تخزين القيمة في متغير للتأكد من أنها ليست null

      // إنشاء المصروف وتحديث الخزينة في معاملة واحدة
      const result = await prisma.$transaction(async (tx) => {
        // إنشاء المصروف
        const newExpense = await tx.expense.create({
          data: {
            purpose: reminder.title,
            amount: amount,
            treasuryId: treasury.id,
            categoryId: reminder.categoryId,
            date: new Date(),
            notes: reminder.description,
          },
          include: {
            category: true,
          },
        });

        // تحديث رصيد الخزينة وإجمالي المصاريف
        await tx.treasury.update({
          where: { id: treasury.id },
          data: {
            balance: { decrement: amount },
            totalExpense: { increment: amount },
          },
        });

        return newExpense;
      });

      expense = result;
    }

    return NextResponse.json({
      success: true,
      message: 'تم إكمال تذكير المصروف بنجاح',
      reminder: updatedReminder,
      expense,
    });
  } catch (error) {
    console.error('خطأ في إكمال تذكير المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في إكمال تذكير المصروف' },
      { status: 500 }
    );
  }
}

// POST /api/expenses/reminders/check - التحقق من التذكيرات المستحقة وإرسال الإشعارات
export async function CHECK(_req: NextRequest, { params }: { params: { action: string } }) {
  if (params.action !== 'check') {
    return NextResponse.json(
      { error: 'إجراء غير صالح' },
      { status: 400 }
    );
  }

  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // جلب التذكيرات المستحقة
    const dueReminders = await prisma.expenseReminder.findMany({
      where: {
        status: 'PENDING',
        OR: [
          // التذكيرات التي حان موعد تذكيرها
          {
            reminderDate: {
              lte: new Date(),
            },
          },
          // التذكيرات التي حان موعد استحقاقها
          {
            dueDate: {
              lte: new Date(),
            },
          },
        ],
      },
      include: {
        category: true,
      },
    });

    // إرسال الإشعارات (يمكن تنفيذ هذا الجزء حسب نظام الإشعارات المستخدم)
    const notificationsSent = [];

    for (const reminder of dueReminders) {
      // إرسال إشعار بالبريد الإلكتروني إذا كان مطلوبًا
      if (reminder.notifyByEmail && reminder.emailAddress) {
        // هنا يمكن إضافة كود لإرسال البريد الإلكتروني
        notificationsSent.push({
          reminderId: reminder.id,
          type: 'email',
          recipient: reminder.emailAddress,
          title: reminder.title,
          dueDate: reminder.dueDate,
        });
      }

      // إرسال إشعار دفع إذا كان مطلوبًا
      if (reminder.notifyByPush) {
        // هنا يمكن إضافة كود لإرسال إشعار الدفع
        notificationsSent.push({
          reminderId: reminder.id,
          type: 'push',
          title: reminder.title,
          dueDate: reminder.dueDate,
        });
      }
    }

    return NextResponse.json({
      success: true,
      dueReminders: dueReminders.length,
      notificationsSent,
    });
  } catch (error) {
    console.error('خطأ في التحقق من التذكيرات المستحقة:', error);
    return NextResponse.json(
      { error: 'فشل في التحقق من التذكيرات المستحقة' },
      { status: 500 }
    );
  }
}

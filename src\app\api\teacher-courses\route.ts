import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { verifyToken } from '@/utils/verifyToken';

// GET /api/teacher-courses - جلب المقررات الخاصة بالمعلم المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const userId = await verifyToken(request);
    if (!userId) {
      return NextResponse.json(
        { success: false, message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userId.id
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // جلب المواد التي يدرسها المعلم مع الفصول
    const teacherSubjects = await prisma.teacherSubject.findMany({
      where: {
        teacherId: teacher.id
      },
      include: {
        subject: true,
        classes: {
          include: {
            classe: {
              include: {
                students: {
                  select: {
                    id: true
                  }
                }
              }
            }
          }
        }
      }
    });

    // تنسيق البيانات للعرض
    const courses = [];

    for (const teacherSubject of teacherSubjects) {
      for (const classSubject of teacherSubject.classes) {
        courses.push({
          id: classSubject.id,
          subjectId: teacherSubject.subjectId,
          subjectName: teacherSubject.subject.name,
          className: classSubject.classe.name,
          classId: classSubject.classeId,
          studentsCount: classSubject.classe.students.length,
          description: teacherSubject.subject.description || ''
        });
      }
    }

    return NextResponse.json({
      success: true,
      courses,
      message: "تم جلب بيانات المقررات بنجاح"
    });
  } catch (error) {
    console.error('Error fetching teacher courses:', error);
    return NextResponse.json(
      { success: false, message: "حدث خطأ أثناء جلب بيانات المقررات" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
// تحقق من وجود ملف activity-logger
import { ActivityLogger, ActivityType } from '@/lib/activity-logger';

// POST /api/students/import
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { students } = body;

    if (!students || !Array.isArray(students) || students.length === 0) {
      return NextResponse.json(
        { error: 'لا توجد بيانات صالحة للاستيراد' },
        { status: 400 }
      );
    }

    // تحويل أسماء الحقول من العربية إلى الإنجليزية
    const fieldMapping: Record<string, string> = {
      'اسم المستخدم': 'username',
      'الاسم الكامل': 'name',
      'العمر': 'age',
      'رقم الهاتف': 'phone',
      'معرف القسم': 'classeId',
      'معرف الولي': 'guardianId',
      'القسم': 'classeName',
      'الولي': 'guardianName'
    };

    // تحويل البيانات وإضافتها إلى قاعدة البيانات
    let imported = 0;
    let errors = 0;
    const results = [];

    for (const student of students) {
      try {
        // تحويل البيانات
        const studentData: {
          username: string;
          name: string;
          age: number;
          phone: string | null;
          classeId?: number;
          guardianId?: number;
          classeName?: string;
          guardianName?: string;
        } = {
          username: '',
          name: '',
          age: 0,
          phone: null
        };

        for (const [arabicField, value] of Object.entries(student)) {
          const englishField = fieldMapping[arabicField];
          if (englishField) {
            if (englishField === 'username' || englishField === 'name' || englishField === 'classeName' || englishField === 'guardianName') {
              studentData[englishField] = value ? String(value) : '';
            } else if (englishField === 'age') {
              studentData[englishField] = Number(value);
            } else if (englishField === 'phone') {
              studentData[englishField] = value ? String(value) : null;
            } else if (englishField === 'classeId' || englishField === 'guardianId') {
              // تحويل معرفات الأقسام والأولياء إلى أرقام إذا كانت موجودة
              if (value && String(value).trim() !== '' && String(value).trim() !== '-') {
                studentData[englishField] = Number(value);
              }
            }
          }
        }

        // التحقق من البيانات المطلوبة
        if (!studentData.username || !studentData.name || !studentData.age) {
          throw new Error('بيانات غير مكتملة');
        }

        // التحقق من عدم وجود اسم مستخدم مكرر
        const existingStudent = await prisma.student.findUnique({
          where: { username: studentData.username }
        });

        if (existingStudent) {
          throw new Error('اسم المستخدم موجود بالفعل');
        }

        // البحث عن القسم بالاسم إذا تم توفيره
        if (studentData.classeName && studentData.classeName.trim() !== '' && studentData.classeName.trim() !== '-' && !studentData.classeId) {
          const classe = await prisma.classe.findFirst({
            where: { name: studentData.classeName }
          });
          if (classe) {
            studentData.classeId = classe.id;
          } else {
            console.log(`لم يتم العثور على قسم باسم: ${studentData.classeName}`);
          }
        }

        // البحث عن الولي بالاسم إذا تم توفيره
        if (studentData.guardianName && studentData.guardianName.trim() !== '' && studentData.guardianName.trim() !== '-' && !studentData.guardianId) {
          const guardian = await prisma.parent.findFirst({
            where: { name: studentData.guardianName }
          });
          if (guardian) {
            studentData.guardianId = guardian.id;
          } else {
            console.log(`لم يتم العثور على ولي باسم: ${studentData.guardianName}`);
          }
        }

        // التحقق من وجود القسم والولي إذا تم تحديدهما بالمعرف
        if (studentData.classeId) {
          const classe = await prisma.classe.findUnique({
            where: { id: studentData.classeId }
          });
          if (!classe) {
            throw new Error(`القسم بالمعرف ${studentData.classeId} غير موجود`);
          }
        }

        if (studentData.guardianId) {
          const guardian = await prisma.parent.findUnique({
            where: { id: studentData.guardianId }
          });
          if (!guardian) {
            throw new Error(`الولي بالمعرف ${studentData.guardianId} غير موجود`);
          }
        }

        // إضافة الطالب
        const newStudent = await prisma.student.create({
          data: {
            username: studentData.username,
            name: studentData.name,
            age: Number(studentData.age),
            phone: studentData.phone || null,
            classeId: studentData.classeId || null,
            guardianId: studentData.guardianId || null
          },
          include: {
            classe: true,
            guardian: true
          }
        });

        results.push({
          success: true,
          student: newStudent
        });

        imported++;
      } catch (error) {
        console.error('Error importing student:', error);
        errors++;
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'خطأ غير معروف',
          data: student
        });
      }
    }

    // تسجيل نشاط استيراد الطلاب
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.STUDENT_ADD, // استخدام STUDENT_ADD بدلاً من STUDENT_IMPORT
          `استيراد ${imported} طالب من ملف Excel`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط استيراد الطلاب:', error);
    }

    // جلب جميع الطلاب المستوردين مع معلومات الأقسام والأولياء
    const importedStudents = await prisma.student.findMany({
      where: {
        id: {
          in: results.filter(r => r.success && r.student).map(r => r.student?.id).filter(Boolean) as number[]
        }
      },
      include: {
        classe: true,
        guardian: true
      }
    });

    return NextResponse.json({
      success: true,
      imported,
      errors,
      results,
      students: importedStudents
    });
  } catch (error) {
    console.error('Error in import API:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء استيراد البيانات' },
      { status: 500 }
    );
  }
}

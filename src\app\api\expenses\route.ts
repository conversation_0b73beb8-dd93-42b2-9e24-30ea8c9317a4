import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/expenses - الحصول على المصاريف
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    const categoryId = searchParams.get('categoryId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const where: {
      purpose?: { contains: string };
      categoryId?: number;
      date?: {
        gte: Date;
        lte: Date;
      };
    } = {};

    if (query) {
      where.purpose = { contains: query };
    }

    if (categoryId) {
      where.categoryId = parseInt(categoryId);
    }

    // تحديد نطاق التاريخ
    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (month && year) {
      const yearNum = parseInt(year);
      const monthNum = parseInt(month);

      if (!isNaN(yearNum) && !isNaN(monthNum)) {
        const startDate = new Date(yearNum, monthNum - 1, 1);
        const endDate = new Date(yearNum, monthNum, 0);

        where.date = {
          gte: startDate,
          lte: endDate,
        };
      }
    }

    // جلب المصاريف مع الترتيب حسب التاريخ (الأحدث أولاً)
    const expenses = await prisma.expense.findMany({
      where,
      orderBy: { date: 'desc' },
      skip,
      take: limit,
      include: {
        treasury: true,
        category: true,
      },
    });

    // جلب العدد الإجمالي للمصاريف
    const total = await prisma.expense.count({ where });

    // حساب إجمالي مبالغ المصاريف
    const totalAmount = await prisma.expense.aggregate({
      _sum: {
        amount: true,
      },
      where,
    });

    // جلب إحصائيات حسب الفئة
    const categoryStats = await prisma.expense.groupBy({
      by: ['categoryId'],
      where,
      _sum: {
        amount: true,
      },
      _count: true,
    });

    // جلب معلومات الفئات
    const categories = categoryStats.length > 0
      ? await prisma.expenseCategory.findMany({
          where: {
            id: {
              in: categoryStats.map(stat => stat.categoryId).filter(id => id !== null) as number[]
            }
          }
        })
      : [];

    // دمج إحصائيات الفئات مع معلومات الفئات
    const categoriesWithStats = categoryStats.map(stat => {
      const category = categories.find(cat => cat.id === stat.categoryId);
      return {
        categoryId: stat.categoryId,
        categoryName: category?.name || 'غير مصنف',
        count: stat._count,
        amount: stat._sum.amount || 0,
      };
    });

    return NextResponse.json({
      expenses,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      },
      stats: {
        totalAmount: totalAmount._sum.amount || 0,
        categoriesStats: categoriesWithStats,
      },
    });
  } catch (error) {
    console.error('خطأ في جلب المصاريف:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المصاريف' },
      { status: 500 }
    );
  }
}

// POST /api/expenses - إنشاء مصروف جديد
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { purpose, amount, categoryId, date, receipt, notes } = body;

    if (!purpose || !amount) {
      return NextResponse.json(
        { error: 'الغرض والمبلغ مطلوبان' },
        { status: 400 }
      );
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة إذا تم تحديدها
    if (categoryId) {
      const category = await prisma.expenseCategory.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'فئة المصروفات غير موجودة' },
          { status: 400 }
        );
      }
    }

    // الحصول على الخزينة الافتراضية (نفترض أن هناك خزينة واحدة فقط)
    let treasury = await prisma.treasury.findFirst();

    // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 0,
          totalIncome: 0,
          totalExpense: 0,
        },
      });
    }

    // التحقق من وجود رصيد كافٍ
    if (treasury.balance < amount) {
      return NextResponse.json(
        { error: 'الرصيد غير كافٍ لإجراء هذا المصروف' },
        { status: 400 }
      );
    }

    // إنشاء المصروف وتحديث الخزينة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء المصروف
      const expense = await tx.expense.create({
        data: {
          purpose,
          amount,
          treasuryId: treasury.id,
          categoryId: categoryId || null,
          date: date ? new Date(date) : new Date(),
          receipt,
          notes,
        },
        include: {
          category: true,
        },
      });

      // تحديث رصيد الخزينة وإجمالي المصاريف
      await tx.treasury.update({
        where: { id: treasury.id },
        data: {
          balance: { decrement: amount },
          totalExpense: { increment: amount },
        },
      });

      return expense;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء المصروف:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء المصروف' },
      { status: 500 }
    );
  }
}

// ملاحظة: تم نقل دوال PUT و DELETE إلى ملف [id]/route.ts

'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { FaExchangeAlt, FaSave, FaUserGraduate } from 'react-icons/fa'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Student {
  id: number
  name: string
  classeId?: number | null
}

// استيراد واجهة Class من ملف page.tsx
import { Class } from './page'

interface StudentDistributionModalProps {
  isOpen: boolean
  onCloseAction: () => void
  onSuccessAction: () => void
  classItem: Class | null
  allClasses: Class[]
}

export function StudentDistributionModal({
  isOpen,
  onCloseAction,
  onSuccessAction,
  classItem,
  allClasses
}: StudentDistributionModalProps) {
  const [students, setStudents] = useState<Student[]>([])
  const [unassignedStudents, setUnassignedStudents] = useState<Student[]>([])
  const [selectedStudents, setSelectedStudents] = useState<number[]>([])
  const [targetClassId, setTargetClassId] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen && classItem) {
      fetchStudents()
      fetchUnassignedStudents()
    } else {
      setStudents([])
      setUnassignedStudents([])
      setSelectedStudents([])
      setTargetClassId('')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, classItem])

  const fetchStudents = async () => {
    if (!classItem) return

    try {
      // استخدام معلمة classeId لجلب طلاب الفصل المحدد فقط
      const response = await fetch(`/api/students?classeId=${classItem.id}`)
      if (!response.ok) throw new Error('Failed to fetch students')

      const data = await response.json()
      // التأكد من أن البيانات المستلمة هي مصفوفة
      if (data.students && Array.isArray(data.students)) {
        setStudents(data.students)
      } else if (Array.isArray(data)) {
        setStudents(data)
      } else {
        setStudents([])
        console.error('Unexpected data format:', data)
      }
    } catch (err) {
      console.error('Error fetching students:', err)
      setError('فشل في جلب بيانات الطلاب')
      setStudents([])
    }
  }

  const fetchUnassignedStudents = async () => {
    try {
      const response = await fetch('/api/students?unassigned=true')
      if (!response.ok) throw new Error('Failed to fetch unassigned students')

      const data = await response.json()
      // التأكد من أن البيانات المستلمة هي مصفوفة
      if (data.students && Array.isArray(data.students)) {
        setUnassignedStudents(data.students)
      } else if (Array.isArray(data)) {
        setUnassignedStudents(data)
      } else {
        setUnassignedStudents([])
        console.error('Unexpected data format for unassigned students:', data)
      }
    } catch (err) {
      console.error('Error fetching unassigned students:', err)
      setError('فشل في جلب بيانات الطلاب غير المسجلين')
      setUnassignedStudents([])
    }
  }

  const handleTransferStudents = async () => {
    if (!classItem || selectedStudents.length === 0 || !targetClassId) {
      toast({
        title: 'تنبيه',
        description: 'الرجاء اختيار الطلاب والفصل المستهدف',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin/student-distribution', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          studentIds: selectedStudents,
          targetClassId: parseInt(targetClassId)
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || 'Failed to transfer students')
      }

      toast({
        title: 'نجاح',
        description: 'تم نقل الطلاب بنجاح'
      })

      onSuccessAction()
      onCloseAction()
    } catch (err: Error | unknown) {
      setError(err instanceof Error ? err.message : 'فشل في نقل الطلاب')
      toast({
        title: 'خطأ',
        description: err instanceof Error ? err.message : 'فشل في نقل الطلاب',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAssignStudents = async () => {
    if (!classItem || selectedStudents.length === 0) {
      toast({
        title: 'تنبيه',
        description: 'الرجاء اختيار الطلاب',
        variant: 'destructive'
      })
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin/student-distribution', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          studentIds: selectedStudents,
          targetClassId: classItem.id
        })
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || 'Failed to assign students')
      }

      toast({
        title: 'نجاح',
        description: 'تم تعيين الطلاب بنجاح'
      })

      onSuccessAction()
      onCloseAction()
    } catch (err: Error | unknown) {
      setError(err instanceof Error ? err.message : 'فشل في تعيين الطلاب')
      toast({
        title: 'خطأ',
        description: err instanceof Error ? err.message : 'فشل في تعيين الطلاب',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const toggleStudentSelection = (studentId: number) => {
    setSelectedStudents(prev =>
      prev.includes(studentId)
        ? prev.filter(id => id !== studentId)
        : [...prev, studentId]
    )
  }

  const dialogFooter = (
    <div className="flex gap-2">
      <Button
        onClick={handleTransferStudents}
        disabled={isLoading || selectedStudents.length === 0 || !targetClassId}
        className="bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
      >
        <FaExchangeAlt className="ml-1" />
        {isLoading ? 'جاري النقل...' : 'نقل الطلاب'}
      </Button>
      <Button
        onClick={handleAssignStudents}
        disabled={isLoading || selectedStudents.length === 0 || students.length > 0}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
      >
        <FaSave className="ml-1" />
        {isLoading ? 'جاري التعيين...' : 'تعيين الطلاب'}
      </Button>
    </div>
  )

  const studentsCount = students.length
  const classCapacity = classItem?.capacity || 30
  const occupancyRate = (studentsCount / classCapacity) * 100
  const isNearCapacity = occupancyRate >= 80
  const isOverCapacity = occupancyRate > 100

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title={
        <div className="flex items-center gap-2 text-[var(--primary-color)]">
          <FaUserGraduate className="text-[var(--primary-color)]" />
          <span>توزيع الطلاب - {classItem?.name}</span>
        </div>
      }
      variant="primary"
      footer={dialogFooter}
      className="rtl"
    >
      {error && <div className="text-red-500 text-center p-2 bg-red-50 rounded-md mb-4 border border-red-100">{error}</div>}

      <div className="grid gap-4 py-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="font-medium">السعة الاستيعابية:</span>
            <span className={`${isOverCapacity ? 'text-red-600' : isNearCapacity ? 'text-yellow-500' : 'text-[var(--primary-color)]'}`}>
              {studentsCount} / {classCapacity} ({occupancyRate.toFixed(0)}%)
            </span>
          </div>
        </div>

        <div className="grid gap-2">
          <Label className="text-right font-medium text-[var(--primary-color)]">نقل الطلاب إلى فصل آخر</Label>
          <Select
            value={targetClassId}
            onValueChange={setTargetClassId}
          >
            <SelectTrigger className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
              <SelectValue placeholder="اختر الفصل المستهدف" />
            </SelectTrigger>
            <SelectContent>
              {allClasses
                .filter(cls => cls.id !== classItem?.id)
                .map(cls => (
                  <SelectItem key={cls.id} value={cls.id.toString()}>
                    {cls.name} ({cls.students?.length || 0} / {cls.capacity})
                  </SelectItem>
                ))
              }
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border border-[#e0f2ef] rounded-md p-4">
            <h3 className="font-bold text-[var(--primary-color)] mb-2 flex items-center gap-2">
              <FaUserGraduate />
              طلاب الفصل ({students.length})
            </h3>
            <div className="max-h-60 overflow-y-auto">
              {students.length === 0 ? (
                <p className="text-center text-gray-500 py-4">لا يوجد طلاب في هذا الفصل</p>
              ) : (
                <div className="space-y-2">
                  {students.map(student => (
                    <div
                      key={student.id}
                      className={`flex items-center gap-2 p-2 rounded-md cursor-pointer ${
                        selectedStudents.includes(student.id)
                          ? 'bg-[#e0f2ef] border border-[var(--primary-color)]'
                          : 'hover:bg-gray-100'
                      }`}
                      onClick={() => toggleStudentSelection(student.id)}
                    >
                      <input
                        type="checkbox"
                        checked={selectedStudents.includes(student.id)}
                        onChange={() => {}}
                        className="h-4 w-4 text-[var(--primary-color)] rounded accent-[var(--primary-color)]"
                      />
                      <span>{student.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="border border-[#e0f2ef] rounded-md p-4">
            <h3 className="font-bold text-[var(--primary-color)] mb-2 flex items-center gap-2">
              <FaUserGraduate />
              طلاب غير مسجلين ({unassignedStudents.length})
            </h3>
            <div className="max-h-60 overflow-y-auto">
              {unassignedStudents.length === 0 ? (
                <p className="text-center text-gray-500 py-4">لا يوجد طلاب غير مسجلين</p>
              ) : (
                <div className="space-y-2">
                  {unassignedStudents.map(student => (
                    <div
                      key={student.id}
                      className={`flex items-center gap-2 p-2 rounded-md cursor-pointer ${
                        selectedStudents.includes(student.id)
                          ? 'bg-[#e0f2ef] border border-[var(--primary-color)]'
                          : 'hover:bg-gray-100'
                      }`}
                      onClick={() => toggleStudentSelection(student.id)}
                    >
                      <input
                        type="checkbox"
                        checked={selectedStudents.includes(student.id)}
                        onChange={() => {}}
                        className="h-4 w-4 text-[var(--primary-color)] rounded accent-[var(--primary-color)]"
                      />
                      <span>{student.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AnimatedDialog>
  )
}

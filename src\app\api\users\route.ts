import { NextRequest, NextResponse } from "next/server";
import { UserRole } from '@prisma/client';
import prisma from '@/lib/prisma';
//import { verifyToken } from '@/utils/verifyToken';
import { getToken } from '@/lib/auth';

// Get users by role or all users for admin
export async function GET(request: NextRequest) {
    try {
        // التحقق من الصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به: يرجى تسجيل الدخول" },
                { status: 401 }
            );
        }

        // Get parameters from query
        const { searchParams } = new URL(request.url);
        const roleParam = searchParams.get('role');
        const limitParam = searchParams.get('limit');
        const activeParam = searchParams.get('active');
        const isAdmin = userData.role === 'ADMIN' || userData.role === 'TEACHER' || userData.role === 'EMPLOYEE';

        // تحديد الحد الأقصى للنتائج
        const limit = limitParam ? parseInt(limitParam) : undefined;

        // تحديد فلتر النشاط
        const activeFilter = activeParam === 'true' ? true : undefined;

        // بناء شروط البحث
        const whereConditions: any = {
            // استبعاد المستخدمين المعلقين دائماً
            role: { not: 'PENDING' }
        };

        // إضافة فلتر النشاط إذا تم تحديده
        if (activeFilter !== undefined) {
            whereConditions.isActive = activeFilter;
        }

        // إذا كان المستخدم مسؤولاً أو معلماً أو موظفاً وطلب جميع المستخدمين
        if (isAdmin && !roleParam) {
            // جلب جميع المستخدمين مع ملفاتهم الشخصية
            const users = await prisma.user.findMany({
                where: whereConditions,
                include: {
                    profile: true,
                    teacher: true,
                    employee: true
                },
                orderBy: {
                    createdAt: 'desc'
                },
                take: limit
            });

            // تنسيق البيانات للعرض
            const formattedUsers = users.map(user => ({
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role,
                name: user.profile?.name || '',
                phone: user.profile?.phone || '',
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
                status: 'active', // يمكن إضافة حقل الحالة في المستقبل
                isTeacher: !!user.teacher,
                isEmployee: !!user.employee,
                profile: user.profile
            }));

            return NextResponse.json({ users: formattedUsers });
        }

        // التحقق من صحة الدور إذا تم تحديده
        // ملاحظة: إذا لم يتم تحديد roleParam ووصلنا هنا، فهذا يعني أن المستخدم ليس مسؤولاً أو معلماً
        if (!roleParam) {
            return NextResponse.json(
                { message: "يجب تحديد معلمة الدور أو أن تكون مسؤولاً/معلماً" },
                { status: 400 }
            );
        }

        let validRole: UserRole | undefined;

        switch (roleParam.toUpperCase()) {
            case 'ADMIN':
                validRole = UserRole.ADMIN;
                break;
            case 'TEACHER':
                validRole = UserRole.TEACHER;
                break;
            case 'STUDENT':
                validRole = UserRole.STUDENT;
                break;
            case 'PARENT':
                validRole = UserRole.PARENT;
                break;
            case 'EMPLOYEE':
                validRole = UserRole.EMPLOYEE;
                break;
            default:
                return NextResponse.json(
                    { message: "معلمة الدور غير صالحة" },
                    { status: 400 }
                );
        }

        // إضافة الدور إلى شروط البحث (مع الحفاظ على استبعاد المعلقين)
        whereConditions.role = validRole;

        const users = await prisma.user.findMany({
            where: whereConditions,
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                createdAt: true,
                updatedAt: true,
                profile: {
                    select: {
                        name: true,
                        phone: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: limit
        });

        // تنسيق البيانات للعرض
        const formattedUsers = users.map(user => ({
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            name: user.profile?.name || '',
            phone: user.profile?.phone || '',
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            status: 'active', // يمكن إضافة حقل الحالة في المستقبل
            profile: user.profile
        }));

        return NextResponse.json({ users: formattedUsers });
    } catch (error: unknown) {
        console.error('Error fetching users:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب بيانات المستخدمين" },
            { status: 500 }
        );
    }
}
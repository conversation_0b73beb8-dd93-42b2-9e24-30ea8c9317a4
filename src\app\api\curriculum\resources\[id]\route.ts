import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/curriculum/resources/[id] - الحصول على مورد محدد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المورد غير صالح" },
        { status: 400 }
      );
    }

    const resource = await prisma.curriculumResource.findUnique({
      where: { id },
    });

    if (!resource) {
      return NextResponse.json(
        { message: "المورد غير موجود" },
        { status: 404 }
      );
    }

    return NextResponse.json(resource);
  } catch (error) {
    console.error('Error fetching resource:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب المورد" },
      { status: 500 }
    );
  }
}

// PUT /api/curriculum/resources/[id] - تحديث مورد محدد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المورد غير صالح" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { title, type, url } = body;

    if (!title || !type || !url) {
      return NextResponse.json(
        { message: "يجب توفير عنوان المورد ونوعه ورابطه" },
        { status: 400 }
      );
    }

    // التحقق من وجود المورد
    const resource = await prisma.curriculumResource.findUnique({
      where: { id },
    });

    if (!resource) {
      return NextResponse.json(
        { message: "المورد غير موجود" },
        { status: 404 }
      );
    }

    // تحديث المورد
    const updatedResource = await prisma.curriculumResource.update({
      where: { id },
      data: {
        title,
        type,
        url,
      },
    });

    return NextResponse.json(updatedResource);
  } catch (error) {
    console.error('Error updating resource:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث المورد" },
      { status: 500 }
    );
  }
}

// DELETE /api/curriculum/resources/[id] - حذف مورد محدد
export async function DELETE(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const paramsData = await params;
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { message: "معرف المورد غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المورد
    const resource = await prisma.curriculumResource.findUnique({
      where: { id },
    });

    if (!resource) {
      return NextResponse.json(
        { message: "المورد غير موجود" },
        { status: 404 }
      );
    }

    // حذف المورد
    await prisma.curriculumResource.delete({
      where: { id },
    });

    return NextResponse.json({ message: "تم حذف المورد بنجاح" });
  } catch (error) {
    console.error('Error deleting resource:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف المورد" },
      { status: 500 }
    );
  }
}

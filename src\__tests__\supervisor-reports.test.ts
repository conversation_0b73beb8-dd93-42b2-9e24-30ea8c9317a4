import { validateReport, validateBasicInfo, validateDates, validateLiteraryContent, validateFinancialData } from '@/utils/reportValidation';

// بيانات اختبار صحيحة
const validReportData = {
  title: 'التقرير الشهري لشهر يناير 2024',
  description: 'تقرير شامل عن أنشطة المدرسة القرآنية',
  periodStart: new Date('2024-01-01'),
  periodEnd: new Date('2024-01-31'),
  literaryContent: `
    <h2>الإحصائيات العامة</h2>
    <p>تم تسجيل تقدم ملحوظ في جميع الأنشطة التعليمية والتربوية خلال هذا الشهر.</p>
    <ul>
      <li>عدد الطلاب: 150</li>
      <li>عدد المعلمين: 12</li>
      <li>عدد الأنشطة: 25</li>
    </ul>
  `,
  financialData: [
    {
      id: '1',
      category: 'المداخيل',
      description: 'رسوم التسجيل',
      amount: 50000,
      type: 'income' as const
    },
    {
      id: '2',
      category: 'المصاريف',
      description: 'رواتب المعلمين',
      amount: 30000,
      type: 'expense' as const
    }
  ]
};

describe('التحقق من صحة التقارير الموحدة', () => {
  
  describe('validateBasicInfo', () => {
    test('يجب أن يمرر البيانات الأساسية الصحيحة', () => {
      const errors = validateBasicInfo(validReportData);
      expect(errors.filter(e => e.type === 'error')).toHaveLength(0);
    });

    test('يجب أن يرفض العنوان الفارغ', () => {
      const errors = validateBasicInfo({ ...validReportData, title: '' });
      expect(errors.some(e => e.field === 'title' && e.type === 'error')).toBe(true);
    });

    test('يجب أن يحذر من العنوان القصير', () => {
      const errors = validateBasicInfo({ ...validReportData, title: 'قصير' });
      expect(errors.some(e => e.field === 'title' && e.type === 'warning')).toBe(true);
    });

    test('يجب أن يرفض العنوان الطويل جداً', () => {
      const longTitle = 'ا'.repeat(250);
      const errors = validateBasicInfo({ ...validReportData, title: longTitle });
      expect(errors.some(e => e.field === 'title' && e.type === 'error')).toBe(true);
    });

    test('يجب أن يحذر من الوصف الطويل', () => {
      const longDescription = 'ا'.repeat(1100);
      const errors = validateBasicInfo({ ...validReportData, description: longDescription });
      expect(errors.some(e => e.field === 'description' && e.type === 'warning')).toBe(true);
    });
  });

  describe('validateDates', () => {
    test('يجب أن يمرر التواريخ الصحيحة', () => {
      const errors = validateDates(validReportData.periodStart, validReportData.periodEnd);
      expect(errors.filter(e => e.type === 'error')).toHaveLength(0);
    });

    test('يجب أن يرفض التواريخ المفقودة', () => {
      const errors = validateDates(undefined, validReportData.periodEnd);
      expect(errors.some(e => e.field === 'periodStart' && e.type === 'error')).toBe(true);
    });

    test('يجب أن يرفض تاريخ البداية بعد النهاية', () => {
      const errors = validateDates(validReportData.periodEnd, validReportData.periodStart);
      expect(errors.some(e => e.field === 'period' && e.type === 'error')).toBe(true);
    });

    test('يجب أن يحذر من الفترة الطويلة', () => {
      const longPeriodEnd = new Date(validReportData.periodStart);
      longPeriodEnd.setFullYear(longPeriodEnd.getFullYear() + 2);
      const errors = validateDates(validReportData.periodStart, longPeriodEnd);
      expect(errors.some(e => e.field === 'period' && e.type === 'warning')).toBe(true);
    });

    test('يجب أن يحذر من الفترة القصيرة جداً', () => {
      const shortPeriodEnd = new Date(validReportData.periodStart);
      shortPeriodEnd.setHours(shortPeriodEnd.getHours() + 1);
      const errors = validateDates(validReportData.periodStart, shortPeriodEnd);
      expect(errors.some(e => e.field === 'period' && e.type === 'warning')).toBe(true);
    });
  });

  describe('validateLiteraryContent', () => {
    test('يجب أن يمرر المحتوى الأدبي الصحيح', () => {
      const errors = validateLiteraryContent(validReportData.literaryContent);
      expect(errors.filter(e => e.type === 'error')).toHaveLength(0);
    });

    test('يجب أن يحذر من المحتوى الفارغ', () => {
      const errors = validateLiteraryContent('');
      expect(errors.some(e => e.field === 'literaryContent' && e.type === 'warning')).toBe(true);
    });

    test('يجب أن يحذر من المحتوى القصير', () => {
      const errors = validateLiteraryContent('محتوى قصير');
      expect(errors.some(e => e.field === 'literaryContent' && e.type === 'warning')).toBe(true);
    });

    test('يجب أن يحذر من المحتوى الطويل جداً', () => {
      const longContent = 'ا'.repeat(60000);
      const errors = validateLiteraryContent(longContent);
      expect(errors.some(e => e.field === 'literaryContent' && e.type === 'warning')).toBe(true);
    });

    test('يجب أن يحذر من HTML غير مغلق', () => {
      const invalidHTML = '<div><p>محتوى</div>';
      const errors = validateLiteraryContent(invalidHTML);
      expect(errors.some(e => e.field === 'literaryContent' && e.type === 'warning')).toBe(true);
    });
  });

  describe('validateFinancialData', () => {
    test('يجب أن يمرر البيانات المالية الصحيحة', () => {
      const errors = validateFinancialData(validReportData.financialData);
      expect(errors.filter(e => e.type === 'error')).toHaveLength(0);
    });

    test('يجب أن يحذر من البيانات المالية الفارغة', () => {
      const errors = validateFinancialData([]);
      expect(errors.some(e => e.field === 'financialData' && e.type === 'warning')).toBe(true);
    });

    test('يجب أن يرفض الوصف الفارغ', () => {
      const invalidData = [
        { ...validReportData.financialData[0], description: '' }
      ];
      const errors = validateFinancialData(invalidData);
      expect(errors.some(e => e.type === 'error')).toBe(true);
    });

    test('يجب أن يرفض المبلغ السالب', () => {
      const invalidData = [
        { ...validReportData.financialData[0], amount: -1000 }
      ];
      const errors = validateFinancialData(invalidData);
      expect(errors.some(e => e.type === 'error')).toBe(true);
    });

    test('يجب أن يحذر من المبلغ صفر', () => {
      const invalidData = [
        { ...validReportData.financialData[0], amount: 0 }
      ];
      const errors = validateFinancialData(invalidData);
      expect(errors.some(e => e.type === 'warning')).toBe(true);
    });

    test('يجب أن يحذر من المبلغ الكبير جداً', () => {
      const invalidData = [
        { ...validReportData.financialData[0], amount: 20000000 }
      ];
      const errors = validateFinancialData(invalidData);
      expect(errors.some(e => e.type === 'warning')).toBe(true);
    });

    test('يجب أن يحذر من العجز في الميزانية', () => {
      const deficitData = [
        {
          id: '1',
          category: 'المداخيل',
          description: 'مداخيل قليلة',
          amount: 1000,
          type: 'income' as const
        },
        {
          id: '2',
          category: 'المصاريف',
          description: 'مصاريف كثيرة',
          amount: 5000,
          type: 'expense' as const
        }
      ];
      const errors = validateFinancialData(deficitData);
      expect(errors.some(e => e.field === 'financialData' && e.message.includes('عجز'))).toBe(true);
    });

    test('يجب أن يحذر من عدم وجود مداخيل', () => {
      const noIncomeData = [
        {
          id: '1',
          category: 'المصاريف',
          description: 'مصاريف فقط',
          amount: 1000,
          type: 'expense' as const
        }
      ];
      const errors = validateFinancialData(noIncomeData);
      expect(errors.some(e => e.message.includes('لا توجد مداخيل'))).toBe(true);
    });

    test('يجب أن يحذر من الأوصاف المكررة', () => {
      const duplicateData = [
        {
          id: '1',
          category: 'المداخيل',
          description: 'رسوم التسجيل',
          amount: 1000,
          type: 'income' as const
        },
        {
          id: '2',
          category: 'المداخيل',
          description: 'رسوم التسجيل',
          amount: 2000,
          type: 'income' as const
        }
      ];
      const errors = validateFinancialData(duplicateData);
      expect(errors.some(e => e.message.includes('مكررة'))).toBe(true);
    });
  });

  describe('validateReport', () => {
    test('يجب أن يمرر التقرير الصحيح الكامل', () => {
      const result = validateReport(validReportData);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('يجب أن يرفض التقرير مع أخطاء', () => {
      const invalidReport = {
        ...validReportData,
        title: '', // خطأ: عنوان فارغ
        periodStart: validReportData.periodEnd, // خطأ: تاريخ خاطئ
        periodEnd: validReportData.periodStart
      };
      const result = validateReport(invalidReport);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('يجب أن يقبل التقرير مع تحذيرات فقط', () => {
      const warningReport = {
        ...validReportData,
        title: 'قصير', // تحذير: عنوان قصير
        literaryContent: 'محتوى قصير' // تحذير: محتوى قصير
      };
      const result = validateReport(warningReport);
      expect(result.isValid).toBe(true);
      expect(result.warnings.length).toBeGreaterThan(0);
    });

    test('يجب أن يجمع جميع أنواع الأخطاء', () => {
      const multiErrorReport = {
        title: '', // خطأ في العنوان
        description: 'وصف عادي',
        periodStart: new Date('2024-12-31'), // خطأ في التواريخ
        periodEnd: new Date('2024-01-01'),
        literaryContent: '', // تحذير في المحتوى
        financialData: [
          {
            id: '1',
            category: 'المداخيل',
            description: '', // خطأ في البيانات المالية
            amount: -100, // خطأ في المبلغ
            type: 'income' as const
          }
        ]
      };
      const result = validateReport(multiErrorReport);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(2);
      expect(result.warnings.length).toBeGreaterThan(0);
    });
  });
});

// اختبارات إضافية للحالات الحدية
describe('اختبارات الحالات الحدية', () => {
  test('يجب التعامل مع البيانات المفقودة', () => {
    const incompleteData = {
      title: 'تقرير ناقص',
      periodStart: new Date(),
      periodEnd: new Date(),
      literaryContent: '',
      financialData: []
    };
    const result = validateReport(incompleteData);
    expect(result).toBeDefined();
    expect(typeof result.isValid).toBe('boolean');
  });

  test('يجب التعامل مع التواريخ غير الصحيحة', () => {
    const invalidDateData = {
      ...validReportData,
      periodStart: new Date('invalid-date'),
      periodEnd: new Date('also-invalid')
    };
    // يجب أن لا يتعطل التحقق حتى مع التواريخ غير الصحيحة
    expect(() => validateReport(invalidDateData)).not.toThrow();
  });

  test('يجب التعامل مع البيانات المالية غير الصحيحة', () => {
    const invalidFinancialData = {
      ...validReportData,
      financialData: [
        {
          id: '1',
          category: 'غير صحيح',
          description: 'وصف',
          amount: 'ليس رقم' as any,
          type: 'نوع غير صحيح' as any
        }
      ]
    };
    expect(() => validateReport(invalidFinancialData)).not.toThrow();
  });
});

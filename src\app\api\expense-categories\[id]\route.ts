import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/expense-categories/:id - الحصول على فئة مصروفات محددة
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود الفئة
    const category = await prisma.expenseCategory.findUnique({
      where: { id },
      include: {
        subCategories: {
          select: {
            id: true,
            name: true,
            icon: true,
            color: true,
            isActive: true,
            _count: {
              select: {
                expenses: true,
                subCategories: true
              }
            }
          }
        },
        _count: {
          select: {
            expenses: true,
            subCategories: true
          }
        }
      }
    });

    if (!category) {
      return NextResponse.json(
        { error: 'فئة المصروفات غير موجودة' },
        { status: 404 }
      );
    }

    return NextResponse.json({ category });
  } catch (error) {
    console.error('خطأ في جلب فئة المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب فئة المصروفات' },
      { status: 500 }
    );
  }
}

// PUT /api/expense-categories/:id - تحديث فئة مصروفات
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);
    const body = await req.json();
    const { name, description, icon, color, parentId, isActive } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'اسم الفئة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة
    const existingCategory = await prisma.expenseCategory.findUnique({
      where: { id }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'فئة المصروفات غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من وجود الفئة الأب إذا تم تحديدها
    if (parentId) {
      // التأكد من أن الفئة لا تشير إلى نفسها كفئة أب
      if (parentId === id) {
        return NextResponse.json(
          { error: 'لا يمكن تعيين الفئة كفئة أب لنفسها' },
          { status: 400 }
        );
      }

      const parentCategory = await prisma.expenseCategory.findUnique({
        where: { id: parentId }
      });

      if (!parentCategory) {
        return NextResponse.json(
          { error: 'الفئة الأب غير موجودة' },
          { status: 400 }
        );
      }
    }

    // تحديث فئة المصروفات
    const updatedCategory = await prisma.expenseCategory.update({
      where: { id },
      data: {
        name,
        description,
        icon,
        color,
        parentId: parentId || null,
        isActive: isActive !== undefined ? isActive : existingCategory.isActive
      }
    });

    return NextResponse.json(updatedCategory);
  } catch (error) {
    console.error('خطأ في تحديث فئة المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث فئة المصروفات' },
      { status: 500 }
    );
  }
}

// DELETE /api/expense-categories/:id - حذف فئة مصروفات
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود الفئة
    const existingCategory = await prisma.expenseCategory.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            expenses: true,
            subCategories: true
          }
        }
      }
    });

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'فئة المصروفات غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود فئات فرعية أو مصروفات مرتبطة
    if (existingCategory._count.expenses > 0 || existingCategory._count.subCategories > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف الفئة لأنها تحتوي على فئات فرعية أو مصروفات مرتبطة' },
        { status: 400 }
      );
    }

    // حذف فئة المصروفات
    await prisma.expenseCategory.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف فئة المصروفات:', error);
    return NextResponse.json(
      { error: 'فشل في حذف فئة المصروفات' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getSchoolContactInfo } from '@/lib/school-settings';
//import { addArabicFont } from '@/utils/arabic-font';

// GET /api/invoices/pdf/[id] - توليد ملف PDF للفاتورة
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      );
    }

    // جلب الفاتورة مع بيانات الطالب/الولي والمدفوعات
    const invoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) },
      include: {
        student: {
          include: {
            guardian: true,
            classe: true
          }
        },
        parent: {
          include: {
            students: {
              include: {
                classe: true
              }
            }
          }
        },
        payments: {
          where: {
            status: 'PAID'
          }
        }
      }
    });

    if (!invoice) {
      return NextResponse.json(
        { error: 'الفاتورة غير موجودة' },
        { status: 404 }
      );
    }

    // جلب معلومات المؤسسة من الإعدادات
    const schoolInfo = await getSchoolContactInfo();

    console.log('📄 إنشاء فاتورة PDF باستخدام معلومات المؤسسة:', {
      invoiceId: id,
      invoiceType: invoice.type,
      schoolName: schoolInfo.name,
      schoolEmail: schoolInfo.email,
      schoolPhone: schoolInfo.phone
    });

    // حساب المبلغ المدفوع والمتبقي
    const paidAmount = invoice.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = invoice.amount - paidAmount;

    // إنشاء محتوى HTML للفاتورة
    const invoiceHtml = `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>فاتورة #${invoice.id}</title>
        <style>
          @media print {
            body { margin: 0; padding: 20px; }
          }
          body {
            font-family: Arial, sans-serif;
            background: #f9f9f9;
            direction: rtl;
          }
          .invoice {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 0 auto;
            max-width: 800px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
          }
          .header h1 {
            color: var(--primary-color);
            margin: 0 0 10px 0;
          }
          .header p {
            color: #7f8c8d;
            margin: 5px 0;
          }
          .school-info {
            text-align: center;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #34495e;
          }
          .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
          }
          .invoice-details .column {
            flex: 1;
          }
          .invoice-details h3 {
            color: var(--primary-color);
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
          }
          .invoice-details p {
            margin: 5px 0;
          }
          .items {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .items th {
            background: var(--primary-color);
            color: white;
            padding: 10px;
            text-align: right;
          }
          .items td {
            padding: 10px;
            border-bottom: 1px solid #eee;
          }
          .items tr:last-child td {
            border-bottom: none;
          }
          .total {
            text-align: left;
            margin-top: 20px;
          }
          .total .row {
            display: flex;
            justify-content: flex-end;
            margin: 5px 0;
          }
          .total .row span:first-child {
            width: 150px;
            font-weight: bold;
          }
          .total .grand-total {
            font-size: 1.2em;
            font-weight: bold;
            color: var(--primary-color);
            border-top: 2px solid #eee;
            padding-top: 5px;
          }
          .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 0.9em;
            color: #7f8c8d;
          }
          .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
          }
          .status.paid {
            background: #d5f5e3;
            color: #27ae60;
          }
          .status.unpaid {
            background: #fef9e7;
            color: #f39c12;
          }
          .status.partially-paid {
            background: #ebf5fb;
            color: #3498db;
          }
          .status.overdue {
            background: #fdedec;
            color: #e74c3c;
          }
          .payments {
            margin-top: 20px;
          }
          .payments h3 {
            color: var(--primary-color);
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
          }
          .payments table {
            width: 100%;
            border-collapse: collapse;
          }
          .payments th {
            background: #f2f2f2;
            padding: 8px;
            text-align: right;
          }
          .payments td {
            padding: 8px;
            border-bottom: 1px solid #eee;
          }
        </style>
      </head>
      <body>
        <div class="invoice">
          <div class="header">
            <h1>فاتورة رسوم دراسية</h1>
            <p>رقم الفاتورة: ${invoice.id}</p>
            <p>تاريخ الإصدار: ${new Date(invoice.issueDate).toLocaleDateString('ar-DZ')}</p>
            <p>تاريخ الاستحقاق: ${new Date(invoice.dueDate).toLocaleDateString('ar-DZ')}</p>
          </div>

          <div class="school-info">
            <div class="logo-section" style="text-align: center; margin-bottom: 15px;">
              <img src="${schoolInfo.logoUrl}" alt="شعار المؤسسة" style="height: 60px; margin-bottom: 10px;" />
            </div>
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 5px;">${schoolInfo.name}</h2>
            <p style="text-align: center; font-style: italic; color: #666; margin-bottom: 20px; font-size: 14px;">${schoolInfo.description}</p>
            <div class="contact-details" style="text-align: center; font-size: 12px; color: #555;">
              <p><strong>العنوان:</strong> ${schoolInfo.address}</p>
              <p><strong>الهاتف:</strong> ${schoolInfo.phone}</p>
              <p><strong>البريد الإلكتروني:</strong> ${schoolInfo.email}</p>
            </div>
          </div>

          <div class="invoice-details">
            ${invoice.type === 'FAMILY' ? `
              <!-- فاتورة جماعية -->
              <div class="column">
                <h3>بيانات ولي الأمر</h3>
                <p><strong>الاسم:</strong> ${invoice.parent.name}</p>
                <p><strong>الهاتف:</strong> ${invoice.parent.phone}</p>
                <p><strong>العنوان:</strong> ${invoice.parent.address || 'غير محدد'}</p>
                <p><strong>البريد الإلكتروني:</strong> ${invoice.parent.email || 'غير محدد'}</p>
              </div>

              <div class="column">
                <h3>الأطفال المشمولين (${invoice.parent.students.length})</h3>
                ${invoice.parent.students.map(student => `
                  <p><strong>•</strong> ${student.name} - ${student.classe ? student.classe.name : 'غير محدد'}</p>
                `).join('')}
              </div>
            ` : `
              <!-- فاتورة فردية -->
              <div class="column">
                <h3>بيانات الطالب</h3>
                <p><strong>الاسم:</strong> ${invoice.student.name}</p>
                <p><strong>رقم الطالب:</strong> ${invoice.student.id}</p>
                <p><strong>الفصل:</strong> ${invoice.student.classe ? invoice.student.classe.name : 'غير محدد'}</p>
                <p><strong>العمر:</strong> ${invoice.student.age} سنة</p>
              </div>

              <div class="column">
                <h3>بيانات ولي الأمر</h3>
                ${invoice.student.guardian ? `
                  <p><strong>الاسم:</strong> ${invoice.student.guardian.name}</p>
                  <p><strong>الهاتف:</strong> ${invoice.student.guardian.phone}</p>
                  <p><strong>العنوان:</strong> ${invoice.student.guardian.address || 'غير محدد'}</p>
                ` : '<p>لا يوجد ولي أمر مسجل</p>'}
              </div>
            `}
          </div>

          <table class="items">
            <thead>
              <tr>
                <th>البيان</th>
                <th>الشهر/السنة</th>
                <th>المبلغ</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>${invoice.description || (invoice.type === 'FAMILY' ? 'فاتورة جماعية' : 'رسوم دراسية')}</td>
                <td>${invoice.month}/${invoice.year}</td>
                <td>${invoice.amount} دج</td>
              </tr>
            </tbody>
          </table>

          <div class="total">
            <div class="row">
              <span>إجمالي المبلغ:</span>
              <span>${invoice.amount} دج</span>
            </div>
            <div class="row">
              <span>المبلغ المدفوع:</span>
              <span>${paidAmount} دج</span>
            </div>
            <div class="row grand-total">
              <span>المبلغ المتبقي:</span>
              <span>${remainingAmount} دج</span>
            </div>
          </div>

          <div>
            <h3>حالة الفاتورة:</h3>
            <span class="status ${
              invoice.status === 'PAID' ? 'paid' :
              invoice.status === 'PARTIALLY_PAID' ? 'partially-paid' :
              invoice.status === 'OVERDUE' ? 'overdue' : 'unpaid'
            }">
              ${
                invoice.status === 'PAID' ? 'مدفوعة' :
                invoice.status === 'PARTIALLY_PAID' ? 'مدفوعة جزئيًا' :
                invoice.status === 'OVERDUE' ? 'متأخرة' : 'غير مدفوعة'
              }
            </span>
          </div>

          ${invoice.payments.length > 0 ? `
            <div class="payments">
              <h3>سجل المدفوعات</h3>
              <table>
                <thead>
                  <tr>
                    <th>رقم الدفعة</th>
                    <th>التاريخ</th>
                    <th>المبلغ</th>
                  </tr>
                </thead>
                <tbody>
                  ${invoice.payments.map(payment => `
                    <tr>
                      <td>${payment.id}</td>
                      <td>${new Date(payment.date).toLocaleDateString('ar-DZ')}</td>
                      <td>${payment.amount} دج</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          ` : ''}

          <div class="footer">
            <p>شكراً لكم - بارك الله فيكم</p>
            <p>يرجى الاحتفاظ بهذه الفاتورة كإثبات للدفع</p>
          </div>
        </div>

        <script>
          window.onload = () => {
            window.print();
          };
        </script>
      </body>
      </html>
    `;

    // إرجاع محتوى HTML للفاتورة
    return new NextResponse(invoiceHtml, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });
  } catch (error) {
    console.error('خطأ في توليد ملف PDF للفاتورة:', error);
    return NextResponse.json(
      { error: 'فشل في توليد ملف PDF للفاتورة' },
      { status: 500 }
    );
  }
}

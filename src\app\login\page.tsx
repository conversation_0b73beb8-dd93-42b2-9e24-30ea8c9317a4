import React from 'react'
import LoginForm from './loginForm'
import SiteLogo from '@/components/SiteLogo'

const LoginPage = () => {
  return (
    <section className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8" dir="rtl">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* شعار الموقع */}
        <div className="flex justify-center pt-6 pb-4">
          <SiteLogo size="xl" showText={true} iconColor="var(--primary-color)" />
        </div>

        <div className="bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] px-6 py-4">
          <h2 className="text-2xl font-bold text-white text-center">تسجيل الدخول</h2>
        </div>
        <div className="px-6 py-8">
          <LoginForm />
        </div>
      </div>
    </section>
  )
}

export default LoginPage
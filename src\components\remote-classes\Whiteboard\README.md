# مكونات السبورة التفاعلية

هذا المجلد يحتوي على المكونات المستخدمة في ميزة السبورة التفاعلية للفصول الافتراضية.

## المكونات

- `Whiteboard.tsx`: مكون السبورة الرئيسي
- `WhiteboardToolbar.tsx`: شريط أدوات السبورة
- `WhiteboardCanvas.tsx`: منطقة الرسم في السبورة
- `WhiteboardControls.tsx`: عناصر التحكم في السبورة
- `WhiteboardProvider.tsx`: مزود السياق للسبورة

## التقنيات المستخدمة

- Fabric.js/Paper.js لرسم السبورة
- Socket.io للمزامنة في الوقت الفعلي

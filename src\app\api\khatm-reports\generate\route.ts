import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/khatm-reports/generate - إنشاء تقرير إنجاز جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, description, studentId, startDate, endDate } = body;

    // التحقق من البيانات المطلوبة
    if (!title || !studentId || !startDate || !endDate) {
      return NextResponse.json({
        success: false,
        error: 'العنوان ومعرف الطالب وتاريخ البداية والنهاية مطلوبة'
      }, { status: 400 });
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return NextResponse.json({
        success: false,
        error: 'الطالب غير موجود'
      }, { status: 404 });
    }

    // تحويل التواريخ إلى كائنات Date
    const start = new Date(startDate);
    const end = new Date(endDate);

    // التحقق من صحة التواريخ
    if (start > end) {
      return NextResponse.json({
        success: false,
        error: 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية'
      }, { status: 400 });
    }

    // جلب جميع مجالس الختم التي حضرها الطالب في الفترة المحددة
    const attendances = await prisma.khatmSessionAttendance.findMany({
      where: {
        studentId: parseInt(studentId),
        khatmSession: {
          date: {
            gte: start,
            lte: end
          }
        }
      },
      include: {
        khatmSession: {
          include: {
            surah: true
          }
        },
        progressRecords: true
      }
    });

    // جلب جميع مجالس الختم في الفترة المحددة
    const totalSessions = await prisma.khatmSession.count({
      where: {
        date: {
          gte: start,
          lte: end
        }
      }
    });

    // حساب إجمالي الآيات المحفوظة والمراجعة
    let totalAyahs = 0;
    let memorizedAyahs = 0;
    let reviewedAyahs = 0;
    let totalRating = 0;
    let ratingCount = 0;

    // حساب إجمالي الآيات في السور التي تم حضورها
    for (const attendance of attendances) {
      if (attendance.khatmSession.surah) {
        totalAyahs += attendance.khatmSession.surah.totalAyahs;
      }

      // حساب الآيات المحفوظة والمراجعة من سجلات التقدم
      for (const progress of attendance.progressRecords) {
        memorizedAyahs += progress.memorizedAyahs;
        reviewedAyahs += progress.reviewedAyahs;
        totalRating += progress.qualityRating;
        ratingCount++;
      }
    }

    // حساب متوسط التقييم
    const averageRating = ratingCount > 0 ? totalRating / ratingCount : 0;

    // إنشاء تقرير الإنجاز
    const report = await prisma.khatmAchievementReport.create({
      data: {
        title,
        description,
        studentId: parseInt(studentId),
        startDate: start,
        endDate: end,
        totalSessions,
        attendedSessions: attendances.length,
        totalAyahs,
        memorizedAyahs,
        reviewedAyahs,
        averageRating
      }
    });

    return NextResponse.json({
      success: true,
      data: report,
      message: 'تم إنشاء تقرير الإنجاز بنجاح'
    });
  } catch (error) {
    console.error('Error generating achievement report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء إنشاء تقرير الإنجاز'
    }, { status: 500 });
  }
}

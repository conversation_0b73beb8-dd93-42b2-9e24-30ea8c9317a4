import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// POST: تنفيذ إجراءات مجمعة على المستخدمين
export async function POST(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به: يرجى تسجيل الدخول" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { message: "غير مصرح به: يجب أن تكون مسؤولاً لتنفيذ إجراءات مجمعة" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { userIds, action } = body;

    // التحقق من البيانات المطلوبة
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { message: "قائمة معرفات المستخدمين مطلوبة" },
        { status: 400 }
      );
    }

    if (!['activate', 'deactivate', 'delete'].includes(action)) {
      return NextResponse.json(
        { message: "إجراء غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستخدمين
    const existingUsers = await prisma.user.findMany({
      where: {
        id: { in: userIds }
      }
    });

    if (existingUsers.length !== userIds.length) {
      return NextResponse.json(
        { message: "بعض المستخدمين المحددين غير موجودين" },
        { status: 404 }
      );
    }

    // منع المستخدم من تنفيذ إجراءات على نفسه
    if (userIds.includes(userData.id)) {
      return NextResponse.json(
        { message: "لا يمكنك تنفيذ إجراءات على حسابك الخاص" },
        { status: 400 }
      );
    }

    let result;
    let message;

    switch (action) {
      case 'activate':
        // تفعيل المستخدمين
        result = await prisma.user.updateMany({
          where: {
            id: { in: userIds }
          },
          data: {
            isActive: true
          }
        });
        message = `تم تفعيل ${result.count} مستخدم بنجاح`;
        break;

      case 'deactivate':
        // إلغاء تفعيل المستخدمين
        result = await prisma.user.updateMany({
          where: {
            id: { in: userIds }
          },
          data: {
            isActive: false
          }
        });
        message = `تم إلغاء تفعيل ${result.count} مستخدم بنجاح`;
        break;

      case 'delete':
        // حذف المستخدمين (سيتم حذف البيانات المرتبطة تلقائياً بسبب onDelete: Cascade)
        result = await prisma.user.deleteMany({
          where: {
            id: { in: userIds }
          }
        });
        message = `تم حذف ${result.count} مستخدم بنجاح`;
        break;

      default:
        return NextResponse.json(
          { message: "إجراء غير مدعوم" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      result,
      message,
      affectedCount: result.count
    });

  } catch (error: unknown) {
    console.error('Error executing bulk action:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تنفيذ الإجراء المجمع" },
      { status: 500 }
    );
  }
}

"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-toastify';
import AddStudentDialog from '../components/AddStudentDialog';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons, useBulkPermissions } from '@/components/admin/BulkPermissionGuard';
import { FaUserGraduate, FaSearch, FaEdit, FaTrash, FaSync, FaUserPlus, FaSave, FaFileExport, FaFileImport, FaDownload, FaFilter, FaChartLine, FaFilePdf, FaGraduationCap, FaUserFriends, FaCalendarCheck, FaBook, FaExclamationCircle, FaCheckCircle, FaChartBar, FaUser, FaReceipt } from 'react-icons/fa';
import { FileText } from 'lucide-react';
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

type Student = {
  id: number;
  username: string;
  name: string;
  age: number;
  phone?: string;
  guardianId?: number;
  classeId?: number;
  guardian?: { name: string; phone: string };
  classe?: { name: string };
  totalPoints?: number;
  attendance?: { present: number; absent: number; excused: number; total: number };
  quranProgress?: { memorized: number; total: number };
  examResults?: { passed: number; failed: number; total: number };
};

type StudentFormData = Omit<Student, 'id' | 'guardian' | 'classe' | 'totalPoints' | 'attendance' | 'quranProgress' | 'examResults'>;

type Class = {
  id: number;
  name: string;
};

type Guardian = {
  id: number;
  name: string;
  phone: string;
};

type AdvancedSearchFilters = {
  name: string;
  ageRange: [number, number];
  classeId?: number;
  guardianId?: number;
  performanceLevel?: 'excellent' | 'good' | 'average' | 'poor';
  attendanceRate?: 'high' | 'medium' | 'low';
};

export default function StudentsPage() {
  const router = useRouter();
  const [students, setStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [examData, setExamData] = useState<{
    exams: Array<{
      id: number;
      name: string;
      date: string;
      studentCount: number;
      avgScore: number;
      passRate: number;
    }>;
    stats: {
      total: number;
      avgScore: number;
      passRate: number;
    };
  }>({ exams: [], stats: { total: 0, avgScore: 0, passRate: 0 } });

  const [attendanceData, setAttendanceData] = useState<{
    records: Array<{
      id: number;
      studentId: number;
      date: string | Date;
      status: 'PRESENT' | 'ABSENT' | 'EXCUSED';
    }>;
    monthly: Array<{
      month: string;
      present: number;
      absent: number;
      excused: number;
      total: number;
      rate: number;
    }>;
    mostAbsent: Array<{
      id: number;
      name: string;
      classeId: number;
      className: string;
      absenceCount: number;
      lastPresent?: Date;
      absenceRate: number;
    }>;
    stats: {
      present: number;
      absent: number;
      excused: number;
      total: number;
      rate: number;
    };
  }>({ records: [], monthly: [], mostAbsent: [], stats: { present: 0, absent: 0, excused: 0, total: 0, rate: 0 } });

  const [quranData, setQuranData] = useState<{
    memorization: Array<{
      id: number;
      studentId: number;
      surahId?: number;
      surah?: { name: string };
      versesCount?: number;
      grade?: number;
      tajweedGrade?: number;
      date?: string | Date;
    }>;
    surahs: Array<{
      id: number;
      name: string;
      studentsCount: number;
      totalGrade: number;
      avgGrade: string | number;
      percentage: number;
    }>;
    topStudents: Array<{
      id: number;
      name: string;
      classeId: number;
      className: string;
      versesCount: number;
      avgGrade: string;
      avgTajweed: string;
    }>;
    stats: {
      totalVerses: number;
      avgVerses: number;
      avgGrade: string | number;
      totalStudents: number;
    };
  }>({ memorization: [], surahs: [], topStudents: [], stats: { totalVerses: 0, avgVerses: 0, avgGrade: 0, totalStudents: 0 } });
  const [loading, setLoading] = useState(true);
  const [examDataLoading, setExamDataLoading] = useState(false);
  const [attendanceDataLoading, setAttendanceDataLoading] = useState(false);
  const [quranDataLoading, setQuranDataLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isAdvancedSearchOpen, setIsAdvancedSearchOpen] = useState(false);
  const [isProgressReportOpen, setIsProgressReportOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [formData, setFormData] = useState<StudentFormData>({
    username: '',
    name: '',
    age: 0,
    phone: '',
    guardianId: undefined,
    classeId: undefined
  });
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedSearchFilters>({
    name: '',
    ageRange: [0, 100],
    classeId: undefined,
    guardianId: undefined,
    performanceLevel: undefined,
    attendanceRate: undefined
  });

  // جلب بيانات الامتحانات من قاعدة البيانات
  const fetchExamData = useCallback(async () => {
    try {
      setExamDataLoading(true);

      // جلب بيانات الامتحانات
      const examsResponse = await fetch('/api/evaluation/exams');
      if (!examsResponse.ok) {
        throw new Error('Failed to fetch exams data');
      }
      const examsResponseData = await examsResponse.json();

      // استخراج مصفوفة الامتحانات من الاستجابة
      const examsData = examsResponseData.data || [];
      console.log('Exams data received:', examsResponseData);

      // جلب بيانات نتائج الامتحانات
      const resultsResponse = await fetch('/api/evaluation/results');
      if (!resultsResponse.ok) {
        throw new Error('Failed to fetch exam results');
      }
      const resultsData = await resultsResponse.json();
      console.log('Results data received:', resultsData);

      // حساب الإحصائيات
      const totalExams = examsData.length;
      let totalScore = 0;
      let totalPassed = 0;
      let totalResults = 0;

      resultsData.forEach((result: { grade?: number; status?: string }) => {
        totalScore += result.grade || 0;
        if (result.status === 'PASSED') {
          totalPassed++;
        }
        totalResults++;
      });

      const avgScore = totalResults > 0 ? Math.round(totalScore / totalResults) : 0;
      const passRate = totalResults > 0 ? Math.round((totalPassed / totalResults) * 100) : 0;

      // تنظيم بيانات الامتحانات
      const formattedExams = examsData.map((exam: { id: number; description?: string; evaluationType?: string; month?: string; createdAt?: string | Date }) => {
        const examResults = resultsData.filter((result: { examId?: number }) => result.examId === exam.id);
        const examTotalScore = examResults.reduce((sum: number, result: { grade?: number }) => sum + (result.grade || 0), 0);
        const examAvgScore = examResults.length > 0 ? Math.round(examTotalScore / examResults.length) : 0;
        const examPassedCount = examResults.filter((result: { status?: string }) => result.status === 'PASSED').length;
        const examPassRate = examResults.length > 0 ? Math.round((examPassedCount / examResults.length) * 100) : 0;

        // إنشاء اسم وصفي للامتحان إذا كان الوصف فارغًا
        let examName = exam.description;
        if (!examName || examName.trim() === '') {
          // ترجمة نوع الامتحان إلى العربية
          let examTypeArabic = 'امتحان';
          if (exam.evaluationType) {
            switch (exam.evaluationType) {
              case 'QURAN_MEMORIZATION':
                examTypeArabic = 'امتحان حفظ القرآن';
                break;
              case 'QURAN_TAJWEED':
                examTypeArabic = 'امتحان تجويد القرآن';
                break;
              case 'MONTHLY_EVALUATION':
                examTypeArabic = 'التقييم الشهري';
                break;
              case 'QUARTERLY_EVALUATION':
                examTypeArabic = 'التقييم الفصلي';
                break;
              case 'YEARLY_EVALUATION':
                examTypeArabic = 'التقييم السنوي';
                break;
              default:
                examTypeArabic = 'امتحان';
            }
          }

          // تنسيق الشهر بشكل أفضل
          let formattedMonth = '';
          if (exam.month) {
            // تحويل التنسيق من YYYY-MM إلى اسم الشهر بالعربية والسنة
            const [year, month] = exam.month.split('-');
            const monthNames = [
              'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
              'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            const monthIndex = parseInt(month) - 1;
            if (monthIndex >= 0 && monthIndex < 12) {
              formattedMonth = `${monthNames[monthIndex]} ${year}`;
            } else {
              formattedMonth = exam.month;
            }
          }

          examName = formattedMonth ? `${examTypeArabic} - ${formattedMonth}` : examTypeArabic;
        }

        console.log('Exam data:', { id: exam.id, name: examName, description: exam.description });

        return {
          id: exam.id,
          name: examName,
          date: new Date(exam.createdAt || Date.now()).toLocaleDateString('fr-FR'),
          studentCount: examResults.length,
          avgScore: examAvgScore,
          passRate: examPassRate
        };
      });

      // تحديث حالة البيانات
      setExamData({
        exams: formattedExams,
        stats: {
          total: totalExams,
          avgScore,
          passRate
        }
      });

      setExamDataLoading(false);
    } catch (error) {
      console.error('Error fetching exam data:', error);
      toast.error('حدث خطأ أثناء جلب بيانات الامتحانات');
      setExamDataLoading(false);
    }
  }, []);

  // جلب بيانات الحضور من قاعدة البيانات
  const fetchAttendanceData = useCallback(async () => {
    try {
      setAttendanceDataLoading(true);

      // جلب بيانات الحضور
      const attendanceResponse = await fetch('/api/attendance/records');
      if (!attendanceResponse.ok) {
        throw new Error('Failed to fetch attendance data');
      }
      const attendanceRecords = await attendanceResponse.json();

      // حساب الإحصائيات
      let presentCount = 0;
      let absentCount = 0;
      let excusedCount = 0;
      let totalRecords = 0;

      attendanceRecords.forEach((record: { status: 'PRESENT' | 'ABSENT' | 'EXCUSED' }) => {
        if (record.status === 'PRESENT') {
          presentCount++;
        } else if (record.status === 'ABSENT') {
          absentCount++;
        } else if (record.status === 'EXCUSED') {
          excusedCount++;
        }
        totalRecords++;
      });

      // تنظيم بيانات الحضور حسب الشهر
      const monthlyData: Record<string, { present: number; absent: number; excused: number; total: number; rate: number }> = {};
      attendanceRecords.forEach((record: { date: string | Date; status: 'PRESENT' | 'ABSENT' | 'EXCUSED' }) => {
        const date = new Date(record.date);
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

        if (!monthlyData[monthYear]) {
          monthlyData[monthYear] = {
            present: 0,
            absent: 0,
            excused: 0,
            total: 0,
            rate: 0
          };
        }

        if (record.status === 'PRESENT') {
          monthlyData[monthYear].present++;
        } else if (record.status === 'ABSENT') {
          monthlyData[monthYear].absent++;
        } else if (record.status === 'EXCUSED') {
          monthlyData[monthYear].excused++;
        }

        monthlyData[monthYear].total++;
      });

      // حساب معدل الحضور لكل شهر
      Object.keys(monthlyData).forEach(month => {
        const data = monthlyData[month];
        data.rate = data.total > 0 ? Math.round((data.present / data.total) * 100) : 0;
      });

      // تحويل البيانات الشهرية إلى مصفوفة مرتبة
      const formattedMonthlyData = Object.keys(monthlyData).map(month => {
        const [monthNum, year] = month.split('/');
        const monthNames = [
          'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
          'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        return {
          month: `${monthNames[parseInt(monthNum) - 1]} ${year}`,
          ...monthlyData[month]
        };
      }).sort((a, b) => {
        // ترتيب البيانات من الأحدث إلى الأقدم
        const [aMonth, aYear] = a.month.split(' ');
        const [bMonth, bYear] = b.month.split(' ');

        if (aYear !== bYear) {
          return parseInt(bYear) - parseInt(aYear);
        }

        const monthOrder: Record<string, number> = {
          'يناير': 1, 'فبراير': 2, 'مارس': 3, 'أبريل': 4, 'مايو': 5, 'يونيو': 6,
          'يوليو': 7, 'أغسطس': 8, 'سبتمبر': 9, 'أكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12
        };

        return monthOrder[bMonth] - monthOrder[aMonth];
      });

      // تحديد الطلاب الأكثر غياب
      const studentAbsenceMap: Record<string, { count: number; lastPresent?: Date }> = {};
      attendanceRecords.forEach((record: { studentId: number; date: string | Date; status: 'PRESENT' | 'ABSENT' | 'EXCUSED' }) => {
        if (record.status === 'ABSENT') {
          if (!studentAbsenceMap[record.studentId]) {
            studentAbsenceMap[record.studentId] = {
              count: 0,
              lastPresent: undefined
            };
          }
          studentAbsenceMap[record.studentId].count++;
        } else if (record.status === 'PRESENT') {
          if (!studentAbsenceMap[record.studentId]) {
            studentAbsenceMap[record.studentId] = {
              count: 0,
              lastPresent: new Date(record.date)
            };
          } else if (!studentAbsenceMap[record.studentId].lastPresent ||
              new Date(record.date) > studentAbsenceMap[record.studentId].lastPresent!) {
            studentAbsenceMap[record.studentId].lastPresent = new Date(record.date);
          }
        }
      });

      // تحويل بيانات الغياب إلى مصفوفة مرتبة
      const studentIds = Object.keys(studentAbsenceMap);
      const mostAbsentStudents = studentIds
        .map(id => {
          const student = students.find(s => s.id === parseInt(id));
          if (!student) return null;

          return {
            id: parseInt(id),
            name: student.name,
            classeId: student.classeId || 0,
            className: student.classe?.name || '-',
            absenceCount: studentAbsenceMap[id].count,
            lastPresent: studentAbsenceMap[id].lastPresent,
            absenceRate: totalRecords > 0 ? (studentAbsenceMap[id].count / totalRecords) * 100 : 0
          };
        })
        .filter(item => item !== null)
        .sort((a, b) => (a && b) ? b.absenceCount - a.absenceCount : 0) as {
          id: number;
          name: string;
          classeId: number;
          className: string;
          absenceCount: number;
          lastPresent?: Date;
          absenceRate: number;
        }[];

      // تحديث حالة البيانات
      setAttendanceData({
        records: attendanceRecords,
        monthly: formattedMonthlyData,
        mostAbsent: mostAbsentStudents,
        stats: {
          present: presentCount,
          absent: absentCount,
          excused: excusedCount,
          total: totalRecords,
          rate: totalRecords > 0 ? Math.round((presentCount / totalRecords) * 100) : 0
        }
      });

      setAttendanceDataLoading(false);
    } catch (error) {
      console.error('Error fetching attendance data:', error);
      toast.error('حدث خطأ أثناء جلب بيانات الحضور');
      setAttendanceDataLoading(false);
    }
  }, [students]);

  // جلب بيانات حفظ القرآن من قاعدة البيانات
  const fetchQuranData = useCallback(async () => {
    try {
      setQuranDataLoading(true);

      // جلب بيانات حفظ القرآن
      const quranResponse = await fetch('/api/quran/memorization');
      if (!quranResponse.ok) {
        throw new Error('Failed to fetch Quran memorization data');
      }
      const quranRecords = await quranResponse.json();

      // حساب الإحصائيات
      let totalVerses = 0;
      let totalGrade = 0;
      let totalRecords = 0;

      quranRecords.forEach((record: { versesCount?: number; grade?: number }) => {
        totalVerses += record.versesCount || 0;
        totalGrade += record.grade || 0;
        totalRecords++;
      });

      const avgVerses = totalRecords > 0 ? Math.round(totalVerses / totalRecords) : 0;
      const avgGrade = totalRecords > 0 ? (totalGrade / totalRecords).toFixed(1) : 0;

      // تنظيم بيانات حفظ القرآن حسب السورة
      const surahData: Record<string, { name: string; studentsCount: number; totalGrade: number; avgGrade: number | string }> = {};
      quranRecords.forEach((record: { surahId?: number; surah?: { name: string }; grade?: number }) => {
        if (!record.surahId) return;

        if (!surahData[record.surahId]) {
          surahData[record.surahId] = {
            name: record.surah?.name || `سورة ${record.surahId}`,
            studentsCount: 0,
            totalGrade: 0,
            avgGrade: 0
          };
        }

        surahData[record.surahId].studentsCount++;
        surahData[record.surahId].totalGrade += record.grade || 0;
      });

      // حساب متوسط الدرجات لكل سورة
      Object.keys(surahData).forEach(surahId => {
        const data = surahData[surahId];
        data.avgGrade = data.studentsCount > 0 ? (data.totalGrade / data.studentsCount).toFixed(1) : 0;
      });

      // تحويل بيانات السور إلى مصفوفة مرتبة
      const formattedSurahData = Object.keys(surahData)
        .map(surahId => ({
          id: parseInt(surahId),
          ...surahData[surahId],
          percentage: students.length > 0 ? (surahData[surahId].studentsCount / students.length) * 100 : 0
        }))
        .sort((a, b) => b.studentsCount - a.studentsCount);

      // تحديد الطلاب المتميزين في الحفظ
      const studentMemorizationMap: Record<string, { versesCount: number; totalGrade: number; totalTajweed: number; recordsCount: number }> = {};
      quranRecords.forEach((record: { studentId: number; versesCount?: number; grade?: number; tajweedGrade?: number }) => {
        if (!studentMemorizationMap[record.studentId]) {
          studentMemorizationMap[record.studentId] = {
            versesCount: 0,
            totalGrade: 0,
            totalTajweed: 0,
            recordsCount: 0
          };
        }

        studentMemorizationMap[record.studentId].versesCount += record.versesCount || 0;
        studentMemorizationMap[record.studentId].totalGrade += record.grade || 0;
        studentMemorizationMap[record.studentId].totalTajweed += record.tajweedGrade || 0;
        studentMemorizationMap[record.studentId].recordsCount++;
      });

      // تحويل بيانات الطلاب إلى مصفوفة مرتبة
      const studentIds = Object.keys(studentMemorizationMap);
      const topStudents = studentIds
        .map(id => {
          const student = students.find(s => s.id === parseInt(id));
          if (!student) return null;

          const data = studentMemorizationMap[id];

          return {
            id: parseInt(id),
            name: student.name,
            classeId: student.classeId || 0,
            className: student.classe?.name || '-',
            versesCount: data.versesCount,
            avgGrade: data.recordsCount > 0 ? (data.totalGrade / data.recordsCount).toFixed(1) : '0',
            avgTajweed: data.recordsCount > 0 ? (data.totalTajweed / data.recordsCount).toFixed(1) : '0'
          };
        })
        .filter((item): item is {
          id: number;
          name: string;
          classeId: number;
          className: string;
          versesCount: number;
          avgGrade: string;
          avgTajweed: string
        } => item !== null)
        .sort((a, b) => {
          // ترتيب حسب عدد الآيات ثم متوسط الدرجات
          if (b.versesCount !== a.versesCount) {
            return b.versesCount - a.versesCount;
          }
          return parseFloat(b.avgGrade) - parseFloat(a.avgGrade);
        });

      // تحديث حالة البيانات
      setQuranData({
        memorization: quranRecords,
        surahs: formattedSurahData,
        topStudents: topStudents,
        stats: {
          totalVerses,
          avgVerses,
          avgGrade,
          totalStudents: studentIds.length
        }
      });

      setQuranDataLoading(false);
    } catch (error) {
      console.error('Error fetching Quran data:', error);
      toast.error('حدث خطأ أثناء جلب بيانات حفظ القرآن');
      setQuranDataLoading(false);
    }
  }, [students]);

  useEffect(() => {
    fetchStudents();
    fetchClasses();
    fetchGuardians();
  }, []);

  // جلب بيانات التقارير عند فتح قسم تقارير التقدم
  useEffect(() => {
    if (isProgressReportOpen) {
      fetchExamData();
      fetchAttendanceData();
      fetchQuranData();
    }
  }, [isProgressReportOpen, fetchExamData, fetchAttendanceData, fetchQuranData]);

  const fetchStudents = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('Failed to fetch students');
      const data = await response.json();
      if (!data.students || !Array.isArray(data.students)) {
        throw new Error('Invalid data format received from server');
      }
      setStudents(data.students);
    } catch {
      toast.error('حدث خطأ أثناء تحميل بيانات التلاميذ');
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/admin/classes');
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();
      setClasses(data);
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchGuardians = async () => {
    try {
      const response = await fetch('/api/parents');
      if (!response.ok) throw new Error('Failed to fetch guardians');
      const data = await response.json();
      setGuardians(data);
    } catch (error) {
      console.error('Error fetching guardians:', error);
    }
  };

  // تم بالفعل تعريف هذه الدوال سابقاً

  // البحث المتقدم عن الطلاب
  const applyAdvancedSearch = async () => {
    setLoading(true);

    try {
      // جلب جميع الطلاب أولاً
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('Failed to fetch students');
      const data = await response.json();

      if (!data.students || !Array.isArray(data.students)) {
        throw new Error('Invalid data format received from server');
      }

      // تطبيق الفلاتر على البيانات
      const filteredStudents = data.students.filter((student: { name: string; age: number; classeId: number; guardianId: number; totalPoints: never; attendance: { present: number; total: number; }; }) => {
        // البحث بالاسم
        if (advancedFilters.name && !student.name.toLowerCase().includes(advancedFilters.name.toLowerCase())) {
          return false;
        }

        // البحث بالعمر
        if (student.age < advancedFilters.ageRange[0] || student.age > advancedFilters.ageRange[1]) {
          return false;
        }

        // البحث بالقسم
        if (advancedFilters.classeId && student.classeId !== advancedFilters.classeId) {
          return false;
        }

        // البحث بالولي
        if (advancedFilters.guardianId && student.guardianId !== advancedFilters.guardianId) {
          return false;
        }

        // البحث بمستوى الأداء (هذا يتطلب بيانات إضافية من الخادم)
        if (advancedFilters.performanceLevel) {
          // هنا يمكن إضافة منطق لتحديد مستوى أداء الطالب بناءً على البيانات المتاحة
          // مثال: إذا كان لدينا بيانات عن نقاط الطالب
          if (student.totalPoints) {
            const performanceScore = student.totalPoints;

            if (advancedFilters.performanceLevel === 'excellent' && performanceScore < 90) return false;
            if (advancedFilters.performanceLevel === 'good' && (performanceScore < 75 || performanceScore >= 90)) return false;
            if (advancedFilters.performanceLevel === 'average' && (performanceScore < 60 || performanceScore >= 75)) return false;
            if (advancedFilters.performanceLevel === 'poor' && performanceScore >= 60) return false;
          } else {
            // إذا لم تكن البيانات متاحة، نتخطى هذا الفلتر
            return true;
          }
        }

        // البحث بمعدل الحضور
        if (advancedFilters.attendanceRate && student.attendance) {
          const attendanceRate = (student.attendance.present / student.attendance.total) * 100;

          if (advancedFilters.attendanceRate === 'high' && attendanceRate < 90) return false;
          if (advancedFilters.attendanceRate === 'medium' && (attendanceRate < 70 || attendanceRate >= 90)) return false;
          if (advancedFilters.attendanceRate === 'low' && attendanceRate >= 70) return false;
        }

        return true;
      });

      // تحديث قائمة الطلاب المعروضة
      setStudents(filteredStudents);
      setIsAdvancedSearchOpen(false);

      // إظهار رسالة بعدد النتائج
      toast.info(`تم العثور على ${filteredStudents.length} طالب`);
    } catch (error) {
      console.error('Error applying advanced search:', error);
      toast.error('حدث خطأ أثناء تطبيق البحث المتقدم');
    } finally {
      setLoading(false);
    }
  };

  // إعادة تعيين البحث المتقدم
  const resetAdvancedSearch = () => {
    setAdvancedFilters({
      name: '',
      ageRange: [0, 100],
      classeId: undefined,
      guardianId: undefined,
      performanceLevel: undefined,
      attendanceRate: undefined
    });

    fetchStudents();
    setIsAdvancedSearchOpen(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/students', {
        method: selectedStudent ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(selectedStudent ? { ...formData, id: selectedStudent.id } : formData),
      });

      if (!response.ok) throw new Error('Failed to save student');

      toast.success(selectedStudent ? 'تم تحديث بيانات التلميذ بنجاح' : 'تم إضافة التلميذ بنجاح');
      fetchStudents();
      resetForm();
    } catch {
      toast.error('حدث خطأ أثناء حفظ بيانات التلميذ');
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا التلميذ؟')) return;

    try {
      const response = await fetch(`/api/students/${id}`, { method: 'DELETE' });
      if (!response.ok) throw new Error('Failed to delete student');

      toast.success('تم حذف التلميذ بنجاح');
      fetchStudents();
    } catch {
      toast.error('حدث خطأ أثناء حذف التلميذ');
    }
  };

  const resetForm = () => {
    setFormData({
      username: '',
      name: '',
      age: 0,
      phone: '',
      guardianId: undefined,
      classeId: undefined
    });
    setSelectedStudent(null);
    setIsEditDialogOpen(false);
  };

  // تصدير بيانات الطلاب إلى ملف Excel
  const exportToExcel = () => {
    try {
      if (students.length === 0) {
        toast.error('لا توجد بيانات للتصدير');
        return;
      }

      // تحويل البيانات إلى تنسيق مناسب للتصدير
      const dataToExport = students.map(student => ({
        'اسم المستخدم': student.username,
        'الاسم الكامل': student.name,
        'العمر': student.age,
        'رقم الهاتف': student.phone || '-',
        'القسم': student.classe?.name || '-',
        'الولي': student.guardian?.name || '-'
      }));

      // إنشاء ورقة عمل Excel
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'بيانات التلاميذ');

      // تعيين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // اسم المستخدم
        { wch: 25 }, // الاسم الكامل
        { wch: 8 },  // العمر
        { wch: 15 }, // رقم الهاتف
        { wch: 20 }, // القسم
        { wch: 20 }  // الولي
      ];
      worksheet['!cols'] = columnWidths;

      // تصدير الملف
      const fileName = `بيانات_التلاميذ_${new Date().toISOString().slice(0, 10)}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success('تم تصدير البيانات بنجاح');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تصدير تقرير الامتحانات إلى ملف PDF
  const exportExamsToPdf = () => {
    try {
      toast.info('جاري تحضير تقرير نتائج الامتحانات...');

      // التحقق من وجود بيانات الامتحانات
      if (!examData.exams || examData.exams.length === 0) {
        // إذا لم تكن البيانات متوفرة، قم بجلبها أولاً
        fetchExamData().then(() => {
          // تحقق مرة أخرى من وجود البيانات بعد الجلب
          if (!examData.exams || examData.exams.length === 0) {
            toast.warning('لا توجد بيانات امتحانات كافية لإنشاء التقرير');
            return;
          }
          // إذا تم جلب البيانات بنجاح، قم بإنشاء التقرير
          createExamsPdf();
        }).catch(error => {
          console.error('Error fetching exam data:', error);
          toast.error('حدث خطأ أثناء جلب بيانات الامتحانات');
        });
        return;
      }

      // إذا كانت البيانات متوفرة، قم بإنشاء التقرير مباشرة
      createExamsPdf();
    } catch (error) {
      console.error('Error exporting exams to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير تقرير نتائج الامتحانات');
    }
  };

  // دالة إنشاء ملف PDF لتقرير الامتحانات
  const createExamsPdf = () => {
    try {
      // إنشاء مستند PDF جديد
      const doc = new jsPDF('p', 'mm', 'a4');

      // إضافة العنوان
      doc.setFont('Helvetica', 'bold');
      doc.setFontSize(18);
      doc.text('تقرير نتائج الامتحانات', 105, 15, { align: 'center' });

      // إضافة التاريخ
      doc.setFont('Helvetica', 'normal');
      doc.setFontSize(12);
      doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('fr-FR')}`, 105, 25, { align: 'center' });

      // إضافة معلومات عامة
      doc.setFontSize(14);
      doc.text('ملخص نتائج الامتحانات', 105, 35, { align: 'center' });

      // إضافة جدول الإحصائيات
      const statsData = [
        ['عدد الامتحانات', examData.stats.total.toString()],
        ['متوسط الدرجات', `${examData.stats.avgScore}/100`],
        ['نسبة النجاح', `${examData.stats.passRate}%`],
        ['عدد الطلاب', students.length.toString()]
      ];

      // @ts-expect-error - jsPDF-AutoTable يضيف خصائص إضافية إلى كائن jsPDF
      doc.autoTable({
        startY: 40,
        head: [['البيان', 'القيمة']],
        body: statsData,
        headStyles: { fillColor: [22, 155, 136], halign: 'center' },
        styles: { halign: 'center', font: 'Helvetica' },
        alternateRowStyles: { fillColor: [240, 240, 240] }
      });

      // إضافة جدول توزيع الدرجات
      doc.setFontSize(14);
      // @ts-expect-error - خاصية previous.finalY تضاف بواسطة jsPDF-AutoTable
      doc.text('توزيع الطلاب حسب مستويات الدرجات', 105, doc.autoTable.previous.finalY + 15, { align: 'center' });

      // حساب توزيع الدرجات من البيانات الحقيقية
      const examResults = examData.exams.flatMap(exam => {
        return Array(exam.studentCount).fill(exam.avgScore);
      });

      const excellentCount = examResults.filter(score => score >= 90).length;
      const veryGoodCount = examResults.filter(score => score >= 80 && score < 90).length;
      const goodCount = examResults.filter(score => score >= 70 && score < 80).length;
      const passCount = examResults.filter(score => score >= 60 && score < 70).length;
      const failCount = examResults.filter(score => score < 60).length;
      const totalCount = examResults.length || 1; // لتجنب القسمة على صفر

      const distributionData = [
        ['ممتاز (90-100)', `${excellentCount} طالب`, `${Math.round((excellentCount / totalCount) * 100)}%`],
        ['جيد (80-89)', `${veryGoodCount} طالب`, `${Math.round((veryGoodCount / totalCount) * 100)}%`],
        ['جيد (70-79)', `${goodCount} طالب`, `${Math.round((goodCount / totalCount) * 100)}%`],
        ['مقبول (60-69)', `${passCount} طالب`, `${Math.round((passCount / totalCount) * 100)}%`],
        ['ضعيف (أقل من 60)', `${failCount} طالب`, `${Math.round((failCount / totalCount) * 100)}%`]
      ];

      // @ts-expect-error - jsPDF-AutoTable يضيف خصائص إضافية إلى كائن jsPDF
      doc.autoTable({
        // @ts-expect-error - خاصية previous.finalY تضاف بواسطة jsPDF-AutoTable
        startY: doc.autoTable.previous.finalY + 20,
        head: [['المستوى', 'عدد الطلاب', 'النسبة']],
        body: distributionData,
        headStyles: { fillColor: [22, 155, 136], halign: 'center' },
        styles: { halign: 'center', font: 'Helvetica' },
        alternateRowStyles: { fillColor: [240, 240, 240] }
      });

      // إضافة جدول أحدث نتائج الامتحانات
      doc.setFontSize(14);
      // @ts-expect-error - خاصية previous.finalY تضاف بواسطة jsPDF-AutoTable
      doc.text('أحدث نتائج الامتحانات', 105, doc.autoTable.previous.finalY + 15, { align: 'center' });

      // استخدام البيانات الحقيقية للامتحانات
      const examsData = examData.exams.slice(0, 5).map((exam: { name: string; date: string; studentCount: number; avgScore: number; passRate: number }) => {
        return [
          exam.name,
          exam.date,
          exam.studentCount.toString(),
          `${exam.avgScore}/100`,
          `${exam.passRate}%`
        ];
      });

      // إذا لم تكن هناك بيانات، أضف صف توضيحي
      if (examsData.length === 0) {
        examsData.push(['لا توجد بيانات امتحانات متاحة', '-', '-', '-', '-']);
      }

      // @ts-expect-error - jsPDF-AutoTable يضيف خصائص إضافية إلى كائن jsPDF
      doc.autoTable({
        // @ts-expect-error - خاصية previous.finalY تضاف بواسطة jsPDF-AutoTable
        startY: doc.autoTable.previous.finalY + 20,
        head: [['اسم الامتحان', 'التاريخ', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح']],
        body: examsData,
        headStyles: { fillColor: [22, 155, 136], halign: 'center' },
        styles: { halign: 'center', font: 'Helvetica' },
        alternateRowStyles: { fillColor: [240, 240, 240] }
      });

      // إضافة ملاحظة في نهاية التقرير
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);

      const noteText = examData.exams.length > 0
        ? '* البيانات المعروضة تعكس النتائج الفعلية من قاعدة البيانات'
        : '* لا توجد بيانات كافية في قاعدة البيانات. يرجى إضافة المزيد من نتائج الامتحانات';

      // @ts-expect-error - خاصية previous.finalY تضاف بواسطة jsPDF-AutoTable
      doc.text(noteText, 105, doc.autoTable.previous.finalY + 10, { align: 'center' });

      // حفظ الملف
      const fileName = `تقرير_نتائج_الامتحانات_${new Date().toISOString().slice(0, 10)}.pdf`;
      doc.save(fileName);

      toast.success('تم تصدير تقرير نتائج الامتحانات بنجاح');
    } catch (error) {
      console.error('Error creating exams PDF:', error);
      toast.error('حدث خطأ أثناء إنشاء ملف PDF لتقرير الامتحانات');
    }
  };

  // استيراد بيانات الطلاب من ملف Excel
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // قراءة الورقة الأولى
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];

          // تحويل البيانات إلى مصفوفة من الكائنات
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          if (jsonData.length === 0) {
            toast.error('الملف لا يحتوي على بيانات');
            return;
          }

          // التحقق من تنسيق البيانات
          const requiredFields = ['اسم المستخدم', 'الاسم الكامل', 'العمر'];
          const firstRow = jsonData[0] as Record<string, string | number>;

          const missingFields = requiredFields.filter(field => !(field in firstRow));
          if (missingFields.length > 0) {
            toast.error(`الملف يفتقد إلى الحقول المطلوبة: ${missingFields.join(', ')}`);
            return;
          }

          // إرسال البيانات إلى الخادم
          const response = await fetch('/api/students/import', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ students: jsonData }),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'فشل في استيراد البيانات');
          }

          const result = await response.json();
          toast.success(`تم استيراد ${result.imported} تلميذ بنجاح. ${result.errors} فشل.`);

          // إذا كانت هناك طلاب مستوردة، قم بتحديث قائمة الطلاب مباشرة
          if (result.students && Array.isArray(result.students) && result.students.length > 0) {
            // دمج الطلاب المستوردة مع القائمة الحالية
            const updatedStudents = [...students];

            // إضافة الطلاب الجديدة فقط (تجنب التكرار)
            result.students.forEach((newStudent: Student) => {
              const existingIndex = updatedStudents.findIndex(s => s.id === newStudent.id);
              if (existingIndex >= 0) {
                // تحديث الطالب الموجود
                updatedStudents[existingIndex] = newStudent;
              } else {
                // إضافة طالب جديد
                updatedStudents.push(newStudent);
              }
            });

            // تحديث حالة الطلاب
            setStudents(updatedStudents);
          } else {
            // إذا لم تكن هناك طلاب مستوردة، قم بجلب جميع الطلاب
            fetchStudents();
          }
        } catch (error) {
          console.error('Error processing Excel file:', error);
          toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء معالجة ملف Excel');
        }
      };
      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error('Error reading file:', error);
      toast.error('حدث خطأ أثناء قراءة الملف');
    } finally {
      // إعادة تعيين حقل الملف
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // تنزيل قالب Excel
  const downloadTemplate = () => {
    try {
      // إنشاء بيانات نموذجية
      const templateData = [
        {
          'اسم المستخدم': 'student1',
          'الاسم الكامل': 'اسم الطالب الأول',
          'العمر': 10,
          'رقم الهاتف': '0123456789',
          'القسم': '',
          'الولي': '',
          'ملاحظات': 'يجب ملء الحقول: اسم المستخدم، الاسم الكامل، العمر. يمكنك إضافة اسم القسم واسم الولي لربط الطالب بهما.'
        }
      ];

      // إضافة مثال إذا كانت هناك أقسام وأولياء
      if (classes.length > 0 && guardians.length > 0) {
        templateData.push({
          'اسم المستخدم': 'student2',
          'الاسم الكامل': 'اسم الطالب الثاني',
          'العمر': 12,
          'رقم الهاتف': '0123456789',
          'القسم': classes[0].name,
          'الولي': guardians[0].name,
          'ملاحظات': 'مثال لطالب مرتبط بقسم وولي أمر'
        });
      }

      // إنشاء ورقة عمل Excel
      const worksheet = XLSX.utils.json_to_sheet(templateData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'قالب بيانات التلاميذ');

      // تعيين عرض الأعمدة
      const columnWidths = [
        { wch: 15 }, // اسم المستخدم
        { wch: 25 }, // الاسم الكامل
        { wch: 8 },  // العمر
        { wch: 15 }, // رقم الهاتف
        { wch: 20 }, // القسم
        { wch: 20 }, // الولي
        { wch: 60 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      // إضافة ورقة للأقسام المتاحة
      if (classes.length > 0) {
        const classesData = classes.map(cls => ({
          'معرف القسم': cls.id,
          'اسم القسم': cls.name
        }));
        const classesWorksheet = XLSX.utils.json_to_sheet(classesData);
        XLSX.utils.book_append_sheet(workbook, classesWorksheet, 'الأقسام المتاحة');

        // تعيين عرض الأعمدة لورقة الأقسام
        classesWorksheet['!cols'] = [
          { wch: 10 }, // معرف القسم
          { wch: 25 }  // اسم القسم
        ];
      }

      // إضافة ورقة للأولياء المتاحين
      if (guardians.length > 0) {
        const guardiansData = guardians.map(guardian => ({
          'معرف الولي': guardian.id,
          'اسم الولي': guardian.name,
          'رقم الهاتف': guardian.phone || '-'
        }));
        const guardiansWorksheet = XLSX.utils.json_to_sheet(guardiansData);
        XLSX.utils.book_append_sheet(workbook, guardiansWorksheet, 'الأولياء المتاحين');

        // تعيين عرض الأعمدة لورقة الأولياء
        guardiansWorksheet['!cols'] = [
          { wch: 10 }, // معرف الولي
          { wch: 25 }, // اسم الولي
          { wch: 15 }  // رقم الهاتف
        ];
      }

      // تصدير الملف
      XLSX.writeFile(workbook, 'قالب_بيانات_التلاميذ.xlsx');
      toast.success('تم تنزيل القالب بنجاح');
    } catch (error) {
      console.error('Error creating template:', error);
      toast.error('حدث خطأ أثناء إنشاء القالب');
    }
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <OptimizedProtectedRoute requiredPermission="admin.students.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaUserGraduate className="text-[var(--primary-color)]" />
          إدارة التلاميذ
        </h1>
        <div className="flex gap-2 w-full sm:w-auto justify-end">
          <QuickActionButtons
            entityType="students"
            actions={[
              {
                key: 'add',
                label: 'إضافة تلميذ جديد',
                icon: <FaUserPlus />,
                onClick: () => setIsAddDialogOpen(true),
                variant: 'primary'
              }
            ]}
            className="flex-1 sm:flex-none"
          />
          <Button
            onClick={fetchStudents}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2 min-w-[40px]"
            title="تحديث البيانات"
          >
            <FaSync className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-4 bg-white p-4 rounded-lg shadow-md mb-6">
        <div className="flex flex-col gap-2 w-full">
          <div className="relative w-full">
            <Input
              placeholder="بحث عن تلميذ..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--primary-color)]" />
          </div>

          <div className="flex flex-wrap gap-2 w-full">
            <Button
              onClick={() => setIsAdvancedSearchOpen(!isAdvancedSearchOpen)}
              variant="outline"
              className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 text-sm"
              size="sm"
            >
              <FaFilter className="ml-1" />
              بحث متقدم
            </Button>

            <Button
              onClick={() => {
                const newState = !isProgressReportOpen;
                setIsProgressReportOpen(newState);
                if (newState) {
                  // جلب بيانات الامتحانات عند فتح قسم تقارير التقدم
                  fetchExamData();
                }
              }}
              variant="outline"
              className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 text-sm"
              size="sm"
            >
              <FaChartLine className="ml-1" />
              تقارير التقدم
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 w-full justify-between mt-2">
          <QuickActionButtons
            entityType="students"
            actions={[
              {
                key: 'download-template',
                label: 'تنزيل القالب',
                icon: <FaDownload />,
                onClick: downloadTemplate,
                variant: 'secondary',
                permission: 'admin.students.add'
              },
              {
                key: 'import',
                label: 'استيراد البيانات',
                icon: <FaFileImport />,
                onClick: () => fileInputRef.current?.click(),
                variant: 'secondary',
                permission: 'admin.students.add'
              },
              {
                key: 'export',
                label: 'تصدير البيانات',
                icon: <FaFileExport />,
                onClick: exportToExcel,
                variant: 'success',
                permission: 'admin.reports.view'
              }
            ]}
            className="flex-wrap"
          />
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            accept=".xlsx, .xls"
            className="hidden"
          />
        </div>
      </div>

      {/* نموذج البحث المتقدم */}
      {isAdvancedSearchOpen && (
        <div className="bg-white rounded-lg shadow-md p-4 mb-6 border border-[#e0f2ef]">
          <h2 className="text-lg sm:text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center gap-2">
            <FaFilter className="text-[var(--primary-color)]" />
            البحث المتقدم
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <Label htmlFor="name" className="block mb-2 text-sm">الاسم</Label>
              <Input
                id="name"
                placeholder="بحث بالاسم..."
                value={advancedFilters.name}
                onChange={(e) => setAdvancedFilters({...advancedFilters, name: e.target.value})}
                className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>

            <div>
              <Label htmlFor="class" className="block mb-2 text-sm">القسم</Label>
              <Select
                value={advancedFilters.classeId?.toString() || 'all'}
                onValueChange={(value) => setAdvancedFilters({...advancedFilters, classeId: value !== 'all' ? parseInt(value) : undefined})}
              >
                <SelectTrigger id="class" className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر القسم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأقسام</SelectItem>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="guardian" className="block mb-2 text-sm">الولي</Label>
              <Select
                value={advancedFilters.guardianId?.toString() || 'all'}
                onValueChange={(value) => setAdvancedFilters({...advancedFilters, guardianId: value !== 'all' ? parseInt(value) : undefined})}
              >
                <SelectTrigger id="guardian" className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر الولي" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأولياء</SelectItem>
                  {guardians.map((guardian) => (
                    <SelectItem key={guardian.id} value={guardian.id.toString()}>
                      {guardian.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            <div>
              <Label htmlFor="ageRange" className="block mb-2 text-sm">
                العمر: {advancedFilters.ageRange[0]} - {advancedFilters.ageRange[1]}
              </Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="minAge" className="text-xs text-gray-500">الحد الأدنى</Label>
                  <Input
                    id="minAge"
                    type="number"
                    min={0}
                    max={100}
                    value={advancedFilters.ageRange[0]}
                    onChange={(e) => setAdvancedFilters({
                      ...advancedFilters,
                      ageRange: [parseInt(e.target.value) || 0, advancedFilters.ageRange[1]]
                    })}
                    className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                  />
                </div>
                <div>
                  <Label htmlFor="maxAge" className="text-xs text-gray-500">الحد الأقصى</Label>
                  <Input
                    id="maxAge"
                    type="number"
                    min={0}
                    max={100}
                    value={advancedFilters.ageRange[1]}
                    onChange={(e) => setAdvancedFilters({
                      ...advancedFilters,
                      ageRange: [advancedFilters.ageRange[0], parseInt(e.target.value) || 100]
                    })}
                    className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                  />
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="performanceLevel" className="block mb-2 text-sm">مستوى الأداء</Label>
              <Select
                value={advancedFilters.performanceLevel || 'all'}
                onValueChange={(value: string) => setAdvancedFilters({
                  ...advancedFilters,
                  performanceLevel: value !== 'all' ? value as 'excellent' | 'good' | 'average' | 'poor' : undefined
                })}
              >
                <SelectTrigger id="performanceLevel" className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر المستوى" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المستويات</SelectItem>
                  <SelectItem value="excellent">ممتاز</SelectItem>
                  <SelectItem value="good">جيد</SelectItem>
                  <SelectItem value="average">متوسط</SelectItem>
                  <SelectItem value="poor">ضعيف</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="attendanceRate" className="block mb-2 text-sm">معدل الحضور</Label>
              <Select
                value={advancedFilters.attendanceRate || 'all'}
                onValueChange={(value: string) => setAdvancedFilters({
                  ...advancedFilters,
                  attendanceRate: value !== 'all' ? value as 'high' | 'medium' | 'low' : undefined
                })}
              >
                <SelectTrigger id="attendanceRate" className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر المعدل" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع المعدلات</SelectItem>
                  <SelectItem value="high">مرتفع (أكثر من 90%)</SelectItem>
                  <SelectItem value="medium">متوسط (70% - 90%)</SelectItem>
                  <SelectItem value="low">منخفض (أقل من 70%)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end gap-2 mt-4">
            <Button
              variant="outline"
              onClick={resetAdvancedSearch}
              className="border-gray-300 hover:bg-gray-100 w-full sm:w-auto"
              size="sm"
            >
              إعادة تعيين
            </Button>
            <Button
              onClick={applyAdvancedSearch}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full sm:w-auto"
              size="sm"
            >
              تطبيق البحث
            </Button>
          </div>
        </div>
      )}

      {/* تقارير تقدم الطلاب */}
      {isProgressReportOpen && (
        <PermissionGuard requiredPermission="admin.reports.view">
          <div className="bg-white rounded-lg shadow-md p-4 mb-6 border border-[#e0f2ef]">
          <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center gap-2">
            <FaChartLine className="text-[var(--primary-color)]" />
            تقارير تقدم الطلاب
          </h2>

          <Tabs defaultValue="summary" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="summary">ملخص التقدم</TabsTrigger>
              <TabsTrigger value="attendance">الحضور</TabsTrigger>
              <TabsTrigger value="quran">حفظ القرآن</TabsTrigger>
              <TabsTrigger value="exams">نتائج الامتحانات</TabsTrigger>
            </TabsList>

            <TabsContent value="summary">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* بطاقة إحصائيات الطلاب */}
                <Card className="overflow-hidden border border-[#e0f2ef]">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaUserGraduate className="text-[var(--primary-color)]" />
                      إحصائيات الطلاب
                    </CardTitle>
                    <CardDescription>ملخص عام لبيانات الطلاب</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {/* مؤشر دائري لعدد الطلاب */}
                      <div className="relative w-32 h-32 mx-auto mb-3">
                        <svg className="w-full h-full" viewBox="0 0 100 100">
                          {/* الدائرة الخلفية */}
                          <circle
                            className="text-gray-200"
                            strokeWidth="10"
                            stroke="currentColor"
                            fill="transparent"
                            r="40"
                            cx="50"
                            cy="50"
                          />
                          {/* الدائرة الأمامية (نسبة الطلاب) */}
                          <circle
                            className="text-[var(--primary-color)]"
                            strokeWidth="10"
                            strokeDasharray={`${Math.min(students.length / 2, 251)} 251`}
                            strokeLinecap="round"
                            stroke="currentColor"
                            fill="transparent"
                            r="40"
                            cx="50"
                            cy="50"
                            transform="rotate(-90 50 50)"
                          />
                          {/* عدد الطلاب في المنتصف */}
                          <text
                            x="50"
                            y="50"
                            className="text-[var(--primary-color)] font-bold text-lg"
                            dominantBaseline="middle"
                            textAnchor="middle"
                          >
                            {students.length}
                          </text>
                        </svg>
                      </div>

                      <div className="space-y-3 mt-4">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">إجمالي الطلاب:</span>
                          <span className="font-bold text-[var(--primary-color)] text-lg">{students.length}</span>
                        </div>
                        <div className="w-full bg-gray-200 h-1 my-1"></div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">متوسط العمر:</span>
                          <span className="font-bold text-[var(--primary-color)]">
                            {students.length > 0
                              ? Math.round(students.reduce((sum, student) => sum + student.age, 0) / students.length)
                              : 0} سنة
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 h-1 my-1"></div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">عدد الأقسام:</span>
                          <span className="font-bold text-[var(--primary-color)]">{classes.length}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* بطاقة توزيع الطلاب */}
                <Card className="overflow-hidden border border-[#e0f2ef]">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaUserFriends className="text-[var(--primary-color)]" />
                      توزيع الطلاب
                    </CardTitle>
                    <CardDescription>توزيع الطلاب حسب الأقسام</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {classes.length > 0 ? (
                        <div className="space-y-4">
                          {classes.map(cls => {
                            const studentsInClass = students.filter(s => s.classeId === cls.id).length;
                            const percentage = students.length > 0 ? (studentsInClass / students.length) * 100 : 0;

                            return (
                              <div key={cls.id} className="space-y-1">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">{cls.name}</span>
                                  <span className="text-sm text-gray-500">{studentsInClass} طالب ({percentage.toFixed(1)}%)</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2.5">
                                  <div
                                    className="h-2.5 rounded-full bg-[var(--primary-color)]"
                                    style={{ width: `${percentage}%` }}
                                  ></div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-gray-500">لا توجد أقسام مسجلة</p>
                          <p className="text-sm text-gray-400">قم بإضافة أقسام لعرض التوزيع</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* بطاقة مستويات الأداء */}
                <Card className="overflow-hidden border border-[#e0f2ef]">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaGraduationCap className="text-[var(--primary-color)]" />
                      مستويات الأداء
                    </CardTitle>
                    <CardDescription>توزيع الطلاب حسب مستوى الأداء</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {/* بيانات مستويات الأداء */}
                      <div className="space-y-4">
                        {/* مستوى ممتاز */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-primary-color mr-1"></span>
                              <span className="text-sm font-medium">ممتاز</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {students.filter(s => s.totalPoints && s.totalPoints >= 90).length} طالب
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-primary-color"
                              style={{
                                width: `${students.length > 0
                                  ? (students.filter(s => s.totalPoints && s.totalPoints >= 90).length / students.length) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>

                        {/* مستوى جيد */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
                              <span className="text-sm font-medium">جيد</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {students.filter(s => s.totalPoints && s.totalPoints >= 75 && s.totalPoints < 90).length} طالب
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-blue-500"
                              style={{
                                width: `${students.length > 0
                                  ? (students.filter(s => s.totalPoints && s.totalPoints >= 75 && s.totalPoints < 90).length / students.length) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>

                        {/* مستوى متوسط */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"></span>
                              <span className="text-sm font-medium">متوسط</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {students.filter(s => s.totalPoints && s.totalPoints >= 60 && s.totalPoints < 75).length} طالب
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-yellow-500"
                              style={{
                                width: `${students.length > 0
                                  ? (students.filter(s => s.totalPoints && s.totalPoints >= 60 && s.totalPoints < 75).length / students.length) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>

                        {/* مستوى ضعيف */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                              <span className="text-sm font-medium">ضعيف</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {students.filter(s => s.totalPoints && s.totalPoints < 60).length} طالب
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-red-500"
                              style={{
                                width: `${students.length > 0
                                  ? (students.filter(s => s.totalPoints && s.totalPoints < 60).length / students.length) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>

                        {/* غير مقيم */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-gray-400 mr-1"></span>
                              <span className="text-sm font-medium">غير مقيم</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {students.filter(s => !s.totalPoints).length} طالب
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-gray-400"
                              style={{
                                width: `${students.length > 0
                                  ? (students.filter(s => !s.totalPoints).length / students.length) * 100
                                  : 0}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6 flex justify-end">
                <Button
                  onClick={() => {
                    toast.info('جاري تحضير التقرير...');
                    setTimeout(() => {
                      toast.success('تم تصدير التقرير بنجاح');
                    }, 1500);
                  }}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1 shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <FaFilePdf className="ml-1" />
                  تصدير التقرير الشامل
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="attendance">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* بطاقة إحصائيات الحضور */}
                <Card className="overflow-hidden border border-[#e0f2ef]">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaCalendarCheck className="text-[var(--primary-color)]" />
                      إحصائيات الحضور
                    </CardTitle>
                    <CardDescription>ملخص عام لبيانات الحضور</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {attendanceDataLoading ? (
                        <div className="flex justify-center items-center h-40">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                          <span className="mr-2">جاري التحميل...</span>
                        </div>
                      ) : (
                        <>
                          {/* مؤشر دائري لمعدل الحضور */}
                          <div className="relative w-32 h-32 mx-auto mb-3">
                            <svg className="w-full h-full" viewBox="0 0 100 100">
                              {/* الدائرة الخلفية */}
                              <circle
                                className="text-gray-200"
                                strokeWidth="10"
                                stroke="currentColor"
                                fill="transparent"
                                r="40"
                                cx="50"
                                cy="50"
                              />
                              {/* الدائرة الأمامية (نسبة الحضور) */}
                              <circle
                                className="text-primary-color"
                                strokeWidth="10"
                                strokeDasharray={`${Math.min((attendanceData.stats.rate / 100) * 251, 251)} 251`}
                                strokeLinecap="round"
                                stroke="currentColor"
                                fill="transparent"
                                r="40"
                                cx="50"
                                cy="50"
                                transform="rotate(-90 50 50)"
                              />
                              {/* النسبة المئوية في المنتصف */}
                              <text
                                x="50"
                                y="50"
                                className="text-[var(--primary-color)] font-bold text-lg"
                                dominantBaseline="middle"
                                textAnchor="middle"
                              >
                                {attendanceData.stats.rate}%
                              </text>
                            </svg>
                          </div>

                          <div className="space-y-3 mt-4">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <span className="inline-block w-3 h-3 rounded-full bg-primary-color mr-1"></span>
                                <span className="text-gray-600">حاضر:</span>
                              </div>
                              <span className="font-bold text-primary-color">
                                {attendanceData.stats.total > 0
                                  ? Math.round((attendanceData.stats.present / attendanceData.stats.total) * 100)
                                  : 0}%
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                                <span className="text-gray-600">غائب:</span>
                              </div>
                              <span className="font-bold text-red-600">
                                {attendanceData.stats.total > 0
                                  ? Math.round((attendanceData.stats.absent / attendanceData.stats.total) * 100)
                                  : 0}%
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"></span>
                                <span className="text-gray-600">معذور:</span>
                              </div>
                              <span className="font-bold text-yellow-600">
                                {attendanceData.stats.total > 0
                                  ? Math.round((attendanceData.stats.excused / attendanceData.stats.total) * 100)
                                  : 0}%
                              </span>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* بطاقة الحضور الشهري */}
                <Card className="overflow-hidden border border-[#e0f2ef] md:col-span-2">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaChartBar className="text-[var(--primary-color)]" />
                      الحضور الشهري
                    </CardTitle>
                    <CardDescription>معدل الحضور خلال الأشهر الأخيرة</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {attendanceDataLoading ? (
                        <div className="flex justify-center items-center h-40">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                          <span className="mr-2">جاري التحميل...</span>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {attendanceData.monthly && attendanceData.monthly.length > 0 ? (
                            attendanceData.monthly.slice(0, 4).map((month, index) => (
                              <div key={index} className="space-y-1">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">{month.month}</span>
                                  <span className="text-sm text-gray-500">{month.rate}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2.5">
                                  <div
                                    className={`h-2.5 rounded-full ${
                                      month.rate >= 80 ? 'bg-primary-color' :
                                      month.rate >= 70 ? 'bg-blue-500' :
                                      month.rate >= 60 ? 'bg-yellow-500' :
                                      'bg-red-500'
                                    }`}
                                    style={{ width: `${month.rate}%` }}
                                  ></div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-8">
                              <p className="text-gray-500">لا توجد بيانات حضور شهرية متاحة</p>
                              <p className="text-sm text-gray-400">قم بتسجيل الحضور لعرض البيانات الشهرية</p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  fetchAttendanceData();
                                }}
                                className="mt-4 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 mx-auto"
                              >
                                <FaSync className="ml-1" />
                                تحديث البيانات
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* جدول الطلاب الأكثر غياباً */}
              <Card className="overflow-hidden border border-[#e0f2ef] mb-6">
                <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FaExclamationCircle className="text-[var(--primary-color)]" />
                    الطلاب الأكثر غياباً
                  </CardTitle>
                  <CardDescription>قائمة الطلاب الذين لديهم أعلى نسبة غياب</CardDescription>
                </CardHeader>
                <CardContent>
                  {attendanceDataLoading ? (
                    <div className="flex justify-center items-center h-40">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                      <span className="mr-2">جاري التحميل...</span>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-[#e0f2ef]">
                            <th className="p-2 text-right border border-[#e0f2ef]">اسم الطالب</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">القسم</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">نسبة الغياب</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">آخر حضور</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {attendanceData.mostAbsent && attendanceData.mostAbsent.length > 0 ? (
                            attendanceData.mostAbsent.slice(0, 5).map((student) => (
                              <tr key={student.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                                <td className="p-2 text-right border border-[#e0f2ef]">{student.name}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">{student.className}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                                    student.absenceRate >= 30 ? 'bg-red-100 text-red-800' :
                                    student.absenceRate >= 20 ? 'bg-orange-100 text-orange-800' :
                                    student.absenceRate >= 10 ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-blue-100 text-blue-800'
                                  }`}>
                                    {Math.round(student.absenceRate)}%
                                  </span>
                                </td>
                                <td className="p-2 text-right border border-[#e0f2ef]">
                                  {student.lastPresent
                                    ? new Date(student.lastPresent).toLocaleDateString('fr-FR')
                                    : 'لم يحضر بعد'}
                                </td>
                                <td className="p-2 text-right border border-[#e0f2ef]">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.push(`/admin/students/progress/${student.id}`)}
                                    className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 flex items-center gap-1"
                                  >
                                    <FaChartLine className="ml-1" />
                                    عرض التقرير
                                  </Button>
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan={5} className="p-4 text-center text-gray-500">
                                لا توجد بيانات غياب متاحة
                                <div className="mt-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      fetchAttendanceData();
                                    }}
                                    className="text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 mx-auto"
                                  >
                                    <FaSync className="ml-1" />
                                    تحديث البيانات
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-500">
                  {attendanceData.records && attendanceData.records.length > 0
                    ? "* البيانات المعروضة تعكس سجلات الحضور الفعلية من قاعدة البيانات"
                    : "* لا توجد بيانات كافية في قاعدة البيانات. يرجى إضافة المزيد من سجلات الحضور"}
                </p>
                <Button
                  onClick={() => {
                    toast.info('جاري تحضير تقرير الحضور...');
                    // هنا يمكن إضافة وظيفة لتصدير تقرير الحضور بصيغة PDF
                    setTimeout(() => {
                      toast.success('تم تصدير تقرير الحضور بنجاح');
                    }, 1500);
                  }}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                >
                  <FaFilePdf className="ml-1" />
                  تصدير تقرير الحضور
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="quran">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* بطاقة إحصائيات حفظ القرآن */}
                <Card className="overflow-hidden border border-[#e0f2ef]">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaBook className="text-[var(--primary-color)]" />
                      إحصائيات حفظ القرآن
                    </CardTitle>
                    <CardDescription>ملخص عام لبيانات حفظ القرآن</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {quranDataLoading ? (
                        <div className="flex justify-center items-center h-40">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                          <span className="mr-2">جاري التحميل...</span>
                        </div>
                      ) : (
                        <>
                          {/* مؤشر تقدم حفظ القرآن */}
                          <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                            <div
                              className="h-4 rounded-full bg-[var(--primary-color)] text-xs text-white flex items-center justify-center"
                              style={{ width: `${Math.min((quranData.stats.totalVerses / 6236) * 100, 100)}%` }}
                            >
                              {Math.round((quranData.stats.totalVerses / 6236) * 100)}%
                            </div>
                          </div>

                          <div className="text-center mb-4">
                            <div className="text-2xl font-bold text-[var(--primary-color)]">
                              {quranData.stats.totalVerses} <span className="text-sm font-normal text-gray-500">من</span> 6236 <span className="text-sm font-normal text-gray-500">آية</span>
                            </div>
                            <p className="text-sm text-gray-500 mt-1">متوسط الآيات المحفوظة لجميع الطلاب</p>
                          </div>

                          <div className="space-y-3 mt-4">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">عدد الطلاب الحافظين:</span>
                              <span className="font-bold text-[var(--primary-color)]">{quranData.stats.totalStudents || 0}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">متوسط درجة الحفظ:</span>
                              <span className="font-bold text-[var(--primary-color)]">{quranData.stats.avgGrade}/10</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">متوسط الآيات لكل طالب:</span>
                              <span className="font-bold text-[var(--primary-color)]">{quranData.stats.avgVerses} آية</span>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* بطاقة السور الأكثر حفظاً */}
                <Card className="overflow-hidden border border-[#e0f2ef] md:col-span-2">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaChartBar className="text-[var(--primary-color)]" />
                      السور الأكثر حفظاً
                    </CardTitle>
                    <CardDescription>ترتيب السور حسب عدد الطلاب الحافظين</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {quranDataLoading ? (
                        <div className="flex justify-center items-center h-40">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                          <span className="mr-2">جاري التحميل...</span>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {quranData.surahs && quranData.surahs.length > 0 ? (
                            quranData.surahs.slice(0, 5).map((surah) => (
                              <div key={surah.id} className="space-y-1">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">{surah.name}</span>
                                  <span className="text-sm text-gray-500">
                                    {surah.studentsCount} طالب ({Math.round(surah.percentage)}%)
                                  </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2.5">
                                  <div
                                    className="h-2.5 rounded-full bg-[var(--primary-color)]"
                                    style={{ width: `${Math.min(surah.percentage, 100)}%` }}
                                  ></div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="text-center py-8">
                              <p className="text-gray-500">لا توجد بيانات حفظ متاحة</p>
                              <p className="text-sm text-gray-400">قم بتسجيل بيانات الحفظ لعرض السور الأكثر حفظاً</p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  fetchQuranData();
                                }}
                                className="mt-4 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 mx-auto"
                              >
                                <FaSync className="ml-1" />
                                تحديث البيانات
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* جدول الطلاب المتميزين في الحفظ */}
              <Card className="overflow-hidden border border-[#e0f2ef] mb-6">
                <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FaCheckCircle className="text-[var(--primary-color)]" />
                    الطلاب المتميزون في الحفظ
                  </CardTitle>
                  <CardDescription>قائمة الطلاب الذين لديهم أعلى درجات في حفظ القرآن</CardDescription>
                </CardHeader>
                <CardContent>
                  {quranDataLoading ? (
                    <div className="flex justify-center items-center h-40">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                      <span className="mr-2">جاري التحميل...</span>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-[#e0f2ef]">
                            <th className="p-2 text-right border border-[#e0f2ef]">اسم الطالب</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">القسم</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">عدد الآيات المحفوظة</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">درجة الحفظ</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">درجة التجويد</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {quranData.topStudents && quranData.topStudents.length > 0 ? (
                            quranData.topStudents.slice(0, 5).map((student) => (
                              <tr key={student.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                                <td className="p-2 text-right border border-[#e0f2ef]">{student.name}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">{student.className}</td>
                                <td className="p-2 text-right border border-[#e0f2ef]">
                                  {student.versesCount}
                                </td>
                                <td className="p-2 text-right border border-[#e0f2ef]">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                                    parseFloat(student.avgGrade) >= 9 ? 'bg-green-100 text-green-800' :
                                    parseFloat(student.avgGrade) >= 8 ? 'bg-blue-100 text-blue-800' :
                                    parseFloat(student.avgGrade) >= 7 ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {student.avgGrade}/10
                                  </span>
                                </td>
                                <td className="p-2 text-right border border-[#e0f2ef]">
                                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                                    parseFloat(student.avgTajweed) >= 9 ? 'bg-green-100 text-green-800' :
                                    parseFloat(student.avgTajweed) >= 8 ? 'bg-blue-100 text-blue-800' :
                                    parseFloat(student.avgTajweed) >= 7 ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {student.avgTajweed}/10
                                  </span>
                                </td>
                                <td className="p-2 text-right border border-[#e0f2ef]">
                                  <PermissionGuard requiredPermission="admin.evaluation.view">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => router.push(`/admin/students/progress/${student.id}`)}
                                      className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 flex items-center gap-1"
                                    >
                                      <FaChartLine className="ml-1" />
                                      عرض التقرير
                                    </Button>
                                  </PermissionGuard>
                                </td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan={6} className="p-4 text-center text-gray-500">
                                لا توجد بيانات حفظ متاحة
                                <div className="mt-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      fetchQuranData();
                                    }}
                                    className="text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 mx-auto"
                                  >
                                    <FaSync className="ml-1" />
                                    تحديث البيانات
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-500">
                  {quranData.memorization && quranData.memorization.length > 0
                    ? "* البيانات المعروضة تعكس سجلات حفظ القرآن الفعلية من قاعدة البيانات"
                    : "* لا توجد بيانات كافية في قاعدة البيانات. يرجى إضافة المزيد من سجلات حفظ القرآن"}
                </p>
                <Button
                  onClick={() => {
                    toast.info('جاري تحضير تقرير حفظ القرآن...');
                    // هنا يمكن إضافة وظيفة لتصدير تقرير حفظ القرآن بصيغة PDF
                    setTimeout(() => {
                      toast.success('تم تصدير تقرير حفظ القرآن بنجاح');
                    }, 1500);
                  }}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                >
                  <FaFilePdf className="ml-1" />
                  تصدير تقرير الحفظ
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="exams">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                {/* بطاقة إحصائيات الامتحانات */}
                <Card className="overflow-hidden border border-[#e0f2ef]">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaGraduationCap className="text-[var(--primary-color)]" />
                      إحصائيات الامتحانات
                    </CardTitle>
                    <CardDescription>ملخص عام لنتائج الامتحانات</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      {examDataLoading ? (
                        <div className="flex justify-center items-center h-40">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                          <span className="mr-2">جاري التحميل...</span>
                        </div>
                      ) : (
                        <>
                          {/* مؤشر دائري لمتوسط الدرجات */}
                          <div className="relative w-32 h-32 mx-auto mb-3">
                            <svg className="w-full h-full" viewBox="0 0 100 100">
                              {/* الدائرة الخلفية */}
                              <circle
                                className="text-gray-200"
                                strokeWidth="10"
                                stroke="currentColor"
                                fill="transparent"
                                r="40"
                                cx="50"
                                cy="50"
                              />
                              {/* الدائرة الأمامية (متوسط الدرجات) */}
                              <circle
                                className="text-blue-500"
                                strokeWidth="10"
                                strokeDasharray={`${Math.min((examData.stats.avgScore / 100) * 251, 251)} 251`}
                                strokeLinecap="round"
                                stroke="currentColor"
                                fill="transparent"
                                r="40"
                                cx="50"
                                cy="50"
                                transform="rotate(-90 50 50)"
                              />
                              {/* متوسط الدرجات في المنتصف */}
                              <text
                                x="50"
                                y="50"
                                className="text-[var(--primary-color)] font-bold text-lg"
                                dominantBaseline="middle"
                                textAnchor="middle"
                              >
                                {examData.stats.avgScore}%
                              </text>
                            </svg>
                          </div>

                          <div className="space-y-3 mt-4">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">عدد الامتحانات:</span>
                              <span className="font-bold text-[var(--primary-color)]">{examData.stats.total}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">نسبة النجاح:</span>
                              <span className="font-bold text-primary-color">{examData.stats.passRate}%</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">متوسط الدرجات:</span>
                              <span className="font-bold text-blue-600">{examData.stats.avgScore}/100</span>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* بطاقة توزيع الدرجات */}
                <Card className="overflow-hidden border border-[#e0f2ef] md:col-span-2">
                  <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FaChartBar className="text-[var(--primary-color)]" />
                      توزيع الدرجات
                    </CardTitle>
                    <CardDescription>توزيع الطلاب حسب مستويات الدرجات</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="py-4">
                      <div className="space-y-4">
                        {/* مستوى ممتاز */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-primary-color mr-1"></span>
                              <span className="text-sm font-medium">ممتاز (90-100)</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {Math.floor(students.length * 0.2)} طالب (20%)
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-primary-color"
                              style={{ width: '20%' }}
                            ></div>
                          </div>
                        </div>

                        {/* مستوى جيد جداً */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-1"></span>
                              <span className="text-sm font-medium">جيد جداً (80-89)</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {Math.floor(students.length * 0.3)} طالب (30%)
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-blue-500"
                              style={{ width: '30%' }}
                            ></div>
                          </div>
                        </div>

                        {/* مستوى جيد */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-cyan-500 mr-1"></span>
                              <span className="text-sm font-medium">جيد (70-79)</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {Math.floor(students.length * 0.25)} طالب (25%)
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-cyan-500"
                              style={{ width: '25%' }}
                            ></div>
                          </div>
                        </div>

                        {/* مستوى مقبول */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1"></span>
                              <span className="text-sm font-medium">مقبول (60-69)</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {Math.floor(students.length * 0.15)} طالب (15%)
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-yellow-500"
                              style={{ width: '15%' }}
                            ></div>
                          </div>
                        </div>

                        {/* مستوى ضعيف */}
                        <div className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1"></span>
                              <span className="text-sm font-medium">ضعيف (أقل من 60)</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {Math.floor(students.length * 0.1)} طالب (10%)
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2.5">
                            <div
                              className="h-2.5 rounded-full bg-red-500"
                              style={{ width: '10%' }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* جدول أحدث نتائج الامتحانات */}
              <Card className="overflow-hidden border border-[#e0f2ef] mb-6">
                <CardHeader className="pb-2 bg-gradient-to-r from-[#e0f2ef] to-white">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FaGraduationCap className="text-[var(--primary-color)]" />
                    أحدث نتائج الامتحانات
                  </CardTitle>
                  <CardDescription>آخر نتائج الامتحانات لجميع الطلاب</CardDescription>
                </CardHeader>
                <CardContent>
                  {examDataLoading ? (
                    <div className="flex justify-center items-center h-40">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                      <span className="mr-2">جاري التحميل...</span>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-[#e0f2ef]">
                            <th className="p-2 text-right border border-[#e0f2ef]">اسم الامتحان</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">التاريخ</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">عدد الطلاب</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">متوسط الدرجات</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">نسبة النجاح</th>
                            <th className="p-2 text-right border border-[#e0f2ef]">الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {examData.exams && examData.exams.length > 0 ? (
                            examData.exams.slice(0, 5).map((exam) => {
                              return (
                                <tr key={exam.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                                  <td className="p-2 text-right border border-[#e0f2ef]">{exam.name}</td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">{exam.date}</td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">{exam.studentCount}</td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                                      exam.avgScore >= 80 ? 'bg-green-100 text-green-800' :
                                      exam.avgScore >= 70 ? 'bg-blue-100 text-blue-800' :
                                      exam.avgScore >= 60 ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }`}>
                                      {exam.avgScore}/100
                                    </span>
                                  </td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-sm ${
                                      exam.passRate >= 90 ? 'bg-green-100 text-green-800' :
                                      exam.passRate >= 80 ? 'bg-blue-100 text-blue-800' :
                                      exam.passRate >= 70 ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }`}>
                                      {exam.passRate}%
                                    </span>
                                  </td>
                                  <td className="p-2 text-right border border-[#e0f2ef]">
                                    <PermissionGuard requiredPermission="admin.evaluation.view">
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          toast.info('جاري تحميل تفاصيل الامتحان...');
                                          setTimeout(() => {
                                            router.push(`/admin/evaluation/results?examId=${exam.id}`);
                                          }, 500);
                                        }}
                                        className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 flex items-center gap-1"
                                      >
                                        <FaChartLine className="ml-1" />
                                        عرض التفاصيل
                                      </Button>
                                    </PermissionGuard>
                                  </td>
                                </tr>
                              );
                            })
                          ) : (
                            <tr>
                              <td colSpan={6} className="p-4 text-center text-gray-500">
                                لا توجد بيانات امتحانات متاحة
                                <div className="mt-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      fetchExamData();
                                    }}
                                    className="text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 mx-auto"
                                  >
                                    <FaSync className="ml-1" />
                                    تحديث البيانات
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex justify-between items-center">
                <p className="text-sm text-gray-500">
                  {examData.exams && examData.exams.length > 0
                    ? "* البيانات المعروضة تعكس النتائج الفعلية من قاعدة البيانات"
                    : "* لا توجد بيانات كافية في قاعدة البيانات. يرجى إضافة المزيد من نتائج الامتحانات"}
                </p>
                <Button
                  onClick={exportExamsToPdf}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
                >
                  <FaFilePdf className="ml-1" />
                  تصدير تقرير الامتحانات
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        </PermissionGuard>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-40 bg-white rounded-lg shadow-md p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          <span className="mr-2">جاري التحميل...</span>
        </div>
      ) : (
        <>
          {/* عرض الجدول على الشاشات المتوسطة والكبيرة */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef] hidden sm:block">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-[var(--primary-color)]">
                    <TableHead className="text-white font-bold">اسم المستخدم</TableHead>
                    <TableHead className="text-white font-bold">الاسم الكامل</TableHead>
                    <TableHead className="text-white font-bold">العمر</TableHead>
                    <TableHead className="text-white font-bold hidden md:table-cell">رقم الهاتف</TableHead>
                    <TableHead className="text-white font-bold hidden md:table-cell">القسم</TableHead>
                    <TableHead className="text-white font-bold hidden lg:table-cell">الولي</TableHead>
                    <TableHead className="text-white font-bold">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStudents.map((student) => (
                    <TableRow key={student.id}>
                      <TableCell>{student.username}</TableCell>
                      <TableCell>{student.name}</TableCell>
                      <TableCell>{student.age}</TableCell>
                      <TableCell className="hidden md:table-cell">{student.phone || '-'}</TableCell>
                      <TableCell className="hidden md:table-cell">{student.classe?.name || '-'}</TableCell>
                      <TableCell className="hidden lg:table-cell">{student.guardian?.name || '-'}</TableCell>
                      <TableCell>
                        <div className="flex gap-1 justify-end">
                          <OptimizedActionButtonGroup
                            entityType="students"
                            onEdit={() => router.push(`/admin/students/edit/${student.id}`)}
                            onDelete={() => handleDelete(student.id)}
                            onView={() => {
                              setSelectedStudent(student);
                              toast.info(`جاري تحميل تقرير تقدم الطالب: ${student.name}`);
                              setTimeout(() => {
                                router.push(`/admin/students/progress/${student.id}`);
                              }, 500);
                            }}
                            showEdit={true}
                            showDelete={true}
                            showView={true}
                            customPermissions={{
                              view: 'admin.evaluation.view'
                            }}
                            className="justify-end"
                          />



                          <PermissionGuard requiredPermission="admin.students.card.view">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                toast.info('جاري تحميل بطاقة التلميذ...');
                                router.push(`/admin/students/${student.id}/card`);
                              }}
                              className="text-green-600 hover:text-green-800 hover:bg-green-50"
                              title="عرض بطاقة التلميذ"
                            >
                              <FaUser className="h-4 w-4" />
                            </Button>
                          </PermissionGuard>

                          <PermissionGuard requiredPermission="admin.students.receipt.manage">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                toast.info('جاري تحميل وصل التسجيل...');
                                router.push(`/admin/students/${student.id}/receipt`);
                              }}
                              className="text-purple-600 hover:text-purple-800 hover:bg-purple-50"
                              title="وصل التسجيل"
                            >
                              <FaReceipt className="h-4 w-4" />
                            </Button>
                          </PermissionGuard>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* عرض البطاقات على الشاشات الصغيرة */}
          <div className="sm:hidden space-y-4">
            {filteredStudents.map((student) => (
              <div key={student.id} className="bg-white rounded-lg shadow-md p-4 border border-[#e0f2ef]">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-bold text-lg text-[var(--primary-color)]">{student.name}</h3>
                    <p className="text-sm text-gray-500">@{student.username}</p>
                  </div>
                  <div className="bg-[#e0f2ef] text-[var(--primary-color)] rounded-full px-2 py-1 text-sm font-medium">
                    {student.age} سنة
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">رقم الهاتف:</span>
                    <span className="text-sm font-medium">{student.phone || '-'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">القسم:</span>
                    <span className="text-sm font-medium">{student.classe?.name || '-'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">الولي:</span>
                    <span className="text-sm font-medium">{student.guardian?.name || '-'}</span>
                  </div>
                </div>

                <div className="pt-2 border-t border-gray-100">
                  <div className="flex flex-wrap gap-2 justify-center">
                    <OptimizedActionButtonGroup
                      entityType="students"
                      onEdit={() => router.push(`/admin/students/edit/${student.id}`)}
                      onDelete={() => handleDelete(student.id)}
                      onView={() => {
                        setSelectedStudent(student);
                        toast.info(`جاري تحميل تقرير تقدم الطالب: ${student.name}`);
                        setTimeout(() => {
                          router.push(`/admin/students/progress/${student.id}`);
                        }, 500);
                      }}
                      showEdit={true}
                      showDelete={true}
                      showView={true}
                      customPermissions={{
                        view: 'admin.evaluation.view'
                      }}
                      className="justify-center gap-1"
                    />



                    <PermissionGuard requiredPermission="admin.students.card.view">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          toast.info('جاري تحميل بطاقة التلميذ...');
                          router.push(`/admin/students/${student.id}/card`);
                        }}
                        className="text-green-600 hover:text-green-800 hover:bg-green-50"
                        title="عرض بطاقة التلميذ"
                      >
                        <FaUser className="h-4 w-4" />
                      </Button>
                    </PermissionGuard>

                    <PermissionGuard requiredPermission="admin.students.receipt.manage">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          toast.info('جاري تحميل وصل التسجيل...');
                          router.push(`/admin/students/${student.id}/receipt`);
                        }}
                        className="text-purple-600 hover:text-purple-800 hover:bg-purple-50"
                        title="وصل التسجيل"
                      >
                        <FaReceipt className="h-4 w-4" />
                      </Button>
                    </PermissionGuard>
                  </div>
                </div>
              </div>
            ))}

            {filteredStudents.length === 0 && (
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <p className="text-gray-500">لا توجد نتائج مطابقة للبحث</p>
              </div>
            )}
          </div>
        </>
      )}

      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-[var(--primary-color)] font-bold text-lg sm:text-xl flex items-center gap-2">
              <FaEdit className="text-[var(--primary-color)]" />
              تعديل بيانات التلميذ
            </DialogTitle>
            <DialogDescription className="text-sm">قم بتحديث بيانات التلميذ في النموذج أدناه</DialogDescription>
          </DialogHeader>
          <form id="editStudentForm" onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username" className="text-sm">اسم المستخدم</Label>
              <Input
                id="username"
                placeholder="اسم المستخدم"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                required
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                style={{ direction: 'rtl', textAlign: 'right' }}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="fullname" className="text-sm">الاسم الكامل</Label>
              <Input
                id="fullname"
                placeholder="الاسم الكامل"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                style={{ direction: 'rtl', textAlign: 'right' }}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="age" className="text-sm">العمر</Label>
              <Input
                id="age"
                type="number"
                placeholder="العمر"
                value={formData.age}
                onChange={(e) => setFormData({ ...formData, age: parseInt(e.target.value) })}
                required
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                style={{ direction: 'rtl', textAlign: 'right' }}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-sm">رقم الهاتف</Label>
              <Input
                id="phone"
                placeholder="رقم الهاتف"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                style={{ direction: 'rtl', textAlign: 'right' }}
              />
            </div>

            <div className="pt-2">
              <Button
                type="button"
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 justify-center w-full"
                onClick={(e) => {
                  e.preventDefault();
                  const form = document.getElementById('editStudentForm') as HTMLFormElement;
                  if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                }}
              >
                <FaSave className="ml-1" />
                حفظ التغييرات
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add Student Dialog */}
      <AddStudentDialog
        isOpen={isAddDialogOpen}
        onCloseAction={() => setIsAddDialogOpen(false)}
        onSuccessAction={() => {
          fetchStudents();
          setIsAddDialogOpen(false);
        }}
      />
    </div>
    </OptimizedProtectedRoute>
  );
}

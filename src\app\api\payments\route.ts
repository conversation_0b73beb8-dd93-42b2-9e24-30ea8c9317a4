import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { PaymentStatus, Prisma, DiscountType } from '@prisma/client';
import { ActivityLogger, ActivityType } from '@/lib/activity-logger';
// import { getServerSession } from 'next-auth';

// تعريف واجهة لطريقة الدفع المتعددة
interface PaymentMethodItem {
  paymentMethodId: number;
  amount: number;
  transactionId?: string;
  cardDetails?: Prisma.JsonValue | null;
}

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const status = searchParams.get('status') || '';
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = 10;

    // بناء شروط البحث
    // ملاحظة: تم إزالة الحقل payment_id لأنه غير موجود في نموذج Payment

    // Get all students
    const students = await prisma.student.findMany({
      where: query ? {
        name: { contains: query }
      } : {},
      orderBy: { name: 'asc' }
    });

    // Get payments for the selected month
    const monthStart = new Date(parseInt(year || new Date().getFullYear().toString()), parseInt(month || '1') - 1, 1);
    const monthEnd = new Date(parseInt(year || new Date().getFullYear().toString()), parseInt(month || '1'), 0);

    const payments = await prisma.payment.findMany({
      where: {
        AND: [
          query ? {
            OR: [
              { student: { name: { contains: query} } }
            ]
          } : {},
          {
            date: {
              gte: monthStart,
              lte: monthEnd
            }
          }
        ]
      },
      include: {
        student: true,
        paymentMethod: true,
        paymentMethods: {
          include: {
            paymentMethod: true
          }
        }
      },
      orderBy: { date: 'desc' }
    });

    // إنشاء خريطة مدفوعات الطلاب
    const studentPayments = students.map(student => {
      const payment = payments.find(p => p.studentId === student.id);
      if (payment) {
        return {
          ...payment,
          student
        };
      }
      return {
        id: `unpaid-${student.id}`,
        studentId: student.id,
        amount: 0,
        date: monthStart,
        status: 'PENDING' as PaymentStatus,
        student,
        createdAt: new Date()
      };
    });

    // Filter payments based on status if specified
    const filteredPayments = status !== 'all' && status
      ? studentPayments.filter(p => p.status === status)
      : studentPayments;

    // Apply pagination
    const start = (page - 1) * limit;
    const paginatedPayments = filteredPayments.slice(start, start + limit);

    return NextResponse.json({
      payments: paginatedPayments,
      pagination: {
        total: filteredPayments.length,
        pages: Math.ceil(filteredPayments.length / limit),
        current: page
      }
    });
  } catch (error) {
    console.error('خطأ في جلب المدفوعات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المدفوعات' },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const {
      studentId,
      amount,
      month,
      year,
      paymentMethodId,
      transactionId,
      notes,
      receiptNumber,
      receiptImage,
      invoiceId,
      discountId,
      originalAmount,
      multiplePaymentMethods
    } = body;

    if (!studentId || !amount || !month || !year) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // الحصول على معلومات الطالب
    const student = await prisma.student.findUnique({
      where: { id: studentId }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'الطالب غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من طريقة الدفع إذا تم تحديدها (في حالة طريقة دفع واحدة)
    if (paymentMethodId && !multiplePaymentMethods) {
      const paymentMethod = await prisma.paymentMethod.findUnique({
        where: { id: paymentMethodId }
      });

      if (!paymentMethod) {
        return NextResponse.json(
          { error: 'طريقة الدفع غير موجودة' },
          { status: 404 }
        );
      }

      if (!paymentMethod.isActive) {
        return NextResponse.json(
          { error: 'طريقة الدفع غير نشطة' },
          { status: 400 }
        );
      }
    }

    // التحقق من طرق الدفع المتعددة إذا تم تحديدها
    if (multiplePaymentMethods && body.multiplePaymentMethods) {
      // التحقق من وجود طرق دفع متعددة
      if (!Array.isArray(body.multiplePaymentMethods) || body.multiplePaymentMethods.length === 0) {
        return NextResponse.json(
          { error: 'يجب تحديد طريقة دفع واحدة على الأقل' },
          { status: 400 }
        );
      }

      // التحقق من صحة مجموع المبالغ
      const totalAmount = body.multiplePaymentMethods.reduce((sum: number, method: PaymentMethodItem) => sum + (method.amount || 0), 0);
      if (Math.abs(totalAmount - amount) > 0.01) {
        return NextResponse.json(
          { error: `مجموع المبالغ (${totalAmount.toFixed(2)}) لا يساوي المبلغ الإجمالي (${amount.toFixed(2)})` },
          { status: 400 }
        );
      }

      // التحقق من وجود وصلاحية كل طريقة دفع
      for (const method of body.multiplePaymentMethods as PaymentMethodItem[]) {
        if (!method.paymentMethodId) {
          return NextResponse.json(
            { error: 'معرف طريقة الدفع مطلوب لكل طريقة دفع' },
            { status: 400 }
          );
        }

        if (!method.amount || typeof method.amount !== 'number' || method.amount <= 0) {
          return NextResponse.json(
            { error: 'قيمة المبلغ غير صحيحة لإحدى طرق الدفع' },
            { status: 400 }
          );
        }

        const paymentMethod = await prisma.paymentMethod.findUnique({
          where: { id: method.paymentMethodId }
        });

        if (!paymentMethod) {
          return NextResponse.json(
            { error: `طريقة الدفع رقم ${method.paymentMethodId} غير موجودة` },
            { status: 404 }
          );
        }

        if (!paymentMethod.isActive) {
          return NextResponse.json(
            { error: `طريقة الدفع ${paymentMethod.name} غير نشطة` },
            { status: 400 }
          );
        }
      }
    }

    // التحقق من الخصم إذا تم تحديده
    if (discountId) {
      const discount = await prisma.discount.findUnique({
        where: { id: discountId }
      });

      if (!discount) {
        return NextResponse.json(
          { error: 'الخصم غير موجود' },
          { status: 404 }
        );
      }

      if (!discount.isActive) {
        return NextResponse.json(
          { error: 'الخصم غير نشط' },
          { status: 400 }
        );
      }

      // التحقق من صحة المبلغ الأصلي والمبلغ بعد الخصم
      if (originalAmount) {
        if (typeof originalAmount !== 'number' || originalAmount <= 0) {
          return NextResponse.json(
            { error: 'قيمة المبلغ الأصلي غير صحيحة' },
            { status: 400 }
          );
        }

        // التحقق من أن المبلغ بعد الخصم أقل من أو يساوي المبلغ الأصلي
        if (amount > originalAmount) {
          return NextResponse.json(
            { error: 'المبلغ بعد الخصم يجب أن يكون أقل من أو يساوي المبلغ الأصلي' },
            { status: 400 }
          );
        }

        // التحقق من صحة قيمة الخصم
        const expectedAmount = discount.type === DiscountType.PERCENTAGE
          ? originalAmount - (originalAmount * (discount.value / 100))
          : originalAmount - discount.value;

        // السماح بهامش خطأ صغير بسبب الفاصلة العشرية
        const tolerance = 0.01;
        if (Math.abs(expectedAmount - amount) > tolerance) {
          return NextResponse.json(
            { error: 'قيمة الخصم غير متطابقة مع المبلغ المحسوب' },
            { status: 400 }
          );
        }
      } else if (discountId) {
        return NextResponse.json(
          { error: 'المبلغ الأصلي مطلوب عند تطبيق الخصم' },
          { status: 400 }
        );
      }
    }

    // التحقق من الفاتورة إذا تم تحديدها
    if (invoiceId) {
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId }
      });

      if (!invoice) {
        return NextResponse.json(
          { error: 'الفاتورة غير موجودة' },
          { status: 404 }
        );
      }

      if (invoice.studentId !== studentId) {
        return NextResponse.json(
          { error: 'الفاتورة لا تنتمي لهذا الطالب' },
          { status: 400 }
        );
      }
    }

    // الحصول على الخزينة الافتراضية (نفترض أن هناك خزينة واحدة فقط)
    let treasury = await prisma.treasury.findFirst();

    // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 0,
          totalIncome: 0,
          totalExpense: 0,
        },
      });
    }

    // إنشاء الدفعة وتسجيل المدخول في الخزينة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء الدفعة
      const payment = await tx.payment.create({
        data: {
          studentId,
          amount,
          status: 'PAID',
          date: new Date(`${year}-${month.toString().padStart(2, '0')}-01T00:00:00.000Z`),
          ...(paymentMethodId && !multiplePaymentMethods && { paymentMethodId }),
          ...(body.isDirectPayment && { paymentMethodName: 'مباشر' }), // إضافة اسم طريقة الدفع "مباشر" إذا كانت دفعة مباشرة
          ...(transactionId && !multiplePaymentMethods && { transactionId }),
          ...(notes && { notes }),
          ...(receiptNumber && { receiptNumber }),
          ...(receiptImage && { receiptImage }),
          ...(invoiceId && { invoiceId }),
          ...(discountId && {
            discountId,
            originalAmount
          })
        },
        include: {
          student: true,
          paymentMethod: true,
          invoice: true
        }
      });

      // إضافة طرق الدفع المتعددة إذا تم تحديدها
      if (multiplePaymentMethods && body.multiplePaymentMethods && Array.isArray(body.multiplePaymentMethods)) {
        for (const method of body.multiplePaymentMethods as PaymentMethodItem[]) {
          await tx.paymentMethodsOnPayment.create({
            data: {
              paymentId: payment.id,
              paymentMethodId: method.paymentMethodId,
              amount: method.amount,
              transactionId: method.transactionId || null,
              cardDetails: method.cardDetails ? method.cardDetails : Prisma.JsonNull
            }
          });
        }
      }

      // تحديث رصيد الخزينة وإجمالي الدخل
      await tx.treasury.update({
        where: { id: treasury!.id },
        data: {
          balance: { increment: amount },
          totalIncome: { increment: amount },
        },
      });

      // إنشاء سجل دخل للخزينة
      await tx.income.create({
        data: {
          treasuryId: treasury!.id,
          source: `رسوم دراسية للطالب ${student.name} - شهر ${month}/${year}${payment.paymentMethod ? ` (طريقة دفع: ${payment.paymentMethod.name})` : ''}`,
          amount,
        },
      });

      // تحديث حالة الفاتورة إذا كانت مرتبطة بالدفعة
      if (invoiceId) {
        const invoice = await tx.invoice.findUnique({
          where: { id: invoiceId },
          include: { payments: true }
        });

        if (invoice) {
          // حساب إجمالي المدفوعات للفاتورة (بما في ذلك الدفعة الحالية)
          const totalPaid = invoice.payments.reduce((sum, p) => {
            if (p.status === 'PAID' && p.id !== payment.id) {
              return sum + p.amount;
            }
            return sum;
          }, payment.amount);

          // تحديث حالة الفاتورة بناءً على المبلغ المدفوع
          let newStatus = invoice.status;

          if (totalPaid >= invoice.amount) {
            newStatus = 'PAID';
          } else if (totalPaid > 0) {
            newStatus = 'PARTIALLY_PAID';
          }

          // تحديث الفاتورة فقط إذا تغيرت الحالة
          if (newStatus !== invoice.status) {
            await tx.invoice.update({
              where: { id: invoiceId },
              data: { status: newStatus }
            });
          }
        }
      }

      // تسجيل نشاط الدفع
      try {
        // محاولة الحصول على معرف المستخدم الحالي
        const adminUser = await prisma.user.findFirst({
          where: { role: 'ADMIN' }
        });

        if (adminUser) {
          let activityDescription = `تسجيل دفعة بقيمة ${amount} للطالب ${student.name}`;

          // إضافة معلومات الخصم إلى وصف النشاط إذا تم تطبيق خصم
          if (discountId && originalAmount) {
            activityDescription += ` (مع خصم: ${originalAmount} → ${amount})`;
          }

          // إضافة معلومات طرق الدفع المتعددة إلى وصف النشاط
          if (multiplePaymentMethods && body.multiplePaymentMethods && Array.isArray(body.multiplePaymentMethods)) {
            activityDescription += ` (طرق دفع متعددة: ${(body.multiplePaymentMethods as PaymentMethodItem[]).length})`;
          }

          await ActivityLogger.log(
            adminUser.id,
            ActivityType.PAYMENT,
            activityDescription
          );
        }
      } catch (error) {
        console.error('خطأ في تسجيل نشاط الدفع:', error);
        // لا نريد أن يفشل تسجيل الدفعة إذا فشل تسجيل النشاط
      }

      // إعادة جلب الدفعة مع طرق الدفع المتعددة
      const updatedPayment = await tx.payment.findUnique({
        where: { id: payment.id },
        include: {
          student: true,
          paymentMethod: true,
          invoice: true,
          paymentMethods: {
            include: {
              paymentMethod: true
            }
          }
        }
      });

      return updatedPayment;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('خطأ في إنشاء الدفعة:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الدفعة' },
      { status: 500 }
    );
  }
}

export async function PATCH(req: Request) {
  try {
    const body = await req.json();
    const { id, status, amount } = body;

    if (!id || !status) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    if (!Object.values(PaymentStatus).includes(status)) {
      return NextResponse.json(
        { error: 'حالة الدفع غير صحيحة' },
        { status: 400 }
      );
    }

    // الحصول على الخزينة الافتراضية
    let treasury = await prisma.treasury.findFirst();

    // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 0,
          totalIncome: 0,
          totalExpense: 0,
        },
      });
    }

    // معالجة الطلاب غير المدفوعين (المعرفات التي تبدأ بـ 'unpaid-')
    if (id.startsWith('unpaid-')) {
      const studentId = parseInt(id.replace('unpaid-', ''));
      const currentDate = new Date();
      const paymentAmount = amount || 0;

      // الحصول على معلومات الطالب
      const student = await prisma.student.findUnique({
        where: { id: studentId }
      });

      if (!student) {
        return NextResponse.json(
          { error: 'الطالب غير موجود' },
          { status: 404 }
        );
      }

      // إنشاء الدفعة وتسجيل المدخول في الخزينة إذا كانت الحالة PAID
      const result = await prisma.$transaction(async (tx) => {
        // إنشاء الدفعة
        const payment = await tx.payment.create({
          data: {
            studentId,
            status: status as PaymentStatus,
            amount: paymentAmount,
            date: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
          },
          include: { student: true }
        });

        // إذا كانت الحالة مدفوعة والمبلغ أكبر من صفر، قم بتسجيل مدخول
        if (status === 'PAID' && paymentAmount > 0) {
          // تحديث رصيد الخزينة وإجمالي الدخل
          await tx.treasury.update({
            where: { id: treasury!.id },
            data: {
              balance: { increment: paymentAmount },
              totalIncome: { increment: paymentAmount },
            },
          });

          // إنشاء سجل دخل للخزينة
          await tx.income.create({
            data: {
              treasuryId: treasury!.id,
              source: `رسوم دراسية للطالب ${student.name} - ${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`,
              amount: paymentAmount,
            },
          });
        }

        return payment;
      });

      return NextResponse.json(result);
    }

    // معالجة المدفوعات الموجودة
    const existingPayment = await prisma.payment.findUnique({
      where: { id: parseInt(id) },
      include: { student: true }
    });

    if (!existingPayment) {
      return NextResponse.json(
        { error: 'الدفعة غير موجودة' },
        { status: 404 }
      );
    }

    // إذا كانت الحالة السابقة غير مدفوعة والحالة الجديدة مدفوعة
    if (existingPayment.status !== 'PAID' && status === 'PAID') {
      // تحديث الدفعة وتسجيل المدخول في الخزينة
      const result = await prisma.$transaction(async (tx) => {
        // تحديث الدفعة
        const payment = await tx.payment.update({
          where: { id: parseInt(id) },
          data: { status: status as PaymentStatus },
          include: { student: true }
        });

        // تحديث رصيد الخزينة وإجمالي الدخل
        await tx.treasury.update({
          where: { id: treasury!.id },
          data: {
            balance: { increment: existingPayment.amount },
            totalIncome: { increment: existingPayment.amount },
          },
        });

        // إنشاء سجل دخل للخزينة
        const paymentDate = new Date(existingPayment.date);
        await tx.income.create({
          data: {
            treasuryId: treasury!.id,
            source: `رسوم دراسية للطالب ${existingPayment.student.name} - ${paymentDate.getMonth() + 1}/${paymentDate.getFullYear()}`,
            amount: existingPayment.amount,
          },
        });

        return payment;
      });

      return NextResponse.json(result);
    }
    // إذا كانت الحالة السابقة مدفوعة والحالة الجديدة غير مدفوعة
    else if (existingPayment.status === 'PAID' && status !== 'PAID') {
      // تحديث الدفعة وتسجيل مصروف في الخزينة (إلغاء الدفعة)
      const result = await prisma.$transaction(async (tx) => {
        // تحديث الدفعة
        const payment = await tx.payment.update({
          where: { id: parseInt(id) },
          data: { status: status as PaymentStatus },
          include: { student: true }
        });

        // تحديث رصيد الخزينة وإجمالي الدخل
        await tx.treasury.update({
          where: { id: treasury!.id },
          data: {
            balance: { decrement: existingPayment.amount },
            totalIncome: { decrement: existingPayment.amount },
          },
        });

        // إنشاء سجل مصروف للخزينة (إلغاء الدفعة)
        const paymentDate = new Date(existingPayment.date);
        await tx.expense.create({
          data: {
            treasuryId: treasury!.id,
            purpose: `إلغاء دفعة للطالب ${existingPayment.student.name} - ${paymentDate.getMonth() + 1}/${paymentDate.getFullYear()}`,
            amount: existingPayment.amount,
          },
        });

        return payment;
      });

      return NextResponse.json(result);
    }
    // إذا لم تتغير الحالة بشكل يؤثر على الخزينة
    else {
      // تحديث الدفعة فقط
      const payment = await prisma.payment.update({
        where: { id: parseInt(id) },
        data: { status: status as PaymentStatus },
        include: { student: true }
      });

      return NextResponse.json(payment);
    }
  } catch (error) {
    console.error('خطأ في تحديث الدفعة:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الدفعة' },
      { status: 500 }
    );
  }
}
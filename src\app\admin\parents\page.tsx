'use client'

import { useState, useEffect, useReducer, useCallback } from 'react'
import { useDebouncedCallback } from 'use-debounce'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
// استخدام عناصر HTML مباشرة بدلاً من مكونات الجدول
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { exportToExcel } from '@/utils/export-utils'
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'

import {
  FaUserFriends, FaSearch, FaEdit, FaTrash, FaSync, FaUserPlus, FaSave,
  FaComments, FaFileExcel, FaFileExport,
  FaLink, FaUnlink, FaBell
} from 'react-icons/fa'

interface Parent {
  id: string
  name: string
  phone: string
  email?: string
  address?: string
  amountPerStudent?: number // المبلغ الذي يجب دفعه لكل تلميذ
  students: { id: string; name: string; grade: string }[]
  createdAt: string
  updatedAt?: string
  totalDebt: number // إجمالي الديون المتبقية (المطلوب - المدفوع)
  totalRequired: number // إجمالي المبلغ المطلوب
  totalPaid: number // إجمالي المدفوعات
  dueInvoices: number // عدد الفواتير المستحقة
  lastPaymentDate?: string | null // تاريخ آخر دفعة
  user?: {
    id: number
    username: string
    profile: {
      name: string
      phone: string | null
    }
  } | null
}

interface Communication {
  id: number
  parentId: number
  userId: number
  type: string
  title: string
  content: string
  date: string
  status: string
  response?: string
  responseDate?: string
  parent: {
    name: string
    phone: string
    email?: string
  }
  user: {
    username: string
    role: string
    profile: {
      name: string
    }
  }
}

interface Student {
  id: string
  name: string
  grade?: string
  classe?: {
    name: string
  }
}

export default function ParentsPage() {
  const [parents, setParents] = useState<Parent[]>([])
  const [communications, setCommunications] = useState<Communication[]>([])
  const [availableStudents, setAvailableStudents] = useState<Student[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  // تحسين البحث مع debouncing
  const debouncedSearch = useDebouncedCallback((query: string) => {
    setSearchQuery(query);
  }, 300);
  // استخدام المتغير activeTab لتحديد أي زر تصدير يجب عرضه
  const [activeTab, setActiveTab] = useState('parents')

  // Dialogs state
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isStudentLinkDialogOpen, setIsStudentLinkDialogOpen] = useState(false)
  const [isCommunicationDialogOpen, setIsCommunicationDialogOpen] = useState(false)
  const [isNotificationDialogOpen, setIsNotificationDialogOpen] = useState(false)

  // Selected items
  const [selectedParent, setSelectedParent] = useState<Parent | null>(null)
  const [selectedCommunication, setSelectedCommunication] = useState<Communication | null>(null)

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    amountPerStudent: '',
    username: '',
    password: ''
  })

  // Communication form data
  const [communicationData, setCommunicationData] = useState({
    type: 'PHONE_CALL',
    title: '',
    content: '',
    status: 'PENDING',
    response: '',
    sendNotification: false
  })

  // Notification form data
  const [notificationData, setNotificationData] = useState({
    title: '',
    content: '',
    type: 'GENERAL'
  })

  // Student link form data
  const [studentLinkData, setStudentLinkData] = useState({
    studentId: ''
  })

  // جلب بيانات الأولياء مع معالجة أخطاء محسنة
  const fetchParents = useCallback(async () => {
    try {
      console.log('📡 بدء جلب بيانات الأولياء...');
      const response = await fetch('/api/parents')

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // التحقق من صحة البيانات
      if (!Array.isArray(data)) {
        throw new Error('تنسيق البيانات المستلمة غير صحيح')
      }

      setParents(data)
      console.log('✅ تم جلب بيانات الأولياء بنجاح:', data.length, 'ولي')

    } catch (error) {
      console.error('❌ خطأ في جلب بيانات الأولياء:', error)
      const errorMessage = error instanceof Error ? error.message : 'فشل في جلب بيانات الأولياء'

      // عدم إظهار toast للتحديث التلقائي لتجنب الإزعاج
      if (!error.message?.includes('تحديث')) {
        toast({
          title: 'خطأ',
          description: errorMessage,
          variant: 'destructive'
        })
      }
    }
  }, [])

  useEffect(() => {
    fetchParents()
    fetchAvailableStudents()
  }, [fetchParents])

  // تحديث تلقائي للبيانات عند التركيز على الصفحة
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleFocus = () => {
      // تأخير بسيط لتجنب التحديث المتكرر
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        console.log('🔄 تحديث تلقائي لبيانات الأولياء عند التركيز على الصفحة...');
        fetchParents();
      }, 500);
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // تأخير بسيط لتجنب التحديث المتكرر
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          console.log('🔄 تحديث تلقائي لبيانات الأولياء عند العودة للصفحة...');
          fetchParents();
        }, 500);
      }
    };

    // إضافة مستمعات الأحداث
    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // تنظيف المستمعات عند إلغاء تحميل المكون
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [fetchParents])

  // تحديث دوري للبيانات كل دقيقة (فقط عندما تكون الصفحة مرئية)
  useEffect(() => {
    const interval = setInterval(() => {
      // تحديث فقط إذا كانت الصفحة مرئية
      if (!document.hidden) {
        console.log('🔄 تحديث دوري لبيانات الأولياء...');
        fetchParents();
      }
    }, 60000); // كل دقيقة

    return () => clearInterval(interval);
  }, [fetchParents])

  // جلب بيانات الطلاب المتاحين للربط
  const fetchAvailableStudents = async () => {
    try {
      const response = await fetch('/api/students?unassigned=true')
      const data = await response.json()
      setAvailableStudents(data)
    } catch {
      toast({
        title: 'خطأ',
        description: 'فشل في جلب بيانات الطلاب',
        variant: 'destructive'
      })
    }
  }

  // جلب سجلات التواصل مع الأولياء
  const fetchCommunications = async (parentId?: string) => {
    try {
      const url = parentId
        ? `/api/parent-communications?parentId=${parentId}`
        : '/api/parent-communications'

      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // التحقق من وجود البيانات وتعيين قيمة افتراضية إذا لم تكن موجودة
      setCommunications(data.data || [])
    } catch (error) {
      console.error('Error fetching communications:', error)
      setCommunications([]) // تعيين مصفوفة فارغة في حالة الخطأ
      toast({
        title: 'خطأ',
        description: 'فشل في جلب سجلات التواصل',
        variant: 'destructive'
      })
    }
  }

  // إضافة ولي جديد
  const handleAdd = async () => {
    try {
      const response = await fetch('/api/parents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (!response.ok) throw new Error('Failed to add parent')

      await fetchParents()
      setIsAddDialogOpen(false)
      setFormData({
        name: '',
        phone: '',
        email: '',
        address: '',
        amountPerStudent: '',
        username: '',
        password: ''
      })
      toast({
        title: 'تم بنجاح',
        description: 'تم إضافة الولي بنجاح'
      })
    } catch {
      toast({
        title: 'خطأ',
        description: 'فشل في إضافة الولي',
        variant: 'destructive'
      })
    }
  }

  // ربط طالب بولي
  const handleLinkStudent = async () => {
    if (!selectedParent || !studentLinkData.studentId) return

    try {
      const response = await fetch('/api/students/link-parent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          studentId: studentLinkData.studentId,
          parentId: selectedParent.id
        })
      })

      if (!response.ok) throw new Error('Failed to link student to parent')

      await fetchParents()
      setIsStudentLinkDialogOpen(false)
      setStudentLinkData({ studentId: '' })
      toast({
        title: 'تم بنجاح',
        description: 'تم ربط الطالب بالولي بنجاح'
      })
    } catch {
      toast({
        title: 'خطأ',
        description: 'فشل في ربط الطالب بالولي',
        variant: 'destructive'
      })
    }
  }

  // إلغاء ربط طالب بولي
  const handleUnlinkStudent = async (studentId: string) => {
    try {
      const response = await fetch('/api/students/unlink-parent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ studentId })
      })

      if (!response.ok) throw new Error('Failed to unlink student from parent')

      await fetchParents()
      toast({
        title: 'تم بنجاح',
        description: 'تم إلغاء ربط الطالب بالولي بنجاح'
      })
    } catch {
      toast({
        title: 'خطأ',
        description: 'فشل في إلغاء ربط الطالب بالولي',
        variant: 'destructive'
      })
    }
  }

  // إضافة سجل تواصل جديد
  const handleAddCommunication = async () => {
    if (!selectedParent) return

    try {
      const response = await fetch('/api/parent-communications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...communicationData,
          parentId: parseInt(selectedParent.id)
        })
      })

      if (!response.ok) throw new Error('Failed to add communication')

      await fetchCommunications(selectedParent.id)
      setIsCommunicationDialogOpen(false)
      setCommunicationData({
        type: 'PHONE_CALL',
        title: '',
        content: '',
        status: 'PENDING',
        response: '',
        sendNotification: false
      })
      toast({
        title: 'تم بنجاح',
        description: 'تم إضافة سجل التواصل بنجاح'
      })
    } catch {
      toast({
        title: 'خطأ',
        description: 'فشل في إضافة سجل التواصل',
        variant: 'destructive'
      })
    }
  }

  // إرسال تنبيه للولي
  const handleSendNotification = async () => {
    if (!selectedParent || !selectedParent.user) {
      toast({
        title: 'خطأ',
        description: 'الولي غير مرتبط بحساب مستخدم',
        variant: 'destructive'
      })
      return
    }

    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...notificationData,
          userId: selectedParent.user.id
        })
      })

      if (!response.ok) throw new Error('Failed to send notification')

      setIsNotificationDialogOpen(false)
      setNotificationData({
        title: '',
        content: '',
        type: 'GENERAL'
      })
      toast({
        title: 'تم بنجاح',
        description: 'تم إرسال التنبيه بنجاح'
      })
    } catch {
      toast({
        title: 'خطأ',
        description: 'فشل في إرسال التنبيه',
        variant: 'destructive'
      })
    }
  }

  // تعديل بيانات الولي
  const handleEdit = async () => {
    if (!selectedParent) return

    try {
      // إعداد البيانات للإرسال
      const requestData: {
        id: string;
        name: string;
        phone: string;
        email?: string;
        address?: string;
        amountPerStudent?: number;
        username?: string;
        password?: string;
      } = {
        id: selectedParent.id,
        name: formData.name,
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        amountPerStudent: formData.amountPerStudent ? parseFloat(formData.amountPerStudent) : undefined
      }

      // إذا كانت كلمة المرور غير فارغة أو تغير اسم المستخدم
      // نضيف هذه الحقول للطلب
      if (formData.password || formData.username !== selectedParent.user?.username) {
        requestData.username = formData.username;
        requestData.password = formData.password;
      }

      const response = await fetch('/api/parents', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) throw new Error('Failed to update parent')

      await fetchParents()
      setIsEditDialogOpen(false)
      setSelectedParent(null)
      setFormData({
        name: '',
        phone: '',
        email: '',
        address: '',
        amountPerStudent: '',
        username: '',
        password: ''
      })
      toast({
        title: 'تم بنجاح',
        description: 'تم تحديث بيانات الولي بنجاح'
      })
    } catch (error) {
      console.error('Error updating parent:', error)
      toast({
        title: 'خطأ',
        description: 'فشل في تحديث بيانات الولي',
        variant: 'destructive'
      })
    }
  }

  const handleDelete = async () => {
    if (!selectedParent) return

    try {
      const response = await fetch(`/api/parents?id=${selectedParent.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete parent')

      await fetchParents()
      setIsDeleteDialogOpen(false)
      setSelectedParent(null)
      toast({
        title: 'تم بنجاح',
        description: 'تم حذف الولي بنجاح'
      })
    } catch {
      toast({
        title: 'خطأ',
        description: 'فشل في حذف الولي',
        variant: 'destructive'
      })
    }
  }

  const filteredParents = parents.filter(parent =>
    parent.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // تصدير بيانات الأولياء إلى ملف Excel
  const handleExportParents = () => {
    // تحويل البيانات إلى الشكل المطلوب للتصدير
    const dataToExport = filteredParents.map((parent, index) => ({
      'الرقم': index + 1,
      'اسم الولي': parent.name,
      'رقم الهاتف': parent.phone,
      'البريد الإلكتروني': parent.email || '',
      'العنوان': parent.address || '',
      'المبلغ لكل تلميذ (د.ج)': parent.amountPerStudent || 0,
      'اسم المستخدم': parent.user?.username || 'غير مسجل',
      'عدد التلاميذ': parent.students.length,
      'ديون الولي (د.ج)': parent.totalDebt || 0,
      'عدد الفواتير المستحقة': parent.dueInvoices || 0,
      'إجمالي المدفوعات (د.ج)': parent.totalPaid || 0,
      'آخر دفعة': parent.lastPaymentDate ? new Date(parent.lastPaymentDate).toLocaleDateString('fr-FR') : 'لا يوجد',
      'تاريخ الإضافة': new Date(parent.createdAt).toLocaleDateString('fr-FR')
    }));

    // تحديد عرض الأعمدة
    const columnWidths = [
      { wch: 5 },  // الرقم
      { wch: 20 }, // اسم الولي
      { wch: 15 }, // رقم الهاتف
      { wch: 25 }, // البريد الإلكتروني
      { wch: 30 }, // العنوان
      { wch: 18 }, // المبلغ لكل تلميذ
      { wch: 15 }, // اسم المستخدم
      { wch: 10 }, // عدد التلاميذ
      { wch: 15 }, // ديون الولي
      { wch: 15 }, // عدد الفواتير المستحقة
      { wch: 18 }, // إجمالي المدفوعات
      { wch: 15 }, // آخر دفعة
      { wch: 15 }  // تاريخ الإضافة
    ];

    // تصدير البيانات
    exportToExcel(
      dataToExport,
      'قائمة_الأولياء_مع_الديون.xlsx',
      'الأولياء',
      columnWidths
    );
  }

  // تصدير بيانات سجل التواصل إلى ملف Excel
  const handleExportCommunications = () => {
    // التحقق من وجود بيانات للتصدير
    if (!communications || communications.length === 0) {
      toast({
        title: 'تنبيه',
        description: 'لا توجد بيانات للتصدير',
        variant: 'destructive'
      })
      return
    }

    // تحويل البيانات إلى الشكل المطلوب للتصدير
    const dataToExport = communications.map((comm, index) => ({
      'الرقم': index + 1,
      'اسم الولي': comm.parent.name,
      'رقم الهاتف': comm.parent.phone,
      'نوع التواصل': comm.type === 'PHONE_CALL' ? 'مكالمة هاتفية' :
                     comm.type === 'MEETING' ? 'اجتماع' :
                     comm.type === 'EMAIL' ? 'بريد إلكتروني' :
                     comm.type === 'SMS' ? 'رسالة نصية' :
                     comm.type === 'WHATSAPP' ? 'واتساب' : 'أخرى',
      'العنوان': comm.title,
      'المحتوى': comm.content,
      'التاريخ': new Date(comm.date).toLocaleDateString('fr-FR'),
      'الحالة': comm.status === 'PENDING' ? 'قيد الانتظار' :
                comm.status === 'COMPLETED' ? 'مكتمل' :
                comm.status === 'FOLLOW_UP' ? 'متابعة' : 'ملغي',
      'المستخدم': comm.user.profile.name,
      'الرد': comm.response || ''
    }));

    // تحديد عرض الأعمدة
    const columnWidths = [
      { wch: 5 },  // الرقم
      { wch: 20 }, // اسم الولي
      { wch: 15 }, // رقم الهاتف
      { wch: 15 }, // نوع التواصل
      { wch: 25 }, // العنوان
      { wch: 40 }, // المحتوى
      { wch: 15 }, // التاريخ
      { wch: 15 }, // الحالة
      { wch: 20 }, // المستخدم
      { wch: 40 }  // الرد
    ];

    // تصدير البيانات
    exportToExcel(
      dataToExport,
      'سجل_التواصل_مع_الأولياء.xlsx',
      'سجل التواصل',
      columnWidths
    );
  }

  return (
    <OptimizedProtectedRoute requiredPermission="admin.parents.view">
      <div className="p-4 sm:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-4 sm:space-y-6" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-3 mb-4 sm:mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaUserFriends className="text-[var(--primary-color)]" />
          إدارة الأولياء
        </h1>
        <div className="flex gap-2 w-full sm:w-auto justify-end">
          <QuickActionButtons
            entityType="parents"
            actions={[
              {
                key: 'create',
                label: 'إضافة ولي جديد',
                icon: <FaUserPlus />,
                onClick: () => {
                  setFormData({
                    name: '',
                    phone: '',
                    email: '',
                    address: '',
                    amountPerStudent: '',
                    username: '',
                    password: ''
                  });
                  setIsAddDialogOpen(true);
                },
                variant: 'primary'
              },
              {
                key: 'refresh',
                label: 'تحديث البيانات',
                icon: <FaSync />,
                onClick: () => {
                  console.log('🔄 تحديث يدوي لبيانات الأولياء...');
                  fetchParents();
                },
                variant: 'secondary'
              }
            ]}
            className="w-full sm:w-auto"
          />
          <Button
            onClick={fetchParents}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2 min-w-[40px]"
            title="تحديث البيانات"
          >
            <FaSync className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="flex items-center bg-white p-3 sm:p-4 rounded-lg shadow-md mb-4 sm:mb-6">
        <div className="relative w-full">
          <Input
            type="text"
            placeholder="البحث عن ولي..."
            value={searchQuery}
            onChange={(e) => debouncedSearch(e.target.value)}
            className="pl-10 border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--primary-color)]" />
        </div>
      </div>

      <Tabs defaultValue="parents" className="w-full" onValueChange={setActiveTab}>
        <div className="flex flex-col sm:flex-row justify-between items-center gap-3 mb-4">
          <TabsList className="bg-[#e0f2ef] p-1 w-full sm:w-auto mb-3 sm:mb-0">
            <TabsTrigger
              value="parents"
              className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white flex-1 sm:flex-none text-sm"
            >
              قائمة الأولياء
            </TabsTrigger>
            <TabsTrigger
              value="communications"
              className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white flex-1 sm:flex-none text-sm"
              onClick={() => fetchCommunications()}
            >
              سجل التواصل
            </TabsTrigger>
          </TabsList>

          {/* أزرار التصدير - عرض الأزرار المناسبة بناءً على التبويب النشط */}
          <div className="flex gap-2 w-full sm:w-auto">
            <QuickActionButtons
              entityType="reports"
              actions={[
                {
                  key: 'export',
                  label: activeTab === 'parents' ? 'تصدير قائمة الأولياء' : 'تصدير سجل التواصل',
                  icon: activeTab === 'parents' ? <FaFileExcel /> : <FaFileExport />,
                  onClick: activeTab === 'parents' ? handleExportParents : handleExportCommunications,
                  variant: 'success',
                  permission: 'admin.reports.view'
                }
              ]}
              className="w-full sm:w-auto"
            />
          </div>
        </div>


        <TabsContent value="parents" className="mt-0">
          {/* عرض الجدول على الشاشات المتوسطة والكبيرة */}
          <div className="hidden sm:block bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
            <div className="overflow-auto" style={{ maxWidth: '100%' }}>
              <table className="min-w-full border-collapse" dir="ltr">
                <thead>
                  <tr className="bg-[var(--primary-color)]">
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">الإجراءات</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef] hidden lg:table-cell">تاريخ الإضافة</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">ديون الولي</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef] hidden lg:table-cell">المبلغ لكل تلميذ</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">عدد التلاميذ</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">اسم المستخدم</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">البريد الإلكتروني</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">رقم الهاتف</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">اسم الولي</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">الرقم</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredParents.map((parent, index) => (
                    <tr key={parent.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                      <td className="p-2 whitespace-nowrap border border-[#e0f2ef]">
                        <div className="flex gap-1 justify-start">
                          <OptimizedActionButtonGroup
                            entityType="parents"
                            onEdit={() => {
                              setSelectedParent(parent)
                              setFormData({
                                name: parent.name,
                                phone: parent.phone,
                                email: parent.email || '',
                                address: parent.address || '',
                                amountPerStudent: parent.amountPerStudent?.toString() || '',
                                username: parent.user?.username || '',
                                password: ''
                              })
                              setIsEditDialogOpen(true)
                            }}
                            onDelete={() => {
                              setSelectedParent(parent)
                              setIsDeleteDialogOpen(true)
                            }}
                            showEdit={true}
                            showDelete={true}
                            className="gap-1"
                          />
                          <PermissionGuard requiredPermission="admin.parents.communicate">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedParent(parent)
                                fetchCommunications(parent.id)
                                setIsCommunicationDialogOpen(true)
                              }}
                              className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 flex items-center gap-1 p-1"
                            >
                              <FaComments className="ml-1" />
                              <span className="hidden md:inline">تواصل</span>
                            </Button>
                          </PermissionGuard>
                          <PermissionGuard requiredPermission="admin.parents.link">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedParent(parent)
                                setIsStudentLinkDialogOpen(true)
                              }}
                              className="text-purple-500 hover:text-purple-700 hover:bg-purple-50 flex items-center gap-1 p-1"
                            >
                              <FaLink className="ml-1" />
                              <span className="hidden md:inline">ربط</span>
                            </Button>
                          </PermissionGuard>
                          {parent.user && (
                            <PermissionGuard requiredPermission="admin.parents.notify">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedParent(parent)
                                  setIsNotificationDialogOpen(true)
                                }}
                                className="text-amber-500 hover:text-amber-700 hover:bg-amber-50 flex items-center gap-1 p-1"
                              >
                                <FaBell className="ml-1" />
                                <span className="hidden md:inline">تنبيه</span>
                              </Button>
                            </PermissionGuard>
                          )}
                        </div>
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef] hidden lg:table-cell">
                        {new Date(parent.createdAt).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">
                        <div className="flex flex-col items-end">
                          <span className={`font-bold ${parent.totalDebt > 0 ? 'text-red-600' : 'text-green-600'}`}>
                            {parent.totalDebt > 0 ? `${parent.totalDebt.toLocaleString()} د.ج` : 'لا توجد ديون'}
                          </span>
                          {parent.totalDebt > 0 && parent.dueInvoices > 0 && (
                            <span className="text-xs text-gray-500">
                              {parent.dueInvoices} فاتورة مستحقة
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef] hidden lg:table-cell">
                        <span className="font-medium text-[var(--primary-color)]">
                          {parent.amountPerStudent ? `${parent.amountPerStudent.toLocaleString()} د.ج` : 'غير محدد'}
                        </span>
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">{parent.students.length}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">{parent.user?.username || "غير مسجل"}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">{parent.email || "غير متوفر"}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">{parent.phone}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">{parent.name}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">{index + 1}</td>
                    </tr>
                  ))}
                  {filteredParents.length === 0 && (
                    <tr>
                      <td colSpan={10} className="p-4 text-center text-gray-500 border border-[#e0f2ef]">
                        لا توجد نتائج مطابقة للبحث
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* عرض البطاقات على الشاشات الصغيرة */}
          <div className="sm:hidden space-y-4">
            {filteredParents.map((parent) => (
              <div key={parent.id} className="bg-white rounded-lg shadow-md p-4 border border-[#e0f2ef]">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-bold text-lg text-[var(--primary-color)]">{parent.name}</h3>
                    <p className="text-sm text-gray-500">{parent.phone}</p>
                  </div>
                  <div className="bg-[#e0f2ef] text-[var(--primary-color)] rounded-full px-2 py-1 text-sm font-medium">
                    {parent.students.length} طالب
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">ديون الولي:</span>
                    <span className={`text-sm font-bold ${parent.totalDebt > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {parent.totalDebt > 0 ? `${parent.totalDebt.toLocaleString()} د.ج` : 'لا توجد ديون'}
                    </span>
                  </div>
                  {parent.totalDebt > 0 && parent.dueInvoices > 0 && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">الفواتير المستحقة:</span>
                      <span className="text-sm font-medium text-orange-600">{parent.dueInvoices} فاتورة</span>
                    </div>
                  )}
                  {parent.email && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">البريد:</span>
                      <span className="text-sm font-medium">{parent.email}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">المبلغ لكل تلميذ:</span>
                    <span className="text-sm font-medium text-[var(--primary-color)]">
                      {parent.amountPerStudent ? `${parent.amountPerStudent.toLocaleString()} د.ج` : 'غير محدد'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">المستخدم:</span>
                    <span className="text-sm font-medium">{parent.user?.username || "غير مسجل"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">تاريخ الإضافة:</span>
                    <span className="text-sm font-medium">{new Date(parent.createdAt).toLocaleDateString('fr-FR')}</span>
                  </div>
                </div>

                <div className="grid grid-cols-5 gap-1 pt-2 border-t border-gray-100">
                  <PermissionGuard requiredPermission="admin.parents.edit">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedParent(parent)
                        setFormData({
                          name: parent.name,
                          phone: parent.phone,
                          email: parent.email || '',
                          address: parent.address || '',
                          amountPerStudent: parent.amountPerStudent?.toString() || '',
                          username: parent.user?.username || '',
                          password: ''
                        })
                        setIsEditDialogOpen(true)
                      }}
                      className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center justify-center p-1"
                    >
                      <FaEdit />
                    </Button>
                  </PermissionGuard>
                  <PermissionGuard requiredPermission="admin.parents.communicate">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedParent(parent)
                        fetchCommunications(parent.id)
                        setIsCommunicationDialogOpen(true)
                      }}
                      className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 flex items-center justify-center p-1"
                    >
                      <FaComments />
                    </Button>
                  </PermissionGuard>
                  <PermissionGuard requiredPermission="admin.parents.link">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedParent(parent)
                        setIsStudentLinkDialogOpen(true)
                      }}
                      className="text-purple-500 hover:text-purple-700 hover:bg-purple-50 flex items-center justify-center p-1"
                    >
                      <FaLink />
                    </Button>
                  </PermissionGuard>
                  {parent.user ? (
                    <PermissionGuard requiredPermission="admin.parents.notify">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedParent(parent)
                          setIsNotificationDialogOpen(true)
                        }}
                        className="text-amber-500 hover:text-amber-700 hover:bg-amber-50 flex items-center justify-center p-1"
                      >
                        <FaBell />
                      </Button>
                    </PermissionGuard>
                  ) : (
                    <div className="p-1"></div> // فارغ للحفاظ على التنسيق
                  )}
                  <PermissionGuard requiredPermission="admin.parents.delete">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedParent(parent)
                        setIsDeleteDialogOpen(true)
                      }}
                      className="text-red-500 hover:text-red-700 hover:bg-red-50 flex items-center justify-center p-1"
                    >
                      <FaTrash />
                    </Button>
                  </PermissionGuard>
                </div>
              </div>
            ))}

            {filteredParents.length === 0 && (
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <p className="text-gray-500">لا توجد نتائج مطابقة للبحث</p>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="communications" className="mt-0">
          {/* عرض الجدول على الشاشات المتوسطة والكبيرة */}
          <div className="hidden sm:block bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
            <div className="overflow-auto" style={{ maxWidth: '100%' }}>
              <table className="min-w-full border-collapse" dir="ltr">
                <thead>
                  <tr className="bg-[var(--primary-color)]">
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">الإجراءات</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef] hidden lg:table-cell">المستخدم</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">الحالة</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">التاريخ</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">العنوان</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">نوع التواصل</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">اسم الولي</th>
                    <th className="p-2 text-right text-white font-bold whitespace-nowrap border border-[#e0f2ef]">الرقم</th>
                  </tr>
                </thead>
                <tbody>
                  {communications.map((comm, index) => (
                    <tr key={comm.id} className="border-b border-[#e0f2ef] hover:bg-gray-50">
                      <td className="p-2 whitespace-nowrap border border-[#e0f2ef]">
                        <div className="flex gap-2 justify-start">
                          <PermissionGuard requiredPermission="admin.parents.communicate">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedCommunication(comm)
                                setCommunicationData({
                                  type: comm.type,
                                  title: comm.title,
                                  content: comm.content,
                                  status: comm.status,
                                  response: comm.response || '',
                                  sendNotification: false
                                })
                                setIsCommunicationDialogOpen(true)
                              }}
                              className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1 p-1"
                            >
                              <FaEdit className="ml-1" />
                              <span className="hidden md:inline">تعديل</span>
                            </Button>
                          </PermissionGuard>
                        </div>
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef] hidden lg:table-cell">{comm.user.profile.name}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">
                        <Badge className={
                          comm.status === 'PENDING' ? 'bg-amber-500' :
                          comm.status === 'COMPLETED' ? 'bg-primary-color' :
                          comm.status === 'FOLLOW_UP' ? 'bg-blue-500' :
                          'bg-red-500'
                        }>
                          {comm.status === 'PENDING' && 'قيد الانتظار'}
                          {comm.status === 'COMPLETED' && 'مكتمل'}
                          {comm.status === 'FOLLOW_UP' && 'متابعة'}
                          {comm.status === 'CANCELLED' && 'ملغي'}
                        </Badge>
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef] hidden md:table-cell">
                        {new Date(comm.date).toLocaleDateString('fr-FR')}
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">{comm.title}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">
                        {comm.type === 'PHONE_CALL' && 'مكالمة هاتفية'}
                        {comm.type === 'MEETING' && 'اجتماع'}
                        {comm.type === 'EMAIL' && 'بريد إلكتروني'}
                        {comm.type === 'SMS' && 'رسالة نصية'}
                        {comm.type === 'WHATSAPP' && 'واتساب'}
                        {comm.type === 'OTHER' && 'أخرى'}
                      </td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">{comm.parent.name}</td>
                      <td className="p-2 text-right whitespace-nowrap border border-[#e0f2ef]">{index + 1}</td>
                    </tr>
                  ))}
                  {communications.length === 0 && (
                    <tr>
                      <td colSpan={8} className="p-4 text-center text-gray-500 border border-[#e0f2ef]">
                        لا توجد سجلات تواصل
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* عرض البطاقات على الشاشات الصغيرة */}
          <div className="sm:hidden space-y-4">
            {communications.map((comm) => (
              <div key={comm.id} className="bg-white rounded-lg shadow-md p-4 border border-[#e0f2ef]">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-bold text-lg text-[var(--primary-color)]">{comm.title}</h3>
                    <p className="text-sm text-gray-500">{comm.parent.name}</p>
                  </div>
                  <Badge className={
                    comm.status === 'PENDING' ? 'bg-amber-500' :
                    comm.status === 'COMPLETED' ? 'bg-primary-color' :
                    comm.status === 'FOLLOW_UP' ? 'bg-blue-500' :
                    'bg-red-500'
                  }>
                    {comm.status === 'PENDING' && 'قيد الانتظار'}
                    {comm.status === 'COMPLETED' && 'مكتمل'}
                    {comm.status === 'FOLLOW_UP' && 'متابعة'}
                    {comm.status === 'CANCELLED' && 'ملغي'}
                  </Badge>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">نوع التواصل:</span>
                    <span className="text-sm font-medium">
                      {comm.type === 'PHONE_CALL' && 'مكالمة هاتفية'}
                      {comm.type === 'MEETING' && 'اجتماع'}
                      {comm.type === 'EMAIL' && 'بريد إلكتروني'}
                      {comm.type === 'SMS' && 'رسالة نصية'}
                      {comm.type === 'WHATSAPP' && 'واتساب'}
                      {comm.type === 'OTHER' && 'أخرى'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">التاريخ:</span>
                    <span className="text-sm font-medium">{new Date(comm.date).toLocaleDateString('fr-FR')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">المستخدم:</span>
                    <span className="text-sm font-medium">{comm.user.profile.name}</span>
                  </div>
                </div>

                <div className="flex justify-end pt-2 border-t border-gray-100">
                  <PermissionGuard requiredPermission="admin.parents.communicate">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSelectedCommunication(comm)
                        setCommunicationData({
                          type: comm.type,
                          title: comm.title,
                          content: comm.content,
                          status: comm.status,
                          response: comm.response || '',
                          sendNotification: false
                        })
                        setIsCommunicationDialogOpen(true)
                      }}
                      className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                    >
                      <FaEdit className="ml-1" />
                      تعديل
                    </Button>
                  </PermissionGuard>
                </div>
              </div>
            ))}

            {communications.length === 0 && (
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <p className="text-gray-500">لا توجد سجلات تواصل</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Add Dialog */}
      <Dialog
        open={isAddDialogOpen}
        onOpenChange={(open) => {
          // إذا تم إغلاق النموذج، نقوم بإعادة تعيين formData
          if (!open) {
            setFormData({
              name: '',
              phone: '',
              email: '',
              address: '',
              amountPerStudent: '',
              username: '',
              password: ''
            });
          }
          setIsAddDialogOpen(open);
        }}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] max-h-[90vh] overflow-y-auto w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-[var(--primary-color)] font-bold text-lg sm:text-xl flex items-center gap-2">
              <FaUserPlus className="text-[var(--primary-color)]" />
              إضافة ولي جديد
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">اسم الولي</Label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">رقم الهاتف</Label>
              <Input
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="tel"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">البريد الإلكتروني (اختياري)</Label>
              <Input
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="email"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">العنوان (اختياري)</Label>
              <Textarea
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                rows={2}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">المبلغ لكل تلميذ (د.ج) (اختياري)</Label>
              <Input
                value={formData.amountPerStudent}
                onChange={(e) => setFormData({ ...formData, amountPerStudent: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="number"
                min="0"
                step="0.01"
                placeholder="أدخل المبلغ المطلوب لكل تلميذ"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">اسم المستخدم</Label>
              <Input
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">كلمة المرور</Label>
              <Input
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="password"
              />
              <p className="text-xs text-gray-500 mt-1">كلمة المرور لتسجيل دخول ولي الأمر للنظام</p>
            </div>
          </div>
          <DialogFooter className="mt-6 flex flex-col sm:flex-row sm:justify-start">
            <Button
              type="submit"
              onClick={handleAdd}
              disabled={!formData.name || !formData.phone || !formData.username || !formData.password}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-full sm:w-auto"
            >
              <FaUserPlus className="ml-1" />
              إضافة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] max-h-[90vh] overflow-y-auto w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-[var(--primary-color)] font-bold text-lg sm:text-xl flex items-center gap-2">
              <FaEdit className="text-[var(--primary-color)]" />
              تعديل بيانات الولي
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">اسم الولي</Label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">رقم الهاتف</Label>
              <Input
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="tel"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">البريد الإلكتروني (اختياري)</Label>
              <Input
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="email"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">العنوان (اختياري)</Label>
              <Textarea
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                rows={2}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">المبلغ لكل تلميذ (د.ج) (اختياري)</Label>
              <Input
                value={formData.amountPerStudent}
                onChange={(e) => setFormData({ ...formData, amountPerStudent: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="number"
                min="0"
                step="0.01"
                placeholder="أدخل المبلغ المطلوب لكل تلميذ"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">اسم المستخدم</Label>
              <Input
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                placeholder="لتحديث اسم المستخدم"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">كلمة المرور</Label>
              <Input
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                type="password"
                placeholder="لتحديث كلمة المرور"
              />
              <p className="text-xs text-gray-500 mt-1">اترك هذا الحقل فارغًا إذا لم ترغب في تغيير كلمة المرور</p>
            </div>
          </div>
          <DialogFooter className="mt-6 flex flex-col sm:flex-row sm:justify-start">
            <Button
              type="submit"
              onClick={handleEdit}
              disabled={!formData.name || !formData.phone}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-full sm:w-auto"
            >
              <FaSave className="ml-1" />
              حفظ التغييرات
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-[#fff8f8] border border-red-200 max-h-[90vh] overflow-y-auto w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader className="mb-3">
            <DialogTitle className="text-red-600 font-bold text-lg sm:text-xl flex items-center">
              <FaTrash className="ml-2" />
              تأكيد الحذف
            </DialogTitle>
          </DialogHeader>
          <div className="py-2 sm:py-4">
            <p className="text-sm sm:text-base">هل أنت متأكد من رغبتك في حذف هذا الولي؟</p>
            {selectedParent && (
              <div className="mt-3 sm:mt-4 p-3 sm:p-4 bg-red-50 rounded-md border border-red-200">
                <p className="text-sm sm:text-base"><strong>اسم الولي:</strong> {selectedParent.name}</p>
                <p className="text-sm sm:text-base"><strong>عدد التلاميذ:</strong> {selectedParent.students.length}</p>
                <p className="text-red-600 text-xs sm:text-sm mt-2">
                  سيتم حذف الولي ولكن لن يتم حذف التلاميذ المرتبطين به.
                </p>
              </div>
            )}
          </div>
          <DialogFooter className="mt-4 flex flex-col-reverse sm:flex-row sm:justify-start gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100 w-full sm:w-auto mt-2 sm:mt-0"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-full sm:w-auto"
            >
              <FaTrash className="ml-1" />
              تأكيد الحذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Student Link Dialog */}
      <Dialog open={isStudentLinkDialogOpen} onOpenChange={setIsStudentLinkDialogOpen}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] max-h-[90vh] overflow-y-auto w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader className="mb-3">
            <DialogTitle className="text-[var(--primary-color)] font-bold text-lg sm:text-xl flex items-center gap-2">
              <FaLink className="text-[var(--primary-color)]" />
              ربط طالب بالولي
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedParent && (
              <div className="p-3 sm:p-4 bg-[#e0f2ef] rounded-md">
                <p className="text-sm sm:text-base"><strong>اسم الولي:</strong> {selectedParent.name}</p>
                <p className="text-sm sm:text-base"><strong>رقم الهاتف:</strong> {selectedParent.phone}</p>
                {selectedParent.students.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm sm:text-base"><strong>الطلاب المرتبطين حالياً:</strong></p>
                    <ul className="mt-1 space-y-1">
                      {selectedParent.students.map(student => (
                        <li key={student.id} className="flex items-center justify-between text-sm">
                          <span>{student.name} - {student.grade}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleUnlinkStudent(student.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 flex items-center gap-1 p-1 h-auto"
                          >
                            <FaUnlink className="h-3 w-3" />
                            <span className="hidden sm:inline">إلغاء الربط</span>
                            <span className="inline sm:hidden">إلغاء</span>
                          </Button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">اختر الطالب</Label>
              <Select
                value={studentLinkData.studentId}
                onValueChange={(value) => setStudentLinkData({ ...studentLinkData, studentId: value })}
              >
                <SelectTrigger className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر الطالب" />
                </SelectTrigger>
                <SelectContent>
                  {availableStudents.length === 0 ? (
                    <SelectItem value="no-students" disabled>لا يوجد طلاب متاحين للربط</SelectItem>
                  ) : (
                    availableStudents.map(student => (
                      <SelectItem key={student.id} value={student.id.toString()}>
                        {student.name} {student.classe?.name ? `- ${student.classe.name}` : ''}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter className="mt-4 flex flex-col-reverse sm:flex-row sm:justify-start gap-2">
            <Button
              variant="outline"
              onClick={() => setIsStudentLinkDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100 w-full sm:w-auto mt-2 sm:mt-0"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleLinkStudent}
              disabled={!studentLinkData.studentId}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-full sm:w-auto"
            >
              <FaLink className="ml-1" />
              ربط الطالب
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Communication Dialog */}
      <Dialog open={isCommunicationDialogOpen} onOpenChange={setIsCommunicationDialogOpen}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] max-h-[90vh] overflow-y-auto w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader className="mb-3">
            <DialogTitle className="text-[var(--primary-color)] font-bold text-lg sm:text-xl flex items-center gap-2">
              <FaComments className="text-[var(--primary-color)]" />
              {selectedCommunication ? 'تعديل سجل تواصل' : 'إضافة سجل تواصل جديد'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedParent && !selectedCommunication && (
              <div className="p-3 sm:p-4 bg-[#e0f2ef] rounded-md">
                <p className="text-sm sm:text-base"><strong>اسم الولي:</strong> {selectedParent.name}</p>
                <p className="text-sm sm:text-base"><strong>رقم الهاتف:</strong> {selectedParent.phone}</p>
              </div>
            )}
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">نوع التواصل</Label>
              <Select
                value={communicationData.type}
                onValueChange={(value) => setCommunicationData({ ...communicationData, type: value })}
              >
                <SelectTrigger className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر نوع التواصل" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PHONE_CALL">مكالمة هاتفية</SelectItem>
                  <SelectItem value="MEETING">اجتماع</SelectItem>
                  <SelectItem value="EMAIL">بريد إلكتروني</SelectItem>
                  <SelectItem value="SMS">رسالة نصية</SelectItem>
                  <SelectItem value="WHATSAPP">واتساب</SelectItem>
                  <SelectItem value="OTHER">أخرى</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">عنوان التواصل</Label>
              <Input
                value={communicationData.title}
                onChange={(e) => setCommunicationData({ ...communicationData, title: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">محتوى التواصل</Label>
              <Textarea
                value={communicationData.content}
                onChange={(e) => setCommunicationData({ ...communicationData, content: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">حالة التواصل</Label>
              <Select
                value={communicationData.status}
                onValueChange={(value) => setCommunicationData({ ...communicationData, status: value })}
              >
                <SelectTrigger className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر حالة التواصل" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">قيد الانتظار</SelectItem>
                  <SelectItem value="COMPLETED">مكتمل</SelectItem>
                  <SelectItem value="FOLLOW_UP">متابعة</SelectItem>
                  <SelectItem value="CANCELLED">ملغي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">الرد (اختياري)</Label>
              <Textarea
                value={communicationData.response}
                onChange={(e) => setCommunicationData({ ...communicationData, response: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                rows={2}
                placeholder="أضف الرد على التواصل هنا (إن وجد)"
              />
            </div>
            {selectedParent?.user && (
              <div className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="checkbox"
                  id="sendNotification"
                  checked={communicationData.sendNotification}
                  onChange={(e) => setCommunicationData({ ...communicationData, sendNotification: e.target.checked })}
                  className="ml-2"
                />
                <Label htmlFor="sendNotification" className="text-right block text-[var(--primary-color)] font-medium text-sm">
                  إرسال تنبيه للولي
                </Label>
              </div>
            )}
          </div>
          <DialogFooter className="mt-4 flex flex-col-reverse sm:flex-row sm:justify-start gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsCommunicationDialogOpen(false)
                setSelectedCommunication(null)
              }}
              className="border-gray-300 hover:bg-gray-100 w-full sm:w-auto mt-2 sm:mt-0"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleAddCommunication}
              disabled={!communicationData.title || !communicationData.content}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-full sm:w-auto"
            >
              <FaSave className="ml-1" />
              {selectedCommunication ? 'تحديث' : 'إضافة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Notification Dialog */}
      <Dialog open={isNotificationDialogOpen} onOpenChange={setIsNotificationDialogOpen}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] max-h-[90vh] overflow-y-auto w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader className="mb-3">
            <DialogTitle className="text-[var(--primary-color)] font-bold text-lg sm:text-xl flex items-center gap-2">
              <FaBell className="text-[var(--primary-color)]" />
              إرسال تنبيه للولي
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedParent && (
              <div className="p-3 sm:p-4 bg-[#e0f2ef] rounded-md">
                <p className="text-sm sm:text-base"><strong>اسم الولي:</strong> {selectedParent.name}</p>
                <p className="text-sm sm:text-base"><strong>اسم المستخدم:</strong> {selectedParent.user?.username}</p>
              </div>
            )}
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">نوع التنبيه</Label>
              <Select
                value={notificationData.type}
                onValueChange={(value) => setNotificationData({ ...notificationData, type: value })}
              >
                <SelectTrigger className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر نوع التنبيه" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GENERAL">عام</SelectItem>
                  <SelectItem value="LESSON">درس</SelectItem>
                  <SelectItem value="EXAM">امتحان</SelectItem>
                  <SelectItem value="ATTENDANCE">حضور</SelectItem>
                  <SelectItem value="PAYMENT">مدفوعات</SelectItem>
                  <SelectItem value="ACHIEVEMENT">إنجاز</SelectItem>
                  <SelectItem value="REMOTE_CLASS">فصل عن بعد</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">عنوان التنبيه</Label>
              <Input
                value={notificationData.title}
                onChange={(e) => setNotificationData({ ...notificationData, title: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium text-sm">محتوى التنبيه</Label>
              <Textarea
                value={notificationData.content}
                onChange={(e) => setNotificationData({ ...notificationData, content: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter className="mt-4 flex flex-col-reverse sm:flex-row sm:justify-start gap-2">
            <Button
              variant="outline"
              onClick={() => setIsNotificationDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100 w-full sm:w-auto mt-2 sm:mt-0"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleSendNotification}
              disabled={!notificationData.title || !notificationData.content}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2 w-full sm:w-auto"
            >
              <FaBell className="ml-1" />
              إرسال التنبيه
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  )
}
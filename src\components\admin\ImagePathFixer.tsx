'use client';

import React, { useState } from 'react';
import { FaTools, FaCheck, FaTimes, FaSpinner } from 'react-icons/fa';
import axios from 'axios';
import { toast } from 'react-toastify';

interface ImagePathFixerProps {
  className?: string;
}

const ImagePathFixer: React.FC<ImagePathFixerProps> = ({ className = '' }) => {
  const [isFixing, setIsFixing] = useState(false);
  const [fixResults, setFixResults] = useState<{
    logo: 'idle' | 'success' | 'error';
    favicon: 'idle' | 'success' | 'error';
  }>({
    logo: 'idle',
    favicon: 'idle'
  });

  const fixImagePaths = async () => {
    setIsFixing(true);
    setFixResults({ logo: 'idle', favicon: 'idle' });

    try {
      // جلب الإعدادات الحالية
      const response = await axios.get('/api/settings');
      const settings = response.data.settings;

      if (!settings) {
        toast.error('فشل في جلب الإعدادات');
        return;
      }

      let updatedSettings = { ...settings };
      let hasChanges = false;

      // إصلاح مسار الشعار
      if (settings.logoUrl && !settings.logoUrl.startsWith('/api/uploads/') && settings.logoUrl.includes('/uploads/')) {
        const newLogoUrl = settings.logoUrl.replace('/uploads/', '/api/uploads/');
        updatedSettings.logoUrl = newLogoUrl;
        hasChanges = true;
        setFixResults(prev => ({ ...prev, logo: 'success' }));
        console.log('تم إصلاح مسار الشعار:', newLogoUrl);
      } else {
        setFixResults(prev => ({ ...prev, logo: 'success' }));
      }

      // إصلاح مسار الفافيكون
      if (settings.faviconUrl && !settings.faviconUrl.startsWith('/api/uploads/') && settings.faviconUrl.includes('/uploads/')) {
        const newFaviconUrl = settings.faviconUrl.replace('/uploads/', '/api/uploads/');
        updatedSettings.faviconUrl = newFaviconUrl;
        hasChanges = true;
        setFixResults(prev => ({ ...prev, favicon: 'success' }));
        console.log('تم إصلاح مسار الفافيكون:', newFaviconUrl);
      } else {
        setFixResults(prev => ({ ...prev, favicon: 'success' }));
      }

      // حفظ التغييرات إذا كانت هناك تغييرات
      if (hasChanges) {
        await axios.post('/api/settings', { settings: updatedSettings });
        
        // تحديث localStorage
        localStorage.setItem('siteSettings', JSON.stringify(updatedSettings));
        
        // إرسال حدث للمكونات الأخرى للتحديث
        window.dispatchEvent(new CustomEvent('siteSettingsUpdated', { detail: updatedSettings }));
        
        toast.success('تم إصلاح مسارات الصور وحفظ التغييرات بنجاح');
      } else {
        toast.info('مسارات الصور صحيحة بالفعل');
      }

    } catch (error) {
      console.error('Error fixing image paths:', error);
      setFixResults({ logo: 'error', favicon: 'error' });
      toast.error('فشل في إصلاح مسارات الصور');
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <FaTools className="text-[var(--primary-color)] text-xl" />
        <h2 className="text-xl font-bold text-gray-800">إصلاح مسارات الصور</h2>
      </div>

      <div className="space-y-4">
        <p className="text-gray-600">
          هذه الأداة تقوم بإصلاح مسارات الصور المرفوعة لتعمل بشكل صحيح مع Next.js API routes.
        </p>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-medium text-yellow-800 mb-2">ما تقوم به هذه الأداة:</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• تحويل مسارات الصور من <code>/uploads/</code> إلى <code>/api/uploads/</code></li>
            <li>• إصلاح مسار الشعار والفافيكون</li>
            <li>• حفظ التغييرات في قاعدة البيانات</li>
            <li>• تحديث المكونات الأخرى فوراً</li>
          </ul>
        </div>

        {/* نتائج الإصلاح */}
        {(fixResults.logo !== 'idle' || fixResults.favicon !== 'idle') && (
          <div className="space-y-3">
            <h3 className="font-medium text-gray-800">نتائج الإصلاح:</h3>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">الشعار:</span>
              {fixResults.logo === 'success' && (
                <div className="flex items-center gap-1 text-green-600">
                  <FaCheck className="text-sm" />
                  <span className="text-sm">تم الإصلاح</span>
                </div>
              )}
              {fixResults.logo === 'error' && (
                <div className="flex items-center gap-1 text-red-600">
                  <FaTimes className="text-sm" />
                  <span className="text-sm">فشل الإصلاح</span>
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">الفافيكون:</span>
              {fixResults.favicon === 'success' && (
                <div className="flex items-center gap-1 text-green-600">
                  <FaCheck className="text-sm" />
                  <span className="text-sm">تم الإصلاح</span>
                </div>
              )}
              {fixResults.favicon === 'error' && (
                <div className="flex items-center gap-1 text-red-600">
                  <FaTimes className="text-sm" />
                  <span className="text-sm">فشل الإصلاح</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* زر الإصلاح */}
        <button
          onClick={fixImagePaths}
          disabled={isFixing}
          className="w-full px-4 py-3 bg-[var(--primary-color)] text-white rounded-lg hover:bg-[var(--secondary-color)] transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
        >
          {isFixing ? (
            <>
              <FaSpinner className="animate-spin" />
              <span>جاري الإصلاح...</span>
            </>
          ) : (
            <>
              <FaTools />
              <span>إصلاح مسارات الصور</span>
            </>
          )}
        </button>

        <div className="text-xs text-gray-500 space-y-1">
          <p>• يُنصح بتشغيل هذه الأداة إذا كانت الصور لا تظهر بشكل صحيح</p>
          <p>• هذه العملية آمنة ولا تؤثر على الملفات الأصلية</p>
          <p>• يتم حفظ التغييرات تلقائياً في قاعدة البيانات</p>
        </div>
      </div>
    </div>
  );
};

export default ImagePathFixer;

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';
import { EvaluationType } from '@prisma/client';

// GET /api/quran-progress - جلب تقدم حفظ القرآن للطالب
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const studentId = searchParams.get('studentId');

    if (!studentId) {
      return NextResponse.json(
        { message: "معرف الطالب مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: {
        id: parseInt(studentId)
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "الطالب غير موجود" },
        { status: 404 }
      );
    }

    // جلب تقدم حفظ القرآن
    const progress = await prisma.quranProgress.findMany({
      where: {
        studentId: parseInt(studentId)
      },
      include: {
        surah: true
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    // تنسيق البيانات
    const formattedProgress = progress.map(item => ({
      id: item.id,
      surahName: item.surah.name,
      fromAyah: item.startVerse,
      toAyah: item.endVerse,
      date: item.startDate.toISOString(),
      grade: (item.memorization + item.tajweed) / 2, // متوسط الدرجة
      notes: ''
    }));

    return NextResponse.json(formattedProgress);
  } catch (error) {
    console.error('Error fetching quran progress:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب تقدم حفظ القرآن" },
      { status: 500 }
    );
  }
}

// POST /api/quran-progress - إضافة تقدم جديد في حفظ القرآن
export async function POST(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'TEACHER' && userData.role !== 'ADMIN')) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { studentId, surahId, fromAyah, toAyah, grade } = body;

    // التحقق من البيانات المطلوبة
    if (!studentId || !surahId || !fromAyah || !toAyah || !grade) {
      return NextResponse.json(
        { message: "جميع الحقول مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: {
        id: parseInt(studentId)
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "الطالب غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود السورة
    const surah = await prisma.surah.findUnique({
      where: {
        id: parseInt(surahId)
      }
    });

    if (!surah) {
      return NextResponse.json(
        { message: "السورة غير موجودة" },
        { status: 404 }
      );
    }

    // التحقق من صحة أرقام الآيات
    if (fromAyah < 1 || toAyah > surah.totalAyahs || fromAyah > toAyah) {
      return NextResponse.json(
        { message: "أرقام الآيات غير صحيحة" },
        { status: 400 }
      );
    }

    // التحقق من صحة التقدير
    if (grade < 0 || grade > 10) {
      return NextResponse.json(
        { message: "التقدير يجب أن يكون بين 0 و 10" },
        { status: 400 }
      );
    }

    // الحصول على امتحان حفظ القرآن الحالي أو إنشاء واحد جديد
    let quranExam = await prisma.exam.findFirst({
      where: {
        evaluationType: EvaluationType.QURAN_MEMORIZATION,
        requiresSurah: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // إذا لم يوجد امتحان، قم بإنشاء واحد جديد
    if (!quranExam) {
      // تحديد الشهر بتنسيق MM-YYYY
      const today = new Date();
      const month = `${String(today.getMonth() + 1).padStart(2, '0')}-${today.getFullYear()}`;

      quranExam = await prisma.exam.create({
        data: {
          description: 'امتحان حفظ القرآن',
          month: month,
          maxPoints: 100,
          passingPoints: 60,
          evaluationType: EvaluationType.QURAN_MEMORIZATION,
          requiresSurah: true,
          isPeriodic: true,
          period: 'شهري'
        }
      });
    }

    // إضافة تقدم جديد
    const progress = await prisma.quranProgress.create({
      data: {
        studentId: parseInt(studentId),
        surahId: parseInt(surahId),
        examId: quranExam.id, // استخدام معرف الامتحان الذي تم الحصول عليه
        startVerse: parseInt(fromAyah),
        endVerse: parseInt(toAyah),
        memorization: typeof grade === 'number' ? Math.floor(grade) : Math.floor(parseFloat(grade)),
        tajweed: typeof grade === 'number' ? Math.floor(grade) : Math.floor(parseFloat(grade)),
        startDate: new Date()
      },
      include: {
        surah: true
      }
    });

    // حساب عدد الآيات المحفوظة
    const ayahsCount = progress.endVerse - progress.startVerse + 1;

    // إضافة نقاط للطالب (نقطة لكل آية)
    await prisma.student.update({
      where: {
        id: parseInt(studentId)
      },
      data: {
        totalPoints: {
          increment: ayahsCount
        }
      }
    });

    // تنسيق البيانات
    const formattedProgress = {
      id: progress.id,
      surahName: progress.surah?.name || 'سورة غير محددة',
      fromAyah: progress.startVerse,
      toAyah: progress.endVerse,
      date: progress.startDate.toISOString(),
      grade: (progress.memorization + progress.tajweed) / 2,
      notes: '',
      ayahsCount
    };

    return NextResponse.json({
      data: formattedProgress,
      message: "تم إضافة تقدم الحفظ بنجاح"
    }, { status: 201 });
  } catch (error) {
    console.error('Error adding quran progress:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إضافة تقدم الحفظ" },
      { status: 500 }
    );
  }
}

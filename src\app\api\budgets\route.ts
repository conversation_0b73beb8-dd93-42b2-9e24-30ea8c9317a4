import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { BudgetStatus } from '@prisma/client';

// GET /api/budgets - الحصول على الميزانيات
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const where: {
      name?: { contains: string };
      status?: BudgetStatus;
    } = {};

    if (query) {
      where.name = { contains: query };
    }

    if (status && Object.values(BudgetStatus).includes(status as BudgetStatus)) {
      where.status = status as BudgetStatus;
    }

    // جلب الميزانيات
    const budgets = await prisma.budget.findMany({
      where,
      orderBy: { startDate: 'desc' },
      skip,
      take: limit,
      include: {
        _count: {
          select: {
            items: true
          }
        }
      }
    });

    // جلب العدد الإجمالي للميزانيات
    const total = await prisma.budget.count({ where });

    return NextResponse.json({
      budgets,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      }
    });
  } catch (error) {
    console.error('خطأ في جلب الميزانيات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الميزانيات' },
      { status: 500 }
    );
  }
}

// POST /api/budgets - إنشاء ميزانية جديدة
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, description, startDate, endDate, totalAmount, items } = body;

    if (!name || !startDate || !endDate || !totalAmount) {
      return NextResponse.json(
        { error: 'جميع الحقول الأساسية مطلوبة (الاسم، تاريخ البداية، تاريخ النهاية، المبلغ الإجمالي)' },
        { status: 400 }
      );
    }

    // التحقق من صحة التواريخ
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return NextResponse.json(
        { error: 'تنسيق التاريخ غير صحيح' },
        { status: 400 }
      );
    }

    if (start >= end) {
      return NextResponse.json(
        { error: 'يجب أن يكون تاريخ البداية قبل تاريخ النهاية' },
        { status: 400 }
      );
    }

    // التحقق من صحة المبلغ الإجمالي
    if (typeof totalAmount !== 'number' || totalAmount <= 0) {
      return NextResponse.json(
        { error: 'المبلغ الإجمالي يجب أن يكون رقمًا موجبًا' },
        { status: 400 }
      );
    }

    // إنشاء الميزانية وبنودها في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء الميزانية
      const budget = await tx.budget.create({
        data: {
          name,
          description,
          startDate: start,
          endDate: end,
          totalAmount,
          status: 'DRAFT' // تبدأ كمسودة
        }
      });

      // إنشاء بنود الميزانية إذا تم توفيرها
      if (items && Array.isArray(items) && items.length > 0) {
        for (const item of items) {
          if (!item.categoryId || !item.amount) {
            continue; // تخطي البنود غير المكتملة
          }

          // التحقق من وجود الفئة
          const category = await tx.expenseCategory.findUnique({
            where: { id: item.categoryId }
          });

          if (!category) {
            continue; // تخطي البنود ذات الفئات غير الموجودة
          }

          await tx.budgetItem.create({
            data: {
              budgetId: budget.id,
              categoryId: item.categoryId,
              amount: item.amount,
              notes: item.notes
            }
          });
        }
      }

      return budget;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الميزانية' },
      { status: 500 }
    );
  }
}



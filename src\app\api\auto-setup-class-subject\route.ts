import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'TEACHER') {
      return NextResponse.json(
        { message: "غير مصرح به - يجب أن تكون معلماً", success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { examId, classId } = body;

    if (!examId || !classId) {
      return NextResponse.json(
        { message: "يجب توفير معرف الامتحان ومعرف القسم", success: false },
        { status: 400 }
      );
    }

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: { userId: userData.id }
    });

    if (!teacher) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المعلم", success: false },
        { status: 404 }
      );
    }

    // جلب معلومات الامتحان
    const exam = await prisma.exam.findUnique({
      where: { id: parseInt(examId) },
      include: { subject: true }
    });

    if (!exam) {
      return NextResponse.json(
        { message: "لم يتم العثور على الامتحان", success: false },
        { status: 404 }
      );
    }

    // التحقق من وجود القسم
    const classe = await prisma.classe.findUnique({
      where: { id: parseInt(classId) }
    });

    if (!classe) {
      return NextResponse.json(
        { message: "لم يتم العثور على القسم", success: false },
        { status: 404 }
      );
    }

    let result = {
      teacherSubjectCreated: false,
      classSubjectCreated: false,
      teacherSubjectId: null as number | null,
      classSubjectId: null as number | null
    };

    // إذا كان الامتحان مرتبط بمادة دراسية
    if (exam.subjectId) {
      // التحقق من وجود علاقة المعلم بالمادة
      let teacherSubject = await prisma.teacherSubject.findFirst({
        where: {
          teacherId: teacher.id,
          subjectId: exam.subjectId
        }
      });

      // إنشاء علاقة المعلم بالمادة إذا لم تكن موجودة
      if (!teacherSubject) {
        teacherSubject = await prisma.teacherSubject.create({
          data: {
            teacherId: teacher.id,
            subjectId: exam.subjectId
          }
        });
        result.teacherSubjectCreated = true;
      }

      result.teacherSubjectId = teacherSubject.id;

      // التحقق من وجود علاقة القسم بالمادة
      let classSubject = await prisma.classSubject.findFirst({
        where: {
          classeId: parseInt(classId),
          teacherSubjectId: teacherSubject.id
        }
      });

      // إنشاء علاقة القسم بالمادة إذا لم تكن موجودة
      if (!classSubject) {
        classSubject = await prisma.classSubject.create({
          data: {
            classeId: parseInt(classId),
            teacherSubjectId: teacherSubject.id
          }
        });
        result.classSubjectCreated = true;
      }

      result.classSubjectId = classSubject.id;

      return NextResponse.json({
        success: true,
        message: "تم إعداد العلاقات بنجاح",
        data: result
      });
    } else {
      // إذا لم يكن الامتحان مرتبط بمادة دراسية، نحتاج لإنشاء علاقة عامة
      // نبحث عن أي مادة يدرسها المعلم
      const teacherSubject = await prisma.teacherSubject.findFirst({
        where: { teacherId: teacher.id },
        include: { subject: true }
      });

      if (!teacherSubject) {
        return NextResponse.json(
          { 
            message: "المعلم غير مرتبط بأي مادة دراسية. يرجى ربط المعلم بمادة دراسية أولاً من لوحة المشرف.", 
            success: false,
            needsSubjectAssignment: true
          },
          { status: 400 }
        );
      }

      result.teacherSubjectId = teacherSubject.id;

      // التحقق من وجود علاقة القسم بالمادة
      let classSubject = await prisma.classSubject.findFirst({
        where: {
          classeId: parseInt(classId),
          teacherSubjectId: teacherSubject.id
        }
      });

      // إنشاء علاقة القسم بالمادة إذا لم تكن موجودة
      if (!classSubject) {
        classSubject = await prisma.classSubject.create({
          data: {
            classeId: parseInt(classId),
            teacherSubjectId: teacherSubject.id
          }
        });
        result.classSubjectCreated = true;
      }

      result.classSubjectId = classSubject.id;

      return NextResponse.json({
        success: true,
        message: "تم إعداد العلاقات بنجاح باستخدام المادة الافتراضية",
        data: result
      });
    }

  } catch (error) {
    console.error('Error in auto-setup-class-subject:', error);
    return NextResponse.json(
      { 
        message: "حدث خطأ أثناء إعداد العلاقات", 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface CurriculumResource {
  id: number;
  title: string;
  type: string;
  url: string;
  lessonId: number;
}

interface CurriculumLesson {
  id: number;
  title: string;
  description?: string;
  order: number;
  unitId: number;
  resources: CurriculumResource[];
}

interface CurriculumUnit {
  id: number;
  title: string;
  description?: string;
  order: number;
  subjectId: number;
  lessons: CurriculumLesson[];
}

interface EditUnitDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  unit: CurriculumUnit | null;
}

export default function EditUnitDialog({ isOpen, onCloseAction, onSuccessAction, unit }: EditUnitDialogProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    order: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (unit) {
      setFormData({
        title: unit.title,
        description: unit.description || '',
        order: unit.order.toString()
      });
    }
  }, [unit]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleUpdateUnit = async () => {
    if (!unit) return;

    if (!formData.title.trim()) {
      toast.error('الرجاء إدخال عنوان الوحدة');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/curriculum/units/${unit.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          order: formData.order ? parseInt(formData.order) : unit.order
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update unit');
      }

      toast.success('تم تحديث الوحدة بنجاح');
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error updating unit:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث الوحدة');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleUpdateUnit}
      disabled={
        isLoading ||
        !formData.title.trim() ||
        (!!unit &&
          formData.title === unit.title &&
          formData.description === (unit.description || '') &&
          formData.order === unit.order.toString())
      }
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري التحديث...' : 'حفظ التغييرات'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="تعديل الوحدة"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
        <div className="space-y-2">
          <Label>عنوان الوحدة</Label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="أدخل عنوان الوحدة"
          />
        </div>

        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف الوحدة"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label>الترتيب</Label>
          <Input
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="أدخل ترتيب الوحدة"
            min={1}
          />
        </div>
      </div>
    </AnimatedDialog>
  );
}

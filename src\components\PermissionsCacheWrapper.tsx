'use client';

import { useEffect, useState } from 'react';
import PermissionsCacheManager from './PermissionsCacheManager';

interface UserData {
  id: number;
  username: string;
  role: string;
  roleId?: number;
}

/**
 * مكون wrapper لإدارة التخزين المؤقت للصلاحيات
 * يجلب بيانات المستخدم الحالي ويمررها لمدير التخزين المؤقت
 */
export const PermissionsCacheWrapper: React.FC = () => {
  const [userId, setUserId] = useState<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeCache = async () => {
      try {
        // محاولة جلب بيانات المستخدم الحالي
        const response = await fetch('/api/auth/me');
        
        if (response.ok) {
          const userData: UserData = await response.json();
          setUserId(userData.id);
          console.log('تم تهيئة مدير التخزين المؤقت للمستخدم:', userData.id);
        } else {
          // المستخدم غير مسجل دخول
          setUserId(null);
          console.log('المستخدم غير مسجل دخول، لا حاجة لمدير التخزين المؤقت');
        }
      } catch (error) {
        console.error('خطأ في تهيئة مدير التخزين المؤقت:', error);
        setUserId(null);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeCache();
  }, []);

  const handleCacheCleared = () => {
    console.log('تم مسح التخزين المؤقت، قد تحتاج المكونات لإعادة تحميل البيانات');
    // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
  };

  // لا نعرض المدير حتى يتم التهيئة
  if (!isInitialized) {
    return null;
  }

  return (
    <PermissionsCacheManager 
      userId={userId} 
      onCacheCleared={handleCacheCleared}
    />
  );
};

export default PermissionsCacheWrapper;

# دليل استخدام نظام الصلاحيات المحسن

## نظرة عامة

تم تطوير نظام صلاحيات محسن لحل مشكلة البطء في التحميل الناتجة عن التحقق من الصلاحيات لكل زر بشكل منفصل. النظام الجديد يتحقق من جميع الصلاحيات مرة واحدة عند تحميل الصفحة.

## المكونات الجديدة

### 1. PermissionsContext
Context مركزي لإدارة الصلاحيات مع تخزين مؤقت محسن.

### 2. PermissionGuard المحسن
يدعم التحقق من صلاحية واحدة أو عدة صلاحيات.

### 3. OptimizedActionButtonGroup
مجموعة أزرار تتحقق من جميع الصلاحيات مرة واحدة.

### 4. BulkPermissionGuard
للتحقق من عدة عناصر بصلاحيات مختلفة.

## أمثلة الاستخدام

### استخدام PermissionGuard المحسن

```tsx
import PermissionGuard from '@/components/admin/PermissionGuard';

// صلاحية واحدة
<PermissionGuard requiredPermission="admin.students.create">
  <button>إضافة طالب</button>
</PermissionGuard>

// عدة صلاحيات (يحتاج واحدة على الأقل)
<PermissionGuard 
  requiredPermissions={["admin.students.view", "admin.students.edit"]}
  requireAll={false}
>
  <div>محتوى الطلاب</div>
</PermissionGuard>

// عدة صلاحيات (يحتاج جميعها)
<PermissionGuard 
  requiredPermissions={["admin.students.edit", "admin.students.delete"]}
  requireAll={true}
>
  <button>إجراءات متقدمة</button>
</PermissionGuard>
```

### استخدام OptimizedActionButtonGroup

```tsx
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';

<OptimizedActionButtonGroup
  entityType="students"
  onEdit={() => handleEdit(student.id)}
  onDelete={() => handleDelete(student.id)}
  onView={() => handleView(student.id)}
  showEdit={true}
  showDelete={true}
  showView={true}
  // صلاحيات مخصصة (اختيارية)
  customPermissions={{
    edit: "admin.students.modify",
    delete: "admin.students.remove"
  }}
/>
```

### استخدام BulkPermissionGuard

```tsx
import BulkPermissionGuard from '@/components/admin/BulkPermissionGuard';

const items = [
  {
    key: 'add-student',
    permission: 'admin.students.create',
    component: <button onClick={handleAddStudent}>إضافة طالب</button>
  },
  {
    key: 'add-teacher',
    permission: 'admin.teachers.create',
    component: <button onClick={handleAddTeacher}>إضافة معلم</button>
  },
  {
    key: 'view-reports',
    permission: 'admin.reports.view',
    component: <button onClick={handleViewReports}>عرض التقارير</button>
  }
];

<BulkPermissionGuard 
  items={items}
  className="flex gap-2"
  wrapperComponent="div"
/>
```

### استخدام QuickActionButtons

```tsx
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

<QuickActionButtons
  entityType="students"
  actions={[
    {
      key: 'create',
      label: 'إضافة طالب',
      icon: <FaPlus />,
      onClick: handleAddStudent,
      variant: 'primary'
    },
    {
      key: 'export',
      label: 'تصدير البيانات',
      icon: <FaDownload />,
      onClick: handleExport,
      variant: 'secondary',
      permission: 'admin.students.export' // صلاحية مخصصة
    },
    {
      key: 'delete-all',
      label: 'حذف المحدد',
      icon: <FaTrash />,
      onClick: handleBulkDelete,
      variant: 'danger'
    }
  ]}
  className="mb-4"
/>
```

### استخدام useBulkPermissions Hook

```tsx
import { useBulkPermissions } from '@/components/admin/BulkPermissionGuard';

const MyComponent = () => {
  const permissions = useBulkPermissions([
    'admin.students.create',
    'admin.students.edit',
    'admin.students.delete',
    'admin.teachers.create'
  ]);

  return (
    <div>
      {permissions['admin.students.create'] && (
        <button>إضافة طالب</button>
      )}
      {permissions['admin.students.edit'] && (
        <button>تعديل طالب</button>
      )}
      {permissions['admin.teachers.create'] && (
        <button>إضافة معلم</button>
      )}
    </div>
  );
};
```

### استخدام OptimizedProtectedRoute

```tsx
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';

// حماية بصلاحية واحدة
<OptimizedProtectedRoute requiredPermission="admin.students.view">
  <StudentsPage />
</OptimizedProtectedRoute>

// حماية بعدة صلاحيات
<OptimizedProtectedRoute 
  requiredPermissions={["admin.reports.view", "admin.analytics.view"]}
  requireAll={false}
>
  <ReportsPage />
</OptimizedProtectedRoute>
```

## مقارنة الأداء

### النظام القديم
```tsx
// كل PermissionGuard يستدعي useUserPermissions منفصل
<PermissionGuard requiredPermission="admin.students.create">
  <button>إضافة</button>
</PermissionGuard>
<PermissionGuard requiredPermission="admin.students.edit">
  <button>تعديل</button>
</PermissionGuard>
<PermissionGuard requiredPermission="admin.students.delete">
  <button>حذف</button>
</PermissionGuard>
// = 3 استدعاءات منفصلة للتحقق من الصلاحيات
```

### النظام الجديد
```tsx
// استدعاء واحد للتحقق من جميع الصلاحيات
<OptimizedActionButtonGroup
  entityType="students"
  onEdit={handleEdit}
  onDelete={handleDelete}
  showEdit={true}
  showDelete={true}
/>
// = استدعاء واحد للتحقق من جميع الصلاحيات
```

## التحسينات المطبقة

1. **Context مركزي**: جلب الصلاحيات مرة واحدة عند تحميل التطبيق
2. **تخزين مؤقت محسن**: حفظ الصلاحيات لمدة 30 دقيقة
3. **Bulk Permission Checking**: التحقق من عدة صلاحيات مرة واحدة
4. **Memoization**: تحسين إعادة الحساب باستخدام useMemo و useCallback
5. **Lazy Loading**: عدم عرض العناصر أثناء التحميل

## الترحيل من النظام القديم

### خطوات الترحيل

1. **إضافة PermissionsProvider** في layout.tsx (تم بالفعل)
2. **استبدال PermissionGuard القديم** بالنسخة المحسنة
3. **استبدال ActionButtonGroup** بـ OptimizedActionButtonGroup
4. **استبدال ProtectedRoute** بـ OptimizedProtectedRoute

### مثال على الترحيل

```tsx
// قبل
import { ActionButtonGroup } from '@/components/admin/ActionButtons';
import PermissionGuard from '@/components/admin/PermissionGuard';

// بعد
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import PermissionGuard from '@/components/admin/PermissionGuard'; // نفس الاسم، محسن داخلياً
```

## نصائح للاستخدام الأمثل

1. **استخدم OptimizedActionButtonGroup** بدلاً من أزرار منفصلة
2. **استخدم BulkPermissionGuard** للعناصر المتعددة
3. **استخدم useBulkPermissions** للتحكم المخصص
4. **تجنب استخدام PermissionGuard** لكل زر منفصل في نفس المكون
5. **استخدم QuickActionButtons** للأزرار السريعة

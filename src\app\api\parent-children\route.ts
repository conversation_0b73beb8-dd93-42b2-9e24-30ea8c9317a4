import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// GET /api/parent-children - جلب قائمة أبناء ولي الأمر المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'PARENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات ولي الأمر
    // أولاً نحاول العثور على المستخدم مع الملف الشخصي
    const user = await prisma.user.findUnique({
      where: {
        id: userData.id
      },
      include: {
        profile: true
      }
    });

    if (!user || !user.profile) {
      console.error('لم يتم العثور على المستخدم أو الملف الشخصي:', userData.id);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المستخدم" },
        { status: 404 }
      );
    }

    // البحث عن ولي الأمر باستخدام اسم الملف الشخصي أو اسم المستخدم
    const parent = await prisma.parent.findFirst({
      where: {
        OR: [
          { name: user.profile.name },
          { name: userData.username }
        ]
      },
      include: {
        students: {
          include: {
            classe: true,
            attendance: {
              take: 10,
              orderBy: {
                date: 'desc'
              }
            },
            exam_points: {
              take: 5,
              orderBy: {
                createdAt: 'desc'
              },
              include: {
                exam: true,
                surah: true
              }
            },
            quranProgress: {
              take: 5,
              orderBy: {
                startDate: 'desc'
              },
              include: {
                surah: true
              }
            }
          }
        }
      }
    });

    if (!parent) {
      console.error('لم يتم العثور على ولي الأمر للمستخدم:', userData.username, 'مع الملف الشخصي:', user.profile.name);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات ولي الأمر" },
        { status: 404 }
      );
    }

    // التحقق من وجود أبناء
    if (!parent.students || parent.students.length === 0) {
      console.log('ولي الأمر موجود ولكن لا يوجد أبناء مسجلين:', parent.name);
      return NextResponse.json({
        children: [],
        message: "لا يوجد أبناء مسجلين لولي الأمر"
      });
    }

    // تنسيق البيانات للعرض
    const children = parent.students.map(student => {
      // حساب نسبة الحضور
      const totalAttendance = student.attendance.length;
      const presentAttendance = student.attendance.filter(a => a.status === 'PRESENT').length;
      const attendanceRate = totalAttendance > 0 ? Math.round((presentAttendance / totalAttendance) * 100) : 0;

      // حساب متوسط الدرجات
      const totalExams = student.exam_points.length;
      const totalPoints = student.exam_points.reduce((sum, exam) => sum + Number(exam.grade), 0);
      const averagePoints = totalExams > 0 ? Math.round(totalPoints / totalExams) : 0;

      // الامتحان القادم (افتراضي)
      const nextExam = {
        title: 'امتحان الشهر القادم',
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // بعد أسبوع
      };

      // آخر تقدم في حفظ القرآن
      const lastQuranProgress = student.quranProgress[0];

      return {
        id: student.id,
        name: student.name,
        age: student.age,
        grade: student.classe?.name || 'غير محدد',
        attendanceRate: attendanceRate,
        averagePoints: averagePoints,
        totalPoints: student.totalPoints,
        nextExam: nextExam,
        lastQuranProgress: lastQuranProgress ? {
          surahName: lastQuranProgress.surah.name,
          startVerse: lastQuranProgress.startVerse,
          endVerse: lastQuranProgress.endVerse,
          date: lastQuranProgress.startDate.toISOString()
        } : null,
        recentExams: student.exam_points.map(exam => ({
          id: exam.id,
          examType: exam.exam.evaluationType,
          surahName: exam.surah?.name || '',
          points: exam.grade,
          maxPoints: exam.exam.maxPoints,
          date: exam.createdAt.toISOString()
        }))
      };
    });

    return NextResponse.json({
      children,
      message: "تم جلب بيانات الأبناء بنجاح"
    });
  } catch (error) {
    console.error('Error fetching parent children:', error);
    // إضافة المزيد من التفاصيل للخطأ
    const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير معروف';
    return NextResponse.json(
      {
        message: "حدث خطأ أثناء جلب بيانات الأبناء",
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

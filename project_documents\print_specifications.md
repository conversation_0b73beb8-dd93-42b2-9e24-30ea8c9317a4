# مواصفات الطباعة لتقارير المشرف

## نظرة عامة
هذا المستند يحدد مواصفات ومتطلبات طباعة التقارير الأدبية والمالية مباشرة من المتصفح.

## المتطلبات العامة للطباعة

### إعدادات الصفحة
- **حجم الورق**: A4 (210 × 297 مم)
- **الاتجاه**: عمودي (Portrait) للتقارير الأدبية، مرن للتقارير المالية
- **الهوامش**: 
  - أعلى: 25 مم
  - أسفل: 25 مم
  - يمين: 20 مم
  - يسار: 20 مم

### الخطوط والتنسيق
- **الخط الأساسي**: Arial أو Helvetica للنصوص العربية
- **حجم الخط**: 
  - العناوين الرئيسية: 16pt
  - العناوين الفرعية: 14pt
  - النص العادي: 12pt
  - الجداول: 10pt
- **المسافات بين الأسطر**: 1.2
- **اتجاه النص**: من اليمين لليسار (RTL)

## CSS للطباعة

### القواعد الأساسية
```css
@media print {
  /* إعدادات الصفحة */
  @page {
    size: A4;
    margin: 25mm 20mm;
    
    /* رأس الصفحة */
    @top-center {
      content: "تقرير المشرف - " attr(data-report-title);
      font-size: 10pt;
      color: #666;
    }
    
    /* تذييل الصفحة */
    @bottom-right {
      content: "صفحة " counter(page) " من " counter(pages);
      font-size: 10pt;
      color: #666;
    }
    
    @bottom-left {
      content: "تاريخ الطباعة: " attr(data-print-date);
      font-size: 10pt;
      color: #666;
    }
  }
  
  /* إعدادات الجسم */
  body {
    font-family: Arial, sans-serif;
    font-size: 12pt;
    line-height: 1.2;
    color: #000;
    background: white;
    margin: 0;
    padding: 0;
  }
  
  /* إخفاء العناصر غير المطلوبة */
  .print\\:hidden,
  .no-print,
  nav,
  .sidebar,
  .toolbar,
  button:not(.print-button),
  .pagination {
    display: none !important;
  }
  
  /* تحسين الجداول */
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 10pt 0;
    page-break-inside: avoid;
  }
  
  th, td {
    border: 1pt solid #000;
    padding: 6pt;
    text-align: right;
    vertical-align: top;
  }
  
  th {
    background-color: #f0f0f0 !important;
    font-weight: bold;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  /* كسر الصفحات */
  .page-break {
    page-break-before: always;
  }
  
  .page-break-after {
    page-break-after: always;
  }
  
  .no-page-break {
    page-break-inside: avoid;
  }
  
  /* العناوين */
  h1, h2, h3 {
    page-break-after: avoid;
    margin-top: 20pt;
    margin-bottom: 10pt;
  }
  
  h1 {
    font-size: 16pt;
    font-weight: bold;
    text-align: center;
  }
  
  h2 {
    font-size: 14pt;
    font-weight: bold;
  }
  
  h3 {
    font-size: 12pt;
    font-weight: bold;
  }
  
  /* الفقرات */
  p {
    margin: 6pt 0;
    text-align: justify;
  }
  
  /* القوائم */
  ul, ol {
    margin: 6pt 0;
    padding-right: 20pt;
  }
  
  li {
    margin: 3pt 0;
  }
  
  /* الصور والرسوم البيانية */
  img, canvas {
    max-width: 100%;
    height: auto;
    page-break-inside: avoid;
  }
  
  /* التوقيع */
  .signature {
    margin-top: 30pt;
    text-align: left;
  }
  
  /* العلامة المائية */
  .watermark {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
    font-size: 72pt;
    color: rgba(0, 0, 0, 0.1);
    z-index: -1;
    pointer-events: none;
  }
}
```

## مواصفات التقرير الأدبي

### هيكل الطباعة
1. **الصفحة الأولى**:
   - رأس الجمعية والمكتب
   - عنوان التقرير
   - الفترة الزمنية
   - المقدمة

2. **الصفحات التالية**:
   - الإحصائيات العامة
   - تفاصيل الطلاب والمعلمين
   - تقدم القرآن الكريم
   - الأنشطة والفعاليات
   - الامتحانات والتقييمات

3. **الصفحة الأخيرة**:
   - الخاتمة
   - التوقيع والتاريخ

### قواعد كسر الصفحات
- تجنب كسر الجداول في منتصفها
- الحفاظ على العناوين مع محتواها
- بداية كل قسم رئيسي في صفحة جديدة

## مواصفات التقرير المالي

### هيكل الطباعة
1. **الصفحة الأولى**:
   - رأس التقرير المالي
   - الملخص التنفيذي
   - الرسوم البيانية الأساسية

2. **الصفحات التالية**:
   - تفاصيل المداخيل
   - تفاصيل المصروفات
   - إحصائيات طرق الدفع
   - التحليل الشهري

3. **الصفحة الأخيرة**:
   - الملاحظات والتوصيات
   - التوقيع والتاريخ

### تنسيق الأرقام
- استخدام الفواصل للآلاف
- عرض العملة بالدينار الجزائري
- تمييز الأرقام السالبة باللون الأحمر
- محاذاة الأرقام لليسار في الجداول

## JavaScript للطباعة

### وظيفة الطباعة الأساسية
```javascript
function printReport(reportTitle, reportDate) {
  // إعداد البيانات للطباعة
  document.body.setAttribute('data-report-title', reportTitle);
  document.body.setAttribute('data-print-date', reportDate);
  
  // إخفاء العناصر غير المطلوبة
  const elementsToHide = document.querySelectorAll('.no-print, .print\\:hidden');
  elementsToHide.forEach(el => el.style.display = 'none');
  
  // تطبيق أنماط الطباعة
  document.body.classList.add('printing');
  
  // طباعة
  window.print();
  
  // إعادة العناصر بعد الطباعة
  setTimeout(() => {
    elementsToHide.forEach(el => el.style.display = '');
    document.body.classList.remove('printing');
  }, 1000);
}
```

### معالجة الأحداث
```javascript
// قبل الطباعة
window.addEventListener('beforeprint', () => {
  console.log('بدء الطباعة...');
  // تحسين التخطيط للطباعة
  optimizeLayoutForPrint();
});

// بعد الطباعة
window.addEventListener('afterprint', () => {
  console.log('انتهاء الطباعة');
  // إعادة التخطيط العادي
  restoreNormalLayout();
});
```

## تحسينات الأداء

### تحميل الخطوط
```css
@font-face {
  font-family: 'ArabicFont';
  src: url('/fonts/arabic-font.woff2') format('woff2');
  font-display: swap;
}
```

### ضغط الصور
- استخدام صور محسنة للطباعة
- تقليل حجم الملفات
- استخدام تنسيقات مناسبة (PNG للشعارات، JPEG للصور)

## اختبار الطباعة

### متصفحات مختلفة
- Chrome: دعم ممتاز لـ CSS الطباعة
- Firefox: دعم جيد مع بعض القيود
- Safari: دعم محدود لبعض الميزات
- Edge: دعم جيد ومتوافق مع Chrome

### أحجام ورق مختلفة
- A4: الحجم الافتراضي
- Letter: للأسواق الأمريكية
- Legal: للمستندات القانونية

### اختبار الجودة
- وضوح النصوص
- محاذاة الجداول
- كسر الصفحات المناسب
- عرض الألوان (إن وجدت)

## معالجة المشاكل الشائعة

### مشاكل الخطوط
- استخدام خطوط احتياطية
- تحميل الخطوط محلياً
- اختبار عرض النصوص العربية

### مشاكل التخطيط
- تجنب العناصر المطلقة الموضع
- استخدام وحدات مناسبة (pt, mm)
- اختبار كسر الصفحات

### مشاكل الأداء
- تحسين حجم المحتوى
- تقليل عدد العناصر
- استخدام CSS مبسط للطباعة

## إرشادات المستخدم

### قبل الطباعة
1. التأكد من اتصال الطابعة
2. اختيار إعدادات الطباعة المناسبة
3. معاينة التقرير قبل الطباعة
4. التحقق من توفر الورق والحبر

### إعدادات الطابعة المقترحة
- جودة الطباعة: عالية
- نوع الورق: عادي أو مكتبي
- الألوان: أبيض وأسود (لتوفير الحبر)
- الهوامش: افتراضية أو مخصصة

### نصائح للحصول على أفضل نتائج
- استخدام ورق عالي الجودة للتقارير المهمة
- التأكد من نظافة الطابعة
- اختبار طباعة صفحة واحدة أولاً
- حفظ نسخة إلكترونية كنسخة احتياطية

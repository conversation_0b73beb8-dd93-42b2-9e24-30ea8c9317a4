'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Download, Printer, FileText, Users, BookOpen, Activity, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { LiteraryReportData, ReportOptions } from '@/lib/reports/literary-report-service';

export default function LiteraryReportPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [reportData, setReportData] = useState<LiteraryReportData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [reportOptions, setReportOptions] = useState<ReportOptions>({
    includeGeneralInfo: true,
    includeStudentStats: true,
    includeQuranProgress: true,
    includeActivities: true,
    includeTrainingCourses: true,
    includeParticipations: true,
  });

  const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : new Date();
  const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : new Date();

  // معلومات المكتب البلدي من المعاملات
  const officeInfo = {
    organizationName: searchParams.get('organizationName') || 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
    officeName: searchParams.get('officeName') || 'المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر',
    branchName: searchParams.get('branchName') || 'شعبة بلدية المنقر',
    presidentName: searchParams.get('presidentName') || 'الوليد بن ناصر قصي',
    presidentTitle: searchParams.get('presidentTitle') || 'رئيس المكتب البلدي',
    logoUrl: searchParams.get('logoUrl') || '',
  };

  useEffect(() => {
    fetchReportData();
  }, [searchParams]);

  const fetchReportData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/reports/literary?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
      );

      if (response.ok) {
        const result = await response.json();
        setReportData(result.data);
      } else {
        console.error('فشل في جلب بيانات التقرير');
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات التقرير:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async (exportFormat: 'pdf' | 'word') => {
    if (!reportData) {
      console.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      if (exportFormat === 'pdf') {
        // استخدام exportToPdf من utils/export-utils.ts مباشرة في المتصفح

        // إعداد بيانات الجداول
        const generalStatsData = [
          ['إجمالي الطلاب', reportData.generalStats.totalStudents.toString()],
          ['إجمالي المعلمين', reportData.generalStats.totalTeachers.toString()],
          ['إجمالي الفصول', reportData.generalStats.totalClasses.toString()],
          ['الحفاظ', reportData.generalStats.totalMemorizers.toString()],
          ['مجالس الختم', reportData.generalStats.totalKhatmSessions.toString()],
          ['الأنشطة', reportData.generalStats.totalActivities.toString()],
        ];

        const classesData = reportData.studentsDetails.classes.map(classe => [
          classe.name,
          classe.studentsCount.toString(),
          classe.capacity.toString(),
          classe.capacity > 0 ? `${Math.round((classe.studentsCount / classe.capacity) * 100)}%` : '0%'
        ]);

        const quranStatsData = [
          ['عدد الحفاظ', reportData.quranDetails.memorizers.toString()],
          ['متوسط درجات الحفظ', reportData.quranDetails.averageMemorization.toFixed(1)],
          ['متوسط درجات التجويد', reportData.quranDetails.averageTajweed.toFixed(1)],
          ['إجمالي سجلات التقدم', reportData.quranDetails.totalProgress.toString()],
        ];

        const activitiesData = [
          ['إجمالي الأنشطة', reportData.activitiesDetails.total.toString()],
          ['مجالس الختم', reportData.khatmDetails.total.toString()],
          ['إجمالي الامتحانات', reportData.examsDetails.total.toString()],
        ];

        const examsData = reportData.examsDetails.exams.slice(0, 10).map(exam => [
          exam.description,
          exam.studentsCount.toString(),
          exam.averageGrade.toFixed(1),
          exam.passedStudents.toString()
        ]);

        // استيراد وتشغيل exportToPdf
        const { exportToPdf } = await import('@/utils/export-utils');

        exportToPdf({
          title: `${officeInfo.organizationName}\n${officeInfo.officeName}\n${officeInfo.branchName}\nالتقرير الأدبي`,
          fileName: `التقرير_الأدبي_${new Date().toISOString().split('T')[0]}.pdf`,
          tables: [
            {
              title: 'الإحصائيات العامة',
              headers: ['البيان', 'العدد'],
              data: generalStatsData,
              headStyles: {
                fillColor: [59, 130, 246],
                textColor: [255, 255, 255]
              }
            },
            {
              title: 'توزيع الطلاب على الفصول',
              headers: ['الفصل', 'عدد الطلاب', 'السعة', 'نسبة الإشغال'],
              data: classesData,
              headStyles: {
                fillColor: [16, 185, 129],
                textColor: [255, 255, 255]
              }
            },
            {
              title: 'إحصائيات القرآن الكريم',
              headers: ['البيان', 'القيمة'],
              data: quranStatsData,
              headStyles: {
                fillColor: [139, 69, 19],
                textColor: [255, 255, 255]
              }
            },
            {
              title: 'الأنشطة والفعاليات',
              headers: ['البيان', 'القيمة'],
              data: activitiesData,
              headStyles: {
                fillColor: [168, 85, 247],
                textColor: [255, 255, 255]
              }
            },
            ...(examsData.length > 0 ? [{
              title: 'الامتحانات والتقييمات',
              headers: ['الامتحان', 'عدد الطلاب', 'المتوسط', 'الناجحون'],
              data: examsData,
              headStyles: {
                fillColor: [245, 158, 11],
                textColor: [255, 255, 255]
              }
            }] : [])
          ],
          additionalContent: [
            {
              text: `للفترة من ${format(startDate, 'PPP', { locale: ar })} إلى ${format(endDate, 'PPP', { locale: ar })}`,
              x: 105,
              y: 30,
              options: { align: 'center' }
            },
            {
              text: `تقرير يوم ${format(new Date(), 'PPP', { locale: ar })}\nعن ${officeInfo.presidentTitle}: ${officeInfo.presidentName}`,
              x: 105,
              y: 280,
              options: { align: 'center' }
            }
          ]
        });

      } else if (exportFormat === 'word') {
        // تصدير Word عبر API
        const response = await fetch('/api/reports/literary/export', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            format: 'word',
            options: {
              ...reportOptions,
              organizationName: officeInfo.organizationName,
              officeName: officeInfo.officeName,
              branchName: officeInfo.branchName,
              presidentName: officeInfo.presidentName,
              presidentTitle: officeInfo.presidentTitle,
              logoUrl: officeInfo.logoUrl,
            },
          }),
        });

        if (response.ok) {
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `التقرير_الأدبي_${new Date().toISOString().split('T')[0]}.html`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } else {
          console.error('فشل في تصدير التقرير');
        }
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير:', error);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleBack = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل التقرير...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="text-center">
          <p className="text-red-600">فشل في تحميل بيانات التقرير</p>
          <Button onClick={handleBack} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* شريط الأدوات */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm border print:hidden">
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Badge variant="secondary">
            <Calendar className="h-3 w-3 mr-1" />
            {format(startDate, 'PPP', { locale: ar })} - {format(endDate, 'PPP', { locale: ar })}
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            تصدير PDF
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExport('word')}>
            <FileText className="h-4 w-4 mr-2" />
            تصدير Word
          </Button>
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            طباعة
          </Button>
        </div>
      </div>

      {/* خيارات التخصيص */}
      <Card className="print:hidden">
          <CardHeader>
            <CardTitle>⚙️ خيارات التقرير</CardTitle>
            <CardDescription>اختر الأقسام التي تريد تضمينها في التقرير</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="generalInfo"
                  checked={reportOptions.includeGeneralInfo}
                  onCheckedChange={(checked) =>
                    setReportOptions(prev => ({ ...prev, includeGeneralInfo: checked as boolean }))
                  }
                />
                <label htmlFor="generalInfo" className="text-sm font-medium">
                  معلومات عامة
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="studentStats"
                  checked={reportOptions.includeStudentStats}
                  onCheckedChange={(checked) =>
                    setReportOptions(prev => ({ ...prev, includeStudentStats: checked as boolean }))
                  }
                />
                <label htmlFor="studentStats" className="text-sm font-medium">
                  إحصائيات الطلاب
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="quranProgress"
                  checked={reportOptions.includeQuranProgress}
                  onCheckedChange={(checked) =>
                    setReportOptions(prev => ({ ...prev, includeQuranProgress: checked as boolean }))
                  }
                />
                <label htmlFor="quranProgress" className="text-sm font-medium">
                  تقدم القرآن
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="activities"
                  checked={reportOptions.includeActivities}
                  onCheckedChange={(checked) =>
                    setReportOptions(prev => ({ ...prev, includeActivities: checked as boolean }))
                  }
                />
                <label htmlFor="activities" className="text-sm font-medium">
                  الأنشطة
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="trainingCourses"
                  checked={reportOptions.includeTrainingCourses}
                  onCheckedChange={(checked) =>
                    setReportOptions(prev => ({ ...prev, includeTrainingCourses: checked as boolean }))
                  }
                />
                <label htmlFor="trainingCourses" className="text-sm font-medium">
                  الدورات التكوينية
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="participations"
                  checked={reportOptions.includeParticipations}
                  onCheckedChange={(checked) =>
                    setReportOptions(prev => ({ ...prev, includeParticipations: checked as boolean }))
                  }
                />
                <label htmlFor="participations" className="text-sm font-medium">
                  المشاركات
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

      {/* معاينة التقرير */}
      <div className="bg-white rounded-lg shadow-sm border p-8 print:shadow-none print:border-none">
        {/* رأس التقرير */}
        <div className="text-center mb-8 border-b pb-6">
          {officeInfo.logoUrl && (
            <div className="flex justify-center mb-4">
              <img
                src={officeInfo.logoUrl}
                alt="شعار الجمعية"
                className="h-20 w-20 object-contain"
              />
            </div>
          )}
          <h1 className="text-2xl font-bold mb-2">{officeInfo.organizationName}</h1>
          <h2 className="text-xl mb-2">{officeInfo.officeName}</h2>
          <h3 className="text-lg mb-4">{officeInfo.branchName}</h3>
          <h2 className="text-xl font-bold mb-2">التقريــر الأدبــــــــي لـ{officeInfo.branchName}</h2>
          <p className="text-gray-600">
            للفترة من: {format(startDate, 'PPP', { locale: ar })} إلى: {format(endDate, 'PPP', { locale: ar })}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            تاريخ إنشاء التقرير: {format(new Date(), 'PPP', { locale: ar })}
          </p>
        </div>

        {/* المقدمة */}
        {reportOptions.includeGeneralInfo && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-4">* مقدمة:</h3>
            <p className="text-gray-700 leading-relaxed mb-4">
              بناء على القرار المؤرخ في صفر 1436هـ الموافق لـ ديسمبر 2014م الذي يُحدد دفتر الشروط المتعلق بإنشاء المؤسسات التعليمية والنوادي والمعاهد التابعة لجمعية العلماء المسلمين الجزائريين ومراقبتها.
            </p>
            <p className="text-gray-700 leading-relaxed mb-4">
              - وعملا بقانون الجمعيات رقم 12 - 06، وتطبيقا للمادتين 05 و 58 من القانون الأساسي.
            </p>
            <p className="text-gray-700 leading-relaxed mb-4">
              - ووفقا للقانون المنظم للعملية التربوية لاسيما المادة 12 المحددة لمهام مدير المدرسة وواجباته.
            </p>
            <p className="text-gray-700 leading-relaxed">
              نَعرض على أسماعكم المحترمة الأعمال التالية التي قامت بها الجمعية بهياكلها أو من يمثلها نيابة عن أعضائها ومن ذلك:
            </p>
          </div>
        )}

        {/* الإحصائيات العامة */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">* الإحصائيات العامة:</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg text-center">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-900">
                {reportData.generalStats.totalStudents}
              </div>
              <div className="text-sm text-blue-700">إجمالي الطلاب</div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg text-center">
              <BookOpen className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-900">
                {reportData.generalStats.totalMemorizers}
              </div>
              <div className="text-sm text-green-700">الحفاظ</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg text-center">
              <Activity className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-900">
                {reportData.generalStats.totalActivities}
              </div>
              <div className="text-sm text-purple-700">الأنشطة</div>
            </div>
          </div>
        </div>

        {/* تفاصيل الطلاب */}
        {reportOptions.includeStudentStats && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-4">1/ الجانب التربوي:</h3>
            <p className="text-gray-700 mb-4">
              يبلغ إجمالي عدد الطلاب المسجلين في الشعبة <strong>{reportData.studentsDetails.total}</strong> طالباً وطالبة،
              موزعين على <strong>{reportData.studentsDetails.classes.length}</strong> صفوف دراسية.
            </p>

            <div className="overflow-x-auto mb-4">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 p-2 text-right">الصف</th>
                    <th className="border border-gray-300 p-2 text-center">عدد الطلاب</th>
                    <th className="border border-gray-300 p-2 text-center">السعة</th>
                    <th className="border border-gray-300 p-2 text-center">نسبة الإشغال</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData.studentsDetails.classes.map((classe) => (
                    <tr key={classe.id}>
                      <td className="border border-gray-300 p-2">{classe.name}</td>
                      <td className="border border-gray-300 p-2 text-center">{classe.studentsCount}</td>
                      <td className="border border-gray-300 p-2 text-center">{classe.capacity}</td>
                      <td className="border border-gray-300 p-2 text-center">
                        {classe.capacity > 0 ? Math.round((classe.studentsCount / classe.capacity) * 100) : 0}%
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <p className="text-gray-700">
              معدل الحضور العام: <strong>{reportData.attendanceDetails.attendanceRate.toFixed(1)}%</strong>
            </p>
          </div>
        )}

        {/* تقدم القرآن */}
        {reportOptions.includeQuranProgress && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-4">2/ الجانب القرآني:</h3>
            <p className="text-gray-700 mb-4">
              حققنا هذا الموسم أفضل نسب حفظ للقرآن الكريم حيث بلغ عدد الحفاظ <strong>{reportData.quranDetails.memorizers}</strong> طالباً،
              بمتوسط درجات حفظ <strong>{reportData.quranDetails.averageMemorization.toFixed(1)}</strong> ومتوسط درجات تجويد <strong>{reportData.quranDetails.averageTajweed.toFixed(1)}</strong>.
            </p>

            <p className="text-gray-700 mb-4">
              إجمالي سجلات التقدم في الحفظ: <strong>{reportData.quranDetails.totalProgress}</strong> سجل.
            </p>

            <p className="text-gray-700">
              وقد أقمنا <strong>{reportData.khatmDetails.total}</strong> مجلس ختم خلال هذه الفترة،
              بمشاركة إجمالية بلغت {reportData.khatmDetails.sessions.reduce((sum, session) => sum + session.attendeesCount, 0)} مشارك.
            </p>
          </div>
        )}

        {/* الأنشطة */}
        {reportOptions.includeActivities && (
          <div className="mb-8">
            <h3 className="text-lg font-bold mb-4">3/ الأنشطة والفعاليات:</h3>
            <p className="text-gray-700 mb-4">
              تم تنظيم <strong>{reportData.activitiesDetails.total}</strong> نشاط وفعالية خلال الفترة المحددة، شملت:
            </p>

            <ul className="list-disc list-inside text-gray-700 space-y-2 mr-4">
              <li>محاضرات ودروس للرجال والنساء</li>
              <li>دورات تكوينية في العلوم الشرعية واللغة العربية</li>
              <li>مسابقات قرآنية ومسابقة المواظبة على صلاة الصبح</li>
              <li>رحلات ترفيهية ومخيمات تعليمية</li>
              <li>حفلات تكريم وختم القرآن الكريم</li>
            </ul>
          </div>
        )}

        {/* الامتحانات والتقييمات */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">4/ الامتحانات والتقييمات:</h3>
          <p className="text-gray-700 mb-4">
            تم إجراء <strong>{reportData.examsDetails.total}</strong> امتحان وتقييم خلال الفترة المحددة.
          </p>

          {reportData.examsDetails.exams.length > 0 && (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 p-2 text-right">الامتحان</th>
                    <th className="border border-gray-300 p-2 text-center">عدد الطلاب</th>
                    <th className="border border-gray-300 p-2 text-center">المتوسط</th>
                    <th className="border border-gray-300 p-2 text-center">الناجحون</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData.examsDetails.exams.slice(0, 5).map((exam) => (
                    <tr key={exam.id}>
                      <td className="border border-gray-300 p-2">{exam.description}</td>
                      <td className="border border-gray-300 p-2 text-center">{exam.studentsCount}</td>
                      <td className="border border-gray-300 p-2 text-center">{exam.averageGrade.toFixed(1)}</td>
                      <td className="border border-gray-300 p-2 text-center">{exam.passedStudents}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* الخاتمة */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">* الخاتمة:</h3>
          <p className="text-gray-700 leading-relaxed">
            هذا ما تيسر عرضه من أعمال الشعبة خلال الفترة المحددة، ونسأل الله التوفيق والسداد في خدمة كتاب الله وسنة رسوله صلى الله عليه وسلم.
          </p>
        </div>

        {/* التوقيع */}
        <div className="text-left mt-12">
          <p className="mb-2">تقرير يوم {format(new Date(), 'PPP', { locale: ar })}</p>
          <p className="mb-8">عن {officeInfo.presidentTitle}:</p>
          <p className="font-bold">{officeInfo.presidentName}</p>
        </div>
      </div>
    </div>
  );
}

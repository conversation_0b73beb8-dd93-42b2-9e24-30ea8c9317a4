import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { teacherId, subjectId } = body;

    if (!teacherId || !subjectId || typeof teacherId !== 'number' || typeof subjectId !== 'number') {
      return NextResponse.json(
        { message: "يجب توفير معرف المعلم ومعرف المادة بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المعلم والمادة
    const [teacher, subject] = await Promise.all([
      prisma.teacher.findUnique({ where: { id: teacherId } }),
      prisma.subject.findUnique({ where: { id: subjectId } }),
    ]);

    if (!teacher) {
      return NextResponse.json(
        { message: "المعلم غير موجود" },
        { status: 404 }
      );
    }

    if (!subject) {
      return NextResponse.json(
        { message: "المادة غير موجودة" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود نفس العلاقة مسبقاً
    const existingTeacherSubject = await prisma.teacherSubject.findFirst({
      where: {
        teacherId,
        subjectId,
      },
    });

    if (existingTeacherSubject) {
      return NextResponse.json(
        { message: "هذا المعلم مسجل مسبقاً لهذه المادة" },
        { status: 400 }
      );
    }

    // إنشاء العلاقة بين المعلم والمادة
    const teacherSubject = await prisma.teacherSubject.create({
      data: {
        teacherId,
        subjectId,
      },
      include: {
        teacher: true,
        subject: true
      },
    });

    return NextResponse.json(teacherSubject, { status: 201 });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء معالجة الطلب" },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const teacherSubjects = await prisma.teacherSubject.findMany({
      include: {
        teacher: true,
        subject: true
      },
    });

    return NextResponse.json(teacherSubjects);
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء استرجاع البيانات" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const numericId = id ? parseInt(id) : null;

    if (!id || !numericId || isNaN(numericId)) {
      return NextResponse.json(
        { message: "يجب توفير معرف العلاقة بتنسيق صحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود العلاقة
    const teacherSubject = await prisma.teacherSubject.findUnique({
      where: { id: numericId },
    });

    if (!teacherSubject) {
      return NextResponse.json(
        { message: "العلاقة غير موجودة" },
        { status: 404 }
      );
    }

    await prisma.teacherSubject.delete({
      where: { id: numericId },
    });

    return NextResponse.json(
      { message: "تم حذف العلاقة بنجاح" },
      { status: 200 }
    );
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف البيانات" },
      { status: 500 }
    );
  }
}
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(_request: Request, { params }: { params: { id: string } }) {
  try {
    // استخدام await للتأكد من أن params جاهزة
    const classId = await Promise.resolve(params.id);

    // التحقق من وجود القسم وجلب بياناته مع الطلاب المرتبطين به
    const classData = await prisma.classe.findUnique({
      where: { id: parseInt(classId) },
      include: {
        students: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    });

    if (!classData) {
      return NextResponse.json(
        { error: 'القسم غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(classData);
  } catch (error) {
    console.error('Error fetching class data:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات القسم' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const classId = parseInt(params.id);
    if (isNaN(classId)) {
      return NextResponse.json(
        { error: 'معرف القسم غير صالح' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name, teacherId } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'اسم القسم مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود القسم
    const existingClass = await prisma.classe.findUnique({
      where: { id: classId }
    });

    if (!existingClass) {
      return NextResponse.json(
        { error: 'القسم غير موجود' },
        { status: 404 }
      );
    }

    // تحديث القسم
    const updateData: Record<string, unknown> = { name };

    // إضافة ربط المعلم بالمادة إذا تم توفيره
    if (teacherId) {
      updateData.classSubjects = {
        connect: { id: teacherId }
      };
    }

    const updatedClass = await prisma.classe.update({
      where: { id: classId },
      data: updateData,
      include: {
        students: true,
        classSubjects: {
          include: {
            teacherSubject: {
              include: {
                teacher: true,
                subject: true
              }
            }
          }
        },
      },
    });

    return NextResponse.json(updatedClass);
  } catch (error) {
    console.error('Error updating class:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث القسم' },
      { status: 500 }
    );
  }
}

export async function DELETE(_request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const classId = parseInt(params.id);
    if (isNaN(classId)) {
      return NextResponse.json(
        { error: 'معرف القسم غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من وجود القسم
    const existingClass = await prisma.classe.findUnique({
      where: { id: classId }
    });

    if (!existingClass) {
      return NextResponse.json(
        { error: 'القسم غير موجود' },
        { status: 404 }
      );
    }

    // حذف القسم
    await prisma.classe.delete({
      where: { id: classId },
    });

    return NextResponse.json({ message: 'تم حذف القسم بنجاح' });
  } catch (error) {
    console.error('Error deleting class:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف القسم' },
      { status: 500 }
    );
  }
}
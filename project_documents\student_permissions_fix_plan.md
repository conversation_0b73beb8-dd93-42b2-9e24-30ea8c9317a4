# خطة إصلاح صلاحيات إدارة التلاميذ

## وصف المشكلة
في صفحة إدارة التلاميذ، عند الدخول كمدير (ADMIN) تعمل الصلاحيات بشكل كامل وتظهر جميع الأزرار، ولكن عند الدخول كموظف لديه الصلاحيات المطلوبة لا تظهر أزرار:
- إضافة تلميذ جديد
- استيراد البيانات
- تصدير البيانات

## تحليل المشكلة

### الصلاحيات المطلوبة
1. **زر إضافة التلميذ**: `admin.students.create`
2. **زر الاستيراد**: `admin.students.create`
3. **زر التصدير**: `admin.reports.view`

### السبب الجذري
- المدير (ADMIN) يحصل على جميع الصلاحيات تلقائياً في نظام الصلاحيات
- الموظفون يحتاجون إلى صلاحيات مُعرَّفة ومُربوطة بأدوارهم في قاعدة البيانات
- قد تكون الصلاحيات غير موجودة في قاعدة البيانات أو غير مُربوطة بأدوار الموظفين

## خطة العمل التفصيلية

### [x] **T01.01: فحص الصلاحيات الموجودة في قاعدة البيانات**
- **الحالة:** مُنجزة
- **المكونات:** قاعدة البيانات، جدول Permission
- **الاعتماديات:** لا يوجد
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم فحص ملف `prisma/seeds/permissions.ts` ووُجد أن الصلاحية المُعرَّفة هي `admin.students.add` وليس `admin.students.create`

### [x] **T01.02: تحديد السبب الجذري للمشكلة**
- **الحالة:** مُنجزة
- **المكونات:** صفحة إدارة التلاميذ، ملف الصلاحيات
- **الاعتماديات:** T01.01
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم اكتشاف أن المشكلة في عدم تطابق أسماء الصلاحيات بين الكود وملف الصلاحيات

### [x] **T01.03: تصحيح أسماء الصلاحيات في صفحة التلاميذ**
- **الحالة:** مُنجزة
- **المكونات:** `src/app/admin/students/page.tsx`
- **الاعتماديات:** T01.02
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم تغيير:
  - زر إضافة التلميذ: `key: 'create'` إلى `key: 'add'` (سيستخدم `admin.students.add`)
  - زر تنزيل القالب: `permission: 'admin.students.create'` إلى `permission: 'admin.students.add'`
  - زر الاستيراد: `permission: 'admin.students.create'` إلى `permission: 'admin.students.add'`

### [x] **T01.04: التأكد من ربط الصلاحيات بأدوار الموظفين**
- **الحالة:** مُنجزة
- **المكونات:** قاعدة البيانات، جدول RolePermission
- **الاعتماديات:** T01.03
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم التأكد من أن الموظفين يمكنهم الحصول على الصلاحيات المطلوبة من خلال صفحة إدارة الأدوار

### [x] **T01.05: اختبار عمل الصلاحيات للموظفين**
- **الحالة:** مُنجزة
- **المكونات:** صفحة إدارة التلاميذ، نظام الصلاحيات
- **الاعتماديات:** T01.04
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم تأكيد المستخدم أن الأزرار تعمل الآن بشكل صحيح

## الملفات المتأثرة
- `src/app/admin/students/page.tsx` - صفحة إدارة التلاميذ
- `src/components/admin/BulkPermissionGuard.tsx` - مكون QuickActionButtons
- `src/contexts/PermissionsContext.tsx` - سياق الصلاحيات
- `src/app/api/auth/permissions/route.ts` - API الصلاحيات
- قاعدة البيانات - جداول Permission, Role, RolePermission

## الصلاحيات المطلوبة للإصلاح
- `admin.students.create` - لإضافة واستيراد التلاميذ
- `admin.reports.view` - لتصدير البيانات

## ملاحظات إضافية
- يجب التأكد من أن نظام الصلاحيات يعمل بشكل صحيح للمدير والموظفين
- يجب اختبار الحل مع مستخدمين مختلفين (مدير وموظف)
- يجب التأكد من عدم تأثير التغييرات على أجزاء أخرى من النظام

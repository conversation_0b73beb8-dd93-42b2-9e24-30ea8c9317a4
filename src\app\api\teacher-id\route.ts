import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'TEACHER') {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const userId = userData.id;

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userId
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المعلم", success: false },
        { status: 404 }
      );
    }

    return NextResponse.json({
      teacherId: teacher.id,
      name: teacher.name,
      success: true
    });
  } catch (error) {
    console.error('Error fetching teacher ID:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب معرف المعلم", success: false },
      { status: 500 }
    );
  }
}

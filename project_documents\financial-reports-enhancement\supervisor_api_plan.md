# خطة بناء API جديد لتقارير المشرف

## نظرة عامة
بناء API جديد ومنفصل تحت مسار `/admin/supervisor-reports` مخصص لتقارير المشرف مع ميزات متقدمة ومحسنة.

## هيكل API الجديد

### المسار الأساسي
```
/api/admin/supervisor-reports/
```

### Endpoints المطلوبة

#### 1. التقارير المالية المحسنة
```
GET /api/admin/supervisor-reports/financial
POST /api/admin/supervisor-reports/financial/export
GET /api/admin/supervisor-reports/financial/quick-stats
GET /api/admin/supervisor-reports/financial/trends
GET /api/admin/supervisor-reports/financial/comparison
```

#### 2. التقارير الأدبية
```
GET /api/admin/supervisor-reports/literary
POST /api/admin/supervisor-reports/literary/export
GET /api/admin/supervisor-reports/literary/summary
```

#### 3. التقارير المدمجة
```
GET /api/admin/supervisor-reports/combined
POST /api/admin/supervisor-reports/combined/export
```

#### 4. الإحصائيات السريعة
```
GET /api/admin/supervisor-reports/dashboard-stats
GET /api/admin/supervisor-reports/alerts
```

## مواصفات API التقارير المالية المحسنة

### GET /api/admin/supervisor-reports/financial

#### المعاملات (Query Parameters)
```typescript
interface FinancialReportParams {
  startDate: string;           // تاريخ البداية (ISO 8601)
  endDate: string;             // تاريخ النهاية (ISO 8601)
  type?: 'all' | 'payments' | 'donations' | 'expenses' | 'incomes';
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
  includeComparison?: boolean; // مقارنة بالفترة السابقة
  includeForecast?: boolean;   // تضمين التنبؤات
  includeAlerts?: boolean;     // تضمين التنبيهات
  format?: 'json' | 'summary'; // تنسيق الاستجابة
}
```

#### مثال على الطلب
```
GET /api/admin/supervisor-reports/financial?startDate=2024-01-01&endDate=2024-12-31&type=all&groupBy=month&includeComparison=true&includeForecast=true
```

#### هيكل الاستجابة المحسنة
```typescript
interface SupervisorFinancialReport {
  // معلومات أساسية
  metadata: {
    generatedAt: string;
    period: {
      startDate: string;
      endDate: string;
      duration: number; // بالأيام
    };
    filters: FinancialReportParams;
    version: string;
  };

  // الملخص التنفيذي
  executiveSummary: {
    currentPeriod: {
      totalIncome: number;
      totalExpenses: number;
      netProfit: number;
      profitMargin: number;
      transactionCount: number;
    };
    previousPeriod?: {
      totalIncome: number;
      totalExpenses: number;
      netProfit: number;
      profitMargin: number;
      transactionCount: number;
    };
    comparison?: {
      incomeChange: number;      // نسبة التغيير
      expenseChange: number;
      profitChange: number;
      marginChange: number;
      transactionChange: number;
    };
  };

  // المؤشرات الرئيسية (KPIs)
  kpis: {
    averageTransactionValue: number;
    monthlyGrowthRate: number;
    expenseRatio: number;
    donationRatio: number;
    paymentMethodEfficiency: Array<{
      methodName: string;
      efficiency: number;
      volume: number;
    }>;
  };

  // البيانات التفصيلية
  detailedData: {
    incomeBreakdown: {
      studentPayments: {
        total: number;
        count: number;
        average: number;
        trend: 'up' | 'down' | 'stable';
        details: Array<PaymentDetail>;
      };
      donations: {
        total: number;
        count: number;
        average: number;
        trend: 'up' | 'down' | 'stable';
        details: Array<DonationDetail>;
      };
      otherIncomes: {
        total: number;
        count: number;
        average: number;
        trend: 'up' | 'down' | 'stable';
        details: Array<IncomeDetail>;
      };
    };
    
    expenseBreakdown: {
      byCategory: Array<{
        categoryName: string;
        total: number;
        count: number;
        percentage: number;
        trend: 'up' | 'down' | 'stable';
        details: Array<ExpenseDetail>;
      }>;
      byMonth: Array<{
        month: string;
        total: number;
        count: number;
        categories: Array<{
          name: string;
          amount: number;
        }>;
      }>;
    };
  };

  // التحليل الزمني
  timeSeriesAnalysis: {
    monthly: Array<{
      period: string;
      income: number;
      expenses: number;
      netProfit: number;
      transactionCount: number;
      growthRate: number;
    }>;
    trends: {
      incomeDirection: 'increasing' | 'decreasing' | 'stable';
      expenseDirection: 'increasing' | 'decreasing' | 'stable';
      profitDirection: 'increasing' | 'decreasing' | 'stable';
      seasonality: Array<{
        month: number;
        factor: number;
      }>;
    };
  };

  // التنبؤات (إذا تم طلبها)
  forecasting?: {
    nextPeriod: {
      predictedIncome: number;
      predictedExpenses: number;
      predictedProfit: number;
      confidence: number; // نسبة الثقة
    };
    recommendations: Array<{
      type: 'income' | 'expense' | 'general';
      priority: 'high' | 'medium' | 'low';
      message: string;
      impact: number;
    }>;
  };

  // التنبيهات (إذا تم طلبها)
  alerts?: Array<{
    id: string;
    type: 'warning' | 'info' | 'critical';
    category: 'budget' | 'trend' | 'anomaly';
    title: string;
    message: string;
    value: number;
    threshold: number;
    createdAt: string;
  }>;

  // الرسوم البيانية
  chartData: {
    incomeVsExpenses: ChartDataset;
    profitTrend: ChartDataset;
    paymentMethods: ChartDataset;
    expenseCategories: ChartDataset;
    monthlyComparison: ChartDataset;
  };
}
```

### GET /api/admin/supervisor-reports/financial/quick-stats

#### الغرض
إحصائيات سريعة للوحة التحكم بدون تفاصيل كثيرة

#### الاستجابة
```typescript
interface QuickFinancialStats {
  currentBalance: number;
  todayIncome: number;
  todayExpenses: number;
  monthToDateIncome: number;
  monthToDateExpenses: number;
  lastTransactionTime: string;
  alertsCount: number;
  trendsIndicator: {
    income: 'up' | 'down' | 'stable';
    expenses: 'up' | 'down' | 'stable';
    profit: 'up' | 'down' | 'stable';
  };
}
```

### GET /api/admin/supervisor-reports/financial/trends

#### المعاملات
```typescript
interface TrendsParams {
  period: 'week' | 'month' | 'quarter' | 'year';
  metric: 'income' | 'expenses' | 'profit' | 'all';
  includeForecasting?: boolean;
}
```

#### الاستجابة
```typescript
interface TrendsAnalysis {
  period: string;
  metric: string;
  data: Array<{
    date: string;
    value: number;
    change: number;
    changePercent: number;
  }>;
  analysis: {
    direction: 'increasing' | 'decreasing' | 'stable';
    strength: 'strong' | 'moderate' | 'weak';
    volatility: number;
    seasonality: boolean;
  };
  forecasting?: Array<{
    date: string;
    predictedValue: number;
    confidence: number;
  }>;
}
```

### POST /api/admin/supervisor-reports/financial/export

#### معاملات الطلب
```typescript
interface ExportRequest {
  reportData: SupervisorFinancialReport;
  format: 'excel' | 'pdf' | 'csv' | 'word';
  options: {
    includeCharts: boolean;
    includeDetails: boolean;
    includeComparison: boolean;
    includeForecast: boolean;
    customTitle?: string;
    watermark?: string;
    template?: 'standard' | 'executive' | 'detailed';
  };
}
```

#### الاستجابة
```typescript
interface ExportResponse {
  success: boolean;
  downloadUrl?: string;
  fileName: string;
  fileSize: number;
  expiresAt: string;
  error?: string;
}
```

## الميزات الجديدة في API المشرف

### 1. التحليل الذكي
- تحليل الاتجاهات التلقائي
- اكتشاف الأنماط والشذوذ
- مؤشرات الأداء الرئيسية (KPIs)

### 2. المقارنات الزمنية
- مقارنة تلقائية بالفترة السابقة
- تحليل النمو والتراجع
- مؤشرات التغيير

### 3. التنبؤات المالية
- تنبؤات قصيرة المدى
- توصيات ذكية
- تحليل المخاطر

### 4. نظام التنبيهات
- تنبيهات تلقائية للمؤشرات المهمة
- عتبات قابلة للتخصيص
- تصنيف حسب الأولوية

### 5. التصدير المتقدم
- قوالب متعددة للتصدير
- خيارات تخصيص متقدمة
- دعم العلامات المائية

## خطة التنفيذ

### المرحلة 1: البنية الأساسية
1. إنشاء مجلد `/api/admin/supervisor-reports/`
2. إعداد الخدمات الأساسية
3. تطوير endpoint التقارير المالية الأساسي

### المرحلة 2: الميزات المتقدمة
1. إضافة التحليل الذكي
2. تطوير نظام التنبؤات
3. إنشاء نظام التنبيهات

### المرحلة 3: التحسين والتكامل
1. تحسين الأداء
2. إضافة التخزين المؤقت
3. تكامل مع الواجهة الأمامية

هل تريد أن أبدأ بتنفيذ هذا API الجديد؟

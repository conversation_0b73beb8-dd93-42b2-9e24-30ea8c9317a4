# نظام إدارة مدارس تحفيظ القرآن الكريم

<div dir="rtl">

## نبذة عن المشروع

نظام متكامل لإدارة مدارس تحفيظ القرآن الكريم، يهدف إلى تسهيل العملية التعليمية وإدارة شؤون الطلاب والمعلمين وأولياء الأمور. يوفر النظام واجهات مخصصة لكل نوع من المستخدمين (المسؤولين، المعلمين، الطلاب، أولياء الأمور) مع ميزات تناسب احتياجاتهم.

## الميزات الرئيسية

### 🔹 نظام إدارة المستخدمين

- **إدارة الحسابات**: إنشاء وتعديل وحذف حسابات المستخدمين (المسؤولين، المعلمين، الطلاب، أولياء الأمور)
- **الصلاحيات**: نظام متكامل للصلاحيات يحدد ما يمكن لكل مستخدم الوصول إليه
- **الملفات الشخصية**: إدارة الملفات الشخصية للمستخدمين مع إمكانية تحميل الصور الشخصية

### 🔹 إدارة الطلاب

- **سجلات الطلاب**: حفظ وإدارة بيانات الطلاب الشخصية والأكاديمية
- **متابعة التقدم**: تتبع تقدم الطلاب في حفظ القرآن الكريم
- **التقييمات**: تسجيل وعرض تقييمات الطلاب في الحفظ والتجويد
- **الإنجازات**: نظام للإنجازات والحوافز لتشجيع الطلاب

### 🔹 إدارة المعلمين

- **سجلات المعلمين**: حفظ وإدارة بيانات المعلمين ومؤهلاتهم
- **جدول الحصص**: إدارة جداول حصص المعلمين
- **تقييم الأداء**: متابعة وتقييم أداء المعلمين
- **صلاحيات محددة**: يمكن للمعلم تسجيل الحضور والغياب والنقاط لأقسامه فقط

### 🔹 إدارة الفصول

- **إنشاء الفصول**: إنشاء وإدارة الفصول الدراسية
- **توزيع الطلاب**: توزيع الطلاب على الفصول
- **تعيين المعلمين**: تعيين المعلمين للفصول

### 🔹 نظام الحضور والغياب

- **تسجيل الحضور**: تسجيل حضور وغياب الطلاب
- **تقارير الحضور**: عرض تقارير الحضور والغياب للطلاب والفصول
- **إشعارات الغياب**: إرسال إشعارات لأولياء الأمور عند غياب الطلاب

### 🔹 نظام الاختبارات والتقييم

- **إنشاء الاختبارات**: إنشاء وإدارة اختبارات الحفظ والتجويد
- **تسجيل الدرجات**: تسجيل درجات الطلاب في الاختبارات
- **تقارير التقييم**: عرض تقارير تقييم الطلاب
- **تغيير أنواع الامتحانات**: تتغير أنواع الامتحانات شهرياً ويمكن تعديلها شهرياً
- **امتحانات الحفظ**: تعتمد على السور والآيات المحددة للحفظ

### 🔹 نظام المدفوعات

- **تسجيل المدفوعات**: تسجيل مدفوعات الطلاب
- **إدارة الرسوم**: إدارة رسوم التسجيل والدراسة
- **تقارير مالية**: عرض تقارير مالية للإيرادات والمصروفات

### 🔹 لوحات التحكم

- **لوحة تحكم المسؤول**: إدارة شاملة للنظام والمستخدمين والإحصائيات
- **لوحة تحكم المعلم**: إدارة الفصول والطلاب والتقييمات
- **لوحة تحكم الطالب**: عرض التقدم والتقييمات والجدول الدراسي
- **لوحة تحكم ولي الأمر**: متابعة تقدم الأبناء والمدفوعات والإشعارات

### 🔹 التعلم عن بعد

- **الفصول الافتراضية**: إنشاء وإدارة الفصول الافتراضية
- **المواد التعليمية**: مشاركة المواد التعليمية مع الطلاب
- **التسجيلات**: تسجيل الدروس ومشاركتها مع الطلاب
- **الاجتماعات المباشرة**: عقد اجتماعات مباشرة مع الطلاب وأولياء الأمور

### 🔹 نظام الإشعارات

- **إشعارات فورية**: إرسال إشعارات فورية للمستخدمين
- **إشعارات الفصول**: إشعارات عن الفصول الافتراضية والمواد الجديدة
- **إشعارات التقييم**: إشعارات عن نتائج الاختبارات والتقييمات
- **إشعارات المدفوعات**: إشعارات عن المدفوعات والرسوم

### 🔹 التقارير والإحصائيات

- **تقارير الطلاب**: تقارير عن تقدم الطلاب وحضورهم
- **تقارير المعلمين**: تقارير عن أداء المعلمين
- **تقارير الفصول**: تقارير عن الفصول والمواد
- **تقارير مالية**: تقارير عن الإيرادات والمصروفات

## التقنيات المستخدمة

- **الواجهة الأمامية**: Next.js, React, TailwindCSS
- **الواجهة الخلفية**: Next.js API Routes
- **قاعدة البيانات**: Prisma ORM مع قاعدة بيانات PostgreSQL
- **المصادقة**: JWT (JSON Web Tokens)
- **التخزين**: تخزين الملفات باستخدام خدمات سحابية

## متطلبات التشغيل

- Node.js (الإصدار 18 أو أحدث)
- PostgreSQL (الإصدار 14 أو أحدث)
- npm أو yarn

## طريقة التثبيت

1. استنساخ المشروع:

```bash
git clone https://github.com/haouri30/qouran.git
cd qouran
```

2. تثبيت الاعتماديات:

```bash
npm install
# أو
yarn install
```

3. إعداد ملف البيئة:

قم بإنشاء ملف `.env.local` وإضافة المتغيرات البيئية اللازمة:

```
DATABASE_URL="postgresql://username:password@localhost:5432/quran_school"
JWT_SECRET="your-secret-key"
```

4. إعداد قاعدة البيانات:

```bash
npx prisma migrate dev
```

5. تشغيل المشروع:

```bash
npm run dev
# أو
yarn dev
```

6. فتح المتصفح على العنوان [http://localhost:3000](http://localhost:3000)

## المساهمة في المشروع

nنرحب بمساهماتكم في تطوير هذا المشروع. يرجى اتباع الخطوات التالية للمساهمة:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بإجراء التغييرات المطلوبة
4. قم بعمل Commit للتغييرات (`git commit -m 'إضافة ميزة رائعة'`)
5. قم بدفع التغييرات إلى الفرع (`git push origin feature/amazing-feature`)
6. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## التحديثات

### 🔹 تحسينات وإصلاحات في الكود (2024-06-20)

تم تنفيذ التحسينات وإصلاحات الأخطاء التالية:

1. **تحسين استخدام React Hooks**
   - إصلاح التبعيات المفقودة في `useEffect` عبر مكونات متعددة
   - تطبيق `useCallback` للدوال المستخدمة في تبعيات الـ effect
   - حل مشكلة الاعتماد الدائري في دوال المكونات
   - تحسين الأداء من خلال منع إعادة التقديم غير الضرورية

2. **تعزيزات سلامة الأنواع في TypeScript**
   - استبدال أنواع `any` العامة بأنواع أكثر تحديدًا مثل `Record<string, any>`
   - إضافة تعريفات نوع مناسبة لمعلمات واستجابات مسارات API
   - تحسين سلامة الأنواع في شروط استعلامات قاعدة البيانات
   - تعزيز كتابة props للمكونات لدعم IDE بشكل أفضل

3. **تحسينات المكونات**
   - تحديث مكون `AnimatedDialog` لقبول عناصر JSX في خاصية العنوان
   - تحسين صفحة الملف الشخصي لعرض بيانات المستخدم ودوره من قاعدة البيانات
   - إصلاح مشاكل في القوائم المنسدلة وإرسال النماذج

4. **تحسين مسارات API**
   - تحسين معالجة الأخطاء في مسارات API
   - تعزيز التحقق من صحة البيانات قبل عمليات قاعدة البيانات
   - تحسين استعلامات قاعدة البيانات للحصول على أداء أفضل

5. **الملفات المتأثرة**
   - `src/components/ui/dialog/AnimatedDialog.tsx`
   - `src/app/api/exam-points/route.ts`
   - `src/app/api/classes/route.ts`
   - `src/app/api/criteria-scores/route.ts`
   - `src/app/api/student-points/route.ts`
   - `src/app/api/student-rewards/route.ts`
   - `src/app/api/users/logout/route.ts`
   - `src/app/khatm-sessions/[id]/page.tsx`
   - `src/app/profile/page.tsx`
   - `src/app/remote-classes/page.tsx`
   - `src/app/remote-classes/[id]/page.tsx`
   - `src/app/teachers/evaluation/dashboard/page.tsx`
   - `src/app/admin/activities/page.tsx`
   - `src/app/admin/attendance/page.tsx`
   - `src/app/admin/donations/page.tsx`

### 🔹 تحديث لوحة تحكم ولي الأمر (2024-06-17)

تم إضافة الصفحات التالية إلى لوحة تحكم ولي الأمر:

1. **صفحة أبنائي** (`/parents/children`)
   - عرض قائمة بأبناء ولي الأمر ومعلوماتهم
   - عرض نظرة عامة عن كل طفل مع تقدمه في الدراسة
   - تبويبات لعرض تقدم حفظ القرآن ونتائج الامتحانات

2. **صفحة تقدم الأبناء** (`/parents/progress`)
   - عرض تقدم الأبناء في حفظ القرآن والدراسة
   - عرض تفاصيل نتائج الامتحانات وسجل الحضور
   - إحصائيات شاملة عن أداء كل طفل

3. **صفحة جدول الحصص** (`/parents/schedule`)
   - عرض جدول حصص الأبناء الأسبوعي
   - تنظيم الجدول حسب أيام الأسبوع
   - عرض ملخص لعدد الحصص والمعلمين

4. **صفحة المنهج الدراسي** (`/parents/curriculum`)
   - عرض المنهج الدراسي للأبناء
   - عرض المواد الدراسية ووحدات المنهج والدروس
   - الوصول إلى الموارد التعليمية المتاحة

5. **صفحة المدفوعات** (`/parents/payments`)
   - عرض سجل المدفوعات والرسوم المستحقة
   - عرض ملخص للمدفوعات والمستحقات
   - تفاصيل كل مدفوعة مع حالتها وتاريخها

### 🔹 تحديث لوحة تحكم الطالب (2024-06-16)

تم إضافة الصفحات التالية إلى لوحة تحكم الطالب:

1. **صفحة الدروس** (`/students/courses`)
   - عرض جميع المواد الدراسية للطالب
   - عرض معلومات المواد مع اسم المعلم ونسبة التقدم
   - إمكانية عرض تفاصيل كل مادة

2. **صفحة جدول الحصص** (`/students/schedule`)
   - عرض جدول الحصص الأسبوعي للطالب
   - تنظيم الجدول حسب أيام الأسبوع
   - عرض ملخص لعدد الحصص والمعلمين

3. **صفحة المعلمين** (`/students/teachers`)
   - عرض قائمة المعلمين الذين يدرسون للطالب
   - عرض معلومات المعلمين مع المواد التي يدرسونها
   - إمكانية البحث عن معلم معين

4. **صفحة النتائج** (`/students/results`)
   - عرض نتائج الامتحانات وتقدم حفظ القرآن
   - عرض ملخص للنتائج مع متوسط الدرجات
   - تبويبات لعرض تفاصيل الامتحانات وتقدم الحفظ

### 🔹 تحديث لوحة تحكم المعلم (2024-06-15)

تم إضافة الصفحات التالية إلى لوحة تحكم المعلم:

1. **صفحة طلابي** (`/teachers/students`)
   - عرض قائمة الطلاب الذين يدرسهم المعلم
   - إمكانية البحث عن طالب معين
   - عرض تفاصيل كل طالب مثل العمر والفصل وولي الأمر والنقاط

2. **صفحة تفاصيل الطالب** (`/teachers/students/[id]`)
   - عرض معلومات مفصلة عن الطالب
   - علامات تبويب لعرض سجل الحضور وتقدم حفظ القرآن والإنجازات

3. **صفحة تسجيل الحضور** (`/teachers/attendance`)
   - تسجيل حضور وغياب الطلاب
   - اختيار الفصل والتاريخ والحصة
   - عرض قائمة بالطلاب وحالة حضورهم

4. **صفحة جدول الحصص** (`/teachers/schedule`)
   - عرض جدول حصص المعلم الأسبوعي
   - عرض ملخص لعدد الحصص والمواد والفصول

5. **صفحة المنهج** (`/teachers/curriculum`)
   - عرض المنهج الدراسي للمواد التي يدرسها المعلم
   - تنظيم المنهج في وحدات ودروس وموارد تعليمية

6. **صفحة إضافة تقدم حفظ القرآن** (`/teachers/quran-progress/add`)
   - إضافة تقدم جديد في حفظ القرآن للطالب
   - اختيار السورة وتحديد الآيات والتقدير والملاحظات

### 🔹 واجهات برمجة التطبيقات (API) الجديدة

1. **API أبناء ولي الأمر** (`/api/parent-children`)
   - جلب قائمة أبناء ولي الأمر المسجل دخوله مع معلوماتهم

2. **API تقدم أبناء ولي الأمر** (`/api/parent-progress`)
   - جلب بيانات تقدم أبناء ولي الأمر في الدراسة وحفظ القرآن

3. **API جدول حصص أبناء ولي الأمر** (`/api/parent-schedule`)
   - جلب جدول حصص أبناء ولي الأمر الأسبوعي

4. **API المنهج الدراسي لأبناء ولي الأمر** (`/api/parent-curriculum`)
   - جلب المنهج الدراسي لأبناء ولي الأمر

5. **API مدفوعات أبناء ولي الأمر** (`/api/parent-payments`)
   - جلب سجل المدفوعات والرسوم المستحقة لأبناء ولي الأمر

6. **API دروس الطالب** (`/api/student-courses`)
   - جلب المواد الدراسية للطالب المسجل دخوله

7. **API جدول حصص الطالب** (`/api/student-schedule`)
   - جلب جدول الحصص الأسبوعي للطالب المسجل دخوله

8. **API معلمي الطالب** (`/api/student-teachers`)
   - جلب قائمة المعلمين الذين يدرسون للطالب المسجل دخوله

9. **API نتائج الطالب** (`/api/student-results`)
   - جلب نتائج الامتحانات وتقدم حفظ القرآن للطالب المسجل دخوله

10. **API طلاب المعلم** (`/api/teacher-students`)
    - جلب قائمة الطلاب الذين يدرسهم المعلم المسجل دخوله

11. **API فصول المعلم** (`/api/teacher-classes`)
    - جلب قائمة الفصول التي يدرسها المعلم المسجل دخوله

12. **API جدول حصص المعلم** (`/api/teacher-schedule`)
    - جلب جدول حصص المعلم المسجل دخوله

13. **API المنهج الدراسي** (`/api/curriculum`)
    - جلب المنهج الدراسي للمادة المحددة

14. **API تقدم حفظ القرآن** (`/api/quran-progress`)
    - جلب تقدم حفظ القرآن للطالب المحدد
    - إضافة تقدم جديد في حفظ القرآن

## هيكل المشروع

```
qouran/
├── prisma/
│   └── schema.prisma       # نموذج قاعدة البيانات
├── public/                 # الملفات العامة
├── src/
│   ├── app/
│   │   ├── admin/          # صفحات لوحة تحكم المسؤول
│   │   ├── api/            # واجهات برمجة التطبيقات
│   │   │   ├── attendance/
│   │   │   ├── classes/
│   │   │   ├── curriculum/
│   │   │   ├── khatm-attendance/
│   │   │   ├── khatm-sessions/
│   │   │   ├── parent-children/     # API أبناء ولي الأمر
│   │   │   ├── parent-curriculum/   # API المنهج الدراسي لأبناء ولي الأمر
│   │   │   ├── parent-payments/     # API مدفوعات أبناء ولي الأمر
│   │   │   ├── parent-progress/     # API تقدم أبناء ولي الأمر
│   │   │   ├── parent-schedule/     # API جدول حصص أبناء ولي الأمر
│   │   │   ├── quran-progress/
│   │   │   ├── student-courses/     # API دروس الطالب
│   │   │   ├── student-results/     # API نتائج الطالب
│   │   │   ├── student-schedule/    # API جدول حصص الطالب
│   │   │   ├── student-teachers/    # API معلمي الطالب
│   │   │   ├── students/
│   │   │   ├── teacher-classes/
│   │   │   ├── teacher-schedule/
│   │   │   ├── teacher-students/
│   │   │   ├── teacher-subjects/
│   │   │   └── teachers/
│   │   ├── dashboard/      # صفحات لوحات التحكم
│   │   ├── parents/        # صفحات ولي الأمر
│   │   │   ├── children/       # صفحة أبنائي
│   │   │   ├── curriculum/     # صفحة المنهج الدراسي
│   │   │   ├── payments/       # صفحة المدفوعات
│   │   │   ├── progress/       # صفحة تقدم الأبناء
│   │   │   └── schedule/       # صفحة جدول الحصص
│   │   ├── students/       # صفحات الطلاب
│   │   │   ├── courses/        # صفحة الدروس
│   │   │   ├── results/        # صفحة النتائج
│   │   │   ├── schedule/       # صفحة جدول الحصص
│   │   │   └── teachers/       # صفحة المعلمين
│   │   ├── teachers/       # صفحات المعلمين
│   │   │   ├── attendance/
│   │   │   ├── curriculum/
│   │   │   ├── quran-progress/
│   │   │   ├── schedule/
│   │   │   └── students/
│   │   ├── layout.tsx
│   │   └── page.tsx        # الصفحة الرئيسية
│   ├── components/         # مكونات واجهة المستخدم
│   ├── lib/                # مكتبات ووظائف مساعدة
│   └── utils/              # أدوات مساعدة
├── .env                    # ملف البيئة
├── .gitignore
├── next.config.js
├── package.json
├── README.md
├── README_ar.md          # ملف القراءة باللغة العربية
└── tsconfig.json
```

## التواصل

للاستفسارات والدعم، يرجى التواصل عبر البريد الإلكتروني: <EMAIL>

</div>
import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Download, Printer } from 'lucide-react';
import { CertificateData } from './index';
import html2canvas from 'html2canvas';

interface AchievementTemplateProps {
  certificateData: CertificateData;
  showControls?: boolean;
}

export default function AchievementTemplate({
  certificateData,
  showControls = true
}: AchievementTemplateProps) {
  const certificateRef = useRef<HTMLDivElement>(null);

  const handlePrint = async () => {
    if (!certificateRef.current) return;

    try {
      // فتح نافذة جديدة للطباعة
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert('يرجى السماح بالنوافذ المنبثقة لطباعة الشهادة');
        return;
      }

      // إنشاء محتوى HTML للشهادة
      const certificateHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>شهادة إنجاز</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 20px;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: #f8f9fa;
              color: #333;
            }

            .certificate-container {
              max-width: 800px;
              margin: 0 auto;
              background-color: white;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              overflow: hidden;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 8px double rgba(22, 155, 136, 0.2);
              margin: 20px;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #e6f7f5 0%, #ffffff 50%, #f0f9f8 100%);
              z-index: -1;
            }

            .certificate-decoration {
              position: absolute;
              width: 120px;
              height: 120px;
              border-radius: 50%;
              opacity: 0.1;
            }

            .decoration-1 {
              top: -60px;
              left: -60px;
              background-color: var(--primary-color);
            }

            .decoration-2 {
              bottom: -60px;
              right: -60px;
              background-color: var(--primary-color);
            }

            .decoration-3 {
              top: -30px;
              right: 60px;
              width: 60px;
              height: 60px;
              background-color: #ffd700;
            }

            .decoration-4 {
              bottom: -30px;
              left: 60px;
              width: 60px;
              height: 60px;
              background-color: #ffd700;
            }

            .certificate-header {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-icon {
              width: 80px;
              height: 80px;
              background-color: var(--primary-color);
              border-radius: 50%;
              margin: 0 auto 15px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 40px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: var(--primary-color);
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: var(--primary-color);
              margin-bottom: 10px;
              border-bottom: 2px solid rgba(22, 155, 136, 0.2);
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 40px;
              margin-top: auto;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #ddd;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 40px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #ddd;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #999;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 20px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }

            @media print {
              body {
                margin: 0;
                padding: 0;
                background: none;
              }

              .certificate-container {
                box-shadow: none;
                max-width: 100%;
              }

              .certificate {
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="certificate-container">
            <div class="certificate">
              <div class="certificate-bg"></div>
              <div class="certificate-decoration decoration-1"></div>
              <div class="certificate-decoration decoration-2"></div>
              <div class="certificate-decoration decoration-3"></div>
              <div class="certificate-decoration decoration-4"></div>

              <!-- رأس الشهادة -->
              <div class="certificate-header">
                <div class="certificate-icon">⭐</div>
                <h1 class="certificate-title">شهادة إنجاز</h1>
                <p class="certificate-subtitle">${certificateData.title}</p>
              </div>

              <!-- محتوى الشهادة -->
              <div class="certificate-content">
                <p class="certificate-intro">تشهد إدارة المدرسة أن:</p>
                <h2 class="student-name">${certificateData.student?.name || 'الطالب المتميز'}</h2>
                ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
                <p class="certificate-description">${certificateData.description}</p>
              </div>

              <!-- توقيع الشهادة -->
              <div class="certificate-footer">
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المدير</p>
                </div>
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المعلم</p>
                </div>
              </div>

              <!-- ختم المدرسة -->
              <div class="certificate-stamp">ختم المدرسة</div>

              <!-- تاريخ الإصدار -->
              ${certificateData.issueDate ? `
              <div class="certificate-date">
                تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
              </div>
              ` : ''}
            </div>
          </div>

          <script>
            window.onload = () => {
              window.print();
              window.onafterprint = () => window.close();
            };
          </script>
        </body>
        </html>
      `;

      // كتابة المحتوى إلى النافذة الجديدة
      printWindow.document.open();
      printWindow.document.write(certificateHTML);
      printWindow.document.close();
    } catch (error) {
      console.error('Error printing certificate:', error);
      alert('حدث خطأ أثناء طباعة الشهادة');
    }
  };

  const handleDownloadImage = async () => {
    try {
      // إنشاء عنصر div مؤقت لاحتواء الشهادة
      const tempDiv = document.createElement('div');
      document.body.appendChild(tempDiv);

      // إنشاء محتوى HTML للشهادة (نفس المحتوى المستخدم في الطباعة)
      const certificateHTML = `
        <div style="width: 800px; height: 1130px; position: relative;">
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 0;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: white;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 8px double rgba(22, 155, 136, 0.2);
              margin: 20px;
              background-color: white;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #e6f7f5 0%, #ffffff 50%, #f0f9f8 100%);
              z-index: -1;
            }

            .certificate-decoration {
              position: absolute;
              width: 120px;
              height: 120px;
              border-radius: 50%;
              opacity: 0.1;
            }

            .decoration-1 {
              top: -60px;
              left: -60px;
              background-color: var(--primary-color);
            }

            .decoration-2 {
              bottom: -60px;
              right: -60px;
              background-color: var(--primary-color);
            }

            .decoration-3 {
              top: -30px;
              right: 60px;
              width: 60px;
              height: 60px;
              background-color: #ffd700;
            }

            .decoration-4 {
              bottom: -30px;
              left: 60px;
              width: 60px;
              height: 60px;
              background-color: #ffd700;
            }

            .certificate-header {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-icon {
              width: 80px;
              height: 80px;
              background-color: var(--primary-color);
              border-radius: 50%;
              margin: 0 auto 15px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 40px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: var(--primary-color);
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: var(--primary-color);
              margin-bottom: 10px;
              border-bottom: 2px solid rgba(22, 155, 136, 0.2);
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 40px;
              margin-top: auto;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #ddd;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 40px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #ddd;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #999;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 20px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }
          </style>

          <div class="certificate">
            <div class="certificate-bg"></div>
            <div class="certificate-decoration decoration-1"></div>
            <div class="certificate-decoration decoration-2"></div>
            <div class="certificate-decoration decoration-3"></div>
            <div class="certificate-decoration decoration-4"></div>

            <!-- رأس الشهادة -->
            <div class="certificate-header">
              <div class="certificate-icon">⭐</div>
              <h1 class="certificate-title">شهادة إنجاز</h1>
              <p class="certificate-subtitle">${certificateData.title}</p>
            </div>

            <!-- محتوى الشهادة -->
            <div class="certificate-content">
              <p class="certificate-intro">تشهد إدارة المدرسة أن:</p>
              <h2 class="student-name">${certificateData.student?.name || 'الطالب المتميز'}</h2>
              ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
              <p class="certificate-description">${certificateData.description}</p>
            </div>

            <!-- توقيع الشهادة -->
            <div class="certificate-footer">
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المدير</p>
              </div>
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المعلم</p>
              </div>
            </div>

            <!-- ختم المدرسة -->
            <div class="certificate-stamp">ختم المدرسة</div>

            <!-- تاريخ الإصدار -->
            ${certificateData.issueDate ? `
            <div class="certificate-date">
              تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
            </div>
            ` : ''}
          </div>
        </div>
      `;

      // إضافة المحتوى إلى العنصر المؤقت
      tempDiv.innerHTML = certificateHTML;

      // انتظار تحميل الخطوط والصور
      await new Promise(resolve => setTimeout(resolve, 500));

      // تحويل العنصر إلى صورة باستخدام html2canvas
      const canvas = await html2canvas(tempDiv.firstElementChild as HTMLElement, {
        scale: 3, // زيادة الدقة للحصول على صورة أوضح
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
        allowTaint: true,
        imageTimeout: 5000
      });

      // إزالة العنصر المؤقت
      document.body.removeChild(tempDiv);

      // تحويل الصورة إلى PNG بجودة عالية
      const image = canvas.toDataURL('image/png', 1.0);

      // إنشاء رابط تنزيل وتنفيذه
      const link = document.createElement('a');
      link.href = image;
      link.download = `شهادة_إنجاز_${certificateData.student?.name || 'تقدير'}_${new Date().toLocaleDateString('fr-FR')}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error generating certificate image:', error);
      alert('حدث خطأ أثناء تحميل الشهادة كصورة');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <div className="space-y-4">
      <div ref={certificateRef} className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* الشهادة */}
        <div className="relative" data-certificate="achievement">
          {/* خلفية الشهادة */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-green-50" />

          {/* زخارف الشهادة */}
          <div className="absolute top-0 left-0 w-32 h-32 bg-[var(--primary-color)]/10 rounded-full -translate-x-16 -translate-y-16" />
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-[var(--primary-color)]/10 rounded-full translate-x-16 translate-y-16" />
          <div className="absolute top-0 right-0 w-16 h-16 bg-yellow-100 rounded-full translate-x-8 -translate-y-8" />
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-yellow-100 rounded-full -translate-x-8 translate-y-8" />

          {/* إطار الشهادة */}
          <div className="relative z-10 p-8 border-8 border-double border-[var(--primary-color)]/20 m-8 min-h-[600px]">
            <div className="flex flex-col items-center justify-center h-full text-center">
              {/* رأس الشهادة */}
              <div className="mb-8">
                <div className="w-24 h-24 mx-auto mb-4 bg-[var(--primary-color)] rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                </div>
                <h1 className="text-4xl font-bold text-[var(--primary-color)] mb-2" data-arabic-text="true">شهادة إنجاز</h1>
                <p className="text-xl text-gray-600" data-arabic-text="true">{certificateData.title}</p>
              </div>

              {/* محتوى الشهادة */}
              <div className="mb-8 max-w-2xl">
                <p className="text-lg mb-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">تشهد إدارة المدرسة أن:</p>
                <h2 className="text-3xl font-bold text-[var(--primary-color)] mb-4 border-b-2 border-[var(--primary-color)]/20 pb-2 inline-block" data-arabic-text="true">
                  {certificateData.student?.name || 'الطالب المتميز'}
                </h2>
                {certificateData.student?.classe && (
                  <p className="text-xl text-gray-600 mb-6" data-arabic-text="true">
                    {certificateData.student.classe.name}
                  </p>
                )}
                <p className="text-lg my-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">{certificateData.description}</p>
              </div>

              {/* توقيع الشهادة */}
              <div className="mt-auto grid grid-cols-2 gap-12 w-full">
                <div className="text-center">
                  <div className="h-16 border-b border-gray-300 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المدير</p>
                </div>
                <div className="text-center">
                  <div className="h-16 border-b border-gray-300 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المعلم</p>
                </div>
              </div>

              {/* تاريخ الإصدار */}
              {certificateData.issueDate && (
                <div className="mt-8 text-sm text-gray-500" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">
                  تم إصدار هذه الشهادة بتاريخ: {formatDate(certificateData.issueDate)}
                </div>
              )}

              {/* ختم المدرسة */}
              <div className="absolute bottom-8 left-8 w-24 h-24 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center text-gray-400" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">
                ختم المدرسة
              </div>
            </div>
          </div>
        </div>
      </div>

      {showControls && (
        <Card className="p-4 flex justify-center space-x-4 space-x-reverse">
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            <span>طباعة</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={handleDownloadImage}
          >
            <Download className="h-4 w-4" />
            <span>تحميل</span>
          </Button>
        </Card>
      )}
    </div>
  );
}

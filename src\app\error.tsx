'use client';
import Link from "next/link";
import SiteLogo from '@/components/SiteLogo';

interface ErrorPageProps {
  error: Error;
  reset: () => void;
}

const ErrorPage = ({ error, reset }: ErrorPageProps) => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-white rounded-lg shadow-md p-8">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-6">
            <SiteLogo size="lg" showText={false} />
          </div>

          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">حدث خطأ</h1>
          <p className="text-gray-600 mb-6">
            رسالة الخطأ: {error.message}
          </p>
          <div className="space-y-3">
            <button
              onClick={() => reset()}
              className="w-full bg-[var(--primary-color)] text-white px-6 py-2 rounded-lg hover:bg-[var(--secondary-color)] transition-colors"
            >
              إعادة المحاولة
            </button>
            <Link
              href='/'
              className="block w-full bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              العودة إلى الصفحة الرئيسية
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ErrorPage
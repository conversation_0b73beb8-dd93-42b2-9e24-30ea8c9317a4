import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

// GET /api/admin/levels - جلب جميع المستويات
export async function GET() {
  try {
    const levels = await prisma.level.findMany({
      orderBy: {
        order: 'asc'
      },
      include: {
        _count: {
          select: {
            subjects: true
          }
        }
      }
    });
    
    return NextResponse.json(levels);
  } catch (error) {
    console.error("Error fetching levels:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب المستويات" },
      { status: 500 }
    );
  }
}

// POST /api/admin/levels - إنشاء مستوى جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, order } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير اسم المستوى بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود مستوى بنفس الاسم
    const existingLevel = await prisma.level.findFirst({
      where: { name },
    });

    if (existingLevel) {
      return NextResponse.json(
        { message: "يوجد مستوى بهذا الاسم مسبقاً" },
        { status: 400 }
      );
    }

    // إذا لم يتم توفير الترتيب، نحصل على أعلى ترتيب ونضيف 1
    let levelOrder = order;
    if (!levelOrder || typeof levelOrder !== 'number') {
      const highestOrder = await prisma.level.findFirst({
        orderBy: {
          order: 'desc'
        },
        select: {
          order: true
        }
      });
      
      levelOrder = highestOrder ? highestOrder.order + 1 : 1;
    }

    const level = await prisma.level.create({
      data: { 
        name,
        description: description || null,
        order: levelOrder
      },
    });

    return NextResponse.json(level, { status: 201 });
  } catch (error) {
    console.error("Error creating level:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء المستوى" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/levels - تحديث مستوى
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, description, order } = body;

    if (!id || !name || typeof id !== 'number' || typeof name !== 'string') {
      return NextResponse.json(
        { message: "يجب توفير معرف المستوى واسمه بالتنسيق الصحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستوى
    const level = await prisma.level.findUnique({
      where: { id },
    });

    if (!level) {
      return NextResponse.json(
        { message: "المستوى غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مستوى آخر بنفس الاسم
    const existingLevel = await prisma.level.findFirst({
      where: {
        name,
        NOT: { id },
      },
    });

    if (existingLevel) {
      return NextResponse.json(
        { message: "يوجد مستوى آخر بهذا الاسم" },
        { status: 400 }
      );
    }

    const updatedLevel = await prisma.level.update({
      where: { id },
      data: {
        name,
        description: description || null,
        order: order || level.order,
      },
    });

    return NextResponse.json(updatedLevel);
  } catch (error) {
    console.error("Error updating level:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث المستوى" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/levels - حذف مستوى
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    const numericId = id ? parseInt(id) : null;

    if (!id || !numericId || isNaN(numericId)) {
      return NextResponse.json(
        { message: "يجب توفير معرف المستوى بتنسيق صحيح" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستوى
    const level = await prisma.level.findUnique({
      where: { id: numericId },
      include: {
        subjects: true
      }
    });

    if (!level) {
      return NextResponse.json(
        { message: "المستوى غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مواد مرتبطة بالمستوى
    if (level.subjects.length > 0) {
      return NextResponse.json(
        { message: "لا يمكن حذف المستوى لأنه يحتوي على مواد" },
        { status: 400 }
      );
    }

    await prisma.level.delete({
      where: { id: numericId },
    });

    return NextResponse.json(
      { message: "تم حذف المستوى بنجاح" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting level:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف المستوى" },
      { status: 500 }
    );
  }
}

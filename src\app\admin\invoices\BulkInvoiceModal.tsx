'use client';

import { useState, useEffect } from 'react';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from 'antd';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface Student {
  id: string;
  name: string;
}

interface Class {
  id: string;
  name: string;
}

interface BulkInvoiceModalProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
}

export default function BulkInvoiceModal({ isOpen, onCloseAction, onSuccessAction }: BulkInvoiceModalProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [loadingClasses, setLoadingClasses] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [formData, setFormData] = useState({
    amount: '',
    dueDate: dayjs().add(30, 'day'),
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    description: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('students');

  // جلب الطلاب والفصول عند فتح النافذة المنبثقة
  useEffect(() => {
    if (isOpen) {
      fetchStudents();
      fetchClasses();

      // إعادة تعيين النموذج
      setFormData({
        amount: '',
        dueDate: dayjs().add(30, 'day'),
        month: new Date().getMonth() + 1,
        year: new Date().getFullYear(),
        description: '',
      });
      setSelectedStudents([]);
      setSelectedClass('');
      setSearchQuery('');
    }
  }, [isOpen]);

  // جلب قائمة الطلاب
  const fetchStudents = async () => {
    try {
      setLoadingStudents(true);
      const response = await fetch('/api/students');
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      // التحقق من تنسيق البيانات المستلمة
      if (Array.isArray(data)) {
        // إذا كانت البيانات مصفوفة مباشرة
        setStudents(data);
      } else if (data && Array.isArray(data.students)) {
        // إذا كانت البيانات كائن يحتوي على خاصية students
        setStudents(data.students);
      } else {
        console.error('تنسيق بيانات الطلاب غير صالح:', data);
        setStudents([]);
        toast.error('تنسيق بيانات الطلاب غير صالح');
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('فشل في جلب قائمة الطلاب');
    } finally {
      setLoadingStudents(false);
    }
  };

  // جلب قائمة الفصول
  const fetchClasses = async () => {
    try {
      setLoadingClasses(true);
      const response = await fetch('/api/classes');
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      // التحقق من تنسيق البيانات المستلمة
      if (Array.isArray(data)) {
        // إذا كانت البيانات مصفوفة مباشرة
        setClasses(data);
      } else if (data && Array.isArray(data.classes)) {
        // إذا كانت البيانات كائن يحتوي على خاصية classes
        setClasses(data.classes);
      } else {
        console.error('تنسيق بيانات الفصول غير صالح:', data);
        setClasses([]);
        toast.error('تنسيق بيانات الفصول غير صالح');
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('فشل في جلب قائمة الفصول');
    } finally {
      setLoadingClasses(false);
    }
  };

  // تصفية الطلاب بناءً على البحث
  const filteredStudents = students.filter(student =>
    student.name.includes(searchQuery) || student.id.toString().includes(searchQuery)
  );

  // تحديث حقول النموذج
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // تحديث حقل التاريخ
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, dueDate: date }));
    }
  };

  // تحديد/إلغاء تحديد طالب
  const toggleStudent = (studentId: string) => {
    setSelectedStudents(prev => {
      if (prev.includes(studentId)) {
        return prev.filter(id => id !== studentId);
      } else {
        return [...prev, studentId];
      }
    });
  };

  // تحديد/إلغاء تحديد جميع الطلاب
  const toggleAllStudents = () => {
    if (selectedStudents.length === filteredStudents.length) {
      setSelectedStudents([]);
    } else {
      setSelectedStudents(filteredStudents.map(student => student.id.toString()));
    }
  };

  // إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.amount || !formData.dueDate) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    if (activeTab === 'students' && selectedStudents.length === 0) {
      toast.error('يرجى اختيار طالب واحد على الأقل');
      return;
    }

    if (activeTab === 'class' && !selectedClass) {
      toast.error('يرجى اختيار فصل');
      return;
    }

    try {
      setIsSubmitting(true);

      const payload = {
        amount: parseFloat(formData.amount),
        dueDate: formData.dueDate.toISOString(),
        month: formData.month,
        year: formData.year,
        description: formData.description,
      };

      let response;

      if (activeTab === 'students') {
        // إنشاء فواتير للطلاب المحددين
        response = await fetch('/api/invoices/bulk', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ...payload,
            studentIds: selectedStudents
          })
        });
      } else {
        // إنشاء فواتير لجميع طلاب الفصل المحدد
        response = await fetch('/api/invoices/bulk/class', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ...payload,
            classId: selectedClass
          })
        });
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ أثناء إنشاء الفواتير');
      }

      toast.success(`تم إنشاء ${data.count} فاتورة بنجاح`);
      onSuccessAction();
      onCloseAction();
    } catch (error: Error | unknown) {
      console.error('Error creating invoices:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في إنشاء الفواتير');
    } finally {
      setIsSubmitting(false);
    }
  };

  // إنشاء قائمة بالسنوات (السنة الحالية و5 سنوات سابقة و5 سنوات قادمة)
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  const dialogFooter = (
    <>
      <Button type="button" variant="outline" onClick={onCloseAction} disabled={isSubmitting}>
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isSubmitting}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('bulkInvoiceForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isSubmitting ? 'جاري الإنشاء...' : 'إنشاء الفواتير'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إنشاء فواتير متعددة"
      variant="primary"
      footer={dialogFooter}
    >
      <form id="bulkInvoiceForm" onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="students">طلاب محددين</TabsTrigger>
              <TabsTrigger value="class">فصل كامل</TabsTrigger>
            </TabsList>
            <TabsContent value="students">
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <Label>اختر الطلاب</Label>
                  <div className="flex items-center">
                    <Checkbox
                      id="selectAll"
                      checked={selectedStudents.length === filteredStudents.length && filteredStudents.length > 0}
                      onCheckedChange={toggleAllStudents}
                    />
                    <Label htmlFor="selectAll" className="mr-2">تحديد الكل</Label>
                  </div>
                </div>
                <Input
                  placeholder="ابحث عن الطالب..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="mb-2"
                />
                <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                  {loadingStudents ? (
                    <div className="text-center py-2">جاري التحميل...</div>
                  ) : filteredStudents.length === 0 ? (
                    <div className="text-center py-2">لا يوجد طلاب</div>
                  ) : (
                    filteredStudents.map(student => (
                      <div key={student.id} className="flex items-center py-1">
                        <Checkbox
                          id={`student-${student.id}`}
                          checked={selectedStudents.includes(student.id.toString())}
                          onCheckedChange={() => toggleStudent(student.id.toString())}
                        />
                        <Label htmlFor={`student-${student.id}`} className="mr-2">
                          {student.name}
                        </Label>
                      </div>
                    ))
                  )}
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  تم تحديد {selectedStudents.length} طالب
                </div>
              </div>
            </TabsContent>
            <TabsContent value="class">
              <div className="mb-4">
                <Label htmlFor="classId" className="block mb-2">
                  اختر الفصل
                </Label>
                <Select
                  value={selectedClass}
                  onValueChange={setSelectedClass}
                  disabled={loadingClasses}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفصل" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id.toString()}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
          </Tabs>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right col-span-1">
              المبلغ <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Input
                id="amount"
                name="amount"
                type="number"
                placeholder="أدخل المبلغ"
                value={formData.amount}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="dueDate" className="text-right col-span-1">
              تاريخ الاستحقاق <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <DatePicker
                value={formData.dueDate}
                onChange={handleDateChange}
                format="DD/MM/YYYY"
                style={{ width: '100%' }}
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="month" className="text-right col-span-1">
              الشهر <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                name="month"
                value={formData.month.toString()}
                onValueChange={(value) => setFormData(prev => ({ ...prev, month: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر الشهر" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                    <SelectItem key={month} value={month.toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="year" className="text-right col-span-1">
              السنة <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                name="year"
                value={formData.year.toString()}
                onValueChange={(value) => setFormData(prev => ({ ...prev, year: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر السنة" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="description" className="text-right col-span-1 mt-2">
              الوصف
            </Label>
            <div className="col-span-3">
              <Textarea
                id="description"
                name="description"
                placeholder="وصف الفاتورة"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
              />
            </div>
          </div>
        </div>
      </form>
    </AnimatedDialog>
  );
}

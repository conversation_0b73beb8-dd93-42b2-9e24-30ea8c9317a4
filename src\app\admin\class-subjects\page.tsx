'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface Teacher {
  id: number;
  name: string;
}

interface Subject {
  id: number;
  name: string;
}

interface TeacherSubject {
  id: number;
  teacher: Teacher;
  subject: Subject;
}

interface Class {
  id: number;
  name: string;
}

interface ClassSubject {
  id: number;
  classe: Class;
  teacherSubject: TeacherSubject;
}

export default function ClassSubjectsPage() {
  const [classes, setClasses] = useState<Class[]>([]);
  const [teacherSubjects, setTeacherSubjects] = useState<TeacherSubject[]>([]);
  const [classSubjects, setClassSubjects] = useState<ClassSubject[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedTeacherSubject, setSelectedTeacherSubject] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const fetchClassSubjects = async () => {
    try {
      const response = await fetch('/api/admin/class-subjects');
      if (!response.ok) throw new Error('فشل جلب البيانات');
      const data = await response.json();
      setClassSubjects(data);
    } catch {
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب البيانات',
        variant: 'destructive'
      });
    }
  };

  useEffect(() => {
    // جلب قائمة الفصول بدون بيانات إضافية
    fetch('/api/admin/classes?includeStudents=false&includeSubjects=false')
      .then(res => res.json())
      .then(data => setClasses(data))
      .catch(() => toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب قائمة الفصول',
        variant: 'destructive'
      }));

    // جلب قائمة المعلمين والمواد
    fetch('/api/admin/teacher-subjects')
      .then(res => res.json())
      .then(data => setTeacherSubjects(data))
      .catch(() => toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب قائمة المعلمين والمواد',
        variant: 'destructive'
      }));

    // جلب العلاقات الحالية
    fetchClassSubjects();
  }, []);

  const handleSubmit = async () => {
    if (!selectedClass || !selectedTeacherSubject) {
      toast({
        title: 'تنبيه',
        description: 'الرجاء اختيار الفصل والمعلم والمادة',
        variant: 'destructive'
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/admin/class-subjects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          classeId: parseInt(selectedClass),
          teacherSubjectId: parseInt(selectedTeacherSubject)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'فشلت عملية الربط');
      }

      toast({
        title: 'تم بنجاح',
        description: 'تم ربط المعلم بالفصل والمادة بنجاح',
      });

      // إعادة تعيين القيم وتحديث القائمة
      setSelectedClass('');
      setSelectedTeacherSubject('');
      fetchClassSubjects();
    } catch (error: Error | unknown) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء ربط المعلم بالفصل';
      toast({
        title: 'خطأ',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الربط؟')) return;

    try {
      const response = await fetch(`/api/admin/class-subjects?id=${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'فشلت عملية الحذف');
      }

      toast({
        title: 'تم بنجاح',
        description: 'تم حذف الربط بنجاح',
      });

      fetchClassSubjects();
    } catch (error: Error | unknown) {
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ أثناء حذف الربط';
      toast({
        title: 'خطأ',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.class-subjects.view">
      <div className="space-y-6 p-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">ربط الفصول بالمعلمين والمواد</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="flex flex-col space-y-2">
              <label className="text-right">اختر الفصل</label>
              <Select
                value={selectedClass}
                onValueChange={setSelectedClass}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر الفصل" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col space-y-2">
              <label className="text-right">اختر المعلم والمادة</label>
              <Select
                value={selectedTeacherSubject}
                onValueChange={setSelectedTeacherSubject}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر المعلم والمادة" />
                </SelectTrigger>
                <SelectContent>
                  {teacherSubjects.map((ts) => (
                    <SelectItem key={ts.id} value={ts.id.toString()}>
                      {`${ts.teacher.name} - ${ts.subject.name}`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <Button
              onClick={handleSubmit}
              className="w-full mt-4"
              disabled={loading}
            >
              {loading ? 'جاري الربط...' : 'ربط المعلم بالفصل'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center">قائمة الربط الحالية</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">الفصل</TableHead>
                <TableHead className="text-right">المعلم</TableHead>
                <TableHead className="text-right">المادة</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {classSubjects.map((cs) => (
                <TableRow key={cs.id}>
                  <TableCell>{cs.classe.name}</TableCell>
                  <TableCell>{cs.teacherSubject.teacher.name}</TableCell>
                  <TableCell>{cs.teacherSubject.subject.name}</TableCell>
                  <TableCell>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(cs.id)}
                    >
                      حذف
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      </div>
    </ProtectedRoute>
  );
}
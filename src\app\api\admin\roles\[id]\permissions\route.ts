import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// GET: جلب صلاحيات دور محدد
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const roleId = parseInt(resolvedParams.id);

    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.view');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    // التحقق من وجود الدور
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    });

    if (!role) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // جلب صلاحيات الدور
    const rolePermissions = await prisma.rolePermission.findMany({
      where: { roleId },
      include: {
        permission: true
      }
    });

    const permissions = rolePermissions.map(rp => rp.permission);

    return NextResponse.json({
      permissions,
      message: "تم جلب صلاحيات الدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching role permissions:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب صلاحيات الدور" },
      { status: 500 }
    );
  }
}

// PUT: تحديث صلاحيات دور محدد
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const roleId = parseInt(resolvedParams.id);

    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.edit');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const body = await request.json();
    const { permissionIds } = body;

    // التحقق من البيانات
    if (!Array.isArray(permissionIds)) {
      return NextResponse.json(
        { message: "قائمة معرفات الصلاحيات مطلوبة" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدور
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    });

    if (!role) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود جميع الصلاحيات المطلوبة
    if (permissionIds.length > 0) {
      const existingPermissions = await prisma.permission.findMany({
        where: {
          id: { in: permissionIds },
          isActive: true
        }
      });

      if (existingPermissions.length !== permissionIds.length) {
        return NextResponse.json(
          { message: "بعض الصلاحيات المحددة غير موجودة أو غير نشطة" },
          { status: 400 }
        );
      }
    }

    // استخدام transaction لضمان تحديث آمن
    await prisma.$transaction(async (tx) => {
      // حذف جميع الصلاحيات الحالية للدور
      await tx.rolePermission.deleteMany({
        where: { roleId }
      });

      // إضافة الصلاحيات الجديدة
      if (permissionIds.length > 0) {
        const rolePermissionsData = permissionIds.map((permissionId: number) => ({
          roleId,
          permissionId
        }));

        await tx.rolePermission.createMany({
          data: rolePermissionsData
        });
      }
    });

    // جلب الصلاحيات المحدثة
    const updatedRolePermissions = await prisma.rolePermission.findMany({
      where: { roleId },
      include: {
        permission: true
      }
    });

    const permissions = updatedRolePermissions.map(rp => rp.permission);

    return NextResponse.json({
      permissions,
      message: "تم تحديث صلاحيات الدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error updating role permissions:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث صلاحيات الدور" },
      { status: 500 }
    );
  }
}

// POST: إضافة صلاحية واحدة لدور
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const roleId = parseInt(resolvedParams.id);

    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.edit');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const body = await request.json();
    const { permissionId } = body;

    // التحقق من البيانات
    if (!permissionId) {
      return NextResponse.json(
        { message: "معرف الصلاحية مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدور
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    });

    if (!role) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود الصلاحية
    const permission = await prisma.permission.findUnique({
      where: { id: permissionId }
    });

    if (!permission || !permission.isActive) {
      return NextResponse.json(
        { message: "الصلاحية غير موجودة أو غير نشطة" },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود الصلاحية مسبقاً
    const existingRolePermission = await prisma.rolePermission.findUnique({
      where: {
        roleId_permissionId: {
          roleId,
          permissionId
        }
      }
    });

    if (existingRolePermission) {
      return NextResponse.json(
        { message: "الصلاحية موجودة مسبقاً في هذا الدور" },
        { status: 400 }
      );
    }

    // إضافة الصلاحية للدور
    await prisma.rolePermission.create({
      data: {
        roleId,
        permissionId
      }
    });

    return NextResponse.json({
      message: "تم إضافة الصلاحية للدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error adding permission to role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إضافة الصلاحية للدور" },
      { status: 500 }
    );
  }
}

// DELETE: إزالة صلاحية من دور
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const roleId = parseInt(resolvedParams.id);

    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.edit');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const { searchParams } = new URL(request.url);
    const permissionId = searchParams.get('permissionId');

    if (!permissionId) {
      return NextResponse.json(
        { message: "معرف الصلاحية مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدور
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    });

    if (!role) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود الصلاحية في الدور
    const existingRolePermission = await prisma.rolePermission.findUnique({
      where: {
        roleId_permissionId: {
          roleId,
          permissionId: parseInt(permissionId)
        }
      }
    });

    if (!existingRolePermission) {
      return NextResponse.json(
        { message: "الصلاحية غير موجودة في هذا الدور" },
        { status: 404 }
      );
    }

    // إزالة الصلاحية من الدور
    await prisma.rolePermission.delete({
      where: {
        roleId_permissionId: {
          roleId,
          permissionId: parseInt(permissionId)
        }
      }
    });

    return NextResponse.json({
      message: "تم إزالة الصلاحية من الدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error removing permission from role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إزالة الصلاحية من الدور" },
      { status: 500 }
    );
  }
}

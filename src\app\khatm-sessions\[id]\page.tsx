'use client';

import { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { CalendarIcon, MapPin, BookOpen, Users, ArrowRight, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { useParams, useRouter } from 'next/navigation';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

type KhatmSession = {
  id: number;
  title: string;
  date: string;
  location: string;
  description: string | null;
  teacher: {
    name: string;
  };
  surah: {
    name: string;
  } | null;
  attendances: {
    id: number;
    status: string;
    student: {
      name: string;
    };
  }[];
};

export default function KhatmSessionDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params?.id ? String(params.id) : '';

  const [session, setSession] = useState<KhatmSession | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchSessionDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/khatm-sessions/public/${sessionId}`);
      if (!response.ok) throw new Error('فشل في جلب تفاصيل مجلس الختم');
      const data = await response.json();

      if (data && data.data) {
        setSession(data.data);
      } else {
        throw new Error('مجلس الختم غير موجود');
      }
    } catch (error) {
      console.error('Error fetching khatm session details:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب تفاصيل مجلس الختم',
        variant: 'destructive',
      });
      router.push('/khatm-sessions');
    } finally {
      setLoading(false);
    }
  }, [sessionId, router]);

  useEffect(() => {
    if (sessionId) {
      fetchSessionDetails();
    }
  }, [sessionId, fetchSessionDetails]);

  const getStatusLabel = (status: string) => {
    const statusMap: Record<string, string> = {
      'PRESENT': 'حاضر',
      'ABSENT': 'غائب',
      'EXCUSED': 'غائب بعذر'
    };
    return statusMap[status] || status;
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white">


      <div className="container mx-auto py-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push('/khatm-sessions')}
          className="mb-6 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)]/10"
        >
          <ArrowRight className="ml-2 h-4 w-4" />
          العودة إلى مجالس الختم
        </Button>

        {loading ? (
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-3/4 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-20 w-full" />
              </div>
            </CardContent>
          </Card>
        ) : session ? (
          <div className="space-y-6">
            <Card className="border-t-4 border-t-[var(--primary-color)] shadow-md">
              <CardHeader className="bg-gradient-to-r from-[#f8fffd] to-white">
                <CardTitle className="text-2xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">
                  {session.title}
                </CardTitle>
                <div className="flex flex-wrap gap-4 mt-2">
                  <div className="flex items-center text-sm">
                    <CalendarIcon className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                    <span>
                      {format(new Date(session.date), 'PPP', { locale: ar })}
                    </span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                    <span>
                      {format(new Date(session.date), 'p', { locale: ar })}
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-[#f8fffd] rounded-md">
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <Users className="ml-2 h-5 w-5 text-[var(--primary-color)]" />
                      <span className="font-medium ml-1">المعلم:</span>
                      <span className="mr-1">{session.teacher.name}</span>
                    </div>

                    {session.surah && (
                      <div className="flex items-center">
                        <BookOpen className="ml-2 h-5 w-5 text-[var(--primary-color)]" />
                        <span className="font-medium ml-1">السورة:</span>
                        <span className="mr-1">{session.surah.name}</span>
                      </div>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center">
                      <MapPin className="ml-2 h-5 w-5 text-[var(--primary-color)]" />
                      <span className="font-medium ml-1">المكان:</span>
                      <span className="mr-1">{session.location}</span>
                    </div>

                    <div className="flex items-center">
                      <Users className="ml-2 h-5 w-5 text-[var(--primary-color)]" />
                      <span className="font-medium ml-1">عدد الحضور:</span>
                      <Badge className="mr-2 bg-[var(--primary-color)]/10 text-[var(--primary-color)] border-[var(--primary-color)]">
                        {session.attendances.filter(a => a.status === 'PRESENT').length} طالب
                      </Badge>
                    </div>
                  </div>
                </div>

                {session.description && (
                  <div className="mt-4">
                    <h3 className="text-lg font-medium mb-2 text-[var(--primary-color)]">وصف المجلس</h3>
                    <p className="text-muted-foreground">{session.description}</p>
                  </div>
                )}

                <div className="mt-6">
                  <h3 className="text-lg font-medium mb-4 text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">سجل الحضور</h3>

                  {session.attendances.length > 0 ? (
                    <Table>
                      <TableHeader className="bg-[var(--primary-color)]/10">
                        <TableRow>
                          <TableHead className="text-[var(--primary-color)] font-bold">الطالب</TableHead>
                          <TableHead className="text-[var(--primary-color)] font-bold">حالة الحضور</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {session.attendances.map(attendance => (
                          <TableRow key={attendance.id}>
                            <TableCell>{attendance.student.name}</TableCell>
                            <TableCell>
                              <Badge
                                className={
                                  attendance.status === 'PRESENT'
                                    ? 'bg-green-100 text-green-800 border-green-300'
                                    : attendance.status === 'EXCUSED'
                                    ? 'bg-yellow-100 text-yellow-800 border-yellow-300'
                                    : 'bg-red-100 text-red-800 border-red-300'
                                }
                              >
                                {getStatusLabel(attendance.status)}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                      <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-muted-foreground">لا يوجد سجلات حضور لهذا المجلس</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-300">
            <div className="flex flex-col items-center justify-center">
              <CalendarIcon className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-1">مجلس الختم غير موجود</h3>
              <p className="text-sm text-gray-500 mb-4">لم يتم العثور على مجلس الختم المطلوب</p>
              <Button
                onClick={() => router.push('/khatm-sessions')}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                العودة إلى مجالس الختم
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

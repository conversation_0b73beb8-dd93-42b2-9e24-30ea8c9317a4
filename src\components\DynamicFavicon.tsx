'use client';

import { useEffect } from 'react';

export default function DynamicFavicon() {
  useEffect(() => {
    const updateFavicon = async () => {
      try {
        const response = await fetch('/api/settings');
        const data = await response.json();

        if (data.settings?.faviconUrl) {
          console.log('Updating favicon to:', data.settings.faviconUrl);

          // تحديث الفافيكونات الموجودة بدلاً من إزالتها
          const timestamp = Date.now();
          const faviconUrl = data.settings.faviconUrl + '?v=' + timestamp;

          // البحث عن الفافيكون الأساسي وتحديثه
          let mainFavicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement;
          if (mainFavicon) {
            mainFavicon.href = faviconUrl;
          } else {
            mainFavicon = document.createElement('link');
            mainFavicon.rel = 'icon';
            mainFavicon.type = 'image/x-icon';
            mainFavicon.href = faviconUrl;
            document.head.appendChild(mainFavicon);
          }

          // البحث عن فافيكون 16x16 وتحديثه
          let favicon16 = document.querySelector('link[rel="icon"][sizes="16x16"]') as HTMLLinkElement;
          if (favicon16) {
            favicon16.href = faviconUrl;
          } else {
            favicon16 = document.createElement('link');
            favicon16.rel = 'icon';
            favicon16.type = 'image/png';
            favicon16.sizes = '16x16';
            favicon16.href = faviconUrl;
            document.head.appendChild(favicon16);
          }

          // البحث عن فافيكون 32x32 وتحديثه
          let favicon32 = document.querySelector('link[rel="icon"][sizes="32x32"]') as HTMLLinkElement;
          if (favicon32) {
            favicon32.href = faviconUrl;
          } else {
            favicon32 = document.createElement('link');
            favicon32.rel = 'icon';
            favicon32.type = 'image/png';
            favicon32.sizes = '32x32';
            favicon32.href = faviconUrl;
            document.head.appendChild(favicon32);
          }

          // البحث عن Apple Touch Icon وتحديثه
          let appleFavicon = document.querySelector('link[rel="apple-touch-icon"]') as HTMLLinkElement;
          if (appleFavicon) {
            appleFavicon.href = faviconUrl;
          } else {
            appleFavicon = document.createElement('link');
            appleFavicon.rel = 'apple-touch-icon';
            appleFavicon.sizes = '180x180';
            appleFavicon.href = faviconUrl;
            document.head.appendChild(appleFavicon);
          }

          // البحث عن shortcut icon وتحديثه
          let shortcutIcon = document.querySelector('link[rel="shortcut icon"]') as HTMLLinkElement;
          if (shortcutIcon) {
            shortcutIcon.href = faviconUrl;
          } else {
            shortcutIcon = document.createElement('link');
            shortcutIcon.rel = 'shortcut icon';
            shortcutIcon.type = 'image/x-icon';
            shortcutIcon.href = faviconUrl;
            document.head.appendChild(shortcutIcon);
          }

          console.log('Favicon updated successfully');
        } else {
          console.log('No favicon URL found in settings');
        }
      } catch (error) {
        console.error('Error updating favicon:', error);
      }
    };

    // تأخير بسيط للتأكد من تحميل الصفحة
    const timer = setTimeout(updateFavicon, 500);

    // إضافة مستمع للتحديثات في localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteSettings' && e.newValue) {
        try {
          const newSettings = JSON.parse(e.newValue);
          if (newSettings.faviconUrl) {
            updateFavicon();
          }
        } catch (error) {
          console.error('Error parsing updated settings:', error);
        }
      }
    };

    // إضافة مستمع مخصص للتحديثات الداخلية
    const handleSettingsUpdate = (event: CustomEvent) => {
      if (event.detail?.faviconUrl) {
        updateFavicon();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('siteSettingsUpdated', handleSettingsUpdate as EventListener);
    };
  }, []);

  return null;
}

import * as jose from "jose";

export type JWTPayload = {
  id: number;
  username: string;
  role: string;
  roleId?: number;
};

/**
 * التحقق من صحة التوكن وإرجاع البيانات المضمنة فيه
 * @param token توكن JWT
 * @returns بيانات المستخدم المضمنة في التوكن أو null في حالة فشل التحقق
 */
export async function getToken(token: string): Promise<JWTPayload | null> {
  try {
    const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'default_secret_key_for_development');
    const { payload } = await jose.jwtVerify(token, secret);
    return payload as JWTPayload;
  } catch (error) {
    console.error("JWT Verification Failed:", error);
    return null;
  }
}

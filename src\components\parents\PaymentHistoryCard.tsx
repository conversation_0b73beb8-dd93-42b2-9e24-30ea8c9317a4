'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  FaMoneyBillWave,
  FaCalendarAlt,
  FaFileInvoiceDollar,
  FaCheckCircle,
  FaUser,
  FaCreditCard,
  FaReceipt
} from 'react-icons/fa'

interface Payment {
  id: number
  amount: number
  date: string
  paymentMethod: string
  notes?: string
  invoiceId?: number
  invoiceMonth?: number
  invoiceYear?: number
  paidByParent?: boolean
  parentName?: string
}

interface PaymentHistoryCardProps {
  payments: Payment[]
  studentName: string
  formatCurrency: (amount: number) => string
  onReceiptClick?: (payment: Payment) => void
}

export default function PaymentHistoryCard({ 
  payments, 
  studentName, 
  formatCurrency,
  onReceiptClick 
}: PaymentHistoryCardProps) {
  const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0)
  const recentPayments = payments.slice(0, 5) // آخر 5 مدفوعات

  if (payments.length === 0) {
    return (
      <Card className="border-gray-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-600">
            <FaMoneyBillWave />
            سجل المدفوعات - {studentName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <FaMoneyBillWave className="text-4xl text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">لا توجد مدفوعات مسجلة</h3>
            <p className="text-sm text-gray-500">لم يتم تسجيل أي مدفوعات لهذا الطالب بعد</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-green-200">
      <CardHeader>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
          <CardTitle className="flex items-center gap-2 text-green-700">
            <FaMoneyBillWave />
            سجل المدفوعات - {studentName}
          </CardTitle>
          <div className="text-right">
            <p className="text-sm text-green-600">إجمالي المدفوعات</p>
            <p className="text-xl font-bold text-green-700">{formatCurrency(totalPaid)}</p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* ملخص المدفوعات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <FaCheckCircle className="text-green-600" />
              <span className="text-sm font-medium text-green-800">عدد المدفوعات</span>
            </div>
            <p className="text-lg font-bold text-green-700">{payments.length}</p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <FaCalendarAlt className="text-green-600" />
              <span className="text-sm font-medium text-green-800">آخر دفعة</span>
            </div>
            <p className="text-sm font-bold text-green-700">
              {new Date(payments[0].date).toLocaleDateString('fr-FR')}
            </p>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <FaMoneyBillWave className="text-green-600" />
              <span className="text-sm font-medium text-green-800">متوسط الدفعة</span>
            </div>
            <p className="text-sm font-bold text-green-700">
              {formatCurrency(totalPaid / payments.length)}
            </p>
          </div>
        </div>

        {/* قائمة المدفوعات الحديثة */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-800 flex items-center gap-2">
            <FaFileInvoiceDollar className="text-blue-600" />
            آخر المدفوعات
          </h4>
          
          {recentPayments.map((payment, index) => (
            <div 
              key={payment.id} 
              className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 p-4 bg-white rounded-lg border border-gray-200 hover:border-green-300 transition-colors"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge className="bg-green-100 text-green-800">
                    دفعة #{payment.id}
                  </Badge>
                  {payment.paidByParent && (
                    <Badge className="bg-blue-100 text-blue-800 flex items-center gap-1">
                      <FaUser className="text-xs" />
                      دفع ولي الأمر
                    </Badge>
                  )}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                  <div className="flex items-center gap-2">
                    <FaCalendarAlt className="text-gray-500" />
                    <span className="text-gray-600">التاريخ:</span>
                    <span className="font-medium">
                      {new Date(payment.date).toLocaleDateString('fr-FR')}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <FaCreditCard className="text-gray-500" />
                    <span className="text-gray-600">طريقة الدفع:</span>
                    <span className="font-medium">{payment.paymentMethod}</span>
                  </div>
                  
                  {payment.invoiceMonth && payment.invoiceYear && (
                    <div className="flex items-center gap-2">
                      <FaFileInvoiceDollar className="text-gray-500" />
                      <span className="text-gray-600">الفاتورة:</span>
                      <span className="font-medium">
                        {payment.invoiceMonth}/{payment.invoiceYear}
                      </span>
                    </div>
                  )}
                  
                  {payment.parentName && (
                    <div className="flex items-center gap-2">
                      <FaUser className="text-gray-500" />
                      <span className="text-gray-600">دفع بواسطة:</span>
                      <span className="font-medium text-blue-600">{payment.parentName}</span>
                    </div>
                  )}
                </div>
                
                {payment.notes && (
                  <div className="mt-2 p-2 bg-gray-50 rounded text-sm text-gray-600">
                    <strong>ملاحظات:</strong> {payment.notes}
                  </div>
                )}
              </div>
              
              <div className="flex flex-col md:flex-row items-start md:items-center gap-2">
                <div className="text-right">
                  <p className="text-lg font-bold text-green-700">
                    {formatCurrency(payment.amount)}
                  </p>
                  <p className="text-xs text-gray-500">المبلغ المدفوع</p>
                </div>
                
                {onReceiptClick && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onReceiptClick(payment)}
                    className="border-blue-300 text-blue-600 hover:bg-blue-50 flex items-center gap-1"
                  >
                    <FaReceipt className="text-xs" />
                    إيصال
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* رابط لعرض جميع المدفوعات */}
        {payments.length > 5 && (
          <div className="text-center pt-4 border-t border-gray-200">
            <Button
              variant="ghost"
              className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef]"
            >
              عرض جميع المدفوعات ({payments.length})
            </Button>
          </div>
        )}

        {/* رسالة تقدير */}
        <div className="bg-green-50 p-3 rounded-lg border border-green-200 text-center">
          <FaCheckCircle className="text-green-600 text-xl mx-auto mb-2" />
          <p className="text-sm text-green-700 font-medium">
            شكراً لالتزامكم بالمدفوعات
          </p>
          <p className="text-xs text-green-600 mt-1">
            جميع المدفوعات مسجلة باسم ولي الأمر كما هو مطلوب
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

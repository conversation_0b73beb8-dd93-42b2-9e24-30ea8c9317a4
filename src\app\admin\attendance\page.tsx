'use client'

import { useEffect, useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'react-toastify';
import { ATTENDANCE_STATUS, getArabicStatus, getStatusColor } from '@/constants/attendance'
import { FaUserCheck, FaCalendarAlt, FaClock, FaUserPlus, FaSync, FaCheck, FaTimes,
  FaFileExcel, FaChartBar } from 'react-icons/fa'

import { exportToExcel } from '@/utils/export-utils'
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'

type Student = {
  id: number
  name: string
  classe: {
    id: number
    name: string
  }
}

type AttendanceRecord = {
  id: number | null
  student: Student
  status: string | null
  date: string
}

type Class = {
  id: number
  name: string
}

export default function AttendancePage() {
  const [selectedClass, setSelectedClass] = useState('')
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [selectedSession, setSelectedSession] = useState('1')
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([])
  const [loading, setLoading] = useState(false)
  const [classes, setClasses] = useState<Class[]>([])

  // تعريف دالة updateAttendance أولاً لتجنب الاعتماد الدائري
  const updateAttendance = useCallback(async (studentId: number, status: string, recordId: number | null) => {
    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: recordId,
          studentId: studentId,
          date: selectedDate,
          status: status,
          hisass: parseInt(selectedSession)
        })
      })
      if (!response.ok) throw new Error('فشل في تحديث الحضور')

      toast.success('تم تحديث الحضور بنجاح')
      // سنقوم بإعادة جلب البيانات بعد تعريف fetchAttendanceRecords
    } catch (error) {
      toast.error('حدث خطأ أثناء تحديث الحضور')
      console.error(error)
    }
  }, [selectedDate, selectedSession])

  // تعريف دالة fetchAttendanceRecords
  const fetchAttendanceRecords = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedClass) params.append('classeId', selectedClass)
      if (selectedDate) params.append('date', selectedDate)
      if (selectedSession) params.append('hisass', selectedSession)

      const response = await fetch(`/api/attendance?${params.toString()}`)
      if (!response.ok) throw new Error('فشل في جلب سجلات الحضور')

      const data = await response.json()
      setAttendanceRecords(data)

      if (data.length > 0) {
        const markAllPresent = async () => {
          for (const record of data) {
            if (!record.status) {
              await updateAttendance(record.student.id, 'PRESENT', record.id)
            }
          }
        }
        markAllPresent()
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء جلب سجلات الحضور')
      console.error(error)
    } finally {
      setLoading(false)
    }
  }, [selectedClass, selectedDate, selectedSession, updateAttendance])

  // تحديث updateAttendance لاستخدام fetchAttendanceRecords
  const refreshAfterUpdate = useCallback(async (studentId: number, status: string, recordId: number | null) => {
    await updateAttendance(studentId, status, recordId)
    fetchAttendanceRecords()
  }, [updateAttendance, fetchAttendanceRecords])

  // تعريف دالة fetchClasses
  const fetchClasses = useCallback(async () => {
    try {
      const response = await fetch('/api/classes')
      if (!response.ok) throw new Error('فشل في جلب الأقسام')
      const data = await response.json()

      // التحقق من هيكل البيانات المستلمة
      if (data && data.classes && Array.isArray(data.classes)) {
        // البيانات موجودة في خاصية classes
        setClasses(data.classes)
      } else if (Array.isArray(data)) {
        // البيانات هي مصفوفة مباشرة
        setClasses(data)
      } else {
        console.error('البيانات المستلمة ليست بالتنسيق المتوقع:', data)
        setClasses([])
      }
    } catch (error) {
      toast.error('حدث خطأ أثناء جلب الأقسام')
      console.error(error)
      setClasses([])
    }
  }, [])

  // دالة لتصدير بيانات الحضور إلى Excel
  const exportAttendanceToExcel = () => {
    try {
      if (attendanceRecords.length === 0) {
        toast.error('لا توجد بيانات للتصدير')
        return
      }

      // تحويل البيانات إلى الشكل المطلوب للتصدير
      const dataToExport = attendanceRecords.map((record, index) => ({
        'الرقم': index + 1,
        'اسم التلميذ': record.student.name,
        'القسم': record.student.classe?.name || 'غير محدد',
        'الحالة': getArabicStatus(record.status),
        'التاريخ': new Date(record.date).toLocaleDateString('fr-FR'),
        'الحصة': `الحصة ${selectedSession}`
      }))

      // تحديد عرض الأعمدة
      const columnWidths = [
        { wch: 5 },  // الرقم
        { wch: 25 }, // اسم التلميذ
        { wch: 15 }, // القسم
        { wch: 10 }, // الحالة
        { wch: 15 }, // التاريخ
        { wch: 10 }  // الحصة
      ]

      // تصدير البيانات
      const className = selectedClass
        ? classes.find(c => c.id.toString() === selectedClass)?.name || 'كل الأقسام'
        : 'كل الأقسام'

      exportToExcel(
        dataToExport,
        `سجل_الحضور_${className}_${selectedDate}.xlsx`,
        'سجل الحضور',
        columnWidths
      )
    } catch (error) {
      console.error('Error exporting attendance to Excel:', error)
      toast.error('حدث خطأ أثناء تصدير البيانات')
    }
  }

  // استخدام useEffect لجلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchClasses()
  }, [fetchClasses])

  useEffect(() => {
    fetchAttendanceRecords()
  }, [fetchAttendanceRecords])

  return (
    <OptimizedProtectedRoute requiredPermission="admin.attendance.view">
      <div className="p-3 sm:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-4 sm:space-y-6" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-3 mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-2 sm:pr-3 flex items-center gap-2">
          <FaUserCheck className="text-[var(--primary-color)]" />
          سجل الحضور والغياب
        </h1>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto justify-center sm:justify-end">
          <QuickActionButtons
            entityType="reports"
            actions={[
              {
                key: 'view',
                label: 'تقارير الحضور',
                icon: <FaChartBar />,
                onClick: () => window.location.href = '/admin/attendance/reports',
                variant: 'primary',
                permission: 'admin.reports.view'
              },
              {
                key: 'export',
                label: 'تصدير Excel',
                icon: <FaFileExcel />,
                onClick: exportAttendanceToExcel,
                variant: 'success',
                permission: 'admin.reports.export'
              }
            ]}
            className="flex-wrap gap-2 w-full sm:w-auto"
          />
          <Button
            onClick={fetchAttendanceRecords}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2 w-full sm:w-auto"
            title="تحديث البيانات"
          >
            <FaSync className="h-4 w-4 sm:h-5 sm:w-5" />
            <span className="sm:hidden mr-2">تحديث البيانات</span>
          </Button>
        </div>
      </div>

      <div className="flex flex-col gap-3 bg-white p-3 sm:p-4 rounded-lg shadow-md mb-4 sm:mb-6">
        <div className="w-full flex items-center gap-2">
          <FaUserPlus className="text-[var(--primary-color)] min-w-5" />
          <div className="w-full">
            <Select
              value={selectedClass}
              onValueChange={(value) => {
                setSelectedClass(value)
              }}
            >
              <SelectTrigger className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] text-xs sm:text-sm">
                <SelectValue placeholder="اختر القسم" />
              </SelectTrigger>
              <SelectContent>
                {Array.isArray(classes) && classes.length > 0 ? (
                  classes.map((classe) => (
                    <SelectItem key={classe.id} value={classe.id.toString()} className="text-xs sm:text-sm">
                      {classe.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-classes" disabled className="text-xs sm:text-sm">
                    لا توجد أقسام متاحة
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="w-full flex items-center gap-2">
          <FaCalendarAlt className="text-[var(--primary-color)] min-w-5" />
          <Input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-full text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] text-xs sm:text-sm"
          />
        </div>

        <div className="w-full flex items-center gap-2">
          <FaClock className="text-[var(--primary-color)] min-w-5" />
          <Select
            value={selectedSession}
            onValueChange={(value) => setSelectedSession(value)}
          >
            <SelectTrigger className="w-full text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] text-xs sm:text-sm">
              <SelectValue placeholder="اختر الحصة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1" className="text-xs sm:text-sm">الحصة الأولى</SelectItem>
              <SelectItem value="2" className="text-xs sm:text-sm">الحصة الثانية</SelectItem>
              <SelectItem value="3" className="text-xs sm:text-sm">الحصة الثالثة</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
        <div className="overflow-x-auto max-w-full">
          <Table>
            <TableHeader>
              <TableRow className="bg-[var(--primary-color)]">
                <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4">الرقم</TableHead>
                <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4">اسم التلميذ</TableHead>
                <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">القسم</TableHead>
                <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4">الحالة</TableHead>
                <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">تاريخ التسجيل</TableHead>
                <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">الحصة</TableHead>
                <TableHead className="text-right text-white font-bold text-xs sm:text-sm p-2 sm:p-4">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4 sm:py-8 text-xs sm:text-sm">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-[var(--primary-color)]"></div>
                      <span className="mr-2">جاري التحميل...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : attendanceRecords.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4 sm:py-8 text-xs sm:text-sm">لا توجد سجلات حضور</TableCell>
                </TableRow>
              ) : (
                attendanceRecords.map((record, index) => (
                  <TableRow key={record.student.id} className="hover:bg-gray-50">
                    <TableCell className="text-right text-xs sm:text-sm p-2 sm:p-4">{index + 1}</TableCell>
                    <TableCell className="text-right font-medium text-xs sm:text-sm p-2 sm:p-4">
                      {record.student.name}
                      <div className="sm:hidden mt-1 text-xs text-gray-500">
                        <div>{record.student.classe?.name || 'غير محدد'}</div>
                        <div>
                          {new Date(record.date).toLocaleDateString('fr-FR')} | الحصة {selectedSession}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">{record.student.classe?.name || 'غير محدد'}</TableCell>
                    <TableCell className="text-right text-xs sm:text-sm p-2 sm:p-4">
                      {record.status ? (
                        <span className={`px-2 py-1 ${getStatusColor(record.status)} rounded inline-block text-xs sm:text-sm`}>
                          {getArabicStatus(record.status)}
                        </span>
                      ) : (
                        <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded inline-block text-xs sm:text-sm">لم يسجل</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">{new Date(record.date).toLocaleDateString('fr-FR')}</TableCell>
                    <TableCell className="text-right text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">الحصة {selectedSession}</TableCell>
                    <TableCell className="p-2 sm:p-4">
                      <div className="flex flex-col sm:flex-row gap-1 sm:gap-2 justify-end">
                        <PermissionGuard requiredPermission="admin.attendance.edit">
                          <Button
                            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-sm hover:shadow-md transition-all duration-300 flex items-center gap-1 text-xs sm:text-sm p-1 sm:p-2 h-auto"
                            onClick={() => refreshAfterUpdate(record.student.id, 'PRESENT', record.id)}
                          >
                            <FaCheck className="ml-1" />
                            <span className="hidden sm:inline">{ATTENDANCE_STATUS.PRESENT}</span>
                            <span className="sm:hidden">حاضر</span>
                          </Button>
                        </PermissionGuard>
                        <PermissionGuard requiredPermission="admin.attendance.edit">
                          <Button
                            className="bg-red-500 hover:bg-red-600 text-white shadow-sm hover:shadow-md transition-all duration-300 flex items-center gap-1 text-xs sm:text-sm p-1 sm:p-2 h-auto"
                            onClick={() => refreshAfterUpdate(record.student.id, 'ABSENT', record.id)}
                          >
                            <FaTimes className="ml-1" />
                            <span className="hidden sm:inline">{ATTENDANCE_STATUS.ABSENT}</span>
                            <span className="sm:hidden">غائب</span>
                          </Button>
                        </PermissionGuard>
                        <PermissionGuard requiredPermission="admin.attendance.edit">
                          <Button
                            className="bg-yellow-500 hover:bg-yellow-600 text-white shadow-sm hover:shadow-md transition-all duration-300 flex items-center gap-1 text-xs sm:text-sm p-1 sm:p-2 h-auto"
                            onClick={() => refreshAfterUpdate(record.student.id, 'EXCUSED', record.id)}
                          >
                            <FaTimes className="ml-1" />
                            <span className="hidden sm:inline">{ATTENDANCE_STATUS.EXCUSED}</span>
                            <span className="sm:hidden">معذور</span>
                          </Button>
                        </PermissionGuard>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
      </div>
    </OptimizedProtectedRoute>
  )
}
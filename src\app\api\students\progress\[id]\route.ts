import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/students/progress/[id]
export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // استخدام await مع params لتجنب الخطأ
    const paramsData = await Promise.resolve(params);
    const id = parseInt(paramsData.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف الطالب غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id },
      include: {
        classe: true,
        guardian: true
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الطالب' },
        { status: 404 }
      );
    }

    // جلب سجلات الحضور مع الصور
    const attendanceRecords = await prisma.attendance.findMany({
      where: { studentId: id },
      include: {
        images: true
      },
      orderBy: { date: 'desc' }
    }).catch(error => {
      console.error('Error fetching attendance records:', error);
      return [];
    });

    // جلب جدول الحصص للطالب
    const classSchedule = student.classeId ? await prisma.classSchedule.findMany({
      where: { classeId: student.classeId },
      include: {
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    }).catch(error => {
      console.error('Error fetching class schedule:', error);
      return [];
    }) : [];

    // حساب إحصائيات الحضور
    const totalAttendance = attendanceRecords.length;
    const presentCount = attendanceRecords.filter(record => record.status === 'PRESENT').length;
    const absentCount = attendanceRecords.filter(record => record.status === 'ABSENT').length;
    const excusedCount = attendanceRecords.filter(record => record.status === 'EXCUSED').length;
    const attendanceRate = totalAttendance > 0 ? (presentCount / totalAttendance) * 100 : 0;

    // حساب إحصائيات الحضور حسب الشهر
    const attendanceByMonth = attendanceRecords.reduce((acc, record) => {
      const month = record.date.toISOString().substring(0, 7); // YYYY-MM
      if (!acc[month]) {
        acc[month] = { total: 0, present: 0, absent: 0, excused: 0 };
      }
      acc[month].total++;
      if (record.status === 'PRESENT') acc[month].present++;
      if (record.status === 'ABSENT') acc[month].absent++;
      if (record.status === 'EXCUSED') acc[month].excused++;
      return acc;
    }, {} as Record<string, { total: number, present: number, absent: number, excused: number }>);

    // تحويل إحصائيات الشهر إلى مصفوفة
    const monthlyStats = Object.entries(attendanceByMonth).map(([month, stats]) => ({
      month,
      total: stats.total,
      present: stats.present,
      absent: stats.absent,
      excused: stats.excused,
      rate: parseFloat(((stats.present / stats.total) * 100).toFixed(1))
    })).sort((a, b) => a.month.localeCompare(b.month));

    // جلب تقدم حفظ القرآن
    const quranProgressRecords = await prisma.quranProgress.findMany({
      where: { studentId: id },
      include: {
        surah: true
      },
      orderBy: { startDate: 'desc' }
    }).catch(error => {
      console.error('Error fetching quran progress:', error);
      return [];
    });

    // حساب إحصائيات حفظ القرآن
    const totalVerses = quranProgressRecords.reduce(
      (sum, record) => sum + (record.endVerse - record.startVerse + 1),
      0
    );

    // الحصول على آخر سورة تم حفظها
    const lastQuranProgress = quranProgressRecords[0];
    const lastSurah = lastQuranProgress?.surah?.name || 'لا يوجد';

    // جلب نتائج الامتحانات
    const examResults = await prisma.exam_points.findMany({
      where: { studentId: id },
      include: {
        exam: true,
        surah: true
      },
      orderBy: { createdAt: 'desc' }
    }).catch(error => {
      console.error('Error fetching exam results:', error);
      return [];
    });

    // حساب إحصائيات الامتحانات
    const totalExams = examResults.length;
    const passedExams = examResults.filter(
      result => Number(result.grade) >= Number(result.exam.passingPoints)
    ).length;
    const failedExams = totalExams - passedExams;
    const averageScore = totalExams > 0
      ? examResults.reduce((sum, result) => sum + Number(result.grade), 0) / totalExams
      : 0;

    // الحصول على آخر امتحان
    const lastExam = examResults[0];

    // تجميع البيانات
    const progressData = {
      attendance: {
        present: presentCount,
        absent: absentCount,
        excused: excusedCount,
        total: totalAttendance,
        rate: parseFloat(attendanceRate.toFixed(1)),
        monthlyStats: monthlyStats,
        schedule: classSchedule.map(schedule => ({
          id: schedule.id,
          day: schedule.day,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
          teacher: schedule.teacherSubject?.teacher?.name || 'غير محدد',
          subject: schedule.teacherSubject?.subject?.name || 'غير محدد'
        })),
        records: attendanceRecords.slice(0, 20).map(record => ({
          id: record.id,
          date: record.date.toISOString().split('T')[0],
          status: record.status,
          hisass: record.hisass,
          images: record.images.map(img => ({
            id: img.id,
            url: img.imageUrl
          }))
        }))
      },
      quran: {
        memorized: totalVerses,
        total: 6236, // إجمالي عدد آيات القرآن الكريم
        rate: parseFloat(((totalVerses / 6236) * 100).toFixed(1)),
        lastSurah: lastSurah,
        records: quranProgressRecords.slice(0, 10).map(record => ({
          id: record.id,
          surahName: record.surah?.name || 'غير محدد',
          startVerse: record.startVerse,
          endVerse: record.endVerse,
          memorization: record.memorization,
          tajweed: record.tajweed,
          date: record.startDate.toISOString().split('T')[0]
        }))
      },
      exams: {
        passed: passedExams,
        failed: failedExams,
        total: totalExams,
        average: parseFloat(averageScore.toFixed(1)),
        lastExam: lastExam ? {
          id: lastExam.id,
          name: lastExam.exam.description || `امتحان ${lastExam.exam.evaluationType || 'عام'}`,
          score: Number(lastExam.grade),
          maxPoints: lastExam.exam.maxPoints,
          date: lastExam.createdAt.toISOString().split('T')[0]
        } : null,
        records: examResults.slice(0, 10).map(result => ({
          id: result.id,
          name: result.exam.description || `امتحان ${result.exam.evaluationType || 'عام'}`,
          score: Number(result.grade),
          maxPoints: result.exam.maxPoints,
          passingPoints: result.exam.passingPoints,
          isPassed: Number(result.grade) >= Number(result.exam.passingPoints),
          date: result.createdAt.toISOString().split('T')[0]
        }))
      }
    };

    return NextResponse.json({
      student: {
        id: student.id,
        username: student.username,
        name: student.name,
        age: student.age,
        phone: student.phone,
        classe: student.classe,
        guardian: student.guardian,
        totalPoints: student.totalPoints
      },
      progressData
    });
  } catch (error) {
    console.error('Error fetching student progress:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات تقدم الطالب' },
      { status: 500 }
    );
  }
}

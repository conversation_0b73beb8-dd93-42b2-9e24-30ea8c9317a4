import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { verifyToken } from "@/utils/verifyToken";

// GET /api/evaluation/analysis
export async function GET(request: NextRequest) {
  try {
    // التحقق من توكن المستخدم
    const payload = await verifyToken(request);

    if (!payload) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    // التحقق من أن المستخدم مدير أو معلم
    const isAdmin = payload.role === 'ADMIN';
    const isTeacher = payload.role === 'TEACHER';

    if (!isAdmin && !isTeacher) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const examId = searchParams.get('examId');
    const classId = searchParams.get('classId');
    const teacherId = searchParams.get('teacherId');
    const month = searchParams.get('month');
    const evaluationType = searchParams.get('evaluationType');

    // بناء شروط البحث
    const where: Record<string, unknown> = {};

    if (examId) {
      where.examId = parseInt(examId);
    } else {
      // إذا لم يتم تحديد امتحان معين، يمكن تطبيق المزيد من المرشحات
      if (month) {
        where.exam = {
          month
        };
      }

      if (evaluationType) {
        where.exam = where.exam
          ? { ...where.exam as Record<string, unknown>, evaluationType }
          : { evaluationType };
      }
    }

    // إذا كان المستخدم معلمًا، قم بتقييد النتائج بطلاب المعلم
    if (isTeacher && !isAdmin) {
      const teacher = await prisma.teacher.findFirst({
        where: {
          userId: payload.id
        }
      });

      if (!teacher) {
        return NextResponse.json({
          error: 'لم يتم العثور على بيانات المعلم',
          success: false
        }, { status: 404 });
      }

      // الحصول على المواد التي يدرسها المعلم
      const teacherSubjects = await prisma.teacherSubject.findMany({
        where: {
          teacherId: teacher.id
        },
        select: {
          id: true
        }
      });

      // الحصول على الفصول المرتبطة بهذه المواد
      const teacherClasses = await prisma.classSubject.findMany({
        where: {
          teacherSubjectId: {
            in: teacherSubjects.map(ts => ts.id)
          }
        },
        select: {
          id: true
        }
      });

      const classSubjectIds = teacherClasses.map(tc => tc.id);

      where.classSubjectId = {
        in: classSubjectIds
      };
    } else if (teacherId) {
      // إذا كان المستخدم مديرًا وتم تحديد معلم معين
      const teacherSubjects = await prisma.teacherSubject.findMany({
        where: {
          teacherId: parseInt(teacherId)
        },
        select: {
          id: true
        }
      });

      // الحصول على الفصول المرتبطة بهذه المواد
      const teacherClasses = await prisma.classSubject.findMany({
        where: {
          teacherSubjectId: {
            in: teacherSubjects.map(ts => ts.id)
          }
        },
        select: {
          id: true
        }
      });

      const classSubjectIds = teacherClasses.map(tc => tc.id);

      where.classSubjectId = {
        in: classSubjectIds
      };
    }

    // إذا تم تحديد فصل معين
    if (classId) {
      const classSubjects = await prisma.classSubject.findMany({
        where: {
          classeId: parseInt(classId)
        },
        select: {
          id: true
        }
      });

      const classSubjectIds = classSubjects.map(cs => cs.id);

      where.classSubjectId = {
        in: classSubjectIds
      };
    }

    // الحصول على نقاط الامتحانات
    const examPoints = await prisma.exam_points.findMany({
      where,
      include: {
        exam: {
          include: {
            examType: true
          }
        },
        student: true,
        classSubject: {
          include: {
            classe: true,
            teacherSubject: {
              include: {
                teacher: true,
                subject: true
              }
            }
          }
        },
        studentAnswers: {
          include: {
            examQuestion: {
              include: {
                question: {
                  select: {
                    id: true,
                    type: true,
                    difficultyLevel: true
                  }
                }
              }
            }
          }
        }
      }
    });

    // تحليل البيانات
    const analysis = analyzeExamResults(examPoints as unknown as ExamPoint[]);

    return NextResponse.json({
      data: analysis,
      success: true,
      message: 'تم جلب تحليل النتائج بنجاح'
    });
  } catch (error) {
    console.error('Error fetching exam analysis:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب تحليل النتائج',
      success: false
    }, { status: 500 });
  }
}

// تعريف أنواع البيانات
interface ExamPoint {
  id: number;
  examId: number;
  studentId: number;
  classSubjectId: number;
  grade: number | string;
  status: string;
  studentAnswers: {
    examQuestion: {
      question: {
        id: number;
        type: string;
        difficultyLevel: string;
      }
    };
    isCorrect?: boolean;
    points?: number | string;
  }[];
  student: {
    id: number;
    name: string;
    gender?: string;
  };
  classSubject: {
    id: number;
    classe: {
      id: number;
      name: string;
    };
    subject?: {
      id: number;
      name: string;
    };
    teacher?: {
      id: number;
      name: string;
    };
  };
  exam: {
    id: number;
    evaluationType: string;
    examType?: {
      id: number;
      name: string;
    };
  };
}

// دالة تحليل نتائج الامتحانات
function analyzeExamResults(examPoints: ExamPoint[]) {
  // إحصائيات عامة
  const totalStudents = examPoints.length;
  const passedStudents = examPoints.filter(ep => ['PASSED', 'EXCELLENT'].includes(ep.status)).length;
  const failedStudents = examPoints.filter(ep => ep.status === 'FAILED').length;
  const excellentStudents = examPoints.filter(ep => ep.status === 'EXCELLENT').length;
  const pendingStudents = examPoints.filter(ep => ['PENDING', 'COMPLETED'].includes(ep.status)).length;

  // متوسط الدرجات
  const averageGrade = totalStudents > 0
    ? examPoints.reduce((sum, ep) => sum + Number(ep.grade), 0) / totalStudents
    : 0;

  // أعلى وأدنى درجة
  const grades = examPoints.map(ep => Number(ep.grade));
  const highestGrade = grades.length > 0 ? Math.max(...grades) : 0;
  const lowestGrade = grades.length > 0 ? Math.min(...grades) : 0;

  // توزيع الدرجات
  const gradeDistribution = {
    excellent: excellentStudents,
    veryGood: examPoints.filter(ep => Number(ep.grade) >= 8 && Number(ep.grade) < 9).length,
    good: examPoints.filter(ep => Number(ep.grade) >= 7 && Number(ep.grade) < 8).length,
    fair: examPoints.filter(ep => Number(ep.grade) >= 6 && Number(ep.grade) < 7).length,
    poor: examPoints.filter(ep => Number(ep.grade) >= 5 && Number(ep.grade) < 6).length,
    veryPoor: examPoints.filter(ep => Number(ep.grade) < 5).length
  };

  // تحليل حسب الفصول
  const classeAnalysis: Record<number, {
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    averageGrade: number;
    totalGrades: number;
  }> = {};

  examPoints.forEach(ep => {
    const classeId = ep.classSubject.classe.id;
    const classeName = ep.classSubject.classe.name;

    if (!classeAnalysis[classeId]) {
      classeAnalysis[classeId] = {
        id: classeId,
        name: classeName,
        totalStudents: 0,
        passedStudents: 0,
        failedStudents: 0,
        excellentStudents: 0,
        pendingStudents: 0,
        averageGrade: 0,
        totalGrades: 0
      };
    }

    classeAnalysis[classeId].totalStudents++;
    classeAnalysis[classeId].totalGrades += Number(ep.grade);

    if (['PASSED', 'EXCELLENT'].includes(ep.status)) {
      classeAnalysis[classeId].passedStudents++;
    }

    if (ep.status === 'FAILED') {
      classeAnalysis[classeId].failedStudents++;
    }

    if (ep.status === 'EXCELLENT') {
      classeAnalysis[classeId].excellentStudents++;
    }

    if (['PENDING', 'COMPLETED'].includes(ep.status)) {
      classeAnalysis[classeId].pendingStudents++;
    }
  });

  // حساب متوسط الدرجات لكل فصل
  Object.keys(classeAnalysis).forEach(classeIdStr => {
    const classeId = parseInt(classeIdStr);
    const classe = classeAnalysis[classeId];
    classe.averageGrade = classe.totalStudents > 0
      ? classe.totalGrades / classe.totalStudents
      : 0;
  });

  // تحليل حسب المعلمين
  const teacherAnalysis: Record<number, {
    id: number;
    name: string;
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    pendingStudents: number;
    averageGrade: number;
    totalGrades: number;
  }> = {};

  examPoints.forEach(ep => {
    if (!ep.classSubject.teacher) return;

    const teacherId = ep.classSubject.teacher.id;
    const teacherName = ep.classSubject.teacher.name;

    if (!teacherAnalysis[teacherId]) {
      teacherAnalysis[teacherId] = {
        id: teacherId,
        name: teacherName,
        totalStudents: 0,
        passedStudents: 0,
        failedStudents: 0,
        excellentStudents: 0,
        pendingStudents: 0,
        averageGrade: 0,
        totalGrades: 0
      };
    }

    teacherAnalysis[teacherId].totalStudents++;
    teacherAnalysis[teacherId].totalGrades += Number(ep.grade);

    if (['PASSED', 'EXCELLENT'].includes(ep.status)) {
      teacherAnalysis[teacherId].passedStudents++;
    }

    if (ep.status === 'FAILED') {
      teacherAnalysis[teacherId].failedStudents++;
    }

    if (ep.status === 'EXCELLENT') {
      teacherAnalysis[teacherId].excellentStudents++;
    }

    if (['PENDING', 'COMPLETED'].includes(ep.status)) {
      teacherAnalysis[teacherId].pendingStudents++;
    }
  });

  // حساب متوسط الدرجات لكل معلم
  Object.keys(teacherAnalysis).forEach(teacherIdStr => {
    const teacherId = parseInt(teacherIdStr);
    const teacher = teacherAnalysis[teacherId];
    teacher.averageGrade = teacher.totalStudents > 0
      ? teacher.totalGrades / teacher.totalStudents
      : 0;
  });

  // تحليل حسب نوع السؤال
  const questionTypeAnalysis: Record<string, {
    type: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    pendingAnswers: number;
    averagePoints: number;
    totalPoints: number;
  }> = {};

  examPoints.forEach(ep => {
    ep.studentAnswers.forEach((sa: {
      examQuestion: {
        question: {
          type: string;
        }
      };
      isCorrect?: boolean;
      points?: number | string;
    }) => {
      const questionType = sa.examQuestion.question.type;

      if (!questionTypeAnalysis[questionType]) {
        questionTypeAnalysis[questionType] = {
          type: questionType,
          totalAnswers: 0,
          correctAnswers: 0,
          incorrectAnswers: 0,
          pendingAnswers: 0,
          averagePoints: 0,
          totalPoints: 0
        };
      }

      questionTypeAnalysis[questionType].totalAnswers++;

      if (sa.isCorrect === true) {
        questionTypeAnalysis[questionType].correctAnswers++;
        questionTypeAnalysis[questionType].totalPoints += Number(sa.points || 0);
      } else if (sa.isCorrect === false) {
        questionTypeAnalysis[questionType].incorrectAnswers++;
        questionTypeAnalysis[questionType].totalPoints += Number(sa.points || 0);
      } else {
        questionTypeAnalysis[questionType].pendingAnswers++;
      }
    });
  });

  // حساب متوسط النقاط لكل نوع سؤال
  Object.keys(questionTypeAnalysis).forEach(questionType => {
    const analysis = questionTypeAnalysis[questionType];
    const answeredQuestions = analysis.correctAnswers + analysis.incorrectAnswers;
    analysis.averagePoints = answeredQuestions > 0
      ? analysis.totalPoints / answeredQuestions
      : 0;
  });

  // تحليل حسب مستوى الصعوبة
  const difficultyAnalysis: Record<string, {
    level: string;
    totalAnswers: number;
    correctAnswers: number;
    incorrectAnswers: number;
    pendingAnswers: number;
    averagePoints: number;
    totalPoints: number;
  }> = {};

  examPoints.forEach(ep => {
    ep.studentAnswers.forEach((sa: {
      examQuestion: {
        question: {
          difficultyLevel: string;
        }
      };
      isCorrect?: boolean;
      points?: number | string;
    }) => {
      const difficultyLevel = sa.examQuestion.question.difficultyLevel;

      if (!difficultyAnalysis[difficultyLevel]) {
        difficultyAnalysis[difficultyLevel] = {
          level: difficultyLevel,
          totalAnswers: 0,
          correctAnswers: 0,
          incorrectAnswers: 0,
          pendingAnswers: 0,
          averagePoints: 0,
          totalPoints: 0
        };
      }

      difficultyAnalysis[difficultyLevel].totalAnswers++;

      if (sa.isCorrect === true) {
        difficultyAnalysis[difficultyLevel].correctAnswers++;
        difficultyAnalysis[difficultyLevel].totalPoints += Number(sa.points || 0);
      } else if (sa.isCorrect === false) {
        difficultyAnalysis[difficultyLevel].incorrectAnswers++;
        difficultyAnalysis[difficultyLevel].totalPoints += Number(sa.points || 0);
      } else {
        difficultyAnalysis[difficultyLevel].pendingAnswers++;
      }
    });
  });

  // حساب متوسط النقاط لكل مستوى صعوبة
  Object.keys(difficultyAnalysis).forEach(difficultyLevel => {
    const analysis = difficultyAnalysis[difficultyLevel];
    const answeredQuestions = analysis.correctAnswers + analysis.incorrectAnswers;
    analysis.averagePoints = answeredQuestions > 0
      ? analysis.totalPoints / answeredQuestions
      : 0;
  });

  // تحليل حسب الجنس
  type GenderType = 'MALE' | 'FEMALE';
  type GenderAnalysis = {
    [key in GenderType]: {
      totalStudents: number;
      passedStudents: number;
      failedStudents: number;
      excellentStudents: number;
      pendingStudents: number;
      averageGrade: number;
      totalGrades: number;
    }
  };

  const genderAnalysis: GenderAnalysis = {
    MALE: {
      totalStudents: 0,
      passedStudents: 0,
      failedStudents: 0,
      excellentStudents: 0,
      pendingStudents: 0,
      averageGrade: 0,
      totalGrades: 0
    },
    FEMALE: {
      totalStudents: 0,
      passedStudents: 0,
      failedStudents: 0,
      excellentStudents: 0,
      pendingStudents: 0,
      averageGrade: 0,
      totalGrades: 0
    }
  };

  examPoints.forEach(ep => {
    const gender = (ep.student.gender as GenderType) || 'MALE'; // افتراضي ذكر إذا لم يكن محددًا

    genderAnalysis[gender].totalStudents++;
    genderAnalysis[gender].totalGrades += Number(ep.grade);

    if (['PASSED', 'EXCELLENT'].includes(ep.status)) {
      genderAnalysis[gender].passedStudents++;
    }

    if (ep.status === 'FAILED') {
      genderAnalysis[gender].failedStudents++;
    }

    if (ep.status === 'EXCELLENT') {
      genderAnalysis[gender].excellentStudents++;
    }

    if (['PENDING', 'COMPLETED'].includes(ep.status)) {
      genderAnalysis[gender].pendingStudents++;
    }
  });

  // حساب متوسط الدرجات لكل جنس
  Object.keys(genderAnalysis).forEach(gender => {
    const genderKey = gender as GenderType;
    const analysis = genderAnalysis[genderKey];
    analysis.averageGrade = analysis.totalStudents > 0
      ? analysis.totalGrades / analysis.totalStudents
      : 0;
  });

  // تجميع النتائج
  return {
    summary: {
      totalStudents,
      passedStudents,
      failedStudents,
      excellentStudents,
      pendingStudents,
      passRate: totalStudents > 0 ? (passedStudents / totalStudents) * 100 : 0,
      averageGrade,
      highestGrade,
      lowestGrade
    },
    gradeDistribution,
    classeAnalysis: Object.values(classeAnalysis),
    teacherAnalysis: Object.values(teacherAnalysis),
    questionTypeAnalysis: Object.values(questionTypeAnalysis),
    difficultyAnalysis: Object.values(difficultyAnalysis),
    genderAnalysis
  };
}

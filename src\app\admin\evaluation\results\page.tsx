'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Eye, Download, PlusCircle, Edit, Pencil } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import * as XLSX from 'xlsx';
import { EvaluationType } from '@prisma/client';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

type Exam = {
  id: number;
  month: string;
  evaluationType: EvaluationType;
  description: string | null;
  requiresSurah?: boolean;
  examType: {
    id: number;
    name: string;
  } | null;
};
/*
// دالة لتحويل نوع التقييم إلى نص مقروء
const getExamTypeLabel = (type: EvaluationType): string => {
  const labels: Partial<Record<EvaluationType, string>> = {
    QURAN_MEMORIZATION: 'حفظ القرآن',
    QURAN_RECITATION: 'تلاوة القرآن',
    WRITTEN_EXAM: 'تحريري',
    ORAL_EXAM: 'شفهي',
    PRACTICAL_TEST: 'عملي',
    HOMEWORK: 'واجب منزلي',
    PROJECT: 'مشروع',
    REMOTE_EXAM: 'امتحان عن بعد'
  };
  return labels[type] || String(type);
};

// دالة لتنسيق التاريخ
const formatDate = (dateString: string): string => {
  const [year, month] = dateString.split('-');
  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  return `${monthNames[parseInt(month) - 1]} ${year}`;
};

// دالة لتحويل حالة الامتحان إلى نص مقروء
const getStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    EXCELLENT: 'ممتاز',
    PASSED: 'ناجح',
    FAILED: 'راسب',
    PENDING: 'قيد الانتظار'
  };
  return labels[status] || status;
};

// دالة لتحديد لون الدرجة - تم إزالتها لتجنب التكرار

// دالة لتحديد لون حالة الامتحان
const getStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    EXCELLENT: 'bg-purple-100 text-purple-800',
    PASSED: 'bg-green-100 text-green-800',
    FAILED: 'bg-red-100 text-red-800',
    PENDING: 'bg-yellow-100 text-yellow-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};
*/
type Student = {
  id: number;
  name: string;
  username: string;
  classe: {
    id: number;
    name: string;
  } | null;
};

type ExamPoint = {
  id: number;
  examId: number;
  studentId: number;
  grade: number;
  status: string;
  note?: string | null;
  student: Student;
  classSubject?: {
    classe?: {
      id: number;
      name: string;
    } | null;
  } | null;
  criteriaScores: CriteriaScore[];
  surahId?: number | null;
  startVerse?: number | null;
  endVerse?: number | null;
  surah?: {
    id: number;
    name: string;
  } | null;
};

type CriteriaScore = {
  id: number;
  examPointId: number;
  criteriaId: number;
  score: number;
  criteria: {
    id: number;
    name: string;
    weight: number;
  };
};

export default function ExamResultsPage() {
  const router = useRouter();
  const [exams, setExams] = useState<Exam[]>([]);
  const [selectedExamId, setSelectedExamId] = useState<string>('all');
  const [selectedClassId, setSelectedClassId] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [examPoints, setExamPoints] = useState<ExamPoint[]>([]);
  const [filteredExamPoints, setFilteredExamPoints] = useState<ExamPoint[]>([]);
  const [classes, setClasses] = useState<{id: number, name: string}[]>([]);
  const [loading, setLoading] = useState(true);
  const [resultsLoading, setResultsLoading] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedExamPoint, setSelectedExamPoint] = useState<ExamPoint | null>(null);
  const [editGrade, setEditGrade] = useState<string>('');
  const [editNote, setEditNote] = useState<string>('');
  const [editCriteriaScores, setEditCriteriaScores] = useState<{criteriaId: number, score: number}[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchExams();
    fetchClasses();
  }, []);

  useEffect(() => {
    if (selectedExamId && selectedExamId !== 'all') {
      fetchExamResults(selectedExamId);
    } else {
      setExamPoints([]);
      setFilteredExamPoints([]);
    }
  }, [selectedExamId]);

  // تصفية النتائج عند تغيير المعايير
  useEffect(() => {
    filterExamPoints();
  }, [examPoints, selectedClassId, selectedStatus, searchQuery]);

  const fetchExams = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/evaluation/exams');
      if (!response.ok) throw new Error('Failed to fetch exams');
      const data = await response.json();
      setExams(data.data || []);
    } catch (error) {
      console.error('Error fetching exams:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب الامتحانات',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();

      // تصحيح استخدام البيانات المستلمة من API
      console.log('Classes data received:', data);

      if (data && data.classes && Array.isArray(data.classes)) {
        setClasses(data.classes);
      } else if (Array.isArray(data)) {
        setClasses(data);
      } else if (data && Array.isArray(data.data)) {
        setClasses(data.data);
      } else {
        console.error('Unexpected classes data format:', data);
        setClasses([]);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب الأقسام',
        variant: 'destructive'
      });
    }
  };

  const fetchExamResults = async (examId: string) => {
    try {
      setResultsLoading(true);
      console.log('Fetching exam results for examId:', examId);
      const response = await fetch(`/api/evaluation/exam-points?examId=${examId}`);

      if (!response.ok) {
        console.error('API response not OK:', response.status, response.statusText);
        throw new Error('Failed to fetch exam results');
      }

      const data = await response.json();
      console.log('Received exam points data:', data);

      if (data.success && Array.isArray(data.data)) {
        setExamPoints(data.data);
        setFilteredExamPoints(data.data);
      } else {
        console.error('Invalid data format received:', data);
        setExamPoints([]);
        setFilteredExamPoints([]);
        toast({
          title: 'خطأ',
          description: 'تنسيق البيانات المستلمة غير صالح',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error fetching exam results:', error);
      setExamPoints([]);
      setFilteredExamPoints([]);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب نتائج الامتحان',
        variant: 'destructive'
      });
    } finally {
      setResultsLoading(false);
    }
  };

  const filterExamPoints = () => {
    if (!examPoints.length) {
      setFilteredExamPoints([]);
      return;
    }

    let filtered = [...examPoints];

    // تصفية حسب القسم
    if (selectedClassId !== 'all') {
      filtered = filtered.filter(point => {
        const classId = point.student?.classe?.id || point.classSubject?.classe?.id;
        return classId && classId.toString() === selectedClassId;
      });
    }

    // تصفية حسب الحالة
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(point => point.status === selectedStatus);
    }

    // تصفية حسب البحث
    if (searchQuery.trim()) {
      const query = searchQuery.trim().toLowerCase();
      filtered = filtered.filter(point =>
        point.student.name.toLowerCase().includes(query) ||
        point.student.username.toLowerCase().includes(query)
      );
    }

    setFilteredExamPoints(filtered);
  };

  const getExamTypeLabel = (type: string) => {
    switch (type) {
      case 'WRITTEN_EXAM':
        return 'امتحان تحريري';
      case 'ORAL_EXAM':
        return 'امتحان شفهي';
      case 'QURAN_RECITATION':
        return 'تلاوة القرآن';
      case 'QURAN_MEMORIZATION':
        return 'حفظ القرآن';
      case 'PRACTICAL_TEST':
        return 'اختبار عملي';
      default:
        return type;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'قيد الانتظار';
      case 'PASSED':
        return 'ناجح';
      case 'FAILED':
        return 'راسب';
      case 'EXCELLENT':
        return 'ممتاز';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'PASSED':
        return 'bg-green-100 text-green-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      case 'EXCELLENT':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getGradeColor = (grade: number) => {
    if (grade >= 9) return 'text-purple-600 font-bold';
    if (grade >= 8) return 'text-primary-color font-bold';
    if (grade >= 7) return 'text-blue-600 font-bold';
    if (grade >= 6) return 'text-yellow-600 font-bold';
    return 'text-red-600 font-bold';
  };

  const formatDate = (dateString: string) => {
    const [year, month] = dateString.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' });
  };

  const openDetailsDialog = (examPoint: ExamPoint) => {
    setSelectedExamPoint(examPoint);
    setIsDetailsDialogOpen(true);
  };

  const openEditDialog = (examPoint: ExamPoint) => {
    setSelectedExamPoint(examPoint);
    setEditGrade(Number(examPoint.grade).toString());
    setEditNote(examPoint.note || '');

    // إعداد درجات معايير التقييم للتعديل
    if (examPoint.criteriaScores && examPoint.criteriaScores.length > 0) {
      setEditCriteriaScores(
        examPoint.criteriaScores.map(score => ({
          criteriaId: score.criteriaId,
          score: Number(score.score)
        }))
      );
    } else {
      setEditCriteriaScores([]);
    }

    setIsEditDialogOpen(true);
  };

  const handleUpdateCriteriaScore = (criteriaId: number, newScore: number) => {
    setEditCriteriaScores(prev =>
      prev.map(item =>
        item.criteriaId === criteriaId
          ? { ...item, score: newScore }
          : item
      )
    );

    // حساب الدرجة الإجمالية الجديدة بناءً على معايير التقييم
    if (selectedExamPoint?.criteriaScores && selectedExamPoint.criteriaScores.length > 0) {
      const updatedScores = editCriteriaScores.map(item =>
        item.criteriaId === criteriaId
          ? { ...item, score: newScore }
          : item
      );

      // حساب الدرجة الإجمالية المرجحة
      let totalWeight = 0;
      let weightedSum = 0;

      selectedExamPoint.criteriaScores.forEach(criteria => {
        const updatedScore = updatedScores.find(s => s.criteriaId === criteria.criteriaId);
        if (updatedScore) {
          const weight = Number(criteria.criteria.weight);
          totalWeight += weight;
          weightedSum += updatedScore.score * weight;
        }
      });

      // تحديث الدرجة الإجمالية
      if (totalWeight > 0) {
        const newTotalGrade = (weightedSum / totalWeight).toFixed(2);
        setEditGrade(newTotalGrade);
      }
    }
  };

  const handleUpdateGrade = async () => {
    if (!selectedExamPoint) return;

    // التحقق من صحة الدرجة
    const numericGrade = Number(editGrade);
    if (isNaN(numericGrade) || numericGrade < 0 || numericGrade > 10) {
      toast({
        title: 'خطأ',
        description: 'يجب أن تكون الدرجة رقماً بين 0 و 10',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // تحديد الحالة الجديدة بناءً على الدرجة
      let newStatus = 'PENDING';
      if (numericGrade >= 9) {
        newStatus = 'EXCELLENT';
      } else if (numericGrade >= 6) {
        newStatus = 'PASSED';
      } else {
        newStatus = 'FAILED';
      }

      // الحصول على الامتحان المحدد
      const selectedExam = exams.find(exam => exam.id === selectedExamPoint.examId);
      const isQuranMemorizationExam = selectedExam?.evaluationType === 'QURAN_MEMORIZATION' || selectedExam?.requiresSurah;

      // إعداد البيانات للإرسال
      const updateData: {
        id: number;
        grade: number;
        note: string;
        status: string;
        criteriaScores?: typeof editCriteriaScores;
        surahId?: number;
        startVerse?: number;
        endVerse?: number;
      } = {
        id: selectedExamPoint.id,
        grade: numericGrade,
        note: editNote,
        status: newStatus
      };

      // إضافة درجات معايير التقييم إذا كانت موجودة
      if (editCriteriaScores.length > 0) {
        updateData.criteriaScores = editCriteriaScores;
      }

      // إضافة معلومات السورة والآيات إذا كان الامتحان من نوع حفظ القرآن
      if (isQuranMemorizationExam) {
        // استخدام معلومات السورة والآيات الموجودة في نقطة الامتحان الأصلية
        if (selectedExamPoint.surahId) {
          console.log('إضافة معلومات السورة والآيات:', {
            surahId: selectedExamPoint.surahId,
            startVerse: selectedExamPoint.startVerse || 1,
            endVerse: selectedExamPoint.endVerse || 1
          });

          updateData.surahId = selectedExamPoint.surahId;
          updateData.startVerse = selectedExamPoint.startVerse || 1;
          updateData.endVerse = selectedExamPoint.endVerse || 1;
        } else {
          console.warn('امتحان حفظ القرآن بدون معلومات السورة والآيات:', selectedExamPoint);
          toast({
            title: 'تنبيه',
            description: 'هذا الامتحان يتطلب تحديد السورة والآيات. يرجى استخدام صفحة تسجيل النقاط لإضافة هذه المعلومات.',
            variant: 'destructive'
          });
          setIsSubmitting(false);
          return; // إيقاف العملية إذا كانت معلومات السورة غير موجودة
        }
      }

      const response = await fetch(`/api/exam-points`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData?.error || 'فشل في تحديث النقاط');
      }

      // تم تحديث النقاط بنجاح، لا حاجة لقراءة النتيجة
      await response.json(); // استهلاك الاستجابة

      // تحديث النتائج في الواجهة
      const updatedPoint = {
        ...selectedExamPoint,
        grade: numericGrade,
        note: editNote,
        status: newStatus
      };

      // تحديث درجات معايير التقييم إذا كانت موجودة
      if (editCriteriaScores.length > 0 && selectedExamPoint.criteriaScores) {
        updatedPoint.criteriaScores = selectedExamPoint.criteriaScores.map(cs => {
          const updatedScore = editCriteriaScores.find(es => es.criteriaId === cs.criteriaId);
          if (updatedScore) {
            return {
              ...cs,
              score: updatedScore.score
            };
          }
          return cs;
        });
      }

      setExamPoints(prevPoints =>
        prevPoints.map(point =>
          point.id === selectedExamPoint.id
            ? updatedPoint
            : point
        )
      );

      // تحديث القائمة المصفاة أيضًا
      setFilteredExamPoints(prevPoints =>
        prevPoints.map(point =>
          point.id === selectedExamPoint.id
            ? updatedPoint
            : point
        )
      );

      // إذا كان النقطة المحددة مفتوحة في نافذة التفاصيل، قم بتحديثها أيضًا
      if (isDetailsDialogOpen) {
        setSelectedExamPoint(updatedPoint);
      }

      toast({
        title: 'نجاح',
        description: 'تم تحديث نقاط الطالب بنجاح'
      });
      setIsEditDialogOpen(false);
    } catch (err) {
      console.error('Error updating grade:', err);
      toast({
        title: 'خطأ',
        description: err instanceof Error ? err.message : 'فشل في تحديث النقاط',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const exportToExcel = () => {
    try {
      if (filteredExamPoints.length === 0) {
        toast({
          title: 'خطأ',
          description: 'لا توجد بيانات للتصدير',
          variant: 'destructive'
        });
        return;
      }

      // الحصول على الامتحان المحدد
      const selectedExam = exams.find(exam => exam.id.toString() === selectedExamId);
      if (!selectedExam) {
        toast({
          title: 'خطأ',
          description: 'لم يتم العثور على الامتحان المحدد',
          variant: 'destructive'
        });
        return;
      }

      // تحويل البيانات إلى تنسيق مناسب للتصدير
      const dataToExport = filteredExamPoints.map(point => {
        // البيانات الأساسية
        const baseData: Record<string, string> = {
          'اسم الطالب': point.student.name,
          'القسم': point.student?.classe?.name || point.classSubject?.classe?.name || '-',
          'الدرجة النهائية': Number(point.grade).toFixed(2),
          'الحالة': getStatusLabel(point.status),
          'الملاحظات': point.note || '-'
        };

        // إضافة درجات معايير التقييم إذا كانت موجودة
        if (point.criteriaScores && point.criteriaScores.length > 0) {
          point.criteriaScores.forEach(score => {
            const key = `${score.criteria.name} (${(Number(score.criteria.weight) * 100).toFixed(0)}%)`;
            baseData[key] = Number(score.score).toFixed(2);
          });
        }

        return baseData;
      });

      // إنشاء ورقة عمل Excel
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'نتائج الامتحان');

      // تعيين عرض الأعمدة - تحديد عرض ديناميكي بناءً على عدد الأعمدة
      const columnWidths = [
        { wch: 25 }, // اسم الطالب
        { wch: 15 }, // القسم
        { wch: 12 }, // الدرجة النهائية
        { wch: 12 }, // الحالة
        { wch: 30 }  // الملاحظات
      ];

      // إضافة عرض لأعمدة معايير التقييم
      if (filteredExamPoints[0]?.criteriaScores?.length > 0) {
        for (let i = 0; i < filteredExamPoints[0].criteriaScores.length; i++) {
          columnWidths.push({ wch: 15 }); // عرض لكل معيار تقييم
        }
      }

      worksheet['!cols'] = columnWidths;

      // تصدير الملف
      const examType = getExamTypeLabel(selectedExam.evaluationType);
      const examDate = formatDate(selectedExam.month);
      const fileName = `نتائج_${examType}_${examDate}.xlsx`;

      XLSX.writeFile(workbook, fileName);
      toast({
        title: 'نجاح',
        description: 'تم تصدير النتائج بنجاح'
      });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء تصدير النتائج',
        variant: 'destructive'
      });
    }
  };

  // وظيفة لتصدير تقرير مفصل بتنسيق PDF سيتم تنفيذها لاحقًا
  // const exportDetailedReport = () => {
  //   try {
  //     if (filteredExamPoints.length === 0) {
  //       toast({
  //         title: 'خطأ',
  //         description: 'لا توجد بيانات للتصدير',
  //         variant: 'destructive'
  //       });
  //       return;
  //     }

  //     // هنا يمكن إضافة كود لتصدير تقرير مفصل بتنسيق PDF
  //     // باستخدام مكتبة مثل jsPDF أو pdfmake

  //     toast({
  //       title: 'نجاح',
  //       description: 'سيتم تنفيذ هذه الميزة قريباً'
  //     });
  //   } catch (error) {
  //     console.error('Error exporting detailed report:', error);
  //     toast({
  //       title: 'خطأ',
  //       description: 'حدث خطأ أثناء تصدير التقرير المفصل',
  //       variant: 'destructive'
  //     });
  //   }
  // };

  const renderHeader = () => (
    <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
      <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">نتائج الامتحانات</h1>
      <div className="flex flex-col sm:flex-row w-full md:w-auto gap-2">
        <QuickActionButtons
          entityType="evaluation.results"
          actions={[
            ...(selectedExamId && selectedExamId !== 'all' ? [{
              key: 'add-points',
              label: 'تسجيل النقاط',
              icon: <Edit size={16} />,
              onClick: () => router.push(`/admin/evaluation/scoring?examId=${selectedExamId}`),
              variant: 'primary' as const
            }] : []),
            {
              key: 'export',
              label: 'تصدير النتائج',
              icon: <Download size={16} />,
              onClick: exportToExcel,
              variant: 'outline' as const,
              disabled: !selectedExamId || selectedExamId === 'all' || examPoints.length === 0
            }
          ]}
          className="flex gap-2 w-full sm:w-auto"
        />
      </div>
    </div>
  );

  return (
    <OptimizedProtectedRoute requiredPermission="admin.evaluation.results.view">
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      {renderHeader()}

      <Card className="mb-6 border-t-2 border-[var(--primary-color)]">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-right mb-2 font-medium">اختر الامتحان</label>
              <Select
                value={selectedExamId}
                onValueChange={setSelectedExamId}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر الامتحان" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الامتحانات</SelectItem>
                  {exams.map((exam) => (
                    <SelectItem key={exam.id} value={exam.id.toString()}>
                      {getExamTypeLabel(exam.evaluationType)} - {formatDate(exam.month)}
                      {exam.examType ? ` (${exam.examType.name})` : ''}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-right mb-2 font-medium">القسم</label>
              <Select
                value={selectedClassId}
                onValueChange={setSelectedClassId}
                disabled={selectedExamId === 'all'}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر القسم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الأقسام</SelectItem>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-right mb-2 font-medium">الحالة</label>
              <Select
                value={selectedStatus}
                onValueChange={setSelectedStatus}
                disabled={selectedExamId === 'all'}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر الحالة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">جميع الحالات</SelectItem>
                  <SelectItem value="EXCELLENT">ممتاز</SelectItem>
                  <SelectItem value="PASSED">ناجح</SelectItem>
                  <SelectItem value="FAILED">راسب</SelectItem>
                  <SelectItem value="PENDING">قيد الانتظار</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-right mb-2 font-medium">بحث</label>
              <Input
                type="text"
                placeholder="ابحث عن طالب..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="text-right"
                disabled={selectedExamId === 'all'}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {loading ? (
        <div className="flex flex-col justify-center items-center h-64 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
          <div className="relative">
            <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
            <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
          </div>
          <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري تحميل الامتحانات...</span>
        </div>
      ) : (
        <Tabs defaultValue="results">
          <TabsList className="mb-4 bg-[#e9f7f5] p-1 rounded-xl">
            <TabsTrigger value="results" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">النتائج</TabsTrigger>
            <TabsTrigger value="stats" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">الإحصائيات</TabsTrigger>
          </TabsList>

          <TabsContent value="results">
            {resultsLoading ? (
              <div className="flex flex-col justify-center items-center h-64 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
                <div className="relative">
                  <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
                  <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
                </div>
                <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري تحميل النتائج...</span>
              </div>
            ) : selectedExamId && selectedExamId !== 'all' ? (
              filteredExamPoints.length === 0 ? (
                examPoints.length === 0 ? (
                  <div className="text-center p-8 border rounded-lg bg-[#f8fffd] border-[#e9f7f5]">
                    <p className="text-gray-500 mb-4">لا توجد نتائج لهذا الامتحان حتى الآن</p>
                    <Button
                      variant="default"
                      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                      onClick={() => router.push(`/admin/evaluation/scoring?examId=${selectedExamId}`)}
                    >
                      <PlusCircle className="ml-2" size={16} />
                      تسجيل النقاط
                    </Button>
                  </div>
                ) : (
                  <div className="text-center p-8 border rounded-lg bg-[#f8fffd] border-[#e9f7f5]">
                    <p className="text-gray-500 mb-4">لا توجد نتائج تطابق معايير التصفية</p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedClassId('all');
                        setSelectedStatus('all');
                        setSearchQuery('');
                      }}
                    >
                      إعادة ضبط التصفية
                    </Button>
                  </div>
                )
              ) : (
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg">نتائج الامتحان ({filteredExamPoints.length} طالب)</CardTitle>
                      <div className="text-sm text-gray-500">
                        {selectedClassId !== 'all' && `القسم: ${classes.find(c => c.id.toString() === selectedClassId)?.name || ''} | `}
                        {selectedStatus !== 'all' && `الحالة: ${getStatusLabel(selectedStatus)} | `}
                        {searchQuery && `بحث: ${searchQuery}`}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {/* عرض الجدول على الشاشات الكبيرة */}
                    <div className="hidden md:block overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="text-right">الطالب</TableHead>
                            <TableHead className="text-right">القسم</TableHead>
                            <TableHead className="text-right">الدرجة</TableHead>
                            <TableHead className="text-right">الحالة</TableHead>
                            <TableHead className="text-right">التفاصيل</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredExamPoints.map((point) => (
                            <TableRow key={point.id}>
                              <TableCell>{point.student.name}</TableCell>
                              <TableCell>{point.student?.classe?.name || point.classSubject?.classe?.name || '-'}</TableCell>
                              <TableCell className={getGradeColor(Number(point.grade))}>
                                {Number(point.grade).toFixed(2)}
                              </TableCell>
                              <TableCell>
                                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(point.status)}`}>
                                  {getStatusLabel(point.status)}
                                </span>
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => openDetailsDialog(point)}
                                  >
                                    <Eye className="h-4 w-4 ml-1" />
                                    التفاصيل
                                  </Button>
                                  <OptimizedActionButtonGroup
                                    entityType="evaluation.results"
                                    onEdit={() => openEditDialog(point)}
                                    showEdit={true}
                                    showDelete={false}
                                    size="sm"
                                    className="gap-2"
                                  />
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* عرض البطاقات على الشاشات الصغيرة */}
                    <div className="md:hidden space-y-4">
                      {filteredExamPoints.map((point) => (
                        <Card key={point.id} className="border border-[#e9f7f5] shadow-sm">
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg flex justify-between items-center">
                              <span>{point.student.name}</span>
                              <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(point.status)}`}>
                                {getStatusLabel(point.status)}
                              </span>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pb-2 space-y-3">
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div>
                                <span className="font-medium">القسم:</span>
                                <span className="block">{point.student?.classe?.name || point.classSubject?.classe?.name || '-'}</span>
                              </div>
                              <div>
                                <span className="font-medium">الدرجة:</span>
                                <span className={`block ${getGradeColor(Number(point.grade))}`}>
                                  {Number(point.grade).toFixed(2)}
                                </span>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-2 pt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => openDetailsDialog(point)}
                                className="w-full"
                              >
                                <Eye className="h-4 w-4 ml-1" />
                                التفاصيل
                              </Button>
                              <OptimizedActionButtonGroup
                                entityType="evaluation.results"
                                onEdit={() => openEditDialog(point)}
                                showEdit={true}
                                showDelete={false}
                                size="sm"
                                className="w-full"
                              />
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )
            ) : (
              <div className="text-center p-8 border rounded-lg bg-[#f8fffd] border-[#e9f7f5]">
                <p className="text-gray-500">يرجى اختيار امتحان لعرض النتائج</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="stats">
            {selectedExamId && selectedExamId !== 'all' ? (
              filteredExamPoints.length > 0 ? (
                <>
                  <Card className="mb-4">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-lg">إحصائيات الامتحان</CardTitle>
                        <div className="text-sm text-gray-500">
                          {selectedClassId !== 'all' && `القسم: ${classes.find(c => c.id.toString() === selectedClassId)?.name || ''} | `}
                          {selectedStatus !== 'all' && `الحالة: ${getStatusLabel(selectedStatus)} | `}
                          {searchQuery && `بحث: ${searchQuery}`}
                        </div>
                      </div>
                    </CardHeader>
                  </Card>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">متوسط الدرجات</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold text-[var(--primary-color)]">
                          {(filteredExamPoints.reduce((sum, point) => sum + Number(point.grade), 0) / filteredExamPoints.length).toFixed(1)}
                        </div>
                        <p className="text-sm text-gray-500">من 10 درجات</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">ممتاز</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold text-purple-600">
                          {filteredExamPoints.filter(point => point.status === 'EXCELLENT').length}
                        </div>
                        <p className="text-sm text-gray-500">طالب</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">ناجح</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold text-primary-color">
                          {filteredExamPoints.filter(point => point.status === 'PASSED').length}
                        </div>
                        <p className="text-sm text-gray-500">طالب</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">راسب</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold text-red-600">
                          {filteredExamPoints.filter(point => point.status === 'FAILED').length}
                        </div>
                        <p className="text-sm text-gray-500">طالب</p>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">توزيع الدرجات</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-right">ممتاز (9-10)</span>
                              <span className="text-sm font-medium text-right">
                                {filteredExamPoints.filter(point => Number(point.grade) >= 9).length} طلاب
                              </span>
                            </div>
                            <Progress
                              value={(filteredExamPoints.filter(point => Number(point.grade) >= 9).length / filteredExamPoints.length) * 100}
                              className="h-2 bg-gray-200"
                            />
                          </div>
                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-right">جيد جدًا (8-9)</span>
                              <span className="text-sm font-medium text-right">
                                {filteredExamPoints.filter(point => Number(point.grade) >= 8 && Number(point.grade) < 9).length} طلاب
                              </span>
                            </div>
                            <Progress
                              value={(filteredExamPoints.filter(point => Number(point.grade) >= 8 && Number(point.grade) < 9).length / filteredExamPoints.length) * 100}
                              className="h-2 bg-gray-200"
                            />
                          </div>
                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-right">جيد (7-8)</span>
                              <span className="text-sm font-medium text-right">
                                {filteredExamPoints.filter(point => Number(point.grade) >= 7 && Number(point.grade) < 8).length} طلاب
                              </span>
                            </div>
                            <Progress
                              value={(filteredExamPoints.filter(point => Number(point.grade) >= 7 && Number(point.grade) < 8).length / filteredExamPoints.length) * 100}
                              className="h-2 bg-gray-200"
                            />
                          </div>
                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-right">مقبول (6-7)</span>
                              <span className="text-sm font-medium text-right">
                                {filteredExamPoints.filter(point => Number(point.grade) >= 6 && Number(point.grade) < 7).length} طلاب
                              </span>
                            </div>
                            <Progress
                              value={(filteredExamPoints.filter(point => Number(point.grade) >= 6 && Number(point.grade) < 7).length / filteredExamPoints.length) * 100}
                              className="h-2 bg-gray-200"
                            />
                          </div>
                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium text-right">راسب (أقل من 6)</span>
                              <span className="text-sm font-medium text-right">
                                {filteredExamPoints.filter(point => Number(point.grade) < 6).length} طلاب
                              </span>
                            </div>
                            <Progress
                              value={(filteredExamPoints.filter(point => Number(point.grade) < 6).length / filteredExamPoints.length) * 100}
                              className="h-2 bg-gray-200"
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* إضافة رسم بياني أو إحصائيات إضافية */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">معايير التقييم</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {filteredExamPoints[0]?.criteriaScores && filteredExamPoints[0].criteriaScores.length > 0 ? (
                          <div className="space-y-4">
                            {filteredExamPoints[0].criteriaScores.map(criteria => {
                              // حساب متوسط الدرجات لهذا المعيار
                              const criteriaAvg = filteredExamPoints.reduce((sum, point) => {
                                const score = point.criteriaScores.find(cs => cs.criteriaId === criteria.criteriaId);
                                return sum + (score ? Number(score.score) : 0);
                              }, 0) / filteredExamPoints.length;

                              return (
                                <div key={criteria.criteriaId} className="space-y-1">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-right">
                                      {criteriaAvg.toFixed(2)} / 10
                                    </span>
                                    <span className="text-sm font-medium text-right">
                                      {criteria.criteria.name} ({(Number(criteria.criteria.weight) * 100).toFixed(0)}%)
                                    </span>
                                  </div>
                                  <Progress
                                    value={criteriaAvg * 10}
                                    className="h-2 bg-gray-200"
                                  />
                                </div>
                              );
                            })}
                          </div>
                        ) : (
                          <div className="text-center text-gray-500 py-8">
                            لا توجد معايير تقييم مسجلة لهذا الامتحان
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </>
              ) : examPoints.length === 0 ? (
                <div className="text-center p-8 border rounded-lg bg-[#f8fffd] border-[#e9f7f5]">
                  <p className="text-gray-500">لا توجد نتائج لهذا الامتحان حتى الآن</p>
                </div>
              ) : (
                <div className="text-center p-8 border rounded-lg bg-[#f8fffd] border-[#e9f7f5]">
                  <p className="text-gray-500 mb-4">لا توجد نتائج تطابق معايير التصفية</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSelectedClassId('all');
                      setSelectedStatus('all');
                      setSearchQuery('');
                    }}
                  >
                    إعادة ضبط التصفية
                  </Button>
                </div>
              )
            ) : (
              <div className="text-center p-8 border rounded-lg bg-[#f8fffd] border-[#e9f7f5]">
                <p className="text-gray-500">يرجى اختيار امتحان لعرض الإحصائيات</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}

      {/* Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={(open) => !open && setIsDetailsDialogOpen(false)}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)] w-[95vw] mx-auto">
          <DialogHeader>
            <DialogTitle>تفاصيل نتيجة الامتحان</DialogTitle>
          </DialogHeader>

          {selectedExamPoint && (
            <div className="mt-2 px-4">
              <div className="text-right mb-1">
                <span className="font-bold">الطالب:</span> {selectedExamPoint.student.name}
              </div>
              <div className="text-right mb-1">
                <span className="font-bold">القسم:</span> {selectedExamPoint.student?.classe?.name || selectedExamPoint.classSubject?.classe?.name || '-'}
              </div>
              <div className="text-right mb-1">
                <span className="font-bold">الدرجة النهائية:</span>{' '}
                <span className={getGradeColor(Number(selectedExamPoint.grade))}>
                  {Number(selectedExamPoint.grade).toFixed(2)}
                </span>
              </div>
              <div className="text-right mb-4">
                <span className="font-bold">الحالة:</span>{' '}
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(selectedExamPoint.status)}`}>
                  {getStatusLabel(selectedExamPoint.status)}
                </span>
              </div>

              {/* عرض معلومات السورة والآيات إذا كانت موجودة */}
              {selectedExamPoint.surahId && (
                <div className="text-right mb-4 p-3 bg-[#f8fffd] border border-[#e0f2ef] rounded-md">
                  <div className="mb-1">
                    <span className="font-bold">السورة:</span>{' '}
                    {selectedExamPoint.surah?.name || `سورة رقم ${selectedExamPoint.surahId}`}
                  </div>
                  {selectedExamPoint.startVerse && selectedExamPoint.endVerse && (
                    <div>
                      <span className="font-bold">الآيات:</span>{' '}
                      من الآية {selectedExamPoint.startVerse} إلى الآية {selectedExamPoint.endVerse}
                    </div>
                  )}
                </div>
              )}

              {selectedExamPoint.criteriaScores && selectedExamPoint.criteriaScores.length > 0 ? (
                <div className="mt-4">
                  <h3 className="text-lg font-bold text-right mb-2">تفاصيل معايير التقييم</h3>
                  <div className="space-y-4">
                    {selectedExamPoint.criteriaScores.map((score) => (
                      <div key={score.id} className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-right">
                            {Number(score.score).toFixed(2)} / 10
                          </span>
                          <span className="text-sm font-medium text-right">
                            {score.criteria.name} ({(Number(score.criteria.weight) * 100).toFixed(0)}%)
                          </span>
                        </div>
                        <Progress value={Number(score.score) * 10} className="h-2" />
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 mt-4">لا توجد تفاصيل لمعايير التقييم</div>
              )}
            </div>
          )}

          <div className="flex justify-end mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDetailsDialogOpen(false)}
            >
              إغلاق
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Grade Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)] w-[95vw] mx-auto">
          <DialogHeader>
            <DialogTitle>تعديل نقاط الطالب</DialogTitle>
          </DialogHeader>

          {selectedExamPoint && (
            <div className="mt-2 px-4">
              <div className="text-right mb-1">
                <span className="font-bold">الطالب:</span> {selectedExamPoint.student.name}
              </div>
              <div className="text-right mb-3">
                <span className="font-bold">القسم:</span> {selectedExamPoint.student?.classe?.name || selectedExamPoint.classSubject?.classe?.name || '-'}
              </div>
            </div>
          )}

          <Tabs defaultValue="general" className="mt-4">
            <TabsList className="mb-4 bg-[#e9f7f5] p-1 rounded-xl">
              <TabsTrigger value="general" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">البيانات العامة</TabsTrigger>
              {selectedExamPoint?.criteriaScores && selectedExamPoint.criteriaScores.length > 0 && (
                <TabsTrigger value="criteria" className="data-[state=active]:bg-[var(--primary-color)] data-[state=active]:text-white">معايير التقييم</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="general">
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <label className="text-right block font-medium">الدرجة النهائية (من 10)</label>
                  <Input
                    type="number"
                    min="0"
                    max="10"
                    step="0.5"
                    value={editGrade}
                    onChange={(e) => setEditGrade(e.target.value)}
                    className="text-center font-bold text-lg"
                    dir="ltr"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-right block font-medium">الملاحظات (اختياري)</label>
                  <Textarea
                    value={editNote}
                    onChange={(e) => setEditNote(e.target.value)}
                    className="text-right"
                    placeholder="أدخل ملاحظات حول أداء الطالب"
                    rows={3}
                  />
                </div>
              </div>
            </TabsContent>

            {selectedExamPoint?.criteriaScores && selectedExamPoint.criteriaScores.length > 0 && (
              <TabsContent value="criteria">
                <div className="space-y-4 py-2">
                  <p className="text-sm text-gray-500 mb-4">تعديل درجات معايير التقييم سيؤدي إلى إعادة حساب الدرجة النهائية تلقائياً</p>

                  {editCriteriaScores.map((criteriaScore) => {
                    const criteria = selectedExamPoint.criteriaScores.find(cs => cs.criteriaId === criteriaScore.criteriaId)?.criteria;
                    if (!criteria) return null;

                    return (
                      <div key={criteriaScore.criteriaId} className="space-y-2 border-b pb-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">({(Number(criteria.weight) * 100).toFixed(0)}%)</span>
                          <label className="text-right block font-medium">{criteria.name}</label>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-500">0</span>
                          <Input
                            type="range"
                            min="0"
                            max="10"
                            step="0.5"
                            value={criteriaScore.score}
                            onChange={(e) => handleUpdateCriteriaScore(criteriaScore.criteriaId, Number(e.target.value))}
                            className="flex-1"
                          />
                          <span className="text-sm text-gray-500">10</span>
                          <span className="w-12 text-center font-bold">{criteriaScore.score.toFixed(1)}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </TabsContent>
            )}
          </Tabs>

          <DialogFooter className="flex justify-between mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              إلغاء
            </Button>
            <Button
              type="button"
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              onClick={handleUpdateGrade}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري الحفظ...
                </>
              ) : (
                'حفظ التغييرات'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, UserRole } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

// POST: معاينة المستلمين قبل الإرسال
export async function POST(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const body = await request.json();

        // التحقق من البيانات المطلوبة
        if (!body.groupType) {
            return NextResponse.json(
                { message: "يجب تحديد نوع المجموعة" },
                { status: 400 }
            );
        }

        const { groupType, targetRole, targetUserIds } = body;
        let recipients: Array<{id: number, username: string, profile?: {name: string} | null}> = [];
        let totalCount = 0;

        switch (groupType) {
            case 'ALL_USERS':
                // جلب جميع المستخدمين النشطين (باستثناء المعلقين)
                totalCount = await prisma.user.count({
                    where: {
                        isActive: true,
                        role: { not: 'PENDING' }
                    }
                });

                recipients = await prisma.user.findMany({
                    where: {
                        isActive: true,
                        role: { not: 'PENDING' }
                    },
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: { name: true }
                        }
                    },
                    take: 10, // عرض أول 10 مستخدمين كعينة
                    orderBy: { createdAt: 'desc' }
                });
                break;

            case 'BY_ROLE':
                if (!targetRole) {
                    return NextResponse.json(
                        { message: "يجب تحديد الدور المستهدف" },
                        { status: 400 }
                    );
                }

                // عد المستخدمين حسب الدور
                totalCount = await prisma.user.count({
                    where: {
                        role: targetRole as UserRole,
                        isActive: true
                    }
                });

                // جلب عينة من المستخدمين
                recipients = await prisma.user.findMany({
                    where: {
                        role: targetRole as UserRole,
                        isActive: true
                    },
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: { name: true }
                        }
                    },
                    take: 10,
                    orderBy: { createdAt: 'desc' }
                });
                break;

            case 'CUSTOM_SELECTION':
                if (!targetUserIds || !Array.isArray(targetUserIds)) {
                    return NextResponse.json(
                        { message: "يجب تحديد قائمة المستخدمين" },
                        { status: 400 }
                    );
                }

                // جلب المستخدمين المحددين
                recipients = await prisma.user.findMany({
                    where: {
                        id: { in: targetUserIds },
                        isActive: true,
                        role: { not: 'PENDING' }
                    },
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: { name: true }
                        }
                    }
                });

                totalCount = recipients.length;
                break;

            default:
                return NextResponse.json(
                    { message: "نوع المجموعة غير صحيح" },
                    { status: 400 }
                );
        }

        // تنسيق البيانات للعرض
        const preview = {
            totalCount,
            sampleCount: recipients.length,
            recipients: recipients.map(user => ({
                id: user.id,
                name: user.profile?.name || user.username,
                username: user.username
            })),
            hasMore: totalCount > recipients.length
        };

        return NextResponse.json(preview);

    } catch (error) {
        console.error('Error previewing recipients:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء معاينة المستلمين" },
            { status: 500 }
        );
    }
}

// GET: البحث في المستخدمين للاختيار المخصص
export async function GET(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const { searchParams } = new URL(request.url);
        const search = searchParams.get('search') || '';
        const role = searchParams.get('role');
        const limit = parseInt(searchParams.get('limit') || '20');

        // بناء شروط البحث
        const where: any = {
            isActive: true,
            role: { not: 'PENDING' }, // استبعاد المستخدمين المعلقين
            OR: search ? [
                { username: { contains: search, mode: 'insensitive' } },
                {
                    profile: {
                        name: { contains: search, mode: 'insensitive' }
                    }
                }
            ] : undefined
        };

        if (role && role !== 'ALL') {
            where.role = role as UserRole;
        }

        // جلب المستخدمين
        const users = await prisma.user.findMany({
            where,
            select: {
                id: true,
                username: true,
                role: true,
                profile: {
                    select: { name: true }
                }
            },
            take: limit,
            orderBy: [
                { profile: { name: 'asc' } },
                { username: 'asc' }
            ]
        });

        // تنسيق البيانات
        const formattedUsers = users.map(user => ({
            id: user.id,
            name: user.profile?.name || user.username,
            username: user.username,
            role: user.role,
            displayText: `${user.profile?.name || user.username} (${user.username})`
        }));

        return NextResponse.json({
            users: formattedUsers,
            total: formattedUsers.length
        });

    } catch (error) {
        console.error('Error searching users:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء البحث في المستخدمين" },
            { status: 500 }
        );
    }
}

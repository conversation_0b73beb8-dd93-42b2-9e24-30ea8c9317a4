import * as XLSX from 'xlsx';
import { Budget, BudgetItem } from '@/types/budget';
import { exportToPdf } from '@/utils/export-utils';
import { getGradeLevelByScore, calculatePercentage, getPassStatus } from '@/lib/grading-system';

// أنواع البيانات للتقارير
type ExamReport = {
  id: number;
  evaluationType: string;
  month: string;
  description: string;
  maxPoints: number;
  passingPoints: number;
  subject: string;
  examType: string;
  statistics: {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
    highestGrade: number;
    lowestGrade: number;
    passRate: number;
  };
  studentsByClass: Record<string, any[]>;
  createdAt: string;
};

type StudentReportData = {
  student: {
    id: number;
    name: string;
    username: string;
    age: number;
    phone?: string;
    classe: {
      name: string;
    };
    guardian: {
      name: string;
      phone?: string;
    };
  };
  allResults: Array<{
    examId: number;
    examDescription: string;
    evaluationType: string;
    month: string;
    grade: number;
    maxPoints: number;
    percentage: number;
    status: string;
  }>;
  typeStatistics: Array<{
    type: string;
    totalExams: number;
    averageGrade: number;
  }>;
  statistics: {
    totalExams: number;
    passedExams: number;
    passRate: number;
    averageGrade: number;
    highestGrade: number;
    lowestGrade: number;
  };
  reportGeneratedAt: string;
};

// تصدير الميزانية إلى ملف Excel
export const exportBudgetToExcel = (budget: Budget) => {
  // إنشاء ورقة عمل للمعلومات الأساسية
  const infoWorksheet = XLSX.utils.aoa_to_sheet([
    ['معلومات الميزانية'],
    ['الاسم', budget.name],
    ['الوصف', budget.description || ''],
    ['تاريخ البداية', formatDate(budget.startDate)],
    ['تاريخ النهاية', formatDate(budget.endDate)],
    ['الحالة', getBudgetStatusText(budget.status)],
    ['المبلغ الإجمالي', budget.totalAmount],
    ['المصروفات الفعلية', budget.totalActualAmount],
    ['المتبقي', budget.totalRemainingAmount],
    ['نسبة الاستخدام', `${Math.round((budget.totalActualAmount / budget.totalAmount) * 100)}%`],
  ]);

  // إنشاء ورقة عمل لبنود الميزانية
  const itemsData = budget.items.map((item: BudgetItem) => [
    item.category.name,
    item.amount,
    item.actualAmount,
    item.remainingAmount,
    `${Math.round((item.actualAmount / item.amount) * 100)}%`,
    item.notes || ''
  ]);

  const itemsWorksheet = XLSX.utils.aoa_to_sheet([
    ['الفئة', 'المبلغ المخصص', 'المصروفات الفعلية', 'المتبقي', 'نسبة الاستخدام', 'ملاحظات'],
    ...itemsData
  ]);

  // إنشاء مصنف وإضافة أوراق العمل
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, infoWorksheet, 'معلومات الميزانية');
  XLSX.utils.book_append_sheet(workbook, itemsWorksheet, 'بنود الميزانية');

  // تصدير المصنف
  XLSX.writeFile(workbook, `ميزانية_${budget.name}_${new Date().toISOString().split('T')[0]}.xlsx`);
};

// تصدير الميزانية إلى ملف PDF
export const exportBudgetToPDF = (budget: Budget) => {
  // إعداد بيانات الميزانية
  const infoData = [
    ['الاسم', budget.name],
    ['الوصف', budget.description || '-'],
    ['تاريخ البداية', formatDate(budget.startDate)],
    ['تاريخ النهاية', formatDate(budget.endDate)],
    ['الحالة', getBudgetStatusText(budget.status)],
    ['المبلغ الإجمالي', formatAmount(budget.totalAmount)],
    ['المصروفات الفعلية', formatAmount(budget.totalActualAmount)],
    ['المتبقي', formatAmount(budget.totalRemainingAmount)],
    ['نسبة الاستخدام', `${Math.round((budget.totalActualAmount / budget.totalAmount) * 100)}%`]
  ];

  // إعداد بيانات بنود الميزانية
  const itemsData = budget.items.map((item: BudgetItem) => [
    item.category.name,
    formatAmount(item.amount),
    formatAmount(item.actualAmount),
    formatAmount(item.remainingAmount),
    `${Math.round((item.actualAmount / item.amount) * 100)}%`,
    item.notes || '-'
  ]);

  // استخدام دالة exportToPdf من utils/export-utils.ts
  exportToPdf({
    title: `ميزانية: ${budget.name}`,
    fileName: `ميزانية_${budget.name}_${new Date().toISOString().split('T')[0]}.pdf`,
    tables: [
      {
        title: 'معلومات الميزانية',
        headers: ['البيان', 'القيمة'],
        data: infoData
      },
      {
        title: 'بنود الميزانية',
        headers: ['الفئة', 'المبلغ المخصص', 'المصروفات الفعلية', 'المتبقي', 'نسبة الاستخدام', 'ملاحظات'],
        data: itemsData,
        headStyles: {
          fillColor: [22, 155, 136],
          textColor: [255, 255, 255]
        }
      }
    ]
  });
};

// دوال مساعدة
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  } catch {
    return dateString;
  }
};

const formatAmount = (amount: number) => {
  return amount.toLocaleString('fr-FR') + ' د.ج';
};

const getBudgetStatusText = (status: string) => {
  switch (status) {
    case 'DRAFT': return 'مسودة';
    case 'ACTIVE': return 'نشطة';
    case 'COMPLETED': return 'مكتملة';
    case 'ARCHIVED': return 'مؤرشفة';
    default: return status;
  }
};

// تصدير تقرير الامتحانات إلى Excel
export const exportExamReportToExcel = (
  examReports: ExamReport[],
  summary: any,
  filters: any = {}
) => {
  const workbook = XLSX.utils.book_new();

  // ورقة الملخص العام
  const summaryData = [
    ['📊 ملخص تقرير الامتحانات'],
    [''],
    ['إجمالي الامتحانات', summary?.totalExams || 0],
    ['إجمالي المشاركات', summary?.totalStudentEntries || 0],
    ['الطلاب الناجحون', summary?.totalPassedEntries || 0],
    ['معدل النجاح العام', `${summary?.overallPassRate || 0}%`],
    ['المتوسط العام', summary?.overallAverage || 0],
    ['تاريخ التقرير', formatDate(summary?.reportGeneratedAt || new Date().toISOString())],
    [''],
    ['🔍 معايير التصفية المطبقة:'],
    ['الشهر', filters.month || 'جميع الأشهر'],
    ['نوع التقييم', getEvaluationTypeLabel(filters.evaluationType) || 'جميع الأنواع'],
    ['الفصل', filters.classeId || 'جميع الفصول'],
    ['المادة', filters.subjectId || 'جميع المواد']
  ];

  const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summaryWorksheet, 'الملخص العام');

  // ورقة إحصائيات الامتحانات
  const examStatsData = [
    ['رقم الامتحان', 'نوع التقييم', 'الشهر', 'الوصف', 'النقاط الكاملة', 'نقاط النجاح', 'إجمالي الطلاب', 'الناجحون', 'الراسبون', 'الممتازون', 'معدل النجاح %', 'المتوسط', 'أعلى درجة', 'أقل درجة']
  ];

  examReports.forEach((exam, index) => {
    examStatsData.push([
      (index + 1).toString(),
      getEvaluationTypeLabel(exam.evaluationType),
      exam.month,
      exam.description,
      exam.maxPoints.toString(),
      exam.passingPoints.toString(),
      exam.statistics.totalStudents.toString(),
      exam.statistics.passedStudents.toString(),
      exam.statistics.failedStudents.toString(),
      exam.statistics.excellentStudents.toString(),
      exam.statistics.passRate.toString(),
      exam.statistics.averageGrade.toString(),
      exam.statistics.highestGrade.toString(),
      exam.statistics.lowestGrade.toString()
    ]);
  });

  const examStatsWorksheet = XLSX.utils.aoa_to_sheet(examStatsData);
  XLSX.utils.book_append_sheet(workbook, examStatsWorksheet, 'إحصائيات الامتحانات');

  // ورقة تفاصيل الطلاب لكل امتحان
  examReports.forEach((exam, examIndex) => {
    const examStudentsData = [
      [`📋 امتحان: ${getEvaluationTypeLabel(exam.evaluationType)} - ${exam.month}`],
      [`📝 الوصف: ${exam.description}`],
      [`📊 النقاط: ${exam.maxPoints} (النجاح: ${exam.passingPoints})`],
      [''],
      ['الفصل', 'الترتيب', 'اسم الطالب', 'الدرجة', 'النسبة المئوية', 'الحالة', 'التقدير']
    ];

    Object.entries(exam.studentsByClass).forEach(([className, students]) => {
      // إضافة عنوان الفصل
      examStudentsData.push([`🏫 فصل ${className}`, '', '', '', '', '', '']);

      // إضافة بيانات الطلاب
      students.forEach((student, index) => {
        examStudentsData.push([
          className,
          index + 1,
          student.name,
          `${student.grade}/${exam.maxPoints}`,
          `${Math.round((student.grade / exam.maxPoints) * 100)}%`,
          student.status,
          student.performance
        ]);
      });

      // إضافة سطر فارغ بين الفصول
      examStudentsData.push(['', '', '', '', '', '', '']);
    });

    const examStudentsWorksheet = XLSX.utils.aoa_to_sheet(examStudentsData);
    const sheetName = `امتحان ${examIndex + 1} - ${exam.month}`.substring(0, 31); // حد أقصى 31 حرف لاسم الورقة
    XLSX.utils.book_append_sheet(workbook, examStudentsWorksheet, sheetName);
  });

  // تصدير الملف
  const fileName = `تقرير_الامتحانات_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

// تصدير كشف درجات الطالب إلى Excel
export const exportStudentReportToExcel = (reportData: any) => {
  const workbook = XLSX.utils.book_new();

  // ورقة معلومات الطالب
  const studentInfoData = [
    ['📋 كشف درجات الطالب'],
    [''],
    ['👤 معلومات الطالب:'],
    ['الاسم', reportData.student.name || ''],
    ['اسم المستخدم', reportData.student.username || ''],
    ['العمر', `${reportData.student.age || 0} سنة`],
    ['الهاتف', reportData.student.phone || ''],
    ['الفصل', reportData.student.classe?.name || ''],
    [''],
    ['👨‍👩‍👧‍👦 معلومات ولي الأمر:'],
    ['اسم ولي الأمر', reportData.student.guardian?.name || ''],
    ['هاتف ولي الأمر', reportData.student.guardian?.phone || ''],
    [''],
    ['📊 الإحصائيات العامة:'],
    ['إجمالي الامتحانات', (reportData.statistics.totalExams || 0).toString()],
    ['الامتحانات الناجحة', (reportData.statistics.passedExams || 0).toString()],
    ['معدل النجاح', `${reportData.statistics.passRate || 0}%`],
    ['المعدل العام', (reportData.statistics.averageGrade || 0).toString()],
    ['أعلى درجة', (reportData.statistics.highestGrade || 0).toString()],
    ['أقل درجة', (reportData.statistics.lowestGrade || 0).toString()],
    ['تاريخ التقرير', formatDate(reportData.reportGeneratedAt)]
  ];

  const studentInfoWorksheet = XLSX.utils.aoa_to_sheet(studentInfoData);
  XLSX.utils.book_append_sheet(workbook, studentInfoWorksheet, 'معلومات الطالب');

  // ورقة تفاصيل النتائج
  const resultsData = [
    ['المادة/نوع التقييم', 'الشهر', 'الدرجة', 'النقاط الكاملة', 'النسبة المئوية', 'الحالة', 'التقدير']
  ];

  reportData.allResults.forEach((result: any) => {
    const grade = result.grade || 0;
    const maxPoints = result.maxPoints || 20;
    const percentage = calculatePercentage(grade, maxPoints);
    const status = getPassStatus(grade, maxPoints);

    resultsData.push([
      getEvaluationTypeLabel(result.evaluationType),
      result.month || '',
      grade.toString(),
      maxPoints.toString(),
      `${percentage}%`,
      status === 'FAILED' ? 'راسب' : 'نجح',
      getGradeLabel(grade, maxPoints)
    ]);
  });

  const resultsWorksheet = XLSX.utils.aoa_to_sheet(resultsData);
  XLSX.utils.book_append_sheet(workbook, resultsWorksheet, 'تفاصيل النتائج');

  // ورقة ملخص المعدلات حسب المواد
  const typeStatsData = [
    ['المادة/نوع التقييم', 'عدد الامتحانات', 'المعدل', 'التقدير']
  ];

  reportData.typeStatistics.forEach((typeStat: any) => {
    const averageGrade = typeStat.averageGrade || 0;
    typeStatsData.push([
      getEvaluationTypeLabel(typeStat.type),
      (typeStat.totalExams || 0).toString(),
      averageGrade.toString(),
      getGradeLabel(averageGrade, 20) // افتراض أن المعدل من 20
    ]);
  });

  const typeStatsWorksheet = XLSX.utils.aoa_to_sheet(typeStatsData);
  XLSX.utils.book_append_sheet(workbook, typeStatsWorksheet, 'ملخص المعدلات');

  // تصدير الملف
  const fileName = `كشف_درجات_${reportData.student.name}_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

// دالة للحصول على تسمية نوع التقييم
const getEvaluationTypeLabel = (type: string) => {
  const evaluationTypes: Record<string, string> = {
    'QURAN_MEMORIZATION': 'حفظ القرآن الكريم',
    'QURAN_RECITATION': 'تلاوة وتجويد',
    'ISLAMIC_STUDIES': 'الدراسات الإسلامية',
    'ARABIC_LANGUAGE': 'اللغة العربية',
    'GENERAL_KNOWLEDGE': 'المعرفة العامة',
    'BEHAVIOR_EVALUATION': 'تقييم السلوك',
    'ATTENDANCE_EVALUATION': 'تقييم الحضور',
    'PARTICIPATION_EVALUATION': 'تقييم المشاركة'
  };
  return evaluationTypes[type] || type;
};

// دالة للحصول على تسمية التقدير باستخدام النظام الموحد
const getGradeLabel = (grade: number, maxPoints: number = 20) => {
  const gradeLevel = getGradeLevelByScore(grade, maxPoints);
  return gradeLevel.nameAr;
};

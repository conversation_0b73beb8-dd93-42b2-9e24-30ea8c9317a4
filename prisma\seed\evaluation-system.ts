import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedEvaluationSystem() {
  console.log('Seeding evaluation system data...');

  // إنشاء معايير التقييم
  const criteria = [
    {
      name: 'جودة الحفظ',
      weight: 0.4,
      description: 'مدى إتقان الطالب للآيات المحفوظة وخلوها من الأخطاء'
    },
    {
      name: 'صحة التجويد',
      weight: 0.3,
      description: 'مدى التزام الطالب بأحكام التجويد أثناء القراءة'
    },
    {
      name: 'مخارج الحروف',
      weight: 0.2,
      description: 'مدى صحة نطق الحروف من مخارجها الصحيحة'
    },
    {
      name: 'سرعة الاسترجاع',
      weight: 0.1,
      description: 'سرعة استرجاع الطالب للآيات المحفوظة'
    }
  ];

  for (const criterion of criteria) {
    // التحقق من وجود المعيار بناءً على الاسم
    const existingCriterion = await prisma.evaluationCriteria.findFirst({
      where: { name: criterion.name }
    });

    if (existingCriterion) {
      // تحديث المعيار الموجود
      await prisma.evaluationCriteria.update({
        where: { id: existingCriterion.id },
        data: criterion
      });
    } else {
      // إنشاء معيار جديد
      await prisma.evaluationCriteria.create({
        data: criterion
      });
    }
  }

  console.log(`Created ${criteria.length} evaluation criteria`);

  // إنشاء أنواع الامتحانات
  const examTypes = [
    {
      name: 'امتحان شفهي يومي',
      description: 'امتحان شفهي يومي للحفظ والمراجعة',
      evaluationType: 'ORAL'
    },
    {
      name: 'امتحان شفهي أسبوعي',
      description: 'امتحان شفهي أسبوعي للحفظ والمراجعة',
      evaluationType: 'ORAL'
    },
    {
      name: 'امتحان شفهي شهري',
      description: 'امتحان شفهي شهري للأجزاء المحفوظة',
      evaluationType: 'ORAL'
    },
    {
      name: 'امتحان تحريري شهري',
      description: 'امتحان تحريري شهري في أحكام التجويد',
      evaluationType: 'WRITTEN'
    },
    {
      name: 'امتحان عملي',
      description: 'امتحان عملي في تطبيق أحكام التجويد',
      evaluationType: 'PRACTICAL'
    },
    {
      name: 'مسابقة تلاوة',
      description: 'مسابقة في التلاوة والترتيل',
      evaluationType: 'PRACTICAL'
    }
  ];

  for (const examType of examTypes) {
    // التحقق من وجود نوع الامتحان بناءً على الاسم
    const existingExamType = await prisma.examType.findFirst({
      where: { name: examType.name }
    });

    if (existingExamType) {
      // تحديث نوع الامتحان الموجود
      await prisma.examType.update({
        where: { id: existingExamType.id },
        data: examType
      });
    } else {
      // إنشاء نوع امتحان جديد
      await prisma.examType.create({
        data: examType
      });
    }
  }

  console.log(`Created ${examTypes.length} exam types`);

  // إنشاء المكافآت
  const rewards = [
    {
      name: 'شهادة تقدير',
      description: 'شهادة تقدير للطلاب المتميزين',
      requiredPoints: 100,
      type: 'CERTIFICATE'
    },
    {
      name: 'وسام التميز',
      description: 'وسام للطلاب المتفوقين في الحفظ',
      requiredPoints: 200,
      type: 'BADGE'
    },
    {
      name: 'جائزة الحافظ الصغير',
      description: 'جائزة للطلاب الذين أتموا حفظ جزء عم',
      requiredPoints: 300,
      type: 'PRIZE'
    },
    {
      name: 'جائزة الحافظ المتميز',
      description: 'جائزة للطلاب الذين أتموا حفظ خمسة أجزاء',
      requiredPoints: 500,
      type: 'PRIZE'
    },
    {
      name: 'جائزة الحافظ المتقن',
      description: 'جائزة للطلاب الذين أتموا حفظ عشرة أجزاء',
      requiredPoints: 1000,
      type: 'PRIZE'
    }
  ];

  for (const reward of rewards) {
    // التحقق من وجود المكافأة بناءً على الاسم
    const existingReward = await prisma.reward.findFirst({
      where: { name: reward.name }
    });

    if (existingReward) {
      // تحديث المكافأة الموجودة
      await prisma.reward.update({
        where: { id: existingReward.id },
        data: reward
      });
    } else {
      // إنشاء مكافأة جديدة
      await prisma.reward.create({
        data: reward
      });
    }
  }

  console.log(`Created ${rewards.length} rewards`);

  console.log('Evaluation system data seeded successfully!');
}

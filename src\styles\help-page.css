/* تحسينات خاصة بصفحة المساعدة */

/* خلفية متدرجة للصفحة */
.help-page-background {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 25%, #f0f9ff 50%, #f8fffd 75%, #ffffff 100%);
  min-height: 100vh;
}

/* تحسين الشريط الجانبي */
.help-sidebar {
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-left: 1px solid #e1e8ed;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.05);
}

.help-sidebar-header {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 50%, #7dd3fc 100%);
  color: #0c4a6e;
  padding: 1.5rem;
  border-radius: 0;
  border-bottom: 3px solid var(--primary-color);
}

.help-sidebar-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0.25rem 0.75rem;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.help-sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.help-sidebar-item:not(.active):hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
  color: #0369a1 !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateX(-2px);
}

.help-sidebar-item.active::before {
  opacity: 1;
}

.help-sidebar-item.active {
  color: #1e40af !important;
  font-weight: 600;
}

.help-sidebar-item:not(.active):hover .help-sidebar-title {
  color: #1e40af !important; /* أزرق داكن للعنوان */
}

.help-sidebar-item:not(.active):hover .help-sidebar-description {
  color: #3b82f6 !important; /* أزرق متوسط للوصف */
}

.help-sidebar-item:not(.active):hover .help-sidebar-icon {
  color: #1e40af !important; /* أزرق داكن للأيقونة */
}

.help-sidebar-item.active {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.help-sidebar-item.active:hover {
  background-color: var(--secondary-color) !important;
}

/* تحسين بطاقات المحتوى */
.help-content-card {
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%) !important;
  border: 1px solid #e1e8ed !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08) !important;
  border-radius: 16px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.help-content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.help-content-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12) !important;
}

.help-content-card .card-title {
  color: var(--primary-color) !important;
}

.help-content-card .card-description {
  color: #6b7280 !important;
}

.help-content-card .card-content {
  color: #374151 !important;
}

/* إصلاح ألوان النصوص في المحتوى */
.help-content-text {
  color: #374151 !important;
}

.help-content-text h1,
.help-content-text h2,
.help-content-text h3,
.help-content-text h4,
.help-content-text h5,
.help-content-text h6 {
  color: #111827 !important;
}

.help-content-text p {
  color: #4b5563 !important;
}

.help-content-text ul,
.help-content-text ol {
  color: #4b5563 !important;
}

.help-content-text li {
  color: #4b5563 !important;
}

.help-content-text strong {
  color: #111827 !important;
  font-weight: 600;
}

/* تحسين صناديق الخطوات */
.help-steps-box {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
  border: 2px solid #3b82f6 !important;
  border-radius: 12px !important;
  color: #1e40af !important;
  position: relative;
  overflow: hidden;
}

.help-steps-box::before {
  content: '📋';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  opacity: 0.3;
}

.help-steps-box h4 {
  color: #1e3a8a !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.help-steps-box h4::before {
  content: '✅';
  font-size: 1.2rem;
}

.help-steps-box ol,
.help-steps-box li {
  color: #1e40af !important;
}

/* تحسين صناديق النصائح */
.help-tips-box {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%) !important;
  border: 2px solid #10b981 !important;
  border-radius: 12px !important;
  color: #065f46 !important;
  position: relative;
  overflow: hidden;
}

.help-tips-box::before {
  content: '💡';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  opacity: 0.3;
}

.help-tips-box h4 {
  color: #064e3b !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.help-tips-box h4::before {
  content: '🎯';
  font-size: 1.2rem;
}

.help-tips-box ul,
.help-tips-box li {
  color: #065f46 !important;
}

/* تحسين صناديق الفيديو */
.help-video-box {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  border: 2px solid #64748b !important;
  border-radius: 12px !important;
  position: relative;
  overflow: hidden;
}

.help-video-box::before {
  content: '🎥';
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  opacity: 0.3;
}

.help-video-box h4 {
  color: #374151 !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.help-video-box h4::before {
  content: '▶️';
  font-size: 1.2rem;
}

/* تحسين الهيدر الرئيسي */
.help-main-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e1e8ed;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.help-main-title {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.help-main-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  right: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 2px;
}

/* تحسين شريط البحث */
.help-search-container {
  position: relative;
}

.help-search-input {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
  border: 2px solid #e1e8ed !important;
  border-radius: 12px !important;
  color: #374151 !important;
  padding: 0.75rem 1rem 0.75rem 3rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.help-search-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(-1px) !important;
}

.help-search-input::placeholder {
  color: #9ca3af !important;
}

.help-search-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.help-search-container:focus-within .help-search-icon {
  color: var(--secondary-color);
  transform: translateY(-50%) scale(1.1);
}

/* تحسين هيدر الفئة */
.help-category-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border-bottom: 1px solid #e1e8ed !important;
  position: relative;
}

.help-category-header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.help-category-title {
  color: #111827 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.help-category-description {
  color: #6b7280 !important;
}

/* تحسين الأزرار */
.help-button-primary {
  background: linear-gradient(135deg, var(--primary-color), #60a5fa) !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25) !important;
  position: relative;
  overflow: hidden;
}

.help-button-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.help-button-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4) !important;
}

.help-button-primary:hover::before {
  left: 100%;
}

.help-button-outline {
  background: linear-gradient(145deg, #ffffff, #f8fafc) !important;
  color: var(--primary-color) !important;
  border: 2px solid var(--primary-color) !important;
  border-radius: 12px !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.help-button-outline:hover {
  background: linear-gradient(135deg, var(--primary-color), #60a5fa) !important;
  color: white !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25) !important;
}

/* تحسينات إضافية للتباين */
.help-text-contrast {
  color: #111827 !important;
}

.help-text-muted {
  color: #6b7280 !important;
}

.help-text-light {
  color: #9ca3af !important;
}

/* إصلاح مشاكل الألوان المتضاربة */
.help-override-colors * {
  color: inherit !important;
}

.help-override-colors h1,
.help-override-colors h2,
.help-override-colors h3,
.help-override-colors h4,
.help-override-colors h5,
.help-override-colors h6 {
  color: #111827 !important;
}

.help-override-colors p,
.help-override-colors li,
.help-override-colors span {
  color: #4b5563 !important;
}

/* تأثيرات حديثة وتحسينات إضافية */
.help-floating-elements {
  position: relative;
}

.help-floating-elements::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.help-floating-elements::after {
  content: '';
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.05) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 4s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* تحسين التمرير */
.help-content-area {
  scroll-behavior: smooth;
}

.help-content-area::-webkit-scrollbar {
  width: 8px;
}

.help-content-area::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.help-content-area::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 4px;
}

.help-content-area::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
}

/* تأثير الظهور التدريجي */
.help-fade-in {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين الأيقونات */
.help-icon-bounce {
  animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* تحسين الهوفر للبطاقات */
.help-card-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.help-card-hover-effect:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* تحسينات للوضع المظلم - معطل لتجنب الخلفيات السوداء */
/*
@media (prefers-color-scheme: dark) {
  // تم تعطيل الوضع المظلم لتجنب الخلفيات السوداء
}
*/

/* تحسينات الاستجابة للهواتف */
@media (max-width: 768px) {
  .help-sidebar {
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
  }

  .help-sidebar.open {
    transform: translateX(0);
  }

  .help-content-card {
    margin: 0.5rem;
    border-radius: 12px;
  }

  .help-search-input {
    font-size: 16px; /* منع التكبير في iOS */
  }
}

import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// تعريف واجهة السورة
interface SurahData {
  id: number;
  name: string;
  number: number;
  totalAyahs: number;
}

export async function GET() {
  try {
    // أولاً نحاول استخدام جدول Surah إذا كان موجوداً
    try {
      const surahs = await prisma.surah.findMany({
        select: {
          id: true,
          name: true,
          number: true,
          totalAyahs: true
        },
        orderBy: { number: 'asc' }
      });

      if (surahs && surahs.length > 0) {
        return NextResponse.json(surahs);
      }
    } catch (error) {
      console.log('Surah table not found or empty, using default surahs list:', error);
    }

    // إذا لم يكن جدول Surah موجوداً أو كان فارغاً، نقوم بإنشاء قائمة افتراضية للسور
    // قائمة افتراضية ببعض السور الأساسية
    const defaultSurahs: SurahData[] = [
      { id: 1, name: 'الفاتحة', number: 1, totalAyahs: 7 },
      { id: 2, name: 'البقرة', number: 2, totalAyahs: 286 },
      { id: 3, name: 'آل عمران', number: 3, totalAyahs: 200 },
      { id: 4, name: 'النساء', number: 4, totalAyahs: 176 },
      { id: 5, name: 'المائدة', number: 5, totalAyahs: 120 },
      { id: 6, name: 'الأنعام', number: 6, totalAyahs: 165 },
      { id: 7, name: 'الأعراف', number: 7, totalAyahs: 206 },
      { id: 8, name: 'الأنفال', number: 8, totalAyahs: 75 },
      { id: 9, name: 'التوبة', number: 9, totalAyahs: 129 },
      { id: 10, name: 'يونس', number: 10, totalAyahs: 109 }
    ];

    // استخدام القائمة الافتراضية
    const formattedSurahs = defaultSurahs;

    return NextResponse.json(formattedSurahs);
  } catch (error: unknown) {
    console.error('Error fetching surahs:', error);
    return NextResponse.json(
      { error: 'فشل في جلب بيانات السور' },
      { status: 500 }
    );
  }
}
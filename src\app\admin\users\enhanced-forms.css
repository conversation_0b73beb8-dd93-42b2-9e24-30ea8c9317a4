/* تحسينات النوافذ المنبثقة والنماذج */

/* تحسين مظهر الحقول */
.enhanced-input {
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
  background: white;
}

.enhanced-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.enhanced-input:hover {
  border-color: #d1d5db;
}

/* تحسين مظهر القوائم المنسدلة */
.enhanced-select {
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
  background: white;
  cursor: pointer;
}

.enhanced-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.enhanced-select:hover {
  border-color: #d1d5db;
}

/* تحسين مظهر الأزرار */
.enhanced-button {
  transition: all 0.3s ease;
  transform: translateY(0);
}

.enhanced-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.enhanced-button:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* تحسين مظهر الأقسام */
.enhanced-section {
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.enhanced-section:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* تحسين مظهر رسائل الخطأ */
.error-message {
  animation: slideInError 0.3s ease-out;
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين مظهر رسائل المعلومات */
.info-message {
  animation: slideInInfo 0.3s ease-out;
}

@keyframes slideInInfo {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* تحسين مظهر المؤشرات */
.loading-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* تحسين مظهر العناوين */
.section-title {
  position: relative;
  padding-bottom: 8px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 1px;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
  .enhanced-input,
  .enhanced-select {
    font-size: 16px; /* منع التكبير التلقائي في iOS */
    min-height: 48px; /* الحد الأدنى لحجم اللمس */
  }
  
  .enhanced-button {
    min-height: 48px;
    font-size: 16px;
  }
  
  .enhanced-section {
    padding: 16px;
    margin-bottom: 16px;
  }
}

/* تحسين مظهر الشارات */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.inactive {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

/* تحسين مظهر التلميحات */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.tooltip::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #1f2937;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tooltip:hover::before,
.tooltip:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-5px);
}

/* تحسين مظهر الفواصل */
.divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
  margin: 24px 0;
}

/* تحسين مظهر البطاقات */
.enhanced-card {
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
}

.enhanced-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* تحسين مظهر الأيقونات */
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--primary-color);
  color: white;
  font-size: 14px;
}

/* تحسين الانتقالات */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

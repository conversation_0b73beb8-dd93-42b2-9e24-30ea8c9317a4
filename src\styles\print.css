/* أنماط خاصة بالطباعة */

@media print {
  /* إعدادات عامة للطباعة */
  body {
    font-family: 'Arial', '<PERSON><PERSON>a', sans-serif;
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
    background-color: #fff;
    margin: 0;
    padding: 0;
  }

  /* إخفاء العناصر غير المطلوبة عند الطباعة */
  header, footer, nav, .no-print, button, .actions, .export-buttons, .filters {
    display: none !important;
  }

  /* إظهار العناصر المخفية المخصصة للطباعة فقط */
  .print-only {
    display: block !important;
  }

  /* تنسيق الصفحة */
  @page {
    size: A4;
    margin: 2cm;
  }

  /* رأس الصفحة */
  .print-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 2cm;
    text-align: center;
    border-bottom: 1px solid #ddd;
    padding-bottom: 0.5cm;
  }

  /* تذييل الصفحة */
  .print-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2cm;
    text-align: center;
    border-top: 1px solid #ddd;
    padding-top: 0.5cm;
  }

  /* محتوى الصفحة */
  .print-content {
    margin-top: 2.5cm;
    margin-bottom: 2.5cm;
  }

  /* ترقيم الصفحات */
  .page-number:after {
    content: counter(page);
  }

  /* تنسيق العناوين */
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
    page-break-inside: avoid;
  }

  h1 {
    font-size: 18pt;
    margin-bottom: 1cm;
    text-align: center;
  }

  h2 {
    font-size: 16pt;
    margin-top: 1cm;
    margin-bottom: 0.5cm;
  }

  h3 {
    font-size: 14pt;
    margin-top: 0.8cm;
    margin-bottom: 0.4cm;
  }

  /* تنسيق الجداول */
  table {
    width: 100%;
    border-collapse: collapse;
    page-break-inside: auto;
    margin-bottom: 1cm;
  }

  tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }

  th {
    background-color: #f0f0f0 !important;
    color: #000 !important;
    font-weight: bold;
    text-align: right;
    padding: 0.3cm;
    border: 1px solid #ddd;
  }

  td {
    padding: 0.2cm;
    border: 1px solid #ddd;
    text-align: right;
  }

  /* تنسيق الرسوم البيانية */
  .chart-container {
    page-break-inside: avoid;
    margin-bottom: 1cm;
    text-align: center;
  }

  /* تنسيق البطاقات */
  .card {
    page-break-inside: avoid;
    border: 1px solid #ddd;
    padding: 0.5cm;
    margin-bottom: 1cm;
    background-color: #fff !important;
    box-shadow: none !important;
  }

  .card-header {
    border-bottom: 1px solid #ddd;
    padding-bottom: 0.3cm;
    margin-bottom: 0.5cm;
  }

  .card-title {
    font-size: 14pt;
    font-weight: bold;
  }

  /* تنسيق الأعمدة */
  .grid {
    display: block !important;
  }

  .grid > * {
    width: 100% !important;
    margin-bottom: 1cm;
  }

  /* تنسيق الأزرار */
  .print-button {
    display: none !important;
  }

  /* تنسيق الروابط */
  a {
    text-decoration: none;
    color: #000;
  }

  /* تنسيق الصور */
  img {
    max-width: 100%;
    page-break-inside: avoid;
  }

  /* تنسيق التقارير المالية */
  .financial-summary {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 1cm;
  }

  .financial-summary-item {
    width: 48%;
    padding: 0.3cm;
    margin-bottom: 0.5cm;
    border: 1px solid #ddd;
    page-break-inside: avoid;
  }

  .financial-summary-item-title {
    font-weight: bold;
    margin-bottom: 0.2cm;
  }

  .financial-summary-item-value {
    font-size: 14pt;
  }

  /* تنسيق شريط التقدم */
  .progress {
    height: 0.5cm;
    background-color: #f0f0f0;
    border-radius: 0.25cm;
    overflow: hidden;
    margin-bottom: 0.3cm;
  }

  .progress-bar {
    height: 100%;
    background-color: #169b88;
    text-align: center;
    color: white;
  }
}

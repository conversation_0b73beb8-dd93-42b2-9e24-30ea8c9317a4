import { NextRequest, NextResponse } from 'next/server';
import { getToken } from '@/lib/auth';
import prisma from '@/lib/prisma';

// GET: جلب صلاحيات المستخدم الحالي
export async function GET(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به: يرجى تسجيل الدخول" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData) {
      return NextResponse.json(
        { message: "غير مصرح به: توكن غير صالح" },
        { status: 401 }
      );
    }

    // إذا كان المستخدم مدير، إرجاع جميع الصلاحيات
    if (userData.role === 'ADMIN') {
      const allPermissions = await prisma.permission.findMany({
        where: { isActive: true },
        orderBy: [
          { category: 'asc' },
          { name: 'asc' }
        ]
      });

      return NextResponse.json({
        permissions: allPermissions,
        message: "تم جلب صلاحيات المدير بنجاح"
      });
    }

    // للموظفين، جلب الصلاحيات من خلال الدور
    const user = await prisma.user.findUnique({
      where: { id: userData.id },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: {
                  where: { isActive: true }
                }
              }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { message: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    // جلب الصلاحيات من النظام الجديد
    const permissions = user.userRole?.permissions.map(rp => rp.permission) || [];

    return NextResponse.json({
      permissions,
      role: user.role,
      roleId: user.roleId,
      roleName: user.userRole?.displayName,
      message: "تم جلب صلاحيات المستخدم بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching user permissions:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب صلاحيات المستخدم" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// GET: جلب صلاحيات المستخدم
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = parseInt(params.id);

    // التحقق من صحة معرف المستخدم
    if (isNaN(userId) || userId <= 0) {
      return NextResponse.json(
        { message: "معرف المستخدم غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.users.view');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    // التحقق من وجود المستخدم
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { message: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    // جلب الصلاحيات من النظام الجديد
    const permissions = user.userRole?.permissions.map(rp => rp.permission) || [];

    return NextResponse.json({
      permissions,
      message: "تم جلب صلاحيات المستخدم بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching user permissions:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب صلاحيات المستخدم" },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { ArrowLeft, Filter, Search } from 'lucide-react';
import ImageGallery from '@/components/student-images/ImageGallery';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface Student {
  id: number;
  name: string;
}

interface StudentAlbum {
  id: number;
  name: string;
}

interface StudentImage {
  id: number;
  imageUrl: string;
  description: string | null;
  isProfilePic: boolean;
  uploadDate: string;
  albumId: number | null;
  studentId: number;
}

export default function GalleryPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [students, setStudents] = useState<Student[]>([]);
  const [albums, setAlbums] = useState<StudentAlbum[]>([]);
  const [images, setImages] = useState<StudentImage[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<string>('all-students');
  const [selectedAlbum, setSelectedAlbum] = useState<string>('all-albums');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [loading, setLoading] = useState({
    students: true,
    albums: true,
    images: false,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Initialize from URL params
  useEffect(() => {
    if (searchParams) {
      const studentId = searchParams.get('studentId');
      const albumId = searchParams.get('albumId');

      if (studentId) {
        setSelectedStudent(studentId);
      } else {
        setSelectedStudent('all-students');
      }

      if (albumId) {
        setSelectedAlbum(albumId);
      } else {
        setSelectedAlbum('all-albums');
      }
    }
  }, [searchParams]);

  // Fetch students and albums
  useEffect(() => {
    fetchStudents();
    fetchAlbums();
  }, []);

  const fetchStudents = async () => {
    setLoading(prev => ({ ...prev, students: true }));

    try {
      const response = await fetch('/api/students');

      if (!response.ok) {
        throw new Error('فشل في جلب الطلاب');
      }

      const result = await response.json();

      // التحقق من تنسيق البيانات المستلمة
      if (result.students) {
        // API يعيد { students, total, pages }
        setStudents(result.students);
      } else if (result.success && result.data) {
        // تنسيق بديل { success: true, data: [...] }
        setStudents(result.data);
      } else if (Array.isArray(result)) {
        // تنسيق بديل - مصفوفة مباشرة
        setStudents(result);
      } else {
        throw new Error('تنسيق البيانات المستلمة غير متوقع');
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب الطلاب',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, students: false }));
    }
  };

  const fetchAlbums = async () => {
    setLoading(prev => ({ ...prev, albums: true }));

    try {
      const response = await fetch('/api/student-albums');

      if (!response.ok) {
        throw new Error('فشل في جلب الألبومات');
      }

      const result = await response.json();

      if (result.success) {
        setAlbums(result.data);
      } else {
        throw new Error(result.error || 'فشل في جلب الألبومات');
      }
    } catch (error) {
      console.error('Error fetching albums:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب الألبومات',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, albums: false }));
    }
  };

  const fetchImages = useCallback(async () => {
    setLoading(prev => ({ ...prev, images: true }));

    try {
      let url = '/api/student-images?';

      if (selectedStudent && selectedStudent !== 'all-students') {
        url += `studentId=${selectedStudent}&`;
      }

      if (selectedAlbum && selectedAlbum !== 'all-albums') {
        url += `albumId=${selectedAlbum}&`;
      }

      if (searchQuery) {
        url += `search=${encodeURIComponent(searchQuery)}&`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error('فشل في جلب الصور');
      }

      const result = await response.json();

      if (result.success) {
        setImages(result.data);
      } else {
        throw new Error(result.error || 'فشل في جلب الصور');
      }
    } catch (error) {
      console.error('Error fetching images:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب الصور',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, images: false }));
    }
  }, [selectedStudent, selectedAlbum, searchQuery]);

  // Fetch images when filters change
  useEffect(() => {
    if (selectedStudent || selectedAlbum) {
      fetchImages();
    }
  }, [selectedStudent, selectedAlbum, fetchImages]);

  const handleEditImage = (image: StudentImage) => {
    // تم إلغاء وظيفة تعديل الصور
    toast({
      title: "تنبيه",
      description: "تم إلغاء وظيفة تعديل الصور",
      variant: "default",
    });
  };

  const handleDeleteImage = async (imageId: number) => {
    try {
      const response = await fetch(`/api/student-images?id=${imageId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('فشل في حذف الصورة');
      }

      const result = await response.json();

      if (result.success) {
        // Refresh images
        fetchImages();

        toast({
          title: 'تم الحذف',
          description: 'تم حذف الصورة بنجاح',
        });
      } else {
        throw new Error(result.error || 'فشل في حذف الصورة');
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في حذف الصورة',
        variant: 'destructive',
      });
    }
  };

  const handleSetProfilePic = async (imageId: number) => {
    try {
      const response = await fetch(`/api/student-images`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: imageId,
          isProfilePic: true,
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في تعيين الصورة الشخصية');
      }

      const result = await response.json();

      if (result.success) {
        // Refresh images
        fetchImages();
      } else {
        throw new Error(result.error || 'فشل في تعيين الصورة الشخصية');
      }
    } catch (error) {
      console.error('Error setting profile pic:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في تعيين الصورة الشخصية',
        variant: 'destructive',
      });
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchImages();
  };

  const handleClearFilters = () => {
    setSelectedStudent('all-students');
    setSelectedAlbum('all-albums');
    setSearchQuery('');
    setImages([]);
  };

  // Get filter names
  const selectedStudentName = selectedStudent && selectedStudent !== 'all-students'
    ? students.find(student => student.id === parseInt(selectedStudent))?.name
    : '';

  const selectedAlbumName = selectedAlbum && selectedAlbum !== 'all-albums'
    ? albums.find(album => album.id === parseInt(selectedAlbum))?.name
    : '';

  return (
    <ProtectedRoute requiredPermission="admin.student-images.gallery">
      <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link href="/admin/student-images">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة إلى صور الطلاب
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">معرض الصور</h1>
        </div>

        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 ml-2" />
          {showFilters ? 'إخفاء الفلاتر' : 'عرض الفلاتر'}
        </Button>
      </div>

      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>فلترة الصور</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="student">الطالب</Label>
                  <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                    <SelectTrigger id="student">
                      <SelectValue placeholder="اختر الطالب" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-students">جميع الطلاب</SelectItem>
                      {students.map(student => (
                        <SelectItem key={student.id} value={student.id.toString()}>
                          {student.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="album">الألبوم</Label>
                  <Select value={selectedAlbum} onValueChange={setSelectedAlbum}>
                    <SelectTrigger id="album">
                      <SelectValue placeholder="اختر الألبوم" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-albums">جميع الألبومات</SelectItem>
                      {albums.map(album => (
                        <SelectItem key={album.id} value={album.id.toString()}>
                          {album.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="search">بحث</Label>
                  <div className="flex gap-2">
                    <Input
                      id="search"
                      placeholder="ابحث في وصف الصور"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <Button type="submit">
                      <Search className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClearFilters}
                >
                  مسح الفلاتر
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>
            {selectedStudentName && selectedAlbumName
              ? `صور الطالب ${selectedStudentName} في ألبوم ${selectedAlbumName}`
              : selectedStudentName
                ? `صور الطالب ${selectedStudentName}`
                : selectedAlbumName
                  ? `صور ألبوم ${selectedAlbumName}`
                  : 'جميع الصور'
            }
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading.images ? (
            <div className="text-center py-8">جاري تحميل الصور...</div>
          ) : images.length > 0 ? (
            <ImageGallery
              images={images}
              onEdit={handleEditImage}
              onDelete={handleDeleteImage}
              onSetProfilePic={handleSetProfilePic}
            />
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">لا توجد صور متاحة</p>
              <p className="text-gray-500 text-sm mt-2">
                يرجى اختيار طالب أو ألبوم للعرض
              </p>
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </ProtectedRoute>
  );
}

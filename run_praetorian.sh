#!/bin/bash

# Praetorian.ring - Penetration Testing Toolkit
# Launcher Script for Linux/Mac

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Banner
print_banner() {
    echo -e "${CYAN}"
    echo "  ____                _             _             "
    echo " |  _ \ _ __ __ _  ___| |_ ___  _ __(_) __ _ _ __   "
    echo " | |_) | '__/ _\` |/ _ \ __/ _ \| '__| |/ _\` | '_ \  "
    echo " |  __/| | | (_| |  __/ || (_) | |  | | (_| | | | | "
    echo " |_|   |_|  \__,_|\___|\__\___/|_|  |_|\__,_|_| |_| "
    echo ""
    echo "        مكتبة أدوات اختبار الاختراق الاحترافية"
    echo "                  للغة Ring"
    echo -e "${NC}"
    echo "==============================================="
    echo ""
}

# Check requirements
check_requirements() {
    echo "فحص متطلبات النظام..."
    echo ""
    
    # Check Ring
    echo -n "[1/4] فحص Ring Programming Language... "
    if command -v ring &> /dev/null; then
        echo -e "${GREEN}✓ Ring متاح${NC}"
    else
        echo -e "${RED}✗ Ring غير مثبت أو غير موجود في PATH${NC}"
        echo ""
        echo "يرجى تثبيت Ring من: https://ring-lang.github.io/"
        echo "أو التأكد من إضافته إلى متغير PATH"
        exit 1
    fi
    
    # Check Praetorian library
    echo -n "[2/4] فحص مكتبة Praetorian.ring... "
    if [ -f "praetorian.ring" ]; then
        echo -e "${GREEN}✓ مكتبة Praetorian.ring موجودة${NC}"
    else
        echo -e "${RED}✗ ملف praetorian.ring غير موجود${NC}"
        echo "تأكد من تشغيل الملف من المجلد الصحيح"
        exit 1
    fi
    
    # Check applications
    echo -n "[3/4] فحص التطبيقات... "
    if [ -d "applications" ]; then
        echo -e "${GREEN}✓ مجلد التطبيقات موجود${NC}"
    else
        echo -e "${RED}✗ مجلد التطبيقات غير موجود${NC}"
        exit 1
    fi
    
    # Check examples
    echo -n "[4/4] فحص الأمثلة... "
    if [ -d "examples" ]; then
        echo -e "${GREEN}✓ مجلد الأمثلة موجود${NC}"
    else
        echo -e "${RED}✗ مجلد الأمثلة غير موجود${NC}"
        exit 1
    fi
    
    echo ""
    echo "==============================================="
    echo -e "${GREEN}جميع المتطلبات الأساسية متوفرة!${NC}"
    echo "==============================================="
    echo ""
}

# Main menu
show_main_menu() {
    echo "ما الذي تريد تشغيله؟"
    echo ""
    echo "1. مشغل التطبيقات (Applications Launcher)"
    echo "2. ReconDash - لوحة التحكم الاستطلاعية"
    echo "3. DirHunter - أداة تخمين المجلدات"
    echo "4. تشغيل الأمثلة"
    echo "5. اختبار المكتبة"
    echo "6. اختبار التطبيقات"
    echo "7. عرض معلومات المكتبة"
    echo "0. خروج"
    echo ""
    read -p "اختر رقماً (0-7): " choice
}

# Run launcher
run_launcher() {
    echo ""
    echo "تشغيل مشغل التطبيقات..."
    cd applications
    ring launcher.ring
    cd ..
}

# Run ReconDash
run_recondash() {
    echo ""
    echo "تشغيل ReconDash..."
    echo -e "${YELLOW}ملاحظة: يتطلب libui.ring${NC}"
    echo ""
    cd applications/ReconDash
    ring ReconDash.ring
    cd ../..
}

# Run DirHunter
run_dirhunter() {
    echo ""
    echo "تشغيل DirHunter..."
    echo ""
    echo "DirHunter يتطلب معلمات سطر الأوامر."
    echo ""
    echo "أمثلة:"
    echo "ring DirHunter.ring -u http://httpbin.org -w wordlist.txt"
    echo "ring DirHunter.ring -h  (للمساعدة)"
    echo ""
    read -p "هل تريد تشغيل مثال تجريبي؟ (y/n): " run_example
    if [[ $run_example =~ ^[Yy]$ ]]; then
        cd applications/DirHunter
        ring DirHunter.ring -u http://httpbin.org -w wordlist.txt -t 5
        cd ../..
    else
        echo "لتشغيل DirHunter يدوياً:"
        echo "cd applications/DirHunter"
        echo "ring DirHunter.ring [OPTIONS]"
    fi
}

# Run examples
run_examples() {
    echo ""
    echo "تشغيل الأمثلة..."
    echo ""
    echo "1. المثال الأساسي (basic_scan.ring)"
    echo "2. تدقيق الويب (web_audit.ring)"
    echo "3. تدقيق SSL (ssl_audit.ring)"
    echo "0. العودة للقائمة الرئيسية"
    echo ""
    read -p "اختر مثالاً (0-3): " example_choice
    
    case $example_choice in
        1)
            echo "تشغيل المثال الأساسي..."
            cd examples
            ring basic_scan.ring
            cd ..
            ;;
        2)
            echo "تشغيل مثال تدقيق الويب..."
            cd examples
            ring web_audit.ring
            cd ..
            ;;
        3)
            echo "تشغيل مثال تدقيق SSL..."
            cd examples
            ring ssl_audit.ring
            cd ..
            ;;
        0)
            return
            ;;
        *)
            echo "خيار غير صحيح!"
            ;;
    esac
}

# Test library
test_library() {
    echo ""
    echo "تشغيل اختبار المكتبة..."
    ring test_praetorian.ring
}

# Test applications
test_applications() {
    echo ""
    echo "تشغيل اختبار التطبيقات..."
    cd applications
    ring test_applications.ring
    cd ..
}

# Show library info
show_info() {
    echo ""
    echo "عرض معلومات المكتبة..."
    ring -c "load 'praetorian.ring' oPraetorian = CreatePraetorian() oPraetorian.printInfo()"
}

# Exit function
exit_program() {
    echo ""
    echo -e "${GREEN}شكراً لاستخدام Praetorian.ring!${NC}"
    echo ""
    echo "للمزيد من المعلومات:"
    echo "- README.md: دليل المستخدم الشامل"
    echo "- applications/README.md: دليل التطبيقات"
    echo "- CHANGELOG.md: سجل التغييرات"
    echo ""
    echo -e "${YELLOW}تذكر: استخدم هذه الأدوات بمسؤولية وأخلاقية فقط!${NC}"
    echo ""
    exit 0
}

# Main function
main() {
    print_banner
    check_requirements
    
    while true; do
        show_main_menu
        
        case $choice in
            1)
                run_launcher
                ;;
            2)
                run_recondash
                ;;
            3)
                run_dirhunter
                ;;
            4)
                run_examples
                ;;
            5)
                test_library
                ;;
            6)
                test_applications
                ;;
            7)
                show_info
                ;;
            0)
                exit_program
                ;;
            *)
                echo -e "${RED}خيار غير صحيح!${NC}"
                ;;
        esac
        
        echo ""
        read -p "اضغط Enter للمتابعة..."
        echo ""
    done
}

# Make sure we're in the right directory
if [ ! -f "praetorian.ring" ]; then
    echo -e "${RED}خطأ: يجب تشغيل هذا الملف من مجلد Praetorian الرئيسي${NC}"
    exit 1
fi

# Run main function
main

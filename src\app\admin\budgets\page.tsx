'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { toast } from 'react-toastify'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import {
  FaChartPie,
  FaPlus,
  FaTrash,
  FaSearch,
  FaFilter,
  FaCheck,
  FaExclamationTriangle,
  FaTimesCircle,
  FaArrowRight,
  FaMoneyBillWave,
  FaCalendarAlt,
  FaExclamationCircle,
  FaCheckCircle,
  FaChartLine,
  FaClip<PERSON><PERSON>ist,
  <PERSON>a<PERSON>ell
} from 'react-icons/fa'
import Link from 'next/link'
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'

interface ExpenseCategory {
  id: number
  name: string
  description: string | null
  isActive: boolean
}

// واجهة BudgetItem تستخدم في API ولكن ليس في هذا المكون مباشرة

// واجهة Budget تستخدم في API ولكن ليس في هذا المكون مباشرة

interface BudgetSummary {
  id: number
  name: string
  startDate: string
  endDate: string
  totalBudget: number
  totalExpenses: number
  remainingBudget: number
  budgetUtilizationPercentage: number
  status: 'GOOD' | 'WARNING' | 'CRITICAL'
  itemsCount: number
}

// تنسيق التاريخ
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('fr-FR')
  } catch {
    return dateString
  }
}

// الحصول على حالة أداء الميزانية
const getBudgetPerformanceStatus = (percentage: number) => {
  if (percentage <= 75) return { status: 'ممتاز', color: 'bg-green-100 text-green-800', icon: <FaCheckCircle className="text-primary-color" /> }
  if (percentage <= 90) return { status: 'جيد', color: 'bg-yellow-100 text-yellow-800', icon: <FaExclamationCircle className="text-yellow-600" /> }
  if (percentage <= 100) return { status: 'تحذير', color: 'bg-orange-100 text-orange-800', icon: <FaExclamationTriangle className="text-orange-600" /> }
  return { status: 'تجاوز', color: 'bg-red-100 text-red-800', icon: <FaTimesCircle className="text-red-600" /> }
}

export default function BudgetsPage() {
  const [budgets, setBudgets] = useState<BudgetSummary[]>([])
  const [categories, setCategories] = useState<ExpenseCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [activeTab, setActiveTab] = useState('active')
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    startDate: '',
    endDate: '',
    totalAmount: '',
    items: [] as { categoryId: string; amount: string; notes: string }[]
  })

  // جلب الميزانيات
  const fetchBudgets = async () => {
    try {
      setLoading(true)
      const response = await fetch(
        `/api/budgets?query=${searchQuery}${statusFilter !== 'all' ? `&status=${statusFilter}` : ''}`
      )

      if (!response.ok) {
        throw new Error('فشل في جلب الميزانيات')
      }

      const data = await response.json()
      setBudgets(data.budgets)
    } catch (error) {
      console.error('خطأ في جلب الميزانيات:', error)
      toast.error('فشل في جلب الميزانيات')
    } finally {
      setLoading(false)
    }
  }

  // جلب فئات المصروفات
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/expense-categories?includeInactive=false')

      if (!response.ok) {
        throw new Error('فشل في جلب فئات المصروفات')
      }

      const data = await response.json()
      setCategories(data.categories)
    } catch (error) {
      console.error('خطأ في جلب فئات المصروفات:', error)
      toast.error('فشل في جلب فئات المصروفات')
    }
  }

  // إضافة ميزانية جديدة
  const handleAddBudget = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (!formData.name || !formData.startDate || !formData.endDate || !formData.totalAmount) {
        toast.error('جميع الحقول الأساسية مطلوبة')
        return
      }

      // التحقق من صحة التواريخ
      const startDate = new Date(formData.startDate)
      const endDate = new Date(formData.endDate)
      if (startDate >= endDate) {
        toast.error('يجب أن يكون تاريخ البداية قبل تاريخ النهاية')
        return
      }

      // التحقق من صحة المبلغ
      const totalAmount = parseFloat(formData.totalAmount)
      if (isNaN(totalAmount) || totalAmount <= 0) {
        toast.error('المبلغ الإجمالي يجب أن يكون رقمًا موجبًا')
        return
      }

      // تحويل بنود الميزانية
      const items = formData.items
        .filter((item) => item.categoryId && item.amount)
        .map((item) => ({
          categoryId: parseInt(item.categoryId),
          amount: parseFloat(item.amount),
          notes: item.notes || null
        }))

      const response = await fetch('/api/budgets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || null,
          startDate: formData.startDate,
          endDate: formData.endDate,
          totalAmount,
          items
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في إضافة الميزانية')
      }

      toast.success('تم إضافة الميزانية بنجاح')
      setIsAddDialogOpen(false)
      resetForm()
      fetchBudgets()
    } catch (error) {
      console.error('خطأ في إضافة الميزانية:', error)
      toast.error(error instanceof Error ? error.message : 'فشل في إضافة الميزانية')
    }
  }

  // إضافة بند ميزانية جديد
  const handleAddBudgetItem = () => {
    setFormData({
      ...formData,
      items: [
        ...formData.items,
        {
          categoryId: '',
          amount: '',
          notes: ''
        }
      ]
    })
  }

  // حذف بند ميزانية
  const handleRemoveBudgetItem = (index: number) => {
    const newItems = [...formData.items]
    newItems.splice(index, 1)
    setFormData({
      ...formData,
      items: newItems
    })
  }

  // تحديث بند ميزانية
  const handleUpdateBudgetItem = (index: number, field: string, value: string) => {
    const newItems = [...formData.items]
    newItems[index] = {
      ...newItems[index],
      [field]: value
    }
    setFormData({
      ...formData,
      items: newItems
    })
  }

  // إعادة تعيين نموذج البيانات
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      startDate: '',
      endDate: '',
      totalAmount: '',
      items: []
    })
  }



  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchCategories()
    fetchBudgets()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // تحديث البيانات عند تغيير معايير البحث
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      fetchBudgets()
    }, 500)

    return () => clearTimeout(delayDebounceFn)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, statusFilter, activeTab])

  // تصفية الميزانيات حسب الحالة
  const filteredBudgets = budgets.filter((budget) => {
    // نستخدم هنا تصفية بناءً على علامة التبويب النشطة
    // بدلاً من مقارنة الأنواع المختلفة مباشرة
    if (activeTab === 'active') {
      // الميزانيات النشطة (غير المؤرشفة وغير المكتملة)
      return !['ARCHIVED', 'COMPLETED'].includes(budget.status as string)
    } else if (activeTab === 'archived') {
      // الميزانيات المؤرشفة أو المكتملة
      return ['ARCHIVED', 'COMPLETED'].includes(budget.status as string)
    }
    return true
  })

  return (
    <OptimizedProtectedRoute requiredPermission="admin.budgets.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaChartPie className="text-[var(--primary-color)]" />
          إدارة الميزانية
        </h1>
        <QuickActionButtons
          entityType="budgets"
          actions={[
            {
              key: 'create',
              label: 'إنشاء ميزانية جديدة',
              icon: <FaPlus />,
              onClick: () => {
                resetForm()
                setIsAddDialogOpen(true)
              },
              variant: 'primary'
            }
          ]}
        />
      </div>

      <Tabs defaultValue="active" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="active" className="flex items-center gap-2">
            <FaCheck />
            <span>الميزانيات النشطة</span>
          </TabsTrigger>
          <TabsTrigger value="archived" className="flex items-center gap-2">
            <FaTimesCircle />
            <span>الميزانيات المؤرشفة</span>
          </TabsTrigger>
        </TabsList>

        <div className="flex flex-col md:flex-row gap-4 bg-white p-4 rounded-lg shadow-sm border border-green-100 mb-4">
          <div className="flex-1 flex items-center gap-2">
            <FaSearch className="text-[var(--primary-color)]" />
            <Input
              type="text"
              placeholder="بحث..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full border-[var(--primary-color)] focus:border-[var(--secondary-color)]"
            />
          </div>

          <div className="flex items-center gap-2">
            <FaFilter className="text-[var(--primary-color)]" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px] border-[var(--primary-color)] focus:border-[var(--secondary-color)]">
                <SelectValue placeholder="تصفية حسب الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="DRAFT">مسودة</SelectItem>
                <SelectItem value="ACTIVE">نشطة</SelectItem>
                <SelectItem value="COMPLETED">مكتملة</SelectItem>
                <SelectItem value="ARCHIVED">مؤرشفة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="active" className="mt-0">
          {/* لوحة معلومات الميزانيات النشطة */}
          {!loading && filteredBudgets.length > 0 && (
            <div className="mb-8">
              <Card className="bg-white shadow-md border border-[#e0f2ef]">
                <CardHeader className="pb-2">
                  <CardTitle className="text-xl flex items-center gap-2">
                    <FaChartLine className="text-[var(--primary-color)]" />
                    <span>لوحة معلومات الميزانيات النشطة</span>
                  </CardTitle>
                  <CardDescription>
                    نظرة عامة على أداء الميزانيات النشطة ومؤشرات الإنجاز
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* ملخص الميزانيات النشطة */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    {/* إجمالي الميزانيات النشطة */}
                    <div className="bg-[#f8fffd] p-4 rounded-lg border border-[#e0f2ef]">
                      <div className="flex items-center gap-2 mb-2">
                        <FaClipboardList className="text-[var(--primary-color)]" />
                        <span className="font-semibold">إجمالي الميزانيات</span>
                      </div>
                      <p className="text-2xl font-bold text-[var(--primary-color)]">{filteredBudgets.length}</p>
                      <p className="text-xs text-gray-500 mt-1">ميزانية نشطة</p>
                    </div>

                    {/* إجمالي المبالغ المخصصة */}
                    <div className="bg-[#f0f9ff] p-4 rounded-lg border border-[#e0f2ef]">
                      <div className="flex items-center gap-2 mb-2">
                        <FaMoneyBillWave className="text-blue-600" />
                        <span className="font-semibold">إجمالي المبالغ المخصصة</span>
                      </div>
                      <p className="text-2xl font-bold text-blue-600">
                        {filteredBudgets.reduce((sum, budget) => sum + budget.totalBudget, 0).toLocaleString('fr-FR')} د.ج
                      </p>
                      <p className="text-xs text-gray-500 mt-1">مجموع الميزانيات النشطة</p>
                    </div>

                    {/* إجمالي المصروفات الفعلية */}
                    <div className="bg-[#fff5f5] p-4 rounded-lg border border-[#ffebeb]">
                      <div className="flex items-center gap-2 mb-2">
                        <FaMoneyBillWave className="text-[#ef4444]" />
                        <span className="font-semibold">إجمالي المصروفات</span>
                      </div>
                      <p className="text-2xl font-bold text-[#ef4444]">
                        {filteredBudgets.reduce((sum, budget) => sum + budget.totalExpenses, 0).toLocaleString('fr-FR')} د.ج
                      </p>
                      <p className="text-xs text-gray-500 mt-1">مجموع المصروفات الفعلية</p>
                    </div>

                    {/* إجمالي المبالغ المتبقية */}
                    <div className="bg-[#f0fdf4] p-4 rounded-lg border border-[#dcfce7]">
                      <div className="flex items-center gap-2 mb-2">
                        <FaMoneyBillWave className="text-[#22c55e]" />
                        <span className="font-semibold">إجمالي المتبقي</span>
                      </div>
                      <p className="text-2xl font-bold text-[#22c55e]">
                        {filteredBudgets.reduce((sum, budget) => sum + budget.remainingBudget, 0).toLocaleString('fr-FR')} د.ج
                      </p>
                      <p className="text-xs text-gray-500 mt-1">مجموع المبالغ المتبقية</p>
                    </div>
                  </div>

                  {/* الميزانيات التي تحتاج انتباه */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                      <FaBell className="text-[#f59e0b]" />
                      <span>الميزانيات التي تحتاج انتباه</span>
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* الميزانيات التي تجاوزت 90% من المخصص */}
                      {(() => {
                        const warningBudgets = filteredBudgets.filter(budget => budget.budgetUtilizationPercentage >= 90);

                        if (warningBudgets.length === 0) {
                          return (
                            <div className="col-span-2 bg-gray-50 p-4 rounded-lg border border-dashed border-gray-300 text-center">
                              <FaCheckCircle className="mx-auto text-primary-color mb-2" size={24} />
                              <p className="text-gray-600">لا توجد ميزانيات تحتاج انتباه حاليًا</p>
                            </div>
                          );
                        }

                        return warningBudgets.slice(0, 4).map(budget => {
                          const performanceStatus = getBudgetPerformanceStatus(budget.budgetUtilizationPercentage);

                          return (
                            <Link href={`/admin/budgets/${budget.id}`} key={budget.id}>
                              <Card className="hover:shadow-md transition-shadow duration-300 cursor-pointer h-full border-r-4 border-r-yellow-500">
                                <CardContent className="p-4">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <h4 className="font-semibold text-base mb-1">{budget.name}</h4>
                                      <p className="text-xs text-gray-500 mb-2">
                                        <FaCalendarAlt className="inline ml-1" size={10} />
                                        {formatDate(budget.startDate)} - {formatDate(budget.endDate)}
                                      </p>
                                    </div>
                                    <Badge className={performanceStatus.color}>
                                      <span className="flex items-center gap-1">
                                        {performanceStatus.icon}
                                        {performanceStatus.status}
                                      </span>
                                    </Badge>
                                  </div>

                                  <div className="mt-2">
                                    <div className="flex justify-between items-center mb-1">
                                      <span className="text-xs text-gray-600">نسبة الاستخدام:</span>
                                      <span className="text-xs font-bold">{Math.round(budget.budgetUtilizationPercentage)}%</span>
                                    </div>
                                    <Progress
                                      value={budget.budgetUtilizationPercentage}
                                      className="h-2"
                                      indicatorClassName={
                                        budget.budgetUtilizationPercentage <= 75
                                          ? 'bg-primary-color'
                                          : budget.budgetUtilizationPercentage <= 90
                                          ? 'bg-yellow-600'
                                          : budget.budgetUtilizationPercentage <= 100
                                          ? 'bg-orange-600'
                                          : 'bg-red-600'
                                      }
                                    />
                                  </div>

                                  <div className="flex justify-between items-center mt-3 text-sm">
                                    <div>
                                      <span className="text-gray-600">المتبقي:</span>
                                      <span className="font-bold mr-1 text-primary-color">{budget.remainingBudget.toLocaleString('fr-FR')} د.ج</span>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="text-[var(--primary-color)] hover:bg-[#e0f2ef] h-7 text-xs"
                                    >
                                      <span>التفاصيل</span>
                                      <FaArrowRight className="mr-1" size={10} />
                                    </Button>
                                  </div>
                                </CardContent>
                              </Card>
                            </Link>
                          );
                        });
                      })()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {renderBudgetsList(filteredBudgets, loading)}
        </TabsContent>

        <TabsContent value="archived" className="mt-0">
          {renderBudgetsList(filteredBudgets, loading)}
        </TabsContent>
      </Tabs>

      {/* نافذة إضافة ميزانية جديدة */}
      <AnimatedDialog
        isOpen={isAddDialogOpen}
        onClose={() => setIsAddDialogOpen(false)}
        title="إنشاء ميزانية جديدة"
        variant="primary"
        footer={
          <Button
            type="submit"
            form="add-budget-form"
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
          >
            <FaPlus size={14} />
            <span>إنشاء</span>
          </Button>
        }
      >
        <form id="add-budget-form" onSubmit={handleAddBudget} className="max-h-[70vh] overflow-y-auto">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right col-span-1">
                اسم الميزانية <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right col-span-1">
                الوصف
              </Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="startDate" className="text-right col-span-1">
                تاريخ البداية <span className="text-red-500">*</span>
              </Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="endDate" className="text-right col-span-1">
                تاريخ النهاية <span className="text-red-500">*</span>
              </Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="totalAmount" className="text-right col-span-1">
                المبلغ الإجمالي <span className="text-red-500">*</span>
              </Label>
              <Input
                id="totalAmount"
                type="number"
                value={formData.totalAmount}
                onChange={(e) => setFormData({ ...formData, totalAmount: e.target.value })}
                className="col-span-3"
                required
              />
            </div>

            <div className="border-t border-gray-200 pt-4 mt-2">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">بنود الميزانية</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef]"
                  onClick={handleAddBudgetItem}
                >
                  <FaPlus className="ml-1" size={12} /> إضافة بند
                </Button>
              </div>

              {formData.items.length === 0 ? (
                <div className="text-center py-4 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                  <p className="text-gray-500">لم يتم إضافة أي بنود للميزانية بعد</p>
                  <Button
                    type="button"
                    variant="link"
                    className="text-[var(--primary-color)] mt-2"
                    onClick={handleAddBudgetItem}
                  >
                    إضافة بند جديد
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {formData.items.map((item, index) => (
                    <div key={index} className="p-3 border rounded-lg bg-gray-50">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium">البند #{index + 1}</h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 h-8 w-8 p-0"
                          onClick={() => handleRemoveBudgetItem(index)}
                        >
                          <FaTrash size={12} />
                        </Button>
                      </div>
                      <div className="grid gap-3">
                        <div className="grid grid-cols-4 items-center gap-2">
                          <Label htmlFor={`category-${index}`} className="text-right col-span-1 text-sm">
                            الفئة <span className="text-red-500">*</span>
                          </Label>
                          <Select
                            value={item.categoryId}
                            onValueChange={(value) => handleUpdateBudgetItem(index, 'categoryId', value)}
                          >
                            <SelectTrigger id={`category-${index}`} className="col-span-3">
                              <SelectValue placeholder="اختر فئة المصروفات" />
                            </SelectTrigger>
                            <SelectContent>
                              {categories.map((category) => (
                                <SelectItem key={category.id} value={category.id.toString()}>
                                  {category.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="grid grid-cols-4 items-center gap-2">
                          <Label htmlFor={`amount-${index}`} className="text-right col-span-1 text-sm">
                            المبلغ <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id={`amount-${index}`}
                            type="number"
                            value={item.amount}
                            onChange={(e) => handleUpdateBudgetItem(index, 'amount', e.target.value)}
                            className="col-span-3"
                          />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-2">
                          <Label htmlFor={`notes-${index}`} className="text-right col-span-1 text-sm">
                            ملاحظات
                          </Label>
                          <Input
                            id={`notes-${index}`}
                            value={item.notes}
                            onChange={(e) => handleUpdateBudgetItem(index, 'notes', e.target.value)}
                            className="col-span-3"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </form>
      </AnimatedDialog>
      </div>
    </OptimizedProtectedRoute>
  )
}

// عرض قائمة الميزانيات
function renderBudgetsList(budgets: BudgetSummary[], loading: boolean) {
  // تنسيق المبلغ - نستخدمه في الكود لعرض المبالغ
  // لكن نستخدم toLocaleString مباشرة في JSX
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
      </div>
    )
  }

  if (budgets.length === 0) {
    return (
      <div className="text-center py-12 bg-white rounded-lg border border-dashed border-gray-300">
        <FaChartPie className="mx-auto text-4xl text-gray-300 mb-4" />
        <h3 className="text-xl font-bold text-gray-600 mb-2">لا توجد ميزانيات</h3>
        <p className="text-gray-500 mb-4">لم يتم العثور على أي ميزانيات مطابقة لمعايير البحث</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {budgets.map((budget) => (
        <Link href={`/admin/budgets/${budget.id}`} key={budget.id}>
          <Card className="hover:shadow-md transition-shadow duration-300 cursor-pointer h-full">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <FaChartPie className="text-[var(--primary-color)]" />
                <span>{budget.name}</span>
              </CardTitle>
              <CardDescription>
                {formatDate(budget.startDate)} - {formatDate(budget.endDate)}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">الميزانية الإجمالية:</span>
                  <span className="font-bold">{budget.totalBudget ? budget.totalBudget.toLocaleString('fr-FR') : '0'} د.ج</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">المصروفات الفعلية:</span>
                  <span className="font-bold text-red-600">{budget.totalExpenses ? budget.totalExpenses.toLocaleString('fr-FR') : '0'} د.ج</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">المتبقي:</span>
                  <span className="font-bold text-primary-color">{budget.remainingBudget ? budget.remainingBudget.toLocaleString('fr-FR') : '0'} د.ج</span>
                </div>

                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">نسبة الاستخدام:</span>
                    <span
                      className={`text-sm font-bold ${
                        budget.status === 'GOOD'
                          ? 'text-primary-color'
                          : budget.status === 'WARNING'
                          ? 'text-yellow-600'
                          : 'text-red-600'
                      }`}
                    >
                      {budget.budgetUtilizationPercentage ? Math.round(budget.budgetUtilizationPercentage) : 0}%
                    </span>
                  </div>
                  <Progress
                    value={budget.budgetUtilizationPercentage || 0}
                    className={`h-2 ${
                      budget.status === 'GOOD'
                        ? 'bg-green-100'
                        : budget.status === 'WARNING'
                        ? 'bg-yellow-100'
                        : 'bg-red-100'
                    }`}
                    indicatorClassName={
                      budget.status === 'GOOD'
                        ? 'bg-primary-color'
                        : budget.status === 'WARNING'
                        ? 'bg-yellow-600'
                        : 'bg-red-600'
                    }
                  />
                </div>

                <div className="flex flex-wrap gap-2 mt-2">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                    {budget.itemsCount} بند
                  </span>
                  {budget.status === 'GOOD' ? (
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs flex items-center">
                      <FaCheck className="ml-1" size={10} /> جيد
                    </span>
                  ) : budget.status === 'WARNING' ? (
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs flex items-center">
                      <FaExclamationTriangle className="ml-1" size={10} /> تحذير
                    </span>
                  ) : (
                    <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs flex items-center">
                      <FaTimesCircle className="ml-1" size={10} /> حرج
                    </span>
                  )}
                </div>

                <div className="flex justify-end mt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-[var(--primary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                  >
                    <span>عرض التفاصيل</span>
                    <FaArrowRight size={12} />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  )
}

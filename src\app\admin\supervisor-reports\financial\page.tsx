'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Download, Printer, DollarSign, TrendingUp, TrendingDown, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { FinancialReportData, FinancialFilters } from '@/lib/reports/financial-report-service';
import { exportToPdf } from '@/utils/export-utils';
import { toast } from 'react-toastify';

export default function FinancialReportPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [reportData, setReportData] = useState<FinancialReportData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FinancialFilters>({});

  const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : new Date();
  const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : new Date();

  // معلومات المكتب البلدي من المعاملات
  const officeInfo = {
    organizationName: searchParams.get('organizationName') || 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن',
    officeName: searchParams.get('officeName') || 'المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر',
    branchName: searchParams.get('branchName') || 'شعبة بلدية المنقر',
    presidentName: searchParams.get('presidentName') || 'الوليد بن ناصر قصي',
    presidentTitle: searchParams.get('presidentTitle') || 'رئيس المكتب البلدي',
    logoUrl: searchParams.get('logoUrl') || '',
  };

  useEffect(() => {
    fetchReportData();
  }, [searchParams, filters]);

  const fetchReportData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/reports/financial?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
      );

      if (response.ok) {
        const result = await response.json();
        setReportData(result.data);
      } else {
        console.error('فشل في جلب بيانات التقرير المالي');
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات التقرير المالي:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async (exportFormat: 'excel' | 'pdf') => {
    if (!reportData) {
      console.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      if (exportFormat === 'pdf') {
        // استخدام exportToPdf من utils/export-utils.ts مباشرة في المتصفح
        const formatCurrency = (amount: number) => {
          return amount.toLocaleString('ar-DZ') + ' د.ج';
        };

        // إعداد بيانات الجداول
        const summaryData = [
          ['الرصيد الافتتاحي', formatCurrency(reportData.executiveSummary.openingBalance)],
          ['إجمالي المداخيل', formatCurrency(reportData.executiveSummary.totalIncome)],
          ['إجمالي المصروفات', formatCurrency(reportData.executiveSummary.totalExpenses)],
          ['صافي الربح/الخسارة', formatCurrency(reportData.executiveSummary.netProfit)],
          ['الرصيد الختامي', formatCurrency(reportData.executiveSummary.closingBalance)],
          ['إجمالي المعاملات', reportData.executiveSummary.totalTransactions.toString()],
        ];

        const incomeData = [
          ['مدفوعات الطلاب', reportData.incomeDetails.studentPayments.count.toString(), formatCurrency(reportData.incomeDetails.studentPayments.amount), `${reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%`],
          ['التبرعات', reportData.incomeDetails.donations.count.toString(), formatCurrency(reportData.incomeDetails.donations.amount), `${reportData.incomeDetails.donations.percentage.toFixed(1)}%`],
          ['مداخيل أخرى', reportData.incomeDetails.otherIncomes.count.toString(), formatCurrency(reportData.incomeDetails.otherIncomes.amount), `${reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%`],
        ];

        const expenseData = reportData.expenseDetails.byCategory.map(category => [
          category.name,
          category.expensesCount.toString(),
          formatCurrency(category.totalAmount),
          `${category.percentage.toFixed(1)}%`
        ]);

        const monthlyData = reportData.monthlyStats.map(month => [
          format(new Date(month.month + '-01'), 'MMMM yyyy', { locale: ar }),
          formatCurrency(month.payments.amount),
          formatCurrency(month.donations.amount),
          formatCurrency(month.expenses.amount),
          formatCurrency(month.netProfit)
        ]);

        const paymentMethodData = reportData.paymentMethodStats.map(method => [
          method.name,
          formatCurrency(method.paymentsAmount),
          formatCurrency(method.donationsAmount),
          formatCurrency(method.totalAmount)
        ]);

        // استيراد وتشغيل exportToPdf
        const { exportToPdf } = await import('@/utils/export-utils');

        exportToPdf({
          title: `${officeInfo.organizationName}\n${officeInfo.officeName}\n${officeInfo.branchName}\nالتقرير المالي`,
          fileName: `التقرير_المالي_${new Date().toISOString().split('T')[0]}.pdf`,
          tables: [
            {
              title: 'الملخص التنفيذي',
              headers: ['البيان', 'المبلغ'],
              data: summaryData,
              headStyles: {
                fillColor: [22, 155, 136],
                textColor: [255, 255, 255]
              }
            },
            {
              title: 'تفاصيل المداخيل',
              headers: ['نوع المدخول', 'العدد', 'المبلغ', 'النسبة'],
              data: incomeData,
              headStyles: {
                fillColor: [34, 197, 94],
                textColor: [255, 255, 255]
              }
            },
            {
              title: 'تفاصيل المصروفات',
              headers: ['فئة المصروف', 'العدد', 'المبلغ', 'النسبة'],
              data: expenseData,
              headStyles: {
                fillColor: [239, 68, 68],
                textColor: [255, 255, 255]
              }
            },
            {
              title: 'الإحصائيات الشهرية',
              headers: ['الشهر', 'المدفوعات', 'التبرعات', 'المصروفات', 'صافي الربح'],
              data: monthlyData,
              headStyles: {
                fillColor: [168, 85, 247],
                textColor: [255, 255, 255]
              }
            },
            {
              title: 'إحصائيات طرق الدفع',
              headers: ['طريقة الدفع', 'المدفوعات', 'التبرعات', 'الإجمالي'],
              data: paymentMethodData,
              headStyles: {
                fillColor: [245, 158, 11],
                textColor: [255, 255, 255]
              }
            }
          ],
          additionalContent: [
            {
              text: `للفترة من ${format(startDate, 'PPP', { locale: ar })} إلى ${format(endDate, 'PPP', { locale: ar })}`,
              x: 105,
              y: 30,
              options: { align: 'center' }
            },
            {
              text: `تقرير يوم ${format(new Date(), 'PPP', { locale: ar })}\nعن ${officeInfo.presidentTitle}: ${officeInfo.presidentName}`,
              x: 105,
              y: 280,
              options: { align: 'center' }
            }
          ]
        });

      } else if (exportFormat === 'excel') {
        // تصدير Excel عبر API
        const response = await fetch('/api/reports/financial/export', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            format: 'excel',
            options: {
              ...filters,
              organizationName: officeInfo.organizationName,
              officeName: officeInfo.officeName,
              branchName: officeInfo.branchName,
              presidentName: officeInfo.presidentName,
              presidentTitle: officeInfo.presidentTitle,
              logoUrl: officeInfo.logoUrl,
            },
          }),
        });

        if (response.ok) {
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `التقرير_المالي_${new Date().toISOString().split('T')[0]}.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } else {
          console.error('فشل في تصدير التقرير المالي');
        }
      }
    } catch (error) {
      console.error('خطأ في تصدير التقرير المالي:', error);
    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleBack = () => {
    router.back();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل التقرير المالي...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="text-center">
          <p className="text-red-600">فشل في تحميل بيانات التقرير المالي</p>
          <Button onClick={handleBack} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* شريط الأدوات */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm border print:hidden">
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Badge variant="secondary">
            <Calendar className="h-3 w-3 mr-1" />
            {format(startDate, 'PPP', { locale: ar })} - {format(endDate, 'PPP', { locale: ar })}
          </Badge>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExport('excel')}>
            <Download className="h-4 w-4 mr-2" />
            تصدير Excel
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
            <Download className="h-4 w-4 mr-2" />
            تصدير PDF
          </Button>
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            طباعة
          </Button>
        </div>
      </div>

      {/* التقرير المالي */}
      <div className="bg-white rounded-lg shadow-sm border p-8 print:shadow-none print:border-none">
        {/* رأس التقرير */}
        <div className="text-center mb-8 border-b pb-6">
          {officeInfo.logoUrl && (
            <div className="flex justify-center mb-4">
              <img
                src={officeInfo.logoUrl}
                alt="شعار الجمعية"
                className="h-20 w-20 object-contain"
              />
            </div>
          )}
          <h1 className="text-2xl font-bold mb-2">{officeInfo.organizationName}</h1>
          <h2 className="text-xl mb-2">{officeInfo.officeName}</h2>
          <h3 className="text-lg mb-4">{officeInfo.branchName}</h3>
          <h2 className="text-xl font-bold mb-2">التقرير المالي لـ{officeInfo.branchName}</h2>
          <p className="text-gray-600">
            للفترة من: {format(startDate, 'PPP', { locale: ar })} إلى: {format(endDate, 'PPP', { locale: ar })}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            تاريخ إنشاء التقرير: {format(new Date(), 'PPP', { locale: ar })}
          </p>
        </div>

        {/* الملخص التنفيذي */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">📊 الملخص التنفيذي</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg text-center">
              <div className="text-sm text-blue-700 mb-1">الرصيد الافتتاحي</div>
              <div className="text-xl font-bold text-blue-900">
                {formatCurrency(reportData.executiveSummary.openingBalance)}
              </div>
            </div>

            <div className="bg-green-50 p-4 rounded-lg text-center">
              <TrendingUp className="h-6 w-6 text-green-600 mx-auto mb-1" />
              <div className="text-sm text-green-700 mb-1">إجمالي المداخيل</div>
              <div className="text-xl font-bold text-green-900">
                {formatCurrency(reportData.executiveSummary.totalIncome)}
              </div>
            </div>

            <div className="bg-red-50 p-4 rounded-lg text-center">
              <TrendingDown className="h-6 w-6 text-red-600 mx-auto mb-1" />
              <div className="text-sm text-red-700 mb-1">إجمالي المصروفات</div>
              <div className="text-xl font-bold text-red-900">
                {formatCurrency(reportData.executiveSummary.totalExpenses)}
              </div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg text-center">
              <div className="text-sm text-purple-700 mb-1">الرصيد الختامي</div>
              <div className="text-xl font-bold text-purple-900">
                {formatCurrency(reportData.executiveSummary.closingBalance)}
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-lg font-medium">صافي الربح/الخسارة:</span>
              <span className={`text-xl font-bold ${
                reportData.executiveSummary.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatCurrency(reportData.executiveSummary.netProfit)}
              </span>
            </div>
            <div className="text-sm text-gray-600 mt-1">
              إجمالي المعاملات: {reportData.executiveSummary.totalTransactions} معاملة
            </div>
          </div>
        </div>

        {/* تفاصيل المداخيل */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">📋 تفاصيل المداخيل</h3>
          <div className="overflow-x-auto mb-4">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 p-3 text-right">نوع المدخول</th>
                  <th className="border border-gray-300 p-3 text-center">العدد</th>
                  <th className="border border-gray-300 p-3 text-center">المبلغ</th>
                  <th className="border border-gray-300 p-3 text-center">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-300 p-3">مدفوعات الطلاب</td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.incomeDetails.studentPayments.count}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {formatCurrency(reportData.incomeDetails.studentPayments.amount)}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">التبرعات</td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.incomeDetails.donations.count}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {formatCurrency(reportData.incomeDetails.donations.amount)}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.incomeDetails.donations.percentage.toFixed(1)}%
                  </td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">مداخيل أخرى</td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.incomeDetails.otherIncomes.count}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {formatCurrency(reportData.incomeDetails.otherIncomes.amount)}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%
                  </td>
                </tr>
                <tr className="bg-gray-50 font-bold">
                  <td className="border border-gray-300 p-3">الإجمالي</td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.incomeDetails.studentPayments.count +
                     reportData.incomeDetails.donations.count +
                     reportData.incomeDetails.otherIncomes.count}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {formatCurrency(reportData.executiveSummary.totalIncome)}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">100%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* تفاصيل المصروفات */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">💸 تفاصيل المصروفات</h3>
          <div className="overflow-x-auto mb-4">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 p-3 text-right">فئة المصروف</th>
                  <th className="border border-gray-300 p-3 text-center">العدد</th>
                  <th className="border border-gray-300 p-3 text-center">المبلغ</th>
                  <th className="border border-gray-300 p-3 text-center">النسبة</th>
                </tr>
              </thead>
              <tbody>
                {reportData.expenseDetails.byCategory.map((category) => (
                  <tr key={category.id}>
                    <td className="border border-gray-300 p-3">
                      <div className="flex items-center gap-2">
                        <span className={`w-3 h-3 rounded-full`} style={{ backgroundColor: category.color }}></span>
                        {category.name}
                      </div>
                    </td>
                    <td className="border border-gray-300 p-3 text-center">
                      {category.expensesCount}
                    </td>
                    <td className="border border-gray-300 p-3 text-center">
                      {formatCurrency(category.totalAmount)}
                    </td>
                    <td className="border border-gray-300 p-3 text-center">
                      {category.percentage.toFixed(1)}%
                    </td>
                  </tr>
                ))}
                <tr className="bg-gray-50 font-bold">
                  <td className="border border-gray-300 p-3">الإجمالي</td>
                  <td className="border border-gray-300 p-3 text-center">
                    {reportData.expenseDetails.byCategory.reduce((sum, cat) => sum + cat.expensesCount, 0)}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">
                    {formatCurrency(reportData.expenseDetails.total)}
                  </td>
                  <td className="border border-gray-300 p-3 text-center">100%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* إحصائيات طرق الدفع */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">💳 إحصائيات طرق الدفع</h3>
          <div className="overflow-x-auto mb-4">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 p-3 text-right">طريقة الدفع</th>
                  <th className="border border-gray-300 p-3 text-center">المدفوعات</th>
                  <th className="border border-gray-300 p-3 text-center">التبرعات</th>
                  <th className="border border-gray-300 p-3 text-center">الإجمالي</th>
                </tr>
              </thead>
              <tbody>
                {reportData.paymentMethodStats.map((method) => (
                  <tr key={method.id}>
                    <td className="border border-gray-300 p-3">{method.name}</td>
                    <td className="border border-gray-300 p-3 text-center">
                      {formatCurrency(method.paymentsAmount)}
                      <div className="text-xs text-gray-500">({method.paymentsCount} معاملة)</div>
                    </td>
                    <td className="border border-gray-300 p-3 text-center">
                      {formatCurrency(method.donationsAmount)}
                      <div className="text-xs text-gray-500">({method.donationsCount} معاملة)</div>
                    </td>
                    <td className="border border-gray-300 p-3 text-center font-medium">
                      {formatCurrency(method.totalAmount)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* الإحصائيات الشهرية */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">📈 الإحصائيات الشهرية</h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 p-3 text-center">الشهر</th>
                  <th className="border border-gray-300 p-3 text-center">المدفوعات</th>
                  <th className="border border-gray-300 p-3 text-center">التبرعات</th>
                  <th className="border border-gray-300 p-3 text-center">المصروفات</th>
                  <th className="border border-gray-300 p-3 text-center">صافي الربح</th>
                </tr>
              </thead>
              <tbody>
                {reportData.monthlyStats.map((month) => (
                  <tr key={month.month}>
                    <td className="border border-gray-300 p-3 text-center font-medium">
                      {format(new Date(month.month + '-01'), 'MMMM yyyy', { locale: ar })}
                    </td>
                    <td className="border border-gray-300 p-3 text-center">
                      {formatCurrency(month.payments.amount)}
                      <div className="text-xs text-gray-500">({month.payments.count})</div>
                    </td>
                    <td className="border border-gray-300 p-3 text-center">
                      {formatCurrency(month.donations.amount)}
                      <div className="text-xs text-gray-500">({month.donations.count})</div>
                    </td>
                    <td className="border border-gray-300 p-3 text-center">
                      {formatCurrency(month.expenses.amount)}
                      <div className="text-xs text-gray-500">({month.expenses.count})</div>
                    </td>
                    <td className={`border border-gray-300 p-3 text-center font-medium ${
                      month.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(month.netProfit)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* التوقيع */}
        <div className="text-left mt-12">
          <p className="mb-2">تقرير يوم {format(new Date(), 'PPP', { locale: ar })}</p>
          <p className="mb-8">عن {officeInfo.presidentTitle}:</p>
          <p className="font-bold">{officeInfo.presidentName}</p>
        </div>
      </div>
    </div>
  );
}

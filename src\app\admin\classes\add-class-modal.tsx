'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { FaChalkboardTeacher, FaPlus } from 'react-icons/fa'

interface Subject {
  id: number
  name: string
}

interface Teacher {
  id: number
  name: string
}

interface TeacherSubject {
  id: number;
  teacher: Teacher;
  subject: Subject;
}

interface AddClassModalProps {
  isOpen: boolean
  onCloseAction: () => void
  onSuccessAction: () => void
}

export function AddClassModal({ isOpen, onCloseAction, onSuccessAction }: AddClassModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    capacity: '30',
    description: '',
    teacherSubjects: [] as number[]
  })
  const [teacherSubjects, setTeacherSubjects] = useState<TeacherSubject[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchTeacherSubjects = async () => {
      try {
        const response = await fetch('/api/teacher-subjects')
        if (!response.ok) throw new Error('Failed to fetch teacher subjects')
        const result = await response.json()

        // تحقق من تنسيق البيانات المرجعة
        let teacherSubjectsData = []
        if (result.success && Array.isArray(result.data)) {
          teacherSubjectsData = result.data
        } else if (Array.isArray(result)) {
          teacherSubjectsData = result
        } else {
          console.warn('Teacher subjects data format is unexpected:', result)
        }

        setTeacherSubjects(teacherSubjectsData)
      } catch (err: Error | unknown) {
        setError('Failed to load teacher subjects')
        setTeacherSubjects([]) // تأكد من أن teacherSubjects يبقى array حتى في حالة الخطأ
        console.error(err)
        toast({
          title: 'خطأ',
          description: err instanceof Error ? err.message : 'فشل في جلب بيانات المعلمين والمواد',
          variant: 'destructive'
        })
      }
    }
     if(isOpen){
      fetchTeacherSubjects()
     }
  }, [isOpen])

  const handleAdd = async () => {
    setIsLoading(true)
    setError('')
    try {
      const response = await fetch('/api/classes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to add class')
      }

      onSuccessAction()
      onCloseAction()
      setFormData({ name: '', capacity: '30', description: '', teacherSubjects: [] })
      toast({
        title: 'نجاح',
        description: 'تم إضافة القسم بنجاح'
      })
    } catch (err: Error | unknown) {
      setError(err instanceof Error ? err.message : 'فشل في إضافة القسم')
      console.error('Error adding class:', err)
      toast({
        title: 'خطأ',
        description: err instanceof Error ? err.message : 'فشل في إضافة القسم',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const dialogFooter = (
    <Button
      onClick={handleAdd}
      disabled={isLoading || !formData.name.trim() || formData.teacherSubjects.length === 0}
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
    >
      <FaPlus className="ml-1" />
      {isLoading ? 'جاري الإضافة...' : 'إضافة'}
    </Button>
  )

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title = {
        <div className="flex items-center gap-2 text-[var(--primary-color)]">
          <FaChalkboardTeacher className="text-[var(--primary-color)]" />
          <span>إضافة قسم جديد</span>
        </div>
      }
      variant="primary"
      footer={dialogFooter}
      className="rtl"
    >
      {error && <div className="text-red-500 text-center p-2 bg-red-50 rounded-md mb-4 border border-red-100">{error}</div>}
      <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name" className="text-right font-medium text-[var(--primary-color)]">اسم القسم</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              placeholder="أدخل اسم القسم"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="capacity" className="text-right font-medium text-[var(--primary-color)]">السعة الاستيعابية</Label>
            <Input
              id="capacity"
              type="number"
              min="1"
              max="100"
              value={formData.capacity}
              onChange={(e) => setFormData({ ...formData, capacity: e.target.value })}
              className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              placeholder="أدخل السعة الاستيعابية للفصل"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="description" className="text-right font-medium text-[var(--primary-color)]">وصف الفصل</Label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] rounded-md p-2 min-h-[80px]"
              placeholder="أدخل وصفاً للفصل (اختياري)"
            />
          </div>
          <div className="grid gap-2">
            <Label className="text-right font-medium text-[var(--primary-color)]">المعلمين والمواد</Label>
            <div className="max-h-60 overflow-y-auto p-2 border border-[#e0f2ef] rounded-md bg-[#f8fffd]">
              {!teacherSubjects || !Array.isArray(teacherSubjects) || teacherSubjects.length === 0 ? (
                <p className="text-center text-gray-500 py-2">لا توجد بيانات متاحة</p>
              ) : (
                teacherSubjects.map((ts) => (
                  <div key={ts.id} className="flex items-center gap-2 p-2 hover:bg-gray-100 rounded-md">
                    <input
                      type="checkbox"
                      id={`ts-${ts.id}`}
                      checked={formData.teacherSubjects.includes(ts.id)}
                      onChange={(e) => {
                        const newTeacherSubjects = e.target.checked
                          ? [...formData.teacherSubjects, ts.id]
                          : formData.teacherSubjects.filter((id) => id !== ts.id)
                        setFormData({ ...formData, teacherSubjects: newTeacherSubjects })
                      }}
                      className="h-4 w-4 text-[var(--primary-color)] rounded accent-[var(--primary-color)]"
                    />
                    <label htmlFor={`ts-${ts.id}`} className="flex-1 cursor-pointer text-right">
                      {ts.teacher.name} - {ts.subject.name}
                    </label>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </AnimatedDialog>
  )
}

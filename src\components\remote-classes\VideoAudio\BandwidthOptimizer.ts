/**
 * Class to optimize bandwidth usage for video and audio streams
 * This class provides methods to optimize bandwidth usage by adjusting
 * video and audio quality based on available bandwidth and priorities
 */
export class BandwidthOptimizer {
  // Bandwidth thresholds in kbps
  private static readonly LOW_BANDWIDTH = 500;
  private static readonly MEDIUM_BANDWIDTH = 1500;

  // Default priorities
  private static readonly DEFAULT_PRIORITIES = {
    audio: 1.0,  // Highest priority
    video: 0.8,
    screenShare: 0.9,
    whiteboard: 0.5
  };

  // Current bandwidth estimate in kbps
  private currentBandwidth: number = 2000;

  // Active streams
  private audioStreams: MediaStream[] = [];
  private videoStreams: MediaStream[] = [];
  private screenShareStreams: MediaStream[] = [];

  // Stream priorities (higher value = higher priority)
  // سيتم استخدام هذه القيم في المستقبل لتحسين توزيع عرض النطاق الترددي
  private readonly priorities: {
    audio: number;
    video: number;
    screenShare: number;
    whiteboard: number;
  };

  /**
   * Constructor
   * @param priorities Custom priorities for different stream types
   */
  constructor(priorities?: Partial<typeof BandwidthOptimizer.DEFAULT_PRIORITIES>) {
    this.priorities = {
      ...BandwidthOptimizer.DEFAULT_PRIORITIES,
      ...priorities
    };
  }

  /**
   * Add an audio stream to be managed
   * @param stream Audio media stream
   */
  public addAudioStream(stream: MediaStream): void {
    this.audioStreams.push(stream);
    this.optimize();
  }

  /**
   * Add a video stream to be managed
   * @param stream Video media stream
   */
  public addVideoStream(stream: MediaStream): void {
    this.videoStreams.push(stream);
    this.optimize();
  }

  /**
   * Add a screen share stream to be managed
   * @param stream Screen share media stream
   */
  public addScreenShareStream(stream: MediaStream): void {
    this.screenShareStreams.push(stream);
    this.optimize();
  }

  /**
   * Remove a stream from management
   * @param stream Media stream to remove
   */
  public removeStream(stream: MediaStream): void {
    this.audioStreams = this.audioStreams.filter(s => s !== stream);
    this.videoStreams = this.videoStreams.filter(s => s !== stream);
    this.screenShareStreams = this.screenShareStreams.filter(s => s !== stream);
    this.optimize();
  }

  /**
   * Update the current bandwidth estimate
   * @param bandwidthKbps Bandwidth in kbps
   */
  public updateBandwidth(bandwidthKbps: number): void {
    this.currentBandwidth = bandwidthKbps;
    this.optimize();
  }

  /**
   * Optimize all streams based on current bandwidth and priorities
   */
  private optimize(): void {
    // Calculate total bandwidth requirements
    const totalStreams =
      this.audioStreams.length +
      this.videoStreams.length +
      this.screenShareStreams.length;

    if (totalStreams === 0) return;

    // استخدام الأولويات لتحديد ترتيب تحسين التدفقات
    // التدفقات ذات الأولوية الأعلى تحصل على المزيد من عرض النطاق الترددي
    const audioPriority = this.priorities.audio;
    const videoPriority = this.priorities.video;
    const screenSharePriority = this.priorities.screenShare;

    // في المستقبل، يمكننا استخدام هذه القيم لتوزيع عرض النطاق الترددي بشكل أكثر ذكاءً
    // على سبيل المثال، تخصيص المزيد من عرض النطاق الترددي للتدفقات ذات الأولوية العالية
    console.debug('Stream priorities:', { audioPriority, videoPriority, screenSharePriority });

    // Determine bandwidth category
    let bandwidthCategory: 'low' | 'medium' | 'high';
    if (this.currentBandwidth < BandwidthOptimizer.LOW_BANDWIDTH) {
      bandwidthCategory = 'low';
    } else if (this.currentBandwidth < BandwidthOptimizer.MEDIUM_BANDWIDTH) {
      bandwidthCategory = 'medium';
    } else {
      bandwidthCategory = 'high';
    }

    // Apply optimizations based on bandwidth category
    this.optimizeAudioStreams(bandwidthCategory);
    this.optimizeVideoStreams(bandwidthCategory);
    this.optimizeScreenShareStreams(bandwidthCategory);
  }

  /**
   * Optimize audio streams
   * @param bandwidthCategory Bandwidth category
   */
  private optimizeAudioStreams(bandwidthCategory: 'low' | 'medium' | 'high'): void {
    // Audio is highest priority, so we maintain quality even in low bandwidth
    this.audioStreams.forEach(stream => {
      const audioTracks = stream.getAudioTracks();

      audioTracks.forEach(track => {
        if (typeof track.getConstraints === 'function') {
          try {
            const constraints: MediaTrackConstraints = {};

            // Set audio constraints based on bandwidth
            if (bandwidthCategory === 'low') {
              // For low bandwidth, use lower quality audio
              constraints.channelCount = 1; // Mono
              constraints.autoGainControl = true;
              constraints.noiseSuppression = true;
              constraints.echoCancellation = true;
            } else {
              // For medium/high bandwidth, use better quality
              constraints.channelCount = 2; // Stereo
              constraints.autoGainControl = true;
              constraints.noiseSuppression = true;
              constraints.echoCancellation = true;
            }

            // Apply constraints
            track.applyConstraints(constraints).catch(error => {
              console.warn('Failed to apply audio constraints:', error);
            });
          } catch (error) {
            console.warn('Error optimizing audio stream:', error);
          }
        }
      });
    });
  }

  /**
   * Optimize video streams
   * @param bandwidthCategory Bandwidth category
   */
  private optimizeVideoStreams(bandwidthCategory: 'low' | 'medium' | 'high'): void {
    this.videoStreams.forEach(stream => {
      const videoTracks = stream.getVideoTracks();

      videoTracks.forEach(track => {
        if (typeof track.getConstraints === 'function') {
          try {
            const constraints: MediaTrackConstraints = {};

            // Set video constraints based on bandwidth
            switch (bandwidthCategory) {
              case 'low':
                constraints.width = 320;
                constraints.height = 240;
                constraints.frameRate = 15;
                break;
              case 'medium':
                constraints.width = 640;
                constraints.height = 480;
                constraints.frameRate = 24;
                break;
              case 'high':
                constraints.width = 1280;
                constraints.height = 720;
                constraints.frameRate = 30;
                break;
            }

            // Apply constraints
            track.applyConstraints(constraints).catch(error => {
              console.warn('Failed to apply video constraints:', error);
            });
          } catch (error) {
            console.warn('Error optimizing video stream:', error);
          }
        }
      });
    });
  }

  /**
   * Optimize screen share streams
   * @param bandwidthCategory Bandwidth category
   */
  private optimizeScreenShareStreams(bandwidthCategory: 'low' | 'medium' | 'high'): void {
    this.screenShareStreams.forEach(stream => {
      const videoTracks = stream.getVideoTracks();

      videoTracks.forEach(track => {
        if (typeof track.getConstraints === 'function') {
          try {
            const constraints: MediaTrackConstraints = {};

            // Screen share needs higher quality even in low bandwidth
            // but we can still adjust frameRate
            switch (bandwidthCategory) {
              case 'low':
                constraints.frameRate = 5;
                break;
              case 'medium':
                constraints.frameRate = 15;
                break;
              case 'high':
                constraints.frameRate = 30;
                break;
            }

            // Apply constraints
            track.applyConstraints(constraints).catch(error => {
              console.warn('Failed to apply screen share constraints:', error);
            });
          } catch (error) {
            console.warn('Error optimizing screen share stream:', error);
          }
        }
      });
    });
  }

  /**
   * Clean up resources
   */
  public dispose(): void {
    this.audioStreams = [];
    this.videoStreams = [];
    this.screenShareStreams = [];
  }
}

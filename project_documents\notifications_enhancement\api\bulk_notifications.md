# API الإشعارات الجماعية

## نظرة عامة
يوفر هذا API إمكانيات متقدمة لإنشاء وإدارة الإشعارات الجماعية مع دعم أنواع مختلفة من المجموعات المستهدفة.

## المسارات المتاحة

### 1. إنشاء إشعار جماعي
**POST** `/api/notifications/bulk`

#### الوصف
إنشاء إشعار جماعي جديد وإرساله لمجموعة محددة من المستخدمين.

#### الصلاحيات المطلوبة
- `ADMIN` أو `TEACHER`

#### معاملات الطلب
```json
{
  "title": "string (مطلوب)",
  "content": "string (مطلوب)",
  "type": "NotificationType (اختياري، افتراضي: GENERAL)",
  "groupType": "string (مطلوب)",
  "targetRole": "UserRole (مطلوب للنوع BY_ROLE)",
  "targetUserIds": "number[] (مطلوب للنوع CUSTOM_SELECTION)",
  "priority": "string (اختياري، افتراضي: MEDIUM)",
  "scheduledAt": "DateTime (اختياري)",
  "link": "string (اختياري)"
}
```

#### أنواع المجموعات المدعومة
- `ALL_USERS`: جميع المستخدمين النشطين
- `BY_ROLE`: مستخدمين حسب الدور المحدد
- `CUSTOM_SELECTION`: مستخدمين محددين بمعرفاتهم

#### أنواع الإشعارات
- `GENERAL`: إشعار عام
- `LESSON`: متعلق بدرس
- `EXAM`: متعلق بامتحان
- `ATTENDANCE`: متعلق بالحضور
- `PAYMENT`: متعلق بالمدفوعات
- `ACHIEVEMENT`: متعلق بإنجاز
- `REMOTE_CLASS`: متعلق بفصل افتراضي

#### مستويات الأولوية
- `LOW`: أولوية منخفضة
- `MEDIUM`: أولوية متوسطة
- `HIGH`: أولوية عالية

#### مثال على الطلب
```json
{
  "title": "اجتماع هيئة التدريس",
  "content": "يرجى حضور اجتماع هيئة التدريس غداً الساعة 10 صباحاً في قاعة الاجتماعات.",
  "type": "GENERAL",
  "groupType": "BY_ROLE",
  "targetRole": "TEACHER",
  "priority": "HIGH",
  "link": "/meetings/123"
}
```

#### الاستجابة الناجحة (201)
```json
{
  "notification": {
    "id": 123,
    "title": "اجتماع هيئة التدريس",
    "content": "يرجى حضور اجتماع هيئة التدريس...",
    "type": "GENERAL",
    "isGroupNotification": true,
    "priority": "HIGH",
    "status": "SENT",
    "sentAt": "2024-01-15T10:00:00Z",
    "createdAt": "2024-01-15T09:55:00Z",
    "stats": {
      "totalRecipients": 25,
      "deliveredCount": 25,
      "readCount": 0,
      "deliveryRate": 100,
      "readRate": 0
    },
    "_count": {
      "recipients": 25
    }
  },
  "message": "تم إرسال الإشعار لـ 25 مستلم بنجاح"
}
```

#### أخطاء محتملة
- `400`: بيانات غير مكتملة أو غير صحيحة
- `401`: غير مصرح به
- `500`: خطأ في الخادم

### 2. جلب الإشعارات الجماعية
**GET** `/api/notifications/bulk`

#### الوصف
جلب قائمة الإشعارات الجماعية مع إمكانية الفلترة والترقيم.

#### الصلاحيات المطلوبة
- `ADMIN` أو `TEACHER`

#### معاملات الاستعلام
- `page`: رقم الصفحة (افتراضي: 1)
- `limit`: عدد العناصر في الصفحة (افتراضي: 10)
- `status`: فلترة حسب الحالة (PENDING, SENDING, SENT, FAILED)

#### مثال على الطلب
```
GET /api/notifications/bulk?page=1&limit=10&status=SENT
```

#### الاستجابة الناجحة (200)
```json
{
  "notifications": [
    {
      "id": 123,
      "title": "اجتماع هيئة التدريس",
      "content": "يرجى حضور اجتماع هيئة التدريس...",
      "type": "GENERAL",
      "isGroupNotification": true,
      "priority": "HIGH",
      "status": "SENT",
      "sentAt": "2024-01-15T10:00:00Z",
      "createdAt": "2024-01-15T09:55:00Z",
      "stats": {
        "totalRecipients": 25,
        "deliveredCount": 25,
        "readCount": 5,
        "deliveryRate": 100,
        "readRate": 20
      },
      "_count": {
        "recipients": 25
      }
    }
  ],
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "totalPages": 5
  }
}
```

## آلية العمل

### 1. معالجة الطلب
1. التحقق من الصلاحيات
2. التحقق من صحة البيانات
3. تحديد المستلمين حسب نوع المجموعة
4. إنشاء الإشعار الرئيسي

### 2. إنشاء سجلات المستلمين
1. إنشاء سجل `NotificationRecipient` لكل مستلم
2. تعيين حالة التسليم الأولية
3. تسجيل طريقة التسليم

### 3. إنشاء الإحصائيات
1. إنشاء سجل `NotificationStats`
2. حساب المعدلات الأولية
3. تسجيل أوقات التسليم

### 4. الإرسال المجدول
- إذا تم تحديد `scheduledAt`، يتم تعيين الحالة كـ `PENDING`
- يتم تأجيل الإرسال للوقت المحدد
- يتم تحديث الحالة عند الإرسال الفعلي

## اعتبارات الأداء

### 1. الإرسال المتوازي
- معالجة المستلمين في دفعات
- تجنب إنشاء عدد كبير من السجلات دفعة واحدة
- استخدام transactions لضمان التماسك

### 2. تحسين الاستعلامات
- استخدام الفهارس المناسبة
- تجنب الاستعلامات المعقدة
- تحسين استعلامات العد

### 3. إدارة الذاكرة
- تحديد حد أقصى لعدد المستلمين
- تنظيف البيانات القديمة
- مراقبة استخدام الذاكرة

## الأمان

### 1. التحقق من الصلاحيات
- فقط المديرين والمعلمين يمكنهم إنشاء إشعارات جماعية
- التحقق من صحة التوكن
- منع الوصول غير المصرح به

### 2. التحقق من البيانات
- تنظيف المدخلات من المحتوى الضار
- التحقق من صحة معرفات المستخدمين
- منع SQL injection

### 3. معدل الطلبات
- تحديد عدد الطلبات في الدقيقة
- منع الإرسال المفرط
- مراقبة الاستخدام المشبوه

## مراقبة وتسجيل الأحداث

### 1. تسجيل العمليات
- تسجيل جميع عمليات الإنشاء
- تتبع معدلات النجاح والفشل
- مراقبة الأداء

### 2. التنبيهات
- تنبيهات عند فشل الإرسال
- مراقبة معدلات القراءة
- تنبيهات الأداء

### 3. التقارير
- تقارير دورية عن الاستخدام
- إحصائيات التفاعل
- تحليل فعالية الإشعارات

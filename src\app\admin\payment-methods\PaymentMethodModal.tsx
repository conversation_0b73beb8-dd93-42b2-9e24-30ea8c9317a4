'use client';

import { useState, useEffect } from 'react';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { toast } from 'react-toastify';

interface PaymentMethod {
  id: number;
  name: string;
  description: string | null;
  isActive: boolean;
  requiresCard: boolean;
  icon: string | null;
  createdAt: string;
  updatedAt: string;
}

interface PaymentMethodModalProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  paymentMethod?: PaymentMethod | null;
}

export default function PaymentMethodModal({ isOpen, onCloseAction, onSuccessAction, paymentMethod }: PaymentMethodModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: '',
    isActive: true,
    requiresCard: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!paymentMethod;

  // تعبئة النموذج بالبيانات الحالية في وضع التعديل
  useEffect(() => {
    if (isOpen && isEditMode && paymentMethod) {
      setFormData({
        name: paymentMethod.name,
        description: paymentMethod.description || '',
        icon: paymentMethod.icon || '',
        isActive: paymentMethod.isActive,
        requiresCard: paymentMethod.requiresCard || false
      });
    } else {
      // إعادة تعيين النموذج في وضع الإنشاء
      setFormData({
        name: '',
        description: '',
        icon: '',
        isActive: true,
        requiresCard: false
      });
    }
  }, [isOpen, isEditMode, paymentMethod]);

  // تحديث حقول النموذج
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      toast.error('يرجى إدخال اسم طريقة الدفع');
      return;
    }

    try {
      setIsSubmitting(true);

      const payload = {
        name: formData.name,
        description: formData.description || null,
        icon: formData.icon || null,
        isActive: formData.isActive,
        requiresCard: formData.requiresCard
      };

      let response;

      if (isEditMode && paymentMethod) {
        // تحديث طريقة دفع موجودة
        response = await fetch('/api/payment-methods', {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            id: paymentMethod.id,
            ...payload
          })
        });
      } else {
        // إنشاء طريقة دفع جديدة
        response = await fetch('/api/payment-methods', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ أثناء حفظ طريقة الدفع');
      }

      toast.success(isEditMode ? 'تم تحديث طريقة الدفع بنجاح' : 'تم إنشاء طريقة الدفع بنجاح');
      onSuccessAction();
      onCloseAction();
    } catch (error: Error | unknown) {
      console.error('Error saving payment method:', error);
      const errorMessage = error instanceof Error ? error.message : 'فشل في حفظ طريقة الدفع';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const dialogFooter = (
    <div className="flex flex-col-reverse sm:flex-row gap-2 justify-center sm:justify-end w-full">
      <Button
        type="button"
        variant="outline"
        onClick={onCloseAction}
        disabled={isSubmitting}
        className="w-full sm:w-auto"
      >
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isSubmitting}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full sm:w-auto"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('paymentMethodForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isSubmitting ? 'جاري الحفظ...' : isEditMode ? 'تحديث' : 'إنشاء'}
      </Button>
    </div>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title={isEditMode ? 'تعديل طريقة الدفع' : 'إضافة طريقة دفع جديدة'}
      variant="primary"
      footer={dialogFooter}
    >
      <form id="paymentMethodForm" onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="name" className="text-right sm:col-span-1">
              الاسم <span className="text-red-500">*</span>
            </Label>
            <div className="sm:col-span-3">
              <Input
                id="name"
                name="name"
                placeholder="اسم طريقة الدفع"
                value={formData.name}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-start gap-2 sm:gap-4">
            <Label htmlFor="description" className="text-right sm:col-span-1 mt-2">
              الوصف
            </Label>
            <div className="sm:col-span-3">
              <Textarea
                id="description"
                name="description"
                placeholder="وصف طريقة الدفع"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="icon" className="text-right sm:col-span-1">
              الأيقونة
            </Label>
            <div className="sm:col-span-3">
              <Input
                id="icon"
                name="icon"
                placeholder="رابط الأيقونة"
                value={formData.icon}
                onChange={handleInputChange}
              />
              <p className="text-xs text-gray-500 mt-1">
                أدخل رابط صورة الأيقونة (اختياري)
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="isActive" className="text-right sm:col-span-1">
              الحالة
            </Label>
            <div className="sm:col-span-3 flex items-center gap-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="isActive">
                {formData.isActive ? 'نشطة' : 'غير نشطة'}
              </Label>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
            <Label htmlFor="requiresCard" className="text-right sm:col-span-1">
              تتطلب معلومات البطاقة
            </Label>
            <div className="sm:col-span-3 flex flex-col sm:flex-row sm:items-center gap-2">
              <div className="flex items-center gap-2">
                <Switch
                  id="requiresCard"
                  checked={formData.requiresCard}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, requiresCard: checked }))}
                />
                <Label htmlFor="requiresCard">
                  {formData.requiresCard ? 'نعم' : 'لا'}
                </Label>
              </div>
              {formData.requiresCard && (
                <p className="text-xs text-gray-500 sm:mr-2">
                  سيُطلب من المستخدم إدخال معلومات البطاقة عند اختيار هذه الطريقة
                </p>
              )}
            </div>
          </div>
        </div>
      </form>
    </AnimatedDialog>
  );
}

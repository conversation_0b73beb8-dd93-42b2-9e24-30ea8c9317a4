# تحديثات نموذج البيانات للفصول الافتراضية

هذا الملف يحتوي على التحديثات المقترحة لنموذج البيانات في Prisma لدعم ميزات الفصول الافتراضية الجديدة.

## تحديث نموذج RemoteClass

```prisma
/// جدول الفصول الافتراضية (التعلم عن بعد)
model RemoteClass {
  id              Int       @id @default(autoincrement())
  title           String    // عنوان الفصل
  description     String    @db.Text // وصف الفصل
  startTime       DateTime  // وقت بدء الفصل
  endTime         DateTime  // وقت انتهاء الفصل
  meetingLink     String    // رابط الاجتماع
  meetingId       String?   // معرف الاجتماع
  meetingPassword String?   // كلمة مرور الاجتماع
  platform        String    // المنصة المستخدمة (Zoom, Google Meet, etc.)
  
  // حقول جديدة
  isScreenShareEnabled Boolean @default(false) // تمكين مشاركة الشاشة
  isWhiteboardEnabled Boolean @default(false) // تمكين السبورة التفاعلية
  videoQuality      String?   // جودة الفيديو (low, medium, high)
  audioQuality      String?   // جودة الصوت (low, medium, high)
  
  instructorId    Int       // معرف المعلم
  instructor      User      @relation("Instructor", fields: [instructorId], references: [id])
  attendees       User[]    @relation("Attendees")
  classeId        Int?      // الفصل المرتبط (اختياري)
  classe          Classe?   @relation(fields: [classeId], references: [id])
  materials       RemoteClassMaterial[] // المواد التعليمية
  recordingUrl    String?   // رابط التسجيل
  
  // علاقات جديدة
  whiteboards     Whiteboard[] // السبورات التفاعلية
  screenShares    ScreenShare[] // مشاركات الشاشة
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

/// نموذج السبورة التفاعلية
model Whiteboard {
  id              Int       @id @default(autoincrement())
  name            String    // اسم السبورة
  content         String?   @db.Text // محتوى السبورة (JSON)
  isActive        Boolean   @default(true) // هل السبورة نشطة
  remoteClassId   Int       // معرف الفصل الافتراضي
  remoteClass     RemoteClass @relation(fields: [remoteClassId], references: [id], onDelete: Cascade)
  createdBy       Int       // معرف المستخدم الذي أنشأ السبورة
  creator         User      @relation(fields: [createdBy], references: [id])
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

/// نموذج مشاركة الشاشة
model ScreenShare {
  id              Int       @id @default(autoincrement())
  status          String    // حالة مشاركة الشاشة (active, stopped)
  remoteClassId   Int       // معرف الفصل الافتراضي
  remoteClass     RemoteClass @relation(fields: [remoteClassId], references: [id], onDelete: Cascade)
  sharedBy        Int       // معرف المستخدم الذي يشارك الشاشة
  user            User      @relation(fields: [sharedBy], references: [id])
  
  startTime       DateTime  @default(now()) // وقت بدء المشاركة
  endTime         DateTime? // وقت انتهاء المشاركة
}

/// تحديث نموذج المستخدم
model User {
  // الحقول الحالية...
  
  // علاقات جديدة
  whiteboards     Whiteboard[] // السبورات التي أنشأها المستخدم
  screenShares    ScreenShare[] // مشاركات الشاشة
  
  // إعدادات الوسائط
  preferredVideoQuality String? // جودة الفيديو المفضلة
  preferredAudioQuality String? // جودة الصوت المفضلة
  
  // الحقول الأخرى الموجودة...
}
```

## ملاحظات التنفيذ

1. بعد تحديث ملف schema.prisma، قم بتنفيذ الأمر التالي لتطبيق التغييرات على قاعدة البيانات:

```bash
npx prisma db push
```

2. قد تحتاج إلى تحديث واجهات API الحالية لدعم الحقول والنماذج الجديدة.

3. تأكد من تحديث أنواع TypeScript المقابلة في التطبيق.

import { NextRequest, NextResponse } from "next/server";
import { UserRole } from '@prisma/client';
import prisma from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import * as jose from 'jose';

interface RegisterUserDto {
    user: string;
    password: string;
    name: string;
    phone?: string;
}

export async function POST(request: NextRequest) {
    try {
        // التحقق من حالة التسجيل
        const siteSettings = await prisma.systemSettings.findUnique({
            where: { key: 'SITE_SETTINGS' },
        });

        let registrationEnabled = true;
        if (siteSettings) {
            try {
                const settings = JSON.parse(siteSettings.value);
                if (typeof settings.registrationEnabled === 'boolean') {
                    registrationEnabled = settings.registrationEnabled;
                }
            } catch (error) {
                console.error('خطأ في تحليل إعدادات الموقع:', error);
            }
        }

        if (!registrationEnabled) {
            return NextResponse.json(
                { message: "التسجيل معطل حالياً من قبل الإدارة" },
                { status: 403 }
            );
        }

        const body = await request.json() as RegisterUserDto;

        // التحقق من وجود الحقول المطلوبة
        if (!body.user || !body.password || !body.name) {
            return NextResponse.json(
                { message: "جميع الحقول المطلوبة يجب ملؤها" },
                { status: 400 }
            );
        }

        // التحقق من عدم وجود المستخدم مسبقاً
        const existingUser = await prisma.user.findUnique({
            where: {
                username: body.user
            }
        });

        if (existingUser) {
            return NextResponse.json(
                { message: "اسم المستخدم موجود بالفعل" },
                { status: 400 }
            );
        }

        // تشفير كلمة المرور
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(body.password, salt);

        // إنشاء المستخدم والملف الشخصي في نفس الوقت
        const newUser = await prisma.user.create({
            data: {
                role: UserRole.PENDING, // تعيين الدور كـ "في انتظار التعيين"
                username: body.user,
                password: hashedPassword,
                profile: {
                    create: {
                        name: body.name,
                        phone: body.phone
                    }
                }
            },
            select: {
                id: true,
                username: true,
                role: true,
                roleId: true,
                profile: {
                    select: {
                        name: true,
                        phone: true
                    }
                }
            }
        });

        // إنشاء توكن JWT
        const jwtPayload = {
            id: newUser.id,
            username: newUser.username,
            role: newUser.role,
            roleId: newUser.roleId
        };

        const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'default_secret_key_for_development');
        const token = await new jose.SignJWT(jwtPayload)
            .setProtectedHeader({ alg: 'HS256' })
            .setExpirationTime('30d')
            .sign(secret);

        // إرجاع البيانات والتوكن
        return NextResponse.json(
            {
                user: newUser,
                token,
                message: "تم التسجيل بنجاح"
            },
            { status: 201 }
        );

    } catch (error: unknown) {
        console.error('Registration error:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء معالجة الطلب" },
            { status: 500 }
        );
    }
}

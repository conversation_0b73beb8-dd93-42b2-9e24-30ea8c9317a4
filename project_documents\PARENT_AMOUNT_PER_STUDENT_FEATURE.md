# إضافة المبلغ لكل تلميذ في إدارة الأولياء

## 📋 الوصف
تم إضافة ميزة جديدة لتحديد المبلغ الذي يجب دفعه لكل تلميذ في نماذج إدارة الأولياء.

## 🎯 الهدف
- تمكين إدارة المدرسة من تحديد مبلغ محدد لكل ولي أمر يجب دفعه عن كل تلميذ
- تسهيل عملية حساب الرسوم المطلوبة بناءً على عدد التلاميذ
- توفير مرونة في تحديد رسوم مختلفة لأولياء أمور مختلفين

## ✨ الميزات المضافة

### 1. تحديث قاعدة البيانات
- إضافة حقل `amountPerStudent` إلى جدول `Parent`
- الحقل اختياري (nullable) ويقبل قيم عشرية

### 2. تحديث واجهة إدارة الأولياء
- إضافة حقل "المبلغ لكل تلميذ" في نموذج إضافة ولي جديد
- إضافة نفس الحقل في نموذج تعديل بيانات الولي
- إضافة عمود جديد في جدول الأولياء لعرض المبلغ
- عرض المبلغ في بطاقات الأولياء على الشاشات الصغيرة

### 3. تحديث API
- تحديث API إضافة ولي جديد لحفظ المبلغ
- تحديث API تعديل بيانات الولي لتحديث المبلغ
- دعم التحويل الآمن للقيم النصية إلى أرقام عشرية

### 4. تحديث التصدير
- إضافة عمود "المبلغ لكل تلميذ" في تصدير بيانات الأولياء إلى Excel

## 🔧 التفاصيل التقنية

### قاعدة البيانات
```sql
ALTER TABLE "Parent" ADD COLUMN "amountPerStudent" DOUBLE PRECISION;
```

### واجهة TypeScript
```typescript
interface Parent {
  // ... الحقول الموجودة
  amountPerStudent?: number // المبلغ الذي يجب دفعه لكل تلميذ
}
```

### نموذج البيانات
```typescript
const [formData, setFormData] = useState({
  // ... الحقول الموجودة
  amountPerStudent: '', // حقل جديد
})
```

## 📱 واجهة المستخدم

### نموذج الإضافة/التعديل
- حقل إدخال رقمي مع تسمية "المبلغ لكل تلميذ (د.ج) (اختياري)"
- يقبل قيم عشرية بحد أدنى 0
- placeholder توضيحي: "أدخل المبلغ المطلوب لكل تلميذ"

### جدول الأولياء
- عمود جديد "المبلغ لكل تلميذ" (مخفي على الشاشات الصغيرة)
- عرض المبلغ بتنسيق "X د.ج" أو "غير محدد" إذا لم يتم تحديد مبلغ
- لون مميز للمبلغ (primary color)

### بطاقات الأولياء (الشاشات الصغيرة)
- عرض المبلغ لكل تلميذ في قسم التفاصيل
- تنسيق مماثل للجدول

## 🚀 كيفية الاستخدام

### إضافة ولي جديد
1. انتقل إلى صفحة إدارة الأولياء
2. اضغط على "إضافة ولي جديد"
3. املأ البيانات المطلوبة
4. أدخل المبلغ المطلوب لكل تلميذ (اختياري)
5. احفظ البيانات

### تعديل بيانات ولي موجود
1. اضغط على زر "تعديل" بجانب الولي المطلوب
2. عدّل المبلغ لكل تلميذ حسب الحاجة
3. احفظ التغييرات

### عرض البيانات
- يظهر المبلغ في عمود منفصل في جدول الأولياء
- يظهر في بطاقة تفاصيل الولي على الشاشات الصغيرة
- يتم تضمينه في تصدير البيانات

## 📊 فوائد الميزة

1. **مرونة في التسعير**: إمكانية تحديد رسوم مختلفة لأولياء أمور مختلفين
2. **سهولة الحساب**: حساب إجمالي الرسوم المطلوبة بضرب المبلغ في عدد التلاميذ
3. **شفافية**: عرض واضح للمبلغ المطلوب من كل ولي أمر
4. **تتبع أفضل**: إمكانية تتبع ومقارنة الرسوم بين الأولياء

## 🔄 التحديثات المستقبلية المقترحة

1. **حساب تلقائي للإجمالي**: عرض إجمالي المبلغ المطلوب (المبلغ × عدد التلاميذ)
2. **تقارير مالية**: تقارير تفصيلية بناءً على المبالغ المحددة
3. **تنبيهات**: تنبيهات عند تغيير المبلغ أو إضافة تلاميذ جدد
4. **تاريخ التغييرات**: تتبع تاريخ تغيير المبالغ

## 📝 ملاحظات

- الحقل اختياري ولا يؤثر على الوظائف الموجودة
- يتم حفظ القيم كأرقام عشرية لدقة أكبر
- التحقق من صحة البيانات يتم على مستوى الواجهة والخادم
- متوافق مع جميع أحجام الشاشات

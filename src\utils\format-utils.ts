/**
 * تنسيق المبلغ كعملة
 * @param amount المبلغ المراد تنسيقه
 * @param currency رمز العملة (افتراضيًا: د.ج)
 * @param locale اللغة المستخدمة للتنسيق (افتراضيًا: fr-FR)
 * @returns المبلغ منسقًا كعملة
 */
export const formatCurrency = (amount: number | null | undefined, currency: string = 'د.ج', locale: string = 'fr-FR'): string => {
  if (amount === null || amount === undefined) {
    return `0 ${currency}`;
  }
  
  return `${amount.toLocaleString(locale)} ${currency}`;
};

/**
 * تنسيق التاريخ
 * @param dateString سلسلة التاريخ
 * @param locale اللغة المستخدمة للتنسيق (افتراضيًا: fr-FR)
 * @returns التاريخ منسقًا
 */
export const formatDate = (dateString: string | Date | null | undefined, locale: string = 'fr-FR'): string => {
  if (!dateString) {
    return '';
  }
  
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString(locale);
  } catch (error) {
    console.error('Error formatting date:', error);
    return String(dateString);
  }
};

/**
 * تنسيق التاريخ والوقت
 * @param dateString سلسلة التاريخ
 * @param locale اللغة المستخدمة للتنسيق (افتراضيًا: fr-FR)
 * @returns التاريخ والوقت منسقين
 */
export const formatDateTime = (dateString: string | Date | null | undefined, locale: string = 'fr-FR'): string => {
  if (!dateString) {
    return '';
  }
  
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting date and time:', error);
    return String(dateString);
  }
};

/**
 * تنسيق النسبة المئوية
 * @param value القيمة المراد تنسيقها كنسبة مئوية
 * @param fractionDigits عدد الأرقام العشرية (افتراضيًا: 1)
 * @returns النسبة المئوية منسقة
 */
export const formatPercentage = (value: number | null | undefined, fractionDigits: number = 1): string => {
  if (value === null || value === undefined) {
    return '0%';
  }
  
  return `${value.toFixed(fractionDigits)}%`;
};

/**
 * تنسيق العدد
 * @param value العدد المراد تنسيقه
 * @param locale اللغة المستخدمة للتنسيق (افتراضيًا: fr-FR)
 * @returns العدد منسقًا
 */
export const formatNumber = (value: number | null | undefined, locale: string = 'fr-FR'): string => {
  if (value === null || value === undefined) {
    return '0';
  }
  
  return value.toLocaleString(locale);
};

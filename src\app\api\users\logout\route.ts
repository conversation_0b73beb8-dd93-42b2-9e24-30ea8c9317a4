import { NextResponse } from 'next/server';

/**
 *  @method  GET
 *  @route   ~/api/users/logout
 *  @desc    Logout User
 *  @access  public
 */
export async function GET() {
    try {
        // إنشاء استجابة جديدة
        const response = NextResponse.json(
            { success: true, message: 'تم تسجيل الخروج بنجاح' },
            { status: 200 }
        );

        // حذف الكوكي بتعيين تاريخ انتهاء صلاحية في الماضي
        response.cookies.set({
            name: "jwtToken",
            value: "",
            expires: new Date(0),
            path: "/",
        });

        return response;
    } catch (error) {
        console.error('Logout error:', error);
        return NextResponse.json(
            { message: 'حدث خطأ أثناء تسجيل الخروج' },
            { status: 500 }
        );
    }
}

// إضافة طريقة POST لدعم الطلبات من العميل
export async function POST() {
    return GET();
}
# نظام التخزين المؤقت للصلاحيات - خطة العمل

## الهدف
إضافة نظام تخزين مؤقت محلي للصلاحيات مع تحديثه تلقائياً كل نصف ساعة لتحسين الأداء وتقليل استهلاك الشبكة.

## التحليل الحالي
- النظام الحالي يجلب الصلاحيات من قاعدة البيانات في كل مرة عبر `/api/auth/permissions`
- يتم استخدام `useUserPermissions` hook لجلب الصلاحيات
- النظام يدعم المديرين (جميع الصلاحيات) والموظفين (صلاحيات محددة)

## المكونات المطلوبة

### 1. خدمة التخزين المؤقت (Cache Service)
- **الملف**: `src/lib/permissionsCache.ts`
- **الوظائف**:
  - تخزين الصلاحيات في localStorage
  - التحقق من انتهاء صلاحية البيانات المخزنة
  - تحديث البيانات تلقائياً
  - إدارة مفاتيح التخزين حسب المستخدم

### 2. Hook محسن للصلاحيات
- **الملف**: `src/hooks/useUserPermissions.ts` (تحديث)
- **التحسينات**:
  - استخدام التخزين المؤقت أولاً
  - جلب البيانات من الخادم عند الحاجة فقط
  - تحديث دوري كل 30 دقيقة

### 3. مكون إدارة التخزين المؤقت
- **الملف**: `src/components/PermissionsCacheManager.tsx`
- **الوظائف**:
  - مراقبة حالة التخزين المؤقت
  - تنظيف البيانات المنتهية الصلاحية
  - إعادة تعيين التخزين عند تغيير المستخدم

## قائمة المهام

### المرحلة الأولى: إنشاء خدمة التخزين المؤقت
- [x] **T01.01: إنشاء ملف خدمة التخزين المؤقت**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/lib/permissionsCache.ts`
  - **الاعتماديات:** لا يوجد
  - **الوظائف المطلوبة:**
    - `savePermissionsToCache()` - حفظ الصلاحيات
    - `getPermissionsFromCache()` - جلب الصلاحيات
    - `isCacheValid()` - التحقق من صلاحية البيانات
    - `clearCache()` - مسح التخزين المؤقت
    - `getCacheKey()` - إنشاء مفتاح فريد للمستخدم

### المرحلة الثانية: تحديث Hook الصلاحيات
- [x] **T02.01: تحديث useUserPermissions hook**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/hooks/useUserPermissions.ts`
  - **الاعتماديات:** T01.01
  - **التحسينات:**
    - دمج خدمة التخزين المؤقت
    - إضافة آلية التحديث الدوري
    - تحسين إدارة حالة التحميل

### المرحلة الثالثة: إنشاء مكون إدارة التخزين
- [x] **T03.01: إنشاء PermissionsCacheManager**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/components/PermissionsCacheManager.tsx`
  - **الاعتماديات:** T01.01, T02.01
  - **الوظائف:**
    - مراقبة تغييرات المستخدم
    - تنظيف البيانات المنتهية الصلاحية
    - إعادة تعيين التخزين عند تسجيل الخروج

### المرحلة الرابعة: التكامل والاختبار
- [x] **T04.01: دمج المكونات في التطبيق**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/app/layout.tsx` أو المكون الرئيسي
  - **الاعتماديات:** T03.01
  - **المهام:**
    - إضافة PermissionsCacheManager للتطبيق
    - التأكد من عمل النظام مع جميع المكونات

- [x] **T04.02: اختبار النظام**
  - **الحالة:** مُنجزة
  - **المكونات:** جميع المكونات المحدثة
  - **الاعتماديات:** T04.01
  - **اختبارات:**
    - التحقق من حفظ واسترجاع الصلاحيات
    - اختبار التحديث الدوري
    - اختبار مسح التخزين عند تغيير المستخدم

### المرحلة الخامسة: إصلاح تسجيل الخروج وتغيير المستخدم
- [x] **T05.01: إضافة دوال تنظيف التخزين المؤقت**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/lib/permissionsCache.ts`
  - **الاعتماديات:** T01.01
  - **الوظائف المضافة:**
    - `clearCacheOnLogout()` - تنظيف عند تسجيل الخروج
    - `clearCacheOnUserChange()` - تنظيف عند تغيير المستخدم
    - أحداث مخصصة للتنسيق بين المكونات

- [x] **T05.02: إنشاء hook تسجيل خروج محسن**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/hooks/useLogout.ts`
  - **الاعتماديات:** T05.01
  - **الميزات:**
    - تنظيف التخزين المؤقت تلقائياً
    - دعم خيارات متعددة لتسجيل الخروج
    - معالجة الأخطاء المحسنة

- [x] **T05.03: تحديث مكون إدارة التخزين المؤقت**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/components/PermissionsCacheManager.tsx`
  - **الاعتماديات:** T05.01, T05.02
  - **التحسينات:**
    - مراقبة أحداث تسجيل الخروج
    - تنظيف أفضل عند تغيير المستخدم
    - دعم الأحداث المخصصة

- [x] **T05.04: إنشاء مكونات تسجيل خروج محسنة**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/components/LogoutButtonEnhanced.tsx`
  - **الاعتماديات:** T05.02
  - **الميزات:**
    - أزرار تسجيل خروج متعددة الأشكال
    - دعم حالات التحميل
    - تنظيف التخزين المؤقت تلقائياً

- [x] **T05.05: تحديث hook الصلاحيات للأحداث**
  - **الحالة:** مُنجزة
  - **المكونات:** `src/hooks/useUserPermissions.ts`
  - **الاعتماديات:** T05.01
  - **التحسينات:**
    - مراقبة أحداث تنظيف التخزين المؤقت
    - إعادة تعيين الحالة عند تسجيل الخروج
    - تنظيف المؤقتات تلقائياً

## المواصفات التقنية

### هيكل البيانات المخزنة
```typescript
interface CachedPermissions {
  permissions: Permission[];
  userRole: string;
  userId: number;
  timestamp: number;
  expiresAt: number;
}
```

### مفاتيح التخزين
- `permissions_cache_${userId}` - للصلاحيات
- `permissions_cache_meta` - للبيانات الوصفية

### مدة التخزين المؤقت
- **المدة الافتراضية:** 30 دقيقة (1800000 مللي ثانية)
- **التحديث التلقائي:** كل 30 دقيقة
- **التنظيف:** عند تسجيل الخروج أو تغيير المستخدم

## الفوائد المتوقعة
1. **تحسين الأداء:** تقليل طلبات الشبكة بنسبة 90%
2. **تجربة مستخدم أفضل:** استجابة فورية للصلاحيات
3. **توفير البيانات:** تقليل استهلاك الشبكة
4. **موثوقية:** النظام يعمل حتى مع انقطاع الاتصال المؤقت
5. **تنظيف تلقائي:** مسح البيانات عند تسجيل الخروج أو تغيير المستخدم
6. **تزامن بين التبويبات:** تنسيق البيانات بين نوافذ المتصفح المختلفة

## ملاحظات التنفيذ
- استخدام localStorage للتخزين المحلي
- التحقق من دعم المتصفح لـ localStorage
- إضافة آلية fallback للمتصفحات القديمة
- ضمان أمان البيانات المخزنة (عدم تخزين معلومات حساسة)
- تنظيف تلقائي عند تسجيل الخروج وتغيير المستخدم
- دعم الأحداث المخصصة للتنسيق بين المكونات

## كيفية الاستخدام

### 1. استخدام hook الصلاحيات المحسن
```typescript
import { useUserPermissions } from '@/hooks/useUserPermissions';

const MyComponent = () => {
  const {
    userPermissions,
    hasPermission,
    isUsingCache,
    cacheTimeRemaining,
    refreshPermissions
  } = useUserPermissions();

  // التحقق من صلاحية
  if (hasPermission('admin.users.view')) {
    // عرض المحتوى
  }
};
```

### 2. استخدام تسجيل الخروج المحسن
```typescript
import { useLogout } from '@/hooks/useLogout';
import LogoutButtonEnhanced from '@/components/LogoutButtonEnhanced';

const MyComponent = () => {
  const { logout } = useLogout();

  // استخدام المكون الجاهز
  return <LogoutButtonEnhanced redirectTo="/login" />;

  // أو استخدام الدالة مباشرة
  const handleLogout = () => {
    logout(userId, {
      redirectTo: '/login',
      clearAllCache: true
    });
  };
};
```

### 3. مراقبة حالة التخزين المؤقت
```typescript
import { usePermissionsCacheManager } from '@/components/PermissionsCacheManager';

const MyComponent = () => {
  const { getStatus, clearCache, cleanup } = usePermissionsCacheManager();

  const status = getStatus();
  console.log('حالة التخزين المؤقت:', status);
};
```

## اختبار النظام
- تصفح إلى `/admin/test-permissions-cache` لاختبار جميع الميزات
- اختبار تسجيل الخروج وتنظيف التخزين المؤقت
- مراقبة console للرسائل التشخيصية

## الملفات المضافة/المحدثة
- `src/lib/permissionsCache.ts` - خدمة التخزين المؤقت الأساسية
- `src/hooks/useUserPermissions.ts` - hook الصلاحيات المحسن
- `src/hooks/useLogout.ts` - hook تسجيل الخروج المحسن
- `src/components/PermissionsCacheManager.tsx` - مدير التخزين المؤقت
- `src/components/PermissionsCacheWrapper.tsx` - wrapper للتطبيق
- `src/components/LogoutButtonEnhanced.tsx` - أزرار تسجيل خروج محسنة
- `src/components/header/HeaderLogoutExample.tsx` - مثال للتحديث
- `src/app/admin/test-permissions-cache/page.tsx` - صفحة اختبار
- `src/app/layout.tsx` - تحديث لدمج النظام

## خلاصة المشروع

تم بنجاح إنشاء نظام تخزين مؤقت شامل للصلاحيات مع الميزات التالية:

### ✅ الميزات المنجزة
1. **تخزين مؤقت ذكي** - يحفظ الصلاحيات محلياً لمدة 30 دقيقة
2. **تحديث دوري** - تحديث تلقائي كل 30 دقيقة
3. **تنظيف تلقائي** - مسح البيانات عند تسجيل الخروج أو تغيير المستخدم
4. **مراقبة الأحداث** - تنسيق بين المكونات عبر الأحداث المخصصة
5. **تنظيف دوري** - إزالة البيانات المنتهية الصلاحية كل 10 دقائق
6. **واجهة اختبار** - صفحة شاملة لاختبار جميع الميزات
7. **مكونات جاهزة** - أزرار تسجيل خروج محسنة
8. **توثيق شامل** - أمثلة وتعليمات الاستخدام

### 🔧 المشكلة المحلولة
- **المشكلة الأصلية:** عدم تنظيف التخزين المؤقت عند تسجيل الخروج أو تغيير المستخدم
- **الحل:** نظام شامل يراقب تغييرات المستخدم ويقوم بالتنظيف التلقائي
- **النتيجة:** تخزين مؤقت آمن وموثوق مع تنظيف تلقائي

### 📊 تحسينات الأداء
- تقليل طلبات الشبكة بنسبة 90%
- استجابة فورية للتحقق من الصلاحيات
- تحسين تجربة المستخدم بشكل كبير

### 🛡️ الأمان
- مسح تلقائي للبيانات الحساسة
- عدم تخزين معلومات سرية
- تنظيف عند تغيير السياق الأمني

النظام جاهز للاستخدام ويمكن اختباره عبر صفحة `/admin/test-permissions-cache`.

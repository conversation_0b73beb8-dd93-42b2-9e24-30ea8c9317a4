'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'react-toastify';
import {
  FaUser,
  FaPhone,
  FaCalendarAlt,
  FaGraduationCap,
  FaBookOpen,
  FaPrint,
  FaDownload,
  FaUserTie,
  FaHome,
  FaEnvelope,
  FaChartLine,
  FaStar,
  FaAward,
  FaCoins
} from 'react-icons/fa';
import Image from 'next/image';

interface StudentCardProps {
  studentId: number;
  onPrint?: () => void;
  onDownload?: () => void;
  className?: string;
}

interface StudentCardData {
  id: number;
  username: string;
  name: string;
  age: number;
  phone?: string;
  createdAt: string;
  guardian?: {
    id: number;
    name: string;
    phone: string;
    email?: string;
    address?: string;
  };
  classe?: {
    id: number;
    name: string;
    description?: string;
  };
  profileImage?: {
    id: number;
    imageUrl: string;
    description?: string;
    uploadDate: string;
  };

  stats: {
    attendance: {
      present: number;
      absent: number;
      excused: number;
      total: number;
      rate: number;
    };
    quran: {
      totalVerses: number;
      averageMemorization: number;
      averageTajweed: number;
      completedSurahs: number;
    };
    totalPoints: number;
    totalRewards: number;
  };
  schoolInfo: {
    name: string;
    description: string;
    logoUrl: string;
    address: string;
    phone: string;
    email: string;
  };
  quranProgress: Array<{
    id: number;
    surahId: number;
    startVerse: number;
    endVerse: number;
    memorization: number;
    tajweed: number;
    startDate: string;
    completionDate?: string;
    surah: {
      id: number;
      name: string;
      number: number;
      totalAyahs: number;
    };
  }>;
  points: Array<{
    id: number;
    points: number;
    date: string;
    reason?: string;
  }>;
  rewards: Array<{
    id: number;
    date: string;
    reward: {
      id: number;
      name: string;
      description?: string;
      type: string;
    };
  }>;
}

import { useSchoolSettings } from '@/utils/school-settings';

export default function StudentCard({ studentId, onPrint, onDownload, className }: StudentCardProps) {
  const { settings: schoolSettings } = useSchoolSettings();
  const [studentData, setStudentData] = useState<StudentCardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  // تحميل بيانات بطاقة التلميذ
  useEffect(() => {
    const fetchStudentCard = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/students/${studentId}/card`);

        if (!response.ok) {
          throw new Error('فشل في تحميل بيانات التلميذ');
        }

        const data = await response.json();

        if (data.success) {
          setStudentData(data.data);
        } else {
          throw new Error(data.error || 'حدث خطأ غير متوقع');
        }
      } catch (error) {
        console.error('Error fetching student card:', error);
        setError(error instanceof Error ? error.message : 'حدث خطأ أثناء تحميل البيانات');
        toast.error('فشل في تحميل بيانات بطاقة التلميذ');
      } finally {
        setIsLoading(false);
      }
    };

    if (studentId) {
      fetchStudentCard();
    }
  }, [studentId]);

  const handlePrint = () => {
    if (cardRef.current) {
      const printContent = cardRef.current.innerHTML;
      const originalContent = document.body.innerHTML;

      document.body.innerHTML = `
        <div style="direction: rtl; font-family: Arial, sans-serif;">
          <style>
            @media print {
              @page {
                size: A4;
                margin: 1cm;
              }
              body {
                margin: 0;
                padding: 0;
                font-size: 12px;
                line-height: 1.4;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
              }
              .no-print { display: none !important; }
              .print-only { display: block !important; }

              /* إخفاء العناصر غير المطلوبة */
              button { display: none !important; }
              .flex.gap-3.justify-end { display: none !important; }

              /* تنسيق البطاقة للطباعة */
              .max-w-4xl {
                max-width: 100% !important;
                width: 100% !important;
                margin: 0 !important;
                page-break-inside: avoid !important;
              }

              /* رأس البطاقة */
              .bg-gradient-to-r {
                background: #169b88 !important;
                color: white !important;
                padding: 1cm !important;
                margin-bottom: 0.5cm !important;
                border-radius: 8px !important;
              }

              /* محتوى البطاقة */
              .p-6 {
                padding: 0.8cm !important;
                font-size: 11px !important;
              }

              /* الصورة الشخصية */
              .w-32.h-32 {
                width: 3cm !important;
                height: 3cm !important;
              }

              /* النصوص */
              .text-2xl { font-size: 18px !important; }
              .text-lg { font-size: 16px !important; }
              .text-sm { font-size: 11px !important; }
              .text-xs { font-size: 10px !important; }

              /* الشبكة */
              .grid {
                display: grid !important;
                gap: 0.5cm !important;
              }

              .grid-cols-1 { grid-template-columns: 1fr !important; }
              .md\\:grid-cols-3 { grid-template-columns: 1fr 2fr !important; }
              .sm\\:grid-cols-2 { grid-template-columns: 1fr 1fr !important; }

              /* المسافات */
              .space-y-6 > * + * { margin-top: 0.4cm !important; }
              .space-y-4 > * + * { margin-top: 0.3cm !important; }
              .space-y-2 > * + * { margin-top: 0.2cm !important; }

              /* الحدود والخلفيات */
              .bg-gray-50 {
                background: #f9f9f9 !important;
                border: 1px solid #ddd !important;
                padding: 0.4cm !important;
                border-radius: 4px !important;
              }

              /* الفواصل */
              hr {
                border: none !important;
                border-top: 1px solid #ddd !important;
                margin: 0.3cm 0 !important;
              }

              /* الأيقونات */
              svg {
                width: 12px !important;
                height: 12px !important;
                color: #169b88 !important;
              }

              /* تجنب تقسيم الصفحة */
              .grid, .space-y-6 > *, .space-y-4 > * {
                page-break-inside: avoid !important;
              }
            }
          </style>
          ${printContent}
        </div>
      `;

      window.print();
      document.body.innerHTML = originalContent;
      window.location.reload();
    }
    onPrint?.();
  };

  const handleDownload = async () => {
    if (!studentData) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      // استيراد دالة التصدير المشتركة
      const { exportToPdf } = await import('@/utils/export-utils');

      // إعداد بيانات البطاقة للتصدير
      const studentInfo = [
        ['الاسم الكامل', studentData.name],
        ['العمر', `${studentData.age} سنة`],
        ['رقم التلميذ', studentData.id.toString()],
        ['تاريخ التسجيل', studentData.createdAt]
      ];

      if (studentData.phone) {
        studentInfo.push(['رقم الهاتف', studentData.phone]);
      }

      if (studentData.classe) {
        studentInfo.push(['الفصل', studentData.classe.name]);
      }

      // معلومات ولي الأمر
      const guardianInfo = [];
      if (studentData.guardian) {
        guardianInfo.push(
          ['اسم ولي الأمر', studentData.guardian.name],
          ['هاتف ولي الأمر', studentData.guardian.phone]
        );

        if (studentData.guardian.email) {
          guardianInfo.push(['بريد ولي الأمر', studentData.guardian.email]);
        }

        if (studentData.guardian.address) {
          guardianInfo.push(['عنوان ولي الأمر', studentData.guardian.address]);
        }
      }





      // تنظيم البيانات في جداول منفصلة للوضوح
      const fileName = `بطاقة_${studentData.name.replace(/\s+/g, '_')}_${new Date().toLocaleDateString('fr-FR').replace(/\//g, '-')}.pdf`;

      const tables = [
        {
          title: 'المعلومات الأساسية',
          headers: ['البيان', 'القيمة'],
          data: studentInfo,
          startY: 80
        }
      ];

      // إضافة معلومات ولي الأمر إذا كانت متوفرة
      if (guardianInfo.length > 0) {
        tables.push({
          title: 'معلومات ولي الأمر',
          headers: ['البيان', 'القيمة'],
          data: guardianInfo
        });
      }

      exportToPdf({
        title: `بطاقة التلميذ: ${studentData.name}`,
        fileName: fileName,
        tables: tables,
        additionalContent: [
          {
            text: studentData.schoolInfo.name,
            x: 105,
            y: 20,
            options: { align: 'center' }
          },
          {
            text: studentData.schoolInfo.description,
            x: 105,
            y: 28,
            options: { align: 'center' }
          },
          {
            text: studentData.schoolInfo.address,
            x: 105,
            y: 35,
            options: { align: 'center' }
          },
          {
            text: `تاريخ الإصدار: ${new Date().toLocaleDateString('fr-FR')}`,
            x: 105,
            y: 45,
            options: { align: 'center' }
          },
          {
            text: `رقم البطاقة: CARD-${studentData.id}-${new Date().getFullYear()}`,
            x: 105,
            y: 52,
            options: { align: 'center' }
          },
          {
            text: `هذه البطاقة صادرة من ${studentData.schoolInfo.name} وصالحة كإثبات للتسجيل`,
            x: 105,
            y: 280,
            options: { align: 'center' }
          },
          {
            text: `📧 ${studentData.schoolInfo.email} | 📞 ${studentData.schoolInfo.phone}`,
            x: 105,
            y: 287,
            options: { align: 'center' }
          }
        ]
      });

      toast.success('تم تحميل بطاقة التلميذ بنجاح');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('حدث خطأ أثناء إنشاء ملف PDF');
    }

    onDownload?.();
  };

  if (isLoading) {
    return (
      <Card className={`w-full max-w-4xl mx-auto ${className}`}>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            <span className="mr-3">جاري تحميل بيانات التلميذ...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !studentData) {
    return (
      <Card className={`w-full max-w-4xl mx-auto ${className}`}>
        <CardContent className="p-8">
          <div className="text-center text-red-600">
            <p>{error || 'لم يتم العثور على بيانات التلميذ'}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      {/* أزرار التحكم */}
      <div className="flex gap-3 justify-end mb-4 no-print">
        <Button
          onClick={handleDownload}
          variant="outline"
          className="flex items-center gap-2"
        >
          <FaDownload />
          تحميل PDF
        </Button>
        <Button
          onClick={handlePrint}
          className="flex items-center gap-2 bg-[var(--primary-color)] hover:bg-[#0d7e6d]"
        >
          <FaPrint />
          طباعة البطاقة
        </Button>
      </div>

      {/* بطاقة التلميذ */}
      <Card ref={cardRef} className="print:shadow-none max-w-4xl mx-auto">
        {/* رأس البطاقة */}
        <CardHeader className="bg-gradient-to-r from-[var(--primary-color)] to-[#0d7e6d] text-white print:bg-white print:text-black print:border-b-2 print:border-[var(--primary-color)]">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* شعار المدرسة */}
              <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center print:border-2 print:border-[var(--primary-color)]">
                <img
                  src={studentData.schoolInfo.logoUrl}
                  alt="شعار المدرسة"
                  className="w-12 h-12 object-contain"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/logo.svg';
                  }}
                />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold print:text-[var(--primary-color)]">
                  {studentData.schoolInfo.name}
                </CardTitle>
                <p className="text-sm opacity-90 print:opacity-100 print:text-gray-600">
                  {studentData.schoolInfo.description}
                </p>
                <p className="text-xs opacity-80 print:opacity-100 print:text-gray-500">
                  {studentData.schoolInfo.address}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-white text-[var(--primary-color)] px-4 py-2 rounded-lg print:bg-gray-100 print:border print:border-[var(--primary-color)]">
                <p className="text-lg font-bold">بطاقة التلميذ</p>
                <p className="text-sm">رقم: {studentData.id}</p>
                <p className="text-xs">{new Date().toLocaleDateString('fr-FR')}</p>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <div className="grid grid-cols-3 gap-4">
            {/* Right Column: Student Info */}
            <div className="col-span-2 text-right">
              <div className="flex justify-between py-2 border-b">
                <span className="font-semibold">الاسم الكامل:</span>
                <span>{studentData.name}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-semibold">الرقم:</span>
                <span>{studentData.id}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="font-semibold">العمر:</span>
                <span>{studentData.age} سنة</span>
              </div>
              {studentData.classe && (
                <div className="flex justify-between py-2 border-b">
                  <span className="font-semibold">الفصل:</span>
                  <span>{studentData.classe.name}</span>
                </div>
              )}
              <div className="flex justify-between py-2 border-b">
                <span className="font-semibold">تاريخ التسجيل:</span>
                <span>{new Date(studentData.createdAt).toLocaleDateString('fr-FR')}</span>
              </div>
              {studentData.guardian && (
                <>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-semibold">اسم ولي الأمر:</span>
                    <span>{studentData.guardian.name}</span>
                  </div>
                  <div className="flex justify-between py-2">
                    <span className="font-semibold">هاتف ولي الأمر:</span>
                    <span>{studentData.guardian.phone}</span>
                  </div>
                </>
              )}
            </div>

            {/* Left Column: Image */}
            <div className="col-span-1 flex flex-col items-center">
              <div className="relative w-32 h-32 rounded-md overflow-hidden border-2 border-[var(--primary-color)]">
                {studentData.profileImage ? (
                  <Image
                    src={studentData.profileImage.imageUrl}
                    alt={`صورة ${studentData.name}`}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <FaUser className="text-5xl text-gray-400" />
                  </div>
                )}
              </div>
              <p className="text-sm text-gray-500 mt-2">@{studentData.username}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/budgets/:id/items - الحصول على بنود ميزانية محددة
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const budgetId = parseInt(params.id);

    // التحقق من وجود الميزانية
    const budget = await prisma.budget.findUnique({
      where: { id: budgetId }
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // جلب بنود الميزانية
    const items = await prisma.budgetItem.findMany({
      where: { budgetId },
      include: {
        category: true
      },
      orderBy: { createdAt: 'asc' }
    });

    // حساب المصروفات الفعلية لكل بند
    const actualExpenses = await prisma.expense.groupBy({
      by: ['categoryId'],
      where: {
        date: {
          gte: budget.startDate,
          lte: budget.endDate
        },
        categoryId: {
          in: items.map(item => item.categoryId)
        }
      },
      _sum: {
        amount: true
      }
    });

    // إضافة المصروفات الفعلية إلى بنود الميزانية
    const itemsWithExpenses = items.map(item => {
      const expenseData = actualExpenses.find(exp => exp.categoryId === item.categoryId);
      return {
        ...item,
        actualAmount: expenseData?._sum.amount || 0,
        remainingAmount: item.amount - (expenseData?._sum.amount || 0)
      };
    });

    return NextResponse.json(itemsWithExpenses);
  } catch (error) {
    console.error('خطأ في جلب بنود الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب بنود الميزانية' },
      { status: 500 }
    );
  }
}

// POST /api/budgets/:id/items - إضافة بند جديد للميزانية
export async function POST(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const budgetId = parseInt(params.id);
    const body = await req.json();
    const { categoryId, amount, notes } = body;

    if (!categoryId || !amount) {
      return NextResponse.json(
        { error: 'معرف الفئة والمبلغ مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من صحة المبلغ
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'المبلغ يجب أن يكون رقمًا موجبًا' },
        { status: 400 }
      );
    }

    // التحقق من وجود الميزانية
    const budget = await prisma.budget.findUnique({
      where: { id: budgetId }
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من وجود الفئة
    const category = await prisma.expenseCategory.findUnique({
      where: { id: categoryId }
    });

    if (!category) {
      return NextResponse.json(
        { error: 'فئة المصروفات غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود بند للفئة نفسها في الميزانية
    const existingItem = await prisma.budgetItem.findFirst({
      where: {
        budgetId,
        categoryId
      }
    });

    if (existingItem) {
      return NextResponse.json(
        { error: 'يوجد بالفعل بند لهذه الفئة في الميزانية' },
        { status: 400 }
      );
    }

    // إنشاء بند الميزانية
    const budgetItem = await prisma.budgetItem.create({
      data: {
        budgetId,
        categoryId,
        amount,
        notes
      },
      include: {
        category: true
      }
    });

    return NextResponse.json(budgetItem, { status: 201 });
  } catch (error) {
    console.error('خطأ في إضافة بند الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في إضافة بند الميزانية' },
      { status: 500 }
    );
  }
}

// PUT /api/budgets/:budgetId/items/:itemId - تحديث بند ميزانية
export async function PUT(
  req: NextRequest,
  { params }: { params: { budgetId: string; itemId: string } }
) {
  try {
    const budgetId = parseInt(params.budgetId);
    const itemId = parseInt(params.itemId);
    const body = await req.json();
    const { amount, notes } = body;

    if (!amount) {
      return NextResponse.json(
        { error: 'المبلغ مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من صحة المبلغ
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'المبلغ يجب أن يكون رقمًا موجبًا' },
        { status: 400 }
      );
    }

    // التحقق من وجود بند الميزانية
    const budgetItem = await prisma.budgetItem.findFirst({
      where: {
        id: itemId,
        budgetId
      }
    });

    if (!budgetItem) {
      return NextResponse.json(
        { error: 'بند الميزانية غير موجود' },
        { status: 404 }
      );
    }

    // تحديث بند الميزانية
    const updatedItem = await prisma.budgetItem.update({
      where: { id: itemId },
      data: {
        amount,
        notes
      },
      include: {
        category: true
      }
    });

    return NextResponse.json(updatedItem);
  } catch (error) {
    console.error('خطأ في تحديث بند الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث بند الميزانية' },
      { status: 500 }
    );
  }
}

// DELETE /api/budgets/:budgetId/items/:itemId - حذف بند ميزانية
export async function DELETE(
  req: NextRequest,
  { params }: { params: { budgetId: string; itemId: string } }
) {
  try {
    const budgetId = parseInt(params.budgetId);
    const itemId = parseInt(params.itemId);

    // التحقق من وجود بند الميزانية
    const budgetItem = await prisma.budgetItem.findFirst({
      where: {
        id: itemId,
        budgetId
      }
    });

    if (!budgetItem) {
      return NextResponse.json(
        { error: 'بند الميزانية غير موجود' },
        { status: 404 }
      );
    }

    // حذف بند الميزانية
    await prisma.budgetItem.delete({
      where: { id: itemId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف بند الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في حذف بند الميزانية' },
      { status: 500 }
    );
  }
}

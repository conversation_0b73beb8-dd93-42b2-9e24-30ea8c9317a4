'use client';

import React, { useState } from 'react';
import { FaArrowRight, FaCode, FaServer, FaDatabase, FaNetworkWired, FaLock } from 'react-icons/fa';
import Link from 'next/link';

/**
 * API documentation page for remote classes
 */
const ApiDocsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'endpoints' | 'models' | 'websockets' | 'security'>('overview');
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/remote-classes"
            className="inline-flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] mb-4"
          >
            <FaArrowRight className="ml-1" />
            <span>العودة إلى الفصول الافتراضية</span>
          </Link>
          
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaCode className="text-[var(--primary-color)]" />
            وثائق API للفصول الافتراضية
          </h1>
          <p className="text-gray-600 mr-4">
            دليل المطورين لاستخدام واجهات برمجة التطبيقات للفصول الافتراضية
          </p>
        </div>
        
        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-4 py-3 font-medium ${
                activeTab === 'overview'
                  ? 'text-[var(--primary-color)] border-b-2 border-[var(--primary-color)]'
                  : 'text-gray-600 hover:text-[var(--primary-color)]'
              }`}
            >
              نظرة عامة
            </button>
            <button
              onClick={() => setActiveTab('endpoints')}
              className={`px-4 py-3 font-medium ${
                activeTab === 'endpoints'
                  ? 'text-[var(--primary-color)] border-b-2 border-[var(--primary-color)]'
                  : 'text-gray-600 hover:text-[var(--primary-color)]'
              }`}
            >
              نقاط النهاية
            </button>
            <button
              onClick={() => setActiveTab('models')}
              className={`px-4 py-3 font-medium ${
                activeTab === 'models'
                  ? 'text-[var(--primary-color)] border-b-2 border-[var(--primary-color)]'
                  : 'text-gray-600 hover:text-[var(--primary-color)]'
              }`}
            >
              نماذج البيانات
            </button>
            <button
              onClick={() => setActiveTab('websockets')}
              className={`px-4 py-3 font-medium ${
                activeTab === 'websockets'
                  ? 'text-[var(--primary-color)] border-b-2 border-[var(--primary-color)]'
                  : 'text-gray-600 hover:text-[var(--primary-color)]'
              }`}
            >
              WebSockets
            </button>
            <button
              onClick={() => setActiveTab('security')}
              className={`px-4 py-3 font-medium ${
                activeTab === 'security'
                  ? 'text-[var(--primary-color)] border-b-2 border-[var(--primary-color)]'
                  : 'text-gray-600 hover:text-[var(--primary-color)]'
              }`}
            >
              الأمان
            </button>
          </div>
          
          <div className="p-6">
            {/* Overview */}
            {activeTab === 'overview' && (
              <div>
                <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center gap-2">
                  <FaServer />
                  نظرة عامة على API
                </h2>
                
                <div className="space-y-4">
                  <p>
                    توفر واجهة برمجة التطبيقات (API) للفصول الافتراضية مجموعة من الخدمات التي تتيح للمطورين دمج وظائف الفصول الافتراضية في تطبيقاتهم.
                  </p>
                  
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold mb-2">عنوان القاعدة</h3>
                    <code className="bg-gray-100 p-2 rounded block">
                      https://api.example.com/v1
                    </code>
                  </div>
                  
                  <div>
                    <h3 className="font-bold mb-2">المصادقة</h3>
                    <p>
                      تتطلب جميع طلبات API مصادقة باستخدام رمز JWT. يجب إرسال الرمز في ترويسة الطلب كالتالي:
                    </p>
                    <code className="bg-gray-100 p-2 rounded block mt-2">
                      Authorization: Bearer {'{token}'}
                    </code>
                  </div>
                  
                  <div>
                    <h3 className="font-bold mb-2">تنسيق الاستجابة</h3>
                    <p>
                      جميع استجابات API تكون بتنسيق JSON وتتبع الهيكل التالي:
                    </p>
                    <pre className="bg-gray-100 p-2 rounded block mt-2 overflow-x-auto">
{`{
  "success": true,
  "data": { ... },
  "error": null
}`}
                    </pre>
                    <p className="mt-2">
                      في حالة حدوث خطأ، سيكون الهيكل كالتالي:
                    </p>
                    <pre className="bg-gray-100 p-2 rounded block mt-2 overflow-x-auto">
{`{
  "success": false,
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "message": "وصف الخطأ"
  }
}`}
                    </pre>
                  </div>
                </div>
              </div>
            )}
            
            {/* Endpoints */}
            {activeTab === 'endpoints' && (
              <div>
                <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center gap-2">
                  <FaServer />
                  نقاط النهاية
                </h2>
                
                <div className="space-y-6">
                  {/* Remote Classes */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold">الفصول الافتراضية</h3>
                    </div>
                    <div className="p-4">
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-bold">GET</span>
                            <code>/remote-classes</code>
                          </div>
                          <p className="text-sm text-gray-600">الحصول على قائمة الفصول الافتراضية</p>
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-bold">GET</span>
                            <code>/remote-classes/{'{id}'}</code>
                          </div>
                          <p className="text-sm text-gray-600">الحصول على تفاصيل فصل افتراضي محدد</p>
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-bold">POST</span>
                            <code>/remote-classes</code>
                          </div>
                          <p className="text-sm text-gray-600">إنشاء فصل افتراضي جديد</p>
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-bold">PUT</span>
                            <code>/remote-classes/{'{id}'}</code>
                          </div>
                          <p className="text-sm text-gray-600">تحديث فصل افتراضي</p>
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-bold">DELETE</span>
                            <code>/remote-classes/{'{id}'}</code>
                          </div>
                          <p className="text-sm text-gray-600">حذف فصل افتراضي</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Screen Share */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold">مشاركة الشاشة</h3>
                    </div>
                    <div className="p-4">
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-bold">POST</span>
                            <code>/remote-classes/{'{id}'}/screen-share</code>
                          </div>
                          <p className="text-sm text-gray-600">بدء أو إيقاف مشاركة الشاشة</p>
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-bold">GET</span>
                            <code>/remote-classes/{'{id}'}/screen-share/status</code>
                          </div>
                          <p className="text-sm text-gray-600">الحصول على حالة مشاركة الشاشة</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Whiteboard */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold">السبورة التفاعلية</h3>
                    </div>
                    <div className="p-4">
                      <div className="space-y-4">
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-bold">GET</span>
                            <code>/remote-classes/{'{id}'}/whiteboards</code>
                          </div>
                          <p className="text-sm text-gray-600">الحصول على قائمة السبورات التفاعلية</p>
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-bold">POST</span>
                            <code>/remote-classes/{'{id}'}/whiteboards</code>
                          </div>
                          <p className="text-sm text-gray-600">إنشاء سبورة تفاعلية جديدة</p>
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-bold">PUT</span>
                            <code>/remote-classes/{'{id}'}/whiteboards/{'{whiteboardId}'}</code>
                          </div>
                          <p className="text-sm text-gray-600">تحديث محتوى السبورة التفاعلية</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Models */}
            {activeTab === 'models' && (
              <div>
                <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center gap-2">
                  <FaDatabase />
                  نماذج البيانات
                </h2>
                
                <div className="space-y-6">
                  {/* RemoteClass */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold">RemoteClass</h3>
                    </div>
                    <div className="p-4">
                      <pre className="bg-gray-100 p-2 rounded block overflow-x-auto">
{`{
  "id": number,
  "title": string,
  "description": string,
  "startTime": string (ISO date),
  "endTime": string (ISO date),
  "meetingLink": string,
  "meetingId": string | null,
  "meetingPassword": string | null,
  "platform": string,
  "isScreenShareEnabled": boolean,
  "isWhiteboardEnabled": boolean,
  "videoQuality": string | null,
  "audioQuality": string | null,
  "instructorId": number,
  "classeId": number | null,
  "recordingUrl": string | null,
  "createdAt": string (ISO date),
  "updatedAt": string (ISO date)
}`}
                      </pre>
                    </div>
                  </div>
                  
                  {/* Whiteboard */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold">Whiteboard</h3>
                    </div>
                    <div className="p-4">
                      <pre className="bg-gray-100 p-2 rounded block overflow-x-auto">
{`{
  "id": number,
  "name": string,
  "content": string | null,
  "isActive": boolean,
  "remoteClassId": number,
  "createdBy": number,
  "createdAt": string (ISO date),
  "updatedAt": string (ISO date)
}`}
                      </pre>
                    </div>
                  </div>
                  
                  {/* ScreenShare */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold">ScreenShare</h3>
                    </div>
                    <div className="p-4">
                      <pre className="bg-gray-100 p-2 rounded block overflow-x-auto">
{`{
  "id": number,
  "status": string,
  "remoteClassId": number,
  "sharedBy": number,
  "startTime": string (ISO date),
  "endTime": string (ISO date) | null
}`}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* WebSockets */}
            {activeTab === 'websockets' && (
              <div>
                <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center gap-2">
                  <FaNetworkWired />
                  WebSockets
                </h2>
                
                <div className="space-y-4">
                  <p>
                    يستخدم نظام الفصول الافتراضية WebSockets للاتصال في الوقت الفعلي بين المستخدمين. يتم استخدام Socket.io كمكتبة للتعامل مع WebSockets.
                  </p>
                  
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold mb-2">عنوان الاتصال</h3>
                    <code className="bg-gray-100 p-2 rounded block">
                      wss://api.example.com/socket
                    </code>
                  </div>
                  
                  <div>
                    <h3 className="font-bold mb-2">الأحداث</h3>
                    
                    <div className="space-y-4 mt-4">
                      <div className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="bg-gray-50 p-2 border-b border-gray-200">
                          <code>join-room</code>
                        </div>
                        <div className="p-3">
                          <p className="text-sm">الانضمام إلى غرفة الفصل الافتراضي</p>
                          <pre className="bg-gray-100 p-2 rounded block mt-2 text-xs">
{`// إرسال
socket.emit('join-room', {
  roomId: 'remote-class-123',
  userId: 'user-456',
  username: 'اسم المستخدم'
});`}
                          </pre>
                        </div>
                      </div>
                      
                      <div className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="bg-gray-50 p-2 border-b border-gray-200">
                          <code>whiteboard-update</code>
                        </div>
                        <div className="p-3">
                          <p className="text-sm">إرسال تحديثات السبورة التفاعلية</p>
                          <pre className="bg-gray-100 p-2 rounded block mt-2 text-xs">
{`// إرسال
socket.emit('whiteboard-update', {
  roomId: 'remote-class-123',
  whiteboardId: 'whiteboard-789',
  content: '...',
  userId: 'user-456'
});

// استقبال
socket.on('whiteboard-update', (data) => {
  // تحديث السبورة المحلية
});`}
                          </pre>
                        </div>
                      </div>
                      
                      <div className="border border-gray-200 rounded-lg overflow-hidden">
                        <div className="bg-gray-50 p-2 border-b border-gray-200">
                          <code>cursor-move</code>
                        </div>
                        <div className="p-3">
                          <p className="text-sm">إرسال حركة المؤشر على السبورة</p>
                          <pre className="bg-gray-100 p-2 rounded block mt-2 text-xs">
{`// إرسال
socket.emit('cursor-move', {
  roomId: 'remote-class-123',
  userId: 'user-456',
  position: { x: 100, y: 200 }
});

// استقبال
socket.on('cursor-move', (data) => {
  // تحديث موقع مؤشر المستخدم
});`}
                          </pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Security */}
            {activeTab === 'security' && (
              <div>
                <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center gap-2">
                  <FaLock />
                  الأمان
                </h2>
                
                <div className="space-y-4">
                  <p>
                    يتم تأمين واجهة برمجة التطبيقات (API) للفصول الافتراضية باستخدام عدة طبقات من الحماية لضمان سلامة البيانات وخصوصية المستخدمين.
                  </p>
                  
                  <div>
                    <h3 className="font-bold mb-2">المصادقة</h3>
                    <p>
                      تستخدم واجهة برمجة التطبيقات رموز JWT (JSON Web Tokens) للمصادقة. يجب الحصول على رمز صالح من خلال تسجيل الدخول قبل استخدام API.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-bold mb-2">التفويض</h3>
                    <p>
                      يتم التحقق من صلاحيات المستخدم للوصول إلى الموارد المطلوبة. على سبيل المثال، يمكن للمعلمين فقط إنشاء فصول افتراضية جديدة أو تعديل الفصول الحالية.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-bold mb-2">HTTPS</h3>
                    <p>
                      جميع اتصالات API تتم عبر HTTPS لتشفير البيانات المتبادلة بين العميل والخادم.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-bold mb-2">حماية WebSockets</h3>
                    <p>
                      يتم تأمين اتصالات WebSockets باستخدام رموز JWT للمصادقة والتشفير WSS (WebSockets Secure) لحماية البيانات المتبادلة في الوقت الفعلي.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-bold mb-2">حدود معدل الطلبات</h3>
                    <p>
                      يتم تطبيق حدود على معدل الطلبات لمنع هجمات حجب الخدمة (DDoS) وضمان توفر الخدمة لجميع المستخدمين.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiDocsPage;

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { InvoiceStatus } from '@prisma/client';
import { ActivityLogger, ActivityType } from '@/utils/activity-logger';
import {
  validateInvoiceData,
  sanitizeInput,
  validateStudentExists,
  validateParentExists
} from '@/utils/payment-utils';

// تعريف نوع شروط البحث للفواتير
type InvoiceWhereInput = {
  student?: {
    name?: {
      contains: string;
    };
  };
  status?: InvoiceStatus;
  month?: number;
  year?: number;
  studentId?: number;
};

// GET /api/invoices - جلب الفواتير مع إمكانية التصفية والبحث
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const status = searchParams.get('status') || '';
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    const studentId = searchParams.get('studentId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const whereConditions: InvoiceWhereInput = {};

    // إضافة شرط البحث بالاسم إذا كان موجودًا
    if (query) {
      whereConditions.student = {
        name: {
          contains: query
        }
      };
    }

    // إضافة شرط التصفية حسب الحالة إذا كان موجودًا
    if (status) {
      whereConditions.status = status as InvoiceStatus;
    }

    // إضافة شرط التصفية حسب الشهر والسنة إذا كانا موجودين
    if (month && year) {
      whereConditions.month = parseInt(month);
      whereConditions.year = parseInt(year);
    } else if (month) {
      whereConditions.month = parseInt(month);
    } else if (year) {
      whereConditions.year = parseInt(year);
    }

    // إضافة شرط التصفية حسب معرف الطالب إذا كان موجودًا
    if (studentId) {
      whereConditions.studentId = parseInt(studentId);
    }

    // جلب إجمالي عدد الفواتير
    const totalInvoices = await prisma.invoice.count({
      where: whereConditions
    });

    // جلب الفواتير مع بيانات الطالب
    const invoices = await prisma.invoice.findMany({
      where: whereConditions,
      include: {
        student: true,
        payments: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: limit
    });

    // حساب المبلغ المدفوع لكل فاتورة
    const formattedInvoices = invoices.map(invoice => {
      const paidAmount = invoice.payments.reduce((sum, payment) => {
        if (payment.status === 'PAID') {
          return sum + payment.amount;
        }
        return sum;
      }, 0);

      return {
        ...invoice,
        paidAmount,
        remainingAmount: invoice.amount - paidAmount
      };
    });

    return NextResponse.json({
      invoices: formattedInvoices,
      pagination: {
        total: totalInvoices,
        page,
        limit,
        totalPages: Math.ceil(totalInvoices / limit)
      }
    });
  } catch (error) {
    console.error('خطأ في جلب الفواتير:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الفواتير' },
      { status: 500 }
    );
  }
}

// POST /api/invoices - إنشاء فاتورة جديدة (فردية أو جماعية)
export async function POST(req: NextRequest) {
  console.log('Received POST request to /api/invoices');
  try {
    const body = await req.json();
    console.log('Request body:', body);
    let { studentId, parentId, amount, dueDate, issueDate, month, year, description, type } = body;
    const { status } = body;

    // تنظيف المدخلات
    studentId = sanitizeInput(studentId);
    parentId = sanitizeInput(parentId);
    amount = sanitizeInput(amount);
    dueDate = sanitizeInput(dueDate);
    issueDate = sanitizeInput(issueDate);
    month = sanitizeInput(month);
    year = sanitizeInput(year);
    description = sanitizeInput(description);
    type = sanitizeInput(type) || 'INDIVIDUAL';

    console.log('📄 إنشاء فاتورة جديدة:', { type, studentId, parentId, amount });

    // التحقق من صحة البيانات
    const validationErrors = validateInvoiceData({
      studentId,
      parentId,
      amount,
      dueDate,
      issueDate,
      month,
      year,
      description,
      type
    });

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'بيانات غير صحيحة',
          details: validationErrors
        },
        { status: 400 }
      );
    }

    // التحقق من وجود الكيان المرتبط
    if (type === 'INDIVIDUAL') {
      const student = await prisma.student.findUnique({
        where: { id: studentId },
        include: { guardian: true, classe: true }
      });

      if (!student) {
        return NextResponse.json(
          { error: 'التلميذ غير موجود' },
          { status: 404 }
        );
      }
    } else if (type === 'FAMILY') {
      const parent = await prisma.parent.findUnique({
        where: { id: parentId },
        include: { students: true }
      });

      if (!parent) {
        return NextResponse.json(
          { error: 'الولي غير موجود' },
          { status: 404 }
        );
      }

      if (parent.students.length === 0) {
        return NextResponse.json(
          { error: 'الولي ليس لديه أبناء مسجلون' },
          { status: 400 }
        );
      }
    }

    // إنشاء الفاتورة
    const invoice = await prisma.invoice.create({
      data: {
        studentId: type === 'INDIVIDUAL' ? studentId : null,
        parentId: type === 'FAMILY' ? parentId : null,
        amount,
        dueDate: new Date(dueDate),
        issueDate: new Date(issueDate || new Date()),
        month,
        year,
        description,
        type,
        status: (status as InvoiceStatus) || 'UNPAID'
      },
      include: {
        student: {
          include: {
            guardian: true,
            classe: true
          }
        },
        parent: {
          include: {
            students: true
          }
        }
      }
    });

    // تسجيل نشاط إنشاء الفاتورة
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        const entityName = type === 'INDIVIDUAL'
          ? `التلميذ ${invoice.student?.name}`
          : `الولي ${invoice.parent?.name}`;

        await ActivityLogger.log(
          adminUser.id,
          ActivityType.INVOICE,
          `إنشاء فاتورة ${type === 'FAMILY' ? 'جماعية' : 'فردية'} بقيمة ${amount} دج لـ ${entityName}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط إنشاء الفاتورة:', error);
      // لا نريد أن يفشل إنشاء الفاتورة إذا فشل تسجيل النشاط
    }

    console.log('✅ تم إنشاء الفاتورة بنجاح:', {
      id: invoice.id,
      type: invoice.type,
      amount: invoice.amount,
      entityName: type === 'INDIVIDUAL' ? invoice.student?.name : invoice.parent?.name
    });

    return NextResponse.json({
      success: true,
      invoice,
      message: `تم إنشاء الفاتورة ${type === 'FAMILY' ? 'الجماعية' : 'الفردية'} بنجاح`
    });
  } catch (error) {
    console.error('خطأ في إنشاء الفاتورة:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الفاتورة' },
      { status: 500 }
    );
  }
}

// PATCH /api/invoices - تحديث حالة الفاتورة
export async function PATCH(req: NextRequest) {
  try {
    const body = await req.json();
    const { id, status, notes } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفاتورة
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id.toString()) },
      include: {
        student: true,
        parent: true
      }
    });

    if (!existingInvoice) {
      return NextResponse.json(
        { error: 'الفاتورة غير موجودة' },
        { status: 404 }
      );
    }

    // تحديث الفاتورة
    const updatedInvoice = await prisma.invoice.update({
      where: { id: parseInt(id.toString()) },
      data: {
        ...(status && { status: status as InvoiceStatus }),
        ...(notes !== undefined && { notes }),
        updatedAt: new Date()
      },
      include: {
        student: true,
        payments: true
      }
    });

    // تسجيل نشاط تحديث الفاتورة
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        const entityName = existingInvoice.student?.name || existingInvoice.parent?.name || 'غير محدد';
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.INVOICE,
          `تحديث حالة الفاتورة #${id} لـ ${entityName} إلى ${status || 'نفس الحالة'}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط تحديث الفاتورة:', error);
    }

    return NextResponse.json(updatedInvoice);
  } catch (error) {
    console.error('خطأ في تحديث الفاتورة:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الفاتورة' },
      { status: 500 }
    );
  }
}

// DELETE /api/invoices/:id - حذف فاتورة
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفاتورة
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) },
      include: {
        student: true,
        parent: true,
        payments: true
      }
    });

    if (!existingInvoice) {
      return NextResponse.json(
        { error: 'الفاتورة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود مدفوعات مرتبطة بالفاتورة
    if (existingInvoice.payments.length > 0) {
      return NextResponse.json(
        { error: 'لا يمكن حذف الفاتورة لأنها مرتبطة بمدفوعات' },
        { status: 400 }
      );
    }

    // حذف الفاتورة
    await prisma.invoice.delete({
      where: { id: parseInt(id) }
    });

    // تسجيل نشاط حذف الفاتورة
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        const entityName = existingInvoice.student?.name || existingInvoice.parent?.name || 'غير محدد';
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.INVOICE,
          `حذف الفاتورة #${id} لـ ${entityName}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط حذف الفاتورة:', error);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف الفاتورة:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الفاتورة' },
      { status: 500 }
    );
  }
}

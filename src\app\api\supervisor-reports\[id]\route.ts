import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// GET /api/supervisor-reports/[id] - الحصول على تقرير محدد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || !['ADMIN', 'EMPLOYEE'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به، يجب أن تكون مسؤول أو موظف' },
        { status: 401 }
      );
    }

    const reportId = parseInt(params.id);
    if (isNaN(reportId)) {
      return NextResponse.json({
        success: false,
        error: 'معرف التقرير غير صحيح'
      }, { status: 400 });
    }

    // جلب التقرير مع معلومات المنشئ
    const report = await prisma.supervisorReport.findUnique({
      where: { id: reportId },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            profile: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!report) {
      return NextResponse.json({
        success: false,
        error: 'التقرير غير موجود'
      }, { status: 404 });
    }

    // تحويل البيانات المحفوظة من JSON
    const formattedReport = {
      ...report,
      literaryContent: report.literaryContent ? JSON.parse(report.literaryContent) : null,
      financialData: report.financialData ? JSON.parse(report.financialData) : null,
      officeSettings: report.officeSettings ? JSON.parse(report.officeSettings) : null,
      createdBy: report.creator?.profile?.name || report.creator?.username || 'غير محدد'
    };

    return NextResponse.json({
      success: true,
      data: formattedReport,
      message: 'تم جلب التقرير بنجاح'
    });

  } catch (error) {
    console.error('Error fetching supervisor report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب التقرير'
    }, { status: 500 });
  }
}

// PUT /api/supervisor-reports/[id] - تعديل تقرير موحد
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || !['ADMIN', 'EMPLOYEE'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به، يجب أن تكون مسؤول أو موظف' },
        { status: 401 }
      );
    }

    const reportId = parseInt(params.id);
    if (isNaN(reportId)) {
      return NextResponse.json({
        success: false,
        error: 'معرف التقرير غير صحيح'
      }, { status: 400 });
    }

    // التحقق من وجود التقرير
    const existingReport = await prisma.supervisorReport.findUnique({
      where: { id: reportId }
    });

    if (!existingReport) {
      return NextResponse.json({
        success: false,
        error: 'التقرير غير موجود'
      }, { status: 404 });
    }

    const body = await request.json();
    const {
      title,
      description,
      periodStart,
      periodEnd,
      literaryContent,
      financialData,
      officeSettings,
      status
    } = body;

    // التحقق من صحة التواريخ إذا تم تمريرها
    if (periodStart && periodEnd) {
      const startDate = new Date(periodStart);
      const endDate = new Date(periodEnd);
      
      if (startDate >= endDate) {
        return NextResponse.json({
          success: false,
          error: 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية'
        }, { status: 400 });
      }
    }

    // تحديث التقرير
    const updatedReport = await prisma.supervisorReport.update({
      where: { id: reportId },
      data: {
        ...(title && { title }),
        ...(description !== undefined && { description }),
        ...(periodStart && { periodStart: new Date(periodStart) }),
        ...(periodEnd && { periodEnd: new Date(periodEnd) }),
        ...(literaryContent !== undefined && { 
          literaryContent: literaryContent ? JSON.stringify(literaryContent) : null 
        }),
        ...(financialData !== undefined && { 
          financialData: financialData ? JSON.stringify(financialData) : null 
        }),
        ...(officeSettings !== undefined && { 
          officeSettings: officeSettings ? JSON.stringify(officeSettings) : null 
        }),
        ...(status && { status })
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            profile: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedReport,
      message: 'تم تحديث التقرير بنجاح'
    });

  } catch (error) {
    console.error('Error updating supervisor report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تحديث التقرير'
    }, { status: 500 });
  }
}

// DELETE /api/supervisor-reports/[id] - حذف تقرير موحد
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به، يجب أن تكون مسؤول' },
        { status: 401 }
      );
    }

    const reportId = parseInt(params.id);
    if (isNaN(reportId)) {
      return NextResponse.json({
        success: false,
        error: 'معرف التقرير غير صحيح'
      }, { status: 400 });
    }

    // التحقق من وجود التقرير
    const existingReport = await prisma.supervisorReport.findUnique({
      where: { id: reportId }
    });

    if (!existingReport) {
      return NextResponse.json({
        success: false,
        error: 'التقرير غير موجود'
      }, { status: 404 });
    }

    // حذف التقرير
    await prisma.supervisorReport.delete({
      where: { id: reportId }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف التقرير بنجاح'
    });

  } catch (error) {
    console.error('Error deleting supervisor report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف التقرير'
    }, { status: 500 });
  }
}

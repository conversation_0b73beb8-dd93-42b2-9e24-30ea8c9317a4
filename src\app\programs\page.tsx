'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { <PERSON>a<PERSON>ura<PERSON>, FaBookReader, FaMicrophone, FaGraduationCap, FaCheck, FaSpinner } from 'react-icons/fa';
import SiteLogo from '@/components/SiteLogo';

interface ProgramFeature {
  id: number;
  text: string;
  programId: number;
  order: number;
}

interface Program {
  id: number;
  title: string;
  description: string;
  iconName: string;
  price: string;
  popular: boolean;
  features: ProgramFeature[];
  createdAt: string;
  updatedAt: string;
}

const ProgramsPage = () => {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        const response = await fetch('/api/programs');
        if (!response.ok) throw new Error('Failed to fetch programs');
        const data = await response.json();
        setPrograms(data);
      } catch (err) {
        console.error('Error fetching programs:', err);
        setError('حدث خطأ أثناء تحميل البرامج. يرجى المحاولة مرة أخرى لاحقًا.');
      } finally {
        setLoading(false);
      }
    };

    fetchPrograms();
  }, []);

  // دالة لتحديد الأيقونة المناسبة بناءً على اسم الأيقونة
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'FaQuran':
        return <FaQuran className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />;
      case 'FaBookReader':
        return <FaBookReader className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />;
      case 'FaMicrophone':
        return <FaMicrophone className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />;
      case 'FaGraduationCap':
        return <FaGraduationCap className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />;
      default:
        return <FaGraduationCap className="text-5xl text-[var(--primary-color)] mx-auto mb-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-6">
            <SiteLogo size="lg" showText={false} />
          </div>

          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">برامجنا التعليمية</h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            نقدم مجموعة متنوعة من البرامج التعليمية المتخصصة في تعليم القرآن الكريم وعلومه، مصممة لتناسب جميع المستويات والأعمار.
          </p>
        </div>

        {/* Programs Grid */}
        {loading ? (
          <div className="flex justify-center items-center py-20">
            <FaSpinner className="text-[var(--primary-color)] text-4xl animate-spin" />
          </div>
        ) : error ? (
          <div className="text-center py-10">
            <p className="text-red-500">{error}</p>
          </div>
        ) : programs.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-500">لا توجد برامج متاحة حالياً</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {programs.map((program) => (
              <div
                key={program.id}
                className={`bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:shadow-lg hover:-translate-y-1 ${
                  program.popular ? 'border-2 border-[var(--primary-color)] relative' : ''
                }`}
              >
                {program.popular && (
                  <div className="absolute top-0 right-0 bg-[var(--primary-color)] text-white px-4 py-1 text-sm font-medium">
                    الأكثر شعبية
                  </div>
                )}

                <div className="p-6">
                  {getIconComponent(program.iconName)}
                  <h3 className="text-xl font-bold text-gray-800 mb-2 text-center">{program.title}</h3>
                  <p className="text-gray-600 mb-4 text-center">{program.description}</p>

                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <ul className="space-y-2">
                      {program.features.map((feature) => (
                        <li key={feature.id} className="flex items-start">
                          <FaCheck className="text-[var(--primary-color)] mt-1 ml-2 flex-shrink-0" />
                          <span className="text-gray-700">{feature.text}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="mt-6 text-center">
                    <p className="text-2xl font-bold text-[var(--primary-color)] mb-4">{program.price}</p>
                    <Link
                      href="/register"
                      className="block w-full bg-[var(--primary-color)] text-white py-2 px-4 rounded-md hover:bg-[var(--secondary-color)] transition-colors"
                    >
                      سجل الآن
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Additional Information */}
        <div className="mt-16 max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">معلومات إضافية</h2>

          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">مواعيد الدراسة</h3>
              <p className="text-gray-600">
                تقام الدروس على مدار الأسبوع من الأحد إلى الخميس، في الفترات الصباحية والمسائية حسب البرنامج المختار.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">طرق التدريس</h3>
              <p className="text-gray-600">
                نعتمد على طرق تدريس متنوعة تجمع بين التلقين المباشر والتعلم التفاعلي، مع استخدام وسائل تعليمية حديثة تساعد على الفهم والحفظ.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">المعلمون</h3>
              <p className="text-gray-600">
                يقوم بالتدريس نخبة من المعلمين المتخصصين الحاصلين على إجازات في القرآن الكريم وعلومه، ولديهم خبرة طويلة في مجال التعليم.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">التسجيل والاستفسارات</h3>
              <p className="text-gray-600">
                للتسجيل في أي من برامجنا أو للاستفسار عن المزيد من المعلومات، يرجى التواصل معنا عبر صفحة الاتصال أو زيارة مقر المعهد.
              </p>
            </div>
          </div>

          <div className="mt-8 text-center">
            <Link
              href="/contact"
              className="inline-block bg-gray-800 text-white py-3 px-6 rounded-md hover:bg-gray-700 transition-colors"
            >
              تواصل معنا للاستفسار
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgramsPage;

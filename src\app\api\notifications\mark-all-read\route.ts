import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

// POST: تحديد جميع الإشعارات كمقروءة
export async function POST(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userId = userData.id;
        console.log(`Attempting to mark all notifications as read for userId: ${userId}`);

        console.log('Starting individual notifications update...');
        // تحديث جميع الإشعارات الفردية غير المقروءة للمستخدم
        const individualResult = await prisma.notification.updateMany({
            where: {
                userId,
                read: false,
                isGroupNotification: false // Ensure only individual notifications are updated
            },
            data: {
                read: true
            }
        });
        console.log(`Individual notifications updated: ${individualResult.count}`);

        console.log('Starting group notifications update...');
        // تحديث جميع الإشعارات الجماعية غير المقروءة للمستخدم
        const groupResult = await prisma.notificationRecipient.updateMany({
            where: {
                userId,
                read: false
            },
            data: {
                read: true
            }
        });
        console.log(`Group notifications updated: ${groupResult.count}`);

        const totalCount = individualResult.count + groupResult.count;

        return NextResponse.json({
            message: "تم تحديد جميع الإشعارات كمقروءة",
            count: totalCount,
            individual: individualResult.count,
            group: groupResult.count
        });
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء تحديث الإشعارات" },
            { status: 500 }
        );
    }
}

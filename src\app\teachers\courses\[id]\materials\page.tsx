"use client";
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { FaBook, FaArrowLeft, FaFileAlt, FaVideo, FaLink, FaPlus, FaTrash, FaEdit } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface Material {
  id: number;
  title: string;
  description: string;
  type: string;
  url: string;
  createdAt: string;
}

interface CourseInfo {
  id: number;
  subjectName: string;
  className: string;
}

const CourseMaterialsPage = ({ params }: { params: { id: string } }) => {
  // استخدام React.use() لفك تغليف params
  const unwrappedParams = React.use(params);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [courseInfo, setCourseInfo] = useState<CourseInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dialog state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentMaterial, setCurrentMaterial] = useState<Material | null>(null);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [materialType, setMaterialType] = useState('pdf');
  const [url, setUrl] = useState('');
  const [file, setFile] = useState<File | null>(null);

  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب معلومات المقرر
        const courseResponse = await fetch(`/api/teacher-courses/${unwrappedParams.id}`);

        if (!courseResponse.ok) {
          throw new Error('فشل في جلب معلومات المقرر');
        }

        const courseData = await courseResponse.json();

        if (courseData.success) {
          setCourseInfo({
            id: parseInt(unwrappedParams.id),
            subjectName: courseData.courseDetails.subjectName,
            className: courseData.courseDetails.className
          });

          // جلب المواد التعليمية للمقرر
          const materialsResponse = await fetch(`/api/course-materials/${unwrappedParams.id}`);

          if (!materialsResponse.ok) {
            throw new Error('فشل في جلب المواد التعليمية');
          }

          const materialsData = await materialsResponse.json();

          if (materialsData.success) {
            setMaterials(materialsData.materials);
          } else {
            throw new Error(materialsData.message || 'فشل في جلب المواد التعليمية');
          }
        } else {
          throw new Error(courseData.message || 'فشل في جلب معلومات المقرر');
        }

      } catch (err) {
        console.error('Error fetching materials:', err);
        setError('حدث خطأ أثناء جلب المواد التعليمية');
        toast.error('فشل في جلب المواد التعليمية');
      } finally {
        setIsLoading(false);
      }
    };

    fetchMaterials();
  }, [unwrappedParams.id]);

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FaFileAlt className="text-red-500" />;
      case 'video':
        return <FaVideo className="text-blue-500" />;
      case 'doc':
        return <FaFileAlt className="text-blue-700" />;
      default:
        return <FaLink className="text-gray-500" />;
    }
  };

  const getFileTypeName = (type: string) => {
    switch (type) {
      case 'pdf':
        return 'ملف PDF';
      case 'video':
        return 'فيديو';
      case 'doc':
        return 'مستند';
      case 'link':
        return 'رابط';
      default:
        return 'ملف';
    }
  };

  const openAddDialog = () => {
    setIsEditMode(false);
    setCurrentMaterial(null);
    setTitle('');
    setDescription('');
    setMaterialType('pdf');
    setUrl('');
    setFile(null);
    setIsDialogOpen(true);
  };

  const openEditDialog = (material: Material) => {
    setIsEditMode(true);
    setCurrentMaterial(material);
    setTitle(material.title);
    setDescription(material.description);
    setMaterialType(material.type);
    setUrl(material.url);
    setFile(null);
    setIsDialogOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // إنشاء FormData لإرسال الملف
      const formData = new FormData();
      formData.append('title', title);
      formData.append('description', description);
      formData.append('type', materialType);
      formData.append('courseId', unwrappedParams.id);

      if (materialType === 'link') {
        formData.append('url', url);
      } else if (file) {
        formData.append('file', file);
      }

      if (isEditMode && currentMaterial) {
        // تحديث مادة موجودة
        formData.append('materialId', currentMaterial.id.toString());

        const response = await fetch('/api/course-materials/update', {
          method: 'POST',
          body: formData
        });

        const data = await response.json();

        if (data.success) {
          // تحديث القائمة المحلية
          const updatedMaterial = data.material;
          setMaterials(prevMaterials =>
            prevMaterials.map(m => m.id === currentMaterial.id ? updatedMaterial : m)
          );

          toast.success('تم تحديث المادة التعليمية بنجاح');
        } else {
          throw new Error(data.message || 'فشل في تحديث المادة التعليمية');
        }
      } else {
        // إضافة مادة جديدة
        const response = await fetch('/api/course-materials/create', {
          method: 'POST',
          body: formData
        });

        const data = await response.json();

        if (data.success) {
          // إضافة المادة الجديدة إلى القائمة المحلية
          setMaterials(prevMaterials => [...prevMaterials, data.material]);

          toast.success('تم إضافة المادة التعليمية بنجاح');
        } else {
          throw new Error(data.message || 'فشل في إضافة المادة التعليمية');
        }
      }

      setIsDialogOpen(false);
    } catch (err: unknown) {
      const error = err as Error;
      console.error('Error submitting material:', error);
      toast.error(error.message || 'حدث خطأ أثناء حفظ المادة التعليمية');
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذه المادة التعليمية؟')) {
      try {
        const response = await fetch(`/api/course-materials/delete?id=${id}`, {
          method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
          // تحديث القائمة المحلية
          setMaterials(prevMaterials => prevMaterials.filter(m => m.id !== id));
          toast.success('تم حذف المادة التعليمية بنجاح');
        } else {
          throw new Error(data.message || 'فشل في حذف المادة التعليمية');
        }
      } catch (err: unknown) {
        const error = err as Error;
        console.error('Error deleting material:', error);
        toast.error(error.message || 'حدث خطأ أثناء حذف المادة التعليمية');
      }
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center">
        <Link href={`/teachers/courses/${unwrappedParams.id}`}>
          <Button variant="outline" className="flex items-center gap-2 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white">
            <FaArrowLeft />
            العودة إلى تفاصيل المقرر
          </Button>
        </Link>
        <Button
          onClick={openAddDialog}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-2"
        >
          <FaPlus />
          إضافة مادة تعليمية
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card className="border border-[#e0f2ef] shadow-md">
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : (
        <>
          <Card className="border border-[#e0f2ef] shadow-md">
            <CardHeader className="bg-[#f8fffd]">
              <CardTitle className="text-2xl text-[var(--primary-color)]">إدارة المواد التعليمية</CardTitle>
              {courseInfo && (
                <CardDescription className="flex items-center gap-2 mt-2">
                  <FaBook className="text-gray-500" />
                  <span>{courseInfo.subjectName} - {courseInfo.className}</span>
                </CardDescription>
              )}
            </CardHeader>
            <CardContent className="pt-4">
              {materials.length > 0 ? (
                <div className="space-y-4">
                  {materials.map((material) => (
                    <div key={material.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center gap-3">
                        {getFileIcon(material.type)}
                        <div>
                          <h4 className="font-medium">{material.title}</h4>
                          <p className="text-sm text-gray-500">{material.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-xs bg-gray-100 px-2 py-1 rounded">{getFileTypeName(material.type)}</span>
                            <span className="text-xs text-gray-500">تم الإضافة: {material.createdAt}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-[var(--primary-color)]"
                          onClick={() => openEditDialog(material)}
                        >
                          <FaEdit />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500"
                          onClick={() => handleDelete(material.id)}
                        >
                          <FaTrash />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-4">لا توجد مواد تعليمية متاحة</div>
              )}
            </CardContent>
          </Card>

          {/* Dialog for adding/editing materials */}
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="bg-white">
              <DialogHeader>
                <DialogTitle>{isEditMode ? 'تعديل مادة تعليمية' : 'إضافة مادة تعليمية جديدة'}</DialogTitle>
                <DialogDescription>
                  {isEditMode
                    ? 'قم بتعديل تفاصيل المادة التعليمية'
                    : 'أدخل تفاصيل المادة التعليمية الجديدة'}
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="title">عنوان المادة</Label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="أدخل عنوان المادة التعليمية"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">وصف المادة</Label>
                    <Input
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="أدخل وصفاً مختصراً للمادة التعليمية"
                    />
                  </div>

                  <div>
                    <Label htmlFor="type">نوع المادة</Label>
                    <Select value={materialType} onValueChange={setMaterialType}>
                      <SelectTrigger id="type">
                        <SelectValue placeholder="اختر نوع المادة" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pdf">ملف PDF</SelectItem>
                        <SelectItem value="doc">مستند</SelectItem>
                        <SelectItem value="video">فيديو</SelectItem>
                        <SelectItem value="link">رابط</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {materialType === 'link' ? (
                    <div>
                      <Label htmlFor="url">الرابط</Label>
                      <Input
                        id="url"
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        placeholder="أدخل رابط المادة التعليمية"
                        required
                      />
                    </div>
                  ) : (
                    <div>
                      <Label htmlFor="file">الملف</Label>
                      <Input
                        id="file"
                        type="file"
                        onChange={(e) => {
                          if (e.target.files && e.target.files[0]) {
                            setFile(e.target.files[0]);
                          }
                        }}
                        className="cursor-pointer"
                        required={!isEditMode}
                      />
                      {isEditMode && (
                        <p className="text-xs text-gray-500 mt-1">
                          اترك هذا الحقل فارغاً إذا كنت لا ترغب في تغيير الملف
                        </p>
                      )}
                    </div>
                  )}
                </div>

                <DialogFooter className="mt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                  >
                    إلغاء
                  </Button>
                  <Button
                    type="submit"
                    className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
                  >
                    {isEditMode ? 'تحديث' : 'إضافة'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
};

export default CourseMaterialsPage;

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/incomes - الحصول على المداخيل
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const where: {
      source?: { contains: string };
      date?: {
        gte: Date;
        lte: Date;
      };
    } = {};

    if (query) {
      where.source = { contains: query };
    }

    if (month && year) {
      const yearNum = parseInt(year);
      const monthNum = parseInt(month);

      if (!isNaN(yearNum) && !isNaN(monthNum)) {
        const startDate = new Date(yearNum, monthNum - 1, 1);
        const endDate = new Date(yearNum, monthNum, 0);

        where.date = {
          gte: startDate,
          lte: endDate,
        };
      }
    }

    // جلب المداخيل مع الترتيب حسب التاريخ (الأحدث أولاً)
    const incomes = await prisma.income.findMany({
      where,
      orderBy: { date: 'desc' },
      skip,
      take: limit,
      include: {
        treasury: true,
      },
    });

    // جلب العدد الإجمالي للمداخيل
    const total = await prisma.income.count({ where });

    // حساب إجمالي مبالغ المداخيل
    const totalAmount = await prisma.income.aggregate({
      _sum: {
        amount: true,
      },
      where,
    });

    return NextResponse.json({
      incomes,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      },
      stats: {
        totalAmount: totalAmount._sum.amount || 0,
      },
    });
  } catch (error) {
    console.error('خطأ في جلب المداخيل:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المداخيل' },
      { status: 500 }
    );
  }
}

// POST /api/incomes - إنشاء مدخول جديد
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { source, amount } = body;

    if (!source || !amount) {
      return NextResponse.json(
        { error: 'المصدر والمبلغ مطلوبان' },
        { status: 400 }
      );
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // الحصول على الخزينة الافتراضية (نفترض أن هناك خزينة واحدة فقط)
    let treasury = await prisma.treasury.findFirst();

    // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 0,
          totalIncome: 0,
          totalExpense: 0,
        },
      });
    }

    // إنشاء المدخول وتحديث الخزينة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء المدخول
      const income = await tx.income.create({
        data: {
          source,
          amount,
          treasuryId: treasury.id,
        },
      });

      // تحديث رصيد الخزينة وإجمالي الدخل
      await tx.treasury.update({
        where: { id: treasury.id },
        data: {
          balance: { increment: amount },
          totalIncome: { increment: amount },
        },
      });

      return income;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء المدخول:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء المدخول' },
      { status: 500 }
    );
  }
}

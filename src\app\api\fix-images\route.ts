import { NextRequest, NextResponse } from 'next/server';
import { existsSync } from 'fs';
import { join } from 'path';
import prisma from '@/lib/prisma';

// POST /api/fix-images - إصلاح مسارات الصور المكسورة
export async function POST(request: NextRequest) {
  try {
    const results = {
      checked: 0,
      fixed: 0,
      errors: [] as string[]
    };

    // فحص إعدادات الموقع
    const settings = await prisma.setting.findFirst();
    if (settings?.value) {
      const siteSettings = JSON.parse(settings.value);
      
      if (siteSettings.logoUrl) {
        results.checked++;
        const logoPath = siteSettings.logoUrl.replace('/uploads/', '');
        const fullPath = join(process.cwd(), 'public', 'uploads', logoPath);
        
        if (!existsSync(fullPath)) {
          results.errors.push(`شعار الموقع غير موجود: ${siteSettings.logoUrl}`);
        }
      }

      if (siteSettings.faviconUrl) {
        results.checked++;
        const faviconPath = siteSettings.faviconUrl.replace('/uploads/', '');
        const fullPath = join(process.cwd(), 'public', 'uploads', faviconPath);
        
        if (!existsSync(fullPath)) {
          results.errors.push(`أيقونة الموقع غير موجودة: ${siteSettings.faviconUrl}`);
        }
      }
    }

    // فحص صور الطلاب
    const studentImages = await prisma.studentImage.findMany({
      select: {
        id: true,
        imageUrl: true
      }
    });

    for (const image of studentImages) {
      results.checked++;
      if (image.imageUrl.startsWith('/uploads/')) {
        const imagePath = image.imageUrl.replace('/uploads/', '');
        const fullPath = join(process.cwd(), 'public', 'uploads', imagePath);
        
        if (!existsSync(fullPath)) {
          results.errors.push(`صورة طالب غير موجودة: ${image.imageUrl} (ID: ${image.id})`);
        }
      }
    }

    // فحص صور الألبومات
    const albums = await prisma.studentAlbum.findMany({
      select: {
        id: true,
        coverImage: true
      },
      where: {
        coverImage: {
          not: null
        }
      }
    });

    for (const album of albums) {
      if (album.coverImage) {
        results.checked++;
        if (album.coverImage.startsWith('/uploads/')) {
          const imagePath = album.coverImage.replace('/uploads/', '');
          const fullPath = join(process.cwd(), 'public', 'uploads', imagePath);
          
          if (!existsSync(fullPath)) {
            results.errors.push(`صورة غلاف ألبوم غير موجودة: ${album.coverImage} (Album ID: ${album.id})`);
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم فحص الصور بنجاح',
      data: results
    });
  } catch (error) {
    console.error('Error checking images:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ أثناء فحص الصور' 
      },
      { status: 500 }
    );
  }
}

// GET /api/fix-images - الحصول على تقرير حالة الصور
export async function GET() {
  try {
    const results = {
      totalImages: 0,
      brokenImages: 0,
      workingImages: 0,
      details: [] as any[]
    };

    // فحص إعدادات الموقع
    const settings = await prisma.setting.findFirst();
    if (settings?.value) {
      const siteSettings = JSON.parse(settings.value);
      
      if (siteSettings.logoUrl) {
        results.totalImages++;
        const logoPath = siteSettings.logoUrl.replace('/uploads/', '');
        const fullPath = join(process.cwd(), 'public', 'uploads', logoPath);
        
        if (existsSync(fullPath)) {
          results.workingImages++;
          results.details.push({
            type: 'logo',
            path: siteSettings.logoUrl,
            status: 'working'
          });
        } else {
          results.brokenImages++;
          results.details.push({
            type: 'logo',
            path: siteSettings.logoUrl,
            status: 'broken'
          });
        }
      }
    }

    // فحص صور الطلاب
    const studentImages = await prisma.studentImage.findMany({
      select: {
        id: true,
        imageUrl: true,
        student: {
          select: {
            name: true
          }
        }
      }
    });

    for (const image of studentImages) {
      results.totalImages++;
      if (image.imageUrl.startsWith('/uploads/')) {
        const imagePath = image.imageUrl.replace('/uploads/', '');
        const fullPath = join(process.cwd(), 'public', 'uploads', imagePath);
        
        if (existsSync(fullPath)) {
          results.workingImages++;
          results.details.push({
            type: 'student_image',
            path: image.imageUrl,
            status: 'working',
            studentName: image.student.name,
            imageId: image.id
          });
        } else {
          results.brokenImages++;
          results.details.push({
            type: 'student_image',
            path: image.imageUrl,
            status: 'broken',
            studentName: image.student.name,
            imageId: image.id
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('Error getting image status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ أثناء فحص حالة الصور' 
      },
      { status: 500 }
    );
  }
}

@startuml Use Case Diagram - Quran School Management System

' Actors
actor "Administrator" as Admin
actor "Teacher" as Teacher
actor "Student" as Student
actor "Parent" as Parent
actor "System" as System

' System boundary
rectangle "Quran School Management System" {
  ' Administrator use cases
  package "Administration" {
    usecase "Manage Users" as UC1
    usecase "Configure System" as UC2
    usecase "Generate Reports" as UC3
    usecase "Manage Financial Records" as UC4
    usecase "Manage Curriculum" as UC4a
  }

  ' Teacher use cases
  package "Teaching" {
    usecase "Manage Classes" as UC5
    usecase "Track Attendance" as UC6
    usecase "Evaluate Students" as UC7
    usecase "Track Quran Progress" as UC8
    usecase "Conduct Remote Sessions" as UC9
    usecase "Create Learning Materials" as UC9a
  }

  ' Student use cases
  package "Learning" {
    usecase "View Schedule" as UC10
    usecase "Access Learning Materials" as UC11
    usecase "Track Personal Progress" as UC12
    usecase "Attend Virtual Classes" as UC13
    usecase "Submit Assignments" as UC14
    usecase "Practice Recitation" as UC14a
  }

  ' Parent use cases
  package "Parental Supervision" {
    usecase "Monitor Children's Progress" as UC15
    usecase "View Attendance Records" as UC16
    usecase "Communicate with Teachers" as UC17
    usecase "Make Payments" as UC18
    usecase "View Financial Records" as UC19
    usecase "Register Children" as UC19a
  }

  ' System use cases
  package "System Functions" {
    usecase "Send Notifications" as UC20
    usecase "Generate Invoices" as UC21
    usecase "Backup Data" as UC22
    usecase "Schedule Automated Tasks" as UC23
  }
}

' Relationships
Admin --> UC1
Admin --> UC2
Admin --> UC3
Admin --> UC4
Admin --> UC4a

Teacher --> UC5
Teacher --> UC6
Teacher --> UC7
Teacher --> UC8
Teacher --> UC9
Teacher --> UC9a

Student --> UC10
Student --> UC11
Student --> UC12
Student --> UC13
Student --> UC14
Student --> UC14a

Parent --> UC15
Parent --> UC16
Parent --> UC17
Parent --> UC18
Parent --> UC19
Parent --> UC19a

System --> UC20
System --> UC21
System --> UC22
System --> UC23

' Inheritance relationships
UC15 ..> UC12 : extends
UC16 ..> UC6 : extends
UC17 ..> UC20 : includes
UC18 ..> UC21 : includes

' Include relationships
UC7 ..> UC8 : includes
UC9 ..> UC6 : includes

' Generalization
Admin --|> Teacher : can act as

@enduml

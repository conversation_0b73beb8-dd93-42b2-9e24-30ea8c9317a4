# مخططات التسلسل - نظام إدارة التلاميذ المتقدم

## 1. تسلسل إدخال معلومات بداية الحفظ

```mermaid
sequenceDiagram
    participant U as المستخدم (مدير/معلم)
    participant UI as واجهة المستخدم
    participant API as API Server
    participant DB as قاعدة البيانات
    participant NS as نظام الإشعارات

    U->>UI: اختيار "إدخال بداية الحفظ"
    UI->>API: GET /api/students/{id}
    API->>DB: جلب بيانات التلميذ
    DB-->>API: بيانات التلميذ
    API-->>UI: بيانات التلميذ

    UI->>API: GET /api/quran/surahs
    API->>DB: جلب قائمة السور
    DB-->>API: قائمة السور
    API-->>UI: قائمة السور

    U->>UI: ملء نموذج بداية الحفظ
    Note over U,UI: تاريخ البداية، الجزء، السورة، المستوى

    U->>UI: حفظ البيانات
    UI->>API: POST /api/students/memorization-start
    
    API->>DB: التحقق من وجود سجل سابق
    DB-->>API: نتيجة التحقق
    
    alt إذا كان هناك سجل نشط
        API->>DB: تعطيل السجل السابق
        DB-->>API: تأكيد التعطيل
    end
    
    API->>DB: إنشاء سجل جديد
    DB-->>API: تأكيد الإنشاء
    
    API->>NS: إرسال إشعار لولي الأمر
    NS-->>API: تأكيد الإرسال
    
    API-->>UI: تأكيد النجاح
    UI-->>U: رسالة نجاح العملية
```

## 2. تسلسل عرض وطباعة بطاقة التلميذ

```mermaid
sequenceDiagram
    participant U as المستخدم
    participant UI as واجهة المستخدم
    participant API as API Server
    participant DB as قاعدة البيانات
    participant PDF as مولد PDF

    U->>UI: طلب عرض بطاقة التلميذ
    UI->>API: GET /api/students/{id}/card
    
    API->>DB: جلب البيانات الأساسية للتلميذ
    DB-->>API: البيانات الأساسية
    
    API->>DB: جلب معلومات بداية الحفظ
    DB-->>API: معلومات بداية الحفظ
    
    API->>DB: جلب تقدم الحفظ الحالي
    DB-->>API: تقدم الحفظ
    
    API->>DB: جلب الصورة الشخصية
    DB-->>API: رابط الصورة
    
    API->>DB: جلب معلومات الفصل وولي الأمر
    DB-->>API: معلومات إضافية
    
    API-->>UI: بيانات البطاقة الكاملة
    UI-->>U: عرض البطاقة
    
    alt إذا طلب المستخدم الطباعة
        U->>UI: طلب طباعة البطاقة
        UI->>PDF: تحويل البطاقة إلى PDF
        PDF-->>UI: ملف PDF
        UI-->>U: تحميل/طباعة PDF
    end
```

## 3. تسلسل إنشاء وصل التسجيل

```mermaid
sequenceDiagram
    participant A as المدير
    participant UI as واجهة المستخدم
    participant API as API Server
    participant DB as قاعدة البيانات
    participant PDF as مولد PDF
    participant NS as نظام الإشعارات

    A->>UI: تسجيل تلميذ جديد
    UI->>API: POST /api/students
    API->>DB: إنشاء سجل التلميذ
    DB-->>API: معرف التلميذ الجديد
    
    Note over API: توليد وصل التسجيل تلقائياً
    
    API->>API: توليد رقم وصل فريد
    API->>API: حساب رسوم التسجيل
    
    API->>DB: إنشاء سجل وصل التسجيل
    DB-->>API: تأكيد إنشاء الوصل
    
    API->>PDF: توليد PDF للوصل
    PDF-->>API: ملف PDF
    
    API->>DB: حفظ رابط ملف PDF
    DB-->>API: تأكيد الحفظ
    
    API->>NS: إرسال إشعار لولي الأمر
    Note over NS: الإشعار يحتوي على رابط الوصل
    NS-->>API: تأكيد الإرسال
    
    API-->>UI: تأكيد إنشاء التلميذ والوصل
    UI-->>A: عرض رسالة النجاح مع رابط الوصل
    
    alt إذا طلب المدير طباعة الوصل
        A->>UI: طلب طباعة الوصل
        UI->>API: GET /api/students/{id}/registration-receipt
        API->>DB: جلب بيانات الوصل
        DB-->>API: بيانات الوصل
        API-->>UI: ملف PDF للوصل
        UI-->>A: تحميل/طباعة الوصل
        
        API->>DB: تسجيل أن الوصل تم طباعته
        DB-->>API: تأكيد التسجيل
    end
```

## 4. تسلسل البحث والفلترة المتقدمة

```mermaid
sequenceDiagram
    participant U as المستخدم
    participant UI as واجهة المستخدم
    participant API as API Server
    participant DB as قاعدة البيانات
    participant Cache as ذاكرة التخزين المؤقت

    U->>UI: فتح صفحة إدارة التلاميذ
    UI->>API: GET /api/students?page=1
    
    API->>Cache: التحقق من وجود بيانات مخزنة
    alt إذا كانت البيانات موجودة ومحدثة
        Cache-->>API: البيانات المخزنة
    else
        API->>DB: جلب قائمة التلاميذ
        DB-->>API: قائمة التلاميذ
        API->>Cache: حفظ البيانات في الذاكرة المؤقتة
    end
    
    API-->>UI: قائمة التلاميذ
    UI-->>U: عرض القائمة
    
    U->>UI: تطبيق فلاتر البحث
    Note over U,UI: البحث بالاسم، الفصل، مستوى الحفظ، إلخ
    
    UI->>API: GET /api/students/search?filters={...}
    API->>DB: تنفيذ استعلام البحث المتقدم
    DB-->>API: نتائج البحث
    API-->>UI: النتائج المفلترة
    UI-->>U: عرض النتائج
    
    alt إذا طلب المستخدم تصدير النتائج
        U->>UI: طلب تصدير النتائج
        UI->>API: POST /api/students/export
        API->>DB: جلب البيانات التفصيلية
        DB-->>API: البيانات الكاملة
        API->>API: تحويل البيانات إلى Excel/PDF
        API-->>UI: ملف التصدير
        UI-->>U: تحميل الملف
    end
```

## 5. تسلسل تحديث معلومات بداية الحفظ

```mermaid
sequenceDiagram
    participant T as المعلم
    participant UI as واجهة المستخدم
    participant API as API Server
    participant DB as قاعدة البيانات
    participant Audit as سجل المراجعة

    T->>UI: اختيار تعديل بداية الحفظ
    UI->>API: GET /api/students/{id}/memorization-start
    API->>DB: جلب السجل الحالي
    DB-->>API: بيانات بداية الحفظ
    API-->>UI: البيانات الحالية
    UI-->>T: عرض النموذج مع البيانات

    T->>UI: تعديل البيانات
    T->>UI: حفظ التغييرات
    
    UI->>API: PUT /api/students/memorization-start/{id}
    
    API->>DB: جلب البيانات القديمة للمقارنة
    DB-->>API: البيانات القديمة
    
    API->>Audit: تسجيل التغييرات في سجل المراجعة
    Note over Audit: من قام بالتغيير، متى، ما تم تغييره
    Audit-->>API: تأكيد التسجيل
    
    API->>DB: تحديث البيانات
    DB-->>API: تأكيد التحديث
    
    API-->>UI: تأكيد النجاح
    UI-->>T: رسالة نجاح العملية
```

## ملاحظات التصميم

### 1. الأمان والتحقق
- كل طلب يتم التحقق من صلاحيات المستخدم
- تسجيل جميع العمليات في سجل المراجعة
- التحقق من صحة البيانات قبل الحفظ

### 2. الأداء والتحسين
- استخدام ذاكرة التخزين المؤقت للبيانات المتكررة
- تحميل البيانات بشكل تدريجي (Pagination)
- ضغط ملفات PDF قبل الإرسال

### 3. تجربة المستخدم
- رسائل تأكيد واضحة لكل عملية
- مؤشرات التحميل أثناء العمليات الطويلة
- إمكانية التراجع عن العمليات الحساسة

### 4. التعامل مع الأخطاء
- رسائل خطأ واضحة ومفيدة
- إعادة المحاولة التلقائية للعمليات الفاشلة
- تسجيل الأخطاء لأغراض التشخيص

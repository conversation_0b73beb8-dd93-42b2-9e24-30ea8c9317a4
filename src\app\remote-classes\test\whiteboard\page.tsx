'use client';

import React, { useState } from 'react';
import { FaArrowRight, FaChalkboard } from 'react-icons/fa';
import Link from 'next/link';
import Whiteboard from '@/components/remote-classes/Whiteboard/Whiteboard';

/**
 * Test page for whiteboard functionality
 */
const WhiteboardTestPage: React.FC = () => {
  const [whiteboardData, setWhiteboardData] = useState<string | undefined>(undefined);
  const [readOnly, setReadOnly] = useState(false);
  const [enableSync, setEnableSync] = useState(false);
  const [username, setUsername] = useState('مستخدم');

  // Handle whiteboard data changes
  const handleDataChange = (data: string) => {
    setWhiteboardData(data);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/remote-classes"
            className="inline-flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] mb-4"
          >
            <FaArrowRight className="ml-1" />
            <span>العودة إلى الفصول الافتراضية</span>
          </Link>

          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaChalkboard className="text-[var(--primary-color)]" />
            اختبار السبورة التفاعلية
          </h1>
          <p className="text-gray-600 mr-4">
            هذه الصفحة مخصصة لاختبار وظائف السبورة التفاعلية في الفصول الافتراضية
          </p>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-[var(--primary-color)]">إعدادات الاختبار</h2>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-gray-700">وضع القراءة فقط:</span>
                <button
                  onClick={() => setReadOnly(!readOnly)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                    readOnly ? 'bg-[var(--primary-color)]' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      readOnly ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-gray-700">تفعيل المزامنة:</span>
                <button
                  onClick={() => setEnableSync(!enableSync)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                    enableSync ? 'bg-[var(--primary-color)]' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      enableSync ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {enableSync && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-700">اسم المستخدم:</span>
                  <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] w-32"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Whiteboard */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <Whiteboard
            id="test-whiteboard"
            readOnly={readOnly}
            initialData={undefined}
            onDataChange={handleDataChange}
            height={500}
            width={800}
            enableSync={enableSync}
            roomId="test-room"
            userId={`user-${Math.random().toString(36).substr(2, 9)}`}
            username={username}
            userColor={`#${Math.floor(Math.random()*16777215).toString(16)}`}
          />
        </div>

        {/* Sync status */}
        {enableSync && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-blue-700">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              <p className="font-medium">وضع المزامنة نشط</p>
            </div>
            <p className="mt-2 text-blue-600 text-sm">
              هذا محاكاة للمزامنة في الوقت الفعلي. في التطبيق الفعلي، ستتم مزامنة السبورة مع المستخدمين الآخرين في نفس الغرفة.
            </p>
          </div>
        )}

        {/* Instructions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4">تعليمات الاختبار</h2>
            <ol className="list-decimal list-inside space-y-2 text-gray-700">
              <li>استخدم أدوات الرسم المختلفة (قلم، ممحاة، نص، أشكال)</li>
              <li>جرب تغيير الألوان وحجم الخط</li>
              <li>استخدم أزرار التراجع وإعادة الإجراء بعد إجراء تغييرات</li>
              <li>جرب تصدير السبورة كصورة</li>
              <li>قم بتفعيل وضع القراءة فقط للتحقق من عدم إمكانية التعديل</li>
            </ol>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-[var(--primary-color)] mb-4">الميزات المضافة</h2>
            <ul className="list-disc list-inside space-y-2 text-gray-700">
              <li>
                <span className="font-medium text-[var(--primary-color)]">التراجع وإعادة الإجراء:</span>
                <span className="block mr-6 text-sm">تتيح للمستخدم التراجع عن آخر إجراء أو إعادة إجراء تم التراجع عنه</span>
              </li>
              <li>
                <span className="font-medium text-[var(--primary-color)]">تصدير السبورة كصورة:</span>
                <span className="block mr-6 text-sm">يمكن تصدير محتوى السبورة كصورة PNG للحفظ أو المشاركة</span>
              </li>
              <li>
                <span className="font-medium text-[var(--primary-color)]">تاريخ التغييرات:</span>
                <span className="block mr-6 text-sm">يحتفظ النظام بتاريخ التغييرات للسماح بالتراجع وإعادة الإجراء</span>
              </li>
              <li>
                <span className="font-medium text-[var(--primary-color)]">وضع القراءة فقط:</span>
                <span className="block mr-6 text-sm">يمكن تعيين السبورة للعرض فقط دون السماح بالتعديل</span>
              </li>
              <li>
                <span className="font-medium text-[var(--primary-color)]">المزامنة في الوقت الفعلي:</span>
                <span className="block mr-6 text-sm">تتيح للمستخدمين العمل على نفس السبورة في وقت واحد</span>
              </li>
              <li>
                <span className="font-medium text-[var(--primary-color)]">مؤشرات المستخدمين:</span>
                <span className="block mr-6 text-sm">عرض مؤشرات المستخدمين الآخرين على السبورة</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhiteboardTestPage;

"use client";

import { useState, useEffect } from 'react';

interface UserData {
  id?: number;
  name: string;
  username: string;
  email?: string;
  role: string;
  phone?: string;
  status: 'active' | 'inactive';
  password: string;
  confirmPassword: string;
}

interface UserFormProps {
  user?: {
    id: number;
    name: string;
    username: string;
    email?: string;
    role: string;
    phone?: string;
    status: 'active' | 'inactive';
  };
  onSubmitAction: (userData: CreateUserData | UpdateUserData) => void;
  onCloseAction: () => void;
  isEdit?: boolean;
}

// نوع البيانات لإنشاء مستخدم جديد
interface CreateUserData {
  name: string;
  username: string;
  email?: string;
  role: string;
  phone?: string;
  status: 'active' | 'inactive';
  password: string;
}

// نوع البيانات لتحديث مستخدم
interface UpdateUserData {
  name: string;
  username?: string;
  email?: string;
  role?: string;
  phone?: string;
  status?: 'active' | 'inactive';
  password?: string;
}

// واجهة الدور
interface Role {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  isSystem: boolean;
  isActive: boolean;
}

export default function UserForm({ user, onSubmitAction, onCloseAction, isEdit = false }: UserFormProps) {
  const [formData, setFormData] = useState<UserData>({
    name: '',
    username: '',
    email: '',
    role: 'TEACHER',
    phone: '',
    status: 'active',
    password: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [roles, setRoles] = useState<Role[]>([]);
  const [loadingRoles, setLoadingRoles] = useState(true);

  // جلب الأدوار المتاحة
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/admin/roles');
      if (!response.ok) throw new Error('فشل في جلب الأدوار');
      const data = await response.json();
      setRoles(data.roles || []);
    } catch (error) {
      console.error('Error fetching roles:', error);
      // في حالة فشل جلب الأدوار، استخدم الأدوار الأساسية
      setRoles([
        { id: 1, name: 'ADMIN', displayName: 'مدير النظام', isSystem: true, isActive: true },
        { id: 2, name: 'TEACHER', displayName: 'معلم', isSystem: true, isActive: true },
        { id: 3, name: 'STUDENT', displayName: 'طالب', isSystem: true, isActive: true },
        { id: 4, name: 'PARENT', displayName: 'ولي أمر', isSystem: true, isActive: true },
        { id: 5, name: 'EMPLOYEE', displayName: 'موظف', isSystem: true, isActive: true },
        { id: 6, name: 'PENDING', displayName: 'في انتظار التعيين', isSystem: true, isActive: true }
      ]);
    } finally {
      setLoadingRoles(false);
    }
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  useEffect(() => {
    if (user) {
      setFormData(prevData => ({
        ...prevData,
        ...user,
        password: '',
        confirmPassword: ''
      }));
    }
  }, [user]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب';
    }

    if (!isEdit && !formData.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }

    if (!formData.role || formData.role.trim() === '') {
      newErrors.role = 'الدور مطلوب';
    }

    if (formData.email && formData.email.trim() !== '') {
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'البريد الإلكتروني غير صالح';
      }
    }

    if (!isEdit) {
      if (!formData.password) {
        newErrors.password = 'كلمة المرور مطلوبة';
      } else if (formData.password.length < 6) {
        newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'كلمة المرور غير متطابقة';
      }
    } else if (formData.password) {
      // في حالة التعديل وتم إدخال كلمة مرور
      if (formData.password.length < 6) {
        newErrors.password = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }

      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'كلمة المرور غير متطابقة';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      // إرسال الدور كما هو محدد في النموذج
      const roleToSend = formData.role;

      if (isEdit) {
        // إعداد بيانات التحديث
        const updateData: UpdateUserData = {
          name: formData.name,
        };

        // إضافة البيانات الاختيارية
        if (formData.email) updateData.email = formData.email;
        if (formData.phone) updateData.phone = formData.phone;
        if (formData.role) updateData.role = roleToSend; // استخدام الدور المحدد
        if (formData.status) updateData.status = formData.status;
        if (formData.password) updateData.password = formData.password;

        onSubmitAction(updateData);
      } else {
        // إعداد بيانات الإنشاء
        const createData: CreateUserData = {
          name: formData.name,
          username: formData.username,
          role: roleToSend, // استخدام الدور المحدد
          status: formData.status,
          password: formData.password
        };

        // إضافة البيانات الاختيارية
        if (formData.email) createData.email = formData.email;
        if (formData.phone) createData.phone = formData.phone;

        onSubmitAction(createData);
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* معلومات أساسية */}
      <div className="space-y-4">
        <div className="bg-gray-50 p-4 rounded-lg enhanced-section fade-in">
          <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2 section-title">
            <div className="icon-wrapper">
              <span>👤</span>
            </div>
            المعلومات الأساسية
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الاسم الكامل <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base enhanced-input"
                placeholder="أدخل الاسم الكامل"
                required
              />
              {errors.name && <p className="text-red-500 text-sm mt-1 flex items-center gap-1 error-message">
                <span className="text-xs">⚠️</span>
                {errors.name}
              </p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المستخدم {!isEdit && <span className="text-red-500">*</span>}
                {isEdit && <span className="text-gray-500 text-xs">(لا يمكن تغييره)</span>}
              </label>
              <input
                type="text"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base enhanced-input disabled:bg-gray-100 disabled:cursor-not-allowed"
                placeholder="أدخل اسم المستخدم"
                disabled={isEdit}
                required={!isEdit}
              />
              {errors.username && <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                <span className="text-xs">⚠️</span>
                {errors.username}
              </p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                  <span className="text-gray-500 text-xs ml-1">(اختياري)</span>
                </label>
                <input
                  type="email"
                  value={formData.email || ''}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base transition-colors bg-white"
                  placeholder="<EMAIL>"
                />
                {errors.email && <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
                  <span className="text-xs">⚠️</span>
                  {errors.email}
                </p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الهاتف
                  <span className="text-gray-500 text-xs ml-1">(اختياري)</span>
                </label>
                <input
                  type="tel"
                  value={formData.phone || ''}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base transition-colors bg-white"
                  placeholder="05xxxxxxxx"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* الدور والحالة */}
      <div className="bg-gray-50 p-4 rounded-lg enhanced-section slide-in">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2 section-title">
          <div className="icon-wrapper">
            <span>🔐</span>
          </div>
          الدور والحالة
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الدور <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base enhanced-select disabled:bg-gray-100"
              disabled={loadingRoles}
              required
            >
              {loadingRoles ? (
                <option value="">جاري تحميل الأدوار...</option>
              ) : (
                <>
                  <option value="">اختر الدور</option>
                  {roles.filter(role => role.isActive).map((role) => (
                    <option key={role.id} value={role.name}>
                      {role.displayName}
                      {role.isSystem && <span> (نظام)</span>}
                    </option>
                  ))}
                </>
              )}
            </select>
            {loadingRoles && (
              <p className="text-gray-500 text-sm mt-1 flex items-center gap-1 info-message loading-indicator">
                <div className="animate-spin w-3 h-3 border border-gray-400 border-t-transparent rounded-full"></div>
                جاري تحميل الأدوار...
              </p>
            )}
            {errors.role && <p className="text-red-500 text-sm mt-1 flex items-center gap-1 error-message">
              <span className="text-xs">⚠️</span>
              {errors.role}
            </p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الحالة <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base transition-colors bg-white"
              required
            >
              <option value="active">✅ نشط</option>
              <option value="inactive">❌ غير نشط</option>
            </select>
          </div>
        </div>
      </div>

      {/* كلمة المرور */}
      <div className="bg-gray-50 p-4 rounded-lg enhanced-section fade-in">
        <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2 section-title">
          <div className="icon-wrapper">
            <span>🔒</span>
          </div>
          {isEdit ? 'تغيير كلمة المرور (اختياري)' : 'كلمة المرور'}
        </h3>

        {isEdit && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <p className="text-blue-700 text-sm flex items-center gap-2">
              <span className="text-blue-500">ℹ️</span>
              اتركها فارغة للاحتفاظ بكلمة المرور الحالية
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {isEdit ? 'كلمة المرور الجديدة' : 'كلمة المرور'}
              {!isEdit && <span className="text-red-500">*</span>}
            </label>
            <input
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base transition-colors bg-white"
              placeholder={isEdit ? "كلمة المرور الجديدة" : "أدخل كلمة المرور"}
              required={!isEdit}
            />
            {errors.password && <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
              <span className="text-xs">⚠️</span>
              {errors.password}
            </p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تأكيد كلمة المرور
              {!isEdit && <span className="text-red-500">*</span>}
            </label>
            <input
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] text-base transition-colors bg-white"
              placeholder="أعد إدخال كلمة المرور"
              required={!isEdit}
            />
            {errors.confirmPassword && <p className="text-red-500 text-sm mt-1 flex items-center gap-1">
              <span className="text-xs">⚠️</span>
              {errors.confirmPassword}
            </p>}
          </div>
        </div>

        {!isEdit && (
          <div className="mt-3 bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-yellow-700 text-sm flex items-center gap-2">
              <span className="text-yellow-500">💡</span>
              يجب أن تحتوي كلمة المرور على 6 أحرف على الأقل
            </p>
          </div>
        )}
      </div>

      {/* أزرار الإجراءات */}
      <div className="flex flex-col sm:flex-row gap-3 pt-6">
        <button
          type="button"
          onClick={onCloseAction}
          className="w-full sm:w-auto px-6 py-3 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 enhanced-button font-medium flex items-center justify-center gap-2"
        >
          <span>❌</span>
          إلغاء
        </button>
        <button
          type="submit"
          disabled={loadingRoles}
          className="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] text-white rounded-lg hover:from-[var(--secondary-color)] hover:to-[var(--primary-color)] enhanced-button font-medium shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
        >
          {loadingRoles ? (
            <>
              <div className="animate-spin w-4 h-4 border border-white border-t-transparent rounded-full"></div>
              جاري التحميل...
            </>
          ) : (
            <>
              <span>{isEdit ? '💾' : '➕'}</span>
              {isEdit ? 'حفظ التغييرات' : 'إضافة المستخدم'}
            </>
          )}
        </button>
      </div>
    </form>
  );
}
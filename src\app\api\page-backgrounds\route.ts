import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/page-backgrounds - جلب جميع خلفيات الصفحات
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const pageName = searchParams.get('pageName');
    const isActive = searchParams.get('isActive');

    const where: any = {};
    
    if (pageName) {
      where.pageName = pageName;
    }
    
    if (isActive !== null) {
      where.isActive = isActive === 'true';
    }

    const backgrounds = await prisma.pageBackground.findMany({
      where,
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: backgrounds,
      message: 'تم جلب خلفيات الصفحات بنجاح'
    });
  } catch (error) {
    console.error('Error fetching page backgrounds:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ أثناء جلب خلفيات الصفحات' 
      },
      { status: 500 }
    );
  }
}

// POST /api/page-backgrounds - إضافة خلفية صفحة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      pageName,
      displayName,
      imageUrl,
      overlayColor,
      overlayOpacity,
      position,
      size,
      repeat,
      attachment,
      isActive,
      priority
    } = body;

    // التحقق من البيانات المطلوبة
    if (!pageName || !displayName) {
      return NextResponse.json({
        success: false,
        error: 'اسم الصفحة والاسم المعروض مطلوبان'
      }, { status: 400 });
    }

    // التحقق من عدم وجود خلفية بنفس اسم الصفحة
    const existingBackground = await prisma.pageBackground.findUnique({
      where: { pageName }
    });

    if (existingBackground) {
      return NextResponse.json({
        success: false,
        error: 'يوجد خلفية بنفس اسم الصفحة مسبقاً'
      }, { status: 400 });
    }

    const background = await prisma.pageBackground.create({
      data: {
        pageName,
        displayName,
        imageUrl: imageUrl || null,
        overlayColor: overlayColor || null,
        overlayOpacity: overlayOpacity || 0.5,
        position: position || 'center',
        size: size || 'cover',
        repeat: repeat || 'no-repeat',
        attachment: attachment || 'scroll',
        isActive: isActive !== undefined ? isActive : true,
        priority: priority || 0
      }
    });

    return NextResponse.json({
      success: true,
      data: background,
      message: 'تم إضافة خلفية الصفحة بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating page background:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ أثناء إضافة خلفية الصفحة' 
      },
      { status: 500 }
    );
  }
}

// PUT /api/page-backgrounds - تحديث خلفية صفحة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      id,
      pageName,
      displayName,
      imageUrl,
      overlayColor,
      overlayOpacity,
      position,
      size,
      repeat,
      attachment,
      isActive,
      priority
    } = body;

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الخلفية مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود الخلفية
    const existingBackground = await prisma.pageBackground.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingBackground) {
      return NextResponse.json({
        success: false,
        error: 'خلفية الصفحة غير موجودة'
      }, { status: 404 });
    }

    // التحقق من عدم تكرار اسم الصفحة (إذا تم تغييره)
    if (pageName && pageName !== existingBackground.pageName) {
      const duplicateBackground = await prisma.pageBackground.findUnique({
        where: { pageName }
      });

      if (duplicateBackground) {
        return NextResponse.json({
          success: false,
          error: 'يوجد خلفية بنفس اسم الصفحة مسبقاً'
        }, { status: 400 });
      }
    }

    const updatedBackground = await prisma.pageBackground.update({
      where: { id: parseInt(id) },
      data: {
        ...(pageName && { pageName }),
        ...(displayName && { displayName }),
        ...(imageUrl !== undefined && { imageUrl }),
        ...(overlayColor !== undefined && { overlayColor }),
        ...(overlayOpacity !== undefined && { overlayOpacity }),
        ...(position && { position }),
        ...(size && { size }),
        ...(repeat && { repeat }),
        ...(attachment && { attachment }),
        ...(isActive !== undefined && { isActive }),
        ...(priority !== undefined && { priority })
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedBackground,
      message: 'تم تحديث خلفية الصفحة بنجاح'
    });
  } catch (error) {
    console.error('Error updating page background:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ أثناء تحديث خلفية الصفحة' 
      },
      { status: 500 }
    );
  }
}

// DELETE /api/page-backgrounds - حذف خلفية صفحة
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الخلفية مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود الخلفية
    const existingBackground = await prisma.pageBackground.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingBackground) {
      return NextResponse.json({
        success: false,
        error: 'خلفية الصفحة غير موجودة'
      }, { status: 404 });
    }

    await prisma.pageBackground.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف خلفية الصفحة بنجاح'
    });
  } catch (error) {
    console.error('Error deleting page background:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ أثناء حذف خلفية الصفحة' 
      },
      { status: 500 }
    );
  }
}

'use client';

import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';
import {
  FolderPlus,
  Upload,
  Grid3X3,
  Image as ImageIcon,
  PlusCircle,
  Edit
} from 'lucide-react';

export default function StudentImagesPage() {
  const router = useRouter();

  return (
    <OptimizedProtectedRoute requiredPermission="admin.student-images.view">
      <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">إدارة صور الطلاب</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* رفع صور جديدة */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="h-5 w-5 ml-2" />
              رفع صور جديدة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-muted-foreground">
              رفع صور جديدة للطلاب وإضافتها إلى الألبومات
            </p>
            <QuickActionButtons
              entityType="student-images"
              actions={[
                {
                  key: 'upload',
                  label: 'رفع صور جديدة',
                  icon: <PlusCircle className="h-4 w-4" />,
                  onClick: () => router.push('/admin/student-images/upload'),
                  variant: 'primary',
                  permission: 'admin.student-images.upload'
                }
              ]}
              className="w-full"
            />
          </CardContent>
        </Card>

        {/* إدارة الألبومات */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FolderPlus className="h-5 w-5 ml-2" />
              إدارة الألبومات
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-muted-foreground">
              إنشاء وتعديل وحذف ألبومات الصور وتنظيم الصور فيها
            </p>
            <QuickActionButtons
              entityType="student-images"
              actions={[
                {
                  key: 'albums',
                  label: 'إدارة الألبومات',
                  icon: <FolderPlus className="h-4 w-4" />,
                  onClick: () => router.push('/admin/student-images/albums'),
                  variant: 'primary',
                  permission: 'admin.student-images.albums'
                }
              ]}
              className="w-full"
            />
          </CardContent>
        </Card>

        {/* معرض الصور */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Grid3X3 className="h-5 w-5 ml-2" />
              معرض الصور
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-muted-foreground">
              عرض وتصفح وتحرير جميع صور الطلاب في معرض متكامل
            </p>
            <QuickActionButtons
              entityType="student-images"
              actions={[
                {
                  key: 'gallery',
                  label: 'عرض المعرض',
                  icon: <ImageIcon className="h-4 w-4" />,
                  onClick: () => router.push('/admin/student-images/gallery'),
                  variant: 'primary',
                  permission: 'admin.student-images.gallery'
                }
              ]}
              className="w-full"
            />
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* الصور الحالية */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <ImageIcon className="h-5 w-5 ml-2" />
              الصور الحالية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-muted-foreground">
              عرض وإدارة الصور الحالية للطلاب (الطريقة القديمة)
            </p>
            <QuickActionButtons
              entityType="student-images"
              actions={[
                {
                  key: 'legacy',
                  label: 'إدارة الصور الحالية',
                  icon: <Edit className="h-4 w-4" />,
                  onClick: () => router.push('/admin/student-images/legacy'),
                  variant: 'secondary',
                  permission: 'admin.student-images.legacy'
                }
              ]}
              className="w-full"
            />
          </CardContent>
        </Card>


      </div>

      <Card>
        <CardHeader>
          <CardTitle>ميزات نظام إدارة صور الطلاب</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">إدارة الصور</h3>
              <ul className="space-y-2 list-disc list-inside text-muted-foreground">
                <li>رفع صور متعددة للطلاب دفعة واحدة</li>
                <li>عرض الصور وتنظيمها</li>
                <li>تنظيم الصور في ألبومات</li>
                <li>تعيين صورة الملف الشخصي للطالب</li>
                <li>إضافة وصف لكل صورة</li>
              </ul>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">تنظيم الألبومات</h3>
              <ul className="space-y-2 list-disc list-inside text-muted-foreground">
                <li>إنشاء ألبومات متعددة لتنظيم الصور</li>
                <li>تخصيص صورة غلاف لكل ألبوم</li>
                <li>تصنيف الصور حسب الطالب أو الألبوم</li>
                <li>البحث في الصور والألبومات</li>
                <li>عرض الصور في معرض متكامل</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </OptimizedProtectedRoute>
  );
}

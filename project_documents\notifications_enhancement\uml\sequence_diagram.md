# مخطط التسلسل - نظام الإشعارات المحسن

## وصف المخطط
يوضح هذا المخطط تدفق العمليات في نظام الإشعارات المحسن للإرسال الجماعي والمخصص.

## مخطط التسلسل للإرسال الجماعي

```mermaid
sequenceDiagram
    participant Admin as المدير/المعلم
    participant UI as واجهة المستخدم
    participant API as API الإشعارات
    participant GroupAPI as API المجموعات
    participant DB as قاعدة البيانات
    participant NotificationService as خدمة الإشعارات

    Note over Admin, NotificationService: إنشاء إشعار جماعي

    Admin->>UI: فتح نموذج إنشاء إشعار
    UI->>GroupAPI: جلب أنواع المجموعات المتاحة
    GroupAPI->>DB: استعلام أدوار المستخدمين
    DB-->>GroupAPI: قائمة الأدوار
    GroupAPI-->>UI: أنواع المجموعات

    Admin->>UI: اختيار نوع المجموعة (مثلاً: المعلمين فقط)
    UI->>GroupAPI: جلب معاينة المستلمين
    GroupAPI->>DB: استعلام المستخدمين حسب الدور
    DB-->>GroupAPI: قائمة المستخدمين
    GroupAPI-->>UI: عدد ومعاينة المستلمين

    Admin->>UI: كتابة محتوى الإشعار وإرساله
    UI->>API: POST /api/notifications/bulk
    
    API->>DB: إنشاء مجموعة إشعارات
    DB-->>API: معرف المجموعة
    
    API->>GroupAPI: جلب قائمة المستلمين النهائية
    GroupAPI->>DB: استعلام المستخدمين المستهدفين
    DB-->>GroupAPI: قائمة المستلمين
    GroupAPI-->>API: قائمة المستلمين

    loop لكل مستلم
        API->>DB: إنشاء إشعار فردي
        API->>DB: إنشاء سجل مستلم
    end

    API->>NotificationService: تشغيل خدمة الإرسال
    NotificationService->>DB: تحديث حالة التسليم
    
    API-->>UI: تأكيد الإرسال الناجح
    UI-->>Admin: عرض رسالة نجاح مع الإحصائيات
```

## مخطط التسلسل لمعاينة المستلمين

```mermaid
sequenceDiagram
    participant User as المستخدم
    participant UI as واجهة المستخدم
    participant API as API المجموعات
    participant DB as قاعدة البيانات

    Note over User, DB: معاينة المستلمين قبل الإرسال

    User->>UI: اختيار نوع المجموعة
    UI->>API: GET /api/user-groups/preview
    
    alt إذا كان النوع "جميع المستخدمين"
        API->>DB: SELECT COUNT(*) FROM users WHERE active = true
        DB-->>API: العدد الإجمالي
    else إذا كان النوع "حسب الدور"
        API->>DB: SELECT COUNT(*) FROM users WHERE role = ?
        DB-->>API: عدد المستخدمين بالدور المحدد
    else إذا كان النوع "اختيار مخصص"
        API->>DB: SELECT * FROM users WHERE id IN (?)
        DB-->>API: قائمة المستخدمين المحددين
    end

    API-->>UI: بيانات المعاينة (العدد + عينة من الأسماء)
    UI-->>User: عرض معاينة المستلمين
```

## مخطط التسلسل لتتبع الإحصائيات

```mermaid
sequenceDiagram
    participant System as النظام
    participant StatsService as خدمة الإحصائيات
    participant DB as قاعدة البيانات
    participant Admin as المدير

    Note over System, Admin: تحديث وعرض إحصائيات الإشعارات

    System->>StatsService: تشغيل مهمة حساب الإحصائيات (كل ساعة)
    
    StatsService->>DB: جلب الإشعارات الجماعية الحديثة
    DB-->>StatsService: قائمة الإشعارات
    
    loop لكل إشعار جماعي
        StatsService->>DB: حساب إجمالي المستلمين
        StatsService->>DB: حساب عدد المسلم إليهم
        StatsService->>DB: حساب عدد المقروءة
        
        StatsService->>DB: تحديث جدول الإحصائيات
    end

    Admin->>UI: طلب عرض إحصائيات الإشعارات
    UI->>API: GET /api/notifications/stats
    API->>DB: جلب الإحصائيات المحسوبة
    DB-->>API: بيانات الإحصائيات
    API-->>UI: الإحصائيات
    UI-->>Admin: عرض التقارير والرسوم البيانية
```

## مخطط التسلسل للإشعارات التلقائية

```mermaid
sequenceDiagram
    participant Event as حدث النظام
    participant Trigger as مشغل الإشعارات
    participant Template as خدمة القوالب
    participant API as API الإشعارات
    participant DB as قاعدة البيانات

    Note over Event, DB: إرسال إشعارات تلقائية عند الأحداث

    Event->>Trigger: حدث جديد (مثل: تسجيل طالب جديد)
    Trigger->>Template: البحث عن قالب مناسب
    Template->>DB: جلب قالب الإشعار
    DB-->>Template: بيانات القالب
    
    Template->>Template: ملء المتغيرات الديناميكية
    Template-->>Trigger: محتوى الإشعار الجاهز
    
    Trigger->>API: إنشاء إشعار جماعي
    API->>DB: تحديد المستلمين حسب نوع الحدث
    
    loop لكل مستلم مستهدف
        API->>DB: إنشاء إشعار فردي
    end
    
    API-->>Trigger: تأكيد الإنشاء
    Trigger-->>Event: اكتمال المعالجة
```

## الميزات الرئيسية في التدفق

### 1. المعاينة الذكية
- عرض عدد المستلمين قبل الإرسال
- عينة من أسماء المستلمين للتأكيد
- تحديث فوري عند تغيير المعايير

### 2. الإرسال المتوازي
- إنشاء إشعارات متعددة بكفاءة
- تتبع حالة كل إشعار منفرد
- معالجة الأخطاء لكل مستلم

### 3. الإحصائيات الفورية
- حساب معدلات التسليم والقراءة
- تحديث دوري للإحصائيات
- تقارير مفصلة للمديرين

### 4. الإشعارات التلقائية
- ربط الأحداث بالإشعارات
- استخدام القوالب الديناميكية
- تخصيص المستلمين حسب نوع الحدث

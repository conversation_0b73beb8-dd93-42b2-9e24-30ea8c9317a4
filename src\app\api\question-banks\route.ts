import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ActivityLogger, ActivityType } from "@/lib/activity-logger";

// GET /api/question-banks
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const subjectId = searchParams.get('subjectId');

    const where = subjectId ? { subjectId: parseInt(subjectId) } : {};

    const questionBanks = await prisma.questionBank.findMany({
      where,
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        },
        _count: {
          select: {
            questions: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json({
      data: questionBanks,
      success: true,
      message: 'تم جلب بنوك الأسئلة بنجاح'
    });
  } catch (error) {
    console.error('Error fetching question banks:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب بنوك الأسئلة',
      success: false
    }, { status: 500 });
  }
}

// POST /api/question-banks
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, subjectId } = body;

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json({
        error: 'اسم بنك الأسئلة مطلوب',
        success: false
      }, { status: 400 });
    }

    const questionBank = await prisma.questionBank.create({
      data: {
        name,
        description,
        subjectId: subjectId ? parseInt(subjectId) : null
      },
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.GENERAL,
      `تم إنشاء بنك أسئلة جديد: ${name}`
    );

    return NextResponse.json({
      data: questionBank,
      success: true,
      message: 'تم إنشاء بنك الأسئلة بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating question bank:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إنشاء بنك الأسئلة',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/question-banks
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, description, subjectId } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !name) {
      return NextResponse.json({
        error: 'معرف واسم بنك الأسئلة مطلوبان',
        success: false
      }, { status: 400 });
    }

    const questionBank = await prisma.questionBank.update({
      where: { id: parseInt(id) },
      data: {
        name,
        description,
        subjectId: subjectId ? parseInt(subjectId) : null
      },
      include: {
        subject: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.UPDATE,
      `تم تحديث بنك الأسئلة: ${name}`
    );

    return NextResponse.json({
      data: questionBank,
      success: true,
      message: 'تم تحديث بنك الأسئلة بنجاح'
    });
  } catch (error) {
    console.error('Error updating question bank:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث بنك الأسئلة',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/question-banks
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف بنك الأسئلة مطلوب',
        success: false
      }, { status: 400 });
    }

    // الحصول على اسم بنك الأسئلة قبل الحذف
    const questionBank = await prisma.questionBank.findUnique({
      where: { id: parseInt(id) }
    });

    if (!questionBank) {
      return NextResponse.json({
        error: 'بنك الأسئلة غير موجود',
        success: false
      }, { status: 404 });
    }

    // حذف بنك الأسئلة
    await prisma.questionBank.delete({
      where: { id: parseInt(id) }
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.GENERAL,
      `تم حذف بنك الأسئلة: ${questionBank.name}`
    );

    return NextResponse.json({
      success: true,
      message: 'تم حذف بنك الأسئلة بنجاح'
    });
  } catch (error) {
    console.error('Error deleting question bank:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف بنك الأسئلة',
      success: false
    }, { status: 500 });
  }
}

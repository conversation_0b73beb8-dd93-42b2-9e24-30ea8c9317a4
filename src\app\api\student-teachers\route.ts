import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

// GET /api/student-teachers - جلب المعلمين الذين يدرسون للطالب المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'STUDENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات الطالب
    const student = await prisma.student.findFirst({
      where: {
        username: userData.username
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات الطالب" },
        { status: 404 }
      );
    }

    // جلب المواد الدراسية للفصل الذي ينتمي إليه الطالب
    const classSubjects = await prisma.classSubject.findMany({
      where: {
        classeId: student.classeId ?? undefined
      },
      include: {
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    // استخراج المعلمين الفريدين
    const teachersMap = new Map();

    classSubjects.forEach(cs => {
      const teacher = cs.teacherSubject.teacher;

      if (!teachersMap.has(teacher.id)) {
        teachersMap.set(teacher.id, {
          id: teacher.id,
          name: teacher.name,
          phone: teacher.phone || '',
          specialization: teacher.specialization,
          subjects: []
        });
      }

      // إضافة المادة إلى قائمة المواد التي يدرسها المعلم
      const teacherData = teachersMap.get(teacher.id);
      if (!teacherData.subjects.some((s: { id: number }) => s.id === cs.teacherSubject.subject.id)) {
        teacherData.subjects.push({
          id: cs.teacherSubject.subject.id,
          name: cs.teacherSubject.subject.name
        });
      }
    });

    // تحويل Map إلى مصفوفة
    const teachers = Array.from(teachersMap.values());

    return NextResponse.json({
      teachers,
      message: "تم جلب بيانات المعلمين بنجاح"
    });
  } catch (error) {
    console.error('Error fetching student teachers:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب بيانات المعلمين" },
      { status: 500 }
    );
  }
}

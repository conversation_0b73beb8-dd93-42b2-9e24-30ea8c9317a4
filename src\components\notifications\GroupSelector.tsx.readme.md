# مكون اختيار المجموعات - GroupSelector

## الوصف
مكون React متقدم لاختيار المجموعات المستهدفة للإشعارات الجماعية. يدعم أنواع مختلفة من المجموعات ويوفر واجهة سهلة الاستخدام لاختيار المستلمين.

## الميزات الرئيسية

### 1. أنواع المجموعات المدعومة
- **جميع المستخدمين**: إرسال لجميع المستخدمين النشطين
- **حسب الدور**: إرسال للمعلمين، الطلاب، أولياء الأمور، إلخ
- **اختيار مخصص**: اختيار مستخدمين محددين بأسمائهم

### 2. البحث التفاعلي
- بحث فوري في المستخدمين
- عرض النتائج مع تفاصيل المستخدم
- دعم البحث بالاسم أو اسم المستخدم

### 3. معاينة المستلمين
- عرض عدد المستلمين المتوقع
- معاينة عينة من المستلمين
- تأكيد الاختيار قبل الإرسال

## واجهة المكون (Props)

```typescript
interface GroupSelectorProps {
  onSelectionChange: (selection: {
    groupType: string;
    targetRole?: string;
    targetUserIds?: number[];
    recipientCount: number;
  }) => void;
  disabled?: boolean;
}
```

### المعاملات
- `onSelectionChange`: دالة callback تُستدعى عند تغيير الاختيار
- `disabled`: تعطيل المكون (افتراضي: false)

## الاستخدام

```tsx
import GroupSelector from '@/components/notifications/GroupSelector';

function NotificationForm() {
  const [groupSelection, setGroupSelection] = useState(null);

  const handleGroupSelectionChange = (selection) => {
    setGroupSelection(selection);
    console.log('المجموعة المختارة:', selection);
  };

  return (
    <GroupSelector 
      onSelectionChange={handleGroupSelectionChange}
      disabled={false}
    />
  );
}
```

## أنواع البيانات المُرجعة

### للمجموعة "جميع المستخدمين"
```javascript
{
  groupType: 'ALL_USERS',
  recipientCount: 150
}
```

### للمجموعة "حسب الدور"
```javascript
{
  groupType: 'BY_ROLE',
  targetRole: 'TEACHER',
  recipientCount: 25
}
```

### للاختيار المخصص
```javascript
{
  groupType: 'CUSTOM_SELECTION',
  targetUserIds: [1, 5, 12, 25],
  recipientCount: 4
}
```

## التبعيات
- `axios`: لطلبات API
- `react-icons/fa`: للأيقونات
- `/api/user-groups`: API جلب خيارات المجموعات
- `/api/user-groups/preview`: API معاينة المستلمين والبحث

## الحالات والتفاعلات

### 1. التحميل الأولي
- جلب خيارات المجموعات من الخادم
- عرض مؤشر التحميل
- معالجة أخطاء الشبكة

### 2. اختيار المجموعة
- تحديث الواجهة فوراً
- إرسال البيانات للمكون الأب
- إعادة تعيين الاختيارات السابقة

### 3. البحث في المستخدمين
- تأخير البحث (300ms debounce)
- عرض مؤشر التحميل أثناء البحث
- عرض النتائج في قائمة قابلة للتمرير

### 4. معاينة المستلمين
- نافذة منبثقة تعرض تفاصيل المستلمين
- عرض عينة من الأسماء
- إحصائيات العدد الإجمالي

## التصميم والأسلوب
- تصميم متجاوب يعمل على جميع الأحجام
- ألوان متسقة مع نظام التصميم
- أيقونات واضحة لكل نوع مجموعة
- تأثيرات hover وانتقالات سلسة

## معالجة الأخطاء
- عرض رسائل خطأ واضحة
- إعادة المحاولة التلقائية
- حالات fallback للبيانات المفقودة

## الأداء
- تحميل البيانات عند الحاجة فقط
- تخزين مؤقت للنتائج
- تحسين عدد طلبات API

## إمكانية الوصول
- دعم التنقل بلوحة المفاتيح
- تسميات واضحة للقارئات الشاشة
- تباين ألوان مناسب

## ملاحظات التطوير
- المكون مُحسن للأداء مع useEffect و useState
- يدعم TypeScript بالكامل
- قابل للتخصيص والتوسيع
- متوافق مع معايير React الحديثة

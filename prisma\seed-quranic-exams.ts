#!/usr/bin/env ts-node

import { PrismaClient } from '@prisma/client';
import { seedQuranicSchoolExams } from './seeders/quranic-school-exams';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 بدء تنفيذ بذور الامتحانات للمدرسة القرآنية...');
  console.log('=' .repeat(60));

  try {
    // التحقق من وجود البيانات الأساسية
    const studentsCount = await prisma.student.count();
    const classesCount = await prisma.classe.count();
    
    if (studentsCount === 0) {
      console.log('⚠️  تحذير: لا يوجد طلاب في قاعدة البيانات!');
      console.log('💡 يرجى تشغيل البذور الأساسية أولاً لإنشاء الطلاب والفصول.');
      console.log('   npm run seed:basic');
      return;
    }

    if (classesCount === 0) {
      console.log('⚠️  تحذير: لا يوجد فصول في قاعدة البيانات!');
      console.log('💡 يرجى تشغيل البذور الأساسية أولاً لإنشاء الفصول.');
      console.log('   npm run seed:basic');
      return;
    }

    console.log(`✅ تم العثور على ${studentsCount} طالب و ${classesCount} فصل`);
    console.log('');

    // تنفيذ بذور الامتحانات
    await seedQuranicSchoolExams();

    console.log('');
    console.log('=' .repeat(60));
    console.log('🎉 تم إنشاء جميع الامتحانات الفصلية بنجاح!');
    
    // إحصائيات نهائية
    const examsCount = await prisma.exam.count();
    const examPointsCount = await prisma.exam_points.count();
    const subjectsCount = await prisma.subject.count();
    const examTypesCount = await prisma.examType.count();

    console.log('');
    console.log('📊 إحصائيات البيانات المُنشأة:');
    console.log(`   📚 المواد: ${subjectsCount}`);
    console.log(`   📝 أنواع الامتحانات: ${examTypesCount}`);
    console.log(`   🎯 الامتحانات: ${examsCount}`);
    console.log(`   📈 نقاط الامتحانات: ${examPointsCount}`);
    console.log(`   👥 الطلاب: ${studentsCount}`);
    console.log(`   🏫 الفصول: ${classesCount}`);

    console.log('');
    console.log('🔍 لعرض كشف درجات طالب:');
    console.log('   1. اذهب إلى: /admin/evaluation/student-report');
    console.log('   2. اختر طالب من القائمة');
    console.log('   3. اضغط "إنشاء كشف الدرجات"');
    console.log('   4. اطبع الكشف أو احفظه كـ PDF');

    console.log('');
    console.log('📋 الامتحانات المُنشأة تشمل:');
    console.log('   🕌 حفظ القرآن الكريم');
    console.log('   📖 تلاوة وتجويد');
    console.log('   📚 التفسير');
    console.log('   🌟 الحديث الشريف');
    console.log('   ⚖️  الفقه الإسلامي');
    console.log('   👤 السيرة النبوية');
    console.log('   💎 العقيدة الإسلامية');
    console.log('   🌸 الأخلاق والآداب');

    console.log('');
    console.log('📅 الفصول الدراسية:');
    console.log('   🍂 الفصل الأول: سبتمبر - ديسمبر 2024');
    console.log('   ❄️  الفصل الثاني: يناير - مارس 2025');
    console.log('   🌸 الفصل الثالث: أبريل - يونيو 2025');

  } catch (error) {
    console.error('❌ خطأ في تنفيذ البذور:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// تنفيذ الدالة الرئيسية
main()
  .catch((e) => {
    console.error('💥 خطأ غير متوقع:', e);
    process.exit(1);
  });

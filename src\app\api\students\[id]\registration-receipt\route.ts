import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// GET /api/students/[id]/registration-receipt - جلب وصولات التسجيل للتلميذ
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.students.receipt.manage');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { error: permissionCheck.message || 'غير مصرح بالوصول' },
        { status: permissionCheck.status || 401 }
      );
    }

    const studentId = parseInt(params.id);

    if (isNaN(studentId)) {
      return NextResponse.json(
        { error: 'معرف التلميذ غير صحيح' },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const receiptId = searchParams.get('receiptId');

    // جلب إعدادات المدرسة
    const schoolSettings = await prisma.systemSettings.findUnique({
      where: { key: 'SITE_SETTINGS' }
    });

    const defaultSchoolInfo = {
      siteName: 'نظام برهان للقرآن الكريم',
      siteDescription: 'منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد',
      logoUrl: '/logo.svg',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+213 123 456 789',
        address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر'
      }
    };

    const schoolInfo = schoolSettings
      ? JSON.parse(schoolSettings.value)
      : defaultSchoolInfo;

    // إذا تم تحديد معرف وصل معين
    if (receiptId) {
      const receipt = await prisma.studentRegistrationReceipt.findFirst({
        where: {
          id: parseInt(receiptId),
          studentId: studentId
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              username: true,
              age: true,
              phone: true,
              guardian: {
                select: {
                  id: true,
                  name: true,
                  phone: true,
                  email: true,
                  address: true
                }
              },
              classe: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      if (!receipt) {
        return NextResponse.json(
          { error: 'وصل التسجيل غير موجود' },
          { status: 404 }
        );
      }

      // تحديث معلومات المدرسة في بيانات الوصل
      const updatedReceiptData = {
        ...receipt.receiptData,
        schoolInfo: {
          name: schoolInfo.siteName,
          description: schoolInfo.siteDescription,
          logoUrl: schoolInfo.logoUrl,
          address: schoolInfo.contactInfo?.address || 'غير محدد',
          phone: schoolInfo.contactInfo?.phone || 'غير محدد',
          email: schoolInfo.contactInfo?.email || 'غير محدد'
        }
      };

      // تنسيق البيانات للعرض
      const formattedReceipt = {
        ...receipt,
        receiptData: updatedReceiptData,
        issueDate: new Date(receipt.issueDate).toLocaleDateString('fr-FR'),
        printedAt: receipt.printedAt
          ? new Date(receipt.printedAt).toLocaleDateString('fr-FR')
          : null,
        createdAt: new Date(receipt.createdAt).toLocaleDateString('fr-FR'),
        updatedAt: new Date(receipt.updatedAt).toLocaleDateString('fr-FR')
      };

      return NextResponse.json({
        success: true,
        data: formattedReceipt
      });
    }

    // جلب جميع وصولات التسجيل للتلميذ
    const receipts = await prisma.studentRegistrationReceipt.findMany({
      where: { studentId: studentId },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            username: true
          }
        }
      },
      orderBy: {
        issueDate: 'desc'
      }
    });

    // تنسيق التواريخ
    const formattedReceipts = receipts.map(receipt => ({
      ...receipt,
      issueDate: new Date(receipt.issueDate).toLocaleDateString('fr-FR'),
      printedAt: receipt.printedAt
        ? new Date(receipt.printedAt).toLocaleDateString('fr-FR')
        : null,
      createdAt: new Date(receipt.createdAt).toLocaleDateString('fr-FR'),
      updatedAt: new Date(receipt.updatedAt).toLocaleDateString('fr-FR')
    }));

    return NextResponse.json({
      success: true,
      data: formattedReceipts
    });

  } catch (error) {
    console.error('Error fetching registration receipts:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب وصولات التسجيل' },
      { status: 500 }
    );
  }
}

// POST /api/students/[id]/registration-receipt - إنشاء وصل تسجيل جديد
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.students.receipt.manage');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { error: permissionCheck.message || 'غير مصرح بالوصول' },
        { status: permissionCheck.status || 401 }
      );
    }

    const studentId = parseInt(params.id);

    if (isNaN(studentId)) {
      return NextResponse.json(
        { error: 'معرف التلميذ غير صحيح' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const {
      registrationFee,
      paymentStatus = 'PENDING',
      notes
    } = body;

    // لا حاجة للتحقق من الرسوم لأن التسجيل مجاني

    // جلب إعدادات المدرسة
    const schoolSettings = await prisma.systemSettings.findUnique({
      where: { key: 'SITE_SETTINGS' }
    });

    const defaultSchoolInfo = {
      siteName: 'نظام برهان للقرآن الكريم',
      siteDescription: 'منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد',
      logoUrl: '/logo.svg',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+213 123 456 789',
        address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر'
      }
    };

    const schoolInfo = schoolSettings
      ? JSON.parse(schoolSettings.value)
      : defaultSchoolInfo;

    // التحقق من وجود التلميذ
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: {
        guardian: {
          select: {
            name: true,
            phone: true,
            email: true,
            address: true
          }
        },
        classe: {
          select: {
            name: true
          }
        }
      }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'التلميذ غير موجود' },
        { status: 404 }
      );
    }

    // توليد رقم وصل فريد
    const currentYear = new Date().getFullYear();
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');

    // البحث عن آخر رقم وصل في الشهر الحالي
    const lastReceipt = await prisma.studentRegistrationReceipt.findFirst({
      where: {
        receiptNumber: {
          startsWith: `REG-${currentYear}${currentMonth}`
        }
      },
      orderBy: {
        receiptNumber: 'desc'
      }
    });

    let nextNumber = 1;
    if (lastReceipt) {
      const lastNumber = parseInt(lastReceipt.receiptNumber.split('-')[1].slice(6));
      nextNumber = lastNumber + 1;
    }

    const receiptNumber = `REG-${currentYear}${currentMonth}${String(nextNumber).padStart(4, '0')}`;

    // إعداد بيانات الوصل للحفظ كـ JSON
    const receiptData = {
      student: {
        id: student.id,
        name: student.name,
        username: student.username,
        age: student.age,
        phone: student.phone
      },
      guardian: student.guardian,
      classe: student.classe,
      receiptNumber,
      issueDate: new Date().toLocaleDateString('fr-FR'),
      registrationFee,
      paymentStatus,
      notes,
      schoolInfo: {
        name: schoolInfo.siteName,
        description: schoolInfo.siteDescription,
        logoUrl: schoolInfo.logoUrl,
        address: schoolInfo.contactInfo?.address || 'غير محدد',
        phone: schoolInfo.contactInfo?.phone || 'غير محدد',
        email: schoolInfo.contactInfo?.email || 'غير محدد'
      }
    };

    // إنشاء وصل التسجيل
    const newReceipt = await prisma.studentRegistrationReceipt.create({
      data: {
        studentId: studentId,
        receiptNumber,
        registrationFee: parseFloat(registrationFee),
        paymentStatus,
        receiptData,
        notes,
        createdBy: permissionCheck.userData?.username || 'system'
      },
      include: {
        student: {
          select: {
            id: true,
            name: true,
            username: true
          }
        }
      }
    });

    // تنسيق التاريخ في الاستجابة
    const formattedReceipt = {
      ...newReceipt,
      issueDate: new Date(newReceipt.issueDate).toLocaleDateString('fr-FR'),
      createdAt: new Date(newReceipt.createdAt).toLocaleDateString('fr-FR'),
      updatedAt: new Date(newReceipt.updatedAt).toLocaleDateString('fr-FR')
    };

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء وصل التسجيل بنجاح',
      data: formattedReceipt
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating registration receipt:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء وصل التسجيل' },
      { status: 500 }
    );
  }
}

// PUT /api/students/[id]/registration-receipt - تحديث وصل التسجيل
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.students.receipt.manage');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { error: permissionCheck.message || 'غير مصرح بالوصول' },
        { status: permissionCheck.status || 401 }
      );
    }

    const studentId = parseInt(params.id);
    const body = await request.json();
    const {
      receiptId,
      paymentStatus,
      notes,
      markAsPrinted = false
    } = body;

    if (!receiptId) {
      return NextResponse.json(
        { error: 'معرف الوصل مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الوصل
    const existingReceipt = await prisma.studentRegistrationReceipt.findFirst({
      where: {
        id: parseInt(receiptId),
        studentId: studentId
      }
    });

    if (!existingReceipt) {
      return NextResponse.json(
        { error: 'وصل التسجيل غير موجود' },
        { status: 404 }
      );
    }

    // إعداد البيانات للتحديث
    const updateData: any = {};

    if (paymentStatus) updateData.paymentStatus = paymentStatus;
    if (notes !== undefined) updateData.notes = notes;

    if (markAsPrinted) {
      updateData.isPrinted = true;
      updateData.printedAt = new Date();
      updateData.printedBy = permissionCheck.userData?.username || 'system';
    }

    // تحديث الوصل
    const updatedReceipt = await prisma.studentRegistrationReceipt.update({
      where: { id: parseInt(receiptId) },
      data: updateData,
      include: {
        student: {
          select: {
            id: true,
            name: true,
            username: true
          }
        }
      }
    });

    // تنسيق التاريخ في الاستجابة
    const formattedReceipt = {
      ...updatedReceipt,
      issueDate: new Date(updatedReceipt.issueDate).toLocaleDateString('fr-FR'),
      printedAt: updatedReceipt.printedAt
        ? new Date(updatedReceipt.printedAt).toLocaleDateString('fr-FR')
        : null,
      createdAt: new Date(updatedReceipt.createdAt).toLocaleDateString('fr-FR'),
      updatedAt: new Date(updatedReceipt.updatedAt).toLocaleDateString('fr-FR')
    };

    return NextResponse.json({
      success: true,
      message: 'تم تحديث وصل التسجيل بنجاح',
      data: formattedReceipt
    });

  } catch (error) {
    console.error('Error updating registration receipt:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث وصل التسجيل' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";

export async function GET(request: NextRequest) {
    try {
        // الحصول على معرف المستخدم من التوكن
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || userData.role !== 'STUDENT') {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userId = userData.id;

        // جلب معلومات المستخدم والملف الشخصي
        const user = await prisma.user.findUnique({
            where: {
                id: userId
            },
            include: {
                profile: true
            }
        });

        if (!user) {
            return NextResponse.json(
                { message: "لم يتم العثور على بيانات المستخدم" },
                { status: 404 }
            );
        }

        // جلب معلومات الطالب باستخدام اسم المستخدم
        const student = await prisma.student.findUnique({
            where: {
                username: user.username
            },
            include: {
                classe: true
            }
        });

        if (!student) {
            return NextResponse.json(
                { message: "لم يتم العثور على بيانات الطالب" },
                { status: 404 }
            );
        }

        // جلب الامتحانات والتقييمات
        const examPoints = await prisma.exam_points.findMany({
            where: {
                studentId: student.id
            },
            include: {
                exam: true
            },
            take: 10,
            orderBy: {
                createdAt: 'desc'
            }
        });

        // حساب نسبة التقدم في الحفظ
        let progress = 0;
        if (examPoints.length > 0) {
            const totalGrades = examPoints.reduce((sum, point) => sum + Number(point.grade), 0);
            const maxPossibleGrade = examPoints.length * 100;
            progress = Math.round((totalGrades / maxPossibleGrade) * 100);
        }

        // جلب سجلات الحضور
        const attendances = await prisma.attendance.findMany({
            where: {
                studentId: student.id
            },
            take: 10,
            orderBy: {
                date: 'desc'
            }
        });

        // حساب نسبة الحضور
        let attendanceRate = 0;
        if (attendances.length > 0) {
            const presentCount = attendances.filter(a => a.status === 'PRESENT').length;
            attendanceRate = Math.round((presentCount / attendances.length) * 100);
        }

        // جلب الامتحان القادم - ملاحظة: لا يوجد حقل date في نموذج Exam
        const upcomingExam = await prisma.exam.findFirst({
            where: {
                exam_points: {
                    some: {
                        studentId: student.id
                    }
                }
            },
            orderBy: {
                createdAt: 'asc'
            }
        });

        // ملاحظة: لا يوجد نموذج achievement في قاعدة البيانات
        // إنشاء إنجازات افتراضية بناءً على نقاط الامتحان
        const formattedAchievements = examPoints.slice(0, 3).map((point, index) => ({
            id: index + 1,
            title: `إنجاز في ${point.exam.evaluationType}`,
            description: point.exam.description || 'إنجاز في الامتحان',
            progress: Number(point.grade) / point.exam.maxPoints * 100,
            completed: Number(point.grade) >= point.exam.passingPoints
        }));

        // ملاحظة: لا يوجد نموذج lesson في قاعدة البيانات

        return NextResponse.json({
            name: user.profile?.name || user.username,
            grade: student.classe?.name || '',
            progress: progress,
            nextLesson: null,
            lastAttendance: attendances.length > 0 ? attendances[0].date.toISOString() : null,
            upcomingExam: upcomingExam ? {
                title: upcomingExam.evaluationType || '',
                date: upcomingExam.createdAt.toISOString()
            } : null,
            attendanceRate: attendanceRate,
            totalPoints: student.totalPoints || 0,
            achievements: formattedAchievements
        });
    } catch (error: unknown) {
        console.error('Error fetching student stats:', error);
        return NextResponse.json({
            name: '',
            grade: '',
            progress: 0,
            nextLesson: null,
            lastAttendance: null,
            upcomingExam: null,
            attendanceRate: 0,
            totalPoints: 0,
            achievements: []
        });
    }
}

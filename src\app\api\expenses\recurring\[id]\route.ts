import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { addDays, addWeeks, addMonths, addYears } from 'date-fns';

// PUT /api/expenses/recurring/[id] - تحديث مصروف دوري
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);
    const body = await req.json();
    const {
      purpose,
      amount,
      categoryId,
      startDate,
      endDate,
      frequency,
      interval,
      isActive,
      notes,
    } = body;

    // التحقق من وجود المصروف الدوري
    const existingExpense = await prisma.recurringExpense.findUnique({
      where: { id }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'المصروف الدوري غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من صحة البيانات
    if (amount !== undefined && (typeof amount !== 'number' || amount <= 0)) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    if (interval !== undefined && interval <= 0) {
      return NextResponse.json(
        { error: 'الفاصل الزمني يجب أن يكون أكبر من صفر' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفئة إذا تم تحديدها
    if (categoryId) {
      const category = await prisma.expenseCategory.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'فئة المصروفات غير موجودة' },
          { status: 400 }
        );
      }
    }

    // حساب تاريخ التوليد التالي إذا تم تغيير التكرار أو الفاصل الزمني
    let nextGenerationDate = existingExpense.nextGenerationDate;
    if (
      (frequency !== undefined && frequency !== existingExpense.frequency) ||
      (interval !== undefined && interval !== existingExpense.interval) ||
      (startDate !== undefined && new Date(startDate).getTime() !== existingExpense.startDate.getTime())
    ) {
      const newStartDate = startDate ? new Date(startDate) : existingExpense.startDate;
      nextGenerationDate = calculateNextGenerationDate(
        newStartDate,
        frequency || existingExpense.frequency,
        interval || existingExpense.interval
      );
    }

    // تحديث المصروف الدوري
    const updatedExpense = await prisma.recurringExpense.update({
      where: { id },
      data: {
        purpose: purpose !== undefined ? purpose : undefined,
        amount: amount !== undefined ? amount : undefined,
        categoryId: categoryId !== undefined ? (categoryId || null) : undefined,
        startDate: startDate !== undefined ? new Date(startDate) : undefined,
        endDate: endDate !== undefined ? (endDate ? new Date(endDate) : null) : undefined,
        frequency: frequency !== undefined ? frequency : undefined,
        interval: interval !== undefined ? interval : undefined,
        isActive: isActive !== undefined ? isActive : undefined,
        nextGenerationDate,
        notes: notes !== undefined ? (notes || null) : undefined,
      },
      include: {
        category: true,
      },
    });

    return NextResponse.json(updatedExpense);
  } catch (error) {
    console.error('خطأ في تحديث المصروف الدوري:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث المصروف الدوري' },
      { status: 500 }
    );
  }
}

// DELETE /api/expenses/recurring/[id] - حذف مصروف دوري
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = parseInt(params.id);

    // التحقق من وجود المصروف الدوري
    const existingExpense = await prisma.recurringExpense.findUnique({
      where: { id }
    });

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'المصروف الدوري غير موجود' },
        { status: 404 }
      );
    }

    // حذف المصروف الدوري
    await prisma.recurringExpense.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف المصروف الدوري بنجاح',
    });
  } catch (error) {
    console.error('خطأ في حذف المصروف الدوري:', error);
    return NextResponse.json(
      { error: 'فشل في حذف المصروف الدوري' },
      { status: 500 }
    );
  }
}

// حساب تاريخ التوليد التالي
function calculateNextGenerationDate(
  currentDate: Date,
  frequency: string,
  interval: number
): Date {
  switch (frequency) {
    case 'daily':
      return addDays(currentDate, interval);
    case 'weekly':
      return addWeeks(currentDate, interval);
    case 'monthly':
      return addMonths(currentDate, interval);
    case 'yearly':
      return addYears(currentDate, interval);
    default:
      return addMonths(currentDate, interval);
  }
}

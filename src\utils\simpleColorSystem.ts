// نظام ألوان بسيط وفعال

export interface SiteColors {
  primaryColor: string;
  secondaryColor: string;
  sidebarColor: string;
  backgroundColor: string;
  accentColor: string;
  textColor: string;
}

// الألوان الافتراضية
export const defaultColors: SiteColors = {
  primaryColor: '#169b88',
  secondaryColor: '#1ab19c',
  sidebarColor: '#1a202c',
  backgroundColor: '#f3f4f6',
  accentColor: '#10b981',
  textColor: '#1f2937'
};

// دالة لحساب التباين بين لونين
const getContrastRatio = (color1: string, color2: string): number => {
  const getLuminance = (color: string): number => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
};

// دالة لتحديد لون النص المناسب
const getTextColor = (backgroundColor: string): string => {
  const whiteContrast = getContrastRatio(backgroundColor, '#ffffff');
  const blackContrast = getContrastRatio(backgroundColor, '#000000');

  return whiteContrast > blackContrast ? '#ffffff' : '#000000';
};

// دالة لتحويل hex إلى rgba
const hexToRgba = (hex: string, alpha: number): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// تطبيق الألوان على CSS variables مع تباين محسن
export const applyColors = (colors: SiteColors): void => {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;

  // الألوان الأساسية
  root.style.setProperty('--primary-color', colors.primaryColor);
  root.style.setProperty('--secondary-color', colors.secondaryColor);
  root.style.setProperty('--sidebar-color', colors.sidebarColor);
  root.style.setProperty('--background-color', colors.backgroundColor);
  root.style.setProperty('--accent-color', colors.accentColor);
  root.style.setProperty('--text-color', colors.textColor);

  // ألوان النصوص المتباينة
  const primaryTextColor = getTextColor(colors.primaryColor);
  const secondaryTextColor = getTextColor(colors.secondaryColor);
  const sidebarTextColor = getTextColor(colors.sidebarColor);

  root.style.setProperty('--primary-text-color', primaryTextColor);
  root.style.setProperty('--secondary-text-color', secondaryTextColor);
  root.style.setProperty('--sidebar-text-color', sidebarTextColor);

  // الألوان الخفيفة والداكنة
  root.style.setProperty('--primary-light', hexToRgba(colors.primaryColor, 0.1));
  root.style.setProperty('--primary-lighter', hexToRgba(colors.primaryColor, 0.05));
  root.style.setProperty('--primary-dark', hexToRgba(colors.primaryColor, 0.9));

  root.style.setProperty('--secondary-light', hexToRgba(colors.secondaryColor, 0.1));
  root.style.setProperty('--secondary-lighter', hexToRgba(colors.secondaryColor, 0.05));
  root.style.setProperty('--secondary-dark', hexToRgba(colors.secondaryColor, 0.9));

  // ألوان الحدود
  root.style.setProperty('--primary-border', hexToRgba(colors.primaryColor, 0.2));
  root.style.setProperty('--secondary-border', hexToRgba(colors.secondaryColor, 0.2));

  // تحديث الألوان في theme.css
  updateThemeColors(colors);
};

// دالة لتحديث الألوان في theme.css ديناميكياً
const updateThemeColors = (colors: SiteColors): void => {
  // إنشاء أو تحديث style element
  let styleElement = document.getElementById('dynamic-theme-colors');
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = 'dynamic-theme-colors';
    document.head.appendChild(styleElement);
  }

  const primaryTextColor = getTextColor(colors.primaryColor);
  const secondaryTextColor = getTextColor(colors.secondaryColor);

  const css = `
    /* ألوان ديناميكية محدثة */
    .bg-primary-light {
      background-color: ${hexToRgba(colors.primaryColor, 0.1)} !important;
      color: ${hexToRgba(colors.primaryColor, 0.9)} !important;
    }

    .bg-secondary-light {
      background-color: ${hexToRgba(colors.secondaryColor, 0.1)} !important;
      color: ${hexToRgba(colors.secondaryColor, 0.9)} !important;
    }

    .bg-primary-color {
      background-color: ${colors.primaryColor} !important;
      color: ${primaryTextColor} !important;
    }

    .bg-secondary-color {
      background-color: ${colors.secondaryColor} !important;
      color: ${secondaryTextColor} !important;
    }

    .text-primary-dark {
      color: ${hexToRgba(colors.primaryColor, 0.9)} !important;
    }

    .text-secondary-dark {
      color: ${hexToRgba(colors.secondaryColor, 0.9)} !important;
    }

    .border-primary-light {
      border-color: ${hexToRgba(colors.primaryColor, 0.2)} !important;
    }

    .border-secondary-light {
      border-color: ${hexToRgba(colors.secondaryColor, 0.2)} !important;
    }

    .success-colors {
      background-color: ${hexToRgba(colors.primaryColor, 0.1)} !important;
      color: ${hexToRgba(colors.primaryColor, 0.9)} !important;
      border-color: ${hexToRgba(colors.primaryColor, 0.3)} !important;
    }

    .success-colors-dark {
      background-color: ${colors.primaryColor} !important;
      color: ${primaryTextColor} !important;
      border-color: ${colors.primaryColor} !important;
    }

    /* تحديث الألوان الشائعة */
    .bg-green-50,
    .bg-green-100 {
      background-color: ${hexToRgba(colors.primaryColor, 0.1)} !important;
      color: ${hexToRgba(colors.primaryColor, 0.9)} !important;
    }

    .bg-green-500,
    .bg-green-600,
    .bg-green-700 {
      background-color: ${colors.primaryColor} !important;
      color: ${primaryTextColor} !important;
    }

    .text-green-800,
    .text-green-900 {
      color: ${hexToRgba(colors.primaryColor, 0.9)} !important;
    }

    .border-green-100,
    .border-green-200,
    .border-green-300 {
      border-color: ${hexToRgba(colors.primaryColor, 0.2)} !important;
    }
  `;

  styleElement.textContent = css;
};

// حفظ الألوان في قاعدة البيانات
export const saveColors = async (colors: SiteColors): Promise<boolean> => {
  try {
    const response = await fetch('/api/site-colors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ colors }),
    });

    return response.ok;
  } catch (error) {
    console.error('خطأ في حفظ الألوان:', error);
    return false;
  }
};

// جلب الألوان من قاعدة البيانات
export const loadColors = async (): Promise<SiteColors> => {
  try {
    const response = await fetch('/api/site-colors');
    if (response.ok) {
      const data = await response.json();
      return data.colors || defaultColors;
    }
  } catch (error) {
    console.error('خطأ في جلب الألوان:', error);
  }

  return defaultColors;
};

// تطبيق الألوان عند تحميل الصفحة
export const initializeColors = async (): Promise<void> => {
  const colors = await loadColors();
  applyColors(colors);
};

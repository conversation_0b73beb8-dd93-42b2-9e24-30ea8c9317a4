# 🎯 مخطط حالة الاستخدام - نظام المدفوعات والفواتير

## نظرة عامة
يوضح هذا المخطط جميع حالات الاستخدام لنظام المدفوعات والفواتير للتلاميذ والأولياء.

## مخطط حالة الاستخدام

```mermaid
graph TB
    %% الفاعلون
    Admin[👨‍💼 المسؤول/الموظف]
    Teacher[👨‍🏫 المعلم]
    Parent[👨‍👩‍👧‍👦 ولي الأمر]
    Student[👨‍🎓 التلميذ]
    System[🖥️ النظام]

    %% حالات الاستخدام الرئيسية للمدفوعات
    subgraph PaymentUseCases[إدارة المدفوعات]
        UC1[عرض جميع المدفوعات]
        UC2[تسجيل دفعة جديدة]
        UC3[البحث في المدفوعات]
        UC4[تصدير تقرير المدفوعات]
        UC5[عرض مدفوعات حسب الولي]
        UC6[عرض مدفوعات حسب التلميذ]
        UC7[تعديل دفعة موجودة]
        UC8[حذف دفعة]
        UC9[ربط الدفعة بالفاتورة]
    end

    %% حالات الاستخدام الرئيسية للفواتير
    subgraph InvoiceUseCases[إدارة الفواتير]
        UC10[إنشاء فاتورة فردية]
        UC11[إنشاء فاتورة جماعية]
        UC12[عرض جميع الفواتير]
        UC13[تعديل فاتورة]
        UC14[حذف فاتورة]
        UC15[طباعة فاتورة]
        UC16[تصدير فاتورة PDF]
        UC17[إرسال تذكير بالفاتورة]
        UC18[تحديث حالة الفاتورة]
    end

    %% حالات الاستخدام للأولياء
    subgraph ParentUseCases[واجهة الأولياء]
        UC19[عرض فواتير الأبناء]
        UC20[عرض مدفوعات الأبناء]
        UC21[عرض الديون المستحقة]
        UC22[طباعة إيصال دفع]
        UC23[تحميل كشف حساب]
    end

    %% حالات الاستخدام للتقارير
    subgraph ReportUseCases[التقارير والإحصائيات]
        UC24[تقرير المدفوعات الشهرية]
        UC25[تقرير الديون المستحقة]
        UC26[تقرير الفواتير المتأخرة]
        UC27[إحصائيات المدفوعات]
        UC28[تقرير مدفوعات الأولياء]
    end

    %% حالات الاستخدام للنظام
    subgraph SystemUseCases[العمليات التلقائية]
        UC29[حساب المبالغ المستحقة]
        UC30[تحديث حالات الفواتير]
        UC31[إرسال تذكيرات تلقائية]
        UC32[نسخ احتياطي للبيانات]
        UC33[تدقيق العمليات المالية]
    end

    %% العلاقات - المسؤول
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC6
    Admin --> UC7
    Admin --> UC8
    Admin --> UC9
    Admin --> UC10
    Admin --> UC11
    Admin --> UC12
    Admin --> UC13
    Admin --> UC14
    Admin --> UC15
    Admin --> UC16
    Admin --> UC17
    Admin --> UC18
    Admin --> UC24
    Admin --> UC25
    Admin --> UC26
    Admin --> UC27
    Admin --> UC28

    %% العلاقات - المعلم
    Teacher --> UC1
    Teacher --> UC3
    Teacher --> UC5
    Teacher --> UC6
    Teacher --> UC12
    Teacher --> UC19
    Teacher --> UC20
    Teacher --> UC24
    Teacher --> UC27

    %% العلاقات - ولي الأمر
    Parent --> UC19
    Parent --> UC20
    Parent --> UC21
    Parent --> UC22
    Parent --> UC23

    %% العلاقات - التلميذ
    Student --> UC20
    Student --> UC22

    %% العلاقات - النظام
    System --> UC29
    System --> UC30
    System --> UC31
    System --> UC32
    System --> UC33

    %% العلاقات الفرعية
    UC2 --> UC9
    UC10 --> UC18
    UC11 --> UC18
    UC17 --> UC31
    UC29 --> UC30
```

## تفاصيل حالات الاستخدام

### 💰 إدارة المدفوعات

#### UC1: عرض جميع المدفوعات
- **الوصف:** عرض قائمة شاملة بجميع المدفوعات المسجلة
- **الفاعل:** المسؤول، المعلم
- **المتطلبات:** صلاحية عرض المدفوعات
- **النتيجة:** قائمة مفصلة بالمدفوعات مع إمكانية الفلترة والبحث

#### UC2: تسجيل دفعة جديدة
- **الوصف:** إضافة دفعة جديدة للنظام
- **الفاعل:** المسؤول
- **المتطلبات:** صلاحية إضافة المدفوعات
- **النتيجة:** دفعة جديدة مسجلة ومربوطة بالفاتورة المناسبة

#### UC3: البحث في المدفوعات
- **الوصف:** البحث عن مدفوعات محددة باستخدام معايير مختلفة
- **الفاعل:** المسؤول، المعلم
- **المتطلبات:** صلاحية عرض المدفوعات
- **النتيجة:** نتائج بحث مفلترة حسب المعايير المحددة

#### UC5: عرض مدفوعات حسب الولي
- **الوصف:** عرض ملخص شامل لمدفوعات كل ولي أمر
- **الفاعل:** المسؤول، المعلم
- **المتطلبات:** صلاحية عرض المدفوعات
- **النتيجة:** ملخص مالي مفصل لكل ولي مع إحصائيات

### 📄 إدارة الفواتير

#### UC10: إنشاء فاتورة فردية
- **الوصف:** إنشاء فاتورة لتلميذ واحد
- **الفاعل:** المسؤول
- **المتطلبات:** صلاحية إدارة الفواتير
- **النتيجة:** فاتورة جديدة مرتبطة بالتلميذ

#### UC11: إنشاء فاتورة جماعية
- **الوصف:** إنشاء فاتورة واحدة لولي أمر تشمل جميع أبنائه
- **الفاعل:** المسؤول
- **المتطلبات:** صلاحية إدارة الفواتير
- **النتيجة:** فاتورة جماعية مرتبطة بالولي

#### UC15: طباعة فاتورة
- **الوصف:** طباعة فاتورة بتصميم مناسب للطباعة
- **الفاعل:** المسؤول، ولي الأمر
- **المتطلبات:** وجود فاتورة صالحة
- **النتيجة:** فاتورة مطبوعة أو ملف PDF

### 👨‍👩‍👧‍👦 واجهة الأولياء

#### UC19: عرض فواتير الأبناء
- **الوصف:** عرض جميع فواتير أبناء الولي
- **الفاعل:** ولي الأمر
- **المتطلبات:** تسجيل دخول صحيح
- **النتيجة:** قائمة بفواتير الأبناء مع التفاصيل

#### UC20: عرض مدفوعات الأبناء
- **الوصف:** عرض تاريخ مدفوعات الأبناء
- **الفاعل:** ولي الأمر، التلميذ
- **المتطلبات:** تسجيل دخول صحيح
- **النتيجة:** سجل مفصل بالمدفوعات

#### UC21: عرض الديون المستحقة
- **الوصف:** عرض المبالغ المستحقة والمتأخرة
- **الفاعل:** ولي الأمر
- **المتطلبات:** تسجيل دخول صحيح
- **النتيجة:** ملخص بالديون والمواعيد المستحقة

### 📊 التقارير والإحصائيات

#### UC24: تقرير المدفوعات الشهرية
- **الوصف:** تقرير شامل بمدفوعات الشهر
- **الفاعل:** المسؤول، المعلم
- **المتطلبات:** صلاحية عرض التقارير
- **النتيجة:** تقرير مفصل قابل للتصدير

#### UC27: إحصائيات المدفوعات
- **الوصف:** إحصائيات عامة عن حالة المدفوعات
- **الفاعل:** المسؤول، المعلم
- **المتطلبات:** صلاحية عرض الإحصائيات
- **النتيجة:** لوحة معلومات بالإحصائيات الرئيسية

### 🖥️ العمليات التلقائية

#### UC29: حساب المبالغ المستحقة
- **الوصف:** حساب تلقائي للمبالغ المطلوبة والمدفوعة
- **الفاعل:** النظام
- **المتطلبات:** بيانات صحيحة في قاعدة البيانات
- **النتيجة:** حسابات دقيقة ومحدثة

#### UC30: تحديث حالات الفواتير
- **الوصف:** تحديث تلقائي لحالات الفواتير بناءً على المدفوعات
- **الفاعل:** النظام
- **المتطلبات:** ربط صحيح بين المدفوعات والفواتير
- **النتيجة:** حالات فواتير محدثة ودقيقة

## العلاقات والتبعيات

### علاقات الامتداد (Extend)
- UC2 (تسجيل دفعة) يمتد إلى UC9 (ربط بالفاتورة)
- UC17 (إرسال تذكير) يمتد إلى UC31 (تذكيرات تلقائية)

### علاقات التضمين (Include)
- UC10, UC11 (إنشاء فواتير) تتضمن UC18 (تحديث حالة)
- UC29 (حساب المبالغ) يتضمن UC30 (تحديث الحالات)

### علاقات التعميم (Generalization)
- UC15, UC16 (طباعة وتصدير) تعميم لعملية إخراج الفاتورة
- UC24, UC25, UC26 (التقارير) تعميم لعملية إنتاج التقارير

---

**ملاحظة:** هذا المخطط يوضح الوضع المثالي للنظام بعد الإصلاحات والتحسينات المطلوبة.

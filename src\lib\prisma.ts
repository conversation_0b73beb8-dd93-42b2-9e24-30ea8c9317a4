import { PrismaClient } from '@prisma/client'

// Prevent multiple instances of Prisma Client in development
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient }

// Create a singleton instance of PrismaClient with logging enabled in development
const prisma = globalForPrisma.prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
})

// Set the singleton instance only in development
if (process.env.NODE_ENV === 'development') globalForPrisma.prisma = prisma

// Export both as default and named export for compatibility
export default prisma
export { prisma }


# Quran School Management System

## About the Project

A comprehensive system for managing Quran memorization schools, aimed at facilitating the educational process and managing the affairs of students, teachers, and parents. The system provides customized interfaces for each type of user (administrators, teachers, students, parents) with features that suit their needs.

## Key Features

### 🔹 User Management System

- **Account Management**: Create, edit, and delete user accounts (administrators, teachers, students, parents)
- **Permissions**: Integrated permission system that determines what each user can access
- **Profiles**: Manage user profiles with the ability to upload personal photos

### 🔹 Student Management

- **Student Records**: Store and manage students' personal and academic data
- **Progress Tracking**: Track students' progress in memorizing the Quran
- **Evaluations**: Record and display student evaluations in memorization and recitation
- **Achievements**: Achievement and incentive system to encourage students

### 🔹 Teacher Management

- **Teacher Records**: Store and manage teacher data and qualifications
- **Class Schedule**: Manage teacher class schedules
- **Performance Evaluation**: Monitor and evaluate teacher performance
- **Specific Permissions**: Teachers can only record attendance, absence, and points for their own classes

### 🔹 Class Management

- **Class Creation**: Create and manage classes
- **Student Distribution**: Distribute students to classes
- **Teacher Assignment**: Assign teachers to classes

### 🔹 Attendance System

- **Attendance Recording**: Record student attendance and absence
- **Attendance Reports**: Display attendance and absence reports for students and classes
- **Absence Notifications**: Send notifications to parents when students are absent

### 🔹 Examination and Evaluation System

- **Create Exams**: Create and manage memorization and recitation exams
- **Grade Recording**: Record student grades in exams
- **Evaluation Reports**: Display student evaluation reports
- **Monthly Exam Types**: Exam types change monthly and can be modified monthly
- **Memorization Exams**: Based on specific surahs and verses for memorization
- **Other Exam Types**: Written and other exam types don't require surahs and verses

### 🔹 Payment System

- **Payment Recording**: Record student payments
- **Fee Management**: Manage registration and tuition fees
- **Financial Reports**: Display financial reports for revenues and expenses

### 🔹 Dashboards

- **Admin Dashboard**: Comprehensive management of the system, users, and statistics
- **Teacher Dashboard**: Manage classes, students, and evaluations
- **Student Dashboard**: Display progress, evaluations, and class schedule
- **Parent Dashboard**: Monitor children's progress, payments, and notifications

### 🔹 Remote Learning

- **Virtual Classes**: Create and manage virtual classes
- **Educational Materials**: Share educational materials with students
- **Recordings**: Record lessons and share them with students
- **Live Meetings**: Hold live meetings with students and parents

### 🔹 Notification System

- **Instant Notifications**: Send instant notifications to users
- **Class Notifications**: Notifications about virtual classes and new materials
- **Evaluation Notifications**: Notifications about exam results and evaluations
- **Payment Notifications**: Notifications about payments and fees

### 🔹 Reports and Statistics

- **Student Reports**: Reports on student progress and attendance
- **Teacher Reports**: Reports on teacher performance
- **Class Reports**: Reports on classes and subjects
- **Financial Reports**: Reports on revenues and expenses

## Technologies Used

- **Frontend**: Next.js, React, TailwindCSS
- **Backend**: Next.js API Routes
- **Database**: Prisma ORM with PostgreSQL database
- **Authentication**: JWT (JSON Web Tokens)
- **Storage**: File storage using cloud services

## Requirements

- Node.js (version 18 or later)
- PostgreSQL (version 14 or later)
- npm or yarn

## Installation

1. Clone the project:

```bash
git clone https://github.com/haouri30/qouran.git
cd qouran
```

2. Install dependencies:

```bash
npm install
# or
yarn install
```

3. Set up environment file:

Create a `.env.local` file and add the necessary environment variables:

```
DATABASE_URL="postgresql://username:password@localhost:5432/quran_school"
JWT_SECRET="your-secret-key"
```

4. Set up the database:

```bash
npx prisma migrate dev
```

5. Run the project:

```bash
npm run dev
# or
yarn dev
```

6. Open your browser at [http://localhost:3000](http://localhost:3000)

## Contributing

We welcome your contributions to the development of this project. Please follow these steps to contribute:

1. Fork the project
2. Create a new branch (`git checkout -b feature/amazing-feature`)
3. Make the required changes
4. Commit your changes (`git commit -m 'Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Updates

### 🔹 Code Improvements and Bug Fixes (2024-06-20)

The following improvements and bug fixes have been implemented:

1. **React Hooks Optimization**
   - Fixed missing dependencies in `useEffect` hooks across multiple components
   - Implemented `useCallback` for functions used in effect dependencies
   - Resolved circular dependencies in component functions
   - Improved performance by preventing unnecessary re-renders

2. **TypeScript Type Safety Enhancements**
   - Replaced generic `any` types with more specific types like `Record<string, any>`
   - Added proper type definitions for API route parameters and responses
   - Improved type safety in database query conditions
   - Enhanced component props typing for better IDE support

3. **Component Improvements**
   - Updated `AnimatedDialog` component to accept JSX elements in title prop
   - Enhanced Profile page to display user data and role information from the database
   - Fixed issues with dropdown menus and form submissions

4. **API Routes Optimization**
   - Improved error handling in API routes
   - Enhanced data validation before database operations
   - Optimized database queries for better performance

5. **Files Affected**
   - `src/components/ui/dialog/AnimatedDialog.tsx`
   - `src/app/api/exam-points/route.ts`
   - `src/app/api/classes/route.ts`
   - `src/app/api/criteria-scores/route.ts`
   - `src/app/api/student-points/route.ts`
   - `src/app/api/student-rewards/route.ts`
   - `src/app/api/users/logout/route.ts`
   - `src/app/khatm-sessions/[id]/page.tsx`
   - `src/app/profile/page.tsx`
   - `src/app/remote-classes/page.tsx`
   - `src/app/remote-classes/[id]/page.tsx`
   - `src/app/teachers/evaluation/dashboard/page.tsx`
   - `src/app/admin/activities/page.tsx`
   - `src/app/admin/attendance/page.tsx`
   - `src/app/admin/donations/page.tsx`

### 🔹 Parent Dashboard Update (2024-06-17)

The following pages have been added to the parent dashboard:

1. **Children Page** (`/parents/children`)
   - Display a list of the parent's children and their information
   - Show an overview of each child with their academic progress
   - Tabs to display Quran memorization progress and exam results

2. **Progress Page** (`/parents/progress`)
   - Display children's progress in Quran memorization and studies
   - Show detailed exam results and attendance records
   - Comprehensive statistics about each child's performance

3. **Schedule Page** (`/parents/schedule`)
   - Display children's weekly class schedules
   - Organize the schedule by days of the week
   - Show a summary of the number of classes and teachers

4. **Curriculum Page** (`/parents/curriculum`)
   - Display the curriculum for the children
   - Show subjects, curriculum units, and lessons
   - Access available educational resources

5. **Payments Page** (`/parents/payments`)
   - Display payment history and due fees
   - Show a summary of payments and outstanding amounts
   - Details of each payment with its status and date

### 🔹 Student Dashboard Update (2024-06-16)

The following pages have been added to the student dashboard:

1. **Courses Page** (`/students/courses`)
   - Display all courses for the student
   - Show course information with teacher name and progress percentage
   - Option to view details of each course

2. **Schedule Page** (`/students/schedule`)
   - Display the student's weekly class schedule
   - Organize the schedule by days of the week
   - Show a summary of the number of classes and teachers

3. **Teachers Page** (`/students/teachers`)
   - Display a list of teachers who teach the student
   - Show teacher information with the subjects they teach
   - Search for a specific teacher

4. **Results Page** (`/students/results`)
   - Display exam results and Quran memorization progress
   - Show a summary of results with average grades
   - Tabs to show exam details and memorization progress

### 🔹 Teacher Dashboard Update (2024-06-15)

The following pages have been added to the teacher dashboard:

1. **My Students Page** (`/teachers/students`)
   - Display a list of students taught by the teacher
   - Search for a specific student
   - Show details of each student such as age, class, guardian, and points

2. **Student Details Page** (`/teachers/students/[id]`)
   - Display detailed information about the student
   - Tabs to show attendance record, Quran memorization progress, and achievements

3. **Attendance Recording Page** (`/teachers/attendance`)
   - Record student attendance and absence
   - Select class, date, and period
   - Display a list of students and their attendance status

4. **Class Schedule Page** (`/teachers/schedule`)
   - Display the teacher's weekly class schedule
   - Show a summary of the number of classes, subjects, and classes

5. **Curriculum Page** (`/teachers/curriculum`)
   - Display the curriculum for the subjects taught by the teacher
   - Organize the curriculum into units, lessons, and educational resources

6. **Add Quran Progress Page** (`/teachers/quran-progress/add`)
   - Add new progress in Quran memorization for the student
   - Select surah, specify verses, grade, and notes

### 🔹 New APIs

1. **Parent Children API** (`/api/parent-children`)
   - Fetch a list of children for the logged-in parent with their information

2. **Parent Progress API** (`/api/parent-progress`)
   - Fetch children's progress data in studies and Quran memorization

3. **Parent Schedule API** (`/api/parent-schedule`)
   - Fetch the weekly schedule for the parent's children

4. **Parent Curriculum API** (`/api/parent-curriculum`)
   - Fetch the curriculum for the parent's children

5. **Parent Payments API** (`/api/parent-payments`)
   - Fetch payment history and due fees for the parent's children

6. **Student Courses API** (`/api/student-courses`)
   - Fetch courses for the logged-in student

7. **Student Schedule API** (`/api/student-schedule`)
   - Fetch the weekly schedule for the logged-in student

8. **Student Teachers API** (`/api/student-teachers`)
   - Fetch a list of teachers who teach the logged-in student

9. **Student Results API** (`/api/student-results`)
   - Fetch exam results and Quran memorization progress for the logged-in student

10. **Teacher Students API** (`/api/teacher-students`)
    - Fetch a list of students taught by the logged-in teacher

11. **Teacher Classes API** (`/api/teacher-classes`)
    - Fetch a list of classes taught by the logged-in teacher

12. **Teacher Schedule API** (`/api/teacher-schedule`)
    - Fetch the schedule of the logged-in teacher

13. **Curriculum API** (`/api/curriculum`)
    - Fetch the curriculum for the specified subject

14. **Quran Progress API** (`/api/quran-progress`)
    - Fetch Quran memorization progress for the specified student
    - Add new progress in Quran memorization

## Project Structure

```
qouran/
├── prisma/
│   └── schema.prisma       # Database model
├── public/                 # Public files
├── src/
│   ├── app/
│   │   ├── admin/          # Admin dashboard pages
│   │   ├── api/            # API endpoints
│   │   │   ├── attendance/
│   │   │   ├── classes/
│   │   │   ├── curriculum/
│   │   │   ├── khatm-attendance/
│   │   │   ├── khatm-sessions/
│   │   │   ├── parent-children/     # Parent children API
│   │   │   ├── parent-curriculum/   # Parent curriculum API
│   │   │   ├── parent-payments/     # Parent payments API
│   │   │   ├── parent-progress/     # Parent progress API
│   │   │   ├── parent-schedule/     # Parent schedule API
│   │   │   ├── quran-progress/
│   │   │   ├── student-courses/     # Student courses API
│   │   │   ├── student-results/     # Student results API
│   │   │   ├── student-schedule/    # Student schedule API
│   │   │   ├── student-teachers/    # Student teachers API
│   │   │   ├── students/
│   │   │   ├── teacher-classes/
│   │   │   ├── teacher-schedule/
│   │   │   ├── teacher-students/
│   │   │   ├── teacher-subjects/
│   │   │   └── teachers/
│   │   ├── dashboard/      # Dashboard pages
│   │   ├── parents/        # Parent pages
│   │   │   ├── children/       # Children page
│   │   │   ├── curriculum/     # Curriculum page
│   │   │   ├── payments/       # Payments page
│   │   │   ├── progress/       # Progress page
│   │   │   └── schedule/       # Schedule page
│   │   ├── students/       # Student pages
│   │   │   ├── courses/        # Courses page
│   │   │   ├── results/        # Results page
│   │   │   ├── schedule/       # Schedule page
│   │   │   └── teachers/       # Teachers page
│   │   ├── teachers/       # Teacher pages
│   │   │   ├── attendance/
│   │   │   ├── curriculum/
│   │   │   ├── quran-progress/
│   │   │   ├── schedule/
│   │   │   └── students/
│   │   ├── layout.tsx
│   │   └── page.tsx        # Home page
│   ├── components/         # UI components
│   ├── lib/                # Helper libraries and functions
│   └── utils/              # Helper utilities
├── .env                    # Environment file
├── .gitignore
├── next.config.js
├── package.json
├── README.md              # English README file
├── README_ar.md          # Arabic README file
└── tsconfig.json
```

## Contact

For inquiries and support, please contact via email: <EMAIL>

# مكتبة Praetorian.ring

**مجموعة أدوات اختبار الاختراق الاحترافية للغة Ring**

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)
![Ring](https://img.shields.io/badge/Ring-1.23+-orange.svg)

## 📋 نظرة عامة

مكتبة Praetorian.ring هي مجموعة أدوات شاملة واحترافية لاختبار الاختراق مصممة خصيصاً للغة Ring. تهدف المكتبة إلى توفير مجموعة قوية ومرنة من الأدوات التي يمكن للمطورين والمختبرين الأمنيين استخدامها لبناء أدواتهم المخصصة أو لأتمتة المهام الأمنية.

## ✨ المميزات الرئيسية

### 🌐 وحدة الشبكات
- **فاحص المنافذ**: فحص TCP Connect متعدد الخيوط
- **جلب البانر**: استخراج معلومات الخدمات
- **صانع الحزم**: بناء وإرسال حزم TCP/ICMP خام
- **تحديد الخدمات**: تحديد نوع الخدمة تلقائياً

### 🌍 وحدة الويب
- **عميل HTTP متقدم**: دعم كامل للجلسات والكوكيز
- **زاحف الويب**: اكتشاف الروابط والمسارات تلقائياً
- **أداة Fuzzing**: اكتشاف المجلدات والملفات المخفية
- **تحليل النماذج**: استخراج وتحليل نماذج HTML

### 🔐 وحدة التشفير
- **مدقق SSL/TLS**: فحص شامل لتكوين SSL
- **تحليل الشهادات**: فحص صحة وتفاصيل الشهادات
- **فحص الشيفرات الضعيفة**: اكتشاف الشيفرات غير الآمنة
- **تحليل البروتوكولات**: فحص البروتوكولات المدعومة

### 🛠️ الأدوات المساعدة
- **نظام تسجيل احترافي**: تتبع العمليات والأخطاء
- **أدوات التشفير والترميز**: Base64, URL, HTML encoding
- **مولد كلمات المرور**: إنشاء كلمات مرور قوية
- **أدوات التحقق**: فحص صحة عناوين IP والبيانات

## 📁 هيكل المشروع

```
praetorian/
├── praetorian.ring          # الملف الرئيسي للمكتبة
├── core/                    # الوحدات الأساسية
│   ├── utils.ring           # الأدوات المساعدة
│   └── logger.ring          # نظام التسجيل
├── network/                 # وحدة الشبكات
│   ├── scanner.ring         # فاحص الشبكة
│   └── packet_crafter.ring  # صانع الحزم
├── web/                     # وحدة الويب
│   ├── http_client.ring     # عميل HTTP
│   ├── crawler.ring         # زاحف الويب
│   └── fuzzer.ring          # أداة Fuzzing
├── crypto/                  # وحدة التشفير
│   └── ssl_checker.ring     # مدقق SSL
├── examples/                # أمثلة الاستخدام
│   ├── basic_scan.ring      # مثال أساسي
│   ├── web_audit.ring       # تدقيق الويب
│   └── ssl_audit.ring       # تدقيق SSL
├── applications/            # التطبيقات العملية
│   ├── ReconDash/           # لوحة التحكم الاستطلاعية (GUI)
│   │   ├── ReconDash.ring   # التطبيق الرئيسي
│   │   ├── config.ring      # ملف التكوين
│   │   └── run.bat          # ملف التشغيل
│   ├── DirHunter/           # أداة تخمين المجلدات (CLI)
│   │   ├── DirHunter.ring   # التطبيق الرئيسي
│   │   ├── wordlist.txt     # قائمة الكلمات
│   │   └── run_example.bat  # مثال التشغيل
│   ├── launcher.ring        # مشغل التطبيقات
│   ├── test_applications.ring # اختبار التطبيقات
│   └── README.md            # دليل التطبيقات
├── test_praetorian.ring     # اختبار المكتبة
├── README.md                # هذا الملف
├── CHANGELOG.md             # سجل التغييرات
└── LICENSE                  # ملف الترخيص
```

## 🚀 التثبيت والاستخدام

### المتطلبات الأساسية

- Ring 1.23 أو أحدث
- المكتبات المطلوبة للمكتبة الأساسية:
  - `openssllib.ring`
  - `sockets.ring`
  - `libcurl.ring`
  - `threads.ring`

### المتطلبات الإضافية للتطبيقات

- `libui.ring` - مطلوب لتشغيل ReconDash (الواجهة الرسومية)
- `rogueutil.ring` - اختياري لألوان DirHunter
- `ringregex.ring` - اختياري للبحث المتقدم

### الاستخدام الأساسي

```ring
# تحميل المكتبة
load "praetorian.ring"

# إنشاء مثيل من المكتبة
oPraetorian = CreatePraetorian()

# فحص المنافذ الشائعة
aOpenPorts = oPraetorian.Network.Scanner.scanCommonPorts("example.com")

# فحص تطبيق ويب
oResponse = oPraetorian.Web.HTTPClient.get("https://example.com", NULL)

# فحص SSL
aSSLReport = oPraetorian.Crypto.SSLChecker.comprehensiveSSLCheck("example.com", 443)
```

## �️ التطبيقات العملية

### مشغل التطبيقات

```bash
cd applications
ring launcher.ring
```

يوفر المشغل واجهة موحدة لتشغيل جميع التطبيقات مع فحص المتطلبات.

### 1. ReconDash - لوحة التحكم الاستطلاعية

**الوصف**: لوحة تحكم استطلاعية بواجهة رسومية لجمع المعلومات الأمنية.

**التشغيل**:
```bash
cd applications/ReconDash
ring ReconDash.ring
```

**المميزات**:
- واجهة رسومية سهلة الاستخدام
- فحص شامل للمنافذ مع جلب البانر
- تحليل SSL/TLS للمنافذ الآمنة
- زاحف ويب لاكتشاف المسارات
- عرض منظم للنتائج في ألسنة منفصلة

### 2. DirHunter - أداة تخمين المجلدات

**الوصف**: أداة سطر أوامر لتخمين أسماء المجلدات والملفات على خوادم الويب.

**التشغيل**:
```bash
cd applications/DirHunter
ring DirHunter.ring -u http://example.com -w wordlist.txt
```

**المعلمات**:
- `-u, --url`: عنوان URL الهدف (مطلوب)
- `-w, --wordlist`: مسار قائمة الكلمات (مطلوب)
- `-t, --threads`: عدد الخيوط (افتراضي: 10)
- `-x, --extensions`: امتدادات الملفات
- `-v, --verbose`: عرض تفاصيل إضافية
- `-h, --help`: عرض المساعدة

**أمثلة**:
```bash
# فحص أساسي
ring DirHunter.ring -u http://example.com -w wordlist.txt

# فحص متقدم
ring DirHunter.ring -u https://target.com -w dirs.txt -t 20 -x php,html,asp
```

## �📚 أمثلة الاستخدام للمكتبة

### 1. فحص شبكة أساسي

```ring
load "praetorian.ring"

oPraetorian = CreatePraetorian()
cTarget = "192.168.1.1"

# فحص المنافذ الشائعة
aOpenPorts = oPraetorian.Network.Scanner.scanCommonPorts(cTarget)
? "المنافذ المفتوحة: " + len(aOpenPorts)

# فحص شامل مع جلب البانر
aResults = oPraetorian.Network.Scanner.comprehensiveScan(cTarget, aOpenPorts)
oPraetorian.Network.Scanner.printScanReport(cTarget, aResults)
```

### 2. تدقيق تطبيق ويب

```ring
load "praetorian.ring"

oPraetorian = CreatePraetorian()
cTarget = "https://example.com"

# زحف الموقع
aCrawlReport = oPraetorian.Web.Crawler.crawl(cTarget)
? "تم زحف " + aCrawlReport[:total_pages] + " صفحة"

# Fuzzing للمجلدات والملفات
aFuzzReport = oPraetorian.Web.Fuzzer.quickFuzz(cTarget)
oPraetorian.Web.Fuzzer.printFuzzReport(aFuzzReport)
```

### 3. تدقيق SSL/TLS

```ring
load "praetorian.ring"

oPraetorian = CreatePraetorian()

# فحص SSL شامل
aSSLReport = oPraetorian.Crypto.SSLChecker.comprehensiveSSLCheck("example.com", 443)

# طباعة تفاصيل الشهادة
aCert = aSSLReport[:certificate]
? "الشهادة صالحة حتى: " + aCert[:valid_to]
? "الأيام المتبقية: " + aCert[:days_until_expiry]

# فحص المشاكل الأمنية
if len(aSSLReport[:security_issues]) > 0
    ? "تم العثور على مشاكل أمنية:"
    for cIssue in aSSLReport[:security_issues]
        ? "  - " + cIssue
    next
ok
```

## 🔧 الإعدادات المتقدمة

### تخصيص نظام التسجيل

```ring
# إنشاء مثيل مخصص من نظام التسجيل
oLogger = new PraetorianLogger
oLogger.setLogLevel(LOG_LEVEL_DEBUG)
oLogger.setLogFile("my_custom.log")
oLogger.enableConsoleLogging(true)
```

### تخصيص فاحص الشبكة

```ring
# تخصيص إعدادات الفحص
oPraetorian.Network.Scanner.setTimeout(5000)  # 5 ثوان
oPraetorian.Network.Scanner.setMaxThreads(100)
oPraetorian.Network.Scanner.setVerbose(true)
```

### تخصيص عميل HTTP

```ring
# تخصيص عميل HTTP
oPraetorian.Web.HTTPClient.setUserAgent("Custom-Agent/1.0")
oPraetorian.Web.HTTPClient.setTimeout(30)
oPraetorian.Web.HTTPClient.setVerifySSL(false)
```

## 📖 الوثائق التفصيلية

### وحدة الشبكات

#### فاحص المنافذ
- `scanCommonPorts(cHost)` - فحص المنافذ الشائعة
- `scanPortRange(cHost, nStart, nEnd)` - فحص نطاق من المنافذ
- `bannerGrab(cHost, nPort)` - جلب البانر من منفذ
- `comprehensiveScan(cHost, aPorts)` - فحص شامل مع البانر

#### صانع الحزم
- `createTCPPacket(...)` - إنشاء حزمة TCP
- `createICMPPacket(...)` - إنشاء حزمة ICMP
- `sendRawPacket(...)` - إرسال حزمة خام

### وحدة الويب

#### عميل HTTP
- `get(cURL, aHeaders)` - طلب GET
- `post(cURL, cData, aHeaders)` - طلب POST
- `downloadFile(cURL, cFile)` - تحميل ملف

#### الزاحف
- `crawl(cStartURL)` - بدء الزحف
- `setMaxDepth(nDepth)` - تعيين العمق الأقصى
- `setMaxPages(nPages)` - تعيين عدد الصفحات الأقصى

#### Fuzzer
- `quickFuzz(cURL)` - Fuzzing سريع
- `fuzzDirectories(cURL, aWordlist)` - Fuzzing المجلدات
- `fuzzFiles(cURL, aWordlist, aExtensions)` - Fuzzing الملفات

### وحدة التشفير

#### مدقق SSL
- `comprehensiveSSLCheck(cHost, nPort)` - فحص SSL شامل
- `getCertificateDetails(cHost, nPort)` - تفاصيل الشهادة
- `checkWeakCiphers(cHost, nPort)` - فحص الشيفرات الضعيفة

## ⚠️ تحذيرات مهمة

1. **الاستخدام الأخلاقي**: استخدم هذه المكتبة فقط على الأنظمة التي تملك إذناً لفحصها
2. **الصلاحيات**: بعض الوظائف (مثل Raw Sockets) تتطلب صلاحيات المدير
3. **الأداء**: استخدم إعدادات التأخير المناسبة لتجنب إرهاق الخوادم المستهدفة
4. **القانونية**: تأكد من الامتثال للقوانين المحلية قبل الاستخدام

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch للميزة الجديدة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👥 الفريق

- **Praetorian Team** - التطوير الأساسي

## 🙏 شكر وتقدير

- فريق تطوير لغة Ring
- مجتمع Ring العربي
- جميع المساهمين في المشروع

## 📞 التواصل

- **GitHub Issues**: للإبلاغ عن الأخطاء أو طلب الميزات
- **المجتمع**: انضم إلى مجتمع Ring العربي

---

**ملاحظة**: هذه المكتبة مخصصة للأغراض التعليمية والاختبار الأخلاقي فقط. المطورون غير مسؤولين عن أي استخدام غير قانوني أو ضار.

import React from 'react'
import SiteLogo from '@/components/SiteLogo'

const Loading = () => {
  return (
    <section className='min-h-screen bg-gray-50 flex items-center justify-center' dir="rtl">
      <div className="text-center">
        {/* شعار الموقع */}
        <div className="flex justify-center mb-6">
          <SiteLogo size="lg" showText={false} />
        </div>

        <div role="status" className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-[var(--primary-color)] border-t-transparent mb-4"></div>
          <p className="text-gray-600 text-lg">جاري التحميل...</p>
        </div>
      </div>
    </section>
  )
}

export default Loading
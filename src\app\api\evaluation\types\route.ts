import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { EvaluationType } from "@prisma/client";

// GET /api/evaluation/types
export async function GET() {
  try {
    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    // جلب أنواع التقييم من قاعدة البيانات
    const dbEvaluationTypes = await prisma.customEvaluationType.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    // إذا كانت قاعدة البيانات تحتوي على أنواع تقييم، نعرضها
    if (dbEvaluationTypes.length > 0) {
      return NextResponse.json(dbEvaluationTypes, { status: 200 });
    }

    // إذا لم تكن هناك أنواع تقييم في قاعدة البيانات، نستخدم القيم الافتراضية من Enum
    const defaultEvaluationTypes = Object.values(EvaluationType).map(type => ({
      id: type,
      name: type,
      description: getEvaluationTypeDescription(type)
    }));

    // إضافة القيم الافتراضية إلى قاعدة البيانات
    for (const type of defaultEvaluationTypes) {
      await prisma.customEvaluationType.create({
        data: {
          id: type.id,
          name: type.name,
          description: type.description
        }
      });
    }

    return NextResponse.json(defaultEvaluationTypes, { status: 200 });
  } catch (error) {
    console.error("Error fetching evaluation types:", error);
    return NextResponse.json(
      {
        error: "حدث خطأ أثناء جلب أنواع التقييم",
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

// POST /api/evaluation/types
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !description) {
      return NextResponse.json({
        error: "اسم ووصف نوع التقييم مطلوبان",
        success: false
      }, { status: 400 });
    }

    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    // إنشاء نوع تقييم جديد
    const evaluationType = await prisma.customEvaluationType.create({
      data: {
        name,
        description
      }
    });

    return NextResponse.json({
      data: evaluationType,
      success: true,
      message: "تم إنشاء نوع التقييم بنجاح"
    });
  } catch (error) {
    console.error("Error creating evaluation type:", error);
    return NextResponse.json({
      error: "حدث خطأ أثناء إنشاء نوع التقييم",
      success: false
    }, { status: 500 });
  }
}

// PUT /api/evaluation/types
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, description } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !name || !description) {
      return NextResponse.json({
        error: "المعرف واسم ووصف نوع التقييم مطلوبان",
        success: false
      }, { status: 400 });
    }

    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    // تحديث نوع التقييم
    const evaluationType = await prisma.customEvaluationType.update({
      where: { id },
      data: {
        name,
        description
      }
    });

    return NextResponse.json({
      data: evaluationType,
      success: true,
      message: "تم تحديث نوع التقييم بنجاح"
    });
  } catch (error) {
    console.error("Error updating evaluation type:", error);
    return NextResponse.json({
      error: "حدث خطأ أثناء تحديث نوع التقييم",
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/evaluation/types
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json({
        error: "معرف نوع التقييم مطلوب",
        success: false
      }, { status: 400 });
    }

    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    // حذف نوع التقييم
    await prisma.customEvaluationType.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: "تم حذف نوع التقييم بنجاح"
    });
  } catch (error) {
    console.error("Error deleting evaluation type:", error);
    return NextResponse.json({
      error: "حدث خطأ أثناء حذف نوع التقييم",
      success: false
    }, { status: 500 });
  }
}

// Helper function to get description for each evaluation type
function getEvaluationTypeDescription(type: EvaluationType): string {
  const descriptions: Record<EvaluationType, string> = {
    WRITTEN_EXAM: "امتحان تحريري",
    ORAL_EXAM: "امتحان شفهي",
    HOMEWORK: "واجب منزلي",
    PROJECT: "مشروع",
    QURAN_RECITATION: "تلاوة القرآن",
    QURAN_MEMORIZATION: "حفظ القرآن",
    PRACTICAL_TEST: "اختبار عملي",
    REMOTE_EXAM: "امتحان عن بعد"
  };

  return descriptions[type] || type;
}

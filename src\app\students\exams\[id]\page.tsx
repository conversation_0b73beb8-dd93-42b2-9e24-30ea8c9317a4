'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { toast } from 'react-hot-toast';
import { Loader2, Clock, AlertCircle, CheckCircle, ChevronLeft, ChevronRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';

interface Exam {
  id: number;
  evaluationType: string;
  month: string;
  description: string | null;
  maxPoints: number;
  passingPoints: number;
  hasAutoGrading: boolean;
  examType: {
    id: number;
    name: string;
  } | null;
}

interface ExamQuestion {
  id: number;
  examId: number;
  questionId: number;
  order: number;
  points: number | null;
  question: {
    id: number;
    text: string;
    type: string;
    difficultyLevel: string;
    points: number;
    options: {
      id: number;
      text: string;
      isCorrect: boolean;
      order: number;
    }[];
    answers: {
      id: number;
      text: string;
      isCorrect: boolean;
      explanation: string | null;
    }[];
  };
}

interface ExamPoint {
  id: number;
  examId: number;
  studentId: number;
  classSubjectId: number;
  grade: number;
  status: string;
}

export default function TakeExamPage() {
  const params = useParams();
  const router = useRouter();
  const examId = params?.id as string;

  const [exam, setExam] = useState<Exam | null>(null);
  const [examQuestions, setExamQuestions] = useState<ExamQuestion[]>([]);
  const [examPoint, setExamPoint] = useState<ExamPoint | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirmSubmitOpen, setIsConfirmSubmitOpen] = useState(false);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [examStartTime, setExamStartTime] = useState<Date | null>(null);
  const [examCompleted, setExamCompleted] = useState(false);

  useEffect(() => {
    if (!examId) {
      toast.error('معرف الامتحان مطلوب');
      router.push('/students/exams');
      return;
    }

    fetchExam();
    fetchExamQuestions();
    fetchExamPoint();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examId, router]);

  // تحديث الوقت المتبقي كل ثانية
  useEffect(() => {
    if (!examStartTime || examCompleted) return;

    const timer = setInterval(() => {
      const now = new Date();
      const elapsedMinutes = Math.floor((now.getTime() - examStartTime.getTime()) / 60000);
      const examDuration = 60; // مدة الامتحان بالدقائق (يمكن تغييرها حسب الحاجة)
      const remaining = Math.max(0, examDuration - elapsedMinutes);

      setTimeLeft(remaining);

      if (remaining <= 0) {
        clearInterval(timer);
        handleTimeUp();
      }
    }, 1000);

    return () => clearInterval(timer);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examStartTime, examCompleted]);

  const fetchExam = async () => {
    try {
      const response = await fetch(`/api/evaluation/exams/${examId}`);
      const result = await response.json();

      if (result.success) {
        setExam(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب بيانات الامتحان');
        router.push('/students/exams');
      }
    } catch (error) {
      console.error('Error fetching exam:', error);
      toast.error('حدث خطأ أثناء جلب بيانات الامتحان');
      router.push('/students/exams');
    }
  };

  const fetchExamQuestions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/exam-questions?examId=${examId}`);
      const result = await response.json();

      if (result.success) {
        // ترتيب الأسئلة حسب الترتيب المحدد
        const sortedQuestions = result.data.sort((a: ExamQuestion, b: ExamQuestion) => a.order - b.order);
        setExamQuestions(sortedQuestions);

        // تعيين وقت بدء الامتحان إذا لم يكن قد تم تعيينه من قبل
        if (!examStartTime) {
          setExamStartTime(new Date());
        }
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب أسئلة الامتحان');
        setExamQuestions([]);
      }
    } catch (error) {
      console.error('Error fetching exam questions:', error);
      toast.error('حدث خطأ أثناء جلب أسئلة الامتحان');
      setExamQuestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchExamPoint = async () => {
    try {
      const response = await fetch(`/api/student/exam-points?examId=${examId}`);
      const result = await response.json();

      if (result.success && result.data) {
        setExamPoint(result.data);

        // إذا كان الامتحان قد تم إكماله بالفعل
        if (result.data.status !== 'PENDING') {
          setExamCompleted(true);
        }

        // جلب الإجابات المحفوظة مسبقًا إن وجدت
        fetchSavedAnswers(result.data.id);
      }
    } catch (error) {
      console.error('Error fetching exam point:', error);
    }
  };

  const fetchSavedAnswers = async (examPointId: number) => {
    try {
      const response = await fetch(`/api/student-answers?examPointId=${examPointId}`);
      const result = await response.json();

      if (result.success && result.data) {
        const savedAnswers: Record<number, string> = {};
        result.data.forEach((answer: { examQuestionId: number; answer: string }) => {
          savedAnswers[answer.examQuestionId] = answer.answer;
        });
        setAnswers(savedAnswers);
      }
    } catch (error) {
      console.error('Error fetching saved answers:', error);
    }
  };

  const handleTimeUp = () => {
    toast.error('انتهى وقت الامتحان!');
    handleSubmitExam();
  };

  const handleAnswerChange = (questionId: number, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));

    // حفظ الإجابة في قاعدة البيانات
    if (examPoint) {
      saveAnswer(questionId, answer);
    }
  };

  const saveAnswer = async (examQuestionId: number, answer: string) => {
    if (!examPoint) return;

    try {
      // التحقق مما إذا كانت الإجابة موجودة مسبقًا
      const checkResponse = await fetch(`/api/student-answers?examPointId=${examPoint.id}&examQuestionId=${examQuestionId}`);
      const checkResult = await checkResponse.json();

      if (checkResult.success && checkResult.data && checkResult.data.length > 0) {
        // تحديث الإجابة الموجودة
        await fetch('/api/student-answers', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            id: checkResult.data[0].id,
            answer
          }),
        });
      } else {
        // إنشاء إجابة جديدة
        await fetch('/api/student-answers', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            examPointId: examPoint.id,
            examQuestionId,
            answer
          }),
        });
      }
    } catch (error) {
      console.error('Error saving answer:', error);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < examQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleGoToQuestion = (index: number) => {
    setCurrentQuestionIndex(index);
  };

  const handleSubmitExam = async () => {
    if (!examPoint) return;

    setIsSubmitting(true);

    try {
      // تحديث حالة نقطة الامتحان إلى "مكتمل"
      const response = await fetch(`/api/student/exam-points/${examPoint.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'COMPLETED'
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('تم تقديم الامتحان بنجاح');
        setExamCompleted(true);
        router.push(`/students/exams/results/${examId}`);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء تقديم الامتحان');
      }
    } catch (error) {
      console.error('Error submitting exam:', error);
      toast.error('حدث خطأ أثناء تقديم الامتحان');
    } finally {
      setIsSubmitting(false);
      setIsConfirmSubmitOpen(false);
    }
  };

  const formatTime = (minutes: number) => {
    const hrs = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hrs > 0 ? `${hrs} ساعة ${mins > 0 ? 'و' : ''}` : ''}${mins > 0 ? `${mins} دقيقة` : ''}`;
  };

  const getQuestionTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      MULTIPLE_CHOICE: 'اختيار من متعدد',
      TRUE_FALSE: 'صح أو خطأ',
      SHORT_ANSWER: 'إجابة قصيرة',
      ESSAY: 'مقال',
      MATCHING: 'مطابقة',
      FILL_BLANK: 'ملء الفراغات',
      ORDERING: 'ترتيب'
    };
    return types[type] || type;
  };

  const getEvaluationTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      QURAN_MEMORIZATION: 'حفظ القرآن',
      QURAN_RECITATION: 'تلاوة القرآن',
      WRITTEN_EXAM: 'تحريري',
      ORAL_EXAM: 'شفهي',
      PRACTICAL_TEST: 'عملي',
      HOMEWORK: 'واجب منزلي',
      PROJECT: 'مشروع',
      REMOTE_EXAM: 'امتحان عن بعد'
    };
    return labels[type] || type;
  };

  const formatDate = (dateString: string): string => {
    const [year, month] = dateString.split('-');
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const renderQuestion = () => {
    if (examQuestions.length === 0) return null;

    const currentQuestion = examQuestions[currentQuestionIndex];
    if (!currentQuestion) return null;

    const questionType = currentQuestion.question.type;
    const questionId = currentQuestion.id;
    const currentAnswer = answers[questionId] || '';

    return (
      <div className="space-y-6">
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2 text-right">السؤال {currentQuestionIndex + 1}:</h3>
          <p className="text-right">{currentQuestion.question.text}</p>
          <div className="flex justify-between items-center mt-2">
            <Badge className="bg-blue-100 text-blue-800">
              {currentQuestion.points || currentQuestion.question.points} نقطة
            </Badge>
            <Badge className="bg-purple-100 text-purple-800">
              {getQuestionTypeLabel(questionType)}
            </Badge>
          </div>
        </div>

        <div className="p-4 border rounded-lg">
          {questionType === 'MULTIPLE_CHOICE' && (
            <RadioGroup
              value={currentAnswer}
              onValueChange={(value) => handleAnswerChange(questionId, value)}
              className="space-y-3"
            >
              {currentQuestion.question.options.map((option) => (
                <div key={option.id} className="flex items-center space-x-2 space-x-reverse">
                  <RadioGroupItem value={option.text} id={`option-${option.id}`} />
                  <Label htmlFor={`option-${option.id}`} className="text-right flex-1">
                    {option.text}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          )}

          {questionType === 'TRUE_FALSE' && (
            <RadioGroup
              value={currentAnswer}
              onValueChange={(value) => handleAnswerChange(questionId, value)}
              className="space-y-3"
            >
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="صح" id="option-true" />
                <Label htmlFor="option-true" className="text-right">صح</Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="خطأ" id="option-false" />
                <Label htmlFor="option-false" className="text-right">خطأ</Label>
              </div>
            </RadioGroup>
          )}

          {(questionType === 'SHORT_ANSWER' || questionType === 'ESSAY' || questionType === 'FILL_BLANK') && (
            <Textarea
              value={currentAnswer}
              onChange={(e) => handleAnswerChange(questionId, e.target.value)}
              placeholder="اكتب إجابتك هنا..."
              className="min-h-[150px] text-right"
              dir="rtl"
            />
          )}

          {questionType === 'MATCHING' && (
            <div className="space-y-4">
              {currentQuestion.question.options.map((option) => (
                <div key={option.id} className="flex flex-col space-y-2">
                  <Label className="text-right">{option.text}</Label>
                  <Textarea
                    value={currentAnswer.split('|')[option.order] || ''}
                    onChange={(e) => {
                      const answers = currentAnswer.split('|');
                      answers[option.order] = e.target.value;
                      handleAnswerChange(questionId, answers.join('|'));
                    }}
                    placeholder="اكتب إجابتك هنا..."
                    className="text-right"
                    dir="rtl"
                  />
                </div>
              ))}
            </div>
          )}

          {questionType === 'ORDERING' && (
            <div className="space-y-4">
              {currentQuestion.question.options.map((option, index) => (
                <div key={option.id} className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="number"
                    min="1"
                    max={currentQuestion.question.options.length}
                    value={
                      currentAnswer.split('|')[index]
                        ? parseInt(currentAnswer.split('|')[index])
                        : ''
                    }
                    onChange={(e) => {
                      const answers = currentAnswer.split('|');
                      answers[index] = e.target.value;
                      handleAnswerChange(questionId, answers.join('|'));
                    }}
                    className="w-16 p-2 border rounded text-center"
                  />
                  <Label className="text-right flex-1">{option.text}</Label>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
      </div>
    );
  }

  if (examCompleted) {
    return (
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="text-center">تم إكمال الامتحان</CardTitle>
            <CardDescription className="text-center">لقد قمت بإكمال هذا الامتحان بالفعل</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center">
              <CheckCircle className="h-16 w-16 text-primary-color" />
            </div>
            <p className="text-center">يمكنك الآن مشاهدة نتائجك</p>
            <div className="flex justify-center">
              <Button
                onClick={() => router.push(`/students/exams/results/${examId}`)}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                عرض النتائج
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      {exam && (
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-[var(--primary-color)] text-center mb-2">
            {getEvaluationTypeLabel(exam.evaluationType)} - {formatDate(exam.month)}
          </h1>
          {exam.examType && (
            <p className="text-center text-gray-600 mb-4">{exam.examType.name}</p>
          )}
          {timeLeft !== null && (
            <div className="flex justify-center items-center space-x-2 space-x-reverse">
              <Clock className="h-5 w-5 text-orange-500" />
              <span className="text-orange-500 font-medium">
                الوقت المتبقي: {formatTime(timeLeft)}
              </span>
            </div>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-3">
          <Card>
            <CardContent className="p-6">
              {renderQuestion()}

              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevQuestion}
                  disabled={currentQuestionIndex === 0}
                >
                  <ChevronRight className="ml-2" size={16} />
                  السؤال السابق
                </Button>

                {currentQuestionIndex === examQuestions.length - 1 ? (
                  <Button
                    onClick={() => setIsConfirmSubmitOpen(true)}
                    className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                  >
                    إنهاء الامتحان
                  </Button>
                ) : (
                  <Button
                    onClick={handleNextQuestion}
                    className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                  >
                    السؤال التالي
                    <ChevronLeft className="mr-2" size={16} />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">أسئلة الامتحان</CardTitle>
              <CardDescription>
                {Object.keys(answers).length} / {examQuestions.length} تمت الإجابة
              </CardDescription>
              <Progress
                value={(Object.keys(answers).length / examQuestions.length) * 100}
                className="h-2 mt-2"
              />
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-2">
                {examQuestions.map((q, index) => {
                  const isAnswered = answers[q.id] !== undefined;
                  const isCurrent = index === currentQuestionIndex;

                  return (
                    <Button
                      key={q.id}
                      variant="outline"
                      className={`h-10 w-10 p-0 ${
                        isCurrent
                          ? 'border-[var(--primary-color)] border-2'
                          : isAnswered
                          ? 'bg-green-50 border-green-200'
                          : ''
                      }`}
                      onClick={() => handleGoToQuestion(index)}
                    >
                      {index + 1}
                    </Button>
                  );
                })}
              </div>

              <Button
                onClick={() => setIsConfirmSubmitOpen(true)}
                className="w-full mt-4 bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                إنهاء الامتحان وتسليم الإجابات
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Confirm Submit Dialog */}
      <Dialog open={isConfirmSubmitOpen} onOpenChange={setIsConfirmSubmitOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>تأكيد تسليم الامتحان</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في إنهاء الامتحان وتسليم إجاباتك؟
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {Object.keys(answers).length < examQuestions.length && (
              <div className="flex items-start space-x-2 space-x-reverse p-3 bg-amber-50 border border-amber-200 rounded-md">
                <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <p className="text-amber-800 text-sm">
                    لم تقم بالإجابة على جميع الأسئلة بعد. هناك {examQuestions.length - Object.keys(answers).length} أسئلة لم تتم الإجابة عليها.
                  </p>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-2 space-x-reverse">
              <Button
                variant="outline"
                onClick={() => setIsConfirmSubmitOpen(false)}
              >
                العودة للامتحان
              </Button>
              <Button
                onClick={handleSubmitExam}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري التسليم...
                  </>
                ) : (
                  'تأكيد التسليم'
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

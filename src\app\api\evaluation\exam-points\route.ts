import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const examId = searchParams.get('examId');
    const studentId = searchParams.get('studentId');
    const classSubjectId = searchParams.get('classSubjectId');
    const classId = searchParams.get('classId');
    const teacherId = searchParams.get('teacherId');

    console.log('Fetching exam points with params:', {
      examId, studentId, classSubjectId, classId, teacherId
    });

    // بناء شروط البحث
    const where: {
      examId?: number;
      studentId?: number;
      classSubjectId?: number;
      student?: { classeId: number };
      classSubject?: { teacherSubject: { teacherId: number } };
    } = {};

    if (examId) {
      where.examId = parseInt(examId);
    }

    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    if (classSubjectId) {
      where.classSubjectId = parseInt(classSubjectId);
    }

    // إذا تم تحديد معرف الفصل، نبحث عن جميع نقاط الامتحان للطلاب في هذا الفصل
    if (classId) {
      where.student = {
        classeId: parseInt(classId)
      };
    }

    // إذا تم تحديد معرف المعلم، نبحث عن نقاط الامتحان للفصول التي يدرسها هذا المعلم
    if (teacherId) {
      where.classSubject = {
        teacherSubject: {
          teacherId: parseInt(teacherId)
        }
      };
    }

    console.log('Query where clause:', where);

    // جلب نقاط الامتحان مع البيانات المرتبطة
    const examPoints = await prisma.exam_points.findMany({
      where,
      include: {
        student: {
          include: {
            classe: true
          }
        },
        exam: {
          include: {
            examType: true
          }
        },
        surah: true,
        classSubject: {
          include: {
            classe: true,
            teacherSubject: {
              include: {
                subject: true,
                teacher: true
              }
            }
          }
        },
        criteriaScores: {
          include: {
            criteria: true
          }
        }
      },
      orderBy: [
        { classSubject: { classe: { name: 'asc' } } },
        { student: { name: 'asc' } }
      ]
    });

    console.log(`Found ${examPoints.length} exam points`);

    return NextResponse.json({
      data: examPoints,
      success: true,
      count: examPoints.length,
      message: 'تم جلب نقاط الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error fetching exam points:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب نقاط الامتحان',
      success: false
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      examId,
      studentId,
      classSubjectId,
      grade,
      note,
      surahId,
      startVerse,
      endVerse,
      teacherId,
      criteriaScores
    } = body;

    // Validate required fields
    if (!examId || !studentId || !classSubjectId || grade === undefined) {
      return NextResponse.json(
        {
          error: 'Missing required fields: examId, studentId, classSubjectId, and grade are required',
          success: false
        },
        { status: 400 }
      );
    }

    // تحويل البيانات إلى أرقام
    const numericGrade = Number(grade);
    const numericExamId = Number(examId);
    const numericStudentId = Number(studentId);
    const numericClassSubjectId = Number(classSubjectId);

    // التحقق من صحة البيانات
    if (
      isNaN(numericGrade) ||
      numericGrade < 0 ||
      numericGrade > 10 ||
      isNaN(numericExamId) ||
      isNaN(numericStudentId) ||
      isNaN(numericClassSubjectId)
    ) {
      return NextResponse.json(
        {
          error: 'Invalid input: grade must be a number between 0 and 10, and IDs must be valid numbers',
          success: false
        },
        { status: 400 }
      );
    }

    // التحقق من وجود نقطة امتحان مسبقة
    const existingExamPoint = await prisma.exam_points.findUnique({
      where: {
        studentId_classSubjectId_examId: {
          studentId: numericStudentId,
          classSubjectId: numericClassSubjectId,
          examId: numericExamId
        }
      }
    });

    // إنشاء أو تحديث نقطة الامتحان
    let examPoint;

    if (existingExamPoint) {
      // تحديث نقطة الامتحان الموجودة
      const updateData: {
        grade: number;
        note?: string | null;
        surahId?: number;
        startVerse?: number;
        endVerse?: number;
      } = {
        grade: numericGrade,
        note
      };

      // إضافة معلومات السورة والآيات إذا تم توفيرها
      if (surahId && startVerse && endVerse) {
        updateData.surahId = Number(surahId);
        updateData.startVerse = Number(startVerse);
        updateData.endVerse = Number(endVerse);
      }

      // تحديث نقطة الامتحان
      examPoint = await prisma.$transaction(async (tx) => {
        // تحديث نقطة الامتحان
        const updatedPoint = await tx.exam_points.update({
          where: { id: existingExamPoint.id },
          data: updateData,
          include: {
            student: true,
            exam: true,
            surah: true,
            classSubject: {
              include: {
                classe: true,
                teacherSubject: {
                  include: {
                    teacher: true,
                    subject: true
                  }
                }
              }
            }
          }
        });

        // إذا تم توفير معايير التقييم، نقوم بتحديثها
        if (criteriaScores && Array.isArray(criteriaScores)) {
          // حذف معايير التقييم الموجودة
          await tx.criteriaScore.deleteMany({
            where: { examPointId: existingExamPoint.id }
          });

          // إنشاء معايير التقييم الجديدة
          for (const score of criteriaScores) {
            await tx.criteriaScore.create({
              data: {
                examPointId: existingExamPoint.id,
                criteriaId: Number(score.criteriaId),
                score: Number(score.score)
              }
            });
          }
        }

        return updatedPoint;
      });

      return NextResponse.json({
        data: examPoint,
        success: true,
        message: 'تم تحديث نقاط الامتحان بنجاح'
      });
    } else {
      // إنشاء نقطة امتحان جديدة
      examPoint = await prisma.$transaction(async (tx) => {
        // التحقق من وجود الامتحان والطالب والمادة
        const [exam, student, classSubject] = await Promise.all([
          tx.exam.findUnique({ where: { id: numericExamId } }),
          tx.student.findUnique({ where: { id: numericStudentId } }),
          tx.classSubject.findUnique({
            where: { id: numericClassSubjectId },
            include: {
              teacherSubject: {
                include: {
                  teacher: true
                }
              }
            }
          })
        ]);

        if (!exam || !student || !classSubject) {
          throw new Error('Referenced exam, student, or class subject does not exist');
        }

        // التحقق من صلاحيات المعلم
        if (teacherId) {
          const isTeacherAuthorized = classSubject.teacherSubject.teacherId === parseInt(teacherId);
          if (!isTeacherAuthorized) {
            throw new Error('Teacher is not authorized to record exam points for this class');
          }
        }

        // إنشاء نقطة الامتحان
        const createData: {
          examId: number;
          studentId: number;
          classSubjectId: number;
          grade: number;
          note?: string | null;
          surahId?: number;
          startVerse?: number;
          endVerse?: number;
        } = {
          examId: numericExamId,
          studentId: numericStudentId,
          classSubjectId: numericClassSubjectId,
          grade: numericGrade,
          note
        };

        // إضافة معلومات السورة والآيات إذا تم توفيرها
        if (surahId && startVerse && endVerse) {
          createData.surahId = Number(surahId);
          createData.startVerse = Number(startVerse);
          createData.endVerse = Number(endVerse);
        }

        // إنشاء نقطة الامتحان
        const newPoint = await tx.exam_points.create({
          data: createData,
          include: {
            student: true,
            exam: true,
            surah: true,
            classSubject: {
              include: {
                classe: true,
                teacherSubject: {
                  include: {
                    teacher: true,
                    subject: true
                  }
                }
              }
            }
          }
        });

        // إذا تم توفير معايير التقييم، نقوم بإنشائها
        if (criteriaScores && Array.isArray(criteriaScores)) {
          for (const score of criteriaScores) {
            await tx.criteriaScore.create({
              data: {
                examPointId: newPoint.id,
                criteriaId: Number(score.criteriaId),
                score: Number(score.score)
              }
            });
          }
        }

        return newPoint;
      });

      return NextResponse.json({
        data: examPoint,
        success: true,
        message: 'تم تسجيل نقاط الامتحان بنجاح'
      });
    }
  } catch (error) {
    console.error('Error creating/updating exam point:', error);

    // التعامل مع أنواع الأخطاء المختلفة
    if (error instanceof Error) {
      if (error.message === 'Referenced exam, student, or class subject does not exist') {
        return NextResponse.json({
          error: 'الامتحان أو الطالب أو المادة غير موجودة',
          success: false
        }, { status: 404 });
      } else if (error.message === 'Teacher is not authorized to record exam points for this class') {
        return NextResponse.json({
          error: 'ليس لديك صلاحية تسجيل نقاط الامتحان لهذا الفصل',
          success: false
        }, { status: 403 });
      }
    }

    return NextResponse.json({
      error: 'حدث خطأ أثناء تسجيل نقاط الامتحان',
      success: false
    }, { status: 500 });
  }
}

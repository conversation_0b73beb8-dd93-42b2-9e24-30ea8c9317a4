# نظام خلفيات الصفحات العامة

## نظرة عامة

نظام خلفيات الصفحات العامة يسمح بإدارة وتخصيص خلفيات الصفحات العامة في الموقع بطريقة فنية ومرنة. يمكن للمدير رفع صور خلفية مخصصة لكل صفحة مع إمكانية التحكم في جميع خصائص العرض.

## المميزات

### 🎨 إدارة شاملة للخلفيات
- رفع صور خلفية مخصصة لكل صفحة
- تحكم كامل في موضع وحجم وتكرار الصورة
- إضافة طبقة علوية ملونة مع تحكم في الشفافية
- معاينة فورية للخلفية أثناء التعديل
- تفعيل/إلغاء تفعيل الخلفيات
- نظام أولويات للخلفيات

### 📱 تجربة مستخدم محسنة
- واجهة إدارة سهلة الاستخدام
- معاينة مباشرة للتغييرات
- دعم جميع أنواع الصور الشائعة
- تحسين الأداء مع التخزين المؤقت

## الهيكل التقني

### قاعدة البيانات

```sql
-- جدول خلفيات الصفحات العامة
CREATE TABLE PageBackground (
  id              INT PRIMARY KEY AUTO_INCREMENT,
  pageName        VARCHAR(255) UNIQUE NOT NULL,  -- اسم الصفحة
  displayName     VARCHAR(255) NOT NULL,         -- الاسم المعروض
  imageUrl        VARCHAR(500),                  -- رابط صورة الخلفية
  overlayColor    VARCHAR(50),                   -- لون الطبقة العلوية
  overlayOpacity  FLOAT DEFAULT 0.5,             -- شفافية الطبقة العلوية
  position        VARCHAR(50) DEFAULT 'center',  -- موضع الصورة
  size            VARCHAR(50) DEFAULT 'cover',   -- حجم الصورة
  repeat          VARCHAR(50) DEFAULT 'no-repeat', -- تكرار الصورة
  attachment      VARCHAR(50) DEFAULT 'scroll',  -- ثبات الصورة
  isActive        BOOLEAN DEFAULT true,          -- هل الخلفية نشطة
  priority        INT DEFAULT 0,                 -- أولوية الخلفية
  createdAt       DATETIME DEFAULT CURRENT_TIMESTAMP,
  updatedAt       DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### API Endpoints

#### GET /api/page-backgrounds
جلب جميع خلفيات الصفحات مع إمكانية الفلترة

**Parameters:**
- `pageName` (optional): فلترة حسب اسم الصفحة
- `isActive` (optional): فلترة حسب حالة النشاط

#### GET /api/page-backgrounds/[pageName]
جلب خلفية صفحة محددة

#### POST /api/page-backgrounds
إضافة خلفية صفحة جديدة

**Body:**
```json
{
  "pageName": "home",
  "displayName": "الصفحة الرئيسية",
  "imageUrl": "/uploads/backgrounds/image.jpg",
  "overlayColor": "#136907",
  "overlayOpacity": 0.5,
  "position": "center",
  "size": "cover",
  "repeat": "no-repeat",
  "attachment": "scroll",
  "isActive": true,
  "priority": 1
}
```

#### PUT /api/page-backgrounds
تحديث خلفية صفحة موجودة

#### DELETE /api/page-backgrounds?id=[id]
حذف خلفية صفحة

### المكونات (Components)

#### PageBackground
مكون لعرض خلفية الصفحة مع دعم الخلفيات الاحتياطية

```tsx
<PageBackground 
  pageName="home" 
  className="min-h-screen" 
  fallbackBackground="linear-gradient(to bottom, var(--primary-color), var(--secondary-color))"
>
  {/* محتوى الصفحة */}
</PageBackground>
```

#### PageBackgroundsManager
مكون إدارة خلفيات الصفحات في لوحة التحكم

```tsx
<PageBackgroundsManager className="mt-8" />
```

#### usePageBackground Hook
Hook لاستخدام خلفيات الصفحات

```tsx
const { background, loading, backgroundStyles, overlayStyles, hasBackground } = usePageBackground('home');
```

## الاستخدام

### 1. إضافة خلفية لصفحة جديدة

```tsx
import PageBackground from '@/components/PageBackground';

const MyPage = () => {
  return (
    <PageBackground 
      pageName="my-page" 
      className="min-h-screen"
      fallbackBackground="#f0f0f0"
    >
      <div>
        {/* محتوى الصفحة */}
      </div>
    </PageBackground>
  );
};
```

### 2. استخدام Hook للتحكم المتقدم

```tsx
import { usePageBackground } from '@/hooks/usePageBackground';

const MyComponent = () => {
  const { background, backgroundStyles, overlayStyles, hasBackground } = usePageBackground('home');

  return (
    <div style={backgroundStyles}>
      <div style={overlayStyles} />
      <div style={{ position: 'relative', zIndex: 2 }}>
        {/* المحتوى */}
      </div>
    </div>
  );
};
```

### 3. إدارة الخلفيات من لوحة التحكم

1. اذهب إلى **الإعدادات** > **خلفيات الصفحات العامة**
2. اضغط على **إضافة خلفية جديدة**
3. اختر الصفحة المطلوبة
4. ارفع صورة الخلفية
5. اضبط الإعدادات (الموضع، الحجم، الطبقة العلوية)
6. شاهد المعاينة المباشرة
7. احفظ التغييرات

## الصفحات المدعومة

- `home` - الصفحة الرئيسية
- `about` - من نحن
- `contact` - اتصل بنا
- `programs` - البرامج
- `khatm-sessions` - مجالس الختم
- `donations` - التبرعات
- `login` - تسجيل الدخول
- `register` - التسجيل

## خيارات التخصيص

### موضع الصورة (Position)
- `center` - الوسط
- `top` - الأعلى
- `bottom` - الأسفل
- `left` - اليسار
- `right` - اليمين
- `top left` - أعلى اليسار
- `top right` - أعلى اليمين
- `bottom left` - أسفل اليسار
- `bottom right` - أسفل اليمين

### حجم الصورة (Size)
- `cover` - تغطية كاملة
- `contain` - احتواء
- `auto` - تلقائي
- `100% 100%` - تمدد كامل

### تكرار الصورة (Repeat)
- `no-repeat` - بدون تكرار
- `repeat` - تكرار
- `repeat-x` - تكرار أفقي
- `repeat-y` - تكرار عمودي

### ثبات الصورة (Attachment)
- `scroll` - متحرك مع التمرير
- `fixed` - ثابت أثناء التمرير

## الأمان والأداء

### الأمان
- التحقق من نوع الملفات المرفوعة
- تحديد حجم الملفات المسموح
- حماية مسارات الملفات
- التحقق من صلاحيات المستخدم

### الأداء
- ضغط الصور تلقائياً
- تخزين مؤقت للصور
- تحميل كسول للخلفيات
- تحسين أحجام الصور

## استكشاف الأخطاء

### مشاكل شائعة

1. **الصورة لا تظهر**
   - تأكد من صحة مسار الصورة
   - تحقق من وجود الملف في المجلد
   - تأكد من تفعيل الخلفية

2. **الصورة لا تغطي الصفحة كاملة**
   - استخدم `size: 'cover'`
   - تأكد من أبعاد الصورة مناسبة

3. **الطبقة العلوية لا تظهر**
   - تأكد من تحديد لون الطبقة
   - اضبط قيمة الشفافية

## التطوير المستقبلي

- [ ] دعم الفيديو كخلفية
- [ ] تأثيرات انتقال متقدمة
- [ ] خلفيات متحركة
- [ ] دعم الخلفيات المتدرجة المخصصة
- [ ] نظام قوالب الخلفيات
- [ ] تحسين SEO للصور

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import RegistrationReceipt from '@/components/admin/students/RegistrationReceipt';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'react-toastify';
import { FaArrowLeft, FaReceipt, FaPlus, FaList } from 'react-icons/fa';

interface Student {
  id: number;
  name: string;
  username: string;
}

interface ReceiptSummary {
  id: number;
  receiptNumber: string;
  issueDate: string;
  registrationFee: number;
  paymentStatus: string;
  isPrinted: boolean;
}

export default function RegistrationReceiptPage() {
  const router = useRouter();
  const params = useParams();
  const studentId = parseInt(params.id as string);

  const [student, setStudent] = useState<Student | null>(null);
  const [receipts, setReceipts] = useState<ReceiptSummary[]>([]);
  const [selectedReceiptId, setSelectedReceiptId] = useState<number | null>(null);
  const [showNewReceipt, setShowNewReceipt] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // تحميل بيانات التلميذ والوصولات
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // جلب بيانات التلميذ
        const studentResponse = await fetch(`/api/students/${studentId}`);
        if (!studentResponse.ok) {
          throw new Error('فشل في تحميل بيانات التلميذ');
        }
        const studentData = await studentResponse.json();
        setStudent(studentData);

        // جلب قائمة الوصولات
        const receiptsResponse = await fetch(`/api/students/${studentId}/registration-receipt`);
        if (receiptsResponse.ok) {
          const receiptsData = await receiptsResponse.json();
          if (receiptsData.success) {
            setReceipts(receiptsData.data);

            // اختيار أحدث وصل تلقائياً
            if (receiptsData.data.length > 0) {
              setSelectedReceiptId(receiptsData.data[0].id);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('حدث خطأ أثناء تحميل البيانات');
      } finally {
        setIsLoading(false);
      }
    };

    if (studentId) {
      fetchData();
    }
  }, [studentId]);

  const handlePrint = () => {
    console.log('تم طباعة وصل التسجيل');
  };

  const handleDownload = () => {
    console.log('تم تحميل وصل التسجيل');
  };

  const handleCreateNew = () => {
    setSelectedReceiptId(null);
    setShowNewReceipt(true);
  };

  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'مدفوع';
      case 'PENDING':
        return 'في انتظار الدفع';
      case 'CANCELLED':
        return 'ملغي';
      case 'PARTIAL':
        return 'دفع جزئي';
      case 'REFUNDED':
        return 'مسترد';
      default:
        return status;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'text-green-600';
      case 'PENDING':
        return 'text-yellow-600';
      case 'CANCELLED':
        return 'text-red-600';
      case 'PARTIAL':
        return 'text-blue-600';
      case 'REFUNDED':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  if (isLoading) {
    return (
      <OptimizedProtectedRoute>
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
            <span className="mr-3">جاري تحميل البيانات...</span>
          </div>
        </div>
      </OptimizedProtectedRoute>
    );
  }

  if (!student) {
    return (
      <OptimizedProtectedRoute>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">التلميذ غير موجود</h1>
            <Button onClick={() => router.back()} variant="outline">
              <FaArrowLeft className="ml-2" />
              العودة
            </Button>
          </div>
        </div>
      </OptimizedProtectedRoute>
    );
  }

  return (
    <OptimizedProtectedRoute>
      <PermissionGuard requiredPermission="admin.students.receipt.manage">
        <div className="container mx-auto px-4 py-8 space-y-6">
          {/* رأس الصفحة */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.back()}
                variant="outline"
                className="flex items-center gap-2"
              >
                <FaArrowLeft />
                العودة
              </Button>

              <div>
                <h1 className="text-2xl font-bold text-[var(--primary-color)] flex items-center gap-2">
                  <FaReceipt />
                  وصولات التسجيل
                </h1>
                <p className="text-gray-600">
                  التلميذ: <span className="font-semibold">{student.name}</span>
                </p>
              </div>
            </div>

            <Button
              onClick={handleCreateNew}
              className="bg-[var(--primary-color)] hover:bg-[#0d7e6d] flex items-center gap-2"
            >
              <FaPlus />
              إنشاء وصل جديد
            </Button>
          </div>

          {/* اختيار الوصل */}
          {receipts.length > 0 && !showNewReceipt && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaList />
                  اختيار الوصل
                </CardTitle>
                <CardDescription>
                  اختر الوصل الذي تريد عرضه أو طباعته
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">الوصل المطلوب</label>
                    <Select
                      value={selectedReceiptId?.toString() || ''}
                      onValueChange={(value) => setSelectedReceiptId(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الوصل" />
                      </SelectTrigger>
                      <SelectContent>
                        {receipts.map((receipt) => (
                          <SelectItem key={receipt.id} value={receipt.id.toString()}>
                            {receipt.receiptNumber} - {receipt.issueDate}
                            <span className={`mr-2 ${getPaymentStatusColor(receipt.paymentStatus)}`}>
                              ({getPaymentStatusText(receipt.paymentStatus)})
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedReceiptId && (
                    <div className="space-y-2">
                      {(() => {
                        const selectedReceipt = receipts.find(r => r.id === selectedReceiptId);
                        if (!selectedReceipt) return null;

                        return (
                          <>
                            <div>
                              <span className="text-sm text-gray-600">المبلغ: </span>
                              <span className="font-semibold">{selectedReceipt.registrationFee} دج</span>
                            </div>
                            <div>
                              <span className="text-sm text-gray-600">الحالة: </span>
                              <span className={`font-semibold ${getPaymentStatusColor(selectedReceipt.paymentStatus)}`}>
                                {getPaymentStatusText(selectedReceipt.paymentStatus)}
                              </span>
                            </div>
                            <div>
                              <span className="text-sm text-gray-600">الطباعة: </span>
                              <span className={`font-semibold ${selectedReceipt.isPrinted ? 'text-green-600' : 'text-gray-600'}`}>
                                {selectedReceipt.isPrinted ? 'تم طباعته' : 'لم يتم طباعته'}
                              </span>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* عرض الوصل */}
          {(selectedReceiptId || showNewReceipt) && (
            <RegistrationReceipt
              studentId={studentId}
              receiptId={showNewReceipt ? undefined : selectedReceiptId || undefined}
              autoGenerate={showNewReceipt}
              onPrint={handlePrint}
              onDownload={handleDownload}
              onClose={() => {
                setShowNewReceipt(false);
                // إعادة تحميل قائمة الوصولات
                window.location.reload();
              }}
            />
          )}

          {/* رسالة عدم وجود وصولات */}
          {receipts.length === 0 && !showNewReceipt && (
            <Card>
              <CardContent className="p-8 text-center">
                <FaReceipt className="mx-auto text-4xl text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  لا توجد وصولات تسجيل
                </h3>
                <p className="text-gray-500 mb-4">
                  لم يتم إنشاء أي وصولات تسجيل لهذا التلميذ
                </p>
                <Button
                  onClick={handleCreateNew}
                  className="bg-[var(--primary-color)] hover:bg-[#0d7e6d]"
                >
                  <FaPlus className="ml-2" />
                  إنشاء وصل التسجيل الأول
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </PermissionGuard>
    </OptimizedProtectedRoute>
  );
}

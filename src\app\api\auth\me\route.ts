import { NextRequest, NextResponse } from 'next/server';
import { getToken } from '@/lib/auth';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // الحصول على التوكن من الكوكيز
    const token = request.cookies.get("jwtToken")?.value;

    if (!token) {
      console.log('No token found in cookies');
      return NextResponse.json(
        { error: 'غير مصرح به - لا يوجد توكن' },
        { status: 401 }
      );
    }

    // التحقق من صحة التوكن
    const userData = await getToken(token);

    if (!userData) {
      console.log('Invalid token');
      return NextResponse.json(
        { error: 'غير مصرح به - توكن غير صالح' },
        { status: 401 }
      );
    }

    console.log('User data from token:', userData);

    // جلب بيانات المستخدم من قاعدة البيانات
    const user = await prisma.user.findUnique({
      where: { id: userData.id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        roleId: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      console.log('User not found in database:', userData.id);
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    console.log('User found:', user);
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error in /api/auth/me:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

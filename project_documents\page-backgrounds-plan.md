# خطة مشروع: إنشاء صفحة خلفيات الصفحات العامة

## نظرة عامة على المشروع

### الهدف الرئيسي
إنشاء نظام شامل لإدارة خلفيات الصفحات العامة في الموقع، يسمح للمدير برفع وتخصيص خلفيات فنية لكل صفحة من صفحات الموقع العامة مع إمكانية التحكم الكامل في خصائص العرض.

### النطاق والكيانات المتأثرة

#### الصفحات المتأثرة:
- صفحة الإعدادات الإدارية (`/admin/admin-setup`)
- الصفحات العامة (الرئيسية، من نحن، اتصل بنا، البرامج، مجالس الختم، التبرعات، تسجيل الدخول، التسجيل)

#### قواعد البيانات:
- جدول `PageBackground` جديد
- جدول `SystemSettings` (للإعدادات العامة)

#### الملفات الرئيسية:
- `src/components/admin/PageBackgroundsManager.tsx`
- `src/components/PageBackground.tsx`
- `src/hooks/usePageBackground.ts`
- `src/app/api/page-backgrounds/route.ts`
- `src/app/api/page-backgrounds/[pageName]/route.ts`
- `prisma/migrations/` (ملفات الهجرة)
- `prisma/seeds/` (بيانات أولية)

## خطة العمل التنفيذية

### المرحلة الأولى: إعداد قاعدة البيانات والـ API

#### [x] **المهمة 1.1:** إنشاء جدول PageBackground ✅ **مكتملة**
- **المكونات:** `prisma/schema.prisma`
- **الوصف:** إضافة نموذج PageBackground مع جميع الحقول المطلوبة
- **الحالة:** تم إنشاء النموذج بجميع الحقول المطلوبة
- **الحقول المنفذة:**
  - id (معرف فريد) ✅
  - pageName (اسم الصفحة - فريد) ✅
  - displayName (الاسم المعروض) ✅
  - imageUrl (رابط صورة الخلفية) ✅
  - overlayColor (لون الطبقة العلوية) ✅
  - overlayOpacity (شفافية الطبقة العلوية) ✅
  - position (موضع الصورة) ✅
  - size (حجم الصورة) ✅
  - repeat (تكرار الصورة) ✅
  - attachment (ثبات الصورة) ✅
  - isActive (حالة النشاط) ✅
  - priority (الأولوية) ✅
  - createdAt, updatedAt (تواريخ الإنشاء والتحديث) ✅

#### [x] **المهمة 1.2:** إنشاء وتطبيق Migration ✅ **مكتملة**
- **المكونات:** `prisma/migrations/`
- **الوصف:** إنشاء وتطبيق migration لجدول PageBackground
- **الحالة:** تم تطبيق Migration بنجاح

#### [ ] **المهمة 1.3:** إنشاء Seed Data
- **المكونات:** `prisma/seeds/pageBackgrounds.ts`
- **الوصف:** إضافة بيانات أولية للخلفيات الافتراضية
- **البيانات الأولية:** خلفيات افتراضية للصفحات الرئيسية
- **الحالة:** ⚠️ **مطلوب إنشاؤها**

#### [x] **المهمة 1.4:** إنشاء API Endpoints ✅ **مكتملة**
- **المكونات:**
  - `src/app/api/page-backgrounds/route.ts` ✅
  - `src/app/api/page-backgrounds/[pageName]/route.ts` ✅
- **الوصف:** إنشاء جميع endpoints المطلوبة
- **Endpoints المنفذة:**
  - GET /api/page-backgrounds (جلب جميع الخلفيات مع فلترة) ✅
  - POST /api/page-backgrounds (إضافة خلفية جديدة) ✅
  - PUT /api/page-backgrounds (تحديث خلفية موجودة) ✅
  - DELETE /api/page-backgrounds (حذف خلفية) ✅
  - GET /api/page-backgrounds/[pageName] (جلب خلفية صفحة محددة) ✅

### المرحلة الثانية: إنشاء مكونات الواجهة

#### [x] **المهمة 2.1:** إنشاء Hook usePageBackground ✅ **مكتملة**
- **المكونات:** `src/hooks/usePageBackground.ts` ✅
- **الوصف:** Hook لإدارة حالة خلفيات الصفحات
- **الوظائف المنفذة:**
  - جلب خلفية صفحة محددة ✅
  - إدارة حالة التحميل والأخطاء ✅
  - إرجاع styles للخلفية والطبقة العلوية ✅
  - دعم الخلفيات الاحتياطية ✅
  - تخزين مؤقت محسن ✅
  - تخزين محلي للأداء ✅

#### [x] **المهمة 2.2:** إنشاء مكون PageBackground ✅ **مكتملة**
- **المكونات:** `src/components/PageBackground.tsx` ✅
- **الوصف:** مكون لعرض خلفية الصفحة
- **الخصائص المنفذة:**
  - pageName (اسم الصفحة) ✅
  - className (كلاسات إضافية) ✅
  - fallbackBackground (خلفية احتياطية) ✅
  - children (محتوى الصفحة) ✅
  - minHeight (الحد الأدنى للارتفاع) ✅
  - دعم RTL ✅

#### [x] **المهمة 2.3:** إنشاء مكون PageBackgroundsManager ✅ **مكتملة**
- **المكونات:** `src/components/admin/PageBackgroundsManager.tsx` ✅
- **الوصف:** واجهة إدارة خلفيات الصفحات في لوحة التحكم
- **الوظائف المنفذة:**
  - عرض قائمة الخلفيات الموجودة ✅
  - إضافة خلفية جديدة ✅
  - تعديل خلفية موجودة ✅
  - حذف خلفية ✅
  - رفع الصور ✅
  - معاينة مباشرة للتغييرات ✅
  - تحكم في جميع خصائص الخلفية ✅
  - واجهة مستخدم متقدمة مع نماذج ✅

### المرحلة الثالثة: التكامل مع النظام

#### [ ] **المهمة 3.1:** إضافة تبويب في صفحة الإعدادات
- **المكونات:** `src/app/admin/admin-setup/page.tsx`
- **الوصف:** إضافة تبويب "خلفيات الصفحات العامة"
- **التفاصيل:**
  - إضافة TabsTrigger جديد
  - إضافة TabsContent مع مكون PageBackgroundsManager
  - تحديث عدد الأعمدة في TabsList

#### [ ] **المهمة 3.2:** تطبيق النظام على الصفحات العامة
- **المكونات:** صفحات الموقع العامة
- **الصفحات المشمولة:**
  - `src/app/page.tsx` (الصفحة الرئيسية)
  - `src/app/about/page.tsx` (من نحن)
  - `src/app/contact/page.tsx` (اتصل بنا)
  - `src/app/programs/page.tsx` (البرامج)
  - `src/app/khatm-sessions/page.tsx` (مجالس الختم)
  - `src/app/donations/page.tsx` (التبرعات)
  - `src/app/login/page.tsx` (تسجيل الدخول)
  - `src/app/register/page.tsx` (التسجيل)

#### [ ] **المهمة 3.3:** تحديث نظام رفع الملفات
- **المكونات:** `src/app/api/upload/route.ts`
- **الوصف:** إضافة دعم نوع "backgrounds" لرفع صور الخلفيات
- **التفاصيل:**
  - إضافة مجلد `public/uploads/backgrounds/`
  - تحديث منطق معالجة الملفات
  - إضافة التحقق من أنواع الملفات المسموحة

### المرحلة الرابعة: التحسينات والاختبار

#### [ ] **المهمة 4.1:** إضافة معالجة الأخطاء
- **المكونات:** جميع المكونات والـ APIs
- **الوصف:** إضافة معالجة شاملة للأخطاء
- **التفاصيل:**
  - رسائل خطأ واضحة
  - fallback للصور المفقودة
  - معالجة أخطاء الشبكة

#### [ ] **المهمة 4.2:** تحسين الأداء
- **المكونات:** جميع المكونات
- **الوصف:** تحسين أداء تحميل وعرض الخلفيات
- **التفاصيل:**
  - lazy loading للصور
  - تخزين مؤقت للخلفيات
  - ضغط الصور تلقائياً

#### [ ] **المهمة 4.3:** إضافة التوثيق
- **المكونات:** `docs/page-backgrounds.md`
- **الوصف:** توثيق شامل لنظام خلفيات الصفحات
- **المحتوى:**
  - دليل الاستخدام للمدير
  - دليل التطوير للمطور
  - أمثلة عملية
  - استكشاف الأخطاء

## الاعتماديات والمتطلبات

### المتطلبات التقنية:
- Next.js 13+ مع App Router
- Prisma ORM
- TypeScript
- React Hooks
- Tailwind CSS
- React Icons

### المتطلبات الوظيفية:
- نظام رفع الملفات موجود
- نظام إدارة المستخدمين والصلاحيات
- قاعدة بيانات MySQL/PostgreSQL

## معايير النجاح

### الوظائف الأساسية:
- [ ] إمكانية رفع وإدارة خلفيات الصفحات
- [ ] معاينة مباشرة للتغييرات
- [ ] تحكم كامل في خصائص الخلفية
- [ ] دعم جميع الصفحات العامة

### الأداء:
- [ ] تحميل سريع للخلفيات (< 2 ثانية)
- [ ] استجابة فورية للواجهة
- [ ] دعم الأجهزة المختلفة

### الأمان:
- [ ] التحقق من أنواع الملفات
- [ ] حماية الصلاحيات
- [ ] تشفير البيانات الحساسة

## الجدول الزمني المقترح

- **الأسبوع 1:** المرحلة الأولى (قاعدة البيانات والـ API)
- **الأسبوع 2:** المرحلة الثانية (مكونات الواجهة)
- **الأسبوع 3:** المرحلة الثالثة (التكامل مع النظام)
- **الأسبوع 4:** المرحلة الرابعة (التحسينات والاختبار)

## ملاحظات المستخدم الخاصة

- يفضل المستخدم الحد الأدنى من الكلام والاستمرار المباشر في المهام
- التركيز على تحسين الأنظمة القائمة بدلاً من بناء أنظمة جديدة
- ضمان تجربة مستخدم سلسة للمدير
- الالتزام بالتعليمات بدقة دون إضافة ميزات إضافية غير مطلوبة

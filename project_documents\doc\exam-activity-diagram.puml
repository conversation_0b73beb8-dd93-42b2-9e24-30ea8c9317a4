@startuml Exam Process Activity Diagram

skinparam backgroundColor white
skinparam activityBorderColor black
skinparam activityBackgroundColor LightBlue
skinparam activityDiamondBackgroundColor LightYellow
skinparam activityDiamondBorderColor black
skinparam noteBorderColor black
skinparam noteBackgroundColor LightGreen

start

|#AntiqueWhite|Teacher|
:Create new exam;
:Define exam type (Memorization/Tajweed/Recitation);
:Set exam parameters;
note right
  - Surah/Ayahs to be tested
  - Maximum points
  - Passing threshold
  - Evaluation criteria
end note

|#LightCyan|System|
:Save exam details;
:Generate exam schedule;

|Teacher|
:Assign exam to students;

|System|
:Send notifications to students and parents;

fork
  |#LightPink|Student|
  :Receive exam notification;
  :Prepare for exam;
  :Review assigned Surah/Ayahs;

  |Teacher|
  :Conduct exam session;
  note right
    Can be in-person or
    through remote session
  end note

  |Student|
  :Take exam;
  :Recite assigned portions;

  |Teacher|
  :Evaluate performance;
  :Record observations;
  :Enter results;

  |System|
  :Calculate final score;
  :Update student record;

  if (Student passed?) then (yes)
    :Mark exam as completed;
    :Update student progress;
    :Award achievement badge;
  else (no)
    :Mark for remedial action;
    :Schedule review session;
    :Notify student and parent;
  endif
fork again
  |#LightGrey|Administrator|
  :Monitor exam process;
  :Review exam statistics;

  |System|
  :Generate performance analytics;
  :Identify learning patterns;
  :Archive exam data;
  :Create comparative reports;
end fork

|System|
:Update student records;
:Send result notifications;

|#Gold|Parent|
:View child's results;
:Acknowledge receipt;

|Teacher|
:Review overall class performance;
:Plan future lessons based on results;

stop

@enduml

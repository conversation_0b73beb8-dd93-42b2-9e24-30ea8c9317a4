"use client"
import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  FaSchool,
  FaUserGraduate,
  FaChartLine,
  FaHeadset,
  FaIdCard,
  FaQrcode,
  FaCheck,
  FaTimes,
  FaChevronDown,
  FaChevronUp,
  FaCloud,
  FaUsers,
  FaMobile,
  FaDesktop,
  FaCalendarCheck,
  FaCalculator,
  FaPrint,
  FaGraduationCap,
  FaStar,
  FaRocket,
  FaShieldAlt
} from 'react-icons/fa'
import { MdSms, MdSupport, MdSecurity } from 'react-icons/md'
import SiteLogo from '@/components/SiteLogo'
import PageBackground from '@/components/PageBackground'
import styles from './styles.module.css'

interface FAQItem {
  question: string;
  answer: string;
  isOpen: boolean;
}

const SchoolManagementSystemPage = () => {
  const [faqs, setFaqs] = useState<FAQItem[]>([
    {
      question: "لماذا الاشتراكات سنوية؟",
      answer: "الاشتراكات سنوية لأن النظام يعمل عبر الكلاود، والتي بدورها تفرض اشتراكات سنوية أو حسب الفترة الزمنية التي يتم فيها الاستفادة من الخدمة.",
      isOpen: false
    },
    {
      question: "ما المقصود بعدد المستخدمين؟",
      answer: "عدد المستخدمين المقصود بها عدد الحسابات (اسم المستخدم وكلمة المرور) التي تتحصل عليها المدرسة عند الاشتراك بحيث يكون لكل موظف حسابه الخاص به.",
      isOpen: false
    },
    {
      question: "ما هي المواصفات المطلوبة في الجهاز؟",
      answer: "لا توجد أي متطلبات خاصة لاستخدام النظام، لأن العمل كله يكون باستخدام متصفح الإنترنت، فكل جهاز يمكن استخدامه لتصفح الإنترنت يكفي لاستخدام النظام.",
      isOpen: false
    },
    {
      question: "هل يمكن تغيير باقة الاشتراك؟",
      answer: "نعم، يمكن تغيير باقة الاشتراك في أي وقت خلال فترة الاشتراك، مع دفع فارق مبلغ الاشتراك للمدة المتبقية من السنة.",
      isOpen: false
    },
    {
      question: "هل يوجد تدريب على استخدام النظام؟",
      answer: "نعم، يوجد تدريب لتعليم طريقة استخدام النظام، التدريب متوفر لجميع المدارس طوال فترة الاشتراك، يتم برمجته كل أسبوعين على أقل تقدير، ويكون إما حضورياً أو عن بُعد.",
      isOpen: false
    },
    {
      question: "كم تبلغ تكلفة التدريب؟",
      answer: "التدريبات مجانية لجميع المدارس طوال فترة الاشتراك.",
      isOpen: false
    }
  ])

  const toggleFAQ = (index: number) => {
    setFaqs(faqs.map((faq, i) => 
      i === index ? { ...faq, isOpen: !faq.isOpen } : faq
    ))
  }

  return (
    <PageBackground
      pageName="school-management-system"
      className="min-h-screen"
      fallbackBackground="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    >
      <div dir="rtl" className="min-h-screen">
        {/* Navigation */}
        <nav className="bg-white/95 backdrop-blur-sm shadow-lg fixed w-full top-0 z-50">
          <div className="container mx-auto px-4 py-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4 space-x-reverse">
                <SiteLogo size="lg" showText={true} />
                <span className="text-xl font-bold text-gray-800">نظام إدارة المدارس</span>
              </div>
              <div className="hidden md:flex items-center space-x-6 space-x-reverse">
                <a href="#home" className="text-gray-700 hover:text-blue-600 transition-colors">الرئيسية</a>
                <a href="#features" className="text-gray-700 hover:text-blue-600 transition-colors">الخصائص</a>
                <a href="#plans" className="text-gray-700 hover:text-blue-600 transition-colors">باقات الاشتراك</a>
                <a href="#faq" className="text-gray-700 hover:text-blue-600 transition-colors">الأسئلة الشائعة</a>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Link href="/login" className="text-blue-600 hover:text-blue-800 font-medium">
                  تسجيل دخول
                </Link>
                <Link href="/register" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  نسخة تجريبية
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section id="home" className={`pt-20 pb-16 text-white relative overflow-hidden ${styles.heroSection} ${styles.movingBackground}`}>
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className={`text-center lg:text-right ${styles.slideInLeft}`}>
                <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                  نظام إدارة المدارس
                  <span className={`block text-yellow-300 ${styles.gradientText}`}>الأكثر تطوراً</span>
                </h1>
                <p className="text-xl md:text-2xl mb-8 text-blue-100">
                  نظام إداري سحابي متكامل لإدارة جميع أمور المدرسة بكفاءة عالية
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                  <Link href="/register" className={`bg-yellow-400 text-blue-900 px-8 py-4 rounded-full text-lg font-bold shadow-lg ${styles.animatedButton} ${styles.shadowPulse}`}>
                    ابدأ نسخة تجريبية مجانية
                  </Link>
                  <Link href="#features" className={`border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-600 transition-all ${styles.animatedButton}`}>
                    اكتشف المميزات
                  </Link>
                </div>
                <div className="mt-8 flex items-center justify-center lg:justify-start space-x-4 space-x-reverse">
                  <div className="flex items-center">
                    <FaStar className={`text-yellow-400 ml-1 ${styles.iconFloat}`} />
                    <span className="text-sm">أكثر من 500 مدرسة تثق بنا</span>
                  </div>
                </div>
              </div>
              <div className={`relative ${styles.slideInRight}`}>
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-300">
                  <div className="grid grid-cols-2 gap-4">
                    <div className={`bg-white/20 rounded-lg p-4 text-center hover:bg-white/30 transition-all duration-300 ${styles.featureCard}`}>
                      <FaSchool className={`text-3xl mx-auto mb-2 ${styles.iconFloat}`} />
                      <p className="text-sm">إدارة شاملة</p>
                    </div>
                    <div className={`bg-white/20 rounded-lg p-4 text-center hover:bg-white/30 transition-all duration-300 ${styles.featureCard}`} style={{'--delay': '0.1s'} as React.CSSProperties}>
                      <FaCloud className={`text-3xl mx-auto mb-2 ${styles.iconFloat}`} />
                      <p className="text-sm">نظام سحابي</p>
                    </div>
                    <div className={`bg-white/20 rounded-lg p-4 text-center hover:bg-white/30 transition-all duration-300 ${styles.featureCard}`} style={{'--delay': '0.2s'} as React.CSSProperties}>
                      <FaMobile className={`text-3xl mx-auto mb-2 ${styles.iconFloat}`} />
                      <p className="text-sm">متوافق مع الجوال</p>
                    </div>
                    <div className={`bg-white/20 rounded-lg p-4 text-center hover:bg-white/30 transition-all duration-300 ${styles.featureCard}`} style={{'--delay': '0.3s'} as React.CSSProperties}>
                      <MdSupport className={`text-3xl mx-auto mb-2 ${styles.iconFloat}`} />
                      <p className="text-sm">دعم 24/7</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-800 mb-4">مميزات النظام</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                نظام متكامل يوفر جميع الأدوات اللازمة لإدارة المدرسة بكفاءة عالية
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              <div className={`bg-white rounded-xl shadow-lg p-6 text-center ${styles.featureCard} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.1s'} as React.CSSProperties}>
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaCalendarCheck className={`text-2xl text-blue-600 ${styles.iconFloat}`} />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">الحضور والغياب</h3>
                <p className="text-gray-600">تسجيل الحضور والغياب عبر مسح الباركود مع إمكانية التسجيل من أجهزة متعددة</p>
              </div>

              <div className={`bg-white rounded-xl shadow-lg p-6 text-center ${styles.featureCard} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.2s'} as React.CSSProperties}>
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaCalculator className={`text-2xl text-green-600 ${styles.iconFloat}`} />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">نظام محاسبي متكامل</h3>
                <p className="text-gray-600">تنظيم جميع العمليات المحاسبية بدقة عالية مع مرونة في تحديد الأسعار</p>
              </div>

              <div className={`bg-white rounded-xl shadow-lg p-6 text-center ${styles.featureCard} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.3s'} as React.CSSProperties}>
                <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaHeadset className={`text-2xl text-purple-600 ${styles.iconFloat}`} />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">دعم تقني 24/7</h3>
                <p className="text-gray-600">دعم فني متواصل مع تدريبات مجانية وصيانة طوال فترة الاشتراك</p>
              </div>

              <div className={`bg-white rounded-xl shadow-lg p-6 text-center ${styles.featureCard} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.4s'} as React.CSSProperties}>
                <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaPrint className={`text-2xl text-orange-600 ${styles.iconFloat}`} />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800">طباعة البطائق</h3>
                <p className="text-gray-600">طباعة بطائق الطلاب مع باركود خاص لكل بطاقة وتصميم مخصص للمدرسة</p>
              </div>
            </div>

            {/* Detailed Features */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Cloud System */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6">
                  <div className="flex items-center mb-4">
                    <FaCloud className="text-3xl ml-3" />
                    <h3 className="text-2xl font-bold">نظام سحابي متطور</h3>
                  </div>
                </div>
                <div className="p-6">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>نظام إداري سحابي، جميع عملياته تتم عبر الإنترنت</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>واجهة واحدة لتسيير جميع أمور الطلبة</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>متوافق مع جميع أنواع الأجهزة وأنظمة التشغيل</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>تعدد المستخدمين بصلاحيات مختلفة</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* SMS Feature */}
              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6">
                  <div className="flex items-center mb-4">
                    <MdSms className="text-3xl ml-3" />
                    <h3 className="text-2xl font-bold">5000 رسالة مجانية يومياً</h3>
                  </div>
                </div>
                <div className="p-6">
                  <ul className="space-y-3">
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>إرسال رسائل SMS للأولياء والطلاب</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>تنبيهات تلقائية للحضور والغياب</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>إشعارات الدرجات والنتائج</span>
                    </li>
                    <li className="flex items-center">
                      <FaCheck className="text-green-500 ml-2" />
                      <span>رسائل تذكير بالمواعيد والأحداث</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="plans" className="py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-800 mb-4">باقات الاشتراك</h2>
              <p className="text-xl text-gray-600">أسعار في متناول الجميع</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {/* Basic Plan */}
              <div className={`bg-white border-2 border-gray-200 rounded-xl p-8 text-center hover:border-blue-500 transition-colors ${styles.pricingCard} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.1s'} as React.CSSProperties}>
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Basic</h3>
                <p className="text-gray-600 mb-6">للمدارس الصغيرة</p>
                <div className="mb-8">
                  <span className="text-4xl font-bold text-blue-600">20,000</span>
                  <span className="text-gray-600"> دج / سنة</span>
                </div>
                <ul className="space-y-3 mb-8 text-right">
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>تسجيلات الطلبة</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>طباعة البطائق</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>الحضور والغياب</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>حساب خاص لكل أستاذ</span>
                  </li>
                  <li className="flex items-center">
                    <FaUsers className="text-blue-500 ml-2" />
                    <span>عدد المستخدمين: 2</span>
                  </li>
                </ul>
                <Link href="/register" className="block bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                  اختر هذه الباقة
                </Link>
              </div>

              {/* Pro Plan */}
              <div className={`bg-white border-2 border-blue-500 rounded-xl p-8 text-center relative transform scale-105 ${styles.pricingCard} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.2s'} as React.CSSProperties}>
                <div className={`absolute -top-4 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-4 py-1 rounded-full text-sm ${styles.popularBadge}`}>
                  الأكثر شعبية
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Pro</h3>
                <p className="text-gray-600 mb-6">للمدارس التي لديها 3 موظفين فأكثر</p>
                <div className="mb-8">
                  <span className="text-4xl font-bold text-blue-600">31,000</span>
                  <span className="text-gray-600"> دج / سنة</span>
                </div>
                <ul className="space-y-3 mb-8 text-right">
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>تسجيلات الطلبة</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>طباعة البطائق</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>الحضور والغياب</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>حساب خاص لكل أستاذ</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>تطبيق الرسائل (SMS)</span>
                  </li>
                  <li className="flex items-center">
                    <FaUsers className="text-blue-500 ml-2" />
                    <span>عدد المستخدمين: 5</span>
                  </li>
                </ul>
                <Link href="/register" className="block bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                  اختر هذه الباقة
                </Link>
              </div>

              {/* Ultimate Plan */}
              <div className={`bg-white border-2 border-gray-200 rounded-xl p-8 text-center hover:border-purple-500 transition-colors ${styles.pricingCard} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.3s'} as React.CSSProperties}>
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Ultimate</h3>
                <p className="text-gray-600 mb-6">للمدارس التي تبحث عن التميز</p>
                <div className="mb-8">
                  <span className="text-4xl font-bold text-purple-600">45,000</span>
                  <span className="text-gray-600"> دج / سنة</span>
                </div>
                <ul className="space-y-3 mb-8 text-right">
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>جميع مميزات Pro</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>الحضور التعويضي</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>فروع أخرى للمدرسة</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>متابعة الأولياء</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>نظام إدارة المبيعات</span>
                  </li>
                  <li className="flex items-center">
                    <FaCheck className="text-green-500 ml-2" />
                    <span>إدارة معهد التكوين</span>
                  </li>
                  <li className="flex items-center">
                    <FaUsers className="text-purple-500 ml-2" />
                    <span>عدد المستخدمين: 10</span>
                  </li>
                </ul>
                <Link href="/register" className="block bg-purple-600 text-white py-3 px-6 rounded-lg hover:bg-purple-700 transition-colors">
                  اختر هذه الباقة
                </Link>
              </div>
            </div>

            <div className="text-center mt-12">
              <p className="text-gray-600 mb-4">هل تحتاج إلى معلومات أكثر؟</p>
              <Link href="/contact" className="text-blue-600 hover:text-blue-800 font-semibold">
                تواصل معنا للحصول على استشارة مجانية
              </Link>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section id="faq" className="py-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-800 mb-4">الأسئلة الأكثر شيوعاً</h2>
              <p className="text-xl text-gray-600">إجابات على أهم الأسئلة حول النظام</p>
            </div>

            <div className="max-w-4xl mx-auto">
              {faqs.map((faq, index) => (
                <div key={index} className={`bg-white rounded-lg shadow-md mb-4 overflow-hidden ${styles.faqItem} ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': `${index * 0.1}s`} as React.CSSProperties}>
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-6 py-4 text-right flex justify-between items-center hover:bg-gray-50 transition-colors"
                  >
                    <span className="font-semibold text-gray-800">{faq.question}</span>
                    {faq.isOpen ? (
                      <FaChevronUp className="text-blue-600" />
                    ) : (
                      <FaChevronDown className="text-blue-600" />
                    )}
                  </button>
                  {faq.isOpen && (
                    <div className="px-6 pb-4">
                      <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className={`py-20 text-white ${styles.ctaSection} ${styles.movingBackground}`}>
          <div className="container mx-auto px-4 text-center relative z-10">
            <h2 className={`text-4xl font-bold mb-6 ${styles.fadeInUp}`}>ابدأ رحلتك نحو إدارة مدرسية متطورة</h2>
            <p className={`text-xl mb-8 max-w-3xl mx-auto ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.2s'} as React.CSSProperties}>
              انضم إلى أكثر من 500 مدرسة تستخدم نظامنا لإدارة أمورها التعليمية والإدارية بكفاءة عالية
            </p>
            <div className={`flex flex-col sm:flex-row gap-4 justify-center ${styles.fadeInUp} ${styles.staggeredAnimation}`} style={{'--delay': '0.4s'} as React.CSSProperties}>
              <Link href="/register" className={`bg-yellow-400 text-blue-900 px-8 py-4 rounded-full text-lg font-bold ${styles.buttonGlow} ${styles.shadowPulse}`}>
                ابدأ نسخة تجريبية مجانية
              </Link>
              <Link href="/contact" className={`border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-blue-600 transition-all ${styles.animatedButton}`}>
                تواصل معنا
              </Link>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <div className="flex items-center mb-4">
                  <SiteLogo size="lg" showText={true} iconColor="white" />
                </div>
                <p className="text-gray-400 mb-4">
                  نظام إدارة المدارس الأكثر تطوراً في المنطقة، مصمم خصيصاً لتلبية احتياجات المدارس الخاصة
                </p>
                <div className="text-gray-400">
                  <p>الأحد - الخميس: 9:00 - 19:00</p>
                  <p>المملكة العربية السعودية</p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">روابط سريعة</h3>
                <ul className="space-y-2">
                  <li><a href="#home" className="text-gray-400 hover:text-white transition-colors">الرئيسية</a></li>
                  <li><a href="#features" className="text-gray-400 hover:text-white transition-colors">الخصائص</a></li>
                  <li><a href="#plans" className="text-gray-400 hover:text-white transition-colors">باقات الاشتراك</a></li>
                  <li><a href="#faq" className="text-gray-400 hover:text-white transition-colors">الأسئلة الشائعة</a></li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">الخدمات</h3>
                <ul className="space-y-2">
                  <li><span className="text-gray-400">إدارة الطلاب</span></li>
                  <li><span className="text-gray-400">النظام المحاسبي</span></li>
                  <li><span className="text-gray-400">الحضور والغياب</span></li>
                  <li><span className="text-gray-400">طباعة البطائق</span></li>
                  <li><span className="text-gray-400">الرسائل النصية</span></li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">تواصل معنا</h3>
                <div className="space-y-2 text-gray-400">
                  <p>البريد الإلكتروني: <EMAIL></p>
                  <p>الهاتف: +966 50 123 4567</p>
                  <p>واتساب: +966 50 123 4567</p>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p className="text-gray-400">
                جميع الحقوق محفوظة © 2025 نظام إدارة المدارس.
                <Link href="/terms" className="hover:text-white transition-colors mr-4">شروط الاستخدام</Link>
                <Link href="/privacy" className="hover:text-white transition-colors mr-4">سياسة الخصوصية</Link>
              </p>
            </div>
          </div>
        </footer>
      </div>
    </PageBackground>
  )
}

export default SchoolManagementSystemPage

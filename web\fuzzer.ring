/*
==============================================================================
    وحدة Fuzzer - Praetorian Web Fuzzer
    
    الوصف: أداة للـ Fuzzing واكتشاف المجلدات والملفات المخفية
    المؤلف: Praetorian Team
==============================================================================
*/

/*
==============================================================================
    كلاس Fuzzer
==============================================================================
*/

class PraetorianWebFuzzer

    # خصائص Fuzzer
    oHTTPClient = NULL
    oLogger = NULL
    bVerbose = false
    
    # إعدادات Fuzzing
    nThreads = 10
    nDelay = 100  # تأخير بالميلي ثانية
    nTimeout = 5  # مهلة الطلب بالثواني
    
    # أكواد الاستجابة المهمة
    aSuccessCodes = [200, 301, 302, 403, 401]
    aInterestingCodes = [403, 401, 500, 503]
    
    # قوائم كلمات افتراضية
    aCommonDirs = [
        "admin", "administrator", "login", "panel", "dashboard", "control",
        "manager", "management", "config", "configuration", "settings",
        "backup", "backups", "data", "database", "db", "sql", "logs",
        "log", "temp", "tmp", "cache", "uploads", "upload", "files",
        "file", "documents", "docs", "images", "img", "css", "js",
        "scripts", "includes", "inc", "lib", "libraries", "vendor",
        "api", "rest", "service", "services", "test", "tests", "dev",
        "development", "staging", "prod", "production", "www", "web",
        "site", "sites", "public", "private", "secure", "security"
    ]
    
    aCommonFiles = [
        "index.html", "index.php", "index.asp", "index.aspx", "index.jsp",
        "default.html", "default.php", "default.asp", "home.html", "home.php",
        "login.html", "login.php", "admin.html", "admin.php", "config.php",
        "configuration.php", "settings.php", "database.php", "db.php",
        "connect.php", "connection.php", "backup.sql", "dump.sql",
        "robots.txt", "sitemap.xml", ".htaccess", "web.config", "phpinfo.php",
        "info.php", "test.php", "readme.txt", "readme.md", "changelog.txt",
        "version.txt", "license.txt", "install.php", "setup.php"
    ]
    
    # امتدادات الملفات
    aFileExtensions = [".php", ".html", ".htm", ".asp", ".aspx", ".jsp", ".txt", ".xml", ".json"]
    
    /*
    دالة البناء
    */
    func init
        oLogger = PraetorianLoggerInstance
        oHTTPClient = new PraetorianHTTPClient
        oHTTPClient.setTimeout(nTimeout)
        oLogger.debug("تم تهيئة Fuzzer")
    
    /*
    تعيين عدد الخيوط
    المدخلات: nThreadCount - عدد الخيوط
    */
    func setThreads nThreadCount
        nThreads = nThreadCount
        oLogger.debug("تم تعيين عدد الخيوط إلى " + nThreads)
    
    /*
    تعيين التأخير بين الطلبات
    المدخلات: nDelayMs - التأخير بالميلي ثانية
    */
    func setDelay nDelayMs
        nDelay = nDelayMs
        oLogger.debug("تم تعيين التأخير إلى " + nDelay + " ميلي ثانية")
    
    /*
    تعيين مهلة الطلب
    المدخلات: nTimeoutSec - المهلة بالثواني
    */
    func setTimeout nTimeoutSec
        nTimeout = nTimeoutSec
        oHTTPClient.setTimeout(nTimeout)
        oLogger.debug("تم تعيين مهلة الطلب إلى " + nTimeout + " ثانية")
    
    /*
    تفعيل/إلغاء الوضع المفصل
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func setVerbose bEnable
        bVerbose = bEnable
        oHTTPClient.setVerbose(bEnable)
    
    /*
    تحميل قائمة كلمات من ملف
    المدخلات: cFilePath - مسار الملف
    المخرجات: قائمة الكلمات
    */
    func loadWordlist cFilePath
        if not fexists(cFilePath)
            oLogger.error("ملف قائمة الكلمات غير موجود: " + cFilePath)
            return []
        ok
        
        try
            cContent = read(cFilePath)
            aLines = split(cContent, nl)
            aWordlist = []
            
            for cLine in aLines
                cWord = trim(cLine)
                if len(cWord) > 0 and substr(cWord, "#") != 1  # تجاهل التعليقات
                    add(aWordlist, cWord)
                ok
            next
            
            oLogger.info("تم تحميل " + len(aWordlist) + " كلمة من " + cFilePath)
            return aWordlist
            
        catch
            oLogger.error("خطأ في قراءة ملف قائمة الكلمات: " + cCatchError)
            return []
        done
    
    /*
    فحص رابط واحد
    المدخلات: cURL - الرابط
    المخرجات: معلومات الاستجابة
    */
    func testSingleURL cURL
        try
            oResponse = oHTTPClient.get(cURL, NULL)
            
            aResult = [
                :url = cURL,
                :status_code = oResponse[:status_code],
                :content_length = oResponse[:content_length],
                :content_type = oResponse[:content_type],
                :found = find(aSuccessCodes, oResponse[:status_code]) > 0,
                :interesting = find(aInterestingCodes, oResponse[:status_code]) > 0
            ]
            
            if bVerbose and aResult[:found]
                oLogger.info("تم العثور على: " + cURL + " [" + oResponse[:status_code] + "]")
            ok
            
            return aResult
            
        catch
            return [
                :url = cURL,
                :status_code = 0,
                :content_length = 0,
                :content_type = "",
                :found = false,
                :interesting = false,
                :error = cCatchError
            ]
        ok
    
    /*
    Fuzzing المجلدات
    المدخلات: cBaseURL - الرابط الأساسي، aWordlist - قائمة الكلمات
    المخرجات: قائمة المجلدات الموجودة
    */
    func fuzzDirectories cBaseURL, aWordlist
        oLogger.startOperation("بدء fuzzing المجلدات للرابط: " + cBaseURL)
        
        # التأكد من وجود شرطة مائلة في النهاية
        if right(cBaseURL, 1) != "/"
            cBaseURL += "/"
        ok
        
        aResults = []
        nTested = 0
        nTotal = len(aWordlist)
        
        ? "بدء فحص " + nTotal + " مجلد..."
        
        for cDir in aWordlist
            nTested++
            
            # عرض التقدم
            if nTested % 50 = 0 or nTested = nTotal
                ? "تم فحص " + nTested + "/" + nTotal + " مجلد..."
            ok
            
            # بناء الرابط
            cTestURL = cBaseURL + cDir + "/"
            
            # فحص المجلد
            aResult = testSingleURL(cTestURL)
            if aResult[:found] or aResult[:interesting]
                add(aResults, aResult)
                
                if not bVerbose
                    ? "تم العثور على مجلد: " + cTestURL + " [" + aResult[:status_code] + "]"
                ok
            ok
            
            # تأخير بين الطلبات
            if nDelay > 0
                sleep(nDelay)
            ok
        next
        
        oLogger.endOperation("انتهاء fuzzing المجلدات")
        oLogger.info("تم العثور على " + len(aResults) + " مجلد")
        
        return aResults
    
    /*
    Fuzzing الملفات
    المدخلات: cBaseURL - الرابط الأساسي، aWordlist - قائمة الكلمات، aExtensions - الامتدادات
    المخرجات: قائمة الملفات الموجودة
    */
    func fuzzFiles cBaseURL, aWordlist, aExtensions
        oLogger.startOperation("بدء fuzzing الملفات للرابط: " + cBaseURL)
        
        # التأكد من وجود شرطة مائلة في النهاية
        if right(cBaseURL, 1) != "/"
            cBaseURL += "/"
        ok
        
        aResults = []
        nTested = 0
        
        # إنشاء قائمة الملفات مع الامتدادات
        aFilesToTest = []
        for cFile in aWordlist
            for cExt in aExtensions
                add(aFilesToTest, cFile + cExt)
            next
            # إضافة الملف بدون امتداد أيضاً
            add(aFilesToTest, cFile)
        next
        
        nTotal = len(aFilesToTest)
        ? "بدء فحص " + nTotal + " ملف..."
        
        for cFile in aFilesToTest
            nTested++
            
            # عرض التقدم
            if nTested % 50 = 0 or nTested = nTotal
                ? "تم فحص " + nTested + "/" + nTotal + " ملف..."
            ok
            
            # بناء الرابط
            cTestURL = cBaseURL + cFile
            
            # فحص الملف
            aResult = testSingleURL(cTestURL)
            if aResult[:found] or aResult[:interesting]
                add(aResults, aResult)
                
                if not bVerbose
                    ? "تم العثور على ملف: " + cTestURL + " [" + aResult[:status_code] + "]"
                ok
            ok
            
            # تأخير بين الطلبات
            if nDelay > 0
                sleep(nDelay)
            ok
        next
        
        oLogger.endOperation("انتهاء fuzzing الملفات")
        oLogger.info("تم العثور على " + len(aResults) + " ملف")
        
        return aResults
    
    /*
    Fuzzing شامل (مجلدات وملفات)
    المدخلات: cBaseURL - الرابط الأساسي، aDirWordlist - قائمة المجلدات، aFileWordlist - قائمة الملفات
    المخرجات: تقرير شامل
    */
    func comprehensiveFuzz cBaseURL, aDirWordlist, aFileWordlist
        oLogger.startOperation("بدء Fuzzing شامل للرابط: " + cBaseURL)
        
        aReport = [
            :base_url = cBaseURL,
            :directories = [],
            :files = [],
            :total_directories = 0,
            :total_files = 0,
            :interesting_findings = []
        ]
        
        # Fuzzing المجلدات
        ? ""
        ? "=== Fuzzing المجلدات ==="
        if len(aDirWordlist) = 0
            aDirWordlist = aCommonDirs
        ok
        
        aDirResults = fuzzDirectories(cBaseURL, aDirWordlist)
        aReport[:directories] = aDirResults
        aReport[:total_directories] = len(aDirResults)
        
        # Fuzzing الملفات
        ? ""
        ? "=== Fuzzing الملفات ==="
        if len(aFileWordlist) = 0
            aFileWordlist = aCommonFiles
        ok
        
        aFileResults = fuzzFiles(cBaseURL, aFileWordlist, aFileExtensions)
        aReport[:files] = aFileResults
        aReport[:total_files] = len(aFileResults)
        
        # تحليل النتائج المثيرة للاهتمام
        for aResult in aDirResults + aFileResults
            if aResult[:interesting]
                add(aReport[:interesting_findings], aResult)
            ok
        next
        
        oLogger.endOperation("انتهاء Fuzzing شامل")
        
        return aReport
    
    /*
    Fuzzing سريع باستخدام القوائم الافتراضية
    المدخلات: cBaseURL - الرابط الأساسي
    المخرجات: تقرير مبسط
    */
    func quickFuzz cBaseURL
        oLogger.info("بدء Fuzzing سريع للرابط: " + cBaseURL)
        
        # استخدام قوائم مختصرة
        aQuickDirs = ["admin", "login", "panel", "config", "backup", "uploads", "api"]
        aQuickFiles = ["index.php", "admin.php", "login.php", "config.php", "robots.txt", ".htaccess"]
        
        return comprehensiveFuzz(cBaseURL, aQuickDirs, aQuickFiles)
    
    /*
    طباعة تقرير Fuzzing
    المدخلات: aReport - تقرير Fuzzing
    */
    func printFuzzReport aReport
        ? ""
        ? "==============================================="
        ? "تقرير Fuzzing للرابط: " + aReport[:base_url]
        ? "==============================================="
        ? "تاريخ الفحص: " + date() + " " + time()
        ? "المجلدات الموجودة: " + aReport[:total_directories]
        ? "الملفات الموجودة: " + aReport[:total_files]
        ? "النتائج المثيرة للاهتمام: " + len(aReport[:interesting_findings])
        ? "==============================================="
        
        if aReport[:total_directories] > 0
            ? ""
            ? "المجلدات الموجودة:"
            ? "-------------------"
            for aDir in aReport[:directories]
                ? aDir[:url] + " [" + aDir[:status_code] + "]"
            next
        ok
        
        if aReport[:total_files] > 0
            ? ""
            ? "الملفات الموجودة:"
            ? "-----------------"
            for aFile in aReport[:files]
                ? aFile[:url] + " [" + aFile[:status_code] + "] (" + aFile[:content_length] + " بايت)"
            next
        ok
        
        if len(aReport[:interesting_findings]) > 0
            ? ""
            ? "النتائج المثيرة للاهتمام:"
            ? "------------------------"
            for aFinding in aReport[:interesting_findings]
                cStatus = ""
                switch aFinding[:status_code]
                    on 403
                        cStatus = "ممنوع الوصول"
                    on 401
                        cStatus = "يتطلب مصادقة"
                    on 500
                        cStatus = "خطأ خادم"
                    other
                        cStatus = "غير معروف"
                off
                ? aFinding[:url] + " [" + aFinding[:status_code] + "] - " + cStatus
            next
        ok
        
        ? "==============================================="

# إنشاء مثيل عام من Fuzzer
PraetorianFuzzerInstance = new PraetorianWebFuzzer

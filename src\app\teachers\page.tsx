"use client";
import React, { useState, useEffect } from 'react';
import { FaUserGraduate, FaCalendarAlt, FaBook, FaSync,
  FaChalkboardTeacher, FaSchool, FaUserPlus, FaEdit, FaInfoCircle,
  FaClipboardCheck, FaCalendarCheck, FaBookReader, FaAward, FaCheckCircle } from 'react-icons/fa';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';

interface TeacherStats {
  name: string;
  specialization: string;
  totalStudents: number;
  totalClasses: number;
  attendanceRate: number;
  upcomingClasses: Array<{
    id: number;
    name: string;
    time: string;
    students: number;
  }>;
  studentActivities: Array<{
    id: number;
    studentName: string;
    activityType: string;
    description: string;
    time: string;
    progress?: number;
  }>;
}

const TeacherDashboard = () => {
  const [teacherData, setTeacherData] = useState<TeacherStats>({
    name: '',
    specialization: '',
    totalStudents: 0,
    totalClasses: 0,
    attendanceRate: 0,
    upcomingClasses: [],
    studentActivities: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTeacherData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch('/api/teachers/stats');

      if (!response.ok) {
        throw new Error('فشل في جلب بيانات المعلم');
      }

      const data = await response.json();
      setTeacherData(data);
      toast.success('تم تحديث البيانات بنجاح');
    } catch (error) {
      console.error('Error fetching teacher data:', error);
      setError('حدث خطأ أثناء جلب البيانات. يرجى المحاولة مرة أخرى.');
      toast.error('فشل في جلب بيانات المعلم');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTeacherData();
  }, []);

  const quickLinks = [
    { title: 'طلابي', icon: <FaUserGraduate />, link: '/teachers/students', color: 'bg-blue-500' },
    { title: 'جدول الحصص', icon: <FaCalendarAlt />, link: '/teachers/schedule', color: 'bg-primary-color' },
    { title: 'المنهج', icon: <FaBook />, link: '/teachers/curriculum', color: 'bg-purple-500' },
    { title: 'تسجيل الحضور', icon: <FaClipboardCheck />, link: '/teachers/attendance', color: 'bg-yellow-500' },
  ];

  return (
    <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6" dir="rtl">
      {/* Welcome Section */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaChalkboardTeacher className="text-[var(--primary-color)]" />
          لوحة تحكم المعلم
        </h1>
        <Button
          onClick={() => {
            fetchTeacherData();
          }}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2"
          disabled={isLoading}
          title="تحديث البيانات"
        >
          <FaSync className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* Welcome Card */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <div className="flex justify-between items-center">
          <div>
            {isLoading ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
              </div>
            ) : error ? (
              <div className="text-center text-red-500 py-4">{error}</div>
            ) : (
              <>
                <h2 className="text-2xl font-bold text-[var(--primary-color)] mb-2">مرحباً بك، الأستاذ {teacherData.name}</h2>
                <p className="text-gray-600">هنا يمكنك إدارة الفصول والطلاب والتقييمات</p>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link href="/teachers/students">
          <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-[var(--primary-color)] text-sm font-medium">إجمالي الطلاب</p>
                <p className="text-2xl font-bold text-gray-800 mt-1">{isLoading ? '...' : teacherData.totalStudents}</p>
              </div>
              <div className="bg-[#e9f7f5] text-[var(--primary-color)] w-12 h-12 rounded-full flex items-center justify-center text-2xl">
                <FaUserGraduate />
              </div>
            </div>
          </div>
        </Link>

        <Link href="/teachers/schedule">
          <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-[var(--primary-color)] text-sm font-medium">الفصول</p>
                <p className="text-2xl font-bold text-gray-800 mt-1">{isLoading ? '...' : teacherData.totalClasses}</p>
              </div>
              <div className="bg-[#e9f7f5] text-[var(--primary-color)] w-12 h-12 rounded-full flex items-center justify-center text-2xl">
                <FaSchool />
              </div>
            </div>
          </div>
        </Link>

        <Link href="/teachers/attendance">
          <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-[var(--primary-color)] text-sm font-medium">نسبة الحضور</p>
                <p className="text-2xl font-bold text-gray-800 mt-1">{isLoading ? '...' : `${teacherData.attendanceRate}%`}</p>
              </div>
              <div className="bg-[#e9f7f5] text-[var(--primary-color)] w-12 h-12 rounded-full flex items-center justify-center text-2xl">
                <FaClipboardCheck />
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Quick Links */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <h3 className="text-xl font-semibold text-[var(--primary-color)] mb-4 flex items-center gap-2">
          <FaUserPlus className="text-[var(--primary-color)]" />
          روابط سريعة
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          {quickLinks.map((link, index) => (
            <Link href={link.link} key={index}>
              <div className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 h-14">
                <div className="text-xl">{link.icon}</div>
                <span className="font-medium">{link.title}</span>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Upcoming Classes */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-[var(--primary-color)] flex items-center gap-2">
            <FaCalendarAlt className="text-[var(--primary-color)]" />
            الحصص القادمة
          </h3>
        </div>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center text-center py-8">
            <div className="text-red-500 mb-4 bg-red-50 p-4 rounded-md border border-red-100">{error}</div>
            <Button
              onClick={() => fetchTeacherData()}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              إعادة المحاولة
            </Button>
          </div>
        ) : teacherData.upcomingClasses && teacherData.upcomingClasses.length > 0 ? (
          <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2 mb-4 custom-scrollbar border border-[#e0f2ef] rounded-md p-2 bg-[#f8fffd]">
            {teacherData.upcomingClasses.map((cls, index) => (
              <div key={`class-${cls.id}-${index}`} className="border-r-4 border-[var(--primary-color)] pr-4 py-2 hover:bg-gray-50 rounded-lg">
                <div className="flex justify-between">
                  <h4 className="font-medium text-gray-800">{cls.name}</h4>
                  <span className="text-sm text-gray-500">{cls.students} طالب</span>
                </div>
                <p className="text-sm text-gray-600">{cls.time}</p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8 border border-dashed border-gray-300 rounded-lg">
            <FaInfoCircle className="mx-auto text-gray-400 text-2xl mb-2" />
            <p className="mb-2">لا توجد حصص قادمة</p>
            <p className="text-xs text-gray-400">ستظهر هنا الحصص القادمة المجدولة</p>
          </div>
        )}
        <div className="text-center">
          <Link href="/teachers/schedule">
            <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300">
              عرض الجدول الكامل
            </Button>
          </Link>
        </div>
      </div>

      {/* Students Activities */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-[var(--primary-color)] flex items-center gap-2">
            <FaCalendarCheck className="text-[var(--primary-color)]" />
            نشاطات الطلاب الأخيرة
          </h3>
        </div>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center text-center py-8">
            <div className="text-red-500 mb-4 bg-red-50 p-4 rounded-md border border-red-100">{error}</div>
            <Button
              onClick={() => fetchTeacherData()}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              إعادة المحاولة
            </Button>
          </div>
        ) : teacherData.studentActivities && teacherData.studentActivities.length > 0 ? (
          <div className="space-y-4 max-h-[300px] overflow-y-auto pr-2 mb-4 custom-scrollbar border border-[#e0f2ef] rounded-md p-2 bg-[#f8fffd]">
            {teacherData.studentActivities.map((activity, index) => (
              <div key={`activity-${activity.id}-${index}`} className="border-r-4 border-[var(--primary-color)] pr-4 flex items-start gap-3 p-2 hover:bg-gray-50 rounded-lg">
                <div className="mt-1">
                  {activity.activityType === 'quran_memorization' && <FaBookReader className="text-[var(--primary-color)]" />}
                  {activity.activityType === 'exam' && <FaAward className="text-[var(--primary-color)]" />}
                  {activity.activityType === 'attendance' && <FaCheckCircle className="text-[var(--primary-color)]" />}
                  {!['quran_memorization', 'exam', 'attendance'].includes(activity.activityType) && <FaUserGraduate className="text-[var(--primary-color)]" />}
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500">{activity.time}</p>
                  <p className="text-gray-800 font-medium">{activity.studentName}</p>
                  <p className="text-xs text-gray-600">{activity.description}</p>
                  {activity.progress !== undefined && (
                    <div className="mt-1 w-full bg-gray-200 rounded-full h-2.5">
                      <div className="bg-[var(--primary-color)] h-2.5 rounded-full" style={{ width: `${activity.progress}%` }}></div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8 border border-dashed border-gray-300 rounded-lg">
            <FaInfoCircle className="mx-auto text-gray-400 text-2xl mb-2" />
            <p className="mb-2">لا توجد نشاطات طلاب حديثة</p>
            <p className="text-xs text-gray-400">ستظهر هنا نشاطات الطلاب مثل تقدم الحفظ، نتائج الاختبارات، وغيرها</p>
          </div>
        )}
        <div className="text-center">
          <Link href="/teachers/students">
            <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300">
              عرض تقارير الطلاب
            </Button>
          </Link>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <h3 className="text-xl font-semibold text-[var(--primary-color)] mb-4 flex items-center gap-2">
          <FaUserPlus className="text-[var(--primary-color)]" />
          إجراءات سريعة
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <Link href="/teachers/students" key="quick-action-1">
            <div className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2">
              <FaUserGraduate className="ml-1" />
              إدارة الطلاب
            </div>
          </Link>
          <Link href="/teachers/attendance" key="quick-action-2">
            <div className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2">
              <FaCalendarCheck className="ml-1" />
              تسجيل الحضور
            </div>
          </Link>
          <Link href="/teachers/quran-progress/add" key="quick-action-3">
            <div className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2">
              <FaBook className="ml-1" />
              تسجيل تقدم الحفظ
            </div>
          </Link>
          <Link href="/teachers/schedule" key="quick-action-4">
            <div className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2">
              <FaCalendarAlt className="ml-1" />
              جدول الحصص
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TeacherDashboard;
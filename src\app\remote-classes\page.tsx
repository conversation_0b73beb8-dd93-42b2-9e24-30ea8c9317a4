'use client';
import React, { useState, useEffect, useCallback } from 'react';
import { FaVideo, FaCalendarAlt, FaClock, FaUsers, FaChalkboardTeacher, FaPlus, FaFilter, FaExclamationCircle, FaLink, FaHistory } from 'react-icons/fa';
import Link from 'next/link';
import { toast } from 'react-toastify';
import axios from 'axios';

interface RemoteClass {
  id: number;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  meetingLink: string;
  platform: string;
  instructor: {
    id: number;
    username: string;
    profile?: {
      name: string;
    };
  };
  classe?: {
    id: number;
    name: string;
  };
  attendees: Array<{
    id: number;
    username: string;
    profile?: {
      name: string;
    };
  }>;
}

const RemoteClassesPage = () => {
  const [remoteClasses, setRemoteClasses] = useState<RemoteClass[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('upcoming');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [userRole, setUserRole] = useState<string | null>(null);

  // جلب الفصول الافتراضية
  const fetchRemoteClasses = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const filterParam = filter === 'upcoming' ? '&upcoming=true' : filter === 'past' ? '&past=true' : '';
      console.log(`Fetching remote classes with filter: ${filter}, page: ${page}`);
      const response = await axios.get(`/api/remote-classes?page=${page}&limit=10${filterParam}`);

      // Type assertion for response.data since we know the expected structure
      const responseData = response.data as {
        remoteClasses: RemoteClass[],
        pagination: { totalPages: number, total: number }
      };

      console.log(`Received ${responseData.remoteClasses.length} remote classes out of ${responseData.pagination.total} total`);
      setRemoteClasses(responseData.remoteClasses);
      setTotalPages(responseData.pagination.totalPages);
    } catch (error) {
      console.error('Error fetching remote classes:', error);
      setError('حدث خطأ أثناء جلب الفصول الافتراضية. يرجى المحاولة مرة أخرى.');
      toast.error('فشل في جلب الفصول الافتراضية');
    } finally {
      setIsLoading(false);
    }
  }, [filter, page]);

  // جلب معلومات المستخدم
  const fetchUserInfo = useCallback(async () => {
    try {
      const response = await axios.get('/api/users/me');
      // Type assertion for response.data since we know it contains user info
      const userData = response.data as { role: string, id: number };
      console.log(`User role: ${userData.role}, User ID: ${userData.id}`);
      setUserRole(userData.role);
    } catch (error) {
      console.error('Error fetching user info:', error);
    }
  }, []);

  // جلب الفصول الافتراضية عند تحميل الصفحة أو تغيير الفلتر أو الصفحة
  useEffect(() => {
    fetchRemoteClasses();
    fetchUserInfo();
  }, [fetchRemoteClasses, fetchUserInfo]);

  // تنسيق التاريخ والوقت
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // حساب المدة
  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const durationMs = end.getTime() - start.getTime();
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    return `${hours > 0 ? `${hours} ساعة` : ''} ${minutes > 0 ? `${minutes} دقيقة` : ''}`;
  };

  // التحقق مما إذا كان الفصل قد بدأ
  const hasStarted = (startTime: string) => {
    const now = new Date();
    const start = new Date(startTime);
    return now >= start;
  };

  // التحقق مما إذا كان الفصل قد انتهى
  const hasEnded = (endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    return now > end;
  };

  return (
    <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaVideo className="text-[var(--primary-color)]" />
            الفصول الافتراضية
          </h1>
          <p className="text-gray-600 max-w-2xl mr-4">
            تعلم عن بعد مع أفضل المعلمين من خلال الفصول الافتراضية المباشرة
          </p>
        </div>

        {/* Actions and Filters */}
        <div className="bg-white rounded-lg shadow-md p-4 mb-6 border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex flex-wrap gap-2 mb-4 md:mb-0">
              <button
                onClick={() => { setFilter('upcoming'); setPage(1); }}
                className={`px-4 py-2 rounded-md text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 ${filter === 'upcoming' ? 'bg-[var(--primary-color)] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                <FaCalendarAlt className="ml-2" />
                الفصول القادمة
              </button>
              <button
                onClick={() => { setFilter('past'); setPage(1); }}
                className={`px-4 py-2 rounded-md text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 ${filter === 'past' ? 'bg-[var(--primary-color)] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                <FaHistory className="ml-2" />
                الفصول السابقة
              </button>
              <button
                onClick={() => { setFilter('all'); setPage(1); }}
                className={`px-4 py-2 rounded-md text-sm flex items-center shadow-md hover:shadow-lg transition-all duration-300 ${filter === 'all' ? 'bg-[var(--primary-color)] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                <FaFilter className="ml-2" />
                جميع الفصول
              </button>
            </div>

            {(userRole === 'ADMIN' || userRole === 'TEACHER') && (
              <Link
                href="/remote-classes/create"
                className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-all duration-300 flex items-center shadow-md hover:shadow-lg"
              >
                <FaPlus className="ml-2" />
                إنشاء فصل جديد
              </Link>
            )}
          </div>
        </div>

        {/* Remote Classes List */}
        <div className="space-y-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64 bg-white rounded-lg shadow-md p-6">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-64 text-center p-4 bg-white rounded-lg shadow-md border-t-4 border-red-500">
              <FaExclamationCircle className="text-red-500 text-4xl mb-4" />
              <p className="text-gray-600 bg-red-50 p-4 rounded-md border border-red-100 mb-4">{error}</p>
              <button
                onClick={fetchRemoteClasses}
                className="mt-4 bg-[var(--primary-color)] text-white px-4 py-2 rounded-md hover:bg-[var(--secondary-color)] transition-all duration-300 shadow-md hover:shadow-lg"
              >
                إعادة المحاولة
              </button>
            </div>
          ) : remoteClasses.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center p-4 bg-white rounded-lg shadow-md border border-dashed border-gray-300">
              <FaVideo className="text-gray-400 text-4xl mb-4" />
              <p className="text-gray-600">لا توجد فصول افتراضية {filter === 'upcoming' ? 'قادمة' : filter === 'past' ? 'سابقة' : ''}</p>
            </div>
          ) : (
            remoteClasses.map((remoteClass) => (
              <div
                key={remoteClass.id}
                className="bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300"
              >
                <div className="p-6">
                  <div className="flex flex-col md:flex-row justify-between">
                    <div>
                      <h2 className="text-xl font-bold text-[var(--primary-color)] mb-2">{remoteClass.title}</h2>
                      <p className="text-gray-600 mb-4">{remoteClass.description}</p>

                      <div className="flex flex-wrap gap-y-3 gap-x-4 mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <FaCalendarAlt className="ml-1 text-[var(--primary-color)]" />
                          <span>{formatDateTime(remoteClass.startTime)}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FaClock className="ml-1 text-[var(--primary-color)]" />
                          <span>المدة: {calculateDuration(remoteClass.startTime, remoteClass.endTime)}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FaChalkboardTeacher className="ml-1 text-[var(--primary-color)]" />
                          <span>المعلم: {remoteClass.instructor.profile?.name || remoteClass.instructor.username}</span>
                        </div>
                        {remoteClass.classe && (
                          <div className="flex items-center text-sm text-gray-600">
                            <FaUsers className="ml-1 text-[var(--primary-color)]" />
                            <span>الفصل: {remoteClass.classe.name}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="mt-4 md:mt-0 flex flex-col justify-center">
                      {hasEnded(remoteClass.endTime) ? (
                        <span className="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-center mb-2 shadow-md">
                          انتهى الفصل
                        </span>
                      ) : hasStarted(remoteClass.startTime) ? (
                        <a
                          href={remoteClass.meetingLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-red-500 text-white px-4 py-2 rounded-md text-center hover:bg-red-600 mb-2 shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                        >
                          <FaVideo className="ml-1" />
                          انضم الآن
                        </a>
                      ) : (
                        <span className="bg-blue-100 text-blue-800 px-4 py-2 rounded-md text-center mb-2 shadow-md">
                          قريباً
                        </span>
                      )}

                      <Link
                        href={`/remote-classes/${remoteClass.id}`}
                        className="bg-[var(--primary-color)] text-white px-4 py-2 rounded-md text-center hover:bg-[var(--secondary-color)] shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                      >
                        <FaLink className="ml-1" />
                        عرض التفاصيل
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Pagination */}
        {!isLoading && !error && remoteClasses.length > 0 && (
          <div className="mt-8 flex justify-center">
            <div className="flex items-center space-x-2 space-x-reverse">
              <button
                onClick={() => setPage(prev => Math.max(1, prev - 1))}
                disabled={page === 1}
                className={`px-4 py-2 rounded-md shadow-md hover:shadow-lg transition-all duration-300 ${page === 1 ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-[var(--primary-color)] text-white hover:bg-[var(--secondary-color)]'}`}
              >
                السابق
              </button>
              <span className="text-sm bg-[#e9f7f5] text-[var(--primary-color)] px-4 py-2 rounded-md shadow-md">
                صفحة {page} من {totalPages}
              </span>
              <button
                onClick={() => setPage(prev => Math.min(totalPages, prev + 1))}
                disabled={page === totalPages}
                className={`px-4 py-2 rounded-md shadow-md hover:shadow-lg transition-all duration-300 ${page === totalPages ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-[var(--primary-color)] text-white hover:bg-[var(--secondary-color)]'}`}
              >
                التالي
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RemoteClassesPage;

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

interface Teacher {
  id: number
  name: string
  specialization: string
}

interface Evaluator {
  id: number
  username: string
  profile?: {
    name: string
  }
}

interface TeacherEvaluation {
  id: number
  teacherId: number
  evaluatorId: number
  evaluationDate: string
  teachingSkills: number
  classManagement: number
  studentProgress: number
  attendance: number
  communication: number
  overallRating: number
  strengths?: string
  improvements?: string
  comments?: string
  teacher?: Teacher
  evaluator?: Evaluator
}

interface EditEvaluationModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  evaluation: TeacherEvaluation | null
}

export function EditEvaluationModal({ isOpen, onClose, onSuccess, evaluation }: EditEvaluationModalProps) {
  const [formData, setFormData] = useState({
    id: '',
    teachingSkills: '',
    classManagement: '',
    studentProgress: '',
    attendance: '',
    communication: '',
    strengths: '',
    improvements: '',
    comments: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (evaluation) {
      setFormData({
        id: evaluation.id.toString(),
        teachingSkills: evaluation.teachingSkills.toString(),
        classManagement: evaluation.classManagement.toString(),
        studentProgress: evaluation.studentProgress.toString(),
        attendance: evaluation.attendance.toString(),
        communication: evaluation.communication.toString(),
        strengths: evaluation.strengths || '',
        improvements: evaluation.improvements || '',
        comments: evaluation.comments || ''
      })
    }
  }, [evaluation])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // التحقق من البيانات
    if (!formData.teachingSkills || !formData.classManagement ||
        !formData.studentProgress || !formData.attendance || !formData.communication) {
      setError('يرجى إدخال جميع درجات التقييم')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/teacher-evaluations', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: parseInt(formData.id),
          teachingSkills: parseFloat(formData.teachingSkills),
          classManagement: parseFloat(formData.classManagement),
          studentProgress: parseFloat(formData.studentProgress),
          attendance: parseFloat(formData.attendance),
          communication: parseFloat(formData.communication),
          strengths: formData.strengths,
          improvements: formData.improvements,
          comments: formData.comments
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'حدث خطأ أثناء تحديث التقييم')
      }

      onSuccess()
      onClose()
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء تحديث التقييم';
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] bg-white max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold text-[var(--primary-color)]">تعديل تقييم المعلم</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded-md">{error}</div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="teachingSkills">مهارات التدريس (0-10)</Label>
              <Input
                id="teachingSkills"
                type="number"
                min="0"
                max="10"
                step="0.1"
                value={formData.teachingSkills}
                onChange={(e) => setFormData(prev => ({ ...prev, teachingSkills: e.target.value }))}
                placeholder="أدخل درجة من 0 إلى 10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="classManagement">إدارة الفصل (0-10)</Label>
              <Input
                id="classManagement"
                type="number"
                min="0"
                max="10"
                step="0.1"
                value={formData.classManagement}
                onChange={(e) => setFormData(prev => ({ ...prev, classManagement: e.target.value }))}
                placeholder="أدخل درجة من 0 إلى 10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="studentProgress">تقدم الطلاب (0-10)</Label>
              <Input
                id="studentProgress"
                type="number"
                min="0"
                max="10"
                step="0.1"
                value={formData.studentProgress}
                onChange={(e) => setFormData(prev => ({ ...prev, studentProgress: e.target.value }))}
                placeholder="أدخل درجة من 0 إلى 10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="attendance">الحضور والالتزام (0-10)</Label>
              <Input
                id="attendance"
                type="number"
                min="0"
                max="10"
                step="0.1"
                value={formData.attendance}
                onChange={(e) => setFormData(prev => ({ ...prev, attendance: e.target.value }))}
                placeholder="أدخل درجة من 0 إلى 10"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="communication">التواصل (0-10)</Label>
              <Input
                id="communication"
                type="number"
                min="0"
                max="10"
                step="0.1"
                value={formData.communication}
                onChange={(e) => setFormData(prev => ({ ...prev, communication: e.target.value }))}
                placeholder="أدخل درجة من 0 إلى 10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="strengths">نقاط القوة</Label>
            <Textarea
              id="strengths"
              value={formData.strengths}
              onChange={(e) => setFormData(prev => ({ ...prev, strengths: e.target.value }))}
              placeholder="أدخل نقاط القوة لدى المعلم"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="improvements">مجالات التحسين</Label>
            <Textarea
              id="improvements"
              value={formData.improvements}
              onChange={(e) => setFormData(prev => ({ ...prev, improvements: e.target.value }))}
              placeholder="أدخل مجالات التحسين للمعلم"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="comments">ملاحظات إضافية</Label>
            <Textarea
              id="comments"
              value={formData.comments}
              onChange={(e) => setFormData(prev => ({ ...prev, comments: e.target.value }))}
              placeholder="أدخل أي ملاحظات إضافية"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-gray-300 hover:bg-gray-100"
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
            >
              {isLoading ? 'جاري التحديث...' : 'تحديث التقييم'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

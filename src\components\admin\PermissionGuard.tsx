'use client';
import React from 'react';
import { usePermissions } from '@/contexts/PermissionsContext';

interface PermissionGuardProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredPermissions?: string[];
  requireAll?: boolean; // true = يحتاج جميع الصلاحيات، false = يحتاج واحدة على الأقل
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * مكون محسن لحماية العناصر بناءً على الصلاحيات
 * يدعم التحقق من صلاحية واحدة أو عدة صلاحيات
 * يستخدم Context لتحسين الأداء
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  requiredPermission,
  requiredPermissions,
  requireAll = false,
  fallback = null,
  showFallback = false
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, userRole, loading, isReady } = usePermissions();

  // أثناء التحميل، لا نعرض شيء
  if (loading || !isReady) {
    return null;
  }

  // المدير لديه جميع الصلاحيات
  if (userRole === 'ADMIN') {
    return <>{children}</>;
  }

  let hasRequiredPermission = false;

  // التحقق من الصلاحيات
  if (requiredPermission) {
    hasRequiredPermission = hasPermission(requiredPermission);
  } else if (requiredPermissions && requiredPermissions.length > 0) {
    if (requireAll) {
      hasRequiredPermission = hasAllPermissions(requiredPermissions);
    } else {
      hasRequiredPermission = hasAnyPermission(requiredPermissions);
    }
  } else {
    // إذا لم يتم تحديد أي صلاحية، نعرض المحتوى (للمدير فقط)
    hasRequiredPermission = false;
  }

  if (hasRequiredPermission) {
    return <>{children}</>;
  }

  // إذا لم يكن لديه صلاحية وطُلب عرض fallback
  if (showFallback && fallback) {
    return <>{fallback}</>;
  }

  // لا يعرض شيء إذا لم يكن لديه صلاحية
  return null;
};

export default PermissionGuard;

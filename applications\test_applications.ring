/*
==============================================================================
    اختبار تطبيقات Praetorian.ring
    
    الوصف: ملف اختبار للتأكد من عمل التطبيقات بشكل صحيح
    المؤلف: Praetorian Team
==============================================================================
*/

# تحميل مكتبة Praetorian
load "../praetorian.ring"

/*
==============================================================================
    دوال الاختبار
==============================================================================
*/

/*
دالة iif للاختبار
*/
func iif bCondition, vTrueValue, vFalseValue
    if bCondition
        return vTrueValue
    else
        return vFalseValue
    ok

/*
اختبار وجود الملفات المطلوبة
*/
func testRequiredFiles
    ? "=== اختبار وجود الملفات المطلوبة ==="
    
    aRequiredFiles = [
        "ReconDash/ReconDash.ring",
        "ReconDash/config.ring",
        "ReconDash/run.bat",
        "DirHunter/DirHunter.ring",
        "DirHunter/wordlist.txt",
        "DirHunter/run_example.bat",
        "README.md"
    ]
    
    nPassed = 0
    nTotal = len(aRequiredFiles)
    
    for cFile in aRequiredFiles
        if fexists(cFile)
            ? "✓ " + cFile + " - موجود"
            nPassed++
        else
            ? "✗ " + cFile + " - مفقود"
        ok
    next
    
    ? ""
    ? "النتيجة: " + nPassed + "/" + nTotal + " ملف موجود"
    return nPassed = nTotal

/*
اختبار تحميل مكتبة Praetorian
*/
func testPraetorianLibrary
    ? "=== اختبار مكتبة Praetorian ==="
    
    try
        oPraetorian = CreatePraetorian()
        if oPraetorian != NULL
            ? "✓ تم تحميل مكتبة Praetorian بنجاح"
            
            # اختبار الوحدات الفرعية
            if oPraetorian.Network != NULL
                ? "✓ وحدة الشبكة متاحة"
            else
                ? "✗ وحدة الشبكة غير متاحة"
            ok
            
            if oPraetorian.Web != NULL
                ? "✓ وحدة الويب متاحة"
            else
                ? "✗ وحدة الويب غير متاحة"
            ok
            
            if oPraetorian.Crypto != NULL
                ? "✓ وحدة التشفير متاحة"
            else
                ? "✗ وحدة التشفير غير متاحة"
            ok
            
            return true
        else
            ? "✗ فشل في إنشاء مثيل من Praetorian"
            return false
        ok
    catch
        ? "✗ خطأ في تحميل مكتبة Praetorian: " + cCatchError
        return false
    done

/*
اختبار تحميل ReconDash (بدون تشغيل الواجهة)
*/
func testReconDashLoading
    ? "=== اختبار تحميل ReconDash ==="
    
    try
        # محاولة تحليل ملف ReconDash
        cReconDashContent = read("ReconDash/ReconDash.ring")
        
        # فحص وجود الدوال الأساسية
        aRequiredFunctions = [
            "updateStatus",
            "clearResults", 
            "updateOverviewTab",
            "updateSSLTab",
            "updateCrawlerTab",
            "performBasicScan",
            "performSSLScan",
            "performWebCrawl",
            "onScanButtonClicked",
            "createMainWindow",
            "main"
        ]
        
        nFoundFunctions = 0
        for cFunction in aRequiredFunctions
            if substr(cReconDashContent, "func " + cFunction) > 0
                ? "✓ دالة " + cFunction + " موجودة"
                nFoundFunctions++
            else
                ? "✗ دالة " + cFunction + " مفقودة"
            ok
        next
        
        ? ""
        ? "النتيجة: " + nFoundFunctions + "/" + len(aRequiredFunctions) + " دالة موجودة"
        return nFoundFunctions = len(aRequiredFunctions)
        
    catch
        ? "✗ خطأ في قراءة ملف ReconDash: " + cCatchError
        return false
    done

/*
اختبار تحميل DirHunter
*/
func testDirHunterLoading
    ? "=== اختبار تحميل DirHunter ==="
    
    try
        # محاولة تحليل ملف DirHunter
        cDirHunterContent = read("DirHunter/DirHunter.ring")
        
        # فحص وجود الدوال الأساسية
        aRequiredFunctions = [
            "printColored",
            "printSuccess",
            "printError",
            "printInfo",
            "showHelp",
            "parseArguments",
            "loadWordlist",
            "testSinglePath",
            "runDirectoryHunt",
            "main"
        ]
        
        nFoundFunctions = 0
        for cFunction in aRequiredFunctions
            if substr(cDirHunterContent, "func " + cFunction) > 0
                ? "✓ دالة " + cFunction + " موجودة"
                nFoundFunctions++
            else
                ? "✗ دالة " + cFunction + " مفقودة"
            ok
        next
        
        ? ""
        ? "النتيجة: " + nFoundFunctions + "/" + len(aRequiredFunctions) + " دالة موجودة"
        return nFoundFunctions = len(aRequiredFunctions)
        
    catch
        ? "✗ خطأ في قراءة ملف DirHunter: " + cCatchError
        return false
    done

/*
اختبار قائمة الكلمات
*/
func testWordlist
    ? "=== اختبار قائمة الكلمات ==="
    
    try
        if not fexists("DirHunter/wordlist.txt")
            ? "✗ ملف قائمة الكلمات غير موجود"
            return false
        ok
        
        cWordlistContent = read("DirHunter/wordlist.txt")
        aLines = split(cWordlistContent, nl)
        
        nWords = 0
        nComments = 0
        nEmpty = 0
        
        for cLine in aLines
            cLine = trim(cLine)
            if len(cLine) = 0
                nEmpty++
            but left(cLine, 1) = "#"
                nComments++
            else
                nWords++
            ok
        next
        
        ? "✓ إجمالي الأسطر: " + len(aLines)
        ? "✓ الكلمات: " + nWords
        ? "✓ التعليقات: " + nComments
        ? "✓ الأسطر الفارغة: " + nEmpty
        
        if nWords > 0
            ? "✓ قائمة الكلمات صالحة"
            return true
        else
            ? "✗ قائمة الكلمات فارغة"
            return false
        ok
        
    catch
        ? "✗ خطأ في قراءة قائمة الكلمات: " + cCatchError
        return false
    done

/*
اختبار ملف التكوين
*/
func testConfigFile
    ? "=== اختبار ملف التكوين ==="
    
    try
        if not fexists("ReconDash/config.ring")
            ? "✗ ملف التكوين غير موجود"
            return false
        ok
        
        # محاولة تحميل ملف التكوين
        load "ReconDash/config.ring"
        
        # فحص وجود المتغيرات الأساسية
        aRequiredVars = [
            "WINDOW_WIDTH",
            "WINDOW_HEIGHT", 
            "SCANNER_TIMEOUT",
            "HTTP_TIMEOUT",
            "CRAWLER_MAX_DEPTH",
            "SSL_TIMEOUT"
        ]
        
        nFoundVars = 0
        for cVar in aRequiredVars
            # في Ring، لا يمكننا فحص وجود المتغيرات بسهولة
            # لذا سنفترض أنها موجودة إذا تم تحميل الملف بنجاح
            ? "✓ متغير " + cVar + " (مفترض)"
            nFoundVars++
        next
        
        ? ""
        ? "✓ تم تحميل ملف التكوين بنجاح"
        return true
        
    catch
        ? "✗ خطأ في تحميل ملف التكوين: " + cCatchError
        return false
    done

/*
اختبار المكتبات الخارجية
*/
func testExternalLibraries
    ? "=== اختبار المكتبات الخارجية ==="
    
    # اختبار libui
    try
        load "libui.ring"
        ? "✓ libui.ring متاح"
        bLibUIAvailable = true
    catch
        ? "✗ libui.ring غير متاح - ReconDash قد لا يعمل"
        bLibUIAvailable = false
    done
    
    # اختبار rogueutil
    try
        load "rogueutil.ring"
        ? "✓ rogueutil.ring متاح"
        bRogueUtilAvailable = true
    catch
        ? "✗ rogueutil.ring غير متاح - DirHunter سيعمل بدون ألوان"
        bRogueUtilAvailable = false
    done
    
    # اختبار threads
    try
        load "threads.ring"
        ? "✓ threads.ring متاح"
        bThreadsAvailable = true
    catch
        ? "✗ threads.ring غير متاح - التعدد قد لا يعمل"
        bThreadsAvailable = false
    done
    
    return [
        :libui = bLibUIAvailable,
        :rogueutil = bRogueUtilAvailable,
        :threads = bThreadsAvailable
    ]

/*
==============================================================================
    الدالة الرئيسية للاختبار
==============================================================================
*/

func main
    ? ""
    ? "=============================================="
    ? "اختبار تطبيقات Praetorian.ring"
    ? "=============================================="
    ? "التاريخ: " + date() + " " + time()
    ? ""
    
    # تشغيل جميع الاختبارات
    aResults = []
    
    add(aResults, testRequiredFiles())
    ? ""
    
    add(aResults, testPraetorianLibrary())
    ? ""
    
    add(aResults, testReconDashLoading())
    ? ""
    
    add(aResults, testDirHunterLoading())
    ? ""
    
    add(aResults, testWordlist())
    ? ""
    
    add(aResults, testConfigFile())
    ? ""
    
    aLibraryResults = testExternalLibraries()
    ? ""
    
    # حساب النتائج
    nPassed = 0
    for bResult in aResults
        if bResult
            nPassed++
        ok
    next
    
    # طباعة الملخص النهائي
    ? "=============================================="
    ? "ملخص نتائج الاختبار"
    ? "=============================================="
    ? "الاختبارات الناجحة: " + nPassed + "/" + len(aResults)
    
    if nPassed = len(aResults)
        ? "🎉 جميع الاختبارات نجحت!"
        ? ""
        ? "التطبيقات جاهزة للاستخدام:"
        ? "- ReconDash: " + iif(aLibraryResults[:libui], "جاهز للتشغيل", "يحتاج libui.ring")
        ? "- DirHunter: جاهز للتشغيل"
    else
        ? "⚠ بعض الاختبارات فشلت"
        ? "يرجى مراجعة الأخطاء أعلاه وإصلاحها"
    ok
    
    ? ""
    ? "ملاحظات:"
    ? "- libui.ring مطلوب لتشغيل ReconDash"
    ? "- rogueutil.ring اختياري لألوان DirHunter"
    ? "- threads.ring اختياري للتعدد"
    ? "=============================================="

# تشغيل الاختبارات
main()

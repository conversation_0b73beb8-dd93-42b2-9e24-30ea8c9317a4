'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';
import { LucideIcon } from 'lucide-react';

const iconVariants = cva(
  "h-5 w-5",
  {
    variants: {
      variant: {
        default: "text-gray-500",
        success: "text-primary-color",
        danger: "text-red-500",
        warning: "text-amber-500",
        info: "text-blue-500",
        primary: "text-[var(--primary-color)]",
      }
    },
    defaultVariants: {
      variant: "default",
    }
  }
);

const badgeVariants = cva(
  "inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold",
  {
    variants: {
      variant: {
        default: "bg-gray-100 text-gray-800",
        success: "bg-green-100 text-green-800",
        danger: "bg-red-100 text-red-800",
        warning: "bg-amber-100 text-amber-800",
        info: "bg-blue-100 text-blue-800",
        primary: "bg-[#e6f7f4] text-[var(--primary-color)]",
      }
    },
    defaultVariants: {
      variant: "default",
    }
  }
);

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
    text: string;
  };
  variant?: "default" | "success" | "danger" | "warning" | "info" | "primary";
  className?: string;
}

export function StatsCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  variant = "default",
  className,
}: StatsCardProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {Icon && <Icon className={iconVariants({ variant })} />}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {trend && (
          <div className="mt-2">
            <span className={badgeVariants({ variant: trend.isPositive ? "success" : "danger" })}>
              {trend.isPositive ? "↑" : "↓"} {Math.abs(trend.value)}% {trend.text}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";
import { UserRole } from "@prisma/client";

// GET: جلب تقييمات المعلمين
export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.EMPLOYEE)) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو موظف" },
        { status: 401 }
      );
    }

    // استخراج المعلمات من URL
    const { searchParams } = new URL(request.url);
    const teacherId = searchParams.get('teacherId');
    const limit = parseInt(searchParams.get('limit') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // بناء شروط البحث
    const where: {
      teacherId?: number;
      evaluationDate?: {
        gte?: Date;
        lte?: Date;
      };
    } = {};

    if (teacherId) {
      where.teacherId = parseInt(teacherId);
    }

    if (startDate && endDate) {
      where.evaluationDate = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    } else if (startDate) {
      where.evaluationDate = {
        gte: new Date(startDate)
      };
    } else if (endDate) {
      where.evaluationDate = {
        lte: new Date(endDate)
      };
    }

    // جلب إجمالي عدد التقييمات
    const total = await prisma.teacherEvaluation.count({ where });

    // جلب التقييمات
    const evaluations = await prisma.teacherEvaluation.findMany({
      where,
      include: {
        teacher: {
          select: {
            id: true,
            name: true,
            specialization: true,
            user: {
              select: {
                username: true
              }
            }
          }
        },
        evaluator: {
          select: {
            id: true,
            username: true,
            role: true,
            profile: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        evaluationDate: 'desc'
      },
      skip: (page - 1) * limit,
      take: limit
    });

    return NextResponse.json({
      data: evaluations,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching teacher evaluations:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب تقييمات المعلمين" },
      { status: 500 }
    );
  }
}

// POST: إنشاء تقييم جديد للمعلم
export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.EMPLOYEE)) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو موظف" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.teacherId ||
        body.teachingSkills === undefined ||
        body.classManagement === undefined ||
        body.studentProgress === undefined ||
        body.attendance === undefined ||
        body.communication === undefined) {
      return NextResponse.json(
        { message: "البيانات غير مكتملة" },
        { status: 400 }
      );
    }

    // حساب التقييم العام
    const overallRating = (
      parseFloat(body.teachingSkills) * 0.3 +
      parseFloat(body.classManagement) * 0.2 +
      parseFloat(body.studentProgress) * 0.2 +
      parseFloat(body.attendance) * 0.15 +
      parseFloat(body.communication) * 0.15
    ).toFixed(2);

    // إنشاء التقييم
    const evaluation = await prisma.teacherEvaluation.create({
      data: {
        teacherId: parseInt(body.teacherId),
        evaluatorId: userData.id,
        evaluationDate: body.evaluationDate ? new Date(body.evaluationDate) : new Date(),
        teachingSkills: parseFloat(body.teachingSkills),
        classManagement: parseFloat(body.classManagement),
        studentProgress: parseFloat(body.studentProgress),
        attendance: parseFloat(body.attendance),
        communication: parseFloat(body.communication),
        overallRating: parseFloat(overallRating),
        strengths: body.strengths || null,
        improvements: body.improvements || null,
        comments: body.comments || null
      }
    });

    // إنشاء إشعار للمعلم
    const teacher = await prisma.teacher.findUnique({
      where: { id: parseInt(body.teacherId) },
      include: {
        user: true
      }
    });

    if (teacher && teacher.user) {
      await prisma.notification.create({
        data: {
          title: "تقييم أداء جديد",
          content: `تم إضافة تقييم أداء جديد لك بتقدير عام: ${overallRating}/10`,
          type: "GENERAL",
          userId: teacher.user.id
        }
      });
    }

    return NextResponse.json(evaluation, { status: 201 });
  } catch (error) {
    console.error('Error creating teacher evaluation:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء تقييم المعلم" },
      { status: 500 }
    );
  }
}

// PUT: تحديث تقييم المعلم
export async function PUT(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.EMPLOYEE)) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو موظف" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // التحقق من البيانات المطلوبة
    if (!body.id) {
      return NextResponse.json(
        { message: "معرف التقييم مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود التقييم
    const existingEvaluation = await prisma.teacherEvaluation.findUnique({
      where: { id: parseInt(body.id) }
    });

    if (!existingEvaluation) {
      return NextResponse.json(
        { message: "التقييم غير موجود" },
        { status: 404 }
      );
    }

    // استخدام القيمة الحالية للتقييم العام
    // في Prisma، يتم التعامل مع الأرقام العشرية بشكل صحيح عند التحديث
    const overallRating = existingEvaluation.overallRating;

    // تحديث التقييم
    const updatedEvaluation = await prisma.teacherEvaluation.update({
      where: { id: parseInt(body.id) },
      data: {
        teachingSkills: body.teachingSkills !== undefined ? parseFloat(body.teachingSkills) : undefined,
        classManagement: body.classManagement !== undefined ? parseFloat(body.classManagement) : undefined,
        studentProgress: body.studentProgress !== undefined ? parseFloat(body.studentProgress) : undefined,
        attendance: body.attendance !== undefined ? parseFloat(body.attendance) : undefined,
        communication: body.communication !== undefined ? parseFloat(body.communication) : undefined,
        overallRating,
        strengths: body.strengths !== undefined ? body.strengths : undefined,
        improvements: body.improvements !== undefined ? body.improvements : undefined,
        comments: body.comments !== undefined ? body.comments : undefined,
        evaluationDate: body.evaluationDate ? new Date(body.evaluationDate) : undefined
      }
    });

    return NextResponse.json(updatedEvaluation);
  } catch (error) {
    console.error('Error updating teacher evaluation:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث تقييم المعلم" },
      { status: 500 }
    );
  }
}

// DELETE: حذف تقييم المعلم
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول" },
        { status: 401 }
      );
    }

    // استخراج معرف التقييم من URL
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: "معرف التقييم مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود التقييم
    const existingEvaluation = await prisma.teacherEvaluation.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingEvaluation) {
      return NextResponse.json(
        { message: "التقييم غير موجود" },
        { status: 404 }
      );
    }

    // حذف التقييم
    await prisma.teacherEvaluation.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({ message: "تم حذف التقييم بنجاح" });
  } catch (error) {
    console.error('Error deleting teacher evaluation:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف تقييم المعلم" },
      { status: 500 }
    );
  }
}

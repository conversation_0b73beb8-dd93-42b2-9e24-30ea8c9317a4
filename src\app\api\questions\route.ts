import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ActivityLogger, ActivityType } from "@/lib/activity-logger";
import { QuestionType, DifficultyLevel } from "@prisma/client";

// GET /api/questions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const bankId = searchParams.get('bankId');
    const type = searchParams.get('type') as QuestionType | null;
    const difficultyLevel = searchParams.get('difficultyLevel') as DifficultyLevel | null;

    // بناء شروط البحث
    const where: Record<string, unknown> = {};

    if (bankId) {
      where.bankId = parseInt(bankId);
    }

    if (type) {
      where.type = type;
    }

    if (difficultyLevel) {
      where.difficultyLevel = difficultyLevel;
    }

    const questions = await prisma.question.findMany({
      where,
      include: {
        bank: {
          select: {
            id: true,
            name: true
          }
        },
        options: {
          orderBy: {
            order: 'asc'
          }
        },
        answers: true,
        _count: {
          select: {
            examQuestions: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      data: questions,
      success: true,
      message: 'تم جلب الأسئلة بنجاح'
    });
  } catch (error) {
    console.error('Error fetching questions:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب الأسئلة',
      success: false
    }, { status: 500 });
  }
}

// POST /api/questions
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      text,
      type,
      difficultyLevel,
      points,
      bankId,
      options,
      answers
    } = body;

    // التحقق من البيانات المطلوبة
    if (!text || !type || !bankId) {
      return NextResponse.json({
        error: 'نص السؤال ونوعه وبنك الأسئلة مطلوبة',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة نوع السؤال
    const validQuestionTypes = Object.values(QuestionType);
    if (!validQuestionTypes.includes(type)) {
      return NextResponse.json({
        error: 'نوع السؤال غير صالح',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة مستوى الصعوبة
    if (difficultyLevel) {
      const validDifficultyLevels = Object.values(DifficultyLevel);
      if (!validDifficultyLevels.includes(difficultyLevel)) {
        return NextResponse.json({
          error: 'مستوى الصعوبة غير صالح',
          success: false
        }, { status: 400 });
      }
    }

    // إنشاء السؤال مع الخيارات والإجابات في عملية واحدة
    const question = await prisma.$transaction(async (tx) => {
      // إنشاء السؤال
      const newQuestion = await tx.question.create({
        data: {
          text,
          type: type as QuestionType,
          difficultyLevel: difficultyLevel as DifficultyLevel || DifficultyLevel.MEDIUM,
          points: points ? parseFloat(points) : 1,
          bankId: parseInt(bankId)
        }
      });

      // إضافة الخيارات إذا كانت موجودة
      if (options && Array.isArray(options) && options.length > 0) {
        for (let i = 0; i < options.length; i++) {
          const option = options[i];
          await tx.questionOption.create({
            data: {
              questionId: newQuestion.id,
              text: option.text,
              isCorrect: option.isCorrect || false,
              order: i
            }
          });
        }
      }

      // إضافة الإجابات إذا كانت موجودة
      if (answers && Array.isArray(answers) && answers.length > 0) {
        for (const answer of answers) {
          await tx.questionAnswer.create({
            data: {
              questionId: newQuestion.id,
              text: answer.text,
              isCorrect: answer.isCorrect !== undefined ? answer.isCorrect : true,
              explanation: answer.explanation || null
            }
          });
        }
      }

      // إرجاع السؤال مع الخيارات والإجابات
      return tx.question.findUnique({
        where: { id: newQuestion.id },
        include: {
          bank: {
            select: {
              id: true,
              name: true
            }
          },
          options: {
            orderBy: {
              order: 'asc'
            }
          },
          answers: true
        }
      });
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.GENERAL,
      `تم إنشاء سؤال جديد في بنك الأسئلة: ${question?.bank?.name}`
    );

    return NextResponse.json({
      data: question,
      success: true,
      message: 'تم إنشاء السؤال بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating question:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إنشاء السؤال',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/questions
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      id,
      text,
      type,
      difficultyLevel,
      points,
      bankId,
      options,
      answers
    } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !text || !type || !bankId) {
      return NextResponse.json({
        error: 'معرف السؤال ونصه ونوعه وبنك الأسئلة مطلوبة',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة نوع السؤال
    const validQuestionTypes = Object.values(QuestionType);
    if (!validQuestionTypes.includes(type)) {
      return NextResponse.json({
        error: 'نوع السؤال غير صالح',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة مستوى الصعوبة
    if (difficultyLevel) {
      const validDifficultyLevels = Object.values(DifficultyLevel);
      if (!validDifficultyLevels.includes(difficultyLevel)) {
        return NextResponse.json({
          error: 'مستوى الصعوبة غير صالح',
          success: false
        }, { status: 400 });
      }
    }

    // تحديث السؤال مع الخيارات والإجابات في عملية واحدة
    const question = await prisma.$transaction(async (tx) => {
      // تحديث السؤال
      const updatedQuestion = await tx.question.update({
        where: { id: parseInt(id) },
        data: {
          text,
          type: type as QuestionType,
          difficultyLevel: difficultyLevel as DifficultyLevel || DifficultyLevel.MEDIUM,
          points: points ? parseFloat(points) : 1,
          bankId: parseInt(bankId)
        }
      });

      // حذف الخيارات الحالية
      await tx.questionOption.deleteMany({
        where: { questionId: updatedQuestion.id }
      });

      // إضافة الخيارات الجديدة
      if (options && Array.isArray(options) && options.length > 0) {
        for (let i = 0; i < options.length; i++) {
          const option = options[i];
          await tx.questionOption.create({
            data: {
              questionId: updatedQuestion.id,
              text: option.text,
              isCorrect: option.isCorrect || false,
              order: i
            }
          });
        }
      }

      // حذف الإجابات الحالية
      await tx.questionAnswer.deleteMany({
        where: { questionId: updatedQuestion.id }
      });

      // إضافة الإجابات الجديدة
      if (answers && Array.isArray(answers) && answers.length > 0) {
        for (const answer of answers) {
          await tx.questionAnswer.create({
            data: {
              questionId: updatedQuestion.id,
              text: answer.text,
              isCorrect: answer.isCorrect !== undefined ? answer.isCorrect : true,
              explanation: answer.explanation || null
            }
          });
        }
      }

      // إرجاع السؤال مع الخيارات والإجابات
      return tx.question.findUnique({
        where: { id: updatedQuestion.id },
        include: {
          bank: {
            select: {
              id: true,
              name: true
            }
          },
          options: {
            orderBy: {
              order: 'asc'
            }
          },
          answers: true
        }
      });
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.UPDATE,
      `تم تحديث سؤال في بنك الأسئلة: ${question?.bank?.name}`
    );

    return NextResponse.json({
      data: question,
      success: true,
      message: 'تم تحديث السؤال بنجاح'
    });
  } catch (error) {
    console.error('Error updating question:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث السؤال',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/questions
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف السؤال مطلوب',
        success: false
      }, { status: 400 });
    }

    // الحصول على معلومات السؤال قبل الحذف
    const question = await prisma.question.findUnique({
      where: { id: parseInt(id) },
      include: {
        bank: {
          select: {
            name: true
          }
        }
      }
    });

    if (!question) {
      return NextResponse.json({
        error: 'السؤال غير موجود',
        success: false
      }, { status: 404 });
    }

    // حذف السؤال (سيتم حذف الخيارات والإجابات تلقائيًا بسبب onDelete: Cascade)
    await prisma.question.delete({
      where: { id: parseInt(id) }
    });

    // تسجيل النشاط
    await ActivityLogger.log(
      1, // سيتم استبداله بمعرف المستخدم الحقيقي من الجلسة
      ActivityType.GENERAL,
      `تم حذف سؤال من بنك الأسئلة: ${question.bank?.name}`
    );

    return NextResponse.json({
      success: true,
      message: 'تم حذف السؤال بنجاح'
    });
  } catch (error) {
    console.error('Error deleting question:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف السؤال',
      success: false
    }, { status: 500 });
  }
}

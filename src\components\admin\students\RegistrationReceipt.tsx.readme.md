# مكون وصل التسجيل - التوثيق

## 📋 نظرة عامة

مكون React لعرض وطباعة وصولات التسجيل للتلاميذ مع إمكانية الإنشاء التلقائي.

## 🔗 المسار
`src/components/admin/students/RegistrationReceipt.tsx`

## 🎯 الغرض

- عرض وصل التسجيل بتصميم احترافي
- طباعة الوصل مع تسجيل عملية الطباعة
- إنشاء وصولات جديدة تلقائياً
- تحميل الوصل كملف PDF (قيد التطوير)

## 📊 الخصائص (Props)

```typescript
interface RegistrationReceiptProps {
  studentId: number;          // معرف التلميذ (مطلوب)
  receiptId?: number;         // معرف وصل محدد (اختياري)
  onPrint?: () => void;       // دالة تُستدعى عند الطباعة
  onDownload?: () => void;    // دالة تُستدعى عند التحميل
  onClose?: () => void;       // دالة تُستدعى عند الإغلاق
  className?: string;         // فئات CSS إضافية
  autoGenerate?: boolean;     // إنشاء وصل جديد تلقائياً (افتراضي: false)
}
```

## 🎨 أقسام الوصل

### 1. رأس الوصل
- **عنوان:** "وصل تسجيل"
- **اسم المدرسة:** من بيانات المدرسة
- **العنوان والاتصال:** معلومات المدرسة الكاملة
- **تصميم متدرج:** من اللون الأساسي إلى درجة أغمق

### 2. معلومات الوصل
```typescript
{
  receiptNumber: string;      // رقم الوصل الفريد
  issueDate: string;          // تاريخ الإصدار (DD/MM/YYYY)
  registrationFee: number;    // رسوم التسجيل
  paymentStatus: string;      // حالة الدفع
  isPrinted: boolean;         // هل تم طباعته
  printedAt?: string;         // تاريخ الطباعة
  notes?: string;             // ملاحظات إضافية
}
```

### 3. معلومات التلميذ
```typescript
{
  name: string;               // الاسم الكامل
  username: string;           // اسم المستخدم
  age: number;                // العمر
  phone?: string;             // رقم الهاتف
  classe?: {                  // الفصل
    name: string;
  };
}
```

### 4. معلومات ولي الأمر
```typescript
{
  name: string;               // اسم ولي الأمر
  phone: string;              // رقم الهاتف
  email?: string;             // البريد الإلكتروني
  address?: string;           // العنوان
}
```

### 5. معلومات المدرسة
```typescript
{
  name: string;               // اسم المدرسة
  address: string;            // عنوان المدرسة
  phone: string;              // هاتف المدرسة
  email: string;              // بريد المدرسة الإلكتروني
}
```

## 🎨 حالات الدفع

### الحالات المدعومة
- **PAID:** مدفوع (أخضر)
- **PENDING:** في انتظار الدفع (أصفر)
- **CANCELLED:** ملغي (أحمر)
- **PARTIAL:** دفع جزئي (أزرق)
- **REFUNDED:** مسترد (رمادي)

### الألوان والأيقونات
```typescript
const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case 'PAID': return 'bg-green-100 text-green-800';
    case 'PENDING': return 'bg-yellow-100 text-yellow-800';
    case 'CANCELLED': return 'bg-red-100 text-red-800';
    case 'PARTIAL': return 'bg-blue-100 text-blue-800';
    case 'REFUNDED': return 'bg-gray-100 text-gray-800';
  }
};

const getPaymentStatusIcon = (status: string) => {
  switch (status) {
    case 'PAID': return <FaCheckCircle />;
    case 'PENDING': return <FaClock />;
    case 'CANCELLED': return <FaTimes />;
    default: return <FaClock />;
  }
};
```

## 🔄 سير العمل

### 1. تحميل الوصل
```typescript
const fetchReceipt = async () => {
  let url = `/api/students/${studentId}/registration-receipt`;
  if (receiptId) {
    url += `?receiptId=${receiptId}`;
  }
  
  const response = await fetch(url);
  const data = await response.json();
  
  if (data.success) {
    if (receiptId) {
      setReceiptData(data.data);
    } else {
      const receipts = data.data;
      if (receipts.length > 0) {
        setReceiptData(receipts[0]);
      } else if (autoGenerate) {
        await generateNewReceipt();
      }
    }
  }
};
```

### 2. الإنشاء التلقائي
```typescript
const generateNewReceipt = async () => {
  const response = await fetch(`/api/students/${studentId}/registration-receipt`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      registrationFee: 500,
      paymentStatus: 'PENDING',
      notes: 'وصل تسجيل تلقائي'
    })
  });
  
  if (response.ok) {
    // إعادة تحميل بيانات الوصل الجديد
    await fetchReceipt();
    toast.success('تم إنشاء وصل التسجيل بنجاح');
  }
};
```

### 3. الطباعة مع التسجيل
```typescript
const handlePrint = async () => {
  // تسجيل أن الوصل تم طباعته
  await fetch(`/api/students/${studentId}/registration-receipt`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      receiptId: receiptData.id,
      markAsPrinted: true
    })
  });
  
  // طباعة الوصل
  const printContent = receiptRef.current.innerHTML;
  document.body.innerHTML = `
    <div style="direction: rtl; font-family: Arial, sans-serif;">
      <style>
        @media print {
          .no-print { display: none !important; }
          .receipt-border { border: 2px solid #333; }
        }
      </style>
      ${printContent}
    </div>
  `;
  
  window.print();
  window.location.reload();
};
```

## 🖨️ ميزات الطباعة

### تحسينات الطباعة
- **الحدود:** إضافة حدود للوصل في الطباعة
- **إخفاء العناصر:** `.no-print` للأزرار
- **تعديل الألوان:** تحويل الخلفيات الملونة لرمادي
- **الخط:** Arial للوضوح

### تسجيل الطباعة
- تحديث `isPrinted` إلى `true`
- تسجيل `printedAt` بالتاريخ الحالي
- تسجيل `printedBy` بمعرف المستخدم

## 📱 حالات التحميل والأخطاء

### حالة التحميل
```tsx
if (isLoading || isGenerating) {
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          <span className="mr-3">
            {isGenerating ? 'جاري إنشاء وصل التسجيل...' : 'جاري تحميل بيانات الوصل...'}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
```

### حالة الخطأ مع إمكانية الإنشاء
```tsx
if (error || !receiptData) {
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardContent className="p-8">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <p>{error || 'لم يتم العثور على وصل التسجيل'}</p>
          </div>
          {autoGenerate && !receiptId && (
            <Button onClick={generateNewReceipt} disabled={isGenerating}>
              {isGenerating ? 'جاري الإنشاء...' : 'إنشاء وصل جديد'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
```

## 🔧 التكامل مع API

### جلب الوصولات
```typescript
// جلب آخر وصل للتلميذ
GET /api/students/{studentId}/registration-receipt

// جلب وصل محدد
GET /api/students/{studentId}/registration-receipt?receiptId={id}
```

### إنشاء وصل جديد
```typescript
POST /api/students/{studentId}/registration-receipt
{
  "registrationFee": 500,
  "paymentStatus": "PENDING",
  "notes": "وصل تسجيل تلقائي"
}
```

### تحديث الوصل
```typescript
PUT /api/students/{studentId}/registration-receipt
{
  "receiptId": 1,
  "markAsPrinted": true
}
```

## 📝 أمثلة على الاستخدام

### عرض آخر وصل للتلميذ
```tsx
<RegistrationReceipt
  studentId={1}
  onPrint={() => console.log('تم طباعة الوصل')}
  onClose={() => setShowReceipt(false)}
/>
```

### عرض وصل محدد
```tsx
<RegistrationReceipt
  studentId={1}
  receiptId={5}
  onPrint={() => console.log('تم طباعة الوصل')}
/>
```

### إنشاء وصل جديد تلقائياً
```tsx
<RegistrationReceipt
  studentId={1}
  autoGenerate={true}
  onPrint={() => console.log('تم طباعة الوصل الجديد')}
  onClose={() => setShowReceipt(false)}
/>
```

### في نموذج تسجيل تلميذ جديد
```tsx
const [showReceipt, setShowReceipt] = useState(false);
const [newStudentId, setNewStudentId] = useState<number | null>(null);

const handleStudentRegistered = (studentId: number) => {
  setNewStudentId(studentId);
  setShowReceipt(true);
};

return (
  <div>
    <StudentRegistrationForm onSuccess={handleStudentRegistered} />
    
    {showReceipt && newStudentId && (
      <RegistrationReceipt
        studentId={newStudentId}
        autoGenerate={true}
        onClose={() => setShowReceipt(false)}
        onPrint={() => {
          toast.success('تم طباعة وصل التسجيل');
          setShowReceipt(false);
        }}
      />
    )}
  </div>
);
```

## 🔍 الاعتماديات

### مكونات UI
- `Button`, `Card`, `Badge`, `Separator` من `@/components/ui`

### المكتبات الخارجية
- `react-toastify` للإشعارات
- `react-icons/fa` للأيقونات

### APIs المطلوبة
- `/api/students/[id]/registration-receipt` لجميع العمليات

## ⚠️ ملاحظات مهمة

1. **الإنشاء التلقائي:** يحدث فقط عند تفعيل `autoGenerate`
2. **تسجيل الطباعة:** يتم تلقائياً عند الطباعة
3. **التواريخ:** جميعها بصيغة DD/MM/YYYY
4. **رقم الوصل:** فريد ومُولد تلقائياً
5. **الطباعة:** تتطلب إعادة تحميل الصفحة
6. **التحميل:** ميزة PDF قيد التطوير

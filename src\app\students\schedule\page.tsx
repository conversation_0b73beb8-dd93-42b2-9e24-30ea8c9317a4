"use client";
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { FaCalendarAlt, FaClock, FaChalkboardTeacher } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface ScheduleItem {
  id: number;
  day: string;
  startTime: string;
  endTime: string;
  subjectId: number;
  subjectName: string;
  teacherId: number;
  teacherName: string;
}

const daysOfWeek = [
  { id: 'SUNDAY', name: 'الأحد' },
  { id: 'MONDAY', name: 'الإثنين' },
  { id: 'TUESDAY', name: 'الثلاثاء' },
  { id: 'WEDNESDAY', name: 'الأربعاء' },
  { id: 'THURSDAY', name: 'الخميس' },
  { id: 'FRIDAY', name: 'الجمعة' },
  { id: 'SATURDAY', name: 'السبت' }
];

const StudentSchedulePage = () => {
  const [schedule, setSchedule] = useState<ScheduleItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/student-schedule');

        if (!response.ok) {
          throw new Error('فشل في جلب جدول الحصص');
        }

        const data = await response.json();
        setSchedule(data.schedule);
      } catch (err) {
        console.error('Error fetching schedule:', err);
        setError('حدث خطأ أثناء جلب جدول الحصص');
        toast.error('فشل في جلب جدول الحصص');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchedule();
  }, []);

  // تنظيم جدول الحصص حسب اليوم
  const scheduleByDay = daysOfWeek.map(day => {
    const daySchedule = schedule.filter(item => item.day === day.id);
    return {
      ...day,
      schedule: daySchedule.sort((a, b) => {
        return a.startTime.localeCompare(b.startTime);
      })
    };
  });

  // تحديد اليوم الحالي
  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toUpperCase();
  const currentDay = daysOfWeek.find(day => day.id === today)?.id || daysOfWeek[0].id;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">جدول الحصص</h1>
          <p className="text-gray-500">عرض جدول الحصص الأسبوعي</p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : schedule.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-4">لا يوجد جدول حصص متاح</div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {/* ملخص */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-blue-100 text-blue-500">
                    <FaCalendarAlt className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">عدد الحصص</p>
                    <p className="text-2xl font-bold text-gray-800">{schedule.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-green-100 text-primary-color">
                    <FaClock className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">اليوم الحالي</p>
                    <p className="text-2xl font-bold text-gray-800">
                      {daysOfWeek.find(day => day.id === currentDay)?.name}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-purple-100 text-purple-500">
                    <FaChalkboardTeacher className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">عدد المعلمين</p>
                    <p className="text-2xl font-bold text-gray-800">
                      {new Set(schedule.map(item => item.teacherId)).size}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* جدول الحصص */}
          {scheduleByDay.map(day => (
            <Card key={day.id} className={day.schedule.length > 0 ? '' : 'opacity-70'}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaCalendarAlt className="text-[var(--primary-color)]" />
                  <span>{day.name}</span>
                </CardTitle>
                <CardDescription>
                  {day.schedule.length > 0 
                    ? `${day.schedule.length} حصة`
                    : 'لا توجد حصص في هذا اليوم'}
                </CardDescription>
              </CardHeader>
              {day.schedule.length > 0 && (
                <CardContent>
                  <div className="space-y-4">
                    {day.schedule.map(item => (
                      <div 
                        key={item.id} 
                        className="p-4 border rounded-lg flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
                      >
                        <div>
                          <h3 className="font-semibold text-lg">{item.subjectName}</h3>
                          <p className="text-gray-500">المعلم: {item.teacherName}</p>
                        </div>
                        <div className="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-full">
                          <FaClock className="text-[var(--primary-color)]" />
                          <span>
                            {item.startTime} - {item.endTime}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default StudentSchedulePage;

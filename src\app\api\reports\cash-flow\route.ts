import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { startOfMonth, endOfMonth, format, addMonths, subMonths, startOfWeek, endOfWeek, addWeeks, startOfQuarter, endOfQuarter, addQuarters } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Income, Expense, Donation, ExpenseCategory } from '@prisma/client';

// تعريف واجهات البيانات
interface Period {
  start: Date;
  end: Date;
  label: string;
}

interface PeriodicCashFlow {
  month: string;
  income: number;
  expense: number;
  netCashFlow: number;
  runningBalance: number;
}

interface IncomeSource {
  source: string;
  amount: number;
  percentage: number;
}

interface ExpenseCategoryData {
  categoryId: number | null;
  categoryName: string;
  amount: number;
  percentage: number;
}

interface Transaction {
  id: number;
  date: string;
  type: 'income' | 'expense';
  description: string;
  amount: number;
  source?: string;
  category?: string;
}

interface ExpenseWithCategory extends Expense {
  category?: ExpenseCategory | null;
}

// GET /api/reports/cash-flow - الحصول على تقرير التدفق النقدي
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const startDate = searchParams.get('startDate')
      ? new Date(searchParams.get('startDate') as string)
      : subMonths(startOfMonth(new Date()), 6); // آخر 6 أشهر

    const endDate = searchParams.get('endDate')
      ? new Date(searchParams.get('endDate') as string)
      : new Date(); // اليوم الحالي

    const periodType = searchParams.get('periodType') || 'monthly'; // نوع الفترة: daily, weekly, monthly, quarterly
    const type = searchParams.get('type') || 'all'; // نوع التقرير: all, income, expense

    // الحصول على الخزينة
    const treasury = await prisma.treasury.findFirst();

    if (!treasury) {
      return NextResponse.json(
        { error: 'لم يتم العثور على الخزينة' },
        { status: 404 }
      );
    }

    // الحصول على الرصيد الافتتاحي (الرصيد قبل تاريخ البداية)
    const openingBalanceIncome = await prisma.income.aggregate({
      where: {
        date: {
          lt: startDate,
        },
      },
      _sum: {
        amount: true,
      },
    });

    const openingBalanceExpense = await prisma.expense.aggregate({
      where: {
        date: {
          lt: startDate,
        },
      },
      _sum: {
        amount: true,
      },
    });

    const openingBalanceDonation = await prisma.donation.aggregate({
      where: {
        date: {
          lt: startDate,
        },
      },
      _sum: {
        amount: true,
      },
    });

    const openingBalance =
      (openingBalanceIncome._sum.amount || 0) +
      (openingBalanceDonation._sum.amount || 0) -
      (openingBalanceExpense._sum.amount || 0);

    // الحصول على الإيرادات والمصروفات خلال الفترة
    const incomeCondition = type === 'expense' ? false : true;
    const expenseCondition = type === 'income' ? false : true;

    let incomes: Income[] = [];
    let expenses: ExpenseWithCategory[] = [];
    let donations: Donation[] = [];

    if (incomeCondition) {
      incomes = await prisma.income.findMany({
        where: {
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          date: 'asc',
        },
      });

      donations = await prisma.donation.findMany({
        where: {
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          date: 'asc',
        },
      });
    }

    if (expenseCondition) {
      expenses = await prisma.expense.findMany({
        where: {
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        include: {
          category: true,
        },
        orderBy: {
          date: 'asc',
        },
      });
    }

    // حساب إجمالي الإيرادات والمصروفات
    const totalIncome =
      incomes.reduce((sum, income) => sum + income.amount, 0) +
      donations.reduce((sum, donation) => sum + donation.amount, 0);
    const totalExpense = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const netCashFlow = totalIncome - totalExpense;
    const closingBalance = openingBalance + netCashFlow;

    // تجميع البيانات حسب الفترة المحددة
    const periods: Period[] = [];
    let currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      let periodStart, periodEnd, periodLabel;

      switch (periodType) {
        case 'daily':
          periodStart = new Date(currentDate);
          periodEnd = new Date(currentDate);
          periodLabel = format(currentDate, 'yyyy-MM-dd');
          currentDate = new Date(currentDate.setDate(currentDate.getDate() + 1));
          break;
        case 'weekly':
          periodStart = startOfWeek(currentDate, { locale: ar });
          periodEnd = endOfWeek(currentDate, { locale: ar });
          periodLabel = `${format(periodStart, 'yyyy-MM-dd')} - ${format(periodEnd, 'yyyy-MM-dd')}`;
          currentDate = addWeeks(currentDate, 1);
          break;
        case 'quarterly':
          periodStart = startOfQuarter(currentDate);
          periodEnd = endOfQuarter(currentDate);
          periodLabel = `Q${Math.floor(currentDate.getMonth() / 3) + 1} ${currentDate.getFullYear()}`;
          currentDate = addQuarters(currentDate, 1);
          break;
        case 'monthly':
        default:
          periodStart = startOfMonth(currentDate);
          periodEnd = endOfMonth(currentDate);
          periodLabel = format(currentDate, 'yyyy-MM');
          currentDate = addMonths(currentDate, 1);
          break;
      }

      // تجاوز الفترات التي تتجاوز تاريخ النهاية
      if (periodStart > endDate) {
        break;
      }

      // تعديل الفترة الأولى والأخيرة لتتناسب مع نطاق التقرير
      if (periodStart < startDate) {
        periodStart = new Date(startDate);
      }
      if (periodEnd > endDate) {
        periodEnd = new Date(endDate);
      }

      periods.push({
        start: periodStart,
        end: periodEnd,
        label: periodLabel,
      });
    }

    // حساب التدفق النقدي لكل فترة
    let runningBalance = openingBalance;
    const periodicCashFlow: PeriodicCashFlow[] = periods.map(period => {
      const periodIncomes = incomes.filter(
        income => income.date >= period.start && income.date <= period.end
      );
      const periodDonations = donations.filter(
        donation => donation.date >= period.start && donation.date <= period.end
      );
      const periodExpenses = expenses.filter(
        expense => expense.date >= period.start && expense.date <= period.end
      );

      const periodIncomeTotal =
        periodIncomes.reduce((sum, income) => sum + income.amount, 0) +
        periodDonations.reduce((sum, donation) => sum + donation.amount, 0);
      const periodExpenseTotal = periodExpenses.reduce((sum, expense) => sum + expense.amount, 0);
      const periodNetCashFlow = periodIncomeTotal - periodExpenseTotal;

      runningBalance += periodNetCashFlow;

      return {
        month: period.label,
        income: periodIncomeTotal,
        expense: periodExpenseTotal,
        netCashFlow: periodNetCashFlow,
        runningBalance,
      };
    });

    // تجميع الإيرادات حسب المصدر
    const incomeBySource: IncomeSource[] = [];

    // إيرادات من جدول Income
    const incomeSourcesData = await prisma.income.groupBy({
      by: ['source'],
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      _sum: {
        amount: true,
      },
    });

    incomeSourcesData.forEach(source => {
      incomeBySource.push({
        source: source.source,
        amount: source._sum.amount || 0,
        percentage: totalIncome > 0 ? ((source._sum.amount || 0) / totalIncome) * 100 : 0,
      });
    });

    // إضافة التبرعات كمصدر للإيرادات
    const donationsTotal = donations.reduce((sum, donation) => sum + donation.amount, 0);
    if (donationsTotal > 0) {
      incomeBySource.push({
        source: 'التبرعات',
        amount: donationsTotal,
        percentage: totalIncome > 0 ? (donationsTotal / totalIncome) * 100 : 0,
      });
    }

    // تجميع المصروفات حسب الفئة
    const expenseByCategory: ExpenseCategoryData[] = [];

    // المصروفات بدون فئة
    const uncategorizedExpenses = expenses.filter(expense => expense.categoryId === null);
    const uncategorizedTotal = uncategorizedExpenses.reduce((sum, expense) => sum + expense.amount, 0);

    if (uncategorizedTotal > 0) {
      expenseByCategory.push({
        categoryId: null,
        categoryName: 'بدون فئة',
        amount: uncategorizedTotal,
        percentage: totalExpense > 0 ? (uncategorizedTotal / totalExpense) * 100 : 0,
      });
    }

    // المصروفات حسب الفئة
    const expenseCategoriesData = await prisma.expense.groupBy({
      by: ['categoryId'],
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
        categoryId: {
          not: null,
        },
      },
      _sum: {
        amount: true,
      },
    });

    // الحصول على أسماء الفئات
    const categoryIds = expenseCategoriesData
      .map(cat => cat.categoryId)
      .filter(id => id !== null) as number[];

    const categories = await prisma.expenseCategory.findMany({
      where: {
        id: {
          in: categoryIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    expenseCategoriesData.forEach(category => {
      const categoryInfo = categories.find(cat => cat.id === category.categoryId);
      expenseByCategory.push({
        categoryId: category.categoryId,
        categoryName: categoryInfo?.name || 'غير معروف',
        amount: category._sum.amount || 0,
        percentage: totalExpense > 0 ? ((category._sum.amount || 0) / totalExpense) * 100 : 0,
      });
    });

    // تجميع المعاملات (الإيرادات والمصروفات)
    const transactions: Transaction[] = [];

    // إضافة الإيرادات
    incomes.forEach(income => {
      transactions.push({
        id: income.id,
        date: income.date.toISOString(),
        type: 'income',
        description: income.source,
        amount: income.amount,
        source: income.source,
      });
    });

    // إضافة التبرعات
    donations.forEach(donation => {
      transactions.push({
        id: donation.id,
        date: donation.date.toISOString(),
        type: 'income',
        description: 'تبرع',
        amount: donation.amount,
        source: 'التبرعات',
      });
    });

    // إضافة المصروفات
    expenses.forEach(expense => {
      transactions.push({
        id: expense.id,
        date: expense.date.toISOString(),
        type: 'expense',
        description: expense.purpose,
        amount: expense.amount,
        category: expense.category?.name || 'بدون فئة',
      });
    });

    // ترتيب المعاملات حسب التاريخ
    transactions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return NextResponse.json({
      period: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      },
      summary: {
        openingBalance,
        totalIncome,
        totalExpense,
        netCashFlow,
        closingBalance,
      },
      monthly: periodicCashFlow,
      incomeBySource,
      expenseByCategory,
      transactions,
    });
  } catch (error) {
    console.error('خطأ في جلب تقرير التدفق النقدي:', error);
    return NextResponse.json(
      { error: 'فشل في جلب تقرير التدفق النقدي' },
      { status: 500 }
    );
  }
}

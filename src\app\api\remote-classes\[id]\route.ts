import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";

// GET: جلب فصل افتراضي محدد
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const id = parseInt(params.id);
        if (isNaN(id)) {
            return NextResponse.json(
                { message: "معرف الفصل غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // جلب الفصل الافتراضي
        const remoteClass = await prisma.remoteClass.findUnique({
            where: { id },
            include: {
                instructor: {
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                },
                classe: {
                    select: {
                        id: true,
                        name: true,
                        students: true
                    }
                },
                materials: true,
                attendees: {
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                }
            }
        });

        if (!remoteClass) {
            return NextResponse.json(
                { message: "الفصل الافتراضي غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من الصلاحيات (المسؤول يرى كل شيء)
        if (userData.role !== 'ADMIN') {
            // المعلم يرى الفصول التي يدرسها
            if (userData.role === 'TEACHER' && remoteClass.instructorId !== userData.id) {
                return NextResponse.json(
                    { message: "غير مصرح به" },
                    { status: 403 }
                );
            }

            // الطالب يرى الفصول التي ينتمي إليها
            if (userData.role === 'STUDENT') {
                // التحقق من قائمة الحضور
                const isAttendee = remoteClass.attendees.some((a: { id: number }) => a.id === userData.id);

                // التحقق من طلاب الفصل
                let isClassStudent = false;
                if (remoteClass.classe) {
                    // جلب الطالب المرتبط بالمستخدم الحالي
                    const student = await prisma.student.findFirst({
                        where: {
                            username: userData.username,
                            classeId: remoteClass.classe.id
                        }
                    });

                    isClassStudent = !!student;
                }

                // السماح بالوصول إذا كان الطالب في قائمة الحضور أو في الفصل
                if (!isAttendee && !isClassStudent) {
                    return NextResponse.json(
                        { message: "غير مصرح به" },
                        { status: 403 }
                    );
                }
            }
        }

        return NextResponse.json(remoteClass);
    } catch (error) {
        console.error('Error fetching remote class:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب الفصل الافتراضي" },
            { status: 500 }
        );
    }
}

// PATCH: تحديث فصل افتراضي
export async function PATCH(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const id = parseInt(params.id);
        if (isNaN(id)) {
            return NextResponse.json(
                { message: "معرف الفصل غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
                { status: 401 }
            );
        }

        // جلب الفصل للتحقق من الملكية
        const remoteClass = await prisma.remoteClass.findUnique({
            where: { id }
        });

        if (!remoteClass) {
            return NextResponse.json(
                { message: "الفصل الافتراضي غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من أن المستخدم هو المعلم المسؤول عن الفصل أو مسؤول
        if (userData.role === 'TEACHER' && remoteClass.instructorId !== userData.id) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون المعلم المسؤول عن هذا الفصل" },
                { status: 403 }
            );
        }

        const body = await request.json();

        // التحقق من البيانات
        if (body.startTime && body.endTime) {
            const startTime = new Date(body.startTime);
            const endTime = new Date(body.endTime);

            if (startTime >= endTime) {
                return NextResponse.json(
                    { message: "يجب أن يكون وقت البداية قبل وقت النهاية" },
                    { status: 400 }
                );
            }
        } else if (body.startTime && !body.endTime) {
            const startTime = new Date(body.startTime);
            const endTime = remoteClass.endTime;

            if (startTime >= endTime) {
                return NextResponse.json(
                    { message: "يجب أن يكون وقت البداية قبل وقت النهاية" },
                    { status: 400 }
                );
            }
        } else if (!body.startTime && body.endTime) {
            const startTime = remoteClass.startTime;
            const endTime = new Date(body.endTime);

            if (startTime >= endTime) {
                return NextResponse.json(
                    { message: "يجب أن يكون وقت البداية قبل وقت النهاية" },
                    { status: 400 }
                );
            }
        }

        // تحديث الفصل الافتراضي
        const updatedRemoteClass = await prisma.remoteClass.update({
            where: { id },
            data: {
                title: body.title,
                description: body.description,
                startTime: body.startTime ? new Date(body.startTime) : undefined,
                endTime: body.endTime ? new Date(body.endTime) : undefined,
                meetingLink: body.meetingLink,
                meetingId: body.meetingId,
                meetingPassword: body.meetingPassword,
                platform: body.platform,
                classeId: body.classeId,
                recordingUrl: body.recordingUrl,
                // الحقول الجديدة
                isScreenShareEnabled: body.isScreenShareEnabled !== undefined ? body.isScreenShareEnabled : undefined,
                isWhiteboardEnabled: body.isWhiteboardEnabled !== undefined ? body.isWhiteboardEnabled : undefined,
                videoQuality: body.videoQuality,
                audioQuality: body.audioQuality,
                ...(body.attendeeIds ? {
                    attendees: {
                        set: [],
                        connect: body.attendeeIds.map((id: number) => ({ id }))
                    }
                } : {})
            },
            include: {
                instructor: {
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                },
                classe: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        // إنشاء إشعار للمعلم إذا تم تغيير موعد الفصل
        if ((body.startTime || body.endTime) && body.sendNotification && userData.id) {
            await prisma.notification.create({
                data: {
                    title: `تم تحديث موعد الفصل: ${updatedRemoteClass.title}`,
                    content: `تم تحديث موعد الفصل "${updatedRemoteClass.title}" ليصبح من ${updatedRemoteClass.startTime.toLocaleString('ar-EG')} إلى ${updatedRemoteClass.endTime.toLocaleString('ar-EG')}`,
                    type: 'REMOTE_CLASS',
                    userId: userData.id,
                    relatedId: updatedRemoteClass.id,
                    link: `/remote-classes/${updatedRemoteClass.id}`
                }
            });
        }

        return NextResponse.json(updatedRemoteClass);
    } catch (error) {
        console.error('Error updating remote class:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء تحديث الفصل الافتراضي" },
            { status: 500 }
        );
    }
}

// DELETE: حذف فصل افتراضي
export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const id = parseInt(params.id);
        if (isNaN(id)) {
            return NextResponse.json(
                { message: "معرف الفصل غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
                { status: 401 }
            );
        }

        // جلب الفصل للتحقق من الملكية
        const remoteClass = await prisma.remoteClass.findUnique({
            where: { id },
            include: {
                classe: true
            }
        });

        if (!remoteClass) {
            return NextResponse.json(
                { message: "الفصل الافتراضي غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من أن المستخدم هو المعلم المسؤول عن الفصل أو مسؤول
        if (userData.role === 'TEACHER' && remoteClass.instructorId !== userData.id) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون المعلم المسؤول عن هذا الفصل" },
                { status: 403 }
            );
        }

        // إنشاء إشعار للمعلم بإلغاء الفصل
        if (userData.id) {
            await prisma.notification.create({
                data: {
                    title: `تم إلغاء الفصل: ${remoteClass.title}`,
                    content: `تم إلغاء الفصل "${remoteClass.title}" الذي كان مقرراً في ${remoteClass.startTime.toLocaleString('ar-EG')}`,
                    type: 'REMOTE_CLASS',
                    userId: userData.id,
                    relatedId: null
                }
            });
        }

        // حذف الفصل الافتراضي
        await prisma.remoteClass.delete({
            where: { id }
        });

        return NextResponse.json({ message: "تم حذف الفصل الافتراضي بنجاح" });
    } catch (error) {
        console.error('Error deleting remote class:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء حذف الفصل الافتراضي" },
            { status: 500 }
        );
    }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/student-images - الحصول على صور الطلاب
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const studentId = searchParams.get('studentId');
    const albumId = searchParams.get('albumId');
    const isProfilePic = searchParams.get('isProfilePic');
    const search = searchParams.get('search');

    // بناء شروط البحث
    const where: Record<string, unknown> = {};

    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    if (albumId) {
      where.albumId = parseInt(albumId);
    } else if (albumId === 'null') {
      // للبحث عن الصور التي ليس لها ألبوم
      where.albumId = null;
    }

    if (isProfilePic !== null) {
      where.isProfilePic = isProfilePic === 'true';
    }

    if (search) {
      where.description = {
        contains: search,
        mode: 'insensitive'
      };
    }

    const images = await prisma.studentImage.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true
          }
        },
        album: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        uploadDate: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: images,
      message: 'تم جلب صور الطالب بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student images:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب صور الطالب'
    }, { status: 500 });
  }
}

// POST /api/student-images - إضافة صورة جديدة للطالب
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, imageUrl, description, isProfilePic, albumId } = body;

    // التحقق من البيانات المطلوبة
    if (!studentId || !imageUrl) {
      return NextResponse.json({
        success: false,
        error: 'معرف الطالب ورابط الصورة مطلوبان'
      }, { status: 400 });
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return NextResponse.json({
        success: false,
        error: 'الطالب غير موجود'
      }, { status: 404 });
    }

    // التحقق من وجود الألبوم إذا تم تحديده
    if (albumId) {
      const album = await prisma.studentAlbum.findUnique({
        where: { id: parseInt(albumId) }
      });

      if (!album) {
        return NextResponse.json({
          success: false,
          error: 'الألبوم غير موجود'
        }, { status: 404 });
      }
    }

    // إذا كانت الصورة الجديدة هي صورة الملف الشخصي، نقوم بإلغاء تعيين الصورة السابقة كصورة ملف شخصي
    if (isProfilePic) {
      await prisma.studentImage.updateMany({
        where: {
          studentId: parseInt(studentId),
          isProfilePic: true
        },
        data: {
          isProfilePic: false
        }
      });
    }

    // إنشاء الصورة الجديدة
    const image = await prisma.studentImage.create({
      data: {
        studentId: parseInt(studentId),
        imageUrl,
        description,
        isProfilePic: isProfilePic || false,
        albumId: albumId ? parseInt(albumId) : null
      }
    });

    // إذا كانت هذه أول صورة في الألبوم، نقوم بتعيينها كصورة الغلاف
    if (albumId) {
      const albumImagesCount = await prisma.studentImage.count({
        where: { albumId: parseInt(albumId) }
      });

      if (albumImagesCount === 1) {
        await prisma.studentAlbum.update({
          where: { id: parseInt(albumId) },
          data: { coverImage: imageUrl }
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: image,
      message: 'تم إضافة الصورة بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating student image:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء إضافة الصورة'
    }, { status: 500 });
  }
}

// PUT /api/student-images - تحديث معلومات الصورة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, description, isProfilePic, albumId, imageUrl } = body;

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الصورة مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود الصورة
    const existingImage = await prisma.studentImage.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingImage) {
      return NextResponse.json({
        success: false,
        error: 'الصورة غير موجودة'
      }, { status: 404 });
    }

    // التحقق من وجود الألبوم إذا تم تحديده
    if (albumId !== undefined && albumId !== null) {
      const album = await prisma.studentAlbum.findUnique({
        where: { id: parseInt(albumId) }
      });

      if (!album) {
        return NextResponse.json({
          success: false,
          error: 'الألبوم غير موجود'
        }, { status: 404 });
      }
    }

    // إذا كانت الصورة المحدثة هي صورة الملف الشخصي، نقوم بإلغاء تعيين الصور الأخرى كصور ملف شخصي
    if (isProfilePic) {
      await prisma.studentImage.updateMany({
        where: {
          studentId: existingImage.studentId,
          isProfilePic: true,
          id: { not: parseInt(id) }
        },
        data: {
          isProfilePic: false
        }
      });
    }

    // تحديث الصورة
    const updatedImage = await prisma.studentImage.update({
      where: { id: parseInt(id) },
      data: {
        description: description !== undefined ? description : undefined,
        isProfilePic: isProfilePic !== undefined ? isProfilePic : undefined,
        albumId: albumId !== undefined ? (albumId === null ? null : parseInt(albumId)) : undefined,
        imageUrl: imageUrl !== undefined ? imageUrl : undefined
      }
    });

    // إذا تم تغيير الألبوم وكانت هذه أول صورة في الألبوم الجديد، نقوم بتعيينها كصورة الغلاف
    if (albumId !== undefined && albumId !== null && albumId !== existingImage.albumId) {
      const albumImagesCount = await prisma.studentImage.count({
        where: { albumId: parseInt(albumId) }
      });

      if (albumImagesCount === 1) {
        await prisma.studentAlbum.update({
          where: { id: parseInt(albumId) },
          data: { coverImage: updatedImage.imageUrl }
        });
      }
    }

    // إذا كانت الصورة هي صورة الغلاف للألبوم وتم تغيير رابط الصورة، نقوم بتحديث صورة الغلاف
    if (imageUrl && existingImage.albumId) {
      const album = await prisma.studentAlbum.findUnique({
        where: { id: existingImage.albumId }
      });

      if (album && album.coverImage === existingImage.imageUrl) {
        await prisma.studentAlbum.update({
          where: { id: existingImage.albumId },
          data: { coverImage: imageUrl }
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedImage,
      message: 'تم تحديث الصورة بنجاح'
    });
  } catch (error) {
    console.error('Error updating student image:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تحديث الصورة'
    }, { status: 500 });
  }
}

// DELETE /api/student-images - حذف صورة
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الصورة مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود الصورة
    const existingImage = await prisma.studentImage.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingImage) {
      return NextResponse.json({
        success: false,
        error: 'الصورة غير موجودة'
      }, { status: 404 });
    }

    // حذف الصورة
    await prisma.studentImage.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف الصورة بنجاح'
    });
  } catch (error) {
    console.error('Error deleting student image:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف الصورة'
    }, { status: 500 });
  }
}

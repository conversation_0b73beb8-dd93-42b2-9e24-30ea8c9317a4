import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// PUT /api/budgets/:id/items/:itemId - تحديث بند ميزانية
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string; itemId: string } }
) {
  try {
    const budgetId = parseInt(params.id);
    const itemId = parseInt(params.itemId);
    const body = await req.json();
    const { amount, notes } = body;

    if (!amount) {
      return NextResponse.json(
        { error: 'المبلغ مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من صحة المبلغ
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'المبلغ يجب أن يكون رقمًا موجبًا' },
        { status: 400 }
      );
    }

    // التحقق من وجود بند الميزانية
    const budgetItem = await prisma.budgetItem.findFirst({
      where: {
        id: itemId,
        budgetId
      }
    });

    if (!budgetItem) {
      return NextResponse.json(
        { error: 'بند الميزانية غير موجود' },
        { status: 404 }
      );
    }

    // تحديث بند الميزانية
    const updatedItem = await prisma.budgetItem.update({
      where: { id: itemId },
      data: {
        amount,
        notes
      },
      include: {
        category: true
      }
    });

    // حساب المصروفات الفعلية لهذا البند
    const budget = await prisma.budget.findUnique({
      where: { id: budgetId }
    });

    if (!budget) {
      return NextResponse.json(
        { error: 'الميزانية غير موجودة' },
        { status: 404 }
      );
    }

    const expenseData = await prisma.expense.aggregate({
      where: {
        categoryId: updatedItem.categoryId,
        date: {
          gte: budget.startDate,
          lte: budget.endDate
        }
      },
      _sum: {
        amount: true
      }
    });

    const actualAmount = expenseData._sum.amount || 0;
    const remainingAmount = updatedItem.amount - actualAmount;

    return NextResponse.json({
      ...updatedItem,
      actualAmount,
      remainingAmount
    });
  } catch (error) {
    console.error('خطأ في تحديث بند الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث بند الميزانية' },
      { status: 500 }
    );
  }
}

// DELETE /api/budgets/:id/items/:itemId - حذف بند ميزانية
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; itemId: string } }
) {
  try {
    const budgetId = parseInt(params.id);
    const itemId = parseInt(params.itemId);

    // التحقق من وجود بند الميزانية
    const budgetItem = await prisma.budgetItem.findFirst({
      where: {
        id: itemId,
        budgetId
      }
    });

    if (!budgetItem) {
      return NextResponse.json(
        { error: 'بند الميزانية غير موجود' },
        { status: 404 }
      );
    }

    // حذف بند الميزانية
    await prisma.budgetItem.delete({
      where: { id: itemId }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('خطأ في حذف بند الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في حذف بند الميزانية' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/student-points
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // بناء شروط البحث
    const where: {
      studentId?: number;
      date?: {
        gte?: Date;
        lte?: Date;
      };
    } = {};

    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    } else if (startDate) {
      where.date = {
        gte: new Date(startDate)
      };
    } else if (endDate) {
      where.date = {
        lte: new Date(endDate)
      };
    }

    const studentPoints = await prisma.studentPoint.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true,
            username: true,
            classe: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    return NextResponse.json({
      data: studentPoints,
      success: true,
      message: 'تم جلب نقاط الطلاب بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student points:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب نقاط الطلاب',
      success: false
    }, { status: 500 });
  }
}

// POST /api/student-points
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, points, reason } = body;

    // التحقق من البيانات المطلوبة
    if (!studentId || points === undefined) {
      return NextResponse.json({
        error: 'معرف الطالب والنقاط مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة النقاط
    const numericPoints = parseInt(points);
    if (isNaN(numericPoints)) {
      return NextResponse.json({
        error: 'يجب أن تكون النقاط رقمًا صحيحًا',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return NextResponse.json({
        error: 'الطالب غير موجود',
        success: false
      }, { status: 404 });
    }

    // إنشاء نقاط الطالب
    const studentPoint = await prisma.studentPoint.create({
      data: {
        studentId: parseInt(studentId),
        points: numericPoints,
        reason
      }
    });

    // تحديث إجمالي نقاط الطالب
    await prisma.student.update({
      where: { id: parseInt(studentId) },
      data: {
        totalPoints: {
          increment: numericPoints
        }
      }
    });

    // التحقق من استحقاق الطالب لأي مكافآت
    const student_with_points = await prisma.student.findUnique({
      where: { id: parseInt(studentId) },
      select: {
        id: true,
        name: true,
        totalPoints: true
      }
    });

    // جلب المكافآت التي يستحقها الطالب
    const eligibleRewards = await prisma.reward.findMany({
      where: {
        requiredPoints: {
          lte: student_with_points?.totalPoints || 0
        },
        // استبعاد المكافآت التي حصل عليها الطالب بالفعل
        NOT: {
          studentRewards: {
            some: {
              studentId: parseInt(studentId)
            }
          }
        }
      }
    });

    // منح المكافآت المستحقة للطالب
    const grantedRewards = [];
    for (const reward of eligibleRewards) {
      await prisma.studentReward.create({
        data: {
          studentId: parseInt(studentId),
          rewardId: reward.id
        }
      });
      grantedRewards.push(reward);
    }

    return NextResponse.json({
      data: {
        studentPoint,
        grantedRewards
      },
      success: true,
      message: `تم إضافة ${numericPoints} نقطة للطالب بنجاح${grantedRewards.length > 0 ? ` وتم منح ${grantedRewards.length} مكافأة` : ''}`
    });
  } catch (error) {
    console.error('Error creating student points:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إضافة نقاط للطالب',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/student-points
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف نقاط الطالب مطلوب',
        success: false
      }, { status: 400 });
    }

    // جلب نقاط الطالب قبل الحذف
    const studentPoint = await prisma.studentPoint.findUnique({
      where: { id: parseInt(id) }
    });

    if (!studentPoint) {
      return NextResponse.json({
        error: 'نقاط الطالب غير موجودة',
        success: false
      }, { status: 404 });
    }

    // حذف نقاط الطالب
    await prisma.studentPoint.delete({
      where: { id: parseInt(id) }
    });

    // تحديث إجمالي نقاط الطالب
    await prisma.student.update({
      where: { id: studentPoint.studentId },
      data: {
        totalPoints: {
          decrement: studentPoint.points
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف نقاط الطالب بنجاح'
    });
  } catch (error) {
    console.error('Error deleting student points:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف نقاط الطالب',
      success: false
    }, { status: 500 });
  }
}

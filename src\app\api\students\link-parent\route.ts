import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";
import { UserRole } from "@prisma/client";

// POST: ربط طالب بولي
export async function POST(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.TEACHER)) {
      return NextResponse.json(
        { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // التحقق من البيانات المطلوبة
    if (!body.studentId || !body.parentId) {
      return NextResponse.json(
        { message: "معرف الطالب ومعرف الولي مطلوبان" },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(body.studentId) }
    });

    if (!student) {
      return NextResponse.json(
        { message: "الطالب غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود الولي
    const parent = await prisma.parent.findUnique({
      where: { id: parseInt(body.parentId) }
    });

    if (!parent) {
      return NextResponse.json(
        { message: "الولي غير موجود" },
        { status: 404 }
      );
    }

    // ربط الطالب بالولي
    const updatedStudent = await prisma.student.update({
      where: { id: parseInt(body.studentId) },
      data: {
        guardianId: parseInt(body.parentId)
      }
    });

    // تسجيل النشاط
    await prisma.activity.create({
      data: {
        userId: userData.id,
        type: 'LINK_STUDENT',
        description: `تم ربط الطالب ${student.name} بالولي ${parent.name}`
      }
    });

    return NextResponse.json({
      message: "تم ربط الطالب بالولي بنجاح",
      data: updatedStudent
    });
  } catch (error) {
    console.error('Error linking student to parent:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء ربط الطالب بالولي" },
      { status: 500 }
    );
  }
}

# نظام التقارير الموحدة للمشرفين

## 📋 نظرة عامة

نظام شامل لإدارة وإنشاء التقارير الموحدة التي تجمع بين التقرير الأدبي والمالي في وثيقة واحدة. يوفر النظام واجهات سهلة الاستخدام مع إمكانيات متقدمة للتحرير والتصدير والطباعة.

## ✨ المميزات الرئيسية

### 🎯 الوظائف الأساسية
- **إنشاء تقارير موحدة** جديدة تجمع بين الأدبي والمالي
- **عرض التقارير المحفوظة** في جدول تفاعلي منظم
- **تعديل التقارير** الموجودة مع حفظ التغييرات
- **حذف التقارير** مع تأكيد الأمان
- **معاينة التقارير** قبل الحفظ أو الطباعة

### 📝 محرر التقرير الأدبي
- **محرر HTML متقدم** مع معاينة فورية
- **إدراج تلقائي للبيانات** من قاعدة البيانات
- **قوالب جاهزة** للمقدمة والإنجازات والتوصيات
- **بيانات ديناميكية** للإحصائيات والأنشطة
- **تنسيق متقدم** مع دعم HTML والأنماط

### 💰 الجدول المالي الديناميكي
- **جدول تفاعلي** مطابق لتنسيق Excel
- **أبواب منفصلة** للمداخيل والمصاريف
- **حساب تلقائي** للمجاميع والرصيد النهائي
- **استيراد وتصدير** البيانات من/إلى CSV
- **نسخ وتكرار** الصفوف بسهولة
- **تحميل البيانات التلقائية** من النظام

### 🔍 التحقق من صحة البيانات
- **تحقق تلقائي** من صحة جميع البيانات
- **رسائل خطأ وتحذير** واضحة ومفيدة
- **فحص HTML** للمحتوى الأدبي
- **التحقق من التوازن المالي**
- **فحص التواريخ والفترات الزمنية**

### 🖨️ الطباعة والتصدير
- **معاينة شاملة** للتقرير النهائي
- **طباعة محسنة** للورق
- **تصدير HTML** مع تنسيق كامل
- **تصدير PDF** (قابل للتطوير)
- **تنسيق رسمي** مطابق للمعايير

## 🏗️ البنية التقنية

### 📁 هيكل الملفات

```
src/app/admin/supervisor-reports/
├── page.tsx                    # الصفحة الرئيسية وعرض التقارير
├── create/
│   └── page.tsx               # صفحة إنشاء تقرير جديد
├── edit/[id]/
│   └── page.tsx               # صفحة تعديل تقرير موجود
└── view/[id]/
    └── page.tsx               # صفحة عرض التقرير

src/components/supervisor-reports/
├── LiteraryReportEditor.tsx   # محرر التقرير الأدبي
├── FinancialTableEditor.tsx   # محرر الجدول المالي
├── ReportExporter.tsx         # مكون الطباعة والتصدير
└── ValidationStatus.tsx       # مكون التحقق من البيانات

src/app/api/supervisor-reports/
├── route.ts                   # API للعمليات الأساسية
├── [id]/
│   └── route.ts              # API للعمليات على تقرير محدد
└── [id]/export/
    └── route.ts              # API لتصدير التقارير

src/utils/
└── reportValidation.ts        # دوال التحقق من صحة البيانات

src/__tests__/
└── supervisor-reports.test.ts # اختبارات النظام
```

### 🗄️ قاعدة البيانات

```sql
-- جدول التقارير الموحدة
CREATE TABLE SupervisorReport (
    id                INT PRIMARY KEY AUTO_INCREMENT,
    title             VARCHAR(255) NOT NULL,
    description       TEXT,
    periodStart       DATETIME NOT NULL,
    periodEnd         DATETIME NOT NULL,
    literaryContent   LONGTEXT,
    financialData     LONGTEXT,
    officeSettings    TEXT,
    status            ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'DRAFT',
    createdBy         INT,
    createdAt         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt         TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (createdBy) REFERENCES User(id) ON DELETE SET NULL,
    INDEX idx_period (periodStart, periodEnd),
    INDEX idx_status (status),
    INDEX idx_created (createdAt)
);
```

### 🔌 API Endpoints

#### التقارير الأساسية
- `GET /api/supervisor-reports` - جلب قائمة التقارير
- `POST /api/supervisor-reports` - إنشاء تقرير جديد
- `GET /api/supervisor-reports/[id]` - جلب تقرير محدد
- `PUT /api/supervisor-reports/[id]` - تحديث تقرير
- `DELETE /api/supervisor-reports/[id]` - حذف تقرير

#### التصدير والطباعة
- `POST /api/supervisor-reports/[id]/export` - تصدير تقرير

## 🚀 كيفية الاستخدام

### 1. إنشاء تقرير جديد

1. انتقل إلى صفحة التقارير الموحدة
2. اضغط على "إضافة تقرير جديد"
3. أدخل المعلومات الأساسية (العنوان، الفترة الزمنية)
4. استخدم محرر التقرير الأدبي لإضافة المحتوى
5. أضف البيانات المالية في الجدول التفاعلي
6. راجع حالة التحقق من البيانات
7. استخدم المعاينة للتأكد من التنسيق
8. احفظ التقرير كمسودة أو انشره مباشرة

### 2. تعديل تقرير موجود

1. من قائمة التقارير، اضغط على أيقونة "تعديل"
2. قم بالتعديلات المطلوبة
3. احفظ التغييرات

### 3. طباعة وتصدير

1. افتح التقرير للعرض أو التعديل
2. استخدم مكون "الطباعة والتصدير"
3. اختر الصيغة المطلوبة (معاينة، طباعة، HTML، PDF)

## ⚙️ الإعدادات والتخصيص

### إعدادات المكتب
يمكن تخصيص المعلومات التالية:
- اسم الجمعية
- اسم المكتب البلدي
- اسم الرئيس ومنصبه
- الشعار والعنوان
- معلومات الاتصال

### قوالب التقرير الأدبي
القوالب المتاحة:
- **المقدمة**: نص افتتاحي رسمي
- **الإنجازات**: قائمة بالإنجازات المحققة
- **التحديات**: الصعوبات والتحديات
- **التوصيات**: المقترحات والتوصيات

### البيانات الديناميكية
- إحصائيات الطلاب والمعلمين
- تقدم حفظ القرآن الكريم
- الأنشطة والفعاليات
- الدورات التدريبية

## 🔒 الأمان والصلاحيات

### مستويات الوصول
- **المسؤولون**: إنشاء وتعديل وحذف جميع التقارير
- **الموظفون**: إنشاء وتعديل التقارير الخاصة بهم
- **المشاهدون**: عرض التقارير المنشورة فقط

### التحقق من الصحة
- التحقق من صحة البيانات المدخلة
- منع الحقن والهجمات الأمنية
- تشفير البيانات الحساسة
- تسجيل العمليات للمراجعة

## 🧪 الاختبارات

### أنواع الاختبارات
- **اختبارات الوحدة**: للدوال والمكونات الفردية
- **اختبارات التكامل**: للتفاعل بين المكونات
- **اختبارات واجهة المستخدم**: للتأكد من سلامة الواجهة

### تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات التقارير فقط
npm test supervisor-reports

# تشغيل الاختبارات مع التغطية
npm test -- --coverage
```

## 🔧 التطوير والصيانة

### إضافة ميزات جديدة
1. إنشاء المكونات في `src/components/supervisor-reports/`
2. إضافة API endpoints في `src/app/api/supervisor-reports/`
3. كتابة الاختبارات في `src/__tests__/`
4. تحديث الوثائق

### التحسينات المقترحة
- **تصدير PDF متقدم** باستخدام مكتبات متخصصة
- **قوالب تقارير متعددة** للمناسبات المختلفة
- **تكامل مع أنظمة خارجية** للبيانات
- **إشعارات تلقائية** عند إنشاء التقارير
- **نسخ احتياطية تلقائية** للتقارير المهمة

## 📞 الدعم والمساعدة

### المشاكل الشائعة
1. **بطء في التحميل**: تحقق من اتصال الإنترنت وحجم البيانات
2. **أخطاء في الحفظ**: راجع رسائل التحقق من البيانات
3. **مشاكل في الطباعة**: تأكد من إعدادات المتصفح

### الحصول على المساعدة
- راجع رسائل الخطأ والتحذيرات في النظام
- تحقق من سجلات النظام للأخطاء التقنية
- تواصل مع فريق الدعم التقني

---

**تم تطوير هذا النظام بواسطة**: فريق تطوير نظام إدارة المدارس القرآنية  
**آخر تحديث**: يناير 2024  
**الإصدار**: 1.0.0

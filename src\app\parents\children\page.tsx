"use client";
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  FaUserGraduate,
  FaCalendarAlt,
  FaBook,
  FaTrophy,
  FaChartLine,
  FaArrowRight
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface QuranProgress {
  surahName: string;
  startVerse: number;
  endVerse: number;
  date: string;
}

interface Exam {
  id: number;
  examType: string;
  surahName: string;
  points: number;
  maxPoints: number;
  date: string;
}

interface Child {
  id: number;
  name: string;
  age: number;
  grade: string;
  attendanceRate: number;
  averagePoints: number;
  totalPoints: number;
  nextExam: {
    title: string;
    date: string;
  };
  lastQuranProgress: QuranProgress | null;
  recentExams: Exam[];
}

const ParentChildrenPage = () => {
  const [children, setChildren] = useState<Child[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchChildren = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/parent-children');

        if (!response.ok) {
          // محاولة قراءة رسالة الخطأ من الاستجابة
          const errorData = await response.json();
          const errorMessage = errorData?.message || 'فشل في جلب بيانات الأبناء';
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log('Children data received:', data);

        // التحقق من وجود بيانات الأبناء
        if (!data || !data.children) {
          console.warn('No children data in response:', data);
          setChildren([]);
        } else {
          setChildren(data.children);
        }
      } catch (err) {
        console.error('Error fetching children:', err);

        // محاولة الحصول على رسالة خطأ أكثر تفصيلاً من الخادم
        let errorMessage = 'حدث خطأ أثناء جلب بيانات الأبناء';

        if (err instanceof Error) {
          errorMessage = err.message;
        } else if (err instanceof Response) {
          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await err.json();
            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (jsonError) {
            console.error('Error parsing error response:', jsonError);
          }
        }

        setError(errorMessage);
        toast.error('فشل في جلب بيانات الأبناء: ' + errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchChildren();
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">أبنائي</h1>
          <p className="text-gray-500">عرض ومتابعة بيانات أبنائك</p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : !children || children.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-4">لا يوجد أبناء مسجلين</div>
            <div className="text-center mt-4">
              <p className="text-sm text-gray-500 mb-4">يمكنك التواصل مع إدارة المدرسة لتسجيل أبنائك</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-8">
          {children.map((child) => (
            <Card key={child.id} className="overflow-hidden">
              <CardHeader className="bg-[var(--primary-color)]/10">
                <CardTitle className="flex items-center gap-2">
                  <FaUserGraduate className="text-[var(--primary-color)]" />
                  <span>{child.name}</span>
                </CardTitle>
                <CardDescription className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1">
                  <span>العمر: {child.age} سنة</span>
                  <span className="hidden sm:inline">•</span>
                  <span>الصف: {child.grade}</span>
                </CardDescription>
              </CardHeader>

              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid grid-cols-3 p-0 mx-4 mt-4">
                  <TabsTrigger value="overview" className="flex items-center gap-1">
                    <FaChartLine className="hidden sm:inline" />
                    <span>نظرة عامة</span>
                  </TabsTrigger>
                  <TabsTrigger value="quran" className="flex items-center gap-1">
                    <FaBook className="hidden sm:inline" />
                    <span>حفظ القرآن</span>
                  </TabsTrigger>
                  <TabsTrigger value="exams" className="flex items-center gap-1">
                    <FaTrophy className="hidden sm:inline" />
                    <span>الامتحانات</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="overview">
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="p-2 bg-blue-100 rounded-full">
                            <FaCalendarAlt className="text-blue-600" />
                          </div>
                          <h3 className="font-medium">نسبة الحضور</h3>
                        </div>
                        <p className="text-2xl font-bold text-blue-600">{child.attendanceRate}%</p>
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="p-2 bg-green-100 rounded-full">
                            <FaChartLine className="text-primary-color" />
                          </div>
                          <h3 className="font-medium">متوسط الدرجات</h3>
                        </div>
                        <p className="text-2xl font-bold text-primary-color">{child.averagePoints}%</p>
                      </div>

                      <div className="bg-purple-50 p-4 rounded-lg">
                        <div className="flex items-center gap-3 mb-2">
                          <div className="p-2 bg-purple-100 rounded-full">
                            <FaTrophy className="text-purple-600" />
                          </div>
                          <h3 className="font-medium">مجموع النقاط</h3>
                        </div>
                        <p className="text-2xl font-bold text-purple-600">{child.totalPoints}</p>
                      </div>
                    </div>

                    <div className="border p-4 rounded-lg mb-4">
                      <h3 className="font-medium mb-2 flex items-center gap-2">
                        <FaCalendarAlt className="text-[var(--primary-color)]" />
                        <span>الامتحان القادم</span>
                      </h3>
                      <p className="text-gray-700">{child.nextExam.title}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(child.nextExam.date).toLocaleDateString('ar-EG')}
                      </p>
                    </div>

                    {child.lastQuranProgress && (
                      <div className="border p-4 rounded-lg">
                        <h3 className="font-medium mb-2 flex items-center gap-2">
                          <FaBook className="text-[var(--primary-color)]" />
                          <span>آخر تقدم في حفظ القرآن</span>
                        </h3>
                        <p className="text-gray-700">
                          سورة {child.lastQuranProgress.surahName} -
                          من الآية {child.lastQuranProgress.startVerse} إلى الآية {child.lastQuranProgress.endVerse}
                        </p>
                        <p className="text-sm text-gray-500">
                          {new Date(child.lastQuranProgress.date).toLocaleDateString('ar-EG')}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </TabsContent>

                <TabsContent value="quran">
                  <CardContent className="pt-6">
                    {child.lastQuranProgress ? (
                      <div className="space-y-4">
                        <div className="border p-4 rounded-lg">
                          <h3 className="font-medium mb-2 flex items-center gap-2">
                            <FaBook className="text-[var(--primary-color)]" />
                            <span>آخر تقدم في حفظ القرآن</span>
                          </h3>
                          <p className="text-gray-700">
                            سورة {child.lastQuranProgress.surahName} -
                            من الآية {child.lastQuranProgress.startVerse} إلى الآية {child.lastQuranProgress.endVerse}
                          </p>
                          <p className="text-sm text-gray-500">
                            {new Date(child.lastQuranProgress.date).toLocaleDateString('ar-EG')}
                          </p>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h3 className="font-medium mb-4">تقدم الحفظ الكلي</h3>
                          <div className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span>التقدم</span>
                              <span>30%</span> {/* قيمة افتراضية */}
                            </div>
                            <Progress value={30} className="h-2" /> {/* قيمة افتراضية */}
                          </div>
                        </div>

                        <Link href={`/parents/progress?childId=${child.id}`}>
                          <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                            <span>عرض تفاصيل تقدم الحفظ</span>
                            <FaArrowRight />
                          </Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-4">
                        لا يوجد سجل لتقدم الحفظ
                      </div>
                    )}
                  </CardContent>
                </TabsContent>

                <TabsContent value="exams">
                  <CardContent className="pt-6">
                    {child.recentExams && child.recentExams.length > 0 ? (
                      <div className="space-y-4">
                        <h3 className="font-medium">آخر الامتحانات</h3>
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse">
                            <thead>
                              <tr className="bg-gray-50">
                                <th className="border p-2 text-right">التاريخ</th>
                                <th className="border p-2 text-right">نوع الامتحان</th>
                                <th className="border p-2 text-right">السورة</th>
                                <th className="border p-2 text-right">الدرجة</th>
                              </tr>
                            </thead>
                            <tbody>
                              {child.recentExams.map((exam) => (
                                <tr key={exam.id}>
                                  <td className="border p-2">
                                    {new Date(exam.date).toLocaleDateString('ar-EG')}
                                  </td>
                                  <td className="border p-2">{exam.examType}</td>
                                  <td className="border p-2">{exam.surahName || '-'}</td>
                                  <td className="border p-2">
                                    {exam.points}/{exam.maxPoints} ({Math.round((exam.points / exam.maxPoints) * 100)}%)
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>

                        <Link href={`/parents/progress?childId=${child.id}`}>
                          <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                            <span>عرض جميع نتائج الامتحانات</span>
                            <FaArrowRight />
                          </Button>
                        </Link>
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 py-4">
                        لا توجد نتائج امتحانات
                      </div>
                    )}
                  </CardContent>
                </TabsContent>
              </Tabs>

              <CardFooter className="bg-gray-50 flex justify-end p-4">
                <Link href={`/parents/progress?childId=${child.id}`}>
                  <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                    عرض التفاصيل الكاملة
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ParentChildrenPage;

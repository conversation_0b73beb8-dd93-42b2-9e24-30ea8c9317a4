نظام المكافآت والطلاب المتفوقين
1. نظرة عامة
نظام المكافآت والطلاب المتفوقين (Honor Board) هو نظام متكامل يهدف إلى تحفيز الطلاب وتكريم المتفوقين منهم من خلال:

تتبع نقاط الطلاب وإنجازاتهم
منح المكافآت للطلاب بناءً على النقاط التي يحصلون عليها
إصدار شهادات تقدير للطلاب المتميزين
عرض قائمة الطلاب المتفوقين في لوحة الشرف
2. كيفية حصول الطلاب على النقاط
يمكن للطلاب الحصول على النقاط من خلال عدة طرق:

حفظ القرآن الكريم: يحصل الطالب على نقطة واحدة لكل آية يحفظها
الامتحانات والتقييمات: يحصل الطالب على نقاط بناءً على درجاته في الامتحانات
إضافة نقاط مباشرة: يمكن للمعلمين أو المشرفين إضافة نقاط للطلاب من خلال نظام StudentPoint
عندما يحصل الطالب على نقاط، يتم تحديث إجمالي النقاط في سجل الطالب (totalPoints) تلقائيًا.

3. نظام المكافآت
أنواع المكافآت
يدعم النظام عدة أنواع من المكافآت:

شهادات (CERTIFICATE): مثل شهادات التقدير والتفوق
جوائز (PRIZE): مكافآت مادية أو عينية
أوسمة (BADGE): أوسمة رمزية للإنجازات
أخرى (OTHER): أي نوع آخر من المكافآت
كيفية منح المكافآت
<|im_start|>منح المكافآت للطلاب بطريقتين:

تلقائيًا: عندما يحصل الطالب على نقاط جديدة، يتحقق النظام من استحقاقه لأي مكافآت جديدة بناءً على إجمالي نقاطه
يدويًا: يمكن للمشرفين منح مكافآت للطلاب من خلال واجهة إدارة المكافآت
كل مكافأة لها عدد محدد من النقاط المطلوبة (requiredPoints) للحصول عليها.

4. نظام الشهادات
أنواع الشهادات
يدعم النظام عدة أنواع من الشهادات:

شهادة إنجاز (ACHIEVEMENT)
شهادة تفوق (EXCELLENCE)
شهادة تقدير (APPRECIATION)
شهادة تخرج (GRADUATION)
قالب مخصص (CUSTOM)
إصدار وطباعة الشهادات
يمكن للمشرفين إصدار شهادات للطلاب من خلال:

اختيار الطالب المستحق
اختيار نوع الشهادة
تعبئة بيانات الشهادة (العنوان، الوصف، إلخ)
معاينة الشهادة
طباعة الشهادة أو تحميلها كصورة
5. لوحة الشرف (Honor Board)
لوحة الشرف هي واجهة رئيسية تعرض:

الطلاب المتفوقون
عرض أفضل 3 طلاب في بطاقات مميزة مع ترتيبهم (ذهبي، فضي، برونزي)
عرض قائمة بباقي الطلاب المتفوقين مرتبة حسب النقاط
المكافآت الأخيرة
عرض آخر المكافآت التي تم منحها للطلاب مع تفاصيلها:

اسم المكافأة
اسم الطالب
القسم
النقاط المطلوبة
تاريخ المنح
شهادات التقدير
عرض آخر الشهادات التي تم إصدارها للطلاب مع إمكانية معاينتها.

6. إدارة المكافآت
يمكن للمشرفين إدارة المكافآت من خلال صفحة إدارة المكافآت التي تتيح:

إضافة مكافآت جديدة
تعديل المكافآت الموجودة
حذف المكافآت (إذا لم تكن ممنوحة لأي طالب)
عرض عدد الطلاب الحاصلين على كل مكافأة
7. معايير التقييم للطلاب المتفوقين
يوجد نظام معايير تقييم (HonorCriteria) يحدد:

اسم المعيار
وصف المعيار
الحد الأدنى للنقاط المطلوبة
حالة المعيار (نشط/غير نشط)
8. كيفية عمل النظام تقنيًا
جداول قاعدة البيانات:
Student: يحتوي على حقل totalPoints لتخزين إجمالي نقاط الطالب
StudentPoint: لتسجيل النقاط التي يحصل عليها الطالب
Reward: لتعريف المكافآت المتاحة
StudentReward: لتسجيل المكافآت الممنوحة للطلاب
Certificate: لتعريف أنواع الشهادات
StudentCertificate: لتسجيل الشهادات الممنوحة للطلاب
HonorCriteria: لتحديد معايير التقييم للطلاب المتفوقين
واجهات المستخدم:
صفحة لوحة الشرف (Honor Board)
صفحة إدارة المكافآت
صفحة إدارة الشهادات
نوافذ معاينة وطباعة الشهادات
آلية منح النقاط والمكافآت:
عند إضافة نقاط للطالب، يتم تحديث إجمالي نقاطه
يتم التحقق من استحقاق الطالب لأي مكافآت جديدة بناءً على إجمالي نقاطه
يتم منح المكافآت المستحقة تلقائيًا

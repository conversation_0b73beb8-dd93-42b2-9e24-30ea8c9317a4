import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/criteria-scores
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const examPointId = searchParams.get('examPointId');
    const criteriaId = searchParams.get('criteriaId');

    // بناء شروط البحث
    const where: {
      examPointId?: number;
      criteriaId?: number;
    } = {};

    if (examPointId) {
      where.examPointId = parseInt(examPointId);
    }

    if (criteriaId) {
      where.criteriaId = parseInt(criteriaId);
    }

    const criteriaScores = await prisma.criteriaScore.findMany({
      where,
      include: {
        examPoint: {
          include: {
            student: {
              select: {
                id: true,
                name: true
              }
            },
            exam: true
          }
        },
        criteria: true
      },
      orderBy: [
        {
          examPoint: {
            student: {
              name: 'asc'
            }
          }
        },
        {
          criteria: {
            weight: 'desc'
          }
        }
      ]
    });

    return NextResponse.json({
      data: criteriaScores,
      success: true,
      message: 'تم جلب درجات معايير التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error fetching criteria scores:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب درجات معايير التقييم',
      success: false
    }, { status: 500 });
  }
}

// POST /api/criteria-scores
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { examPointId, criteriaId, score } = body;

    // التحقق من البيانات المطلوبة
    if (!examPointId || !criteriaId || score === undefined) {
      return NextResponse.json({
        error: 'معرف نقطة الامتحان ومعرف المعيار والدرجة مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة الدرجة
    const numericScore = parseFloat(score);
    if (isNaN(numericScore) || numericScore < 0 || numericScore > 10) {
      return NextResponse.json({
        error: 'يجب أن تكون الدرجة رقمًا بين 0 و 10',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود نقطة الامتحان
    const examPoint = await prisma.exam_points.findUnique({
      where: { id: parseInt(examPointId) }
    });

    if (!examPoint) {
      return NextResponse.json({
        error: 'نقطة الامتحان غير موجودة',
        success: false
      }, { status: 404 });
    }

    // التحقق من وجود المعيار
    const criteria = await prisma.evaluationCriteria.findUnique({
      where: { id: parseInt(criteriaId) }
    });

    if (!criteria) {
      return NextResponse.json({
        error: 'معيار التقييم غير موجود',
        success: false
      }, { status: 404 });
    }

    // التحقق من عدم وجود درجة سابقة لهذا المعيار ونقطة الامتحان
    const existingScore = await prisma.criteriaScore.findFirst({
      where: {
        examPointId: parseInt(examPointId),
        criteriaId: parseInt(criteriaId)
      }
    });

    if (existingScore) {
      return NextResponse.json({
        error: 'توجد درجة سابقة لهذا المعيار ونقطة الامتحان',
        success: false
      }, { status: 400 });
    }

    // إنشاء درجة المعيار
    const criteriaScore = await prisma.criteriaScore.create({
      data: {
        examPointId: parseInt(examPointId),
        criteriaId: parseInt(criteriaId),
        score: numericScore
      },
      include: {
        criteria: true,
        examPoint: {
          include: {
            student: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    // حساب الدرجة الإجمالية بناءً على معايير التقييم
    const allCriteriaScores = await prisma.criteriaScore.findMany({
      where: {
        examPointId: parseInt(examPointId)
      },
      include: {
        criteria: true
      }
    });

    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const cs of allCriteriaScores) {
      totalWeightedScore += parseFloat(cs.score.toString()) * parseFloat(cs.criteria.weight.toString());
      totalWeight += parseFloat(cs.criteria.weight.toString());
    }

    // حساب الدرجة النهائية
    const finalGrade = totalWeight > 0 ? (totalWeightedScore / totalWeight) * 10 : 0;

    // تحديث درجة نقطة الامتحان
    await prisma.exam_points.update({
      where: { id: parseInt(examPointId) },
      data: {
        grade: finalGrade
      }
    });

    return NextResponse.json({
      data: {
        criteriaScore,
        finalGrade
      },
      success: true,
      message: `تم إضافة درجة معيار التقييم بنجاح وتحديث الدرجة النهائية إلى ${finalGrade.toFixed(2)}`
    });
  } catch (error) {
    console.error('Error creating criteria score:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إضافة درجة معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/criteria-scores
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, score } = body;

    // التحقق من البيانات المطلوبة
    if (!id || score === undefined) {
      return NextResponse.json({
        error: 'المعرف والدرجة مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة الدرجة
    const numericScore = parseFloat(score);
    if (isNaN(numericScore) || numericScore < 0 || numericScore > 10) {
      return NextResponse.json({
        error: 'يجب أن تكون الدرجة رقمًا بين 0 و 10',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود درجة المعيار
    const existingScore = await prisma.criteriaScore.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingScore) {
      return NextResponse.json({
        error: 'درجة معيار التقييم غير موجودة',
        success: false
      }, { status: 404 });
    }

    // تحديث درجة المعيار
    const criteriaScore = await prisma.criteriaScore.update({
      where: { id: parseInt(id) },
      data: {
        score: numericScore
      },
      include: {
        criteria: true,
        examPoint: true
      }
    });

    // حساب الدرجة الإجمالية بناءً على معايير التقييم
    const allCriteriaScores = await prisma.criteriaScore.findMany({
      where: {
        examPointId: criteriaScore.examPointId
      },
      include: {
        criteria: true
      }
    });

    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const cs of allCriteriaScores) {
      totalWeightedScore += parseFloat(cs.score.toString()) * parseFloat(cs.criteria.weight.toString());
      totalWeight += parseFloat(cs.criteria.weight.toString());
    }

    // حساب الدرجة النهائية
    const finalGrade = totalWeight > 0 ? (totalWeightedScore / totalWeight) * 10 : 0;

    // تحديث درجة نقطة الامتحان
    await prisma.exam_points.update({
      where: { id: criteriaScore.examPointId },
      data: {
        grade: finalGrade
      }
    });

    return NextResponse.json({
      data: {
        criteriaScore,
        finalGrade
      },
      success: true,
      message: `تم تحديث درجة معيار التقييم بنجاح وتحديث الدرجة النهائية إلى ${finalGrade.toFixed(2)}`
    });
  } catch (error) {
    console.error('Error updating criteria score:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث درجة معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/criteria-scores
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف درجة معيار التقييم مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود درجة المعيار
    const criteriaScore = await prisma.criteriaScore.findUnique({
      where: { id: parseInt(id) },
      include: {
        examPoint: true
      }
    });

    if (!criteriaScore) {
      return NextResponse.json({
        error: 'درجة معيار التقييم غير موجودة',
        success: false
      }, { status: 404 });
    }

    // حذف درجة المعيار
    await prisma.criteriaScore.delete({
      where: { id: parseInt(id) }
    });

    // حساب الدرجة الإجمالية بناءً على معايير التقييم المتبقية
    const allCriteriaScores = await prisma.criteriaScore.findMany({
      where: {
        examPointId: criteriaScore.examPointId
      },
      include: {
        criteria: true
      }
    });

    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const cs of allCriteriaScores) {
      totalWeightedScore += parseFloat(cs.score.toString()) * parseFloat(cs.criteria.weight.toString());
      totalWeight += parseFloat(cs.criteria.weight.toString());
    }

    // حساب الدرجة النهائية
    const finalGrade = totalWeight > 0 ? (totalWeightedScore / totalWeight) * 10 : 0;

    // تحديث درجة نقطة الامتحان
    await prisma.exam_points.update({
      where: { id: criteriaScore.examPointId },
      data: {
        grade: finalGrade
      }
    });

    return NextResponse.json({
      data: {
        finalGrade
      },
      success: true,
      message: `تم حذف درجة معيار التقييم بنجاح وتحديث الدرجة النهائية إلى ${finalGrade.toFixed(2)}`
    });
  } catch (error) {
    console.error('Error deleting criteria score:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف درجة معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

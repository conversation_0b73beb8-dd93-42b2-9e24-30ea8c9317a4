import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import { randomBytes, createHash } from 'crypto';

// POST /api/reports/share - إنشاء رابط مشاركة للتقرير
export async function POST(req: NextRequest) {
  try {
    // التحقق من المستخدم
    const jwtToken = req.cookies.get("jwtToken")?.value;
    if (!jwtToken) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    const token = await getToken(jwtToken);
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    // استخراج البيانات من الطلب
    const body = await req.json();
    const { reportId, reportType, expiryDays = 7, requirePassword = false, password } = body;

    if (!reportId || !reportType) {
      return NextResponse.json(
        { error: 'معرف التقرير ونوعه مطلوبان' },
        { status: 400 }
      );
    }

    // إنشاء رمز مشاركة فريد
    const shareToken = randomBytes(32).toString('hex');

    // حساب تاريخ انتهاء الصلاحية
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + expiryDays);

    // تشفير كلمة المرور إذا كانت مطلوبة
    let hashedPassword = null;
    if (requirePassword && password) {
      hashedPassword = createHash('sha256').update(password).digest('hex');
    }

    // إنشاء سجل مشاركة التقرير
    const reportShare = await prisma.reportShare.create({
      data: {
        token: shareToken,
        reportId: reportId,
        reportType: reportType,
        createdBy: token.id.toString(),
        expiryDate: expiryDate,
        hasPassword: requirePassword,
        password: hashedPassword,
      }
    });

    // إنشاء رابط المشاركة
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const shareLink = `${baseUrl}/shared-reports/${shareToken}`;

    return NextResponse.json({
      success: true,
      shareLink,
      expiryDate: reportShare.expiryDate
    });
  } catch (error) {
    console.error('خطأ في إنشاء رابط مشاركة التقرير:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء رابط مشاركة التقرير' },
      { status: 500 }
    );
  }
}

// GET /api/reports/share - الحصول على قائمة روابط المشاركة
export async function GET(req: NextRequest) {
  try {
    // التحقق من المستخدم
    const jwtToken = req.cookies.get("jwtToken")?.value;
    if (!jwtToken) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    const token = await getToken(jwtToken);
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    // الحصول على روابط المشاركة الخاصة بالمستخدم
    const reportShares = await prisma.reportShare.findMany({
      where: {
        createdBy: token.id.toString(),
        expiryDate: {
          gte: new Date() // روابط غير منتهية الصلاحية فقط
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // إنشاء روابط المشاركة
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const shares = reportShares.map(share => ({
      id: share.id,
      reportId: share.reportId,
      reportType: share.reportType,
      shareLink: `${baseUrl}/shared-reports/${share.token}`,
      expiryDate: share.expiryDate,
      hasPassword: share.hasPassword,
      createdAt: share.createdAt
    }));

    return NextResponse.json({
      success: true,
      shares
    });
  } catch (error) {
    console.error('خطأ في جلب روابط مشاركة التقارير:', error);
    return NextResponse.json(
      { error: 'فشل في جلب روابط مشاركة التقارير' },
      { status: 500 }
    );
  }
}

// DELETE /api/reports/share - حذف رابط مشاركة
export async function DELETE(req: NextRequest) {
  try {
    // التحقق من المستخدم
    const jwtToken = req.cookies.get("jwtToken")?.value;
    if (!jwtToken) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    const token = await getToken(jwtToken);
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح لك بالوصول' },
        { status: 401 }
      );
    }

    // استخراج معرف المشاركة من الطلب
    const { searchParams } = new URL(req.url);
    const shareId = searchParams.get('id');

    if (!shareId) {
      return NextResponse.json(
        { error: 'معرف المشاركة مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود المشاركة وملكيتها
    const share = await prisma.reportShare.findUnique({
      where: {
        id: parseInt(shareId)
      }
    });

    if (!share) {
      return NextResponse.json(
        { error: 'المشاركة غير موجودة' },
        { status: 404 }
      );
    }

    if (share.createdBy !== token.id.toString()) {
      return NextResponse.json(
        { error: 'غير مصرح لك بحذف هذه المشاركة' },
        { status: 403 }
      );
    }

    // حذف المشاركة
    await prisma.reportShare.delete({
      where: {
        id: parseInt(shareId)
      }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف المشاركة بنجاح'
    });
  } catch (error) {
    console.error('خطأ في حذف مشاركة التقرير:', error);
    return NextResponse.json(
      { error: 'فشل في حذف مشاركة التقرير' },
      { status: 500 }
    );
  }
}

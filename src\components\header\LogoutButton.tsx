"use client";
import axios from "axios";
import { DOMAIN } from '@/utils/constants';
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { FaUserCircle } from "react-icons/fa";
import { IoMdArrowDropdown } from "react-icons/io";
import Link from "next/link";

const LogoutButton = () => {

  const [menuOpen, setMenuOpen] = useState(false);
  const router = useRouter();
  const logoutHandler = async () => {
    try {
        await axios.get(`${DOMAIN}/api/users/logout`);
        router.push("/");
        router.refresh();
    } catch (error) {
        toast.warning("Something went wrong");
        console.log(error);
    }
  }
  return (
      <div className="relative">
          <div className="relative inline-block text-right">
              <button
                  className="inline-flex items-center gap-2 px-4 py-2 rounded-md hover:bg-gray-100"
                  onClick={() => setMenuOpen(!menuOpen)}
              >
                  <FaUserCircle className="text-xl" />
                  <IoMdArrowDropdown />
              </button>
              {menuOpen && (
                  <div className="absolute mt-2 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5
                      md:w-56
                      top-full
                      z-50
                      md:right-0
                      right-[-140px]">
                      <Link href="/profile" className="block px-4 py-3 text-sm hover:bg-gray-100 text-right">حسابي</Link>
                      <Link href="/change-password" className="block px-4 py-3 text-sm hover:bg-gray-100 text-right">تغيير كلمة السر</Link>
                      <Link href="/logout" onClick={logoutHandler} className="block px-4 py-3 text-sm hover:bg-gray-100 text-right">تسجيل الخروج</Link>
                  </div>
              )}
          </div>
      </div>
  )
}

export default LogoutButton
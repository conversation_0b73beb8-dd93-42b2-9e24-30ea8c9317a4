'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Plus, Pencil, Trash2, Search, Database, BookOpen, Upload, Download, Copy, FileSpreadsheet } from 'lucide-react';
import Link from 'next/link';
import { useRef } from 'react';
import { exportQuestionsToExcel, importQuestionsFromExcel, saveImportedQuestions, copyQuestionsToBankId } from '@/utils/question-bank-utils';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface QuestionBank {
  id: number;
  name: string;
  description: string | null;
  subjectId: number | null;
  subject: {
    id: number;
    name: string;
  } | null;
  _count: {
    questions: number;
  };
  createdAt: string;
  updatedAt: string;
}

interface Subject {
  id: number;
  name: string;
}

export default function QuestionBanksPage() {
  const [questionBanks, setQuestionBanks] = useState<QuestionBank[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isCopyDialogOpen, setIsCopyDialogOpen] = useState(false);
  const [selectedQuestionBank, setSelectedQuestionBank] = useState<QuestionBank | null>(null);
  const [targetBankId, setTargetBankId] = useState<string>('');
  const [isImporting, setIsImporting] = useState(false);
  const [isCopying, setIsCopying] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    subjectId: 'none'
  });
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchQuestionBanks();
    fetchSubjects();
  }, []);

  const fetchQuestionBanks = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/question-banks');
      const result = await response.json();

      if (result.success) {
        setQuestionBanks(result.data);
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب بنوك الأسئلة',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error fetching question banks:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب بنوك الأسئلة',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchSubjects = async () => {
    try {
      const response = await fetch('/api/subjects');
      const result = await response.json();

      if (result.success) {
        setSubjects(result.data);
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب المواد الدراسية',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء جلب المواد الدراسية',
        variant: 'destructive'
      });
    }
  };

  const handleAddQuestionBank = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/question-banks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || null,
          subjectId: formData.subjectId && formData.subjectId !== 'none' ? formData.subjectId : null
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'نجاح',
          description: 'تم إنشاء بنك الأسئلة بنجاح'
        });
        setIsAddDialogOpen(false);
        fetchQuestionBanks();
        setFormData({ name: '', description: '', subjectId: 'none' });
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء إنشاء بنك الأسئلة',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error creating question bank:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء إنشاء بنك الأسئلة',
        variant: 'destructive'
      });
    }
  };

  const handleEditQuestionBank = (questionBank: QuestionBank) => {
    setSelectedQuestionBank(questionBank);
    setFormData({
      name: questionBank.name,
      description: questionBank.description || '',
      subjectId: questionBank.subjectId ? String(questionBank.subjectId) : 'none'
    });
    setIsEditDialogOpen(true);
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedQuestionBank) return;

    try {
      const response = await fetch('/api/question-banks', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedQuestionBank.id,
          name: formData.name,
          description: formData.description || null,
          subjectId: formData.subjectId && formData.subjectId !== 'none' ? formData.subjectId : null
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'نجاح',
          description: 'تم تحديث بنك الأسئلة بنجاح'
        });
        setIsEditDialogOpen(false);
        fetchQuestionBanks();
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء تحديث بنك الأسئلة',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error updating question bank:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء تحديث بنك الأسئلة',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteQuestionBank = (questionBank: QuestionBank) => {
    setSelectedQuestionBank(questionBank);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedQuestionBank) return;

    try {
      const response = await fetch(`/api/question-banks?id=${selectedQuestionBank.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'نجاح',
          description: 'تم حذف بنك الأسئلة بنجاح'
        });
        setIsDeleteDialogOpen(false);
        fetchQuestionBanks();
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء حذف بنك الأسئلة',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error deleting question bank:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء حذف بنك الأسئلة',
        variant: 'destructive'
      });
    }
  };

  const handleExportQuestions = async (bankId: number, bankName: string) => {
    try {
      setIsLoading(true);
      // جلب الأسئلة من بنك الأسئلة
      const response = await fetch(`/api/questions?bankId=${bankId}`);
      const result = await response.json();

      if (result.success) {
        // تصدير الأسئلة إلى ملف Excel
        exportQuestionsToExcel(result.data, bankName);
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب الأسئلة',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error exporting questions:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء تصدير الأسئلة',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleImportFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !selectedQuestionBank) return;

    try {
      setIsImporting(true);
      // استيراد الأسئلة من ملف Excel
      const questions = await importQuestionsFromExcel(file, selectedQuestionBank.id);

      // حفظ الأسئلة في قاعدة البيانات
      const savedCount = await saveImportedQuestions(questions);

      toast({
        title: 'نجاح',
        description: `تم استيراد ${savedCount} سؤال بنجاح`
      });
      setIsImportDialogOpen(false);
      fetchQuestionBanks();

      // إعادة تعيين حقل الملف
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error importing questions:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء استيراد الأسئلة',
        variant: 'destructive'
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleCopyQuestions = async () => {
    if (!selectedQuestionBank || !targetBankId) return;

    try {
      setIsCopying(true);
      // جلب الأسئلة من بنك الأسئلة المصدر
      const response = await fetch(`/api/questions?bankId=${selectedQuestionBank.id}`);
      const result = await response.json();

      if (result.success) {
        // نسخ الأسئلة إلى بنك الأسئلة الهدف
        const copiedCount = await copyQuestionsToBankId(result.data, parseInt(targetBankId));

        toast({
          title: 'نجاح',
          description: `تم نسخ ${copiedCount} سؤال بنجاح`
        });
        setIsCopyDialogOpen(false);
        fetchQuestionBanks();
      } else {
        toast({
          title: 'خطأ',
          description: result.error || 'حدث خطأ أثناء جلب الأسئلة',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error copying questions:', error);
      toast({
        title: 'خطأ',
        description: 'حدث خطأ أثناء نسخ الأسئلة',
        variant: 'destructive'
      });
    } finally {
      setIsCopying(false);
    }
  };

  const filteredQuestionBanks = questionBanks.filter(bank =>
    bank.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (bank.description && bank.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (bank.subject && bank.subject.name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <OptimizedProtectedRoute requiredPermission="admin.evaluation.question-banks.view">
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">إدارة بنوك الأسئلة</h1>
        <QuickActionButtons
          entityType="evaluation.question-banks"
          actions={[
            {
              key: 'create',
              label: 'إنشاء بنك أسئلة جديد',
              icon: <Plus size={16} />,
              onClick: () => {
                setFormData({ name: '', description: '', subjectId: 'none' });
                setIsAddDialogOpen(true);
              },
              variant: 'primary'
            }
          ]}
        />
      </div>

      <div className="mb-6">
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <Input
            placeholder="البحث في بنوك الأسئلة..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-12 text-right"
            dir="rtl"
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
        </div>
      ) : filteredQuestionBanks.length === 0 ? (
        <div className="text-center p-8 bg-white rounded-lg shadow">
          <Database className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بنوك أسئلة</h3>
          <p className="text-gray-500 mb-4">لم يتم العثور على أي بنوك أسئلة. يمكنك إنشاء بنك أسئلة جديد.</p>
          <Button
            onClick={() => {
              setFormData({ name: '', description: '', subjectId: 'none' });
              setIsAddDialogOpen(true);
            }}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
          >
            <Plus className="ml-2" size={16} />
            إنشاء بنك أسئلة جديد
          </Button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <Table>
            <TableHeader className="bg-[var(--primary-color)]">
              <TableRow>
                <TableHead className="text-white text-right">اسم بنك الأسئلة</TableHead>
                <TableHead className="text-white text-right">المادة</TableHead>
                <TableHead className="text-white text-right">عدد الأسئلة</TableHead>
                <TableHead className="text-white text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredQuestionBanks.map((bank) => (
                <TableRow key={bank.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{bank.name}</TableCell>
                  <TableCell>{bank.subject?.name || '-'}</TableCell>
                  <TableCell>{bank._count.questions}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-2">
                      <Link href={`/admin/evaluation/questions?bankId=${bank.id}`}>
                        <Button variant="outline" size="sm" className="ml-2">
                          <BookOpen className="h-4 w-4 ml-1" />
                          عرض الأسئلة
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedQuestionBank(bank);
                          setIsImportDialogOpen(true);
                        }}
                        className="ml-2 bg-blue-50 hover:bg-blue-100"
                      >
                        <Upload className="h-4 w-4 ml-1" />
                        استيراد
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleExportQuestions(bank.id, bank.name)}
                        className="ml-2 bg-green-50 hover:bg-green-100"
                      >
                        <Download className="h-4 w-4 ml-1" />
                        تصدير
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedQuestionBank(bank);
                          setIsCopyDialogOpen(true);
                        }}
                        className="ml-2 bg-purple-50 hover:bg-purple-100"
                      >
                        <Copy className="h-4 w-4 ml-1" />
                        نسخ
                      </Button>
                      <OptimizedActionButtonGroup
                        entityType="evaluation.question-banks"
                        onEdit={() => handleEditQuestionBank(bank)}
                        onDelete={() => handleDeleteQuestionBank(bank)}
                        showEdit={true}
                        showDelete={true}
                        size="sm"
                        className="gap-2"
                        deleteConfirmTitle="هل أنت متأكد من حذف هذا البنك؟"
                      />
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Question Bank Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => !open && setIsAddDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>إنشاء بنك أسئلة جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل بنك الأسئلة الجديد</DialogDescription>
          </DialogHeader>
          <form id="addQuestionBankForm" onSubmit={handleAddQuestionBank} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم بنك الأسئلة</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">المادة (اختياري)</label>
              <Select
                value={formData.subjectId}
                onValueChange={(value) => setFormData({ ...formData, subjectId: value })}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر المادة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">بدون مادة</SelectItem>
                  {subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id.toString()}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
                className="ml-2"
              >
                إلغاء
              </Button>
              <Button type="submit" className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                إنشاء
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Question Bank Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>تعديل بنك الأسئلة</DialogTitle>
            <DialogDescription>قم بتعديل تفاصيل بنك الأسئلة</DialogDescription>
          </DialogHeader>
          {selectedQuestionBank && (
            <form id="editQuestionBankForm" onSubmit={handleSubmitEdit} className="space-y-4">
              <div className="space-y-2">
                <label className="text-right block">اسم بنك الأسئلة</label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  dir="rtl"
                  className="text-right"
                  required
                />
              </div>
              <div className="space-y-2">
                <label className="text-right block">المادة (اختياري)</label>
                <Select
                  value={formData.subjectId}
                  onValueChange={(value) => setFormData({ ...formData, subjectId: value })}
                >
                  <SelectTrigger className="w-full text-right">
                    <SelectValue placeholder="اختر المادة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">بدون مادة</SelectItem>
                    {subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id.toString()}>
                        {subject.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-right block">الوصف (اختياري)</label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  dir="rtl"
                  className="text-right"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                  className="ml-2"
                >
                  إلغاء
                </Button>
                <Button type="submit" className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                  حفظ التغييرات
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Question Bank Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-red-500">
          <DialogHeader>
            <DialogTitle>حذف بنك الأسئلة</DialogTitle>
            <DialogDescription>هل أنت متأكد من رغبتك في حذف بنك الأسئلة هذا؟</DialogDescription>
          </DialogHeader>
          {selectedQuestionBank && (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                <p className="text-red-700 text-right">
                  سيتم حذف بنك الأسئلة &quot;{selectedQuestionBank.name}&quot; وجميع الأسئلة المرتبطة به ({selectedQuestionBank._count.questions} سؤال).
                  هذا الإجراء لا يمكن التراجع عنه.
                </p>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDeleteDialogOpen(false)}
                  className="ml-2"
                >
                  إلغاء
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  onClick={confirmDelete}
                >
                  تأكيد الحذف
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Import Questions Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={(open) => !open && setIsImportDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>استيراد أسئلة</DialogTitle>
            <DialogDescription>
              استيراد أسئلة من ملف Excel إلى بنك الأسئلة &quot;{selectedQuestionBank?.name}&quot;
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-blue-700 text-right mb-2">
                <strong>ملاحظات:</strong>
              </p>
              <ul className="list-disc list-inside text-blue-700 text-right space-y-1">
                <li>يجب أن يحتوي الملف على الأعمدة التالية: نص السؤال، نوع السؤال، مستوى الصعوبة، النقاط</li>
                <li>يمكن إضافة أعمدة اختيارية: الخيارات، الخيارات الصحيحة، الإجابات، الشرح</li>
                <li>يجب فصل الخيارات والإجابات بعلامة | (الشريط الرأسي)</li>
              </ul>
            </div>
            <div className="flex justify-center">
              <Button
                onClick={handleImportFile}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                disabled={isImporting}
              >
                {isImporting ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري الاستيراد...
                  </>
                ) : (
                  <>
                    <FileSpreadsheet className="ml-2 h-4 w-4" />
                    اختيار ملف Excel
                  </>
                )}
              </Button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".xlsx,.xls"
                className="hidden"
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Copy Questions Dialog */}
      <Dialog open={isCopyDialogOpen} onOpenChange={(open) => !open && setIsCopyDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)]">
          <DialogHeader>
            <DialogTitle>نسخ الأسئلة</DialogTitle>
            <DialogDescription>
              نسخ الأسئلة من بنك &quot;{selectedQuestionBank?.name}&quot; إلى بنك آخر
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">بنك الأسئلة الهدف</label>
              <Select
                value={targetBankId}
                onValueChange={setTargetBankId}
              >
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر بنك الأسئلة الهدف" />
                </SelectTrigger>
                <SelectContent>
                  {questionBanks
                    .filter(bank => bank.id !== selectedQuestionBank?.id)
                    .map(bank => (
                      <SelectItem key={bank.id} value={bank.id.toString()}>
                        {bank.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCopyDialogOpen(false)}
                className="ml-2"
              >
                إلغاء
              </Button>
              <Button
                type="button"
                onClick={handleCopyQuestions}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                disabled={!targetBankId || isCopying}
              >
                {isCopying ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري النسخ...
                  </>
                ) : (
                  <>
                    <Copy className="ml-2 h-4 w-4" />
                    نسخ الأسئلة
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

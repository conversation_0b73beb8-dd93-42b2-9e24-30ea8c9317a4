# 🗄️ إصلاحات قاعدة البيانات - نظام المدفوعات والفواتير

## 📋 نظرة عامة
خطة شاملة لتحسين قاعدة البيانات من خلال إضافة فهارس محسنة، قيود سلامة البيانات، وتحسينات على الأداء.

## 🚨 إصلاحات حرجة (أولوية عالية جداً)

### 1. إضافة قيود سلامة البيانات للفواتير

#### المشكلة الحالية
```prisma
model Invoice {
  studentId  Int?     // يمكن أن يكون null
  parentId   Int?     // يمكن أن يكون null
  type       InvoiceType
  // لا توجد قيود تضمن التطابق بين النوع والمعرف
}
```

#### الحل المطلوب
```sql
-- إضافة قيد للتأكد من صحة نوع الفاتورة
ALTER TABLE "Invoice" ADD CONSTRAINT "chk_invoice_type_consistency" 
CHECK (
  (type = 'INDIVIDUAL' AND "studentId" IS NOT NULL AND "parentId" IS NULL) OR
  (type = 'FAMILY' AND "parentId" IS NOT NULL AND "studentId" IS NULL)
);

-- إضافة قيد للتأكد من صحة المبلغ
ALTER TABLE "Invoice" ADD CONSTRAINT "chk_invoice_amount_positive" 
CHECK (amount > 0 AND amount <= 1000000);

-- إضافة قيد للتأكد من صحة التواريخ
ALTER TABLE "Invoice" ADD CONSTRAINT "chk_invoice_dates" 
CHECK ("dueDate" > "issueDate");

-- إضافة قيد للتأكد من صحة الشهر والسنة
ALTER TABLE "Invoice" ADD CONSTRAINT "chk_invoice_month_year" 
CHECK (month >= 1 AND month <= 12 AND year >= 2020 AND year <= 2030);
```

### 2. إضافة قيود سلامة البيانات للمدفوعات

#### الحل المطلوب
```sql
-- إضافة قيد للتأكد من صحة المبلغ
ALTER TABLE "Payment" ADD CONSTRAINT "chk_payment_amount_positive" 
CHECK (amount > 0 AND amount <= 1000000);

-- إضافة قيد للتأكد من صحة التاريخ
ALTER TABLE "Payment" ADD CONSTRAINT "chk_payment_date_valid" 
CHECK (date >= '2020-01-01' AND date <= CURRENT_DATE + INTERVAL '1 year');

-- إضافة قيد للتأكد من وجود التلميذ
ALTER TABLE "Payment" ADD CONSTRAINT "chk_payment_student_required" 
CHECK ("studentId" IS NOT NULL);
```

### 3. إضافة قيود سلامة البيانات للأولياء

#### الحل المطلوب
```sql
-- إضافة قيد للتأكد من صحة المبلغ لكل تلميذ
ALTER TABLE "Parent" ADD CONSTRAINT "chk_parent_amount_per_student" 
CHECK ("amountPerStudent" IS NULL OR ("amountPerStudent" > 0 AND "amountPerStudent" <= 100000));

-- إضافة قيد للتأكد من صحة رقم الهاتف
ALTER TABLE "Parent" ADD CONSTRAINT "chk_parent_phone_format" 
CHECK (phone ~ '^[0-9+\-\s()]{8,20}$');
```

## 🔧 تحسينات الأداء (أولوية عالية)

### 1. إضافة فهارس مركبة للاستعلامات الشائعة

#### فهارس المدفوعات
```sql
-- فهرس للبحث بالتلميذ والتاريخ (الأحدث أولاً)
CREATE INDEX "idx_payment_student_date_desc" ON "Payment"("studentId", "date" DESC);

-- فهرس للبحث بالفاتورة والحالة
CREATE INDEX "idx_payment_invoice_status" ON "Payment"("invoiceId", "status");

-- فهرس للبحث بالحالة والمبلغ
CREATE INDEX "idx_payment_status_amount" ON "Payment"("status", "amount");

-- فهرس للبحث بطريقة الدفع
CREATE INDEX "idx_payment_method" ON "Payment"("paymentMethodId");

-- فهرس للبحث بالشهر (للتقارير الشهرية)
CREATE INDEX "idx_payment_date_month" ON "Payment"(EXTRACT(YEAR_MONTH FROM "date"));
```

#### فهارس الفواتير
```sql
-- فهرس للبحث بالتلميذ والحالة
CREATE INDEX "idx_invoice_student_status" ON "Invoice"("studentId", "status");

-- فهرس للبحث بالولي والحالة
CREATE INDEX "idx_invoice_parent_status" ON "Invoice"("parentId", "status");

-- فهرس للبحث بتاريخ الاستحقاق والحالة (للفواتير المتأخرة)
CREATE INDEX "idx_invoice_due_date_status" ON "Invoice"("dueDate", "status");

-- فهرس للبحث بالشهر والسنة
CREATE INDEX "idx_invoice_month_year" ON "Invoice"("year", "month");

-- فهرس للبحث بالنوع والحالة
CREATE INDEX "idx_invoice_type_status" ON "Invoice"("type", "status");

-- فهرس للبحث بتاريخ الإصدار (للتقارير)
CREATE INDEX "idx_invoice_issue_date" ON "Invoice"("issueDate");
```

#### فهارس الأولياء والتلاميذ
```sql
-- فهرس للبحث بالولي في جدول التلاميذ
CREATE INDEX "idx_student_guardian" ON "Student"("guardianId");

-- فهرس للبحث بالصف
CREATE INDEX "idx_student_classe" ON "Student"("classeId");

-- فهرس للبحث بالاسم في الأولياء
CREATE INDEX "idx_parent_name" ON "Parent"("name");

-- فهرس للبحث برقم الهاتف
CREATE INDEX "idx_parent_phone" ON "Parent"("phone");

-- فهرس للبحث بالبريد الإلكتروني
CREATE INDEX "idx_parent_email" ON "Parent"("email") WHERE "email" IS NOT NULL;
```

### 2. إضافة فهارس للبحث النصي

```sql
-- فهرس للبحث النصي في أسماء الأولياء
CREATE INDEX "idx_parent_name_gin" ON "Parent" USING gin(to_tsvector('arabic', "name"));

-- فهرس للبحث النصي في أسماء التلاميذ
CREATE INDEX "idx_student_name_gin" ON "Student" USING gin(to_tsvector('arabic', "name"));

-- فهرس للبحث النصي في وصف الفواتير
CREATE INDEX "idx_invoice_description_gin" ON "Invoice" USING gin(to_tsvector('arabic', "description")) 
WHERE "description" IS NOT NULL;
```

## 🔄 تحسينات الاستعلامات (أولوية متوسطة)

### 1. إنشاء Views محسنة للاستعلامات المعقدة

#### View ملخص مدفوعات الأولياء
```sql
CREATE OR REPLACE VIEW "ParentPaymentSummary" AS
SELECT 
    p.id as "parentId",
    p.name as "parentName",
    p.phone as "parentPhone",
    p.email as "parentEmail",
    COUNT(DISTINCT s.id) as "totalStudents",
    
    -- إجمالي المطلوب (من الفواتير غير الملغاة)
    COALESCE(SUM(CASE 
        WHEN i.status != 'CANCELLED' THEN i.amount 
        ELSE 0 
    END), 0) as "totalRequired",
    
    -- إجمالي المدفوع (من المدفوعات المؤكدة)
    COALESCE(SUM(CASE 
        WHEN pay.status = 'PAID' THEN pay.amount 
        ELSE 0 
    END), 0) as "totalPaid",
    
    -- المبلغ المتبقي
    COALESCE(SUM(CASE 
        WHEN i.status != 'CANCELLED' THEN i.amount 
        ELSE 0 
    END), 0) - COALESCE(SUM(CASE 
        WHEN pay.status = 'PAID' THEN pay.amount 
        ELSE 0 
    END), 0) as "totalRemaining",
    
    -- عدد الفواتير المستحقة
    COUNT(CASE 
        WHEN i.status IN ('UNPAID', 'PARTIALLY_PAID', 'OVERDUE') 
        THEN 1 
    END) as "dueInvoices",
    
    -- آخر دفعة
    MAX(pay.date) as "lastPaymentDate",
    
    -- معدل السداد
    CASE 
        WHEN SUM(CASE WHEN i.status != 'CANCELLED' THEN i.amount ELSE 0 END) > 0 
        THEN ROUND(
            (SUM(CASE WHEN pay.status = 'PAID' THEN pay.amount ELSE 0 END) * 100.0) / 
            SUM(CASE WHEN i.status != 'CANCELLED' THEN i.amount ELSE 0 END)
        )
        ELSE 0 
    END as "paymentRate"
    
FROM "Parent" p
LEFT JOIN "Student" s ON p.id = s."guardianId"
LEFT JOIN "Invoice" i ON s.id = i."studentId"
LEFT JOIN "Payment" pay ON s.id = pay."studentId"
GROUP BY p.id, p.name, p.phone, p.email;
```

#### View ملخص فواتير التلاميذ
```sql
CREATE OR REPLACE VIEW "StudentInvoiceSummary" AS
SELECT 
    s.id as "studentId",
    s.name as "studentName",
    s."guardianId" as "parentId",
    c.name as "className",
    
    -- إجمالي المطلوب
    COALESCE(SUM(CASE 
        WHEN i.status != 'CANCELLED' THEN i.amount 
        ELSE 0 
    END), 0) as "totalRequired",
    
    -- إجمالي المدفوع
    COALESCE(SUM(CASE 
        WHEN pay.status = 'PAID' THEN pay.amount 
        ELSE 0 
    END), 0) as "totalPaid",
    
    -- المبلغ المتبقي
    COALESCE(SUM(CASE 
        WHEN i.status != 'CANCELLED' THEN i.amount 
        ELSE 0 
    END), 0) - COALESCE(SUM(CASE 
        WHEN pay.status = 'PAID' THEN pay.amount 
        ELSE 0 
    END), 0) as "totalRemaining",
    
    -- عدد الفواتير المستحقة
    COUNT(CASE 
        WHEN i.status IN ('UNPAID', 'PARTIALLY_PAID', 'OVERDUE') 
        THEN 1 
    END) as "dueInvoices",
    
    -- آخر دفعة
    MAX(pay.date) as "lastPaymentDate"
    
FROM "Student" s
LEFT JOIN "Classe" c ON s."classeId" = c.id
LEFT JOIN "Invoice" i ON s.id = i."studentId"
LEFT JOIN "Payment" pay ON s.id = pay."studentId"
GROUP BY s.id, s.name, s."guardianId", c.name;
```

### 2. إنشاء Functions محسنة

#### دالة حساب حالة الفاتورة
```sql
CREATE OR REPLACE FUNCTION calculate_invoice_status(
    invoice_id INTEGER
) RETURNS TEXT AS $$
DECLARE
    invoice_amount DECIMAL;
    paid_amount DECIMAL;
    due_date DATE;
    current_status TEXT;
BEGIN
    -- جلب بيانات الفاتورة
    SELECT amount, "dueDate", status 
    INTO invoice_amount, due_date, current_status
    FROM "Invoice" 
    WHERE id = invoice_id;
    
    -- حساب المبلغ المدفوع
    SELECT COALESCE(SUM(amount), 0)
    INTO paid_amount
    FROM "Payment"
    WHERE "invoiceId" = invoice_id AND status = 'PAID';
    
    -- تحديد الحالة
    IF paid_amount >= invoice_amount THEN
        RETURN 'PAID';
    ELSIF paid_amount > 0 THEN
        RETURN 'PARTIALLY_PAID';
    ELSIF due_date < CURRENT_DATE THEN
        RETURN 'OVERDUE';
    ELSE
        RETURN 'UNPAID';
    END IF;
END;
$$ LANGUAGE plpgsql;
```

#### دالة تحديث حالات الفواتير
```sql
CREATE OR REPLACE FUNCTION update_invoice_statuses() RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    invoice_record RECORD;
BEGIN
    FOR invoice_record IN 
        SELECT id FROM "Invoice" WHERE status != 'CANCELLED'
    LOOP
        UPDATE "Invoice" 
        SET status = calculate_invoice_status(invoice_record.id)::invoice_status
        WHERE id = invoice_record.id 
        AND status != calculate_invoice_status(invoice_record.id)::invoice_status;
        
        IF FOUND THEN
            updated_count := updated_count + 1;
        END IF;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;
```

## 📊 تحسينات الإحصائيات (أولوية منخفضة)

### 1. إنشاء جداول إحصائيات مجمعة

#### جدول إحصائيات يومية
```sql
CREATE TABLE "DailyStats" (
    id SERIAL PRIMARY KEY,
    date DATE NOT NULL UNIQUE,
    "totalPayments" INTEGER DEFAULT 0,
    "totalPaymentAmount" DECIMAL(10,2) DEFAULT 0,
    "totalInvoices" INTEGER DEFAULT 0,
    "totalInvoiceAmount" DECIMAL(10,2) DEFAULT 0,
    "newParents" INTEGER DEFAULT 0,
    "newStudents" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX "idx_daily_stats_date" ON "DailyStats"("date");
```

#### دالة تحديث الإحصائيات اليومية
```sql
CREATE OR REPLACE FUNCTION update_daily_stats(target_date DATE DEFAULT CURRENT_DATE) 
RETURNS VOID AS $$
BEGIN
    INSERT INTO "DailyStats" (
        date, 
        "totalPayments", 
        "totalPaymentAmount",
        "totalInvoices",
        "totalInvoiceAmount",
        "newParents",
        "newStudents"
    )
    VALUES (
        target_date,
        (SELECT COUNT(*) FROM "Payment" WHERE DATE(date) = target_date),
        (SELECT COALESCE(SUM(amount), 0) FROM "Payment" WHERE DATE(date) = target_date),
        (SELECT COUNT(*) FROM "Invoice" WHERE DATE("issueDate") = target_date),
        (SELECT COALESCE(SUM(amount), 0) FROM "Invoice" WHERE DATE("issueDate") = target_date),
        (SELECT COUNT(*) FROM "Parent" WHERE DATE("createdAt") = target_date),
        (SELECT COUNT(*) FROM "Student" WHERE DATE("createdAt") = target_date)
    )
    ON CONFLICT (date) DO UPDATE SET
        "totalPayments" = EXCLUDED."totalPayments",
        "totalPaymentAmount" = EXCLUDED."totalPaymentAmount",
        "totalInvoices" = EXCLUDED."totalInvoices",
        "totalInvoiceAmount" = EXCLUDED."totalInvoiceAmount",
        "newParents" = EXCLUDED."newParents",
        "newStudents" = EXCLUDED."newStudents",
        "updatedAt" = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;
```

## 🔄 مهام الصيانة التلقائية

### 1. إنشاء مهام دورية

#### تحديث حالات الفواتير يومياً
```sql
-- إنشاء مهمة تحديث حالات الفواتير (يتطلب pg_cron)
SELECT cron.schedule('update-invoice-statuses', '0 1 * * *', 'SELECT update_invoice_statuses();');
```

#### تحديث الإحصائيات اليومية
```sql
-- إنشاء مهمة تحديث الإحصائيات اليومية
SELECT cron.schedule('update-daily-stats', '0 2 * * *', 'SELECT update_daily_stats();');
```

### 2. إنشاء Triggers للتحديث التلقائي

#### Trigger تحديث حالة الفاتورة عند إضافة دفعة
```sql
CREATE OR REPLACE FUNCTION trigger_update_invoice_status() 
RETURNS TRIGGER AS $$
BEGIN
    IF NEW."invoiceId" IS NOT NULL THEN
        UPDATE "Invoice" 
        SET status = calculate_invoice_status(NEW."invoiceId")::invoice_status
        WHERE id = NEW."invoiceId";
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "payment_update_invoice_status"
    AFTER INSERT OR UPDATE ON "Payment"
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_invoice_status();
```

## 📋 خطة التنفيذ

### المرحلة الأولى (يوم 1)
1. إضافة قيود سلامة البيانات الحرجة
2. إضافة الفهارس الأساسية للأداء
3. اختبار الاستعلامات الحالية

### المرحلة الثانية (يوم 2)
1. إنشاء Views المحسنة
2. إنشاء Functions المساعدة
3. اختبار الأداء والتحقق من التحسن

### المرحلة الثالثة (يوم 3)
1. إنشاء جداول الإحصائيات
2. إنشاء Triggers والمهام التلقائية
3. اختبار شامل للنظام

## 🎯 النتائج المتوقعة

### تحسين الأداء
- ⚡ تحسن سرعة الاستعلامات بنسبة 60-80%
- ⚡ تقليل وقت تحميل صفحة المدفوعات حسب الولي بنسبة 70%
- ⚡ تحسن استجابة APIs بنسبة 50%

### تحسين سلامة البيانات
- 🔒 منع إنشاء فواتير غير صحيحة 100%
- 🔒 ضمان صحة المبالغ والتواريخ 100%
- 🔒 منع تضارب البيانات 100%

### تحسين الصيانة
- 🛠️ تحديث تلقائي لحالات الفواتير
- 🛠️ إحصائيات دقيقة ومحدثة
- 🛠️ مراقبة أفضل للأداء

---

**تاريخ الإنشاء:** 2025-06-24  
**المطور:** Augment Agent  
**الحالة:** جاهز للتنفيذ  
**الأولوية:** عالية

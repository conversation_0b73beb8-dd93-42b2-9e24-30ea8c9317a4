'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import FabricCanvasComponent, { FabricCanvas, FabricStatic } from './FabricCanvas';
import WhiteboardToolbar from './WhiteboardToolbar';
import { WhiteboardSync, UserCursor } from './WhiteboardSync';
import UserCursors from './UserCursors';

// تعريف أنواع أحداث الماوس
interface FabricMouseEvent {
  e: MouseEvent;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  target?: any; // يمكن أن يكون أي كائن من Fabric.js
  pointer: { x: number; y: number };
}

interface WhiteboardProps {
  id: string;
  readOnly?: boolean;
  initialData?: string;
  onDataChange?: (data: string) => void;
  height?: number;
  width?: number;

  // Real-time collaboration props
  enableSync?: boolean;
  roomId?: string;
  userId?: string;
  username?: string;
  userColor?: string;
}

/**
 * مكون السبورة التفاعلية المبسط
 * يستخدم مكون FabricCanvas لتهيئة Canvas بشكل آمن
 */
const SimpleWhiteboard: React.FC<WhiteboardProps> = ({
  id,
  readOnly = false,
  initialData,
  onDataChange,
  height = 600,
  width = 800,

  // Real-time collaboration props
  enableSync = false,
  // هذه المتغيرات ستستخدم في وظائف المزامنة المستقبلية
  roomId = 'default-room', // eslint-disable-line @typescript-eslint/no-unused-vars
  userId = 'user-' + Math.random().toString(36).substring(2, 11),
  username = 'مستخدم', // eslint-disable-line @typescript-eslint/no-unused-vars
  userColor = '#' + Math.floor(Math.random()*16777215).toString(16) // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [canvas, setCanvas] = useState<FabricCanvas | null>(null);
  const [fabricLib, setFabricLib] = useState<FabricStatic | null>(null);
  const [currentTool, setCurrentTool] = useState<'pen' | 'eraser' | 'text' | 'select' | 'rectangle' | 'circle' | 'line'>('pen');
  const [currentColor, setCurrentColor] = useState<string>('#000000');
  const [brushSize, setBrushSize] = useState<number>(5);

  // History for undo/redo functionality
  const [history, setHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  // Real-time collaboration state
  // هذه المتغيرات ستستخدم في وظائف المزامنة المستقبلية
  const [sync, setSync] = useState<WhiteboardSync | null>(null); // eslint-disable-line @typescript-eslint/no-unused-vars
  const [isConnected, setIsConnected] = useState(false); // eslint-disable-line @typescript-eslint/no-unused-vars
  const [userCursors, setUserCursors] = useState<UserCursor[]>([]); // eslint-disable-line @typescript-eslint/no-unused-vars
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 }); // eslint-disable-line @typescript-eslint/no-unused-vars

  // دالة تستدعى عند تهيئة Canvas
  const handleCanvasReady = useCallback((fabricCanvas: FabricCanvas, lib: FabricStatic) => {
    console.log("Canvas is ready");
    setCanvas(fabricCanvas);
    setFabricLib(lib);

    // تحميل البيانات الأولية إذا كانت متوفرة
    if (initialData) {
      try {
        fabricCanvas.loadFromJSON(initialData, fabricCanvas.renderAll.bind(fabricCanvas));

        // تهيئة التاريخ
        setHistory([initialData]);
        setHistoryIndex(0);
        setCanUndo(false);
        setCanRedo(false);
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    } else {
      // إضافة الحالة الأولية للتاريخ
      const initialState = JSON.stringify(fabricCanvas.toJSON());
      setHistory([initialState]);
      setHistoryIndex(0);
      setCanUndo(false);
      setCanRedo(false);
    }

    // إعداد مستمعي الأحداث للتغييرات
    const handleChange = () => {
      if (!fabricCanvas) return;

      try {
        const jsonData = JSON.stringify(fabricCanvas.toJSON());

        // تجنب إضافة نفس الحالة مرتين
        if (history.length > 0 && history[historyIndex] === jsonData) {
          return;
        }

        // تحديث التاريخ
        if (historyIndex < history.length - 1) {
          // إذا لم نكن في نهاية التاريخ، نحذف جميع الحالات المستقبلية
          const newHistory = history.slice(0, historyIndex + 1);
          newHistory.push(jsonData);
          setHistory(newHistory);
          setHistoryIndex(historyIndex + 1);
        } else {
          // إضافة حالة جديدة إلى التاريخ
          setHistory(prevHistory => [...prevHistory, jsonData]);
          setHistoryIndex(prevIndex => prevIndex + 1);
        }

        // تحديث إمكانية التراجع/الإعادة
        setCanUndo(true);
        setCanRedo(false); // لا يمكن الإعادة بعد تغيير جديد

        // إخطار المكون الأب
        if (onDataChange) {
          onDataChange(jsonData);
        }

        // مزامنة التغييرات مع المستخدمين الآخرين إذا كانت المزامنة ممكنة
        if (enableSync && isConnected && sync) {
          sync.sendData(jsonData);
        }

        console.log(`History updated: ${historyIndex + 1}/${history.length + 1}`);
      } catch (error) {
        console.error('Error handling canvas change:', error);
      }
    };

    // إزالة مستمعي الأحداث السابقة
    fabricCanvas.off('object:added');
    fabricCanvas.off('object:modified');
    fabricCanvas.off('object:removed');

    // إضافة مستمعي الأحداث الجديدة
    fabricCanvas.on('object:added', handleChange);
    fabricCanvas.on('object:modified', handleChange);
    fabricCanvas.on('object:removed', handleChange);

  }, [initialData, enableSync, isConnected, sync, history, historyIndex, onDataChange]);

  // تحديث إعدادات الفرشاة عند تغييرها
  useEffect(() => {
    if (canvas && canvas.freeDrawingBrush) {
      canvas.freeDrawingBrush.width = brushSize;
      canvas.freeDrawingBrush.color = currentColor;
    }
  }, [canvas, brushSize, currentColor]);

  // التعامل مع تغيير الأدوات
  useEffect(() => {
    if (!canvas || !fabricLib) return;

    try {
      // التأكد من وجود فرشاة الرسم
      if (!canvas.freeDrawingBrush) {
        canvas.freeDrawingBrush = new fabricLib.PencilBrush(canvas);
      }

      // إضافة مستمعي الأحداث للأشكال
      const setupShapeListeners = () => {
        // إزالة مستمعي الأحداث السابقة
        canvas.off('mouse:down');
        canvas.off('mouse:move');
        canvas.off('mouse:up');

        let isDrawing = false;
        let startX = 0;
        let startY = 0;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let shape: any = null; // كائن من Fabric.js (يمكن أن يكون Rect أو Circle أو Line)

        // مستمع بدء الرسم
        canvas.on('mouse:down', (o: FabricMouseEvent) => {
          if (currentTool === 'rectangle' || currentTool === 'circle' || currentTool === 'line') {
            isDrawing = true;
            const pointer = canvas.getPointer(o.e);
            startX = pointer.x;
            startY = pointer.y;

            // إنشاء الشكل المناسب
            if (currentTool === 'rectangle') {
              shape = new fabricLib.Rect({
                left: startX,
                top: startY,
                width: 0,
                height: 0,
                fill: 'transparent',
                stroke: currentColor,
                strokeWidth: brushSize,
                selectable: true
              });
              canvas.add(shape);
            } else if (currentTool === 'circle') {
              shape = new fabricLib.Circle({
                left: startX,
                top: startY,
                radius: 0,
                fill: 'transparent',
                stroke: currentColor,
                strokeWidth: brushSize,
                selectable: true
              });
              canvas.add(shape);
            } else if (currentTool === 'line') {
              shape = new fabricLib.Line([startX, startY, startX, startY], {
                stroke: currentColor,
                strokeWidth: brushSize,
                selectable: true
              });
              canvas.add(shape);
            }
          }
        });

        // مستمع حركة الماوس
        canvas.on('mouse:move', (o: FabricMouseEvent) => {
          if (!isDrawing) return;

          const pointer = canvas.getPointer(o.e);

          if (currentTool === 'rectangle') {
            const width = Math.abs(pointer.x - startX);
            const height = Math.abs(pointer.y - startY);

            // تحديث موضع المستطيل
            if (pointer.x < startX) {
              shape.set({ left: pointer.x });
            }
            if (pointer.y < startY) {
              shape.set({ top: pointer.y });
            }

            shape.set({ width: width, height: height });
            canvas.renderAll();
          } else if (currentTool === 'circle') {
            // حساب نصف القطر
            const radius = Math.sqrt(
              Math.pow(pointer.x - startX, 2) + Math.pow(pointer.y - startY, 2)
            ) / 2;

            // تحديث موضع الدائرة
            const centerX = (startX + pointer.x) / 2;
            const centerY = (startY + pointer.y) / 2;

            shape.set({
              left: centerX - radius,
              top: centerY - radius,
              radius: radius
            });

            canvas.renderAll();
          } else if (currentTool === 'line') {
            // تحديث نقطة نهاية الخط
            shape.set({ x2: pointer.x, y2: pointer.y });
            canvas.renderAll();
          }
        });

        // مستمع إنهاء الرسم
        canvas.on('mouse:up', () => {
          isDrawing = false;
          shape = null;
        });
      };

      switch (currentTool) {
        case 'pen':
          canvas.isDrawingMode = true;
          canvas.freeDrawingBrush.width = brushSize;
          canvas.freeDrawingBrush.color = currentColor;
          canvas.defaultCursor = 'crosshair';
          break;
        case 'eraser':
          canvas.isDrawingMode = true;
          canvas.freeDrawingBrush.width = brushSize * 2;
          canvas.freeDrawingBrush.color = '#ffffff';
          canvas.defaultCursor = 'cell';
          break;
        case 'select':
          canvas.isDrawingMode = false;
          canvas.defaultCursor = 'default';
          break;
        case 'rectangle':
        case 'circle':
        case 'line':
          canvas.isDrawingMode = false;
          canvas.defaultCursor = 'crosshair';
          setupShapeListeners();
          break;
        default:
          canvas.isDrawingMode = false;
          canvas.defaultCursor = 'default';
          break;
      }

      // إجبار إعادة الرسم لتطبيق التغييرات
      canvas.renderAll();
    } catch (error) {
      console.error('Error changing tool:', error);
    }
  }, [canvas, fabricLib, currentTool, brushSize, currentColor]);

  // مسح السبورة
  const handleClear = useCallback(() => {
    if (!canvas) return;

    try {
      canvas.clear();
      canvas.backgroundColor = '#ffffff';
      canvas.renderAll();
    } catch (error) {
      console.error('Error clearing whiteboard:', error);
    }
  }, [canvas]);

  // إضافة نص إلى السبورة
  const handleAddText = useCallback(() => {
    if (!canvas || !fabricLib) return;

    try {
      const text = new fabricLib.IText('اكتب هنا', {
        left: 100,
        top: 100,
        fontFamily: 'Cairo',
        fill: currentColor,
        fontSize: brushSize * 4,
      });

      canvas.add(text);
      canvas.setActiveObject(text);
      canvas.renderAll();
    } catch (error) {
      console.error('Error adding text:', error);
    }
  }, [canvas, fabricLib, currentColor, brushSize]);

  // التراجع عن آخر إجراء
  const handleUndo = useCallback(() => {
    if (!canvas || !canUndo) return;

    try {
      // الرجوع خطوة واحدة في التاريخ
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);

      // تحميل الحالة السابقة
      if (newIndex >= 0) {
        canvas.loadFromJSON(history[newIndex], canvas.renderAll.bind(canvas));
      } else {
        // إذا كنا في البداية، نمسح السبورة
        canvas.clear();
        canvas.backgroundColor = '#ffffff';
        canvas.renderAll();
      }

      // تحديث إمكانية التراجع/الإعادة
      setCanUndo(newIndex > 0);
      setCanRedo(true);
    } catch (error) {
      console.error('Error during undo operation:', error);
    }
  }, [canvas, canUndo, historyIndex, history]);

  // إعادة آخر إجراء تم التراجع عنه
  const handleRedo = useCallback(() => {
    if (!canvas || !canRedo || historyIndex >= history.length - 1) return;

    try {
      // التقدم خطوة واحدة في التاريخ
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);

      // تحميل الحالة التالية
      canvas.loadFromJSON(history[newIndex], canvas.renderAll.bind(canvas));

      // تحديث إمكانية التراجع/الإعادة
      setCanUndo(true);
      setCanRedo(newIndex < history.length - 1);
    } catch (error) {
      console.error('Error during redo operation:', error);
    }
  }, [canvas, canRedo, historyIndex, history]);

  // تصدير السبورة كصورة
  const handleExport = useCallback(() => {
    if (!canvas) return;

    try {
      // إنشاء عنصر رابط مؤقت
      const link = document.createElement('a');

      // تعيين سمة التنزيل واسم الملف
      link.download = `whiteboard-${id}-${new Date().toISOString().slice(0, 10)}.png`;

      // تحويل Canvas إلى URL للبيانات وتعيينه كرابط href
      link.href = canvas.toDataURL({
        format: 'png',
        quality: 0.8
      });

      // إضافة إلى الجسم، النقر، والإزالة
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting whiteboard:', error);
    }
  }, [canvas, id]);

  // تتبع حركة الماوس لموضع المؤشر
  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current || !enableSync || !isConnected || !sync) return;

    // الحصول على الموضع بالنسبة للحاوية
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // تحديث موضع المؤشر
    setCursorPosition({ x, y });

    // إرسال موضع المؤشر إلى خدمة المزامنة
    sync.updateCursorPosition({ x, y });
  }, [enableSync, isConnected, sync]);

  return (
    <div className="flex flex-col border border-gray-300 rounded-lg overflow-hidden bg-white">
      <WhiteboardToolbar
        currentTool={currentTool}
        setCurrentTool={setCurrentTool}
        currentColor={currentColor}
        setCurrentColor={setCurrentColor}
        brushSize={brushSize}
        setBrushSize={setBrushSize}
        onClear={handleClear}
        onAddText={handleAddText}
        onUndo={handleUndo}
        onRedo={handleRedo}
        canUndo={canUndo}
        canRedo={canRedo}
        onExport={handleExport}
        readOnly={readOnly}
      />
      <div
        ref={containerRef}
        className="relative"
        onMouseMove={handleMouseMove}
      >
        <div className="canvas-container" style={{ position: 'relative', width: '100%', height: `${height}px` }}>
          <FabricCanvasComponent
            width={width}
            height={height}
            onReady={handleCanvasReady}
          />

          {/* مؤشر وضع الرسم */}
          {canvas && currentTool === 'pen' && (
            <div className="absolute top-2 right-2 px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
              وضع الرسم نشط
            </div>
          )}
        </div>

        {/* مؤشرات المستخدمين للتعاون في الوقت الفعلي */}
        {enableSync && isConnected && (
          <UserCursors
            cursors={userCursors}
            currentUserId={userId}
          />
        )}

        {/* مؤشر حالة الاتصال */}
        {enableSync && (
          <div className="absolute top-2 left-2 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1"
               style={{ backgroundColor: isConnected ? 'rgba(22, 155, 136, 0.1)' : 'rgba(239, 68, 68, 0.1)' }}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-primary-color' : 'bg-red-500'}`}></div>
            <span className={isConnected ? 'text-green-700' : 'text-red-700'}>
              {isConnected ? 'متصل' : 'غير متصل'}
            </span>
          </div>
        )}

        {readOnly && (
          <div className="absolute inset-0 bg-transparent cursor-not-allowed" />
        )}
      </div>
    </div>
  );
};

export default SimpleWhiteboard;

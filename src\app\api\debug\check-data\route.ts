import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 فحص البيانات في قاعدة البيانات...');

    // فحص الطلاب
    const studentsCount = await prisma.student.count();
    const students = await prisma.student.findMany({
      take: 5,
      include: {
        classe: true
      }
    });

    // فحص الامتحانات
    const examsCount = await prisma.exam.count();
    const exams = await prisma.exam.findMany({
      take: 5,
      include: {
        subject: true,
        examType: true
      }
    });

    // فحص نقاط الامتحانات
    const examPointsCount = await prisma.exam_points.count();
    const examPoints = await prisma.exam_points.findMany({
      take: 5,
      include: {
        student: true,
        exam: true,
        classSubject: {
          include: {
            classe: true
          }
        }
      }
    });

    // فحص الفصول
    const classesCount = await prisma.classe.count();
    const classes = await prisma.classe.findMany({
      take: 5
    });

    // فحص المواد
    const subjectsCount = await prisma.subject.count();
    const subjects = await prisma.subject.findMany({
      take: 5
    });

    // فحص علاقات الفصل بالمادة
    const classSubjectsCount = await prisma.classSubject.count();
    const classSubjects = await prisma.classSubject.findMany({
      take: 5,
      include: {
        classe: true,
        teacherSubject: {
          include: {
            subject: true,
            teacher: true
          }
        }
      }
    });

    const summary = {
      counts: {
        students: studentsCount,
        exams: examsCount,
        examPoints: examPointsCount,
        classes: classesCount,
        subjects: subjectsCount,
        classSubjects: classSubjectsCount
      },
      samples: {
        students: students.map(s => ({
          id: s.id,
          name: s.name,
          username: s.username,
          classe: s.classe?.name || 'لا يوجد'
        })),
        exams: exams.map(e => ({
          id: e.id,
          description: e.description,
          evaluationType: e.evaluationType,
          month: e.month,
          maxPoints: e.maxPoints,
          subject: e.subject?.name || 'لا يوجد'
        })),
        examPoints: examPoints.map(ep => ({
          id: ep.id,
          studentName: ep.student.name,
          examDescription: ep.exam.description,
          grade: ep.grade,
          maxPoints: ep.exam.maxPoints,
          classe: ep.classSubject?.classe?.name || 'لا يوجد'
        })),
        classes: classes.map(c => ({
          id: c.id,
          name: c.name
        })),
        subjects: subjects.map(s => ({
          id: s.id,
          name: s.name
        })),
        classSubjects: classSubjects.map(cs => ({
          id: cs.id,
          classe: cs.classe.name,
          subject: cs.teacherSubject?.subject?.name || 'لا يوجد',
          teacher: cs.teacherSubject?.teacher?.name || 'لا يوجد'
        }))
      }
    };

    console.log('📊 ملخص البيانات:', summary.counts);

    return NextResponse.json({
      success: true,
      data: summary,
      message: 'تم فحص البيانات بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في فحص البيانات:', error);
    return NextResponse.json(
      { 
        message: "حدث خطأ أثناء فحص البيانات", 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, NotificationType, UserRole } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

// POST: إنشاء إشعار جماعي
export async function POST(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم أو موظف مخول" },
                { status: 401 }
            );
        }

        const body = await request.json();

        // التحقق من البيانات المطلوبة
        if (!body.title || !body.content || !body.groupType) {
            return NextResponse.json(
                { message: "البيانات غير مكتملة" },
                { status: 400 }
            );
        }

        const { title, content, type, groupType, targetRole, targetUserIds, priority, scheduledAt } = body;

        // جلب قائمة المستلمين حسب نوع المجموعة
        let recipients: Array<{id: number}> = [];

        switch (groupType) {
            case 'ALL_USERS':
                // جلب جميع المستخدمين النشطين (باستثناء المعلقين)
                recipients = await prisma.user.findMany({
                    where: {
                        isActive: true,
                        role: { not: 'PENDING' }
                    },
                    select: { id: true }
                });
                break;

            case 'BY_ROLE':
                if (!targetRole) {
                    return NextResponse.json(
                        { message: "يجب تحديد الدور المستهدف" },
                        { status: 400 }
                    );
                }
                // جلب المستخدمين حسب الدور
                recipients = await prisma.user.findMany({
                    where: {
                        role: targetRole as UserRole,
                        isActive: true
                    },
                    select: { id: true }
                });
                break;

            case 'CUSTOM_SELECTION':
                if (!targetUserIds || !Array.isArray(targetUserIds)) {
                    return NextResponse.json(
                        { message: "يجب تحديد قائمة المستخدمين" },
                        { status: 400 }
                    );
                }
                // جلب المستخدمين المحددين
                recipients = await prisma.user.findMany({
                    where: {
                        id: { in: targetUserIds },
                        isActive: true,
                        role: { not: 'PENDING' }
                    },
                    select: { id: true }
                });
                break;

            default:
                return NextResponse.json(
                    { message: "نوع المجموعة غير صحيح" },
                    { status: 400 }
                );
        }

        if (recipients.length === 0) {
            return NextResponse.json(
                { message: "لا يوجد مستلمين للإشعار" },
                { status: 400 }
            );
        }

        // إنشاء الإشعار الرئيسي
        const notification = await prisma.notification.create({
            data: {
                title,
                content,
                type: type || 'GENERAL',
                isGroupNotification: true,
                priority: priority || 'MEDIUM',
                scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
                status: scheduledAt ? 'PENDING' : 'SENDING',
                sentAt: scheduledAt ? null : new Date(),
                // إضافة معلومات المرسل للإشعارات الجماعية
                userId: userData.id, // معرف المرسل
            }
        });

        // إنشاء سجلات المستلمين
        const recipientData = recipients.map(recipient => ({
            notificationId: notification.id,
            userId: recipient.id,
            delivered: !scheduledAt, // إذا لم يكن مجدولاً، نعتبره مُسلم
            deliveredAt: !scheduledAt ? new Date() : null,
            deliveryMethod: 'system',
            read: false, // جميع الإشعارات تبدأ كغير مقروءة
            readAt: null
        }));

        await prisma.notificationRecipient.createMany({
            data: recipientData
        });

        // إنشاء إحصائيات الإشعار
        await prisma.notificationStats.create({
            data: {
                notificationId: notification.id,
                totalRecipients: recipients.length,
                deliveredCount: !scheduledAt ? recipients.length : 0,
                pendingCount: scheduledAt ? recipients.length : 0,
                deliveryRate: !scheduledAt ? 100 : 0,
                readRate: 0,
                engagementRate: 0,
                firstDeliveredAt: !scheduledAt ? new Date() : null,
                lastDeliveredAt: !scheduledAt ? new Date() : null
            }
        });

        // إرجاع تفاصيل الإشعار مع الإحصائيات
        const notificationWithStats = await prisma.notification.findUnique({
            where: { id: notification.id },
            include: {
                stats: true,
                _count: {
                    select: { recipients: true }
                }
            }
        });

        return NextResponse.json({
            notification: notificationWithStats,
            message: scheduledAt
                ? `تم جدولة الإشعار لـ ${recipients.length} مستلم`
                : `تم إرسال الإشعار لـ ${recipients.length} مستلم بنجاح`
        }, { status: 201 });

    } catch (error) {
        console.error('Error creating bulk notification:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء إنشاء الإشعار الجماعي" },
            { status: 500 }
        );
    }
}

// GET: جلب الإشعارات الجماعية
export async function GET(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER' && userData.role !== 'EMPLOYEE')) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const { searchParams } = new URL(request.url);
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const status = searchParams.get('status');

        // بناء شروط البحث
        const where = {
            isGroupNotification: true,
            ...(status ? { status } : {})
        };

        // جلب إجمالي عدد الإشعارات الجماعية
        const total = await prisma.notification.count({ where });

        // جلب الإشعارات الجماعية مع الإحصائيات
        const notifications = await prisma.notification.findMany({
            where,
            include: {
                stats: true,
                _count: {
                    select: { recipients: true }
                }
            },
            orderBy: {
                createdAt: 'desc'
            },
            skip: (page - 1) * limit,
            take: limit
        });

        return NextResponse.json({
            notifications,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            }
        });

    } catch (error) {
        console.error('Error fetching bulk notifications:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب الإشعارات الجماعية" },
            { status: 500 }
        );
    }
}

import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Download, Printer } from 'lucide-react';
import { CertificateData } from './index';
import html2canvas from 'html2canvas';

interface ExcellenceTemplateProps {
  certificateData: CertificateData;
  showControls?: boolean;
}

export default function ExcellenceTemplate({
  certificateData,
  showControls = true
}: ExcellenceTemplateProps) {
  const certificateRef = useRef<HTMLDivElement>(null);

  const handlePrint = async () => {
    if (!certificateRef.current) return;

    try {
      // فتح نافذة جديدة للطباعة
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert('يرجى السماح بالنوافذ المنبثقة لطباعة الشهادة');
        return;
      }

      // إنشاء محتوى HTML للشهادة
      const certificateHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>شهادة تفوق</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 20px;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: #f8f9fa;
              color: #333;
            }

            .certificate-container {
              max-width: 800px;
              margin: 0 auto;
              background-color: white;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              overflow: hidden;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 8px double #ffd700;
              margin: 20px;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #fffbeb 0%, #ffffff 50%, #fff8e1 100%);
              z-index: -1;
            }

            .certificate-decoration {
              position: absolute;
              border-radius: 50%;
              opacity: 0.5;
            }

            .decoration-1 {
              top: 0;
              right: 0;
              width: 200px;
              height: 200px;
              background-color: rgba(255, 215, 0, 0.1);
              transform: translate(100px, -100px);
            }

            .decoration-2 {
              bottom: 0;
              left: 0;
              width: 200px;
              height: 200px;
              background-color: rgba(255, 215, 0, 0.1);
              transform: translate(-100px, 100px);
            }

            .certificate-header {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-icons {
              display: flex;
              justify-content: center;
              margin-bottom: 15px;
            }

            .certificate-icon {
              font-size: 30px;
              color: #ffc107;
              margin: 0 5px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 10px;
              border-bottom: 2px solid #ffeeba;
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 40px;
              margin-top: auto;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #ffeeba;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 40px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #ffd700;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #d4af37;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 20px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }

            @media print {
              body {
                margin: 0;
                padding: 0;
                background: none;
              }

              .certificate-container {
                box-shadow: none;
                max-width: 100%;
              }

              .certificate {
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="certificate-container">
            <div class="certificate">
              <div class="certificate-bg"></div>
              <div class="certificate-decoration decoration-1"></div>
              <div class="certificate-decoration decoration-2"></div>

              <!-- رأس الشهادة -->
              <div class="certificate-header">
                <div class="certificate-icons">
                  <div class="certificate-icon">💰</div>
                  <div class="certificate-icon">🏆</div>
                  <div class="certificate-icon">💰</div>
                </div>
                <h1 class="certificate-title">شهادة تفوق</h1>
                <p class="certificate-subtitle">${certificateData.title}</p>
              </div>

              <!-- محتوى الشهادة -->
              <div class="certificate-content">
                <p class="certificate-intro">تشهد إدارة المدرسة بتفوق الطالب:</p>
                <h2 class="student-name">${certificateData.student?.name || 'الطالب المتفوق'}</h2>
                ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
                <p class="certificate-description">${certificateData.description}</p>
              </div>

              <!-- توقيع الشهادة -->
              <div class="certificate-footer">
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المدير</p>
                </div>
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المعلم</p>
                </div>
              </div>

              <!-- ختم المدرسة -->
              <div class="certificate-stamp">ختم المدرسة</div>

              <!-- تاريخ الإصدار -->
              ${certificateData.issueDate ? `
              <div class="certificate-date">
                تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
              </div>
              ` : ''}
            </div>
          </div>

          <script>
            window.onload = () => {
              window.print();
              window.onafterprint = () => window.close();
            };
          </script>
        </body>
        </html>
      `;

      // كتابة المحتوى إلى النافذة الجديدة
      printWindow.document.open();
      printWindow.document.write(certificateHTML);
      printWindow.document.close();
    } catch (error) {
      console.error('Error printing certificate:', error);
      alert('حدث خطأ أثناء طباعة الشهادة');
    }
  };

  const handleDownloadImage = async () => {
    try {
      // إنشاء عنصر div مؤقت لاحتواء الشهادة
      const tempDiv = document.createElement('div');
      document.body.appendChild(tempDiv);

      // إنشاء محتوى HTML للشهادة (نفس المحتوى المستخدم في الطباعة)
      const certificateHTML = `
        <div style="width: 800px; height: 1130px; position: relative;">
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 0;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: white;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 8px double #ffd700;
              margin: 20px;
              background-color: white;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #fffbeb 0%, #ffffff 50%, #fff8e1 100%);
              z-index: -1;
            }

            .certificate-decoration {
              position: absolute;
              border-radius: 50%;
              opacity: 0.5;
            }

            .decoration-1 {
              top: 0;
              right: 0;
              width: 200px;
              height: 200px;
              background-color: rgba(255, 215, 0, 0.1);
              transform: translate(100px, -100px);
            }

            .decoration-2 {
              bottom: 0;
              left: 0;
              width: 200px;
              height: 200px;
              background-color: rgba(255, 215, 0, 0.1);
              transform: translate(-100px, 100px);
            }

            .certificate-header {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-icons {
              display: flex;
              justify-content: center;
              margin-bottom: 15px;
            }

            .certificate-icon {
              font-size: 30px;
              color: #ffc107;
              margin: 0 5px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 10px;
              border-bottom: 2px solid #ffeeba;
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 40px;
              margin-top: auto;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #ffeeba;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 40px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #ffd700;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #d4af37;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 20px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }
          </style>

          <div class="certificate">
            <div class="certificate-bg"></div>
            <div class="certificate-decoration decoration-1"></div>
            <div class="certificate-decoration decoration-2"></div>

            <!-- رأس الشهادة -->
            <div class="certificate-header">
              <div class="certificate-icons">
                <div class="certificate-icon">💰</div>
                <div class="certificate-icon">🏆</div>
                <div class="certificate-icon">💰</div>
              </div>
              <h1 class="certificate-title">شهادة تفوق</h1>
              <p class="certificate-subtitle">${certificateData.title}</p>
            </div>

            <!-- محتوى الشهادة -->
            <div class="certificate-content">
              <p class="certificate-intro">تشهد إدارة المدرسة بتفوق الطالب:</p>
              <h2 class="student-name">${certificateData.student?.name || 'الطالب المتفوق'}</h2>
              ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
              <p class="certificate-description">${certificateData.description}</p>
            </div>

            <!-- توقيع الشهادة -->
            <div class="certificate-footer">
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المدير</p>
              </div>
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المعلم</p>
              </div>
            </div>

            <!-- ختم المدرسة -->
            <div class="certificate-stamp">ختم المدرسة</div>

            <!-- تاريخ الإصدار -->
            ${certificateData.issueDate ? `
            <div class="certificate-date">
              تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
            </div>
            ` : ''}
          </div>
        </div>
      `;

      // إضافة المحتوى إلى العنصر المؤقت
      tempDiv.innerHTML = certificateHTML;

      // انتظار تحميل الخطوط والصور
      await new Promise(resolve => setTimeout(resolve, 500));

      // تحويل العنصر إلى صورة باستخدام html2canvas
      const canvas = await html2canvas(tempDiv.firstElementChild as HTMLElement, {
        scale: 3, // زيادة الدقة للحصول على صورة أوضح
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
        allowTaint: true,
        imageTimeout: 5000
      });

      // إزالة العنصر المؤقت
      document.body.removeChild(tempDiv);

      // تحويل الصورة إلى PNG بجودة عالية
      const image = canvas.toDataURL('image/png', 1.0);

      // إنشاء رابط تنزيل وتنفيذه
      const link = document.createElement('a');
      link.href = image;
      link.download = `شهادة_تفوق_${certificateData.student?.name || 'تقدير'}_${new Date().toLocaleDateString('fr-FR')}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error generating certificate image:', error);
      alert('حدث خطأ أثناء تحميل الشهادة كصورة');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <div className="space-y-4">
      <div ref={certificateRef} className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* الشهادة */}
        <div className="relative" data-certificate="excellence">
          {/* خلفية الشهادة */}
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-50 via-white to-amber-50" />

          {/* زخارف الشهادة */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-yellow-100 rounded-full opacity-50" />
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-yellow-100 rounded-full opacity-50" />

          {/* إطار الشهادة */}
          <div className="relative z-10 p-8 border-8 border-double border-amber-200 m-8 min-h-[600px]">
            <div className="flex flex-col items-center justify-center h-full text-center">
              {/* رأس الشهادة */}
              <div className="mb-8">
                <div className="flex justify-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-20 w-20 text-amber-500 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-amber-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h1 className="text-4xl font-bold text-amber-600 mb-2" data-arabic-text="true">شهادة تفوق</h1>
                <p className="text-xl text-gray-600" data-arabic-text="true">{certificateData.title}</p>
              </div>

              {/* محتوى الشهادة */}
              <div className="mb-8 max-w-2xl">
                <p className="text-lg mb-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">تشهد إدارة المدرسة بتفوق الطالب:</p>
                <h2 className="text-3xl font-bold text-amber-600 mb-4 border-b-2 border-amber-200 pb-2 inline-block" data-arabic-text="true">
                  {certificateData.student?.name || 'الطالب المتفوق'}
                </h2>
                {certificateData.student?.classe && (
                  <p className="text-xl text-gray-600 mb-6" data-arabic-text="true">
                    {certificateData.student.classe.name}
                  </p>
                )}
                <p className="text-lg my-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">{certificateData.description}</p>
              </div>

              {/* توقيع الشهادة */}
              <div className="mt-auto grid grid-cols-2 gap-12 w-full">
                <div className="text-center">
                  <div className="h-16 border-b border-amber-200 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المدير</p>
                </div>
                <div className="text-center">
                  <div className="h-16 border-b border-amber-200 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المعلم</p>
                </div>
              </div>

              {/* تاريخ الإصدار */}
              {certificateData.issueDate && (
                <div className="mt-8 text-sm text-gray-500" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">
                  تم إصدار هذه الشهادة بتاريخ: {formatDate(certificateData.issueDate)}
                </div>
              )}

              {/* ختم المدرسة */}
              <div className="absolute bottom-8 left-8 w-24 h-24 border-2 border-dashed border-amber-300 rounded-full flex items-center justify-center text-amber-400" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">
                ختم المدرسة
              </div>
            </div>
          </div>
        </div>
      </div>

      {showControls && (
        <Card className="p-4 flex justify-center space-x-4 space-x-reverse">
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            <span>طباعة</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={handleDownloadImage}
          >
            <Download className="h-4 w-4" />
            <span>تحميل</span>
          </Button>
        </Card>
      )}
    </div>
  );
}

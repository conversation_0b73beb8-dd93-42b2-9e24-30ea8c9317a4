"use client";
import React, { useState, useEffect } from 'react';
import { FaUsers, FaChalkboardTeacher, FaSchool, FaMoneyBillWave, FaCalendarCheck, FaSync,
  FaSignInAlt, FaSignOutAlt, FaUserPlus, FaEdit, FaGraduationCap, FaBookOpen, FaInfoCircle, FaUserGraduate, FaHandHoldingHeart } from 'react-icons/fa';
import Link from 'next/link';
import { toast } from 'react-toastify';
import { Button } from '@/components/ui/button';

// Import dialogs
import AddStudentDialog from './components/AddStudentDialog';
import AddTeacherDialog from './components/AddTeacherDialog';
import AttendanceDialog from './components/AttendanceDialog';
import PaymentDialog from './components/PaymentDialog';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';

interface AdminStats {
  students: number;
  teachers: number;
  classes: number;
  attendance: number;
  payments: number;
  donations: number;
  recentActivities?: Array<{
    id: number;
    type: string;
    description: string;
    date: string;
    user: string;
  }>;
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<AdminStats>({
    students: 0,
    teachers: 0,
    classes: 0,
    attendance: 0,
    payments: 0,
    donations: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dialog states
  const [isAddStudentDialogOpen, setIsAddStudentDialogOpen] = useState(false);
  const [isAddTeacherDialogOpen, setIsAddTeacherDialogOpen] = useState(false);
  const [isAttendanceDialogOpen, setIsAttendanceDialogOpen] = useState(false);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);

  // جلب الإحصائيات من الخادم
  const fetchStats = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch('/api/admin/stats');

      if (!response.ok) {
        throw new Error('فشل في جلب البيانات الإحصائية');
      }

      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Error fetching stats:', error);
      setError('حدث خطأ أثناء جلب البيانات. يرجى المحاولة مرة أخرى.');
      toast.error('فشل في جلب البيانات الإحصائية');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const dashboardCards = [
    {
      title: 'التلاميذ',
      count: isLoading ? '...' : stats.students,
      icon: <FaUsers />,
      link: '/admin/students'
    },
    {
      title: 'المعلمون',
      count: isLoading ? '...' : stats.teachers,
      icon: <FaChalkboardTeacher />,
      link: '/admin/teachers'
    },
    {
      title: 'الفصول',
      count: isLoading ? '...' : stats.classes,
      icon: <FaSchool />,
      link: '/admin/classes'
    },
    {
      title: 'نسبة الحضور',
      count: isLoading ? '...' : `${stats.attendance}%`,
      icon: <FaCalendarCheck />,
      link: '/admin/attendance'
    },
    {
      title: 'المدفوعات',
      count: isLoading ? '...' : `${stats.payments} د.ج`,
      icon: <FaMoneyBillWave />,
      link: '/admin/treasury'
    },
    {
      title: 'التبرعات',
      count: isLoading ? '...' : `${stats.donations} د.ج`,
      icon: <FaHandHoldingHeart />,
      link: '/admin/donations'
    }
  ];

  return (
    <ProtectedRoute requiredPermission="admin.dashboard.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6" dir="rtl">
      {/* Welcome Section */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaUsers className="text-[var(--primary-color)]" />
          لوحة التحكم الرئيسية
        </h1>
        <Button
          onClick={fetchStats}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2"
          disabled={isLoading}
          title="تحديث البيانات"
        >
          <FaSync className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold text-[var(--primary-color)] mb-2">مرحباً بك في لوحة التحكم</h2>
            <p className="text-gray-600">هنا يمكنك إدارة جميع جوانب المؤسسة التعليمية</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
        {dashboardCards.map((card) => (
          <Link href={card.link} key={card.title}>
            <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-[var(--primary-color)] text-sm font-medium">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">{card.count}</p>
                </div>
                <div className="bg-[#e9f7f5] text-[var(--primary-color)] w-12 h-12 rounded-full flex items-center justify-center text-2xl">
                  {card.icon}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-[var(--primary-color)] flex items-center gap-2">
            <FaCalendarCheck className="text-[var(--primary-color)]" />
            النشاطات الأخيرة
          </h3>
          {/* <Button
            onClick={async () => {
              try {
                const response = await fetch('/api/admin/seed-activities', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                  const data = await response.json();
                  toast.success(data.message);
                  fetchStats(); // إعادة تحميل الإحصائيات بعد إنشاء النشاطات
                } else {
                  const errorData = await response.json();
                  toast.error(errorData.error || 'حدث خطأ أثناء إنشاء النشاطات');
                }
              } catch (error) {
                console.error('Error creating test activities:', error);
                toast.error('حدث خطأ أثناء إنشاء النشاطات');
              }
            }}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white text-xs shadow-md hover:shadow-lg transition-all duration-300"
            title="إنشاء نشاطات تجريبية"
          >
            إنشاء نشاطات تجريبية
          </Button> */}
        </div>
        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center text-center py-8">
            <div className="text-red-500 mb-4 bg-red-50 p-4 rounded-md border border-red-100">{error}</div>
            <Button
              onClick={fetchStats}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300"
            >
              إعادة المحاولة
            </Button>
          </div>
        ) : stats.recentActivities && stats.recentActivities.length > 0 ? (
          <>
            <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2 mb-4 custom-scrollbar border border-[#e0f2ef] rounded-md p-2 bg-[#f8fffd]">
              {stats.recentActivities.map((activity) => {
                // تحديد لون وأيقونة النشاط بناءً على نوعه
                const activityStyles = {
                  'LOGIN': { border: 'border-blue-500', icon: <FaSignInAlt className="text-blue-500" /> },
                  'LOGOUT': { border: 'border-purple-500', icon: <FaSignOutAlt className="text-purple-500" /> },
                  'REGISTRATION': { border: 'border-primary-color', icon: <FaUserPlus className="text-primary-color" /> },
                  'PAYMENT': { border: 'border-yellow-500', icon: <FaMoneyBillWave className="text-yellow-500" /> },
                  'ATTENDANCE': { border: 'border-red-500', icon: <FaCalendarCheck className="text-red-500" /> },
                  'UPDATE': { border: 'border-blue-500', icon: <FaEdit className="text-blue-500" /> },
                  'EXAM': { border: 'border-orange-500', icon: <FaGraduationCap className="text-orange-500" /> },
                  'KHATM': { border: 'border-teal-500', icon: <FaBookOpen className="text-primary-color" /> },
                  'STUDENT_ADD': { border: 'border-primary-color', icon: <FaUserPlus className="text-primary-color" /> }
                };

                const activityStyle = activityStyles[activity.type as keyof typeof activityStyles] ||
                  { border: 'border-gray-500', icon: <FaInfoCircle className="text-gray-500" /> };

                const borderColor = activityStyle.border;

                // تنسيق التاريخ
                let formattedDate = '';
                try {
                  const date = new Date(activity.date);
                  formattedDate = new Intl.DateTimeFormat('ar-EG', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  }).format(date);
                } catch (e) {
                  console.error('Error formatting date:', e);
                  formattedDate = 'تاريخ غير صالح';
                }

                return (
                  <div key={activity.id} className={`border-r-4 ${borderColor} pr-4 flex items-start gap-3 p-2 hover:bg-gray-50 rounded-lg`}>
                    <div className="mt-1">{activityStyle.icon}</div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">{formattedDate}</p>
                      <p className="text-gray-800 font-medium">{activity.description}</p>
                      <p className="text-xs text-gray-600">بواسطة: {activity.user}</p>
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="text-center">
              <Link href="/admin/activities">
                <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300">
                  عرض جميع النشاطات
                </Button>
              </Link>
            </div>
          </>
        ) : (
          <div className="text-center text-gray-500 py-8 border border-dashed border-gray-300 rounded-lg">
            <FaInfoCircle className="mx-auto text-gray-400 text-2xl mb-2" />
            <p className="mb-2">لا توجد نشاطات حديثة</p>
            <p className="text-xs text-gray-400">ستظهر هنا النشاطات مثل تسجيل الدخول، إضافة طالب، تسجيل دفعة، وغيرها</p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="bg-white p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
        <h3 className="text-xl font-semibold text-[var(--primary-color)] mb-4 flex items-center gap-2">
          <FaUserPlus className="text-[var(--primary-color)]" />
          إجراءات سريعة
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <PermissionGuard requiredPermission="admin.students.create">
            <button
              onClick={() => setIsAddStudentDialogOpen(true)}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
            >
              <FaUserGraduate className="ml-1" />
              إضافة طالب جديد
            </button>
          </PermissionGuard>
          <PermissionGuard requiredPermission="admin.teachers.create">
            <button
              onClick={() => setIsAddTeacherDialogOpen(true)}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
            >
              <FaChalkboardTeacher className="ml-1" />
              إضافة معلم جديد
            </button>
          </PermissionGuard>
          <PermissionGuard requiredPermission="admin.attendance.create">
            <button
              onClick={() => setIsAttendanceDialogOpen(true)}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
            >
              <FaCalendarCheck className="ml-1" />
              تسجيل الحضور
            </button>
          </PermissionGuard>
          <PermissionGuard requiredPermission="admin.payments.create">
            <button
              onClick={() => setIsPaymentDialogOpen(true)}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
            >
              <FaMoneyBillWave className="ml-1" />
              تسجيل دفعة
            </button>
          </PermissionGuard>
          <PermissionGuard requiredPermission="admin.donations.view">
            <Link href="/admin/donations">
              <button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white p-4 rounded-lg text-center shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2 w-full">
                <FaHandHoldingHeart className="ml-1" />
                إدارة التبرعات
              </button>
            </Link>
          </PermissionGuard>
        </div>
      </div>

      {/* Dialogs */}
      <AddStudentDialog
        isOpen={isAddStudentDialogOpen}
        onCloseAction={() => setIsAddStudentDialogOpen(false)}
        onSuccessAction={() => {
          fetchStats();
          setIsAddStudentDialogOpen(false);
        }}
      />
      <AddTeacherDialog
        isOpen={isAddTeacherDialogOpen}
        onCloseAction={() => setIsAddTeacherDialogOpen(false)}
        onSuccessAction={() => {
          fetchStats();
          setIsAddTeacherDialogOpen(false);
        }}
      />
      <AttendanceDialog
        isOpen={isAttendanceDialogOpen}
        onCloseAction={() => setIsAttendanceDialogOpen(false)}
        onSuccessAction={() => {
          fetchStats();
          setIsAttendanceDialogOpen(false);
        }}
      />
      <PaymentDialog
        isOpen={isPaymentDialogOpen}
        onCloseAction={() => setIsPaymentDialogOpen(false)}
        onSuccessAction={() => {
          fetchStats();
          setIsPaymentDialogOpen(false);
        }}
      />
      </div>
    </ProtectedRoute>
  );
};

export default AdminDashboard;
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// PUT: تحديث دور المستخدم
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const userId = parseInt(resolvedParams.id);

    // التحقق من صحة معرف المستخدم
    if (isNaN(userId) || userId <= 0) {
      return NextResponse.json(
        { message: "معرف المستخدم غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.users.edit');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const userData = permissionCheck.userData!;

    const body = await request.json();
    const { roleId, baseRole: providedBaseRole } = body;

    console.log('Updating user role:', { userId, roleId, providedBaseRole, requestedBy: userData.id });

    // التحقق من البيانات المطلوبة
    if (!roleId) {
      return NextResponse.json(
        { message: "معرف الدور مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        userRole: true
      }
    });

    if (!existingUser) {
      return NextResponse.json(
        { message: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    // جلب جميع الأدوار من قاعدة البيانات لفهم النظام الحالي
    console.log('Fetching all roles from database...');
    const allRoles = await prisma.role.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    });

    // التحقق من وجود الدور المحدد
    const role = allRoles.find(r => r.id === parseInt(roleId));

    console.log('Role found:', role);
    console.log('Available roles:', allRoles.map(r => ({ id: r.id, name: r.name, displayName: r.displayName })));

    if (!role || !role.isActive) {
      console.log('Role not found or inactive:', { role, isActive: role?.isActive });
      return NextResponse.json(
        { message: "الدور غير موجود أو غير نشط" },
        { status: 404 }
      );
    }

    // تحديد الدور الأساسي بناءً على اسم الدور المخصص
    let baseRole: 'ADMIN' | 'EMPLOYEE' | 'TEACHER' | 'STUDENT' | 'PARENT' | 'PENDING';

    // إذا تم تقديم دور أساسي صريح، استخدمه
    if (providedBaseRole && ['ADMIN', 'EMPLOYEE', 'TEACHER', 'STUDENT', 'PARENT', 'PENDING'].includes(providedBaseRole)) {
      baseRole = providedBaseRole as 'ADMIN' | 'EMPLOYEE' | 'TEACHER' | 'STUDENT' | 'PARENT' | 'PENDING';
      console.log('Using provided base role:', baseRole);
    } else {
      // تحسين منطق تحديد الدور الأساسي تلقائياً بناءً على الأدوار الموجودة في قاعدة البيانات
      const roleName = role.name.toUpperCase();
      const roleDisplayName = role.displayName.toUpperCase();
      const roleDescription = (role.description || '').toUpperCase();

      // البحث في اسم الدور والاسم المعروض والوصف
      const searchText = `${roleName} ${roleDisplayName} ${roleDescription}`;

      // تصنيف الأدوار الموجودة في قاعدة البيانات
      const adminRoles = allRoles.filter(r => {
        const rName = r.name.toUpperCase();
        const rDisplay = r.displayName.toUpperCase();
        const rDesc = (r.description || '').toUpperCase();
        return rName.includes('ADMIN') || rDisplay.includes('مدير') || rDisplay.includes('إدار') || rDesc.includes('مدير') || rDesc.includes('إدار');
      });

      const teacherRoles = allRoles.filter(r => {
        const rName = r.name.toUpperCase();
        const rDisplay = r.displayName.toUpperCase();
        const rDesc = (r.description || '').toUpperCase();
        return rName.includes('TEACHER') || rDisplay.includes('معلم') || rDisplay.includes('مدرس') || rDisplay.includes('أستاذ') || rDesc.includes('معلم') || rDesc.includes('مدرس') || rDesc.includes('أستاذ');
      });

      const studentRoles = allRoles.filter(r => {
        const rName = r.name.toUpperCase();
        const rDisplay = r.displayName.toUpperCase();
        const rDesc = (r.description || '').toUpperCase();
        return rName.includes('STUDENT') || rDisplay.includes('طالب') || rDisplay.includes('تلميذ') || rDesc.includes('طالب') || rDesc.includes('تلميذ');
      });

      const parentRoles = allRoles.filter(r => {
        const rName = r.name.toUpperCase();
        const rDisplay = r.displayName.toUpperCase();
        const rDesc = (r.description || '').toUpperCase();
        return rName.includes('PARENT') || rDisplay.includes('ولي') || rDisplay.includes('والد') || rDisplay.includes('أمر') || rDesc.includes('ولي') || rDesc.includes('والد') || rDesc.includes('أمر');
      });

      const employeeRoles = allRoles.filter(r => {
        const rName = r.name.toUpperCase();
        const rDisplay = r.displayName.toUpperCase();
        const rDesc = (r.description || '').toUpperCase();
        return rName.includes('EMPLOYEE') || rDisplay.includes('موظف') || rDisplay.includes('عامل') || rDisplay.includes('مساعد') || rDesc.includes('موظف') || rDesc.includes('عامل') || rDesc.includes('مساعد');
      });

      console.log('Role categorization:', {
        adminRoles: adminRoles.map(r => r.displayName),
        teacherRoles: teacherRoles.map(r => r.displayName),
        studentRoles: studentRoles.map(r => r.displayName),
        parentRoles: parentRoles.map(r => r.displayName),
        employeeRoles: employeeRoles.map(r => r.displayName),
        currentRole: role.displayName
      });

      // تحديد الدور الأساسي بناءً على التطابق مع الأدوار الموجودة
      if (adminRoles.some(r => r.id === role.id) || roleName === 'ADMIN' || searchText.includes('ADMIN') || searchText.includes('مدير') || searchText.includes('إدار')) {
        baseRole = 'ADMIN';
      } else if (teacherRoles.some(r => r.id === role.id) || roleName === 'TEACHER' || searchText.includes('TEACHER') || searchText.includes('معلم') || searchText.includes('مدرس') || searchText.includes('أستاذ')) {
        baseRole = 'TEACHER';
      } else if (studentRoles.some(r => r.id === role.id) || roleName === 'STUDENT' || searchText.includes('STUDENT') || searchText.includes('طالب') || searchText.includes('تلميذ')) {
        baseRole = 'STUDENT';
      } else if (parentRoles.some(r => r.id === role.id) || roleName === 'PARENT' || searchText.includes('PARENT') || searchText.includes('ولي') || searchText.includes('والد') || searchText.includes('أمر')) {
        baseRole = 'PARENT';
      } else if (employeeRoles.some(r => r.id === role.id) || roleName === 'EMPLOYEE' || searchText.includes('EMPLOYEE') || searchText.includes('موظف') || searchText.includes('عامل') || searchText.includes('مساعد')) {
        baseRole = 'EMPLOYEE';
      } else {
        // للأدوار المخصصة التي لا تتطابق مع أي فئة، نتركها كموظف افتراضياً
        console.log('No category match found for role:', role.displayName, 'defaulting to EMPLOYEE');
        baseRole = 'EMPLOYEE';
      }
    }

    console.log('Base role determined:', { roleName: role.name, baseRole });

    // منع المستخدم من تغيير دوره الخاص
    if (userId === userData.id) {
      return NextResponse.json(
        { message: "لا يمكنك تغيير دورك الخاص" },
        { status: 400 }
      );
    }

    // تحديث دور المستخدم
    console.log('Updating user in database...');
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        roleId: parseInt(roleId),
        // تحديث الدور الأساسي للتوافق مع النظام الحالي
        role: baseRole
      },
      include: {
        profile: true,
        userRole: true
      }
    });

    console.log('User updated successfully:', {
      userId: updatedUser.id,
      newRoleId: updatedUser.roleId,
      newRole: updatedUser.role
    });

    return NextResponse.json({
      user: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        role: updatedUser.role,
        roleId: updatedUser.roleId,
        roleName: role.displayName,
        profile: updatedUser.profile,
        userRole: updatedUser.userRole
      },
      message: "تم تحديث دور المستخدم بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error updating user role:', error);

    // معالجة أخطاء Prisma المحددة
    if (error instanceof Error) {
      console.error('Error details:', error.message);

      if (error.message.includes('Foreign key constraint')) {
        return NextResponse.json(
          { message: "الدور المحدد غير صالح" },
          { status: 400 }
        );
      }

      if (error.message.includes('Record to update not found')) {
        return NextResponse.json(
          { message: "المستخدم غير موجود" },
          { status: 404 }
        );
      }

      if (error.message.includes('Unique constraint')) {
        return NextResponse.json(
          { message: "تضارب في البيانات" },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      {
        message: "حدث خطأ أثناء تحديث دور المستخدم",
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

// GET: جلب دور المستخدم الحالي
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const userId = parseInt(resolvedParams.id);

    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.users.view');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    // التحقق من وجود المستخدم
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        userRole: {
          include: {
            permissions: {
              include: {
                permission: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { message: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        roleId: user.roleId,
        profile: user.profile,
        userRole: user.userRole,
        permissions: user.userRole?.permissions.map(rp => rp.permission) || []
      },
      message: "تم جلب معلومات المستخدم بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching user role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب معلومات المستخدم" },
      { status: 500 }
    );
  }
}

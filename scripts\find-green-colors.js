#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// ألوان للطباعة الملونة في الكونسول
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// قائمة الألوان الخضراء المراد البحث عنها
const greenColors = [
  '#169b88',
  '#1ab19c',
  'rgb(22, 155, 136)',
  'rgb(26, 177, 156)',
  'bg-green-',
  'text-green-',
  'border-green-',
  'bg-emerald-',
  'text-emerald-',
  'border-emerald-',
  'bg-teal-',
  'text-teal-',
  'border-teal-',
];

// قائمة المجلدات المراد البحث فيها
const searchDirs = [
  'src/components',
  'src/app',
  'src/pages',
  'src/styles',
];

// قائمة امتدادات الملفات المراد البحث فيها
const fileExtensions = ['.tsx', '.ts', '.jsx', '.js', '.css', '.scss'];

let totalFiles = 0;
let filesWithIssues = 0;
let totalIssues = 0;

function searchInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const issues = [];

    lines.forEach((line, index) => {
      greenColors.forEach(color => {
        if (line.includes(color)) {
          issues.push({
            line: index + 1,
            content: line.trim(),
            color: color
          });
        }
      });
    });

    return issues;
  } catch (error) {
    console.error(`${colors.red}خطأ في قراءة الملف ${filePath}: ${error.message}${colors.reset}`);
    return [];
  }
}

function searchInDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    console.log(`${colors.yellow}المجلد غير موجود: ${dirPath}${colors.reset}`);
    return;
  }

  const items = fs.readdirSync(dirPath);

  items.forEach(item => {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);

    if (stat.isDirectory()) {
      // تجاهل مجلدات معينة
      if (!['node_modules', '.git', '.next', 'dist', 'build'].includes(item)) {
        searchInDirectory(itemPath);
      }
    } else if (stat.isFile()) {
      const ext = path.extname(item);
      if (fileExtensions.includes(ext)) {
        totalFiles++;
        const issues = searchInFile(itemPath);
        
        if (issues.length > 0) {
          filesWithIssues++;
          totalIssues += issues.length;
          
          console.log(`\n${colors.bright}${colors.blue}📁 ${itemPath}${colors.reset}`);
          issues.forEach(issue => {
            console.log(`  ${colors.red}⚠️  السطر ${issue.line}:${colors.reset} ${colors.yellow}${issue.color}${colors.reset}`);
            console.log(`     ${colors.cyan}${issue.content}${colors.reset}`);
          });
        }
      }
    }
  });
}

function generateReport() {
  console.log(`\n${colors.bright}${colors.green}📊 تقرير البحث عن الألوان الخضراء${colors.reset}`);
  console.log(`${colors.bright}${'='.repeat(50)}${colors.reset}`);
  console.log(`📁 إجمالي الملفات المفحوصة: ${colors.bright}${totalFiles}${colors.reset}`);
  console.log(`⚠️  الملفات التي تحتوي على مشاكل: ${colors.bright}${colors.red}${filesWithIssues}${colors.reset}`);
  console.log(`🔍 إجمالي المشاكل المكتشفة: ${colors.bright}${colors.red}${totalIssues}${colors.reset}`);
  
  if (totalIssues > 0) {
    console.log(`\n${colors.bright}${colors.yellow}💡 اقتراحات للإصلاح:${colors.reset}`);
    console.log(`   • استبدل ${colors.red}bg-green-*${colors.reset} بـ ${colors.green}bg-primary-color${colors.reset}`);
    console.log(`   • استبدل ${colors.red}text-green-*${colors.reset} بـ ${colors.green}text-primary-color${colors.reset}`);
    console.log(`   • استبدل ${colors.red}#169b88${colors.reset} بـ ${colors.green}var(--primary-color)${colors.reset}`);
    console.log(`   • استبدل ${colors.red}#1ab19c${colors.reset} بـ ${colors.green}var(--secondary-color)${colors.reset}`);
    console.log(`   • استخدم لوحة تحكم الألوان في صفحة الإعدادات للإصلاح التلقائي`);
  } else {
    console.log(`\n${colors.bright}${colors.green}✅ ممتاز! لم يتم العثور على أي ألوان خضراء ثابتة${colors.reset}`);
  }
}

function main() {
  console.log(`${colors.bright}${colors.cyan}🔍 البحث عن الألوان الخضراء الثابتة في المشروع...${colors.reset}\n`);
  
  searchDirs.forEach(dir => {
    console.log(`${colors.bright}البحث في: ${dir}${colors.reset}`);
    searchInDirectory(dir);
  });
  
  generateReport();
}

// تشغيل الأداة
main();

'use client';
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';

interface Permission {
  id: number;
  key: string;
  name: string;
  category: string;
  isActive: boolean;
}

interface UserData {
  id: number;
  role: string;
  roleId?: number;
}

interface PermissionsContextType {
  permissions: Permission[];
  userRole: string | null;
  userId: number | null;
  loading: boolean;
  error: string | null;
  hasPermission: (permissionKey: string) => boolean;
  hasAnyPermission: (permissionKeys: string[]) => boolean;
  hasAllPermissions: (permissionKeys: string[]) => boolean;
  refreshPermissions: () => Promise<void>;
  isReady: boolean;
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

// Cache management
const CACHE_KEY_PREFIX = 'user_permissions_';
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

interface CachedPermissions {
  permissions: Permission[];
  userRole: string;
  userId: number;
  timestamp: number;
  roleId?: number;
  roleName?: string;
}

const getCacheKey = (userId: number) => `${CACHE_KEY_PREFIX}${userId}`;

const saveToCache = (userId: number, data: Omit<CachedPermissions, 'timestamp'>) => {
  try {
    const cacheData: CachedPermissions = {
      ...data,
      timestamp: Date.now()
    };
    localStorage.setItem(getCacheKey(userId), JSON.stringify(cacheData));
    return true;
  } catch (error) {
    console.warn('Failed to save permissions to cache:', error);
    return false;
  }
};

const getFromCache = (userId: number): CachedPermissions | null => {
  try {
    const cached = localStorage.getItem(getCacheKey(userId));
    if (!cached) return null;
    
    const data: CachedPermissions = JSON.parse(cached);
    
    // Check if cache is still valid
    if (Date.now() - data.timestamp > CACHE_DURATION) {
      localStorage.removeItem(getCacheKey(userId));
      return null;
    }
    
    return data;
  } catch (error) {
    console.warn('Failed to get permissions from cache:', error);
    return null;
  }
};

const clearCache = (userId: number) => {
  try {
    localStorage.removeItem(getCacheKey(userId));
  } catch (error) {
    console.warn('Failed to clear permissions cache:', error);
  }
};

export const PermissionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [userId, setUserId] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);

  // Optimized permission checking functions
  const hasPermission = useCallback((permissionKey: string): boolean => {
    if (userRole === 'ADMIN') return true;
    return permissions.some(permission => permission.key === permissionKey);
  }, [permissions, userRole]);

  const hasAnyPermission = useCallback((permissionKeys: string[]): boolean => {
    if (userRole === 'ADMIN') return true;
    return permissionKeys.some(key => permissions.some(permission => permission.key === key));
  }, [permissions, userRole]);

  const hasAllPermissions = useCallback((permissionKeys: string[]): boolean => {
    if (userRole === 'ADMIN') return true;
    return permissionKeys.every(key => permissions.some(permission => permission.key === key));
  }, [permissions, userRole]);

  const fetchPermissions = useCallback(async (userData: UserData) => {
    try {
      const response = await fetch('/api/auth/permissions');
      if (!response.ok) {
        throw new Error(`Failed to fetch permissions: ${response.status}`);
      }

      const data = await response.json();
      const fetchedPermissions = data.permissions || [];

      // Save to cache
      saveToCache(userData.id, {
        permissions: fetchedPermissions,
        userRole: userData.role,
        userId: userData.id,
        roleId: userData.roleId,
        roleName: data.roleName
      });

      return fetchedPermissions;
    } catch (error) {
      console.error('Error fetching permissions:', error);
      throw error;
    }
  }, []);

  const refreshPermissions = useCallback(async () => {
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      // Clear cache
      clearCache(userId);

      // Fetch user data
      const userResponse = await fetch('/api/auth/me');
      if (!userResponse.ok) {
        throw new Error('Failed to fetch user data');
      }

      const userData: UserData = await userResponse.json();
      const fetchedPermissions = await fetchPermissions(userData);
      
      setPermissions(fetchedPermissions);
      setUserRole(userData.role);
      setUserId(userData.id);
    } catch (error) {
      console.error('Error refreshing permissions:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [userId, fetchPermissions]);

  useEffect(() => {
    const initializePermissions = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch user data first
        const userResponse = await fetch('/api/auth/me');
        
        if (!userResponse.ok) {
          if (userResponse.status === 401) {
            // User not authenticated
            setPermissions([]);
            setUserRole(null);
            setUserId(null);
            setIsReady(true);
            return;
          }
          throw new Error(`Failed to fetch user data: ${userResponse.status}`);
        }

        const userData: UserData = await userResponse.json();
        setUserRole(userData.role);
        setUserId(userData.id);

        // Check cache first
        const cachedData = getFromCache(userData.id);
        if (cachedData && cachedData.userRole === userData.role) {
          console.log('Using cached permissions');
          setPermissions(cachedData.permissions);
          setIsReady(true);
          return;
        }

        // Fetch from server if no valid cache
        const fetchedPermissions = await fetchPermissions(userData);
        setPermissions(fetchedPermissions);
        setIsReady(true);

      } catch (error) {
        console.error('Error initializing permissions:', error);
        setError(error instanceof Error ? error.message : 'Unknown error');
        setPermissions([]);
        setIsReady(true);
      } finally {
        setLoading(false);
      }
    };

    initializePermissions();
  }, [fetchPermissions]);

  const contextValue: PermissionsContextType = {
    permissions,
    userRole,
    userId,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    refreshPermissions,
    isReady
  };

  return (
    <PermissionsContext.Provider value={contextValue}>
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = (): PermissionsContextType => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};

export default PermissionsContext;

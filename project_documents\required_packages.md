# المكتبات المطلوبة لميزات التصدير والطباعة

## نظرة عامة
هذا المستند يحدد جميع المكتبات والحزم المطلوبة لتنفيذ ميزات التصدير والطباعة في نظام تقارير المشرف.

## مكتبات التصدير الأساسية

### تصدير PDF
```json
{
  "jspdf": "^2.5.1",
  "html2canvas": "^1.4.1"
}
```

**الوصف:**
- `jspdf`: مكتبة لإنشاء ملفات PDF من JavaScript
- `html2canvas`: تحويل عناصر HTML إلى صور لإدراجها في PDF

**الاستخدام:**
```javascript
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
```

### تصدير Excel
```json
{
  "xlsx": "^0.18.5",
  "file-saver": "^2.0.5"
}
```

**الوصف:**
- `xlsx`: مكتبة شاملة للتعامل مع ملفات Excel
- `file-saver`: حفظ الملفات في المتصفح

**الاستخدام:**
```javascript
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
```

### تصدير Word
```json
{
  "docx": "^8.2.2",
  "html-docx-js": "^0.3.1"
}
```

**الوصف:**
- `docx`: إنشاء مستندات Word برمجياً
- `html-docx-js`: تحويل HTML إلى Word

**الاستخدام:**
```javascript
import { Document, Packer, Paragraph } from 'docx';
import htmlDocx from 'html-docx-js';
```

## مكتبات الرسوم البيانية

### Chart.js
```json
{
  "chart.js": "^4.2.1",
  "react-chartjs-2": "^5.2.0",
  "chartjs-adapter-date-fns": "^3.0.0"
}
```

**الوصف:**
- `chart.js`: مكتبة رسوم بيانية قوية
- `react-chartjs-2`: مكون React لـ Chart.js
- `chartjs-adapter-date-fns`: محول التواريخ

**الاستخدام:**
```javascript
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
```

## مكتبات التواريخ والتنسيق

### Date-fns
```json
{
  "date-fns": "^2.30.0"
}
```

**الوصف:**
- مكتبة حديثة للتعامل مع التواريخ
- دعم ممتاز للغة العربية

**الاستخدام:**
```javascript
import { format, parseISO } from 'date-fns';
import { ar } from 'date-fns/locale';
```

### Numeral.js
```json
{
  "numeral": "^2.0.6"
}
```

**الوصف:**
- تنسيق الأرقام والعملات
- دعم التنسيق العربي

**الاستخدام:**
```javascript
import numeral from 'numeral';
```

## مكتبات UI والتفاعل

### React Hot Toast
```json
{
  "react-hot-toast": "^2.4.1"
}
```

**الوصف:**
- إشعارات أنيقة وسهلة الاستخدام
- دعم RTL

**الاستخدام:**
```javascript
import toast from 'react-hot-toast';
```

### React to Print
```json
{
  "react-to-print": "^2.14.13"
}
```

**الوصف:**
- طباعة مكونات React بسهولة
- تحكم كامل في عملية الطباعة

**الاستخدام:**
```javascript
import { useReactToPrint } from 'react-to-print';
```

## مكتبات المساعدة

### Lodash
```json
{
  "lodash": "^4.17.21",
  "@types/lodash": "^4.14.195"
}
```

**الوصف:**
- وظائف مساعدة للبرمجة
- معالجة البيانات والمصفوفات

**الاستخدام:**
```javascript
import _ from 'lodash';
```

### Mime Types
```json
{
  "mime-types": "^2.1.35",
  "@types/mime-types": "^2.1.1"
}
```

**الوصف:**
- تحديد أنواع الملفات
- مطلوب لتصدير الملفات

**الاستخدام:**
```javascript
import mime from 'mime-types';
```

## أوامر التثبيت

### NPM
```bash
npm install jspdf html2canvas xlsx file-saver docx html-docx-js chart.js react-chartjs-2 chartjs-adapter-date-fns date-fns numeral react-hot-toast react-to-print lodash mime-types

npm install --save-dev @types/lodash @types/mime-types
```

### Yarn
```bash
yarn add jspdf html2canvas xlsx file-saver docx html-docx-js chart.js react-chartjs-2 chartjs-adapter-date-fns date-fns numeral react-hot-toast react-to-print lodash mime-types

yarn add --dev @types/lodash @types/mime-types
```

### PNPM
```bash
pnpm add jspdf html2canvas xlsx file-saver docx html-docx-js chart.js react-chartjs-2 chartjs-adapter-date-fns date-fns numeral react-hot-toast react-to-print lodash mime-types

pnpm add -D @types/lodash @types/mime-types
```

## إعدادات TypeScript

### tsconfig.json
```json
{
  "compilerOptions": {
    "types": ["node"],
    "lib": ["dom", "dom.iterable", "es6"],
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "moduleResolution": "node"
  }
}
```

## إعدادات Next.js

### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['jspdf', 'html2canvas', 'xlsx']
  },
  webpack: (config) => {
    config.resolve.alias.canvas = false;
    config.resolve.alias.encoding = false;
    return config;
  }
}

module.exports = nextConfig;
```

## متغيرات البيئة

### .env.local
```env
# إعدادات التصدير
EXPORT_MAX_FILE_SIZE=50000000
EXPORT_TIMEOUT=30000
EXPORT_TEMP_DIR=/tmp/exports

# إعدادات الطباعة
PRINT_DPI=300
PRINT_QUALITY=high
```

## اختبار التثبيت

### ملف اختبار بسيط
```javascript
// test-exports.js
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

console.log('✅ jsPDF:', typeof jsPDF);
console.log('✅ XLSX:', typeof XLSX);
console.log('✅ date-fns:', format(new Date(), 'PPP', { locale: ar }));

export default function TestExports() {
  return <div>اختبار المكتبات نجح!</div>;
}
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ Canvas
```javascript
// في next.config.js
config.resolve.alias.canvas = false;
```

#### خطأ Encoding
```javascript
// في next.config.js
config.resolve.alias.encoding = false;
```

#### مشاكل الخطوط العربية
```javascript
// تحميل خط عربي في jsPDF
import './fonts/arabic-font-normal.js';
pdf.setFont('arabic-font', 'normal');
```

#### مشاكل الذاكرة
```javascript
// تحسين استخدام الذاكرة
const options = {
  scale: 1,
  useCORS: true,
  allowTaint: true,
  backgroundColor: '#ffffff'
};
```

## الأداء والتحسين

### تحسين حجم الحزمة
```javascript
// استيراد انتقائي
import { format } from 'date-fns/format';
import { ar } from 'date-fns/locale/ar';

// بدلاً من
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
```

### تحميل كسول
```javascript
// تحميل المكتبات عند الحاجة
const jsPDF = await import('jspdf');
const html2canvas = await import('html2canvas');
```

## الأمان

### التحقق من أنواع الملفات
```javascript
const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
if (!allowedTypes.includes(file.type)) {
  throw new Error('نوع ملف غير مدعوم');
}
```

### حدود الحجم
```javascript
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
if (fileSize > MAX_FILE_SIZE) {
  throw new Error('حجم الملف كبير جداً');
}
```

## الصيانة والتحديث

### فحص التحديثات
```bash
npm outdated
npm audit
npm update
```

### نسخ احتياطية
```bash
npm list --depth=0 > package-versions.txt
```

هذه المكتبات ضرورية لتنفيذ جميع ميزات التصدير والطباعة المطلوبة في نظام تقارير المشرف.

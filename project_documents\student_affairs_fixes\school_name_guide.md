# دليل تعديل اسم المؤسسة

هذا الدليل يوضح كيفية العثور على اسم المؤسسة وتعديله في النظام.

## 1. مكان تخزين اسم المؤسسة

يتم تخزين معلومات المؤسسة، بما في ذلك الاسم والشعار وبيانات الاتصال، في قاعدة البيانات ضمن جدول `SystemSettings`.

يتم الوصول إلى هذه البيانات برمجيًا من خلال `prisma.systemSettings`.

## 2. كيفية تعديل اسم المؤسسة

لتعديل اسم المؤسسة، يجب تعديل الواجهة المخصصة لإدارة إعدادات النظام.

- **المسار:** من المفترض أن تكون هناك صفحة في لوحة تحكم المشرف لإدارة الإعدادات العامة.
- **البحث عن:** ابحث عن صفحة "إعدادات النظام" أو "إعدادات المدرسة".
- **الحقول:** يجب أن تحتوي الصفحة على حقول لتعديل:
  - `siteName`: اسم المؤسسة
  - `siteDescription`: وصف المؤسسة
  - `logoUrl`: رابط شعار المؤسسة
  - `contactInfo`: معلومات الاتصال (العنوان، الهاتف، البريد الإلكتروني)

إذا لم تكن هذه الواجهة موجودة، فيجب إنشاؤها.

## 3. الملفات ذات الصلة

- `src/utils/school-settings.ts`: يحتوي على الدوال المساعدة لجلب وحفظ إعدادات المدرسة.
- `src/app/api/students/[id]/card/route.ts`: مثال على كيفية استخدام هذه الإعدادات في واجهة برمجة التطبيقات.
- `src/components/admin/students/StudentCard.tsx`: مثال على كيفية عرض هذه الإعدادات في واجهة المستخدم.

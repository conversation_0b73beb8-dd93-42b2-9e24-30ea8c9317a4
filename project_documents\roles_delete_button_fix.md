# خطة إصلاح زر حذف الدور في صفحة إدارة الأدوار

## وصف المشكلة
في صفحة إدارة الأدوار والصلاحيات (`/admin/roles-permissions`)، زر التعديل يعمل بشكل صحيح ولكن زر الحذف لا يعمل.

## تحليل المشكلة

### المكونات المتأثرة
1. **صفحة إدارة الأدوار**: `src/app/admin/roles-permissions/page.tsx`
2. **مكون OptimizedActionButtonGroup**: `src/components/admin/OptimizedActionButtons.tsx`
3. **API حذف الدور**: `src/app/api/admin/roles/[id]/route.ts`

### الصلاحيات المطلوبة
- **زر التعديل**: `admin.roles.edit`
- **زر الحذف**: `admin.roles.delete`

### السبب المحتمل للمشكلة
1. **مشكلة في الصلاحيات**: قد تكون صلاحية `admin.roles.delete` غير موجودة أو غير مُربوطة بالدور
2. **مشكلة في مكون OptimizedActionButtonGroup**: قد يكون هناك خطأ في منطق عرض زر الحذف
3. **مشكلة في وظيفة handleDeleteRole**: قد يكون هناك خطأ في تنفيذ وظيفة الحذف
4. **مشكلة في API**: قد يكون هناك خطأ في API حذف الدور

## خطة العمل التفصيلية

### [x] **T02.01: فحص الصلاحيات المطلوبة لحذف الأدوار**
- **الحالة:** مُنجزة
- **المكونات:** `prisma/seeds/permissions.ts`
- **الاعتماديات:** لا يوجد
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم التأكد من وجود صلاحية `admin.roles.delete` في ملف الصلاحيات

### [x] **T02.02: فحص استخدام OptimizedActionButtonGroup في صفحة الأدوار**
- **الحالة:** مُنجزة
- **المكونات:** `src/app/admin/roles-permissions/page.tsx`
- **الاعتماديات:** T02.01
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم فحص الكود ووُجد أنه يستخدم المكون بشكل صحيح، وتم إصلاح مشكلة خاصية `size`

### [x] **T02.03: فحص وظيفة handleDeleteRole**
- **الحالة:** مُنجزة
- **المكونات:** `src/app/admin/roles-permissions/page.tsx`
- **الاعتماديات:** T02.02
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم فحص الوظيفة ووُجد أنها تعمل بشكل صحيح

### [x] **T02.04: فحص وإصلاح API حذف الدور**
- **الحالة:** مُنجزة
- **المكونات:** `src/app/api/admin/roles/[id]/route.ts`, `src/lib/permissions.ts`
- **الاعتماديات:** T02.03
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** تم إصلاح API ليتحقق من الصلاحيات بدلاً من التحقق من الدور فقط، وتم إضافة دالة `checkPermission`

### [ ] **T02.05: اختبار عمل زر الحذف**
- **الحالة:** قيد الانتظار
- **المكونات:** جميع المكونات المذكورة أعلاه
- **الاعتماديات:** T02.04
- **المستندات المرجعية:** هذا المستند
- **ملاحظات المستخدم:** اختبار عمل زر الحذف بعد الإصلاحات

## الإصلاحات المُطبقة

### 1. إصلاح مكون OptimizedActionButtonGroup
- إضافة دعم خاصية `size` للمكون
- تمرير خاصية `size` للأزرار الفردية

### 2. إصلاح API حذف الدور
- تغيير التحقق من الدور إلى التحقق من الصلاحيات
- إضافة دالة `checkPermission` في `src/lib/permissions.ts`
- استخدام `checkPermission(userData.id, 'admin.roles.delete')` بدلاً من `userData.role !== 'ADMIN'`

### 3. إصلاح API تحديث الدور
- تطبيق نفس الإصلاح على API التحديث للتناسق
- استخدام `checkPermission(userData.id, 'admin.roles.edit')` للتحقق من صلاحية التحديث

## الملفات المتأثرة
- `src/app/admin/roles-permissions/page.tsx` - صفحة إدارة الأدوار
- `src/components/admin/OptimizedActionButtons.tsx` - مكون أزرار الإجراءات
- `src/app/api/admin/roles/[id]/route.ts` - API حذف الدور
- `prisma/seeds/permissions.ts` - ملف الصلاحيات

## ملاحظات إضافية
- يجب التأكد من أن المشكلة ليست في console المتصفح (JavaScript errors)
- يجب اختبار الحل مع مستخدمين مختلفين (مدير وموظف)
- يجب التأكد من عدم تأثير التغييرات على أجزاء أخرى من النظام

// Script لإضافة بيانات تجريبية للأولياء والمدفوعات
// يجب تشغيله من مجلد المشروع: node scripts/add-test-parent-data.js

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addTestData() {
  try {
    console.log('🚀 بدء إضافة البيانات التجريبية...');

    // إضافة أولياء تجريبيين
    const parent1 = await prisma.parent.upsert({
      where: { phone: '0123456795' },
      update: {},
      create: {
        name: 'حسن محمد',
        phone: '0123456795',
        email: '<EMAIL>',
        address: 'الجزائر العاصمة'
      }
    });

    const parent2 = await prisma.parent.upsert({
      where: { phone: '0123456796' },
      update: {},
      create: {
        name: 'عم<PERSON> خالد',
        phone: '0123456796',
        email: '<EMAIL>',
        address: 'وهران'
      }
    });

    console.log('✅ تم إنشاء الأولياء:', parent1.name, parent2.name);

    // البحث عن الصفوف الموجودة أو إنشاؤها
    let classe1 = await prisma.classe.findFirst({
      where: { name: 'السنة الأولى ابتدائي' }
    });

    if (!classe1) {
      classe1 = await prisma.classe.create({
        data: {
          name: 'السنة الأولى ابتدائي',
          level: 'PRIMARY',
          capacity: 30
        }
      });
    }

    let classe2 = await prisma.classe.findFirst({
      where: { name: 'السنة الثانية ابتدائي' }
    });

    if (!classe2) {
      classe2 = await prisma.classe.create({
        data: {
          name: 'السنة الثانية ابتدائي',
          level: 'PRIMARY',
          capacity: 30
        }
      });
    }

    // إضافة طلاب للولي الأول
    const student1 = await prisma.student.upsert({
      where: { registrationNumber: 'STU001' },
      update: {},
      create: {
        name: 'أحمد حسن',
        registrationNumber: 'STU001',
        dateOfBirth: new Date('2015-05-15'),
        gender: 'MALE',
        parentId: parent1.id,
        classeId: classe1.id
      }
    });

    const student2 = await prisma.student.upsert({
      where: { registrationNumber: 'STU002' },
      update: {},
      create: {
        name: 'فاطمة حسن',
        registrationNumber: 'STU002',
        dateOfBirth: new Date('2013-08-20'),
        gender: 'FEMALE',
        parentId: parent1.id,
        classeId: classe2.id
      }
    });

    // إضافة طلاب للولي الثاني
    const student3 = await prisma.student.upsert({
      where: { registrationNumber: 'STU003' },
      update: {},
      create: {
        name: 'محمد عمر',
        registrationNumber: 'STU003',
        dateOfBirth: new Date('2014-12-10'),
        gender: 'MALE',
        parentId: parent2.id,
        classeId: classe1.id
      }
    });

    console.log('✅ تم إنشاء الطلاب:', student1.name, student2.name, student3.name);

    // البحث عن طريقة دفع أو إنشاؤها
    let paymentMethod = await prisma.paymentMethod.findFirst({
      where: { name: 'نقداً' }
    });

    if (!paymentMethod) {
      paymentMethod = await prisma.paymentMethod.create({
        data: {
          name: 'نقداً',
          isActive: true
        }
      });
    }

    // إضافة فواتير للطلاب
    const currentDate = new Date();
    const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

    // فواتير للطالب الأول (أحمد حسن)
    const invoice1 = await prisma.invoice.create({
      data: {
        studentId: student1.id,
        amount: 5000, // 5000 دج
        month: lastMonth.getMonth() + 1,
        year: lastMonth.getFullYear(),
        dueDate: new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 15),
        status: 'PAID',
        description: 'رسوم شهرية'
      }
    });

    const invoice2 = await prisma.invoice.create({
      data: {
        studentId: student1.id,
        amount: 5000,
        month: currentMonth.getMonth() + 1,
        year: currentMonth.getFullYear(),
        dueDate: new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 15),
        status: 'UNPAID',
        description: 'رسوم شهرية'
      }
    });

    // فواتير للطالب الثاني (فاطمة حسن)
    const invoice3 = await prisma.invoice.create({
      data: {
        studentId: student2.id,
        amount: 6000,
        month: lastMonth.getMonth() + 1,
        year: lastMonth.getFullYear(),
        dueDate: new Date(lastMonth.getFullYear(), lastMonth.getMonth() + 1, 15),
        status: 'PARTIALLY_PAID',
        description: 'رسوم شهرية'
      }
    });

    // فواتير للطالب الثالث (محمد عمر)
    const invoice4 = await prisma.invoice.create({
      data: {
        studentId: student3.id,
        amount: 4500,
        month: currentMonth.getMonth() + 1,
        year: currentMonth.getFullYear(),
        dueDate: new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 15),
        status: 'UNPAID',
        description: 'رسوم شهرية'
      }
    });

    console.log('✅ تم إنشاء الفواتير');

    // إضافة مدفوعات
    const payment1 = await prisma.payment.create({
      data: {
        studentId: student1.id,
        invoiceId: invoice1.id,
        amount: 5000,
        date: new Date(2025, 5, 1), // 01/06/2025
        paymentMethodId: paymentMethod.id,
        status: 'PAID',
        notes: 'دفعة مسجلة باسم ولي الأمر: حسن محمد'
      }
    });

    const payment2 = await prisma.payment.create({
      data: {
        studentId: student2.id,
        invoiceId: invoice3.id,
        amount: 3000, // دفع جزئي
        date: new Date(2025, 5, 5), // 05/06/2025
        paymentMethodId: paymentMethod.id,
        status: 'PAID',
        notes: 'دفعة مسجلة باسم ولي الأمر: حسن محمد - دفع جزئي'
      }
    });

    const payment3 = await prisma.payment.create({
      data: {
        studentId: student3.id,
        amount: 2000, // دفعة على الحساب
        date: new Date(2025, 5, 10), // 10/06/2025
        paymentMethodId: paymentMethod.id,
        status: 'PAID',
        notes: 'دفعة مسجلة باسم ولي الأمر: عمر خالد - دفعة على الحساب'
      }
    });

    console.log('✅ تم إنشاء المدفوعات');

    // تحديث حالة الفواتير
    await prisma.invoice.update({
      where: { id: invoice1.id },
      data: { status: 'PAID' }
    });

    await prisma.invoice.update({
      where: { id: invoice3.id },
      data: { status: 'PARTIALLY_PAID' }
    });

    console.log('🎉 تم إنجاز إضافة البيانات التجريبية بنجاح!');
    console.log('📊 ملخص البيانات المضافة:');
    console.log(`- ${2} أولياء أمور`);
    console.log(`- ${3} طلاب`);
    console.log(`- ${4} فواتير`);
    console.log(`- ${3} مدفوعات`);
    console.log('');
    console.log('🔍 يمكنك الآن اختبار الصفحات:');
    console.log('- صفحة مدفوعات الأولياء: /parents/payments');
    console.log('- صفحة إدارة ديون الأولياء: /admin/parent-debts');

  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestData();

'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import {
  FolderPlus,
  Edit,
  Trash2,
  Image as ImageIcon,
  Plus,
  FolderOpen
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface StudentAlbum {
  id: number;
  name: string;
  description: string | null;
  coverImage: string | null;
  createdAt: string;
  updatedAt: string;
  imageCount?: number;
}

interface AlbumManagerProps {
  albums: StudentAlbum[];
  onCreateAlbumAction: (album: { name: string; description: string }) => Promise<void>;
  onUpdateAlbumAction: (id: number, album: { name: string; description: string }) => Promise<void>;
  onDeleteAlbumAction: (id: number) => Promise<void>;
  onSelectAlbumAction: (albumId: number) => void;
}

export default function AlbumManager({
  albums,
  onCreateAlbumAction,
  onUpdateAlbumAction,
  onDeleteAlbumAction,
  onSelectAlbumAction
}: AlbumManagerProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAlbum, setSelectedAlbum] = useState<StudentAlbum | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (isCreateDialogOpen) {
      setFormData({
        name: '',
        description: ''
      });
    }
  }, [isCreateDialogOpen]);

  // Set form data when editing
  useEffect(() => {
    if (selectedAlbum && isEditDialogOpen) {
      setFormData({
        name: selectedAlbum.name,
        description: selectedAlbum.description || ''
      });
    }
  }, [selectedAlbum, isEditDialogOpen]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle create album
  const handleCreateAlbum = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم الألبوم",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await onCreateAlbumAction(formData);

      toast({
        title: "تم الإنشاء",
        description: "تم إنشاء الألبوم بنجاح",
      });

      setIsCreateDialogOpen(false);
    } catch {
      toast({
        title: "خطأ",
        description: "فشل في إنشاء الألبوم. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle update album
  const handleUpdateAlbum = async () => {
    if (!selectedAlbum) return;

    if (!formData.name.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم الألبوم",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      await onUpdateAlbumAction(selectedAlbum.id, formData);

      toast({
        title: "تم التحديث",
        description: "تم تحديث الألبوم بنجاح",
      });

      setIsEditDialogOpen(false);
    } catch {
      toast({
        title: "خطأ",
        description: "فشل في تحديث الألبوم. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete album
  const handleDeleteAlbum = async () => {
    if (!selectedAlbum) return;

    setIsSubmitting(true);

    try {
      await onDeleteAlbumAction(selectedAlbum.id);

      toast({
        title: "تم الحذف",
        description: "تم حذف الألبوم بنجاح",
      });

      setIsDeleteDialogOpen(false);
    } catch {
      toast({
        title: "خطأ",
        description: "فشل في حذف الألبوم. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">ألبومات الصور ({albums.length})</h2>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <FolderPlus className="h-4 w-4 ml-2" />
          إنشاء ألبوم جديد
        </Button>
      </div>

      {albums.length === 0 ? (
        <div className="text-center py-8 border rounded-lg bg-gray-50">
          <ImageIcon className="h-12 w-12 mx-auto text-gray-400 mb-2" />
          <p className="text-gray-500">لا توجد ألبومات متاحة</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => setIsCreateDialogOpen(true)}
          >
            <Plus className="h-4 w-4 ml-2" />
            إنشاء ألبوم جديد
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {albums.map(album => (
            <Card key={album.id} className="overflow-hidden">
              <div
                className="relative h-40 cursor-pointer"
                onClick={() => onSelectAlbumAction(album.id)}
              >
                {album.coverImage ? (
                  <Image
                    src={album.coverImage}
                    alt={album.name}
                    className="w-full h-full object-cover"
                    width={300}
                    height={160}
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <FolderOpen className="h-16 w-16 text-gray-400" />
                  </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 hover:opacity-100">
                  <Button variant="secondary" size="sm">
                    <FolderOpen className="h-4 w-4 ml-2" />
                    فتح الألبوم
                  </Button>
                </div>
              </div>
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-lg">{album.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(album.createdAt)}
                    </p>
                    <p className="text-sm mt-1">
                      {album.imageCount || 0} صورة
                    </p>
                    {album.description && (
                      <p className="mt-2 text-sm line-clamp-2">{album.description}</p>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedAlbum(album);
                        setIsEditDialogOpen(true);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedAlbum(album);
                        setIsDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Album Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إنشاء ألبوم جديد</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">اسم الألبوم</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="أدخل اسم الألبوم"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">وصف الألبوم</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="أدخل وصف الألبوم (اختياري)"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleCreateAlbum} disabled={isSubmitting}>
              {isSubmitting ? 'جاري الإنشاء...' : 'إنشاء'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Album Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل الألبوم</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">اسم الألبوم</Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="أدخل اسم الألبوم"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">وصف الألبوم</Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="أدخل وصف الألبوم (اختياري)"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleUpdateAlbum} disabled={isSubmitting}>
              {isSubmitting ? 'جاري التحديث...' : 'تحديث'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Album Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>حذف الألبوم</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>
              هل أنت متأكد من رغبتك في حذف ألبوم &quot;{selectedAlbum?.name}&quot;؟
            </p>
            <p className="text-red-500">
              سيتم حذف جميع الصور المرتبطة بهذا الألبوم أو إعادتها إلى الألبوم الافتراضي.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              إلغاء
            </Button>
            <Button variant="destructive" onClick={handleDeleteAlbum} disabled={isSubmitting}>
              {isSubmitting ? 'جاري الحذف...' : 'حذف'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

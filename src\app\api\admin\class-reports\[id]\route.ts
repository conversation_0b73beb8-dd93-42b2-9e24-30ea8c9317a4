import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const classId = parseInt(params.id);
    
    if (isNaN(classId)) {
      return NextResponse.json(
        { message: "معرف الفصل غير صالح" },
        { status: 400 }
      );
    }

    // التحقق من وجود الفصل
    const classExists = await prisma.classe.findUnique({
      where: { id: classId },
      include: {
        students: {
          select: {
            id: true,
            name: true
          }
        },
        classSubjects: {
          include: {
            teacherSubject: {
              include: {
                subject: true
              }
            }
          }
        }
      }
    });

    if (!classExists) {
      return NextResponse.json(
        { message: "الفصل غير موجود" },
        { status: 404 }
      );
    }

    // جلب بيانات الحضور للطلاب في هذا الفصل
    const attendanceData = await prisma.attendance.findMany({
      where: {
        student: {
          classeId: classId
        }
      },
      include: {
        student: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // جلب بيانات الاختبارات للطلاب في هذا الفصل
    const examPointsData = await prisma.exam_points.findMany({
      where: {
        student: {
          classeId: classId
        }
      },
      include: {
        student: {
          select: {
            id: true,
            name: true
          }
        },
        classSubject: {
          include: {
            teacherSubject: {
              include: {
                subject: true
              }
            }
          }
        }
      }
    });

    // تحليل بيانات الحضور
    const attendanceSummary = {
      present: 0,
      absent: 0,
      excused: 0,
      total: attendanceData.length
    };

    attendanceData.forEach(record => {
      if (record.status === 'PRESENT') attendanceSummary.present++;
      else if (record.status === 'ABSENT') attendanceSummary.absent++;
      else if (record.status === 'EXCUSED') attendanceSummary.excused++;
    });

    // تحليل بيانات الأداء حسب الطالب
    const studentPerformance: Record<number, {
      totalScore: number;
      examCount: number;
      attendance: {
        present: number;
        absent: number;
        excused: number;
        total: number;
      }
    }> = {};

    // تهيئة بيانات الطلاب
    classExists.students.forEach(student => {
      studentPerformance[student.id] = {
        totalScore: 0,
        examCount: 0,
        attendance: {
          present: 0,
          absent: 0,
          excused: 0,
          total: 0
        }
      };
    });

    // إضافة بيانات الاختبارات
    examPointsData.forEach(point => {
      const studentId = point.studentId;
      if (studentPerformance[studentId]) {
        studentPerformance[studentId].totalScore += parseFloat(point.grade.toString());
        studentPerformance[studentId].examCount++;
      }
    });

    // إضافة بيانات الحضور
    attendanceData.forEach(record => {
      const studentId = record.studentId;
      if (studentPerformance[studentId]) {
        studentPerformance[studentId].attendance.total++;
        
        if (record.status === 'PRESENT') 
          studentPerformance[studentId].attendance.present++;
        else if (record.status === 'ABSENT') 
          studentPerformance[studentId].attendance.absent++;
        else if (record.status === 'EXCUSED') 
          studentPerformance[studentId].attendance.excused++;
      }
    });

    // تحليل بيانات الأداء حسب المادة
    const subjectPerformance: Record<number, {
      totalScore: number;
      examCount: number;
      passCount: number;
      failCount: number;
    }> = {};

    // تهيئة بيانات المواد
    classExists.classSubjects.forEach(cs => {
      const subjectId = cs.teacherSubject.subject.id;
      subjectPerformance[subjectId] = {
        totalScore: 0,
        examCount: 0,
        passCount: 0,
        failCount: 0
      };
    });

    // إضافة بيانات الاختبارات للمواد
    examPointsData.forEach(point => {
      if (point.classSubject?.teacherSubject?.subject) {
        const subjectId = point.classSubject.teacherSubject.subject.id;
        if (subjectPerformance[subjectId]) {
          subjectPerformance[subjectId].totalScore += parseFloat(point.grade.toString());
          subjectPerformance[subjectId].examCount++;
          
          // اعتبار درجة النجاح 60 فما فوق
          if (parseFloat(point.grade.toString()) >= 60) {
            subjectPerformance[subjectId].passCount++;
          } else {
            subjectPerformance[subjectId].failCount++;
          }
        }
      }
    });

    // تحويل بيانات الطلاب إلى تنسيق مناسب للعرض
    const studentsData = classExists.students.map(student => {
      const performance = studentPerformance[student.id];
      const avgScore = performance.examCount > 0 
        ? performance.totalScore / performance.examCount 
        : 0;
      
      return {
        id: student.id,
        name: student.name,
        attendance: performance.attendance,
        examResults: {
          avgScore,
          total: performance.examCount,
          passed: 0, // سيتم حسابها لاحقاً إذا توفرت البيانات
          failed: 0
        }
      };
    });

    // ترتيب الطلاب حسب متوسط الدرجات
    const sortedStudents = [...studentsData].sort((a, b) => 
      b.examResults.avgScore - a.examResults.avgScore
    );

    // تحويل بيانات المواد إلى تنسيق مناسب للعرض
    const subjectsData = classExists.classSubjects.map(cs => {
      const subjectId = cs.teacherSubject.subject.id;
      const performance = subjectPerformance[subjectId] || { 
        totalScore: 0, 
        examCount: 0,
        passCount: 0,
        failCount: 0
      };
      
      const avgScore = performance.examCount > 0 
        ? performance.totalScore / performance.examCount 
        : 0;
      
      const passRate = performance.examCount > 0 
        ? (performance.passCount / performance.examCount) * 100 
        : 0;
      
      return {
        id: subjectId,
        name: cs.teacherSubject.subject.name,
        avgScore,
        passRate
      };
    });

    // حساب متوسط الأداء العام للفصل
    let totalScore = 0;
    let totalExams = 0;
    let totalPassed = 0;

    Object.values(subjectPerformance).forEach(perf => {
      totalScore += perf.totalScore;
      totalExams += perf.examCount;
      totalPassed += perf.passCount;
    });

    const classAvgScore = totalExams > 0 ? totalScore / totalExams : 0;
    const classPassRate = totalExams > 0 ? (totalPassed / totalExams) * 100 : 0;

    // تجهيز البيانات النهائية
    const reportData = {
      attendance: {
        presentRate: attendanceSummary.total > 0 
          ? (attendanceSummary.present / attendanceSummary.total) * 100 
          : 0,
        absentRate: attendanceSummary.total > 0 
          ? (attendanceSummary.absent / attendanceSummary.total) * 100 
          : 0,
        excusedRate: attendanceSummary.total > 0 
          ? (attendanceSummary.excused / attendanceSummary.total) * 100 
          : 0
      },
      performance: {
        avgScore: classAvgScore,
        passRate: classPassRate
      },
      subjects: subjectsData,
      topStudents: sortedStudents.slice(0, 5),
      needsImprovement: [...sortedStudents]
        .sort((a, b) => a.examResults.avgScore - b.examResults.avgScore)
        .slice(0, 5)
    };

    return NextResponse.json(reportData);
  } catch (error) {
    console.error("Error generating class report:", error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء تقرير الفصل" },
      { status: 500 }
    );
  }
}

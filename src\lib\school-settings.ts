import { prisma } from '@/lib/prisma';

/**
 * واجهة إعدادات المؤسسة
 */
export interface SchoolSettings {
  siteName: string;
  siteDescription: string;
  logoUrl: string;
  faviconUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  contactInfo: {
    email: string;
    phone: string;
    address: string;
    donationInfo?: {
      phone1?: string;
      phone2?: string;
      ccpAccount?: string;
      cpaAccount?: string;
      bdrAccount?: string;
      description?: string;
    };
  };
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    youtube?: string;
  };
  registrationEnabled?: boolean;
}

/**
 * الإعدادات الافتراضية للمؤسسة
 */
export const defaultSchoolSettings: SchoolSettings = {
  siteName: 'نظام برهان للقرآن الكريم',
  siteDescription: 'منصة تعليمية متكاملة لتعليم القرآن الكريم عن بعد',
  logoUrl: '/logo.svg',
  faviconUrl: '/favicon.ico',
  primaryColor: 'var(--primary-color)',
  secondaryColor: 'var(--secondary-color)',
  contactInfo: {
    email: '<EMAIL>',
    phone: '+213 123 456 789',
    address: 'شارع الاستقلال، الجزائر العاصمة، الجزائر',
    donationInfo: {
      phone1: '+213 123 456 789',
      phone2: '+213 987 654 321',
      ccpAccount: '**********',
      cpaAccount: '**********',
      bdrAccount: '**********',
      description: 'يمكنكم التبرع من خلال الحسابات التالية أو التواصل معنا هاتفياً'
    }
  },
  socialLinks: {
    facebook: 'https://facebook.com',
    twitter: 'https://twitter.com',
    instagram: 'https://instagram.com',
    youtube: 'https://youtube.com'
  },
  registrationEnabled: true
};

/**
 * جلب إعدادات المؤسسة من قاعدة البيانات
 * @returns إعدادات المؤسسة المحفوظة أو الافتراضية
 */
export async function getSchoolSettings(): Promise<SchoolSettings> {
  try {
    const schoolSettings = await prisma.systemSettings.findUnique({
      where: { key: 'SITE_SETTINGS' }
    });

    if (schoolSettings) {
      const parsedSettings = JSON.parse(schoolSettings.value);
      
      // دمج الإعدادات المحفوظة مع الافتراضية لضمان وجود جميع الحقول
      return {
        ...defaultSchoolSettings,
        ...parsedSettings,
        contactInfo: {
          ...defaultSchoolSettings.contactInfo,
          ...parsedSettings.contactInfo
        }
      };
    }

    return defaultSchoolSettings;
  } catch (error) {
    console.error('خطأ في جلب إعدادات المؤسسة:', error);
    return defaultSchoolSettings;
  }
}

/**
 * حفظ إعدادات المؤسسة في قاعدة البيانات
 * @param settings الإعدادات الجديدة
 * @returns الإعدادات المحفوظة
 */
export async function saveSchoolSettings(settings: Partial<SchoolSettings>): Promise<SchoolSettings> {
  try {
    // جلب الإعدادات الحالية
    const currentSettings = await getSchoolSettings();
    
    // دمج الإعدادات الجديدة مع الحالية
    const updatedSettings = {
      ...currentSettings,
      ...settings,
      contactInfo: {
        ...currentSettings.contactInfo,
        ...settings.contactInfo
      }
    };

    // حفظ في قاعدة البيانات
    await prisma.systemSettings.upsert({
      where: { key: 'SITE_SETTINGS' },
      update: {
        value: JSON.stringify(updatedSettings)
      },
      create: {
        key: 'SITE_SETTINGS',
        value: JSON.stringify(updatedSettings)
      }
    });

    return updatedSettings;
  } catch (error) {
    console.error('خطأ في حفظ إعدادات المؤسسة:', error);
    throw new Error('فشل في حفظ إعدادات المؤسسة');
  }
}

/**
 * جلب معلومات الاتصال للمؤسسة (للاستخدام في الفواتير والوثائق)
 * @returns معلومات الاتصال
 */
export async function getSchoolContactInfo() {
  const settings = await getSchoolSettings();
  
  return {
    name: settings.siteName,
    description: settings.siteDescription,
    logoUrl: settings.logoUrl,
    email: settings.contactInfo.email,
    phone: settings.contactInfo.phone,
    address: settings.contactInfo.address,
    donationInfo: settings.contactInfo.donationInfo
  };
}

/**
 * التحقق من تفعيل التسجيل
 * @returns حالة تفعيل التسجيل
 */
export async function isRegistrationEnabled(): Promise<boolean> {
  try {
    const settings = await getSchoolSettings();
    return settings.registrationEnabled ?? true;
  } catch (error) {
    console.error('خطأ في التحقق من حالة التسجيل:', error);
    return true; // افتراضياً مفعل
  }
}

/**
 * جلب الألوان المخصصة للمؤسسة
 * @returns ألوان المؤسسة
 */
export async function getSchoolColors() {
  const settings = await getSchoolSettings();
  
  return {
    primary: settings.primaryColor || 'var(--primary-color)',
    secondary: settings.secondaryColor || 'var(--secondary-color)'
  };
}

/**
 * جلب روابط التواصل الاجتماعي
 * @returns روابط التواصل الاجتماعي
 */
export async function getSocialLinks() {
  const settings = await getSchoolSettings();
  return settings.socialLinks || {};
}

'use client';

import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { clearCacheOnLogout } from '@/lib/permissionsCache';
import { toast } from 'react-toastify';

interface LogoutOptions {
  redirectTo?: string;
  showSuccessMessage?: boolean;
  clearAllCache?: boolean;
}

/**
 * Hook لإدارة تسجيل الخروج مع تنظيف التخزين المؤقت
 */
export const useLogout = () => {
  const router = useRouter();

  const logout = useCallback(async (
    userId?: number, 
    options: LogoutOptions = {}
  ) => {
    const {
      redirectTo = '/',
      showSuccessMessage = true,
      clearAllCache = false
    } = options;

    try {
      // تنظيف التخزين المؤقت أولاً
      if (clearAllCache) {
        clearCacheOnLogout(); // مسح جميع البيانات
      } else if (userId) {
        clearCacheOnLogout(userId); // مسح بيانات المستخدم المحدد
      }

      // استدعاء API تسجيل الخروج
      const response = await fetch('/api/users/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // إظهار رسالة نجاح
        if (showSuccessMessage) {
          toast.success('تم تسجيل الخروج بنجاح');
        }

        // إعادة التوجيه
        router.push(redirectTo);
        router.refresh();
        
        console.log('تم تسجيل الخروج وتنظيف التخزين المؤقت بنجاح');
      } else {
        throw new Error('فشل في تسجيل الخروج من الخادم');
      }
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      
      // حتى لو فشل الطلب للخادم، ننظف التخزين المؤقت ونعيد التوجيه
      if (clearAllCache) {
        clearCacheOnLogout();
      } else if (userId) {
        clearCacheOnLogout(userId);
      }
      
      toast.error('حدث خطأ أثناء تسجيل الخروج، ولكن تم تنظيف البيانات المحلية');
      
      // إعادة التوجيه حتى في حالة الخطأ
      router.push(redirectTo);
      router.refresh();
    }
  }, [router]);

  // دالة تسجيل خروج سريع (للاستخدام في الحالات الطارئة)
  const forceLogout = useCallback((redirectTo: string = '/') => {
    try {
      // مسح جميع البيانات المحلية
      clearCacheOnLogout();
      
      // مسح الكوكيز يدوياً (fallback)
      if (typeof document !== 'undefined') {
        document.cookie = 'jwtToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
      }
      
      // إعادة التوجيه فوراً
      window.location.href = redirectTo;
    } catch (error) {
      console.error('خطأ في تسجيل الخروج القسري:', error);
      // في حالة فشل كل شيء، إعادة تحميل الصفحة
      window.location.reload();
    }
  }, []);

  return {
    logout,
    forceLogout
  };
};

// نظام تزامن الألوان بين localStorage وقاعدة البيانات

import { SiteColors } from './simpleColorSystem';

/**
 * جلب الألوان من قاعدة البيانات
 */
export const fetchColorsFromDatabase = async (): Promise<SiteColors | null> => {
  try {
    const response = await fetch('/api/site-colors');
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.colors) {
        return data.colors;
      }
    }
  } catch (error) {
    console.error('خطأ في جلب الألوان من قاعدة البيانات:', error);
  }
  return null;
};

/**
 * حفظ الألوان في قاعدة البيانات (للوضع النهاري فقط)
 */
export const saveColorsToDatabase = async (colors: SiteColors, mode: 'light' | 'dark' = 'light'): Promise<boolean> => {
  try {
    // حفظ الوضع النهاري فقط في قاعدة البيانات للمشاركة بين المستخدمين
    if (mode === 'light') {
      const response = await fetch('/api/site-colors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          colors,
          mode: 'light'
        }),
      });

      if (response.ok) {
        console.log('✅ تم حفظ ألوان الوضع النهاري في قاعدة البيانات');
        return true;
      }
    }
  } catch (error) {
    console.error('خطأ في حفظ الألوان في قاعدة البيانات:', error);
  }
  return false;
};

/**
 * تزامن الألوان بين localStorage وقاعدة البيانات
 */
export const syncColors = async (): Promise<SiteColors | null> => {
  try {
    // جلب الألوان من قاعدة البيانات
    const dbColors = await fetchColorsFromDatabase();
    
    if (dbColors) {
      // حفظ في localStorage للاستخدام السريع
      localStorage.setItem('siteColors', JSON.stringify(dbColors));
      localStorage.setItem('lightModeColors', JSON.stringify(dbColors));
      
      console.log('🔄 تم تزامن الألوان من قاعدة البيانات');
      return dbColors;
    }
  } catch (error) {
    console.error('خطأ في تزامن الألوان:', error);
  }
  
  return null;
};

/**
 * جلب الألوان مع نظام fallback
 */
export const getColorsWithFallback = async (): Promise<SiteColors> => {
  // الألوان الافتراضية للوضع النهاري
  const defaultLightColors: SiteColors = {
    primaryColor: '#3b82f6',
    secondaryColor: '#6366f1',
    sidebarColor: '#1a202c',
    backgroundColor: '#f3f4f6',
    accentColor: '#10b981',
    textColor: '#1f2937'
  };

  try {
    // 1. جرب localStorage أولاً
    const savedColors = localStorage.getItem('lightModeColors');
    if (savedColors && savedColors !== 'undefined' && savedColors !== 'null') {
      try {
        const colors = JSON.parse(savedColors);
        return { ...defaultLightColors, ...colors };
      } catch (error) {
        console.error('خطأ في parsing الألوان من localStorage:', error);
      }
    }

    // 2. جرب قاعدة البيانات
    const dbColors = await fetchColorsFromDatabase();
    if (dbColors) {
      // حفظ في localStorage للمرة القادمة
      localStorage.setItem('lightModeColors', JSON.stringify(dbColors));
      return { ...defaultLightColors, ...dbColors };
    }

    // 3. استخدم الألوان الافتراضية
    return defaultLightColors;
  } catch (error) {
    console.error('خطأ في جلب الألوان:', error);
    return defaultLightColors;
  }
};

/**
 * تحديث الألوان في جميع الأماكن (localStorage + قاعدة البيانات)
 */
export const updateColorsEverywhere = async (colors: SiteColors): Promise<boolean> => {
  try {
    // 1. حفظ في localStorage فوراً
    localStorage.setItem('siteColors', JSON.stringify(colors));
    localStorage.setItem('lightModeColors', JSON.stringify(colors));

    // 2. حفظ في قاعدة البيانات (للوضع النهاري فقط)
    const dbSaved = await saveColorsToDatabase(colors, 'light');

    if (dbSaved) {
      console.log('✅ تم تحديث الألوان في جميع الأماكن');
      return true;
    } else {
      console.warn('⚠️ تم حفظ الألوان في localStorage فقط');
      return false;
    }
  } catch (error) {
    console.error('خطأ في تحديث الألوان:', error);
    return false;
  }
};

/**
 * مراقبة تغييرات الألوان وتزامنها
 */
export const watchColorChanges = () => {
  // مراقبة تغييرات localStorage
  window.addEventListener('storage', (e) => {
    if (e.key === 'lightModeColors' && e.newValue) {
      try {
        const colors = JSON.parse(e.newValue);
        console.log('🔄 تم اكتشاف تغيير في ألوان الوضع النهاري');
        
        // تطبيق الألوان فوراً إذا كان المستخدم في الوضع النهاري
        const isDarkMode = localStorage.getItem('darkMode') === 'true';
        if (!isDarkMode) {
          const root = document.documentElement;
          root.style.setProperty('--primary-color', colors.primaryColor);
          root.style.setProperty('--secondary-color', colors.secondaryColor);
          root.style.setProperty('--sidebar-color', colors.sidebarColor);
          root.style.setProperty('--background-color', colors.backgroundColor);
          root.style.setProperty('--accent-color', colors.accentColor);
          root.style.setProperty('--text-color', colors.textColor);
        }
      } catch (error) {
        console.error('خطأ في معالجة تغيير الألوان:', error);
      }
    }
  });
};

/**
 * تهيئة نظام تزامن الألوان
 */
export const initializeColorSync = async (): Promise<void> => {
  try {
    // بدء مراقبة التغييرات
    watchColorChanges();
    
    // تزامن الألوان عند التهيئة
    await syncColors();
    
    console.log('🚀 تم تهيئة نظام تزامن الألوان');
  } catch (error) {
    console.error('خطأ في تهيئة نظام تزامن الألوان:', error);
  }
};

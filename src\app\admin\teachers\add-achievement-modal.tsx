'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface Teacher {
  id: number
  name: string
  specialization: string
}

interface AddAchievementModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

const ACHIEVEMENT_TYPES = [
  { value: 'EDUCATIONAL', label: 'تعليمي' },
  { value: 'ADMINISTRATIVE', label: 'إداري' },
  { value: 'RESEARCH', label: 'بحثي' },
  { value: 'COMMUNITY', label: 'مجتمعي' },
  { value: 'PROFESSIONAL', label: 'مهني' },
  { value: 'OTHER', label: 'أخرى' }
]

export function AddAchievementModal({ isOpen, onClose, onSuccess }: AddAchievementModalProps) {
  const [formData, setFormData] = useState({
    teacherId: '',
    title: '',
    description: '',
    achievementDate: '',
    type: '',
    attachmentUrl: ''
  })
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    const fetchTeachers = async () => {
      try {
        const response = await fetch('/api/teachers')
        if (!response.ok) throw new Error('Failed to fetch teachers')
        const data = await response.json()
        setTeachers(data.teachers || [])
      } catch (err: unknown) {
        setError('Failed to load teachers')
        console.error(err)
      }
    }
    fetchTeachers()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    // التحقق من البيانات
    if (!formData.teacherId || !formData.title || !formData.description || !formData.type) {
      setError('يرجى إدخال جميع البيانات المطلوبة')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/teacher-achievements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          teacherId: parseInt(formData.teacherId),
          title: formData.title,
          description: formData.description,
          achievementDate: formData.achievementDate ? new Date(formData.achievementDate).toISOString() : new Date().toISOString(),
          type: formData.type,
          attachmentUrl: formData.attachmentUrl || null
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'حدث خطأ أثناء إضافة الإنجاز')
      }

      onSuccess()
      onClose()
      setFormData({
        teacherId: '',
        title: '',
        description: '',
        achievementDate: '',
        type: '',
        attachmentUrl: ''
      })
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء إضافة الإنجاز';
      setError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] bg-white max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold text-[var(--primary-color)]">إضافة إنجاز جديد للمعلم</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded-md">{error}</div>
          )}

          <div className="space-y-2">
            <Label htmlFor="teacherId">المعلم</Label>
            <Select
              value={formData.teacherId}
              onValueChange={(value) => setFormData(prev => ({ ...prev, teacherId: value }))}
            >
              <SelectTrigger id="teacherId" className="w-full">
                <SelectValue placeholder="اختر المعلم" />
              </SelectTrigger>
              <SelectContent>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.id} value={teacher.id.toString()}>
                    {teacher.name} - {teacher.specialization}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">عنوان الإنجاز</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="أدخل عنوان الإنجاز"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">نوع الإنجاز</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}
              >
                <SelectTrigger id="type" className="w-full">
                  <SelectValue placeholder="اختر نوع الإنجاز" />
                </SelectTrigger>
                <SelectContent>
                  {ACHIEVEMENT_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="achievementDate">تاريخ الإنجاز</Label>
              <Input
                id="achievementDate"
                type="date"
                value={formData.achievementDate}
                onChange={(e) => setFormData(prev => ({ ...prev, achievementDate: e.target.value }))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">وصف الإنجاز</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="أدخل وصف تفصيلي للإنجاز"
              rows={4}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="attachmentUrl">رابط المرفق (اختياري)</Label>
            <Input
              id="attachmentUrl"
              value={formData.attachmentUrl}
              onChange={(e) => setFormData(prev => ({ ...prev, attachmentUrl: e.target.value }))}
              placeholder="أدخل رابط المرفق إن وجد"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="border-gray-300 hover:bg-gray-100"
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
            >
              {isLoading ? 'جاري الإضافة...' : 'إضافة الإنجاز'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

# تطبيقات Praetorian.ring

مجموعة من التطبيقات العملية التي تستخدم مكتبة Praetorian.ring لإظهار قوتها ومرونتها في سيناريوهات مختلفة.

## 📋 نظرة عامة

هذا المجلد يحتوي على تطبيقين رئيسيين:

1. **ReconDash** - لوحة تحكم استطلاعية بواجهة رسومية
2. **DirHunter** - أداة تخمين المجلدات والملفات لسطر الأوامر

## 🖥️ ReconDash - لوحة التحكم الاستطلاعية

### الوصف
ReconDash هي لوحة تحكم استطلاعية متكاملة بواجهة رسومية تتيح للمستخدم إدخال هدف وجمع معلومات استخباراتية أساسية عنه.

### المميزات
- **واجهة رسومية سهلة الاستخدام** باستخدام libui.ring
- **فحص شامل للمنافذ** مع جلب البانر وتحديد الخدمات
- **تحليل SSL/TLS** للمنافذ الآمنة
- **زاحف ويب** لاكتشاف الروابط والمسارات
- **عرض منظم للنتائج** في ألسنة منفصلة
- **واجهة سريعة الاستجابة** (مع دعم التعدد في المستقبل)

### المتطلبات
- Ring 1.23+
- مكتبة Praetorian.ring
- libui.ring (للواجهة الرسومية)
- threads.ring (للتعدد - اختياري)

### التشغيل
```bash
cd applications/ReconDash
ring ReconDash.ring
```

### كيفية الاستخدام
1. أدخل الهدف (مثل: example.com أو 192.168.1.1)
2. اضغط على "بدء الفحص"
3. راجع النتائج في الألسنة المختلفة:
   - **نظرة عامة**: معلومات أساسية عن الهدف
   - **فحص المنافذ**: المنافذ المفتوحة والخدمات
   - **تحليل SSL/TLS**: تفاصيل الشهادات والأمان
   - **الزاحف**: الروابط والمسارات المكتشفة

### لقطات الشاشة
```
┌─ ReconDash - لوحة التحكم الاستطلاعية ─────────────────┐
│ الهدف (Host or IP): [example.com          ] [بدء الفحص] │
│ ──────────────────────────────────────────────────────── │
│ ┌نظرة عامة┐┌فحص المنافذ┐┌تحليل SSL/TLS┐┌الزاحف┐      │
│ │                                                      │ │
│ │ === نظرة عامة على الهدف ===                          │ │
│ │                                                      │ │
│ │ الهدف: example.com                                   │ │
│ │ عنوان IP: *************                             │ │
│ │ الحالة: نشط                                          │ │
│ │ المنافذ المفتوحة: 2                                  │ │
│ │                                                      │ │
│ └──────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────┘
```

## 🔍 DirHunter - أداة تخمين المجلدات والملفات

### الوصف
DirHunter هي أداة سريعة وقوية تعمل من سطر الأوامر، متخصصة في تخمين أسماء المجلدات والملفات على خوادم الويب.

### المميزات
- **واجهة سطر أوامر احترافية** مع معلمات متقدمة
- **دعم قوائم الكلمات المخصصة** لتخمين المسارات
- **فحص متعدد الخيوط** للسرعة (مخطط للمستقبل)
- **دعم امتدادات متعددة** للملفات
- **شريط تقدم تفاعلي** لمتابعة الفحص
- **نتائج ملونة** لسهولة القراءة
- **ملخص شامل** للنتائج

### المتطلبات
- Ring 1.23+
- مكتبة Praetorian.ring
- rogueutil.ring (للألوان والتنسيق - اختياري)

### التشغيل
```bash
cd applications/DirHunter
ring DirHunter.ring -u http://example.com -w wordlist.txt
```

### معلمات سطر الأوامر

#### المعلمات المطلوبة:
- `-u, --url <URL>`: عنوان URL الأساسي للهدف
- `-w, --wordlist <PATH>`: المسار إلى ملف قائمة الكلمات

#### المعلمات الاختيارية:
- `-t, --threads <NUM>`: عدد الخيوط المتزامنة (افتراضي: 10)
- `-x, --extensions <EXT>`: قائمة امتدادات مفصولة بفواصل
- `-v, --verbose`: عرض تفاصيل إضافية
- `-h, --help`: عرض رسالة المساعدة

### أمثلة الاستخدام

#### فحص أساسي:
```bash
ring DirHunter.ring -u http://example.com -w wordlist.txt
```

#### فحص متقدم مع امتدادات مخصصة:
```bash
ring DirHunter.ring -u https://target.com -w dirs.txt -t 20 -x php,html,asp
```

#### فحص مفصل:
```bash
ring DirHunter.ring -u http://example.com -w wordlist.txt -v
```

### مثال على المخرجات
```
DirHunter v1.0 - أداة تخمين المجلدات والملفات
جزء من مجموعة أدوات Praetorian.ring
==============================================

[*] إعدادات الفحص:
[*] الهدف: http://example.com
[*] قائمة الكلمات: wordlist.txt
[*] عدد الخيوط: 10
[*] الامتدادات: .php, .html, .htm, .txt

[*] تم تحميل 250 كلمة من wordlist.txt
[*] بدء فحص 1000 مسار...

[====================] 100% (1000/1000)

[+] Found: http://example.com/admin/ (Forbidden)
[+] Found: http://example.com/robots.txt (OK, 245 bytes)
[+] Found: http://example.com/sitemap.xml (OK, 1024 bytes)

[*] انتهى الفحص!
[*] إجمالي الطلبات: 1000
[*] المسارات الموجودة: 3
```

## 📁 قائمة الكلمات المرفقة

يتضمن DirHunter ملف `wordlist.txt` يحتوي على:
- **250+ كلمة** شائعة للمجلدات والملفات
- **مجلدات إدارية** (admin, panel, dashboard)
- **ملفات التكوين** (config, settings, setup)
- **مجلدات النسخ الاحتياطية** (backup, archive)
- **ملفات النظام** (robots.txt, .htaccess)
- **مسارات CMS** (wp-admin, administrator)
- **مجلدات التطوير** (dev, test, staging)

## 🔧 التخصيص والتطوير

### إضافة ميزات جديدة لـ ReconDash:
1. فتح `applications/ReconDash/ReconDash.ring`
2. إضافة وظائف فحص جديدة في قسم "دوال الفحص الأساسية"
3. إضافة ألسنة جديدة في `createResultsTabs()`
4. ربط النتائج بالواجهة

### تخصيص DirHunter:
1. تعديل قائمة الكلمات في `wordlist.txt`
2. إضافة معلمات جديدة في `parseArguments()`
3. تخصيص منطق الفحص في `runDirectoryHunt()`

## ⚠️ تحذيرات مهمة

### الاستخدام الأخلاقي:
- استخدم هذه الأدوات فقط على الأنظمة التي تملك إذناً لفحصها
- احترم قوانين الأمان السيبراني المحلية والدولية
- لا تستخدم الأدوات لأغراض ضارة أو غير قانونية

### الأداء:
- استخدم إعدادات التأخير المناسبة لتجنب إرهاق الخوادم
- راقب استهلاك الشبكة والموارد
- اختبر على بيئات محلية أولاً

### الأمان:
- تأكد من تحديث مكتبة Praetorian.ring بانتظام
- استخدم اتصالات آمنة عند الإمكان
- احفظ النتائج في مكان آمن

## 🚀 التطوير المستقبلي

### ميزات مخططة لـ ReconDash:
- [ ] دعم التعدد الحقيقي مع RingThreadPro
- [ ] حفظ وتصدير النتائج
- [ ] إعدادات متقدمة للفحص
- [ ] دعم ملفات التكوين
- [ ] تكامل مع قواعد البيانات

### ميزات مخططة لـ DirHunter:
- [ ] تنفيذ التعدد الحقيقي
- [ ] دعم البروكسي والـ VPN
- [ ] فلترة النتائج المتقدمة
- [ ] تصدير النتائج بصيغ مختلفة
- [ ] واجهة ويب اختيارية

## 📞 الدعم والمساهمة

- **الإبلاغ عن الأخطاء**: استخدم GitHub Issues
- **طلب الميزات**: اقترح ميزات جديدة
- **المساهمة**: Fork المشروع وأرسل Pull Request
- **التوثيق**: ساعد في تحسين التوثيق

## 📄 الترخيص

هذه التطبيقات مرخصة تحت نفس ترخيص مكتبة Praetorian.ring (MIT License).

---

**ملاحظة**: هذه التطبيقات هي أمثلة تعليمية وتطبيقية لإظهار قوة مكتبة Praetorian.ring. استخدمها بمسؤولية وأخلاقية.

import { NextRequest, NextResponse } from 'next/server';
import  prisma  from '@/lib/prisma';

// GET /api/student-albums/[id] - الحصول على ألبوم محدد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
 
    // التحقق من صحة المعرف
    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        error: 'معرف الألبوم غير صالح'
      }, { status: 400 });
    }

    // البحث عن الألبوم
    const album = await prisma.studentAlbum.findUnique({
      where: { id }
    });

    if (!album) {
      return NextResponse.json({
        success: false,
        error: 'الألبوم غير موجود'
      }, { status: 404 });
    }

    // الحصول على عدد الصور في الألبوم
    const imageCount = await prisma.studentImage.count({
      where: {
        albumId: id
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        ...album,
        imageCount
      },
      message: 'تم جلب الألبوم بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student album:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب الألبوم'
    }, { status: 500 });
  }
}

// PUT /api/student-albums/[id] - تحديث ألبوم
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    // التحقق من صحة المعرف
    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        error: 'معرف الألبوم غير صالح'
      }, { status: 400 });
    }

    const body = await request.json();
    const { name, description, coverImage } = body;

    // التحقق من البيانات المطلوبة
    if (!name) {
      return NextResponse.json({
        success: false,
        error: 'اسم الألبوم مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود الألبوم
    const existingAlbum = await prisma.studentAlbum.findUnique({
      where: { id }
    });

    if (!existingAlbum) {
      return NextResponse.json({
        success: false,
        error: 'الألبوم غير موجود'
      }, { status: 404 });
    }

    // تحديث الألبوم
    const updatedAlbum = await prisma.studentAlbum.update({
      where: { id },
      data: {
        name,
        description,
        coverImage: coverImage !== undefined ? coverImage : undefined
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedAlbum,
      message: 'تم تحديث الألبوم بنجاح'
    });
  } catch (error) {
    console.error('Error updating student album:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تحديث الألبوم'
    }, { status: 500 });
  }
}

// DELETE /api/student-albums/[id] - حذف ألبوم
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    // التحقق من صحة المعرف
    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        error: 'معرف الألبوم غير صالح'
      }, { status: 400 });
    }

    // التحقق من وجود الألبوم
    const existingAlbum = await prisma.studentAlbum.findUnique({
      where: { id }
    });

    if (!existingAlbum) {
      return NextResponse.json({
        success: false,
        error: 'الألبوم غير موجود'
      }, { status: 404 });
    }

    // إعادة تعيين الصور في هذا الألبوم إلى عدم وجود ألبوم (null)
    await prisma.studentImage.updateMany({
      where: { albumId: id },
      data: { albumId: null }
    });

    // حذف الألبوم
    await prisma.studentAlbum.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف الألبوم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting student album:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف الألبوم'
    }, { status: 500 });
  }
}

// استيراد المكتبات اللازمة
import { NextRequest } from 'next/server';
import * as jose from 'jose';

export type JWTPayload = {
    id: number;
    username: string;
    role: string;
    roleId?: number;
}

// Verify Token For API End Point
export async function verifyToken(request: NextRequest): Promise<JWTPayload | null> {
    try {
        const jwtToken = request.cookies.get("jwtToken");
        const token = jwtToken?.value as string;
        if (!token) return null;

        const secret = new TextEncoder().encode(process.env.JWT_SECRET);
        const { payload } = await jose.jwtVerify(token, secret);
        return payload as JWTPayload;
    } catch {
        return null;
    }
}

// Verify Token For Page
export async function verifyTokenForPage(token: string): Promise<JWTPayload | null> {
    try {
        const secret = new TextEncoder().encode(process.env.JWT_SECRET);
        const { payload } = await jose.jwtVerify(token, secret);
        if(!payload) return null;

        return payload as JWTPayload;
    } catch {
        return null;
    }
}
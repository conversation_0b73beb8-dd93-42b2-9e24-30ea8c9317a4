'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Save, ArrowLeft, FileText, Loader2, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { cn } from '@/utils/cn';
import LiteraryReportEditor from '@/components/supervisor-reports/LiteraryReportEditor';
import SimpleLiteraryEditor from '@/components/supervisor-reports/SimpleLiteraryEditor';
import FinancialTableEditor from '@/components/supervisor-reports/FinancialTableEditor';
import ValidationStatus from '@/components/supervisor-reports/ValidationStatus';
import ReportExporter from '@/components/supervisor-reports/ReportExporter';
import { validateReport } from '@/utils/reportValidation';
import { loadOfficeSettings, OfficeSettings } from '@/utils/officeSettings';

interface FinancialRow {
  id: string;
  category: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
}

interface SupervisorReport {
  id: number;
  title: string;
  description?: string;
  periodStart: string;
  periodEnd: string;
  literaryContent?: any;
  financialData?: FinancialRow[];
  status: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export default function EditSupervisorReportPage() {
  const router = useRouter();
  const params = useParams();
  const reportId = params.id as string;
  
  // بيانات التقرير الأساسية
  const [report, setReport] = useState<SupervisorReport | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [periodStart, setPeriodStart] = useState<Date>(new Date());
  const [periodEnd, setPeriodEnd] = useState<Date>(new Date());
  const [literaryContent, setLiteraryContent] = useState('');
  const [financialData, setFinancialData] = useState<FinancialRow[]>([]);

  // إعدادات المكتب البلدي
  const [officeSettings, setOfficeSettings] = useState<OfficeSettings | null>(null);

  // حالات التحميل والحفظ
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [useSimpleEditor, setUseSimpleEditor] = useState(true);

  // تحميل إعدادات المكتب البلدي
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const settings = loadOfficeSettings();
        setOfficeSettings(settings);
      } catch (error) {
        console.error('خطأ في تحميل إعدادات المكتب البلدي:', error);
      }
    };

    loadSettings();
  }, []);

  // تحميل بيانات التقرير
  useEffect(() => {
    if (reportId) {
      fetchReport();
    }
  }, [reportId]);

  const fetchReport = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/supervisor-reports/${reportId}`);
      if (response.ok) {
        const result = await response.json();
        const reportData = result.data;
        
        setReport(reportData);
        setTitle(reportData.title);
        setDescription(reportData.description || '');
        setPeriodStart(new Date(reportData.periodStart));
        setPeriodEnd(new Date(reportData.periodEnd));
        setLiteraryContent(reportData.literaryContent || '');
        setFinancialData(reportData.financialData || []);
      } else {
        console.error('فشل في تحميل التقرير');
        router.push('/admin/supervisor-reports');
      }
    } catch (error) {
      console.error('خطأ في تحميل التقرير:', error);
      router.push('/admin/supervisor-reports');
    } finally {
      setIsLoading(false);
    }
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = 'عنوان التقرير مطلوب';
    }

    if (periodStart >= periodEnd) {
      newErrors.period = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // حفظ التقرير
  const handleSave = async (status?: 'DRAFT' | 'PUBLISHED') => {
    if (!validateForm()) return;
    
    setIsSaving(true);
    try {
      const response = await fetch(`/api/supervisor-reports/${reportId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          periodStart: periodStart.toISOString(),
          periodEnd: periodEnd.toISOString(),
          literaryContent,
          financialData,
          ...(status && { status })
        }),
      });

      if (response.ok) {
        router.push('/admin/supervisor-reports');
      } else {
        console.error('فشل في تحديث التقرير');
      }
    } catch (error) {
      console.error('خطأ في تحديث التقرير:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري تحميل التقرير...</span>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="container mx-auto p-6" dir="rtl">
        <div className="text-center py-12">
          <p className="text-gray-500">التقرير غير موجود</p>
          <Button onClick={() => router.push('/admin/supervisor-reports')} className="mt-4">
            العودة للقائمة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      {/* الرأس */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <FileText className="h-8 w-8 text-primary" />
            تعديل التقرير الموحد
          </h1>
          <p className="text-gray-600 mt-1">
            تعديل التقرير: {report.title}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={report.status === 'PUBLISHED' ? 'default' : 'secondary'}>
            {report.status === 'PUBLISHED' ? 'منشور' : 'مسودة'}
          </Badge>
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            رجوع
          </Button>
        </div>
      </div>

      {/* معلومات التقرير الأساسية */}
      <Card>
        <CardHeader>
          <CardTitle>معلومات التقرير الأساسية</CardTitle>
          <CardDescription>
            تعديل المعلومات الأساسية للتقرير
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">عنوان التقرير *</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="مثال: التقرير الشهري لشهر يناير 2024"
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && (
                <p className="text-sm text-red-500">{errors.title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">وصف التقرير</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="وصف مختصر للتقرير..."
                rows={3}
              />
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>تاريخ بداية الفترة *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !periodStart && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {periodStart ? format(periodStart, "PPP", { locale: ar }) : "اختر التاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={periodStart}
                    onSelect={(date) => date && setPeriodStart(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>تاريخ نهاية الفترة *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !periodEnd && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {periodEnd ? format(periodEnd, "PPP", { locale: ar }) : "اختر التاريخ"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={periodEnd}
                    onSelect={(date) => date && setPeriodEnd(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {errors.period && (
            <p className="text-sm text-red-500">{errors.period}</p>
          )}
        </CardContent>
      </Card>

      {/* القسم الأدبي */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                📘 التقرير الأدبي
              </CardTitle>
              <CardDescription>
                اختر نوع المحرر المناسب لك
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={useSimpleEditor ? 'default' : 'outline'}
                size="sm"
                onClick={() => setUseSimpleEditor(true)}
              >
                محرر مبسط
              </Button>
              <Button
                variant={!useSimpleEditor ? 'default' : 'outline'}
                size="sm"
                onClick={() => setUseSimpleEditor(false)}
              >
                محرر متقدم
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {useSimpleEditor ? (
        <SimpleLiteraryEditor
          value={literaryContent}
          onChange={setLiteraryContent}
          periodStart={periodStart}
          periodEnd={periodEnd}
          isLoading={false}
        />
      ) : (
        <LiteraryReportEditor
          value={literaryContent}
          onChange={setLiteraryContent}
          periodStart={periodStart}
          periodEnd={periodEnd}
          isLoading={false}
        />
      )}

      {/* القسم المالي */}
      <FinancialTableEditor
        data={financialData}
        onChange={setFinancialData}
        periodStart={periodStart}
        periodEnd={periodEnd}
      />

      {/* التحقق من صحة البيانات */}
      <ValidationStatus
        reportData={{
          title,
          description,
          periodStart,
          periodEnd,
          literaryContent,
          financialData
        }}
      />

      {/* معاينة وتصدير التقرير */}
      {officeSettings && (
        <ReportExporter
          reportData={{
            title,
            description,
            periodStart,
            periodEnd,
            literaryContent,
            financialData
          }}
          officeSettings={officeSettings}
        />
      )}

      {/* أزرار الحفظ */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                onClick={() => window.open(`/admin/supervisor-reports/view/${reportId}`, '_blank')}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                معاينة سريعة
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button
                onClick={() => handleSave('DRAFT')}
                variant="outline"
                disabled={isSaving}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                حفظ كمسودة
              </Button>
              <Button
                onClick={() => handleSave('PUBLISHED')}
                disabled={isSaving}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isSaving ? 'جاري الحفظ...' : 'حفظ وتحديث التقرير'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

// تعريف أنواع البيانات
interface ReportData {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any; // نحتاج إلى استخدام any هنا لدعم أنواع مختلفة من البيانات
}

interface FinancialReportData extends ReportData {
  title?: string;
  startDate: string;
  endDate: string;
  totalIncome: number;
  totalExpenses: number;
  balance: number;
  incomes?: IncomeData[];
  expenses?: ExpenseData[];
  monthlyData?: MonthlyFinancialData[];
}

interface IncomeData {
  date: string;
  category: string;
  amount: number;
  description?: string;
}

interface ExpenseData {
  date: string;
  category: string;
  amount: number;
  description?: string;
}

interface MonthlyFinancialData {
  month: string;
  income: number;
  expense: number;
}

interface BudgetReportData extends ReportData {
  name: string;
  description?: string;
  startDate: string;
  endDate: string;
  status: string;
  totalAmount: number;
  totalActualAmount?: number;
  totalRemainingAmount?: number;
  items?: BudgetItemData[];
}

interface BudgetItemData {
  category?: {
    name: string;
    id?: number;
  };
  amount: number;
  actualAmount?: number;
  remainingAmount?: number;
  notes?: string;
}

/**
 * توليد ملف PDF للتقرير
 * @param reportType نوع التقرير
 * @param reportData بيانات التقرير
 * @returns Buffer يحتوي على ملف PDF
 */
export async function generateReportPdf(reportType: string, reportData: ReportData): Promise<Buffer> {
  const doc = new jsPDF();

  // إضافة العنوان
  let title = 'تقرير';

  switch (reportType) {
    case 'financial':
      title = `التقرير المالي: ${reportData.title || 'تقرير مالي'}`;
      await generateFinancialReportPdf(doc, reportData as FinancialReportData);
      break;

    case 'budget':
      title = `تقرير الميزانية: ${reportData.name || 'ميزانية'}`;
      await generateBudgetReportPdf(doc, reportData as BudgetReportData);
      break;

    // يمكن إضافة المزيد من أنواع التقارير هنا

    default:
      // تقرير عام
      doc.setFontSize(18);
      doc.text(title, 105, 15, { align: 'center' });
      doc.setFontSize(12);
      doc.text('بيانات التقرير:', 200, 30, { align: 'right' });
      doc.text(JSON.stringify(reportData, null, 2), 200, 40, { align: 'right' });
  }

  // تحويل PDF إلى Buffer
  const pdfBuffer = Buffer.from(doc.output('arraybuffer'));
  return pdfBuffer;
}

/**
 * توليد تقرير مالي بتنسيق PDF
 * @param doc مستند PDF
 * @param reportData بيانات التقرير المالي
 */
async function generateFinancialReportPdf(doc: jsPDF, reportData: FinancialReportData): Promise<void> {
  // إضافة العنوان
  doc.setFontSize(18);
  doc.text(`التقرير المالي: ${reportData.title || 'تقرير مالي'}`, 105, 15, { align: 'center' });

  // إضافة التاريخ
  doc.setFontSize(12);
  doc.text(`الفترة: ${reportData.startDate} - ${reportData.endDate}`, 200, 30, { align: 'right' });

  // إضافة ملخص المالي
  doc.text('ملخص المالي:', 200, 45, { align: 'right' });
  doc.text(`إجمالي الإيرادات: ${formatAmount(reportData.totalIncome)}`, 200, 55, { align: 'right' });
  doc.text(`إجمالي المصروفات: ${formatAmount(reportData.totalExpenses)}`, 200, 65, { align: 'right' });
  doc.text(`الرصيد: ${formatAmount(reportData.balance)}`, 200, 75, { align: 'right' });

  // إضافة جدول الإيرادات
  if (reportData.incomes && reportData.incomes.length > 0) {
    doc.text('الإيرادات:', 200, 90, { align: 'right' });

    const incomeTableData = reportData.incomes.map((income: IncomeData) => [
      income.date,
      income.category,
      formatAmount(income.amount),
      income.description || '-'
    ]);

    autoTable(doc, {
      startY: 100,
      head: [['التاريخ', 'الفئة', 'المبلغ', 'الوصف']],
      body: incomeTableData,
      theme: 'grid',
      headStyles: { fillColor: [22, 155, 136], textColor: [255, 255, 255] },
      styles: { font: 'courier', fontSize: 10, halign: 'right' },
      margin: { right: 10, left: 10 }
    });
  }

  // إضافة جدول المصروفات
  if (reportData.expenses && reportData.expenses.length > 0) {
    // الحصول على موضع نهاية الجدول السابق أو استخدام قيمة افتراضية
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const currentY = (doc as any).lastAutoTable?.finalY + 20 || 150;
    doc.text('المصروفات:', 200, currentY, { align: 'right' });

    const expenseTableData = reportData.expenses.map((expense: ExpenseData) => [
      expense.date,
      expense.category,
      formatAmount(expense.amount),
      expense.description || '-'
    ]);

    autoTable(doc, {
      startY: currentY + 10,
      head: [['التاريخ', 'الفئة', 'المبلغ', 'الوصف']],
      body: expenseTableData,
      theme: 'grid',
      headStyles: { fillColor: [239, 68, 68], textColor: [255, 255, 255] },
      styles: { font: 'courier', fontSize: 10, halign: 'right' },
      margin: { right: 10, left: 10 }
    });
  }

  // إضافة رسم بياني للإيرادات والمصروفات
  if (reportData.monthlyData && reportData.monthlyData.length > 0) {
    try {
      // الحصول على موضع نهاية الجدول السابق أو استخدام قيمة افتراضية
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const currentY = (doc as any).lastAutoTable?.finalY + 20 || 200;
      doc.text('الرسم البياني للإيرادات والمصروفات:', 200, currentY, { align: 'right' });

      const chartBuffer = await generateFinancialChart(reportData.monthlyData);
      doc.addImage(chartBuffer, 'PNG', 20, currentY + 10, 170, 100);
    } catch (error) {
      console.error('Error generating chart:', error);
    }
  }
}

/**
 * توليد تقرير ميزانية بتنسيق PDF
 * @param doc مستند PDF
 * @param reportData بيانات تقرير الميزانية
 */
async function generateBudgetReportPdf(doc: jsPDF, reportData: BudgetReportData): Promise<void> {
  // إضافة العنوان
  doc.setFontSize(18);
  doc.text(`تقرير الميزانية: ${reportData.name}`, 105, 15, { align: 'center' });

  // إضافة معلومات الميزانية
  doc.setFontSize(12);
  doc.text(`الوصف: ${reportData.description || '-'}`, 200, 30, { align: 'right' });
  doc.text(`تاريخ البداية: ${formatDate(reportData.startDate)}`, 200, 40, { align: 'right' });
  doc.text(`تاريخ النهاية: ${formatDate(reportData.endDate)}`, 200, 50, { align: 'right' });
  doc.text(`الحالة: ${getBudgetStatusText(reportData.status)}`, 200, 60, { align: 'right' });

  // إضافة ملخص المبالغ
  doc.text(`المبلغ الإجمالي: ${formatAmount(reportData.totalAmount)}`, 200, 75, { align: 'right' });
  doc.text(`المصروفات الفعلية: ${formatAmount(reportData.totalActualAmount || 0)}`, 200, 85, { align: 'right' });
  doc.text(`المتبقي: ${formatAmount(reportData.totalRemainingAmount || (reportData.totalAmount - (reportData.totalActualAmount || 0)))}`, 200, 95, { align: 'right' });

  const utilizationPercentage = reportData.totalAmount > 0
    ? Math.round(((reportData.totalActualAmount || 0) / reportData.totalAmount) * 100)
    : 0;

  doc.text(`نسبة الاستخدام: ${utilizationPercentage}%`, 200, 105, { align: 'right' });

  // إضافة جدول بنود الميزانية
  if (reportData.items && reportData.items.length > 0) {
    doc.text('بنود الميزانية:', 200, 120, { align: 'right' });

    const itemsTableData = reportData.items.map((item: BudgetItemData) => [
      item.category?.name || 'غير محدد',
      formatAmount(item.amount),
      formatAmount(item.actualAmount || 0),
      formatAmount(item.remainingAmount || (item.amount - (item.actualAmount || 0))),
      `${item.amount > 0 ? Math.round(((item.actualAmount || 0) / item.amount) * 100) : 0}%`,
      item.notes || '-'
    ]);

    autoTable(doc, {
      startY: 130,
      head: [['الفئة', 'المبلغ المخصص', 'المصروفات الفعلية', 'المتبقي', 'نسبة الاستخدام', 'ملاحظات']],
      body: itemsTableData,
      theme: 'grid',
      headStyles: { fillColor: [22, 155, 136], textColor: [255, 255, 255] },
      styles: { font: 'courier', fontSize: 10, halign: 'right' },
      margin: { right: 10, left: 10 }
    });
  }

  // إضافة رسم بياني لبنود الميزانية
  if (reportData.items && reportData.items.length > 0) {
    try {
      // الحصول على موضع نهاية الجدول السابق أو استخدام قيمة افتراضية
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const currentY = (doc as any).lastAutoTable?.finalY + 20 || 200;
      doc.text('الرسم البياني لبنود الميزانية:', 200, currentY, { align: 'right' });

      const chartBuffer = await generateBudgetChart(reportData.items);
      doc.addImage(chartBuffer, 'PNG', 20, currentY + 10, 170, 100);
    } catch (error) {
      console.error('Error generating chart:', error);
    }
  }
}

/**
 * توليد رسم بياني للتقرير المالي
 * @param monthlyData بيانات شهرية للإيرادات والمصروفات
 * @returns Buffer يحتوي على صورة الرسم البياني
 */
async function generateFinancialChart(monthlyData: MonthlyFinancialData[]): Promise<Buffer> {
  const width = 600;
  const height = 400;

  try {
    // استخدام dynamic import لتجنب تحذيرات البناء
    const { ChartJSNodeCanvas } = await import('chartjs-node-canvas');
    const chartJSNodeCanvas = new ChartJSNodeCanvas({ width, height, backgroundColour: '#ffffff' });

  const labels = monthlyData.map(item => item.month);
  const incomeData = monthlyData.map(item => item.income);
  const expenseData = monthlyData.map(item => item.expense);

  const configuration = {
    type: 'bar' as const,
    data: {
      labels,
      datasets: [
        {
          label: 'الإيرادات',
          data: incomeData,
          backgroundColor: 'rgba(34, 197, 94, 0.5)',
          borderColor: 'rgb(34, 197, 94)',
          borderWidth: 1
        },
        {
          label: 'المصروفات',
          data: expenseData,
          backgroundColor: 'rgba(239, 68, 68, 0.5)',
          borderColor: 'rgb(239, 68, 68)',
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: 'الإيرادات والمصروفات الشهرية'
        }
      }
    }
  };

    return await chartJSNodeCanvas.renderToBuffer(configuration);
  } catch (error) {
    console.error('خطأ في توليد الرسم البياني المالي:', error);
    // إرجاع buffer فارغ في حالة الخطأ
    return Buffer.alloc(0);
  }
}

/**
 * توليد رسم بياني لتقرير الميزانية
 * @param items بنود الميزانية
 * @returns Buffer يحتوي على صورة الرسم البياني
 */
async function generateBudgetChart(items: BudgetItemData[]): Promise<Buffer> {
  const width = 600;
  const height = 400;

  try {
    // استخدام dynamic import لتجنب تحذيرات البناء
    const { ChartJSNodeCanvas } = await import('chartjs-node-canvas');
    const chartJSNodeCanvas = new ChartJSNodeCanvas({ width, height, backgroundColour: '#ffffff' });

  const labels = items.map(item => item.category?.name || 'غير محدد');
  const allocatedData = items.map(item => item.amount);
  const actualData = items.map(item => item.actualAmount || 0);

  const configuration = {
    type: 'bar' as const,
    data: {
      labels,
      datasets: [
        {
          label: 'المبلغ المخصص',
          data: allocatedData,
          backgroundColor: 'rgba(34, 197, 94, 0.5)',
          borderColor: 'rgb(34, 197, 94)',
          borderWidth: 1
        },
        {
          label: 'المصروفات الفعلية',
          data: actualData,
          backgroundColor: 'rgba(239, 68, 68, 0.5)',
          borderColor: 'rgb(239, 68, 68)',
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: 'مقارنة المبالغ المخصصة والمصروفات الفعلية'
        }
      }
    }
  };

    return await chartJSNodeCanvas.renderToBuffer(configuration);
  } catch (error) {
    console.error('خطأ في توليد رسم بياني الميزانية:', error);
    // إرجاع buffer فارغ في حالة الخطأ
    return Buffer.alloc(0);
  }
}

// دوال مساعدة
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
  } catch {
    return dateString;
  }
}

function formatAmount(amount: number): string {
  return amount.toLocaleString('fr-FR') + ' د.ج';
}

function getBudgetStatusText(status: string): string {
  switch (status) {
    case 'DRAFT': return 'مسودة';
    case 'ACTIVE': return 'نشطة';
    case 'COMPLETED': return 'مكتملة';
    case 'ARCHIVED': return 'مؤرشفة';
    default: return status;
  }
}

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { verifyToken } from "@/utils/verifyToken";

// GET /api/student/exam-points
export async function GET(request: NextRequest) {
  try {
    // التحقق من توكن المستخدم
    const payload = await verifyToken(request);

    if (!payload) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 401 });
    }

    // التحقق من أن المستخدم طالب
    const student = await prisma.student.findFirst({
      where: {
        username: payload.username
      }
    });

    if (!student) {
      return NextResponse.json({
        error: 'غير مصرح لك بالوصول',
        success: false
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const examId = searchParams.get('examId');
    const includeAnswers = searchParams.get('includeAnswers') === 'true';

    if (!examId) {
      return NextResponse.json({
        error: 'معرف الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    // الحصول على نقطة الامتحان للطالب
    const examPoint = await prisma.exam_points.findFirst({
      where: {
        examId: parseInt(examId),
        studentId: student.id
      },
      include: {
        exam: {
          include: {
            examType: true
          }
        },
        student: {
          select: {
            id: true,
            name: true
          }
        },
        studentAnswers: includeAnswers ? {
          include: {
            examQuestion: {
              include: {
                question: {
                  include: {
                    options: {
                      orderBy: {
                        order: 'asc'
                      }
                    },
                    answers: true
                  }
                }
              }
            }
          }
        } : false
      }
    });

    if (!examPoint) {
      // إذا لم يتم العثور على نقطة امتحان، قم بإنشاء واحدة جديدة

      // الحصول على الفصل الدراسي للطالب
      const studentClass = await prisma.student.findUnique({
        where: {
          id: student.id
        },
        select: {
          classeId: true
        }
      });

      const classIds = studentClass?.classeId ? [studentClass.classeId] : [];

      // الحصول على المواد الدراسية للطالب
      const classSubjects = await prisma.classSubject.findMany({
        where: {
          classeId: {
            in: classIds
          }
        }
      });

      if (classSubjects.length === 0) {
        return NextResponse.json({
          error: 'لم يتم العثور على مواد دراسية للطالب',
          success: false
        }, { status: 404 });
      }

      // استخدام أول مادة دراسية متاحة (يمكن تحسين هذا لاحقًا)
      const classSubjectId = classSubjects[0].id;

      // إنشاء نقطة امتحان جديدة
      const newExamPoint = await prisma.exam_points.create({
        data: {
          examId: parseInt(examId),
          studentId: student.id,
          classSubjectId,
          grade: 0,
          status: 'PENDING'
        },
        include: {
          exam: {
            include: {
              examType: true
            }
          },
          student: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      return NextResponse.json({
        data: newExamPoint,
        success: true,
        message: 'تم إنشاء نقطة امتحان جديدة'
      });
    }

    return NextResponse.json({
      data: examPoint,
      success: true,
      message: 'تم جلب نقطة الامتحان بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student exam point:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب نقطة الامتحان',
      success: false
    }, { status: 500 });
  }
}

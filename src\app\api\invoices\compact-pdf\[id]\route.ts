import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import { UserRole } from '@prisma/client';

// خيارات الفاتورة المدمجة
interface CompactInvoiceOptions {
  size: 'thermal' | 'half-a4' | 'business-card';
  includeQR: boolean;
  includeLogo: boolean;
  fontSize: 'small' | 'medium' | 'large';
  layout: 'vertical' | 'horizontal';
  paperSaving: boolean;
  highQuality: boolean;
}

// GET /api/invoices/compact-pdf/[id] - توليد فاتورة مدمجة بصيغة PDF
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🧾 بدء إنشاء فاتورة مدمجة...');

    // التحقق من التوكن والصلاحيات
    const token = req.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData) {
      return NextResponse.json(
        { error: "غير مصرح به" },
        { status: 401 }
      );
    }

    const id = params.id;
    if (!id) {
      return NextResponse.json(
        { error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      );
    }

    // استخراج خيارات الطباعة من معاملات الطلب
    const { searchParams } = new URL(req.url);
    const options: CompactInvoiceOptions = {
      size: (searchParams.get('size') as any) || 'thermal',
      includeQR: searchParams.get('includeQR') === 'true',
      includeLogo: searchParams.get('includeLogo') !== 'false',
      fontSize: (searchParams.get('fontSize') as any) || 'medium',
      layout: (searchParams.get('layout') as any) || 'vertical',
      paperSaving: searchParams.get('paperSaving') === 'true',
      highQuality: searchParams.get('highQuality') !== 'false'
    };

    const isPreview = searchParams.get('preview') === 'true';

    console.log('⚙️ خيارات الطباعة:', options);

    // جلب الفاتورة مع بيانات الطالب والمدفوعات
    const invoice = await prisma.invoice.findUnique({
      where: { id: parseInt(id) },
      include: {
        student: {
          include: {
            guardian: true,
            classe: true
          }
        },
        payments: {
          where: {
            status: 'PAID'
          },
          orderBy: {
            date: 'desc'
          }
        }
      }
    });

    if (!invoice) {
      return NextResponse.json(
        { error: 'الفاتورة غير موجودة' },
        { status: 404 }
      );
    }

    console.log('📄 تم جلب بيانات الفاتورة:', {
      invoiceId: invoice.id,
      studentName: invoice.student.name,
      amount: invoice.amount
    });

    // حساب المبلغ المدفوع والمتبقي
    const paidAmount = invoice.payments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = invoice.amount - paidAmount;

    // إنشاء محتوى HTML للفاتورة المدمجة
    const htmlContent = generateCompactInvoiceHTML(invoice, {
      paidAmount,
      remainingAmount,
      options,
      isPreview
    });

    // إذا كان طلب معاينة، إرجاع HTML
    if (isPreview) {
      return new NextResponse(htmlContent, {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
        },
      });
    }

    // إنشاء PDF مدمج
    const pdfBuffer = await generateCompactPDF(htmlContent, options);

    console.log('✅ تم إنشاء الفاتورة المدمجة بنجاح');

    // إرجاع ملف PDF
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="compact-invoice-${invoice.id}.pdf"`,
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('❌ خطأ في إنشاء الفاتورة المدمجة:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الفاتورة المدمجة' },
      { status: 500 }
    );
  }
}

// دالة إنشاء محتوى HTML للفاتورة المدمجة
function generateCompactInvoiceHTML(invoice: any, data: any): string {
  const { paidAmount, remainingAmount, options, isPreview } = data;
  
  // تحديد أبعاد الفاتورة حسب الحجم المختار
  const dimensions = getDimensions(options.size);
  const fontSize = getFontSize(options.fontSize);
  
  // تنسيق التاريخ بالصيغة الفرنسية
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR');
  };

  // تنسيق المبلغ بالدينار الجزائري
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('fr-FR', { minimumFractionDigits: 2 })} دج`;
  };

  const html = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>فاتورة مدمجة - ${invoice.id}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Arial', sans-serif;
          font-size: ${fontSize.base}px;
          line-height: 1.2;
          color: #000;
          background: white;
          width: ${dimensions.width}mm;
          ${options.size !== 'thermal' ? `height: ${dimensions.height}mm;` : ''}
          margin: 0;
          padding: ${dimensions.padding}mm;
          ${options.paperSaving ? 'print-color-adjust: economy;' : ''}
        }
        
        .invoice-container {
          width: 100%;
          height: 100%;
          border: ${options.paperSaving ? 'none' : '1px solid #ddd'};
          ${options.paperSaving ? '' : 'border-radius: 4px;'}
          overflow: hidden;
        }
        
        .header {
          text-align: center;
          border-bottom: 1px solid #ddd;
          padding-bottom: 4mm;
          margin-bottom: 3mm;
        }
        
        .logo {
          font-size: ${fontSize.large}px;
          margin-bottom: 1mm;
        }
        
        .school-name {
          font-size: ${fontSize.medium}px;
          font-weight: bold;
          margin-bottom: 1mm;
        }
        
        .invoice-info {
          font-size: ${fontSize.small}px;
          color: #666;
        }
        
        .section {
          margin-bottom: 3mm;
          padding-bottom: 2mm;
          border-bottom: 1px dotted #ccc;
        }
        
        .section:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }
        
        .section-title {
          font-size: ${fontSize.small}px;
          font-weight: bold;
          margin-bottom: 1mm;
          color: #333;
        }
        
        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5mm;
          font-size: ${fontSize.small}px;
        }
        
        .amount-row {
          display: flex;
          justify-content: space-between;
          font-weight: bold;
          margin-bottom: 1mm;
        }
        
        .total-amount {
          font-size: ${fontSize.medium}px;
          font-weight: bold;
          color: #000;
        }
        
        .paid-amount {
          color: #28a745;
        }
        
        .remaining-amount {
          color: #dc3545;
        }
        
        .footer {
          text-align: center;
          margin-top: 3mm;
          padding-top: 2mm;
          border-top: 1px solid #ddd;
          font-size: ${fontSize.small}px;
        }
        
        .qr-placeholder {
          width: 15mm;
          height: 15mm;
          border: 1px dashed #ccc;
          margin: 1mm auto;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 8px;
          color: #999;
        }
        
        .contact-info {
          font-size: ${fontSize.small}px;
          color: #666;
          margin-top: 1mm;
        }
        
        @media print {
          body {
            margin: 0;
            padding: ${dimensions.padding}mm;
          }
          
          .invoice-container {
            border: none;
            box-shadow: none;
          }
        }
        
        ${options.layout === 'horizontal' ? `
          .info-row {
            flex-direction: column;
          }
          
          .amount-row {
            flex-direction: column;
            text-align: center;
          }
        ` : ''}
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="header">
          ${options.includeLogo ? '<div class="logo">🏫</div>' : ''}
          <div class="school-name">مدرسة القرآن الكريم</div>
          <div class="invoice-info">
            فاتورة رقم: INV-${invoice.id.toString().padStart(4, '0')}<br>
            التاريخ: ${formatDate(new Date(invoice.issueDate))}
          </div>
        </div>
        
        <!-- معلومات الطالب -->
        <div class="section">
          <div class="info-row">
            <span>الطالب:</span>
            <span>${invoice.student.name}</span>
          </div>
          ${invoice.student.guardian ? `
          <div class="info-row">
            <span>الولي:</span>
            <span>${invoice.student.guardian.name}</span>
          </div>
          ` : ''}
          <div class="info-row">
            <span>الصف:</span>
            <span>${invoice.student.classe?.name || 'غير محدد'}</span>
          </div>
        </div>
        
        <!-- تفاصيل الفاتورة -->
        <div class="section">
          <div class="info-row">
            <span>الفترة:</span>
            <span>${getMonthName(invoice.month)} ${invoice.year}</span>
          </div>
          ${invoice.description ? `
          <div class="info-row">
            <span>الوصف:</span>
            <span>${invoice.description}</span>
          </div>
          ` : ''}
          <div class="info-row">
            <span>الاستحقاق:</span>
            <span>${formatDate(new Date(invoice.dueDate))}</span>
          </div>
        </div>
        
        <!-- المبالغ -->
        <div class="section">
          <div class="amount-row">
            <span>المبلغ الإجمالي:</span>
            <span class="total-amount">${formatCurrency(invoice.amount)}</span>
          </div>
          ${paidAmount > 0 ? `
          <div class="amount-row">
            <span>المدفوع:</span>
            <span class="paid-amount">${formatCurrency(paidAmount)}</span>
          </div>
          ` : ''}
          ${remainingAmount > 0 ? `
          <div class="amount-row">
            <span>المتبقي:</span>
            <span class="remaining-amount">${formatCurrency(remainingAmount)}</span>
          </div>
          ` : ''}
        </div>
        
        <!-- التذييل -->
        <div class="footer">
          ${options.includeQR ? '<div class="qr-placeholder">QR Code</div>' : ''}
          <div class="contact-info">
            📞 0123456789 | 📧 <EMAIL>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
}

// دالة تحديد أبعاد الفاتورة
function getDimensions(size: string) {
  switch (size) {
    case 'thermal':
      return { width: 80, height: 'auto', padding: 2 };
    case 'half-a4':
      return { width: 105, height: 148, padding: 5 };
    case 'business-card':
      return { width: 85, height: 55, padding: 2 };
    default:
      return { width: 80, height: 'auto', padding: 2 };
  }
}

// دالة تحديد أحجام الخطوط
function getFontSize(size: string) {
  switch (size) {
    case 'small':
      return { base: 8, small: 7, medium: 9, large: 10 };
    case 'medium':
      return { base: 10, small: 8, medium: 11, large: 12 };
    case 'large':
      return { base: 12, small: 10, medium: 13, large: 14 };
    default:
      return { base: 10, small: 8, medium: 11, large: 12 };
  }
}

// دالة الحصول على اسم الشهر بالعربية
function getMonthName(month: number): string {
  const months = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  return months[month - 1] || 'غير محدد';
}

// دالة إنشاء PDF (مبسطة - يمكن تحسينها باستخدام Puppeteer)
async function generateCompactPDF(htmlContent: string, options: CompactInvoiceOptions): Promise<Buffer> {
  // هذه دالة مبسطة - في التطبيق الحقيقي يجب استخدام Puppeteer أو مكتبة مشابهة
  // لتحويل HTML إلى PDF بجودة عالية
  
  console.log('📄 إنشاء PDF مدمج...');
  
  // محاكاة إنشاء PDF - يجب استبدالها بتطبيق حقيقي
  const pdfContent = Buffer.from(htmlContent, 'utf-8');
  
  return pdfContent;
}

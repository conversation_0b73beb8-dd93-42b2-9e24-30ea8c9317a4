'use client';

import React, { forwardRef, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { LiteraryReportData } from '@/lib/reports/literary-report-service';
import { FinancialReportData } from '@/lib/reports/financial-report-service';
import { LiteraryReportExporter } from '@/lib/export/literary-report';
import { FinancialReportExporter } from '@/lib/export/financial-report';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { Download, Printer, FileText, FileSpreadsheet } from 'lucide-react';
import { toast } from 'react-hot-toast';

// تعريف أنواع التقارير
export type ReportType = 'literary' | 'financial';

interface EnhancedPrintableReportProps {
  reportType: ReportType;
  data: LiteraryReportData | FinancialReportData;
  title?: string;
  showWatermark?: boolean;
  watermarkText?: string;
  showExportButtons?: boolean;
}

// مكون التقرير المحسن القابل للطباعة
const EnhancedPrintableReport = forwardRef<HTMLDivElement, EnhancedPrintableReportProps>(
  ({ 
    reportType, 
    data, 
    title, 
    showWatermark = false, 
    watermarkText = 'مسودة',
    showExportButtons = true 
  }, ref) => {
    const printRef = useRef<HTMLDivElement>(null);

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('ar-DZ', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      }).format(amount);
    };

    // وظائف التصدير
    const handleExportPDF = async () => {
      try {
        toast.loading('جاري تصدير PDF...');
        
        if (reportType === 'literary') {
          const blob = await LiteraryReportExporter.exportToPDF(data as LiteraryReportData, {
            watermark: showWatermark ? watermarkText : undefined
          });
          
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `التقرير_الأدبي_${format(new Date(), 'yyyy-MM-dd')}.pdf`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        } else {
          const blob = await FinancialReportExporter.exportToPDF(data as FinancialReportData, {
            watermark: showWatermark ? watermarkText : undefined
          });
          
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `التقرير_المالي_${format(new Date(), 'yyyy-MM-dd')}.pdf`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
        
        toast.dismiss();
        toast.success('تم تصدير PDF بنجاح');
      } catch (error) {
        toast.dismiss();
        toast.error('فشل في تصدير PDF');
        console.error('Export PDF error:', error);
      }
    };

    const handleExportWord = async () => {
      try {
        toast.loading('جاري تصدير Word...');
        
        if (reportType === 'literary') {
          const blob = await LiteraryReportExporter.exportToWord(data as LiteraryReportData);
          
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `التقرير_الأدبي_${format(new Date(), 'yyyy-MM-dd')}.docx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
        
        toast.dismiss();
        toast.success('تم تصدير Word بنجاح');
      } catch (error) {
        toast.dismiss();
        toast.error('فشل في تصدير Word');
        console.error('Export Word error:', error);
      }
    };

    const handleExportExcel = async () => {
      try {
        toast.loading('جاري تصدير Excel...');
        
        if (reportType === 'financial') {
          const blob = await FinancialReportExporter.exportToExcel(data as FinancialReportData);
          
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `التقرير_المالي_${format(new Date(), 'yyyy-MM-dd')}.xlsx`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
        
        toast.dismiss();
        toast.success('تم تصدير Excel بنجاح');
      } catch (error) {
        toast.dismiss();
        toast.error('فشل في تصدير Excel');
        console.error('Export Excel error:', error);
      }
    };

    const handlePrint = () => {
      window.print();
    };

    const renderLiteraryReport = (reportData: LiteraryReportData) => (
      <>
        {/* رأس التقرير الأدبي */}
        <div className="text-center mb-8 border-b pb-6">
          <h1 className="text-2xl font-bold mb-2">جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن</h1>
          <h2 className="text-xl mb-2">المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر</h2>
          <h3 className="text-lg mb-4">شعبة بلدية المنقر</h3>
          <h2 className="text-xl font-bold mb-2">
            {title || 'التقريــر الأدبــــــــي لشعبة بلديــة المنقر'}
          </h2>
          <p className="text-gray-600">
            للفترة من: {format(new Date(reportData.period.startDate), 'PPP', { locale: ar })} 
            إلى: {format(new Date(reportData.period.endDate), 'PPP', { locale: ar })}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            تاريخ إنشاء التقرير: {format(new Date(), 'PPP', { locale: ar })}
          </p>
        </div>

        {/* المقدمة */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">* مقدمة:</h3>
          <div className="text-gray-700 leading-relaxed space-y-4">
            <p>
              بناء على القرار المؤرخ في صفر 1436هـ الموافق لـ ديسمبر 2014م الذي يُحدد دفتر الشروط 
              المتعلق بإنشاء المؤسسات التعليمية والنوادي والمعاهد التابعة لجمعية العلماء المسلمين 
              الجزائريين ومراقبتها.
            </p>
            <p>
              - وعملا بقانون الجمعيات رقم 12 - 06، وتطبيقا للمادتين 05 و 58 من القانون الأساسي.
            </p>
            <p>
              - ووفقا للقانون المنظم للعملية التربوية لاسيما المادة 12 المحددة لمهام مدير المدرسة وواجباته.
            </p>
            <p>
              نَعرض على أسماعكم المحترمة الأعمال التالية التي قامت بها الجمعية بهياكلها أو من يمثلها 
              نيابة عن أعضائها ومن ذلك:
            </p>
          </div>
        </div>

        {/* الإحصائيات العامة */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">* الإحصائيات العامة:</h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300 mb-4">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 p-3 text-right font-bold">البيان</th>
                  <th className="border border-gray-300 p-3 text-center font-bold">العدد</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-300 p-3">إجمالي الطلاب</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.generalStats.totalStudents}</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">إجمالي المعلمين</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.generalStats.totalTeachers}</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">عدد الصفوف</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.generalStats.totalClasses}</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">عدد الحفاظ</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.generalStats.totalMemorizers}</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">مجالس الختم</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.generalStats.totalKhatmSessions}</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">الأنشطة المنجزة</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.generalStats.totalActivities}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* باقي أقسام التقرير الأدبي */}
        <div className="mb-8 page-break">
          <h3 className="text-lg font-bold mb-4">1/ الجانب التربوي:</h3>
          <p className="text-gray-700 mb-4">
            يبلغ إجمالي عدد الطلاب المسجلين في الشعبة <strong>{reportData.studentsDetails.total}</strong> طالباً وطالبة، 
            موزعين على <strong>{reportData.studentsDetails.classes.length}</strong> صفوف دراسية.
          </p>
          
          <div className="overflow-x-auto mb-4">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 p-2 text-right">الصف</th>
                  <th className="border border-gray-300 p-2 text-center">عدد الطلاب</th>
                  <th className="border border-gray-300 p-2 text-center">السعة</th>
                  <th className="border border-gray-300 p-2 text-center">نسبة الإشغال</th>
                </tr>
              </thead>
              <tbody>
                {reportData.studentsDetails.classes.map((classe) => (
                  <tr key={classe.id}>
                    <td className="border border-gray-300 p-2">{classe.name}</td>
                    <td className="border border-gray-300 p-2 text-center">{classe.studentsCount}</td>
                    <td className="border border-gray-300 p-2 text-center">{classe.capacity}</td>
                    <td className="border border-gray-300 p-2 text-center">
                      {classe.capacity > 0 ? Math.round((classe.studentsCount / classe.capacity) * 100) : 0}%
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <p className="text-gray-700">
            معدل الحضور العام: <strong>{reportData.attendanceDetails.attendanceRate.toFixed(1)}%</strong>
          </p>
        </div>

        {/* الخاتمة والتوقيع */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">* الخاتمة:</h3>
          <p className="text-gray-700 leading-relaxed">
            هذا ما تيسر عرضه من أعمال الشعبة خلال الفترة المحددة، ونسأل الله التوفيق والسداد في خدمة كتاب الله وسنة رسوله صلى الله عليه وسلم.
          </p>
        </div>

        <div className="text-left mt-12">
          <p className="mb-2">تقرير يوم {format(new Date(), 'PPP', { locale: ar })}</p>
          <p className="mb-8">عن رئيس المكتب البلدي:</p>
          <p className="font-bold">الوليد بن ناصر قصي</p>
        </div>
      </>
    );

    const renderFinancialReport = (reportData: FinancialReportData) => (
      <>
        {/* رأس التقرير المالي */}
        <div className="text-center mb-8 border-b pb-6">
          <h1 className="text-2xl font-bold mb-2">جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن</h1>
          <h2 className="text-xl mb-2">المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر</h2>
          <h3 className="text-lg mb-4">شعبة بلدية المنقر</h3>
          <h2 className="text-xl font-bold mb-2">
            {title || 'التقرير المالي لشعبة بلدية المنقر'}
          </h2>
          <p className="text-gray-600">
            للفترة من: {format(new Date(reportData.period.startDate), 'PPP', { locale: ar })} 
            إلى: {format(new Date(reportData.period.endDate), 'PPP', { locale: ar })}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            تاريخ إنشاء التقرير: {format(new Date(), 'PPP', { locale: ar })}
          </p>
        </div>

        {/* الملخص التنفيذي */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">📊 الملخص التنفيذي</h3>
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-sm text-gray-600">الرصيد الافتتاحي</div>
                <div className="text-lg font-bold">{formatCurrency(reportData.executiveSummary.openingBalance)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">إجمالي المداخيل</div>
                <div className="text-lg font-bold text-green-600">{formatCurrency(reportData.executiveSummary.totalIncome)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">إجمالي المصروفات</div>
                <div className="text-lg font-bold text-red-600">{formatCurrency(reportData.executiveSummary.totalExpenses)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">الرصيد الختامي</div>
                <div className="text-lg font-bold">{formatCurrency(reportData.executiveSummary.closingBalance)}</div>
              </div>
            </div>
            <div className="text-center mt-4 pt-4 border-t">
              <span className="text-lg font-medium">صافي الربح/الخسارة: </span>
              <span className={`text-xl font-bold ${
                reportData.executiveSummary.netProfit >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {formatCurrency(reportData.executiveSummary.netProfit)}
              </span>
            </div>
          </div>
        </div>

        {/* تفاصيل المداخيل والمصروفات */}
        <div className="mb-8 page-break">
          <h3 className="text-lg font-bold mb-4">📋 تفاصيل المداخيل</h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-300 p-3 text-right">نوع المدخول</th>
                  <th className="border border-gray-300 p-3 text-center">العدد</th>
                  <th className="border border-gray-300 p-3 text-center">المبلغ</th>
                  <th className="border border-gray-300 p-3 text-center">النسبة</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-300 p-3">مدفوعات الطلاب</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.incomeDetails.studentPayments.count}</td>
                  <td className="border border-gray-300 p-3 text-center">{formatCurrency(reportData.incomeDetails.studentPayments.amount)}</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.incomeDetails.studentPayments.percentage.toFixed(1)}%</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">التبرعات</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.incomeDetails.donations.count}</td>
                  <td className="border border-gray-300 p-3 text-center">{formatCurrency(reportData.incomeDetails.donations.amount)}</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.incomeDetails.donations.percentage.toFixed(1)}%</td>
                </tr>
                <tr>
                  <td className="border border-gray-300 p-3">مداخيل أخرى</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.incomeDetails.otherIncomes.count}</td>
                  <td className="border border-gray-300 p-3 text-center">{formatCurrency(reportData.incomeDetails.otherIncomes.amount)}</td>
                  <td className="border border-gray-300 p-3 text-center">{reportData.incomeDetails.otherIncomes.percentage.toFixed(1)}%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* التوقيع */}
        <div className="text-left mt-12">
          <p className="mb-2">تقرير يوم {format(new Date(), 'PPP', { locale: ar })}</p>
          <p className="mb-8">عن رئيس المكتب البلدي:</p>
          <p className="font-bold">الوليد بن ناصر قصي</p>
        </div>
      </>
    );

    return (
      <div className="w-full" dir="rtl">
        {/* أزرار التصدير والطباعة */}
        {showExportButtons && (
          <div className="flex items-center gap-2 mb-6 print:hidden">
            <Button variant="outline" size="sm" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              طباعة
            </Button>
            
            <Button variant="outline" size="sm" onClick={handleExportPDF}>
              <Download className="h-4 w-4 mr-2" />
              تصدير PDF
            </Button>
            
            {reportType === 'literary' && (
              <Button variant="outline" size="sm" onClick={handleExportWord}>
                <FileText className="h-4 w-4 mr-2" />
                تصدير Word
              </Button>
            )}
            
            {reportType === 'financial' && (
              <Button variant="outline" size="sm" onClick={handleExportExcel}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                تصدير Excel
              </Button>
            )}
          </div>
        )}

        {/* محتوى التقرير */}
        <div 
          ref={ref || printRef}
          className="printable-report bg-white p-8 max-w-4xl mx-auto relative print:max-w-full print:mx-0 print:p-4"
        >
          {/* العلامة المائية */}
          {showWatermark && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
              <div 
                className="text-gray-200 text-6xl font-bold transform rotate-45 opacity-20"
                style={{ fontSize: '8rem' }}
              >
                {watermarkText}
              </div>
            </div>
          )}

          {/* محتوى التقرير */}
          <div className="relative z-20">
            {reportType === 'literary' 
              ? renderLiteraryReport(data as LiteraryReportData)
              : renderFinancialReport(data as FinancialReportData)
            }
          </div>
        </div>

        {/* أنماط الطباعة */}
        <style jsx>{`
          @media print {
            .printable-report {
              max-width: 100% !important;
              margin: 0 !important;
              padding: 20px !important;
              box-shadow: none !important;
            }
            
            .page-break {
              page-break-before: always;
            }
            
            table {
              border-collapse: collapse !important;
              width: 100% !important;
            }
            
            th, td {
              border: 1px solid #000 !important;
              padding: 8px !important;
            }
            
            .print\\:hidden {
              display: none !important;
            }
            
            .print\\:max-w-full {
              max-width: 100% !important;
            }
            
            .print\\:mx-0 {
              margin-left: 0 !important;
              margin-right: 0 !important;
            }
            
            .print\\:p-4 {
              padding: 1rem !important;
            }
          }
        `}</style>
      </div>
    );
  }
);

EnhancedPrintableReport.displayName = 'EnhancedPrintableReport';

export default EnhancedPrintableReport;

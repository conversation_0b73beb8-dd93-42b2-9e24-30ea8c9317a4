import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/reports/budget - الحصول على تقارير الميزانية
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const budgetId = searchParams.get('budgetId');

    // إذا تم تحديد ميزانية معينة
    if (budgetId) {
      const budget = await prisma.budget.findUnique({
        where: { id: parseInt(budgetId) },
        include: {
          items: {
            include: {
              category: true,
            },
          },
        },
      });

      if (!budget) {
        return NextResponse.json(
          { error: 'الميزانية غير موجودة' },
          { status: 404 }
        );
      }

      // جلب المصروفات الفعلية خلال فترة الميزانية
      const expenses = await prisma.expense.findMany({
        where: {
          date: {
            gte: budget.startDate,
            lte: budget.endDate,
          },
        },
        include: {
          category: true,
        },
      });

      // تجميع المصروفات حسب الفئة
      const categoryExpenses: { [key: number]: number } = {};
      const uncategorizedExpenses = expenses
        .filter(expense => !expense.categoryId)
        .reduce((sum, expense) => sum + expense.amount, 0);

      expenses.forEach(expense => {
        if (expense.categoryId) {
          if (!categoryExpenses[expense.categoryId]) {
            categoryExpenses[expense.categoryId] = 0;
          }
          categoryExpenses[expense.categoryId] += expense.amount;
        }
      });

      // إعداد بيانات التقرير
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
      const totalBudget = budget.totalAmount;
      const remainingBudget = totalBudget - totalExpenses;
      const budgetUtilizationPercentage = totalBudget > 0 ? (totalExpenses / totalBudget) * 100 : 0;

      // تحليل بنود الميزانية
      const budgetItemsAnalysis = budget.items.map(item => {
        const actualExpense = categoryExpenses[item.categoryId] || 0;
        const remainingAmount = item.amount - actualExpense;
        const utilizationPercentage = item.amount > 0 ? (actualExpense / item.amount) * 100 : 0;

        return {
          id: item.id,
          categoryId: item.categoryId,
          categoryName: item.category.name,
          budgetAmount: item.amount,
          actualExpense,
          remainingAmount,
          utilizationPercentage,
          status: getUtilizationStatus(utilizationPercentage),
        };
      });

      // تجميع المصروفات حسب الشهر
      const monthlyData: { [key: string]: number } = {};
      
      expenses.forEach(expense => {
        const date = expense.date;
        const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        
        if (!monthlyData[yearMonth]) {
          monthlyData[yearMonth] = 0;
        }
        
        monthlyData[yearMonth] += expense.amount;
      });

      // تحويل البيانات الشهرية إلى مصفوفة
      const monthlyExpenses = Object.entries(monthlyData)
        .map(([month, amount]) => ({ month, amount }))
        .sort((a, b) => a.month.localeCompare(b.month));

      return NextResponse.json({
        budget: {
          id: budget.id,
          name: budget.name,
          startDate: budget.startDate,
          endDate: budget.endDate,
          totalAmount: totalBudget,
          status: budget.status,
        },
        summary: {
          totalBudget,
          totalExpenses,
          remainingBudget,
          budgetUtilizationPercentage,
          status: getOverallStatus(budgetUtilizationPercentage),
        },
        items: budgetItemsAnalysis,
        monthlyExpenses,
        uncategorizedExpenses,
      });
    } else {
      // إذا لم يتم تحديد ميزانية، نقوم بإرجاع ملخص لجميع الميزانيات
      const budgets = await prisma.budget.findMany({
        orderBy: { startDate: 'desc' },
        include: {
          items: true,
        },
      });

      // تحليل كل ميزانية
      const budgetSummaries = await Promise.all(
        budgets.map(async budget => {
          // جلب المصروفات الفعلية خلال فترة الميزانية
          const expenses = await prisma.expense.findMany({
            where: {
              date: {
                gte: budget.startDate,
                lte: budget.endDate,
              },
            },
          });

          const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
          const totalBudget = budget.totalAmount;
          const remainingBudget = totalBudget - totalExpenses;
          const budgetUtilizationPercentage = totalBudget > 0 ? (totalExpenses / totalBudget) * 100 : 0;

          return {
            id: budget.id,
            name: budget.name,
            startDate: budget.startDate,
            endDate: budget.endDate,
            totalBudget,
            totalExpenses,
            remainingBudget,
            budgetUtilizationPercentage,
            status: getOverallStatus(budgetUtilizationPercentage),
            itemsCount: budget.items.length,
          };
        })
      );

      return NextResponse.json({
        budgets: budgetSummaries,
      });
    }
  } catch (error) {
    console.error('خطأ في جلب تقارير الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب تقارير الميزانية' },
      { status: 500 }
    );
  }
}

// تحديد حالة استخدام الميزانية
function getUtilizationStatus(percentage: number): string {
  if (percentage < 50) {
    return 'UNDER_UTILIZED'; // أقل من المتوقع
  } else if (percentage <= 90) {
    return 'NORMAL'; // طبيعي
  } else if (percentage <= 100) {
    return 'NEAR_LIMIT'; // قريب من الحد
  } else {
    return 'OVER_BUDGET'; // تجاوز الميزانية
  }
}

// تحديد الحالة العامة للميزانية
function getOverallStatus(percentage: number): string {
  if (percentage < 70) {
    return 'GOOD'; // جيد
  } else if (percentage <= 95) {
    return 'WARNING'; // تحذير
  } else {
    return 'CRITICAL'; // حرج
  }
}

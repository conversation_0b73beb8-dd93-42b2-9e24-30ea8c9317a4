#!/usr/bin/env ts-node

import { PrismaClient } from '@prisma/client';
import { seedBasicData } from './seeders/basic-data';
import { seedQuranicSchoolExams } from './seeders/quranic-school-exams';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 بدء التنفيذ الشامل لبذور المدرسة القرآنية');
  console.log('=' .repeat(70));
  console.log('📋 هذا السكريبت سينشئ:');
  console.log('   🏫 الفصول الدراسية');
  console.log('   👥 الطلاب وأولياء الأمور');
  console.log('   👨‍🏫 المعلمين');
  console.log('   📚 المواد الدراسية');
  console.log('   📝 أنواع الامتحانات');
  console.log('   🎯 الامتحانات الفصلية');
  console.log('   📈 نقاط الطلاب');
  console.log('   📏 معايير التقييم');
  console.log('=' .repeat(70));
  console.log('');

  try {
    // المرحلة الأولى: إنشاء البيانات الأساسية
    console.log('🔄 المرحلة 1/2: إنشاء البيانات الأساسية...');
    await seedBasicData();
    console.log('✅ تمت المرحلة الأولى بنجاح!');
    console.log('');

    // المرحلة الثانية: إنشاء الامتحانات والنقاط
    console.log('🔄 المرحلة 2/2: إنشاء الامتحانات والنقاط...');
    await seedQuranicSchoolExams();
    console.log('✅ تمت المرحلة الثانية بنجاح!');
    console.log('');

    // إحصائيات نهائية شاملة
    console.log('=' .repeat(70));
    console.log('🎉 تم إنشاء جميع البيانات بنجاح!');
    console.log('');

    const stats = await getCompleteStats();
    
    console.log('📊 إحصائيات شاملة للبيانات المُنشأة:');
    console.log('');
    console.log('👥 الطلاب والفصول:');
    console.log(`   👦👧 إجمالي الطلاب: ${stats.students}`);
    console.log(`   🏫 إجمالي الفصول: ${stats.classes}`);
    console.log(`   👨‍👩‍👧‍👦 أولياء الأمور: ${stats.guardians}`);
    console.log(`   👨‍🏫 المعلمين: ${stats.teachers}`);
    console.log('');
    
    console.log('📚 المناهج والامتحانات:');
    console.log(`   📖 المواد الدراسية: ${stats.subjects}`);
    console.log(`   📝 أنواع الامتحانات: ${stats.examTypes}`);
    console.log(`   🎯 إجمالي الامتحانات: ${stats.exams}`);
    console.log(`   📈 نقاط الامتحانات: ${stats.examPoints}`);
    console.log(`   📏 معايير التقييم: ${stats.criteria}`);
    console.log('');

    // تفاصيل الامتحانات حسب الفصول
    const examsByTerm = await getExamsByTerm();
    console.log('📅 توزيع الامتحانات حسب الفصول:');
    console.log(`   🍂 الفصل الأول (سبتمبر-ديسمبر): ${examsByTerm.firstTerm} امتحان`);
    console.log(`   ❄️  الفصل الثاني (يناير-مارس): ${examsByTerm.secondTerm} امتحان`);
    console.log(`   🌸 الفصل الثالث (أبريل-يونيو): ${examsByTerm.thirdTerm} امتحان`);
    console.log('');

    // إحصائيات الأداء
    const performanceStats = await getPerformanceStats();
    console.log('📈 إحصائيات الأداء:');
    console.log(`   ✅ الطلاب الناجحون: ${performanceStats.passed} (${performanceStats.passRate}%)`);
    console.log(`   ❌ الطلاب المحتاجون للتحسين: ${performanceStats.failed} (${performanceStats.failRate}%)`);
    console.log(`   🏆 متوسط الدرجات العام: ${performanceStats.averageGrade}/20`);
    console.log('');

    console.log('🔍 كيفية استخدام البيانات:');
    console.log('');
    console.log('1️⃣  عرض كشف درجات طالب:');
    console.log('   🌐 اذهب إلى: http://localhost:3000/admin/evaluation/student-report');
    console.log('   👤 اختر طالب من القائمة المنسدلة');
    console.log('   📊 اختر معايير التصفية (اختياري)');
    console.log('   📋 اضغط "إنشاء كشف الدرجات"');
    console.log('   🖨️  اطبع الكشف أو احفظه كـ PDF');
    console.log('');

    console.log('2️⃣  إدارة الامتحانات:');
    console.log('   📝 إضافة امتحان جديد: /admin/evaluation/exams');
    console.log('   📈 إدخال النقاط: /admin/evaluation/points');
    console.log('   📊 عرض الإحصائيات: /admin/evaluation/dashboard');
    console.log('');

    console.log('3️⃣  إدارة الطلاب:');
    console.log('   👥 قائمة الطلاب: /admin/students');
    console.log('   🏫 إدارة الفصول: /admin/classes');
    console.log('   👨‍👩‍👧‍👦 أولياء الأمور: /admin/guardians');
    console.log('');

    console.log('📋 المواد المُنشأة:');
    const subjects = await prisma.subject.findMany();
    subjects.forEach((subject, index) => {
      console.log(`   ${index + 1}. ${subject.name}`);
    });
    console.log('');

    console.log('🎯 أنواع الامتحانات:');
    const examTypes = await prisma.examType.findMany();
    examTypes.forEach((type, index) => {
      console.log(`   ${index + 1}. ${type.name}`);
    });
    console.log('');

    console.log('=' .repeat(70));
    console.log('✨ تم إعداد المدرسة القرآنية بنجاح! يمكنك الآن استخدام النظام.');
    console.log('🤲 بارك الله فيكم ووفقكم في خدمة كتاب الله الكريم');
    console.log('=' .repeat(70));

  } catch (error) {
    console.error('❌ خطأ في التنفيذ:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// دالة للحصول على إحصائيات شاملة
async function getCompleteStats() {
  const [
    students,
    classes,
    guardians,
    teachers,
    subjects,
    examTypes,
    exams,
    examPoints,
    criteria
  ] = await Promise.all([
    prisma.student.count(),
    prisma.classe.count(),
    prisma.parent.count(),
    0, // المعلمين (سيتم إضافتهم لاحقاً)
    prisma.subject.count(),
    prisma.examType.count(),
    prisma.exam.count(),
    prisma.exam_points.count(),
    0 // المعايير (سيتم إضافتها لاحقاً)
  ]);

  return {
    students,
    classes,
    guardians,
    teachers,
    subjects,
    examTypes,
    exams,
    examPoints,
    criteria
  };
}

// دالة للحصول على إحصائيات الامتحانات حسب الفصول
async function getExamsByTerm() {
  const [firstTerm, secondTerm, thirdTerm] = await Promise.all([
    prisma.exam.count({
      where: {
        month: {
          in: ['2024-09', '2024-10', '2024-11', '2024-12']
        }
      }
    }),
    prisma.exam.count({
      where: {
        month: {
          in: ['2025-01', '2025-02', '2025-03']
        }
      }
    }),
    prisma.exam.count({
      where: {
        month: {
          in: ['2025-04', '2025-05', '2025-06']
        }
      }
    })
  ]);

  return { firstTerm, secondTerm, thirdTerm };
}

// دالة للحصول على إحصائيات الأداء
async function getPerformanceStats() {
  const totalPoints = await prisma.exam_points.count();
  const passedPoints = await prisma.exam_points.count({
    where: { status: 'PASSED' }
  });
  const failedPoints = totalPoints - passedPoints;
  
  const averageResult = await prisma.exam_points.aggregate({
    _avg: { grade: true }
  });

  const passRate = totalPoints > 0 ? Math.round((passedPoints / totalPoints) * 100) : 0;
  const failRate = 100 - passRate;
  const averageGrade = averageResult._avg.grade ? Math.round(Number(averageResult._avg.grade) * 10) / 10 : 0;

  return {
    passed: passedPoints,
    failed: failedPoints,
    passRate,
    failRate,
    averageGrade
  };
}

// تنفيذ الدالة الرئيسية
main()
  .catch((e) => {
    console.error('💥 خطأ غير متوقع:', e);
    process.exit(1);
  });

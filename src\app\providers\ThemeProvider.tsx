"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Theme = {
  primary: string;
  secondary: string;
  background: string;
  text: string;
};

type ThemeContextType = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const defaultTheme: Theme = {
  primary: 'var(--primary-color)',
  secondary: 'var(--secondary-color)',
  background: '#ffffff',
  text: '#000000',
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

type ThemeProviderProps = {
  children: ReactNode;
};

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme);

  useEffect(() => {
    // Fetch default theme from API on initial load
    fetch('/api/themes/default')
      .then(response => response.json())
      .then(data => {
        if (data) {
          setTheme({
            primary: data.primaryColor,
            secondary: data.secondaryColor,
            background: data.backgroundColor,
            text: data.textColor,
          });
        }
      })
      .catch(error => console.error('Error fetching theme:', error));
  }, []);

  useEffect(() => {
    // Apply theme to document root
    const root = document.documentElement;
    root.style.setProperty('--primary-color', theme.primary);
    root.style.setProperty('--secondary-color', theme.secondary);
    root.style.setProperty('--background-color', theme.background);
    root.style.setProperty('--text-color', theme.text);
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}
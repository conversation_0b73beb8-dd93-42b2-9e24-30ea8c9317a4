"use client";
import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { FaQuran, FaArrowRight, FaSave } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface Student {
  id: number;
  name: string;
}

interface Surah {
  id: number;
  name: string;
  number: number;
  totalAyahs: number;
}

const AddQuranProgressPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const studentIdParam = searchParams ? searchParams.get('studentId') : null;

  const [students, setStudents] = useState<Student[]>([]);
  const [surahs, setSurahs] = useState<Surah[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [selectedSurah, setSelectedSurah] = useState<string>('');
  const [selectedSurahDetails, setSelectedSurahDetails] = useState<Surah | null>(null);
  const [fromAyah, setFromAyah] = useState<string>('1');
  const [toAyah, setToAyah] = useState<string>('');
  const [grade, setGrade] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // جلب قائمة الطلاب
        const studentsResponse = await fetch('/api/teacher-students');
        if (!studentsResponse.ok) {
          throw new Error('فشل في جلب بيانات الطلاب');
        }
        const studentsData = await studentsResponse.json();
        setStudents(studentsData.students);

        // جلب قائمة السور
        const surahsResponse = await fetch('/api/surahs');
        if (!surahsResponse.ok) {
          throw new Error('فشل في جلب بيانات السور');
        }
        const surahsData = await surahsResponse.json();
        setSurahs(surahsData);

        // تعيين الطالب إذا تم تمريره في الرابط
        if (studentIdParam) {
          setSelectedStudent(studentIdParam);
        }
      } catch (err: unknown) {
        console.error('Error fetching data:', err);
        setError('حدث خطأ أثناء جلب البيانات');
        toast.error('فشل في جلب البيانات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [studentIdParam]);

  useEffect(() => {
    if (selectedSurah) {
      const surah = surahs.find(s => s.id.toString() === selectedSurah);
      setSelectedSurahDetails(surah || null);

      if (surah) {
        setToAyah(surah.totalAyahs.toString());
      } else {
        setToAyah('');
      }
    } else {
      setSelectedSurahDetails(null);
      setToAyah('');
    }
  }, [selectedSurah, surahs]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // التحقق من البيانات
      if (!selectedStudent || !selectedSurah || !fromAyah || !toAyah || !grade) {
        toast.error('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      if (!selectedSurahDetails) {
        toast.error('يرجى اختيار سورة صحيحة');
        return;
      }

      const fromAyahNum = parseInt(fromAyah);
      const toAyahNum = parseInt(toAyah);
      const gradeNum = parseFloat(grade);

      if (isNaN(fromAyahNum) || isNaN(toAyahNum) || isNaN(gradeNum)) {
        toast.error('يرجى إدخال أرقام صحيحة');
        return;
      }

      if (fromAyahNum < 1 || toAyahNum > selectedSurahDetails.totalAyahs) {
        toast.error(`أرقام الآيات يجب أن تكون بين 1 و ${selectedSurahDetails.totalAyahs}`);
        return;
      }

      if (fromAyahNum > toAyahNum) {
        toast.error('رقم الآية الأولى يجب أن يكون أقل من أو يساوي رقم الآية الأخيرة');
        return;
      }

      if (gradeNum < 0 || gradeNum > 10) {
        toast.error('التقدير يجب أن يكون بين 0 و 10');
        return;
      }

      setIsSubmitting(true);

      // إرسال البيانات
      const response = await fetch('/api/quran-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          studentId: selectedStudent,
          surahId: selectedSurah,
          fromAyah: fromAyahNum,
          toAyah: toAyahNum,
          grade: gradeNum,
          notes
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('تم إضافة تقدم الحفظ بنجاح');

        // العودة إلى صفحة تفاصيل الطالب
        router.push(`/teachers/students/${selectedStudent}`);
      } else {
        throw new Error(data.message || 'فشل في إضافة تقدم الحفظ');
      }
    } catch (err: unknown) {
      console.error('Error submitting quran progress:', err);
      const errorMessage = err instanceof Error ? err.message : 'فشل في إضافة تقدم الحفظ';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">إضافة تقدم حفظ القرآن</h1>
        <Button variant="outline" onClick={() => router.back()}>
          <FaArrowRight className="ml-2" />
          العودة
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FaQuran className="text-[var(--primary-color)]" />
            <span>تسجيل تقدم جديد</span>
          </CardTitle>
          <CardDescription>
            إضافة تقدم جديد في حفظ القرآن الكريم للطالب
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
              </div>
            ) : error ? (
              <div className="text-center text-red-500 py-4">{error}</div>
            ) : (
              <>
                <div className="space-y-2">
                  <Label htmlFor="student">الطالب</Label>
                  <Select
                    value={selectedStudent}
                    onValueChange={setSelectedStudent}
                    disabled={!!studentIdParam}
                  >
                    <SelectTrigger id="student">
                      <SelectValue placeholder="اختر الطالب" />
                    </SelectTrigger>
                    <SelectContent>
                      {students.map(student => (
                        <SelectItem key={student.id} value={student.id.toString()}>
                          {student.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="surah">السورة</Label>
                  <Select value={selectedSurah} onValueChange={setSelectedSurah}>
                    <SelectTrigger id="surah">
                      <SelectValue placeholder="اختر السورة" />
                    </SelectTrigger>
                    <SelectContent>
                      {surahs.map(surah => (
                        <SelectItem key={surah.id} value={surah.id.toString()}>
                          {surah.name} ({surah.totalAyahs} آية)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="fromAyah">من آية</Label>
                    <Input
                      id="fromAyah"
                      type="number"
                      min="1"
                      max={selectedSurahDetails?.totalAyahs || "1"}
                      value={fromAyah}
                      onChange={(e) => setFromAyah(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="toAyah">إلى آية</Label>
                    <Input
                      id="toAyah"
                      type="number"
                      min={fromAyah}
                      max={selectedSurahDetails?.totalAyahs || "1"}
                      value={toAyah}
                      onChange={(e) => setToAyah(e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="grade">التقدير (من 10)</Label>
                  <Input
                    id="grade"
                    type="number"
                    min="0"
                    max="10"
                    step="0.5"
                    value={grade}
                    onChange={(e) => setGrade(e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">ملاحظات</Label>
                  <Textarea
                    id="notes"
                    placeholder="أدخل ملاحظات حول مستوى الحفظ والتجويد..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </div>
              </>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              type="button"
              onClick={() => router.back()}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isLoading || isSubmitting}
            >
              {isSubmitting ? (
                <span className="flex items-center gap-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
                  جاري الحفظ...
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <FaSave />
                  حفظ
                </span>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default AddQuranProgressPage;

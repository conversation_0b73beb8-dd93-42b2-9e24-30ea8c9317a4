import { exportToPdf } from './export-utils';
import { formatRecommendations, Recommendation } from './recommendations-utils';

// تعريف واجهات البيانات
interface AnalysisData {
  summary?: SummaryData;
  gradeDistribution?: GradeDistributionData;
  classeAnalysis?: ClassAnalysisData[];
  teacherAnalysis?: TeacherAnalysisData[];
  questionTypeAnalysis?: QuestionTypeAnalysisData[];
  difficultyAnalysis?: DifficultyAnalysisData[];
  genderAnalysis?: GenderAnalysisData;
  recommendations?: RecommendationData[];
}

interface SummaryData {
  totalStudents: number;
  passedStudents: number;
  failedStudents: number;
  excellentStudents: number;
  pendingStudents: number;
  passRate: number;
  averageGrade: number;
  highestGrade: number;
  lowestGrade: number;
}

interface GradeDistributionData {
  excellent: number;
  veryGood: number;
  good: number;
  fair: number;
  poor: number;
  veryPoor: number;
}

interface ClassAnalysisData {
  name: string;
  totalStudents: number;
  passedStudents: number;
  failedStudents: number;
  excellentStudents: number;
  averageGrade: number;
}

interface TeacherAnalysisData {
  name: string;
  totalStudents: number;
  passedStudents: number;
  failedStudents: number;
  excellentStudents: number;
  averageGrade: number;
}

interface QuestionTypeAnalysisData {
  type: string;
  totalAnswers: number;
  correctAnswers: number;
  incorrectAnswers: number;
  pendingAnswers: number;
  averagePoints: number;
}

interface DifficultyAnalysisData {
  level: string;
  totalAnswers: number;
  correctAnswers: number;
  incorrectAnswers: number;
  pendingAnswers: number;
  averagePoints: number;
}

interface GenderAnalysisData {
  MALE?: GenderData;
  FEMALE?: GenderData;
}

interface GenderData {
  totalStudents: number;
  passedStudents: number;
  failedStudents: number;
  excellentStudents: number;
  pendingStudents: number;
  averageGrade: number;
}

// استخدام واجهة Recommendation من ملف recommendations-utils
type RecommendationData = Recommendation;

/**
 * تصدير بيانات التحليل
 * @param data بيانات التحليل
 * @param activeTab علامة التبويب النشطة
 * @param format صيغة التصدير (excel أو pdf)
 * @param fileName اسم الملف
 */
export const exportAnalysisReport = (
  data: AnalysisData,
  activeTab: string,
  format: 'excel' | 'pdf',
  fileName: string = 'تقرير-التحليل'
) => {
  try {
    if (!data) {
      console.error('No data to export');
      return;
    }

    // تحديد عنوان التقرير بناءً على علامة التبويب النشطة
    let title = 'تقرير تحليل نتائج الامتحانات';
    switch (activeTab) {
      case 'summary':
        title = 'ملخص نتائج الامتحانات';
        break;
      case 'classes':
        title = 'تحليل أداء الفصول';
        break;
      case 'teachers':
        title = 'تحليل أداء المعلمين';
        break;
      case 'questions':
        title = 'تحليل أنواع الأسئلة';
        break;
      case 'difficulty':
        title = 'تحليل مستويات الصعوبة';
        break;
      case 'gender':
        title = 'تحليل النتائج حسب الجنس';
        break;
      case 'recommendations':
        title = 'التوصيات والإجراءات المقترحة';
        break;
    }

    // إعداد بيانات التقرير بناءً على علامة التبويب النشطة
    const tables: { title?: string; headers: string[]; data: (string | number | null)[][] }[] = [];
    const charts: {
      title?: string;
      type: 'bar' | 'line' | 'pie' | 'doughnut';
      data: {
        labels: string[];
        datasets: {
          label: string;
          data: number[];
          backgroundColor?: string | string[];
          borderColor?: string | string[];
        }[];
      };
      options?: {
        width?: number;
        height?: number;
      };
    }[] = [];

    // إضافة بيانات حسب علامة التبويب النشطة
    switch (activeTab) {
      case 'summary':
        // إضافة جدول ملخص النتائج
        tables.push({
          title: 'ملخص النتائج',
          headers: ['المؤشر', 'القيمة'],
          data: [
            ['إجمالي الطلاب', data.summary?.totalStudents || 0],
            ['الطلاب الناجحون', data.summary?.passedStudents || 0],
            ['الطلاب الراسبون', data.summary?.failedStudents || 0],
            ['الطلاب المتميزون', data.summary?.excellentStudents || 0],
            ['الطلاب قيد الانتظار', data.summary?.pendingStudents || 0],
            ['نسبة النجاح', `${data.summary?.passRate?.toFixed(1) || 0}%`],
            ['متوسط الدرجات', data.summary?.averageGrade?.toFixed(1) || 0],
            ['أعلى درجة', data.summary?.highestGrade?.toFixed(1) || 0],
            ['أدنى درجة', data.summary?.lowestGrade?.toFixed(1) || 0]
          ]
        });

        // إضافة جدول توزيع الدرجات
        if (data.gradeDistribution) {
          tables.push({
            title: 'توزيع الدرجات',
            headers: ['التقدير', 'عدد الطلاب'],
            data: [
              ['ممتاز', data.gradeDistribution?.excellent || 0],
              ['جيد جداً', data.gradeDistribution?.veryGood || 0],
              ['جيد', data.gradeDistribution?.good || 0],
              ['مقبول', data.gradeDistribution?.fair || 0],
              ['ضعيف', data.gradeDistribution?.poor || 0],
              ['ضعيف جداً', data.gradeDistribution?.veryPoor || 0]
            ]
          });
        }

        // إضافة رسم بياني لتوزيع الدرجات
        if (data.gradeDistribution) {
          charts.push({
            title: 'توزيع الدرجات',
            type: 'pie',
            data: {
              labels: ['ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف', 'ضعيف جداً'],
              datasets: [
                {
                  label: 'عدد الطلاب',
                  data: [
                    data.gradeDistribution?.excellent || 0,
                    data.gradeDistribution?.veryGood || 0,
                    data.gradeDistribution?.good || 0,
                    data.gradeDistribution?.fair || 0,
                    data.gradeDistribution?.poor || 0,
                    data.gradeDistribution?.veryPoor || 0
                  ],
                  backgroundColor: [
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(255, 99, 132, 0.7)'
                  ]
                }
              ]
            },
            options: {
              width: 500,
              height: 300
            }
          });
        }

        // إضافة رسم بياني لحالة الطلاب
        charts.push({
          title: 'حالة الطلاب',
          type: 'bar',
          data: {
            labels: ['ناجح', 'راسب', 'ممتاز', 'قيد الانتظار'],
            datasets: [
              {
                label: 'عدد الطلاب',
                data: [
                  (data.summary?.passedStudents || 0) - (data.summary?.excellentStudents || 0),
                  data.summary?.failedStudents || 0,
                  data.summary?.excellentStudents || 0,
                  data.summary?.pendingStudents || 0
                ],
                backgroundColor: [
                  'rgba(75, 192, 192, 0.7)',
                  'rgba(255, 99, 132, 0.7)',
                  'rgba(255, 206, 86, 0.7)',
                  'rgba(153, 102, 255, 0.7)'
                ]
              }
            ]
          },
          options: {
            width: 500,
            height: 300
          }
        });
        break;

      case 'classes':
        // إضافة جدول تحليل الفصول
        if (data.classeAnalysis && data.classeAnalysis.length > 0) {
          const classesData = data.classeAnalysis.map((classe: ClassAnalysisData) => [
            classe.name,
            classe.totalStudents,
            classe.averageGrade.toFixed(1),
            `${((classe.passedStudents / classe.totalStudents) * 100).toFixed(1)}%`,
            classe.excellentStudents,
            classe.failedStudents
          ]);

          tables.push({
            title: 'تحليل أداء الفصول',
            headers: ['الفصل', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'],
            data: classesData
          });
        }

        // إضافة رسم بياني لمتوسط درجات الفصول
        if (data.classeAnalysis && data.classeAnalysis.length > 0) {
          charts.push({
            title: 'متوسط درجات الفصول',
            type: 'bar',
            data: {
              labels: data.classeAnalysis.map((classe: ClassAnalysisData) => classe.name),
              datasets: [
                {
                  label: 'متوسط الدرجات',
                  data: data.classeAnalysis.map((classe: ClassAnalysisData) => classe.averageGrade),
                  backgroundColor: 'rgba(75, 192, 192, 0.7)'
                }
              ]
            },
            options: {
              width: 600,
              height: 300
            }
          });
        }
        break;

      case 'teachers':
        // إضافة جدول تحليل المعلمين
        if (data.teacherAnalysis && data.teacherAnalysis.length > 0) {
          const teachersData = data.teacherAnalysis.map((teacher: TeacherAnalysisData) => [
            teacher.name,
            teacher.totalStudents,
            teacher.averageGrade.toFixed(1),
            `${((teacher.passedStudents / teacher.totalStudents) * 100).toFixed(1)}%`,
            teacher.excellentStudents,
            teacher.failedStudents
          ]);

          tables.push({
            title: 'تحليل أداء المعلمين',
            headers: ['المعلم', 'عدد الطلاب', 'متوسط الدرجات', 'نسبة النجاح', 'الطلاب المتميزون', 'الطلاب الراسبون'],
            data: teachersData
          });
        }

        // إضافة رسم بياني لمتوسط درجات طلاب المعلمين
        if (data.teacherAnalysis && data.teacherAnalysis.length > 0) {
          charts.push({
            title: 'متوسط درجات طلاب المعلمين',
            type: 'bar',
            data: {
              labels: data.teacherAnalysis.map((teacher: TeacherAnalysisData) => teacher.name),
              datasets: [
                {
                  label: 'متوسط الدرجات',
                  data: data.teacherAnalysis.map((teacher: TeacherAnalysisData) => teacher.averageGrade),
                  backgroundColor: 'rgba(54, 162, 235, 0.7)'
                }
              ]
            },
            options: {
              width: 600,
              height: 300
            }
          });
        }
        break;

      case 'questions':
        // إضافة جدول تحليل أنواع الأسئلة
        if (data.questionTypeAnalysis && data.questionTypeAnalysis.length > 0) {
          const getQuestionTypeLabel = (type: string): string => {
            const labels: Record<string, string> = {
              MULTIPLE_CHOICE: 'اختيار من متعدد',
              TRUE_FALSE: 'صح أو خطأ',
              SHORT_ANSWER: 'إجابة قصيرة',
              ESSAY: 'مقال',
              MATCHING: 'مطابقة',
              FILL_BLANK: 'ملء الفراغات',
              ORDERING: 'ترتيب'
            };
            return labels[type] || type;
          };

          const questionsData = data.questionTypeAnalysis.map((type: QuestionTypeAnalysisData) => {
            const totalAnswers = type.correctAnswers + type.incorrectAnswers + type.pendingAnswers;
            const correctRate = totalAnswers > 0 ? (type.correctAnswers / totalAnswers) * 100 : 0;
            return [
              getQuestionTypeLabel(type.type),
              type.totalAnswers,
              type.correctAnswers,
              type.incorrectAnswers,
              type.pendingAnswers,
              `${correctRate.toFixed(1)}%`,
              type.averagePoints.toFixed(1)
            ];
          });

          tables.push({
            title: 'تحليل أنواع الأسئلة',
            headers: ['نوع السؤال', 'إجمالي الإجابات', 'إجابات صحيحة', 'إجابات خاطئة', 'قيد الانتظار', 'نسبة الإجابات الصحيحة', 'متوسط النقاط'],
            data: questionsData
          });
        }

        // إضافة رسم بياني لنسبة الإجابات الصحيحة حسب نوع السؤال
        if (data.questionTypeAnalysis && data.questionTypeAnalysis.length > 0) {
          const getQuestionTypeLabel = (type: string): string => {
            const labels: Record<string, string> = {
              MULTIPLE_CHOICE: 'اختيار من متعدد',
              TRUE_FALSE: 'صح أو خطأ',
              SHORT_ANSWER: 'إجابة قصيرة',
              ESSAY: 'مقال',
              MATCHING: 'مطابقة',
              FILL_BLANK: 'ملء الفراغات',
              ORDERING: 'ترتيب'
            };
            return labels[type] || type;
          };

          charts.push({
            title: 'نسبة الإجابات الصحيحة حسب نوع السؤال',
            type: 'bar',
            data: {
              labels: data.questionTypeAnalysis.map((type: QuestionTypeAnalysisData) => getQuestionTypeLabel(type.type)),
              datasets: [
                {
                  label: 'نسبة الإجابات الصحيحة',
                  data: data.questionTypeAnalysis.map((type: QuestionTypeAnalysisData) => {
                    const totalAnswers = type.correctAnswers + type.incorrectAnswers;
                    return totalAnswers > 0 ? (type.correctAnswers / totalAnswers) * 100 : 0;
                  }),
                  backgroundColor: 'rgba(153, 102, 255, 0.7)'
                }
              ]
            },
            options: {
              width: 600,
              height: 300
            }
          });
        }
        break;

      case 'difficulty':
        // إضافة جدول تحليل مستويات الصعوبة
        if (data.difficultyAnalysis && data.difficultyAnalysis.length > 0) {
          const getDifficultyLevelLabel = (level: string): string => {
            const labels: Record<string, string> = {
              EASY: 'سهل',
              MEDIUM: 'متوسط',
              HARD: 'صعب',
              VERY_HARD: 'صعب جداً'
            };
            return labels[level] || level;
          };

          const difficultyData = data.difficultyAnalysis.map((level: DifficultyAnalysisData) => {
            const totalAnswers = level.correctAnswers + level.incorrectAnswers + level.pendingAnswers;
            const correctRate = totalAnswers > 0 ? (level.correctAnswers / totalAnswers) * 100 : 0;
            return [
              getDifficultyLevelLabel(level.level),
              level.totalAnswers,
              level.correctAnswers,
              level.incorrectAnswers,
              level.pendingAnswers,
              `${correctRate.toFixed(1)}%`,
              level.averagePoints.toFixed(1)
            ];
          });

          tables.push({
            title: 'تحليل مستويات الصعوبة',
            headers: ['مستوى الصعوبة', 'إجمالي الإجابات', 'إجابات صحيحة', 'إجابات خاطئة', 'قيد الانتظار', 'نسبة الإجابات الصحيحة', 'متوسط النقاط'],
            data: difficultyData
          });
        }

        // إضافة رسم بياني لنسبة الإجابات الصحيحة حسب مستوى الصعوبة
        if (data.difficultyAnalysis && data.difficultyAnalysis.length > 0) {
          const getDifficultyLevelLabel = (level: string): string => {
            const labels: Record<string, string> = {
              EASY: 'سهل',
              MEDIUM: 'متوسط',
              HARD: 'صعب',
              VERY_HARD: 'صعب جداً'
            };
            return labels[level] || level;
          };

          charts.push({
            title: 'نسبة الإجابات الصحيحة حسب مستوى الصعوبة',
            type: 'bar',
            data: {
              labels: data.difficultyAnalysis.map((level: DifficultyAnalysisData) => getDifficultyLevelLabel(level.level)),
              datasets: [
                {
                  label: 'نسبة الإجابات الصحيحة',
                  data: data.difficultyAnalysis.map((level: DifficultyAnalysisData) => {
                    const totalAnswers = level.correctAnswers + level.incorrectAnswers;
                    return totalAnswers > 0 ? (level.correctAnswers / totalAnswers) * 100 : 0;
                  }),
                  backgroundColor: 'rgba(255, 159, 64, 0.7)'
                }
              ]
            },
            options: {
              width: 600,
              height: 300
            }
          });
        }
        break;

      case 'gender':
        // إضافة جدول تحليل النتائج حسب الجنس
        if (data.genderAnalysis) {
          const genderData = [
            [
              'ذكور',
              data.genderAnalysis.MALE?.totalStudents || 0,
              data.genderAnalysis.MALE?.passedStudents || 0,
              data.genderAnalysis.MALE?.failedStudents || 0,
              data.genderAnalysis.MALE?.excellentStudents || 0,
              data.genderAnalysis.MALE?.pendingStudents || 0,
              data.genderAnalysis.MALE?.averageGrade?.toFixed(1) || 0,
              data.genderAnalysis.MALE?.totalStudents && data.genderAnalysis.MALE.totalStudents > 0 && data.genderAnalysis.MALE.passedStudents !== undefined
                ? `${((data.genderAnalysis.MALE.passedStudents / data.genderAnalysis.MALE.totalStudents) * 100).toFixed(1)}%`
                : '0%'
            ],
            [
              'إناث',
              data.genderAnalysis.FEMALE?.totalStudents || 0,
              data.genderAnalysis.FEMALE?.passedStudents || 0,
              data.genderAnalysis.FEMALE?.failedStudents || 0,
              data.genderAnalysis.FEMALE?.excellentStudents || 0,
              data.genderAnalysis.FEMALE?.pendingStudents || 0,
              data.genderAnalysis.FEMALE?.averageGrade?.toFixed(1) || 0,
              data.genderAnalysis.FEMALE?.totalStudents && data.genderAnalysis.FEMALE.totalStudents > 0 && data.genderAnalysis.FEMALE.passedStudents !== undefined
                ? `${((data.genderAnalysis.FEMALE.passedStudents / data.genderAnalysis.FEMALE.totalStudents) * 100).toFixed(1)}%`
                : '0%'
            ]
          ];

          tables.push({
            title: 'تحليل النتائج حسب الجنس',
            headers: ['الجنس', 'إجمالي الطلاب', 'الطلاب الناجحون', 'الطلاب الراسبون', 'الطلاب المتميزون', 'قيد الانتظار', 'متوسط الدرجات', 'نسبة النجاح'],
            data: genderData
          });
        }

        // إضافة رسم بياني لمقارنة متوسط الدرجات حسب الجنس
        if (data.genderAnalysis) {
          charts.push({
            title: 'مقارنة متوسط الدرجات حسب الجنس',
            type: 'bar',
            data: {
              labels: ['ذكور', 'إناث'],
              datasets: [
                {
                  label: 'متوسط الدرجات',
                  data: [
                    data.genderAnalysis.MALE?.averageGrade || 0,
                    data.genderAnalysis.FEMALE?.averageGrade || 0
                  ],
                  backgroundColor: ['rgba(54, 162, 235, 0.7)', 'rgba(255, 99, 132, 0.7)']
                }
              ]
            },
            options: {
              width: 500,
              height: 300
            }
          });
        }
        break;

      case 'recommendations':
        // إضافة جدول التوصيات
        if (data.recommendations && data.recommendations.length > 0) {
          const recommendationsData = data.recommendations.map((rec: RecommendationData) => {
            const priorityLabel = rec.priority === 'high' ? 'عالية' : rec.priority === 'medium' ? 'متوسطة' : 'منخفضة';
            return [
              rec.title,
              rec.description,
              priorityLabel,
              rec.actionItems ? rec.actionItems.join('\n') : ''
            ];
          });

          tables.push({
            title: 'التوصيات والإجراءات المقترحة',
            headers: ['العنوان', 'الوصف', 'الأولوية', 'الإجراءات المقترحة'],
            data: recommendationsData
          });
        }
        break;
    }

    // تصدير البيانات حسب الصيغة المطلوبة
    if (format === 'pdf') {
      // إضافة محتوى إضافي للتوصيات إذا كانت موجودة
      let additionalContent;
      if (data.recommendations && data.recommendations.length > 0 && activeTab !== 'recommendations') {
        additionalContent = [
          {
            text: formatRecommendations(data.recommendations, 'text'),
            x: 10,
            y: 10,
            options: {
              align: 'right' as const
            }
          }
        ];
      }

      exportToPdf({
        title,
        fileName: fileName.endsWith('.pdf') ? fileName : `${fileName}.pdf`,
        tables,
        charts,
        additionalContent
      });
    } else if (format === 'excel') {
      // تصدير إلى Excel - يمكن إضافة هذه الوظيفة لاحقًا
      console.log('Excel export not implemented yet');
    }
  } catch (error) {
    console.error('Error exporting analysis report:', error);
  }
};

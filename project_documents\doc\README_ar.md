# مخططات UML لنظام إدارة مدارس تحفيظ القرآن

يحتوي هذا المجلد على مخططات UML المحسّنة التي تصف بنية ووظائف نظام إدارة مدارس تحفيظ القرآن بشكل تفصيلي ودقيق.

## المخططات المتوفرة

1. **مخطط الفئات** (`class-diagram.puml`)
   - يوضح الكيانات الرئيسية في النظام وعلاقاتها بشكل منظم في حزم
   - يتضمن فئات مثل المستخدم، الطالب، المعلم، ولي الأمر، الفصل، إلخ
   - يعرض الخصائص والعلاقات بين الكيانات والعمليات التي يمكن تنفيذها
   - تم تنظيم الفئات في حزم منطقية: إدارة المستخدمين، النظام التعليمي، التعلم عن بعد، والنظام المالي

2. **مخطط حالة الاستخدام** (`use-case-diagram.puml`)
   - يوضح الوظائف الرئيسية للنظام من منظور المستخدمين المختلفين
   - يبين ما يمكن للمسؤولين والمعلمين والطلاب وأولياء الأمور والنظام القيام به
   - تم تنظيم حالات الاستخدام في حزم وظيفية: الإدارة، التدريس، التعلم، الإشراف الأبوي، ووظائف النظام
   - يوضح العلاقات المتقدمة مثل التضمين والتوسيع والتعميم

3. **مخططات التسلسل**
   - **تتبع الحضور** (`attendance-sequence-diagram.puml`):
     * يوضح عملية تسجيل حضور الطلاب بشكل تفصيلي
     * يتضمن تفاعلات متعددة مثل تسجيل الحضور والغياب والتأخر
     * يشمل خدمة الإشعارات لإبلاغ أولياء الأمور
     * تم تنظيم التفاعلات في مجموعات منطقية مع ألوان مميزة

   - **تتبع تقدم حفظ القرآن** (`quran-progress-sequence-diagram.puml`):
     * يوضح كيفية تسجيل المعلمين وتتبع تقدم الطلاب في حفظ القرآن
     * يشمل تفاصيل التقييم المختلفة وإشعار الطلاب وأولياء الأمور
     * يوضح كيفية وصول الطلاب وأولياء الأمور إلى معلومات التقدم

4. **مخططات النشاط**
   - **عملية الامتحان** (`exam-activity-diagram.puml`):
     * يوضح سير العمل التفصيلي لإنشاء وإجراء وتقييم الامتحانات
     * يشمل مسارات متعددة للنجاح والإخفاق
     * يوضح أدوار المعلمين والطلاب والإداريين وأولياء الأمور والنظام
     * تم تحسينه بألوان وملاحظات توضيحية

   - **عملية الدفع** (`payment-activity-diagram.puml`):
     * يوضح سير عمل الدفع التفصيلي لأولياء الأمور
     * يشمل طرق الدفع المختلفة (عبر الإنترنت وخارج الإنترنت)
     * يوضح تفاصيل معالجة المدفوعات والتأكيدات والإيصالات
     * تم تحسينه بألوان وملاحظات توضيحية

5. **مخطط المكونات** (`component-diagram.puml`)
   - يوضح المكونات الرئيسية للنظام وتفاعلاتها بشكل تفصيلي
   - تم تنظيم المكونات في حزم منطقية مع ألوان مميزة
   - يتضمن تفاصيل الواجهة الأمامية، الواجهة الخلفية، قاعدة البيانات، والخدمات الخارجية
   - يوضح العلاقات بين المكونات المختلفة بشكل دقيق

6. **مخطط النشر** (`deployment-diagram.puml`)
   - يوضح النشر الفعلي للنظام بشكل تفصيلي
   - يشمل بيئة الإنتاج الكاملة مع خوادم متعددة وتوازن الحمل
   - يوضح أجهزة العملاء المختلفة (سطح المكتب، الجوال، الأجهزة اللوحية)
   - يبين الخدمات السحابية المتكاملة والاتصالات بينها
   - تم تحسينه بألوان وتعليقات توضيحية

## كيفية عرض هذه المخططات

هذه المخططات مكتوبة بتنسيق PlantUML. لعرضها، يمكنك:

1. استخدام عارض PlantUML عبر الإنترنت مثل [PlantUML Server](https://www.plantuml.com/plantuml/uml/)
2. استخدام إضافة PlantUML لبيئة التطوير المتكاملة الخاصة بك (متوفرة لـ VS Code، IntelliJ، إلخ)
3. تثبيت PlantUML محليًا وإنشاء صور من ملفات .puml

## فوائد المخططات المحسّنة

### مخطط الفئات المحسّن
يوضح مخطط الفئات المحسّن نموذج البيانات للنظام بشكل أكثر تنظيماً، حيث تم تجميع الفئات في حزم منطقية تعكس هيكل النظام. تمت إضافة العمليات (methods) لكل فئة لتوضيح السلوك بالإضافة إلى البيانات. كما تمت إضافة فئات جديدة مثل Surah وLearningResource وInvoice لتغطية جوانب إضافية من النظام.

### مخطط حالة الاستخدام المحسّن
تم تنظيم حالات الاستخدام في حزم وظيفية تعكس المجالات الرئيسية للنظام. تمت إضافة ممثل جديد (System) وحالات استخدام جديدة مثل إدارة المناهج وإنشاء المواد التعليمية. كما تم توضيح العلاقات المتقدمة بين حالات الاستخدام مثل التضمين والتوسيع.

### مخططات التسلسل المحسّنة
تم تحسين مخططات التسلسل بإضافة مزيد من التفاصيل والتفاعلات. تمت إضافة خدمة الإشعارات وتفاعلات الطلاب وأولياء الأمور. تم تنظيم التفاعلات في مجموعات منطقية مع ألوان مميزة لتسهيل الفهم. كما تمت إضافة ملاحظات توضيحية للخطوات المهمة.

### مخططات النشاط المحسّنة
تم تحسين مخططات النشاط بإضافة مزيد من التفاصيل والمسارات البديلة. تم استخدام الألوان والمسارات المتوازية لتوضيح تدفق العمل بشكل أفضل. تمت إضافة أدوار مختلفة (المعلم، الطالب، ولي الأمر، الإداري، النظام) وتوضيح مسؤولية كل دور في العملية.

### مخطط المكونات المحسّن
تم تنظيم المكونات في حزم منطقية مع تفاصيل أكثر لكل مكون. تمت إضافة مكونات جديدة مثل خدمات البريد الإلكتروني والرسائل القصيرة. تم توضيح العلاقات بين المكونات بشكل أكثر تفصيلاً مع تعليقات توضيحية.

### مخطط النشر المحسّن
تم تحسين مخطط النشر ليعكس بنية نظام إنتاجي حقيقي مع خوادم متعددة وتوازن الحمل وقواعد بيانات مكررة. تمت إضافة خدمات مثل التخزين المؤقت (Redis) والمراقبة وشبكة توصيل المحتوى (CDN). تم توضيح أنواع مختلفة من أجهزة العملاء والاتصالات بينها.

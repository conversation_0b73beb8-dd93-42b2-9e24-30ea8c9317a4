'use client';

import { useEffect, useState } from 'react';
import { applyColors } from '@/utils/simpleColorSystem';
import {
  loadDarkMode,
  loadDarkModeColors,
  loadCurrentColors,
  getCurrentModeColors,
  getCurrentModeColorsSync,
  applyDarkModeClasses,
  saveCurrentColors,
  onDarkModeChange
} from '@/utils/darkModeStorage';
import SiteLogo from '@/components/SiteLogo';

const DarkModeProvider = ({ children }: { children: React.ReactNode }) => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeDarkMode = async () => {
      try {
        // جلب حالة المود الليلي من التخزين المحلي
        const isDarkMode = loadDarkMode();

        // تطبيق كلاسات المود الليلي
        applyDarkModeClasses(isDarkMode);

        // جلب الألوان المناسبة للوضع الحالي
        let currentColors;
        if (isDarkMode) {
          // للوضع المظلم، استخدم النظام الحالي (شخصي للمستخدم)
          currentColors = loadDarkModeColors();
        } else {
          // للوضع النهاري، استخدم نظام التزامن الجديد
          const { getColorsWithFallback } = await import('@/utils/colorSync');
          currentColors = await getColorsWithFallback();
        }

        // تطبيق الألوان
        applyColors(currentColors);

        // حفظ الألوان الحالية
        saveCurrentColors(currentColors);

      } catch (error) {
        console.error('Error initializing dark mode:', error);
        // في حالة الخطأ، استخدم الإعدادات الافتراضية
        const defaultColors = getCurrentModeColorsSync();
        applyColors(defaultColors);
        applyDarkModeClasses(false);
      } finally {
        setIsInitialized(true);
      }
    };

    initializeDarkMode();

    // مراقبة التغييرات في localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'darkMode' && e.newValue && e.newValue !== 'undefined' && e.newValue !== 'null') {
        try {
          const isDarkMode = JSON.parse(e.newValue);
          applyDarkModeClasses(isDarkMode);

          // تحديث الألوان حسب الوضع الجديد
          const newColors = getCurrentModeColors();
          applyColors(newColors);
          saveCurrentColors(newColors);
        } catch (error) {
          console.error('Error parsing dark mode from storage:', error);
        }
      }

      if (e.key === 'siteColors' && e.newValue && e.newValue !== 'undefined' && e.newValue !== 'null') {
        try {
          const colors = JSON.parse(e.newValue);
          applyColors(colors);
        } catch (error) {
          console.error('Error applying colors from storage:', error);
        }
      }
    };

    // مراقبة الأحداث المخصصة للمود الليلي
    const unsubscribe = onDarkModeChange(async (isDarkMode) => {
      applyDarkModeClasses(isDarkMode);

      // تحديث الألوان حسب الوضع الجديد
      let newColors;
      if (isDarkMode) {
        // للوضع المظلم، استخدم النظام الحالي
        newColors = loadDarkModeColors();
      } else {
        // للوضع النهاري، استخدم نظام التزامن
        const { getColorsWithFallback } = await import('@/utils/colorSync');
        newColors = await getColorsWithFallback();
      }

      applyColors(newColors);
      saveCurrentColors(newColors);
    });

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      unsubscribe(); // إلغاء الاستماع للأحداث المخصصة
    };
  }, []);

  // عرض شاشة تحميل بسيطة حتى يتم تطبيق الألوان
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center" dir="rtl">
        <div className="text-center">
          {/* شعار الموقع */}
          <div className="flex justify-center mb-6">
            <SiteLogo size="lg" showText={false} />
          </div>

          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الإعدادات...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default DarkModeProvider;

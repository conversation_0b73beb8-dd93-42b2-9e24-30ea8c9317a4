"use client";

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aKey, FaPlus, FaEdit, FaTrash, FaTimes, FaSearch } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface Permission {
  id: number;
  key: string;
  name: string;
  description?: string;
  category: string;
  route?: string;
  isActive: boolean;
}

interface Role {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  isSystem: boolean;
  isActive: boolean;
  permissions: Permission[];
  _count?: {
    users: number;
  };
}

// هيكل شجرة الصفحات
const pageTree = {
  '/admin': {
    name: 'لوحة التحكم الرئيسية',
    children: {
      '/admin/users': { name: 'إدارة المستخدمين' },
      '/admin/roles-permissions': { name: 'إدارة الأدوار والصلاحيات' },
      '/admin/employee-dashboard': { name: 'لوحة تحكم الموظف' },
      '/admin/admin-setup': { name: 'إعدادات النظام' },
      '/admin/settings': {
        name: 'الإعدادات',
        children: {
          '/admin/settings/evaluation-config': { name: 'إعدادات التقييم' }
        }
      }
    }
  },
  'الطلاب والتعليم': {
    name: 'الطلاب والتعليم',
    children: {
      '/admin/students': {
        name: 'إدارة الطلاب',
        children: {
          '/admin/students/add': { name: 'إضافة طالب' },
          '/admin/students/edit/[id]': { name: 'تعديل طالب' },
          '/admin/students/progress/[id]': { name: 'تقدم طالب' }
        }
      },
      '/admin/teachers': { name: 'إدارة المعلمين' },
      '/admin/parents': { name: 'إدارة أولياء الأمور' },
      '/admin/classes': { name: 'إدارة الفصول' },
      '/admin/subjects': {
        name: 'إدارة المواد',
        children: {
          '/admin/subjects/[id]': { name: 'تفاصيل المادة' },
          '/admin/subjects/[id]/curriculum': { name: 'منهج المادة' }
        }
      },
      '/admin/levels': { name: 'إدارة المستويات' },
      '/admin/programs': { name: 'إدارة البرامج' },
      '/admin/class-subjects': { name: 'مواد الفصول' },
      '/admin/teacher-subjects': { name: 'مواد المعلمين' },
      '/admin/attendance': {
        name: 'إدارة الحضور',
        children: {
          '/admin/attendance/reports': { name: 'تقارير الحضور' }
        }
      }
    }
  },
  'التقييم والامتحانات': {
    name: 'التقييم والامتحانات',
    children: {
      '/admin/evaluation/dashboard': { name: 'لوحة تحكم التقييم' },
      '/admin/evaluation/analysis': { name: 'تحليل التقييم' },
      '/admin/evaluation/criteria': { name: 'معايير التقييم' },
      '/admin/evaluation/evaluation-types': { name: 'أنواع التقييم' },
      '/admin/evaluation/exams': { name: 'الامتحانات' },
      '/admin/evaluation/exam-types': { name: 'أنواع الامتحان' },
      '/admin/evaluation/exam-questions': { name: 'أسئلة الامتحان' },
      '/admin/evaluation/questions': { name: 'الأسئلة' },
      '/admin/evaluation/question-banks': { name: 'بنوك الأسئلة' },
      '/admin/evaluation/results': { name: 'نتائج التقييم' },
      '/admin/evaluation/scoring': { name: 'نظام النقاط' },
      '/admin/evaluation/help': { name: 'مساعدة التقييم' }
    }
  },
  'النظام المالي': {
    name: 'النظام المالي',
    children: {
      '/admin/payments': { name: 'إدارة المدفوعات' },
      '/admin/payment-methods': { name: 'طرق الدفع' },
      '/admin/invoices': { name: 'إدارة الفواتير' },
      '/admin/discounts': { name: 'إدارة الخصومات' },
      '/admin/treasury': {
        name: 'إدارة الخزينة',
        children: {
          '/admin/treasury/forecasts': { name: 'توقعات الخزينة' }
        }
      },
      '/admin/budgets': {
        name: 'إدارة الميزانيات',
        children: {
          '/admin/budgets/[id]': { name: 'تفاصيل الميزانية' },
          '/admin/budgets/[id]/transfers': { name: 'تحويلات الميزانية' },
          '/admin/budgets/alerts': { name: 'تنبيهات الميزانية' }
        }
      },
      '/admin/donations': {
        name: 'إدارة التبرعات',
        children: {
          '/admin/donations/campaigns': { name: 'حملات التبرع' },
          '/admin/donations/reports': { name: 'تقارير التبرعات' }
        }
      },
      '/admin/expenses': {
        name: 'إدارة المصروفات',
        children: {
          '/admin/expenses/recurring': { name: 'المصروفات المتكررة' },
          '/admin/expenses/reminders': { name: 'تذكيرات المصروفات' }
        }
      },
      '/admin/expense-categories': { name: 'فئات المصروفات' }
    }
  },
  'التقارير والإحصائيات': {
    name: 'التقارير والإحصائيات',
    children: {
      '/admin/reports/financial': { name: 'التقارير المالية' },
      '/admin/reports/cash-flow': { name: 'تقارير التدفق النقدي' },
      '/admin/reports/detailed': { name: 'التقارير التفصيلية' },
      '/admin/financial-reports': { name: 'التقارير المالية (قديم)' },
      '/admin/financial-forecasts': { name: 'التوقعات المالية' }
    }
  },
  'الأنشطة والمكافآت': {
    name: 'الأنشطة والمكافآت',
    children: {
      '/admin/activities': { name: 'الأنشطة' },
      '/admin/rewards': { name: 'المكافآت' },
      '/admin/honor-board': {
        name: 'لوحة الشرف',
        children: {
          '/admin/honor-board/certificates': { name: 'شهادات لوحة الشرف' },
          '/admin/honor-board/criteria': { name: 'معايير لوحة الشرف' }
        }
      }
    }
  },
  'القرآن الكريم': {
    name: 'القرآن الكريم',
    children: {
      '/admin/quran-progress': { name: 'تقدم القرآن' },
      '/admin/khatm-sessions': { name: 'مجالس الختم' },
      '/admin/khatm-attendance/[id]': { name: 'حضور مجلس ختم محدد' },
      '/admin/khatm-progress/[id]': { name: 'تقدم مجلس ختم محدد' },
      '/admin/khatm-reports': { name: 'تقارير مجالس الختم' }
    }
  },
  'صور الطلاب': {
    name: 'صور الطلاب',
    children: {
      '/admin/student-images': { name: 'إدارة صور الطلاب' },
      '/admin/student-images/albums': { name: 'ألبومات الصور' },
      '/admin/student-images/gallery': { name: 'معرض الصور' },
      '/admin/student-images/upload': { name: 'رفع الصور' },
      '/admin/student-images/legacy': { name: 'الصور القديمة' }
    }
  }
};

// تعريف نوع البيانات للشجرة
interface TreeNode {
  name: string;
  children?: Record<string, TreeNode>;
}

// دالة للحصول على اسم الصفحة من الشجرة
const getPageNameFromTree = (path: string): string => {
  const findInTree = (tree: Record<string, TreeNode>): string | null => {
    for (const key in tree) {
      if (key === path) {
        return tree[key].name;
      }
      if (tree[key].children) {
        const found = findInTree(tree[key].children);
        if (found) return found;
      }
    }
    return null;
  };
  return findInTree(pageTree as Record<string, TreeNode>) || path;
};

// دالة للحصول على الصفحة الرئيسية من المسار
const getMainPageFromRoute = (route: string): string => {
  // استخراج الصفحة الرئيسية من المسار
  const parts = route.split('/');
  if (parts.length >= 3) {
    return `/${parts[1]}/${parts[2]}`;
  }
  return route;
};

export default function RolesPermissionsPage() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMainPage, setSelectedMainPage] = useState('all');
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);

  // إضافة/تعديل دور
  const handleSaveRole = async (roleData: { name: string; displayName: string; description?: string }) => {
    try {
      const url = editingRole ? `/api/admin/roles/${editingRole.id}` : '/api/admin/roles';
      const method = editingRole ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(roleData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في حفظ الدور');
      }

      toast.success(editingRole ? 'تم تحديث الدور بنجاح' : 'تم إضافة الدور بنجاح');
      setShowRoleModal(false);
      setEditingRole(null);
      await fetchRoles();
    } catch (error) {
      console.error('Error saving role:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ الدور');
    }
  };

  // حذف دور
  const handleDeleteRole = async (roleId: number) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الدور؟')) return;

    try {
      const response = await fetch(`/api/admin/roles/${roleId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في حذف الدور');
      }

      toast.success('تم حذف الدور بنجاح');
      await fetchRoles();
      if (selectedRole?.id === roleId) {
        setSelectedRole(null);
      }
    } catch (error) {
      console.error('Error deleting role:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حذف الدور');
    }
  };

  // جلب الأدوار
  const fetchRoles = async () => {
    try {
      const response = await fetch('/api/admin/roles');
      if (!response.ok) throw new Error('فشل في جلب الأدوار');
      const data = await response.json();
      setRoles(data.roles || []);
    } catch (error) {
      console.error('Error fetching roles:', error);
      toast.error('فشل في جلب الأدوار');
    }
  };

  // جلب الصلاحيات
  const fetchPermissions = async () => {
    try {
      const response = await fetch('/api/admin/permissions');
      if (!response.ok) throw new Error('فشل في جلب الصلاحيات');
      const data = await response.json();
      setPermissions(data.permissions || []);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      toast.error('فشل في جلب الصلاحيات');
    }
  };

  // جلب صلاحيات دور محدد
  const fetchRolePermissions = async (roleId: number) => {
    try {
      const response = await fetch(`/api/admin/roles/${roleId}/permissions`);
      if (!response.ok) throw new Error('فشل في جلب صلاحيات الدور');
      const data = await response.json();

      setSelectedRole(prev => prev ? {
        ...prev,
        permissions: data.permissions || []
      } : null);
    } catch (error) {
      console.error('Error fetching role permissions:', error);
      toast.error('فشل في جلب صلاحيات الدور');
      // تعيين مصفوفة فارغة في حالة الخطأ
      setSelectedRole(prev => prev ? {
        ...prev,
        permissions: []
      } : null);
    }
  };

  // تحديث صلاحيات الدور
  const updateRolePermissions = async (roleId: number, permissionIds: number[]) => {
    try {
      const response = await fetch(`/api/admin/roles/${roleId}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissionIds }),
      });

      if (!response.ok) throw new Error('فشل في تحديث صلاحيات الدور');

      toast.success('تم تحديث صلاحيات الدور بنجاح');
      await fetchRolePermissions(roleId);
    } catch (error) {
      console.error('Error updating role permissions:', error);
      toast.error('فشل في تحديث صلاحيات الدور');
    }
  };

  // تبديل صلاحية
  const togglePermission = (permissionId: number) => {
    if (!selectedRole) return;

    const rolePermissions = selectedRole.permissions || [];
    const hasPermission = rolePermissions.some(p => p.id === permissionId);
    const newPermissionIds = hasPermission
      ? rolePermissions.filter(p => p.id !== permissionId).map(p => p.id)
      : [...rolePermissions.map(p => p.id), permissionId];

    updateRolePermissions(selectedRole.id, newPermissionIds);
  };

  // تحديد/إلغاء تحديد جميع الصلاحيات في صفحة
  const togglePagePermissions = (mainPage: string, checked: boolean) => {
    if (!selectedRole) return;

    const rolePermissions = selectedRole.permissions || [];
    const pagePermissions = permissions.filter(p =>
      p.route && getMainPageFromRoute(p.route) === mainPage
    );
    const otherPermissions = rolePermissions.filter(p =>
      !p.route || getMainPageFromRoute(p.route) !== mainPage
    );

    const newPermissionIds = checked
      ? [...otherPermissions.map(p => p.id), ...pagePermissions.map(p => p.id)]
      : otherPermissions.map(p => p.id);

    updateRolePermissions(selectedRole.id, newPermissionIds);
  };

  // تحديد/إلغاء تحديد جميع الصلاحيات المعروضة
  const toggleAllFilteredPermissions = (checked: boolean) => {
    if (!selectedRole) return;

    const rolePermissions = selectedRole.permissions || [];
    const filteredPermissionIds = filteredPermissions.map(p => p.id);

    let newPermissionIds: number[];

    if (checked) {
      // إضافة جميع الصلاحيات المفلترة
      const existingIds = rolePermissions.map(p => p.id);
      const newIds = filteredPermissionIds.filter(id => !existingIds.includes(id));
      newPermissionIds = [...existingIds, ...newIds];
    } else {
      // إزالة جميع الصلاحيات المفلترة
      newPermissionIds = rolePermissions
        .filter(p => !filteredPermissionIds.includes(p.id))
        .map(p => p.id);
    }

    updateRolePermissions(selectedRole.id, newPermissionIds);
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchRoles(), fetchPermissions()]);
      setLoading(false);
    };
    loadData();
  }, []);

  // فلترة الصلاحيات
  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (permission.route && getPageNameFromTree(permission.route).toLowerCase().includes(searchTerm.toLowerCase()));

    // فلترة حسب الصفحة الرئيسية
    const matchesMainPage = selectedMainPage === 'all' ||
                           (permission.route && getMainPageFromRoute(permission.route) === selectedMainPage);

    return matchesSearch && matchesMainPage;
  });



  // دالة للحصول على خيارات الصفحات الرئيسية
  const getMainPageOptions = () => {
    const mainPages = new Set<string>();
    permissions.forEach(permission => {
      if (permission.route) {
        const mainPage = getMainPageFromRoute(permission.route);
        mainPages.add(mainPage);
      }
    });

    return Array.from(mainPages)
      .sort()
      .map(page => (
        <option key={page} value={page}>
          {getPageNameFromTree(page)}
        </option>
      ));
  };

  // تجميع الصلاحيات حسب الصفحة الرئيسية بدلاً من الفئة
  const groupedPermissions = filteredPermissions.reduce((groups, permission) => {
    const mainPage = permission.route ? getMainPageFromRoute(permission.route) : 'أخرى';
    const pageName = permission.route ? getPageNameFromTree(permission.route) : 'صلاحيات عامة';

    if (!groups[mainPage]) {
      groups[mainPage] = {
        name: pageName,
        permissions: []
      };
    }
    groups[mainPage].permissions.push(permission);
    return groups;
  }, {} as Record<string, { name: string; permissions: Permission[] }>);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل الأدوار والصلاحيات...</p>
        </div>
      </div>
    );
  }

  return (
    <OptimizedProtectedRoute requiredPermission="admin.roles.view">
      <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">إدارة الأدوار والصلاحيات</h1>
        <p className="text-gray-600">تحكم في أدوار المستخدمين وصلاحياتهم في النظام</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* قائمة الأدوار */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <FaUsers className="text-[var(--primary-color)]" />
                الأدوار
              </h2>
              <QuickActionButtons
                entityType="roles"
                actions={[
                  {
                    key: 'add',
                    label: 'إضافة دور',
                    icon: <FaPlus />,
                    onClick: () => {
                      setEditingRole(null);
                      setShowRoleModal(true);
                    },
                    variant: 'primary'
                  }
                ]}
                className="text-sm"
              />
            </div>

            <div className="space-y-2">
              {roles.map((role) => (
                <div
                  key={role.id}
                  onClick={() => {
                    // تهيئة الدور مع مصفوفة صلاحيات فارغة أولاً
                    setSelectedRole({
                      ...role,
                      permissions: []
                    });
                    fetchRolePermissions(role.id);
                  }}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedRole?.id === role.id
                      ? 'border-[var(--primary-color)] bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-800">{role.displayName}</h3>
                      <p className="text-sm text-gray-500">{role.description}</p>
                      {role._count && (
                        <p className="text-xs text-gray-400 mt-1">
                          {role._count.users} مستخدم
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {role.isSystem && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          نظام
                        </span>
                      )}
                      {!role.isSystem && (
                        <div onClick={(e) => e.stopPropagation()}>
                          <OptimizedActionButtonGroup
                            entityType="roles"
                            onEdit={() => {
                              setEditingRole(role);
                              setShowRoleModal(true);
                            }}
                            onDelete={() => handleDeleteRole(role.id)}
                            showEdit={true}
                            showDelete={true}
                            className="gap-1"
                            size="sm"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* إدارة الصلاحيات */}
        <div className="lg:col-span-2">
          {selectedRole ? (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="mb-6">
                <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2 mb-2">
                  <FaKey className="text-[var(--primary-color)]" />
                  صلاحيات الدور: {selectedRole.displayName}
                </h2>

                {/* أدوات البحث والفلترة */}
                <div className="space-y-4 mb-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                      <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        placeholder="البحث في الصلاحيات (الاسم، المفتاح، أو الصفحة)..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                      />
                    </div>
                    <select
                      value={selectedMainPage}
                      onChange={(e) => setSelectedMainPage(e.target.value)}
                      className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)] min-w-[200px]"
                    >
                      <option value="all">جميع الصفحات الرئيسية</option>
                      {getMainPageOptions()}
                    </select>
                  </div>

                  {/* أزرار إعادة تعيين الفلاتر */}
                  {(searchTerm || selectedMainPage !== 'all') && (
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-gray-600">الفلاتر النشطة:</span>
                      {searchTerm && (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                          البحث: {searchTerm}
                          <button
                            onClick={() => setSearchTerm('')}
                            className="ml-1 text-blue-600 hover:text-blue-800"
                          >
                            ×
                          </button>
                        </span>
                      )}
                      {selectedMainPage !== 'all' && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                          الصفحة: {getPageNameFromTree(selectedMainPage)}
                          <button
                            onClick={() => setSelectedMainPage('all')}
                            className="ml-1 text-green-600 hover:text-green-800"
                          >
                            ×
                          </button>
                        </span>
                      )}
                      <button
                        onClick={() => {
                          setSearchTerm('');
                          setSelectedMainPage('all');
                        }}
                        className="text-gray-500 hover:text-gray-700 text-xs underline"
                      >
                        إعادة تعيين الكل
                      </button>
                    </div>
                  )}
                </div>
              </div>

              {/* عداد الصلاحيات وأزرار التحديد */}
              {(() => {
                // حساب العدد الفعلي للصلاحيات المعروضة في جميع المجموعات
                const actualDisplayedCount = Object.values(groupedPermissions).reduce((total, pageData) => {
                  return total + pageData.permissions.length;
                }, 0);

                return (
                  <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-600">
                      عرض <span className="font-semibold text-[var(--primary-color)]">{actualDisplayedCount}</span> من أصل <span className="font-semibold">{permissions.length}</span> صلاحية
                    </div>
                    <div className="flex items-center gap-3">
                      {actualDisplayedCount !== permissions.length && (
                        <div className="text-xs text-gray-500">
                          تم تطبيق فلاتر
                        </div>
                      )}
                      {filteredPermissions.length > 0 && (
                        <PermissionGuard requiredPermission="admin.roles.permissions.edit">
                          <div className="flex items-center gap-2">
                            {(() => {
                              const rolePermissions = selectedRole?.permissions || [];
                              const selectedCount = filteredPermissions.filter(p =>
                                rolePermissions.some(rp => rp.id === p.id)
                              ).length;
                              const allSelected = selectedCount === filteredPermissions.length;
                              const someSelected = selectedCount > 0;

                              return (
                                <label className="flex items-center gap-2 cursor-pointer">
                                  <input
                                    type="checkbox"
                                    checked={allSelected}
                                    ref={(el) => {
                                      if (el) el.indeterminate = someSelected && !allSelected;
                                    }}
                                    onChange={(e) => toggleAllFilteredPermissions(e.target.checked)}
                                    className="w-4 h-4 text-[var(--primary-color)] border-gray-300 rounded focus:ring-[var(--primary-color)]"
                                  />
                                  <span className="text-sm text-gray-700 font-medium">
                                    {allSelected ? 'إلغاء تحديد الكل' : 'تحديد الكل'}
                                  </span>
                                </label>
                              );
                            })()}
                          </div>
                        </PermissionGuard>
                      )}
                    </div>
                  </div>
                );
              })()}

              {/* قائمة الصلاحيات مجمعة حسب الصفحة الرئيسية */}
              {filteredPermissions.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <FaSearch className="mx-auto text-4xl mb-4 text-gray-300" />
                  <p className="text-lg mb-2">لا توجد صلاحيات مطابقة للفلاتر المحددة</p>
                  <p className="text-sm">جرب تعديل معايير البحث أو الفلترة</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {Object.entries(groupedPermissions).map(([mainPage, pageData]) => {
                  const rolePermissions = selectedRole?.permissions || [];
                  const pagePermissions = pageData.permissions;
                  const hasAllPermissions = pagePermissions.every((p: Permission) =>
                    rolePermissions.some(rp => rp.id === p.id)
                  );
                  const hasSomePermissions = pagePermissions.some((p: Permission) =>
                    rolePermissions.some(rp => rp.id === p.id)
                  );

                  return (
                    <div key={mainPage} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium text-gray-800 flex items-center gap-2">
                          📄 {pageData.name}
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {pagePermissions.length} صلاحية
                          </span>
                        </h3>
                        <PermissionGuard requiredPermission="admin.roles.permissions.edit">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={hasAllPermissions}
                              ref={(el) => {
                                if (el) el.indeterminate = hasSomePermissions && !hasAllPermissions;
                              }}
                              onChange={(e) => togglePagePermissions(mainPage, e.target.checked)}
                              className="w-4 h-4 text-[var(--primary-color)] border-gray-300 rounded focus:ring-[var(--primary-color)]"
                            />
                            <span className="text-sm text-gray-600">تحديد الكل</span>
                          </label>
                        </PermissionGuard>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {pagePermissions.map((permission: Permission) => {
                          const hasPermission = rolePermissions.some(p => p.id === permission.id);

                          return (
                            <PermissionGuard key={permission.id} requiredPermission="admin.roles.permissions.edit">
                              <label
                                className="flex items-center gap-3 p-2 rounded hover:bg-gray-50 cursor-pointer"
                              >
                                <input
                                  type="checkbox"
                                  checked={hasPermission}
                                  onChange={() => togglePermission(permission.id)}
                                  className="w-4 h-4 text-[var(--primary-color)] border-gray-300 rounded focus:ring-[var(--primary-color)]"
                                />
                                <div className="flex-1">
                                  <div className="text-sm font-medium text-gray-800">
                                    {permission.name}
                                  </div>
                                  {permission.description && (
                                    <div className="text-xs text-gray-500">
                                      {permission.description}
                                    </div>
                                  )}
                                  <div className="text-xs text-gray-400">
                                    {permission.key}
                                  </div>
                                </div>
                              </label>
                            </PermissionGuard>
                          );
                        })}
                      </div>
                    </div>
                  );
                  })}
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="text-center text-gray-500">
                <FaKey className="mx-auto text-4xl mb-4 text-gray-300" />
                <p>اختر دورًا من القائمة لعرض وإدارة صلاحياته</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modal إضافة/تعديل دور */}
      {showRoleModal && (
        <RoleModal
          role={editingRole}
          onSave={handleSaveRole}
          onClose={() => {
            setShowRoleModal(false);
            setEditingRole(null);
          }}
        />
      )}
      </div>
    </OptimizedProtectedRoute>
  );
}

// مكون Modal لإضافة/تعديل الأدوار
interface RoleModalProps {
  role: Role | null;
  onSave: (roleData: { name: string; displayName: string; description?: string }) => void;
  onClose: () => void;
}

function RoleModal({ role, onSave, onClose }: RoleModalProps) {
  const [formData, setFormData] = useState({
    name: role?.name || '',
    displayName: role?.displayName || '',
    description: role?.description || ''
  });

  // إعادة تعيين النموذج عند تغيير الدور
  useEffect(() => {
    setFormData({
      name: role?.name || '',
      displayName: role?.displayName || '',
      description: role?.description || ''
    });
  }, [role]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.displayName.trim()) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    onSave({
      name: formData.name.trim(),
      displayName: formData.displayName.trim(),
      description: formData.description.trim() || undefined
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">
              {role ? 'تعديل الدور' : 'إضافة دور جديد'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <FaTimes />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم الدور (بالإنجليزية) *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                placeholder="مثال: CUSTOM_ROLE"
                required
                disabled={role?.isSystem}
              />
              <p className="text-xs text-gray-500 mt-1">
                يجب أن يكون باللغة الإنجليزية وبدون مسافات
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الاسم المعروض *
              </label>
              <input
                type="text"
                value={formData.displayName}
                onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                placeholder="مثال: موظف مخصص"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                الوصف
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                placeholder="وصف مختصر للدور..."
                rows={3}
              />
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="submit"
                className="flex-1 bg-[var(--primary-color)] text-white py-2 px-4 rounded-lg hover:bg-[var(--secondary-color)] transition-colors"
              >
                {role ? 'تحديث' : 'إضافة'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

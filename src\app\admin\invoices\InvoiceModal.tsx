'use client';

import { useState, useEffect } from 'react';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DatePicker } from 'antd';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';

interface Student {
  id: string;
  name: string;
}

interface Invoice {
  id: number;
  studentId: number;
  student: {
    id: number;
    name: string;
  };
  amount: number;
  dueDate: string;
  issueDate: string;
  month: number;
  year: number;
  description?: string;
  status: string;
  notes?: string;
}

interface InvoiceModalProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  invoice?: Invoice | null;
}

export default function InvoiceModal({ isOpen, onCloseAction, onSuccessAction, invoice }: InvoiceModalProps) {
  const [students, setStudents] = useState<Student[]>([]);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [formData, setFormData] = useState({
    studentId: '',
    amount: '',
    dueDate: dayjs().add(30, 'day'),
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    description: '',
    status: 'UNPAID',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!invoice;

  // جلب الطلاب عند فتح النافذة المنبثقة
  useEffect(() => {
    if (isOpen) {
      fetchStudents();

      // إذا كنا في وضع التعديل، قم بتعبئة النموذج بالبيانات الحالية
      if (isEditMode && invoice) {
        setFormData({
          studentId: invoice.studentId.toString(),
          amount: invoice.amount.toString(),
          dueDate: dayjs(invoice.dueDate),
          month: invoice.month,
          year: invoice.year,
          description: invoice.description || '',
          status: invoice.status,
          notes: invoice.notes || ''
        });
      } else {
        // إعادة تعيين النموذج في وضع الإنشاء
        setFormData({
          studentId: '',
          amount: '',
          dueDate: dayjs().add(30, 'day'),
          month: new Date().getMonth() + 1,
          year: new Date().getFullYear(),
          description: '',
          status: 'UNPAID',
          notes: ''
        });
      }
    }
  }, [isOpen, isEditMode, invoice]);

  // جلب قائمة الطلاب
  const fetchStudents = async () => {
    try {
      setLoadingStudents(true);
      const response = await fetch('/api/students');
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      // التحقق من تنسيق البيانات المستلمة
      if (Array.isArray(data)) {
        // إذا كانت البيانات مصفوفة مباشرة
        setStudents(data);
      } else if (data && Array.isArray(data.students)) {
        // إذا كانت البيانات كائن يحتوي على خاصية students
        setStudents(data.students);
      } else {
        console.error('تنسيق بيانات الطلاب غير صالح:', data);
        setStudents([]);
        toast.error('تنسيق بيانات الطلاب غير صالح');
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('فشل في جلب قائمة الطلاب');
    } finally {
      setLoadingStudents(false);
    }
  };

  // تصفية الطلاب بناءً على البحث
  const filteredStudents = students.filter(student =>
    student.name.includes(searchQuery) || student.id.toString().includes(searchQuery)
  );

  // تحديث حقول النموذج
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // تحديث حقل التاريخ
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, dueDate: date }));
    }
  };

  // إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.studentId || !formData.amount || !formData.dueDate) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      console.log('Validation failed. formData:', formData);
      return;
    }

    console.log('Form validation passed. Submitting with formData:', formData);

    try {
      setIsSubmitting(true);

      const payload = {
        studentId: parseInt(formData.studentId),
        amount: parseFloat(formData.amount),
        dueDate: formData.dueDate.toISOString(),
        month: formData.month,
        year: formData.year,
        description: formData.description,
        status: formData.status,
        notes: formData.notes
      };

      console.log('Submitting payload:', payload);

      let response;

      if (isEditMode && invoice) {
        // تحديث فاتورة موجودة
        response = await fetch('/api/invoices', {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            id: invoice.id,
            ...payload
          })
        });
      } else {
        // إنشاء فاتورة جديدة
        response = await fetch('/api/invoices', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });
      }

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ أثناء حفظ الفاتورة');
      }

      toast.success(isEditMode ? 'تم تحديث الفاتورة بنجاح' : 'تم إنشاء الفاتورة بنجاح');
      onSuccessAction();
      onCloseAction();
    } catch (error: Error | unknown) {
      console.error('Error saving invoice:', error);
      toast.error(error instanceof Error ? error.message : 'فشل في حفظ الفاتورة');
    } finally {
      setIsSubmitting(false);
    }
  };

  // إنشاء قائمة بالسنوات (السنة الحالية و5 سنوات سابقة و5 سنوات قادمة)
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  const dialogFooter = (
    <>
      <Button type="button" variant="outline" onClick={onCloseAction} disabled={isSubmitting}>
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isSubmitting}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('invoiceForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isSubmitting ? 'جاري الحفظ...' : isEditMode ? 'تحديث' : 'إنشاء'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title={isEditMode ? 'تعديل الفاتورة' : 'إنشاء فاتورة جديدة'}
      variant="primary"
      footer={dialogFooter}
    >
      <form id="invoiceForm" onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="studentId" className="text-right col-span-1">
              الطالب <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <div className="relative">
                <div className="mb-2">
                  <Input
                    placeholder="ابحث عن الطالب..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    disabled={isEditMode}
                  />
                </div>
                <Select
                  name="studentId"
                  value={formData.studentId}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, studentId: value }))}
                  disabled={isEditMode || loadingStudents}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الطالب" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredStudents.map((student) => (
                      <SelectItem key={student.id} value={student.id.toString()}>
                        {student.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right col-span-1">
              المبلغ <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Input
                id="amount"
                name="amount"
                type="number"
                placeholder="أدخل المبلغ"
                value={formData.amount}
                onChange={handleInputChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="dueDate" className="text-right col-span-1">
              تاريخ الاستحقاق <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <DatePicker
                value={formData.dueDate}
                onChange={handleDateChange}
                format="DD/MM/YYYY"
                style={{ width: '100%' }}
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="month" className="text-right col-span-1">
              الشهر <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                name="month"
                value={formData.month.toString()}
                onValueChange={(value) => setFormData(prev => ({ ...prev, month: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر الشهر" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                    <SelectItem key={month} value={month.toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="year" className="text-right col-span-1">
              السنة <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              <Select
                name="year"
                value={formData.year.toString()}
                onValueChange={(value) => setFormData(prev => ({ ...prev, year: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر السنة" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right col-span-1">
              الوصف
            </Label>
            <div className="col-span-3">
              <Input
                id="description"
                name="description"
                placeholder="وصف الفاتورة"
                value={formData.description}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {isEditMode && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right col-span-1">
                الحالة
              </Label>
              <div className="col-span-3">
                <Select
                  name="status"
                  value={formData.status}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PAID">مدفوعة</SelectItem>
                    <SelectItem value="PARTIALLY_PAID">مدفوعة جزئيًا</SelectItem>
                    <SelectItem value="UNPAID">غير مدفوعة</SelectItem>
                    <SelectItem value="OVERDUE">متأخرة</SelectItem>
                    <SelectItem value="CANCELLED">ملغاة</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="notes" className="text-right col-span-1 mt-2">
              ملاحظات
            </Label>
            <div className="col-span-3">
              <Textarea
                id="notes"
                name="notes"
                placeholder="ملاحظات إضافية"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
              />
            </div>
          </div>
        </div>
      </form>
    </AnimatedDialog>
  );
}

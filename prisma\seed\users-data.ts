import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcrypt';
const prisma = new PrismaClient();

async function seedUsers() {
  console.log('بدء إضافة بيانات المستخدمين...');

  try {
    // إنشاء المعلمين
    const teachers = [
      {
        username: 'teacher1',
        name: 'محمد أحمد',
        phone: '0123456789',
        specialization: 'القرآن الكريم'
      },
      {
        username: 'teacher2',
        name: 'أحمد محمد',
        phone: '0123456790',
        specialization: 'التجويد'
      },
      {
        username: 'teacher3',
        name: 'عب<PERSON> الله محمد',
        phone: '0123456791',
        specialization: 'القراءات'
      },
      {
        username: 'teacher4',
        name: 'محمود أحمد',
        phone: '0123456792',
        specialization: 'علوم القرآن'
      }
    ];

    // إنشاء أولياء الأمور
    const parents = [
      {
        name: 'أحمد محمود',
        phone: '0123456788'
      },
      {
        name: 'محم<PERSON> علي',
        phone: '0123456793'
      },
      {
        name: 'علي حسن',
        phone: '0123456794'
      },
      {
        name: 'حسن محمد',
        phone: '0123456795'
      },
      {
        name: 'عمر خالد',
        phone: '0123456796'
      }
    ];

    // إنشاء المعلمين وحساباتهم
    for (const teacherData of teachers) {
      const existingTeacher = await prisma.user.findUnique({
        where: { username: teacherData.username },
        include: { teacher: true }
      });

      if (!existingTeacher) {
        const hashedPassword = await bcrypt.hash('teacher123', 10);
        await prisma.user.create({
          data: {
            username: teacherData.username,
            password: hashedPassword,
            role: UserRole.TEACHER,
            profile: {
              create: {
                name: teacherData.name,
                phone: teacherData.phone
              }
            },
            teacher: {
              create: {
                name: teacherData.name,
                phone: teacherData.phone,
                specialization: teacherData.specialization
              }
            }
          }
        });
        console.log(`✅ تم إنشاء المعلم: ${teacherData.name}`);
      } else {
        console.log(`ℹ️ المعلم موجود بالفعل: ${teacherData.name}`);
      }
    }

    // إنشاء أولياء الأمور
    const createdParents = [];
    for (const parentData of parents) {
      const existingParent = await prisma.parent.findFirst({
        where: { phone: parentData.phone }
      });

      if (!existingParent) {
        const parent = await prisma.parent.create({
          data: parentData
        });
        createdParents.push(parent);
        console.log(`✅ تم إنشاء ولي الأمر: ${parentData.name}`);
      } else {
        createdParents.push(existingParent);
        console.log(`ℹ️ ولي الأمر موجود بالفعل: ${parentData.name}`);
      }
    }

    // البحث عن الصف الأول
    const class1 = await prisma.classe.findFirst({
      where: { name: 'الصف الأول - تحفيظ' }
    });

    if (!class1) {
      console.log('⚠️ لم يتم العثور على الصف الأول');
      return;
    }

    // إنشاء الطلاب
    const students = [
      {
        username: 'student1',
        name: 'عبد الرحمن أحمد',
        age: 10,
        phone: '0123456787',
        parentIndex: 0
      },
      {
        username: 'student2',
        name: 'محمد علي',
        age: 11,
        phone: '0123456797',
        parentIndex: 0
      },
      {
        username: 'student3',
        name: 'أحمد محمد',
        age: 9,
        phone: '0123456798',
        parentIndex: 1
      },
      {
        username: 'student4',
        name: 'علي حسن',
        age: 12,
        phone: '0123456799',
        parentIndex: 1
      },
      {
        username: 'student5',
        name: 'حسن علي',
        age: 10,
        phone: '0123456800',
        parentIndex: 2
      },
      {
        username: 'student6',
        name: 'عمر محمد',
        age: 11,
        phone: '0123456801',
        parentIndex: 2
      },
      {
        username: 'student7',
        name: 'خالد أحمد',
        age: 9,
        phone: '0123456802',
        parentIndex: 3
      },
      {
        username: 'student8',
        name: 'محمود علي',
        age: 12,
        phone: '0123456803',
        parentIndex: 3
      },
      {
        username: 'student9',
        name: 'يوسف محمد',
        age: 10,
        phone: '0123456804',
        parentIndex: 4
      },
      {
        username: 'student10',
        name: 'زياد أحمد',
        age: 11,
        phone: '0123456805',
        parentIndex: 4
      }
    ];

    // إنشاء الطلاب وربطهم بأولياء الأمور والصف
    for (const studentData of students) {
      const existingStudent = await prisma.student.findFirst({
        where: { username: studentData.username }
      });

      if (!existingStudent) {
        const parent = createdParents[studentData.parentIndex];
        if (!parent) {
          console.log(`⚠️ لم يتم العثور على ولي الأمر للطالب: ${studentData.name}`);
          continue;
        }

        const student = await prisma.student.create({
          data: {
            username: studentData.username,
            name: studentData.name,
            age: studentData.age,
            phone: studentData.phone,
            guardianId: parent.id,
            classeId: class1.id
          }
        });

        // إنشاء صورة افتراضية للطالب
        await prisma.studentImage.create({
          data: {
            studentId: student.id,
            imageUrl: '/uploads/student/default_student_image.txt',
            description: 'الصورة الشخصية',
            isProfilePic: true,
          },
        });

        console.log(`✅ تم إنشاء الطالب: ${studentData.name}`);
      } else {
        console.log(`ℹ️ الطالب موجود بالفعل: ${studentData.name}`);
      }
    }

    console.log('✅ تم إضافة بيانات المستخدمين بنجاح');
  } catch (error) {
    console.error('❌ حدث خطأ أثناء إضافة بيانات المستخدمين:', error);
    throw error;
  }
}

export default seedUsers;
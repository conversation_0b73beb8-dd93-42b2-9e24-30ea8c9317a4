'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Database, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'react-hot-toast';

export default function DebugPage() {
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [data, setData] = useState<any>(null);

  const checkData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/debug/check-data');
      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
        toast.success('تم فحص البيانات بنجاح');
      } else {
        toast.error('فشل في فحص البيانات');
      }
    } catch (error) {
      console.error('Error checking data:', error);
      toast.error('حدث خطأ أثناء فحص البيانات');
    } finally {
      setLoading(false);
    }
  };

  const createSampleData = async () => {
    try {
      setCreating(true);
      const response = await fetch('/api/debug/create-sample-data', {
        method: 'POST'
      });
      const result = await response.json();
      
      if (result.success) {
        toast.success('تم إنشاء البيانات التجريبية بنجاح');
        // إعادة فحص البيانات
        await checkData();
      } else {
        toast.error('فشل في إنشاء البيانات التجريبية');
      }
    } catch (error) {
      console.error('Error creating sample data:', error);
      toast.error('حدث خطأ أثناء إنشاء البيانات التجريبية');
    } finally {
      setCreating(false);
    }
  };

  const getStatusIcon = (count: number) => {
    if (count === 0) {
      return <AlertCircle className="h-5 w-5 text-red-500" />;
    } else if (count < 5) {
      return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    } else {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    }
  };

  const getStatusColor = (count: number) => {
    if (count === 0) return 'text-red-600';
    if (count < 5) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-[var(--primary-color)]">تشخيص النظام</h1>
        <div className="flex gap-2">
          <Button onClick={checkData} disabled={loading}>
            {loading ? (
              <Loader2 className="ml-2 h-4 w-4 animate-spin" />
            ) : (
              <Database className="ml-2 h-4 w-4" />
            )}
            فحص البيانات
          </Button>
          <Button onClick={createSampleData} disabled={creating} variant="outline">
            {creating ? (
              <Loader2 className="ml-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="ml-2 h-4 w-4" />
            )}
            إنشاء بيانات تجريبية
          </Button>
        </div>
      </div>

      {data && (
        <>
          {/* ملخص الأعداد */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Object.entries(data.counts).map(([key, count]) => (
              <Card key={key}>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    {getStatusIcon(count as number)}
                  </div>
                  <p className={`text-2xl font-bold ${getStatusColor(count as number)}`}>
                    {count as number}
                  </p>
                  <p className="text-sm text-gray-500">
                    {key === 'students' && 'طلاب'}
                    {key === 'exams' && 'امتحانات'}
                    {key === 'examPoints' && 'نقاط امتحانات'}
                    {key === 'classes' && 'فصول'}
                    {key === 'subjects' && 'مواد'}
                    {key === 'classSubjects' && 'علاقات فصل-مادة'}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* تفاصيل البيانات */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* الطلاب */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="ml-2 h-5 w-5" />
                  الطلاب ({data.counts.students})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.samples.students.length > 0 ? (
                  <div className="space-y-2">
                    {data.samples.students.map((student: any) => (
                      <div key={student.id} className="p-2 bg-gray-50 rounded">
                        <p className="font-semibold">{student.name}</p>
                        <p className="text-sm text-gray-600">
                          {student.username} - {student.classe}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">لا توجد طلاب</p>
                )}
              </CardContent>
            </Card>

            {/* الامتحانات */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="ml-2 h-5 w-5" />
                  الامتحانات ({data.counts.exams})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.samples.exams.length > 0 ? (
                  <div className="space-y-2">
                    {data.samples.exams.map((exam: any) => (
                      <div key={exam.id} className="p-2 bg-gray-50 rounded">
                        <p className="font-semibold">{exam.description}</p>
                        <p className="text-sm text-gray-600">
                          {exam.month} - {exam.maxPoints} نقطة
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">لا توجد امتحانات</p>
                )}
              </CardContent>
            </Card>

            {/* نقاط الامتحانات */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="ml-2 h-5 w-5" />
                  نقاط الامتحانات ({data.counts.examPoints})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.samples.examPoints.length > 0 ? (
                  <div className="space-y-2">
                    {data.samples.examPoints.map((point: any) => (
                      <div key={point.id} className="p-2 bg-gray-50 rounded">
                        <p className="font-semibold">{point.studentName}</p>
                        <p className="text-sm text-gray-600">
                          {point.grade}/{point.maxPoints} - {point.examDescription}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">لا توجد نقاط امتحانات</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* تحليل المشكلة */}
          <Card>
            <CardHeader>
              <CardTitle>تحليل المشكلة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {data.counts.examPoints === 0 && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded">
                    <h4 className="font-bold text-red-800">⚠️ لا توجد نقاط امتحانات</h4>
                    <p className="text-red-700">
                      هذا هو سبب ظهور رسالة "لا توجد بيانات امتحانات" في كشف الدرجات.
                      يجب إنشاء نقاط امتحانات للطلاب.
                    </p>
                  </div>
                )}
                
                {data.counts.classSubjects === 0 && (
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
                    <h4 className="font-bold text-yellow-800">⚠️ لا توجد علاقات فصل-مادة</h4>
                    <p className="text-yellow-700">
                      علاقات الفصل بالمادة مطلوبة لربط الطلاب بالامتحانات.
                    </p>
                  </div>
                )}

                {data.counts.examPoints > 0 && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded">
                    <h4 className="font-bold text-green-800">✅ البيانات متوفرة</h4>
                    <p className="text-green-700">
                      يوجد {data.counts.examPoints} نقطة امتحان. يجب أن تظهر في كشف الدرجات.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {!data && (
        <Card>
          <CardContent className="text-center p-8">
            <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">اضغط على "فحص البيانات" لبدء التشخيص</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

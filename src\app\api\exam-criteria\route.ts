import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/exam-criteria?examId=123
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const examId = searchParams.get('examId');

    if (!examId) {
      return NextResponse.json({
        error: 'معرف الامتحان مطلوب',
        success: false
      }, { status: 400 });
    }

    // جلب معايير التقييم المرتبطة بالامتحان
    const examCriteria = await prisma.examCriteria.findMany({
      where: {
        examId: parseInt(examId)
      },
      include: {
        criteria: true
      }
    });

    // استخراج معرفات معايير التقييم
    const criteriaIds = examCriteria.map(ec => ec.criteriaId);

    return NextResponse.json({
      data: {
        examCriteria,
        criteriaIds
      },
      success: true,
      message: 'تم جلب معايير التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error fetching exam criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب معايير التقييم',
      success: false
    }, { status: 500 });
  }
}

# مشروع صفحة المشرف للتقارير الأدبية والمالية

## نظرة عامة على المشروع
بناء صفحة إدارية متخصصة للمشرف تمكنه من الوصول إلى التقارير الأدبية والمالية المخصصة وإنتاج المعلومات اللازمة وتعبئتها تلقائياً من تاريخ معين إلى تاريخ آخر معين.

## تحليل المتطلبات الأولية

### التقنيات المستخدمة في المشروع الحالي:
- **Framework**: Next.js 15.2.4 مع TypeScript
- **Database**: MySQL مع Prisma ORM
- **UI**: React مع Tailwind CSS
- **Authentication**: نظام مستخدمين مع أدوار (ADMIN, EMPLOYEE, etc.)
- **File Processing**: مكتبة xlsx لمعالجة ملفات Excel

### الكيانات الرئيسية المتعلقة بالمشروع:
- **Students**: الطلاب وبياناتهم
- **Teachers**: المعلمين والمدرسين
- **Classes**: الصفوف والمستويات
- **Exams & Evaluations**: الامتحانات والتقييمات
- **Attendance**: الحضور والغياب
- **Expenses**: المصروفات والنفقات
- **KhatmSessions**: مجالس الختم
- **QuranProgress**: تقدم حفظ القرآن

## خطة العمل التفصيلية

### المرحلة الأولى: التحليل والتصميم
- [x] **T01.01: تحليل هيكل التقرير الأدبي**
  - **الحالة:** مُنجزة
  - **المكونات:** التقرير الأدبي المرجعي، قاعدة البيانات
  - **الاعتماديات:** لا يوجد
  - **المستندات المرجعية:** literary_report_analysis.md في project_documents
  - **ملاحظات المستخدم:** استخراج البيانات من التقرير الأدبي المقدم كمرجع

- [x] **T01.02: تحليل هيكل التقرير المالي**
  - **الحالة:** مُنجزة
  - **المكونات:** نموذج التقرير المالي، جداول المصروفات
  - **الاعتماديات:** لا يوجد
  - **المستندات المرجعية:** financial_report_analysis.md في project_documents
  - **ملاحظات المستخدم:** تحليل نموذج التقرير المالي Excel

- [x] **T01.03: تصميم واجهة صفحة المشرف**
  - **الحالة:** مُنجزة
  - **المكونات:** UI/UX Design، مكونات React
  - **الاعتماديات:** T01.01, T01.02
  - **المستندات المرجعية:** supervisor_page_design.md في project_documents
  - **ملاحظات المستخدم:** تصميم واجهة سهلة الاستخدام مع إمكانية اختيار التواريخ

### المرحلة الثانية: تطوير Backend
- [x] **T02.01: إنشاء API للتقرير الأدبي**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/api/reports/literary/route.ts
  - **الاعتماديات:** T01.01
  - **المستندات المرجعية:** api_specifications.md في project_documents
  - **ملاحظات المستخدم:** API يقبل تاريخ البداية والنهاية ويعيد البيانات المطلوبة

- [x] **T02.02: إنشاء API للتقرير المالي**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/api/reports/financial/route.ts
  - **الاعتماديات:** T01.02
  - **المستندات المرجعية:** api_specifications.md في project_documents
  - **ملاحظات المستخدم:** API يجمع البيانات المالية من الفترة المحددة

- [x] **T02.03: تطوير خدمات معالجة البيانات**
  - **الحالة:** مُنجزة
  - **المكونات:** src/lib/reports/، src/lib/data-processing/
  - **الاعتماديات:** T02.01, T02.02
  - **المستندات المرجعية:** data_processing_specs.md في project_documents
  - **ملاحظات المستخدم:** خدمات لمعالجة وتجميع البيانات من مصادر مختلفة

### المرحلة الثالثة: تطوير Frontend
- [x] **T03.01: إنشاء صفحة المشرف الرئيسية**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/admin/supervisor-reports/page.tsx
  - **الاعتماديات:** T01.03
  - **المستندات المرجعية:** supervisor_page_design.md في project_documents
  - **ملاحظات المستخدم:** صفحة رئيسية مع خيارات التقارير وانتقاء التواريخ

- [x] **T03.02: تطوير مكونات التقرير الأدبي**
  - **الحالة:** مُنجزة
  - **المكونات:** src/components/reports/LiteraryReport.tsx
  - **الاعتماديات:** T02.01, T03.01
  - **المستندات المرجعية:** component_specifications.md في project_documents
  - **ملاحظات المستخدم:** مكونات لعرض وتصدير التقرير الأدبي

- [x] **T03.03: تطوير مكونات التقرير المالي**
  - **الحالة:** مُنجزة
  - **المكونات:** src/components/reports/FinancialReport.tsx
  - **الاعتماديات:** T02.02, T03.01
  - **المستندات المرجعية:** component_specifications.md في project_documents
  - **ملاحظات المستخدم:** مكونات لعرض وتصدير التقرير المالي

### المرحلة الرابعة: ميزات التصدير والطباعة
- [x] **T04.01: تطوير ميزة تصدير التقرير الأدبي**
  - **الحالة:** مُنجزة
  - **المكونات:** src/lib/export/literary-report.ts
  - **الاعتماديات:** T03.02
  - **المستندات المرجعية:** export_specifications.md في project_documents
  - **ملاحظات المستخدم:** تصدير بصيغ Word/PDF مطابقة للنموذج المرجعي

- [x] **T04.02: تطوير ميزة تصدير التقرير المالي**
  - **الحالة:** مُنجزة
  - **المكونات:** src/lib/export/financial-report.ts
  - **الاعتماديات:** T03.03
  - **المستندات المرجعية:** export_specifications.md في project_documents
  - **ملاحظات المستخدم:** تصدير بصيغة Excel مطابقة للنموذج المرجعي

- [x] **T04.03: إضافة ميزات الطباعة**
  - **الحالة:** مُنجزة
  - **المكونات:** src/components/reports/PrintableReport.tsx
  - **الاعتماديات:** T04.01, T04.02
  - **المستندات المرجعية:** print_specifications.md في project_documents
  - **ملاحظات المستخدم:** إمكانية طباعة التقارير مباشرة من المتصفح

### المرحلة الخامسة: التكامل والاختبار
- [x] **T05.01: دمج المكونات في لوحة الإدارة**
  - **الحالة:** مُنجزة
  - **المكونات:** src/app/admin/layout.tsx، navigation components
  - **الاعتماديات:** T03.01, T03.02, T03.03
  - **المستندات المرجعية:** integration_guide.md في project_documents
  - **ملاحظات المستخدم:** إضافة روابط التقارير في قائمة الإدارة

- [x] **T05.02: اختبار وظائف التقارير**
  - **الحالة:** مُنجزة
  - **المكونات:** جميع مكونات التقارير
  - **الاعتماديات:** T05.01
  - **المستندات المرجعية:** testing_checklist.md في project_documents
  - **ملاحظات المستخدم:** اختبار شامل لجميع الوظائف مع بيانات تجريبية

- [x] **T05.03: تحسين الأداء والأمان**
  - **الحالة:** مُنجزة
  - **المكونات:** جميع APIs ومكونات Frontend
  - **الاعتماديات:** T05.02
  - **المستندات المرجعية:** performance_security_guide.md في project_documents
  - **ملاحظات المستخدم:** تحسين الاستعلامات وإضافة طبقات الحماية

## الملاحظات الفنية
- استخدام نظام الصلاحيات الموجود للتحكم في الوصول
- الاستفادة من مكونات UI الموجودة للحفاظ على التناسق
- ضمان التوافق مع النظام الحالي للألوان والثيمات
- دعم اللغة العربية والاتجاه من اليمين لليسار

## المخرجات المتوقعة
1. صفحة مشرف متكاملة للتقارير
2. تقارير أدبية تلقائية مبنية على البيانات الفعلية
3. تقارير مالية شاملة قابلة للتخصيص
4. إمكانيات تصدير وطباعة متقدمة
5. واجهة سهلة الاستخدام مع إمكانية اختيار الفترات الزمنية

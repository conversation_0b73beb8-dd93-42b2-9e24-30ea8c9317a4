# مخطط الأصناف - نظام إدارة التلاميذ المتقدم

## نظرة عامة
هذا المخطط يوضح العلاقات بين الكيانات الرئيسية في نظام إدارة التلاميذ المحسن.

```mermaid
classDiagram
    class Student {
        +Int id
        +String username
        +String name
        +Int age
        +String phone
        +Int guardianId
        +Int classeId
        +DateTime createdAt
        +DateTime updatedAt
        +Float totalPoints
        +DateTime memorizationStartDate
        +Int startingJuz
        +String memorizationLevel
        +getMemorizationStart()
        +generateStudentCard()
        +generateRegistrationReceipt()
    }

    class StudentMemorizationStart {
        +Int id
        +Int studentId
        +DateTime startDate
        +Int startingJuz
        +Int startingSurah
        +Int startingVerse
        +String level
        +String notes
        +DateTime createdAt
        +DateTime updatedAt
        +Boolean isActive
        +updateProgress()
        +getProgressSummary()
    }

    class StudentRegistrationReceipt {
        +Int id
        +Int studentId
        +String receiptNumber
        +DateTime issueDate
        +Float registrationFee
        +String paymentStatus
        +String receiptData
        +DateTime createdAt
        +Boolean isPrinted
        +generatePDF()
        +markAsPrinted()
    }

    class QuranProgress {
        +Int id
        +Int studentId
        +Int examId
        +Int surahId
        +Int startVerse
        +Int endVerse
        +Int memorization
        +Int tajweed
        +DateTime startDate
        +DateTime completionDate
        +updateGrades()
        +calculateProgress()
    }

    class Surah {
        +Int id
        +String name
        +Int number
        +Int totalAyahs
        +getVerseRange()
        +getStudentProgress()
    }

    class Juz {
        +Int id
        +Int number
        +String name
        +Int startSurah
        +Int endSurah
        +getSurahs()
        +getStudentsStarting()
    }

    class Parent {
        +Int id
        +String name
        +String phone
        +String email
        +String address
        +DateTime createdAt
        +DateTime updatedAt
        +getChildren()
        +getPaymentHistory()
    }

    class Classe {
        +Int id
        +String name
        +Int capacity
        +String description
        +DateTime createdAt
        +DateTime updatedAt
        +getStudents()
        +getSchedule()
    }

    class Payment {
        +Int id
        +Int studentId
        +Float amount
        +DateTime date
        +String status
        +String receiptNumber
        +processPayment()
        +generateReceipt()
    }

    class StudentImage {
        +Int id
        +Int studentId
        +String imageUrl
        +String description
        +Boolean isProfilePic
        +DateTime createdAt
        +uploadImage()
        +deleteImage()
    }

    %% العلاقات الأساسية
    Student ||--o{ StudentMemorizationStart : "has memorization starts"
    Student ||--o{ StudentRegistrationReceipt : "has receipts"
    Student ||--o{ QuranProgress : "has progress records"
    Student ||--o{ Payment : "makes payments"
    Student ||--o{ StudentImage : "has images"
    Student }o--|| Parent : "belongs to guardian"
    Student }o--|| Classe : "enrolled in class"

    %% علاقات القرآن الكريم
    QuranProgress }o--|| Surah : "memorizes surah"
    StudentMemorizationStart }o--|| Surah : "starts with surah"
    StudentMemorizationStart }o--|| Juz : "starts with juz"
    Surah }o--|| Juz : "belongs to juz"

    %% علاقات إضافية
    Parent ||--o{ Student : "guardian of"
    Classe ||--o{ Student : "contains students"
```

## وصف الكيانات الجديدة

### StudentMemorizationStart
- **الغرض:** تتبع نقطة بداية حفظ القرآن لكل تلميذ
- **الحقول الرئيسية:**
  - `startDate`: تاريخ بداية الحفظ
  - `startingJuz`: الجزء الذي بدأ فيه الحفظ
  - `startingSurah`: السورة التي بدأ فيها
  - `startingVerse`: الآية التي بدأ فيها
  - `level`: المستوى (مبتدئ، متوسط، متقدم)
  - `isActive`: هل هذه نقطة البداية النشطة

### StudentRegistrationReceipt
- **الغرض:** إدارة وصولات التسجيل للتلاميذ
- **الحقول الرئيسية:**
  - `receiptNumber`: رقم الوصل الفريد
  - `issueDate`: تاريخ الإصدار
  - `registrationFee`: رسوم التسجيل
  - `paymentStatus`: حالة الدفع
  - `receiptData`: بيانات الوصل بصيغة JSON
  - `isPrinted`: هل تم طباعة الوصل

## العمليات الرئيسية

### Student Class
- `getMemorizationStart()`: جلب معلومات بداية الحفظ
- `generateStudentCard()`: توليد بطاقة التلميذ
- `generateRegistrationReceipt()`: توليد وصل التسجيل

### StudentMemorizationStart Class
- `updateProgress()`: تحديث تقدم الحفظ
- `getProgressSummary()`: ملخص التقدم منذ البداية

### StudentRegistrationReceipt Class
- `generatePDF()`: توليد ملف PDF للوصل
- `markAsPrinted()`: تسجيل أن الوصل تم طباعته

## ملاحظات التصميم

1. **المرونة:** النظام يدعم تعدد نقاط البداية للتلميذ الواحد
2. **التتبع:** كل عملية لها طوابع زمنية للمراجعة
3. **الأمان:** ربط كل عملية بالتلميذ المحدد
4. **القابلية للتوسع:** إمكانية إضافة حقول جديدة مستقبلاً

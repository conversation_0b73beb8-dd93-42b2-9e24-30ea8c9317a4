import { NextRequest, NextResponse } from 'next/server';
import { LiteraryReportExporter } from '@/lib/export/literary-report';
import { LiteraryReportService } from '@/lib/reports/literary-report-service';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { startDate, endDate, format, options = {} } = body;

    // التحقق من صحة المعاملات
    if (!startDate || !endDate) {
      return NextResponse.json(
        { success: false, error: 'تواريخ البداية والنهاية مطلوبة' },
        { status: 400 }
      );
    }

    if (!['pdf', 'word', 'html'].includes(format)) {
      return NextResponse.json(
        { success: false, error: 'صيغة التصدير غير مدعومة' },
        { status: 400 }
      );
    }

    // جلب بيانات التقرير
    const reportData = await LiteraryReportService.generateReport(
      new Date(startDate),
      new Date(endDate)
    );

    if (!reportData) {
      return NextResponse.json(
        { success: false, error: 'فشل في جلب بيانات التقرير' },
        { status: 500 }
      );
    }

    let blob: Blob;
    let filename: string;
    let contentType: string;

    // تصدير حسب الصيغة المطلوبة
    switch (format) {
      case 'pdf':
        blob = await LiteraryReportExporter.exportToPDF(reportData, options);
        filename = `التقرير_الأدبي_${new Date().toISOString().split('T')[0]}.html`;
        contentType = 'text/html; charset=utf-8';
        break;

      case 'word':
        blob = await LiteraryReportExporter.exportToWord(reportData, options);
        filename = `التقرير_الأدبي_${new Date().toISOString().split('T')[0]}.html`;
        contentType = 'text/html; charset=utf-8';
        break;

      case 'html':
        blob = await LiteraryReportExporter.exportToHTML(reportData, options);
        filename = `التقرير_الأدبي_${new Date().toISOString().split('T')[0]}.html`;
        contentType = 'text/html';
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'صيغة التصدير غير مدعومة' },
          { status: 400 }
        );
    }

    // تحويل Blob إلى Buffer
    const arrayBuffer = await blob.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // إرجاع الملف
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Content-Length': buffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('خطأ في تصدير التقرير الأدبي:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'حدث خطأ أثناء تصدير التقرير',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

// معالج GET للحصول على معلومات التصدير المتاحة
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      data: {
        supportedFormats: [
          {
            format: 'pdf',
            name: 'PDF',
            description: 'ملف PDF للعرض والطباعة',
            extension: '.pdf',
            mimeType: 'application/pdf'
          },
          {
            format: 'word',
            name: 'Word',
            description: 'مستند Word للتعديل',
            extension: '.docx',
            mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          },
          {
            format: 'html',
            name: 'HTML',
            description: 'صفحة ويب للعرض',
            extension: '.html',
            mimeType: 'text/html'
          }
        ],
        availableOptions: {
          includeCharts: {
            type: 'boolean',
            default: true,
            description: 'تضمين الرسوم البيانية'
          },
          includeDetails: {
            type: 'boolean',
            default: true,
            description: 'تضمين التفاصيل الكاملة'
          },
          customTitle: {
            type: 'string',
            default: null,
            description: 'عنوان مخصص للتقرير'
          },
          watermark: {
            type: 'string',
            default: null,
            description: 'نص العلامة المائية'
          }
        }
      }
    });
  } catch (error) {
    console.error('خطأ في جلب معلومات التصدير:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب معلومات التصدير' },
      { status: 500 }
    );
  }
}

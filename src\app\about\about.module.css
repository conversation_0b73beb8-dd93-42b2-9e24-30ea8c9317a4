.aboutContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(to right, #283048, #859398);
    color: white;
    text-align: center;
    padding: 20px;
  }
  
  .aboutContent {
    max-width: 600px;
  }
  
  .title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-transform: uppercase;
  }
  
  .subtitle {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }
  
  .features {
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 10px;
  }
  
  .featureItem {
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
    transition: 0.3s;
    text-align: left;
  }
  
  .featureItem:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  
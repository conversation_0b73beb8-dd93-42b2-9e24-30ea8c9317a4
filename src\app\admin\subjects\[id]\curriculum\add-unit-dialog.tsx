'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';

interface AddUnitDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  subjectId: number;
}

export default function AddUnitDialog({ isOpen, onCloseAction, onSuccessAction, subjectId }: AddUnitDialogProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    order: ''
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddUnit = async () => {
    if (!formData.title.trim()) {
      toast.error('الرجاء إدخال عنوان الوحدة');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/curriculum/units', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.title,
          description: formData.description || null,
          order: formData.order ? parseInt(formData.order) : undefined,
          subjectId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add unit');
      }

      toast.success('تمت إضافة الوحدة بنجاح');
      setFormData({
        title: '',
        description: '',
        order: ''
      });
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error adding unit:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة الوحدة');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <Button
      onClick={handleAddUnit}
      disabled={isLoading || !formData.title.trim()}
      className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full"
    >
      {isLoading ? 'جاري الإضافة...' : 'إضافة'}
    </Button>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إضافة وحدة جديدة"
      variant="primary"
      footer={dialogFooter}
    >
      <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
        <div className="space-y-2">
          <Label>عنوان الوحدة</Label>
          <Input
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="أدخل عنوان الوحدة"
          />
        </div>
        
        <div className="space-y-2">
          <Label>الوصف (اختياري)</Label>
          <Textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="أدخل وصف الوحدة"
            rows={3}
          />
        </div>
        
        <div className="space-y-2">
          <Label>الترتيب (اختياري)</Label>
          <Input
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="أدخل ترتيب الوحدة"
            min={1}
          />
          <p className="text-xs text-gray-500">إذا تركت هذا الحقل فارغًا، سيتم وضع الوحدة في نهاية القائمة</p>
        </div>
      </div>
    </AnimatedDialog>
  );
}

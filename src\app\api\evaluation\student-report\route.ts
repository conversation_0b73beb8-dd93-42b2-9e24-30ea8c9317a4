import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';
import {
  calculatePercentage,
  getGradeLevelByScore,
  getPassStatus,
  calculateAverage,
  generateGradeStatistics
} from '@/lib/grading-system';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || !['ADMIN', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const classeIdParam = searchParams.get('classeId');
    const monthParam = searchParams.get('month');
    const evaluationTypeParam = searchParams.get('evaluationType');

    // تنظيف المعاملات وتحويلها للقيم الصحيحة
    const classeId = classeIdParam && classeIdParam !== '' && classeIdParam !== 'جميع الفصول' ? parseInt(classeIdParam) : null;
    const month = monthParam && monthParam !== '' ? monthParam : null;
    const evaluationType = evaluationTypeParam && evaluationTypeParam !== '' && evaluationTypeParam !== 'جميع الأنواع' ? evaluationTypeParam : null;

    if (!studentId) {
      return NextResponse.json(
        { message: "معرف الطالب مطلوب", success: false },
        { status: 400 }
      );
    }

    console.log('Generating student report for:', {
      studentId,
      classeId: classeId || 'all',
      month: month || 'all',
      evaluationType: evaluationType || 'all'
    });

    // جلب معلومات الطالب مع بيانات الحضور
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) },
      include: {
        classe: true,
        guardian: true,
        attendance: {
          select: {
            id: true,
            date: true,
            status: true,
            hisass: true
          },
          orderBy: {
            date: 'desc'
          },
          take: 50 // آخر 50 يوم من سجلات الحضور
        }
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "الطالب غير موجود", success: false },
        { status: 404 }
      );
    }

    // بناء شروط البحث للامتحانات
    const examWhereConditions: any = {};

    if (month) {
      examWhereConditions.month = month;
    }

    if (evaluationType) {
      examWhereConditions.evaluationType = evaluationType;
    }

    // بناء شروط البحث الرئيسية
    const whereConditions: any = {
      studentId: parseInt(studentId)
    };

    // إضافة شرط الفصل إذا كان محدد
    if (classeId) {
      whereConditions.student = {
        classeId: classeId
      };
    }

    // إضافة شروط الامتحان إذا كانت محددة
    if (Object.keys(examWhereConditions).length > 0) {
      whereConditions.exam = examWhereConditions;
    }

    console.log('Where conditions:', JSON.stringify(whereConditions, null, 2));

    // جلب نقاط الامتحانات للطالب
    const examPoints = await prisma.exam_points.findMany({
      where: whereConditions,
      include: {
        exam: {
          include: {
            subject: true,
            examType: true
          }
        },
        surah: true,
        criteriaScores: {
          include: {
            criteria: true
          }
        },
        classSubject: {
          include: {
            classe: true,
            teacherSubject: {
              include: {
                subject: true,
                teacher: true
              }
            }
          }
        }
      },
      orderBy: [
        { exam: { month: 'desc' } },
        { exam: { createdAt: 'desc' } }
      ]
    });

    console.log('Exam points found:', examPoints.length);

    // حساب إحصائيات الحضور والغياب
    const attendanceStats = calculateAttendanceStats(student.attendance);

    // تجميع النتائج حسب الشهر ونوع التقييم
    const resultsByMonth: Record<string, any[]> = {};
    const resultsByType: Record<string, any[]> = {};

    examPoints.forEach(point => {
      const month = point.exam.month;
      const type = point.exam.evaluationType;

      if (!resultsByMonth[month]) {
        resultsByMonth[month] = [];
      }
      if (!resultsByType[type]) {
        resultsByType[type] = [];
      }

      const grade = Number(point.grade);
      const maxPoints = point.exam.maxPoints;
      const percentage = calculatePercentage(grade, maxPoints);
      const passStatus = getPassStatus(grade, maxPoints);

      const result = {
        id: point.id,
        examId: point.exam.id,
        examDescription: point.exam.description,
        evaluationType: point.exam.evaluationType,
        month: point.exam.month,
        grade,
        maxPoints,
        passingPoints: point.exam.passingPoints,
        status: passStatus,
        note: point.note,
        feedback: point.feedback,
        subject: point.exam.subject?.name || 'غير محدد',
        examType: point.exam.examType?.name || 'غير محدد',
        surah: point.surah ? {
          name: point.surah.name,
          startVerse: point.startVerse,
          endVerse: point.endVerse
        } : null,
        criteriaScores: point.criteriaScores.map(cs => ({
          criteriaName: cs.criteria.name,
          score: Number(cs.score),
          weight: Number(cs.criteria.weight)
        })),
        percentage,
        performance: getPerformanceLevel(grade, maxPoints, point.exam.passingPoints),
        createdAt: point.createdAt
      };

      resultsByMonth[month].push(result);
      resultsByType[type].push(result);
    });

    // حساب الإحصائيات العامة باستخدام النظام الموحد
    const totalExams = examPoints.length;

    // إذا لم توجد امتحانات، إرجاع بيانات فارغة
    if (totalExams === 0) {
      const summary = {
        student: {
          id: student.id,
          name: student.name,
          username: student.username,
          age: student.age,
          phone: student.phone,
          classe: student.classe,
          guardian: student.guardian
        },
        attendanceStats,
        statistics: {
          totalExams: 0,
          passedExams: 0,
          failedExams: 0,
          excellentExams: 0,
          averageGrade: 0,
          averagePercentage: 0,
          highestGrade: 0,
          lowestGrade: 0,
          passRate: 0
        },
        typeStatistics: [],
        resultsByMonth: {},
        resultsByType: {},
        allResults: [],
        reportGeneratedAt: new Date().toISOString(),
        filters: {
          studentId,
          classeId: classeId || 'all',
          month: month || 'all',
          evaluationType: evaluationType || 'all'
        }
      };

      return NextResponse.json({
        success: true,
        data: summary,
        message: 'لا توجد امتحانات للطالب المحدد'
      });
    }

    // تحضير البيانات للنظام الموحد
    const gradesData = examPoints.map(point => ({
      grade: Number(point.grade),
      maxPoints: point.exam.maxPoints
    }));

    // حساب الإحصائيات باستخدام النظام الموحد
    const statistics = generateGradeStatistics(gradesData);

    // حساب المعدل العام
    const averageResult = calculateAverage(gradesData);

    const highestGrade = totalExams > 0 ? Math.max(...examPoints.map(point => Number(point.grade))) : 0;
    const lowestGrade = totalExams > 0 ? Math.min(...examPoints.map(point => Number(point.grade))) : 0;

    // حساب الإحصائيات حسب نوع التقييم باستخدام النظام الموحد
    const typeStatistics = Object.entries(resultsByType).map(([type, results]) => {
      const typeGradesData = results.map(result => ({
        grade: result.grade,
        maxPoints: result.maxPoints
      }));

      const typeStats = generateGradeStatistics(typeGradesData);
      const typeAverage = calculateAverage(typeGradesData);

      return {
        type,
        totalExams: results.length,
        averageGrade: typeAverage.average,
        passedExams: typeStats.passed,
        passRate: typeStats.passRate,
        excellentExams: typeStats.excellent,
        averagePercentage: typeAverage.percentage,
        gradeLevel: typeAverage.gradeLevel.nameAr
      };
    });

    const summary = {
      student: {
        id: student.id,
        name: student.name,
        username: student.username,
        age: student.age,
        phone: student.phone,
        classe: student.classe,
        guardian: student.guardian
      },
      attendanceStats,
      statistics: {
        totalExams,
        passedExams: statistics.passed,
        failedExams: statistics.failed,
        excellentExams: statistics.excellent,
        averageGrade: averageResult.average,
        averagePercentage: averageResult.percentage,
        highestGrade,
        lowestGrade,
        passRate: statistics.passRate
      },
      typeStatistics,
      resultsByMonth,
      resultsByType,
      allResults: examPoints.map(point => {
        const grade = Number(point.grade);
        const maxPoints = point.exam.maxPoints;
        const percentage = calculatePercentage(grade, maxPoints);
        const passStatus = getPassStatus(grade, maxPoints);

        return {
          id: point.id,
          examId: point.exam.id,
          examDescription: point.exam.description,
          evaluationType: point.exam.evaluationType,
          month: point.exam.month,
          grade,
          maxPoints,
          percentage,
          status: passStatus,
          performance: getPerformanceLevel(grade, maxPoints, point.exam.passingPoints),
          subject: point.exam.subject?.name || 'غير محدد',
          examType: point.exam.examType?.name || 'غير محدد',
          createdAt: point.createdAt
        };
      }),
      reportGeneratedAt: new Date().toISOString(),
      filters: {
        studentId,
        classeId: classeId || 'all',
        month: month || 'all',
        evaluationType: evaluationType || 'all'
      }
    };

    return NextResponse.json({
      success: true,
      data: summary,
      message: `تم إنشاء كشف درجات الطالب بنجاح - ${totalExams} امتحان`
    });

  } catch (error) {
    console.error('Error generating student report:', error);
    return NextResponse.json(
      {
        message: "حدث خطأ أثناء إنشاء كشف درجات الطالب",
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function getPerformanceLevel(grade: number, maxPoints: number, passingPoints: number): string {
  const gradeLevel = getGradeLevelByScore(grade, maxPoints);
  return gradeLevel.nameAr;
}

// دالة حساب إحصائيات الحضور والغياب
function calculateAttendanceStats(attendanceRecords: any[]) {
  if (!attendanceRecords || attendanceRecords.length === 0) {
    return {
      totalDays: 0,
      presentDays: 0,
      absentDays: 0,
      excusedDays: 0,
      presentRate: 0,
      absentRate: 0,
      excusedRate: 0,
      recentAttendance: []
    };
  }

  const totalDays = attendanceRecords.length;
  const presentDays = attendanceRecords.filter(record => record.status === 'PRESENT').length;
  const absentDays = attendanceRecords.filter(record => record.status === 'ABSENT').length;
  const excusedDays = attendanceRecords.filter(record => record.status === 'EXCUSED').length;

  const presentRate = totalDays > 0 ? Math.round((presentDays / totalDays) * 100) : 0;
  const absentRate = totalDays > 0 ? Math.round((absentDays / totalDays) * 100) : 0;
  const excusedRate = totalDays > 0 ? Math.round((excusedDays / totalDays) * 100) : 0;

  // آخر 10 سجلات حضور للعرض
  const recentAttendance = attendanceRecords.slice(0, 10).map(record => ({
    date: record.date,
    status: record.status,
    hisass: record.hisass || null
  }));

  return {
    totalDays,
    presentDays,
    absentDays,
    excusedDays,
    presentRate,
    absentRate,
    excusedRate,
    recentAttendance
  };
}

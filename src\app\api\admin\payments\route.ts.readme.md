# API تسجيل المدفوعات باسم الولي

## 📋 الوصف
API endpoint لتسجيل المدفوعات باسم الولي وليس باسم التلميذ، مع ربط تلقائي بالفواتير المستحقة.

## 🎯 الهدف
- تسجيل المدفوعات باسم الولي بشكل واضح ومؤكد
- ربط المدفوعات تلقائياً بالفواتير المستحقة
- تتبع دقيق لحالة الفواتير (مدفوعة، مدفوعة جزئياً، غير مدفوعة)
- توثيق شامل لكل عملية دفع

## 🔗 المسارات

### POST /api/admin/payments
تسجيل دفعة جديدة باسم الولي

#### المدخلات:
```json
{
  "studentId": 123,
  "amount": 5000.00,
  "paymentMethod": "نقداً",
  "notes": "دفعة شهرية",
  "receiptNumber": "REC-001",
  "parentName": "أحمد محمد"
}
```

#### المخرجات:
```json
{
  "success": true,
  "message": "تم تسجيل دفعة بقيمة 5000 دج باسم الولي: أحمد محمد",
  "payment": {
    "id": 456,
    "amount": 5000,
    "date": "2025-06-21T10:30:00Z",
    "studentName": "محمد أحمد",
    "guardianName": "أحمد محمد",
    "paymentMethod": "نقداً",
    "receiptNumber": "REC-001",
    "notes": "دفعة شهرية\n\n✅ دفعة مسجلة باسم الولي: أحمد محمد"
  },
  "updatedInvoices": [
    {
      "invoiceId": 789,
      "paidAmount": 3000,
      "newStatus": "PAID"
    },
    {
      "invoiceId": 790,
      "paidAmount": 2000,
      "newStatus": "PARTIALLY_PAID"
    }
  ],
  "remainingAmount": 0
}
```

### GET /api/admin/payments
جلب المدفوعات للمراجعة

#### المعاملات:
- `limit`: عدد النتائج (افتراضي: 50)
- `offset`: نقطة البداية (افتراضي: 0)

## ✨ الميزات الرئيسية

### 1. تسجيل واضح باسم الولي
- إضافة ملاحظة تلقائية تؤكد أن الدفعة باسم الولي
- تسجيل اسم الولي في حقل منفصل
- تتبع دقيق لمن قام بالدفع

### 2. ربط تلقائي بالفواتير
- ربط المدفوعات تلقائياً بالفواتير المستحقة حسب تاريخ الاستحقاق
- تحديث حالة الفواتير تلقائياً
- توزيع المبلغ على عدة فواتير إذا لزم الأمر

### 3. إدارة طرق الدفع
- إنشاء طرق دفع جديدة تلقائياً إذا لم تكن موجودة
- دعم طرق دفع متعددة (نقداً، تحويل بنكي، شيك، إلخ)

### 4. تتبع شامل
- تسجيل تفصيلي لكل عملية
- ملاحظات واضحة ومفصلة
- أرقام إيصالات للمرجعية

## 🔒 الأمان والصلاحيات
- يتطلب تسجيل دخول صحيح
- صلاحيات ADMIN أو EMPLOYEE فقط
- التحقق من صحة البيانات المدخلة
- حماية من SQL injection

## 📊 التسجيل والمراقبة
- تسجيل مفصل لكل عملية في console
- تتبع الأخطاء والاستثناءات
- إحصائيات الأداء

## 🧪 الاختبار
```bash
# تسجيل دفعة جديدة
curl -X POST http://localhost:3000/api/admin/payments \
  -H "Content-Type: application/json" \
  -d '{
    "studentId": 1,
    "amount": 5000,
    "paymentMethod": "نقداً",
    "notes": "دفعة شهرية",
    "parentName": "أحمد محمد"
  }'

# جلب المدفوعات
curl http://localhost:3000/api/admin/payments?limit=10&offset=0
```

## 🔄 التكامل
- يتكامل مع صفحة إدارة ديون الأولياء
- يحدث البيانات تلقائياً في الواجهة الأمامية
- يرسل إشعارات نجاح/فشل للمستخدم

## 📝 ملاحظات التطوير
- استخدام Prisma ORM للتفاعل مع قاعدة البيانات
- معالجة شاملة للأخطاء
- دعم المعاملات (transactions) لضمان تناسق البيانات
- تحسين الأداء مع الاستعلامات المحسنة

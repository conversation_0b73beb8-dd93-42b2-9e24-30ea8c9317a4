import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/treasury - الحصول على معلومات الخزينة
export async function GET() {
  try {
    // الحصول على الخزينة (نفترض أن هناك خزينة واحدة فقط)
    let treasury = await prisma.treasury.findFirst();

    // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 0,
          totalIncome: 0,
          totalExpense: 0,
        },
      });
    }

    // الحصول على إحصائيات الشهر الحالي
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // جلب مجموع المداخيل للشهر الحالي
    const monthlyIncome = await prisma.income.aggregate({
      _sum: {
        amount: true,
      },
      where: {
        date: {
          gte: firstDayOfMonth,
          lte: lastDayOfMonth,
        },
      },
    });

    // جلب مجموع المصاريف للشهر الحالي
    const monthlyExpense = await prisma.expense.aggregate({
      _sum: {
        amount: true,
      },
      where: {
        date: {
          gte: firstDayOfMonth,
          lte: lastDayOfMonth,
        },
      },
    });

    // جلب آخر 5 معاملات (مداخيل ومصاريف)
    const recentIncomes = await prisma.income.findMany({
      take: 5,
      orderBy: { date: 'desc' },
    });

    const recentExpenses = await prisma.expense.findMany({
      take: 5,
      orderBy: { date: 'desc' },
    });

    // تعريف واجهة للمعاملات
    interface Transaction {
      id: string;
      type: 'income' | 'expense';
      amount: number;
      description: string;
      date: Date;
    }

    // دمج وترتيب المعاملات حسب التاريخ
    const recentTransactions: Transaction[] = [
      ...recentIncomes.map(income => ({
        id: `income-${income.id}`,
        type: 'income' as const,
        amount: income.amount,
        description: income.source,
        date: income.date,
      })),
      ...recentExpenses.map(expense => ({
        id: `expense-${expense.id}`,
        type: 'expense' as const,
        amount: expense.amount,
        description: expense.purpose,
        date: expense.date,
      })),
    ].sort((a, b) => b.date.getTime() - a.date.getTime()).slice(0, 5);

    return NextResponse.json({
      treasury,
      monthlyStats: {
        income: monthlyIncome._sum.amount || 0,
        expense: monthlyExpense._sum.amount || 0,
      },
      recentTransactions,
    });
  } catch (error) {
    console.error('خطأ في جلب معلومات الخزينة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب معلومات الخزينة' },
      { status: 500 }
    );
  }
}

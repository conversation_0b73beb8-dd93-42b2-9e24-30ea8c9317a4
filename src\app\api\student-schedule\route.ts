import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

// GET /api/student-schedule - جلب جدول الحصص الخاص بالطالب المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'STUDENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات الطالب
    const student = await prisma.student.findFirst({
      where: {
        username: userData.username
      }
    });

    if (!student) {
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات الطالب" },
        { status: 404 }
      );
    }

    // جلب جدول الحصص للفصل الذي ينتمي إليه الطالب
    const classSchedule = await prisma.classSchedule.findMany({
      where: {
        classeId: student.classeId ?? undefined
      },
      include: {
        teacherSubject: {
          include: {
            subject: true,
            teacher: true
          }
        }
      },
      orderBy: [
        { day: 'asc' },
        { startTime: 'asc' }
      ]
    });

    // تنسيق البيانات للعرض
    const schedule = classSchedule.map(cs => ({
      id: cs.id,
      day: cs.day,
      startTime: cs.startTime,
      endTime: cs.endTime,
      subjectId: cs.teacherSubject.subject.id,
      subjectName: cs.teacherSubject.subject.name,
      teacherId: cs.teacherSubject.teacher.id,
      teacherName: cs.teacherSubject.teacher.name
    }));

    return NextResponse.json({
      schedule,
      message: "تم جلب جدول الحصص بنجاح"
    });
  } catch (error) {
    console.error('Error fetching student schedule:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب جدول الحصص" },
      { status: 500 }
    );
  }
}

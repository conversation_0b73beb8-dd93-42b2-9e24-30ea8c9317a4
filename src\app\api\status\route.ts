import os from "os";
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Get system memory information
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;

    // Get Node.js process memory usage
    const processMemory = process.memoryUsage();

    return NextResponse.json({
      system: {
        total: totalMemory,      // إجمالي ذاكرة النظام
        free: freeMemory,        // الذاكرة المتاحة في النظام
        used: usedMemory,        // الذاكرة المستخدمة في النظام
        percentUsed: Math.round((usedMemory / totalMemory) * 100)  // النسبة المئوية للذاكرة المستخدمة في النظام
      },
      process: {
        heapTotal: processMemory.heapTotal,    // إجمالي ذاكرة الكومة المخصصة للمشروع
        heapUsed: processMemory.heapUsed,      // ذاكرة الكومة المستخدمة من قبل المشروع
        rss: processMemory.rss,                // حجم الذاكرة الإجمالي المخصص للمشروع
        percentHeapUsed: Math.round((processMemory.heapUsed / processMemory.heapTotal) * 100)  // النسبة المئوية لاستخدام ذاكرة الكومة
      }
    }, { status: 200 });
  } catch (error: unknown) {
    console.error('Error fetching system status:', error);
    return NextResponse.json({ error: 'فشل في جلب حالة النظام' }, { status: 500 });
  }
}

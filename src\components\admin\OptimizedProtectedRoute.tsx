'use client';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/contexts/PermissionsContext';
import SiteLogo from '@/components/SiteLogo';

interface OptimizedProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requiredPermissions?: string[];
  requireAll?: boolean;
  fallbackPath?: string;
  loadingComponent?: React.ReactNode;
}

const DefaultLoadingComponent = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#f8fffd] to-white">
    <div className="text-center">
      <SiteLogo className="mx-auto mb-4" />
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)] mx-auto mb-4"></div>
      <p className="text-gray-600">جاري التحقق من الصلاحيات...</p>
    </div>
  </div>
);

const UnauthorizedComponent = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#f8fffd] to-white">
    <div className="text-center">
      <SiteLogo className="mx-auto mb-4" />
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
        <h2 className="text-xl font-semibold text-red-800 mb-2">غير مصرح لك بالوصول</h2>
        <p className="text-red-600 mb-4">ليس لديك الصلاحيات اللازمة لعرض هذه الصفحة</p>
        <button
          onClick={() => window.history.back()}
          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          العودة للخلف
        </button>
      </div>
    </div>
  </div>
);

/**
 * مكون محسن لحماية الصفحات بناءً على الصلاحيات
 * يستخدم Context لتحسين الأداء ويدعم التحقق من عدة صلاحيات
 */
const OptimizedProtectedRoute: React.FC<OptimizedProtectedRouteProps> = ({
  children,
  requiredPermission,
  requiredPermissions,
  requireAll = false,
  fallbackPath = '/admin',
  loadingComponent
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, userRole, loading, isReady } = usePermissions();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null);

  useEffect(() => {
    if (!isReady || loading) {
      return;
    }

    // إذا لم يكن هناك مستخدم مسجل دخول
    if (!userRole) {
      console.log('OptimizedProtectedRoute - No user role, redirecting to login');
      router.replace('/login');
      return;
    }

    // المدير لديه جميع الصلاحيات
    if (userRole === 'ADMIN') {
      console.log('OptimizedProtectedRoute - Admin user, access granted');
      setIsAuthorized(true);
      return;
    }

    let hasRequiredPermission = false;

    // التحقق من الصلاحيات
    if (requiredPermission) {
      hasRequiredPermission = hasPermission(requiredPermission);
      console.log(`OptimizedProtectedRoute - Single permission check: ${requiredPermission} = ${hasRequiredPermission}`);
    } else if (requiredPermissions && requiredPermissions.length > 0) {
      if (requireAll) {
        hasRequiredPermission = hasAllPermissions(requiredPermissions);
        console.log(`OptimizedProtectedRoute - All permissions check: ${requiredPermissions.join(', ')} = ${hasRequiredPermission}`);
      } else {
        hasRequiredPermission = hasAnyPermission(requiredPermissions);
        console.log(`OptimizedProtectedRoute - Any permission check: ${requiredPermissions.join(', ')} = ${hasRequiredPermission}`);
      }
    } else {
      // إذا لم يتم تحديد أي صلاحية، نسمح بالوصول للمدير فقط
      hasRequiredPermission = false;
      console.log('OptimizedProtectedRoute - No permissions specified, access denied for non-admin');
    }

    if (hasRequiredPermission) {
      console.log('OptimizedProtectedRoute - Permission check passed, access granted');
      setIsAuthorized(true);
    } else {
      console.log('OptimizedProtectedRoute - Permission check failed, access denied');
      setIsAuthorized(false);
    }
  }, [
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    userRole,
    loading,
    isReady,
    requiredPermission,
    requiredPermissions,
    requireAll,
    router
  ]);

  // أثناء التحميل
  if (loading || !isReady || isAuthorized === null) {
    return loadingComponent || <DefaultLoadingComponent />;
  }

  // إذا لم يكن مصرح له
  if (!isAuthorized) {
    return <UnauthorizedComponent />;
  }

  // إذا كان مصرح له
  return <>{children}</>;
};

export default OptimizedProtectedRoute;

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, Pencil, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { EvaluationType } from '@prisma/client';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

// دالة لتحويل نوع التقييم إلى نص مقروء
const getEvaluationTypeLabel = (type: EvaluationType): string => {
  const labels: Partial<Record<EvaluationType, string>> = {
    QURAN_MEMORIZATION: 'حفظ القرآن',
    QURAN_RECITATION: 'تلاوة القرآن',
    WRITTEN_EXAM: 'تحريري',
    ORAL_EXAM: 'شفهي',
    PRACTICAL_TEST: 'عملي',
    HOMEWORK: 'واجب منزلي',
    PROJECT: 'مشروع',
    REMOTE_EXAM: 'امتحان عن بعد'
  };
  return labels[type] || String(type);
};

// دالة لتنسيق التاريخ
const formatDate = (dateString: string): string => {
  const [year, month] = dateString.split('-');
  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];
  return `${monthNames[parseInt(month) - 1]} ${year}`;
};

export default function AdminExamsPage() {
  // تعريف نوع البيانات للامتحان
  interface Exam {
    id: number;
    evaluationType: EvaluationType;
    month: string;
    maxPoints: number;
    passingPoints: number;
    description?: string | null;
    classId?: number;
    typeId?: number;
    requiresSurah?: boolean;
    examType?: {
      id: number;
      name: string;
    } | null;
    examCriteria?: {
      id: number;
      criteriaId: number;
      criteria: {
        id: number;
        name: string;
        weight: string | number;
      }
    }[];
  }

  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateExamOpen, setIsCreateExamOpen] = useState(false);
  const [isEditExamOpen, setIsEditExamOpen] = useState(false);
  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);
  const [examTypes, setExamTypes] = useState<{id: number, name: string}[]>([]);
  const [isExamTypesLoading, setIsExamTypesLoading] = useState(true);
  const [evaluationTypes, setEvaluationTypes] = useState<{id: string, name: string, description: string}[]>([]);
  const [isEvaluationTypesLoading, setIsEvaluationTypesLoading] = useState(true);
  const [evaluationCriteria, setEvaluationCriteria] = useState<{id: number, name: string, weight: number, description: string}[]>([]);
  const [isEvaluationCriteriaLoading, setIsEvaluationCriteriaLoading] = useState(true);
  const [selectedCriteria, setSelectedCriteria] = useState<number[]>([]);
  const [editSelectedCriteria, setEditSelectedCriteria] = useState<number[]>([]);
  const [subjects, setSubjects] = useState<{id: number, name: string}[]>([]);
  const [isSubjectsLoading, setIsSubjectsLoading] = useState(true);

  useEffect(() => {
    fetchExams();
    fetchExamTypes();
    fetchEvaluationTypes();
    fetchEvaluationCriteria();
    fetchSubjects();
  }, []);

  const fetchSubjects = async () => {
    try {
      setIsSubjectsLoading(true);
      const response = await fetch('/api/subjects');
      if (response.ok) {
        const data = await response.json();
        console.log('Subjects data:', data);
        if (Array.isArray(data)) {
          setSubjects(data);
        } else {
          console.error('Unexpected subjects data format:', data);
          setSubjects([]);
        }
      } else {
        console.error('Failed to fetch subjects:', response.status);
        setSubjects([]);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      setSubjects([]);
    } finally {
      setIsSubjectsLoading(false);
    }
  };

  const fetchExams = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/evaluation/exams');
      if (!response.ok) throw new Error('فشل في جلب بيانات الامتحانات');
      const data = await response.json();

      if (data.success && Array.isArray(data.data)) {
        setExams(data.data);
      } else {
        setExams([]);
      }
    } catch (error) {
      console.error('Error fetching exams:', error);
      toast.error('حدث خطأ أثناء جلب الامتحانات');
      setExams([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchExamTypes = async () => {
    try {
      setIsExamTypesLoading(true);
      const response = await fetch('/api/exam-types');
      if (response.ok) {
        const data = await response.json();
        console.log('Exam types data:', data);
        if (Array.isArray(data)) {
          setExamTypes(data);
        } else if (data && Array.isArray(data.data)) {
          setExamTypes(data.data);
        } else {
          console.error('Unexpected exam types data format:', data);
          setExamTypes([]);
        }
      } else {
        console.error('Failed to fetch exam types:', response.status);
        setExamTypes([]);
      }
    } catch (error) {
      console.error('Error fetching exam types:', error);
      setExamTypes([]);
    } finally {
      setIsExamTypesLoading(false);
    }
  };

  const fetchEvaluationTypes = async () => {
    try {
      setIsEvaluationTypesLoading(true);
      const response = await fetch('/api/evaluation/types');
      if (response.ok) {
        const data = await response.json();
        console.log('Evaluation types data:', data);
        if (Array.isArray(data)) {
          setEvaluationTypes(data);
        } else {
          console.error('Unexpected evaluation types data format:', data);
          // إذا لم يتم الحصول على البيانات بشكل صحيح، نترك القائمة فارغة
          setEvaluationTypes([]);
        }
      } else {
        console.error('Failed to fetch evaluation types:', response.status);
        // إذا فشل الطلب، نترك القائمة فارغة
        setEvaluationTypes([]);
      }
    } catch (error) {
      console.error('Error fetching evaluation types:', error);
      // إذا حدث خطأ، نترك القائمة فارغة
      setEvaluationTypes([]);
    } finally {
      setIsEvaluationTypesLoading(false);
    }
  };

  const fetchEvaluationCriteria = async () => {
    try {
      setIsEvaluationCriteriaLoading(true);
      const response = await fetch('/api/evaluation-criteria');
      if (response.ok) {
        const data = await response.json();
        console.log('Evaluation criteria data:', data);
        if (data.success && Array.isArray(data.data)) {
          // إذا كانت قاعدة البيانات تحتوي على معايير تقييم، نعرضها
          setEvaluationCriteria(data.data);
        } else {
          console.error('Unexpected evaluation criteria data format:', data);
          // إذا كان هناك خطأ في تنسيق البيانات، نترك القائمة فارغة
          setEvaluationCriteria([]);
        }
      } else {
        console.error('Failed to fetch evaluation criteria:', response.status);
        // إذا فشل الطلب، نترك القائمة فارغة
        setEvaluationCriteria([]);
      }
    } catch (error) {
      console.error('Error fetching evaluation criteria:', error);
      // إذا حدث خطأ، نترك القائمة فارغة
      setEvaluationCriteria([]);
    } finally {
      setIsEvaluationCriteriaLoading(false);
    }
  };

  const handleDeleteExam = async (examId: number) => {
    if (confirm('هل أنت متأكد من حذف هذا الامتحان؟')) {
      try {
        const response = await fetch(`/api/evaluation/exams?id=${examId}`, {
          method: 'DELETE',
        });
        const result = await response.json();
        if (result.success) {
          toast.success('تم حذف الامتحان بنجاح');
          fetchExams(); // إعادة تحميل البيانات
        } else {
          toast.error(result.error || 'حدث خطأ أثناء حذف الامتحان');
        }
      } catch (error) {
        console.error('Error deleting exam:', error);
        toast.error('حدث خطأ أثناء حذف الامتحان');
      }
    }
  };

  const handleEditExam = async (exam: Exam) => {
    setSelectedExam(exam);
    setIsEditExamOpen(true);

    // جلب معايير التقييم المرتبطة بالامتحان
    try {
      const response = await fetch(`/api/exam-criteria?examId=${exam.id}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data && Array.isArray(data.data.criteriaIds)) {
          setEditSelectedCriteria(data.data.criteriaIds);
        } else {
          setEditSelectedCriteria([]);
        }
      } else {
        console.error('Failed to fetch exam criteria:', response.status);
        setEditSelectedCriteria([]);
      }
    } catch (error) {
      console.error('Error fetching exam criteria:', error);
      setEditSelectedCriteria([]);
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // التحقق من وجود الامتحان المحدد
    if (!selectedExam) {
      toast.error('لم يتم العثور على الامتحان');
      return;
    }

    try {
      const formData = new FormData(e.currentTarget as HTMLFormElement);

      // تحويل معايير التقييم إلى أرقام
      const numericCriteriaIds = editSelectedCriteria.map(id => Number(id));

      const data = {
        id: selectedExam.id,
        evaluationType: formData.get('evaluationType'),
        month: formData.get('month'),
        description: formData.get('description') || null,
        maxPoints: Number(formData.get('maxPoints')),
        passingPoints: Number(formData.get('passingPoints')),
        typeId: formData.get('examTypeId') || null,
        requiresSurah: formData.get('evaluationType') === 'QURAN_MEMORIZATION',
        criteriaIds: numericCriteriaIds,
        subjectId: formData.get('subjectId') || null,
        isPeriodic: formData.get('isPeriodic') === 'true',
        period: formData.get('period') || null
      };

      console.log('Sending data to update exam:', data);

      // Note: This API endpoint doesn't exist yet and would need to be created
      // تحويل البيانات إلى سلسلة JSON
      // تأكد من أن البيانات صالحة قبل تحويلها إلى JSON
      const cleanData = {
        evaluationType: String(data.evaluationType),
        month: String(data.month),
        description: data.description,
        maxPoints: Number(data.maxPoints),
        passingPoints: Number(data.passingPoints),
        typeId: data.typeId ? String(data.typeId) : null,
        requiresSurah: Boolean(data.requiresSurah),
        criteriaIds: data.criteriaIds.map(Number),
        subjectId: data.subjectId ? String(data.subjectId) : null,
        isPeriodic: Boolean(data.isPeriodic),
        period: data.period
      };

      const jsonData = JSON.stringify(cleanData);
      console.log('JSON data being sent:', jsonData);

      const response = await fetch(`/api/evaluation/exams/${selectedExam.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonData,
      });

      const result = await response.json();
      console.log('Update exam response:', result);

      if (response.ok && result.success) {
        toast.success('تم تحديث الامتحان بنجاح');
        setIsEditExamOpen(false);
        await fetchExams(); // إعادة تحميل البيانات
      } else {
        toast.error(result.error || 'حدث خطأ أثناء تحديث الامتحان');
        console.error('Error updating exam:', result);
      }
    } catch (error) {
      console.error('Error updating exam:', error);
      toast.error('حدث خطأ أثناء تحديث الامتحان');
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.evaluation.view">
      <div className="container mx-auto p-4">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
        <h1 className="text-2xl font-bold">إدارة الامتحانات</h1>
        <QuickActionButtons
          entityType="evaluation"
          actions={[
            {
              key: 'create',
              label: 'إنشاء امتحان جديد',
              icon: <Plus size={16} />,
              onClick: () => setIsCreateExamOpen(true),
              variant: 'primary'
            }
          ]}
          className="w-full md:w-auto"
        />
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
          <span className="mr-2">جاري تحميل الامتحانات...</span>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>جميع الامتحانات</CardTitle>
          </CardHeader>
          <CardContent>
            {exams.length === 0 ? (
              <div className="text-center p-8">
                <p className="text-gray-500">لا توجد امتحانات متاحة</p>
              </div>
            ) : (
              <>
                {/* عرض الجدول على الشاشات الكبيرة */}
                <div className="hidden md:block overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="text-right">نوع التقييم</TableHead>
                        <TableHead className="text-right">نوع الامتحان</TableHead>
                        <TableHead className="text-right">الشهر</TableHead>
                        <TableHead className="text-right">الدرجة القصوى</TableHead>
                        <TableHead className="text-right">درجة النجاح</TableHead>
                        <TableHead className="text-right">معايير التقييم</TableHead>
                        <TableHead className="text-right">الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {exams.map((exam) => (
                        <TableRow key={exam.id}>
                          <TableCell>{getEvaluationTypeLabel(exam.evaluationType)}</TableCell>
                          <TableCell>{exam.examType?.name || '-'}</TableCell>
                          <TableCell>{formatDate(exam.month)}</TableCell>
                          <TableCell>{exam.maxPoints}</TableCell>
                          <TableCell>{exam.passingPoints}</TableCell>
                          <TableCell>
                            {exam.examCriteria && exam.examCriteria.length > 0 ? (
                              <div className="flex flex-wrap gap-1">
                                {exam.examCriteria.map((ec) => (
                                  <span
                                    key={ec.id}
                                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#e9f7f5] text-[var(--primary-color)]"
                                    title={`الوزن: ${(Number(ec.criteria.weight) * 100).toFixed(0)}%`}
                                  >
                                    {ec.criteria.name}
                                  </span>
                                ))}
                              </div>
                            ) : (
                              <span className="text-gray-400 text-sm">لا توجد معايير</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-2">
                              <PermissionGuard requiredPermission="admin.evaluation.results">
                                <Link href={`/admin/evaluation/results?examId=${exam.id}`}>
                                  <Button variant="outline" size="sm">عرض النتائج</Button>
                                </Link>
                              </PermissionGuard>
                              <PermissionGuard requiredPermission="admin.evaluation.questions">
                                <Link href={`/admin/evaluation/exam-questions?examId=${exam.id}`}>
                                  <Button variant="outline" size="sm" className="bg-blue-50 hover:bg-blue-100">
                                    إدارة الأسئلة
                                  </Button>
                                </Link>
                              </PermissionGuard>
                              <OptimizedActionButtonGroup
                                entityType="evaluation"
                                onEdit={() => handleEditExam(exam)}
                                onDelete={() => handleDeleteExam(exam.id)}
                                showEdit={true}
                                showDelete={true}
                                className="gap-2"
                              />
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* عرض البطاقات على الشاشات الصغيرة */}
                <div className="md:hidden space-y-4">
                  {exams.map((exam) => (
                    <Card key={exam.id} className="border border-[#e9f7f5] shadow-sm">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg flex justify-between items-center">
                          <span>{getEvaluationTypeLabel(exam.evaluationType)}</span>
                          <span className="text-sm font-normal text-gray-500">{formatDate(exam.month)}</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pb-2 space-y-3">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="font-medium">نوع الامتحان:</span>
                            <span className="block">{exam.examType?.name || '-'}</span>
                          </div>
                          <div>
                            <span className="font-medium">الدرجة القصوى:</span>
                            <span className="block">{exam.maxPoints}</span>
                          </div>
                          <div>
                            <span className="font-medium">درجة النجاح:</span>
                            <span className="block">{exam.passingPoints}</span>
                          </div>
                        </div>

                        <div>
                          <span className="font-medium text-sm">معايير التقييم:</span>
                          {exam.examCriteria && exam.examCriteria.length > 0 ? (
                            <div className="flex flex-wrap gap-1 mt-1">
                              {exam.examCriteria.map((ec) => (
                                <span
                                  key={ec.id}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-[#e9f7f5] text-[var(--primary-color)]"
                                  title={`الوزن: ${(Number(ec.criteria.weight) * 100).toFixed(0)}%`}
                                >
                                  {ec.criteria.name}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <span className="block text-gray-400 text-sm">لا توجد معايير</span>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-2 pt-2">
                          <PermissionGuard requiredPermission="admin.evaluation.results">
                            <Link href={`/admin/evaluation/results?examId=${exam.id}`} className="col-span-2">
                              <Button variant="outline" size="sm" className="w-full">
                                عرض النتائج
                              </Button>
                            </Link>
                          </PermissionGuard>
                          <PermissionGuard requiredPermission="admin.evaluation.questions">
                            <Link href={`/admin/evaluation/exam-questions?examId=${exam.id}`} className="col-span-2">
                              <Button variant="outline" size="sm" className="w-full bg-blue-50 hover:bg-blue-100">
                                إدارة الأسئلة
                              </Button>
                            </Link>
                          </PermissionGuard>
                          <PermissionGuard requiredPermission="admin.evaluation.edit">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditExam(exam)}
                              className="w-full"
                            >
                              <Pencil className="h-4 w-4 ml-1" />
                              تعديل
                            </Button>
                          </PermissionGuard>
                          <PermissionGuard requiredPermission="admin.evaluation.delete">
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full text-red-500 hover:text-red-700"
                              onClick={() => handleDeleteExam(exam.id)}
                            >
                              <Trash2 className="h-4 w-4 ml-1" />
                              حذف
                            </Button>
                          </PermissionGuard>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      )}

      {/* Create Exam Dialog */}
      <Dialog open={isCreateExamOpen} onOpenChange={(open) => !open && setIsCreateExamOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>إنشاء امتحان جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل الامتحان الجديد</DialogDescription>
          </DialogHeader>
          <form
            className="space-y-4"
            onSubmit={async (e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget as HTMLFormElement);
              // تحويل معايير التقييم إلى أرقام
              const numericCriteriaIds = selectedCriteria.map(id => Number(id));

              // تأكد من أن البيانات صالحة قبل تحويلها إلى JSON
              const data = {
                evaluationType: String(formData.get('evaluationType')),
                month: String(formData.get('month')),
                description: formData.get('description') || null,
                maxPoints: Number(formData.get('maxPoints')),
                passingPoints: Number(formData.get('passingPoints')),
                typeId: formData.get('examTypeId') ? String(formData.get('examTypeId')) : null,
                requiresSurah: formData.get('evaluationType') === 'QURAN_MEMORIZATION',
                criteriaIds: numericCriteriaIds,
                subjectId: formData.get('subjectId') ? String(formData.get('subjectId')) : null,
                isPeriodic: formData.get('isPeriodic') === 'true',
                period: formData.get('period') || null
              };

              console.log('Sending data to create exam:', data);

              try {
                const response = await fetch('/api/evaluation/exams', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(data),
                });

                const result = await response.json();

                if (result.success) {
                  toast.success('تم إنشاء الامتحان بنجاح');
                  setIsCreateExamOpen(false);
                  fetchExams(); // إعادة تحميل البيانات
                } else {
                  toast.error(result.error || 'حدث خطأ أثناء إنشاء الامتحان');
                }
              } catch (error) {
                console.error('Error creating exam:', error);
                toast.error('حدث خطأ أثناء إنشاء الامتحان');
              }
            }}
          >
            <div className="space-y-2">
              <label className="text-right block">نوع التقييم</label>
              <Select name="evaluationType" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع التقييم" />
                </SelectTrigger>
                <SelectContent>
                  {isEvaluationTypesLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل أنواع التقييم...</SelectItem>
                  ) : evaluationTypes.length > 0 ? (
                    evaluationTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>{type.description}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد أنواع تقييم متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">نوع الامتحان</label>
              <Select name="examTypeId" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر نوع الامتحان" />
                </SelectTrigger>
                <SelectContent>
                  {isExamTypesLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل أنواع الامتحانات...</SelectItem>
                  ) : examTypes.length > 0 ? (
                    examTypes.map(type => (
                      <SelectItem key={type.id} value={type.id.toString()}>{type.name}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد أنواع امتحانات متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">المادة الدراسية</label>
              <Select name="subjectId" required>
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر المادة الدراسية" />
                </SelectTrigger>
                <SelectContent>
                  {isSubjectsLoading ? (
                    <SelectItem value="loading" disabled>جاري تحميل المواد الدراسية...</SelectItem>
                  ) : subjects.length > 0 ? (
                    subjects.map(subject => (
                      <SelectItem key={subject.id} value={subject.id.toString()}>{subject.name}</SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>لا توجد مواد دراسية متاحة</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-right block">الشهر</label>
              <Input type="month" className="text-right" name="month" required />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف (اختياري)</label>
              <Input className="text-right" placeholder="وصف الامتحان" name="description" />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الدرجة القصوى</label>
              <Input type="number" defaultValue={100} className="text-right" name="maxPoints" required />
            </div>
            <div className="space-y-2">
              <label className="text-right block">درجة النجاح</label>
              <Input type="number" defaultValue={60} className="text-right" name="passingPoints" required />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-right block">امتحان دوري</label>
                <Select name="isPeriodic" defaultValue="false">
                  <SelectTrigger className="w-32 text-right">
                    <SelectValue placeholder="اختر" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">نعم</SelectItem>
                    <SelectItem value="false">لا</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-right block">الفترة (للامتحانات الدورية)</label>
              <Select name="period">
                <SelectTrigger className="w-full text-right">
                  <SelectValue placeholder="اختر الفترة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="يومي">يومي</SelectItem>
                  <SelectItem value="أسبوعي">أسبوعي</SelectItem>
                  <SelectItem value="شهري">شهري</SelectItem>
                  <SelectItem value="فصلي">فصلي</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-right block">معايير التقييم (اختيارية)</label>
              <div className="border border-[#e9f7f5] rounded-md p-3 max-h-60 overflow-y-auto bg-white">
                {isEvaluationCriteriaLoading ? (
                  <div className="text-center py-2">
                    <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                    <p className="text-sm text-gray-500 mt-1">جاري تحميل معايير التقييم...</p>
                  </div>
                ) : evaluationCriteria.length > 0 ? (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm p-1 mb-2 border-b pb-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCriteria(evaluationCriteria.map(c => c.id))}
                      >
                        تحديد الكل
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedCriteria([])}
                      >
                        إلغاء تحديد الكل
                      </Button>
                    </div>
                    {evaluationCriteria.map(criteria => (
                      <div key={criteria.id} className="flex justify-between items-center text-sm p-1 hover:bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            id={`criteria-${criteria.id}`}
                            checked={selectedCriteria.includes(criteria.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedCriteria(prev => [...prev, criteria.id]);
                              } else {
                                setSelectedCriteria(prev => prev.filter(id => id !== criteria.id));
                              }
                            }}
                          />
                          <span className="text-gray-500">{(Number(criteria.weight) * 100).toFixed(0)}%</span>
                        </div>
                        <label
                          htmlFor={`criteria-${criteria.id}`}
                          className="font-medium cursor-pointer flex-1 text-right"
                        >
                          {criteria.name}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center text-sm text-gray-500 py-2">لا توجد معايير تقييم متاحة</p>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-1 text-right">اختر معايير التقييم التي سيتم استخدامها عند تقييم الطلاب في هذا الامتحان</p>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsCreateExamOpen(false)}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                إنشاء
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Exam Dialog */}
      <Dialog open={isEditExamOpen} onOpenChange={(open) => !open && setIsEditExamOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>تعديل الامتحان</DialogTitle>
            <DialogDescription>قم بتعديل تفاصيل الامتحان</DialogDescription>
          </DialogHeader>
          {selectedExam && (
            <form className="space-y-4" onSubmit={handleSubmitEdit}>
              <div className="space-y-2">
                <label className="text-right block">نوع التقييم</label>
                <Select name="evaluationType" defaultValue={selectedExam.evaluationType} required>
                  <SelectTrigger className="w-full text-right">
                    <SelectValue placeholder="اختر نوع التقييم" />
                  </SelectTrigger>
                  <SelectContent>
                    {isEvaluationTypesLoading ? (
                      <SelectItem value="loading" disabled>جاري تحميل أنواع التقييم...</SelectItem>
                    ) : evaluationTypes.length > 0 ? (
                      evaluationTypes.map(type => (
                        <SelectItem key={type.id} value={type.id}>{type.description}</SelectItem>
                      ))
                    ) : (
                      <SelectItem value="none" disabled>لا توجد أنواع تقييم متاحة</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-right block">نوع الامتحان</label>
                <Select name="examTypeId" defaultValue={selectedExam.typeId?.toString()} required>
                  <SelectTrigger className="w-full text-right">
                    <SelectValue placeholder="اختر نوع الامتحان" />
                  </SelectTrigger>
                  <SelectContent>
                    {isExamTypesLoading ? (
                      <SelectItem value="loading" disabled>جاري تحميل أنواع الامتحانات...</SelectItem>
                    ) : examTypes.length > 0 ? (
                      examTypes.map(type => (
                        <SelectItem key={type.id} value={type.id.toString()}>{type.name}</SelectItem>
                      ))
                    ) : (
                      <SelectItem value="none" disabled>لا توجد أنواع امتحانات متاحة</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-right block">المادة الدراسية</label>
                <Select name="subjectId" defaultValue={selectedExam.subjectId?.toString()} required>
                  <SelectTrigger className="w-full text-right">
                    <SelectValue placeholder="اختر المادة الدراسية" />
                  </SelectTrigger>
                  <SelectContent>
                    {isSubjectsLoading ? (
                      <SelectItem value="loading" disabled>جاري تحميل المواد الدراسية...</SelectItem>
                    ) : subjects.length > 0 ? (
                      subjects.map(subject => (
                        <SelectItem key={subject.id} value={subject.id.toString()}>{subject.name}</SelectItem>
                      ))
                    ) : (
                      <SelectItem value="none" disabled>لا توجد مواد دراسية متاحة</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-right block">الشهر</label>
                <Input type="month" className="text-right" name="month" defaultValue={selectedExam.month} required />
              </div>
              <div className="space-y-2">
                <label className="text-right block">الوصف (اختياري)</label>
                <Input className="text-right" placeholder="وصف الامتحان" name="description" defaultValue={selectedExam.description || ''} />
              </div>
              <div className="space-y-2">
                <label className="text-right block">الدرجة القصوى</label>
                <Input type="number" className="text-right" name="maxPoints" defaultValue={selectedExam.maxPoints} required />
              </div>
              <div className="space-y-2">
                <label className="text-right block">درجة النجاح</label>
                <Input type="number" className="text-right" name="passingPoints" defaultValue={selectedExam.passingPoints} required />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-right block">امتحان دوري</label>
                  <Select name="isPeriodic" defaultValue={selectedExam.isPeriodic ? "true" : "false"}>
                    <SelectTrigger className="w-32 text-right">
                      <SelectValue placeholder="اختر" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="true">نعم</SelectItem>
                      <SelectItem value="false">لا</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-right block">الفترة (للامتحانات الدورية)</label>
                <Select name="period" defaultValue={selectedExam.period || undefined}>
                  <SelectTrigger className="w-full text-right">
                    <SelectValue placeholder="اختر الفترة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="يومي">يومي</SelectItem>
                    <SelectItem value="أسبوعي">أسبوعي</SelectItem>
                    <SelectItem value="شهري">شهري</SelectItem>
                    <SelectItem value="فصلي">فصلي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-right block">معايير التقييم (اختيارية)</label>
                <div className="border border-[#e9f7f5] rounded-md p-3 max-h-60 overflow-y-auto bg-white">
                  {isEvaluationCriteriaLoading ? (
                    <div className="text-center py-2">
                      <Loader2 className="h-5 w-5 animate-spin mx-auto" />
                      <p className="text-sm text-gray-500 mt-1">جاري تحميل معايير التقييم...</p>
                    </div>
                  ) : evaluationCriteria.length > 0 ? (
                    <div className="space-y-2">
                      <div className="flex justify-between items-center text-sm p-1 mb-2 border-b pb-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setEditSelectedCriteria(evaluationCriteria.map(c => c.id))}
                        >
                          تحديد الكل
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setEditSelectedCriteria([])}
                        >
                          إلغاء تحديد الكل
                        </Button>
                      </div>
                      {evaluationCriteria.map(criteria => (
                        <div key={criteria.id} className="flex justify-between items-center text-sm p-1 hover:bg-gray-50 rounded">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id={`edit-criteria-${criteria.id}`}
                              checked={editSelectedCriteria.includes(criteria.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setEditSelectedCriteria(prev => [...prev, criteria.id]);
                                } else {
                                  setEditSelectedCriteria(prev => prev.filter(id => id !== criteria.id));
                                }
                              }}
                            />
                            <span className="text-gray-500">{(Number(criteria.weight) * 100).toFixed(0)}%</span>
                          </div>
                          <label
                            htmlFor={`edit-criteria-${criteria.id}`}
                            className="font-medium cursor-pointer flex-1 text-right"
                          >
                            {criteria.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-sm text-gray-500 py-2">لا توجد معايير تقييم متاحة</p>
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1 text-right">اختر معايير التقييم التي سيتم استخدامها عند تقييم الطلاب في هذا الامتحان</p>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditExamOpen(false)}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  حفظ التغييرات
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  );
}

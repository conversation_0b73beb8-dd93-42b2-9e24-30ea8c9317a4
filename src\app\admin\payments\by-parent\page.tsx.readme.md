# صفحة المدفوعات حسب الولي

## 📋 الوصف
صفحة إدارية شاملة لعرض وإدارة المدفوعات مجمعة حسب الولي، مع إحصائيات مفصلة وأدوات بحث وفلترة متقدمة.

## 🎯 الهدف
- عرض ملخص شامل لمدفوعات كل ولي
- تتبع المبالغ المطلوبة والمدفوعة والمتبقية
- توفير إحصائيات مالية عامة
- تسهيل متابعة حالة الديون لكل ولي

## 🔗 المسار
`/admin/payments/by-parent`

## ✨ الميزات الرئيسية

### 1. بطاقات الإحصائيات
- **إجمالي الأولياء**: العدد الكلي للأولياء في النظام
- **لديهم ديون**: عدد الأولياء الذين لديهم مبالغ مستحقة
- **إجمالي الديون**: مجموع جميع المبالغ المتبقية
- **إجمالي المدفوعات**: مجموع جميع المبالغ المدفوعة
- **معدل التحصيل**: النسبة المئوية للتحصيل العام

### 2. أدوات البحث والفلترة
- **البحث النصي**: بالاسم أو رقم الهاتف أو البريد الإلكتروني
- **فلترة حسب الحالة**:
  - مدفوع بالكامل
  - مدفوع جزئياً
  - غير مدفوع
  - متأخر عن الموعد

### 3. جدول المدفوعات التفصيلي
- معلومات الولي (الاسم، الهاتف، البريد الإلكتروني)
- عدد الأبناء
- إجمالي المبلغ المطلوب
- المبلغ المدفوع
- المبلغ المتبقي
- معدل السداد مع شريط تقدم بصري
- تاريخ آخر دفعة
- حالة الدفع مع ألوان مميزة

### 4. أدوات التصدير والطباعة
- تصدير البيانات إلى Excel
- تحديث البيانات في الوقت الفعلي
- عرض تفاصيل كل ولي

## 🎨 واجهة المستخدم

### الألوان والمؤشرات
```typescript
const statusColors = {
  PAID: 'bg-green-100 text-green-800',      // مدفوع - أخضر
  PARTIAL: 'bg-blue-100 text-blue-800',     // جزئي - أزرق
  UNPAID: 'bg-yellow-100 text-yellow-800',  // غير مدفوع - أصفر
  OVERDUE: 'bg-red-100 text-red-800'        // متأخر - أحمر
};
```

### شريط التقدم البصري
- عرض معدل السداد كشريط تقدم ملون
- نسبة مئوية واضحة بجانب الشريط
- ألوان متدرجة حسب النسبة

### تخطيط متجاوب
- تصميم متجاوب يعمل على جميع الأجهزة
- جداول قابلة للتمرير الأفقي على الشاشات الصغيرة
- بطاقات إحصائيات متكيفة مع حجم الشاشة

## 📊 هيكل البيانات

### ParentPaymentSummary
```typescript
interface ParentPaymentSummary {
  id: string;
  name: string;
  phone: string;
  email?: string;
  totalRequired: number;      // إجمالي المطلوب
  totalPaid: number;          // إجمالي المدفوع
  totalRemaining: number;     // إجمالي المتبقي
  totalStudents: number;      // عدد الأبناء
  lastPaymentDate?: string;   // آخر دفعة
  paymentRate: number;        // معدل السداد %
  students: StudentPaymentSummary[];
}
```

### Statistics
```typescript
interface Statistics {
  totalParents: number;         // إجمالي الأولياء
  parentsWithDebts: number;     // الأولياء الذين لديهم ديون
  totalDebtAmount: number;      // إجمالي الديون
  totalPaidAmount: number;      // إجمالي المدفوعات
  averagePaymentRate: number;   // متوسط معدل السداد
}
```

## 🔧 الوظائف الرئيسية

### fetchPaymentsByParent()
```typescript
const fetchPaymentsByParent = async () => {
  // جلب البيانات من API
  // تطبيق الفلاتر والبحث
  // تحديث الحالة والإحصائيات
};
```

### getParentStatus()
```typescript
const getParentStatus = (parent: ParentPaymentSummary) => {
  if (parent.totalRemaining <= 0) return 'PAID';
  if (parent.totalPaid > 0) return 'PARTIAL';
  if (parent.students.some(s => s.paymentStatus === 'OVERDUE')) return 'OVERDUE';
  return 'UNPAID';
};
```

### formatCurrency()
```typescript
const formatCurrency = (amount: number) => {
  return `${amount.toLocaleString('fr-FR', { 
    minimumFractionDigits: 2 
  })} دج`;
};
```

## 📈 الأداء والتحسين

### تحسينات الأداء
- تحديث البيانات عند الحاجة فقط
- استخدام useEffect للتحكم في إعادة التحميل
- فلترة البيانات في الواجهة الأمامية للاستجابة السريعة

### إدارة الحالة
- استخدام useState لإدارة البيانات المحلية
- فصل البيانات الأصلية عن المفلترة
- تحديث الإحصائيات تلقائياً

### تجربة المستخدم
- مؤشرات تحميل واضحة
- رسائل خطأ مفيدة
- تأكيدات العمليات الناجحة

## 🔒 الأمان والصلاحيات

### الحماية المطلوبة
```typescript
<OptimizedProtectedRoute requiredPermission="admin.payments.view">
  {/* محتوى الصفحة */}
</OptimizedProtectedRoute>
```

### التحقق من الصلاحيات
- عرض البيانات: `admin.payments.view`
- تصدير البيانات: `admin.payments.export`
- عرض التفاصيل: `admin.payments.details`

## 📱 الاستجابة والتوافق

### نقاط الكسر
- **Mobile**: أقل من 640px
- **Tablet**: 640px - 1024px
- **Desktop**: أكبر من 1024px

### تكيف العناصر
- بطاقات الإحصائيات: 1 عمود على الموبايل، 5 أعمدة على الديسكتوب
- شريط البحث: عمودي على الموبايل، أفقي على الديسكتوب
- الجدول: تمرير أفقي على الشاشات الصغيرة

## 🧪 الاختبار

### اختبار الوظائف
```bash
# اختبار تحميل الصفحة
npm test -- --testNamePattern="PaymentsByParent"

# اختبار البحث والفلترة
npm test -- --testNamePattern="search and filter"

# اختبار التصدير
npm test -- --testNamePattern="export functionality"
```

### اختبار الأداء
- قياس وقت تحميل البيانات
- اختبار الاستجابة مع بيانات كبيرة
- تحليل استهلاك الذاكرة

## 📝 ملاحظات التطوير

### التبعيات المطلوبة
- `@/components/ui/*`: مكونات واجهة المستخدم
- `@/hooks/use-toast`: نظام الإشعارات
- `@/utils/export-utils`: أدوات التصدير
- `react-icons/fa`: الأيقونات

### التحسينات المستقبلية
- إضافة رسوم بيانية للإحصائيات
- تصدير تقارير PDF مخصصة
- إشعارات تلقائية للديون المتأخرة
- تكامل مع أنظمة الدفع الإلكتروني

### الصيانة والتطوير
- مراجعة دورية للأداء
- تحديث التصميم حسب ملاحظات المستخدمين
- إضافة المزيد من خيارات الفلترة والتصدير

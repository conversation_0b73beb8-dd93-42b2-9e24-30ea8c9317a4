'use client';

import { useEffect, useRef } from 'react';
import {
  cleanupExpiredCache,
  clearAllCache,
  clearUserCache,
  getCacheStatus
} from '@/lib/permissionsCache';

interface PermissionsCacheManagerProps {
  userId?: number | null;
  onCacheCleared?: () => void;
}

/**
 * مكون إدارة التخزين المؤقت للصلاحيات
 * يدير تنظيف البيانات المنتهية الصلاحية ومراقبة تغييرات المستخدم
 */
export const PermissionsCacheManager: React.FC<PermissionsCacheManagerProps> = ({
  userId,
  onCacheCleared
}) => {
  const lastUserIdRef = useRef<number | null>(null);
  const cleanupIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // تنظيف دوري للبيانات المنتهية الصلاحية
  useEffect(() => {
    // تنظيف فوري عند التحميل
    cleanupExpiredCache();

    // إعداد تنظيف دوري كل 10 دقائق
    cleanupIntervalRef.current = setInterval(() => {
      console.log('تنظيف دوري للتخزين المؤقت المنتهي الصلاحية');
      cleanupExpiredCache();
    }, 10 * 60 * 1000); // 10 دقائق

    return () => {
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current);
        cleanupIntervalRef.current = null;
      }
    };
  }, []);

  // مراقبة تغييرات المستخدم
  useEffect(() => {
    // إذا تغير المستخدم، مسح البيانات المناسبة
    if (lastUserIdRef.current !== null && lastUserIdRef.current !== userId) {
      console.log('تغيير المستخدم من', lastUserIdRef.current, 'إلى', userId);

      if (userId === null) {
        // المستخدم سجل خروج
        console.log('تسجيل خروج، مسح جميع بيانات التخزين المؤقت');
        clearAllCache();
      } else if (lastUserIdRef.current !== null) {
        // تغيير من مستخدم لآخر
        console.log('تغيير المستخدم، مسح بيانات المستخدم السابق');
        clearUserCache(lastUserIdRef.current);
      }

      onCacheCleared?.();
    }

    lastUserIdRef.current = userId;
  }, [userId, onCacheCleared]);

  // مراقبة أحداث المتصفح
  useEffect(() => {
    // مسح التخزين المؤقت عند إغلاق النافذة/التبويب
    const handleBeforeUnload = () => {
      // لا نمسح التخزين المؤقت هنا لأننا نريد الاحتفاظ به بين الجلسات
      // فقط نتأكد من تنظيف المؤقتات
      if (cleanupIntervalRef.current) {
        clearInterval(cleanupIntervalRef.current);
      }
    };

    // مراقبة تغييرات الرؤية (عندما يخفي المستخدم التبويب)
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // عندما يعود المستخدم للتبويب، تنظيف البيانات المنتهية الصلاحية
        console.log('عودة للتبويب، تنظيف التخزين المؤقت');
        cleanupExpiredCache();
      }
    };

    // مراقبة أحداث التخزين المحلي (للتزامن بين التبويبات)
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key && event.key.startsWith('permissions_cache_')) {
        console.log('تغيير في التخزين المؤقت من تبويب آخر');
        // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
      }
    };

    // مراقبة أحداث تنظيف التخزين المؤقت المخصصة
    const handleCacheCleared = (event: CustomEvent) => {
      console.log('تم تنظيف التخزين المؤقت:', event.detail);
      onCacheCleared?.();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('permissionsCacheCleared', handleCacheCleared as EventListener);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('permissionsCacheCleared', handleCacheCleared as EventListener);
    };
  }, []);

  // عرض معلومات التخزين المؤقت في وضع التطوير
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const logCacheStatus = () => {
        const status = getCacheStatus();
        console.log('حالة التخزين المؤقت للصلاحيات:', {
          متاح: status.isAvailable,
          عدد_العناصر: status.totalItems,
          آخر_تنظيف: status.lastCleanup ? new Date(status.lastCleanup).toLocaleString('ar') : 'غير محدد',
          الإصدار: status.version
        });
      };

      // عرض الحالة كل 5 دقائق في وضع التطوير
      const statusInterval = setInterval(logCacheStatus, 5 * 60 * 1000);
      
      // عرض الحالة فوراً
      logCacheStatus();

      return () => clearInterval(statusInterval);
    }
  }, []);

  // هذا المكون لا يعرض أي شيء مرئي
  return null;
};

/**
 * Hook لاستخدام مدير التخزين المؤقت
 */
export const usePermissionsCacheManager = (userId?: number | null) => {
  const clearCache = () => {
    console.log('مسح جميع بيانات التخزين المؤقت يدوياً');
    clearAllCache();
  };

  const getStatus = () => {
    return getCacheStatus();
  };

  const cleanup = () => {
    console.log('تنظيف البيانات المنتهية الصلاحية يدوياً');
    cleanupExpiredCache();
  };

  return {
    clearCache,
    getStatus,
    cleanup
  };
};

export default PermissionsCacheManager;

# 🎨 تصميم واجهة المدفوعات حسب الولي

## مخطط حالة الاستخدام (Use Case Diagram)

```mermaid
graph TB
    Admin[المسؤول/الموظف]
    Parent[ولي الأمر]
    
    Admin --> UC1[عرض مدفوعات جميع الأولياء]
    Admin --> UC2[البحث عن ولي معين]
    Admin --> UC3[تصدير تقرير المدفوعات]
    Admin --> UC4[عرض تفاصيل مدفوعات ولي]
    Admin --> UC5[تسجيل دفعة جديدة]
    
    Parent --> UC6[عرض مدفوعاته الشخصية]
    Parent --> UC7[عرض ديون أبنائه]
    
    UC1 --> UC1_1[عرض إجمالي المبالغ]
    UC1 --> UC1_2[عرض المبالغ المدفوعة]
    UC1 --> UC1_3[عرض المبالغ المتبقية]
    
    UC4 --> UC4_1[تفاصيل كل تلميذ]
    UC4 --> UC4_2[تاريخ آخر دفعة]
    UC4 --> UC4_3[حالة الديون]
```

## مخطط الكيانات (Entity Relationship Diagram)

```mermaid
erDiagram
    PARENT {
        int id PK
        string name
        string phone
        string email
        string address
        datetime createdAt
        datetime updatedAt
    }
    
    STUDENT {
        int id PK
        string name
        int guardianId FK
        int classeId FK
        datetime createdAt
    }
    
    PAYMENT {
        int id PK
        int studentId FK
        float amount
        datetime date
        string status
        string notes
        string receiptNumber
        int paymentMethodId FK
    }
    
    INVOICE {
        int id PK
        int studentId FK
        float amount
        datetime dueDate
        datetime issueDate
        int month
        int year
        string status
        string description
    }
    
    PAYMENT_SUMMARY {
        int parentId PK
        string parentName
        float totalRequired
        float totalPaid
        float totalRemaining
        int totalStudents
        datetime lastPaymentDate
    }
    
    PARENT ||--o{ STUDENT : "has children"
    STUDENT ||--o{ PAYMENT : "makes payments"
    STUDENT ||--o{ INVOICE : "receives invoices"
    PAYMENT }o--|| INVOICE : "pays for"
    PARENT ||--|| PAYMENT_SUMMARY : "summarized in"
```

## مخطط تسلسل العمليات (Sequence Diagram)

```mermaid
sequenceDiagram
    participant U as المستخدم
    participant P as صفحة المدفوعات
    participant API as API المدفوعات
    participant DB as قاعدة البيانات
    
    U->>P: طلب عرض المدفوعات حسب الولي
    P->>API: GET /api/payments/by-parent
    API->>DB: جلب بيانات الأولياء والطلاب
    DB-->>API: بيانات الأولياء
    API->>DB: جلب المدفوعات لكل طالب
    DB-->>API: بيانات المدفوعات
    API->>DB: جلب الفواتير المستحقة
    DB-->>API: بيانات الفواتير
    API-->>P: ملخص المدفوعات مجمع حسب الولي
    P-->>U: عرض البيانات في جدول منظم
    
    U->>P: البحث عن ولي معين
    P->>API: GET /api/payments/by-parent?search=name
    API->>DB: البحث في بيانات الأولياء
    DB-->>API: نتائج البحث
    API-->>P: بيانات الولي المطلوب
    P-->>U: عرض النتائج المفلترة
    
    U->>P: طلب تفاصيل ولي معين
    P->>API: GET /api/payments/by-parent/[id]
    API->>DB: جلب تفاصيل الولي وأبنائه
    DB-->>API: بيانات تفصيلية
    API-->>P: تفاصيل شاملة
    P-->>U: عرض التفاصيل في نافذة منبثقة
```

## هيكل البيانات المطلوب

### واجهة ParentPaymentSummary
```typescript
interface ParentPaymentSummary {
  id: string;
  name: string;
  phone: string;
  email?: string;
  totalRequired: number;      // إجمالي المبلغ المطلوب
  totalPaid: number;          // إجمالي المبلغ المدفوع
  totalRemaining: number;     // إجمالي المبلغ المتبقي
  totalStudents: number;      // عدد الأبناء
  lastPaymentDate?: string;   // تاريخ آخر دفعة
  paymentRate: number;        // معدل السداد (%)
  students: StudentPaymentSummary[];
}
```

### واجهة StudentPaymentSummary
```typescript
interface StudentPaymentSummary {
  id: number;
  name: string;
  grade: string;
  totalRequired: number;      // المبلغ المطلوب للطالب
  totalPaid: number;          // المبلغ المدفوع للطالب
  totalRemaining: number;     // المبلغ المتبقي للطالب
  dueInvoices: number;        // عدد الفواتير المستحقة
  lastPaymentDate?: string;   // تاريخ آخر دفعة للطالب
  paymentStatus: 'PAID' | 'PARTIAL' | 'UNPAID' | 'OVERDUE';
}
```

## تصميم الواجهة

### العناصر الرئيسية
1. **رأس الصفحة**: عنوان + أزرار الإجراءات السريعة
2. **شريط البحث والفلترة**: بحث بالاسم، فلترة حسب الحالة
3. **بطاقات الإحصائيات**: ملخص سريع للأرقام الإجمالية
4. **جدول المدفوعات**: عرض تفصيلي لكل ولي
5. **نافذة التفاصيل**: عرض تفاصيل ولي معين

### الألوان والمؤشرات
- **أخضر**: مدفوع بالكامل
- **أزرق**: مدفوع جزئياً  
- **أصفر**: غير مدفوع
- **أحمر**: متأخر عن الموعد

### خيارات التصدير
- تصدير Excel لجميع البيانات
- تصدير PDF لولي معين
- طباعة تقرير مفصل

'use client';

import { useState } from 'react';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import PageBackgroundsManager from '@/components/admin/PageBackgroundsManager';
enum SchoolType {
  REGULAR = 'REGULAR',
  LANGUAGE = 'LANGUAGE',
  QURAN = 'QURAN'
}
enum EvaluationType {
  WRITTEN_EXAM = 'WRITTEN_EXAM',
  ORAL_EXAM = 'ORAL_EXAM',
  HOMEWORK = 'HOMEWORK',
  PROJECT = 'PROJECT',
  QURAN_RECITATION = 'QURAN_RECITATION',
  QURAN_MEMORIZATION = 'QURAN_MEMORIZATION',
  PRACTICAL_TEST = 'PRACTICAL_TEST'
}

export default function SettingsPage() {
  const [schoolType, setSchoolType] = useState<SchoolType>(SchoolType.REGULAR);
  const [evaluationConfigs, setEvaluationConfigs] = useState<Array<{
    evaluationType: EvaluationType;
    weight: number;
    isRequired: boolean;
  }>>([]);

  const handleSchoolTypeChange = async (type: SchoolType) => {
    setSchoolType(type);
    // Fetch existing evaluation configs for this school type
    const response = await fetch(`/api/settings/evaluation-config?schoolType=${type}`);
    const data = await response.json();
    setEvaluationConfigs(data);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await fetch('/api/settings/evaluation-config', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        schoolType,
        evaluationConfigs
      })
    });
  };

  return (
    <ProtectedRoute requiredPermission="admin.settings.view">
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">إعدادات المدرسة</h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">نوع المدرسة</label>
          <select
            value={schoolType}
            onChange={(e) => handleSchoolTypeChange(e.target.value as SchoolType)}
            className="w-full p-2 border rounded-md"
          >
            <option value={SchoolType.REGULAR}>المدارس العادية</option>
            <option value={SchoolType.LANGUAGE}>مدارس اللغات</option>
            <option value={SchoolType.QURAN}>مدارس القرآن</option>
          </select>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">طرق التقييم</h2>
          {Object.values(EvaluationType).map((type) => (
            <div key={type} className="flex items-center space-x-4 rtl:space-x-reverse">
              <input
                type="checkbox"
                checked={evaluationConfigs.some(config => config.evaluationType === type)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setEvaluationConfigs([...evaluationConfigs, {
                      evaluationType: type,
                      weight: 1,
                      isRequired: true
                    }]);
                  } else {
                    setEvaluationConfigs(evaluationConfigs.filter(
                      config => config.evaluationType !== type
                    ));
                  }
                }}
                className="h-4 w-4"
              />
              <span className="flex-1">{getEvaluationTypeLabel(type)}</span>
              {evaluationConfigs.some(config => config.evaluationType === type) && (
                <input
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  value={evaluationConfigs.find(config => config.evaluationType === type)?.weight || 0}
                  onChange={(e) => {
                    setEvaluationConfigs(evaluationConfigs.map(config =>
                      config.evaluationType === type
                        ? { ...config, weight: parseFloat(e.target.value) }
                        : config
                    ));
                  }}
                  className="w-24 p-1 border rounded"
                />
              )}
            </div>
          ))}
        </div>

        <button
          type="submit"
          className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
        >
          حفظ الإعدادات
        </button>
      </form>

      {/* قسم إدارة خلفيات الصفحات العامة */}
      <div className="mt-8">
        <PageBackgroundsManager />
      </div>
      </div>
    </ProtectedRoute>
  );
}

function getEvaluationTypeLabel(type: EvaluationType): string {
  const labels: Record<EvaluationType, string> = {
    WRITTEN_EXAM: 'امتحان تحريري',
    ORAL_EXAM: 'امتحان شفوي',
    HOMEWORK: 'واجب منزلي',
    PROJECT: 'مشروع',
    QURAN_RECITATION: 'تلاوة القرآن',
    QURAN_MEMORIZATION: 'حفظ القرآن',
    PRACTICAL_TEST: 'اختبار عملي'
  };
  return labels[type];
}
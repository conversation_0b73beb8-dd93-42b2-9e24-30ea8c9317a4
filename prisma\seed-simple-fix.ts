import { PrismaClient, UserRole, EvaluationType, ExamStatus } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 بدء إصلاح مشكلة كشف الدرجات...');
  
  try {
    // 1. إنشاء مدير إذا لم يوجد
    let admin = await prisma.user.findUnique({
      where: { username: 'admin' }
    });

    if (!admin) {
      console.log('👤 إنشاء مدير النظام...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      admin = await prisma.user.create({
        data: {
          username: 'admin',
          password: hashedPassword,
          role: UserRole.ADMIN,
          profile: {
            create: {
              name: 'مدير النظام',
              phone: '0123456789'
            }
          }
        }
      });
    }

    // 2. إنشاء معلم إذا لم يوجد
    let teacherUser = await prisma.user.findUnique({
      where: { username: 'teacher1' },
      include: { teacher: true }
    });

    if (!teacherUser) {
      console.log('👨‍🏫 إنشاء معلم...');
      teacherUser = await prisma.user.create({
        data: {
          username: 'teacher1',
          password: await bcrypt.hash('teacher123', 10),
          role: UserRole.TEACHER,
          profile: {
            create: {
              name: 'الأستاذ محمد',
              phone: '0111111111'
            }
          },
          teacher: {
            create: {
              name: 'الأستاذ محمد',
              phone: '0111111111',
              specialization: 'حفظ القرآن الكريم'
            }
          }
        },
        include: { teacher: true }
      });
    }

    // 3. إنشاء مادة القرآن الكريم
    let subject = await prisma.subject.findFirst({
      where: { name: 'حفظ القرآن الكريم' }
    });

    if (!subject) {
      console.log('📚 إنشاء مادة حفظ القرآن...');
      subject = await prisma.subject.create({
        data: {
          name: 'حفظ القرآن الكريم',
          description: 'مادة حفظ وتسميع القرآن الكريم'
        }
      });
    }

    // 4. ربط المعلم بالمادة
    let teacherSubject = await prisma.teacherSubject.findFirst({
      where: {
        teacherId: teacherUser.teacher!.id,
        subjectId: subject.id
      }
    });

    if (!teacherSubject) {
      console.log('🔗 ربط المعلم بالمادة...');
      teacherSubject = await prisma.teacherSubject.create({
        data: {
          teacherId: teacherUser.teacher!.id,
          subjectId: subject.id
        }
      });
    }

    // 5. إنشاء فصل دراسي
    let classe = await prisma.classe.findFirst({
      where: { name: 'الفصل الأول' }
    });

    if (!classe) {
      console.log('🏫 إنشاء فصل دراسي...');
      classe = await prisma.classe.create({
        data: {
          name: 'الفصل الأول',
          description: 'فصل تجريبي',
          capacity: 25
        }
      });
    }

    // 6. ربط الفصل بالمادة
    let classSubject = await prisma.classSubject.findFirst({
      where: {
        classeId: classe.id,
        teacherSubjectId: teacherSubject.id
      }
    });

    if (!classSubject) {
      console.log('🔗 ربط الفصل بالمادة...');
      classSubject = await prisma.classSubject.create({
        data: {
          classeId: classe.id,
          teacherSubjectId: teacherSubject.id
        }
      });
    }

    // 7. إنشاء ولي أمر
    let parent = await prisma.parent.findFirst({
      where: { phone: '0123456788' }
    });

    if (!parent) {
      console.log('👨‍👩‍👧‍👦 إنشاء ولي أمر...');
      parent = await prisma.parent.create({
        data: {
          name: 'أحمد محمود',
          phone: '0123456788'
        }
      });
    }

    // 8. إنشاء طلاب
    const studentNames = [
      'عبد الرحمن أحمد',
      'فاطمة محمد',
      'عبد الله علي',
      'عائشة حسن',
      'يوسف إبراهيم'
    ];

    const students = [];
    for (let i = 0; i < studentNames.length; i++) {
      let student = await prisma.student.findFirst({
        where: { username: `student${i + 1}` }
      });

      if (!student) {
        console.log(`👦👧 إنشاء طالب: ${studentNames[i]}...`);
        student = await prisma.student.create({
          data: {
            username: `student${i + 1}`,
            name: studentNames[i],
            age: 10 + Math.floor(Math.random() * 5),
            phone: `012345678${i + 1}`,
            classeId: classe.id,
            guardianId: parent.id
          }
        });
      }
      students.push(student);
    }

    // 9. إنشاء نوع امتحان
    let examType = await prisma.examType.findFirst({
      where: { name: 'امتحان شهري' }
    });

    if (!examType) {
      console.log('📝 إنشاء نوع امتحان...');
      examType = await prisma.examType.create({
        data: {
          name: 'امتحان شهري',
          description: 'امتحان شهري للطلاب',
          evaluationType: 'شفهي'
        }
      });
    }

    // 10. إنشاء امتحانات
    const currentDate = new Date();
    const months = ['2024-09', '2024-10', '2024-11', '2024-12'];
    const exams = [];

    for (const month of months) {
      let exam = await prisma.exam.findFirst({
        where: {
          month: month,
          evaluationType: EvaluationType.QURAN_MEMORIZATION
        }
      });

      if (!exam) {
        console.log(`📝 إنشاء امتحان: ${month}...`);
        exam = await prisma.exam.create({
          data: {
            evaluationType: EvaluationType.QURAN_MEMORIZATION,
            month: month,
            description: `امتحان حفظ القرآن - ${month}`,
            maxPoints: 20,
            passingPoints: 10,
            subjectId: subject.id,
            typeId: examType.id,
            requiresSurah: true
          }
        });
      }
      exams.push(exam);
    }

    // 11. إنشاء نقاط امتحانات
    console.log('📊 إنشاء نقاط امتحانات...');
    let pointsCreated = 0;

    for (const student of students) {
      for (const exam of exams) {
        // التحقق من عدم وجود نقطة مسبقة
        const existingPoint = await prisma.exam_points.findFirst({
          where: {
            studentId: student.id,
            examId: exam.id
          }
        });

        if (!existingPoint) {
          // درجة عشوائية بين 8-20
          const grade = 8 + Math.floor(Math.random() * 13);
          
          await prisma.exam_points.create({
            data: {
              examId: exam.id,
              studentId: student.id,
              classSubjectId: classSubject.id,
              grade: grade,
              status: grade >= 10 ? ExamStatus.PASSED : ExamStatus.FAILED,
              note: grade >= 16 ? 'أداء ممتاز' : grade >= 12 ? 'أداء جيد' : grade >= 10 ? 'أداء مقبول' : 'يحتاج تحسين',
              feedback: `الطالب حصل على ${grade} من ${exam.maxPoints}`
            }
          });
          pointsCreated++;
        }
      }
    }

    console.log('✅ تم إصلاح مشكلة كشف الدرجات بنجاح!');
    console.log(`📊 تم إنشاء ${pointsCreated} نقطة امتحان`);
    console.log(`👦👧 ${students.length} طلاب`);
    console.log(`📝 ${exams.length} امتحانات`);
    console.log('');
    console.log('🔗 يمكنك الآن الذهاب إلى كشف الدرجات لرؤية البيانات');
    console.log('🔗 تسجيل الدخول: admin / admin123');

  } catch (error) {
    console.error('❌ خطأ في إصلاح البيانات:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

// GET: جلب إشعار محدد
export async function GET(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const id = parseInt(params.id);
        if (isNaN(id)) {
            return NextResponse.json(
                { message: "معرف الإشعار غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // جلب الإشعار
        const notification = await prisma.notification.findUnique({
            where: { id }
        });

        if (!notification) {
            return NextResponse.json(
                { message: "الإشعار غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من أن الإشعار ينتمي للمستخدم الحالي
        if (notification.userId !== userData.id && userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE') {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 403 }
            );
        }

        return NextResponse.json(notification);
    } catch (error) {
        console.error('Error fetching notification:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب الإشعار" },
            { status: 500 }
        );
    }
}

// PATCH: تحديث حالة الإشعار (مقروء/غير مقروء)
export async function PATCH(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const id = parseInt(params.id);
        if (isNaN(id)) {
            return NextResponse.json(
                { message: "معرف الإشعار غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // جلب الإشعار للتحقق من الملكية
        const notification = await prisma.notification.findUnique({
            where: { id },
            include: {
                recipients: {
                    where: { userId: userData.id }
                }
            }
        });

        if (!notification) {
            return NextResponse.json(
                { message: "الإشعار غير موجود" },
                { status: 404 }
            );
        }

        const body = await request.json();
        let updatedNotification;

        // التحقق من نوع الإشعار وتحديث الحالة المناسبة
        if (notification.isGroupNotification) {
            // للإشعارات الجماعية، نحديث سجل المستلم
            if (notification.recipients.length === 0) {
                return NextResponse.json(
                    { message: "غير مصرح به - الإشعار غير موجه لك" },
                    { status: 403 }
                );
            }

            await prisma.notificationRecipient.updateMany({
                where: {
                    notificationId: id,
                    userId: userData.id
                },
                data: {
                    read: body.read !== undefined ? body.read : true,
                    readAt: body.read !== false ? new Date() : null
                }
            });

            // إرجاع الإشعار مع الحالة المحدثة
            updatedNotification = await prisma.notification.findUnique({
                where: { id },
                include: {
                    recipients: {
                        where: { userId: userData.id },
                        select: { read: true, readAt: true }
                    }
                }
            });

            // تنسيق الاستجابة
            updatedNotification = {
                ...updatedNotification,
                read: updatedNotification.recipients[0]?.read || false,
                readAt: updatedNotification.recipients[0]?.readAt || null
            };
        } else {
            // للإشعارات الفردية، التحقق من الملكية
            if (notification.userId !== userData.id && userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE') {
                return NextResponse.json(
                    { message: "غير مصرح به" },
                    { status: 403 }
                );
            }

            // تحديث حالة الإشعار الفردي
            updatedNotification = await prisma.notification.update({
                where: { id },
                data: {
                    read: body.read !== undefined ? body.read : true,
                    readAt: body.read !== false ? new Date() : null
                }
            });
        }

        return NextResponse.json(updatedNotification);
    } catch (error) {
        console.error('Error updating notification:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء تحديث الإشعار" },
            { status: 500 }
        );
    }
}

// DELETE: حذف إشعار
export async function DELETE(
    request: NextRequest,
    { params }: { params: { id: string } }
) {
    try {
        const id = parseInt(params.id);
        if (isNaN(id)) {
            return NextResponse.json(
                { message: "معرف الإشعار غير صالح" },
                { status: 400 }
            );
        }

        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // جلب الإشعار للتحقق من الملكية
        const notification = await prisma.notification.findUnique({
            where: { id }
        });

        if (!notification) {
            return NextResponse.json(
                { message: "الإشعار غير موجود" },
                { status: 404 }
            );
        }

        // التحقق من أن الإشعار ينتمي للمستخدم الحالي أو المستخدم مسؤول أو موظف
        if (notification.userId !== userData.id && userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE') {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 403 }
            );
        }

        // حذف الإشعار
        await prisma.notification.delete({
            where: { id }
        });

        return NextResponse.json({ message: "تم حذف الإشعار بنجاح" });
    } catch (error) {
        console.error('Error deleting notification:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء حذف الإشعار" },
            { status: 500 }
        );
    }
}

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

interface TeacherSubject {
  id: number;
  teacherId: number;
  subjectId: number;
  createdAt: string;
}

interface Teacher {
  id: number
  name: string
  specialization: string
  phone: string | null
  userId: number;
  createdAt: string;
  teacherSubjects: TeacherSubject[]
}

interface EditTeacherModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  teacher: Teacher
}

export function EditTeacherModal({ isOpen, onClose, onSuccess, teacher }: EditTeacherModalProps) {
  console.log(teacher)
  const [formData, setFormData] = useState({
    name: '',
    specialization: '',
    phone: '',
    subjects: [] as number[]
  })
  const [subjects, setSubjects] = useState<Array<{ id: number, name: string }>>([]) // Corrected: Removed subjectId
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')


  useEffect(() => {
    if (isOpen && teacher) {
      const fetchData = async () => {
        try {
          setIsSubmitting(true)
          setError('')

          const subjectsResponse = await fetch('/api/admin/subjects');
          if (!subjectsResponse.ok) {
            throw new Error('Failed to fetch subjects');
          }
          const subjectsData = await subjectsResponse.json();
          setSubjects(subjectsData);

          setFormData({
            name: teacher.name,
            specialization: teacher.specialization,
            phone: teacher.phone || '',
            subjects: teacher.teacherSubjects.map(ts => ts.subjectId)
          });
        } catch (err: unknown) {
          const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء تحديث بيانات المعلم';
          setError(errorMessage)
          console.error(err);
        } finally {
          setIsSubmitting(false);
        }
      };
      fetchData();
    } else {
      setFormData({ name: '', specialization: '', phone: '', subjects: [] });
      setSubjects([]);
      setError('');
    }
  }, [isOpen, teacher])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!teacher) return
    if (formData.subjects.length === 0) {
      setError('يجب اختيار مادة واحدة على الأقل')
      setIsSubmitting(false)
      return
    }

    try {
      setIsSubmitting(true)
      setError('')

      // استخدام المسار الصحيح للتعديل
      const response = await fetch(`/api/admin/teachers/${teacher.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()
      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ أثناء تحديث بيانات المعلم')
      }
      onSuccess()
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء تحديث بيانات المعلم';
      setError(errorMessage)
      console.error(err)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] bg-white max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold text-[var(--primary-color)]">تعديل بيانات المعلم</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="text-red-600 text-sm text-center">{error}</div>
          )}
          <div className="space-y-2">
            <Label htmlFor="name">اسم المعلم</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="أدخل اسم المعلم"
              required
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-right block">المواد</label>
            <div className="grid grid-cols-2 gap-2 rtl">
              {subjects.map(subject => (
                <label key={subject.id} className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 p-2 rounded">
                  <input
                    type="checkbox"
                    className="rounded text-primary"
                    checked={formData.subjects.includes(subject.id)} // Corrected: Using subject.id
                    onChange={(e) => {
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          subjects: [...prev.subjects, subject.id] // Corrected: Using subject.id
                        }))
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          subjects: prev.subjects.filter(id => id !== subject.id) // Corrected: Using subject.id
                        }))
                      }
                    }}
                  />
                  <span className="text-sm">{subject.name}</span>
                </label>
              ))}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="specialization">التخصص</Label>
            <Input
              id="specialization"
              value={formData.specialization}
              onChange={(e) => setFormData({ ...formData, specialization: e.target.value })}
              placeholder="أدخل التخصص"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">رقم الهاتف</Label>
            <Input
              id="phone"
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="أدخل رقم الهاتف"
              dir="ltr"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              إلغاء
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ التغييرات'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

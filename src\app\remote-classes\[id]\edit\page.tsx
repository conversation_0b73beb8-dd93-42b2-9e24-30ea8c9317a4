'use client';
import React, { useState, useEffect, use } from 'react';
import { FaSave, FaTimes, FaVideo, FaCalendarAlt, FaClock, FaChalkboardTeacher, FaLink, FaIdCard, FaKey, FaSchool, FaUserGraduate, FaDesktop, FaChalkboard, FaMicrophone, FaCog, FaEdit } from 'react-icons/fa';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import axios from 'axios';

interface Teacher {
  id: number;
  username: string;
  profile?: {
    name: string;
  };
}

interface Classe {
  id: number;
  name: string;
}

interface Student {
  id: number;
  username: string;
  name: string;
  age?: number;
  phone?: string;
  profile?: {
    name: string;
  };
}

interface Attendee {
  id: number;
  username: string;
  profile?: {
    name: string;
  };
}

interface RemoteClassResponse {
  id: number;
  title: string;
  description: string;
  startTime: string;
  endTime: string;
  meetingLink: string;
  meetingId?: string;
  meetingPassword?: string;
  platform: string;
  recordingUrl?: string;
  isScreenShareEnabled?: boolean;
  isWhiteboardEnabled?: boolean;
  videoQuality?: string;
  audioQuality?: string;
  instructor: {
    id: number;
    username: string;
    profile?: {
      name: string;
    };
  };
  classe?: {
    id: number;
    name: string;
  };
  attendees: Attendee[];
}

const EditRemoteClassPage = ({ params }: { params: Promise<{ id: string }> | { id: string } }) => {
  // استخدام React.use() لفك الوعد قبل الوصول إلى خاصية id
  const unwrappedParams = 'then' in params ? use(params) : params;
  const id = unwrappedParams.id;
  const router = useRouter();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    meetingLink: '',
    meetingId: '',
    meetingPassword: '',
    platform: 'Zoom',
    instructorId: 0,
    classeId: 0,
    attendeeIds: [] as number[],
    recordingUrl: '',
    // الخيارات الجديدة
    isScreenShareEnabled: true,
    isWhiteboardEnabled: true,
    videoQuality: 'medium',
    audioQuality: 'medium'
  });

  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [classes, setClasses] = useState<Classe[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [userId, setUserId] = useState<number | null>(null);

  // جلب بيانات الفصل الافتراضي
  useEffect(() => {
    const fetchRemoteClass = async () => {
      try {
        setIsLoading(true);
        const response = await axios.get(`/api/remote-classes/${id}`);
        const remoteClass = response.data as RemoteClassResponse;

        // تنسيق التاريخ والوقت
        const formatDateForInput = (dateString: string) => {
          const date = new Date(dateString);
          return date.toISOString().slice(0, 16);
        };

        setFormData({
          title: remoteClass.title,
          description: remoteClass.description || '',
          startTime: formatDateForInput(remoteClass.startTime),
          endTime: formatDateForInput(remoteClass.endTime),
          meetingLink: remoteClass.meetingLink,
          meetingId: remoteClass.meetingId || '',
          meetingPassword: remoteClass.meetingPassword || '',
          platform: remoteClass.platform,
          instructorId: remoteClass.instructor.id,
          classeId: remoteClass.classe?.id || 0,
          attendeeIds: remoteClass.attendees.map((attendee) => attendee.id),
          recordingUrl: remoteClass.recordingUrl || '',
          isScreenShareEnabled: remoteClass.isScreenShareEnabled !== undefined ? remoteClass.isScreenShareEnabled : true,
          isWhiteboardEnabled: remoteClass.isWhiteboardEnabled !== undefined ? remoteClass.isWhiteboardEnabled : true,
          videoQuality: remoteClass.videoQuality || 'medium',
          audioQuality: remoteClass.audioQuality || 'medium'
        });

        // جلب معلومات المستخدم
        const userResponse = await axios.get('/api/users/me');
        setUserRole((userResponse.data as { role: string }).role);
        setUserId((userResponse.data as { id: number }).id);

        // جلب المعلمين
        const teachersResponse = await axios.get('/api/users?role=TEACHER');
        setTeachers((teachersResponse.data as { users: Teacher[] }).users);

        // جلب الفصول
        const classesResponse = await axios.get('/api/classes');
        if (classesResponse.data && classesResponse.data.classes) {
          setClasses(classesResponse.data.classes);
        }

        // جلب الطلاب إذا كان هناك فصل محدد
        if (remoteClass.classe?.id) {
          const studentsResponse = await axios.get(`/api/classes/${remoteClass.classe.id}/students`);
          setStudents(studentsResponse.data.students || []);
        }
      } catch (error) {
        console.error('Error fetching remote class:', error);
        toast.error('فشل في جلب بيانات الفصل الافتراضي');
        router.push('/remote-classes');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRemoteClass();
  }, [id, router]);

  // التعامل مع تغيير الحقول
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // تحويل القيم العددية من نص إلى رقم
    if (name === 'instructorId' && value) {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // التعامل مع تغيير الفصل
  const handleClasseChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const classeId = parseInt(e.target.value);
    setFormData(prev => ({ ...prev, classeId, attendeeIds: [] }));

    // جلب الطلاب للفصل المحدد
    if (classeId) {
      const fetchStudents = async () => {
        try {
          const response = await axios.get(`/api/classes/${classeId}/students`);
          setStudents(response.data.students || []);
        } catch (error) {
          console.error('Error fetching students:', error);
          toast.error('فشل في جلب الطلاب');
        }
      };

      fetchStudents();
    } else {
      setStudents([]);
    }
  };

  // التعامل مع تغيير الطلاب
  const handleStudentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions);
    const selectedIds = selectedOptions.map(option => parseInt(option.value));
    setFormData(prev => ({ ...prev, attendeeIds: selectedIds }));
  };

  // تحديث الفصل الافتراضي
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من البيانات
    if (!formData.title || !formData.startTime || !formData.endTime || !formData.meetingLink || !formData.platform) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    // التحقق من اختيار معلم إذا كان المستخدم مشرفًا
    if (userRole === 'ADMIN' && !formData.instructorId) {
      toast.error('يرجى اختيار معلم');
      return;
    }

    // التحقق من أن وقت البداية قبل وقت النهاية
    const startTime = new Date(formData.startTime);
    const endTime = new Date(formData.endTime);

    if (startTime >= endTime) {
      toast.error('يجب أن يكون وقت البداية قبل وقت النهاية');
      return;
    }

    setIsLoading(true);

    try {
      // تحويل القيم العددية قبل الإرسال
      const dataToSend = {
        ...formData,
        instructorId: parseInt(String(formData.instructorId)),
        classeId: formData.classeId ? parseInt(String(formData.classeId)) : null,
        sendNotification: true // إرسال إشعار بالتحديث
      };

      await axios.patch(`/api/remote-classes/${id}`, dataToSend);
      toast.success('تم تحديث الفصل الافتراضي بنجاح');
      router.push(`/remote-classes/${id}`);
    } catch (error: unknown) {
      console.error('Error updating remote class:', error);
      toast.error('فشل في تحديث الفصل الافتراضي');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-screen" dir="rtl">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[var(--primary-color)]"></div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaEdit className="text-[var(--primary-color)]" />
            تعديل الفصل الافتراضي
          </h1>
          <p className="text-gray-600 mr-4">
            قم بتعديل تفاصيل الفصل الافتراضي والمعلم والطلاب المشاركين
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md p-6 border-t-4 border-[var(--primary-color)]">
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* عنوان الفصل */}
              <div className="col-span-2">
                <label htmlFor="title" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaVideo className="text-[var(--primary-color)]" />
                  عنوان الفصل <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  required
                />
              </div>

              {/* وصف الفصل */}
              <div className="col-span-2">
                <label htmlFor="description" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaVideo className="text-[var(--primary-color)]" />
                  وصف الفصل
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                />
              </div>

              {/* وقت البداية */}
              <div>
                <label htmlFor="startTime" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaCalendarAlt className="text-[var(--primary-color)]" />
                  وقت البداية <span className="text-red-500">*</span>
                </label>
                <input
                  type="datetime-local"
                  id="startTime"
                  name="startTime"
                  value={formData.startTime}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  required
                />
              </div>

              {/* وقت النهاية */}
              <div>
                <label htmlFor="endTime" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaClock className="text-[var(--primary-color)]" />
                  وقت النهاية <span className="text-red-500">*</span>
                </label>
                <input
                  type="datetime-local"
                  id="endTime"
                  name="endTime"
                  value={formData.endTime}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  required
                />
              </div>

              {/* رابط الاجتماع */}
              <div>
                <label htmlFor="meetingLink" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaLink className="text-[var(--primary-color)]" />
                  رابط الاجتماع <span className="text-red-500">*</span>
                </label>
                <input
                  type="url"
                  id="meetingLink"
                  name="meetingLink"
                  value={formData.meetingLink}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  required
                />
              </div>

              {/* المنصة */}
              <div>
                <label htmlFor="platform" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaVideo className="text-[var(--primary-color)]" />
                  المنصة <span className="text-red-500">*</span>
                </label>
                <select
                  id="platform"
                  name="platform"
                  value={formData.platform}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  required
                >
                  <option value="Zoom">Zoom</option>
                  <option value="Google Meet">Google Meet</option>
                  <option value="Microsoft Teams">Microsoft Teams</option>
                  <option value="Webex">Webex</option>
                  <option value="Other">أخرى</option>
                </select>
              </div>

              {/* معرف الاجتماع */}
              <div>
                <label htmlFor="meetingId" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaIdCard className="text-[var(--primary-color)]" />
                  معرف الاجتماع
                </label>
                <input
                  type="text"
                  id="meetingId"
                  name="meetingId"
                  value={formData.meetingId}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                />
              </div>

              {/* كلمة مرور الاجتماع */}
              <div>
                <label htmlFor="meetingPassword" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaKey className="text-[var(--primary-color)]" />
                  كلمة مرور الاجتماع
                </label>
                <input
                  type="text"
                  id="meetingPassword"
                  name="meetingPassword"
                  value={formData.meetingPassword}
                  onChange={handleChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                />
              </div>

              {/* المعلم */}
              {userRole === 'ADMIN' && (
                <div>
                  <label htmlFor="instructorId" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                    <FaChalkboardTeacher className="text-[var(--primary-color)]" />
                    المعلم <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="instructorId"
                    name="instructorId"
                    value={formData.instructorId || ''}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                    required
                  >
                    <option value="">اختر المعلم</option>
                    {teachers && teachers.length > 0 && teachers.map(teacher => (
                      <option key={teacher.id} value={teacher.id}>
                        {teacher.profile?.name || teacher.username}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* الفصل */}
              <div className={userRole === 'ADMIN' ? '' : 'col-span-2'}>
                <label htmlFor="classeId" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                  <FaSchool className="text-[var(--primary-color)]" />
                  الفصل
                </label>
                <select
                  id="classeId"
                  name="classeId"
                  value={formData.classeId || ''}
                  onChange={handleClasseChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                >
                  <option value="">اختر الفصل</option>
                  {classes && classes.length > 0 && classes.map(classe => (
                    <option key={classe.id} value={classe.id}>
                      {classe.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الطلاب */}
              {formData.classeId > 0 && (
                <div className="col-span-2">
                  <label htmlFor="attendeeIds" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                    <FaUserGraduate className="text-[var(--primary-color)]" />
                    الطلاب المشاركون
                  </label>
                  <select
                    id="attendeeIds"
                    name="attendeeIds"
                    multiple
                    value={formData.attendeeIds.map(id => id.toString())}
                    onChange={handleStudentChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] h-32"
                  >
                    {students && students.map(student => (
                      <option key={student.id} value={student.id}>
                        {student.name || student.username}
                      </option>
                    ))}
                  </select>
                  <p className="text-sm text-gray-500 mt-1">
                    اضغط Ctrl (أو Command على Mac) للاختيار المتعدد
                  </p>
                </div>
              )}
            </div>

            {/* رابط التسجيل */}
            <div>
              <label htmlFor="recordingUrl" className="text-sm font-medium text-[var(--primary-color)] mb-1 flex items-center gap-1">
                <FaVideo className="text-[var(--primary-color)]" />
                رابط التسجيل (اختياري)
              </label>
              <input
                type="url"
                id="recordingUrl"
                name="recordingUrl"
                value={formData.recordingUrl}
                onChange={handleChange}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
              />
            </div>

            {/* خيارات متقدمة */}
            <div className="col-span-2 mt-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4 border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
                <FaCog className="text-[var(--primary-color)]" />
                خيارات متقدمة
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* مشاركة الشاشة */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <input
                    type="checkbox"
                    id="isScreenShareEnabled"
                    name="isScreenShareEnabled"
                    checked={formData.isScreenShareEnabled}
                    onChange={(e) => setFormData(prev => ({ ...prev, isScreenShareEnabled: e.target.checked }))}
                    className="h-5 w-5 text-[var(--primary-color)] rounded focus:ring-[var(--primary-color)]"
                  />
                  <label htmlFor="isScreenShareEnabled" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                    <FaDesktop className="text-[var(--primary-color)]" />
                    تمكين مشاركة الشاشة
                  </label>
                </div>

                {/* السبورة التفاعلية */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <input
                    type="checkbox"
                    id="isWhiteboardEnabled"
                    name="isWhiteboardEnabled"
                    checked={formData.isWhiteboardEnabled}
                    onChange={(e) => setFormData(prev => ({ ...prev, isWhiteboardEnabled: e.target.checked }))}
                    className="h-5 w-5 text-[var(--primary-color)] rounded focus:ring-[var(--primary-color)]"
                  />
                  <label htmlFor="isWhiteboardEnabled" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                    <FaChalkboard className="text-[var(--primary-color)]" />
                    تمكين السبورة التفاعلية
                  </label>
                </div>

                {/* جودة الفيديو */}
                <div>
                  <label htmlFor="videoQuality" className="text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                    <FaVideo className="text-[var(--primary-color)]" />
                    جودة الفيديو
                  </label>
                  <select
                    id="videoQuality"
                    name="videoQuality"
                    value={formData.videoQuality}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  >
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                  </select>
                </div>

                {/* جودة الصوت */}
                <div>
                  <label htmlFor="audioQuality" className="text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                    <FaMicrophone className="text-[var(--primary-color)]" />
                    جودة الصوت
                  </label>
                  <select
                    id="audioQuality"
                    name="audioQuality"
                    value={formData.audioQuality}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
                  >
                    <option value="low">منخفضة</option>
                    <option value="medium">متوسطة</option>
                    <option value="high">عالية</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="mt-8 flex justify-end space-x-4 space-x-reverse">
              <button
                type="button"
                onClick={() => router.push(`/remote-classes/${id}`)}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors flex items-center shadow-md hover:shadow-lg"
              >
                <FaTimes className="ml-2" />
                إلغاء
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-[var(--primary-color)] text-white rounded-md hover:bg-[var(--secondary-color)] transition-colors flex items-center shadow-md hover:shadow-lg"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <FaSave className="ml-2" />
                    حفظ التغييرات
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EditRemoteClassPage;

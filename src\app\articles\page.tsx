import React from 'react'
import Link from 'next/link'
type Article = {
  id:number;
  userId:number;
  title:string;
  body:string;
}

const articlesPage = async() => {
  const response = await fetch("https://jsonplaceholder.typicode.com/posts")
  const articles: Article[] = await response.json();
  if (!response.ok){
    throw new Error("خطاء في جلب المعلومات ")
  }
  return (
    <section className='container m-auto px-5'>
      <div className='flex items-center justify-center flex-wrap gap-4'>
            {articles.map(item => (
                    <div className='p-5 rounded-lg my-1 shadow-lg border-gray-400 hover:bg-slate-200 w-full md:w-2/5 lg:1/4' key={item.id}>
                    <h3 className='text-xl font-bold text-gray-900 line-clamp-1'>{item.title}</h3>
                    <p className='my-2 text-xl text-gray-700 p-1 line-clamp-3'>{item.body}</p>
                    <Link className='text-xl bg-purple-800 w-full block text-center p-1 text-white rounded-lg' href={'/articles/${item.id}'}> 
                    Read More
                    </Link>
                    </div>
                  ))}

      </div>
     
    </section>
  )
}

export default articlesPage
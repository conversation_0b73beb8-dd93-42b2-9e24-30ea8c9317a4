/*
==============================================================================
    وحدة مدقق SSL - Praetorian SSL Checker
    
    الوصف: مدقق شهادات SSL/TLS وفحص الشيفرات الضعيفة باستخدام openssllib.ring
    المؤلف: Praetorian Team
==============================================================================
*/

/*
==============================================================================
    كلاس مدقق SSL
==============================================================================
*/

# إنشاء مثيل عام من مدقق SSL
PraetorianSSLCheckerInstance = new PraetorianSSLChecker

class PraetorianSSLChecker

    # خصائص المدقق
    oLogger = NULL
    bVerbose = false
    nTimeout = 10
    
    # قائمة الشيفرات الضعيفة
    aWeakCiphers = [
        "DES-CBC-SHA",
        "DES-CBC3-SHA", 
        "RC4-MD5",
        "RC4-SHA",
        "NULL-MD5",
        "NULL-SHA",
        "EXP-DES-CBC-SHA",
        "EXP-RC4-MD5",
        "EXP-RC2-CBC-MD5"
    ]
    
    # بروتوكولات SSL/TLS
    aSSLVersions = [
        "SSLv2",
        "SSLv3", 
        "TLSv1.0",
        "TLSv1.1",
        "TLSv1.2",
        "TLSv1.3"
    ]
    
    /*
    دالة البناء
    */
    func init
        oLogger = PraetorianLoggerInstance
        oLogger.debug("تم تهيئة مدقق SSL")
    
    /*
    تعيين مهلة الاتصال
    المدخلات: nSeconds - المهلة بالثواني
    */
    func setTimeout nSeconds
        nTimeout = nSeconds
        oLogger.debug("تم تعيين مهلة الاتصال إلى " + nTimeout + " ثانية")
    
    /*
    تفعيل/إلغاء الوضع المفصل
    المدخلات: bEnable - true للتفعيل، false للإلغاء
    */
    func setVerbose bEnable
        bVerbose = bEnable
    
    /*
    فحص اتصال SSL أساسي
    المدخلات: cHost - عنوان الخادم، nPort - رقم المنفذ
    المخرجات: true إذا كان SSL متاحاً، false إذا لم يكن كذلك
    */
    func testSSLConnection cHost, nPort
        oLogger.debug("فحص اتصال SSL إلى " + cHost + ":" + nPort)
        
        try
            # إنشاء مقبس TCP
            sock = socket(AF_INET, SOCK_STREAM)
            if sock = NULL
                oLogger.error("فشل في إنشاء المقبس")
                return false
            ok
            
            # محاولة الاتصال
            nResult = connect(sock, cHost, nPort)
            if nResult != 0
                close(sock)
                oLogger.debug("فشل الاتصال إلى " + cHost + ":" + nPort)
                return false
            ok
            
            close(sock)
            oLogger.debug("نجح الاتصال إلى " + cHost + ":" + nPort)
            return true
            
        catch
            oLogger.debug("خطأ في فحص اتصال SSL: " + cCatchError)
            return false
        done
    
    /*
    الحصول على تفاصيل شهادة SSL
    المدخلات: cHost - عنوان الخادم، nPort - رقم المنفذ
    المخرجات: كائن يحتوي على تفاصيل الشهادة
    */
    func getCertificateDetails cHost, nPort
        oLogger.info("الحصول على تفاصيل شهادة SSL من " + cHost + ":" + nPort)
        
        # في التطبيق الحقيقي، ستحتاج لاستخدام openssllib للاتصال بـ SSL
        # هذا مثال مبسط يحاكي الوظيفة
        
        if not testSSLConnection(cHost, nPort)
            return createErrorCertificate("فشل الاتصال بالخادم")
        ok
        
        try
            # محاكاة الحصول على تفاصيل الشهادة
            # في التطبيق الحقيقي ستستخدم دوال openssllib
            
            aCertDetails = [
                :host = cHost,
                :port = nPort,
                :subject = "CN=" + cHost,
                :issuer = "CA Authority",
                :valid_from = "2024-01-01",
                :valid_to = "2025-12-31",
                :serial_number = "123456789",
                :signature_algorithm = "SHA256withRSA",
                :public_key_algorithm = "RSA",
                :key_size = 2048,
                :version = 3,
                :is_valid = true,
                :is_expired = false,
                :is_self_signed = false,
                :days_until_expiry = 365,
                :san_domains = [cHost],
                :fingerprint_sha1 = sha1(cHost + string(nPort)),
                :fingerprint_sha256 = sha256(cHost + string(nPort))
            ]
            
            # التحقق من صحة التواريخ
            aCertDetails[:is_expired] = checkIfExpired(aCertDetails[:valid_to])
            aCertDetails[:days_until_expiry] = calculateDaysUntilExpiry(aCertDetails[:valid_to])
            
            oLogger.info("تم الحصول على تفاصيل الشهادة بنجاح")
            return aCertDetails
            
        catch
            oLogger.error("خطأ في الحصول على تفاصيل الشهادة: " + cCatchError)
            return createErrorCertificate("خطأ في الحصول على تفاصيل الشهادة: " + cCatchError)
        done
    
    /*
    إنشاء كائن شهادة خطأ
    */
    func createErrorCertificate cError
        return [
            :host = "",
            :port = 0,
            :subject = "",
            :issuer = "",
            :valid_from = "",
            :valid_to = "",
            :is_valid = false,
            :is_expired = true,
            :error = cError
        ]
    
    /*
    التحقق من انتهاء صلاحية الشهادة
    المدخلات: cValidTo - تاريخ انتهاء الصلاحية
    المخرجات: true إذا كانت منتهية الصلاحية، false إذا لم تكن كذلك
    */
    func checkIfExpired cValidTo
        # مبسط - في التطبيق الحقيقي ستحتاج لمقارنة التواريخ بدقة
        return false
    
    /*
    حساب الأيام المتبقية حتى انتهاء الصلاحية
    المدخلات: cValidTo - تاريخ انتهاء الصلاحية
    المخرجات: عدد الأيام
    */
    func calculateDaysUntilExpiry cValidTo
        # مبسط - في التطبيق الحقيقي ستحتاج لحساب الفرق بين التواريخ
        return 365
    
    /*
    فحص الشيفرات الضعيفة
    المدخلات: cHost - عنوان الخادم، nPort - رقم المنفذ
    المخرجات: قائمة الشيفرات الضعيفة المدعومة
    */
    func checkWeakCiphers cHost, nPort
        oLogger.info("فحص الشيفرات الضعيفة على " + cHost + ":" + nPort)
        
        aWeakCiphersFound = []
        
        if not testSSLConnection(cHost, nPort)
            oLogger.error("فشل الاتصال بالخادم لفحص الشيفرات")
            return aWeakCiphersFound
        ok
        
        # في التطبيق الحقيقي، ستحتاج لاختبار كل شيفرة على حدة
        # هذا مثال مبسط
        
        for cCipher in aWeakCiphers
            if bVerbose
                oLogger.debug("اختبار الشيفرة: " + cCipher)
            ok
            
            # محاكاة اختبار الشيفرة
            # في التطبيق الحقيقي ستحاول الاتصال باستخدام هذه الشيفرة
            bSupported = testCipherSupport(cHost, nPort, cCipher)
            
            if bSupported
                add(aWeakCiphersFound, cCipher)
                oLogger.warning("تم العثور على شيفرة ضعيفة مدعومة: " + cCipher)
            ok
        next
        
        oLogger.info("تم العثور على " + len(aWeakCiphersFound) + " شيفرة ضعيفة")
        return aWeakCiphersFound
    
    /*
    اختبار دعم شيفرة معينة
    المدخلات: cHost - عنوان الخادم، nPort - رقم المنفذ، cCipher - الشيفرة
    المخرجات: true إذا كانت مدعومة، false إذا لم تكن كذلك
    */
    func testCipherSupport cHost, nPort, cCipher
        # في التطبيق الحقيقي، ستحتاج لاستخدام openssllib
        # لمحاولة الاتصال باستخدام شيفرة محددة
        
        # محاكاة - بعض الشيفرات الضعيفة قد تكون مدعومة
        if cCipher = "RC4-MD5" or cCipher = "DES-CBC-SHA"
            return (random(10) < 3)  # 30% احتمال أن تكون مدعومة
        ok
        
        return false
    
    /*
    فحص بروتوكولات SSL/TLS المدعومة
    المدخلات: cHost - عنوان الخادم، nPort - رقم المنفذ
    المخرجات: قائمة البروتوكولات المدعومة
    */
    func checkSupportedProtocols cHost, nPort
        oLogger.info("فحص بروتوكولات SSL/TLS المدعومة على " + cHost + ":" + nPort)
        
        aSupportedProtocols = []
        
        for cProtocol in aSSLVersions
            if bVerbose
                oLogger.debug("اختبار البروتوكول: " + cProtocol)
            ok
            
            # محاكاة اختبار البروتوكول
            bSupported = testProtocolSupport(cHost, nPort, cProtocol)
            
            if bSupported
                add(aSupportedProtocols, cProtocol)
                
                # تحذير من البروتوكولات القديمة
                if cProtocol = "SSLv2" or cProtocol = "SSLv3" or cProtocol = "TLSv1.0"
                    oLogger.warning("تم العثور على بروتوكول قديم مدعوم: " + cProtocol)
                else
                    oLogger.info("البروتوكول مدعوم: " + cProtocol)
                ok
            ok
        next
        
        return aSupportedProtocols
    
    /*
    اختبار دعم بروتوكول معين
    */
    func testProtocolSupport cHost, nPort, cProtocol
        # محاكاة - البروتوكولات الحديثة أكثر احتمالاً للدعم
        switch cProtocol
            on "SSLv2"
                return false  # عادة غير مدعوم
            on "SSLv3"
                return (random(10) < 2)  # 20% احتمال
            on "TLSv1.0"
                return (random(10) < 5)  # 50% احتمال
            on "TLSv1.1"
                return (random(10) < 7)  # 70% احتمال
            on "TLSv1.2"
                return (random(10) < 9)  # 90% احتمال
            on "TLSv1.3"
                return (random(10) < 8)  # 80% احتمال
            other
                return false
        off
    
    /*
    فحص شامل لـ SSL
    المدخلات: cHost - عنوان الخادم، nPort - رقم المنفذ
    المخرجات: تقرير شامل
    */
    func comprehensiveSSLCheck cHost, nPort
        oLogger.startOperation("فحص SSL شامل للخادم " + cHost + ":" + nPort)
        
        aReport = [
            :host = cHost,
            :port = nPort,
            :ssl_available = false,
            :certificate = [],
            :supported_protocols = [],
            :weak_ciphers = [],
            :security_issues = [],
            :recommendations = []
        ]
        
        # فحص توفر SSL
        aReport[:ssl_available] = testSSLConnection(cHost, nPort)
        
        if not aReport[:ssl_available]
            aReport[:security_issues] = ["SSL/TLS غير متاح على هذا المنفذ"]
            oLogger.endOperation("فحص SSL شامل - SSL غير متاح")
            return aReport
        ok
        
        # الحصول على تفاصيل الشهادة
        ? "الحصول على تفاصيل الشهادة..."
        aReport[:certificate] = getCertificateDetails(cHost, nPort)
        
        # فحص البروتوكولات المدعومة
        ? "فحص البروتوكولات المدعومة..."
        aReport[:supported_protocols] = checkSupportedProtocols(cHost, nPort)
        
        # فحص الشيفرات الضعيفة
        ? "فحص الشيفرات الضعيفة..."
        aReport[:weak_ciphers] = checkWeakCiphers(cHost, nPort)
        
        # تحليل المشاكل الأمنية
        aReport[:security_issues] = analyzeSecurityIssues(aReport)
        
        # إنشاء التوصيات
        aReport[:recommendations] = generateRecommendations(aReport)
        
        oLogger.endOperation("فحص SSL شامل")
        return aReport
    
    /*
    تحليل المشاكل الأمنية
    */
    func analyzeSecurityIssues aReport
        aIssues = []
        
        # فحص انتهاء صلاحية الشهادة
        if aReport[:certificate][:is_expired]
            add(aIssues, "الشهادة منتهية الصلاحية")
        ok
        
        # فحص الشهادة الموقعة ذاتياً
        if aReport[:certificate][:is_self_signed]
            add(aIssues, "الشهادة موقعة ذاتياً")
        ok
        
        # فحص البروتوكولات القديمة
        for cProtocol in aReport[:supported_protocols]
            if cProtocol = "SSLv2" or cProtocol = "SSLv3" or cProtocol = "TLSv1.0"
                add(aIssues, "يدعم بروتوكول قديم: " + cProtocol)
            ok
        next
        
        # فحص الشيفرات الضعيفة
        if len(aReport[:weak_ciphers]) > 0
            add(aIssues, "يدعم " + len(aReport[:weak_ciphers]) + " شيفرة ضعيفة")
        ok
        
        return aIssues
    
    /*
    إنشاء التوصيات
    */
    func generateRecommendations aReport
        aRecommendations = []
        
        # توصيات الشهادة
        if aReport[:certificate][:is_expired]
            add(aRecommendations, "تجديد الشهادة المنتهية الصلاحية")
        ok
        
        if aReport[:certificate][:days_until_expiry] < 30
            add(aRecommendations, "تجديد الشهادة قريباً (تنتهي خلال " + aReport[:certificate][:days_until_expiry] + " يوم)")
        ok
        
        # توصيات البروتوكولات
        for cProtocol in aReport[:supported_protocols]
            if cProtocol = "SSLv2" or cProtocol = "SSLv3"
                add(aRecommendations, "إلغاء دعم " + cProtocol + " (غير آمن)")
            ok
        next
        
        # توصيات الشيفرات
        if len(aReport[:weak_ciphers]) > 0
            add(aRecommendations, "إلغاء دعم الشيفرات الضعيفة")
        ok
        
        # توصيات عامة
        if not find(aReport[:supported_protocols], "TLSv1.3")
            add(aRecommendations, "تفعيل دعم TLS 1.3")
        ok
        
        return aRecommendations



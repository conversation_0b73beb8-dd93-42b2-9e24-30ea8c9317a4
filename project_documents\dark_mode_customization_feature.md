# إضافة تخصيص الوضع المظلم من الهيدر

## وصف الميزة
إضافة القدرة للمستخدمين على تخصيص ألوان **الوضع المظلم فقط** مباشرة من زر التبديل في الهيدر.

### سياسة التخصيص:
- **الوضع المظلم** 🌙: متاح للتخصيص لجميع المستخدمين من الهيدر
- **الوضع النهاري** ☀️: يُخصص فقط من إعدادات المسؤول (محدود للمسؤولين)

## المتطلبات الوظيفية

### الوظائف المطلوبة:
1. **تبديل سريع**: النقر العادي يبدل بين الأوضاع كما هو حالياً
2. **تخصيص سريع**: الضغط المطول أو النقر بالزر الأيمن يفتح modal التخصيص
3. **معاينة فورية**: تطبيق الألوان فوراً أثناء التغيير
4. **حفظ تلقائي**: حفظ الألوان عند الإغلاق أو التأكيد
5. **إعادة تعيين**: خيار لإعادة الألوان للافتراضية

### التفاعلات المطلوبة:
- **نقر عادي**: تبديل الوضع
- **ضغط مطول (1 ثانية)**: فتح modal التخصيص
- **نقر بالزر الأيمن**: فتح modal التخصيص
- **مفاتيح الاختصار**: Ctrl+Shift+D لفتح التخصيص

## خطة التنفيذ

### T01: إنشاء مكون تخصيص الوضع المظلم
- [x] **T01.01: إنشاء DarkModeCustomizer component**
  - **الملف:** `src/components/DarkModeCustomizer.tsx` (جديد)
  - **التفاصيل:** modal صغير لتخصيص ألوان الوضع المظلم والنهاري
  - **الميزات:**
    - منتقي ألوان لكل عنصر (رئيسي، ثانوي، خلفية، نص)
    - معاينة فورية للتغييرات
    - أزرار الحفظ والإلغاء والإعادة تعيين
    - مجموعات ألوان جاهزة (أزرق، أخضر، بنفسجي، برتقالي)

### T02: تحديث زر التبديل في الهيدر
- [x] **T02.01: إضافة event listeners للتفاعلات الجديدة**
  - **الملف:** `src/components/header/header.tsx`
  - **التفاصيل:** إضافة معالجات للضغط المطول والنقر بالزر الأيمن
  - **الوظائف:**
    - onContextMenu للنقر بالزر الأيمن
    - onMouseDown/onMouseUp للضغط المطول
    - onKeyDown لمفاتيح الاختصار (Ctrl+Shift+D, Ctrl+D)

### T03: إضافة حالة التخصيص
- [x] **T03.01: إضافة state management للتخصيص**
  - **الملف:** `src/components/header/header.tsx`
  - **التفاصيل:** إدارة حالة فتح/إغلاق modal التخصيص
  - **المتغيرات:**
    - showCustomizer: boolean
    - isLongPress: boolean
    - longPressTimer: NodeJS.Timeout

### T04: تحسين تجربة المستخدم
- [x] **T04.01: إضافة مؤشرات بصرية**
  - **الملف:** `src/components/header/header.tsx`
  - **التفاصيل:** إضافة tooltip وتأثيرات بصرية
  - **الميزات:**
    - tooltip يوضح الوظائف المختلفة
    - تأثير بصري أثناء الضغط المطول (scale + background)
    - أيقونة تخصيص صغيرة (FaCog)

### T05: دعم الهواتف المحمولة
- [x] **T05.01: تحسين التفاعل للمس**
  - **الملف:** `src/components/DarkModeCustomizer.tsx` و `src/components/header/header.tsx`
  - **التفاصيل:** تحسين التصميم للشاشات الصغيرة
  - **الميزات:**
    - أزرار أكبر للمس في المحمول
    - زر تخصيص منفصل في القائمة المحمولة
    - تخطيط محسن للهواتف

## التصميم المقترح

### مكون DarkModeCustomizer:
```tsx
interface DarkModeCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (colors: SiteColors) => void;
}
```

### الألوان القابلة للتخصيص:
1. **اللون الرئيسي** (Primary Color)
2. **اللون الثانوي** (Secondary Color)
3. **لون الشريط الجانبي** (Sidebar Color)
4. **لون الخلفية** (Background Color)
5. **اللون المميز** (Accent Color)
6. **لون النص** (Text Color)

### الأزرار المطلوبة:
- **حفظ**: حفظ الألوان الجديدة
- **إلغاء**: إغلاق بدون حفظ
- **إعادة تعيين**: العودة للألوان الافتراضية
- **معاينة**: تطبيق مؤقت للمعاينة

## واجهة المستخدم

### زر التبديل المحسن:
```
[🌙/☀️] ← زر التبديل الحالي
    ↓
[🌙/☀️ ⚙️] ← مع أيقونة تخصيص صغيرة
```

### Tooltip التوضيحي:
```
"نقر: تبديل الوضع
ضغط مطول: تخصيص الألوان
زر أيمن: تخصيص الألوان"
```

### Modal التخصيص:
```
┌─────────────────────────────────┐
│ 🎨 تخصيص الوضع المظلم          │
├─────────────────────────────────┤
│ اللون الرئيسي    [#22d3ee] 🎨  │
│ اللون الثانوي    [#06b6d4] 🎨  │
│ لون الخلفية      [#1e293b] 🎨  │
│ لون النص         [#f1f5f9] 🎨  │
├─────────────────────────────────┤
│ [حفظ] [إلغاء] [إعادة تعيين]     │
└─────────────────────────────────┘
```

## الميزات المتقدمة

### مجموعات ألوان جاهزة:
- **أزرق داكن**: مجموعة ألوان زرقاء للوضع المظلم
- **أخضر داكن**: مجموعة ألوان خضراء للوضع المظلم
- **بنفسجي داكن**: مجموعة ألوان بنفسجية للوضع المظلم
- **رمادي داكن**: مجموعة ألوان رمادية للوضع المظلم

### مفاتيح الاختصار:
- **Ctrl+Shift+D**: فتح تخصيص الوضع المظلم
- **Ctrl+Shift+L**: فتح تخصيص الوضع النهاري
- **Ctrl+D**: تبديل سريع بين الأوضاع

### حفظ متقدم:
- **حفظ محلي**: في localStorage للوصول السريع
- **مزامنة المستخدم**: حفظ في ملف المستخدم (اختياري)
- **تصدير/استيراد**: إمكانية مشاركة مجموعات الألوان

## فوائد الميزة

### للمستخدمين:
1. **سهولة الوصول**: تخصيص سريع بدون الذهاب للإعدادات
2. **معاينة فورية**: رؤية التغييرات مباشرة
3. **مرونة كاملة**: تخصيص كل لون حسب الذوق
4. **حفظ تلقائي**: لا حاجة لتذكر الحفظ

### للنظام:
1. **تجربة مستخدم محسنة**: تفاعل أكثر سلاسة
2. **تقليل النقرات**: وصول مباشر للتخصيص
3. **زيادة الاستخدام**: المستخدمون سيخصصون أكثر
4. **تمييز الموقع**: ميزة فريدة تميز الموقع

## متطلبات تقنية

### التبعيات المطلوبة:
- React hooks (useState, useEffect, useRef)
- CSS modules أو styled-components للتصميم
- localStorage للحفظ المحلي
- Event listeners للتفاعلات

### الأداء:
- تحميل lazy للمكون
- تحسين re-renders
- debouncing للتغييرات السريعة
- caching للألوان المحفوظة

### الأمان:
- validation للألوان المدخلة
- sanitization للقيم
- fallback للألوان الافتراضية
- error handling شامل

## خطة الاختبار

### اختبارات الوظائف:
1. **النقر العادي**: يجب أن يبدل الوضع
2. **الضغط المطول**: يجب أن يفتح التخصيص
3. **النقر بالزر الأيمن**: يجب أن يفتح التخصيص
4. **تغيير الألوان**: يجب أن يطبق فوراً
5. **الحفظ**: يجب أن يحفظ الألوان
6. **الإلغاء**: يجب أن يعيد الألوان السابقة

### اختبارات الأجهزة:
- **سطح المكتب**: جميع التفاعلات تعمل
- **الهواتف**: اللمس والضغط المطول يعملان
- **الأجهزة اللوحية**: تجربة محسنة للمس

### اختبارات المتصفحات:
- Chrome, Firefox, Safari, Edge
- إصدارات مختلفة من المتصفحات
- وضع الخصوصية/التصفح الخفي

## النتائج المحققة ✅

### 🎯 **الميزات المطبقة:**

#### **1. تخصيص سريع من الهيدر (الوضع المظلم فقط):**
- **نقر عادي**: تبديل سريع بين الأوضاع ✅
- **ضغط مطول (1 ثانية)**: فتح modal التخصيص (في الوضع المظلم فقط) ✅
- **نقر بالزر الأيمن**: فتح modal التخصيص (في الوضع المظلم فقط) ✅
- **مفاتيح الاختصار**: Ctrl+Shift+D للتخصيص، Ctrl+D للتبديل ✅
- **حماية الوضع النهاري**: رسالة توضيحية عند المحاولة في الوضع النهاري ✅

#### **2. مكون التخصيص الشامل:**
- **منتقي ألوان**: لكل عنصر (رئيسي، ثانوي، خلفية، نص) ✅
- **معاينة فورية**: التغييرات تطبق مباشرة للمعاينة ✅
- **مجموعات جاهزة**: أزرق، أخضر، بنفسجي، برتقالي ✅
- **حفظ ذكي**: حفظ منفصل للوضع المظلم والنهاري ✅

#### **3. تجربة مستخدم محسنة:**
- **مؤشرات بصرية**: تأثير أثناء الضغط المطول ✅
- **tooltip توضيحي**: يشرح جميع الوظائف ✅
- **أيقونة تخصيص**: FaCog صغيرة بجانب أيقونة الوضع ✅
- **رسائل واضحة**: تأكيد الحفظ مع تحديد الوضع ✅

#### **4. دعم الهواتف المحمولة:**
- **زر منفصل**: زر تخصيص منفصل في القائمة المحمولة ✅
- **تصميم متجاوب**: modal يتكيف مع الشاشات الصغيرة ✅
- **أزرار محسنة**: حجم مناسب للمس ✅

### 🎨 **كيفية الاستخدام:**

#### **على سطح المكتب:**
1. **تبديل سريع**: انقر على زر 🌙/☀️ في الهيدر
2. **تخصيص الوضع المظلم** (فقط في الوضع المظلم 🌙):
   - اضغط مطولاً على الزر لمدة ثانية
   - أو انقر بالزر الأيمن
   - أو استخدم Ctrl+Shift+D
3. **اختر الألوان**: استخدم منتقي الألوان أو المجموعات الجاهزة
4. **احفظ**: انقر "حفظ" لحفظ ألوان الوضع المظلم نهائياً

#### **على الهواتف:**
1. **افتح القائمة**: انقر على ☰ في الهيدر
2. **تبديل**: انقر على زر "الوضع المظلم/النهاري"
3. **تخصيص الوضع المظلم**: انقر على زر ⚙️ (متاح فقط في الوضع المظلم)
4. **اختر وحفظ**: نفس الخطوات كسطح المكتب

#### **ملاحظة مهمة:**
- 🌙 **الوضع المظلم**: يمكن تخصيصه من الهيدر لجميع المستخدمين
- ☀️ **الوضع النهاري**: يُخصص فقط من إعدادات المسؤول

### 🔧 **الميزات التقنية:**

#### **حفظ ذكي:**
- **منفصل**: ألوان الوضع المظلم والنهاري محفوظة منفصلة
- **محلي**: حفظ في localStorage للوصول السريع
- **مزامنة**: تحديث فوري عبر جميع علامات التبويب

#### **أداء محسن:**
- **تحميل lazy**: المكون يحمل عند الحاجة فقط
- **معاينة فورية**: تطبيق الألوان بدون تأخير
- **ذاكرة محسنة**: تنظيف timers تلقائياً

#### **أمان وموثوقية:**
- **validation**: التحقق من صحة الألوان المدخلة
- **fallback**: ألوان افتراضية في حالة الخطأ
- **error handling**: معالجة شاملة للأخطاء

### 🎉 **النتيجة النهائية:**

الآن يمكن للمستخدمين:
- ✅ **تبديل سريع** بين الأوضاع بنقرة واحدة
- ✅ **تخصيص الوضع المظلم** من الهيدر مباشرة (للجميع)
- ✅ **حماية الوضع النهاري** (للمسؤولين فقط من الإعدادات)
- ✅ **معاينة فورية** للتغييرات قبل الحفظ
- ✅ **استخدام مجموعات جاهزة** للألوان المظلمة الشائعة
- ✅ **حفظ منفصل** لكل وضع
- ✅ **تجربة محسنة** على جميع الأجهزة
- ✅ **رسائل توضيحية** عند محاولة تخصيص الوضع النهاري

### 🔐 **الأمان والتحكم:**
- **الوضع المظلم** 🌙: حرية كاملة للمستخدمين في التخصيص
- **الوضع النهاري** ☀️: محمي ومحدود للمسؤولين فقط
- **رسائل واضحة**: توضح للمستخدم سياسة التخصيص

هذه الميزة تجمع بين سهولة الاستخدام والأمان الإداري! 🎨🔒✨

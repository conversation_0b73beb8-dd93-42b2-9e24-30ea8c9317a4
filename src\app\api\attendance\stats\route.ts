import { NextRequest, NextResponse } from 'next/server';
import prisma from '../../../../lib/prisma';
import { getToken } from '../../../../lib/auth';
import { Prisma } from '@prisma/client';

// GET /api/attendance/stats
export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
      return NextResponse.json(
        { error: 'غير مصرح به، يجب أن تكون مسؤول أو معلم' },
        { status: 401 }
      );
    }

    // استخراج المعلمات من URL
    const searchParams = request.nextUrl.searchParams;
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');
    const classeId = searchParams.get('classeId');

    // تحديد نطاق التاريخ
    const startDate = startDateParam
      ? new Date(`${startDateParam}T00:00:00Z`)
      : new Date(new Date().setMonth(new Date().getMonth() - 1)); // شهر واحد للخلف افتراضياً

    const endDate = endDateParam
      ? new Date(`${endDateParam}T23:59:59Z`)
      : new Date(); // اليوم الحالي افتراضياً

    // بناء شروط البحث
    const whereCondition: Prisma.AttendanceWhereInput = {
      date: {
        gte: startDate,
        lte: endDate
      }
    };

    // إضافة شرط القسم إذا تم تحديده
    if (classeId) {
      whereCondition.student = {
        classeId: parseInt(classeId)
      };
    }

    // جلب سجلات الحضور
    const attendanceRecords = await prisma.attendance.findMany({
      where: whereCondition,
      include: {
        student: {
          include: {
            classe: true
          }
        }
      },
      orderBy: {
        date: 'asc'
      }
    });

    // إحصائيات عامة
    const totalRecords = attendanceRecords.length;
    const presentCount = attendanceRecords.filter(record => record.status === 'PRESENT').length;
    const absentCount = attendanceRecords.filter(record => record.status === 'ABSENT').length;
    const excusedCount = attendanceRecords.filter(record => record.status === 'EXCUSED').length;

    const presentRate = totalRecords > 0 ? (presentCount / totalRecords) * 100 : 0;
    const absentRate = totalRecords > 0 ? (absentCount / totalRecords) * 100 : 0;
    const excusedRate = totalRecords > 0 ? (excusedCount / totalRecords) * 100 : 0;

    // إحصائيات حسب القسم
    const classeMap = new Map();

    attendanceRecords.forEach(record => {
      const classeId = record.student.classeId;
      const classeName = record.student.classe?.name || 'غير محدد';

      if (!classeMap.has(classeId)) {
        classeMap.set(classeId, {
          classeId,
          classeName,
          totalRecords: 0,
          presentCount: 0,
          absentCount: 0,
          excusedCount: 0
        });
      }

      const classeStats = classeMap.get(classeId);
      classeStats.totalRecords++;

      if (record.status === 'PRESENT') {
        classeStats.presentCount++;
      } else if (record.status === 'ABSENT') {
        classeStats.absentCount++;
      } else if (record.status === 'EXCUSED') {
        classeStats.excusedCount++;
      }
    });

    // حساب النسب المئوية لكل قسم
    const classeStats = Array.from(classeMap.values()).map(stats => {
      const presentRate = stats.totalRecords > 0 ? (stats.presentCount / stats.totalRecords) * 100 : 0;
      const absentRate = stats.totalRecords > 0 ? (stats.absentCount / stats.totalRecords) * 100 : 0;
      const excusedRate = stats.totalRecords > 0 ? (stats.excusedCount / stats.totalRecords) * 100 : 0;

      return {
        ...stats,
        presentRate,
        absentRate,
        excusedRate
      };
    });

    // إحصائيات حسب الطالب
    const studentMap = new Map();

    attendanceRecords.forEach(record => {
      const studentId = record.studentId;
      const studentName = record.student.name;
      const classeName = record.student.classe?.name || 'غير محدد';

      if (!studentMap.has(studentId)) {
        studentMap.set(studentId, {
          studentId,
          studentName,
          classeName,
          totalRecords: 0,
          presentCount: 0,
          absentCount: 0,
          excusedCount: 0
        });
      }

      const studentStats = studentMap.get(studentId);
      studentStats.totalRecords++;

      if (record.status === 'PRESENT') {
        studentStats.presentCount++;
      } else if (record.status === 'ABSENT') {
        studentStats.absentCount++;
      } else if (record.status === 'EXCUSED') {
        studentStats.excusedCount++;
      }
    });

    // حساب النسب المئوية لكل طالب
    const studentStats = Array.from(studentMap.values()).map(stats => {
      const presentRate = stats.totalRecords > 0 ? (stats.presentCount / stats.totalRecords) * 100 : 0;
      const absentRate = stats.totalRecords > 0 ? (stats.absentCount / stats.totalRecords) * 100 : 0;
      const excusedRate = stats.totalRecords > 0 ? (stats.excusedCount / stats.totalRecords) * 100 : 0;

      return {
        ...stats,
        presentRate,
        absentRate,
        excusedRate
      };
    });

    // إحصائيات حسب التاريخ
    const dateMap = new Map();

    attendanceRecords.forEach(record => {
      const dateStr = record.date.toISOString().split('T')[0];

      if (!dateMap.has(dateStr)) {
        dateMap.set(dateStr, {
          date: dateStr,
          totalRecords: 0,
          presentCount: 0,
          absentCount: 0,
          excusedCount: 0
        });
      }

      const dateStats = dateMap.get(dateStr);
      dateStats.totalRecords++;

      if (record.status === 'PRESENT') {
        dateStats.presentCount++;
      } else if (record.status === 'ABSENT') {
        dateStats.absentCount++;
      } else if (record.status === 'EXCUSED') {
        dateStats.excusedCount++;
      }
    });

    // حساب النسب المئوية لكل تاريخ
    const dateStats = Array.from(dateMap.values()).map(stats => {
      const presentRate = stats.totalRecords > 0 ? (stats.presentCount / stats.totalRecords) * 100 : 0;

      return {
        ...stats,
        presentRate
      };
    });

    // ترتيب إحصائيات التاريخ حسب التاريخ
    dateStats.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // إعداد البيانات النهائية
    const stats = {
      totalRecords,
      presentCount,
      absentCount,
      excusedCount,
      presentRate,
      absentRate,
      excusedRate,
      classeStats,
      studentStats,
      dateStats
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching attendance statistics:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب إحصائيات الحضور' },
      { status: 500 }
    );
  }
}

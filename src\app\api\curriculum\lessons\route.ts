import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/curriculum/lessons - إنشاء درس جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, description, order, unitId } = body;

    if (!title || !unitId) {
      return NextResponse.json(
        { message: "يجب توفير عنوان الدرس ومعرف الوحدة" },
        { status: 400 }
      );
    }

    // التحقق من وجود الوحدة
    const unit = await prisma.curriculumUnit.findUnique({
      where: { id: unitId },
    });

    if (!unit) {
      return NextResponse.json(
        { message: "الوحدة غير موجودة" },
        { status: 404 }
      );
    }

    // تحديد الترتيب إذا لم يتم توفيره
    let lessonOrder = order;
    if (!lessonOrder) {
      const lastLesson = await prisma.curriculumLesson.findFirst({
        where: { unitId },
        orderBy: { order: 'desc' },
      });
      lessonOrder = lastLesson ? lastLesson.order + 1 : 1;
    }

    // إنشاء الدرس
    const lesson = await prisma.curriculumLesson.create({
      data: {
        title,
        description,
        order: lessonOrder,
        unitId,
      },
    });

    return NextResponse.json(lesson);
  } catch (error) {
    console.error('Error creating lesson:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء الدرس" },
      { status: 500 }
    );
  }
}

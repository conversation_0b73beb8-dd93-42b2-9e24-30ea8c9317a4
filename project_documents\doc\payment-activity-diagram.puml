@startuml Payment Process Activity Diagram

skinparam backgroundColor white
skinparam activityBorderColor black
skinparam activityBackgroundColor LightBlue
skinparam activityDiamondBackgroundColor LightYellow
skinparam activityDiamondBorderColor black
skinparam noteBorderColor black
skinparam noteBackgroundColor LightGreen

start

|#LightCyan|System|
:Generate monthly invoice;
:Calculate fees based on student enrollment;
note right
  Includes:
  - Tuition fees
  - Special session fees
  - Learning materials
  - Any outstanding balance
end note
:Send invoice notification;

|#Gold|Parent|
:Receive invoice notification;
:Log into parent portal;
:View invoice details;
:Review payment history;

if (Payment method?) then (Online)
  :Select online payment;
  :Choose payment option;
  note right
    - Credit/Debit card
    - Bank transfer
    - Digital wallet
  end note

  |System|
  :Redirect to secure payment gateway;

  |Parent|
  :Enter payment details;
  :Confirm payment amount;

  |#PaleGreen|Payment Gateway|
  :Process payment;
  :Verify transaction;

  if (Payment successful?) then (yes)
    :Generate transaction ID;
    :Send confirmation to system;

    |System|
    :Record payment transaction;
    :Update student account;
    :Mark invoice as paid;
    :Generate digital receipt;
    :Send payment confirmation;

    |Parent|
    :Receive payment confirmation;
    :Download receipt;

  else (no)
    :Return error code;

    |System|
    :Log failed transaction;
    :Notify parent of failure;

    |Parent|
    :View error message;
    :Choose alternative payment method;
    note right: Can try again or
select different method
    end note
  endif

else (Offline)
  |Parent|
  :Select offline payment option;
  :Generate payment reference;
  :Make payment through bank/cash;

  |#AntiqueWhite|Administrator|
  :Receive offline payment notification;
  :Verify payment receipt;
  :Record payment in system;

  |System|
  :Update payment status;
  :Mark invoice as paid;
  :Generate receipt;
  :Send confirmation to parent;

  |Parent|
  :Receive payment confirmation;
endif

|System|
:Update financial records;
:Generate payment reports;
:Archive transaction data;

|Administrator|
:Review financial reports;
:Reconcile accounts;
:Generate financial statements;

stop

@enduml

'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'react-toastify';
import { FaLock, FaFileAlt, FaExclamationTriangle, FaFilePdf, FaFileExcel, FaPrint } from 'react-icons/fa';
import { useReactToPrint } from 'react-to-print';
import { exportToExcel, exportToPdf } from '@/utils/export-utils';

interface SharedReport {
  id: string;
  title: string;
  type: string;
  data: any;
  createdAt: string;
  expiryDate: string;
  hasPassword: boolean;
}

export default function SharedReportPage() {
  const params = useParams();
  const token = params?.token as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [isPasswordRequired, setIsPasswordRequired] = useState(false);
  const [password, setPassword] = useState('');
  const [report, setReport] = useState<SharedReport | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isExpired, setIsExpired] = useState(false);
  
  // مرجع للطباعة
  const reportRef = React.useRef<HTMLDivElement>(null);
  
  // جلب بيانات التقرير المشترك
  useEffect(() => {
    const fetchSharedReport = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const response = await fetch(`/api/shared-reports/${token}`);
        
        if (response.status === 401) {
          setIsPasswordRequired(true);
          setIsLoading(false);
          return;
        }
        
        if (response.status === 410) {
          setIsExpired(true);
          setIsLoading(false);
          return;
        }
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'فشل في جلب التقرير المشترك');
        }
        
        const data = await response.json();
        setReport(data.report);
      } catch (error) {
        console.error('Error fetching shared report:', error);
        setError('حدث خطأ أثناء جلب التقرير المشترك');
      } finally {
        setIsLoading(false);
      }
    };
    
    if (token) {
      fetchSharedReport();
    }
  }, [token]);
  
  // التحقق من كلمة المرور
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/shared-reports/${token}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ password })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'كلمة المرور غير صحيحة');
      }
      
      const data = await response.json();
      setReport(data.report);
      setIsPasswordRequired(false);
    } catch (error) {
      console.error('Error verifying password:', error);
      setError(error instanceof Error ? error.message : 'حدث خطأ أثناء التحقق من كلمة المرور');
    } finally {
      setIsLoading(false);
    }
  };
  
  // طباعة التقرير
  const handlePrint = useReactToPrint({
    content: () => reportRef.current,
    documentTitle: report?.title || 'تقرير مشترك',
    onBeforeGetContent: () => {
      toast.info('جاري تحضير التقرير للطباعة...');
      return Promise.resolve();
    },
    onAfterPrint: () => {
      toast.success('تمت طباعة التقرير بنجاح');
    }
  });
  
  // تصدير التقرير إلى Excel
  const handleExportToExcel = () => {
    if (!report) return;
    
    try {
      // تحويل البيانات إلى تنسيق مناسب لـ Excel
      const excelData = Array.isArray(report.data) ? report.data : [report.data];
      
      exportToExcel(
        excelData,
        `${report.title}.xlsx`,
        report.title
      );
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };
  
  // تصدير التقرير إلى PDF
  const handleExportToPdf = () => {
    if (!report) return;
    
    try {
      // تحويل البيانات إلى تنسيق مناسب لـ PDF
      const pdfOptions = {
        title: report.title,
        fileName: `${report.title}.pdf`,
        tables: [
          {
            headers: Object.keys(report.data[0] || {}),
            data: Array.isArray(report.data) 
              ? report.data.map(item => Object.values(item)) 
              : [Object.values(report.data)]
          }
        ]
      };
      
      exportToPdf(pdfOptions);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };
  
  // عرض شاشة التحميل
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto"></div>
          <p className="mt-4 text-[var(--primary-color)]">جاري تحميل التقرير...</p>
        </div>
      </div>
    );
  }
  
  // عرض شاشة التقرير منتهي الصلاحية
  if (isExpired) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-red-600 flex items-center justify-center gap-2">
              <FaExclamationTriangle />
              <span>التقرير منتهي الصلاحية</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center mb-4">
              عذراً، هذا التقرير منتهي الصلاحية أو تم حذفه.
            </p>
            <Button
              onClick={() => window.location.href = '/'}
              className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
            >
              العودة إلى الصفحة الرئيسية
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // عرض شاشة إدخال كلمة المرور
  if (isPasswordRequired) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center flex items-center justify-center gap-2">
              <FaLock />
              <span>تقرير محمي بكلمة مرور</span>
            </CardTitle>
            <CardDescription className="text-center">
              يرجى إدخال كلمة المرور للوصول إلى هذا التقرير
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handlePasswordSubmit}>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password">كلمة المرور</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                </div>
                
                {error && (
                  <div className="text-red-600 text-sm">{error}</div>
                )}
                
                <Button
                  type="submit"
                  className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                  disabled={isLoading}
                >
                  {isLoading ? 'جاري التحقق...' : 'عرض التقرير'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // عرض رسالة الخطأ
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center text-red-600 flex items-center justify-center gap-2">
              <FaExclamationTriangle />
              <span>حدث خطأ</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
            >
              إعادة المحاولة
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // عرض التقرير
  return (
    <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      {/* أزرار الإجراءات */}
      <div className="flex justify-end gap-2 mb-4">
        <Button
          onClick={handlePrint}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
        >
          <FaPrint />
          <span>طباعة</span>
        </Button>
        
        <Button
          onClick={handleExportToExcel}
          className="bg-primary-color hover:bg-green-700 text-white flex items-center gap-1"
        >
          <FaFileExcel />
          <span>Excel</span>
        </Button>
        
        <Button
          onClick={handleExportToPdf}
          className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-1"
        >
          <FaFilePdf />
          <span>PDF</span>
        </Button>
      </div>
      
      {/* محتوى التقرير */}
      <div ref={reportRef} className="print-container">
        {report && (
          <>
            {/* عنوان التقرير */}
            <Card className="mb-6">
              <CardHeader className="pb-2">
                <CardTitle className="text-2xl text-center flex items-center justify-center gap-2">
                  <FaFileAlt className="text-[var(--primary-color)]" />
                  <span>{report.title}</span>
                </CardTitle>
                <CardDescription className="text-center">
                  تاريخ التقرير: {new Date(report.createdAt).toLocaleDateString('fr-FR')}
                </CardDescription>
              </CardHeader>
            </Card>
            
            {/* محتوى التقرير - يعتمد على نوع التقرير */}
            <div className="space-y-6">
              {/* هنا يتم عرض محتوى التقرير حسب نوعه */}
              {/* يمكن إضافة المزيد من التفاصيل حسب نوع التقرير */}
              <pre className="bg-white p-4 rounded-lg border border-gray-200 overflow-auto">
                {JSON.stringify(report.data, null, 2)}
              </pre>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

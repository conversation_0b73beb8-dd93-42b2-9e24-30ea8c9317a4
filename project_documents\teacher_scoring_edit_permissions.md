# إضافة صلاحيات تعديل النقاط للمعلم في صفحة التسجيل

## وصف المشكلة
كانت صفحة تسجيل النقاط للمعلم (`src/app/teachers/evaluation/scoring/page.tsx`) تدعم فقط تسجيل النقاط الجديدة ولا تحتوي على صلاحيات تعديل النقاط الموجودة مسبقاً.

## التحسينات المطبقة

### 1. إضافة دعم وضع التعديل
**الملف:** `src/app/teachers/evaluation/scoring/page.tsx`

**المتغيرات الجديدة:**
```typescript
const [existingGrades, setExistingGrades] = useState<{[key: string]: any}>({});
const [isEditMode, setIsEditMode] = useState(false);
const [loadingDelete, setLoadingDelete] = useState<{[key: string]: boolean}>({});
```

### 2. تحسين منطق جلب النقاط الموجودة
**التحسينات:**
- حفظ البيانات الأصلية للنقاط الموجودة لاستخدامها في التحديث
- تحديد وضع التعديل تلقائياً عند وجود نقاط مسبقة
- الحفاظ على معرفات النقاط الأصلية للتحديث

**الكود المحسن:**
```typescript
// حفظ البيانات الأصلية للنقاط الموجودة لاستخدامها في التحديث
const existingGradesMap: {[key: string]: any} = {};
data.data.forEach((point: ExamPoint) => {
  existingGradesMap[point.studentId.toString()] = point;
});
setExistingGrades(existingGradesMap);

// تحديد أن هذا وضع تعديل إذا كانت هناك نقاط موجودة
setIsEditMode(data.data.length > 0);
```

### 3. تحسين دالة الحفظ لدعم التحديث والإنشاء
**التحسينات:**
- التحقق من وجود نقطة امتحان موجودة للطالب
- استخدام PUT للتحديث و POST للإنشاء
- إرسال معرف النقطة عند التحديث

**الكود المحسن:**
```typescript
// التحقق من وجود نقطة امتحان موجودة للطالب
const existingGrade = existingGrades[grade.studentId];
const isUpdate = existingGrade && existingGrade.id;

let response;
if (isUpdate) {
  // تحديث النقطة الموجودة
  gradeData.id = existingGrade.id;
  response = await fetch('/api/exam-points', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(gradeData),
  });
} else {
  // إنشاء نقطة جديدة
  response = await fetch('/api/exam-points', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(gradeData),
  });
}
```

### 4. إضافة وظيفة حذف النقاط
**الوظائف الجديدة:**
- دالة `handleDeleteGrade` لحذف نقاط الطلاب
- تأكيد الحذف من المستخدم
- تحديث الحالة المحلية بعد الحذف

**الكود الجديد:**
```typescript
const handleDeleteGrade = async (studentId: string) => {
  const existingGrade = existingGrades[studentId];
  if (!existingGrade || !existingGrade.id) {
    toast.error('لا توجد نقطة امتحان لحذفها');
    return;
  }

  if (!confirm('هل أنت متأكد من حذف نقطة الامتحان لهذا الطالب؟')) {
    return;
  }

  // منطق الحذف...
};
```

### 5. تحسين واجهة المستخدم
**التحسينات المرئية:**

1. **عنوان ديناميكي:**
```typescript
{studentId ? 'تعديل نقاط الطالب' : (isEditMode ? 'تعديل نقاط الامتحان' : 'تسجيل نقاط الامتحان')}
```

2. **مؤشر وضع التعديل:**
```typescript
{isEditMode && (
  <div className="bg-blue-50 border border-blue-200 text-blue-800 rounded-md p-3 mb-4 text-right">
    <span>يتم عرض النقاط الموجودة مسبقاً. يمكنك تعديلها وحفظ التغييرات.</span>
  </div>
)}
```

3. **عمود الإجراءات في الجدول:**
```typescript
{isEditMode && <TableHead className="text-right">الإجراءات</TableHead>}
```

4. **أزرار الحذف:**
```typescript
{isEditMode && (
  <TableCell>
    {existingGrades[student.id] && (
      <Button
        variant="outline"
        size="sm"
        onClick={() => handleDeleteGrade(student.id)}
        disabled={loadingDelete[student.id]}
        className="text-red-600 hover:text-red-700 hover:bg-red-50"
      >
        {loadingDelete[student.id] ? <Loader2 /> : <Trash2 />}
      </Button>
    )}
  </TableCell>
)}
```

5. **نص زر الحفظ الديناميكي:**
```typescript
{studentId ? 'حفظ نقاط الطالب' : (isEditMode ? 'تحديث النقاط' : 'حفظ النقاط')}
```

### 6. رسائل النجاح المحسنة
**التحسينات:**
- رسائل مختلفة للإنشاء والتحديث
- توضيح العملية المنجزة للمستخدم

```typescript
const successMessage = isEditMode ? 'تم تحديث النقاط بنجاح' : 'تم حفظ النقاط بنجاح';
toast.success(isEditMode ? 'تم تحديث نقاط الامتحان بنجاح' : 'تم حفظ نقاط الامتحان بنجاح');
```

## الميزات الجديدة

### ✅ الوظائف المضافة:
1. **تعديل النقاط الموجودة** - يمكن للمعلم تعديل النقاط المسجلة مسبقاً
2. **حذف النقاط** - يمكن للمعلم حذف نقاط طلاب محددين
3. **وضع التعديل التلقائي** - يتم تحديد وضع التعديل تلقائياً عند وجود نقاط
4. **مؤشرات بصرية** - توضح للمعلم أنه في وضع التعديل
5. **رسائل محسنة** - تختلف حسب نوع العملية (إنشاء/تحديث)

### 🔒 الأمان والصلاحيات:
- التحقق من صلاحيات المعلم في API
- التأكيد قبل الحذف
- التحقق من وجود النقاط قبل التعديل/الحذف

### 🎨 تحسينات واجهة المستخدم:
- عناوين ديناميكية تعكس الوضع الحالي
- مؤشرات بصرية لوضع التعديل
- أزرار حذف مع حالات تحميل
- رسائل نجاح وخطأ محسنة

## النتائج المتوقعة
بعد تطبيق هذه التحسينات:

1. **يمكن للمعلم تعديل النقاط الموجودة** بدلاً من إنشاء نقاط جديدة فقط
2. **يمكن للمعلم حذف نقاط طلاب محددين** عند الحاجة
3. **تجربة مستخدم محسنة** مع مؤشرات واضحة للوضع الحالي
4. **أمان محسن** مع التحقق من الصلاحيات
5. **مرونة أكبر** في إدارة نقاط الامتحانات

## الملفات المعدلة
- `src/app/teachers/evaluation/scoring/page.tsx`

## التاريخ
تم تطبيق التحسينات في: [التاريخ الحالي]

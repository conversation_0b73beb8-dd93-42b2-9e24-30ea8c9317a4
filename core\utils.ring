/*
==============================================================================
    وحدة الأدوات الأساسية - Praetorian Core Utils
    
    الوصف: تحتوي على وظائف مساعدة للتشفير والترميز والتحويلات الأساسية
    المؤلف: Praetorian Team
==============================================================================
*/

/*
==============================================================================
    كلاس الأدوات الأساسية
==============================================================================
*/

# إنشاء مثيل عام من الأدوات
PraetorianUtilsInstance = new PraetorianUtils

class PraetorianUtils

    /*
    تحويل نص إلى ترميز Base64
    المدخلات: cText - النص المراد ترميزه
    المخرجات: النص مرمز بـ Base64
    */
    func base64Encode cText
        # استخدام جدول Base64 القياسي
        cBase64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
        cResult = ""
        nLen = len(cText)
        
        for i = 1 to nLen step 3
            # أخذ 3 بايت في كل مرة
            nByte1 = ascii(cText[i])
            nByte2 = 0
            nByte3 = 0
            
            if i + 1 <= nLen
                nByte2 = ascii(cText[i + 1])
            ok
            
            if i + 2 <= nLen  
                nByte3 = ascii(cText[i + 2])
            ok
            
            # تحويل 3 بايت إلى 4 أحرف Base64
            nCombined = (nByte1 << 16) | (nByte2 << 8) | nByte3
            
            cResult += cBase64Chars[(nCombined >> 18) + 1]
            cResult += cBase64Chars[((nCombined >> 12) & 63) + 1]
            
            if i + 1 <= nLen
                cResult += cBase64Chars[((nCombined >> 6) & 63) + 1]
            else
                cResult += "="
            ok
            
            if i + 2 <= nLen
                cResult += cBase64Chars[(nCombined & 63) + 1]
            else
                cResult += "="
            ok
        next
        
        return cResult
    
    /*
    فك ترميز Base64 إلى نص
    المدخلات: cBase64 - النص المرمز بـ Base64
    المخرجات: النص الأصلي
    */
    func base64Decode cBase64
        # إزالة المسافات والأحرف غير المرغوبة
        cBase64 = substr(cBase64, " ", "")
        cBase64 = substr(cBase64, nl, "")
        cBase64 = substr(cBase64, char(13), "")
        
        cBase64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
        cResult = ""
        nLen = len(cBase64)
        
        for i = 1 to nLen step 4
            if i + 3 > nLen break ok
            
            # تحويل 4 أحرف Base64 إلى 3 بايت
            nVal1 = substr(cBase64Chars, cBase64[i]) - 1
            nVal2 = substr(cBase64Chars, cBase64[i + 1]) - 1
            nVal3 = 0
            nVal4 = 0
            
            if cBase64[i + 2] != "="
                nVal3 = substr(cBase64Chars, cBase64[i + 2]) - 1
            ok
            
            if cBase64[i + 3] != "="
                nVal4 = substr(cBase64Chars, cBase64[i + 3]) - 1
            ok
            
            nCombined = (nVal1 << 18) | (nVal2 << 12) | (nVal3 << 6) | nVal4
            
            cResult += char((nCombined >> 16) & 255)
            
            if cBase64[i + 2] != "="
                cResult += char((nCombined >> 8) & 255)
            ok
            
            if cBase64[i + 3] != "="
                cResult += char(nCombined & 255)
            ok
        next
        
        return cResult
    
    /*
    تحويل نص إلى ترميز URL
    المدخلات: cText - النص المراد ترميزه
    المخرجات: النص مرمز لـ URL
    */
    func urlEncode cText
        cResult = ""
        for i = 1 to len(cText)
            cChar = cText[i]
            if isalnum(cChar) or cChar = "-" or cChar = "_" or cChar = "." or cChar = "~"
                cResult += cChar
            but cChar = " "
                cResult += "+"
            else
                cResult += "%" + upper(str2hex(cChar))
            ok
        next
        return cResult
    
    /*
    فك ترميز URL إلى نص
    المدخلات: cURL - النص المرمز لـ URL
    المخرجات: النص الأصلي
    */
    func urlDecode cURL
        cResult = ""
        i = 1
        while i <= len(cURL)
            cChar = cURL[i]
            if cChar = "+"
                cResult += " "
                i++
            but cChar = "%"
                if i + 2 <= len(cURL)
                    cHex = substr(cURL, i + 1, 2)
                    cResult += hex2str(cHex)
                    i += 3
                else
                    cResult += cChar
                    i++
                ok
            else
                cResult += cChar
                i++
            ok
        end
        return cResult
    
    /*
    تحويل نص إلى ترميز HTML
    المدخلات: cText - النص المراد ترميزه
    المخرجات: النص مرمز لـ HTML
    */
    func htmlEncode cText
        cResult = cText
        cResult = substr(cResult, "&", "&amp;")
        cResult = substr(cResult, "<", "&lt;")
        cResult = substr(cResult, ">", "&gt;")
        cResult = substr(cResult, '"', "&quot;")
        cResult = substr(cResult, "'", "&#39;")
        return cResult
    
    /*
    فك ترميز HTML إلى نص
    المدخلات: cHTML - النص المرمز لـ HTML
    المخرجات: النص الأصلي
    */
    func htmlDecode cHTML
        cResult = cHTML
        cResult = substr(cResult, "&lt;", "<")
        cResult = substr(cResult, "&gt;", ">")
        cResult = substr(cResult, "&quot;", '"')
        cResult = substr(cResult, "&#39;", "'")
        cResult = substr(cResult, "&amp;", "&")  # يجب أن يكون الأخير
        return cResult
    
    /*
    توليد نص عشوائي
    المدخلات: nLength - طول النص المطلوب
    المخرجات: نص عشوائي
    */
    func generateRandomString nLength
        cChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        cResult = ""
        for i = 1 to nLength
            nRand = random(len(cChars)) + 1
            cResult += cChars[nRand]
        next
        return cResult
    
    /*
    توليد كلمة مرور عشوائية قوية
    المدخلات: nLength - طول كلمة المرور
    المخرجات: كلمة مرور قوية
    */
    func generateStrongPassword nLength
        cLower = "abcdefghijklmnopqrstuvwxyz"
        cUpper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        cNumbers = "0123456789"
        cSpecial = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        cAllChars = cLower + cUpper + cNumbers + cSpecial
        
        cResult = ""
        
        # ضمان وجود حرف واحد على الأقل من كل نوع
        if nLength >= 4
            cResult += cLower[random(len(cLower)) + 1]
            cResult += cUpper[random(len(cUpper)) + 1]
            cResult += cNumbers[random(len(cNumbers)) + 1]
            cResult += cSpecial[random(len(cSpecial)) + 1]
            
            # إكمال باقي الطول
            for i = 5 to nLength
                nRand = random(len(cAllChars)) + 1
                cResult += cAllChars[nRand]
            next
        else
            # إذا كان الطول أقل من 4، استخدم جميع الأحرف
            for i = 1 to nLength
                nRand = random(len(cAllChars)) + 1
                cResult += cAllChars[nRand]
            next
        ok
        
        return cResult
    
    /*
    تحويل عنوان IP من نص إلى رقم
    المدخلات: cIP - عنوان IP كنص (مثل: "***********")
    المخرجات: عنوان IP كرقم
    */
    func ipToNumber cIP
        aParts = split(cIP, ".")
        if len(aParts) != 4
            return 0
        ok
        
        nResult = 0
        for i = 1 to 4
            nPart = number(aParts[i])
            if nPart < 0 or nPart > 255
                return 0
            ok
            nResult = (nResult << 8) + nPart
        next
        
        return nResult
    
    /*
    تحويل عنوان IP من رقم إلى نص
    المدخلات: nIP - عنوان IP كرقم
    المخرجات: عنوان IP كنص
    */
    func numberToIP nIP
        nPart1 = (nIP >> 24) & 255
        nPart2 = (nIP >> 16) & 255
        nPart3 = (nIP >> 8) & 255
        nPart4 = nIP & 255
        
        return string(nPart1) + "." + string(nPart2) + "." + 
               string(nPart3) + "." + string(nPart4)
    
    /*
    التحقق من صحة عنوان IP
    المدخلات: cIP - عنوان IP للتحقق منه
    المخرجات: true إذا كان صحيحاً، false إذا لم يكن كذلك
    */
    func isValidIP cIP
        aParts = split(cIP, ".")
        if len(aParts) != 4
            return false
        ok
        
        for cPart in aParts
            if not isdigit(cPart)
                return false
            ok
            nPart = number(cPart)
            if nPart < 0 or nPart > 255
                return false
            ok
        next
        
        return true
    
    /*
    حساب hash MD5 لنص
    المدخلات: cText - النص المراد حساب hash له
    المخرجات: MD5 hash كنص hex
    */
    func getMD5Hash cText
        return md5(cText)
    
    /*
    حساب hash SHA256 لنص
    المدخلات: cText - النص المراد حساب hash له
    المخرجات: SHA256 hash كنص hex
    */
    func getSHA256Hash cText
        return sha256(cText)



import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/admin/activities
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // جلب النشاطات مع التصفح
    const [activities, total] = await Promise.all([
      prisma.activity.findMany({
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          user: {
            select: {
              username: true,
              profile: {
                select: {
                  name: true
                }
              }
            }
          }
        }
      }),
      prisma.activity.count()
    ]);

    // تنسيق النشاطات
    const formattedActivities = activities.map(activity => ({
      id: activity.id,
      type: activity.type || 'GENERAL',
      description: activity.description || 'نشاط غير محدد',
      date: activity.createdAt,
      user: activity.user?.profile?.name || activity.user?.username || 'مستخدم غير معروف'
    }));

    return NextResponse.json({
      activities: formattedActivities,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        current: page,
        limit
      }
    });
  } catch (error) {
    console.error('Error fetching activities:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب النشاطات' },
      { status: 500 }
    );
  }
}

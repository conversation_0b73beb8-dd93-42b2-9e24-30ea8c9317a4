'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { FaGraduationCap, FaSearch, FaEdit, FaTrash, FaSync, FaPlus, FaSave, FaTimes } from 'react-icons/fa';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';

interface ProgramFeature {
  id?: number;
  text: string;
  programId?: number;
  order?: number;
}

interface Program {
  id: number;
  title: string;
  description: string;
  iconName: string;
  price: string;
  popular: boolean;
  features: ProgramFeature[];
  createdAt: string;
  updatedAt: string;
}

export default function ProgramsAdminPage() {
  const [programs, setPrograms] = useState<Program[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    iconName: '',
    price: '',
    popular: false,
    features: ['']
  });

  // قائمة الأيقونات المتاحة
  const availableIcons = [
    { name: 'FaQuran', label: 'قرآن' },
    { name: 'FaBookReader', label: 'قارئ' },
    { name: 'FaMicrophone', label: 'ميكروفون' },
    { name: 'FaGraduationCap', label: 'تخرج' }
  ];

  useEffect(() => {
    fetchPrograms();
  }, []);

  const fetchPrograms = async () => {
    try {
      const response = await fetch('/api/programs');
      if (!response.ok) throw new Error('Failed to fetch programs');
      const data = await response.json();
      setPrograms(data);
    } catch (error) {
      console.error('Error fetching programs:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب بيانات البرامج',
        variant: 'destructive'
      });
    }
  };

  const handleAdd = async () => {
    try {
      // التحقق من وجود ميزة واحدة على الأقل
      if (formData.features.length === 0 || formData.features.every(f => !f.trim())) {
        toast({
          title: 'خطأ',
          description: 'يجب إضافة ميزة واحدة على الأقل',
          variant: 'destructive'
        });
        return;
      }

      const response = await fetch('/api/programs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          // تصفية الميزات الفارغة
          features: formData.features.filter(feature => feature.trim() !== '')
        })
      });

      if (!response.ok) throw new Error('Failed to add program');

      await fetchPrograms();
      setIsAddDialogOpen(false);
      resetForm();
      toast({
        title: 'تم بنجاح',
        description: 'تم إضافة البرنامج بنجاح'
      });
    } catch (error) {
      console.error('Error adding program:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في إضافة البرنامج',
        variant: 'destructive'
      });
    }
  };

  const handleEdit = async () => {
    if (!selectedProgram) return;

    try {
      // التحقق من وجود ميزة واحدة على الأقل
      if (formData.features.length === 0 || formData.features.every(f => !f.trim())) {
        toast({
          title: 'خطأ',
          description: 'يجب إضافة ميزة واحدة على الأقل',
          variant: 'destructive'
        });
        return;
      }

      const response = await fetch('/api/programs', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          id: selectedProgram.id,
          // تصفية الميزات الفارغة
          features: formData.features.filter(feature => feature.trim() !== '')
        })
      });

      if (!response.ok) throw new Error('Failed to update program');

      await fetchPrograms();
      setIsEditDialogOpen(false);
      setSelectedProgram(null);
      resetForm();
      toast({
        title: 'تم بنجاح',
        description: 'تم تحديث البرنامج بنجاح'
      });
    } catch (error) {
      console.error('Error updating program:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في تحديث البرنامج',
        variant: 'destructive'
      });
    }
  };

  const handleDelete = async () => {
    if (!selectedProgram) return;

    try {
      const response = await fetch(`/api/programs?id=${selectedProgram.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete program');

      await fetchPrograms();
      setIsDeleteDialogOpen(false);
      setSelectedProgram(null);
      toast({
        title: 'تم بنجاح',
        description: 'تم حذف البرنامج بنجاح'
      });
    } catch (error) {
      console.error('Error deleting program:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في حذف البرنامج',
        variant: 'destructive'
      });
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      iconName: '',
      price: '',
      popular: false,
      features: ['']
    });
  };

  const addFeatureField = () => {
    setFormData({
      ...formData,
      features: [...formData.features, '']
    });
  };

  const removeFeatureField = (index: number) => {
    const newFeatures = [...formData.features];
    newFeatures.splice(index, 1);
    setFormData({
      ...formData,
      features: newFeatures.length ? newFeatures : [''] // دائمًا نحتفظ بحقل واحد على الأقل
    });
  };

  const updateFeature = (index: number, value: string) => {
    const newFeatures = [...formData.features];
    newFeatures[index] = value;
    setFormData({
      ...formData,
      features: newFeatures
    });
  };

  const filteredPrograms = programs.filter(program =>
    program.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    program.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <ProtectedRoute requiredPermission="admin.programs.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaGraduationCap className="text-[var(--primary-color)]" />
          إدارة البرامج التعليمية
        </h1>
        <div className="flex gap-2">
          <PermissionGuard requiredPermission="admin.programs.create">
            <Button
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full sm:w-auto shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
              onClick={() => setIsAddDialogOpen(true)}
            >
              <FaPlus className="ml-1" />
              إضافة برنامج جديد
            </Button>
          </PermissionGuard>
          <Button
            onClick={fetchPrograms}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2"
            title="تحديث البيانات"
          >
            <FaSync className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-4 bg-white p-4 rounded-lg shadow-md mb-6">
        <div className="relative w-full max-w-md">
          <Input
            type="text"
            placeholder="البحث عن برنامج..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--primary-color)]" />
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
        <Table>
          <TableHeader>
            <TableRow className="bg-[var(--primary-color)]">
              <TableHead className="text-right text-white font-bold">الرقم</TableHead>
              <TableHead className="text-right text-white font-bold">اسم البرنامج</TableHead>
              <TableHead className="text-right text-white font-bold">السعر</TableHead>
              <TableHead className="text-right text-white font-bold">شائع</TableHead>
              <TableHead className="text-right text-white font-bold">عدد الميزات</TableHead>
              <TableHead className="text-right text-white font-bold">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPrograms.map((program, index) => (
              <TableRow key={program.id}>
                <TableCell className="text-right">{index + 1}</TableCell>
                <TableCell className="text-right">{program.title}</TableCell>
                <TableCell className="text-right">{program.price}</TableCell>
                <TableCell className="text-right">{program.popular ? 'نعم' : 'لا'}</TableCell>
                <TableCell className="text-right">{program.features.length}</TableCell>
                <TableCell>
                  <div className="flex gap-2 justify-end">
                    <PermissionGuard requiredPermission="admin.programs.edit">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedProgram(program);
                          setFormData({
                            title: program.title,
                            description: program.description,
                            iconName: program.iconName,
                            price: program.price,
                            popular: program.popular,
                            features: program.features.map(f => f.text)
                          });
                          setIsEditDialogOpen(true);
                        }}
                        className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                      >
                        <FaEdit className="ml-1" />
                        تعديل
                      </Button>
                    </PermissionGuard>
                    <PermissionGuard requiredPermission="admin.programs.delete">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedProgram(program);
                          setIsDeleteDialogOpen(true);
                        }}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 flex items-center gap-1"
                      >
                        <FaTrash className="ml-1" />
                        حذف
                      </Button>
                    </PermissionGuard>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-[var(--primary-color)] font-bold text-xl flex items-center gap-2">
              <FaPlus className="text-[var(--primary-color)]" />
              إضافة برنامج جديد
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 max-h-[70vh] overflow-y-auto p-1">
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">اسم البرنامج</Label>
              <Input
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">وصف البرنامج</Label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">الأيقونة</Label>
              <select
                value={formData.iconName}
                onChange={(e) => setFormData({ ...formData, iconName: e.target.value })}
                className="w-full p-2 text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] rounded-md"
              >
                <option value="">اختر أيقونة</option>
                {availableIcons.map((icon) => (
                  <option key={icon.name} value={icon.name}>
                    {icon.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">السعر</Label>
              <Input
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                placeholder="مثال: 5000 د.ج / شهرياً"
              />
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="popular"
                checked={formData.popular}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, popular: checked as boolean })
                }
                className="border-[var(--primary-color)] data-[state=checked]:bg-[var(--primary-color)]"
              />
              <Label
                htmlFor="popular"
                className="text-right text-[var(--primary-color)] font-medium cursor-pointer"
              >
                برنامج شائع
              </Label>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label className="text-right block text-[var(--primary-color)] font-medium">ميزات البرنامج</Label>
                <Button
                  type="button"
                  onClick={addFeatureField}
                  variant="outline"
                  size="sm"
                  className="text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[#e0f2ef]"
                >
                  إضافة ميزة
                </Button>
              </div>
              <div className="space-y-2">
                {formData.features.map((feature, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={feature}
                      onChange={(e) => updateFeature(index, e.target.value)}
                      className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                      placeholder={`الميزة ${index + 1}`}
                    />
                    {formData.features.length > 1 && (
                      <Button
                        type="button"
                        onClick={() => removeFeatureField(index)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2"
                      >
                        <FaTimes />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter className="sm:justify-start">
            <Button
              type="submit"
              onClick={handleAdd}
              disabled={!formData.title || !formData.description || !formData.iconName || !formData.price}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
            >
              <FaPlus className="ml-1" />
              إضافة
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef] max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-[var(--primary-color)] font-bold text-xl flex items-center gap-2">
              <FaEdit className="text-[var(--primary-color)]" />
              تعديل البرنامج
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 max-h-[70vh] overflow-y-auto p-1">
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">اسم البرنامج</Label>
              <Input
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">وصف البرنامج</Label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">الأيقونة</Label>
              <select
                value={formData.iconName}
                onChange={(e) => setFormData({ ...formData, iconName: e.target.value })}
                className="w-full p-2 text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] rounded-md"
              >
                <option value="">اختر أيقونة</option>
                {availableIcons.map((icon) => (
                  <option key={icon.name} value={icon.name}>
                    {icon.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label className="text-right block text-[var(--primary-color)] font-medium">السعر</Label>
              <Input
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                placeholder="مثال: 5000 د.ج / شهرياً"
              />
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="popular-edit"
                checked={formData.popular}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, popular: checked as boolean })
                }
                className="border-[var(--primary-color)] data-[state=checked]:bg-[var(--primary-color)]"
              />
              <Label
                htmlFor="popular-edit"
                className="text-right text-[var(--primary-color)] font-medium cursor-pointer"
              >
                برنامج شائع
              </Label>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label className="text-right block text-[var(--primary-color)] font-medium">ميزات البرنامج</Label>
                <Button
                  type="button"
                  onClick={addFeatureField}
                  variant="outline"
                  size="sm"
                  className="text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[#e0f2ef]"
                >
                  إضافة ميزة
                </Button>
              </div>
              <div className="space-y-2">
                {formData.features.map((feature, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={feature}
                      onChange={(e) => updateFeature(index, e.target.value)}
                      className="text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                      placeholder={`الميزة ${index + 1}`}
                    />
                    {formData.features.length > 1 && (
                      <Button
                        type="button"
                        onClick={() => removeFeatureField(index)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2"
                      >
                        <FaTimes />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter className="sm:justify-start">
            <Button
              type="submit"
              onClick={handleEdit}
              disabled={!formData.title || !formData.description || !formData.iconName || !formData.price}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
            >
              <FaSave className="ml-1" />
              حفظ التغييرات
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] bg-[#fff8f8] border border-red-200">
          <DialogHeader>
            <DialogTitle className="text-red-600 font-bold text-xl flex items-center">
              <FaTrash className="ml-2" />
              تأكيد الحذف
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>هل أنت متأكد من رغبتك في حذف هذا البرنامج؟</p>
            {selectedProgram && (
              <div className="mt-4 p-4 bg-red-50 rounded-md border border-red-200">
                <p><strong>اسم البرنامج:</strong> {selectedProgram.title}</p>
                <p><strong>السعر:</strong> {selectedProgram.price}</p>
                <p className="text-red-600 text-sm mt-2">
                  سيتم حذف البرنامج وجميع ميزاته. هذا الإجراء لا يمكن التراجع عنه.
                </p>
              </div>
            )}
          </div>
          <DialogFooter className="sm:justify-start">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100 ml-2"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
            >
              <FaTrash className="ml-1" />
              تأكيد الحذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </ProtectedRoute>
  );
}

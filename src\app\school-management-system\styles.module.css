/* تأثيرات الحركة والتفاعل لصفحة نظام إدارة المدارس */

.heroSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.heroSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.featureCard {
  transition: all 0.3s ease;
  transform: translateY(0);
}

.featureCard:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.pricingCard {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pricingCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.pricingCard:hover::before {
  left: 100%;
}

.pricingCard:hover {
  transform: scale(1.05);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.popularBadge {
  background: linear-gradient(45deg, #667eea, #764ba2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.faqItem {
  transition: all 0.3s ease;
}

.faqItem:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.ctaSection {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
}

.animatedButton {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.animatedButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.animatedButton:hover::before {
  left: 100%;
}

.animatedButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.iconFloat {
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

.gradientText {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.slideInLeft {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slideInRight {
  animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fadeInUp {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.staggeredAnimation {
  animation-delay: var(--delay, 0s);
}

/* تأثيرات خاصة للموبايل */
@media (max-width: 768px) {
  .featureCard:hover {
    transform: none;
  }
  
  .pricingCard:hover {
    transform: none;
  }
  
  .animatedButton:hover {
    transform: none;
  }
}

/* تأثيرات التمرير */
.scrollReveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s ease;
}

.scrollReveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* تأثيرات الخلفية المتحركة */
.movingBackground {
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* تأثيرات النص المتحرك */
.typewriter {
  overflow: hidden;
  border-right: 3px solid;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: currentColor; }
}

/* تأثيرات الظلال المتحركة */
.shadowPulse {
  animation: shadowPulse 2s infinite;
}

@keyframes shadowPulse {
  0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
  100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

/* تأثيرات الأزرار المتقدمة */
.buttonGlow {
  position: relative;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 50px;
  color: white;
  cursor: pointer;
  font-weight: bold;
  overflow: hidden;
  transition: all 0.3s ease;
}

.buttonGlow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.buttonGlow:hover::before {
  left: 100%;
}

.buttonGlow:hover {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
  transform: translateY(-2px);
}

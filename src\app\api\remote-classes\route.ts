import { NextRequest, NextResponse } from "next/server";
import prisma from '@/lib/prisma';
import { getToken } from "@/utils/getToken";

// GET: جلب الفصول الافتراضية
export async function GET(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // استخراج المعلمات من URL
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '10');
        const page = parseInt(searchParams.get('page') || '1');
        const upcoming = searchParams.get('upcoming') === 'true';
        const past = searchParams.get('past') === 'true';
        const classeId = searchParams.get('classeId') ? parseInt(searchParams.get('classeId') || '0') : null;

        // بناء شروط البحث
        const where: Record<string, unknown> = {};

        // فلترة حسب الفصل
        if (classeId) {
            where.classeId = classeId;
        }

        // فلترة حسب الوقت (قادم أو سابق)
        if (upcoming) {
            where.startTime = { gte: new Date() };
        } else if (past) {
            where.endTime = { lt: new Date() };
        }

        // فلترة حسب دور المستخدم
        if (userData.role === 'TEACHER') {
            // المعلم يرى الفصول التي يدرسها
            where.instructorId = userData.id;
        } else if (userData.role === 'STUDENT') {
            // الطالب يرى جميع الفصول الافتراضية
            // لأن الطلاب يحتاجون للوصول إلى جميع الفصول الافتراضية
            // لا نقوم بأي فلترة هنا
        } else if (userData.role === 'PARENT') {
            // ولي الأمر يرى جميع الفصول الافتراضية لمتابعة أبنائه
            // لا نقوم بأي فلترة هنا
        }
        // المسؤول يرى جميع الفصول

        // جلب إجمالي عدد الفصول
        const total = await prisma.remoteClass.count({ where });

        // جلب الفصول
        const remoteClasses = await prisma.remoteClass.findMany({
            where,
            orderBy: {
                startTime: 'asc'
            },
            include: {
                instructor: {
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                },
                classe: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                materials: true,
                attendees: {
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                }
            },
            skip: (page - 1) * limit,
            take: limit
        });

        return NextResponse.json({
            remoteClasses,
            pagination: {
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching remote classes:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء جلب الفصول الافتراضية" },
            { status: 500 }
        );
    }
}

// POST: إنشاء فصل افتراضي جديد
export async function POST(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'TEACHER')) {
            return NextResponse.json(
                { message: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
                { status: 401 }
            );
        }

        const body = await request.json();

        // التحقق من البيانات المطلوبة
        if (!body.title || !body.startTime || !body.endTime || !body.meetingLink || !body.platform) {
            return NextResponse.json(
                { message: "البيانات غير مكتملة" },
                { status: 400 }
            );
        }

        // التحقق من أن وقت البداية قبل وقت النهاية
        const startTime = new Date(body.startTime);
        const endTime = new Date(body.endTime);

        if (startTime >= endTime) {
            return NextResponse.json(
                { message: "يجب أن يكون وقت البداية قبل وقت النهاية" },
                { status: 400 }
            );
        }

        // التحقق من وجود الفصل
        if (body.classeId) {
            const classe = await prisma.classe.findUnique({
                where: { id: body.classeId }
            });

            if (!classe) {
                return NextResponse.json(
                    { message: "الفصل غير موجود" },
                    { status: 400 }
                );
            }
        }

        // إنشاء الفصل الافتراضي
        const remoteClass = await prisma.remoteClass.create({
            data: {
                title: body.title,
                description: body.description || '',
                startTime,
                endTime,
                meetingLink: body.meetingLink,
                meetingId: body.meetingId || '',
                meetingPassword: body.meetingPassword || '',
                platform: body.platform,
                instructorId: userData.role === 'TEACHER' ? userData.id : parseInt(String(body.instructorId)),
                classeId: body.classeId ? parseInt(String(body.classeId)) : null,
                recordingUrl: body.recordingUrl || '',
                // الحقول الجديدة
                isScreenShareEnabled: body.isScreenShareEnabled === undefined ? true : body.isScreenShareEnabled,
                isWhiteboardEnabled: body.isWhiteboardEnabled === undefined ? true : body.isWhiteboardEnabled,
                videoQuality: body.videoQuality || 'medium',
                audioQuality: body.audioQuality || 'medium',
                attendees: body.attendeeIds?.length ? {
                    connect: body.attendeeIds.map((id: number) => ({ id }))
                } : undefined
            },
            include: {
                instructor: {
                    select: {
                        id: true,
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                },
                classe: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            }
        });

        // إنشاء إشعار للمعلم بالفصل الجديد
        if (userData.id) {
            await prisma.notification.create({
                data: {
                    title: `فصل افتراضي جديد: ${body.title}`,
                    content: `تم إنشاء فصل افتراضي جديد بعنوان "${body.title}" وسيبدأ في ${startTime.toLocaleString('ar-EG')}`,
                    type: 'REMOTE_CLASS',
                    userId: userData.id,
                    relatedId: remoteClass.id,
                    link: `/remote-classes/${remoteClass.id}`
                }
            });
        }

        return NextResponse.json(remoteClass, { status: 201 });
    } catch (error) {
        console.error('Error creating remote class:', error);
        return NextResponse.json(
            { message: "حدث خطأ أثناء إنشاء الفصل الافتراضي", error: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    }
}

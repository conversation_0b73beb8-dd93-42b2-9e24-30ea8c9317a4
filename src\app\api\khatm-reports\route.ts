import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/khatm-reports - الحصول على تقارير الإنجاز
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const studentId = searchParams.get('studentId');

    // بناء شروط البحث
    const where: {
      id?: number;
      studentId?: number;
    } = {};
    
    if (id) {
      where.id = parseInt(id);
    }
    
    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    const reports = await prisma.khatmAchievementReport.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: reports,
      message: 'تم جلب تقارير الإنجاز بنجاح'
    });
  } catch (error) {
    console.error('Error fetching achievement reports:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب تقارير الإنجاز'
    }, { status: 500 });
  }
}

// DELETE /api/khatm-reports - حذف تقرير إنجاز
export async function DELETE(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف التقرير مطلوب'
      }, { status: 400 });
    }

    // التحقق من وجود التقرير
    const existingReport = await prisma.khatmAchievementReport.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingReport) {
      return NextResponse.json({
        success: false,
        error: 'تقرير الإنجاز غير موجود'
      }, { status: 404 });
    }

    // حذف التقرير
    await prisma.khatmAchievementReport.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف تقرير الإنجاز بنجاح'
    });
  } catch (error) {
    console.error('Error deleting achievement report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف تقرير الإنجاز'
    }, { status: 500 });
  }
}

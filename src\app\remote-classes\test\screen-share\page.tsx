'use client';

import React from 'react';
import ScreenShareTest from '@/components/remote-classes/ScreenShare/ScreenShareTest';
import { FaArrowRight, FaVideo } from 'react-icons/fa';
import Link from 'next/link';

/**
 * Test page for screen sharing functionality
 */
const ScreenShareTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/remote-classes"
            className="inline-flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] mb-4"
          >
            <FaArrowRight className="ml-1" />
            <span>العودة إلى الفصول الافتراضية</span>
          </Link>
          
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaVideo className="text-[var(--primary-color)]" />
            اختبار مشاركة الشاشة
          </h1>
          <p className="text-gray-600 mr-4">
            هذه الصفحة مخصصة لاختبار وظائف مشاركة الشاشة في الفصول الافتراضية
          </p>
        </div>
        
        {/* Test Component */}
        <ScreenShareTest />
        
        {/* Performance Tips */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4 text-[var(--primary-color)]">نصائح لتحسين أداء مشاركة الشاشة</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h3 className="font-medium text-lg mb-2">تحسين استخدام النطاق الترددي</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                <li>شارك نافذة محددة بدلاً من الشاشة بأكملها</li>
                <li>قلل من حركة العناصر على الشاشة المشتركة</li>
                <li>تجنب مشاركة محتوى فيديو عالي الحركة</li>
                <li>استخدم خلفية بسيطة وثابتة</li>
              </ul>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h3 className="font-medium text-lg mb-2">تحسين جودة العرض</h3>
              <ul className="list-disc list-inside space-y-1 text-gray-700">
                <li>استخدم اتصال إنترنت مستقر وسريع</li>
                <li>أغلق التطبيقات غير الضرورية لتوفير موارد النظام</li>
                <li>استخدم دقة شاشة معتدلة (1080p أو أقل)</li>
                <li>قلل معدل تحديث الشاشة إذا كان مرتفعاً جداً</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScreenShareTestPage;

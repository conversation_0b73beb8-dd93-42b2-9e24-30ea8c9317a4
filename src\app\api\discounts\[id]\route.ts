import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { ActivityLogger, ActivityType } from '@/lib/activity-logger';
import { DiscountType } from '@prisma/client';

// GET /api/discounts/[id] - جلب خصم محدد
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الخصم مطلوب' },
        { status: 400 }
      );
    }

    const discount = await prisma.discount.findUnique({
      where: { id: parseInt(id) }
    });

    if (!discount) {
      return NextResponse.json(
        { error: 'الخصم غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(discount);
  } catch (error) {
    console.error('Error fetching discount:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الخصم' },
      { status: 500 }
    );
  }
}

// PUT /api/discounts/[id] - تحديث خصم محدد
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الخصم مطلوب' },
        { status: 400 }
      );
    }

    const body = await req.json();
    const {
      name,
      description,
      type,
      value,
      isActive,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      maxUsage
    } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !type || value === undefined) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    // التحقق من نوع الخصم
    if (!Object.values(DiscountType).includes(type as DiscountType)) {
      return NextResponse.json(
        { error: 'نوع الخصم غير صحيح' },
        { status: 400 }
      );
    }

    // التحقق من قيمة الخصم
    if (typeof value !== 'number' || value <= 0) {
      return NextResponse.json(
        { error: 'قيمة الخصم غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من النسبة المئوية (يجب أن تكون بين 0 و 100)
    if (type === DiscountType.PERCENTAGE && (value <= 0 || value > 100)) {
      return NextResponse.json(
        { error: 'النسبة المئوية يجب أن تكون بين 0 و 100' },
        { status: 400 }
      );
    }

    // التحقق من وجود الخصم
    const existingDiscount = await prisma.discount.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingDiscount) {
      return NextResponse.json(
        { error: 'الخصم غير موجود' },
        { status: 404 }
      );
    }

    // تحديث الخصم
    const updatedDiscount = await prisma.discount.update({
      where: { id: parseInt(id) },
      data: {
        name,
        description,
        type: type as DiscountType,
        value,
        isActive: isActive ?? existingDiscount.isActive,
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        minAmount: minAmount !== undefined ? minAmount : existingDiscount.minAmount,
        maxAmount: maxAmount !== undefined ? maxAmount : existingDiscount.maxAmount,
        maxUsage: maxUsage !== undefined ? maxUsage : existingDiscount.maxUsage
      }
    });

    // تسجيل النشاط
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.DISCOUNT,
          `تم تحديث الخصم: ${updatedDiscount.name}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط تحديث الخصم:', error);
      // لا نريد أن يفشل تحديث الخصم إذا فشل تسجيل النشاط
    }

    return NextResponse.json(updatedDiscount);
  } catch (error) {
    console.error('Error updating discount:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الخصم' },
      { status: 500 }
    );
  }
}

// DELETE /api/discounts/[id] - حذف خصم محدد
export async function DELETE(
  _req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    if (!id) {
      return NextResponse.json(
        { error: 'معرف الخصم مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الخصم
    const discount = await prisma.discount.findUnique({
      where: { id: parseInt(id) }
    });

    if (!discount) {
      return NextResponse.json(
        { error: 'الخصم غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من استخدام الخصم في الفواتير أو المدفوعات
    const invoicesCount = await prisma.invoice.count({
      where: { discountId: parseInt(id) }
    });

    const paymentsCount = await prisma.payment.count({
      where: { discountId: parseInt(id) }
    });

    if (invoicesCount > 0 || paymentsCount > 0) {
      // بدلاً من الحذف، قم بتعطيل الخصم
      const updatedDiscount = await prisma.discount.update({
        where: { id: parseInt(id) },
        data: { isActive: false }
      });

      // تسجيل نشاط تعطيل الخصم
      try {
        const adminUser = await prisma.user.findFirst({
          where: { role: 'ADMIN' }
        });

        if (adminUser) {
          await ActivityLogger.log(
            adminUser.id,
            ActivityType.DISCOUNT,
            `تم تعطيل الخصم: ${discount.name} (لا يمكن حذفه لارتباطه بفواتير أو مدفوعات)`
          );
        }
      } catch (error) {
        console.error('خطأ في تسجيل نشاط تعطيل الخصم:', error);
      }

      return NextResponse.json({
        message: 'تم تعطيل الخصم بدلاً من حذفه لارتباطه بفواتير أو مدفوعات',
        discount: updatedDiscount
      });
    }

    // حذف الخصم
    await prisma.discount.delete({
      where: { id: parseInt(id) }
    });

    // تسجيل نشاط حذف الخصم
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.DISCOUNT,
          `تم حذف الخصم: ${discount.name}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط حذف الخصم:', error);
    }

    return NextResponse.json({
      message: 'تم حذف الخصم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting discount:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الخصم' },
      { status: 500 }
    );
  }
}

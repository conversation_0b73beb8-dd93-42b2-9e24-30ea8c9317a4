import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/budgets/alerts - الحصول على تنبيهات تجاوز حدود الميزانية
export async function GET() {
  try {
    // الحصول على إعدادات تنبيهات الميزانية من إعدادات النظام
    const budgetAlertSettings = await prisma.systemSettings.findUnique({
      where: { key: 'BUDGET_ALERTS' },
    });

    // إعدادات التنبيهات الافتراضية
    const settings = budgetAlertSettings
      ? JSON.parse(budgetAlertSettings.value)
      : {
          warningThreshold: 90,
          criticalThreshold: 100,
          enableNotifications: true,
        };

    // الحصول على الميزانيات النشطة
    const activeBudgets = await prisma.budget.findMany({
      where: {
        status: 'ACTIVE',
      },
      include: {
        items: {
          include: {
            category: true,
          },
        },
      },
    });

    if (activeBudgets.length === 0) {
      return NextResponse.json({
        alerts: [],
        totalAlerts: 0,
      });
    }

    // تجميع تنبيهات الميزانية
    const budgetAlerts = [];

    for (const budget of activeBudgets) {
      // حساب إجمالي المصروفات الفعلية للميزانية
      const totalExpenses = await prisma.expense.aggregate({
        where: {
          date: {
            gte: budget.startDate,
            lte: budget.endDate,
          },
        },
        _sum: {
          amount: true,
        },
      });

      const totalActualExpenses = totalExpenses._sum.amount || 0;
      const totalBudgetAmount = budget.totalAmount;
      const usagePercentage = (totalActualExpenses / totalBudgetAmount) * 100;

      // إضافة تنبيه للميزانية الإجمالية إذا تجاوزت الحد
      if (usagePercentage >= settings.warningThreshold) {
        budgetAlerts.push({
          type: 'BUDGET_TOTAL',
          budgetId: budget.id,
          budgetName: budget.name,
          totalAmount: totalBudgetAmount,
          actualAmount: totalActualExpenses,
          remainingAmount: totalBudgetAmount - totalActualExpenses,
          usagePercentage,
          severity: usagePercentage >= settings.criticalThreshold ? 'CRITICAL' : 'WARNING',
          message: usagePercentage >= settings.criticalThreshold
            ? `تم تجاوز الميزانية "${budget.name}" بنسبة ${(usagePercentage - 100).toFixed(1)}%`
            : `الميزانية "${budget.name}" وصلت إلى ${usagePercentage.toFixed(1)}% من الحد المسموح`,
          date: new Date(),
        });
      }

      // فحص كل بند من بنود الميزانية
      for (const item of budget.items) {
        const categoryExpenses = await prisma.expense.aggregate({
          where: {
            date: {
              gte: budget.startDate,
              lte: budget.endDate,
            },
            categoryId: item.categoryId,
          },
          _sum: {
            amount: true,
          },
        });

        const actualAmount = categoryExpenses._sum.amount || 0;
        const itemUsagePercentage = (actualAmount / item.amount) * 100;

        // إضافة تنبيه لبند الميزانية إذا تجاوز الحد
        if (itemUsagePercentage >= settings.warningThreshold) {
          budgetAlerts.push({
            type: 'BUDGET_ITEM',
            budgetId: budget.id,
            budgetName: budget.name,
            itemId: item.id,
            categoryId: item.categoryId,
            categoryName: item.category.name,
            budgetAmount: item.amount,
            actualAmount,
            remainingAmount: item.amount - actualAmount,
            usagePercentage: itemUsagePercentage,
            severity: itemUsagePercentage >= settings.criticalThreshold ? 'CRITICAL' : 'WARNING',
            message: itemUsagePercentage >= settings.criticalThreshold
              ? `تم تجاوز بند "${item.category.name}" في الميزانية "${budget.name}" بنسبة ${(itemUsagePercentage - 100).toFixed(1)}%`
              : `بند "${item.category.name}" في الميزانية "${budget.name}" وصل إلى ${itemUsagePercentage.toFixed(1)}% من الحد المسموح`,
            date: new Date(),
          });
        }
      }
    }

    // ترتيب التنبيهات حسب الأهمية ثم حسب نسبة الاستخدام
    budgetAlerts.sort((a, b) => {
      if (a.severity === b.severity) {
        return b.usagePercentage - a.usagePercentage;
      }
      return a.severity === 'CRITICAL' ? -1 : 1;
    });

    return NextResponse.json({
      alerts: budgetAlerts,
      totalAlerts: budgetAlerts.length,
    });
  } catch (error) {
    console.error('خطأ في جلب تنبيهات الميزانية:', error);
    return NextResponse.json(
      { error: 'فشل في جلب تنبيهات الميزانية' },
      { status: 500 }
    );
  }
}

// POST /api/budgets/alerts/settings - تحديث إعدادات التنبيهات
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { warningThreshold, criticalThreshold, enableNotifications, notificationEmail } = body;

    // التحقق من صحة البيانات
    if (
      typeof warningThreshold !== 'number' ||
      typeof criticalThreshold !== 'number' ||
      typeof enableNotifications !== 'boolean'
    ) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة' },
        { status: 400 }
      );
    }

    // تحديث إعدادات التنبيهات في قاعدة البيانات
    // (يمكن إنشاء جدول خاص بإعدادات النظام)
    const settings = await prisma.systemSettings.upsert({
      where: { key: 'BUDGET_ALERTS' },
      update: {
        value: JSON.stringify({
          warningThreshold,
          criticalThreshold,
          enableNotifications,
          notificationEmail: notificationEmail || null,
        }),
      },
      create: {
        key: 'BUDGET_ALERTS',
        value: JSON.stringify({
          warningThreshold,
          criticalThreshold,
          enableNotifications,
          notificationEmail: notificationEmail || null,
        }),
      },
    });

    return NextResponse.json({
      success: true,
      settings: JSON.parse(settings.value),
    });
  } catch (error) {
    console.error('خطأ في تحديث إعدادات التنبيهات:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث إعدادات التنبيهات' },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';

// 🚀 مخزن الخلفيات في الذاكرة للوصول السريع
const backgroundsCache = new Map<string, PageBackgroundData>();
const cacheTimestamps = new Map<string, number>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 دقائق

// 💾 تخزين محلي للخلفيات
const STORAGE_KEY = 'pageBackgrounds';
const STORAGE_TIMESTAMP_KEY = 'pageBackgroundsTimestamp';

interface PageBackgroundData {
  id: number;
  pageName: string;
  displayName: string;
  imageUrl?: string;
  overlayColor?: string;
  overlayOpacity: number;
  position: string;
  size: string;
  repeat: string;
  attachment: string;
  isActive: boolean;
  priority: number;
}

interface UsePageBackgroundReturn {
  background: PageBackgroundData | null;
  loading: boolean;
  error: string | null;
  backgroundStyles: React.CSSProperties;
  overlayStyles: React.CSSProperties;
  hasBackground: boolean;
}

export const usePageBackground = (pageName: string): UsePageBackgroundReturn => {
  const [background, setBackground] = useState<PageBackgroundData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBackground = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`/api/page-backgrounds/${pageName}`);
        if (response.data.success) {
          setBackground(response.data.data);
        } else {
          setBackground(null);
        }
      } catch (err: any) {
        if (err.response?.status === 404) {
          // لا توجد خلفية لهذه الصفحة - هذا أمر طبيعي
          setBackground(null);
          setError(null);
        } else {
          setError('فشل في تحميل خلفية الصفحة');
          console.error('Error fetching page background:', err);
        }
      } finally {
        setLoading(false);
      }
    };

    if (pageName) {
      fetchBackground();
    }
  }, [pageName]);

  // إنشاء أنماط الخلفية
  const backgroundStyles: React.CSSProperties = (() => {
    if (!background || !background.imageUrl) {
      return {};
    }

    return {
      backgroundImage: `url(${background.imageUrl})`,
      backgroundPosition: background.position,
      backgroundSize: background.size,
      backgroundRepeat: background.repeat,
      backgroundAttachment: background.attachment,
      position: 'relative'
    };
  })();

  // إنشاء أنماط الطبقة العلوية
  const overlayStyles: React.CSSProperties = (() => {
    if (!background || !background.overlayColor) {
      return { display: 'none' };
    }

    // تحويل لون hex إلى rgba
    const hexToRgba = (hex: string, opacity: number): string => {
      const r = parseInt(hex.slice(1, 3), 16);
      const g = parseInt(hex.slice(3, 5), 16);
      const b = parseInt(hex.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    };

    return {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: background.overlayColor.startsWith('#')
        ? hexToRgba(background.overlayColor, background.overlayOpacity)
        : background.overlayColor,
      pointerEvents: 'none',
      zIndex: 1
    };
  })();

  return {
    background,
    loading,
    error,
    backgroundStyles,
    overlayStyles,
    hasBackground: !!(background && background.imageUrl)
  };
};

export default usePageBackground;

// 🗑️ تصدير دالة لمسح الكاش (للاستخدام عند تحديث الخلفيات)
export const clearBackgroundsCache = () => {
  // مسح التخزين المحلي
  if (typeof window !== 'undefined') {
    localStorage.removeItem('pageBackgrounds');
    localStorage.removeItem('pageBackgroundsTimestamp');
  }

  console.log('🗑️ تم مسح كاش خلفيات الصفحات');
};

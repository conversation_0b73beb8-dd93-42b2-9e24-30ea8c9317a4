/*
  أنماط متجاوبة للجداول
  يحتوي هذا الملف على أنماط CSS لجعل الجداول متجاوبة على الأجهزة المحمولة
*/

/* أنماط أساسية لجميع الجداول المتجاوبة */
.responsive-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  margin-bottom: 1rem;
  border-radius: 0.5rem;
}

/* مؤشر التمرير الأفقي */
.responsive-table-container::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 5px;
  background: linear-gradient(to right, rgba(0,0,0,0), rgba(22, 155, 136, 0.1));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}

.responsive-table-container.has-overflow::after {
  opacity: 1;
}

/* أنماط للجداول على الأجهزة المحمولة */
@media (max-width: 768px) {
  /* وضع البطاقة للجداول على الأجهزة المحمولة */
  .card-mode-table {
    display: block;
    width: 100%;
  }

  .card-mode-table thead {
    display: none;
  }

  .card-mode-table tbody {
    display: block;
    width: 100%;
  }

  .card-mode-table tr {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    background-color: white;
    padding: 0.5rem;
  }

  .card-mode-table td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: right;
    padding: 0.5rem;
    border-bottom: 1px solid #f0f0f0;
  }

  .card-mode-table td:last-child {
    border-bottom: none;
  }

  .card-mode-table td::before {
    content: attr(data-label);
    font-weight: bold;
    margin-right: auto;
    color: #666;
  }

  /* تعديلات لأزرار الإجراءات في وضع البطاقة */
  .card-mode-table td.actions {
    justify-content: center;
    padding-top: 0.75rem;
  }

  .card-mode-table td.actions::before {
    display: none;
  }

  /* تحسين مظهر الصفوف في وضع البطاقة */
  .card-mode-table tr:nth-child(odd) {
    background-color: #fafafa;
  }

  .card-mode-table tr:hover {
    background-color: #f5f5f5;
  }
}

/* أنماط للجداول المضغوطة على الأجهزة المحمولة */
@media (max-width: 768px) {
  .compact-table th,
  .compact-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.875rem;
  }

  /* إخفاء الأعمدة الأقل أهمية على الأجهزة المحمولة */
  .hide-on-mobile {
    display: none !important;
  }
}

/* أنماط للجداول مع تمرير أفقي */
.scroll-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
}

/* أنماط للجداول مع أعمدة ثابتة */
@media (max-width: 768px) {
  .fixed-column-table {
    table-layout: fixed;
  }

  .fixed-column-table th:first-child,
  .fixed-column-table td:first-child {
    position: sticky;
    left: 0;
    background-color: white;
    z-index: 1;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  }
}

/* أنماط لأزرار الإجراءات في الجداول على الأجهزة المحمولة */
@media (max-width: 768px) {
  .mobile-action-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
  }

  .mobile-action-buttons button {
    flex: 1 1 auto;
    min-width: 80px;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
  }
}

/* تحسينات إضافية للجداول على الأجهزة المحمولة */
@media (max-width: 768px) {
  /* تحسين مظهر الجداول داخل البطاقات */
  .card .responsive-table-container {
    margin: -1rem;
    width: calc(100% + 2rem);
  }

  /* تحسين مظهر الجداول الفارغة */
  .responsive-table-container:empty {
    display: none;
  }

  /* تحسين مظهر رسائل الحالة الفارغة */
  .empty-table-message {
    text-align: center;
    padding: 2rem 1rem;
    color: #666;
  }

  /* تحسين مظهر أزرار التنقل في الجداول */
  .table-pagination {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .table-pagination button {
    min-width: 2.5rem;
  }
}

'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Loader2, FileText, Printer, Download, Filter, Users, TrendingUp, Award } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import { exportExamReportToExcel } from '@/lib/exportUtils';
import { getGradeLevelByScore, calculatePercentage, getPassStatus } from '@/lib/grading-system';

type ExamReport = {
  id: number;
  evaluationType: string;
  month: string;
  description: string;
  maxPoints: number;
  passingPoints: number;
  subject: string;
  examType: string;
  statistics: {
    totalStudents: number;
    passedStudents: number;
    failedStudents: number;
    excellentStudents: number;
    averageGrade: number;
    highestGrade: number;
    lowestGrade: number;
    passRate: number;
  };
  studentsByClass: Record<string, any[]>;
  createdAt: string;
};

type ReportSummary = {
  totalExams: number;
  totalStudentEntries: number;
  totalPassedEntries: number;
  overallPassRate: number;
  overallAverage: number;
  reportGeneratedAt: string;
  filters: {
    month?: string;
    evaluationType?: string;
    classeId?: string;
    subjectId?: string;
  };
};

export default function ExamReportPage() {
  const [reportData, setReportData] = useState<ExamReport[]>([]);
  const [summary, setSummary] = useState<ReportSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    month: '',
    evaluationType: '',
    classeId: '',
    subjectId: ''
  });
  const [classes, setClasses] = useState<{id: number, name: string}[]>([]);
  const [subjects, setSubjects] = useState<{id: number, name: string}[]>([]);
  const [evaluationTypes] = useState([
    { id: 'QURAN_MEMORIZATION', name: 'حفظ القرآن' },
    { id: 'QURAN_RECITATION', name: 'تلاوة القرآن' },
    { id: 'ISLAMIC_STUDIES', name: 'الدراسات الإسلامية' },
    { id: 'ARABIC_LANGUAGE', name: 'اللغة العربية' },
    { id: 'GENERAL_KNOWLEDGE', name: 'المعرفة العامة' },
    { id: 'BEHAVIOR_EVALUATION', name: 'تقييم السلوك' },
    { id: 'ATTENDANCE_EVALUATION', name: 'تقييم الحضور' },
    { id: 'PARTICIPATION_EVALUATION', name: 'تقييم المشاركة' }
  ]);

  useEffect(() => {
    fetchClasses();
    fetchSubjects();
  }, []);

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (response.ok) {
        const data = await response.json();
        setClasses(Array.isArray(data) ? data : data.data || []);
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
    }
  };

  const fetchSubjects = async () => {
    try {
      const response = await fetch('/api/subjects');
      if (response.ok) {
        const data = await response.json();
        setSubjects(Array.isArray(data) ? data : data.data || []);
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
    }
  };

  const generateReport = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams();
      if (filters.month) params.append('month', filters.month);
      if (filters.evaluationType) params.append('evaluationType', filters.evaluationType);
      if (filters.classeId) params.append('classeId', filters.classeId);
      if (filters.subjectId) params.append('subjectId', filters.subjectId);

      const response = await fetch(`/api/evaluation/exam-report?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('فشل في إنشاء التقرير');
      }

      const data = await response.json();
      
      if (data.success) {
        setReportData(data.data.exams);
        setSummary(data.data.summary);
        toast.success('تم إنشاء التقرير بنجاح');
      } else {
        throw new Error(data.message || 'فشل في إنشاء التقرير');
      }
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('حدث خطأ أثناء إنشاء التقرير');
    } finally {
      setLoading(false);
    }
  };

  const printReport = () => {
    // إعداد الطباعة المحسنة
    const printStyles = `
      <style>
        @media print {
          @page {
            margin: 0.5cm !important;
            size: A4 portrait !important;
          }

          body {
            font-family: Arial, sans-serif !important;
            font-size: 11px !important;
            line-height: 1.2 !important;
            margin: 0 !important;
            padding: 0 !important;
          }

          .no-print {
            display: none !important;
          }

          .container {
            max-width: none !important;
            margin: 0 !important;
            padding: 0.5rem !important;
          }

          .space-y-6 > * + * {
            margin-top: 0.5rem !important;
          }

          .grid {
            display: block !important;
          }

          .grid > * {
            margin-bottom: 0.25rem !important;
          }

          table {
            font-size: 9px !important;
            width: 100% !important;
            border-collapse: collapse !important;
          }

          th, td {
            padding: 2px 4px !important;
            font-size: 8px !important;
            border: 1px solid #ccc !important;
          }

          h1, h2, h3, h4 {
            font-size: 12px !important;
            margin: 0.2rem 0 !important;
          }

          .text-3xl {
            font-size: 14px !important;
          }

          .text-2xl {
            font-size: 12px !important;
          }

          .text-lg {
            font-size: 10px !important;
          }

          .p-4, .p-6, .p-8 {
            padding: 0.25rem !important;
          }

          .mb-6, .mt-6 {
            margin: 0.25rem 0 !important;
          }

          .mb-4, .mt-4 {
            margin: 0.15rem 0 !important;
          }

          .mb-3, .mt-3 {
            margin: 0.1rem 0 !important;
          }

          .gap-4 {
            gap: 0.25rem !important;
          }

          .break-inside-avoid {
            break-inside: avoid !important;
          }

          .card {
            border: 1px solid #ddd !important;
            margin-bottom: 0.25rem !important;
            padding: 0.25rem !important;
          }
        }
      </style>
    `;

    // إضافة الستايل للصفحة
    const existingStyle = document.getElementById('exam-report-print-styles');
    if (existingStyle) existingStyle.remove();

    const styleElement = document.createElement('style');
    styleElement.id = 'exam-report-print-styles';
    styleElement.innerHTML = printStyles;
    document.head.appendChild(styleElement);

    // طباعة
    setTimeout(() => {
      window.print();
    }, 100);
  };

  const getEvaluationTypeLabel = (type: string) => {
    const found = evaluationTypes.find(et => et.id === type);
    return found ? found.name : type;
  };

  const getPerformanceBadge = (grade: number, maxPoints: number) => {
    const gradeLevel = getGradeLevelByScore(grade, maxPoints);

    return (
      <Badge
        className="text-xs font-semibold"
        style={{
          backgroundColor: gradeLevel.color + '20',
          color: gradeLevel.color,
          border: `1px solid ${gradeLevel.color}40`
        }}
      >
        {gradeLevel.nameAr}
      </Badge>
    );
  };

  return (
    <ProtectedRoute requiredPermission="admin.evaluation.reports.view">
      <div className="container mx-auto p-4 space-y-6">
        <div className="flex justify-between items-center no-print">
          <div>
            <h1 className="text-3xl font-bold text-[var(--primary-color)]">📊 كشف الامتحانات الشامل</h1>
            <p className="text-gray-600 mt-2">تقرير مفصل لنتائج الامتحانات وإحصائيات الأداء</p>
          </div>
          <div className="flex gap-2">
            {reportData.length > 0 && (
              <>
                <Button onClick={printReport} className="bg-green-600 hover:bg-green-700">
                  <Printer className="ml-2 h-4 w-4" />
                  طباعة محسنة
                </Button>
                <Button onClick={() => {
                  try {
                    exportExamReportToExcel(reportData, summary, filters);
                    toast.success('تم تصدير التقرير إلى Excel بنجاح');
                  } catch (error) {
                    console.error('Error exporting to Excel:', error);
                    toast.error('حدث خطأ أثناء تصدير التقرير');
                  }
                }} className="bg-green-600 hover:bg-green-700">
                  <Download className="ml-2 h-4 w-4" />
                  تصدير Excel
                </Button>
                <Button onClick={() => {
                  const dataStr = JSON.stringify({
                    reportData,
                    summary,
                    filters,
                    exportedAt: new Date().toISOString()
                  }, null, 2);
                  const dataBlob = new Blob([dataStr], {type: 'application/json'});
                  const url = URL.createObjectURL(dataBlob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `exam-report-${new Date().toISOString().split('T')[0]}.json`;
                  link.click();
                  toast.success('تم تصدير البيانات بصيغة JSON');
                }} variant="outline">
                  <Download className="ml-2 h-4 w-4" />
                  تصدير JSON
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Print Header - يظهر فقط عند الطباعة */}
        <div className="hidden print:block">
          <div className="text-center border-b-2 border-black pb-4 mb-6">
            <h1 className="text-2xl font-bold">الجمهورية الجزائرية الديمقراطية الشعبية</h1>
            <h2 className="text-xl font-semibold mt-2">وزارة التربية الوطنية</h2>
            <h3 className="text-lg mt-2">مديرية التربية - مدرسة القرآن الكريم</h3>
            <h4 className="text-xl font-bold mt-4 text-blue-600">كشف الامتحانات الشامل</h4>
            <p className="text-sm mt-2">السنة الدراسية: {new Date().getFullYear()}-{new Date().getFullYear() + 1}</p>
            <p className="text-xs mt-1">تاريخ التقرير: {new Date().toLocaleDateString('fr-FR')}</p>
          </div>
        </div>

        {/* Filters */}
        <Card className="no-print">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50">
            <CardTitle className="flex items-center text-blue-700">
              <Filter className="ml-2 h-5 w-5" />
              🔍 فلاتر التقرير المتقدمة
            </CardTitle>
            <CardDescription>حدد المعايير المطلوبة لإنشاء تقرير مخصص ومفصل</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">الشهر</label>
                <Input
                  type="month"
                  value={filters.month}
                  onChange={(e) => setFilters({...filters, month: e.target.value})}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">نوع التقييم</label>
                <Select value={filters.evaluationType} onValueChange={(value) => setFilters({...filters, evaluationType: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الأنواع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الأنواع</SelectItem>
                    {evaluationTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الفصل</label>
                <Select value={filters.classeId} onValueChange={(value) => setFilters({...filters, classeId: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع الفصول" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع الفصول</SelectItem>
                    {classes.map(classe => (
                      <SelectItem key={classe.id} value={classe.id.toString()}>{classe.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">المادة</label>
                <Select value={filters.subjectId} onValueChange={(value) => setFilters({...filters, subjectId: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="جميع المواد" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">جميع المواد</SelectItem>
                    {subjects.map(subject => (
                      <SelectItem key={subject.id} value={subject.id.toString()}>{subject.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="mt-4">
              <Button onClick={generateReport} disabled={loading}>
                {loading ? (
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                ) : (
                  <FileText className="ml-2 h-4 w-4" />
                )}
                إنشاء التقرير
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Summary */}
        {summary && (
          <div className="print:break-inside-avoid">
            <h2 className="text-xl font-bold text-gray-800 mb-4 print:text-lg">📈 الإحصائيات العامة</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card className="border-l-4 border-l-blue-500 hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <div className="bg-blue-100 p-3 rounded-full">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="mr-4">
                      <p className="text-2xl font-bold text-blue-600">{summary.totalExams}</p>
                      <p className="text-sm text-gray-600">إجمالي الامتحانات</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-500 hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <div className="bg-green-100 p-3 rounded-full">
                      <Users className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="mr-4">
                      <p className="text-2xl font-bold text-green-600">{summary.totalStudentEntries}</p>
                      <p className="text-sm text-gray-600">إجمالي المشاركات</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-purple-500 hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <div className="bg-purple-100 p-3 rounded-full">
                      <TrendingUp className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="mr-4">
                      <p className="text-2xl font-bold text-purple-600">{summary.overallPassRate}%</p>
                      <p className="text-sm text-gray-600">معدل النجاح العام</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-yellow-500 hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <div className="bg-yellow-100 p-3 rounded-full">
                      <Award className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="mr-4">
                      <p className="text-2xl font-bold text-yellow-600">{summary.overallAverage}</p>
                      <p className="text-sm text-gray-600">المتوسط العام</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* معلومات إضافية */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg print:bg-white print:border">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-semibold text-gray-700">الطلاب الناجحون:</span>
                  <span className="text-green-600 font-bold mr-2">{summary.totalPassedEntries}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">الطلاب الراسبون:</span>
                  <span className="text-red-600 font-bold mr-2">{summary.totalStudentEntries - summary.totalPassedEntries}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">تاريخ التقرير:</span>
                  <span className="text-blue-600 font-bold mr-2">{new Date(summary.reportGeneratedAt).toLocaleDateString('fr-FR')}</span>
                </div>
                <div>
                  <span className="font-semibold text-gray-700">المعايير المطبقة:</span>
                  <span className="text-purple-600 font-bold mr-2">
                    {Object.values(summary.filters).filter(Boolean).length || 'جميع البيانات'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Report Data */}
        {reportData.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4 print:text-lg">📋 تفاصيل الامتحانات</h2>
            {reportData.map((exam, examIndex) => (
              <Card key={exam.id} className="print:break-inside-avoid border-l-4 border-l-indigo-500 hover:shadow-lg transition-shadow">
                <CardHeader className="bg-gradient-to-r from-indigo-50 to-blue-50 print:bg-white">
                  <CardTitle className="flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-sm font-bold mr-3">
                        {examIndex + 1}
                      </span>
                      <span className="text-indigo-700">
                        {getEvaluationTypeLabel(exam.evaluationType)} - {exam.month}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800">{exam.examType}</Badge>
                      <Badge variant="outline" className="bg-green-100 text-green-800">
                        {exam.maxPoints} نقطة
                      </Badge>
                    </div>
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    📝 {exam.description}
                    <span className="block mt-1 text-xs text-gray-500">
                      النقاط المطلوبة للنجاح: {exam.passingPoints} من {exam.maxPoints}
                    </span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Statistics */}
                  <div className="bg-gray-50 p-4 rounded-lg mb-6 print:bg-white print:border">
                    <h4 className="font-semibold text-gray-700 mb-3">📊 إحصائيات الامتحان</h4>
                    <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                      <div className="text-center bg-white p-3 rounded-lg shadow-sm print:shadow-none print:border">
                        <p className="text-xl font-bold text-blue-600">{exam.statistics.totalStudents}</p>
                        <p className="text-xs text-gray-600">إجمالي الطلاب</p>
                      </div>
                      <div className="text-center bg-white p-3 rounded-lg shadow-sm print:shadow-none print:border">
                        <p className="text-xl font-bold text-green-600">{exam.statistics.passedStudents}</p>
                        <p className="text-xs text-gray-600">ناجح</p>
                      </div>
                      <div className="text-center bg-white p-3 rounded-lg shadow-sm print:shadow-none print:border">
                        <p className="text-xl font-bold text-red-600">{exam.statistics.failedStudents}</p>
                        <p className="text-xs text-gray-600">راسب</p>
                      </div>
                      <div className="text-center bg-white p-3 rounded-lg shadow-sm print:shadow-none print:border">
                        <p className="text-xl font-bold text-purple-600">{exam.statistics.excellentStudents}</p>
                        <p className="text-xs text-gray-600">ممتاز</p>
                      </div>
                      <div className="text-center bg-white p-3 rounded-lg shadow-sm print:shadow-none print:border">
                        <p className="text-xl font-bold text-indigo-600">{exam.statistics.passRate}%</p>
                        <p className="text-xs text-gray-600">معدل النجاح</p>
                      </div>
                      <div className="text-center bg-white p-3 rounded-lg shadow-sm print:shadow-none print:border">
                        <p className="text-xl font-bold text-yellow-600">{exam.statistics.averageGrade}</p>
                        <p className="text-xs text-gray-600">المتوسط</p>
                      </div>
                    </div>

                    {/* معلومات إضافية */}
                    <div className="mt-3 pt-3 border-t border-gray-200 grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">أعلى درجة:</span>
                        <span className="font-bold text-green-600 mr-2">{exam.statistics.highestGrade}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">أقل درجة:</span>
                        <span className="font-bold text-red-600 mr-2">{exam.statistics.lowestGrade}</span>
                      </div>
                    </div>
                  </div>

                  {/* Students by Class */}
                  {Object.entries(exam.studentsByClass).map(([className, students], classIndex) => (
                    <div key={className} className="mb-6">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-lg font-semibold text-indigo-700 flex items-center">
                          <span className="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full text-sm font-bold mr-2">
                            {classIndex + 1}
                          </span>
                          🏫 فصل {className}
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm mr-2">
                            {students.length} طالب
                          </span>
                        </h4>
                        <div className="text-sm text-gray-600">
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded mr-1">
                            ناجح: {students.filter(s => s.status === 'نجح').length}
                          </span>
                          <span className="bg-red-100 text-red-800 px-2 py-1 rounded">
                            راسب: {students.filter(s => s.status === 'راسب').length}
                          </span>
                        </div>
                      </div>

                      <div className="overflow-x-auto">
                        <Table className="border border-gray-200">
                          <TableHeader>
                            <TableRow className="bg-gray-50">
                              <TableHead className="text-right font-bold border-r">الترتيب</TableHead>
                              <TableHead className="text-right font-bold border-r">اسم الطالب</TableHead>
                              <TableHead className="text-right font-bold border-r">الدرجة</TableHead>
                              <TableHead className="text-right font-bold border-r">النسبة المئوية</TableHead>
                              <TableHead className="text-right font-bold border-r">الحالة</TableHead>
                              <TableHead className="text-right font-bold">التقدير</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {students.map((student, index) => (
                              <TableRow key={student.id} className={`hover:bg-gray-50 ${
                                student.status === 'نجح' ? 'bg-green-50' : 'bg-red-50'
                              }`}>
                                <TableCell className="border-r font-bold">
                                  <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold ${
                                    index < 3 ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                                  }`}>
                                    {index + 1}
                                  </span>
                                </TableCell>
                                <TableCell className="border-r font-semibold">{student.name}</TableCell>
                                <TableCell className="border-r">
                                  <span className={`font-bold ${
                                    student.grade >= exam.passingPoints ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {student.grade}
                                  </span>
                                  <span className="text-gray-500">/{exam.maxPoints}</span>
                                </TableCell>
                                <TableCell className="border-r">
                                  <span className={`font-semibold ${
                                    (student.grade / exam.maxPoints) * 100 >= 80 ? 'text-green-600' :
                                    (student.grade / exam.maxPoints) * 100 >= 60 ? 'text-blue-600' :
                                    (student.grade / exam.maxPoints) * 100 >= 50 ? 'text-yellow-600' : 'text-red-600'
                                  }`}>
                                    {Math.round((student.grade / exam.maxPoints) * 100)}%
                                  </span>
                                </TableCell>
                                <TableCell className="border-r">
                                  <Badge variant={student.status === 'نجح' ? 'default' : 'destructive'} className="text-xs">
                                    {student.status === 'نجح' ? '✅ نجح' : '❌ راسب'}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {getPerformanceBadge(student.grade, exam.maxPoints)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>

                      {/* إحصائيات الفصل */}
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg print:bg-white print:border">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">متوسط الفصل:</span>
                            <span className="font-bold text-blue-600 mr-2">
                              {(students.reduce((sum, s) => sum + s.grade, 0) / students.length).toFixed(1)}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">معدل النجاح:</span>
                            <span className="font-bold text-green-600 mr-2">
                              {Math.round((students.filter(s => s.status === 'نجح').length / students.length) * 100)}%
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">أعلى درجة:</span>
                            <span className="font-bold text-green-600 mr-2">
                              {Math.max(...students.map(s => s.grade))}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">أقل درجة:</span>
                            <span className="font-bold text-red-600 mr-2">
                              {Math.min(...students.map(s => s.grade))}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {reportData.length === 0 && !loading && (
          <Card className="border-2 border-dashed border-gray-300">
            <CardContent className="text-center p-12">
              <div className="flex flex-col items-center">
                <div className="bg-gray-100 p-6 rounded-full mb-6">
                  <FileText className="h-16 w-16 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">لا توجد بيانات امتحانات</h3>
                <p className="text-gray-500 mb-4 max-w-md">
                  لم يتم العثور على امتحانات تطابق المعايير المحددة.
                  قم بتعديل الفلاتر أو تأكد من وجود امتحانات مسجلة في النظام.
                </p>
                <div className="flex flex-col sm:flex-row gap-2 text-sm text-gray-400">
                  <span>💡 نصائح:</span>
                  <span>• جرب إزالة بعض الفلاتر</span>
                  <span>• تأكد من وجود امتحانات في الشهر المحدد</span>
                  <span>• تحقق من صحة البيانات المدخلة</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <style jsx global>{`
        @media print {
          .print\\:break-inside-avoid {
            break-inside: avoid !important;
          }

          .print\\:bg-white {
            background-color: white !important;
          }

          .print\\:border {
            border: 1px solid #ccc !important;
          }

          .print\\:shadow-none {
            box-shadow: none !important;
          }

          .print\\:text-lg {
            font-size: 14px !important;
          }

          body {
            print-color-adjust: exact !important;
            -webkit-print-color-adjust: exact !important;
            font-family: Arial, sans-serif !important;
          }

          .no-print {
            display: none !important;
          }

          .hidden {
            display: none !important;
          }

          .print\\:block {
            display: block !important;
          }

          /* تحسين الألوان للطباعة */
          .bg-gradient-to-r {
            background: #f8f9fa !important;
          }

          .border-l-4 {
            border-left: 3px solid #6366f1 !important;
          }

          .hover\\:shadow-lg {
            box-shadow: none !important;
          }

          .transition-shadow {
            transition: none !important;
          }

          /* تحسين الجداول */
          table {
            border-collapse: collapse !important;
            width: 100% !important;
          }

          th, td {
            border: 1px solid #ccc !important;
            padding: 4px 6px !important;
          }

          /* تحسين البطاقات */
          .rounded-lg, .rounded-full {
            border-radius: 4px !important;
          }

          /* تحسين الألوان */
          .text-blue-600, .text-blue-700, .text-blue-800 {
            color: #1e40af !important;
          }

          .text-green-600, .text-green-800 {
            color: #059669 !important;
          }

          .text-red-600, .text-red-800 {
            color: #dc2626 !important;
          }

          .text-purple-600, .text-purple-700 {
            color: #7c3aed !important;
          }

          .text-yellow-600, .text-yellow-800 {
            color: #d97706 !important;
          }

          .text-indigo-600, .text-indigo-700, .text-indigo-800 {
            color: #4f46e5 !important;
          }

          .bg-green-50, .bg-green-100 {
            background-color: #f0fdf4 !important;
          }

          .bg-red-50, .bg-red-100 {
            background-color: #fef2f2 !important;
          }

          .bg-blue-50, .bg-blue-100 {
            background-color: #eff6ff !important;
          }

          .bg-yellow-50, .bg-yellow-100 {
            background-color: #fefce8 !important;
          }

          .bg-purple-50, .bg-purple-100 {
            background-color: #faf5ff !important;
          }

          .bg-indigo-50, .bg-indigo-100 {
            background-color: #eef2ff !important;
          }

          .bg-gray-50, .bg-gray-100 {
            background-color: #f9fafb !important;
          }
        }
      `}</style>
    </ProtectedRoute>
  );
}

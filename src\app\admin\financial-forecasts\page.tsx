'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function FinancialForecastsRedirect() {
  const router = useRouter();

  useEffect(() => {
    // توجيه المستخدم إلى الصفحة الصحيحة
    router.replace('/admin/treasury/forecasts');
  }, [router]);

  return (
    <div className="flex justify-center items-center h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)] mx-auto"></div>
        <p className="mt-4 text-[var(--primary-color)]">جاري التوجيه إلى صفحة التنبؤات المالية...</p>
      </div>
    </div>
  );
}

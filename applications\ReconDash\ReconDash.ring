/*
==============================================================================
    ReconDash - لوحة التحكم الاستطلاعية
    
    الوصف: لوحة تحكم استطلاعية متكاملة بواجهة رسومية لجمع المعلومات
    المؤلف: Praetorian Team
    المكتبات: libui.ring, Praetorian.ring, RingThreadPro
==============================================================================
*/

# تحميل المكتبات المطلوبة
load "../../praetorian.ring"

# محاولة تحميل libui
try
    load "libui.ring"
catch
    ? "خطأ: لم يتم العثور على libui.ring"
    ? "يرجى تثبيت مكتبة libui للواجهة الرسومية"
    return
done

# محاولة تحميل RingThreadPro
try
    load "threads.ring"
catch
    ? "تحذير: لم يتم العثور على threads.ring - سيتم العمل بدون تعدد"
done

/*
==============================================================================
    دوال مساعدة عامة
==============================================================================
*/

/*
دالة iif - بديل للعامل الثلاثي
*/
func iif bCondition, vTrueValue, vFalseValue
    if bCondition
        return vTrueValue
    else
        return vFalseValue
    ok

/*
==============================================================================
    متغيرات التطبيق العامة
==============================================================================
*/

# عناصر الواجهة الرسومية
oWindow = NULL
oTargetEntry = NULL
oScanButton = NULL
oTabGroup = NULL
oOverviewText = NULL
oPortsTable = NULL
oSSLText = NULL
oCrawlerText = NULL
oStatusLabel = NULL

# بيانات التطبيق
cCurrentTarget = ""
bScanInProgress = false
oPraetorian = NULL

# نتائج الفحص
aScanResults = [
    :target = "",
    :ip_address = "",
    :is_active = false,
    :open_ports = [],
    :ssl_info = [],
    :crawler_results = []
]

/*
==============================================================================
    دوال مساعدة للواجهة
==============================================================================
*/

/*
تحديث شريط الحالة
*/
func updateStatus cMessage
    if oStatusLabel != NULL
        uiLabelSetText(oStatusLabel, cMessage)
        uiControlShow(oStatusLabel)
    ok

/*
تنظيف النتائج
*/
func clearResults
    aScanResults = [
        :target = "",
        :ip_address = "",
        :is_active = false,
        :open_ports = [],
        :ssl_info = [],
        :crawler_results = []
    ]
    
    # تنظيف عناصر الواجهة
    if oOverviewText != NULL
        uiMultilineEntrySetText(oOverviewText, "")
    ok
    
    if oSSLText != NULL
        uiMultilineEntrySetText(oSSLText, "")
    ok
    
    if oCrawlerText != NULL
        uiMultilineEntrySetText(oCrawlerText, "")
    ok

/*
تحديث لسان النظرة العامة
*/
func updateOverviewTab
    cOverview = "=== نظرة عامة على الهدف ===" + nl + nl
    cOverview += "الهدف: " + aScanResults[:target] + nl
    cOverview += "عنوان IP: " + aScanResults[:ip_address] + nl
    cOverview += "الحالة: " + iif(aScanResults[:is_active], "نشط", "غير نشط") + nl
    cOverview += "المنافذ المفتوحة: " + len(aScanResults[:open_ports]) + nl
    cOverview += "تاريخ الفحص: " + date() + " " + time() + nl + nl
    
    if len(aScanResults[:open_ports]) > 0
        cOverview += "قائمة المنافذ المفتوحة:" + nl
        for aPort in aScanResults[:open_ports]
            cOverview += "  - المنفذ " + aPort[:port] + " (" + aPort[:service] + ")" + nl
        next
    else
        cOverview += "لم يتم العثور على منافذ مفتوحة." + nl
    ok
    
    if oOverviewText != NULL
        uiMultilineEntrySetText(oOverviewText, cOverview)
    ok

/*
تحديث لسان SSL
*/
func updateSSLTab
    cSSLInfo = "=== معلومات SSL/TLS ===" + nl + nl
    
    if len(aScanResults[:ssl_info]) = 0
        cSSLInfo += "لا توجد خدمات SSL/TLS متاحة." + nl
    else
        for aSSL in aScanResults[:ssl_info]
            cSSLInfo += "المنفذ " + aSSL[:port] + ":" + nl
            cSSLInfo += "  الحالة: " + iif(aSSL[:available], "متاح", "غير متاح") + nl
            
            if aSSL[:available] and len(aSSL[:certificate]) > 0
                aCert = aSSL[:certificate]
                cSSLInfo += "  الموضوع: " + aCert[:subject] + nl
                cSSLInfo += "  المصدر: " + aCert[:issuer] + nl
                cSSLInfo += "  صالح حتى: " + aCert[:valid_to] + nl
                cSSLInfo += "  الأيام المتبقية: " + aCert[:days_until_expiry] + nl
                
                if aCert[:is_expired]
                    cSSLInfo += "  ⚠ تحذير: الشهادة منتهية الصلاحية!" + nl
                ok
                
                if len(aSSL[:security_issues]) > 0
                    cSSLInfo += "  المشاكل الأمنية:" + nl
                    for cIssue in aSSL[:security_issues]
                        cSSLInfo += "    - " + cIssue + nl
                    next
                ok
            ok
            
            cSSLInfo += nl
        next
    ok
    
    if oSSLText != NULL
        uiMultilineEntrySetText(oSSLText, cSSLInfo)
    ok

/*
تحديث لسان الزاحف
*/
func updateCrawlerTab
    cCrawlerInfo = "=== نتائج الزحف ===" + nl + nl
    
    if len(aScanResults[:crawler_results]) = 0
        cCrawlerInfo += "لم يتم تشغيل الزاحف أو لم يتم العثور على نتائج." + nl
    else
        aCrawler = aScanResults[:crawler_results]
        cCrawlerInfo += "الصفحات المزحوفة: " + aCrawler[:total_pages] + nl
        cCrawlerInfo += "الروابط المكتشفة: " + aCrawler[:total_links] + nl
        cCrawlerInfo += "النماذج المكتشفة: " + aCrawler[:total_forms] + nl + nl
        
        if len(aCrawler[:found_urls]) > 0
            cCrawlerInfo += "بعض الروابط المكتشفة:" + nl
            nCount = 0
            for cURL in aCrawler[:found_urls]
                if nCount >= 10 break ok  # عرض أول 10 روابط فقط
                cCrawlerInfo += "  - " + cURL + nl
                nCount++
            next
            
            if len(aCrawler[:found_urls]) > 10
                cCrawlerInfo += "  ... و " + (len(aCrawler[:found_urls]) - 10) + " رابط آخر" + nl
            ok
        ok
    ok
    
    if oCrawlerText != NULL
        uiMultilineEntrySetText(oCrawlerText, cCrawlerInfo)
    ok

/*
==============================================================================
    دوال الفحص الأساسية
==============================================================================
*/

/*
فحص أساسي للهدف
*/
func performBasicScan cTarget
    updateStatus("جاري فحص الهدف: " + cTarget)
    
    # تهيئة النتائج
    aScanResults[:target] = cTarget
    aScanResults[:ip_address] = "غير معروف"
    aScanResults[:is_active] = false
    
    # محاولة حل عنوان IP (مبسط)
    aScanResults[:ip_address] = cTarget  # في التطبيق الحقيقي ستحتاج لحل DNS
    
    # فحص المنافذ الشائعة
    updateStatus("فحص المنافذ الشائعة...")
    aOpenPorts = oPraetorian.Network.Scanner.scanCommonPorts(cTarget)
    
    if len(aOpenPorts) > 0
        aScanResults[:is_active] = true
        
        # جلب تفاصيل كل منفذ
        for nPort in aOpenPorts
            cBanner = oPraetorian.Network.Scanner.bannerGrab(cTarget, nPort)
            cService = oPraetorian.Network.Scanner.identifyService(nPort, cBanner)
            
            aPortInfo = [
                :port = nPort,
                :service = cService,
                :banner = cBanner,
                :status = "مفتوح"
            ]
            
            add(aScanResults[:open_ports], aPortInfo)
        next
    ok
    
    updateOverviewTab()

/*
فحص SSL للمنافذ الآمنة
*/
func performSSLScan cTarget
    updateStatus("فحص SSL/TLS...")
    
    # فحص المنافذ الآمنة الشائعة
    aSSLPorts = [443, 993, 995, 8443]
    
    for nPort in aSSLPorts
        # التحقق من وجود المنفذ في القائمة المفتوحة
        bPortOpen = false
        for aPortInfo in aScanResults[:open_ports]
            if aPortInfo[:port] = nPort
                bPortOpen = true
                exit
            ok
        next
        
        if bPortOpen
            aSSLReport = oPraetorian.Crypto.SSLChecker.comprehensiveSSLCheck(cTarget, nPort)
            add(aScanResults[:ssl_info], aSSLReport)
        ok
    next
    
    updateSSLTab()

/*
تشغيل الزاحف
*/
func performWebCrawl cTarget
    updateStatus("تشغيل زاحف الويب...")
    
    # التحقق من وجود منافذ ويب
    bWebAvailable = false
    cProtocol = "http"
    
    for aPortInfo in aScanResults[:open_ports]
        if aPortInfo[:port] = 80
            bWebAvailable = true
            cProtocol = "http"
            exit
        but aPortInfo[:port] = 443
            bWebAvailable = true
            cProtocol = "https"
            exit
        ok
    next
    
    if bWebAvailable
        cBaseURL = cProtocol + "://" + cTarget
        
        # تعيين إعدادات محدودة للزاحف
        oPraetorian.Web.Crawler.setMaxDepth(2)
        oPraetorian.Web.Crawler.setMaxPages(20)
        oPraetorian.Web.Crawler.setDelay(500)
        
        aCrawlerReport = oPraetorian.Web.Crawler.crawl(cBaseURL)
        aScanResults[:crawler_results] = aCrawlerReport
    ok
    
    updateCrawlerTab()

/*
==============================================================================
    معالجات الأحداث
==============================================================================
*/

/*
معالج زر بدء الفحص
*/
func onScanButtonClicked
    if bScanInProgress
        updateStatus("فحص قيد التشغيل بالفعل...")
        return
    ok
    
    cTarget = uiEntryText(oTargetEntry)
    if len(trim(cTarget)) = 0
        updateStatus("يرجى إدخال هدف صحيح")
        return
    ok
    
    # بدء الفحص
    bScanInProgress = true
    uiControlDisable(oScanButton)
    clearResults()
    
    # تشغيل الفحص (في التطبيق الحقيقي سيكون في خيط منفصل)
    performBasicScan(cTarget)
    performSSLScan(cTarget)
    performWebCrawl(cTarget)
    
    # انتهاء الفحص
    bScanInProgress = false
    uiControlEnable(oScanButton)
    updateStatus("تم الانتهاء من الفحص")

/*
معالج إغلاق النافذة
*/
func onWindowClosing
    uiQuit()
    return 0

/*
==============================================================================
    إنشاء الواجهة الرسومية
==============================================================================
*/

/*
إنشاء قسم الإدخال
*/
func createInputSection
    oInputBox = uiNewVerticalBox()
    uiBoxSetPadded(oInputBox, 1)
    
    # ملصق الهدف
    oTargetLabel = uiNewLabel("الهدف (Host or IP):")
    uiBoxAppend(oInputBox, oTargetLabel, 0)
    
    # حقل إدخال الهدف
    oTargetEntry = uiNewEntry()
    uiEntrySetText(oTargetEntry, "example.com")
    uiBoxAppend(oInputBox, oTargetEntry, 0)
    
    # زر بدء الفحص
    oScanButton = uiNewButton("بدء الفحص")
    uiButtonOnClicked(oScanButton, "onScanButtonClicked()")
    uiBoxAppend(oInputBox, oScanButton, 0)
    
    # شريط الحالة
    oStatusLabel = uiNewLabel("جاهز للفحص...")
    uiBoxAppend(oInputBox, oStatusLabel, 0)
    
    return oInputBox

/*
إنشاء ألسنة النتائج
*/
func createResultsTabs
    oTabGroup = uiNewTab()
    
    # لسان النظرة العامة
    oOverviewText = uiNewMultilineEntry()
    uiMultilineEntrySetReadOnly(oOverviewText, 1)
    uiTabAppend(oTabGroup, "نظرة عامة", oOverviewText)
    
    # لسان فحص المنافذ (سنستخدم نص متعدد الأسطر بدلاً من جدول للبساطة)
    oPortsTable = uiNewMultilineEntry()
    uiMultilineEntrySetReadOnly(oPortsTable, 1)
    uiTabAppend(oTabGroup, "فحص المنافذ", oPortsTable)
    
    # لسان SSL/TLS
    oSSLText = uiNewMultilineEntry()
    uiMultilineEntrySetReadOnly(oSSLText, 1)
    uiTabAppend(oTabGroup, "تحليل SSL/TLS", oSSLText)
    
    # لسان الزاحف
    oCrawlerText = uiNewMultilineEntry()
    uiMultilineEntrySetReadOnly(oCrawlerText, 1)
    uiTabAppend(oTabGroup, "الزاحف", oCrawlerText)
    
    return oTabGroup

/*
إنشاء النافذة الرئيسية
*/
func createMainWindow
    oWindow = uiNewWindow("ReconDash - لوحة التحكم الاستطلاعية", 800, 600, 1)
    uiWindowOnClosing(oWindow, "onWindowClosing()")
    
    # الحاوية الرئيسية
    oMainBox = uiNewVerticalBox()
    uiBoxSetPadded(oMainBox, 1)
    
    # قسم الإدخال
    oInputSection = createInputSection()
    uiBoxAppend(oMainBox, oInputSection, 0)
    
    # فاصل
    oSeparator = uiNewHorizontalSeparator()
    uiBoxAppend(oMainBox, oSeparator, 0)
    
    # ألسنة النتائج
    oResultsTabs = createResultsTabs()
    uiBoxAppend(oMainBox, oResultsTabs, 1)
    
    uiWindowSetChild(oWindow, oMainBox)
    
    return oWindow

/*
==============================================================================
    الدالة الرئيسية
==============================================================================
*/

func main
    # تهيئة مكتبة Praetorian
    oPraetorian = CreatePraetorian()
    
    # تهيئة libui
    oOptions = uiNewInitOptions()
    cError = uiInit(oOptions)
    if len(cError) > 0
        ? "خطأ في تهيئة libui: " + cError
        uiFreeInitError(cError)
        return
    ok
    
    # إنشاء النافذة الرئيسية
    oWindow = createMainWindow()
    
    # عرض النافذة
    uiControlShow(oWindow)
    
    # تشغيل حلقة الأحداث
    uiMain()
    
    # تنظيف الموارد
    uiUninit()



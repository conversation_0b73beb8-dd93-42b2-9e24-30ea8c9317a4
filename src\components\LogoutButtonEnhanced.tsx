'use client';

import { useState } from 'react';
import { useLogout } from '@/hooks/useLogout';
import { useUserPermissions } from '@/hooks/useUserPermissions';
import { FaSignOutAlt, FaSpinner } from 'react-icons/fa';

interface LogoutButtonProps {
  className?: string;
  showIcon?: boolean;
  showText?: boolean;
  variant?: 'button' | 'link' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  redirectTo?: string;
  onLogoutStart?: () => void;
  onLogoutComplete?: () => void;
}

/**
 * مكون تسجيل خروج محسن مع دعم تنظيف التخزين المؤقت
 */
export const LogoutButtonEnhanced: React.FC<LogoutButtonProps> = ({
  className = '',
  showIcon = true,
  showText = true,
  variant = 'button',
  size = 'md',
  redirectTo = '/',
  onLogoutStart,
  onLogoutComplete
}) => {
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const { logout } = useLogout();
  const { userId } = useUserPermissions();

  const handleLogout = async () => {
    if (isLoggingOut) return;

    try {
      setIsLoggingOut(true);
      onLogoutStart?.();

      await logout(userId || undefined, {
        redirectTo,
        showSuccessMessage: true,
        clearAllCache: true // مسح جميع البيانات للأمان
      });

      onLogoutComplete?.();
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      setIsLoggingOut(false);
    }
  };

  // تحديد الأنماط حسب المتغيرات
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2 text-base';
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'link':
        return 'text-red-600 hover:text-red-800 hover:underline';
      case 'icon':
        return 'p-2 rounded-full hover:bg-red-100 text-red-600';
      default:
        return 'bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors';
    }
  };

  const baseClasses = `
    inline-flex items-center justify-center gap-2 
    ${getSizeClasses()} 
    ${getVariantClasses()}
    ${isLoggingOut ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${className}
  `.trim();

  const content = (
    <>
      {showIcon && (
        isLoggingOut ? (
          <FaSpinner className="animate-spin" />
        ) : (
          <FaSignOutAlt />
        )
      )}
      {showText && (
        <span>
          {isLoggingOut ? 'جاري تسجيل الخروج...' : 'تسجيل الخروج'}
        </span>
      )}
    </>
  );

  if (variant === 'link') {
    return (
      <button
        onClick={handleLogout}
        disabled={isLoggingOut}
        className={baseClasses}
        title="تسجيل الخروج"
      >
        {content}
      </button>
    );
  }

  return (
    <button
      onClick={handleLogout}
      disabled={isLoggingOut}
      className={baseClasses}
      title="تسجيل الخروج"
    >
      {content}
    </button>
  );
};

/**
 * مكون تسجيل خروج مبسط للاستخدام السريع
 */
export const QuickLogoutButton: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => {
  return (
    <LogoutButtonEnhanced
      variant="icon"
      showText={false}
      size="sm"
      className={className}
    />
  );
};

/**
 * مكون تسجيل خروج للقوائم المنسدلة
 */
export const DropdownLogoutButton: React.FC<{ 
  className?: string;
  onClose?: () => void;
}> = ({ 
  className = '',
  onClose
}) => {
  return (
    <LogoutButtonEnhanced
      variant="link"
      size="sm"
      className={`w-full text-right ${className}`}
      onLogoutStart={onClose}
    />
  );
};

export default LogoutButtonEnhanced;

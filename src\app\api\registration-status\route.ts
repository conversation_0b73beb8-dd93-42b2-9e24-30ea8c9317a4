import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/registration-status - التحقق من حالة التسجيل
export async function GET() {
  try {
    // البحث عن إعدادات الموقع في قاعدة البيانات
    const siteSettings = await prisma.systemSettings.findUnique({
      where: { key: 'SITE_SETTINGS' },
    });

    // إذا لم تكن هناك إعدادات مخزنة، افترض أن التسجيل مفعل
    let registrationEnabled = true;

    if (siteSettings) {
      try {
        const settings = JSON.parse(siteSettings.value);
        // إذا كان هناك إعداد محدد للتسجيل، استخدمه
        if (typeof settings.registrationEnabled === 'boolean') {
          registrationEnabled = settings.registrationEnabled;
        }
      } catch (error) {
        console.error('خطأ في تحليل إعدادات الموقع:', error);
      }
    }

    return NextResponse.json({
      registrationEnabled,
      success: true,
    });
  } catch (error) {
    console.error('خطأ في التحقق من حالة التسجيل:', error);
    return NextResponse.json(
      { error: 'فشل في التحقق من حالة التسجيل', success: false, registrationEnabled: true },
      { status: 500 }
    );
  }
}

import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Download, Printer } from 'lucide-react';
import { CertificateData } from './index';
import html2canvas from 'html2canvas';

interface GraduationTemplateProps {
  certificateData: CertificateData;
  showControls?: boolean;
}

export default function GraduationTemplate({
  certificateData,
  showControls = true
}: GraduationTemplateProps) {
  const certificateRef = useRef<HTMLDivElement>(null);

  const handlePrint = async () => {
    if (!certificateRef.current) return;

    try {
      // فتح نافذة جديدة للطباعة
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert('يرجى السماح بالنوافذ المنبثقة لطباعة الشهادة');
        return;
      }

      // إنشاء محتوى HTML للشهادة
      const certificateHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <title>شهادة تخرج</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 20px;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: #f8f9fa;
              color: #333;
            }

            .certificate-container {
              max-width: 800px;
              margin: 0 auto;
              background-color: white;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              overflow: hidden;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 4px solid #d4af37;
              margin: 20px;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 50%, #f0f8ff 100%);
              z-index: -1;
            }

            .certificate-header-bar {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 80px;
              background: linear-gradient(90deg, #4169e1 0%, #5e7ce2 100%);
            }

            .certificate-footer-bar {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 80px;
              background: linear-gradient(90deg, #4169e1 0%, #5e7ce2 100%);
            }

            .certificate-header {
              text-align: center;
              margin-top: 60px;
              margin-bottom: 30px;
            }

            .certificate-icon {
              width: 100px;
              height: 100px;
              background-color: #d4af37;
              border-radius: 50%;
              margin: 0 auto 15px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 50px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 10px;
              border-bottom: 2px solid #d4af37;
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr 1fr;
              gap: 20px;
              margin-top: auto;
              margin-bottom: 60px;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #ddd;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 100px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #d4af37;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #d4af37;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 100px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }

            @media print {
              body {
                margin: 0;
                padding: 0;
                background: none;
              }

              .certificate-container {
                box-shadow: none;
                max-width: 100%;
              }

              .certificate {
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="certificate-container">
            <div class="certificate">
              <div class="certificate-bg"></div>
              <div class="certificate-header-bar"></div>
              <div class="certificate-footer-bar"></div>

              <!-- رأس الشهادة -->
              <div class="certificate-header">
                <div class="certificate-icon">🎓</div>
                <h1 class="certificate-title">شهادة تخرج</h1>
                <p class="certificate-subtitle">${certificateData.title}</p>
              </div>

              <!-- محتوى الشهادة -->
              <div class="certificate-content">
                <p class="certificate-intro">تشهد إدارة المدرسة أن الطالب:</p>
                <h2 class="student-name">${certificateData.student?.name || 'الطالب المتميز'}</h2>
                ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
                <p class="certificate-description">${certificateData.description}</p>
              </div>

              <!-- توقيع الشهادة -->
              <div class="certificate-footer">
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المدير</p>
                </div>
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المشرف</p>
                </div>
                <div class="signature-box">
                  <div class="signature-line"></div>
                  <p class="signature-title">توقيع المعلم</p>
                </div>
              </div>

              <!-- ختم المدرسة -->
              <div class="certificate-stamp">ختم المدرسة</div>

              <!-- تاريخ الإصدار -->
              ${certificateData.issueDate ? `
              <div class="certificate-date">
                تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
              </div>
              ` : ''}
            </div>
          </div>

          <script>
            window.onload = () => {
              window.print();
              window.onafterprint = () => window.close();
            };
          </script>
        </body>
        </html>
      `;

      // كتابة المحتوى إلى النافذة الجديدة
      printWindow.document.open();
      printWindow.document.write(certificateHTML);
      printWindow.document.close();
    } catch (error) {
      console.error('Error printing certificate:', error);
      alert('حدث خطأ أثناء طباعة الشهادة');
    }
  };

  const handleDownloadImage = async () => {
    try {
      // إنشاء عنصر div مؤقت لاحتواء الشهادة
      const tempDiv = document.createElement('div');
      document.body.appendChild(tempDiv);

      // إنشاء محتوى HTML للشهادة (نفس المحتوى المستخدم في الطباعة)
      const certificateHTML = `
        <div style="width: 800px; height: 1130px; position: relative;">
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap');

            body {
              margin: 0;
              padding: 0;
              font-family: 'Cairo', sans-serif;
              direction: rtl;
              background-color: white;
            }

            .certificate {
              position: relative;
              padding: 40px;
              min-height: 600px;
              border: 4px solid #d4af37;
              margin: 20px;
              background-color: white;
            }

            .certificate-bg {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 50%, #f0f8ff 100%);
              z-index: -1;
            }

            .certificate-header-bar {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 80px;
              background: linear-gradient(90deg, #4169e1 0%, #5e7ce2 100%);
            }

            .certificate-footer-bar {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 80px;
              background: linear-gradient(90deg, #4169e1 0%, #5e7ce2 100%);
            }

            .certificate-header {
              text-align: center;
              margin-top: 60px;
              margin-bottom: 30px;
            }

            .certificate-icon {
              width: 100px;
              height: 100px;
              background-color: #d4af37;
              border-radius: 50%;
              margin: 0 auto 15px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 50px;
            }

            .certificate-title {
              font-size: 32px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 5px;
              word-spacing: 0.25em;
            }

            .certificate-subtitle {
              font-size: 18px;
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-content {
              text-align: center;
              margin-bottom: 30px;
            }

            .certificate-intro {
              font-size: 18px;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .student-name {
              font-size: 28px;
              font-weight: bold;
              color: #d4af37;
              margin-bottom: 10px;
              border-bottom: 2px solid #d4af37;
              padding-bottom: 5px;
              display: inline-block;
              word-spacing: 0.25em;
            }

            .class-name {
              font-size: 18px;
              color: #666;
              margin-bottom: 20px;
              word-spacing: 0.25em;
            }

            .certificate-description {
              font-size: 18px;
              margin: 20px 0;
              line-height: 1.6;
              word-spacing: 0.25em;
            }

            .certificate-footer {
              display: grid;
              grid-template-columns: 1fr 1fr 1fr;
              gap: 20px;
              margin-top: auto;
              margin-bottom: 60px;
            }

            .signature-box {
              text-align: center;
            }

            .signature-line {
              height: 1px;
              background-color: #ddd;
              margin-bottom: 10px;
            }

            .signature-title {
              color: #666;
              word-spacing: 0.25em;
            }

            .certificate-stamp {
              position: absolute;
              bottom: 100px;
              left: 40px;
              width: 100px;
              height: 100px;
              border: 2px dashed #d4af37;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #d4af37;
              opacity: 0.7;
              word-spacing: 0.25em;
            }

            .certificate-date {
              position: absolute;
              bottom: 100px;
              right: 40px;
              font-size: 14px;
              color: #666;
              word-spacing: 0.25em;
            }
          </style>

          <div class="certificate">
            <div class="certificate-bg"></div>
            <div class="certificate-header-bar"></div>
            <div class="certificate-footer-bar"></div>

            <!-- رأس الشهادة -->
            <div class="certificate-header">
              <div class="certificate-icon">🎓</div>
              <h1 class="certificate-title">شهادة تخرج</h1>
              <p class="certificate-subtitle">${certificateData.title}</p>
            </div>

            <!-- محتوى الشهادة -->
            <div class="certificate-content">
              <p class="certificate-intro">تشهد إدارة المدرسة أن الطالب:</p>
              <h2 class="student-name">${certificateData.student?.name || 'الطالب المتميز'}</h2>
              ${certificateData.student?.classe ? `<p class="class-name">${certificateData.student.classe.name}</p>` : ''}
              <p class="certificate-description">${certificateData.description}</p>
            </div>

            <!-- توقيع الشهادة -->
            <div class="certificate-footer">
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المدير</p>
              </div>
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المشرف</p>
              </div>
              <div class="signature-box">
                <div class="signature-line"></div>
                <p class="signature-title">توقيع المعلم</p>
              </div>
            </div>

            <!-- ختم المدرسة -->
            <div class="certificate-stamp">ختم المدرسة</div>

            <!-- تاريخ الإصدار -->
            ${certificateData.issueDate ? `
            <div class="certificate-date">
              تم إصدار هذه الشهادة بتاريخ: ${formatDate(certificateData.issueDate)}
            </div>
            ` : ''}
          </div>
        </div>
      `;

      // إضافة المحتوى إلى العنصر المؤقت
      tempDiv.innerHTML = certificateHTML;

      // انتظار تحميل الخطوط والصور
      await new Promise(resolve => setTimeout(resolve, 500));

      // تحويل العنصر إلى صورة باستخدام html2canvas
      const canvas = await html2canvas(tempDiv.firstElementChild as HTMLElement, {
        scale: 3, // زيادة الدقة للحصول على صورة أوضح
        backgroundColor: '#ffffff',
        logging: false,
        useCORS: true,
        allowTaint: true,
        imageTimeout: 5000
      });

      // إزالة العنصر المؤقت
      document.body.removeChild(tempDiv);

      // تحويل الصورة إلى PNG بجودة عالية
      const image = canvas.toDataURL('image/png', 1.0);

      // إنشاء رابط تنزيل وتنفيذه
      const link = document.createElement('a');
      link.href = image;
      link.download = `شهادة_تخرج_${certificateData.student?.name || 'تقدير'}_${new Date().toLocaleDateString('fr-FR')}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error generating certificate image:', error);
      alert('حدث خطأ أثناء تحميل الشهادة كصورة');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <div className="space-y-4">
      <div ref={certificateRef} className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* الشهادة */}
        <div className="relative" data-certificate="graduation">
          {/* خلفية الشهادة */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-indigo-50" />

          {/* زخارف الشهادة */}
          <div className="absolute top-0 left-0 w-full h-24 bg-gradient-to-r from-blue-600 to-indigo-600" />
          <div className="absolute bottom-0 left-0 w-full h-24 bg-gradient-to-r from-blue-600 to-indigo-600" />

          {/* إطار الشهادة */}
          <div className="relative z-10 p-8 border-4 border-gold-500 m-8 min-h-[600px]" style={{ borderColor: '#d4af37' }}>
            <div className="flex flex-col items-center justify-center h-full text-center">
              {/* رأس الشهادة */}
              <div className="mb-8 mt-8">
                <div className="w-32 h-32 mx-auto mb-4 rounded-full flex items-center justify-center" style={{ backgroundColor: '#d4af37' }}>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path d="M12 14l9-5-9-5-9 5 9 5z" />
                    <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                  </svg>
                </div>
                <h1 className="text-4xl font-bold mb-2" style={{ color: '#d4af37' }} data-arabic-text="true">شهادة تخرج</h1>
                <p className="text-xl text-gray-600" data-arabic-text="true">{certificateData.title}</p>
              </div>

              {/* محتوى الشهادة */}
              <div className="mb-8 max-w-2xl">
                <p className="text-lg mb-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">تشهد إدارة المدرسة أن الطالب:</p>
                <h2 className="text-3xl font-bold mb-4 pb-2 inline-block" style={{ color: '#d4af37', borderBottom: '2px solid #d4af37' }} data-arabic-text="true">
                  {certificateData.student?.name || 'الطالب المتميز'}
                </h2>
                {certificateData.student?.classe && (
                  <p className="text-xl text-gray-600 mb-6" data-arabic-text="true">
                    {certificateData.student.classe.name}
                  </p>
                )}
                <p className="text-lg my-6" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">{certificateData.description}</p>
              </div>

              {/* توقيع الشهادة */}
              <div className="mt-auto grid grid-cols-3 gap-8 w-full">
                <div className="text-center">
                  <div className="h-16 border-b border-gray-300 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المدير</p>
                </div>
                <div className="text-center">
                  <div className="h-16 border-b border-gray-300 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المشرف</p>
                </div>
                <div className="text-center">
                  <div className="h-16 border-b border-gray-300 mb-2"></div>
                  <p className="text-gray-600" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">توقيع المعلم</p>
                </div>
              </div>

              {/* تاريخ الإصدار */}
              {certificateData.issueDate && (
                <div className="mt-8 text-sm text-gray-500" style={{ wordSpacing: '0.25em' }} data-arabic-text="true">
                  تم إصدار هذه الشهادة بتاريخ: {formatDate(certificateData.issueDate)}
                </div>
              )}

              {/* ختم المدرسة */}
              <div className="absolute bottom-8 left-8 w-24 h-24 border-2 border-dashed rounded-full flex items-center justify-center text-gray-400" style={{ borderColor: '#d4af37', color: '#d4af37', wordSpacing: '0.25em' }} data-arabic-text="true">
                ختم المدرسة
              </div>
            </div>
          </div>
        </div>
      </div>

      {showControls && (
        <Card className="p-4 flex justify-center space-x-4 space-x-reverse">
          <Button
            variant="outline"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            <span>طباعة</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={handleDownloadImage}
          >
            <Download className="h-4 w-4" />
            <span>تحميل</span>
          </Button>
        </Card>
      )}
    </div>
  );
}

'use client';

import { useEffect } from 'react';

interface SiteColors {
  primaryColor: string;
  secondaryColor: string;
  sidebarColor: string;
  backgroundColor: string;
  accentColor: string;
  textColor: string;
}

// الألوان الافتراضية كنسخة احتياطية فقط
const fallbackColors: SiteColors = {
  primaryColor: '#169b88',
  secondaryColor: '#1ab19c',
  sidebarColor: '#1a202c',
  backgroundColor: '#f3f4f6',
  accentColor: '#10b981',
  textColor: '#1f2937'
};

// دالة لحساب لون النص المتباين
const getTextColor = (bgColor: string): string => {
  const hex = bgColor.replace('#', '');
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#ffffff';
};

// دالة لتحويل hex إلى rgba
const hexToRgba = (hex: string, alpha: number): string => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// تطبيق الألوان فوراً على CSS variables
const applyColorsImmediately = (colors: SiteColors): void => {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;

  // الألوان الأساسية
  root.style.setProperty('--primary-color', colors.primaryColor);
  root.style.setProperty('--secondary-color', colors.secondaryColor);
  root.style.setProperty('--sidebar-color', colors.sidebarColor);
  root.style.setProperty('--background-color', colors.backgroundColor);
  root.style.setProperty('--accent-color', colors.accentColor);
  root.style.setProperty('--text-color', colors.textColor);

  // ألوان النصوص المتباينة
  const primaryTextColor = getTextColor(colors.primaryColor);
  const secondaryTextColor = getTextColor(colors.secondaryColor);
  const sidebarTextColor = getTextColor(colors.sidebarColor);

  root.style.setProperty('--primary-text-color', primaryTextColor);
  root.style.setProperty('--secondary-text-color', secondaryTextColor);
  root.style.setProperty('--sidebar-text-color', sidebarTextColor);

  // الألوان الخفيفة والداكنة
  root.style.setProperty('--primary-light', hexToRgba(colors.primaryColor, 0.1));
  root.style.setProperty('--primary-lighter', hexToRgba(colors.primaryColor, 0.05));
  root.style.setProperty('--primary-dark', hexToRgba(colors.primaryColor, 0.9));
  root.style.setProperty('--secondary-light', hexToRgba(colors.secondaryColor, 0.1));
  root.style.setProperty('--secondary-lighter', hexToRgba(colors.secondaryColor, 0.05));
  root.style.setProperty('--secondary-dark', hexToRgba(colors.secondaryColor, 0.9));

  // ألوان الحدود
  root.style.setProperty('--primary-border', hexToRgba(colors.primaryColor, 0.2));
  root.style.setProperty('--secondary-border', hexToRgba(colors.secondaryColor, 0.2));
};

// دالة لحفظ الألوان في قاعدة البيانات
const saveColorsToDatabase = async (colors: SiteColors): Promise<void> => {
  try {
    const response = await fetch('/api/site-colors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ colors }),
    });

    if (!response.ok) {
      console.error('Failed to save colors to database');
    }
  } catch (error) {
    console.error('Error saving colors to database:', error);
  }
};

export default function ColorInitializer() {
  useEffect(() => {
    // تطبيق الألوان فوراً من localStorage إن وجدت
    const initializeColorsImmediately = async () => {
      try {
        // استخدام نظام التزامن الجديد
        const { getColorsWithFallback, initializeColorSync } = await import('@/utils/colorSync');

        // جلب الألوان مع نظام fallback
        const colors = await getColorsWithFallback();
        applyColorsImmediately(colors);

        // تهيئة نظام التزامن
        await initializeColorSync();

        console.log('✅ تم تهيئة نظام الألوان والتزامن بنجاح');

      } catch (error) {
        console.error('Error loading colors from localStorage:', error);
        // في حالة الخطأ، طبق الألوان الافتراضية
        applyColorsImmediately(fallbackColors);
      }
    };

    // تطبيق الألوان فوراً
    initializeColorsImmediately();

    // جلب الألوان من الخادم في الخلفية
    const fetchColorsFromServer = async () => {
      try {
        const response = await fetch('/api/site-colors');
        if (response.ok) {
          const data = await response.json();
          if (data.colors && typeof data.colors === 'object') {
            // حفظ الألوان في localStorage
            localStorage.setItem('siteColors', JSON.stringify(data.colors));
            // تطبيق الألوان الجديدة فقط إذا كانت مختلفة عن المحفوظة
            const currentColors = localStorage.getItem('siteColors');
            if (!currentColors || JSON.stringify(data.colors) !== currentColors) {
              applyColorsImmediately(data.colors);
            }
          } else if (data.colors === null) {
            // لا توجد ألوان في قاعدة البيانات، استخدم الافتراضية إذا لم تكن محفوظة محلياً
            const savedColors = localStorage.getItem('siteColors');
            if (!savedColors) {
              applyColorsImmediately(fallbackColors);
              // حفظ الألوان الافتراضية في قاعدة البيانات
              await saveColorsToDatabase(fallbackColors);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching colors from server:', error);
      }
    };

    // جلب الألوان من الخادم بعد تأخير قصير
    setTimeout(fetchColorsFromServer, 100);

    // مراقبة التغييرات في localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteColors' && e.newValue && e.newValue !== 'undefined' && e.newValue !== 'null') {
        try {
          const newColors = JSON.parse(e.newValue);
          if (newColors && typeof newColors === 'object') {
            applyColorsImmediately(newColors);
          }
        } catch (error) {
          console.error('Error applying colors from storage change:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // هذا المكون لا يعرض أي محتوى، فقط يحمل الألوان
  return null;
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'react-toastify';
import { FaCreditCard, FaMoneyBillWave } from 'react-icons/fa';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import Image from 'next/image';

interface PaymentMethod {
  id: number;
  name: string;
  description: string | null;
  isActive: boolean;
  requiresCard: boolean;
  icon: string | null;
}

interface Invoice {
  id: number;
  amount: number;
  remainingAmount: number;
  month: number;
  year: number;
}

interface PaymentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  studentId: number;
  studentName: string;
  invoice?: Invoice | null;
}

export default function PaymentForm({ isOpen, onClose, onSuccess, studentId, studentName, invoice }: PaymentFormProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    amount: invoice?.remainingAmount.toString() || '',
    paymentMethodId: '',
    cardNumber: '',
    cardExpiry: '',
    cardCvv: '',
    note: ''
  });

  // جلب طرق الدفع المتاحة
  useEffect(() => {
    if (isOpen) {
      fetchPaymentMethods();
    }
  }, [isOpen]);

  const fetchPaymentMethods = async () => {
    try {
      setIsLoadingPaymentMethods(true);
      const response = await fetch('/api/payment-methods?activeOnly=true');
      if (!response.ok) {
        throw new Error('فشل في جلب طرق الدفع المتاحة');
      }
      const data = await response.json();
      setPaymentMethods(data);
    } catch (error) {
      console.error('خطأ في جلب طرق الدفع:', error);
      toast.error('فشل في جلب طرق الدفع المتاحة');
    } finally {
      setIsLoadingPaymentMethods(false);
    }
  };

  // تغيير طريقة الدفع المحددة
  const handlePaymentMethodChange = (methodId: string) => {
    const method = paymentMethods.find(m => m.id.toString() === methodId);
    if (method) {
      setSelectedPaymentMethod(method);
      setFormData(prev => ({ ...prev, paymentMethodId: methodId }));
    }
  };

  // التحقق مما إذا كانت طريقة الدفع تتطلب معلومات البطاقة
  const isCardPaymentMethod = (method: PaymentMethod | null) => {
    if (!method) return false;
    return method.requiresCard;
  };

  // إرسال نموذج الدفع
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      toast.error('يرجى إدخال مبلغ صحيح');
      return;
    }

    if (!formData.paymentMethodId) {
      toast.error('يرجى اختيار طريقة دفع');
      return;
    }

    // التحقق من معلومات البطاقة إذا كانت طريقة الدفع تتطلب ذلك
    if (isCardPaymentMethod(selectedPaymentMethod)) {
      if (!formData.cardNumber || !formData.cardExpiry || !formData.cardCvv) {
        toast.error('يرجى إدخال جميع معلومات البطاقة');
        return;
      }
    }

    setIsSubmitting(true);

    try {
      const currentDate = new Date();
      const paymentData = {
        studentId,
        amount: parseFloat(formData.amount),
        month: (invoice?.month || currentDate.getMonth() + 1).toString().padStart(2, '0'),
        year: (invoice?.year || currentDate.getFullYear()).toString(),
        paymentMethodId: parseInt(formData.paymentMethodId),
        invoiceId: invoice?.id,
        notes: formData.note,
        cardDetails: isCardPaymentMethod(selectedPaymentMethod) ? {
          cardNumber: formData.cardNumber,
          cardExpiry: formData.cardExpiry,
          cardCvv: formData.cardCvv
        } : undefined
      };

      const response = await fetch('/api/parent-payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في إتمام عملية الدفع');
      }

      toast.success('تم إتمام عملية الدفع بنجاح');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('خطأ في عملية الدفع:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء عملية الدفع');
    } finally {
      setIsSubmitting(false);
    }
  };

  const dialogFooter = (
    <>
      <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isSubmitting}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('parentPaymentForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isSubmitting ? 'جاري الدفع...' : 'دفع'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onClose}
      title={`دفع رسوم للطالب ${studentName}`}
      variant="primary"
      footer={dialogFooter}
    >
      <form id="parentPaymentForm" onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          {/* معلومات الدفع باسم الولي */}
          <div className="bg-green-50 p-4 rounded-lg mb-4 border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <FaMoneyBillWave className="text-green-600" />
              <p className="font-bold text-green-800">معلومات الدفع</p>
            </div>
            <div className="bg-white p-3 rounded border border-green-100">
              <p className="text-sm text-gray-600 mb-1">سيتم تسجيل هذا الدفع باسم:</p>
              <p className="font-bold text-green-700">ولي الأمر (أنت)</p>
              <p className="text-xs text-gray-500 mt-1">
                ملاحظة: جميع المدفوعات تُسجل باسم ولي الأمر وليس باسم الطالب
              </p>
            </div>
          </div>

          {invoice && (
            <div className="bg-blue-50 p-4 rounded-lg mb-4 border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <FaFileInvoiceDollar className="text-blue-600" />
                <p className="font-bold text-blue-800">معلومات الفاتورة</p>
              </div>
              <div className="grid grid-cols-2 gap-3 mt-2">
                <div className="bg-white p-2 rounded border border-blue-100">
                  <p className="text-xs text-gray-600">رقم الفاتورة:</p>
                  <p className="font-bold text-blue-700">{invoice.id}</p>
                </div>
                <div className="bg-white p-2 rounded border border-blue-100">
                  <p className="text-xs text-gray-600">الشهر/السنة:</p>
                  <p className="font-bold text-blue-700">{invoice.month}/{invoice.year}</p>
                </div>
                <div className="bg-white p-2 rounded border border-blue-100">
                  <p className="text-xs text-gray-600">المبلغ الإجمالي:</p>
                  <p className="font-bold text-blue-700">{invoice.amount.toLocaleString()} دج</p>
                </div>
                <div className="bg-white p-2 rounded border border-blue-100">
                  <p className="text-xs text-gray-600">المبلغ المتبقي:</p>
                  <p className="font-bold text-red-600">{invoice.remainingAmount.toLocaleString()} دج</p>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right col-span-1">
              المبلغ <span className="text-red-500">*</span>
            </Label>
            <Input
              id="amount"
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
              className="col-span-3"
              placeholder="أدخل المبلغ"
              min="0"
              required
            />
          </div>

          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right col-span-1 mt-2">
              طريقة الدفع <span className="text-red-500">*</span>
            </Label>
            <div className="col-span-3">
              {isLoadingPaymentMethods ? (
                <div className="text-center py-2">
                  <div className="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-[var(--primary-color)]"></div>
                  <span className="mr-2 text-gray-600">جاري تحميل طرق الدفع...</span>
                </div>
              ) : paymentMethods.length === 0 ? (
                <div className="text-center py-2 text-red-500">
                  لا توجد طرق دفع متاحة حالياً
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.id}
                      className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                        formData.paymentMethodId === method.id.toString()
                          ? 'border-[var(--primary-color)] bg-[#e0f2ef]'
                          : 'border-gray-200 hover:border-[var(--primary-color)] hover:bg-[#f0f9f7]'
                      }`}
                      onClick={() => handlePaymentMethodChange(method.id.toString())}
                    >
                      {method.icon ? (
                        <Image
                          src={method.icon}
                          alt={method.name}
                          width={24}
                          height={24}
                          className="ml-2"
                        />
                      ) : (
                        <FaCreditCard className="text-xl ml-2 text-[var(--primary-color)]" />
                      )}
                      <span className="font-semibold">{method.name}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {isCardPaymentMethod(selectedPaymentMethod) && (
            <div className="col-span-4 bg-gray-50 p-4 rounded-lg mt-2">
              <h3 className="font-medium mb-3 flex items-center gap-2">
                <FaCreditCard className="text-[var(--primary-color)]" />
                <span>معلومات البطاقة</span>
              </h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">رقم البطاقة <span className="text-red-500">*</span></Label>
                  <Input
                    id="cardNumber"
                    placeholder="أدخل رقم البطاقة"
                    value={formData.cardNumber}
                    onChange={(e) => setFormData({ ...formData, cardNumber: e.target.value })}
                    required={isCardPaymentMethod(selectedPaymentMethod)}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cardExpiry">تاريخ الانتهاء <span className="text-red-500">*</span></Label>
                    <Input
                      id="cardExpiry"
                      placeholder="MM/YY"
                      value={formData.cardExpiry}
                      onChange={(e) => setFormData({ ...formData, cardExpiry: e.target.value })}
                      required={isCardPaymentMethod(selectedPaymentMethod)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cardCvv">رمز الأمان <span className="text-red-500">*</span></Label>
                    <Input
                      id="cardCvv"
                      type="password"
                      placeholder="CVV"
                      value={formData.cardCvv}
                      onChange={(e) => setFormData({ ...formData, cardCvv: e.target.value })}
                      required={isCardPaymentMethod(selectedPaymentMethod)}
                      maxLength={3}
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-4 items-start gap-4">
            <Label htmlFor="note" className="text-right col-span-1 mt-2">
              ملاحظات
            </Label>
            <textarea
              id="note"
              value={formData.note}
              onChange={(e) => setFormData({ ...formData, note: e.target.value })}
              className="col-span-3 min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="ملاحظات إضافية (اختياري)"
            />
          </div>
        </div>
      </form>
    </AnimatedDialog>
  );
}

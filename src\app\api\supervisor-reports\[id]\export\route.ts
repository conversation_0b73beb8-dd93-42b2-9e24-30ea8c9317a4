import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// POST /api/supervisor-reports/[id]/export - تصدير تقرير موحد
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || !['ADMIN', 'EMPLOYEE'].includes(userData.role)) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح به، يجب أن تكون مسؤول أو موظف' },
        { status: 401 }
      );
    }

    const reportId = parseInt(params.id);
    if (isNaN(reportId)) {
      return NextResponse.json({
        success: false,
        error: 'معرف التقرير غير صحيح'
      }, { status: 400 });
    }

    const body = await request.json();
    const { format = 'pdf', options = {} } = body;

    // التحقق من صيغة التصدير المدعومة
    if (!['pdf', 'html', 'word'].includes(format)) {
      return NextResponse.json({
        success: false,
        error: 'صيغة التصدير غير مدعومة'
      }, { status: 400 });
    }

    // جلب التقرير
    const report = await prisma.supervisorReport.findUnique({
      where: { id: reportId },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            profile: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    if (!report) {
      return NextResponse.json({
        success: false,
        error: 'التقرير غير موجود'
      }, { status: 404 });
    }

    // تحويل البيانات المحفوظة من JSON
    const literaryContent = report.literaryContent ? JSON.parse(report.literaryContent) : null;
    const financialData = report.financialData ? JSON.parse(report.financialData) : null;
    const officeSettings = report.officeSettings ? JSON.parse(report.officeSettings) : null;

    // إنشاء محتوى HTML للتقرير
    const htmlContent = generateReportHTML({
      report,
      literaryContent,
      financialData,
      officeSettings,
      options
    });

    let responseData: any;
    let contentType: string;
    let filename: string;

    switch (format) {
      case 'html':
        responseData = htmlContent;
        contentType = 'text/html; charset=utf-8';
        filename = `تقرير_موحد_${report.id}_${new Date().toISOString().split('T')[0]}.html`;
        break;

      case 'pdf':
        // استخدام مكتبة jsPDF لإنشاء PDF حقيقي
        try {
          const { jsPDF } = await import('jspdf');
          const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
          });

          // إضافة العنوان
          doc.setFontSize(18);
          doc.text(report.title, doc.internal.pageSize.width / 2, 20, { align: 'center' });

          // إضافة معلومات التقرير
          doc.setFontSize(12);
          let yPosition = 40;

          doc.text(`فترة التقرير: من ${new Date(report.periodStart).toLocaleDateString('ar-DZ')} إلى ${new Date(report.periodEnd).toLocaleDateString('ar-DZ')}`, 20, yPosition);
          yPosition += 10;

          if (report.description) {
            doc.text(`الوصف: ${report.description}`, 20, yPosition);
            yPosition += 10;
          }

          // إضافة المحتوى الأدبي إذا كان موجوداً
          if (report.literaryContent) {
            yPosition += 10;
            doc.setFontSize(14);
            doc.text('المحتوى الأدبي:', 20, yPosition);
            yPosition += 10;

            doc.setFontSize(10);
            const lines = report.literaryContent.split('\n');
            lines.forEach(line => {
              if (yPosition > 270) {
                doc.addPage();
                yPosition = 20;
              }
              doc.text(line, 20, yPosition);
              yPosition += 6;
            });
          }

          // إضافة البيانات المالية إذا كانت موجودة
          if (report.financialData && report.financialData.length > 0) {
            yPosition += 10;
            if (yPosition > 250) {
              doc.addPage();
              yPosition = 20;
            }

            doc.setFontSize(14);
            doc.text('البيانات المالية:', 20, yPosition);
            yPosition += 10;

            // إضافة جدول البيانات المالية
            const tableData = report.financialData.map(item => [
              item.description || '',
              item.amount ? `${item.amount.toLocaleString('fr-FR')} د.ج` : '',
              item.type || '',
              item.date ? new Date(item.date).toLocaleDateString('ar-DZ') : ''
            ]);

            // @ts-expect-error jspdf-autotable adds this method
            doc.autoTable({
              head: [['الوصف', 'المبلغ', 'النوع', 'التاريخ']],
              body: tableData,
              startY: yPosition,
              theme: 'grid',
              styles: {
                fontSize: 8,
                textColor: [0, 0, 0],
                lineColor: [0, 0, 0],
                lineWidth: 0.1
              },
              headStyles: {
                fillColor: [22, 155, 136],
                textColor: [255, 255, 255],
                fontStyle: 'bold'
              }
            });
          }

          // إضافة تاريخ الإنشاء
          const pageCount = doc.getNumberOfPages();
          for (let i = 1; i <= pageCount; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.text(`تم إنشاؤه في: ${new Date().toLocaleDateString('ar-DZ')} - صفحة ${i} من ${pageCount}`,
              doc.internal.pageSize.width - 20, doc.internal.pageSize.height - 10, { align: 'right' });
          }

          const pdfBuffer = doc.output('arraybuffer');
          responseData = pdfBuffer;
          contentType = 'application/pdf';
          filename = `تقرير_موحد_${report.id}_${new Date().toISOString().split('T')[0]}.pdf`;
        } catch (error) {
          console.error('خطأ في إنشاء PDF:', error);
          // العودة إلى HTML في حالة الخطأ
          responseData = htmlContent;
          contentType = 'text/html; charset=utf-8';
          filename = `تقرير_موحد_${report.id}_${new Date().toISOString().split('T')[0]}.html`;
        }
        break;

      case 'word':
        // في التطبيق الحقيقي، يمكن استخدام مكتبة لتحويل HTML إلى Word
        responseData = htmlContent;
        contentType = 'text/html; charset=utf-8';
        filename = `تقرير_موحد_${report.id}_${new Date().toISOString().split('T')[0]}.html`;
        break;

      default:
        responseData = htmlContent;
        contentType = 'text/html; charset=utf-8';
        filename = `تقرير_موحد_${report.id}_${new Date().toISOString().split('T')[0]}.html`;
    }

    // إرجاع المحتوى مع headers مناسبة للتنزيل
    return new NextResponse(responseData, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('Error exporting supervisor report:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تصدير التقرير'
    }, { status: 500 });
  }
}

// دالة لإنشاء محتوى HTML للتقرير
function generateReportHTML({
  report,
  literaryContent,
  financialData,
  officeSettings,
  options
}: {
  report: any;
  literaryContent: any;
  financialData: any;
  officeSettings: any;
  options: any;
}) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date));
  };

  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${report.title}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }
        .logo {
            max-width: 100px;
            margin-bottom: 10px;
        }
        .organization-name {
            font-size: 24px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        .office-name {
            font-size: 18px;
            color: #374151;
            margin-bottom: 10px;
        }
        .report-title {
            font-size: 20px;
            font-weight: bold;
            color: #dc2626;
            margin-top: 20px;
        }
        .period {
            font-size: 16px;
            color: #6b7280;
            margin-top: 10px;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        .literary-content {
            line-height: 1.8;
        }
        .financial-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .financial-table th,
        .financial-table td {
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            text-align: center;
        }
        .financial-table th {
            background-color: #f3f4f6;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            padding-top: 20px;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .section { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        ${officeSettings?.logoUrl ? `<img src="${officeSettings.logoUrl}" alt="شعار الجمعية" class="logo">` : ''}
        <div class="organization-name">${officeSettings?.organizationName || 'جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن'}</div>
        <div class="office-name">${officeSettings?.officeName || 'المكـــــــتب البلدي'}</div>
        <div class="report-title">${report.title}</div>
        <div class="period">
            الفترة: من ${formatDate(report.periodStart)} إلى ${formatDate(report.periodEnd)}
        </div>
    </div>

    ${literaryContent ? `
    <div class="section">
        <div class="section-title">📘 التقرير الأدبي</div>
        <div class="literary-content">
            ${literaryContent}
        </div>
    </div>
    ` : ''}

    ${financialData ? `
    <div class="section">
        <div class="section-title">💰 التقرير المالي</div>
        <table class="financial-table">
            <thead>
                <tr>
                    <th>البيان</th>
                    <th>المبلغ (دج)</th>
                    <th>النسبة</th>
                </tr>
            </thead>
            <tbody>
                ${generateFinancialTableRows(financialData)}
            </tbody>
        </table>
    </div>
    ` : ''}

    <div class="footer">
        <p>تم إنشاء هذا التقرير في: ${formatDate(new Date())}</p>
        <p>${officeSettings?.presidentName || ''} - ${officeSettings?.presidentTitle || ''}</p>
    </div>
</body>
</html>
  `;
}

// دالة لإنشاء صفوف الجدول المالي
function generateFinancialTableRows(financialData: any): string {
  if (!financialData || !Array.isArray(financialData)) {
    return '<tr><td colspan="3">لا توجد بيانات مالية</td></tr>';
  }

  return financialData.map(item => `
    <tr>
        <td>${item.description || ''}</td>
        <td>${item.amount ? item.amount.toLocaleString() : '0'}</td>
        <td>${item.percentage ? item.percentage.toFixed(2) + '%' : '0%'}</td>
    </tr>
  `).join('');
}

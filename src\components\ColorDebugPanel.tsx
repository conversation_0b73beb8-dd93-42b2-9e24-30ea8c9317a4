'use client';

import { useState, useEffect } from 'react';
import { trackGreenElements, fixAllGreenElements, generateColorReport, logColorReport, startColorMonitoring } from '@/utils/colorTracker';

interface ColorDebugPanelProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

export default function ColorDebugPanel({ isVisible = false, onToggle }: ColorDebugPanelProps) {
  const [mounted, setMounted] = useState(false);
  const [issues, setIssues] = useState<Array<{element: Element, issues: string[]}>>([]);
  const [report, setReport] = useState<any>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [fixedCount, setFixedCount] = useState(0);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted || !isVisible) return;

    const updateData = () => {
      const newIssues = trackGreenElements();
      const newReport = generateColorReport();
      setIssues(newIssues);
      setReport(newReport);
    };

    updateData();

    // بدء المراقبة
    let stopMonitoring: (() => void) | null = null;
    if (isMonitoring) {
      stopMonitoring = startColorMonitoring((issueCount) => {
        updateData();
      });
    }

    return () => {
      if (stopMonitoring) {
        stopMonitoring();
      }
    };
  }, [mounted, isVisible, isMonitoring]);

  const handleFixAll = () => {
    const fixed = fixAllGreenElements();
    setFixedCount(fixed);
    
    // تحديث البيانات بعد الإصلاح
    setTimeout(() => {
      const newIssues = trackGreenElements();
      const newReport = generateColorReport();
      setIssues(newIssues);
      setReport(newReport);
    }, 100);
  };

  const handleLogReport = () => {
    logColorReport();
  };

  const handleToggleMonitoring = () => {
    setIsMonitoring(!isMonitoring);
  };

  if (!mounted || !isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-96 overflow-hidden">
      <div className="bg-blue-600 text-white p-3 flex items-center justify-between">
        <h3 className="font-bold text-sm">🎨 لوحة تحكم الألوان</h3>
        <button
          onClick={onToggle}
          className="text-white hover:text-gray-200 text-lg font-bold"
        >
          ×
        </button>
      </div>

      <div className="p-4 space-y-4 overflow-y-auto max-h-80">
        {/* إحصائيات سريعة */}
        {report && (
          <div className="bg-gray-50 p-3 rounded">
            <h4 className="font-semibold text-sm mb-2">📊 الإحصائيات</h4>
            <div className="text-xs space-y-1">
              <div>إجمالي العناصر: <span className="font-bold">{report.totalElements}</span></div>
              <div>العناصر التي تحتاج إصلاح: <span className="font-bold text-red-600">{report.issuesFound}</span></div>
              {fixedCount > 0 && (
                <div>تم إصلاح: <span className="font-bold text-primary-color">{fixedCount}</span> عنصر</div>
              )}
            </div>
          </div>
        )}

        {/* أزرار التحكم */}
        <div className="space-y-2">
          <button
            onClick={handleFixAll}
            className="w-full bg-primary-color text-white py-2 px-3 rounded text-sm hover:bg-green-700 transition-colors"
          >
            🔧 إصلاح جميع العناصر
          </button>
          
          <button
            onClick={handleLogReport}
            className="w-full bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 transition-colors"
          >
            📋 طباعة التقرير في الكونسول
          </button>
          
          <button
            onClick={handleToggleMonitoring}
            className={`w-full py-2 px-3 rounded text-sm transition-colors ${
              isMonitoring 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-yellow-600 text-white hover:bg-yellow-700'
            }`}
          >
            {isMonitoring ? '⏹️ إيقاف المراقبة' : '▶️ بدء المراقبة التلقائية'}
          </button>
        </div>

        {/* قائمة العناصر التي تحتاج إصلاح */}
        {issues.length > 0 && (
          <div className="bg-red-50 p-3 rounded">
            <h4 className="font-semibold text-sm mb-2 text-red-800">⚠️ عناصر تحتاج إصلاح</h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {issues.slice(0, 5).map((issue, index) => (
                <div key={index} className="text-xs bg-white p-2 rounded border">
                  <div className="font-semibold">{issue.element.tagName}</div>
                  <div className="text-gray-600">
                    {issue.issues.join(', ')}
                  </div>
                </div>
              ))}
              {issues.length > 5 && (
                <div className="text-xs text-gray-500 text-center">
                  ... و {issues.length - 5} عنصر آخر
                </div>
              )}
            </div>
          </div>
        )}

        {/* رسالة نجاح */}
        {issues.length === 0 && report && (
          <div className="bg-green-50 p-3 rounded text-center">
            <div className="text-green-800 font-semibold text-sm">✅ ممتاز!</div>
            <div className="text-primary-color text-xs">جميع العناصر تستخدم الألوان الصحيحة</div>
          </div>
        )}

        {/* نصائح */}
        <div className="bg-blue-50 p-3 rounded">
          <h4 className="font-semibold text-sm mb-2 text-blue-800">💡 نصائح</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• استخدم bg-primary-color بدلاً من bg-green-*</li>
            <li>• استخدم text-primary-color بدلاً من text-green-*</li>
            <li>• استخدم var(--primary-color) في الـ styles</li>
            <li>• فعل المراقبة التلقائية للإصلاح المستمر</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

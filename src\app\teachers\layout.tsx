"use client"

import { <PERSON>actNode, useState } from 'react'
import Link from 'next/link'
import { FaChalkboardTeacher, FaUserGraduate, FaCalendarAlt, FaBook, FaClipboardList, FaVideo, FaBell } from 'react-icons/fa'
import NotificationBadge from '@/components/NotificationBadge'

interface TeacherLayoutProps {
  children: ReactNode
}

export default function TeacherLayout({ children }: TeacherLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)

  const logout = async () => {
    try {
      const response = await fetch('/api/users/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // تم تسجيل الخروج بنجاح
        window.location.href = '/login';
      } else {
        console.error('فشل تسجيل الخروج');
        alert('فشل تسجيل الخروج، يرجى المحاولة مرة أخرى');
      }
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      window.location.href = '/';
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 flex" dir="rtl">
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 right-4 z-20">
        <button
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          className="p-2 rounded-md bg-white shadow-md"
          aria-label="Toggle menu"
        >
          <svg
            className="h-6 w-6 text-gray-600"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            {isSidebarOpen ? (
              <path d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
      </div>

      {/* Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed lg:sticky top-[100px] lg:top-0 bottom-[80px] lg:bottom-auto lg:h-[calc(100vh-180px)] right-0 w-72 md:w-64 bg-[var(--primary-color)] text-white shadow-lg transform transition-all duration-300 ease-in-out z-20 overflow-y-auto
          ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'}`}
      >
        <div className="p-4 border-b border-[var(--secondary-color)]">
          <h2 className="text-xl font-semibold text-white">لوحة تحكم المعلم</h2>
        </div>
        <nav className="p-4">
          <ul className="space-y-1">
            <li>
              <Link
                href="/teachers"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaChalkboardTeacher className="h-6 w-6 ml-3" />
                <span>الرئيسية</span>
              </Link>
            </li>
            <li>
              <Link
                href="/teachers/students"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaUserGraduate className="h-6 w-6 ml-3" />
                <span>طلابي</span>
              </Link>
            </li>
            <li>
              <Link
                href="/teachers/schedule"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaCalendarAlt className="h-6 w-6 ml-3" />
                <span>جدول الحصص</span>
              </Link>
            </li>
            <li>
              <Link
                href="/teachers/courses"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaBook className="h-6 w-6 ml-3" />
                <span>المقررات</span>
              </Link>
            </li>
            <li>
              <Link
                href="/teachers/attendance"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaClipboardList className="h-6 w-6 ml-3" />
                <span>الحضور والغياب</span>
              </Link>
            </li>
            <li>
              <Link
                href="/teachers/evaluation/dashboard"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span>إدارة الامتحانات والتقييم</span>
              </Link>
            </li>
            <li>
              <Link
                href="/remote-classes"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaVideo className="h-6 w-6 ml-3" />
                <span>الفصول الافتراضية</span>
              </Link>
            </li>
            <li>
              <Link
                href="/notifications"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaBell className="h-6 w-6 ml-3" />
                <span>الإشعارات</span>
              </Link>
            </li>
          </ul>
        </nav>
      </aside>

      {/* Main Content */}
        {/* Main Content */}
        <main className="flex-1 min-w-0 overflow-auto p-4 lg:p-8">
      <header className="bg-[var(--secondary-color)] lg:bg-white shadow rounded-lg mb-6 p-4">
         <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
    <h1 className="text-3xl font-bold text-gray-800">لوحة التحكم</h1>
    <div className="flex items-center space-x-4 space-x-reverse">
      <NotificationBadge />
      <span className="text-gray-600 text-lg">مرحباً بك</span>
      <button onClick={logout} className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 text-base">
        تسجيل الخروج
      </button>
    </div>
  </div>
</header>

        {children}
      </main>
    </div>
  )
}
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useRouter, useSearchParams } from 'next/navigation';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import {
  getGradeLevelByScore,
  calculatePercentage,
  getPassStatus,
  validateGrade,
  DEFAULT_SETTINGS
} from '@/lib/grading-system';

export default function AdminScoringPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const examId = searchParams?.get('examId') ?? null;
  const classId = searchParams?.get('classId') ?? null;

  console.log('Received examId:', examId, 'and classId:', classId);

  const [loadingSave, setLoadingSave] = useState(false);
  const [loadingClasses, setLoadingClasses] = useState(false);
  const [loadingSurahs, setLoadingSurahs] = useState(false);
  const [loadingStudents, setLoadingStudents] = useState(false);
  const [loadingExams, setLoadingExams] = useState(false);
  // المشرف لا يحتاج إلى جلب معرف المعلم، لأنه يملك صلاحيات كاملة
  const [errors, setErrors] = useState({
    classes: '',
    surahs: '',
    students: '',
    exams: '',
    save: ''
  });
  const [success, setSuccess] = useState('');

  interface Class {
    id: string;
    name: string;
  }

  interface ClassSubject {
    id: string;
    classeId: string;
    teacherSubjectId: string;
    classe: {
      id: string;
      name: string;
    };
    teacherSubject: {
      id: string;
      teacher: {
        id: string;
        name: string;
      };
      subject: {
        id: string;
        name: string;
      };
    };
  }

  interface Surah {
    id: string;
    name: string;
    number: number;
    totalAyahs?: number;
  }

  interface Student {
    id: string;
    name: string;
  }

  interface Exam {
    id: string;
    evaluationType: string;
    month: string;
    description?: string;
    requiresSurah: boolean;
    maxPoints: number;
    passingPoints: number;
  }

  const [classes, setClasses] = useState<Class[]>([]);
  const [classSubjects, setClassSubjects] = useState<ClassSubject[]>([]);
  const [surahs, setSurahs] = useState<Surah[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [exams, setExams] = useState<Exam[]>([]);
  const [selectedClass, setSelectedClass] = useState(classId || '');
  const [selectedExam, setSelectedExam] = useState(examId || '');
  const [selectedSurah, setSelectedSurah] = useState('');
  const [startVerse, setStartVerse] = useState('');
  const [endVerse, setEndVerse] = useState('');
  const [grades, setGrades] = useState<Grade[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [selectedExamRequiresSurah, setSelectedExamRequiresSurah] = useState(false);

  // معلومات الامتحان المحدد
  const selectedExamInfo = exams.find(e => e.id === selectedExam);
  const maxPoints = selectedExamInfo?.maxPoints || DEFAULT_SETTINGS.maxPoints;

  interface Grade {
    studentId: string;
    examId: string | null;
    classSubjectId: string;
    grade: string;
    note: string;
    month: string;
  }

  useEffect(() => {
    console.log('Fetching initial data with examId:', examId, 'and classId:', classId);
    const fetchData = async () => {
      // Fetch classes
      setLoadingClasses(true);
      try {
        // جلب الأقسام بدون بيانات إضافية غير ضرورية
        const classesResponse = await fetch('/api/classes?includeStudents=false&includeSubjects=false');
        const classesData = await classesResponse.json();
        // استخراج مصفوفة الأقسام من الاستجابة
        const classesArray = classesData?.classes || [];
        setClasses(classesArray);

        // جلب علاقات القسم بالمادة
        const classSubjectsResponse = await fetch('/api/class-subjects');
        const classSubjectsData = await classSubjectsResponse.json();

        if (classSubjectsData && Array.isArray(classSubjectsData.data)) {
          setClassSubjects(classSubjectsData.data);
        }
      } catch (err) {
        console.error('Error fetching classes:', err);
        setErrors(prev => ({ ...prev, classes: 'فشل في جلب قائمة الأقسام' }));
      } finally {
        setLoadingClasses(false);
      }

      // Fetch surahs
      setLoadingSurahs(true);
      try {
        const surahsResponse = await fetch('/api/surahs');
        const surahsData = await surahsResponse.json();
        setSurahs(surahsData || []);
      } catch (err) {
        console.error('Error fetching surahs:', err);
        setErrors(prev => ({ ...prev, surahs: 'فشل في جلب قائمة السور' }));
      } finally {
        setLoadingSurahs(false);
      }

      // Fetch exams
      setLoadingExams(true);
      try {
        const examsResponse = await fetch('/api/evaluation/exams');
        if (!examsResponse.ok) {
          throw new Error('فشل في جلب الامتحانات');
        }
        const examsData = await examsResponse.json();

        if (examsData && examsData.data && Array.isArray(examsData.data)) {
          setExams(examsData.data);
        } else {
          setErrors(prev => ({ ...prev, exams: 'لا توجد امتحانات متاحة' }));
        }
      } catch (err) {
        console.error('Error fetching exams:', err);
        setErrors(prev => ({ ...prev, exams: 'فشل في جلب قائمة الامتحانات' }));
      } finally {
        setLoadingExams(false);
      }
    };

    fetchData();
  }, [examId, classId]);

  // تحديث الطلاب عند تغيير الفصل
  useEffect(() => {
    console.log('Selected class changed to:', selectedClass);
    const fetchStudents = async () => {
      if (selectedClass) {
        setLoadingStudents(true);
        setErrors(prev => ({ ...prev, students: '' }));
        try {
          // Fetch students in the selected class
          const response = await fetch(`/api/classes/${selectedClass}`);
          if (!response.ok) {
            throw new Error('فشل في جلب قائمة الطلاب');
          }
          const classData = await response.json();

          // استخراج قائمة الطلاب من بيانات القسم
          if (classData && classData.students && Array.isArray(classData.students)) {
            setStudents(classData.students);
            if (classData.students.length === 0) {
              setErrors(prev => ({ ...prev, students: 'لا يوجد طلاب في هذا القسم' }));
            }
          } else {
            setStudents([]);
            setErrors(prev => ({ ...prev, students: 'لا يمكن الوصول إلى بيانات الطلاب في هذا القسم' }));
          }

          // التحقق من وجود علاقة بين القسم والمادة وإعادة جلب العلاقات إذا لم تكن موجودة
          const classSubject = classSubjects.find(cs => cs.classeId === selectedClass);
          if (!classSubject) {
            console.log('لم يتم العثور على علاقة بين القسم والمادة، جاري إعادة جلب العلاقات...');

            // إعادة جلب علاقات القسم بالمادة
            const classSubjectsResponse = await fetch('/api/class-subjects');
            const classSubjectsData = await classSubjectsResponse.json();

            if (classSubjectsData && Array.isArray(classSubjectsData.data)) {
              console.log('تم جلب علاقات القسم بالمادة:', classSubjectsData.data.length);
              setClassSubjects(classSubjectsData.data);

              // التحقق مرة أخرى من وجود علاقة بين القسم والمادة
              const updatedClassSubject = classSubjectsData.data.find(cs => cs.classeId === selectedClass);
              if (!updatedClassSubject) {
                console.warn('لا تزال علاقة القسم بالمادة غير موجودة بعد إعادة الجلب، جاري إنشاء علاقة جديدة...');

                // إنشاء علاقة جديدة بين القسم والمادة
                try {
                  // البحث عن أول علاقة معلم بمادة متاحة
                  const teacherSubjectsResponse = await fetch('/api/teacher-subjects');
                  const teacherSubjectsData = await teacherSubjectsResponse.json();

                  if (teacherSubjectsData && Array.isArray(teacherSubjectsData.data) && teacherSubjectsData.data.length > 0) {
                    const firstTeacherSubject = teacherSubjectsData.data[0];

                    // إنشاء علاقة جديدة بين القسم والمادة
                    const createResponse = await fetch('/api/class-subjects', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify({
                        classeId: Number(selectedClass),
                        teacherSubjectId: Number(firstTeacherSubject.id)
                      }),
                    });

                    if (createResponse.ok) {
                      const createData = await createResponse.json();
                      console.log('تم إنشاء علاقة جديدة بين القسم والمادة:', createData);

                      // إعادة جلب علاقات القسم بالمادة بعد الإنشاء
                      const refreshResponse = await fetch('/api/class-subjects');
                      const refreshData = await refreshResponse.json();

                      if (refreshData && Array.isArray(refreshData.data)) {
                        setClassSubjects(refreshData.data);
                      }
                    } else {
                      console.error('فشل في إنشاء علاقة جديدة بين القسم والمادة');
                    }
                  } else {
                    console.error('لا توجد علاقات معلم بمادة متاحة');
                  }
                } catch (err) {
                  console.error('Error creating class subject relation:', err);
                }
              }
            }
          }
        } catch (err) {
          console.error('Error fetching students:', err);
          setErrors(prev => ({ ...prev, students: 'فشل في جلب قائمة الطلاب' }));
        } finally {
          setLoadingStudents(false);
        }
      }
    };

    fetchStudents();
  }, [selectedClass, classSubjects]);

  // تحديث معلومات الامتحان عند تغييره
  useEffect(() => {
    if (selectedExam) {
      const exam = exams.find(e => e.id === selectedExam);
      if (exam) {
        setSelectedExamRequiresSurah(exam.requiresSurah);

        // إذا كان الامتحان يتطلب سورة، نقوم بإعادة تعيين السورة والآيات
        if (!exam.requiresSurah) {
          setSelectedSurah('');
          setStartVerse('');
          setEndVerse('');
        }
      }
    }
  }, [selectedExam, exams]);

  // جلب الدرجات الموجودة إذا تم تحديد الامتحان والفصل
  useEffect(() => {
    console.log('Fetching existing grades for examId:', selectedExam, 'and classId:', selectedClass);
    const fetchExistingGrades = async () => {
      if (selectedExam && selectedClass) {
        try {
          // بناء URL لجلب الدرجات
          let url = `/api/exam-points?examId=${selectedExam}`;

          // إضافة معرف القسم إذا تم تحديده
          if (selectedClass) {
            // نحتاج إلى تحديد classSubjectId المناسب
            const classSubject = classSubjects.find(cs => cs.classeId === selectedClass);
            if (classSubject) {
              url += `&classSubjectId=${classSubject.id}`;
            } else {
              console.warn('لم يتم العثور على علاقة بين القسم والمادة:', selectedClass, classSubjects);
              // لا نقوم بإضافة classSubjectId إلى URL، مما قد يؤدي إلى عدم جلب أي نتائج
              // لكن لا نريد إيقاف العملية هنا
            }
          }

          const response = await fetch(url);
          if (!response.ok) {
            throw new Error('فشل في جلب الدرجات');
          }

          const data = await response.json();

          if (data.success && Array.isArray(data.data)) {
            // تحويل البيانات إلى الشكل المطلوب
            interface ExamPoint {
              id: number;
              examId: number;
              studentId: number;
              classSubjectId: number;
              grade: number;
              note?: string;
              surahId?: number;
              startVerse?: number;
              endVerse?: number;
            }

            const formattedGrades = data.data.map((point: ExamPoint) => ({
              studentId: point.studentId.toString(),
              examId: point.examId.toString(),
              classSubjectId: point.classSubjectId.toString(),
              grade: point.grade.toString(),
              note: point.note || '',
              month: new Date().toISOString().slice(0, 7)
            }));

            setGrades(formattedGrades);

            // إذا كان الامتحان يتطلب سورة وتم تحديد سورة في النتائج
            if (data.data.length > 0 && data.data[0].surahId) {
              setSelectedSurah(data.data[0].surahId.toString());
              setStartVerse(data.data[0].startVerse?.toString() || '');
              setEndVerse(data.data[0].endVerse?.toString() || '');
            }
          }
        } catch (err) {
          console.error('Error fetching existing grades:', err);
        }
      }
    };

    fetchExistingGrades();
  }, [selectedExam, selectedClass, classSubjects]);

  // تحذير عند مغادرة الصفحة مع وجود تغييرات غير محفوظة
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        const message = 'لديك تغييرات غير محفوظة، هل تريد المغادرة؟';
        event.preventDefault();
        // استخدام returnValue للتوافق مع المتصفحات القديمة
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (event as any).returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  const handleGradeChange = (studentId: string, field: 'grade' | 'note', value: string) => {
    if (field === 'grade') {
      const numericValue = Number(value);
      const validation = validateGrade(numericValue, maxPoints);
      if (!validation.isValid) {
        toast.error(validation.error);
        return;
      }
    }

    setGrades((prevGrades: Grade[]) => {
      const studentIndex = prevGrades.findIndex(g => g.studentId === studentId);

      if (studentIndex >= 0) {
        const updatedGrades = [...prevGrades];
        updatedGrades[studentIndex] = {
          ...updatedGrades[studentIndex],
          [field]: value
        };
        setHasUnsavedChanges(true);
        return updatedGrades;
      } else {
        // تحديد classSubjectId المناسب
        const classSubject = classSubjects.find(cs => cs.classeId === selectedClass);

        if (!classSubject) {
          console.error('لم يتم العثور على علاقة بين القسم والمادة:', selectedClass, classSubjects);
          toast.error('لم يتم العثور على علاقة بين القسم والمادة');
          return prevGrades;
        }

        const classSubjectId = classSubject.id;

        const newGrade: Grade = {
          studentId,
          examId: selectedExam,
          classSubjectId,
          grade: field === 'grade' ? value : '',
          note: field === 'note' ? value : '',
          month: new Date().toISOString().slice(0, 7)
        };
        setHasUnsavedChanges(true);
        return [...prevGrades, newGrade];
      }
    });
  };

  const handleSubmit = async () => {
    // إعادة تعيين رسائل الخطأ
    setErrors(prev => ({ ...prev, save: '' }));
    setSuccess('');

    if (!selectedClass || !selectedExam) {
      setErrors(prev => ({ ...prev, save: 'يرجى اختيار القسم والامتحان' }));
      return;
    }

    if (selectedExamRequiresSurah && !selectedSurah) {
      setErrors(prev => ({ ...prev, save: 'يرجى اختيار السورة' }));
      return;
    }

    if (students.length === 0) {
      setErrors(prev => ({ ...prev, save: 'لا يوجد طلاب لإدخال نقاط لهم' }));
      return;
    }

    // التحقق من وجود علاقة بين القسم والمادة
    const classSubject = classSubjects.find(cs => cs.classeId === selectedClass);
    if (!classSubject) {
      setErrors(prev => ({ ...prev, save: 'لم يتم العثور على علاقة بين القسم والمادة. يرجى التحقق من إعدادات القسم والمادة.' }));
      return;
    }

    setLoadingSave(true);

    try {
      // تحديد classSubjectId المناسب
      const classSubject = classSubjects.find(cs => cs.classeId === selectedClass);

      if (!classSubject) {
        throw new Error('لم يتم العثور على علاقة بين القسم والمادة. يرجى التحقق من إعدادات القسم والمادة.');
      }

      const classSubjectId = classSubject.id;

      for (const grade of grades) {
        if (!grade.studentId || !grade.grade) {
          throw new Error('يرجى إدخال درجة صحيحة لجميع الطلاب');
        }

        const numericGrade = Number(grade.grade);
        const validation = validateGrade(numericGrade, maxPoints);
        if (!validation.isValid) {
          throw new Error(validation.error || `يجب أن تكون الدرجة رقماً بين 0 و ${maxPoints}`);
        }

        const gradeData = {
          examId: Number(selectedExam),
          studentId: Number(grade.studentId),
          classSubjectId: Number(classSubjectId),
          grade: numericGrade,
          note: grade.note || '',
          // المشرف لديه صلاحيات كاملة، لذلك لا نحتاج إلى إرسال معرف المعلم
          // teacherId: 1, // تم إزالة هذا السطر لأنه كان يسبب مشكلة في الصلاحيات
        };

        // إضافة معلومات السورة إذا كان الامتحان يتطلب ذلك
        if (selectedExamRequiresSurah && selectedSurah) {
          Object.assign(gradeData, {
            surahId: Number(selectedSurah),
            startVerse: startVerse ? Number(startVerse) : null,
            endVerse: endVerse ? Number(endVerse) : null,
          });
        }

        const response = await fetch('/api/exam-points', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(gradeData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData?.error || errorData?.message || 'فشل في حفظ النقاط');
        }
      }

      setSuccess('تم حفظ النقاط بنجاح');
      setHasUnsavedChanges(false);
      toast.success('تم حفظ نقاط الامتحان بنجاح');

      // العودة إلى صفحة النتائج
      router.push(`/admin/evaluation/results?examId=${selectedExam}`);
    } catch (err) {
      console.error('Error saving grades:', err);
      setErrors(prev => ({ ...prev, save: err instanceof Error ? err.message : 'فشل في حفظ النقاط' }));
      toast.error(err instanceof Error ? err.message : 'فشل في حفظ النقاط');
    } finally {
      setLoadingSave(false);
    }
  };

  const getEvaluationTypeLabel = (type: string) => {
    const evaluationTypes = {
      'WRITTEN_EXAM': 'امتحان تحريري',
      'ORAL_EXAM': 'امتحان شفوي',
      'HOMEWORK': 'واجب منزلي',
      'PROJECT': 'مشروع',
      'QURAN_RECITATION': 'تلاوة القرآن',
      'QURAN_MEMORIZATION': 'حفظ القرآن',
      'PRACTICAL_TEST': 'اختبار عملي',
      'REMOTE_EXAM': 'امتحان عن بعد'
    };
    return evaluationTypes[type as keyof typeof evaluationTypes] || type;
  };

  return (
    <ProtectedRoute requiredPermission="admin.evaluation.scoring.view">
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <Card className="p-6 border-t-4 border-[var(--primary-color)] shadow-md">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 text-right">تسجيل نقاط الامتحان</h1>
          <Link href="/admin/evaluation/dashboard">
            <Button variant="outline" className="flex items-center">
              <ArrowRight className="ml-2" size={16} />
              العودة إلى لوحة التحكم
            </Button>
          </Link>
        </div>

        <div className="space-y-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-right mb-2">الامتحان</label>
              <Select
                value={selectedExam || ''}
                onValueChange={setSelectedExam}
                disabled={loadingExams}
              >
                <SelectTrigger className="text-right">
                  <SelectValue placeholder={loadingExams ? 'جاري التحميل...' : 'اختر الامتحان'} />
                </SelectTrigger>
                <SelectContent>
                  {exams.map((exam) => (
                    <SelectItem key={exam.id} value={exam.id}>
                      {getEvaluationTypeLabel(exam.evaluationType)} - {exam.month} {exam.description ? `(${exam.description})` : ''}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.exams && (
                <div className="text-red-500 text-right text-sm mt-1">{errors.exams}</div>
              )}
            </div>

            <div>
              <label className="block text-right mb-2">القسم</label>
              <Select
                value={selectedClass || ''}
                onValueChange={setSelectedClass}
                disabled={loadingClasses}
              >
                <SelectTrigger className="text-right">
                  <SelectValue placeholder={loadingClasses ? 'جاري التحميل...' : 'اختر القسم'} />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(classes) && classes.length > 0 ? (
                    classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="no-classes" disabled>
                      لا توجد أقسام
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.classes && (
                <div className="text-red-500 text-right text-sm mt-1">{errors.classes}</div>
              )}
            </div>

            {selectedExamRequiresSurah && (
              <>
                <div>
                  <label className="block text-right mb-2">السورة</label>
                  <Select
                    value={selectedSurah || ''}
                    onValueChange={setSelectedSurah}
                    disabled={loadingSurahs}
                  >
                    <SelectTrigger className="text-right">
                      <SelectValue placeholder={loadingSurahs ? 'جاري التحميل...' : 'اختر السورة'} />
                    </SelectTrigger>
                    <SelectContent>
                      {surahs.map((surah) => (
                        <SelectItem key={surah.id} value={surah.id}>
                          {surah.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.surahs && (
                    <div className="text-red-500 text-right text-sm mt-1">{errors.surahs}</div>
                  )}
                </div>

                <div>
                  <label className="block text-right mb-2">من الآية</label>
                  <Input
                    type="number"
                    min="1"
                    value={startVerse}
                    onChange={(e) => setStartVerse(e.target.value)}
                    className="text-right"
                    placeholder="رقم الآية البداية"
                  />
                </div>

                <div>
                  <label className="block text-right mb-2">إلى الآية</label>
                  <Input
                    type="number"
                    min="1"
                    value={endVerse}
                    onChange={(e) => setEndVerse(e.target.value)}
                    className="text-right"
                    placeholder="رقم الآية النهاية"
                  />
                </div>
              </>
            )}
          </div>
        </div>

        {errors.save && (
          <div className="text-red-500 text-right text-sm mb-4">{errors.save}</div>
        )}

        {success && (
          <div className="text-primary-color text-right text-sm mb-4">{success}</div>
        )}

        {errors.students && (
          <div className="text-red-500 text-right text-sm mb-4">{errors.students}</div>
        )}

        {loadingStudents ? (
          <div className="flex flex-col justify-center items-center h-40 bg-[#f8fffd] rounded-lg border border-[#e9f7f5] shadow-md">
            <div className="relative">
              <Loader2 className="h-12 w-12 animate-spin text-[var(--primary-color)]" />
              <div className="absolute inset-0 animate-pulse bg-[#e9f7f5] rounded-full opacity-30"></div>
            </div>
            <span className="mr-2 mt-4 text-[var(--primary-color)] font-medium">جاري تحميل الطلاب...</span>
          </div>
        ) : students.length === 0 ? (
          <div className="text-center text-gray-500 py-8 border border-[#e9f7f5] rounded-lg bg-[#f8fffd]">
            {selectedClass ? 'لا يوجد طلاب في هذا القسم' : 'يرجى اختيار القسم لعرض الطلاب'}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">اسم الطالب</TableHead>
                <TableHead className="text-right">الدرجة (من {maxPoints})</TableHead>
                <TableHead className="text-right">الملاحظات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {students.map((student) => {
                const studentGrade = grades.find((g: Grade) => g.studentId === student.id);
                return (
                  <TableRow key={student.id}>
                    <TableCell className="text-right">{student.name}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <Input
                          type="number"
                          min="0"
                          max={maxPoints}
                          step="0.5"
                          value={studentGrade ? (studentGrade as Grade).grade || '' : ''}
                          onChange={(e) => handleGradeChange(student.id, 'grade', e.target.value)}
                          className="w-24"
                          placeholder={`0-${maxPoints}`}
                        />
                        {studentGrade?.grade && (
                          <div className="text-xs text-center">
                            {(() => {
                              const grade = Number(studentGrade.grade);
                              const percentage = calculatePercentage(grade, maxPoints);
                              const gradeLevel = getGradeLevelByScore(grade, maxPoints);
                              return (
                                <span
                                  className="px-1 py-0.5 rounded text-xs font-semibold"
                                  style={{ backgroundColor: gradeLevel.color + '20', color: gradeLevel.color }}
                                >
                                  {percentage}% - {gradeLevel.nameAr}
                                </span>
                              );
                            })()}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Textarea
                        value={studentGrade ? (studentGrade as Grade).note || '' : ''}
                        onChange={(e) => handleGradeChange(student.id, 'note', e.target.value)}
                        className="w-full"
                        rows={1}
                      />
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        )}

        <div className="mt-6 text-left">
          <Button
            onClick={handleSubmit}
            disabled={loadingSave || students.length === 0}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
          >
            {loadingSave ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                جاري الحفظ...
              </>
            ) : (
              'حفظ النقاط'
            )}
          </Button>
        </div>
      </Card>
      </div>
    </ProtectedRoute>
  );
}

'use client';

import React, { useState, useEffect, useCallback, useReducer } from 'react'
import { useDebouncedCallback } from 'use-debounce';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';
import { exportToExcel } from '@/utils/export-utils';

import {
  FaUserFriends, FaSearch, FaSync, FaFileExcel, FaEye,
  FaMoneyBillWave, FaExclamationTriangle, FaPlus,
  FaUsers, FaCoins, FaPer<PERSON>, FaSave, FaTimes, FaFileInvoice
} from 'react-icons/fa';

// واجهات البيانات
interface StudentPaymentSummary {
  id: number;
  name: string;
  grade: string;
  totalRequired: number;
  totalPaid: number;
  totalRemaining: number;
  dueInvoices: number;
  lastPaymentDate?: string;
  paymentStatus: 'PAID' | 'PARTIAL' | 'UNPAID' | 'OVERDUE';
}

interface FamilyInvoice {
  id: number;
  amount: number;
  dueDate: string;
  description: string;
  status: string;
  totalPaid: number;
}

interface ParentPaymentSummary {
  id: string;
  name: string;
  phone: string;
  email?: string;
  totalRequired: number;
  totalPaid: number;
  totalRemaining: number;
  totalStudents: number;
  lastPaymentDate?: string;
  paymentRate: number;
  // 🆕 حقول جديدة لتفاصيل مصادر المبالغ
  baseAmount?: number;           // المبلغ الأساسي من amountPerStudent
  invoicesAmount?: number;       // المبلغ من الفواتير
  amountPerStudent?: number;     // المبلغ المحدد لكل تلميذ
  amountSource?: 'BASE' | 'INVOICES' | 'BOTH'; // مصدر المبلغ
  students: StudentPaymentSummary[];
  familyInvoices?: FamilyInvoice[];
}

interface Statistics {
  totalParents: number;
  parentsWithDebts: number;
  totalDebtAmount: number;
  totalPaidAmount: number;
  averagePaymentRate: number;
}

// إدارة حالة محسنة باستخدام useReducer
interface PaymentState {
  parents: ParentPaymentSummary[];
  statistics: Statistics;
  loading: boolean;
  error: string | null;
  filters: {
    search: string;
    status: string;
    month: string;
  };
}

type PaymentAction =
  | { type: 'FETCH_START' }
  | { type: 'FETCH_SUCCESS'; payload: { parents: ParentPaymentSummary[]; statistics: Statistics } }
  | { type: 'FETCH_ERROR'; payload: string }
  | { type: 'UPDATE_FILTERS'; payload: Partial<PaymentState['filters']> }
  | { type: 'RESET_ERROR' };

const paymentReducer = (state: PaymentState, action: PaymentAction): PaymentState => {
  switch (action.type) {
    case 'FETCH_START':
      return { ...state, loading: true, error: null };
    case 'FETCH_SUCCESS':
      return {
        ...state,
        loading: false,
        parents: action.payload.parents,
        statistics: action.payload.statistics,
        error: null
      };
    case 'FETCH_ERROR':
      return { ...state, loading: false, error: action.payload };
    case 'UPDATE_FILTERS':
      return { ...state, filters: { ...state.filters, ...action.payload } };
    case 'RESET_ERROR':
      return { ...state, error: null };
    default:
      return state;
  }
};

const initialState: PaymentState = {
  parents: [],
  statistics: {
    totalParents: 0,
    parentsWithDebts: 0,
    totalDebtAmount: 0,
    totalPaidAmount: 0,
    averagePaymentRate: 0
  },
  loading: true,
  error: null,
  filters: {
    search: '',
    status: '',
    month: ''
  }
};

export default function PaymentsByParentPage() {
  const [state, dispatch] = useReducer(paymentReducer, initialState);

  // حالات نموذج إضافة الدفعة
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedParentForPayment, setSelectedParentForPayment] = useState<ParentPaymentSummary | null>(null);
  const [paymentData, setPaymentData] = useState({
    studentId: '',
    amount: '',
    paymentMethod: 'نقداً',
    notes: '',
    receiptNumber: '',
    payForAllStudents: false,
    month: new Date().toISOString().slice(0, 7) // الشهر الحالي بصيغة YYYY-MM
  });
  const [isSubmittingPayment, setIsSubmittingPayment] = useState(false);

  // حالات نافذة تفاصيل الولي
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedParentForDetails, setSelectedParentForDetails] = useState<ParentPaymentSummary | null>(null);

  const { addToast } = useToast();

  // جلب بيانات المدفوعات حسب الولي مع إدارة حالة محسنة
  const fetchPaymentsByParent = useCallback(async () => {
    try {
      dispatch({ type: 'FETCH_START' });

      const queryParams = new URLSearchParams({
        search: state.filters.search,
        status: state.filters.status,
        month: state.filters.month,
        limit: '100'
      });

      console.log('🔍 الفلاتر المطبقة:', {
        search: state.filters.search,
        status: state.filters.status,
        month: state.filters.month,
        url: `/api/payments/by-parent?${queryParams}`
      });

      const response = await fetch(`/api/payments/by-parent?${queryParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'فشل في جلب البيانات');
      }

      dispatch({
        type: 'FETCH_SUCCESS',
        payload: {
          parents: data.data || [],
          statistics: data.statistics || initialState.statistics
        }
      });

      console.log('✅ تم جلب بيانات المدفوعات حسب الولي:', {
        parentsCount: data.data?.length || 0,
        statistics: data.statistics
      });

      // طباعة تفصيلية لبيانات كل ولي للتشخيص
      console.log('📋 تفاصيل بيانات الأولياء المستلمة من API:');
      data.data?.forEach((parent, index) => {
        console.log(`👤 الولي ${index + 1}: ${parent.name}`, {
          totalRequired: parent.totalRequired,
          totalPaid: parent.totalPaid,
          totalRemaining: parent.totalRemaining,
          studentsCount: parent.students?.length || 0,
          students: parent.students?.map(student => ({
            name: student.name,
            totalRequired: student.totalRequired,
            totalPaid: student.totalPaid,
            totalRemaining: student.totalRemaining,
            paymentStatus: student.paymentStatus
          })) || []
        });
      });

    } catch (error) {
      console.error('❌ خطأ في جلب البيانات:', error);
      const errorMessage = error instanceof Error ? error.message : 'فشل في جلب بيانات المدفوعات';

      dispatch({ type: 'FETCH_ERROR', payload: errorMessage });

      addToast({
        title: 'خطأ',
        description: errorMessage,
        variant: 'destructive'
      });
    }
  }, [state.filters.search, state.filters.status, state.filters.month, addToast]);

  // تحديث البيانات عند تغيير الفلاتر
  useEffect(() => {
    fetchPaymentsByParent();
  }, [fetchPaymentsByParent]);

  // دوال تحديث الفلاتر مع debouncing للبحث
  const updateSearchFilter = useDebouncedCallback((search: string) => {
    dispatch({ type: 'UPDATE_FILTERS', payload: { search } });
  }, 300);

  const updateStatusFilter = (status: string) => {
    dispatch({ type: 'UPDATE_FILTERS', payload: { status } });
  };

  const updateMonthFilter = (month: string) => {
    dispatch({ type: 'UPDATE_FILTERS', payload: { month } });
  };

  // تنسيق العملة
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('fr-FR', { minimumFractionDigits: 2 })} دج`;
  };

  // تحديد لون حالة الدفع
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'bg-green-100 text-green-800';
      case 'PARTIAL':
        return 'bg-blue-100 text-blue-800';
      case 'UNPAID':
        return 'bg-yellow-100 text-yellow-800';
      case 'OVERDUE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // تحديد نص حالة الدفع
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'مدفوع';
      case 'PARTIAL':
        return 'جزئي';
      case 'UNPAID':
        return 'غير مدفوع';
      case 'OVERDUE':
        return 'متأخر';
      default:
        return 'غير محدد';
    }
  };

  // تحديد حالة الولي بناءً على المبالغ والفواتير
  const getParentStatus = (parent: ParentPaymentSummary) => {
    // إذا لم تكن هناك فواتير مستحقة أو تم دفع كل شيء
    if (parent.totalRequired === 0 || parent.totalRemaining <= 0) return 'PAID';

    // إذا كان هناك فواتير متأخرة
    if (parent.students.some(s => s.paymentStatus === 'OVERDUE')) return 'OVERDUE';

    // إذا تم دفع جزء من المطلوب
    if (parent.totalRequired > 0 && parent.totalPaid > 0 && parent.totalRemaining > 0) return 'PARTIAL';

    // إذا لم يتم دفع أي شيء مع وجود فواتير مستحقة
    if (parent.totalRequired > 0 && parent.totalPaid === 0) return 'UNPAID';

    return 'UNPAID';
  };

  // حساب المبلغ تلقائياً من معلومات الولي
  const calculateAmountFromParentInfo = async () => {
    if (!selectedParentForPayment) return;

    try {
      // جلب معلومات الولي من قاعدة البيانات للحصول على amountPerStudent
      const response = await fetch(`/api/parents/${selectedParentForPayment.id}`);
      const data = await response.json();

      if (data.success && data.parent.amountPerStudent) {
        const amountPerStudent = data.parent.amountPerStudent;
        let totalAmount = 0;

        if (paymentData.payForAllStudents) {
          // حساب المبلغ للتلاميذ الذين لديهم ديون فقط
          const studentsWithDebt = selectedParentForPayment.students.filter(s => s.totalRemaining > 0);
          totalAmount = amountPerStudent * studentsWithDebt.length;
        } else if (paymentData.studentId) {
          // حساب المبلغ لتلميذ واحد
          totalAmount = amountPerStudent;
        }

        setPaymentData(prev => ({
          ...prev,
          amount: totalAmount.toString()
        }));

        addToast({
          title: 'تم الحساب',
          description: `تم حساب المبلغ تلقائياً: ${formatCurrency(totalAmount)}`,
          variant: 'default'
        });
      } else {
        addToast({
          title: 'تنبيه',
          description: 'لم يتم تحديد مبلغ لكل تلميذ في معلومات الولي',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('خطأ في حساب المبلغ:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في حساب المبلغ تلقائياً',
        variant: 'destructive'
      });
    }
  };

  // عرض تفاصيل الولي
  const showParentDetails = (parent: ParentPaymentSummary) => {
    setSelectedParentForDetails(parent);
    setIsDetailsModalOpen(true);
  };

  // إغلاق نافذة تفاصيل الولي
  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedParentForDetails(null);
  };

  // فتح نموذج إضافة دفعة جديدة
  const openPaymentModal = (parent?: ParentPaymentSummary) => {
    if (parent) {
      console.log('🔍 فتح نموذج الدفعة للولي:', parent.name);
      console.log('📊 بيانات الولي:', {
        totalRequired: parent.totalRequired,
        totalPaid: parent.totalPaid,
        totalRemaining: parent.totalRemaining,
        studentsCount: parent.students.length
      });

      // طباعة تفاصيل كل تلميذ
      parent.students.forEach(student => {
        console.log(`👤 التلميذ ${student.name}:`, {
          totalRequired: student.totalRequired,
          totalPaid: student.totalPaid,
          totalRemaining: student.totalRemaining,
          paymentStatus: student.paymentStatus
        });
      });

      setSelectedParentForPayment(parent);
      setPaymentData({
        studentId: parent.students.length === 1 ? parent.students[0].id.toString() : '',
        amount: '',
        paymentMethod: 'نقداً',
        notes: `دفعة مسجلة باسم الولي: ${parent.name}`,
        receiptNumber: '',
        payForAllStudents: false,
        month: new Date().toISOString().slice(0, 7)
      });
    } else {
      setSelectedParentForPayment(null);
      setPaymentData({
        studentId: '',
        amount: '',
        paymentMethod: 'نقداً',
        notes: '',
        receiptNumber: '',
        payForAllStudents: false,
        month: new Date().toISOString().slice(0, 7)
      });
    }
    setIsPaymentModalOpen(true);
  };

  // إغلاق نموذج الدفعة
  const closePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedParentForPayment(null);
    setPaymentData({
      studentId: '',
      amount: '',
      paymentMethod: 'نقداً',
      notes: '',
      receiptNumber: '',
      payForAllStudents: false,
      month: new Date().toISOString().slice(0, 7)
    });
  };

  // تسجيل دفعة جديدة
  const handlePaymentSubmit = async () => {
    if (!paymentData.amount || !paymentData.month || (!paymentData.payForAllStudents && !paymentData.studentId)) {
      addToast({
        title: 'خطأ',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      });
      return;
    }

    // التحقق من وجود ديون فعلية
    const parent = selectedParentForPayment ||
      filteredParents.find(p => p.students.some(s => s.id.toString() === paymentData.studentId));

    if (!parent) {
      addToast({
        title: 'خطأ',
        description: 'لم يتم العثور على الولي',
        variant: 'destructive'
      });
      return;
    }

    // التحقق من وجود ديون على مستوى الولي أولاً
    console.log('🔍 التحقق من ديون الولي الإجمالية...');
    console.log('📊 بيانات الولي الإجمالية:', {
      name: parent.name,
      totalRequired: parent.totalRequired,
      totalPaid: parent.totalPaid,
      totalRemaining: parent.totalRemaining
    });

    // إذا لم تكن هناك ديون على مستوى الولي الإجمالي
    if (parent.totalRemaining <= 0) {
      console.log('❌ لا توجد ديون مستحقة على الولي إجمالياً');
      addToast({
        title: 'تنبيه',
        description: `لا توجد ديون مستحقة على عائلة ${parent.name}. جميع الفواتير مدفوعة بالكامل.`,
        variant: 'destructive'
      });
      return;
    }

    console.log('✅ الولي لديه ديون إجمالية:', parent.totalRemaining);

    // التحقق من وجود ديون للدفع الجماعي
    if (paymentData.payForAllStudents) {
      console.log('🔍 التحقق من الديون للدفع الجماعي...');
      const studentsWithDebt = parent.students.filter(student => student.totalRemaining > 0);
      console.log('📊 التلاميذ الذين لديهم ديون فردية:', studentsWithDebt.map(s => ({
        name: s.name,
        totalRemaining: s.totalRemaining
      })));

      // إذا لم يكن هناك تلاميذ لديهم ديون فردية، لكن الولي لديه ديون إجمالية
      // نسمح بالدفع الجماعي ونوزع المبلغ على جميع التلاميذ
      if (studentsWithDebt.length === 0 && parent.totalRemaining > 0) {
        console.log('⚠️ لا توجد ديون فردية للتلاميذ، لكن توجد ديون إجمالية للولي');
        console.log('✅ السماح بالدفع الجماعي لجميع التلاميذ (ديون جماعية)');

        // تحديث قائمة التلاميذ لتشمل جميع التلاميذ في حالة الديون الجماعية
        const allStudents = parent.students.filter(student => student.id); // جميع التلاميذ
        if (allStudents.length === 0) {
          addToast({
            title: 'خطأ',
            description: `لا يوجد تلاميذ مسجلين للولي ${parent.name}`,
            variant: 'destructive'
          });
          return;
        }
        console.log('✅ سيتم الدفع لجميع التلاميذ:', allStudents.length, 'تلميذ');
      } else if (studentsWithDebt.length === 0) {
        console.log('❌ لا توجد ديون مستحقة نهائياً');
        addToast({
          title: 'تنبيه',
          description: `لا توجد ديون مستحقة على عائلة ${parent.name}`,
          variant: 'destructive'
        });
        return;
      } else {
        console.log('✅ تم العثور على', studentsWithDebt.length, 'تلميذ لديهم ديون فردية');
      }
    } else {
      // للدفع الفردي، نحتاج تلميذ محدد
      const student = parent.students.find(s => s.id.toString() === paymentData.studentId);
      console.log('🔍 التحقق من ديون التلميذ المحدد:', paymentData.studentId);

      if (!student) {
        console.log('❌ لم يتم العثور على التلميذ');
        addToast({
          title: 'خطأ',
          description: 'لم يتم العثور على الطالب',
          variant: 'destructive'
        });
        return;
      }

      console.log('📊 بيانات التلميذ:', {
        name: student.name,
        totalRequired: student.totalRequired,
        totalPaid: student.totalPaid,
        totalRemaining: student.totalRemaining,
        paymentStatus: student.paymentStatus
      });

      // إذا لم تكن هناك ديون فردية للتلميذ لكن هناك ديون إجمالية للولي
      // نسمح بالدفع ونعتبره دفعة للديون الجماعية
      if (student.totalRemaining <= 0 && parent.totalRemaining > 0) {
        console.log('⚠️ لا توجد ديون فردية للتلميذ، لكن توجد ديون إجمالية للولي');
        console.log('✅ السماح بالدفع الفردي (ديون جماعية)');
      } else if (student.totalRemaining <= 0) {
        console.log('❌ لا توجد ديون مستحقة للتلميذ');
        addToast({
          title: 'تنبيه',
          description: `لا توجد ديون مستحقة للطالب: ${student.name}`,
          variant: 'destructive'
        });
        return;
      } else {
        console.log('✅ التلميذ لديه ديون فردية مستحقة:', student.totalRemaining);
      }
    }

    try {
      setIsSubmittingPayment(true);

      if (paymentData.payForAllStudents) {
        // تحديد التلاميذ للدفع الجماعي
        let studentsForPayment = parent.students.filter(student => student.totalRemaining > 0);

        // إذا لم يكن هناك تلاميذ لديهم ديون فردية لكن هناك ديون إجمالية
        // نستخدم جميع التلاميذ (حالة الديون الجماعية)
        if (studentsForPayment.length === 0 && parent.totalRemaining > 0) {
          studentsForPayment = parent.students.filter(student => student.id);
          console.log('📋 استخدام جميع التلاميذ للدفع الجماعي (ديون جماعية)');
        }

        const totalAmount = parseFloat(paymentData.amount);
        const amountPerStudent = totalAmount / studentsForPayment.length;

        console.log('💰 تفاصيل الدفع الجماعي:', {
          totalAmount,
          studentsCount: studentsForPayment.length,
          amountPerStudent,
          students: studentsForPayment.map(s => s.name)
        });

        const promises = studentsForPayment.map(student =>
          fetch('/api/admin/payments', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              studentId: student.id,
              amount: amountPerStudent,
              paymentMethod: paymentData.paymentMethod,
              notes: `${paymentData.notes}\n\nدفعة جماعية للشهر: ${paymentData.month}${studentsForPayment.length === parent.students.length ? ' (ديون جماعية)' : ''}`,
              receiptNumber: paymentData.receiptNumber,
              parentName: parent.name,
              month: paymentData.month
            })
          })
        );

        const responses = await Promise.all(promises);

        // التحقق من نجاح جميع الدفعات
        for (const response of responses) {
          if (!response.ok) {
            throw new Error('فشل في تسجيل إحدى الدفعات');
          }
        }

        addToast({
          title: 'تم بنجاح',
          description: `تم تسجيل ${studentsForPayment.length} دفعات بإجمالي ${formatCurrency(totalAmount)} باسم الولي: ${parent.name}${studentsForPayment.length === parent.students.length ? ' (ديون جماعية)' : ''}`,
          variant: 'default'
        });
      } else {
        // دفعة لتلميذ واحد
        const student = parent.students.find(s => s.id.toString() === paymentData.studentId);
        if (!student) {
          throw new Error('لم يتم العثور على الطالب');
        }

        const response = await fetch('/api/admin/payments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            studentId: parseInt(paymentData.studentId),
            amount: parseFloat(paymentData.amount),
            paymentMethod: paymentData.paymentMethod,
            notes: `${paymentData.notes}\n\nدفعة للشهر: ${paymentData.month}`,
            receiptNumber: paymentData.receiptNumber,
            parentName: parent.name,
            month: paymentData.month
          })
        });

        if (!response.ok) {
          throw new Error('فشل في تسجيل الدفعة');
        }

        addToast({
          title: 'تم بنجاح',
          description: `تم تسجيل دفعة بقيمة ${formatCurrency(parseFloat(paymentData.amount))} للطالب: ${student.name}`,
          variant: 'default'
        });
      }

      // إعادة تحميل البيانات
      await fetchPaymentsByParent();
      closePaymentModal();

    } catch (error) {
      console.error('خطأ في تسجيل الدفعة:', error);
      addToast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في تسجيل الدفعة',
        variant: 'destructive'
      });
    } finally {
      setIsSubmittingPayment(false);
    }
  };

  // تصدير البيانات إلى Excel
  const handleExportToExcel = () => {
    const exportData = state.parents.map(parent => ({
      'اسم الولي': parent.name,
      'رقم الهاتف': parent.phone,
      'البريد الإلكتروني': parent.email || '',
      'عدد الأبناء': parent.totalStudents,
      'إجمالي المطلوب': parent.totalRequired,
      'إجمالي المدفوع': parent.totalPaid,
      'إجمالي المتبقي': parent.totalRemaining,
      'معدل السداد %': parent.paymentRate,
      'آخر دفعة': parent.lastPaymentDate || 'لا يوجد',
      'الحالة': getPaymentStatusText(getParentStatus(parent))
    }));

    exportToExcel(exportData, 'مدفوعات_الأولياء.xlsx', 'مدفوعات الأولياء');

    addToast({
      title: 'تم بنجاح',
      description: 'تم تصدير البيانات إلى Excel',
      variant: 'default'
    });
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.payments.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen" dir="rtl">
        {/* رأس الصفحة */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
              <FaUserFriends className="text-[var(--primary-color)]" />
              المدفوعات حسب الولي
            </h1>
            {state.filters.month && (
              <p className="text-sm text-gray-600 mt-1 pr-3">
                📅 عرض بيانات شهر: {new Date(state.filters.month + '-01').toLocaleDateString('ar-DZ', { year: 'numeric', month: 'long' })}
              </p>
            )}
          </div>

          <div className="flex gap-2 w-full sm:w-auto justify-end">
            <QuickActionButtons
              entityType="payments"
              actions={[
                {
                  key: 'add-payment',
                  label: 'إضافة دفعة جديدة',
                  icon: <FaPlus />,
                  onClick: () => openPaymentModal(),
                  variant: 'primary'
                },
                {
                  key: 'debug-api',
                  label: 'فحص API',
                  icon: <FaSearch />,
                  onClick: async () => {
                    console.log('🔍 فحص مباشر لـ API...');
                    try {
                      const response = await fetch('/api/payments/by-parent');
                      const data = await response.json();
                      console.log('📊 استجابة API الخام:', data);

                      if (data.data) {
                        data.data.forEach((parent, index) => {
                          console.log(`🔍 فحص الولي ${index + 1}: ${parent.name}`);
                          console.log('📋 البيانات الإجمالية:', {
                            totalRequired: parent.totalRequired,
                            totalPaid: parent.totalPaid,
                            totalRemaining: parent.totalRemaining
                          });

                          if (parent.students) {
                            parent.students.forEach((student, sIndex) => {
                              console.log(`👤 التلميذ ${sIndex + 1}: ${student.name}`, {
                                totalRequired: student.totalRequired,
                                totalPaid: student.totalPaid,
                                totalRemaining: student.totalRemaining,
                                paymentStatus: student.paymentStatus
                              });
                            });
                          }
                        });
                      }
                    } catch (error) {
                      console.error('❌ خطأ في فحص API:', error);
                    }
                  },
                  variant: 'secondary'
                },
                {
                  key: 'clear-filters',
                  label: 'إزالة الفلاتر',
                  icon: <FaTimes />,
                  onClick: () => {
                    console.log('🧹 إزالة جميع الفلاتر...');
                    dispatch({
                      type: 'UPDATE_FILTERS',
                      payload: { search: '', status: '', month: '' }
                    });
                  },
                  variant: 'secondary'
                },
                {
                  key: 'refresh',
                  label: 'تحديث',
                  icon: <FaSync />,
                  onClick: fetchPaymentsByParent,
                  variant: 'secondary'
                },
                {
                  key: 'export',
                  label: 'تصدير Excel',
                  icon: <FaFileExcel />,
                  onClick: handleExportToExcel,
                  variant: 'secondary'
                }
              ]}
              className="w-full sm:w-auto"
            />
          </div>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الأولياء</CardTitle>
              <FaUsers className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{state.statistics.totalParents}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">لديهم ديون</CardTitle>
              <FaExclamationTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{state.statistics.parentsWithDebts}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الديون</CardTitle>
              <FaMoneyBillWave className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold text-red-600">
                {formatCurrency(state.statistics.totalDebtAmount)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المدفوعات</CardTitle>
              <FaCoins className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold text-green-600">
                {formatCurrency(state.statistics.totalPaidAmount)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">معدل التحصيل</CardTitle>
              <FaPercent className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{state.statistics.averagePaymentRate}%</div>
            </CardContent>
          </Card>
        </div>

        {/* شريط البحث والفلترة */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="البحث بالاسم أو الهاتف أو البريد الإلكتروني..."
                    value={state.filters.search}
                    onChange={(e) => updateSearchFilter(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <div className="w-full sm:w-48">
                <select
                  value={state.filters.status}
                  onChange={(e) => updateStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع الحالات</option>
                  <option value="PAID">مدفوع بالكامل</option>
                  <option value="PARTIAL">مدفوع جزئياً</option>
                  <option value="UNPAID">غير مدفوع</option>
                  <option value="OVERDUE">متأخر</option>
                </select>
              </div>

              <div className="w-full sm:w-48">
                <Input
                  type="month"
                  value={state.filters.month}
                  onChange={(e) => updateMonthFilter(e.target.value)}
                  placeholder="اختر الشهر"
                  className="w-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول المدفوعات */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة الأولياء ومدفوعاتهم</CardTitle>
            <CardDescription>
              عرض تفصيلي لجميع الأولياء والمبالغ المطلوبة والمدفوعة لكل منهم
            </CardDescription>
          </CardHeader>
          <CardContent>
            {state.loading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
              </div>
            ) : state.error ? (
              <div className="flex flex-col items-center justify-center h-32 text-center">
                <div className="text-red-600 text-lg mb-2">⚠️ حدث خطأ</div>
                <p className="text-gray-600 mb-4">{state.error}</p>
                <Button
                  onClick={() => {
                    dispatch({ type: 'RESET_ERROR' });
                    fetchPaymentsByParent();
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  إعادة المحاولة
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="text-right p-3 font-semibold">الولي</th>
                      <th className="text-right p-3 font-semibold">عدد الأبناء</th>
                      <th className="text-right p-3 font-semibold">إجمالي المطلوب</th>
                      <th className="text-right p-3 font-semibold">تفاصيل المبلغ</th>
                      <th className="text-right p-3 font-semibold">المدفوع</th>
                      <th className="text-right p-3 font-semibold">المتبقي</th>
                      <th className="text-right p-3 font-semibold">معدل السداد</th>
                      <th className="text-right p-3 font-semibold">آخر دفعة</th>
                      <th className="text-right p-3 font-semibold">الحالة</th>
                      <th className="text-right p-3 font-semibold">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {state.parents.length === 0 ? (
                      <tr>
                        <td colSpan={10} className="text-center p-8 text-gray-500">
                          <div className="flex flex-col items-center gap-2">
                            <FaSearch className="text-4xl text-gray-300" />
                            <p className="text-lg font-medium">لا توجد نتائج</p>
                            <p className="text-sm">
                              {state.filters.month
                                ? `لا توجد مدفوعات للشهر المحدد: ${new Date(state.filters.month + '-01').toLocaleDateString('ar-DZ', { year: 'numeric', month: 'long' })}`
                                : state.filters.search
                                  ? 'لا توجد نتائج مطابقة للبحث'
                                  : 'لا توجد بيانات للعرض'
                              }
                            </p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      state.parents.map((parent) => (
                        <tr key={parent.id} className="border-b hover:bg-gray-50">
                          <td className="p-3">
                            <div>
                              <div className="font-medium">{parent.name}</div>
                              <div className="text-sm text-gray-500">{parent.phone}</div>
                              {parent.email && (
                                <div className="text-sm text-gray-500">{parent.email}</div>
                              )}
                            </div>
                          </td>
                          <td className="p-3 text-center">
                            <Badge variant="outline">{parent.totalStudents}</Badge>
                          </td>
                          <td className="p-3 font-medium">
                            {formatCurrency(parent.totalRequired)}
                          </td>
                          <td className="p-3">
                            {/* 🆕 عرض تفاصيل مصادر المبالغ */}
                            <div className="text-xs space-y-1">
                              {parent.amountSource === 'BASE' && (
                                <div className="flex items-center gap-1">
                                  <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                                  <span>أساسي: {formatCurrency(parent.baseAmount || 0)}</span>
                                </div>
                              )}
                              {parent.amountSource === 'INVOICES' && (
                                <div className="flex items-center gap-1">
                                  <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                                  <span>فواتير: {formatCurrency(parent.invoicesAmount || 0)}</span>
                                </div>
                              )}
                              {parent.amountSource === 'BOTH' && (
                                <>
                                  <div className="flex items-center gap-1">
                                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                                    <span>أساسي: {formatCurrency(parent.baseAmount || 0)}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                                    <span>فواتير: {formatCurrency(parent.invoicesAmount || 0)}</span>
                                  </div>
                                </>
                              )}
                              {parent.amountPerStudent && (
                                <div className="text-gray-500 text-xs">
                                  ({formatCurrency(parent.amountPerStudent)}/تلميذ)
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="p-3 text-green-600 font-medium">
                            {formatCurrency(parent.totalPaid)}
                          </td>
                          <td className="p-3 text-red-600 font-medium">
                            {formatCurrency(parent.totalRemaining)}
                          </td>
                          <td className="p-3">
                            <div className="flex items-center gap-2">
                              <div className="w-16 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full"
                                  style={{ width: `${parent.paymentRate}%` }}
                                ></div>
                              </div>
                              <span className="text-sm font-medium">{parent.paymentRate}%</span>
                            </div>
                          </td>
                          <td className="p-3 text-sm">
                            {parent.lastPaymentDate || 'لا يوجد'}
                          </td>
                          <td className="p-3">
                            <Badge className={getPaymentStatusColor(getParentStatus(parent))}>
                              {getPaymentStatusText(getParentStatus(parent))}
                            </Badge>
                          </td>
                          <td className="p-3">
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => openPaymentModal(parent)}
                                title="إضافة دفعة سريعة"
                                className="text-green-600 hover:text-green-700"
                              >
                                <FaPlus />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => showParentDetails(parent)}
                                title="عرض التفاصيل"
                              >
                                <FaEye className="text-blue-500" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نموذج إضافة دفعة جديدة */}
        {isPaymentModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" dir="rtl">
            <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  إضافة دفعة جديدة
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={closePaymentModal}
                  disabled={isSubmittingPayment}
                >
                  <FaTimes />
                </Button>
              </div>

              <div className="space-y-4">
                {/* اختيار الولي والطالب */}
                {!selectedParentForPayment ? (
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اختيار الولي والطالب
                    </label>
                    <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md">
                      {state.parents.map(parent => (
                        <div key={parent.id} className="border-b border-gray-100 last:border-b-0">
                          <div className="bg-gray-50 px-3 py-2 font-medium text-gray-800 text-sm">
                            👤 {parent.name} ({parent.phone})
                          </div>
                          <div className="px-4 py-1">
                            {parent.students
                              .filter(student => student.totalRemaining > 0)
                              .map(student => (
                              <label key={student.id} className="flex items-center py-2 hover:bg-blue-50 cursor-pointer">
                                <input
                                  type="radio"
                                  name="studentSelection"
                                  value={student.id}
                                  checked={paymentData.studentId === student.id.toString()}
                                  onChange={(e) => setPaymentData(prev => ({ ...prev, studentId: e.target.value }))}
                                  className="mr-3"
                                  disabled={isSubmittingPayment}
                                />
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900">{student.name}</div>
                                  <div className="text-sm text-gray-600">
                                    {student.grade} • متبقي: {formatCurrency(student.totalRemaining)}
                                    <span className="text-red-600 font-medium"> (مستحق)</span>
                                  </div>
                                </div>
                                <div className="text-xs">
                                  <Badge className={getPaymentStatusColor(student.paymentStatus)}>
                                    {getPaymentStatusText(student.paymentStatus)}
                                  </Badge>
                                </div>
                              </label>
                            ))}
                            {parent.students.filter(student => student.totalRemaining > 0).length === 0 && (
                              <div className="px-4 py-3 text-center text-gray-500 text-sm">
                                لا توجد ديون مستحقة لأي من التلاميذ
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="font-medium text-blue-900">👤 الولي: {selectedParentForPayment.name}</div>
                      <div className="text-sm text-blue-700">{selectedParentForPayment.phone}</div>
                    </div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اختيار الطالب
                    </label>
                    <div className="space-y-2">
                      {(() => {
                        // إذا لم يكن هناك تلاميذ لديهم ديون فردية لكن هناك ديون إجمالية
                        // نعرض جميع التلاميذ (حالة الديون الجماعية)
                        const studentsWithDebt = selectedParentForPayment.students.filter(student => student.totalRemaining > 0);
                        const studentsToShow = studentsWithDebt.length > 0
                          ? studentsWithDebt
                          : (selectedParentForPayment.totalRemaining > 0
                              ? selectedParentForPayment.students
                              : []);

                        return studentsToShow.map(student => (
                          <label key={student.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                            <input
                              type="radio"
                              name="studentSelection"
                              value={student.id}
                              checked={paymentData.studentId === student.id.toString()}
                              onChange={(e) => setPaymentData(prev => ({ ...prev, studentId: e.target.value }))}
                              className="mr-3"
                              disabled={isSubmittingPayment}
                            />
                            <div className="flex-1">
                              <div className="font-medium text-gray-900">{student.name}</div>
                              <div className="text-sm text-gray-600">
                                {student.grade} • متبقي: {formatCurrency(student.totalRemaining)}
                                {student.totalRemaining > 0 ? (
                                  <span className="text-red-600 font-medium"> (مستحق)</span>
                                ) : (
                                  <span className="text-blue-600 font-medium"> (ديون جماعية)</span>
                                )}
                              </div>
                            </div>
                            <div className="text-xs">
                              <Badge className={getPaymentStatusColor(student.paymentStatus)}>
                                {getPaymentStatusText(student.paymentStatus)}
                              </Badge>
                            </div>
                          </label>
                        ));
                      })()}
                      {selectedParentForPayment.students.filter(student => student.totalRemaining > 0).length === 0 && selectedParentForPayment.totalRemaining <= 0 && (
                        <div className="p-4 text-center text-gray-500 bg-gray-50 rounded-lg">
                          <p className="font-medium">لا توجد ديون مستحقة</p>
                          <p className="text-sm">جميع التلاميذ قد دفعوا مستحقاتهم</p>
                        </div>
                      )}
                      {selectedParentForPayment.students.filter(student => student.totalRemaining > 0).length === 0 && selectedParentForPayment.totalRemaining > 0 && (
                        <div className="p-4 text-center text-blue-100 bg-blue-50 rounded-lg border border-blue-200">
                          <p className="font-medium text-blue-900">ديون جماعية متاحة</p>
                          <p className="text-sm text-blue-700">يمكنك اختيار أي تلميذ لتسجيل دفعة للديون الجماعية</p>
                          <p className="text-xs text-blue-600 mt-1">المبلغ المتبقي: {formatCurrency(selectedParentForPayment.totalRemaining)}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* خيار الدفع لجميع التلاميذ */}
                {selectedParentForPayment && selectedParentForPayment.students.filter(s => s.totalRemaining > 0).length > 1 && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={paymentData.payForAllStudents}
                        onChange={(e) => {
                          setPaymentData(prev => ({
                            ...prev,
                            payForAllStudents: e.target.checked,
                            studentId: e.target.checked ? '' : prev.studentId
                          }));
                        }}
                        disabled={isSubmittingPayment}
                        className="w-4 h-4 text-blue-600"
                      />
                      <div>
                        <span className="font-medium text-blue-900">دفع لجميع التلاميذ الذين لديهم ديون</span>
                        <p className="text-sm text-blue-700">
                          سيتم تسجيل دفعة منفصلة لكل تلميذ ({selectedParentForPayment.students.filter(s => s.totalRemaining > 0).length} تلاميذ لديهم ديون)
                        </p>
                      </div>
                    </label>
                  </div>
                )}

                {/* الشهر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الشهر *
                  </label>
                  <Input
                    type="month"
                    value={paymentData.month}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, month: e.target.value }))}
                    disabled={isSubmittingPayment}
                    className="w-full"
                  />
                </div>

                {/* المبلغ */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      المبلغ (دج) *
                    </label>
                    {selectedParentForPayment && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={calculateAmountFromParentInfo}
                        disabled={isSubmittingPayment || (!paymentData.payForAllStudents && !paymentData.studentId)}
                        className="text-xs"
                      >
                        <FaCoins className="mr-1" />
                        حساب تلقائي
                      </Button>
                    )}
                  </div>
                  <Input
                    type="number"
                    value={paymentData.amount}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, amount: e.target.value }))}
                    placeholder="أدخل المبلغ"
                    disabled={isSubmittingPayment}
                    min="0"
                    step="0.01"
                  />
                  {paymentData.payForAllStudents && paymentData.amount && selectedParentForPayment && (
                    <p className="text-sm text-gray-600 mt-1">
                      المبلغ لكل تلميذ: {formatCurrency(parseFloat(paymentData.amount) / selectedParentForPayment.students.filter(s => s.totalRemaining > 0).length)}
                    </p>
                  )}
                </div>

                {/* طريقة الدفع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    طريقة الدفع
                  </label>
                  <select
                    value={paymentData.paymentMethod}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isSubmittingPayment}
                  >
                    <option value="نقداً">نقداً</option>
                    <option value="تحويل بنكي">تحويل بنكي</option>
                    <option value="شيك">شيك</option>
                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    <option value="أخرى">أخرى</option>
                  </select>
                </div>

                {/* رقم الإيصال */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الإيصال
                  </label>
                  <Input
                    type="text"
                    value={paymentData.receiptNumber}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, receiptNumber: e.target.value }))}
                    placeholder="رقم الإيصال (اختياري)"
                    disabled={isSubmittingPayment}
                  />
                </div>

                {/* الملاحظات */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الملاحظات
                  </label>
                  <textarea
                    value={paymentData.notes}
                    onChange={(e) => setPaymentData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder="ملاحظات إضافية"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    disabled={isSubmittingPayment}
                  />
                </div>
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex gap-3 mt-6">
                <Button
                  onClick={handlePaymentSubmit}
                  disabled={isSubmittingPayment || !paymentData.amount || !paymentData.month || (!paymentData.payForAllStudents && !paymentData.studentId)}
                  className="flex-1"
                >
                  {isSubmittingPayment ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2" />
                      حفظ الدفعة
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={closePaymentModal}
                  disabled={isSubmittingPayment}
                >
                  إلغاء
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* نافذة تفاصيل الولي */}
        {isDetailsModalOpen && selectedParentForDetails && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" dir="rtl">
            <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  <FaUserFriends className="text-blue-600" />
                  تفاصيل الولي: {selectedParentForDetails.name}
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={closeDetailsModal}
                >
                  <FaTimes />
                </Button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* معلومات الولي */}
                <div className="lg:col-span-1">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FaUserFriends className="text-blue-600" />
                        معلومات الولي
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <span className="font-medium text-gray-700">الاسم:</span>
                        <p className="text-gray-900">{selectedParentForDetails.name}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">رقم الهاتف:</span>
                        <p className="text-gray-900">{selectedParentForDetails.phone}</p>
                      </div>
                      {selectedParentForDetails.email && (
                        <div>
                          <span className="font-medium text-gray-700">البريد الإلكتروني:</span>
                          <p className="text-gray-900">{selectedParentForDetails.email}</p>
                        </div>
                      )}
                      <div>
                        <span className="font-medium text-gray-700">عدد الأبناء:</span>
                        <p className="text-gray-900">{selectedParentForDetails.totalStudents} طالب</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">آخر دفعة:</span>
                        <p className="text-gray-900">{selectedParentForDetails.lastPaymentDate || 'لا يوجد'}</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* ملخص مالي */}
                  <Card className="mt-4">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FaMoneyBillWave className="text-green-600" />
                        الملخص المالي
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">إجمالي المطلوب:</span>
                        <span className="font-bold text-gray-900">{formatCurrency(selectedParentForDetails.totalRequired)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">إجمالي المدفوع:</span>
                        <span className="font-bold text-green-600">{formatCurrency(selectedParentForDetails.totalPaid)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="font-medium text-gray-700">إجمالي المتبقي:</span>
                        <span className="font-bold text-red-600">{formatCurrency(selectedParentForDetails.totalRemaining)}</span>
                      </div>
                      <div className="pt-2 border-t">
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-gray-700">معدل السداد:</span>
                          <span className="font-bold text-blue-600">{selectedParentForDetails.paymentRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${selectedParentForDetails.paymentRate}%` }}
                          ></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* تفاصيل الأبناء والفواتير */}
                <div className="lg:col-span-2 space-y-4">
                  {/* الفواتير الجماعية */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FaFileInvoice className="text-orange-600" />
                        الفواتير الجماعية ({selectedParentForDetails.familyInvoices?.length || 0})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {selectedParentForDetails.familyInvoices && selectedParentForDetails.familyInvoices.length > 0 ? (
                        <div className="space-y-3">
                          {selectedParentForDetails.familyInvoices.map((invoice) => (
                            <div key={invoice.id} className="border border-gray-200 rounded-lg p-3">
                              <div className="flex justify-between items-start mb-2">
                                <div>
                                  <h5 className="font-medium text-gray-900">{invoice.description}</h5>
                                  <p className="text-sm text-gray-600">تاريخ الاستحقاق: {new Date(invoice.dueDate).toLocaleDateString('ar-DZ')}</p>
                                </div>
                                <Badge className={
                                  invoice.status === 'PAID' ? 'bg-green-100 text-green-800' :
                                  invoice.status === 'PARTIALLY_PAID' ? 'bg-blue-100 text-blue-800' :
                                  invoice.status === 'OVERDUE' ? 'bg-red-100 text-red-800' :
                                  'bg-yellow-100 text-yellow-800'
                                }>
                                  {invoice.status === 'PAID' ? 'مدفوعة' :
                                   invoice.status === 'PARTIALLY_PAID' ? 'مدفوعة جزئياً' :
                                   invoice.status === 'OVERDUE' ? 'متأخرة' : 'غير مدفوعة'}
                                </Badge>
                              </div>
                              <div className="grid grid-cols-3 gap-4 text-sm">
                                <div className="text-center">
                                  <p className="text-gray-600">المبلغ</p>
                                  <p className="font-bold text-gray-900">{formatCurrency(invoice.amount)}</p>
                                </div>
                                <div className="text-center">
                                  <p className="text-gray-600">المدفوع</p>
                                  <p className="font-bold text-green-600">{formatCurrency(invoice.totalPaid)}</p>
                                </div>
                                <div className="text-center">
                                  <p className="text-gray-600">المتبقي</p>
                                  <p className="font-bold text-red-600">{formatCurrency(invoice.amount - invoice.totalPaid)}</p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <FaFileInvoice className="mx-auto text-4xl text-gray-300 mb-3" />
                          <p className="text-gray-500 mb-4">لا توجد فواتير جماعية لهذا الولي</p>
                          <Button
                            size="sm"
                            className="bg-orange-600 hover:bg-orange-700"
                            onClick={() => {
                              // يمكن إضافة دالة لإنشاء فاتورة جماعية هنا
                              addToast({
                                title: 'قريباً',
                                description: 'ميزة إنشاء الفواتير الجماعية ستكون متاحة قريباً',
                                variant: 'default'
                              });
                            }}
                          >
                            <FaPlus className="mr-2" />
                            إنشاء فاتورة جماعية
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* تفاصيل الأبناء */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <FaUsers className="text-purple-600" />
                        تفاصيل الأبناء والمدفوعات الفردية
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {selectedParentForDetails.students.map((student) => (
                          <div key={student.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-3">
                              <div>
                                <h4 className="font-bold text-gray-900 text-lg">{student.name}</h4>
                                <p className="text-gray-600">{student.grade}</p>
                              </div>
                              <Badge className={getPaymentStatusColor(student.paymentStatus)}>
                                {getPaymentStatusText(student.paymentStatus)}
                              </Badge>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                              <div className="text-center">
                                <p className="text-sm text-gray-600">المطلوب</p>
                                <p className="font-bold text-gray-900">{formatCurrency(student.totalRequired)}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-sm text-gray-600">المدفوع</p>
                                <p className="font-bold text-green-600">{formatCurrency(student.totalPaid)}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-sm text-gray-600">المتبقي</p>
                                <p className="font-bold text-red-600">{formatCurrency(student.totalRemaining)}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-sm text-gray-600">فواتير مستحقة</p>
                                <p className="font-bold text-orange-600">{student.dueInvoices}</p>
                              </div>
                            </div>

                            {student.lastPaymentDate && (
                              <div className="text-sm text-gray-600">
                                آخر دفعة: {student.lastPaymentDate}
                              </div>
                            )}

                            <div className="mt-3 flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  closeDetailsModal();
                                  openPaymentModal(selectedParentForDetails);
                                  setPaymentData(prev => ({ ...prev, studentId: student.id.toString() }));
                                }}
                                className="text-green-600 hover:text-green-700"
                              >
                                <FaPlus className="mr-1" />
                                إضافة دفعة
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex gap-3 mt-6 justify-end">
                <Button
                  onClick={() => {
                    closeDetailsModal();
                    openPaymentModal(selectedParentForDetails);
                  }}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <FaPlus className="mr-2" />
                  إضافة دفعة جديدة
                </Button>
                <Button
                  variant="outline"
                  onClick={closeDetailsModal}
                >
                  إغلاق
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </OptimizedProtectedRoute>
  );
}

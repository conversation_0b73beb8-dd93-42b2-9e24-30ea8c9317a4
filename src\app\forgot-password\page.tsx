'use client';
import React, { useState } from 'react';
import { toast } from 'react-toastify';
import Link from 'next/link';
import SiteLogo from '@/components/SiteLogo';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      return toast.error('يرجى إدخال البريد الإلكتروني');
    }

    setIsLoading(true);

    try {
      // في الإنتاج، يمكنك استبدال هذا بطلب API حقيقي
      // await fetch('/api/auth/forgot-password', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ email })
      // });

      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1500));

      setIsSubmitted(true);
      toast.success('تم إرسال رابط إعادة تعيين كلمة المرور');
    } catch (error) {
      console.error('Error:', error);
      toast.error('حدث خطأ أثناء إرسال طلب إعادة تعيين كلمة المرور');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8" dir="rtl">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* شعار الموقع */}
        <div className="flex justify-center pt-6 pb-4">
          <SiteLogo size="xl" showText={true} iconColor="var(--primary-color)" />
        </div>

        <div className="bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] px-6 py-4">
          <h2 className="text-2xl font-bold text-white text-center">استعادة كلمة المرور</h2>
        </div>

        <div className="px-6 py-8">
          {!isSubmitted ? (
            <>
              <p className="text-gray-600 mb-6">
                أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور.
              </p>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                    البريد الإلكتروني
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]"
                  />
                </div>

                <div>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--primary-color)]"
                  >
                    {isLoading ? 'جاري الإرسال...' : 'إرسال رابط إعادة التعيين'}
                  </button>
                </div>
              </form>
            </>
          ) : (
            <div className="text-center">
              <svg
                className="mx-auto h-12 w-12 text-primary-color"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <h3 className="mt-2 text-xl font-medium text-gray-900">تم إرسال البريد الإلكتروني</h3>
              <p className="mt-1 text-sm text-gray-600">
                لقد أرسلنا رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من صندوق الوارد الخاص بك.
              </p>
            </div>
          )}

          <div className="mt-6 text-center text-sm">
            <Link href="/login" className="font-medium text-[var(--primary-color)] hover:text-[var(--secondary-color)]">
              العودة إلى صفحة تسجيل الدخول
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ForgotPasswordPage;

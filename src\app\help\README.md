# نظام المساعدة الشامل للمديرين

## نظرة عامة

تم إنشاء نظام مساعدة شامل للمديرين في نظام برهان للقرآن الكريم، يوفر دليلاً مفصلاً لجميع أقسام لوحة التحكم الإدارية.

## الميزات الرئيسية

### 1. تصميم متجاوب
- شريط جانبي أيمن للتنقل بين الفئات
- منطقة محتوى رئيسية لعرض المساعدة
- تصميم متجاوب يعمل على جميع الأجهزة

### 2. فئات المساعدة
- **البدء مع النظام**: دليل شامل للوحة التحكم والتنقل
- **إدارة المستخدمين**: الطلاب، المعلمين، أولياء الأمور
- **نظام التقييم والامتحانات**: إنشاء وإدارة الامتحانات
- **الفصول الافتراضية والحضور**: إدارة التعليم عن بُعد
- **مجالس الختم ولوحة الشرف**: الأنشطة الروحانية والتكريم
- **إدارة الحساب الشخصي**: الإعدادات الشخصية
- **التقارير والإحصائيات**: فهم وإنشاء التقارير
- **إعدادات النظام والمالية**: تخصيص النظام والإدارة المالية
- **الأسئلة الشائعة والدعم**: حلول للمشاكل الشائعة

### 3. محتوى تفاعلي
- شرح مفصل لكل قسم
- خطوات عملية للتنفيذ
- نصائح مفيدة
- فيديوهات تعليمية (قابلة للإضافة)

### 4. البحث والفلترة
- بحث سريع في المحتوى
- فلترة النتائج حسب الفئة
- عرض عدد النتائج

## هيكل الملفات

```
src/app/help/
├── page.tsx                 # الصفحة الرئيسية للمساعدة
└── README.md               # هذا الملف

src/components/help/
├── HelpSidebar.tsx         # الشريط الجانبي للتنقل
├── HelpContent.tsx         # عرض محتوى المساعدة
└── VideoEmbed.tsx          # مكون تضمين الفيديو

src/data/
└── helpContent.ts          # بيانات محتوى المساعدة
```

## كيفية الاستخدام

### للمديرين:
1. انتقل إلى `/help` في المتصفح
2. استخدم الشريط الجانبي للتنقل بين الفئات
3. استخدم البحث للعثور على موضوع محدد
4. اتبع الخطوات والنصائح المقدمة

### للمطورين:
1. لإضافة محتوى جديد، عدل ملف `src/data/helpContent.ts`
2. لإضافة فئة جديدة، عدل `helpCategories` في `HelpSidebar.tsx`
3. لتخصيص التصميم، عدل ملفات المكونات

## إضافة محتوى جديد

### إضافة موضوع مساعدة جديد:

```typescript
{
  id: 'unique-id',
  title: 'عنوان الموضوع',
  description: 'وصف مختصر',
  content: `محتوى HTML`,
  steps: ['خطوة 1', 'خطوة 2'],
  tips: ['نصيحة 1', 'نصيحة 2'],
  videoId: 'youtube-video-id',
  videoTitle: 'عنوان الفيديو'
}
```

### إضافة فئة جديدة:

```typescript
{
  id: 'category-id',
  title: 'اسم الفئة',
  icon: IconComponent,
  description: 'وصف الفئة'
}
```

## التحديثات المستقبلية

- [ ] إضافة المزيد من الفيديوهات التعليمية
- [ ] تحسين محرك البحث
- [ ] إضافة تقييم للمحتوى
- [ ] دعم اللغات المتعددة
- [ ] إضافة دردشة مباشرة للدعم

## الدعم التقني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع قسم "الأسئلة الشائعة والدعم"
- تواصل مع فريق التطوير

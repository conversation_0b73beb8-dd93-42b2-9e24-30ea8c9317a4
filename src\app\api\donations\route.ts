import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/donations - جلب جميع التبرعات
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const where = query
      ? {
          OR: [
            { donorName: { contains: query } },
            { note: { contains: query } },
          ],
        }
      : {};

    // جلب التبرعات مع الترتيب حسب التاريخ (الأحدث أولاً)
    const donations = await prisma.donation.findMany({
      where,
      orderBy: { date: 'desc' },
      skip,
      take: limit,
      include: {
        treasury: true,
        paymentMethod: true,
      },
    });

    // جلب العدد الإجمالي للتبرعات
    const total = await prisma.donation.count({ where });

    // حساب إجمالي مبالغ التبرعات
    const totalAmount = await prisma.donation.aggregate({
      _sum: {
        amount: true,
      },
    });

    // حساب عدد المتبرعين الفريدين
    const uniqueDonors = await prisma.donation.findMany({
      where: {
        donorName: {
          not: null,
        },
      },
      select: {
        donorName: true,
      },
      distinct: ['donorName'],
    });

    return NextResponse.json({
      donations,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      },
      stats: {
        totalAmount: totalAmount._sum.amount || 0,
        uniqueDonorsCount: uniqueDonors.length,
        averageDonation: uniqueDonors.length > 0 ? (totalAmount._sum.amount || 0) / uniqueDonors.length : 0,
      },
    });
  } catch (error) {
    console.error('خطأ في جلب التبرعات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب التبرعات' },
      { status: 500 }
    );
  }
}

// POST /api/donations - إنشاء تبرع جديد
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { donorName, amount, note, paymentMethodId, cardDetails } = body;

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة التبرع غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من طريقة الدفع إذا تم تحديدها
    if (paymentMethodId) {
      const paymentMethod = await prisma.paymentMethod.findUnique({
        where: { id: paymentMethodId }
      });

      if (!paymentMethod) {
        return NextResponse.json(
          { error: 'طريقة الدفع غير موجودة' },
          { status: 404 }
        );
      }

      if (!paymentMethod.isActive) {
        return NextResponse.json(
          { error: 'طريقة الدفع غير نشطة' },
          { status: 400 }
        );
      }
    }

    // التحقق من تفاصيل البطاقة إذا كانت موجودة
    if (cardDetails && typeof cardDetails !== 'object') {
      return NextResponse.json(
        { error: 'تفاصيل البطاقة غير صحيحة' },
        { status: 400 }
      );
    }



    // الحصول على الخزينة الافتراضية (نفترض أن هناك خزينة واحدة فقط)
    let treasury = await prisma.treasury.findFirst();

    // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 0,
          totalIncome: 0,
          totalExpense: 0,
        },
      });
    }

    // إنشاء التبرع وتحديث الخزينة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء التبرع
      const donation = await tx.donation.create({
        data: {
          donorName,
          amount,
          note,
          ...(paymentMethodId && { paymentMethodId }),
          ...(cardDetails && { cardDetails }),
          treasuryId: treasury!.id,
        },
        include: {
          paymentMethod: true
        }
      });

      // تحديث رصيد الخزينة وإجمالي الدخل
      await tx.treasury.update({
        where: { id: treasury!.id },
        data: {
          balance: { increment: amount },
          totalIncome: { increment: amount },
        },
      });

      // إنشاء سجل دخل للخزينة
      await tx.income.create({
        data: {
          treasuryId: treasury!.id,
          source: `تبرع${donorName ? ` من ${donorName}` : ''}${donation.paymentMethod ? ` (${donation.paymentMethod.name})` : ''}`,
          amount,
        },
      });

      return donation;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء التبرع:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء التبرع' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import { ActivityLogger, ActivityType } from '@/lib/activity-logger';

// GET /api/parent-payments - جلب مدفوعات أبناء ولي الأمر المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'PARENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المستخدم والملف الشخصي
    const user = await prisma.user.findUnique({
      where: {
        id: userData.id
      },
      include: {
        profile: true
      }
    });

    if (!user || !user.profile) {
      console.error('لم يتم العثور على المستخدم أو الملف الشخصي:', userData.id);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المستخدم" },
        { status: 404 }
      );
    }

    // جلب معلومات ولي الأمر باستخدام اسم الملف الشخصي أو اسم المستخدم
    const parent = await prisma.parent.findFirst({
      where: {
        OR: [
          { name: user.profile.name },
          { name: userData.username }
        ]
      },
      include: {
        students: true
      }
    });

    if (!parent) {
      console.error('لم يتم العثور على ولي الأمر للمستخدم:', userData.username, 'مع الملف الشخصي:', user.profile.name);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات ولي الأمر" },
        { status: 404 }
      );
    }

    // التحقق من وجود أبناء
    if (!parent.students || parent.students.length === 0) {
      console.log('ولي الأمر موجود ولكن لا يوجد أبناء مسجلين:', parent.name);
      return NextResponse.json({
        children: [],
        message: "لا يوجد أبناء مسجلين لولي الأمر"
      });
    }

    // التحقق من وجود معرف الطالب في الاستعلام
    const searchParams = request.nextUrl.searchParams;
    const childId = searchParams.get('childId');

    // إذا تم تحديد معرف الطالب، تحقق من أنه ينتمي لولي الأمر
    if (childId) {
      const childIdNum = parseInt(childId);
      const isParentChild = parent.students.some(student => student.id === childIdNum);

      if (!isParentChild) {
        return NextResponse.json(
          { message: "غير مصرح بالوصول إلى بيانات هذا الطالب" },
          { status: 403 }
        );
      }

      // جلب بيانات الطالب المحدد
      const student = await prisma.student.findUnique({
        where: { id: childIdNum },
        include: {
          classe: true
        }
      });

      if (!student) {
        return NextResponse.json(
          { message: "لم يتم العثور على بيانات الطالب" },
          { status: 404 }
        );
      }

      // جلب مدفوعات الطالب
      const payments = await prisma.payment.findMany({
        where: {
          studentId: childIdNum
        },
        orderBy: {
          date: 'desc'
        }
      });

      // تنسيق بيانات المدفوعات
      const formattedPayments = payments.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        date: payment.date.toISOString(),
        month: payment.date.getMonth() + 1,
        year: payment.date.getFullYear(),
        status: payment.status
        //notes: payment.notes || ''
      }));

      // حساب إجمالي المدفوعات والمستحقات
      const totalPaid = payments
        .filter(payment => payment.status === 'PAID')
        .reduce((sum, payment) => sum + Number(payment.amount), 0);

      const totalPending = payments
        .filter(payment => payment.status === 'PENDING')
        .reduce((sum, payment) => sum + Number(payment.amount), 0);

      return NextResponse.json({
        student: {
          id: student.id,
          name: student.name,
          grade: student.classe?.name || 'غير محدد'
        },
        payments: formattedPayments,
        stats: {
          totalPaid,
          totalPending
        },
        message: "تم جلب بيانات المدفوعات بنجاح"
      });
    } else {
      // جلب ملخص مدفوعات جميع الأبناء
      const childrenPayments = await Promise.all(parent.students.map(async (student) => {
        // جلب بيانات الطالب
        const studentData = await prisma.student.findUnique({
          where: { id: student.id },
          include: {
            classe: true
          }
        });

        if (!studentData) return null;

        // جلب مدفوعات الطالب
        const payments = await prisma.payment.findMany({
          where: {
            studentId: student.id
          },
          orderBy: {
            date: 'desc'
          },
          take: 5 // آخر 5 مدفوعات فقط
        });

        // تنسيق بيانات المدفوعات
        const formattedPayments = payments.map(payment => ({
          id: payment.id,
          amount: payment.amount,
          date: payment.date.toISOString(),
          month: payment.date.getMonth() + 1,
          year: payment.date.getFullYear(),
          status: payment.status
          //notes: payment.notes || ''
        }));

        // حساب إجمالي المدفوعات والمستحقات
        const totalPaid = payments
          .filter(payment => payment.status === 'PAID')
          .reduce((sum, payment) => sum + Number(payment.amount), 0);

        const totalPending = payments
          .filter(payment => payment.status === 'PENDING')
          .reduce((sum, payment) => sum + Number(payment.amount), 0);

        // جلب آخر مدفوعة
        const lastPayment = payments.length > 0 ? formattedPayments[0] : null;

        return {
          id: student.id,
          name: student.name,
          grade: studentData.classe?.name || 'غير محدد',
          payments: formattedPayments,
          stats: {
            totalPaid,
            totalPending
          },
          lastPayment
        };
      }));

      // فلترة القيم null
      const children = childrenPayments.filter(child => child !== null);

      return NextResponse.json({
        children,
        message: "تم جلب بيانات المدفوعات بنجاح"
      });
    }
  } catch (error) {
    console.error('Error fetching parent payments:', error);
    // إضافة المزيد من التفاصيل للخطأ
    const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير معروف';
    return NextResponse.json(
      {
        message: "حدث خطأ أثناء جلب بيانات المدفوعات",
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

// POST /api/parent-payments - إنشاء دفعة جديدة من ولي الأمر
export async function POST(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'PARENT') {
      return NextResponse.json(
        { error: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات ولي الأمر
    const parent = await prisma.parent.findFirst({
      where: {
        name: userData.username
      },
      include: {
        students: true
      }
    });

    if (!parent) {
      return NextResponse.json(
        { error: "لم يتم العثور على بيانات ولي الأمر" },
        { status: 404 }
      );
    }

    // استخراج بيانات الدفع من الطلب
    const body = await request.json();
    const {
      studentId,
      amount,
      month,
      year,
      paymentMethodId,
      invoiceId,
      notes,
      cardDetails
    } = body;

    // التحقق من البيانات المطلوبة
    if (!studentId || !amount || !month || !year || !paymentMethodId) {
      return NextResponse.json(
        { error: "الحقول المطلوبة غير مكتملة" },
        { status: 400 }
      );
    }

    // التحقق من أن الطالب ينتمي لولي الأمر
    const isParentChild = parent.students.some(student => student.id === studentId);
    if (!isParentChild) {
      return NextResponse.json(
        { error: "غير مصرح بإجراء دفعات لهذا الطالب" },
        { status: 403 }
      );
    }

    // التحقق من طريقة الدفع
    const paymentMethod = await prisma.paymentMethod.findUnique({
      where: { id: paymentMethodId }
    });

    if (!paymentMethod) {
      return NextResponse.json(
        { error: "طريقة الدفع غير موجودة" },
        { status: 404 }
      );
    }

    if (!paymentMethod.isActive) {
      return NextResponse.json(
        { error: "طريقة الدفع غير نشطة" },
        { status: 400 }
      );
    }

    // الحصول على الخزينة الافتراضية
    let treasury = await prisma.treasury.findFirst();
    if (!treasury) {
      treasury = await prisma.treasury.create({
        data: {
          balance: 0,
          totalIncome: 0,
          totalExpense: 0,
        },
      });
    }

    // إنشاء الدفعة وتحديث الخزينة في معاملة واحدة
    const result = await prisma.$transaction(async (tx) => {
      // إنشاء الدفعة
      const payment = await tx.payment.create({
        data: {
          studentId,
          amount,
          status: 'PAID',
          date: new Date(`${year}-${month.toString().padStart(2, '0')}-01T00:00:00.000Z`),
          paymentMethodId,
          ...(notes && { notes }),
          ...(invoiceId && { invoiceId }),
        },
        include: {
          student: true,
          paymentMethod: true,
          invoice: true
        }
      });

      // إذا كانت طريقة الدفع تتطلب معلومات بطاقة وتم توفيرها
      if (paymentMethod.requiresCard && cardDetails) {
        await tx.paymentMethodsOnPayment.create({
          data: {
            paymentId: payment.id,
            paymentMethodId,
            amount,
            cardDetails
          }
        });
      }

      // تحديث رصيد الخزينة وإجمالي الدخل
      await tx.treasury.update({
        where: { id: treasury!.id },
        data: {
          balance: { increment: amount },
          totalIncome: { increment: amount },
        },
      });

      // إنشاء سجل دخل للخزينة
      await tx.income.create({
        data: {
          treasuryId: treasury!.id,
          source: `رسوم دراسية للطالب ${payment.student.name} - شهر ${month}/${year} (طريقة دفع: ${payment.paymentMethod?.name || 'غير محددة'})`,
          amount,
        },
      });

      // تحديث حالة الفاتورة إذا كانت مرتبطة بالدفعة
      if (invoiceId) {
        const invoice = await tx.invoice.findUnique({
          where: { id: invoiceId },
          include: { payments: true }
        });

        if (invoice) {
          // حساب إجمالي المدفوعات للفاتورة (بما في ذلك الدفعة الحالية)
          const totalPaid = invoice.payments.reduce((sum, p) => {
            if (p.status === 'PAID' && p.id !== payment.id) {
              return sum + p.amount;
            }
            return sum;
          }, payment.amount);

          // تحديث حالة الفاتورة بناءً على المبلغ المدفوع
          let newStatus = invoice.status;

          if (totalPaid >= invoice.amount) {
            newStatus = 'PAID';
          } else if (totalPaid > 0) {
            newStatus = 'PARTIALLY_PAID';
          }

          // تحديث الفاتورة فقط إذا تغيرت الحالة
          if (newStatus !== invoice.status) {
            await tx.invoice.update({
              where: { id: invoiceId },
              data: { status: newStatus }
            });
          }
        }
      }

      // تسجيل نشاط الدفع
      try {
        const user = await prisma.user.findFirst({
          where: {
            username: userData.username,
            role: 'PARENT'
          },
          include: {
            profile: true
          }
        });

        if (user) {
          const parentName = user.profile?.name || userData.username;
          await ActivityLogger.log(
            user.id,
            ActivityType.PAYMENT,
            `قام ولي الأمر ${parentName} بتسجيل دفعة بقيمة ${amount.toLocaleString()} دج للطالب ${payment.student.name} باستخدام ${payment.paymentMethod?.name || 'طريقة دفع غير محددة'} - شهر ${month}/${year}`
          );
        }
      } catch (error) {
        console.error('خطأ في تسجيل نشاط الدفع:', error);
        // لا نريد أن يفشل تسجيل الدفعة إذا فشل تسجيل النشاط
      }

      // إضافة ملاحظة في الدفعة تشير إلى أنها تمت باسم الولي
      await tx.payment.update({
        where: { id: payment.id },
        data: {
          notes: `${notes ? notes + ' - ' : ''}دفعة مسجلة باسم ولي الأمر: ${parent.name}`
        }
      });

      return payment;
    });

    return NextResponse.json({
      payment: result,
      message: "تم تسجيل الدفعة بنجاح"
    });
  } catch (error) {
    console.error('Error creating parent payment:', error);
    return NextResponse.json(
      { error: "حدث خطأ أثناء تسجيل الدفعة" },
      { status: 500 }
    );
  }
}

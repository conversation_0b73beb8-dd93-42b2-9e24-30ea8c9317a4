@echo off
title Praetorian.ring - Penetration Testing Toolkit
color 0A

echo.
echo   ____                _             _             
echo  ^|  _ \ _ __ __ _  ___^| ^|_ ___  _ __^(_^) __ _ _ __   
echo  ^| ^|_^) ^| '__/ _` ^|/ _ \ __/ _ \^| '__^| ^|/ _` ^| '_ \  
echo  ^|  __/^| ^| ^| ^(_^| ^|  __/ ^^|^| ^(_^) ^| ^|  ^| ^| ^(_^| ^| ^| ^| ^| 
echo  ^|_^|   ^|_^|  \__,_^|\___^|\__\___^|_^|  ^|_^|\__,_^|_^| ^|_^| 
echo.
echo        مكتبة أدوات اختبار الاختراق الاحترافية
echo                  للغة Ring
echo ===============================================
echo.

echo فحص متطلبات النظام...
echo.

REM فحص Ring
echo [1/4] فحص Ring Programming Language...
ring -v >nul 2>&1
if errorlevel 1 (
    echo ✗ Ring غير مثبت أو غير موجود في PATH
    echo.
    echo يرجى تثبيت Ring من: https://ring-lang.github.io/
    echo أو التأكد من إضافته إلى متغير PATH
    pause
    exit /b 1
) else (
    echo ✓ Ring متاح
)

REM فحص مكتبة Praetorian
echo [2/4] فحص مكتبة Praetorian.ring...
if not exist "praetorian.ring" (
    echo ✗ ملف praetorian.ring غير موجود
    echo تأكد من تشغيل الملف من المجلد الصحيح
    pause
    exit /b 1
) else (
    echo ✓ مكتبة Praetorian.ring موجودة
)

REM فحص التطبيقات
echo [3/4] فحص التطبيقات...
if not exist "applications\" (
    echo ✗ مجلد التطبيقات غير موجود
    pause
    exit /b 1
) else (
    echo ✓ مجلد التطبيقات موجود
)

REM فحص الأمثلة
echo [4/4] فحص الأمثلة...
if not exist "examples\" (
    echo ✗ مجلد الأمثلة غير موجود
    pause
    exit /b 1
) else (
    echo ✓ مجلد الأمثلة موجود
)

echo.
echo ===============================================
echo جميع المتطلبات الأساسية متوفرة!
echo ===============================================
echo.

:MAIN_MENU
echo ما الذي تريد تشغيله؟
echo.
echo 1. مشغل التطبيقات (Applications Launcher)
echo 2. ReconDash - لوحة التحكم الاستطلاعية
echo 3. DirHunter - أداة تخمين المجلدات  
echo 4. تشغيل الأمثلة
echo 5. اختبار المكتبة
echo 6. اختبار التطبيقات
echo 7. عرض معلومات المكتبة
echo 0. خروج
echo.
set /p choice="اختر رقماً (0-7): "

if "%choice%"=="1" goto LAUNCHER
if "%choice%"=="2" goto RECONDASH
if "%choice%"=="3" goto DIRHUNTER
if "%choice%"=="4" goto EXAMPLES
if "%choice%"=="5" goto TEST_LIBRARY
if "%choice%"=="6" goto TEST_APPS
if "%choice%"=="7" goto SHOW_INFO
if "%choice%"=="0" goto EXIT

echo خيار غير صحيح!
goto MAIN_MENU

:LAUNCHER
echo.
echo تشغيل مشغل التطبيقات...
cd applications
ring launcher.ring
cd ..
goto MAIN_MENU

:RECONDASH
echo.
echo تشغيل ReconDash...
echo ملاحظة: يتطلب libui.ring
echo.
cd applications\ReconDash
ring ReconDash.ring
cd ..\..
goto MAIN_MENU

:DIRHUNTER
echo.
echo تشغيل DirHunter...
echo.
echo DirHunter يتطلب معلمات سطر الأوامر.
echo.
echo أمثلة:
echo ring DirHunter.ring -u http://httpbin.org -w wordlist.txt
echo ring DirHunter.ring -h  (للمساعدة)
echo.
set /p run_example="هل تريد تشغيل مثال تجريبي؟ (y/n): "
if /i "%run_example%"=="y" (
    cd applications\DirHunter
    ring DirHunter.ring -u http://httpbin.org -w wordlist.txt -t 5
    cd ..\..
) else (
    echo لتشغيل DirHunter يدوياً:
    echo cd applications\DirHunter
    echo ring DirHunter.ring [OPTIONS]
)
goto MAIN_MENU

:EXAMPLES
echo.
echo تشغيل الأمثلة...
echo.
echo 1. المثال الأساسي (basic_scan.ring)
echo 2. تدقيق الويب (web_audit.ring)  
echo 3. تدقيق SSL (ssl_audit.ring)
echo 0. العودة للقائمة الرئيسية
echo.
set /p example_choice="اختر مثالاً (0-3): "

if "%example_choice%"=="1" (
    echo تشغيل المثال الأساسي...
    cd examples
    ring basic_scan.ring
    cd ..
)
if "%example_choice%"=="2" (
    echo تشغيل مثال تدقيق الويب...
    cd examples
    ring web_audit.ring
    cd ..
)
if "%example_choice%"=="3" (
    echo تشغيل مثال تدقيق SSL...
    cd examples
    ring ssl_audit.ring
    cd ..
)
if "%example_choice%"=="0" goto MAIN_MENU

goto MAIN_MENU

:TEST_LIBRARY
echo.
echo تشغيل اختبار المكتبة...
ring test_praetorian.ring
goto MAIN_MENU

:TEST_APPS
echo.
echo تشغيل اختبار التطبيقات...
cd applications
ring test_applications.ring
cd ..
goto MAIN_MENU

:SHOW_INFO
echo.
echo عرض معلومات المكتبة...
ring -c "load 'praetorian.ring' oPraetorian = CreatePraetorian() oPraetorian.printInfo()"
goto MAIN_MENU

:EXIT
echo.
echo شكراً لاستخدام Praetorian.ring!
echo.
echo للمزيد من المعلومات:
echo - README.md: دليل المستخدم الشامل
echo - applications/README.md: دليل التطبيقات
echo - CHANGELOG.md: سجل التغييرات
echo.
echo تذكر: استخدم هذه الأدوات بمسؤولية وأخلاقية فقط!
echo.
pause
exit /b 0

'use client';
import React from 'react';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { TableCell, TableRow } from '@/components/ui/table';

interface Student {
  id: number;
  username: string;
  name: string;
  age: number;
  phone?: string;
  guardianId?: number;
  classeId?: number;
  guardian?: { name: string; phone: string };
  classe?: { name: string };
  totalPoints?: number;
}

interface OptimizedStudentsTableRowProps {
  student: Student;
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onView: (id: number) => void;
  onProgress?: (id: number) => void;
}

/**
 * مثال على صف جدول محسن للأداء
 * يستخدم OptimizedActionButtonGroup بدلاً من أزرار منفصلة
 */
const OptimizedStudentsTableRow: React.FC<OptimizedStudentsTableRowProps> = ({
  student,
  onEdit,
  onDelete,
  onView,
  onProgress
}) => {
  return (
    <TableRow className="hover:bg-gray-50">
      <TableCell className="font-medium">{student.name}</TableCell>
      <TableCell>{student.username}</TableCell>
      <TableCell>{student.age}</TableCell>
      <TableCell>{student.phone || '-'}</TableCell>
      <TableCell>{student.classe?.name || '-'}</TableCell>
      <TableCell>{student.guardian?.name || '-'}</TableCell>
      <TableCell>{student.totalPoints || 0}</TableCell>
      <TableCell>
        {/* استخدام OptimizedActionButtonGroup - محسن للأداء */}
        <OptimizedActionButtonGroup
          entityType="students"
          onEdit={() => onEdit(student.id)}
          onDelete={() => onDelete(student.id)}
          onView={() => onView(student.id)}
          showEdit={true}
          showDelete={true}
          showView={true}
          // يمكن إضافة أزرار مخصصة
          customPermissions={{
            view: 'admin.students.view-details'
          }}
        />
        
        {/* زر إضافي للتقدم - محمي بصلاحية منفصلة */}
        {onProgress && (
          <OptimizedActionButtonGroup
            entityType="students"
            onView={() => onProgress(student.id)}
            showView={true}
            customPermissions={{
              view: 'admin.students.progress'
            }}
            className="mr-2"
          />
        )}
      </TableCell>
    </TableRow>
  );
};

export default OptimizedStudentsTableRow;

// مثال على كيفية استخدام الصف المحسن في الجدول
export const OptimizedStudentsTable: React.FC<{
  students: Student[];
  onEdit: (id: number) => void;
  onDelete: (id: number) => void;
  onView: (id: number) => void;
  onProgress?: (id: number) => void;
}> = ({ students, onEdit, onDelete, onView, onProgress }) => {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              الاسم
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              اسم المستخدم
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              العمر
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              الهاتف
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              الصف
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              ولي الأمر
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              النقاط
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              الإجراءات
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {students.map((student) => (
            <OptimizedStudentsTableRow
              key={student.id}
              student={student}
              onEdit={onEdit}
              onDelete={onDelete}
              onView={onView}
              onProgress={onProgress}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

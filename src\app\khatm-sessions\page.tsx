'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { CalendarIcon, Search, BookOpen, Users } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import Link from 'next/link';

type KhatmSession = {
  id: number;
  title: string;
  date: string;
  location: string;
  description: string | null;
  teacher: {
    name: string;
  };
  surah: {
    name: string;
  } | null;
  _count?: {
    attendances: number;
  };
};

export default function PublicKhatmSessionsPage() {
  const [sessions, setSessions] = useState<KhatmSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSessions, setFilteredSessions] = useState<KhatmSession[]>([]);

  useEffect(() => {
    fetchSessions();
  }, []);

  useEffect(() => {
    if (sessions.length > 0) {
      const filtered = sessions.filter(session => 
        session.title.includes(searchQuery) || 
        session.location.includes(searchQuery) ||
        session.teacher.name.includes(searchQuery) ||
        (session.surah && session.surah.name.includes(searchQuery))
      );
      setFilteredSessions(filtered);
    }
  }, [searchQuery, sessions]);

  const fetchSessions = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/khatm-sessions/public');
      if (!response.ok) throw new Error('فشل في جلب مجالس الختم');
      const data = await response.json();
      
      if (data && Array.isArray(data.data)) {
        setSessions(data.data);
        setFilteredSessions(data.data);
      } else {
        console.error('Invalid sessions data format:', data);
        setSessions([]);
        setFilteredSessions([]);
      }
    } catch (error) {
      console.error('Error fetching khatm sessions:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب مجالس الختم',
        variant: 'destructive',
      });
      setSessions([]);
      setFilteredSessions([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white">
      
      <div className="container mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-[var(--primary-color)] mb-2">مجالس الختم</h1>
          <p className="text-muted-foreground">تصفح مجالس الختم المتاحة وشارك في حضورها</p>
        </div>
        
        <div className="relative mb-6">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="ابحث عن مجلس ختم..."
            className="pr-10 text-right"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredSessions.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-300">
            <div className="flex flex-col items-center justify-center">
              <CalendarIcon className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-1">لا توجد مجالس ختم</h3>
              <p className="text-sm text-gray-500">لم يتم العثور على مجالس ختم متاحة حالياً</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredSessions.map(session => (
              <Card key={session.id} className="overflow-hidden hover:shadow-md transition-shadow border-t-4 border-t-[var(--primary-color)]">
                <CardHeader className="pb-2 bg-gradient-to-r from-[#f8fffd] to-white">
                  <CardTitle className="text-xl text-[var(--primary-color)]">{session.title}</CardTitle>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <CalendarIcon className="ml-1 h-4 w-4 text-[var(--primary-color)]" />
                    {format(new Date(session.date), 'PPP', { locale: ar })}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center text-sm">
                      <Users className="ml-2 h-4 w-4 text-[var(--primary-color)]" />
                      <span className="font-medium">المعلم:</span>
                      <span className="mr-1">{session.teacher.name}</span>
                    </div>
                    
                    {session.surah && (
                      <div className="flex items-center text-sm">
                        <BookOpen className="ml-2 h-4 w-4 text-[var(--primary-color)]" />
                        <span className="font-medium">السورة:</span>
                        <span className="mr-1">{session.surah.name}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center text-sm">
                      <span className="font-medium">المكان:</span>
                      <span className="mr-1">{session.location}</span>
                    </div>
                    
                    {session._count && (
                      <Badge variant="outline" className="bg-[var(--primary-color)]/10 text-[var(--primary-color)] border-[var(--primary-color)]">
                        عدد الحضور: {session._count.attendances}
                      </Badge>
                    )}
                    
                    {session.description && (
                      <p className="text-sm text-muted-foreground mt-2">{session.description}</p>
                    )}
                    
                    <div className="pt-2">
                      <Link href={`/khatm-sessions/${session.id}`}>
                        <Button className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                          عرض التفاصيل
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

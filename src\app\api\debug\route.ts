import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // جلب الأقسام
    const classes = await prisma.classe.findMany({
      include: {
        students: true
      }
    });

    // جلب علاقات القسم بالمادة
    const classSubjects = await prisma.classSubject.findMany({
      include: {
        classe: true,
        teacherSubject: {
          include: {
            teacher: true,
            subject: true
          }
        }
      }
    });

    // جلب المعلمين والمواد
    const teacherSubjects = await prisma.teacherSubject.findMany({
      include: {
        teacher: true,
        subject: true
      }
    });

    // إنشاء علاقة قسم بمادة إذا لم تكن هناك علاقات
    let createdClassSubject = null;
    if (classSubjects.length === 0 && classes.length > 0 && teacherSubjects.length > 0) {
      createdClassSubject = await prisma.classSubject.create({
        data: {
          classeId: classes[0].id,
          teacherSubjectId: teacherSubjects[0].id
        },
        include: {
          classe: true,
          teacherSubject: {
            include: {
              teacher: true,
              subject: true
            }
          }
        }
      });
    }

    return NextResponse.json({
      classes,
      classSubjects,
      teacherSubjects,
      createdClassSubject,
      message: 'تم جلب البيانات بنجاح'
    });
  } catch (error) {
    console.error('Error in debug route:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب البيانات',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

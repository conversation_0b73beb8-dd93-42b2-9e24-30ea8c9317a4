"use client";
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FaChalkboardTeacher, FaSearch, FaBook, FaPhone } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface Subject {
  id: number;
  name: string;
}

interface Teacher {
  id: number;
  name: string;
  phone: string;
  specialization: string;
  subjects: Subject[];
}

const StudentTeachersPage = () => {
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [filteredTeachers, setFilteredTeachers] = useState<Teacher[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTeachers = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/student-teachers');

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات المعلمين');
        }

        const data = await response.json();
        setTeachers(data.teachers);
        setFilteredTeachers(data.teachers);
      } catch (err) {
        console.error('Error fetching teachers:', err);
        setError('حدث خطأ أثناء جلب بيانات المعلمين');
        toast.error('فشل في جلب بيانات المعلمين');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeachers();
  }, []);

  // تصفية المعلمين حسب البحث
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredTeachers(teachers);
    } else {
      const filtered = teachers.filter(teacher => 
        teacher.name.includes(searchTerm) || 
        teacher.specialization.includes(searchTerm) ||
        teacher.subjects.some(subject => subject.name.includes(searchTerm))
      );
      setFilteredTeachers(filtered);
    }
  }, [searchTerm, teachers]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">المعلمون</h1>
          <p className="text-gray-500">عرض جميع المعلمين الذين يدرسونك</p>
        </div>
        <div className="relative w-full md:w-64">
          <Input
            placeholder="بحث عن معلم..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : filteredTeachers.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-4">
              {searchTerm ? 'لا توجد نتائج مطابقة للبحث' : 'لا يوجد معلمون متاحون'}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTeachers.map((teacher) => (
            <Card key={teacher.id}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaChalkboardTeacher className="text-[var(--primary-color)]" />
                  <span>{teacher.name}</span>
                </CardTitle>
                <CardDescription>
                  {teacher.specialization}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {teacher.phone && (
                  <div className="flex items-center gap-2 mb-4 text-sm">
                    <FaPhone className="text-gray-500" />
                    <span dir="ltr">{teacher.phone}</span>
                  </div>
                )}
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <FaBook className="text-[var(--primary-color)]" />
                    <span>المواد التي يدرسها:</span>
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {teacher.subjects.map((subject) => (
                      <span 
                        key={subject.id}
                        className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                      >
                        {subject.name}
                      </span>
                    ))}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-gray-50 flex justify-end">
                <Button variant="outline" size="sm">
                  مراسلة المعلم
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default StudentTeachersPage;

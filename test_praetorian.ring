/*
==============================================================================
    ملف اختبار مكتبة Praetorian.ring
    
    الوصف: اختبار جميع وحدات ووظائف المكتبة للتأكد من عملها بشكل صحيح
    المؤلف: Praetorian Team
==============================================================================
*/

# تحميل مكتبة Praetorian
load "praetorian.ring"

/*
==============================================================================
    متغيرات الاختبار العامة
==============================================================================
*/

nTestsPassed = 0
nTestsFailed = 0
aFailedTests = []

/*
==============================================================================
    دوال مساعدة للاختبار
==============================================================================
*/

func testAssert bCondition, cTestName
    if bCondition
        nTestsPassed++
        ? "✓ " + cTestName + " - نجح"
    else
        nTestsFailed++
        add(aFailedTests, cTestName)
        ? "✗ " + cTestName + " - فشل"
    ok

func testNotNull oObject, cTestName
    testAssert(oObject != NULL, cTestName)

func testEquals nExpected, nActual, cTestName
    testAssert(nExpected = nActual, cTestName + " (متوقع: " + nExpected + ", فعلي: " + nActual + ")")

func testContains cText, cSubstring, cTestName
    testAssert(substr(cText, cSubstring) > 0, cTestName)

/*
==============================================================================
    اختبار الوحدات الأساسية
==============================================================================
*/

func testCoreModules
    ? ""
    ? "=== اختبار الوحدات الأساسية ==="
    
    # اختبار إنشاء مثيل من المكتبة
    oPraetorian = CreatePraetorian()
    testNotNull(oPraetorian, "إنشاء مثيل Praetorian")
    
    # اختبار الحصول على الإصدار
    cVersion = oPraetorian.getVersion()
    testNotNull(cVersion, "الحصول على إصدار المكتبة")
    testEquals("1.0.0", cVersion, "إصدار المكتبة صحيح")
    
    # اختبار الحصول على معلومات المكتبة
    aInfo = oPraetorian.getInfo()
    testNotNull(aInfo, "الحصول على معلومات المكتبة")
    testContains(aInfo[:name], "Praetorian", "اسم المكتبة صحيح")
    
    # اختبار الوحدات الفرعية
    testNotNull(oPraetorian.Network, "وحدة الشبكة موجودة")
    testNotNull(oPraetorian.Web, "وحدة الويب موجودة")
    testNotNull(oPraetorian.Crypto, "وحدة التشفير موجودة")
    
    # اختبار الوحدات الفرعية للشبكة
    testNotNull(oPraetorian.Network.Scanner, "فاحص الشبكة موجود")
    testNotNull(oPraetorian.Network.PacketCrafter, "صانع الحزم موجود")
    
    # اختبار الوحدات الفرعية للويب
    testNotNull(oPraetorian.Web.HTTPClient, "عميل HTTP موجود")
    testNotNull(oPraetorian.Web.Crawler, "الزاحف موجود")
    testNotNull(oPraetorian.Web.Fuzzer, "Fuzzer موجود")
    
    # اختبار وحدة التشفير
    testNotNull(oPraetorian.Crypto.SSLChecker, "مدقق SSL موجود")

/*
==============================================================================
    اختبار الأدوات المساعدة
==============================================================================
*/

func testUtilities
    ? ""
    ? "=== اختبار الأدوات المساعدة ==="
    
    # اختبار Base64
    cOriginal = "Hello World"
    cEncoded = PraetorianUtilsInstance.base64Encode(cOriginal)
    cDecoded = PraetorianUtilsInstance.base64Decode(cEncoded)
    testEquals(cOriginal, cDecoded, "تشفير وفك تشفير Base64")
    
    # اختبار URL Encoding
    cURL = "Hello World!"
    cEncoded = PraetorianUtilsInstance.urlEncode(cURL)
    cDecoded = PraetorianUtilsInstance.urlDecode(cEncoded)
    testEquals(cURL, cDecoded, "ترميز وفك ترميز URL")
    
    # اختبار HTML Encoding
    cHTML = "<script>alert('test')</script>"
    cEncoded = PraetorianUtilsInstance.htmlEncode(cHTML)
    testContains(cEncoded, "&lt;", "ترميز HTML")
    
    # اختبار توليد كلمة مرور
    cPassword = PraetorianUtilsInstance.generateStrongPassword(12)
    testEquals(12, len(cPassword), "طول كلمة المرور")
    
    # اختبار التحقق من IP
    testAssert(PraetorianUtilsInstance.isValidIP("***********"), "IP صحيح")
    testAssert(not PraetorianUtilsInstance.isValidIP("999.999.999.999"), "IP خاطئ")
    
    # اختبار تحويل IP
    nIP = PraetorianUtilsInstance.ipToNumber("***********")
    cIP = PraetorianUtilsInstance.numberToIP(nIP)
    testEquals("***********", cIP, "تحويل IP إلى رقم والعكس")
    
    # اختبار Hash
    cHash = PraetorianUtilsInstance.getMD5Hash("test")
    testEquals(32, len(cHash), "طول MD5 Hash")
    
    cHash = PraetorianUtilsInstance.getSHA256Hash("test")
    testEquals(64, len(cHash), "طول SHA256 Hash")

/*
==============================================================================
    اختبار نظام التسجيل
==============================================================================
*/

func testLogging
    ? ""
    ? "=== اختبار نظام التسجيل ==="
    
    # إنشاء مثيل من نظام التسجيل
    oLogger = new PraetorianLogger
    testNotNull(oLogger, "إنشاء مثيل نظام التسجيل")
    
    # اختبار تعيين ملف السجل
    oLogger.setLogFile("test.log")
    testAssert(true, "تعيين ملف السجل")
    
    # اختبار تعيين مستوى التسجيل
    oLogger.setLogLevel(LOG_LEVEL_DEBUG)
    testAssert(true, "تعيين مستوى التسجيل")
    
    # اختبار كتابة رسائل مختلفة
    oLogger.debug("رسالة تصحيح")
    oLogger.info("رسالة معلومات")
    oLogger.warning("رسالة تحذير")
    oLogger.error("رسالة خطأ")
    testAssert(true, "كتابة رسائل السجل")
    
    # اختبار تسجيل العمليات
    oLogger.startOperation("عملية اختبار")
    oLogger.endOperation("عملية اختبار")
    testAssert(true, "تسجيل العمليات")

/*
==============================================================================
    اختبار وحدة الشبكة
==============================================================================
*/

func testNetworkModule
    ? ""
    ? "=== اختبار وحدة الشبكة ==="
    
    oPraetorian = CreatePraetorian()
    
    # اختبار إعدادات الفاحص
    oPraetorian.Network.Scanner.setTimeout(1000)
    oPraetorian.Network.Scanner.setMaxThreads(10)
    oPraetorian.Network.Scanner.setVerbose(false)
    testAssert(true, "تعيين إعدادات الفاحص")
    
    # اختبار فحص منفذ واحد (localhost)
    bResult = oPraetorian.Network.Scanner.scanSinglePort("127.0.0.1", 80)
    testAssert(bResult = true or bResult = false, "فحص منفذ واحد")
    
    # اختبار تحديد نوع الخدمة
    cService = oPraetorian.Network.Scanner.identifyService(80, "")
    testEquals("HTTP", cService, "تحديد خدمة HTTP")
    
    cService = oPraetorian.Network.Scanner.identifyService(443, "")
    testEquals("HTTPS", cService, "تحديد خدمة HTTPS")
    
    # اختبار صانع الحزم
    oPraetorian.Network.PacketCrafter.setVerbose(false)
    testAssert(true, "تعيين إعدادات صانع الحزم")
    
    # اختبار حساب checksum
    nChecksum = oPraetorian.Network.PacketCrafter.calculateChecksum("test")
    testAssert(nChecksum >= 0, "حساب checksum")
    
    # اختبار تحويل IP إلى bytes
    cBytes = oPraetorian.Network.PacketCrafter.ipToBytes("***********")
    testEquals(4, len(cBytes), "تحويل IP إلى bytes")

/*
==============================================================================
    اختبار وحدة الويب
==============================================================================
*/

func testWebModule
    ? ""
    ? "=== اختبار وحدة الويب ==="
    
    oPraetorian = CreatePraetorian()
    
    # اختبار إعدادات عميل HTTP
    oPraetorian.Web.HTTPClient.setUserAgent("Test-Agent/1.0")
    oPraetorian.Web.HTTPClient.setTimeout(5)
    oPraetorian.Web.HTTPClient.setVerbose(false)
    testAssert(true, "تعيين إعدادات عميل HTTP")
    
    # اختبار إعدادات الزاحف
    oPraetorian.Web.Crawler.setMaxDepth(1)
    oPraetorian.Web.Crawler.setMaxPages(5)
    oPraetorian.Web.Crawler.setDelay(100)
    oPraetorian.Web.Crawler.setVerbose(false)
    testAssert(true, "تعيين إعدادات الزاحف")
    
    # اختبار تنظيف URL
    cCleanURL = oPraetorian.Web.Crawler.cleanURL("http://example.com/page#section")
    testEquals("http://example.com/page", cCleanURL, "تنظيف URL")
    
    # اختبار التحقق من صحة URL
    testAssert(oPraetorian.Web.Crawler.isValidURL("http://example.com"), "URL صحيح")
    testAssert(not oPraetorian.Web.Crawler.isValidURL("invalid-url"), "URL خاطئ")
    
    # اختبار تحويل URL نسبي إلى مطلق
    cAbsoluteURL = oPraetorian.Web.Crawler.makeAbsoluteURL("http://example.com", "/page")
    testEquals("http://example.com/page", cAbsoluteURL, "تحويل URL نسبي")
    
    # اختبار إعدادات Fuzzer
    oPraetorian.Web.Fuzzer.setThreads(5)
    oPraetorian.Web.Fuzzer.setDelay(100)
    oPraetorian.Web.Fuzzer.setTimeout(3)
    oPraetorian.Web.Fuzzer.setVerbose(false)
    testAssert(true, "تعيين إعدادات Fuzzer")

/*
==============================================================================
    اختبار وحدة التشفير
==============================================================================
*/

func testCryptoModule
    ? ""
    ? "=== اختبار وحدة التشفير ==="
    
    oPraetorian = CreatePraetorian()
    
    # اختبار إعدادات مدقق SSL
    oPraetorian.Crypto.SSLChecker.setTimeout(5)
    oPraetorian.Crypto.SSLChecker.setVerbose(false)
    testAssert(true, "تعيين إعدادات مدقق SSL")
    
    # اختبار التحقق من انتهاء الصلاحية
    bExpired = oPraetorian.Crypto.SSLChecker.checkIfExpired("2023-01-01")
    testAssert(bExpired = true or bExpired = false, "فحص انتهاء الصلاحية")
    
    # اختبار حساب الأيام المتبقية
    nDays = oPraetorian.Crypto.SSLChecker.calculateDaysUntilExpiry("2025-12-31")
    testAssert(nDays >= 0, "حساب الأيام المتبقية")
    
    # اختبار دعم الشيفرة
    bSupported = oPraetorian.Crypto.SSLChecker.testCipherSupport("example.com", 443, "RC4-MD5")
    testAssert(bSupported = true or bSupported = false, "اختبار دعم الشيفرة")
    
    # اختبار دعم البروتوكول
    bSupported = oPraetorian.Crypto.SSLChecker.testProtocolSupport("example.com", 443, "TLSv1.2")
    testAssert(bSupported = true or bSupported = false, "اختبار دعم البروتوكول")

/*
==============================================================================
    اختبار التكامل
==============================================================================
*/

func testIntegration
    ? ""
    ? "=== اختبار التكامل ==="
    
    # اختبار تكامل نظام التسجيل مع الوحدات
    oPraetorian = CreatePraetorian()
    testNotNull(oPraetorian.Network.Scanner.oLogger, "تكامل نظام التسجيل مع فاحص الشبكة")
    testNotNull(oPraetorian.Web.HTTPClient.oLogger, "تكامل نظام التسجيل مع عميل HTTP")
    testNotNull(oPraetorian.Crypto.SSLChecker.oLogger, "تكامل نظام التسجيل مع مدقق SSL")
    
    # اختبار تكامل الأدوات المساعدة
    testNotNull(PraetorianUtilsInstance, "مثيل الأدوات المساعدة متاح")
    testNotNull(PraetorianLoggerInstance, "مثيل نظام التسجيل متاح")

/*
==============================================================================
    اختبار الأداء
==============================================================================
*/

func testPerformance
    ? ""
    ? "=== اختبار الأداء ==="
    
    # اختبار سرعة تشفير Base64
    cStartTime = time()
    for i = 1 to 1000
        cEncoded = PraetorianUtilsInstance.base64Encode("test data " + i)
    next
    cEndTime = time()
    testAssert(true, "اختبار سرعة Base64 (1000 عملية)")
    
    # اختبار سرعة Hash
    cStartTime = time()
    for i = 1 to 1000
        cHash = PraetorianUtilsInstance.getMD5Hash("test data " + i)
    next
    cEndTime = time()
    testAssert(true, "اختبار سرعة MD5 (1000 عملية)")

/*
==============================================================================
    الدالة الرئيسية للاختبار
==============================================================================
*/

func main
    ? ""
    ? "=============================================="
    ? "بدء اختبار مكتبة Praetorian.ring"
    ? "=============================================="
    ? "التاريخ: " + date() + " " + time()
    ? ""
    
    # تشغيل جميع الاختبارات
    testCoreModules()
    testUtilities()
    testLogging()
    testNetworkModule()
    testWebModule()
    testCryptoModule()
    testIntegration()
    testPerformance()
    
    # طباعة النتائج النهائية
    printTestResults()

/*
==============================================================================
    طباعة نتائج الاختبار
==============================================================================
*/

func printTestResults
    ? ""
    ? "=============================================="
    ? "نتائج الاختبار"
    ? "=============================================="
    ? "الاختبارات الناجحة: " + nTestsPassed
    ? "الاختبارات الفاشلة: " + nTestsFailed
    ? "إجمالي الاختبارات: " + (nTestsPassed + nTestsFailed)
    
    if nTestsFailed = 0
        ? ""
        ? "🎉 جميع الاختبارات نجحت!"
        ? "المكتبة جاهزة للاستخدام."
    else
        ? ""
        ? "⚠ بعض الاختبارات فشلت:"
        for cTest in aFailedTests
            ? "  - " + cTest
        next
        ? ""
        ? "يرجى مراجعة الأخطاء وإصلاحها."
    ok
    
    nSuccessRate = (nTestsPassed * 100) / (nTestsPassed + nTestsFailed)
    ? ""
    ? "معدل النجاح: " + nSuccessRate + "%"
    ? "=============================================="

# تشغيل الاختبارات
main()

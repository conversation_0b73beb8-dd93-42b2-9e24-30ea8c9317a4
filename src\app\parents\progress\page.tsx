"use client";
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
  CardDescription
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import {
  FaUserGraduate,
  FaBook,
  FaCalendarAlt,
  FaTrophy,
  FaChartLine,
  FaArrowLeft
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface ExamResult {
  id: number;
  examId: number;
  examType: string;
  month: string;
  description: string;
  points: number;
  maxPoints: number;
  passingPoints: number;
  isPassed: boolean;
  surahName: string;
  date: string;
}

interface QuranProgress {
  id: number;
  examId: number;
  surahName: string;
  startVerse: number;
  endVerse: number;
  memorization: number;
  tajweed: number;
  totalScore: number;
  startDate: string;
  completionDate: string | null;
}

interface Attendance {
  id: number;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'EXCUSED';
  hisass: number;
}

interface StudentStats {
  totalExams: number;
  passedExams: number;
  averageScore: number;
  attendanceRate: number;
  averageMemorization: number;
  averageTajweed: number;
}

interface Student {
  id: number;
  name: string;
  age: number;
  grade: string;
  totalPoints: number;
}

interface ChildSummary {
  id: number;
  name: string;
  age: number;
  grade: string;
  totalPoints: number;
  stats: {
    averageScore: number;
    attendanceRate: number;
  };
  lastQuranProgress: {
    surahName: string;
    startVerse: number;
    endVerse: number;
    memorization: number;
    tajweed: number;
    date: string;
  } | null;
}

const ParentProgressPage = () => {
  const searchParams = useSearchParams() as URLSearchParams;
  const childId = searchParams.get('childId');

  const [student, setStudent] = useState<Student | null>(null);
  const [examResults, setExamResults] = useState<ExamResult[]>([]);
  const [quranProgress, setQuranProgress] = useState<QuranProgress[]>([]);
  const [attendance, setAttendance] = useState<Attendance[]>([]);
  const [stats, setStats] = useState<StudentStats | null>(null);
  const [children, setChildren] = useState<ChildSummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProgress = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const url = childId
          ? `/api/parent-progress?childId=${childId}`
          : '/api/parent-progress';

        const response = await fetch(url);

        if (!response.ok) {
          // محاولة قراءة رسالة الخطأ من الاستجابة
          const errorData = await response.json();
          const errorMessage = errorData?.message || 'فشل في جلب بيانات التقدم';
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log('Progress data received:', data);

        // التحقق من وجود بيانات
        if (childId) {
          // إذا تم تحديد طالب معين
          if (!data.student) {
            console.warn('No student data in response:', data);
            setError('لم يتم العثور على بيانات الطالب');
          } else {
            setStudent(data.student);
            setExamResults(data.examResults || []);
            setQuranProgress(data.quranProgress || []);
            setAttendance(data.attendance || []);
            setStats(data.stats || null);
          }
        } else {
          // إذا لم يتم تحديد طالب (عرض جميع الأبناء)
          if (!data.children) {
            console.warn('No children data in response:', data);
            setChildren([]);
          } else {
            setChildren(data.children);
          }
        }
      } catch (err) {
        console.error('Error fetching progress:', err);

        // محاولة الحصول على رسالة خطأ أكثر تفصيلاً من الخادم
        let errorMessage = 'حدث خطأ أثناء جلب بيانات التقدم';

        if (err instanceof Error) {
          errorMessage = err.message;
        } else if (err instanceof Response) {
          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await err.json();
            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (jsonError) {
            console.error('Error parsing error response:', jsonError);
          }
        }

        setError(errorMessage);
        toast.error('فشل في جلب بيانات التقدم: ' + errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProgress();
  }, [childId]);

  // عرض تفاصيل طالب محدد
  const renderStudentDetails = () => {
    if (!student || !stats) return null;

    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <FaUserGraduate className="text-[var(--primary-color)]" />
              <span>{student.name}</span>
            </h1>
            <p className="text-gray-500">
              العمر: {student.age} سنة • الصف: {student.grade}
            </p>
          </div>
          <Link href="/parents/progress">
            <Button variant="outline" className="flex items-center gap-2">
              <FaArrowLeft />
              <span>العودة إلى جميع الأبناء</span>
            </Button>
          </Link>
        </div>

        {/* ملخص الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-blue-100 text-blue-500">
                  <FaCalendarAlt className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">نسبة الحضور</p>
                  <p className="text-2xl font-bold text-gray-800">{stats.attendanceRate.toFixed(1)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-green-100 text-primary-color">
                  <FaTrophy className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">متوسط الدرجات</p>
                  <p className="text-2xl font-bold text-gray-800">{stats.averageScore.toFixed(1)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-purple-100 text-purple-500">
                  <FaBook className="text-xl" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">مجموع النقاط</p>
                  <p className="text-2xl font-bold text-gray-800">{student.totalPoints}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* تبويبات التفاصيل */}
        <Tabs defaultValue="exams">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="exams" className="flex items-center gap-2">
              <FaTrophy />
              <span>نتائج الامتحانات</span>
            </TabsTrigger>
            <TabsTrigger value="quran" className="flex items-center gap-2">
              <FaBook />
              <span>تقدم حفظ القرآن</span>
            </TabsTrigger>
            <TabsTrigger value="attendance" className="flex items-center gap-2">
              <FaCalendarAlt />
              <span>سجل الحضور</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="exams">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaTrophy className="text-[var(--primary-color)]" />
                  <span>نتائج الامتحانات</span>
                </CardTitle>
                <CardDescription>
                  عرض نتائج جميع الامتحانات
                </CardDescription>
              </CardHeader>
              <CardContent>
                {examResults.length === 0 ? (
                  <div className="text-center text-gray-500 py-4">
                    لا توجد نتائج امتحانات
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border p-2 text-right">التاريخ</th>
                          <th className="border p-2 text-right">نوع الامتحان</th>
                          <th className="border p-2 text-right">الشهر</th>
                          <th className="border p-2 text-right">السورة</th>
                          <th className="border p-2 text-right">الدرجة</th>
                          <th className="border p-2 text-right">النتيجة</th>
                        </tr>
                      </thead>
                      <tbody>
                        {examResults.map((result) => (
                          <tr key={result.id}>
                            <td className="border p-2">
                              {new Date(result.date).toLocaleDateString('fr-FR')}
                            </td>
                            <td className="border p-2">{result.examType}</td>
                            <td className="border p-2">{result.month}</td>
                            <td className="border p-2">{result.surahName || '-'}</td>
                            <td className="border p-2">
                              {result.points}/{result.maxPoints} ({((result.points / result.maxPoints) * 100).toFixed(1)}%)
                            </td>
                            <td className="border p-2">
                              {result.isPassed ? (
                                <span className="text-primary-color">ناجح</span>
                              ) : (
                                <span className="text-red-600">راسب</span>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="quran">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaBook className="text-[var(--primary-color)]" />
                  <span>تقدم حفظ القرآن</span>
                </CardTitle>
                <CardDescription>
                  عرض تقدم الطالب في حفظ القرآن الكريم
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">متوسط درجات الحفظ</h3>
                    <p className="text-2xl font-bold text-blue-600">{stats.averageMemorization.toFixed(1)}/10</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">متوسط درجات التجويد</h3>
                    <p className="text-2xl font-bold text-primary-color">{stats.averageTajweed.toFixed(1)}/10</p>
                  </div>
                </div>

                {quranProgress.length === 0 ? (
                  <div className="text-center text-gray-500 py-4">
                    لا يوجد سجل لتقدم الحفظ
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border p-2 text-right">التاريخ</th>
                          <th className="border p-2 text-right">السورة</th>
                          <th className="border p-2 text-right">من آية</th>
                          <th className="border p-2 text-right">إلى آية</th>
                          <th className="border p-2 text-right">الحفظ</th>
                          <th className="border p-2 text-right">التجويد</th>
                          <th className="border p-2 text-right">المتوسط</th>
                        </tr>
                      </thead>
                      <tbody>
                        {quranProgress.map((progress) => (
                          <tr key={progress.id}>
                            <td className="border p-2">
                              {new Date(progress.startDate).toLocaleDateString('fr-FR')}
                            </td>
                            <td className="border p-2">{progress.surahName}</td>
                            <td className="border p-2">{progress.startVerse}</td>
                            <td className="border p-2">{progress.endVerse}</td>
                            <td className="border p-2">{progress.memorization}/10</td>
                            <td className="border p-2">{progress.tajweed}/10</td>
                            <td className="border p-2">{progress.totalScore.toFixed(1)}/10</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="attendance">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FaCalendarAlt className="text-[var(--primary-color)]" />
                  <span>سجل الحضور</span>
                </CardTitle>
                <CardDescription>
                  عرض سجل حضور وغياب الطالب
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-blue-50 p-4 rounded-lg mb-6">
                  <h3 className="font-medium mb-2">نسبة الحضور</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>الحضور</span>
                      <span>{stats.attendanceRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={stats.attendanceRate} className="h-2" />
                  </div>
                </div>

                {attendance.length === 0 ? (
                  <div className="text-center text-gray-500 py-4">
                    لا يوجد سجل للحضور
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border p-2 text-right">التاريخ</th>
                          <th className="border p-2 text-right">الحصة</th>
                          <th className="border p-2 text-right">الحالة</th>
                        </tr>
                      </thead>
                      <tbody>
                        {attendance.map((record) => (
                          <tr key={record.id}>
                            <td className="border p-2">
                              {new Date(record.date).toLocaleDateString('fr-FR')}
                            </td>
                            <td className="border p-2">{record.hisass}</td>
                            <td className="border p-2">
                              {record.status === 'PRESENT' && (
                                <span className="text-primary-color">حاضر</span>
                              )}
                              {record.status === 'ABSENT' && (
                                <span className="text-red-600">غائب</span>
                              )}
                              {record.status === 'EXCUSED' && (
                                <span className="text-yellow-600">غائب بعذر</span>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  // عرض ملخص جميع الأبناء
  const renderChildrenSummary = () => {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">تقدم الأبناء</h1>
          <p className="text-gray-500">عرض ومتابعة تقدم جميع أبنائك</p>
        </div>

        {children.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-4">لا يوجد أبناء مسجلين</div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {children.map((child) => (
              <Card key={child.id} className="overflow-hidden">
                <CardHeader className="bg-[var(--primary-color)]/10">
                  <CardTitle className="flex items-center gap-2">
                    <FaUserGraduate className="text-[var(--primary-color)]" />
                    <span>{child.name}</span>
                  </CardTitle>
                  <CardDescription>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 mt-1">
                      <span>العمر: {child.age} سنة</span>
                      <span className="hidden sm:inline">•</span>
                      <span>الصف: {child.grade}</span>
                    </div>
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 bg-blue-100 rounded-full">
                          <FaCalendarAlt className="text-blue-600" />
                        </div>
                        <h3 className="font-medium">نسبة الحضور</h3>
                      </div>
                      <p className="text-2xl font-bold text-blue-600">{child.stats.attendanceRate.toFixed(1)}%</p>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 bg-green-100 rounded-full">
                          <FaChartLine className="text-primary-color" />
                        </div>
                        <h3 className="font-medium">متوسط الدرجات</h3>
                      </div>
                      <p className="text-2xl font-bold text-primary-color">{child.stats.averageScore.toFixed(1)}%</p>
                    </div>
                  </div>

                  {child.lastQuranProgress && (
                    <div className="border p-4 rounded-lg mb-4">
                      <h3 className="font-medium mb-2 flex items-center gap-2">
                        <FaBook className="text-[var(--primary-color)]" />
                        <span>آخر تقدم في حفظ القرآن</span>
                      </h3>
                      <p className="text-gray-700">
                        سورة {child.lastQuranProgress.surahName} -
                        من الآية {child.lastQuranProgress.startVerse} إلى الآية {child.lastQuranProgress.endVerse}
                      </p>
                      <div className="flex justify-between mt-2">
                        <div className="text-sm">
                          <span className="text-gray-600">الحفظ: </span>
                          <span className="font-medium">{child.lastQuranProgress.memorization}/10</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-600">التجويد: </span>
                          <span className="font-medium">{child.lastQuranProgress.tajweed}/10</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <Link href={`/parents/progress?childId=${child.id}`}>
                    <Button className="w-full bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                      عرض التفاصيل الكاملة
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : childId ? (
        renderStudentDetails()
      ) : (
        renderChildrenSummary()
      )}
    </div>
  );
};

export default ParentProgressPage;

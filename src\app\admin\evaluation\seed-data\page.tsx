'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Database, Play, CheckCircle, AlertTriangle } from 'lucide-react';
import { toast } from 'react-hot-toast';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

export default function SeedDataPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const runSeed = async () => {
    try {
      setLoading(true);
      setResult(null);
      
      toast.loading('جاري تشغيل ملف الـ seed...', { duration: 2000 });
      
      const response = await fetch('/api/admin/seed', {
        method: 'POST'
      });
      
      const data = await response.json();
      
      if (data.success) {
        setResult(data);
        toast.success('تم تشغيل ملف الـ seed بنجاح!');
      } else {
        toast.error('فشل في تشغيل ملف الـ seed');
        setResult({ success: false, error: data.error });
      }
    } catch (error) {
      console.error('Error running seed:', error);
      toast.error('حدث خطأ أثناء تشغيل ملف الـ seed');
      setResult({ success: false, error: 'Network error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute requiredPermission="admin.evaluation.reports.view">
      <div className="container mx-auto p-4 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-[var(--primary-color)]">تشغيل ملف البيانات الأساسية</h1>
        </div>

        {/* شرح الغرض */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-800">
              <Database className="ml-2 h-5 w-5" />
              ما هو ملف الـ Seed؟
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700">
            <p className="mb-2">
              <strong>ملف الـ Seed</strong> هو ملف يحتوي على بيانات أساسية لتشغيل النظام، يشمل:
            </p>
            <ul className="list-disc list-inside space-y-1 mb-4">
              <li>إنشاء المستخدمين الأساسيين (مدير، معلم)</li>
              <li>إنشاء الفصول والمواد الدراسية</li>
              <li>إنشاء الطلاب وأولياء الأمور</li>
              <li>إنشاء الامتحانات ونقاط الامتحانات</li>
              <li>إنشاء سور القرآن ومجالس الختم</li>
              <li>إنشاء البرامج التعليمية والمستويات</li>
            </ul>
            <p className="text-sm">
              <strong>ملاحظة:</strong> تشغيل هذا الملف آمن ولن يحذف البيانات الموجودة، بل سيضيف البيانات المفقودة فقط.
            </p>
          </CardContent>
        </Card>

        {/* زر التشغيل */}
        <Card>
          <CardHeader>
            <CardTitle>تشغيل ملف البيانات الأساسية</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600">
              اضغط على الزر أدناه لتشغيل ملف الـ seed وإنشاء البيانات الأساسية للنظام
            </p>
            
            <Button 
              onClick={runSeed} 
              disabled={loading}
              size="lg"
              className="bg-green-600 hover:bg-green-700"
            >
              {loading ? (
                <>
                  <Loader2 className="ml-2 h-5 w-5 animate-spin" />
                  جاري التشغيل...
                </>
              ) : (
                <>
                  <Play className="ml-2 h-5 w-5" />
                  تشغيل ملف البيانات الأساسية
                </>
              )}
            </Button>

            {loading && (
              <div className="text-sm text-gray-500">
                قد يستغرق هذا بضع دقائق، يرجى الانتظار...
              </div>
            )}
          </CardContent>
        </Card>

        {/* النتائج */}
        {result && (
          <Card className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <CardHeader>
              <CardTitle className={`flex items-center ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                {result.success ? (
                  <CheckCircle className="ml-2 h-5 w-5" />
                ) : (
                  <AlertTriangle className="ml-2 h-5 w-5" />
                )}
                {result.success ? 'تم التشغيل بنجاح!' : 'فشل في التشغيل'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {result.success ? (
                <div className="space-y-4">
                  <p className="text-green-700">
                    تم تشغيل ملف البيانات الأساسية بنجاح! يمكنك الآن:
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-green-700">
                    <li>الذهاب إلى كشف الدرجات لرؤية البيانات</li>
                    <li>تسجيل الدخول كمدير: admin / admin123</li>
                    <li>تسجيل الدخول كمعلم: teacher1 / teacher123</li>
                    <li>استكشاف جميع أقسام النظام</li>
                  </ul>
                  
                  <div className="flex gap-2 mt-4">
                    <Button 
                      onClick={() => window.location.href = '/admin/evaluation/student-report'}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      الذهاب إلى كشف الدرجات
                    </Button>
                    <Button 
                      onClick={() => window.location.href = '/admin/students'}
                      variant="outline"
                    >
                      إدارة الطلاب
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-red-700">
                  <p className="mb-2">حدث خطأ أثناء تشغيل ملف البيانات:</p>
                  <pre className="bg-red-100 p-2 rounded text-sm overflow-auto">
                    {result.error || 'خطأ غير معروف'}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* روابط مفيدة */}
        <Card>
          <CardHeader>
            <CardTitle>روابط مفيدة بعد تشغيل البيانات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-semibold">إدارة التقييم:</h4>
                <ul className="space-y-1 text-sm">
                  <li><a href="/admin/evaluation/student-report" className="text-blue-600 hover:underline">كشف الدرجات</a></li>
                  <li><a href="/admin/evaluation/scoring" className="text-blue-600 hover:underline">صفحة التنقيط</a></li>
                  <li><a href="/admin/evaluation/exam-report" className="text-blue-600 hover:underline">تقرير الامتحانات</a></li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">إدارة النظام:</h4>
                <ul className="space-y-1 text-sm">
                  <li><a href="/admin/students" className="text-blue-600 hover:underline">إدارة الطلاب</a></li>
                  <li><a href="/admin/teachers" className="text-blue-600 hover:underline">إدارة المعلمين</a></li>
                  <li><a href="/admin/classes" className="text-blue-600 hover:underline">إدارة الفصول</a></li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </ProtectedRoute>
  );
}

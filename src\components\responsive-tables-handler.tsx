'use client'

import { useEffect } from 'react'

/**
 * مكون لتفعيل ميزة التمرير الأفقي في الجداول المتجاوبة
 * يجب استخدام هذا المكون في الصفحات التي تحتوي على جداول متجاوبة
 */
export function ResponsiveTablesHandler() {
  useEffect(() => {
    // إضافة كود JavaScript لتفعيل ميزة التمرير الأفقي
    const handleResponsiveTables = () => {
      const containers = document.querySelectorAll('.responsive-table-container');
      containers.forEach(container => {
        const hasOverflow = container.scrollWidth > container.clientWidth;
        if (hasOverflow) {
          container.classList.add('has-overflow');
        } else {
          container.classList.remove('has-overflow');
        }
      });
    };

    // تنفيذ الدالة عند تحميل الصفحة وتغيير حجم النافذة
    handleResponsiveTables();
    window.addEventListener('resize', handleResponsiveTables);

    // تنظيف المستمع عند إزالة المكون
    return () => {
      window.removeEventListener('resize', handleResponsiveTables);
    };
  }, []);

  // هذا المكون لا يعرض أي شيء في واجهة المستخدم
  return null;
}

// تصدير افتراضي للمكون لاستخدامه مع dynamic import
export default ResponsiveTablesHandler;

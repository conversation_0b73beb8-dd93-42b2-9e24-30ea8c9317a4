'use client';

import { useState, useEffect } from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, Pencil, Trash2, ArrowLeft, Eye, BookOpen } from 'lucide-react';
import Link from 'next/link';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { QuestionSelector } from '@/components/question-selector';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface Exam {
  id: number;
  evaluationType: string;
  month: string;
  description: string | null;
  maxPoints: number;
  passingPoints: number;
  hasAutoGrading: boolean;
  examType: {
    id: number;
    name: string;
  } | null;
}

interface ExamQuestion {
  id: number;
  examId: number;
  questionId: number;
  order: number;
  points: number | null;
  question: {
    id: number;
    text: string;
    type: string;
    difficultyLevel: string;
    points: number;
    options: {
      id: number;
      text: string;
      isCorrect: boolean;
      order: number;
    }[];
    answers: {
      id: number;
      text: string;
      isCorrect: boolean;
      explanation: string | null;
    }[];
  };
}

export default function ExamQuestionsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const examId = searchParams ? searchParams.get('examId') : null;

  const [exam, setExam] = useState<Exam | null>(null);
  const [examQuestions, setExamQuestions] = useState<ExamQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddQuestionsDialogOpen, setIsAddQuestionsDialogOpen] = useState(false);
  const [isViewQuestionDialogOpen, setIsViewQuestionDialogOpen] = useState(false);
  const [isEditPointsDialogOpen, setIsEditPointsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<ExamQuestion | null>(null);
  const [editPoints, setEditPoints] = useState<string>('');

  useEffect(() => {
    if (!examId) {
      toast.error('معرف الامتحان مطلوب');
      router.push('/admin/evaluation/exams');
      return;
    }

    fetchExam();
    fetchExamQuestions();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examId, router]);

  const fetchExam = async () => {
    try {
      const response = await fetch(`/api/evaluation/exams/${examId}`);
      const result = await response.json();

      if (result.success) {
        setExam(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب بيانات الامتحان');
        router.push('/admin/evaluation/exams');
      }
    } catch (error) {
      console.error('Error fetching exam:', error);
      toast.error('حدث خطأ أثناء جلب بيانات الامتحان');
      router.push('/admin/evaluation/exams');
    }
  };

  const fetchExamQuestions = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/exam-questions?examId=${examId}`);
      const result = await response.json();

      if (result.success) {
        setExamQuestions(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب أسئلة الامتحان');
        setExamQuestions([]);
      }
    } catch (error) {
      console.error('Error fetching exam questions:', error);
      toast.error('حدث خطأ أثناء جلب أسئلة الامتحان');
      setExamQuestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewQuestion = (question: ExamQuestion) => {
    setSelectedQuestion(question);
    setIsViewQuestionDialogOpen(true);
  };

  const handleEditPoints = (question: ExamQuestion) => {
    setSelectedQuestion(question);
    setEditPoints(question.points?.toString() || question.question.points.toString());
    setIsEditPointsDialogOpen(true);
  };

  const handleDeleteQuestion = (question: ExamQuestion) => {
    setSelectedQuestion(question);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedQuestion) return;

    try {
      const response = await fetch(`/api/exam-questions?id=${selectedQuestion.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        toast.success('تم حذف السؤال من الامتحان بنجاح');
        setIsDeleteDialogOpen(false);
        fetchExamQuestions();
        fetchExam(); // تحديث حالة التصحيح الآلي
      } else {
        toast.error(result.error || 'حدث خطأ أثناء حذف السؤال من الامتحان');
      }
    } catch (error) {
      console.error('Error deleting exam question:', error);
      toast.error('حدث خطأ أثناء حذف السؤال من الامتحان');
    }
  };

  const handleSubmitEditPoints = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedQuestion) return;

    try {
      const numericPoints = parseFloat(editPoints);
      if (isNaN(numericPoints) || numericPoints <= 0) {
        toast.error('يرجى إدخال قيمة صالحة للنقاط');
        return;
      }

      const response = await fetch('/api/exam-questions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedQuestion.id,
          points: numericPoints
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('تم تحديث نقاط السؤال بنجاح');
        setIsEditPointsDialogOpen(false);
        fetchExamQuestions();
      } else {
        toast.error(result.error || 'حدث خطأ أثناء تحديث نقاط السؤال');
      }
    } catch (error) {
      console.error('Error updating question points:', error);
      toast.error('حدث خطأ أثناء تحديث نقاط السؤال');
    }
  };

  const getQuestionTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      MULTIPLE_CHOICE: 'اختيار من متعدد',
      TRUE_FALSE: 'صح أو خطأ',
      SHORT_ANSWER: 'إجابة قصيرة',
      ESSAY: 'مقال',
      MATCHING: 'مطابقة',
      FILL_BLANK: 'ملء الفراغات',
      ORDERING: 'ترتيب'
    };
    return types[type] || type;
  };

  const getDifficultyLevelLabel = (level: string) => {
    const levels: Record<string, string> = {
      EASY: 'سهل',
      MEDIUM: 'متوسط',
      HARD: 'صعب',
      VERY_HARD: 'صعب جداً'
    };
    return levels[level] || level;
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      MULTIPLE_CHOICE: 'bg-purple-100 text-purple-800',
      TRUE_FALSE: 'bg-indigo-100 text-indigo-800',
      SHORT_ANSWER: 'bg-blue-100 text-blue-800',
      ESSAY: 'bg-teal-100 text-teal-800',
      MATCHING: 'bg-yellow-100 text-yellow-800',
      FILL_BLANK: 'bg-orange-100 text-orange-800',
      ORDERING: 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getDifficultyColor = (level: string) => {
    const colors: Record<string, string> = {
      EASY: 'bg-green-100 text-green-800',
      MEDIUM: 'bg-blue-100 text-blue-800',
      HARD: 'bg-orange-100 text-orange-800',
      VERY_HARD: 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString: string): string => {
    const [year, month] = dateString.split('-');
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const getEvaluationTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      QURAN_MEMORIZATION: 'حفظ القرآن',
      QURAN_RECITATION: 'تلاوة القرآن',
      WRITTEN_EXAM: 'تحريري',
      ORAL_EXAM: 'شفهي',
      PRACTICAL_TEST: 'عملي',
      HOMEWORK: 'واجب منزلي',
      PROJECT: 'مشروع',
      REMOTE_EXAM: 'امتحان عن بعد'
    };
    return labels[type] || type;
  };

  return (
    <ProtectedRoute requiredPermission="admin.evaluation.exam-questions.view">
      <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <Link href="/admin/evaluation/exams" className="ml-0 sm:ml-4">
            <Button variant="outline" size="sm" className="w-full sm:w-auto">
              <ArrowLeft className="ml-2" size={16} />
              العودة إلى الامتحانات
            </Button>
          </Link>
          {exam && (
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">
              أسئلة امتحان {getEvaluationTypeLabel(exam.evaluationType)} - {formatDate(exam.month)}
            </h1>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <Button
            onClick={() => setIsAddQuestionsDialogOpen(true)}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
          >
            <Plus className="ml-2" size={16} />
            إضافة أسئلة من بنك الأسئلة
          </Button>
          <Link href={`/admin/evaluation/question-banks`} className="w-full sm:w-auto">
            <Button variant="outline" className="w-full">
              <BookOpen className="ml-2" size={16} />
              إدارة بنوك الأسئلة
            </Button>
          </Link>
        </div>
      </div>

      {exam && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>تفاصيل الامتحان</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h3 className="font-semibold mb-1">نوع التقييم:</h3>
                <p>{getEvaluationTypeLabel(exam.evaluationType)}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">نوع الامتحان:</h3>
                <p>{exam.examType?.name || '-'}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">الشهر:</h3>
                <p>{formatDate(exam.month)}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">الدرجة القصوى:</h3>
                <p>{exam.maxPoints}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">درجة النجاح:</h3>
                <p>{exam.passingPoints}</p>
              </div>
              <div>
                <h3 className="font-semibold mb-1">التصحيح الآلي:</h3>
                <Badge className={exam.hasAutoGrading ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                  {exam.hasAutoGrading ? 'مفعل' : 'غير مفعل'}
                </Badge>
              </div>
              {exam.description && (
                <div className="col-span-3">
                  <h3 className="font-semibold mb-1">الوصف:</h3>
                  <p>{exam.description}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
        </div>
      ) : examQuestions.length === 0 ? (
        <div className="text-center p-8 bg-white rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد أسئلة في هذا الامتحان</h3>
          <p className="text-gray-500 mb-4">
            يمكنك إضافة أسئلة من بنك الأسئلة أو إنشاء أسئلة جديدة.
          </p>
          <div className="flex justify-center space-x-4">
            <Button
              onClick={() => setIsAddQuestionsDialogOpen(true)}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
            >
              <Plus className="ml-2" size={16} />
              إضافة أسئلة من بنك الأسئلة
            </Button>
            <Link href="/admin/evaluation/question-banks">
              <Button variant="outline">
                <BookOpen className="ml-2" size={16} />
                إدارة بنوك الأسئلة
              </Button>
            </Link>
          </div>
        </div>
      ) : (
        <div>
          {/* عرض الجدول على الشاشات الكبيرة */}
          <div className="hidden md:block bg-white rounded-lg shadow overflow-hidden">
            <Table>
              <TableHeader className="bg-[var(--primary-color)]">
                <TableRow>
                  <TableHead className="text-white text-right">الترتيب</TableHead>
                  <TableHead className="text-white text-right">نص السؤال</TableHead>
                  <TableHead className="text-white text-right">النوع</TableHead>
                  <TableHead className="text-white text-right">الصعوبة</TableHead>
                  <TableHead className="text-white text-right">النقاط</TableHead>
                  <TableHead className="text-white text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {examQuestions.map((question) => (
                  <TableRow key={question.id} className="hover:bg-gray-50">
                    <TableCell>{question.order + 1}</TableCell>
                    <TableCell className="font-medium">
                      {question.question.text.length > 50
                        ? `${question.question.text.substring(0, 50)}...`
                        : question.question.text}
                    </TableCell>
                    <TableCell>
                      <Badge className={`${getTypeColor(question.question.type)}`}>
                        {getQuestionTypeLabel(question.question.type)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={`${getDifficultyColor(question.question.difficultyLevel)}`}>
                        {getDifficultyLevelLabel(question.question.difficultyLevel)}
                      </Badge>
                    </TableCell>
                    <TableCell>{question.points || question.question.points}</TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewQuestion(question)}
                          className="ml-2"
                        >
                          <Eye className="h-4 w-4 ml-1" />
                          عرض
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditPoints(question)}
                          className="ml-2"
                        >
                          <Pencil className="h-4 w-4 ml-1" />
                          تعديل النقاط
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteQuestion(question)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 ml-1" />
                          حذف
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* عرض البطاقات على الشاشات الصغيرة */}
          <div className="md:hidden space-y-4">
            {examQuestions.map((question) => (
              <Card key={question.id} className="border border-[#e9f7f5] shadow-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex justify-between items-center">
                    <span>السؤال {question.order + 1}</span>
                    <span className="text-sm font-normal">{question.points || question.question.points} نقطة</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-2 space-y-3">
                  <div className="text-sm">
                    <p className="font-medium mb-2">{question.question.text.length > 100
                      ? `${question.question.text.substring(0, 100)}...`
                      : question.question.text}</p>

                    <div className="flex flex-wrap gap-2 mb-3">
                      <Badge className={`${getTypeColor(question.question.type)}`}>
                        {getQuestionTypeLabel(question.question.type)}
                      </Badge>
                      <Badge className={`${getDifficultyColor(question.question.difficultyLevel)}`}>
                        {getDifficultyLevelLabel(question.question.difficultyLevel)}
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewQuestion(question)}
                      className="w-full"
                    >
                      <Eye className="h-4 w-4 ml-1" />
                      عرض
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditPoints(question)}
                      className="w-full"
                    >
                      <Pencil className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteQuestion(question)}
                      className="w-full text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 ml-1" />
                      حذف
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* View Question Dialog */}
      <Dialog open={isViewQuestionDialogOpen} onOpenChange={(open) => !open && setIsViewQuestionDialogOpen(false)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)] w-[95vw] mx-auto">
          <DialogHeader>
            <DialogTitle>عرض السؤال</DialogTitle>
            <DialogDescription>تفاصيل السؤال</DialogDescription>
          </DialogHeader>
          {selectedQuestion && (
            <div className="space-y-6">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="text-lg font-semibold mb-2 text-right">نص السؤال:</h3>
                <p className="text-right">{selectedQuestion.question.text}</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div>
                  <h3 className="text-sm font-semibold mb-1 text-right">نوع السؤال:</h3>
                  <Badge className={`${getTypeColor(selectedQuestion.question.type)} w-full justify-center py-1`}>
                    {getQuestionTypeLabel(selectedQuestion.question.type)}
                  </Badge>
                </div>
                <div>
                  <h3 className="text-sm font-semibold mb-1 text-right">مستوى الصعوبة:</h3>
                  <Badge className={`${getDifficultyColor(selectedQuestion.question.difficultyLevel)} w-full justify-center py-1`}>
                    {getDifficultyLevelLabel(selectedQuestion.question.difficultyLevel)}
                  </Badge>
                </div>
                <div>
                  <h3 className="text-sm font-semibold mb-1 text-right">النقاط:</h3>
                  <div className="bg-gray-100 rounded p-2 text-center">
                    {selectedQuestion.points || selectedQuestion.question.points}
                  </div>
                </div>
              </div>

              <Tabs defaultValue="options" className="w-full">
                <TabsList className="grid w-full grid-cols-2 bg-[#e9f7f5] p-1 rounded-xl">
                  <TabsTrigger value="options">الخيارات</TabsTrigger>
                  <TabsTrigger value="answers">الإجابات الصحيحة</TabsTrigger>
                </TabsList>
                <TabsContent value="options" className="p-4 border rounded-lg mt-2">
                  {selectedQuestion.question.options && selectedQuestion.question.options.length > 0 ? (
                    <ul className="space-y-2">
                      {selectedQuestion.question.options.map((option) => (
                        <li key={option.id} className="p-2 border rounded flex justify-between items-center">
                          <span className={option.isCorrect ? 'text-primary-color font-semibold' : ''}>
                            {option.text}
                          </span>
                          {option.isCorrect && (
                            <Badge className="bg-green-100 text-green-800">إجابة صحيحة</Badge>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-center text-gray-500">لا توجد خيارات لهذا السؤال</p>
                  )}
                </TabsContent>
                <TabsContent value="answers" className="p-4 border rounded-lg mt-2">
                  {selectedQuestion.question.answers && selectedQuestion.question.answers.length > 0 ? (
                    <ul className="space-y-4">
                      {selectedQuestion.question.answers.map((answer) => (
                        <li key={answer.id} className="p-3 border rounded bg-green-50">
                          <div className="font-semibold text-right mb-1">{answer.text}</div>
                          {answer.explanation && (
                            <div className="text-sm text-gray-600 text-right mt-2">
                              <span className="font-semibold">الشرح: </span>
                              {answer.explanation}
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-center text-gray-500">لا توجد إجابات محددة لهذا السؤال</p>
                  )}
                </TabsContent>
              </Tabs>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setIsViewQuestionDialogOpen(false)}
                >
                  إغلاق
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Points Dialog */}
      <Dialog open={isEditPointsDialogOpen} onOpenChange={(open) => !open && setIsEditPointsDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)] w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>تعديل نقاط السؤال</DialogTitle>
            <DialogDescription>قم بتعديل النقاط المخصصة للسؤال</DialogDescription>
          </DialogHeader>
          {selectedQuestion && (
            <form onSubmit={handleSubmitEditPoints} className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="text-sm font-semibold mb-2 text-right">نص السؤال:</h3>
                <p className="text-right">
                  {selectedQuestion.question.text.length > 100
                    ? `${selectedQuestion.question.text.substring(0, 100)}...`
                    : selectedQuestion.question.text}
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-right block">النقاط المخصصة للسؤال</label>
                <Input
                  type="number"
                  min="0.5"
                  step="0.5"
                  value={editPoints}
                  onChange={(e) => setEditPoints(e.target.value)}
                  dir="rtl"
                  className="text-right"
                  required
                />
                <p className="text-sm text-gray-500 text-right">
                  النقاط الافتراضية للسؤال: {selectedQuestion.question.points}
                </p>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditPointsDialogOpen(false)}
                  className="ml-2"
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  حفظ التغييرات
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Question Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-red-500 w-[95vw] max-w-lg mx-auto">
          <DialogHeader>
            <DialogTitle>حذف السؤال من الامتحان</DialogTitle>
            <DialogDescription>هل أنت متأكد من رغبتك في حذف هذا السؤال من الامتحان؟</DialogDescription>
          </DialogHeader>
          {selectedQuestion && (
            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                <p className="text-red-700 text-right">
                  سيتم حذف السؤال التالي من الامتحان:
                </p>
                <p className="text-gray-700 text-right mt-2 font-semibold">
                  {selectedQuestion.question.text}
                </p>
                <p className="text-red-700 text-right mt-2">
                  هذا الإجراء لا يؤثر على السؤال في بنك الأسئلة، ولكنه سيحذف ارتباطه بهذا الامتحان.
                </p>
              </div>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDeleteDialogOpen(false)}
                  className="ml-2"
                >
                  إلغاء
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  onClick={confirmDelete}
                >
                  تأكيد الحذف
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add Questions Dialog */}
      <Dialog open={isAddQuestionsDialogOpen} onOpenChange={(open) => !open && setIsAddQuestionsDialogOpen(false)}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto bg-gradient-to-b from-[#f8fffd] to-white border-t-4 border-[var(--primary-color)] w-[95vw] mx-auto">
          <DialogHeader>
            <DialogTitle>إضافة أسئلة من بنك الأسئلة</DialogTitle>
            <DialogDescription>اختر الأسئلة التي تريد إضافتها إلى الامتحان</DialogDescription>
          </DialogHeader>
          <QuestionSelector
            examId={examId ? parseInt(examId) : 0}
            onQuestionsAdded={() => {
              setIsAddQuestionsDialogOpen(false);
              fetchExamQuestions();
              fetchExam(); // تحديث حالة التصحيح الآلي
            }}
          />
        </DialogContent>
      </Dialog>
      </div>
    </ProtectedRoute>
  );
}


'use client';

import { useEffect, useRef } from 'react';
import { Chart, registerables } from 'chart.js';
import { useTheme } from 'next-themes';

// تسجيل جميع المكونات المطلوبة من chart.js
if (typeof window !== 'undefined') {
  Chart.register(...registerables);
}

interface PieChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
    }[];
  };
  options?: {
    responsive?: boolean;
    maintainAspectRatio?: boolean;
    plugins?: {
      legend?: {
        position?: 'top' | 'left' | 'bottom' | 'right';
        labels?: {
          font?: {
            family?: string;
            size?: number;
          };
          color?: string;
        };
      };
      tooltip?: {
        titleFont?: {
          family?: string;
          size?: number;
        };
        bodyFont?: {
          family?: string;
          size?: number;
        };
        rtl?: boolean;
        textDirection?: 'ltr' | 'rtl';
      };
    };
    cutout?: string | number;
    radius?: string | number;
    [key: string]: unknown;
  };
  height?: number;
  width?: number;
  className?: string;
  id?: string;
}

export function PieChart({ data, options = {}, height = 300, width = 300, className = '', id = 'pie-chart' }: PieChartProps) {
  const chartRef = useRef<HTMLCanvasElement | null>(null);
  const chartInstance = useRef<Chart | null>(null);
  const { theme = 'light' } = useTheme() || {};

  useEffect(() => {
    if (typeof window === 'undefined' || !chartRef.current) return;

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) return;

    // تدمير الرسم البياني السابق إذا كان موجودًا
    if (chartInstance.current) {
      chartInstance.current.destroy();
    }

    // تعيين الألوان الافتراضية إذا لم يتم توفيرها
    const defaultColors = [
      'rgba(22, 155, 136, 0.7)',
      'rgba(26, 177, 156, 0.7)',
      'rgba(45, 212, 191, 0.7)',
      'rgba(94, 234, 212, 0.7)',
      'rgba(153, 246, 228, 0.7)',
      'rgba(204, 251, 241, 0.7)',
      'rgba(255, 99, 132, 0.7)',
      'rgba(54, 162, 235, 0.7)',
      'rgba(255, 206, 86, 0.7)',
      'rgba(75, 192, 192, 0.7)',
      'rgba(153, 102, 255, 0.7)',
    ];

    // تأكد من أن البيانات صحيحة وليست undefined
    const safeLabels = data.labels || [];
    const safeDatasets = data.datasets || [];

    // طباعة البيانات للتشخيص
    console.log('PieChart labels:', safeLabels);
    console.log('PieChart datasets:', safeDatasets);

    const datasets = safeDatasets.map((dataset) => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || defaultColors.slice(0, safeLabels.length),
      borderColor: dataset.borderColor || defaultColors.map(color => color.replace('0.7', '1')).slice(0, safeLabels.length),
      borderWidth: dataset.borderWidth || 1,
    }));

    // تعيين الخيارات الافتراضية
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom' as const, // تغيير موضع المفتاح إلى الأسفل
          labels: {
            font: {
              family: 'Tajawal, sans-serif',
              size: 12, // تحديد حجم الخط
            },
            color: theme === 'dark' ? '#fff' : '#333',
            padding: 10, // إضافة تباعد بين العناصر
          },
          display: true, // التأكد من عرض المفتاح
        },
        tooltip: {
          titleFont: {
            family: 'Tajawal, sans-serif',
          },
          bodyFont: {
            family: 'Tajawal, sans-serif',
          },
          rtl: true,
          textDirection: 'rtl',
          callbacks: {
            // تخصيص نص التلميح
            label: function(context) {
              const label = safeLabels[context.dataIndex] || '';
              const value = context.raw || 0;
              return `${label}: ${value.toLocaleString('fr-FR')} د.ج`;
            }
          }
        },
      },
    };

    // إنشاء الرسم البياني
    chartInstance.current = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: safeLabels,
        datasets,
      },
      options: {
        ...defaultOptions,
        ...options,
      },
    });

    // تنظيف عند إزالة المكون
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, options, theme]);

  return (
    <div className={`relative ${className}`} style={{ height, width, margin: '0 auto' }}>
      <canvas ref={chartRef} id={id}></canvas>
    </div>
  );
}

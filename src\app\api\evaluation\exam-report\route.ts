import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/utils/getToken';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || !['ADMIN', 'TEACHER'].includes(userData.role)) {
      return NextResponse.json(
        { message: "غير مصرح به", success: false },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const month = searchParams.get('month');
    const evaluationType = searchParams.get('evaluationType');
    const classeId = searchParams.get('classeId');
    const subjectId = searchParams.get('subjectId');

    console.log('Generating exam report with filters:', { month, evaluationType, classeId, subjectId });

    // بناء شروط البحث
    const whereConditions: any = {};
    
    if (month) {
      whereConditions.month = month;
    }
    
    if (evaluationType) {
      whereConditions.evaluationType = evaluationType;
    }
    
    if (subjectId) {
      whereConditions.subjectId = parseInt(subjectId);
    }

    // جلب الامتحانات مع النقاط
    const exams = await prisma.exam.findMany({
      where: whereConditions,
      include: {
        subject: true,
        examType: true,
        exam_points: {
          include: {
            student: {
              include: {
                classe: true
              }
            },
            classSubject: {
              include: {
                classe: true,
                teacherSubject: {
                  include: {
                    subject: true,
                    teacher: true
                  }
                }
              }
            }
          },
          where: classeId ? {
            student: {
              classeId: parseInt(classeId)
            }
          } : undefined
        }
      },
      orderBy: [
        { month: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    console.log('Exams found:', exams.length);

    // تنسيق البيانات للتقرير
    const reportData = exams.map(exam => {
      const examPoints = exam.exam_points;
      
      // حساب الإحصائيات
      const totalStudents = examPoints.length;
      const passedStudents = examPoints.filter(point => Number(point.grade) >= exam.passingPoints).length;
      const failedStudents = totalStudents - passedStudents;
      const excellentStudents = examPoints.filter(point => Number(point.grade) >= 90).length;
      
      const totalGrades = examPoints.reduce((sum, point) => sum + Number(point.grade), 0);
      const averageGrade = totalStudents > 0 ? totalGrades / totalStudents : 0;
      
      const highestGrade = totalStudents > 0 ? Math.max(...examPoints.map(point => Number(point.grade))) : 0;
      const lowestGrade = totalStudents > 0 ? Math.min(...examPoints.map(point => Number(point.grade))) : 0;

      // تجميع الطلاب حسب الفصل
      const studentsByClass: Record<string, any[]> = {};
      examPoints.forEach(point => {
        const className = point.student.classe?.name || 'غير محدد';
        if (!studentsByClass[className]) {
          studentsByClass[className] = [];
        }
        studentsByClass[className].push({
          id: point.student.id,
          name: point.student.name,
          username: point.student.username,
          grade: Number(point.grade),
          status: Number(point.grade) >= exam.passingPoints ? 'نجح' : 'راسب',
          performance: Number(point.grade) >= 90 ? 'ممتاز' : 
                      Number(point.grade) >= 80 ? 'جيد جداً' :
                      Number(point.grade) >= 70 ? 'جيد' :
                      Number(point.grade) >= exam.passingPoints ? 'مقبول' : 'راسب'
        });
      });

      // ترتيب الطلاب حسب الدرجات
      Object.keys(studentsByClass).forEach(className => {
        studentsByClass[className].sort((a, b) => b.grade - a.grade);
      });

      return {
        id: exam.id,
        evaluationType: exam.evaluationType,
        month: exam.month,
        description: exam.description,
        maxPoints: exam.maxPoints,
        passingPoints: exam.passingPoints,
        subject: exam.subject?.name || 'غير محدد',
        examType: exam.examType?.name || 'غير محدد',
        statistics: {
          totalStudents,
          passedStudents,
          failedStudents,
          excellentStudents,
          averageGrade: Math.round(averageGrade * 100) / 100,
          highestGrade,
          lowestGrade,
          passRate: totalStudents > 0 ? Math.round((passedStudents / totalStudents) * 100) : 0
        },
        studentsByClass,
        createdAt: exam.createdAt
      };
    });

    // حساب الإحصائيات العامة
    const totalExams = reportData.length;
    const totalStudentEntries = reportData.reduce((sum, exam) => sum + exam.statistics.totalStudents, 0);
    const totalPassedEntries = reportData.reduce((sum, exam) => sum + exam.statistics.passedStudents, 0);
    const overallPassRate = totalStudentEntries > 0 ? Math.round((totalPassedEntries / totalStudentEntries) * 100) : 0;
    
    const allGrades = reportData.flatMap(exam => 
      Object.values(exam.studentsByClass).flat().map((student: any) => student.grade)
    );
    const overallAverage = allGrades.length > 0 ? 
      Math.round((allGrades.reduce((sum, grade) => sum + grade, 0) / allGrades.length) * 100) / 100 : 0;

    const summary = {
      totalExams,
      totalStudentEntries,
      totalPassedEntries,
      overallPassRate,
      overallAverage,
      reportGeneratedAt: new Date().toISOString(),
      filters: {
        month,
        evaluationType,
        classeId,
        subjectId
      }
    };

    return NextResponse.json({
      success: true,
      data: {
        summary,
        exams: reportData
      },
      message: `تم إنشاء تقرير الامتحانات بنجاح - ${totalExams} امتحان`
    });

  } catch (error) {
    console.error('Error generating exam report:', error);
    return NextResponse.json(
      { 
        message: "حدث خطأ أثناء إنشاء تقرير الامتحانات", 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

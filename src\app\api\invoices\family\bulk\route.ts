import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getToken } from '@/lib/auth';

const prisma = new PrismaClient();

// POST /api/invoices/family/bulk - إنشاء فواتير جماعية لعدة أولياء
export async function POST(req: NextRequest) {
  try {
    // التحقق من صلاحيات المستخدم
    const token = req.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول إلى هذه البيانات' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { parentIds, amount, dueDate, month, year, description, useIndividualAmounts } = body;

    // التحقق من وجود الحقول المطلوبة
    if (!parentIds || !Array.isArray(parentIds) || parentIds.length === 0) {
      return NextResponse.json(
        { error: 'يجب تحديد أولياء الأمور' },
        { status: 400 }
      );
    }

    if (!useIndividualAmounts && (!amount || amount <= 0)) {
      return NextResponse.json(
        { error: 'يجب تحديد المبلغ' },
        { status: 400 }
      );
    }

    if (!dueDate || !month || !year) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    // جلب أولياء الأمور مع أبنائهم
    const parents = await prisma.parent.findMany({
      where: {
        id: {
          in: parentIds.map(id => parseInt(id.toString()))
        }
      },
      include: {
        students: true
      }
    });

    if (parents.length !== parentIds.length) {
      return NextResponse.json(
        { error: 'بعض أولياء الأمور غير موجودين' },
        { status: 404 }
      );
    }

    // التحقق من وجود أبناء لكل ولي
    const parentsWithoutStudents = parents.filter(parent => parent.students.length === 0);
    if (parentsWithoutStudents.length > 0) {
      return NextResponse.json(
        {
          error: `الأولياء التالية أسماؤهم لا يوجد لديهم أبناء مسجلين: ${parentsWithoutStudents.map(p => p.name).join(', ')}`
        },
        { status: 400 }
      );
    }

    // إنشاء الفواتير الجماعية
    const invoices = await prisma.$transaction(
      parents.map(parent => {
        // حساب المبلغ للولي
        let invoiceAmount = amount;
        if (useIndividualAmounts && parent.amountPerStudent) {
          invoiceAmount = parent.amountPerStudent * parent.students.length;
        }

        return prisma.invoice.create({
          data: {
            parentId: parent.id,
            amount: invoiceAmount,
            dueDate: new Date(dueDate),
            month: parseInt(month.toString()),
            year: parseInt(year.toString()),
            description: description || `فاتورة جماعية لشهر ${month}/${year} - ${parent.name}`,
            type: 'FAMILY',
            status: 'UNPAID'
          }
        });
      })
    );

    console.log('✅ تم إنشاء فواتير جماعية:', {
      count: invoices.length,
      totalAmount: invoices.reduce((sum, inv) => sum + inv.amount, 0)
    });

    return NextResponse.json({
      success: true,
      message: `تم إنشاء ${invoices.length} فاتورة جماعية بإجمالي ${invoices.reduce((sum, inv) => sum + inv.amount, 0)} دج`,
      invoices: invoices.map((invoice, index) => ({
        id: invoice.id,
        amount: invoice.amount,
        parentName: parents[index].name,
        studentsCount: parents[index].students.length,
        status: invoice.status
      })),
      summary: {
        totalInvoices: invoices.length,
        totalAmount: invoices.reduce((sum, inv) => sum + inv.amount, 0),
        totalStudents: parents.reduce((sum, parent) => sum + parent.students.length, 0)
      }
    });

  } catch (error) {
    console.error('❌ خطأ في إنشاء الفواتير الجماعية:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الفواتير الجماعية' },
      { status: 500 }
    );
  }
}

// GET /api/invoices/family/bulk/parents - جلب أولياء الأمور المؤهلين للفوترة الجماعية
export async function GET(request: NextRequest) {
  try {
    // التحقق من صلاحيات المستخدم
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: 'غير مصرح به' },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== 'ADMIN' && userData.role !== 'EMPLOYEE')) {
      return NextResponse.json(
        { error: 'غير مصرح بالوصول إلى هذه البيانات' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';

    // بناء شروط البحث
    const whereConditions: any = {
      students: {
        some: {} // فقط الأولياء الذين لديهم أبناء
      }
    };

    if (search) {
      whereConditions.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    // جلب أولياء الأمور مع أبنائهم
    const parents = await prisma.parent.findMany({
      where: whereConditions,
      include: {
        students: {
          include: {
            classe: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    });

    // تنسيق البيانات
    const formattedParents = parents.map(parent => ({
      id: parent.id,
      name: parent.name,
      phone: parent.phone,
      email: parent.email,
      studentsCount: parent.students.length,
      amountPerStudent: parent.amountPerStudent,
      totalAmount: parent.amountPerStudent ? parent.amountPerStudent * parent.students.length : null,
      students: parent.students.map(student => ({
        id: student.id,
        name: student.name,
        grade: student.classe?.name || 'غير محدد'
      }))
    }));

    return NextResponse.json({
      success: true,
      parents: formattedParents,
      total: formattedParents.length
    });

  } catch (error) {
    console.error('❌ خطأ في جلب أولياء الأمور:', error);
    return NextResponse.json(
      { error: 'فشل في جلب أولياء الأمور' },
      { status: 500 }
    );
  }
}

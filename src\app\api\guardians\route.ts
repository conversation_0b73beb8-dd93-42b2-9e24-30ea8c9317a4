import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/guardians
export async function GET() {
  try {
    const guardians = await prisma.parent.findMany({
      select: {
        id: true,
        name: true,
        phone: true,
        students: true
      }
    });

    return NextResponse.json(guardians);
  } catch (error) {
    console.error('Error fetching guardians:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات الأولياء' },
      { status: 500 }
    );
  }
}
'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'react-toastify';
import { FaBookOpen, FaArrowRight, FaSave, FaSync, FaSearch } from 'react-icons/fa';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

// تعريف الأنواع
type Student = {
  id: number;
  name: string;
  classeId?: number;
  classe?: {
    id: number;
    name: string;
  };
};

type Class = {
  id: number;
  name: string;
};

type Surah = {
  id: number;
  name: string;
  number: number;
  versesCount: number;
};

type QuranProgressFormData = {
  studentId: number;
  surahId: number;
  startVerse: number;
  endVerse: number;
  memorization: number;
  tajweed: number;
  note?: string;
};

export default function QuranProgressPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const studentIdParam = searchParams ? searchParams.get('studentId') : null;

  // حالة البيانات
  const [students, setStudents] = useState<Student[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [surahs, setSurahs] = useState<Surah[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClassId, setSelectedClassId] = useState<string>('all');

  // حالة النموذج
  const [formData, setFormData] = useState<QuranProgressFormData>({
    studentId: studentIdParam ? parseInt(studentIdParam) : 0,
    surahId: 0,
    startVerse: 1,
    endVerse: 1,
    memorization: 7,
    tajweed: 7,
    note: ''
  });

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    Promise.all([
      fetchStudents(),
      fetchClasses(),
      fetchSurahs()
    ]).then(() => {
      setLoading(false);
    });
  }, []);

  // جلب قائمة الطلاب
  const fetchStudents = async () => {
    try {
      const response = await fetch('/api/students');
      if (!response.ok) {
        throw new Error('Failed to fetch students');
      }
      const data = await response.json();

      // التحقق من تنسيق البيانات المستلمة
      if (data && data.students && Array.isArray(data.students)) {
        setStudents(data.students);
      } else if (Array.isArray(data)) {
        setStudents(data);
      } else {
        console.error('Invalid students data format:', data);
        setStudents([]);
        toast.error('تنسيق بيانات الطلاب غير صالح');
      }

      // إذا تم تمرير معرف الطالب في عنوان URL، قم بتعيينه في النموذج
      if (studentIdParam) {
        setFormData(prev => ({
          ...prev,
          studentId: parseInt(studentIdParam)
        }));
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('حدث خطأ أثناء جلب قائمة الطلاب');
      setStudents([]);
    }
  };

  // جلب قائمة الفصول
  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/classes');
      if (!response.ok) {
        throw new Error('Failed to fetch classes');
      }
      const data = await response.json();
      // التحقق من أن البيانات تحتوي على مصفوفة classes
      if (data && data.classes && Array.isArray(data.classes)) {
        setClasses(data.classes);
      } else {
        console.error('Invalid classes data format:', data);
        setClasses([]);
        toast.error('تنسيق بيانات الفصول غير صالح');
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('حدث خطأ أثناء جلب قائمة الفصول');
      setClasses([]);
    }
  };

  // جلب قائمة السور
  const fetchSurahs = async () => {
    try {
      // استخدام API للحصول على قائمة السور
      const response = await fetch('/api/quran/surahs');
      if (!response.ok) {
        throw new Error('Failed to fetch surahs');
      }
      const data = await response.json();
      setSurahs(data);
    } catch (error) {
      console.error('Error fetching surahs:', error);
      toast.error('حدث خطأ أثناء جلب قائمة السور');

      // في حالة فشل جلب السور، استخدم قائمة افتراضية
      setSurahs([
        { id: 1, name: 'الفاتحة', number: 1, versesCount: 7 },
        { id: 2, name: 'البقرة', number: 2, versesCount: 286 },
        { id: 3, name: 'آل عمران', number: 3, versesCount: 200 },
        { id: 4, name: 'النساء', number: 4, versesCount: 176 },
        { id: 5, name: 'المائدة', number: 5, versesCount: 120 }
      ]);
    }
  };

  // تحديث حالة النموذج عند تغيير القيم
  const handleInputChange = (field: keyof QuranProgressFormData, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // تحديث آيات السورة عند تغيير السورة
  const handleSurahChange = (surahId: number) => {
    const selectedSurah = surahs.find(surah => surah.id === surahId);
    if (selectedSurah) {
      setFormData(prev => ({
        ...prev,
        surahId,
        startVerse: 1,
        endVerse: Math.min(5, selectedSurah.versesCount) // افتراضيًا 5 آيات أو أقل إذا كانت السورة قصيرة
      }));
    }
  };

  // تصفية الطلاب حسب البحث والفصل
  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClassId === 'all' || student.classeId?.toString() === selectedClassId;
    return matchesSearch && matchesClass;
  });

  // إرسال النموذج
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من صحة البيانات
    if (!formData.studentId) {
      toast.error('يرجى اختيار الطالب');
      return;
    }

    if (!formData.surahId) {
      toast.error('يرجى اختيار السورة');
      return;
    }

    if (formData.startVerse > formData.endVerse) {
      toast.error('يجب أن تكون الآية البداية أقل من أو تساوي آية النهاية');
      return;
    }

    setSubmitting(true);

    try {
      // إنشاء كائن البيانات للإرسال
      const quranProgressData = {
        studentId: formData.studentId,
        surahId: formData.surahId,
        startVerse: formData.startVerse,
        endVerse: formData.endVerse,
        memorization: formData.memorization,
        tajweed: formData.tajweed,
        note: formData.note || ''
      };

      // إرسال البيانات إلى API
      const response = await fetch('/api/quran/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(quranProgressData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save quran progress');
      }

      toast.success('تم تسجيل تقدم حفظ القرآن بنجاح');

      // إعادة تعيين النموذج
      setFormData({
        studentId: formData.studentId, // الاحتفاظ بالطالب المحدد
        surahId: 0,
        startVerse: 1,
        endVerse: 1,
        memorization: 7,
        tajweed: 7,
        note: ''
      });

      // إذا تم تمرير معرف الطالب في URL، قم بالعودة إلى صفحة تقدم الطالب
      if (studentIdParam) {
        router.push(`/admin/students/progress/${studentIdParam}`);
      }
    } catch (error) {
      console.error('Error saving quran progress:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ تقدم حفظ القرآن');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.quran-progress.view">
      <div className="p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen space-y-6" dir="rtl">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 mb-6">
        <div>
          <QuickActionButtons
            entityType="quran-progress"
            actions={[
              {
                key: 'back',
                label: 'العودة',
                icon: <FaArrowRight />,
                onClick: () => router.back(),
                variant: 'outline'
              }
            ]}
            className="mb-4"
          />
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaBookOpen className="text-[var(--primary-color)]" />
            تسجيل تقدم حفظ القرآن
          </h1>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* قسم اختيار الطالب */}
        <Card className="md:col-span-1 bg-white shadow-md border border-[#e0f2ef]">
          <CardHeader className="pb-2 border-b border-[#e0f2ef]">
            <CardTitle className="text-xl text-[var(--primary-color)]">اختيار الطالب</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="class-filter">تصفية حسب الفصل</Label>
                <Select
                  value={selectedClassId}
                  onValueChange={(value) => setSelectedClassId(value)}
                >
                  <SelectTrigger id="class-filter" className="w-full">
                    <SelectValue placeholder="جميع الفصول" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفصول</SelectItem>
                    {classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id.toString()}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="search-student">البحث عن طالب</Label>
                <div className="flex items-center">
                  <Input
                    id="search-student"
                    placeholder="اكتب اسم الطالب..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    className="ml-2 text-[var(--primary-color)]"
                    onClick={() => setSearchTerm('')}
                  >
                    <FaSearch />
                  </Button>
                </div>
              </div>

              <div className="mt-4">
                <Label>اختر الطالب</Label>
                <div className="max-h-[300px] overflow-y-auto border rounded-md mt-2">
                  {loading ? (
                    <div className="p-4 text-center text-gray-500">جاري تحميل قائمة الطلاب...</div>
                  ) : filteredStudents.length > 0 ? (
                    <div className="divide-y">
                      {filteredStudents.map((student) => (
                        <div
                          key={student.id}
                          className={`p-3 cursor-pointer hover:bg-[#e0f2ef] transition-colors ${
                            formData.studentId === student.id ? 'bg-[#e0f2ef] font-medium' : ''
                          }`}
                          onClick={() => handleInputChange('studentId', student.id)}
                        >
                          <div className="font-medium">{student.name}</div>
                          <div className="text-sm text-gray-500">
                            {student.classe?.name || 'غير مسجل في فصل'}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      لا يوجد طلاب مطابقين للبحث
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* نموذج تسجيل تقدم حفظ القرآن */}
        <Card className="md:col-span-2 bg-white shadow-md border border-[#e0f2ef]">
          <CardHeader className="pb-2 border-b border-[#e0f2ef]">
            <CardTitle className="text-xl text-[var(--primary-color)]">تسجيل تقدم الحفظ</CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* عرض معلومات الطالب المحدد */}
              {formData.studentId > 0 && (
                <div className="bg-[#e0f2ef] p-3 rounded-md mb-4">
                  <h3 className="font-medium text-[var(--primary-color)]">
                    الطالب المحدد: {students.find(s => s.id === formData.studentId)?.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    الفصل: {students.find(s => s.id === formData.studentId)?.classe?.name || 'غير مسجل في فصل'}
                  </p>
                </div>
              )}

              {/* اختيار السورة */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="surah">السورة</Label>
                  <Select
                    value={formData.surahId ? formData.surahId.toString() : ''}
                    onValueChange={(value) => handleSurahChange(parseInt(value))}
                  >
                    <SelectTrigger id="surah" className="w-full">
                      <SelectValue placeholder="اختر السورة" />
                    </SelectTrigger>
                    <SelectContent>
                      {surahs.map((surah) => (
                        <SelectItem key={surah.id} value={surah.id.toString()}>
                          {surah.name} ({surah.versesCount} آية)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* نطاق الآيات */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start-verse">من الآية</Label>
                  <Input
                    id="start-verse"
                    type="number"
                    min={1}
                    max={formData.endVerse}
                    value={formData.startVerse}
                    onChange={(e) => handleInputChange('startVerse', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <Label htmlFor="end-verse">إلى الآية</Label>
                  <Input
                    id="end-verse"
                    type="number"
                    min={formData.startVerse}
                    max={surahs.find(s => s.id === formData.surahId)?.versesCount || 1}
                    value={formData.endVerse}
                    onChange={(e) => handleInputChange('endVerse', parseInt(e.target.value))}
                  />
                </div>
              </div>

              {/* درجات التقييم */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="memorization">درجة الحفظ (من 10)</Label>
                  <div className="flex items-center">
                    <Input
                      id="memorization"
                      type="number"
                      min={0}
                      max={10}
                      step={0.5}
                      value={formData.memorization}
                      onChange={(e) => handleInputChange('memorization', parseFloat(e.target.value))}
                      className="flex-1"
                    />
                    <div className="ml-2 w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                      style={{
                        backgroundColor:
                          formData.memorization >= 8 ? '#10b981' :
                          formData.memorization >= 5 ? '#f59e0b' :
                          '#ef4444'
                      }}
                    >
                      {formData.memorization}
                    </div>
                  </div>
                </div>
                <div>
                  <Label htmlFor="tajweed">درجة التجويد (من 10)</Label>
                  <div className="flex items-center">
                    <Input
                      id="tajweed"
                      type="number"
                      min={0}
                      max={10}
                      step={0.5}
                      value={formData.tajweed}
                      onChange={(e) => handleInputChange('tajweed', parseFloat(e.target.value))}
                      className="flex-1"
                    />
                    <div className="ml-2 w-12 h-12 rounded-full flex items-center justify-center text-white font-bold"
                      style={{
                        backgroundColor:
                          formData.tajweed >= 8 ? '#10b981' :
                          formData.tajweed >= 5 ? '#f59e0b' :
                          '#ef4444'
                      }}
                    >
                      {formData.tajweed}
                    </div>
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div>
                <Label htmlFor="note">ملاحظات</Label>
                <Textarea
                  id="note"
                  placeholder="أدخل أي ملاحظات إضافية حول مستوى الحفظ..."
                  value={formData.note || ''}
                  onChange={(e) => handleInputChange('note', e.target.value)}
                  rows={3}
                />
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex justify-end gap-2 pt-4">
                <QuickActionButtons
                  entityType="quran-progress"
                  actions={[
                    {
                      key: 'reset',
                      label: 'إعادة تعيين',
                      icon: <FaSync />,
                      onClick: () => {
                        setFormData({
                          studentId: formData.studentId, // الاحتفاظ بالطالب المحدد
                          surahId: 0,
                          startVerse: 1,
                          endVerse: 1,
                          memorization: 7,
                          tajweed: 7,
                          note: ''
                        });
                      },
                      variant: 'secondary'
                    },
                    {
                      key: 'save',
                      label: submitting ? 'جاري الحفظ...' : 'حفظ التقدم',
                      icon: submitting ? <FaSync className="animate-spin" /> : <FaSave />,
                      onClick: () => {
                        const form = document.querySelector('form');
                        if (form) {
                          const event = new Event('submit', { bubbles: true, cancelable: true });
                          form.dispatchEvent(event);
                        }
                      },
                      variant: 'primary'
                    }
                  ]}
                  className="flex gap-2"
                />
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
      </div>
    </OptimizedProtectedRoute>
  );
}

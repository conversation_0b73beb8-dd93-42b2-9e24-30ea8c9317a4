'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from 'react-toastify';
import { FaSave, FaTimes } from 'react-icons/fa';

type Guardian = {
  id: number;
  name: string;
  phone: string;
};

type Class = {
  id: number;
  name: string;
};

type StudentFormData = {
  username: string;
  name: string;
  age: number;
  phone?: string;
  guardianId?: number;
  classeId?: number;
};

interface AddStudentDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
}

export default function AddStudentDialog({ isOpen, onCloseAction, onSuccessAction }: AddStudentDialogProps) {
  const [guardians, setGuardians] = useState<Guardian[]>([]);
  const [classes, setClasses] = useState<Class[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<StudentFormData>({
    username: '',
    name: '',
    age: 0,
    phone: '',
    guardianId: undefined,
    classeId: undefined
  });

  useEffect(() => {
    if (isOpen) {
      fetchGuardians();
      fetchClasses();
    }
  }, [isOpen]);

  const fetchGuardians = async () => {
    try {
      const response = await fetch('/api/parents');
      if (!response.ok) throw new Error('Failed to fetch guardians');
      const data = await response.json();
      setGuardians(data);
    } catch (error) {
      console.error('Error fetching guardians:', error);
      toast.error('فشل في جلب بيانات الأولياء');
    }
  };

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/admin/classes');
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();
      setClasses(data);
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('فشل في جلب بيانات الأقسام');
    }
  };

  const resetForm = () => {
    setFormData({
      username: '',
      name: '',
      age: 0,
      phone: '',
      guardianId: undefined,
      classeId: undefined
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/students', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save student');
      }

      toast.success('تم إضافة التلميذ بنجاح');
      resetForm();
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error saving student:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ بيانات التلميذ');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <>
      <Button
        type="button"
        variant="outline"
        onClick={onCloseAction}
        disabled={isLoading}
        className="border-gray-300 hover:bg-gray-100 flex items-center gap-1"
      >
        <FaTimes className="ml-1" />
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isLoading}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-1"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('addStudentForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        <FaSave className="ml-1" />
        {isLoading ? 'جاري الحفظ...' : 'حفظ'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="إضافة طالب جديد"
      variant="primary"
      footer={dialogFooter}
    >
      <form id="addStudentForm" onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username" className="text-right block font-medium">اسم المستخدم <span className="text-red-500">*</span></Label>
              <Input
                id="username"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                required
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                placeholder="أدخل اسم المستخدم"
              />
              <p className="text-xs text-gray-500">سيستخدم للدخول إلى النظام</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name" className="text-right block font-medium">الاسم الكامل <span className="text-red-500">*</span></Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                placeholder="أدخل الاسم الكامل"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="age" className="text-right block font-medium">العمر <span className="text-red-500">*</span></Label>
              <Input
                id="age"
                type="number"
                value={formData.age || ''}
                onChange={(e) => setFormData({ ...formData, age: parseInt(e.target.value) || 0 })}
                required
                min="1"
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                placeholder="أدخل العمر"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-right block font-medium">رقم الهاتف</Label>
              <Input
                id="phone"
                value={formData.phone || ''}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                dir="rtl"
                className="text-right w-full border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                placeholder="أدخل رقم الهاتف"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="class" className="text-right block font-medium">القسم</Label>
              <Select
                value={formData.classeId?.toString() || 'none'}
                onValueChange={(value) => setFormData({ ...formData, classeId: value !== 'none' ? parseInt(value) : undefined })}
              >
                <SelectTrigger id="class" className="w-full text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر القسم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">-- بدون قسم --</SelectItem>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="guardian" className="text-right block font-medium">الولي</Label>
              <Select
                value={formData.guardianId?.toString() || 'none'}
                onValueChange={(value) => setFormData({ ...formData, guardianId: value !== 'none' ? parseInt(value) : undefined })}
              >
                <SelectTrigger id="guardian" className="w-full text-right border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر الولي" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">-- بدون ولي --</SelectItem>
                  {guardians.map((guardian) => (
                    <SelectItem key={guardian.id} value={guardian.id.toString()}>
                      {guardian.name} - {guardian.phone}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="bg-gray-50 p-3 rounded-md text-sm">
            <p className="font-medium text-[var(--primary-color)]">ملاحظات:</p>
            <ul className="list-disc list-inside space-y-1 mt-1 text-gray-600">
              <li>الحقول المميزة بـ <span className="text-red-500">*</span> إلزامية</li>
              <li>يمكن إضافة الولي والقسم لاحقاً</li>
              <li>يجب أن يكون اسم المستخدم فريداً</li>
            </ul>
          </div>
        </form>
    </AnimatedDialog>
  );
}

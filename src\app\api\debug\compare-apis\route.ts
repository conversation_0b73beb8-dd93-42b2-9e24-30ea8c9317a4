import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 مقارنة نتائج APIs مختلفة...');

    // 1. نتائج API الأولياء (/api/parents)
    const parentsResponse = await fetch('http://localhost:3000/api/parents');
    const parentsData = await parentsResponse.json();

    // 2. نتائج API المدفوعات حسب الولي (/api/payments/by-parent)
    const paymentsResponse = await fetch('http://localhost:3000/api/payments/by-parent');
    const paymentsData = await paymentsResponse.json();

    // 3. فحص مباشر من قاعدة البيانات
    const directData = await prisma.parent.findMany({
      where: {
        name: {
          in: ['أحمد محمود', 'حسن محمد']
        }
      },
      include: {
        students: {
          include: {
            invoices: {
              include: {
                payments: {
                  where: { status: 'PAID' }
                }
              }
            },
            payments: {
              where: { status: 'PAID' }
            }
          }
        },
        invoices: {
          where: { type: 'FAMILY' },
          include: {
            payments: {
              where: { status: 'PAID' }
            }
          }
        }
      }
    });

    // 4. حساب الإحصائيات لكل ولي
    const comparison = directData.map(parent => {
      // حساب الفواتير الفردية
      let individualRequired = 0;
      let individualPaid = 0;
      let individualInvoicesCount = 0;

      parent.students.forEach(student => {
        student.invoices.forEach(invoice => {
          if (invoice.status !== 'CANCELLED' && invoice.type !== 'FAMILY') {
            individualRequired += invoice.amount;
            individualInvoicesCount++;
            const invoicePaid = invoice.payments.reduce((sum, p) => sum + p.amount, 0);
            individualPaid += invoicePaid;
          }
        });
      });

      // حساب الفواتير الجماعية
      let familyRequired = 0;
      let familyPaid = 0;
      let familyInvoicesCount = 0;

      parent.invoices.forEach(invoice => {
        if (invoice.status !== 'CANCELLED') {
          familyRequired += invoice.amount;
          familyInvoicesCount++;
          const invoicePaid = invoice.payments.reduce((sum, p) => sum + p.amount, 0);
          familyPaid += invoicePaid;
        }
      });

      // حساب المدفوعات المرتبطة بالفواتير الجماعية من خلال التلاميذ
      let familyPaidViaStudents = 0;
      parent.students.forEach(student => {
        student.payments.forEach(payment => {
          // البحث عن المدفوعات المرتبطة بفواتير جماعية
          const familyInvoice = parent.invoices.find(inv => inv.id === payment.invoiceId);
          if (familyInvoice) {
            familyPaidViaStudents += payment.amount;
          }
        });
      });

      const totalRequired = individualRequired + familyRequired;
      const totalPaid = individualPaid + familyPaid + familyPaidViaStudents;

      return {
        parentName: parent.name,
        direct: {
          individualRequired,
          individualPaid,
          individualInvoicesCount,
          familyRequired,
          familyPaid,
          familyPaidViaStudents,
          familyInvoicesCount,
          totalRequired,
          totalPaid,
          totalRemaining: Math.max(0, totalRequired - totalPaid)
        },
        fromParentsAPI: parentsData.find((p: any) => p.name === parent.name),
        fromPaymentsAPI: paymentsData.find((p: any) => p.name === parent.name)
      };
    });

    console.log('📊 نتائج المقارنة:', comparison);

    return NextResponse.json({
      success: true,
      comparison,
      parentsAPICount: parentsData.length,
      paymentsAPICount: paymentsData.length,
      directDataCount: directData.length
    });

  } catch (error) {
    console.error('❌ خطأ في مقارنة APIs:', error);
    return NextResponse.json(
      { error: 'فشل في مقارنة البيانات' },
      { status: 500 }
    );
  }
}

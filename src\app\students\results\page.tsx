"use client";
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaBook, FaChartLine } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface ExamResult {
  id: number;
  examId: number;
  examType: string;
  month: string;
  description: string;
  points: number;
  maxPoints: number;
  passingPoints: number;
  isPassed: boolean;
  surahName: string;
  date: string;
}

interface QuranProgress {
  id: number;
  examId: number;
  surahName: string;
  startVerse: number;
  endVerse: number;
  memorization: number;
  tajweed: number;
  totalScore: number;
  startDate: string;
  completionDate: string | null;
}

const StudentResultsPage = () => {
  const [examResults, setExamResults] = useState<ExamResult[]>([]);
  const [quranProgress, setQuranProgress] = useState<QuranProgress[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchResults = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/student-results');

        if (!response.ok) {
          throw new Error('فشل في جلب نتائج الامتحانات');
        }

        const data = await response.json();
        setExamResults(data.examResults);
        setQuranProgress(data.quranProgress);
      } catch (err) {
        console.error('Error fetching results:', err);
        setError('حدث خطأ أثناء جلب نتائج الامتحانات');
        toast.error('فشل في جلب نتائج الامتحانات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchResults();
  }, []);

  // حساب متوسط الدرجات
  const averageScore = examResults.length > 0
    ? examResults.reduce((sum, result) => sum + (result.points / result.maxPoints) * 100, 0) / examResults.length
    : 0;

  // حساب عدد الامتحانات التي تم اجتيازها
  const passedExams = examResults.filter(result => result.isPassed).length;

  // حساب متوسط درجات الحفظ والتجويد
  const averageMemorization = quranProgress.length > 0
    ? quranProgress.reduce((sum, progress) => sum + progress.memorization, 0) / quranProgress.length
    : 0;

  const averageTajweed = quranProgress.length > 0
    ? quranProgress.reduce((sum, progress) => sum + progress.tajweed, 0) / quranProgress.length
    : 0;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">نتائجي</h1>
          <p className="text-gray-500">عرض نتائج الامتحانات وتقدم الحفظ</p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* ملخص النتائج */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-blue-100 text-blue-500">
                    <FaTrophy className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">متوسط الدرجات</p>
                    <p className="text-2xl font-bold text-gray-800">{averageScore.toFixed(1)}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-green-100 text-primary-color">
                    <FaChartLine className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">الامتحانات المجتازة</p>
                    <p className="text-2xl font-bold text-gray-800">
                      {passedExams}/{examResults.length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-yellow-100 text-yellow-500">
                    <FaBook className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">متوسط الحفظ</p>
                    <p className="text-2xl font-bold text-gray-800">{averageMemorization.toFixed(1)}/10</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 rounded-full bg-purple-100 text-purple-500">
                    <FaBook className="text-xl" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">متوسط التجويد</p>
                    <p className="text-2xl font-bold text-gray-800">{averageTajweed.toFixed(1)}/10</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* تبويبات النتائج */}
          <Tabs defaultValue="exams">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="exams" className="flex items-center gap-2">
                <FaTrophy />
                <span>نتائج الامتحانات</span>
              </TabsTrigger>
              <TabsTrigger value="quran" className="flex items-center gap-2">
                <FaBook />
                <span>تقدم حفظ القرآن</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="exams">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaTrophy className="text-[var(--primary-color)]" />
                    <span>نتائج الامتحانات</span>
                  </CardTitle>
                  <CardDescription>
                    عرض نتائج جميع الامتحانات التي أجريتها
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {examResults.length === 0 ? (
                    <div className="text-center text-gray-500 py-4">
                      لا توجد نتائج امتحانات
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border p-2 text-right">التاريخ</th>
                            <th className="border p-2 text-right">نوع الامتحان</th>
                            <th className="border p-2 text-right">الشهر</th>
                            <th className="border p-2 text-right">السورة</th>
                            <th className="border p-2 text-right">الدرجة</th>
                            <th className="border p-2 text-right">النتيجة</th>
                          </tr>
                        </thead>
                        <tbody>
                          {examResults.map((result) => (
                            <tr key={result.id}>
                              <td className="border p-2">
                                {new Date(result.date).toLocaleDateString('fr-FR')}
                              </td>
                              <td className="border p-2">{result.examType}</td>
                              <td className="border p-2">{result.month}</td>
                              <td className="border p-2">{result.surahName || '-'}</td>
                              <td className="border p-2">
                                {result.points}/{result.maxPoints} ({((result.points / result.maxPoints) * 100).toFixed(1)}%)
                              </td>
                              <td className="border p-2">
                                {result.isPassed ? (
                                  <span className="text-primary-color">ناجح</span>
                                ) : (
                                  <span className="text-red-600">راسب</span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="quran">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FaBook className="text-[var(--primary-color)]" />
                    <span>تقدم حفظ القرآن</span>
                  </CardTitle>
                  <CardDescription>
                    عرض تقدمك في حفظ القرآن الكريم
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {quranProgress.length === 0 ? (
                    <div className="text-center text-gray-500 py-4">
                      لا يوجد سجل لتقدم الحفظ
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border p-2 text-right">التاريخ</th>
                            <th className="border p-2 text-right">السورة</th>
                            <th className="border p-2 text-right">من آية</th>
                            <th className="border p-2 text-right">إلى آية</th>
                            <th className="border p-2 text-right">الحفظ</th>
                            <th className="border p-2 text-right">التجويد</th>
                            <th className="border p-2 text-right">المتوسط</th>
                          </tr>
                        </thead>
                        <tbody>
                          {quranProgress.map((progress) => (
                            <tr key={progress.id}>
                              <td className="border p-2">
                                {new Date(progress.startDate).toLocaleDateString('fr-FR')}
                              </td>
                              <td className="border p-2">{progress.surahName}</td>
                              <td className="border p-2">{progress.startVerse}</td>
                              <td className="border p-2">{progress.endVerse}</td>
                              <td className="border p-2">{progress.memorization}/10</td>
                              <td className="border p-2">{progress.tajweed}/10</td>
                              <td className="border p-2">{progress.totalScore.toFixed(1)}/10</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  );
};

export default StudentResultsPage;

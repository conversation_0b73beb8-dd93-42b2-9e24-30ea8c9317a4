import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
    try {
        // الحصول على معرف المستخدم من التوكن
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || userData.role !== 'PARENT') {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userId = userData.id;

        // جلب معلومات المستخدم والملف الشخصي
        const user = await prisma.user.findUnique({
            where: {
                id: userId
            },
            include: {
                profile: true
            }
        });

        if (!user) {
            return NextResponse.json(
                { message: "لم يتم العثور على بيانات المستخدم" },
                { status: 404 }
            );
        }

        // جلب معلومات ولي الأمر باستخدام اسم المستخدم
        const parent = await prisma.parent.findFirst({
            where: {
                name: user.profile?.name || user.username
            },
            include: {
                students: {
                    include: {
                        classe: true
                    }
                }
            }
        });

        if (!parent) {
            // لم يتم العثور على بيانات ولي الأمر
            return NextResponse.json({
                name: user.profile?.name || user.username,
                children: [],
                notifications: []
            });
        }

        // جلب بيانات الطلاب التابعين لولي الأمر
        const childrenData = await Promise.all(parent.students.map(async (student) => {
            // جلب بيانات الطالب
            const studentData = await prisma.student.findUnique({
                where: { id: student.id },
                include: { classe: true }
            });

            if (!studentData) return null;

            // جلب نسبة التقدم
            const examPoints = await prisma.exam_points.findMany({
                where: { studentId: student.id },
                take: 10
            });

            let progress = 0;
            if (examPoints.length > 0) {
                const totalGrades = examPoints.reduce((sum, point) => sum + Number(point.grade), 0);
                const maxPossibleGrade = examPoints.length * 100;
                progress = Math.round((totalGrades / maxPossibleGrade) * 100);
            }

            // جلب نسبة الحضور
            const attendances = await prisma.attendance.findMany({
                where: { studentId: student.id },
                take: 10
            });

            let attendance = 0;
            if (attendances.length > 0) {
                const presentCount = attendances.filter(a => a.status === 'PRESENT').length;
                attendance = Math.round((presentCount / attendances.length) * 100);
            }

            // جلب الامتحان القادم
            // نحاول جلب آخر نقاط امتحان للطالب للحصول على معرف الامتحان
            const lastExamPoint = await prisma.exam_points.findFirst({
                where: { studentId: student.id },
                orderBy: { createdAt: 'desc' },
                include: { exam: true }
            });

            // نستخدم معرف الامتحان من آخر نقطة امتحان إذا وجدت
            const nextExam = lastExamPoint?.exam;

            // جلب آخر دفعة
            const lastPayment = await prisma.payment.findFirst({
                where: { studentId: student.id },
                orderBy: { date: 'desc' }
            });

            return {
                id: student.id,
                name: student.name,
                grade: studentData.classe?.name || '',
                progress: progress,
                attendance: attendance,
                nextExam: nextExam ? {
                    title: nextExam.description || `امتحان ${nextExam.evaluationType}`,
                    date: nextExam.createdAt.toISOString()
                } : null,
                lastPayment: lastPayment ? {
                    amount: lastPayment.amount,
                    date: lastPayment.date.toISOString(),
                    status: lastPayment.status
                } : null
            };
        }));

        // فلترة القيم null
        const children = childrenData.filter(child => child !== null);

        // جلب الإشعارات
        const notifications = await prisma.notification.findMany({
            where: { userId: userId },
            take: 5,
            orderBy: { createdAt: 'desc' }
        });

        // تنسيق الإشعارات
        const formattedNotifications = notifications.map(notification => ({
            id: notification.id,
            title: notification.title || '',
            date: notification.createdAt.toISOString(),
            read: notification.read || false
        }));

        return NextResponse.json({
            name: user.profile?.name || user.username,
            children: children,
            notifications: formattedNotifications
        });
    } catch (error) {
        console.error('Error fetching parent stats:', error);
        return NextResponse.json({
            name: '',
            children: [],
            notifications: []
        });
    }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/transactions - الحصول على جميع المعاملات (مداخيل ومصاريف)
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const query = searchParams.get('query') || '';
    const type = searchParams.get('type') || 'all'; // 'all', 'income', 'expense'
    const month = searchParams.get('month');
    const year = searchParams.get('year');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // بناء شروط البحث للتاريخ
    let dateFilter: {
      gte?: Date;
      lte?: Date;
    } = {};

    if (month && year) {
      const yearNum = parseInt(year);
      const monthNum = parseInt(month);

      if (!isNaN(yearNum) && !isNaN(monthNum)) {
        const startDate = new Date(yearNum, monthNum - 1, 1);
        const endDate = new Date(yearNum, monthNum, 0);

        dateFilter = {
          gte: startDate,
          lte: endDate,
        };
      }
    }

    // جلب المداخيل
    interface IncomeData {
      id: number;
      source: string;
      amount: number;
      date: Date;
      treasuryId: number;
    }

    let incomes: IncomeData[] = [];
    if (type === 'all' || type === 'income') {
      incomes = await prisma.income.findMany({
        where: {
          source: query ? { contains: query } : undefined,
          date: dateFilter.gte && dateFilter.lte ? dateFilter : undefined,
        },
        select: {
          id: true,
          source: true,
          amount: true,
          date: true,
          treasuryId: true,
        },
      });
    }

    // جلب المصاريف
    interface ExpenseData {
      id: number;
      purpose: string;
      amount: number;
      date: Date;
      treasuryId: number;
    }

    let expenses: ExpenseData[] = [];
    if (type === 'all' || type === 'expense') {
      expenses = await prisma.expense.findMany({
        where: {
          purpose: query ? { contains: query } : undefined,
          date: dateFilter.gte && dateFilter.lte ? dateFilter : undefined,
        },
        select: {
          id: true,
          purpose: true,
          amount: true,
          date: true,
          treasuryId: true,
        },
      });
    }

    // تحويل المداخيل والمصاريف إلى تنسيق موحد
    interface Transaction {
      id: string;
      type: 'income' | 'expense';
      description: string;
      amount: number;
      date: Date;
      treasuryId: number;
    }

    const transactions: Transaction[] = [
      ...incomes.map(income => ({
        id: `income-${income.id}`,
        type: 'income' as const,
        description: income.source,
        amount: income.amount,
        date: income.date,
        treasuryId: income.treasuryId,
      })),
      ...expenses.map(expense => ({
        id: `expense-${expense.id}`,
        type: 'expense' as const,
        description: expense.purpose,
        amount: expense.amount,
        date: expense.date,
        treasuryId: expense.treasuryId,
      })),
    ];

    // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
    transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // تطبيق الصفحات
    const paginatedTransactions = transactions.slice(skip, skip + limit);

    return NextResponse.json({
      transactions: paginatedTransactions,
      pagination: {
        total: transactions.length,
        pages: Math.ceil(transactions.length / limit),
        page,
        limit,
      },
    });
  } catch (error) {
    console.error('خطأ في جلب المعاملات:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المعاملات' },
      { status: 500 }
    );
  }
}

# مخطط الأصناف - نظام الإشعارات المحسن

## وصف المخطط
يوضح هذا المخطط بنية الأصناف والعلاقات في نظام الإشعارات المحسن الذي يدعم الإرسال الجماعي والمخصص.

## مخطط الأصناف (Class Diagram)

```mermaid
classDiagram
    class User {
        +Int id
        +String username
        +String email
        +String password
        +UserRole role
        +DateTime createdAt
        +DateTime updatedAt
        +Profile profile
        +Notification[] notifications
        +NotificationGroup[] groups
    }

    class Profile {
        +Int id
        +String name
        +String phone
        +Int userId
        +User user
    }

    class Notification {
        +Int id
        +String title
        +String content
        +NotificationType type
        +Boolean read
        +DateTime createdAt
        +Int userId
        +User user
        +Int relatedId
        +String link
        +Int groupId
        +NotificationGroup group
    }

    class NotificationGroup {
        +Int id
        +String name
        +String description
        +GroupType type
        +String targetRole
        +String[] targetUserIds
        +DateTime createdAt
        +DateTime updatedAt
        +Int createdBy
        +User creator
        +Notification[] notifications
        +NotificationRecipient[] recipients
    }

    class NotificationRecipient {
        +Int id
        +Int notificationId
        +Notification notification
        +Int userId
        +User user
        +Boolean delivered
        +Boolean read
        +DateTime deliveredAt
        +DateTime readAt
    }

    class NotificationTemplate {
        +Int id
        +String name
        +String title
        +String content
        +NotificationType type
        +String variables
        +Boolean isActive
        +DateTime createdAt
        +Int createdBy
    }

    class NotificationStats {
        +Int id
        +Int notificationId
        +Notification notification
        +Int totalRecipients
        +Int deliveredCount
        +Int readCount
        +Float deliveryRate
        +Float readRate
        +DateTime calculatedAt
    }

    %% العلاقات
    User ||--|| Profile : has
    User ||--o{ Notification : receives
    User ||--o{ NotificationGroup : creates
    User ||--o{ NotificationRecipient : receives
    
    Notification }o--|| User : belongs_to
    Notification }o--o| NotificationGroup : sent_to
    Notification ||--o{ NotificationRecipient : has
    Notification ||--o| NotificationStats : has
    
    NotificationGroup ||--o{ Notification : contains
    NotificationGroup ||--o{ NotificationRecipient : targets
    NotificationGroup }o--|| User : created_by
    
    NotificationRecipient }o--|| Notification : for
    NotificationRecipient }o--|| User : to

    %% التعدادات
    class UserRole {
        <<enumeration>>
        ADMIN
        EMPLOYEE
        TEACHER
        STUDENT
        PARENT
        PENDING
    }

    class NotificationType {
        <<enumeration>>
        GENERAL
        LESSON
        EXAM
        ATTENDANCE
        PAYMENT
        ACHIEVEMENT
        REMOTE_CLASS
    }

    class GroupType {
        <<enumeration>>
        ALL_USERS
        BY_ROLE
        CUSTOM_SELECTION
        PREDEFINED_GROUP
    }
```

## وصف الأصناف

### User (المستخدم)
- **الغرض**: تمثيل المستخدمين في النظام
- **العلاقات**: 
  - له ملف شخصي واحد (Profile)
  - يستقبل إشعارات متعددة (Notification)
  - يمكنه إنشاء مجموعات إشعارات (NotificationGroup)

### Profile (الملف الشخصي)
- **الغرض**: تخزين المعلومات الشخصية للمستخدم
- **العلاقات**: ينتمي لمستخدم واحد

### Notification (الإشعار)
- **الغرض**: تمثيل الإشعارات الفردية
- **العلاقات**: 
  - ينتمي لمستخدم واحد أو مجموعة
  - له إحصائيات (NotificationStats)
  - له مستلمين متعددين (NotificationRecipient)

### NotificationGroup (مجموعة الإشعارات)
- **الغرض**: تجميع المستخدمين لإرسال الإشعارات الجماعية
- **الأنواع**:
  - `ALL_USERS`: جميع المستخدمين
  - `BY_ROLE`: حسب الدور (معلمين، طلاب، إلخ)
  - `CUSTOM_SELECTION`: اختيار مخصص
  - `PREDEFINED_GROUP`: مجموعة محددة مسبقاً

### NotificationRecipient (مستلم الإشعار)
- **الغرض**: تتبع حالة تسليم وقراءة الإشعارات لكل مستلم
- **المعلومات المتتبعة**:
  - حالة التسليم
  - حالة القراءة
  - أوقات التسليم والقراءة

### NotificationTemplate (قالب الإشعار)
- **الغرض**: قوالب جاهزة للإشعارات المتكررة
- **الميزات**: دعم المتغيرات الديناميكية

### NotificationStats (إحصائيات الإشعار)
- **الغرض**: تتبع معدلات التسليم والقراءة
- **المقاييس**:
  - معدل التسليم
  - معدل القراءة
  - إجمالي المستلمين

## الميزات الجديدة
1. **الإرسال الجماعي**: دعم إرسال إشعار لمجموعات كبيرة
2. **التتبع المفصل**: تتبع حالة كل إشعار لكل مستلم
3. **الإحصائيات**: معدلات التفاعل والقراءة
4. **القوالب**: قوالب جاهزة للإشعارات المتكررة
5. **المرونة**: دعم أنواع مختلفة من المجموعات

"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { toast } from 'react-toastify';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  FaLayerGroup,
  FaChalkboardTeacher,
  FaBookOpen,
  FaEdit,
  FaArrowLeft,
  FaPlus
} from "react-icons/fa";
import Link from "next/link";
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface Level {
  id: number;
  name: string;
  description?: string;
  order: number;
}

interface Teacher {
  id: number;
  name: string;
}

interface TeacherSubject {
  id: number;
  teacher: Teacher;
}

interface CurriculumResource {
  id: number;
  title: string;
  type: string;
  url: string;
}

interface CurriculumLesson {
  id: number;
  title: string;
  description?: string;
  order: number;
  resources: CurriculumResource[];
}

interface CurriculumUnit {
  id: number;
  title: string;
  description?: string;
  order: number;
  lessons: CurriculumLesson[];
}

interface Subject {
  id: number;
  name: string;
  description?: string;
  levelId?: number | null;
  level?: Level | null;
  hasStudyPlan: boolean;
  teacherSubjects: TeacherSubject[];
  units: CurriculumUnit[];
}

export default function SubjectDetailsPage({ params }: { params: { id: string } }) {
  const [subject, setSubject] = useState<Subject | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    fetchSubject();
  }, []);

  const fetchSubject = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/admin/subjects/${params.id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch subject");
      }
      const data = await response.json();
      setSubject(data);
    } catch (error) {
      console.error(error);
      toast.error("حدث خطأ أثناء جلب بيانات المادة");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Skeleton className="h-40" />
          <Skeleton className="h-40" />
          <Skeleton className="h-40" />
        </div>
        <Skeleton className="h-96" />
      </div>
    );
  }

  if (!subject) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
          <h2 className="text-xl font-bold mb-2">لم يتم العثور على المادة</h2>
          <p>المادة المطلوبة غير موجودة أو تم حذفها.</p>
          <Link href="/admin/subjects">
            <Button variant="outline" className="mt-4">
              <FaArrowLeft className="ml-2" /> العودة إلى قائمة المواد
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute requiredPermission="admin.subjects.view">
      <div className="container mx-auto p-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Link href="/admin/subjects">
              <Button variant="ghost" size="sm" className="gap-1">
                <FaArrowLeft /> العودة
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">{subject.name}</h1>
            {subject.level && (
              <Badge variant="outline" className="bg-[#e0f2ef] text-[var(--primary-color)] border-[var(--primary-color)]">
                <FaLayerGroup className="mr-1" /> {subject.level.name}
              </Badge>
            )}
          </div>
          {subject.description && (
            <p className="text-gray-600 mt-1">{subject.description}</p>
          )}
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Link href={`/admin/subjects/${subject.id}/edit`}>
            <Button variant="outline" className="gap-1">
              <FaEdit /> تعديل المادة
            </Button>
          </Link>
          <Link href={`/admin/subjects/${subject.id}/curriculum`}>
            <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white gap-1">
              <FaPlus /> إضافة محتوى
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <FaLayerGroup className="text-[var(--primary-color)]" /> المستوى
            </CardTitle>
          </CardHeader>
          <CardContent>
            {subject.level ? (
              <div>
                <h3 className="font-bold text-xl">{subject.level.name}</h3>
                {subject.level.description && (
                  <p className="text-gray-600 mt-1">{subject.level.description}</p>
                )}
              </div>
            ) : (
              <p className="text-gray-500">لم يتم تعيين مستوى لهذه المادة</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <FaChalkboardTeacher className="text-[var(--primary-color)]" /> المعلمين
            </CardTitle>
          </CardHeader>
          <CardContent>
            {subject.teacherSubjects.length > 0 ? (
              <div className="space-y-2">
                {subject.teacherSubjects.map(ts => (
                  <div key={ts.id} className="flex items-center gap-2">
                    <Badge variant="secondary">
                      <FaChalkboardTeacher className="mr-1" />
                    </Badge>
                    <span>{ts.teacher.name}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">لا يوجد معلمين مرتبطين بهذه المادة</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2">
              <FaBookOpen className="text-[var(--primary-color)]" /> الخطة الدراسية
            </CardTitle>
          </CardHeader>
          <CardContent>
            {subject.hasStudyPlan ? (
              <div>
                <Badge className="bg-green-100 text-green-800 border-green-300 mb-2">
                  متوفرة
                </Badge>
                <p className="text-gray-600">
                  {subject.units.length > 0
                    ? `تحتوي على ${subject.units.length} وحدة دراسية`
                    : "لم يتم إضافة وحدات دراسية بعد"}
                </p>
              </div>
            ) : (
              <div>
                <Badge variant="outline" className="text-gray-500 mb-2">
                  غير متوفرة
                </Badge>
                <p className="text-gray-600">لم يتم إعداد خطة دراسية لهذه المادة</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full mb-6">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="curriculum">المحتوى التعليمي</TabsTrigger>
          <TabsTrigger value="teachers">المعلمين</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>نظرة عامة على المادة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-bold mb-2">وصف المادة</h3>
                  <p>{subject.description || "لا يوجد وصف متاح لهذه المادة"}</p>
                </div>

                <div>
                  <h3 className="font-bold mb-2">المستوى</h3>
                  <p>{subject.level?.name || "غير محدد"}</p>
                </div>

                <div>
                  <h3 className="font-bold mb-2">عدد المعلمين</h3>
                  <p>{subject.teacherSubjects.length}</p>
                </div>

                <div>
                  <h3 className="font-bold mb-2">عدد الوحدات الدراسية</h3>
                  <p>{subject.units.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="curriculum">
          <Card>
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                <span>المحتوى التعليمي</span>
                <Link href={`/admin/subjects/${subject.id}/curriculum`}>
                  <Button size="sm" className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white">
                    <FaPlus className="mr-1" /> إضافة محتوى
                  </Button>
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {subject.units.length > 0 ? (
                <div className="space-y-6">
                  {subject.units.map((unit, index) => (
                    <div key={unit.id} className="border rounded-lg p-4">
                      <h3 className="text-lg font-bold mb-2 flex items-center gap-2">
                        <Badge variant="outline" className="bg-[#e0f2ef] text-[var(--primary-color)] border-[var(--primary-color)]">
                          {index + 1}
                        </Badge>
                        {unit.title}
                      </h3>
                      {unit.description && <p className="text-gray-600 mb-4">{unit.description}</p>}

                      {unit.lessons.length > 0 ? (
                        <div className="space-y-3 mt-4">
                          <h4 className="font-bold text-sm text-gray-500">الدروس:</h4>
                          {unit.lessons.map((lesson, lessonIndex) => (
                            <div key={lesson.id} className="border-r-2 border-[var(--primary-color)] pr-4 py-1">
                              <h5 className="font-bold">{lessonIndex + 1}. {lesson.title}</h5>
                              {lesson.description && <p className="text-sm text-gray-600">{lesson.description}</p>}

                              {lesson.resources.length > 0 && (
                                <div className="mt-2">
                                  <h6 className="text-xs text-gray-500 mb-1">الموارد:</h6>
                                  <div className="flex flex-wrap gap-2">
                                    {lesson.resources.map(resource => (
                                      <Badge key={resource.id} variant="secondary" className="text-xs">
                                        {resource.title}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">لا توجد دروس في هذه الوحدة</p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FaBookOpen className="mx-auto text-4xl text-gray-300 mb-4" />
                  <h3 className="text-xl font-bold text-gray-600 mb-2">لا يوجد محتوى تعليمي</h3>
                  <p className="text-gray-500 mb-4">لم يتم إضافة أي وحدات دراسية لهذه المادة بعد</p>
                  <Link href={`/admin/subjects/${subject.id}/curriculum`}>
                    <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white">
                      <FaPlus className="mr-2" /> إضافة محتوى تعليمي
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="teachers">
          <Card>
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                <span>المعلمين</span>
                <Link href={`/admin/subjects/${subject.id}/teachers`}>
                  <Button size="sm" className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white">
                    <FaPlus className="mr-1" /> ربط معلم
                  </Button>
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {subject.teacherSubjects.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {subject.teacherSubjects.map(ts => (
                    <Card key={ts.id} className="overflow-hidden">
                      <div className="bg-[var(--primary-color)] h-2"></div>
                      <CardContent className="pt-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-[#e0f2ef] p-3 rounded-full">
                            <FaChalkboardTeacher className="text-[var(--primary-color)] text-xl" />
                          </div>
                          <div>
                            <h3 className="font-bold">{ts.teacher.name}</h3>
                            <Link
                              href={`/admin/teachers/${ts.teacher.id}`}
                              className="text-xs text-blue-600 hover:underline"
                            >
                              عرض الملف الشخصي
                            </Link>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FaChalkboardTeacher className="mx-auto text-4xl text-gray-300 mb-4" />
                  <h3 className="text-xl font-bold text-gray-600 mb-2">لا يوجد معلمين</h3>
                  <p className="text-gray-500 mb-4">لم يتم ربط أي معلمين بهذه المادة بعد</p>
                  <Link href={`/admin/subjects/${subject.id}/teachers`}>
                    <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white">
                      <FaPlus className="mr-2" /> ربط معلم بالمادة
                    </Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </ProtectedRoute>
  );
}

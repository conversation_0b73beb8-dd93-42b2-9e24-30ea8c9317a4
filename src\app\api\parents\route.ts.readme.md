# API الأولياء المحسن

## 📋 الوصف
API endpoint محسن لإدارة الأولياء مع حساب دقيق للديون والمدفوعات وتحسينات شاملة على الأمان والأداء.

## 🎯 الهدف
- حساب دقيق للديون والمدفوعات
- تحسين الأمان والتحقق من البيانات
- دعم شامل لإدارة الأولياء
- تحسين الأداء والاستجابة

## 🔗 المسارات

### GET /api/parents
جلب جميع الأولياء مع حساب الديون والمدفوعات

#### الاستجابة:
```json
[
  {
    "id": "123",
    "name": "محمد أحمد",
    "phone": "0555123456",
    "email": "<EMAIL>",
    "address": "الجزائر العاصمة",
    "amountPerStudent": 5000,
    "students": [
      {
        "id": "456",
        "name": "أحمد محمد",
        "grade": "السنة الأولى"
      }
    ],
    "createdAt": "2025-01-01T00:00:00.000Z",
    "totalDebt": 2500,
    "totalRequired": 10000,
    "totalPaid": 7500,
    "dueInvoices": 1,
    "lastPaymentDate": "2025-06-20T00:00:00.000Z",
    "user": {
      "id": 789,
      "username": "mohamed_ahmed",
      "profile": {
        "name": "محمد أحمد",
        "phone": "0555123456"
      }
    }
  }
]
```

### POST /api/parents
إضافة ولي جديد

#### المعاملات المطلوبة:
- `name`: اسم الولي
- `phone`: رقم الهاتف
- `username`: اسم المستخدم
- `password`: كلمة المرور

#### المعاملات الاختيارية:
- `email`: البريد الإلكتروني
- `address`: العنوان
- `amountPerStudent`: المبلغ المطلوب لكل تلميذ

#### مثال على الطلب:
```json
{
  "name": "محمد أحمد",
  "phone": "0555123456",
  "email": "<EMAIL>",
  "address": "الجزائر العاصمة",
  "amountPerStudent": 5000,
  "username": "mohamed_ahmed",
  "password": "securePassword123"
}
```

### PUT /api/parents
تحديث بيانات ولي موجود

#### المعاملات:
- `id`: معرف الولي
- باقي الحقول القابلة للتحديث

## 🔧 التحسينات المطبقة

### 1. حساب دقيق للديون والمدفوعات

#### قبل الإصلاح:
```typescript
// حساب خاطئ - يشمل جميع الفواتير
student.invoices.forEach(invoice => {
  totalDebt += invoice.amount; // خطأ: يشمل الملغاة
  
  const invoicePaid = invoice.payments.reduce((sum, payment) => 
    sum + payment.amount, 0); // خطأ: يشمل غير المؤكدة
  totalPaid += invoicePaid;
});
```

#### بعد الإصلاح:
```typescript
// حساب صحيح - يستبعد الملغاة ويشمل المؤكدة فقط
student.invoices.forEach(invoice => {
  // تجاهل الفواتير الملغاة
  if (invoice.status === 'CANCELLED') {
    return;
  }

  totalRequired += invoice.amount;

  // حساب إجمالي المدفوعات للفاتورة (المؤكدة فقط)
  const invoicePaid = invoice.payments
    .filter(payment => payment.status === 'PAID')
    .reduce((sum, payment) => sum + payment.amount, 0);
  totalPaid += invoicePaid;

  // إذا كانت الفاتورة غير مدفوعة بالكامل وليست ملغاة
  if (invoicePaid < invoice.amount && invoice.status !== 'CANCELLED') {
    dueInvoices++;
  }
});

// حساب المبلغ المتبقي (الدين الفعلي)
const remainingDebt = Math.max(0, totalRequired - totalPaid);
```

### 2. تنظيف وتحقق البيانات

#### في POST:
```typescript
// تنظيف المدخلات
name = sanitizeInput(name);
phone = sanitizeInput(phone);
email = sanitizeInput(email);
address = sanitizeInput(address);
username = sanitizeInput(username);
password = sanitizeInput(password);

// التحقق من صحة المبلغ لكل تلميذ
if (amountPerStudent && !validateAmount(parseFloat(amountPerStudent))) {
  return NextResponse.json(
    { error: 'المبلغ لكل تلميذ غير صحيح' },
    { status: 400 }
  );
}
```

#### في PUT:
```typescript
// نفس التحققات للتحديث
name = sanitizeInput(name);
phone = sanitizeInput(phone);
// ... باقي الحقول

if (amountPerStudent && !validateAmount(parseFloat(amountPerStudent))) {
  return NextResponse.json(
    { error: 'المبلغ لكل تلميذ غير صحيح' },
    { status: 400 }
  );
}
```

### 3. تحسين آخر تاريخ دفعة
```typescript
// تحديث آخر تاريخ دفعة (من المدفوعات المؤكدة فقط)
const confirmedPayments = student.payments.filter(payment => payment.status === 'PAID');
if (confirmedPayments.length > 0) {
  const studentLastPayment = new Date(confirmedPayments[0].date);
  if (!lastPaymentDate || studentLastPayment > lastPaymentDate) {
    lastPaymentDate = studentLastPayment;
  }
}
```

### 4. إرجاع بيانات شاملة
```typescript
return {
  ...parent,
  user: user || null,
  totalDebt: remainingDebt, // المبلغ المتبقي (الدين الفعلي)
  totalRequired, // إجمالي المطلوب
  totalPaid, // إجمالي المدفوع
  dueInvoices,
  lastPaymentDate
};
```

## 📊 الحقول المحسوبة

### totalDebt (الدين الفعلي)
- **التعريف:** المبلغ المتبقي = totalRequired - totalPaid
- **الحساب:** `Math.max(0, totalRequired - totalPaid)`
- **الغرض:** إظهار المبلغ الفعلي المستحق على الولي

### totalRequired (إجمالي المطلوب)
- **التعريف:** مجموع جميع الفواتير غير الملغاة
- **الحساب:** مجموع `invoice.amount` للفواتير التي `status !== 'CANCELLED'`
- **الغرض:** إظهار إجمالي المبلغ المطلوب من الولي

### totalPaid (إجمالي المدفوع)
- **التعريف:** مجموع جميع المدفوعات المؤكدة
- **الحساب:** مجموع `payment.amount` للمدفوعات التي `status === 'PAID'`
- **الغرض:** إظهار إجمالي ما دفعه الولي فعلياً

### dueInvoices (الفواتير المستحقة)
- **التعريف:** عدد الفواتير غير المدفوعة بالكامل
- **الحساب:** عدد الفواتير التي `invoicePaid < invoice.amount && status !== 'CANCELLED'`
- **الغرض:** إظهار عدد الفواتير التي تحتاج متابعة

### lastPaymentDate (آخر دفعة)
- **التعريف:** تاريخ آخر دفعة مؤكدة
- **الحساب:** أحدث `payment.date` للمدفوعات التي `status === 'PAID'`
- **الغرض:** متابعة نشاط الدفع للولي

## 🔒 قواعد الأمان

### تنظيف المدخلات
- إزالة الأحرف الضارة (`<>`)
- إزالة المسافات الزائدة
- تحويل القيم السالبة إلى موجبة للمبالغ

### التحقق من البيانات
- التحقق من وجود الحقول المطلوبة
- التحقق من صحة المبالغ (0 < amount < 1,000,000)
- التحقق من عدم تكرار اسم المستخدم

### معالجة الأخطاء
- رسائل خطأ واضحة ومفيدة
- تسجيل الأخطاء في console
- إرجاع رموز HTTP صحيحة

## 📈 تحسينات الأداء

### استعلامات محسنة
```typescript
// جلب البيانات المطلوبة فقط
include: {
  students: {
    include: {
      classe: true,
      invoices: {
        include: {
          payments: {
            where: { status: 'PAID' }, // المؤكدة فقط
            orderBy: { date: 'desc' }
          }
        }
      },
      payments: {
        where: { status: 'PAID' }, // المؤكدة فقط
        orderBy: { date: 'desc' },
        take: 1 // آخر دفعة فقط
      }
    }
  }
}
```

### معالجة فعالة
- حساب الديون في حلقة واحدة
- تجنب الاستعلامات المتكررة
- استخدام Promise.all للعمليات المتوازية

## 🎯 الفوائد المحققة

### 1. دقة الحسابات
- ✅ استبعاد الفواتير الملغاة
- ✅ اعتبار المدفوعات المؤكدة فقط
- ✅ حساب صحيح للديون المتبقية

### 2. تحسين الأمان
- ✅ تنظيف شامل للمدخلات
- ✅ تحقق دقيق من البيانات
- ✅ معالجة آمنة للأخطاء

### 3. تجربة مستخدم أفضل
- ✅ بيانات دقيقة وموثوقة
- ✅ رسائل خطأ واضحة
- ✅ استجابة سريعة

### 4. صيانة أسهل
- ✅ كود منظم ومقسم
- ✅ استخدام دوال مساعدة
- ✅ تسجيل شامل للعمليات

---

**تاريخ التحديث:** 2025-06-24  
**المطور:** Augment Agent  
**الإصدار:** 2.0  
**الحالة:** محسن ومختبر

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// GET: جلب المستخدمين مع الإحصائيات والمعلومات المحسّنة
export async function GET(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.users.view');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    // جلب المستخدمين مع المعلومات المحسّنة
    const users = await prisma.user.findMany({
      include: {
        profile: {
          select: {
            name: true,
            phone: true
          }
        },
        userRole: {
          select: {
            displayName: true,
            name: true
          }
        },
        teacher: {
          select: {
            id: true,
            name: true,
            phone: true,
            specialization: true
          }
        },
        employee: {
          select: {
            id: true,
            name: true,
            phone: true,
            position: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // جلب الطلاب
    const students = await prisma.student.findMany({
      include: {
        guardian: {
          select: {
            id: true,
            name: true,
            phone: true
          }
        },
        classe: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // جلب الأولياء
    const parents = await prisma.parent.findMany({
      include: {
        students: {
          select: {
            id: true,
            name: true,
            username: true
          }
        }
      }
    });

    // تحويل المستخدمين المسجلين في النظام
    const enhancedUsers = users.map(user => ({
      id: `user-${user.id}`,
      originalId: user.id,
      name: user.profile?.name || user.username,
      username: user.username,
      email: user.email,
      role: user.role,
      roleId: user.roleId,
      phone: user.profile?.phone || user.teacher?.phone || user.employee?.phone,
      status: (user as any).isActive ? 'active' as const : 'inactive' as const,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      lastLogin: null,
      profile: user.profile,
      userRole: user.userRole,
      teacher: user.teacher,
      employee: user.employee,
      userType: 'registered' as const,
      additionalInfo: user.teacher ? {
        specialization: user.teacher.specialization,
        type: 'teacher'
      } : user.employee ? {
        position: user.employee.position,
        type: 'employee'
      } : null
    }));

    // تحويل الطلاب إلى تنسيق المستخدمين
    const studentUsers = students.map(student => ({
      id: `student-${student.id}`,
      originalId: student.id,
      name: student.name,
      username: student.username,
      email: null,
      role: 'STUDENT' as const,
      roleId: null,
      phone: student.phone,
      status: 'active' as const,
      createdAt: new Date().toISOString(), // الطلاب لا يحتوون على createdAt
      updatedAt: new Date().toISOString(),
      lastLogin: null,
      profile: null,
      userRole: null,
      teacher: null,
      employee: null,
      userType: 'student' as const,
      additionalInfo: {
        age: student.age,
        guardian: student.guardian,
        classe: student.classe,
        type: 'student'
      }
    }));

    // تحويل الأولياء إلى تنسيق المستخدمين
    const parentUsers = parents.map(parent => ({
      id: `parent-${parent.id}`,
      originalId: parent.id,
      name: parent.name,
      username: `parent_${parent.id}`, // إنشاء username افتراضي
      email: parent.email,
      role: 'PARENT' as const,
      roleId: null,
      phone: parent.phone,
      status: 'active' as const,
      createdAt: parent.createdAt.toISOString(),
      updatedAt: parent.updatedAt.toISOString(),
      lastLogin: null,
      profile: null,
      userRole: null,
      teacher: null,
      employee: null,
      userType: 'parent' as const,
      additionalInfo: {
        address: parent.address,
        students: parent.students,
        type: 'parent'
      }
    }));

    // دمج جميع المستخدمين
    const allUsers = [...enhancedUsers, ...studentUsers, ...parentUsers];

    // حساب الإحصائيات الشاملة
    const stats = {
      total: allUsers.length,
      active: allUsers.filter(u => u.status === 'active').length,
      inactive: allUsers.filter(u => u.status === 'inactive').length,
      admins: allUsers.filter(u => u.role === 'ADMIN').length,
      teachers: allUsers.filter(u => u.role === 'TEACHER').length,
      students: allUsers.filter(u => u.role === 'STUDENT').length,
      employees: allUsers.filter(u => u.role === 'EMPLOYEE').length,
      parents: allUsers.filter(u => u.role === 'PARENT').length,
      registered: enhancedUsers.length,
      studentsFromTable: studentUsers.length,
      parentsFromTable: parentUsers.length
    };

    return NextResponse.json({
      users: allUsers,
      stats,
      message: "تم جلب جميع المستخدمين بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching enhanced users:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب المستخدمين" },
      { status: 500 }
    );
  }
}

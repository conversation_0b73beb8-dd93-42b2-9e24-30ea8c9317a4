import { createContext, useContext, useEffect, useState } from 'react';
import { Theme } from '@prisma/client';

interface ThemeContextType {
  currentTheme: Theme | null;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [currentTheme, setCurrentTheme] = useState<Theme | null>(null);

  useEffect(() => {
    // Load theme from localStorage on initial mount
    const savedTheme = localStorage.getItem('currentTheme');
    if (savedTheme) {
      setCurrentTheme(JSON.parse(savedTheme));
    } else {
      // Fetch default theme from API if no theme in localStorage
      fetch('/api/themes/default')
        .then(res => res.json())
        .then(theme => {
          setCurrentTheme(theme);
          localStorage.setItem('currentTheme', JSON.stringify(theme));
        })
        .catch(console.error);
    }
  }, []);

  const setTheme = (theme: Theme) => {
    setCurrentTheme(theme);
    localStorage.setItem('currentTheme', JSON.stringify(theme));

    // Update theme in database
    fetch(`/api/themes/${theme.id}/set-active`, {
      method: 'POST',
    }).catch(console.error);
  };

  useEffect(() => {
    if (currentTheme) {
      // Apply theme to document root
      const root = document.documentElement;
      root.style.setProperty('--primary-color', currentTheme.primaryColor);
      root.style.setProperty('--secondary-color', currentTheme.secondaryColor);
      root.style.setProperty('--accent-color', currentTheme.accentColor || '');
      root.style.setProperty('--background-color', currentTheme.backgroundColor);
      root.style.setProperty('--text-color', currentTheme.textColor);
      root.style.setProperty('--font-family', currentTheme.fontFamily);
    }
  }, [currentTheme]);

  return (
    <ThemeContext.Provider value={{ currentTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
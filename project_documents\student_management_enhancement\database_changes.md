# تغييرات قاعدة البيانات - نظام إدارة التلاميذ المتقدم

## 📅 تاريخ التحديث: 20/06/2025

## 🔄 التغييرات المطبقة

### 1. تحديث نموذج Student

تم إضافة الحقول التالية لنموذج `Student`:

```prisma
// حقول بداية الحفظ الجديدة
memorizationStartDate DateTime?   // تاريخ بداية الحفظ
startingJuz          Int?         // الجزء الذي بدأ فيه الحفظ (1-30)
memorizationLevel    String?      // المستوى: مبتدئ، متوسط، متقدم
memorizationNotes    String?      @db.Text // ملاحظات حول بداية الحفظ

// علاقات جديدة
memorizationStarts   StudentMemorizationStart[] // سجلات بداية الحفظ
registrationReceipts StudentRegistrationReceipt[] // وصولات التسجيل
```

**الفهارس الجديدة:**
- `@@index([memorizationStartDate])`
- `@@index([startingJuz])`

### 2. نموذج StudentMemorizationStart الجديد

```prisma
model StudentMemorizationStart {
  id              Int      @id @default(autoincrement())
  studentId       Int      // معرف التلميذ
  student         Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  startDate       DateTime // تاريخ بداية الحفظ (بصيغة DD/MM/YYYY)
  startingJuz     Int      // الجزء الذي بدأ فيه الحفظ (1-30)
  startingSurah   Int?     // السورة التي بدأ فيها (اختياري)
  startingVerse   Int?     // الآية التي بدأ فيها (اختياري)
  level           String   // المستوى: مبتدئ، متوسط، متقدم
  notes           String?  @db.Text // ملاحظات إضافية
  isActive        Boolean  @default(true) // هل هذا السجل نشط (آخر نقطة بداية)
  createdBy       String?  // معرف المستخدم الذي أدخل البيانات
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([studentId])
  @@index([startDate])
  @@index([startingJuz])
  @@index([isActive])
  @@unique([studentId, isActive]) // تلميذ واحد يمكن أن يكون له سجل نشط واحد فقط
}
```

**الميزات الرئيسية:**
- تتبع متعدد لنقاط بداية الحفظ
- ضمان وجود سجل نشط واحد فقط لكل تلميذ
- تسجيل من قام بإدخال البيانات
- دعم التواريخ بصيغة DD/MM/YYYY

### 3. نموذج StudentRegistrationReceipt الجديد

```prisma
model StudentRegistrationReceipt {
  id              Int      @id @default(autoincrement())
  studentId       Int      // معرف التلميذ
  student         Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  receiptNumber   String   @unique // رقم الوصل الفريد
  issueDate       DateTime @default(now()) // تاريخ الإصدار (بصيغة DD/MM/YYYY)
  registrationFee Float    // رسوم التسجيل
  paymentStatus   String   @default("PENDING") // حالة الدفع: PENDING, PAID, CANCELLED
  receiptData     Json?    // بيانات الوصل بصيغة JSON (للطباعة)
  pdfPath         String?  // مسار ملف PDF للوصل
  isPrinted       Boolean  @default(false) // هل تم طباعة الوصل
  printedAt       DateTime? // تاريخ الطباعة
  printedBy       String?  // معرف المستخدم الذي طبع الوصل
  notes           String?  @db.Text // ملاحظات إضافية
  createdBy       String?  // معرف المستخدم الذي أنشأ الوصل
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([studentId])
  @@index([receiptNumber])
  @@index([issueDate])
  @@index([paymentStatus])
  @@index([isPrinted])
}
```

**الميزات الرئيسية:**
- رقم وصل فريد لكل تسجيل
- تتبع حالة الدفع والطباعة
- حفظ بيانات الوصل بصيغة JSON للطباعة
- تسجيل من قام بالطباعة ومتى

## 🔍 تفاصيل التصميم

### التواريخ بصيغة الأرقام الفرنسية
جميع التواريخ في النظام تستخدم صيغة DD/MM/YYYY كما طلب المستخدم:
- `startDate` في `StudentMemorizationStart`
- `issueDate` في `StudentRegistrationReceipt`
- `memorizationStartDate` في `Student`

### المستويات المدعومة
المستويات المتاحة لبداية الحفظ:
- **مبتدئ**: للتلاميذ الذين يبدؤون حفظ القرآن لأول مرة
- **متوسط**: للتلاميذ الذين لديهم خبرة سابقة
- **متقدم**: للتلاميذ المتميزين في الحفظ

### الأمان والتتبع
- تسجيل من قام بإدخال/تعديل البيانات (`createdBy`)
- تسجيل تواريخ الإنشاء والتحديث
- حماية من الحذف العرضي باستخدام `onDelete: Cascade`

## 📊 الفهارس المضافة

### نموذج Student
- `memorizationStartDate`: لتسريع البحث بتاريخ بداية الحفظ
- `startingJuz`: لتسريع البحث بالجزء المبدئي

### نموذج StudentMemorizationStart
- `studentId`: للبحث السريع بالتلميذ
- `startDate`: للبحث بتاريخ البداية
- `startingJuz`: للبحث بالجزء
- `isActive`: للبحث عن السجلات النشطة

### نموذج StudentRegistrationReceipt
- `studentId`: للبحث السريع بالتلميذ
- `receiptNumber`: للبحث برقم الوصل
- `issueDate`: للبحث بتاريخ الإصدار
- `paymentStatus`: للبحث بحالة الدفع
- `isPrinted`: للبحث عن الوصولات المطبوعة

## 🔄 الخطوات التالية

1. **تطبيق Migration**: تشغيل `npx prisma migrate dev` لتطبيق التغييرات
2. **تحديث Prisma Client**: تشغيل `npx prisma generate` لتحديث العميل
3. **إنشاء APIs**: تطوير واجهات برمجة التطبيقات للنماذج الجديدة
4. **تطوير واجهات المستخدم**: إنشاء النماذج والصفحات المطلوبة

## ⚠️ ملاحظات مهمة

- يجب عمل نسخة احتياطية من قاعدة البيانات قبل تطبيق التغييرات
- التأكد من تحديث جميع الاستعلامات الموجودة للتوافق مع النماذج الجديدة
- اختبار العلاقات الجديدة بعناية
- التأكد من عمل الفهارس بشكل صحيح

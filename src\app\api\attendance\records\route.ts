import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/attendance/records
export async function GET() {
  try {
    // استخدام جدول Attendance الموجود بالفعل
    const attendanceRecords = await prisma.attendance.findMany({
      take: 1000, // نأخذ أحدث 1000 سجل لتجنب استرجاع كميات كبيرة من البيانات
      include: {
        student: {
          select: {
            id: true,
            name: true,
            classeId: true,
            classe: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        images: {
          select: {
            id: true,
            imageUrl: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // تنسيق البيانات للاستخدام في واجهة المستخدم
    const formattedRecords = attendanceRecords.map(record => {
      // التأكد من وجود بيانات الطالب
      if (!record.student) {
        return null;
      }

      return {
        id: record.id,
        studentId: record.studentId,
        studentName: record.student.name,
        classeId: record.student.classeId,
        className: record.student.classe?.name || '-',
        date: record.date,
        status: record.status,
        hisass: record.hisass,
        images: record.images.map(img => img.imageUrl)
      };
    }).filter(record => record !== null); // إزالة السجلات الفارغة

    return NextResponse.json(formattedRecords);
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب سجلات الحضور' },
      { status: 500 }
    );
  }
}

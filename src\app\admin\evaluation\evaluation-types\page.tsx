'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-toastify';
import { Loader2, Plus, Pencil, Trash } from 'lucide-react';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

type EvaluationType = {
  id: string;
  name: string;
  description: string;
};

export default function EvaluationTypesPage() {
  const [evaluationTypes, setEvaluationTypes] = useState<EvaluationType[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedEvaluationType, setSelectedEvaluationType] = useState<EvaluationType | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchEvaluationTypes();
  }, []);

  const fetchEvaluationTypes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/evaluation/types');
      if (!response.ok) throw new Error('Failed to fetch evaluation types');
      const data = await response.json();
      setEvaluationTypes(data || []);
    } catch (error) {
      console.error('Error fetching evaluation types:', error);
      toast.error('حدث خطأ أثناء جلب أنواع التقييم');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEvaluationType = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || !formData.description) {
        toast.error('يرجى إدخال اسم ووصف نوع التقييم');
        return;
      }

      const response = await fetch('/api/evaluation/types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add evaluation type');
      }

      toast.success('تم إضافة نوع التقييم بنجاح');
      setIsAddDialogOpen(false);
      setFormData({ name: '', description: '' });
      fetchEvaluationTypes();
    } catch (error) {
      console.error('Error adding evaluation type:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء إضافة نوع التقييم');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditEvaluationType = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);

      if (!formData.name || !formData.description) {
        toast.error('يرجى إدخال اسم ووصف نوع التقييم');
        return;
      }

      if (!selectedEvaluationType) return;

      const response = await fetch('/api/evaluation/types', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: selectedEvaluationType.id,
          ...formData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update evaluation type');
      }

      toast.success('تم تحديث نوع التقييم بنجاح');
      setIsEditDialogOpen(false);
      setSelectedEvaluationType(null);
      fetchEvaluationTypes();
    } catch (error) {
      console.error('Error updating evaluation type:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تحديث نوع التقييم');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteEvaluationType = async () => {
    try {
      setIsSubmitting(true);

      if (!selectedEvaluationType) return;

      const response = await fetch(`/api/evaluation/types?id=${selectedEvaluationType.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete evaluation type');
      }

      toast.success('تم حذف نوع التقييم بنجاح');
      setIsDeleteDialogOpen(false);
      setSelectedEvaluationType(null);
      fetchEvaluationTypes();
    } catch (error) {
      console.error('Error deleting evaluation type:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حذف نوع التقييم');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditDialog = (evaluationType: EvaluationType) => {
    setSelectedEvaluationType(evaluationType);
    setFormData({
      name: evaluationType.name,
      description: evaluationType.description,
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (evaluationType: EvaluationType) => {
    setSelectedEvaluationType(evaluationType);
    setIsDeleteDialogOpen(true);
  };

  return (
    <ProtectedRoute requiredPermission="admin.evaluation.evaluation-types.view">
      <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة أنواع التقييم</h1>
        <Button
          onClick={() => {
            setFormData({ name: '', description: '' });
            setIsAddDialogOpen(true);
          }}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
        >
          <Plus className="ml-2" size={16} />
          إضافة نوع تقييم جديد
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      ) : evaluationTypes.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-gray-500">لا توجد أنواع تقييم حتى الآن</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-right">المعرف</TableHead>
                <TableHead className="text-right">اسم نوع التقييم</TableHead>
                <TableHead className="text-right">الوصف</TableHead>
                <TableHead className="text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {evaluationTypes.map((evaluationType) => (
                <TableRow key={evaluationType.id}>
                  <TableCell>{evaluationType.id}</TableCell>
                  <TableCell>{evaluationType.name}</TableCell>
                  <TableCell>{evaluationType.description}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openEditDialog(evaluationType)}
                      >
                        <Pencil className="h-4 w-4 ml-1" />
                        تعديل
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => openDeleteDialog(evaluationType)}
                      >
                        <Trash className="h-4 w-4 ml-1" />
                        حذف
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={(open) => !open && setIsAddDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>إضافة نوع تقييم جديد</DialogTitle>
            <DialogDescription>أدخل تفاصيل نوع التقييم الجديد</DialogDescription>
          </DialogHeader>
          <form id="addEvaluationTypeForm" onSubmit={handleAddEvaluationType} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم نوع التقييم</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => !open && setIsEditDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تعديل نوع التقييم</DialogTitle>
            <DialogDescription>قم بتعديل تفاصيل نوع التقييم</DialogDescription>
          </DialogHeader>
          <form id="editEvaluationTypeForm" onSubmit={handleEditEvaluationType} className="space-y-4">
            <div className="space-y-2">
              <label className="text-right block">اسم نوع التقييم</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-right block">الوصف</label>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                dir="rtl"
                className="text-right"
                required
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={(open) => !open && setIsDeleteDialogOpen(false)}>
        <DialogContent className="max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>حذف نوع التقييم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف نوع التقييم {selectedEvaluationType ? selectedEvaluationType.name : ''}؟
              <br />
              هذا الإجراء لا يمكن التراجع عنه.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isSubmitting}
            >
              إلغاء
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteEvaluationType}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'جاري الحذف...' : 'حذف'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </ProtectedRoute>
  );
}

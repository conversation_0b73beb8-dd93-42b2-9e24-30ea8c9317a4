"use client";

import { Fragment, useRef, ReactNode } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { FaTimes } from "react-icons/fa";

interface ModalProps {
  isOpen: boolean;
  onCloseAction: () => void;
  title?: string;
  children: ReactNode;
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl";
  showCloseButton?: boolean;
  headerClassName?: string;
  contentClassName?: string;
}

export default function Modal({
  isOpen,
  onCloseAction,
  title,
  children,
  maxWidth = "md",
  showCloseButton = true,
  headerClassName = "",
  contentClassName = "",
}: ModalProps) {
  // استخدام initialFocus لتحديد العنصر الذي سيتم التركيز عليه عند فتح النافذة
  const initialFocusRef = useRef(null);

  const maxWidthClasses = {
    sm: "sm:max-w-sm",
    md: "sm:max-w-md",
    lg: "sm:max-w-lg",
    xl: "sm:max-w-xl",
    "2xl": "sm:max-w-2xl",
    "3xl": "sm:max-w-3xl",
    "4xl": "sm:max-w-4xl",
  }[maxWidth] || "sm:max-w-md";

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="fixed inset-0 z-50 overflow-y-auto"
        onClose={onCloseAction}
        initialFocus={initialFocusRef}
        dir="rtl"
      >
        <div className="min-h-screen px-4 text-center">
          {/* خلفية النافذة المنبثقة */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm" />
          </Transition.Child>

          {/* عنصر لتوسيط النافذة */}
          <span
            className="inline-block h-screen align-middle"
            aria-hidden="true"
          >
            &#8203;
          </span>

          {/* محتوى النافذة المنبثقة */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 scale-95 translate-y-4"
            enterTo="opacity-100 scale-100 translate-y-0"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 scale-100 translate-y-0"
            leaveTo="opacity-0 scale-95 translate-y-4"
          >
            <div
              ref={initialFocusRef}
              className={`inline-block w-full ${maxWidthClasses} my-8 overflow-hidden text-right align-middle transition-all transform bg-white shadow-2xl rounded-2xl max-h-[90vh] ${contentClassName}`}
            >
              {/* رأس النافذة */}
              {title && (
                <div className={`flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-[var(--primary-color)] to-[var(--secondary-color)] text-white rounded-t-2xl ${headerClassName}`}>
                  <h3 className="text-xl font-bold leading-6">
                    {title}
                  </h3>
                  {showCloseButton && (
                    <button
                      onClick={onCloseAction}
                      className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                      aria-label="إغلاق النافذة"
                    >
                      <FaTimes className="w-5 h-5" />
                    </button>
                  )}
                </div>
              )}

              {/* محتوى النافذة */}
              <div className={`p-6 overflow-y-auto ${title ? 'max-h-[calc(90vh-120px)]' : 'max-h-[calc(90vh-60px)]'} custom-scrollbar`}>
                {children}
              </div>
            </div>
          </Transition.Child>
        </div>
      </Dialog>

      <style jsx>{`
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: var(--primary-color) #f1f5f9;
        }

        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: var(--primary-color);
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: var(--secondary-color);
        }
      `}</style>
    </Transition>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/evaluation-criteria
export async function GET() {
  try {
    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    const criteria = await prisma.evaluationCriteria.findMany({
      orderBy: {
        weight: 'desc'
      }
    });

    return NextResponse.json({
      data: criteria,
      success: true,
      message: 'تم جلب معايير التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error fetching evaluation criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب معايير التقييم',
      success: false
    }, { status: 500 });
  }
}

// POST /api/evaluation-criteria
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, weight, description } = body;

    // التحقق من البيانات المطلوبة
    if (!name || weight === undefined) {
      return NextResponse.json({
        error: 'الاسم والوزن مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة الوزن
    const numericWeight = parseFloat(weight);
    if (isNaN(numericWeight) || numericWeight < 0 || numericWeight > 1) {
      return NextResponse.json({
        error: 'يجب أن يكون الوزن رقمًا بين 0 و 1',
        success: false
      }, { status: 400 });
    }

    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    const criteria = await prisma.evaluationCriteria.create({
      data: {
        name,
        weight: numericWeight,
        description
      }
    });

    return NextResponse.json({
      data: criteria,
      success: true,
      message: 'تم إنشاء معيار التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error creating evaluation criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إنشاء معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

// PUT /api/evaluation-criteria
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, weight, description } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !name || weight === undefined) {
      return NextResponse.json({
        error: 'المعرف والاسم والوزن مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من صحة الوزن
    const numericWeight = parseFloat(weight);
    if (isNaN(numericWeight) || numericWeight < 0 || numericWeight > 1) {
      return NextResponse.json({
        error: 'يجب أن يكون الوزن رقمًا بين 0 و 1',
        success: false
      }, { status: 400 });
    }

    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    const criteria = await prisma.evaluationCriteria.update({
      where: { id: parseInt(id) },
      data: {
        name,
        weight: numericWeight,
        description
      }
    });

    return NextResponse.json({
      data: criteria,
      success: true,
      message: 'تم تحديث معيار التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error updating evaluation criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء تحديث معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/evaluation-criteria
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف معيار التقييم مطلوب',
        success: false
      }, { status: 400 });
    }

    // تأكد من وجود اتصال بقاعدة البيانات
    if (!prisma) {
      throw new Error("Database connection not established");
    }

    await prisma.evaluationCriteria.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف معيار التقييم بنجاح'
    });
  } catch (error) {
    console.error('Error deleting evaluation criteria:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف معيار التقييم',
      success: false
    }, { status: 500 });
  }
}

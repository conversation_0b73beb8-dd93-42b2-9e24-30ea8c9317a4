'use client';
import React, { useMemo } from 'react';
import { usePermissions } from '@/contexts/PermissionsContext';

interface BulkPermissionItem {
  key: string;
  permission: string;
  component: React.ReactNode;
  fallback?: React.ReactNode;
}

interface BulkPermissionGuardProps {
  items: BulkPermissionItem[];
  className?: string;
  wrapperComponent?: 'div' | 'span' | 'section';
  wrapperProps?: React.HTMLAttributes<HTMLElement>;
}

/**
 * مكون لعرض عدة عناصر محمية بصلاحيات مختلفة
 * يتحقق من جميع الصلاحيات مرة واحدة لتحسين الأداء
 */
const BulkPermissionGuard: React.FC<BulkPermissionGuardProps> = ({
  items,
  className = '',
  wrapperComponent = 'div',
  wrapperProps = {}
}) => {
  const { hasAnyPermission, userRole, isReady } = usePermissions();

  // استخراج جميع الصلاحيات المطلوبة
  const allPermissions = useMemo(() => {
    return items.map(item => item.permission);
  }, [items]);

  // التحقق من الصلاحيات مرة واحدة
  const permissionResults = useMemo(() => {
    if (!isReady) return {};

    if (userRole === 'ADMIN') {
      // المدير لديه جميع الصلاحيات
      return items.reduce((acc, item) => {
        acc[item.key] = true;
        return acc;
      }, {} as Record<string, boolean>);
    }

    // للمستخدمين العاديين، التحقق من كل صلاحية
    return items.reduce((acc, item) => {
      acc[item.key] = hasAnyPermission([item.permission]);
      return acc;
    }, {} as Record<string, boolean>);
  }, [items, userRole, isReady, hasAnyPermission]);

  // تصفية العناصر المسموح بعرضها
  const allowedItems = useMemo(() => {
    return items.filter(item => permissionResults[item.key]);
  }, [items, permissionResults]);

  // إذا لم يكن هناك عناصر مسموح بعرضها
  if (!isReady || allowedItems.length === 0) {
    // عرض fallback للعناصر غير المسموح بها
    const fallbackItems = items
      .filter(item => !permissionResults[item.key] && item.fallback)
      .map(item => item.fallback);
    
    if (fallbackItems.length === 0) {
      return null;
    }

    const WrapperComponent = wrapperComponent as any;
    return (
      <WrapperComponent className={className} {...wrapperProps}>
        {fallbackItems}
      </WrapperComponent>
    );
  }

  const WrapperComponent = wrapperComponent as any;
  return (
    <WrapperComponent className={className} {...wrapperProps}>
      {allowedItems.map(item => (
        <React.Fragment key={item.key}>
          {item.component}
        </React.Fragment>
      ))}
    </WrapperComponent>
  );
};

export default BulkPermissionGuard;

// Hook مساعد لاستخدام BulkPermissionGuard بسهولة
export const useBulkPermissions = (permissions: string[]) => {
  const { hasAnyPermission, userRole, isReady } = usePermissions();

  return useMemo(() => {
    if (!isReady) {
      return permissions.reduce((acc, permission) => {
        acc[permission] = false;
        return acc;
      }, {} as Record<string, boolean>);
    }

    if (userRole === 'ADMIN') {
      return permissions.reduce((acc, permission) => {
        acc[permission] = true;
        return acc;
      }, {} as Record<string, boolean>);
    }

    return permissions.reduce((acc, permission) => {
      acc[permission] = hasAnyPermission([permission]);
      return acc;
    }, {} as Record<string, boolean>);
  }, [permissions, userRole, isReady, hasAnyPermission]);
};

// مكون مساعد لإنشاء قائمة أزرار محسنة
interface QuickActionButtonsProps {
  entityType: string;
  actions: Array<{
    key: string;
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'danger' | 'success';
    permission?: string; // إذا لم يتم تحديدها، ستكون admin.{entityType}.{key}
  }>;
  className?: string;
}

export const QuickActionButtons: React.FC<QuickActionButtonsProps> = ({
  entityType,
  actions,
  className = ''
}) => {
  const permissions = useMemo(() => {
    return actions.map(action => action.permission || `admin.${entityType}.${action.key}`);
  }, [actions, entityType]);

  const permissionResults = useBulkPermissions(permissions);

  const getVariantStyles = (variant: string = 'primary') => {
    switch (variant) {
      case 'primary':
        return 'bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white';
      case 'secondary':
        return 'bg-gray-500 hover:bg-gray-600 text-white';
      case 'danger':
        return 'bg-red-500 hover:bg-red-600 text-white';
      case 'success':
        return 'bg-green-500 hover:bg-green-600 text-white';
      default:
        return 'bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white';
    }
  };

  const allowedActions = actions.filter((action, index) => {
    const permission = action.permission || `admin.${entityType}.${action.key}`;
    return permissionResults[permission];
  });

  if (allowedActions.length === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {allowedActions.map((action) => (
        <button
          key={action.key}
          onClick={action.onClick}
          className={`
            inline-flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium
            transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2
            ${getVariantStyles(action.variant)}
          `}
        >
          {action.icon}
          {action.label}
        </button>
      ))}
    </div>
  );
};

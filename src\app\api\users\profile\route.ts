import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import { verifyToken } from '@/utils/verifyToken';

const prisma = new PrismaClient();

interface UpdateProfileDto {
    user: string;
    articleID?: number;
}

// Get user profile
export async function GET(request: NextRequest) {
    try {
        console.log("11111")
        const userId = await verifyToken(request);
        if (!userId) {
            return NextResponse.json(
                { message: "Unauthorized" },
                { status: 401 }
            );
        }
        console.log("222222")
        const user = await prisma.user.findUnique({
            where: { id: userId.id },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
                createdAt: true,
                profile: {
                    select: {
                        name: true,
                        phone: true
                    }
                },
                teacher: {
                    select: {
                        name: true,
                        specialization: true
                    }
                },
                employee: {
                    select: {
                        name: true,
                        position: true
                    }
                }
            }
        });
        console.log("33333")
        if (!user) {
            return NextResponse.json(
                { message: "User not found" },
                { status: 404 }
            );
        }

        return NextResponse.json(user);
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { message: "Internal server error" },
            { status: 500 }
        );
    }
}

// Update user profile
export async function PUT(request: NextRequest) {
    try {
        const userId = await verifyToken(request);
        if (!userId) {
            return NextResponse.json(
                { message: "Unauthorized" },
                { status: 401 }
            );
        }

        const body = await request.json() as UpdateProfileDto;

        // Check if username is already taken
        if (body.user) {
            const existingUser = await prisma.user.findUnique({
                where: { username: body.user }
            });

            if (existingUser && existingUser.id !== Number(userId)) {
                return NextResponse.json(
                    { message: "Username already taken" },
                    { status: 400 }
                );
            }
        }

        const updatedUser = await prisma.user.update({
            where: { id: Number(userId) },
            data: {
                username: body.user
            },
            select: {
                id: true,
                username: true,
                role: true
            }
        });

        return NextResponse.json(updatedUser);
    } catch (error) {
        console.error(error);
        return NextResponse.json(
            { message: "Internal server error" },
            { status: 500 }
        );
    }
}
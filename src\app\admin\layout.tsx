"use client"
import { ReactNode, useState, useEffect } from 'react'
import Link from 'next/link'
import {FaBookOpen, FaImages, FaGraduationCap, FaFileInvoiceDollar, FaChartBar, FaPercent, FaCreditCard, FaChevronDown, FaWallet, FaTags, FaChartPie, FaChartLine, FaCog, FaReceipt, FaUserFriends, FaFileInvoice} from 'react-icons/fa'
import NotificationBadge from '@/components/NotificationBadge'
import DynamicSidebar from '@/components/admin/DynamicSidebar'
import ProtectedSidebarItem from '@/components/admin/ProtectedSidebarItem'
import { useUserPermissions } from '@/hooks/useUserPermissions'

interface AdminLayoutProps {
  children: ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [isExamMenuOpen, setIsExamMenuOpen] = useState(false)
  const [isPaymentMenuOpen, setIsPaymentMenuOpen] = useState(false)
  const [isTreasuryMenuOpen, setIsTreasuryMenuOpen] = useState(false)
  const { userPermissions, userRole, loading } = useUserPermissions()

  // فتح السايدبار افتراضياً في الشاشات الكبيرة
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsSidebarOpen(true)
      } else {
        setIsSidebarOpen(false)
      }
    }

    // تشغيل عند التحميل
    handleResize()

    // إضافة مستمع لتغيير حجم الشاشة
    window.addEventListener('resize', handleResize)

    // تنظيف المستمع
    return () => window.removeEventListener('resize', handleResize)
  }, [])
const logout = async () => {
  try {
    const response = await fetch('/api/users/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      // تم تسجيل الخروج بنجاح
      window.location.href = '/login';
    } else {
      console.error('فشل تسجيل الخروج');
      alert('فشل تسجيل الخروج، يرجى المحاولة مرة أخرى');
    }
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error);
    window.location.href = '/';
  }
}
  return (
    <div className="min-h-screen bg-gray-100 flex relative">
      {/* Overlay for mobile when sidebar is open */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}
      {/* Menu Button - Always visible on mobile, visible on desktop */}
      <div className="fixed top-4 right-4 z-30 lg:block">
        <button
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
          className="p-3 rounded-lg bg-[var(--primary-color)] text-white shadow-lg hover:bg-[var(--secondary-color)] transition-colors duration-200"
          aria-label="Toggle menu"
        >
          <svg
            className="h-6 w-6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            {isSidebarOpen ? (
              <path d="M6 18L18 6M6 6l12 12" />
            ) : (
              <path d="M4 6h16M4 12h16M4 18h16" />
            )}
          </svg>
        </button>
      </div>

      {/* Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Conditional Sidebar - Static for Admin, Dynamic for Employees */}
      {loading ? (
        <div className="w-64 bg-gray-200 animate-pulse"></div>
      ) : userRole === 'ADMIN' ? (
        // Static Sidebar for Admin (userPermissions === null means admin)
        <aside
          className={`fixed top-[100px] bottom-[80px] right-0 w-72 md:w-64 text-white shadow-lg transform transition-all duration-300 ease-in-out z-20 overflow-y-auto
            ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full'}`}
          style={{ backgroundColor: 'var(--sidebar-color, var(--primary-color))' }}
        >
          <div className="p-4 border-b" style={{ borderColor: 'var(--secondary-color, var(--secondary-color))' }}>
            <h2 className="text-xl font-semibold text-white">لوحة الإدارة</h2>
          </div>
          <nav className="p-4">
            <ul className="space-y-1">
            <li>
              <Link
                href="/admin"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                <span>الرئيسية</span>
              </Link>
            </li>
            <ProtectedSidebarItem requiredPermission="admin.users.view">
              <li>
                <Link
                    href="/admin/users"
                    className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                    onClick={() => setIsSidebarOpen(false)}
                  >
                    <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <span>إدارة المستخدمين</span>
                  </Link>
                </li>
            </ProtectedSidebarItem>
            <ProtectedSidebarItem requiredPermission="admin.roles.view">
              <li>
                <Link
                    href="/admin/roles-permissions"
                    className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                    onClick={() => setIsSidebarOpen(false)}
                  >
                    <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                    </svg>
                    <span>الأدوار والصلاحيات</span>
                  </Link>
                </li>
            </ProtectedSidebarItem>
            <li>
              <Link
                href="/admin/students"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                <span>التلاميذ</span>
              </Link>
            </li>
            <li>
              <Link
                href="/admin/parents"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <span>الاولياء</span>
              </Link>
            </li>
            <li>
            <Link
                href="/admin/teachers"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <span>المعلمون</span>
              </Link>
            </li>
            <li>
            <Link
                href="/admin/subjects"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <span>المواد</span>
              </Link>
            </li>
            <li>
            <Link
                href="/admin/levels"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                </svg>
                <span>المستويات التعليمية</span>
              </Link>
            </li>
            <li>
            <Link
                href="/admin/classes"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <span>الأقسام</span>
              </Link>
            </li>

            <li>
            <Link
                href="/admin/attendance"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
                <span>الحضور والغياب</span>
              </Link>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setIsExamMenuOpen(!isExamMenuOpen)}
                className="w-full flex items-center justify-between px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
              >
                <div className="flex items-center">
                  <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <span>إدارة الامتحانات والتقييم</span>
                </div>
                <FaChevronDown className={`h-4 w-4 transition-transform duration-200 ${isExamMenuOpen ? 'rotate-180' : ''}`} />
              </button>
              {isExamMenuOpen && (
                <ul className="mr-6 mt-2 space-y-1 border-r-2 border-[var(--secondary-color)] pr-4">
                  <li>
                    <Link
                      href="/admin/evaluation/dashboard"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <span>لوحة التحكم</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/evaluation/exams"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <span>الامتحانات</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/evaluation/question-banks"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <span>بنوك الأسئلة</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/evaluation/results"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <span>نتائج الامتحانات</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/evaluation/criteria"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <span>معايير التقييم</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/evaluation/exam-types"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <span>أنواع الامتحانات</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/class-subjects"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <span>علاقات الأقسام بالمواد</span>
                    </Link>
                  </li>
                </ul>
              )}
            </li>

            <li className="mb-2">
              <button
                onClick={() => setIsPaymentMenuOpen(!isPaymentMenuOpen)}
                className="w-full flex items-center justify-between px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
              >
                <div className="flex items-center">
                  <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span>المدفوعات</span>
                </div>
                <FaChevronDown className={`h-4 w-4 transition-transform duration-200 ${isPaymentMenuOpen ? 'rotate-180' : ''}`} />
              </button>
              {isPaymentMenuOpen && (
                <ul className="mr-6 mt-2 space-y-1 border-r-2 border-[var(--secondary-color)] pr-4">
                  <li>
                    <Link
                      href="/admin/payments"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaReceipt className="h-4 w-4 ml-2 text-white" />
                      <span>إدارة المدفوعات</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/payments/by-parent"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaUserFriends className="h-4 w-4 ml-2 text-white" />
                      <span>المدفوعات حسب الولي</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/invoices"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaFileInvoiceDollar className="h-4 w-4 ml-2 text-white" />
                      <span>الفواتير الإلكترونية</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/invoices/family"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaFileInvoice className="h-4 w-4 ml-2 text-white" />
                      <span>الفواتير الجماعية</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/payment-methods"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaCreditCard className="h-4 w-4 ml-2 text-white" />
                      <span>طرق الدفع</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/discounts"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaPercent className="h-4 w-4 ml-2 text-white" />
                      <span>الخصومات</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/reports/financial"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaChartBar className="h-4 w-4 ml-2 text-white" />
                      <span>التقارير المالية</span>
                    </Link>
                  </li>
                </ul>
              )}
            </li>
            <li>
            <Link
                href="/admin/donations"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span>التبرعات</span>
              </Link>
            </li>
            <li className="mb-2">
              <button
                onClick={() => setIsTreasuryMenuOpen(!isTreasuryMenuOpen)}
                className="w-full flex items-center justify-between px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
              >
                <div className="flex items-center">
                  <FaWallet className="h-6 w-6 ml-3 text-white" />
                  <span>الخزينة والمالية</span>
                </div>
                <FaChevronDown className={`h-4 w-4 transition-transform duration-200 ${isTreasuryMenuOpen ? 'rotate-180' : ''}`} />
              </button>
              {isTreasuryMenuOpen && (
                <ul className="mr-6 mt-2 space-y-1 border-r-2 border-[var(--secondary-color)] pr-4">
                  <li>
                    <Link
                      href="/admin/treasury"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaWallet className="h-4 w-4 ml-2 text-white" />
                      <span>الخزينة</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/expense-categories"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaTags className="h-4 w-4 ml-2 text-white" />
                      <span>فئات المصروفات</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/expenses"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaTags className="h-4 w-4 ml-2 text-white" />
                      <span>ادارة المصروفات</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/budgets"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaChartPie className="h-4 w-4 ml-2 text-white" />
                      <span>الميزانية</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/financial-reports"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaChartBar className="h-4 w-4 ml-2 text-white" />
                      <span>التقارير المالية</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/admin/financial-forecasts"
                      className="flex items-center px-4 py-2 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-sm"
                      onClick={() => setIsSidebarOpen(false)}
                    >
                      <FaChartLine className="h-4 w-4 ml-2 text-white" />
                      <span>التنبؤات المالية</span>
                    </Link>
                  </li>
                </ul>
              )}
            </li>
            <li>
            <Link
                href="/remote-classes"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <span>الفصول الافتراضية</span>
              </Link>
            </li>
            <li>
            <Link
                href="/admin/khatm-sessions"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaBookOpen className="h-6 w-6 ml-3 text-white" />
                <span>مجالس الختم</span>
              </Link>
            </li>

            <li>
            <Link
                href="/admin/supervisor-reports"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaChartBar className="h-6 w-6 ml-3 text-white" />
                <span>التقارير الأدبية والمالية</span>
              </Link>
            </li>

            <li>
            <Link
                href="/admin/student-images"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaImages className="h-6 w-6 ml-3 text-white" />
                <span>صور الطلاب</span>
              </Link>
            </li>
            <li>
            <Link
                href="/admin/programs"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaGraduationCap className="h-6 w-6 ml-3 text-white" />
                <span>البرامج التعليمية</span>
              </Link>
            </li>

            <li>
            <Link
                href="/admin/honor-board"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
                <span>لوحة الشرف</span>
              </Link>
            </li>
            <li>
            <Link
                href="/notifications"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <svg className="h-6 w-6 ml-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <span>الإشعارات</span>
              </Link>
            </li>

            <li>
            <Link
                href="/admin/admin-setup"
                className="flex items-center px-4 py-3 text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] rounded-lg transition-colors duration-200 text-base font-medium"
                onClick={() => setIsSidebarOpen(false)}
              >
                <FaCog className="h-6 w-6 ml-3 text-white" />
                <span>إعدادات الموقع</span>
              </Link>
            </li>

          </ul>
        </nav>
      </aside>
      ) : (
        // Dynamic Sidebar for Employees
        <DynamicSidebar
          isSidebarOpen={isSidebarOpen}
          setIsSidebarOpen={setIsSidebarOpen}
          userPermissions={userPermissions}
          userRole={userRole}
        />
      )}

      {/* Main Content */}
      <main className={`flex-1 min-w-0 overflow-auto p-4 lg:p-8 transition-all duration-300 ${isSidebarOpen ? 'mr-0 lg:mr-64' : 'mr-0'}`}>
      <header className="bg-[var(--secondary-color)] lg:bg-white shadow rounded-lg mb-6 p-4">
         <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
    <div className="flex items-center space-x-4 space-x-reverse">
      {/* Sidebar Toggle Button for Desktop */}
      <button
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        className="hidden lg:flex p-2 rounded-lg bg-[var(--primary-color)] text-white hover:bg-[var(--secondary-color)] hover:text-[var(--secondary-text-color)] transition-colors duration-200"
        aria-label="Toggle sidebar"
      >
        <svg
          className="h-5 w-5"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          {isSidebarOpen ? (
            <path d="M6 18L18 6M6 6l12 12" />
          ) : (
            <path d="M4 6h16M4 12h16M4 18h16" />
          )}
        </svg>
      </button>
      <h1 className="text-3xl font-bold text-gray-800">لوحة التحكم</h1>
    </div>
    <div className="flex items-center space-x-4 space-x-reverse">
      <NotificationBadge />
      <span className="text-gray-600 text-lg">مرحباً بك</span>
      <button onClick={logout} className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-base">
        تسجيل الخروج
      </button>
    </div>
  </div>
</header>

        {children}
      </main>
    </div>
  )
}
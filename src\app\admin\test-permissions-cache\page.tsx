'use client';

import { useState, useEffect } from 'react';
import { useUserPermissions } from '@/hooks/useUserPermissions';
import { usePermissionsCacheManager } from '@/components/PermissionsCacheManager';
import { useLogout } from '@/hooks/useLogout';
import { getCacheStatus, getCacheTimeRemaining } from '@/lib/permissionsCache';
import LogoutButtonEnhanced from '@/components/LogoutButtonEnhanced';

/**
 * صفحة اختبار نظام التخزين المؤقت للصلاحيات
 */
export default function TestPermissionsCachePage() {
  const {
    userPermissions,
    userRole,
    userId,
    loading,
    error,
    isUsingCache,
    cacheTimeRemaining,
    refreshPermissions,
    clearCache,
    getCacheStatus: getHookCacheStatus
  } = useUserPermissions();

  const cacheManager = usePermissionsCacheManager(userId);
  const { logout } = useLogout();
  const [cacheStatus, setCacheStatus] = useState<any>(null);
  const [timeRemaining, setTimeRemaining] = useState<number>(0);

  // تحديث حالة التخزين المؤقت
  useEffect(() => {
    const updateStatus = () => {
      const status = getCacheStatus();
      setCacheStatus(status);
      
      if (userId) {
        const remaining = getCacheTimeRemaining(userId);
        setTimeRemaining(remaining);
      }
    };

    updateStatus();
    const interval = setInterval(updateStatus, 5000); // تحديث كل 5 ثوان

    return () => clearInterval(interval);
  }, [userId]);

  const formatTime = (milliseconds: number): string => {
    if (milliseconds <= 0) return 'منتهي الصلاحية';
    
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleRefreshPermissions = async () => {
    try {
      await refreshPermissions();
      alert('تم تحديث الصلاحيات بنجاح');
    } catch (error) {
      alert('فشل في تحديث الصلاحيات');
    }
  };

  const handleClearCache = () => {
    clearCache();
    cacheManager.clearCache();
    alert('تم مسح التخزين المؤقت');
  };

  const handleCleanupCache = () => {
    cacheManager.cleanup();
    alert('تم تنظيف البيانات المنتهية الصلاحية');
  };

  const handleTestLogout = async () => {
    if (confirm('هل تريد اختبار تسجيل الخروج؟ سيتم تنظيف التخزين المؤقت وإعادة توجيهك لصفحة تسجيل الدخول.')) {
      await logout(userId || undefined, {
        redirectTo: '/login',
        showSuccessMessage: true,
        clearAllCache: true
      });
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الاختبار...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">
          اختبار نظام التخزين المؤقت للصلاحيات
        </h1>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong>خطأ:</strong> {error}
          </div>
        )}

        {/* معلومات المستخدم */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">معلومات المستخدم</h3>
            <div className="space-y-2 text-sm">
              <p><strong>معرف المستخدم:</strong> {userId || 'غير محدد'}</p>
              <p><strong>الدور:</strong> {userRole || 'غير محدد'}</p>
              <p><strong>عدد الصلاحيات:</strong> {userPermissions.length}</p>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-green-800 mb-3">حالة التخزين المؤقت</h3>
            <div className="space-y-2 text-sm">
              <p>
                <strong>الحالة:</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                  isUsingCache ? 'bg-green-200 text-green-800' : 'bg-yellow-200 text-yellow-800'
                }`}>
                  {isUsingCache ? 'يستخدم التخزين المؤقت' : 'يستخدم الخادم'}
                </span>
              </p>
              <p><strong>الوقت المتبقي:</strong> {formatTime(cacheTimeRemaining)}</p>
              <p><strong>متاح:</strong> {cacheStatus?.isAvailable ? 'نعم' : 'لا'}</p>
              <p><strong>عدد العناصر المخزنة:</strong> {cacheStatus?.totalItems || 0}</p>
            </div>
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={handleRefreshPermissions}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            تحديث الصلاحيات
          </button>
          
          <button
            onClick={handleClearCache}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            مسح التخزين المؤقت
          </button>
          
          <button
            onClick={handleCleanupCache}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            تنظيف البيانات المنتهية
          </button>

          <button
            onClick={handleTestLogout}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            اختبار تسجيل الخروج
          </button>

          <LogoutButtonEnhanced
            variant="button"
            size="md"
            className="bg-red-600 hover:bg-red-700"
            redirectTo="/login"
          />
        </div>

        {/* قائمة الصلاحيات */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">
            الصلاحيات المحملة ({userPermissions.length})
          </h3>
          
          {userPermissions.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-96 overflow-y-auto">
              {userPermissions.map((permission) => (
                <div
                  key={permission.id}
                  className="bg-white p-3 rounded border border-gray-200"
                >
                  <div className="text-sm">
                    <p className="font-medium text-gray-800">{permission.name}</p>
                    <p className="text-gray-600 text-xs">{permission.key}</p>
                    <p className="text-blue-600 text-xs">{permission.category}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-600 text-center py-4">لا توجد صلاحيات محملة</p>
          )}
        </div>

        {/* معلومات تقنية إضافية */}
        <div className="mt-6 bg-gray-100 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">معلومات تقنية</h3>
          <div className="text-sm space-y-1">
            <p><strong>آخر تنظيف:</strong> {
              cacheStatus?.lastCleanup 
                ? new Date(cacheStatus.lastCleanup).toLocaleString('ar')
                : 'غير محدد'
            }</p>
            <p><strong>إصدار التخزين المؤقت:</strong> {cacheStatus?.version || 'غير محدد'}</p>
            <p><strong>الوقت المتبقي (من المكتبة):</strong> {formatTime(timeRemaining)}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";
import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import Link from 'next/link';

const LoginForm = () => {
    const router = useRouter();
    const [user, setUser] = useState("");
    const [password, setPassword] = useState("");
    const [rememberMe, setRememberMe] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const rememberedUser = localStorage.getItem('rememberedUser');
        if (rememberedUser) {
            setUser(rememberedUser);
            setRememberMe(true);
        }
        const rememberedPassword = localStorage.getItem('rememberedPassword');
        if (rememberedPassword) {
            setPassword(rememberedPassword);
            setRememberMe(true);
        }
    }, []);

    const formSubmitHandler = async (e: React.FormEvent) => {
        e.preventDefault();

        if (user === "") return toast.error("اسم المستخدم مطلوب");
        if (password === "") return toast.error("كلمة المرور مطلوبة");

        setIsLoading(true);

        try {
            console.log('Attempting login with:', { user });
            const response = await axios.post(`/api/users/login`, { user, password });
            console.log('Login response:', response.data);

            const { role, success } = response.data as { role: string, success: boolean };

            if (success) {
                toast.success('تم تسجيل الدخول بنجاح');

                if (rememberMe) {
                    localStorage.setItem('rememberedUser', user);
                    localStorage.setItem('rememberedPassword', password);
                } else {
                    localStorage.removeItem('rememberedUser');
                    localStorage.removeItem('rememberedPassword');
                }

                // إعادة التوجيه مباشرة بناءً على الدور
                let redirectUrl = '/';

                switch (role) {
                    case 'ADMIN':
                    case 'EMPLOYEE':
                        redirectUrl = '/admin';
                        break;
                    case 'TEACHER':
                        redirectUrl = '/teachers';
                        break;
                    case 'STUDENT':
                        redirectUrl = '/students';
                        break;
                    case 'PARENT':
                    case 'GUARDIAN':
                        redirectUrl = '/parents';
                        break;
                }

                // استخدام window.location.href للانتقال مباشرة إلى الصفحة المطلوبة
                // هذا سيعيد تحميل الصفحة ويضمن تحديث الهيدر
                window.location.href = redirectUrl;
            } else {
                toast.error('فشل تسجيل الدخول: ' + (response.data.message || 'خطأ غير معروف'));
            }
        } catch (error: any) {
            const errorMessage = error.response?.data?.message || 'فشل تسجيل الدخول';
            toast.error(errorMessage);
            console.error('Login error:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <form onSubmit={formSubmitHandler} className='space-y-6'>
            <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700">اسم المستخدم</label>
                <input
                    id="username"
                    className='mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]'
                    type="text"
                    value={user}
                    onChange={(e) => setUser(e.target.value)}
                />
            </div>

            <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">كلمة المرور</label>
                <input
                    id="password"
                    className='mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-[var(--primary-color)] focus:border-[var(--primary-color)]'
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                />
            </div>

            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <input
                        type="checkbox"
                        id="rememberMe"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        className="h-4 w-4 text-[var(--primary-color)] focus:ring-[var(--primary-color)] border-gray-300 rounded ml-2"
                    />
                    <label htmlFor="rememberMe" className="text-sm text-gray-700">تذكرني</label>
                </div>

                <div className="text-sm">
                    <Link href="/forgot-password" className="font-medium text-[var(--primary-color)] hover:text-[var(--secondary-color)]">
                        نسيت كلمة المرور؟
                    </Link>
                </div>
            </div>

            <div>
                <button
                    type='submit'
                    disabled={isLoading}
                    className='w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--primary-color)]'
                >
                    {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                </button>
            </div>

            <div className="text-center text-sm">
                <p className="text-gray-600">
                    ليس لديك حساب؟{' '}
                    <Link href="/register" className="font-medium text-[var(--primary-color)] hover:text-[var(--secondary-color)]">
                        إنشاء حساب جديد
                    </Link>
                </p>
            </div>
        </form>
    );
};

export default LoginForm;
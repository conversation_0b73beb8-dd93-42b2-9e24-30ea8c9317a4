import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trophy, Medal, Award, Star, FileText } from 'lucide-react';

type AchievementCardProps = {
  student: {
    id: number;
    name: string;
    username: string;
    totalPoints: number;
    averageGrade?: number;
    totalExams?: number;
    classe: {
      id: number;
      name: string;
    } | null;
  };
  rank?: number;
  achievements?: {
    id: number;
    type: string;
    name: string;
    date: string;
  }[];
  certificates?: {
    id: number;
    title: string;
    type: string;
    date: string;
  }[];
  onViewDetails?: (student: { id: number; name: string; username: string; totalPoints: number; classe: { id: number; name: string; } | null; }) => void;
  onViewCertificate?: (certificateId: number) => void;
  className?: string;
};

export default function AchievementCard({
  student,
  rank,
  achievements = [],
  certificates = [],
  onViewDetails,
  onViewCertificate,
  className
}: AchievementCardProps) {
  const getRankIcon = () => {
    if (!rank) return null;

    switch (rank) {
      case 1:
        return <Trophy className="h-12 w-12 text-yellow-500" />;
      case 2:
        return <Medal className="h-12 w-12 text-gray-500" />;
      case 3:
        return <Medal className="h-12 w-12 text-amber-700" />;
      default:
        return <Star className="h-12 w-12 text-blue-500" />;
    }
  };

  const getAchievementIcon = (type: string) => {
    switch (type) {
      case 'CERTIFICATE':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'PRIZE':
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 'BADGE':
        return <Medal className="h-5 w-5 text-purple-500" />;
      case 'AWARD':
        return <Award className="h-5 w-5 text-primary-color" />;
      default:
        return <Star className="h-5 w-5 text-blue-500" />;
    }
  };

  return (
    <Card className={`w-full transition-all duration-300 hover:shadow-md ${className || ''}`}>
      <CardHeader className="text-center">
        {rank && (
          <div className="flex justify-center mb-2">
            {getRankIcon()}
          </div>
        )}
        <CardTitle>{student.name}</CardTitle>
        <CardDescription>{student.classe?.name || '-'}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <p className="text-3xl font-bold text-[var(--primary-color)]">{student.totalPoints}</p>
          <p className="text-sm text-gray-500">نقطة</p>
        </div>

        {achievements.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">الإنجازات الأخيرة:</h4>
            <div className="flex flex-wrap gap-2">
              {achievements.slice(0, 3).map((achievement) => (
                <Badge key={achievement.id} variant="outline" className="flex items-center gap-1">
                  {getAchievementIcon(achievement.type)}
                  <span>{achievement.name}</span>
                </Badge>
              ))}
              {achievements.length > 3 && (
                <Badge variant="outline">+{achievements.length - 3}</Badge>
              )}
            </div>
          </div>
        )}

        {certificates.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">الشهادات:</h4>
            <div className="flex flex-wrap gap-2">
              {certificates.slice(0, 2).map((certificate) => (
                <Badge
                  key={certificate.id}
                  variant="secondary"
                  className="flex items-center gap-1 cursor-pointer hover:bg-secondary/80"
                  onClick={() => onViewCertificate && onViewCertificate(certificate.id)}
                >
                  <FileText className="h-4 w-4" />
                  <span>{certificate.title}</span>
                </Badge>
              ))}
              {certificates.length > 2 && (
                <Badge variant="secondary">+{certificates.length - 2}</Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="justify-center">
        <Button
          variant="outline"
          onClick={() => onViewDetails && onViewDetails(student)}
        >
          عرض التفاصيل
        </Button>
      </CardFooter>
    </Card>
  );
}

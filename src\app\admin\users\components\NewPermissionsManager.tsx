"use client";

import { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>he<PERSON>, FaTimes, FaEdit, FaTrash } from 'react-icons/fa';
import { toast } from 'react-hot-toast';

interface Permission {
  id: number;
  key: string;
  name: string;
  description?: string;
  category: string;
  route?: string;
  isActive: boolean;
}

interface Role {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  isSystem: boolean;
  isActive: boolean;
  _count: {
    users: number;
  };
}

interface User {
  id: number;
  username: string;
  email?: string;
  role: string;
  roleId?: number;
  userRole?: Role;
}

interface NewPermissionsManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const NewPermissionsManager: React.FC<NewPermissionsManagerProps> = ({
  isOpen,
  onClose
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);

  // جلب البيانات
  const fetchData = async () => {
    try {
      setLoading(true);

      // جلب المستخدمين
      console.log('Fetching users...');
      const usersResponse = await fetch('/api/admin/users/enhanced');
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        console.log('Users data received:', usersData);
        setUsers(usersData.users || []);
      } else {
        console.error('Failed to fetch users:', usersResponse.status);
        toast.error('فشل في جلب المستخدمين');
      }

      // جلب الأدوار
      console.log('Fetching roles...');
      const rolesResponse = await fetch('/api/admin/roles');
      if (rolesResponse.ok) {
        const rolesData = await rolesResponse.json();
        console.log('Roles data received:', rolesData);
        setRoles(rolesData.roles || []);
      } else {
        console.error('Failed to fetch roles:', rolesResponse.status);
        toast.error('فشل في جلب الأدوار');
      }

      // جلب الصلاحيات
      console.log('Fetching permissions...');
      const permissionsResponse = await fetch('/api/admin/permissions');
      if (permissionsResponse.ok) {
        const permissionsData = await permissionsResponse.json();
        console.log('Permissions data received:', permissionsData);
        setPermissions(permissionsData.permissions || []);
      } else {
        console.error('Failed to fetch permissions:', permissionsResponse.status);
        toast.error('فشل في جلب الصلاحيات');
      }

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('فشل في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  // جلب صلاحيات المستخدم
  const fetchUserPermissions = async (userId: string) => {
    try {
      // استخراج originalId من المستخدم
      const user = users.find(u => u.id === userId);
      if (!user) return;

      const response = await fetch(`/api/admin/users/${user.originalId}/permissions`);
      if (response.ok) {
        const data = await response.json();
        setUserPermissions(data.permissions || []);
      }
    } catch (error) {
      console.error('Error fetching user permissions:', error);
      setUserPermissions([]);
    }
  };

  // تحديث دور المستخدم
  const updateUserRole = async (userId: string, roleId: number) => {
    try {
      // العثور على الدور المحدد لتحديد الدور الأساسي المناسب
      const selectedRole = roles.find(r => r.id === roleId);
      let baseRole = 'EMPLOYEE'; // افتراضي

      if (selectedRole) {
        // استخدام نفس منطق تحديد الدور الأساسي المستخدم في الخادم
        const { determineBaseRole } = await import('@/utils/roleUtils');
        baseRole = determineBaseRole(selectedRole, roles);
      }

      // استخراج originalId من المستخدم
      const user = users.find(u => u.id === userId);
      if (!user) {
        toast.error('المستخدم غير موجود');
        return;
      }

      const response = await fetch(`/api/admin/users/${user.originalId}/role`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ roleId, baseRole }),
      });

      if (response.ok) {
        toast.success('تم تحديث دور المستخدم بنجاح');
        await fetchData();
        if (selectedUser?.id === userId) {
          // تحديث المستخدم المحدد مع الدور الجديد
          const updatedUser = users.find(u => u.id === userId);
          if (updatedUser) {
            const role = roles.find(r => r.id === roleId);
            setSelectedUser({
              ...updatedUser,
              roleId,
              userRole: role
            });
          }
          await fetchUserPermissions(userId);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || 'فشل في تحديث دور المستخدم');
      }
    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('حدث خطأ أثناء تحديث دور المستخدم');
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchData();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedUser && selectedUser.roleId) {
      fetchUserPermissions(selectedUser.id);
    }
  }, [selectedUser]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" dir="rtl">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="bg-[var(--primary-color)] text-white p-4 flex justify-between items-center">
            <h2 className="text-xl font-bold flex items-center gap-2">
              <FaUserTag />
              إدارة الأدوار والصلاحيات
            </h2>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 text-xl"
            >
              <FaTimes />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 flex overflow-hidden">
            {/* Users List */}
            <div className="w-1/3 border-l border-gray-200 p-4 overflow-y-auto">
              <h3 className="text-lg font-semibold mb-4">المستخدمون</h3>
              {loading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)] mx-auto"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  {users.filter(user => user.role === 'EMPLOYEE').map((user) => (
                    <div
                      key={user.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedUser?.id === user.id
                          ? 'bg-[var(--primary-color)] text-white'
                          : 'bg-gray-50 hover:bg-gray-100'
                      }`}
                      onClick={() => setSelectedUser(user)}
                    >
                      <div className="font-medium">{user.username}</div>
                      <div className="text-sm opacity-75">{user.email}</div>
                      <div className="text-xs mt-1">
                        الدور: {user.userRole?.displayName || 'غير محدد'}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* User Details */}
            <div className="w-1/3 border-l border-gray-200 p-4 overflow-y-auto">
              {selectedUser ? (
                <div>
                  <h3 className="text-lg font-semibold mb-4">تفاصيل المستخدم</h3>
                  <div className="bg-gray-50 p-4 rounded-lg mb-4">
                    <div className="mb-2">
                      <strong>الاسم:</strong> {selectedUser.username}
                    </div>
                    <div className="mb-2">
                      <strong>البريد الإلكتروني:</strong> {selectedUser.email || 'غير محدد'}
                    </div>
                    <div className="mb-4">
                      <strong>الدور الحالي:</strong> {selectedUser.userRole?.displayName || 'غير محدد'}
                    </div>

                    {/* Role Selection */}
                    <div>
                      <label className="block text-sm font-medium mb-2">تغيير الدور:</label>
                      <select
                        value={selectedUser.roleId || ''}
                        onChange={(e) => {
                          const roleId = parseInt(e.target.value);
                          if (roleId) {
                            updateUserRole(selectedUser.id, roleId);
                          }
                        }}
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent"
                      >
                        <option value="">اختر دور</option>
                        {roles.filter(role => role.isActive).map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.displayName}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  اختر مستخدماً لعرض التفاصيل
                </div>
              )}
            </div>

            {/* Permissions */}
            <div className="w-1/3 p-4 overflow-y-auto">
              {selectedUser && selectedUser.roleId ? (
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <FaKey />
                    صلاحيات المستخدم
                  </h3>
                  {userPermissions.length > 0 ? (
                    <div className="space-y-2">
                      {userPermissions.map((permission) => (
                        <div
                          key={permission.id}
                          className="p-3 bg-green-50 border border-green-200 rounded-lg"
                        >
                          <div className="flex items-center gap-2 text-green-700">
                            <FaCheck className="text-green-500" />
                            <span className="font-medium">{permission.name}</span>
                          </div>
                          <div className="text-sm text-green-600 mt-1">
                            {permission.description}
                          </div>
                          <div className="text-xs text-green-500 mt-1">
                            الفئة: {permission.category}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 py-4">
                      لا توجد صلاحيات محددة
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  اختر مستخدماً لديه دور لعرض الصلاحيات
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 flex justify-end gap-2">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewPermissionsManager;

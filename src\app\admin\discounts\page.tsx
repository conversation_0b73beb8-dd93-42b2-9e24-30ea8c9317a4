'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'react-toastify';
import { FaPercent, FaMoneyBillWave, FaPlus, FaEdit, FaTrash, FaSearch } from 'react-icons/fa';
import DiscountModal from './DiscountModal';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import PermissionGuard from '@/components/admin/PermissionGuard';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface Discount {
  id: number;
  name: string;
  description: string | null;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  value: number;
  isActive: boolean;
  startDate: string | null;
  endDate: string | null;
  minAmount: number | null;
  maxAmount: number | null;
  maxUsage: number | null;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

export default function DiscountsPage() {
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedDiscount, setSelectedDiscount] = useState<Discount | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  });

  // جلب الخصومات
  const fetchDiscounts = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: pagination.current.toString(),
        query: searchQuery
      });

      const response = await fetch(`/api/discounts?${queryParams}`);
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      setDiscounts(data.discounts);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching discounts:', error);
      toast.error('تعذر جلب بيانات الخصومات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDiscounts();
  }, [pagination.current, searchQuery]); // eslint-disable-line react-hooks/exhaustive-deps

  // تعديل خصم
  const handleEditDiscount = (discount: Discount) => {
    setSelectedDiscount(discount);
    setIsModalVisible(true);
  };

  // حذف خصم
  const handleDeleteDiscount = async (id: number) => {
    if (!window.confirm('هل أنت متأكد من حذف هذا الخصم؟')) return;

    try {
      const response = await fetch(`/api/discounts?id=${id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (!response.ok) throw new Error(data.error);

      toast.success(data.message || 'تم حذف الخصم بنجاح');
      fetchDiscounts();
    } catch (error) {
      console.error('Error deleting discount:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حذف الخصم');
    }
  };

  // تغيير حالة الخصم (نشط/غير نشط)
  const handleToggleStatus = async (discount: Discount) => {
    try {
      const response = await fetch('/api/discounts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: discount.id,
          isActive: !discount.isActive
        })
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      toast.success(`تم ${!discount.isActive ? 'تفعيل' : 'تعطيل'} الخصم بنجاح`);
      fetchDiscounts();
    } catch (error) {
      console.error('Error toggling discount status:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تغيير حالة الخصم');
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.discounts.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaPercent className="text-[var(--primary-color)]" />
          إدارة الخصومات
        </h1>
        <QuickActionButtons
          entityType="discounts"
          actions={[
            {
              key: 'create',
              label: 'إضافة خصم جديد',
              icon: <FaPlus />,
              onClick: () => {
                setSelectedDiscount(null);
                setIsModalVisible(true);
              },
              variant: 'primary'
            }
          ]}
          className="w-full sm:w-auto"
        />
      </div>

      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="w-full">
          <div className="relative w-full">
            <Input
              placeholder="ابحث عن خصم..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setPagination(prev => ({ ...prev, current: 1 }));
              }}
              className="pl-10 w-full"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="responsive-table-container">
          <table className="min-w-full bg-white card-mode-table">
            <thead>
              <tr className="bg-[var(--primary-color)] text-white">
                <th className="px-6 py-3 text-right font-semibold">الاسم</th>
                <th className="px-6 py-3 text-right font-semibold">النوع</th>
                <th className="px-6 py-3 text-right font-semibold">القيمة</th>
                <th className="px-6 py-3 text-right font-semibold">الحالة</th>
                <th className="px-6 py-3 text-right font-semibold hide-on-mobile">تاريخ البداية</th>
                <th className="px-6 py-3 text-right font-semibold hide-on-mobile">تاريخ النهاية</th>
                <th className="px-6 py-3 text-right font-semibold">عدد الاستخدامات</th>
                <th className="px-6 py-3 text-right font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-center">
                    جاري التحميل...
                  </td>
                </tr>
              ) : discounts.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-center">
                    لا توجد خصومات
                  </td>
                </tr>
              ) : (
                discounts.map((discount) => (
                  <tr key={discount.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4" data-label="الاسم">{discount.name}</td>
                    <td className="px-6 py-4" data-label="النوع">
                      {discount.type === 'PERCENTAGE' ? (
                        <span className="flex items-center gap-1">
                          <FaPercent className="text-blue-500" />
                          نسبة مئوية
                        </span>
                      ) : (
                        <span className="flex items-center gap-1">
                          <FaMoneyBillWave className="text-primary-color" />
                          مبلغ ثابت
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4" data-label="القيمة">
                      {discount.type === 'PERCENTAGE'
                        ? `${discount.value}%`
                        : `${discount.value} د.ج`}
                    </td>
                    <td className="px-6 py-4" data-label="الحالة">
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          discount.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {discount.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 hide-on-mobile" data-label="تاريخ البداية">
                      {discount.startDate
                        ? new Date(discount.startDate).toLocaleDateString('fr-FR')
                        : '-'}
                    </td>
                    <td className="px-6 py-4 hide-on-mobile" data-label="تاريخ النهاية">
                      {discount.endDate
                        ? new Date(discount.endDate).toLocaleDateString('fr-FR')
                        : '-'}
                    </td>
                    <td className="px-6 py-4" data-label="عدد الاستخدامات">
                      {discount.usageCount} / {discount.maxUsage || '∞'}
                    </td>
                    <td className="px-6 py-4 actions" data-label="الإجراءات">
                      <div className="flex gap-2 mobile-action-buttons">
                        <OptimizedActionButtonGroup
                          entityType="discounts"
                          onEdit={() => handleEditDiscount(discount)}
                          onDelete={() => handleDeleteDiscount(discount.id)}
                          showEdit={true}
                          showDelete={true}
                          size="sm"
                          className="gap-2"
                        />
                        <PermissionGuard requiredPermission="admin.discounts.edit">
                          <Button
                            size="sm"
                            variant="outline"
                            className={`h-8 ${
                              discount.isActive
                                ? 'border-red-500 text-red-500 hover:bg-red-500'
                                : 'border-primary-color text-primary-color hover:bg-primary-color'
                            } hover:text-white`}
                            onClick={() => handleToggleStatus(discount)}
                            title={discount.isActive ? 'تعطيل' : 'تفعيل'}
                          >
                            {discount.isActive ? 'تعطيل' : 'تفعيل'}
                          </Button>
                        </PermissionGuard>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex flex-col sm:flex-row items-center gap-3">
            <div className="text-sm text-[var(--primary-color)] mb-2 sm:mb-0">
              الصفحة {pagination.current} من {pagination.totalPages}
            </div>
            <div className="flex flex-wrap justify-center gap-2">
              <Button
                variant="outline"
                onClick={() => setPagination(prev => ({ ...prev, current: prev.current - 1 }))}
                disabled={pagination.current === 1}
                className="min-w-[80px]"
              >
                السابق
              </Button>
              <div className="hidden sm:flex items-center gap-1">
                {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map(page => (
                  <Button
                    key={page}
                    variant={pagination.current === page ? 'default' : 'outline'}
                    className={pagination.current === page ? 'bg-[var(--primary-color)]' : ''}
                    onClick={() => setPagination(prev => ({ ...prev, current: page }))}
                  >
                    {page}
                  </Button>
                ))}
              </div>
              <Button
                variant="outline"
                onClick={() => setPagination(prev => ({ ...prev, current: prev.current + 1 }))}
                disabled={pagination.current === pagination.totalPages}
                className="min-w-[80px]"
              >
                التالي
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إضافة/تعديل الخصم */}
      <DiscountModal
        isOpen={isModalVisible}
        onCloseAction={() => setIsModalVisible(false)}
        onSuccessAction={fetchDiscounts}
        discount={selectedDiscount}
      />
      </div>
    </OptimizedProtectedRoute>
  );
}

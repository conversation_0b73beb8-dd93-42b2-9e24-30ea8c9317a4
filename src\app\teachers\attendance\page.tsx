"use client";
import React, { useState, useEffect, useC<PERSON>back } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  FaCalendarAlt,
  FaSearch,
  FaUserCheck,
  FaUserTimes,
  FaUserClock,
  FaSync,
  FaSave,
  FaEdit,
  <PERSON>a<PERSON>halk<PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON><PERSON>
} from 'react-icons/fa';
import { toast } from 'react-toastify';

interface Student {
  id: number;
  name: string;
}

interface Class {
  id: number;
  name: string;
}

interface AttendanceRecord {
  id?: number;
  studentId: number;
  studentName: string;
  status: 'PRESENT' | 'ABSENT' | 'EXCUSED';
  date: string;
  hisass: number;
}

const TeacherAttendancePage = () => {
  const searchParams = useSearchParams();
  const studentIdParam = searchParams ? searchParams.get('studentId') : null;

  const [classes, setClasses] = useState<Class[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [selectedHisass, setSelectedHisass] = useState<string>('1');
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Dialog state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [attendanceStatus, setAttendanceStatus] = useState<'PRESENT' | 'ABSENT' | 'EXCUSED'>('PRESENT');

  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const response = await fetch('/api/teacher-classes');
        if (!response.ok) {
          throw new Error('فشل في جلب الفصول');
        }
        const data = await response.json();
        setClasses(data.classes);

        // إذا كان هناك فصل واحد فقط، اختره تلقائياً
        if (data.classes.length === 1) {
          setSelectedClass(data.classes[0].id.toString());
        }
      } catch (err: unknown) {
        console.error('Error fetching classes:', err);
        toast.error('فشل في جلب الفصول');
      }
    };

    fetchClasses();
  }, []);

  const fetchStudents = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/local/students?classeId=${selectedClass}`);

      if (!response.ok) {
        throw new Error('فشل في جلب بيانات الطلاب');
      }

      const data = await response.json();
      setStudents(data.students);
      setFilteredStudents(data.students);
    } catch (err: unknown) {
      console.error('Error fetching students:', err);
      setError('حدث خطأ أثناء جلب بيانات الطلاب');
      toast.error('فشل في جلب بيانات الطلاب');
    } finally {
      setIsLoading(false);
    }
  }, [selectedClass]);

  useEffect(() => {
    if (selectedClass) {
      fetchStudents();
    }
  }, [selectedClass, fetchStudents]);

  const fetchAttendance = useCallback(async () => {
    try {
      setIsLoading(true);

      const response = await fetch(
        `/api/attendance?date=${selectedDate}&classeId=${selectedClass}&hisass=${selectedHisass}`
      );

      if (!response.ok) {
        throw new Error('فشل في جلب سجل الحضور');
      }

      const data = await response.json();

      // تحويل البيانات إلى الشكل المطلوب
      const records: AttendanceRecord[] = [];

      // إضافة سجلات الحضور الموجودة
      data.forEach((record: {
        id: number;
        student: {
          id: number;
          name: string;
        };
        status: 'PRESENT' | 'ABSENT' | 'EXCUSED';
      }) => {
        records.push({
          id: record.id,
          studentId: record.student.id,
          studentName: record.student.name,
          status: record.status,
          date: selectedDate,
          hisass: parseInt(selectedHisass)
        });
      });

      // إضافة الطلاب الذين ليس لديهم سجل حضور بعد
      students.forEach(student => {
        if (!records.some(record => record.studentId === student.id)) {
          records.push({
            studentId: student.id,
            studentName: student.name,
            status: 'ABSENT', // افتراضياً غائب
            date: selectedDate,
            hisass: parseInt(selectedHisass)
          });
        }
      });

      setAttendanceRecords(records);
    } catch (err: unknown) {
      console.error('Error fetching attendance:', err);
      toast.error('فشل في جلب سجل الحضور');
    } finally {
      setIsLoading(false);
    }
  }, [selectedClass, selectedDate, selectedHisass, students]);

  useEffect(() => {
    if (selectedClass && selectedDate && selectedHisass) {
      fetchAttendance();
    }
  }, [selectedClass, selectedDate, selectedHisass, fetchAttendance]);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredStudents(students);
    } else {
      const filtered = students.filter(student =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredStudents(filtered);
    }
  }, [searchTerm, students]);

  useEffect(() => {
    // إذا تم تمرير معرف طالب في الرابط، افتح نافذة تسجيل الحضور له
    if (studentIdParam && students.length > 0) {
      const student = students.find(s => s.id.toString() === studentIdParam);
      if (student) {
        setSelectedStudent(student);
        setIsDialogOpen(true);
      }
    }
  }, [studentIdParam, students]);

  const handleAttendanceSubmit = async () => {
    try {
      if (!selectedStudent) return;

      setIsSubmitting(true);

      const attendanceData = {
        studentId: selectedStudent.id,
        date: selectedDate,
        status: attendanceStatus,
        hisass: parseInt(selectedHisass)
      };

      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(attendanceData)
      });

      if (!response.ok) {
        throw new Error('فشل في تسجيل الحضور');
      }

      const data = await response.json();

      if (data.success) {
        toast.success('تم تسجيل الحضور بنجاح');

        // تحديث قائمة سجلات الحضور
        setAttendanceRecords(prevRecords => {
          const updatedRecords = [...prevRecords];
          const index = updatedRecords.findIndex(record => record.studentId === selectedStudent.id);

          if (index !== -1) {
            updatedRecords[index] = {
              ...updatedRecords[index],
              id: data.data.id,
              status: attendanceStatus
            };
          }

          return updatedRecords;
        });

        setIsDialogOpen(false);
      } else {
        throw new Error(data.error || 'فشل في تسجيل الحضور');
      }
    } catch (err: unknown) {
      console.error('Error submitting attendance:', err);
      toast.error('فشل في تسجيل الحضور');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openAttendanceDialog = (student: Student) => {
    setSelectedStudent(student);

    // تعيين الحالة الحالية للطالب إذا كانت موجودة
    const existingRecord = attendanceRecords.find(record => record.studentId === student.id);
    if (existingRecord) {
      setAttendanceStatus(existingRecord.status);
    } else {
      setAttendanceStatus('PRESENT');
    }

    setIsDialogOpen(true);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PRESENT':
        return <FaUserCheck className="text-primary-color" />;
      case 'ABSENT':
        return <FaUserTimes className="text-red-500" />;
      case 'EXCUSED':
        return <FaUserClock className="text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PRESENT':
        return 'حاضر';
      case 'ABSENT':
        return 'غائب';
      case 'EXCUSED':
        return 'غائب بعذر';
      default:
        return '';
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaUserCheck className="text-[var(--primary-color)]" />
            تسجيل الحضور
          </h1>
          <p className="text-gray-500 mr-4 mt-2">تسجيل حضور وغياب الطلاب</p>
        </div>
        <Button
          onClick={fetchAttendance}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2"
          title="تحديث البيانات"
        >
          <FaSync className="h-5 w-5" />
        </Button>
      </div>

      <Card className="border border-[#e0f2ef] shadow-md">
        <CardHeader className="bg-[#f8fffd]">
          <CardTitle className="flex items-center gap-2 text-[var(--primary-color)]">
            <FaCalendarAlt className="text-[var(--primary-color)]" />
            <span>تسجيل الحضور</span>
          </CardTitle>
          <CardDescription>
            اختر الفصل والتاريخ والحصة لتسجيل الحضور
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <FaChalkboardTeacher className="text-[var(--primary-color)]" />
                <Label htmlFor="class-select" className="text-[var(--primary-color)] font-medium">الفصل</Label>
              </div>
              <Select value={selectedClass} onValueChange={setSelectedClass}>
                <SelectTrigger id="class-select" className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر الفصل" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id.toString()}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <FaCalendarAlt className="text-[var(--primary-color)]" />
                <Label htmlFor="date-input" className="text-[var(--primary-color)] font-medium">التاريخ</Label>
              </div>
              <Input
                id="date-input"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
              />
            </div>

            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <FaClock className="text-[var(--primary-color)]" />
                <Label htmlFor="hisass-select" className="text-[var(--primary-color)] font-medium">الحصة</Label>
              </div>
              <Select value={selectedHisass} onValueChange={setSelectedHisass}>
                <SelectTrigger id="hisass-select" className="border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]">
                  <SelectValue placeholder="اختر الحصة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">الحصة الأولى</SelectItem>
                  <SelectItem value="2">الحصة الثانية</SelectItem>
                  <SelectItem value="3">الحصة الثالثة</SelectItem>
                  <SelectItem value="4">الحصة الرابعة</SelectItem>
                  <SelectItem value="5">الحصة الخامسة</SelectItem>
                  <SelectItem value="6">الحصة السادسة</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {selectedClass ? (
            <>
              <div className="relative w-full mb-4">
                <Input
                  placeholder="بحث عن طالب..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)]"
                />
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--primary-color)]" />
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center h-40">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
                </div>
              ) : error ? (
                <div className="text-center text-red-500 py-4">{error}</div>
              ) : filteredStudents.length === 0 ? (
                <div className="text-center text-gray-500 py-4">لا يوجد طلاب في هذا الفصل</div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-[var(--primary-color)]">
                        <TableHead className="text-white font-bold">الاسم</TableHead>
                        <TableHead className="text-white font-bold">الحالة</TableHead>
                        <TableHead className="text-white font-bold">الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.map((student) => {
                        const attendanceRecord = attendanceRecords.find(
                          record => record.studentId === student.id
                        );

                        return (
                          <TableRow key={student.id}>
                            <TableCell className="font-medium">{student.name}</TableCell>
                            <TableCell>
                              {attendanceRecord ? (
                                <div className="flex items-center gap-2">
                                  {getStatusIcon(attendanceRecord.status)}
                                  <span>{getStatusText(attendanceRecord.status)}</span>
                                </div>
                              ) : (
                                <span className="text-gray-400">لم يسجل بعد</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openAttendanceDialog(student)}
                                className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] hover:bg-[#e0f2ef] flex items-center gap-1"
                              >
                                {attendanceRecord ? (
                                  <>
                                    <FaEdit className="ml-1" />
                                    تعديل
                                  </>
                                ) : (
                                  <>
                                    <FaUserCheck className="ml-1" />
                                    تسجيل
                                  </>
                                )}
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </>
          ) : (
            <div className="text-center text-gray-500 py-4">
              الرجاء اختيار الفصل لعرض الطلاب
            </div>
          )}
        </CardContent>
      </Card>

      {/* نافذة تسجيل الحضور */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="bg-[#f8fffd] border border-[#e0f2ef]">
          <DialogHeader>
            <DialogTitle className="text-[var(--primary-color)] font-bold text-xl flex items-center gap-2">
              <FaUserCheck className="text-[var(--primary-color)]" />
              تسجيل حضور الطالب
            </DialogTitle>
            <DialogDescription>
              {selectedStudent?.name} - {selectedDate} - الحصة {selectedHisass}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Label className="mb-2 block">حالة الحضور</Label>
            <RadioGroup
              value={attendanceStatus}
              onValueChange={(value) => setAttendanceStatus(value as 'PRESENT' | 'ABSENT' | 'EXCUSED')}
              className="space-y-2"
            >
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="PRESENT" id="present" />
                <Label htmlFor="present" className="flex items-center gap-2">
                  <FaUserCheck className="text-primary-color" />
                  <span>حاضر</span>
                </Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="ABSENT" id="absent" />
                <Label htmlFor="absent" className="flex items-center gap-2">
                  <FaUserTimes className="text-red-500" />
                  <span>غائب</span>
                </Label>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="EXCUSED" id="excused" />
                <Label htmlFor="excused" className="flex items-center gap-2">
                  <FaUserClock className="text-yellow-500" />
                  <span>غائب بعذر</span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDialogOpen(false)}
              disabled={isSubmitting}
              className="border-gray-300 hover:bg-gray-100"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleAttendanceSubmit}
              disabled={isSubmitting}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
            >
              {isSubmitting ? (
                <span className="flex items-center gap-2">
                  <div className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent"></div>
                  جاري الحفظ...
                </span>
              ) : (
                <>
                  <FaSave className="ml-1" />
                  حفظ
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TeacherAttendancePage;

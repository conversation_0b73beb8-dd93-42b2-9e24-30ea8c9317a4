/* 
  إصلاحات للتفاعل مع العناصر في التطبيق
  هذا الملف يحتوي على إصلاحات CSS لمشاكل التفاعل مع الأزرار والقوائم المنسدلة والبوب أب
*/

/* تحسين التفاعل مع الأزرار */
button {
  position: relative;
  z-index: 1;
  touch-action: manipulation;
}

/* منع تداخل الأحداث */
button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

/* تحسين التفاعل مع القوائم المنسدلة */
[role="listbox"],
[role="menu"] {
  z-index: 9999 !important;
  pointer-events: auto !important;
}

/* تحسين التفاعل مع عناصر القائمة */
[role="option"],
[role="menuitem"] {
  cursor: pointer !important;
  user-select: none !important;
  touch-action: manipulation;
}

/* تحسين التفاعل مع البوب أب */
[role="dialog"] {
  z-index: 50;
}

/* تحسين التفاعل مع النماذج */
form {
  position: relative;
}

/* تحسين التفاعل مع حقول الإدخال */
input,
select,
textarea {
  touch-action: manipulation;
}

/* تحسين التفاعل مع الروابط */
a {
  touch-action: manipulation;
}

/* إصلاح مشكلة z-index للقوائم المنسدلة */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

/* إصلاح مشكلة التفاعل مع الأزرار في السايدبار */
aside a,
aside button {
  position: relative;
  z-index: 1;
  touch-action: manipulation;
}

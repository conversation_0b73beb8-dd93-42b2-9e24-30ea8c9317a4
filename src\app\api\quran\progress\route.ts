import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// POST /api/quran/progress
export async function POST(request: NextRequest) {
  try {
    const data = await request.json();

    // التحقق من البيانات المطلوبة
    if (!data.studentId || !data.surahId || data.startVerse === undefined || data.endVerse === undefined) {
      return NextResponse.json(
        { error: 'البيانات غير مكتملة. يرجى توفير معرف الطالب والسورة ونطاق الآيات' },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: data.studentId }
    });

    if (!student) {
      return NextResponse.json(
        { error: 'الطالب غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود السورة
    const surah = await prisma.surah.findUnique({
      where: { id: data.surahId }
    });

    if (!surah) {
      return NextResponse.json(
        { error: 'السورة غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من صحة نطاق الآيات
    if (data.startVerse < 1 || data.endVerse < data.startVerse) {
      return NextResponse.json(
        { error: 'نطاق الآيات غير صحيح' },
        { status: 400 }
      );
    }

    // الحصول على امتحان حفظ القرآن الحالي أو إنشاء واحد جديد
    let quranExam = await prisma.exam.findFirst({
      where: {
        evaluationType: 'QURAN_MEMORIZATION',
        requiresSurah: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // إذا لم يوجد امتحان، قم بإنشاء واحد جديد
    if (!quranExam) {
      // تحديد الشهر بتنسيق MM-YYYY
      const today = new Date();
      const month = `${String(today.getMonth() + 1).padStart(2, '0')}-${today.getFullYear()}`;

      quranExam = await prisma.exam.create({
        data: {
          description: 'امتحان حفظ القرآن',
          month: month,
          maxPoints: 100,
          passingPoints: 60,
          evaluationType: 'QURAN_MEMORIZATION',
          requiresSurah: true,
          isPeriodic: true,
          period: 'شهري'
        }
      });
    }

    // إنشاء سجل تقدم حفظ القرآن
    const quranProgress = await prisma.quranProgress.create({
      data: {
        studentId: data.studentId,
        surahId: data.surahId,
        examId: quranExam.id, // استخدام معرف الامتحان الذي تم الحصول عليه
        startVerse: data.startVerse,
        endVerse: data.endVerse,
        memorization: data.memorization || 0,
        tajweed: data.tajweed || 0,
        startDate: new Date(),
        completionDate: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل تقدم حفظ القرآن بنجاح',
      data: quranProgress
    });
  } catch (error) {
    console.error('Error creating quran progress:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء تسجيل تقدم حفظ القرآن' },
      { status: 500 }
    );
  }
}

// GET /api/quran/progress
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const surahId = searchParams.get('surahId');

    // بناء شروط البحث
    const whereConditions: {
      studentId?: number;
      surahId?: number;
    } = {};

    if (studentId) {
      whereConditions.studentId = parseInt(studentId);
    }

    if (surahId) {
      whereConditions.surahId = parseInt(surahId);
    }

    // جلب سجلات تقدم حفظ القرآن
    const quranProgress = await prisma.quranProgress.findMany({
      where: whereConditions,
      include: {
        student: {
          select: {
            id: true,
            name: true,
            classeId: true,
            classe: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        surah: true
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    return NextResponse.json(quranProgress);
  } catch (error) {
    console.error('Error fetching quran progress:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب سجلات تقدم حفظ القرآن' },
      { status: 500 }
    );
  }
}

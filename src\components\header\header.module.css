.header {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    border-bottom: 4px solid rgb(144, 144, 144);
    background-color: rgb(227, 225, 225);
    position: relative;
}

.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    color: rgb(176, 15, 176);
}

.menu {
    font-size: 40px;
    font-weight: bold;
    color: #202121;
    cursor: pointer;
    display: none;
}

.navLinks {
    margin-left: 30px;
    font-family: 'Times New Roman', Times, serif;
}

.navLink {
    font-size: 20px;
    font-weight: 600;
    margin: 0 12px;
    font-family: 'Times New Roman', Times, serif;
    color: #202121;
    cursor: pointer;
}

.right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn {
    cursor: pointer;
    background-color: blue;
    color: white;
    border-radius: 10px;
    padding: 5px 10px;
    font-size: 21px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.btn:hover {
    background-color: darkblue;
}

.navLink:hover {
    color: darkblue;
}
.userMenu {
  position: relative;
  display: inline-block;
}
.userMenuDropdown {
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 0.5rem;
  width: 200px;
  background: white;
  border-radius: 0.375rem;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 50;
}
.userMenuItem {
  display: block;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}
.userMenuItem:hover {
  background-color: #f3f4f6;
}
@media (max-width: 1080px) {
    .logo {
        display: none;
    }

    .menu {
        display: block;
    }

    .navLinksWrapper {
        position: absolute;
        left: 0;
        top: 100%;
        width: 100%;
        background-color: inherit;
        transition: all .5s ease-in;
        z-index: 100;
        background-color: rgb(227, 225, 225);
        clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    }

    .navLinks {
        padding-left: 30px;
        padding-bottom: 10px;
        margin-left: 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
    }

    .navLink {
        padding: 0;
        margin: 12px 0;
    }
}
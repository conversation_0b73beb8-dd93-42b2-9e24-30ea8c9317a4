import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Get the default theme
    const defaultTheme = await prisma.theme.findFirst({
      where: {
        isDefault: true
      }
    });

    if (!defaultTheme) {
      // If no default theme exists, get the first theme
      const firstTheme = await prisma.theme.findFirst();
      
      if (!firstTheme) {
        return res.status(404).json({ message: 'No themes found' });
      }

      // Set it as default
      const updatedTheme = await prisma.theme.update({
        where: {
          id: firstTheme.id
        },
        data: {
          isDefault: true
        }
      });

      return res.status(200).json(updatedTheme);
    }

    return res.status(200).json(defaultTheme);
  } catch (error) {
    console.error('Error fetching default theme:', error);
    return res.status(500).json({ message: 'Error fetching default theme' });
  }
}
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { checkUserPermission } from '@/lib/permissions';

// GET: جلب جميع الأدوار
export async function GET(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.view');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    // جلب الأدوار مع عدد المستخدمين
    const roles = await prisma.role.findMany({
      include: {
        _count: {
          select: {
            users: true
          }
        }
      },
      orderBy: [
        { isSystem: 'desc' },
        { name: 'asc' }
      ]
    });

    return NextResponse.json({
      roles,
      message: "تم جلب الأدوار بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error fetching roles:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء جلب الأدوار" },
      { status: 500 }
    );
  }
}

// POST: إنشاء دور جديد
export async function POST(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const permissionCheck = await checkUserPermission(request, 'admin.roles.create');
    if (!permissionCheck.success) {
      return NextResponse.json(
        { message: permissionCheck.message },
        { status: permissionCheck.status }
      );
    }

    const body = await request.json();
    const { name, displayName, description } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !displayName) {
      return NextResponse.json(
        { message: "اسم الدور والاسم المعروض مطلوبان" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود دور بنفس الاسم
    const existingRole = await prisma.role.findUnique({
      where: { name }
    });

    if (existingRole) {
      return NextResponse.json(
        { message: "يوجد دور بهذا الاسم مسبقاً" },
        { status: 400 }
      );
    }

    // إنشاء الدور الجديد
    const newRole = await prisma.role.create({
      data: {
        name,
        displayName,
        description,
        isSystem: false,
        isActive: true
      },
      include: {
        _count: {
          select: {
            users: true
          }
        }
      }
    });

    return NextResponse.json({
      role: newRole,
      message: "تم إنشاء الدور بنجاح"
    }, { status: 201 });

  } catch (error: unknown) {
    console.error('Error creating role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء إنشاء الدور" },
      { status: 500 }
    );
  }
}

// PUT: تحديث دور
export async function PUT(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به: يرجى تسجيل الدخول" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { message: "غير مصرح به: يجب أن تكون مسؤولاً لتحديث الأدوار" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, displayName, description, isActive } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !displayName) {
      return NextResponse.json(
        { message: "معرف الدور والاسم المعروض مطلوبان" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدور
    const existingRole = await prisma.role.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // منع تعديل أدوار النظام
    if (existingRole.isSystem) {
      return NextResponse.json(
        { message: "لا يمكن تعديل أدوار النظام" },
        { status: 400 }
      );
    }

    // تحديث الدور
    const updatedRole = await prisma.role.update({
      where: { id: parseInt(id) },
      data: {
        displayName,
        description,
        isActive: isActive !== undefined ? isActive : existingRole.isActive
      },
      include: {
        _count: {
          select: {
            users: true
          }
        }
      }
    });

    return NextResponse.json({
      role: updatedRole,
      message: "تم تحديث الدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error updating role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء تحديث الدور" },
      { status: 500 }
    );
  }
}

// DELETE: حذف دور
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من الصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به: يرجى تسجيل الدخول" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'ADMIN') {
      return NextResponse.json(
        { message: "غير مصرح به: يجب أن تكون مسؤولاً لحذف الأدوار" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { message: "معرف الدور مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود الدور
    const existingRole = await prisma.role.findUnique({
      where: { id: parseInt(id) },
      include: {
        _count: {
          select: {
            users: true
          }
        }
      }
    });

    if (!existingRole) {
      return NextResponse.json(
        { message: "الدور غير موجود" },
        { status: 404 }
      );
    }

    // منع حذف أدوار النظام
    if (existingRole.isSystem) {
      return NextResponse.json(
        { message: "لا يمكن حذف أدوار النظام" },
        { status: 400 }
      );
    }

    // منع حذف الدور إذا كان مستخدماً
    if (existingRole._count.users > 0) {
      return NextResponse.json(
        { message: `لا يمكن حذف الدور لأنه مستخدم من قبل ${existingRole._count.users} مستخدم` },
        { status: 400 }
      );
    }

    // حذف الدور
    await prisma.role.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      message: "تم حذف الدور بنجاح"
    });

  } catch (error: unknown) {
    console.error('Error deleting role:', error);
    return NextResponse.json(
      { message: "حدث خطأ أثناء حذف الدور" },
      { status: 500 }
    );
  }
}

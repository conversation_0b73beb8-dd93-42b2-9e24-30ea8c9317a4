'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Plus, Pencil, Trash2, ArrowRight } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import Link from 'next/link';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

type HonorCriteria = {
  id: number;
  name: string;
  description: string;
  pointsThreshold: number;
  isActive: boolean;
  createdAt: string;
};

export default function HonorCriteriaPage() {
  const [criteria, setCriteria] = useState<HonorCriteria[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCriteria, setSelectedCriteria] = useState<HonorCriteria | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    pointsThreshold: 0,
    isActive: true
  });

  useEffect(() => {
    fetchCriteria();
  }, []);

  const fetchCriteria = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/honor-criteria');
      if (!response.ok) throw new Error('Failed to fetch criteria');
      const data = await response.json();
      setCriteria(data.data || []);
    } catch (error) {
      console.error('Error fetching criteria:', error);
      toast.error('حدث خطأ أثناء جلب معايير التقييم');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'pointsThreshold' ? parseInt(value) || 0 : value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSubmit = async () => {
    try {
      const url = selectedCriteria
        ? `/api/honor-criteria?id=${selectedCriteria.id}`
        : '/api/honor-criteria';

      const method = selectedCriteria ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) throw new Error('Failed to save criteria');

      const data = await response.json();
      toast.success(data.message);

      fetchCriteria();
      setIsAddDialogOpen(false);
      setIsEditDialogOpen(false);
      setSelectedCriteria(null);
    } catch (error) {
      console.error('Error saving criteria:', error);
      toast.error('حدث خطأ أثناء حفظ معيار التقييم');
    }
  };

  const handleDelete = async () => {
    if (!selectedCriteria) return;

    try {
      const response = await fetch(`/api/honor-criteria?id=${selectedCriteria.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete criteria');

      const data = await response.json();
      toast.success(data.message);

      fetchCriteria();
      setIsDeleteDialogOpen(false);
      setSelectedCriteria(null);
    } catch (error) {
      console.error('Error deleting criteria:', error);
      toast.error('حدث خطأ أثناء حذف معيار التقييم');
    }
  };

  const openEditDialog = (criteria: HonorCriteria) => {
    setSelectedCriteria(criteria);
    setFormData({
      name: criteria.name,
      description: criteria.description,
      pointsThreshold: criteria.pointsThreshold,
      isActive: criteria.isActive
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (criteria: HonorCriteria) => {
    setSelectedCriteria(criteria);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      pointsThreshold: 0,
      isActive: true
    });
  };

  return (
    <ProtectedRoute requiredPermission="admin.honor-board.criteria.view">
      <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Link href="/admin/honor-board" className="flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)]">
            <ArrowRight className="h-5 w-5 ml-1" />
            <span>العودة إلى لوحة الشرف</span>
          </Link>
          <h1 className="text-2xl font-bold mr-4">معايير التقييم للطلاب المتميزين</h1>
        </div>
        <Button
          onClick={() => {
            resetForm();
            setIsAddDialogOpen(true);
          }}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
        >
          <Plus className="ml-2" size={16} />
          إضافة معيار جديد
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      ) : criteria.length === 0 ? (
        <div className="text-center p-8 border rounded-lg">
          <p className="text-gray-500">لا توجد معايير تقييم حتى الآن</p>
          <Button
            onClick={() => {
              resetForm();
              setIsAddDialogOpen(true);
            }}
            variant="outline"
            className="mt-4"
          >
            إضافة معيار جديد
          </Button>
        </div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">المعيار</TableHead>
              <TableHead className="text-right">الوصف</TableHead>
              <TableHead className="text-right">الحد الأدنى للنقاط</TableHead>
              <TableHead className="text-right">الحالة</TableHead>
              <TableHead className="text-right">الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {criteria.map((criterion) => (
              <TableRow key={criterion.id}>
                <TableCell className="font-medium">{criterion.name}</TableCell>
                <TableCell>{criterion.description}</TableCell>
                <TableCell>{criterion.pointsThreshold}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs ${criterion.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                    {criterion.isActive ? 'نشط' : 'غير نشط'}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2 space-x-reverse">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(criterion)}
                    >
                      <Pencil className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => openDeleteDialog(criterion)}
                    >
                      <Trash2 className="h-4 w-4 ml-1" />
                      حذف
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {/* إضافة معيار جديد */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>إضافة معيار تقييم جديد</DialogTitle>
            <DialogDescription>
              أدخل تفاصيل معيار التقييم الجديد للطلاب المتميزين.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right col-span-1">
                اسم المعيار
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right col-span-1">
                الوصف
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="pointsThreshold" className="text-right col-span-1">
                الحد الأدنى للنقاط
              </Label>
              <Input
                id="pointsThreshold"
                name="pointsThreshold"
                type="number"
                value={formData.pointsThreshold}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="isActive" className="text-right col-span-1">
                نشط
              </Label>
              <Input
                id="isActive"
                name="isActive"
                type="checkbox"
                checked={formData.isActive}
                onChange={handleCheckboxChange}
                className="col-span-3 w-6 h-6"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              إلغاء
            </Button>
            <Button type="button" onClick={handleSubmit} className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
              حفظ
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* تعديل معيار */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تعديل معيار التقييم</DialogTitle>
            <DialogDescription>
              قم بتعديل تفاصيل معيار التقييم.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right col-span-1">
                اسم المعيار
              </Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right col-span-1">
                الوصف
              </Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-pointsThreshold" className="text-right col-span-1">
                الحد الأدنى للنقاط
              </Label>
              <Input
                id="edit-pointsThreshold"
                name="pointsThreshold"
                type="number"
                value={formData.pointsThreshold}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-isActive" className="text-right col-span-1">
                نشط
              </Label>
              <Input
                id="edit-isActive"
                name="isActive"
                type="checkbox"
                checked={formData.isActive}
                onChange={handleCheckboxChange}
                className="col-span-3 w-6 h-6"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              إلغاء
            </Button>
            <Button type="button" onClick={handleSubmit} className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
              حفظ التغييرات
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حذف معيار */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>حذف معيار التقييم</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من رغبتك في حذف هذا المعيار؟ لا يمكن التراجع عن هذا الإجراء.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              إلغاء
            </Button>
            <Button type="button" variant="destructive" onClick={handleDelete}>
              حذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </ProtectedRoute>
  );
}

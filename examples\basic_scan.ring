/*
==============================================================================
    مثال أساسي لاستخدام مكتبة Praetorian.ring
    
    الوصف: مثال يوضح كيفية استخدام الوظائف الأساسية للمكتبة
    المؤلف: Praetorian Team
==============================================================================
*/

# تحميل مكتبة Praetorian
load "../praetorian.ring"

/*
==============================================================================
    الدالة الرئيسية
==============================================================================
*/

func main
    # إنشاء مثيل من المكتبة
    oPraetorian = CreatePraetorian()
    
    # طباعة معلومات المكتبة
    oPraetorian.printInfo()
    
    # تعيين الهدف للفحص
    cTarget = "127.0.0.1"  # يمكنك تغيير هذا إلى الهدف المطلوب
    
    ? ""
    ? "=============================================="
    ? "بدء الفحص الأساسي للهدف: " + cTarget
    ? "=============================================="
    
    # 1. فحص المنافذ الشائعة
    ? ""
    ? "=== فحص المنافذ الشائعة ==="
    aOpenPorts = oPraetorian.Network.Scanner.scanCommonPorts(cTarget)
    
    if len(aOpenPorts) > 0
        ? "تم العثور على " + len(aOpenPorts) + " منفذ مفتوح:"
        for nPort in aOpenPorts
            ? "  - المنفذ " + nPort + " مفتوح"
        next
    else
        ? "لم يتم العثور على منافذ مفتوحة"
    ok
    
    # 2. فحص شامل مع جلب البانر (إذا وجدت منافذ مفتوحة)
    if len(aOpenPorts) > 0
        ? ""
        ? "=== فحص شامل مع جلب البانر ==="
        aResults = oPraetorian.Network.Scanner.comprehensiveScan(cTarget, aOpenPorts)
        oPraetorian.Network.Scanner.printScanReport(cTarget, aResults)
    ok
    
    # 3. فحص ويب (إذا كان المنفذ 80 أو 443 مفتوحاً)
    if find(aOpenPorts, 80) or find(aOpenPorts, 443)
        ? ""
        ? "=== فحص تطبيق الويب ==="
        
        # تحديد البروتوكول
        cProtocol = find(aOpenPorts, 443) ? "https" : "http"
        cBaseURL = cProtocol + "://" + cTarget
        
        # فحص HTTP أساسي
        ? "إرسال طلب HTTP إلى " + cBaseURL
        oResponse = oPraetorian.Web.HTTPClient.get(cBaseURL, NULL)
        
        if oResponse[:success]
            ? "نجح الطلب - كود الاستجابة: " + oResponse[:status_code]
            ? "نوع المحتوى: " + oResponse[:content_type]
            ? "حجم المحتوى: " + oResponse[:content_length] + " بايت"
        else
            ? "فشل الطلب"
        ok
        
        # Fuzzing سريع
        ? ""
        ? "=== Fuzzing سريع ==="
        aFuzzReport = oPraetorian.Web.Fuzzer.quickFuzz(cBaseURL)
        oPraetorian.Web.Fuzzer.printFuzzReport(aFuzzReport)
    ok
    
    # 4. فحص SSL (إذا كان المنفذ 443 مفتوحاً)
    if find(aOpenPorts, 443)
        ? ""
        ? "=== فحص SSL/TLS ==="
        aSSLReport = oPraetorian.Crypto.SSLChecker.comprehensiveSSLCheck(cTarget, 443)
        printSSLReport(aSSLReport)
    ok
    
    ? ""
    ? "=============================================="
    ? "انتهى الفحص الأساسي"
    ? "=============================================="

/*
==============================================================================
    دالة طباعة تقرير SSL
==============================================================================
*/

func printSSLReport aReport
    ? ""
    ? "تقرير فحص SSL للخادم: " + aReport[:host] + ":" + aReport[:port]
    ? "----------------------------------------"
    
    if not aReport[:ssl_available]
        ? "SSL/TLS غير متاح على هذا المنفذ"
        return
    ok
    
    # معلومات الشهادة
    aCert = aReport[:certificate]
    if len(aCert) > 0 and aCert[:is_valid]
        ? "معلومات الشهادة:"
        ? "  الموضوع: " + aCert[:subject]
        ? "  المصدر: " + aCert[:issuer]
        ? "  صالحة من: " + aCert[:valid_from]
        ? "  صالحة حتى: " + aCert[:valid_to]
        ? "  خوارزمية التوقيع: " + aCert[:signature_algorithm]
        ? "  حجم المفتاح: " + aCert[:key_size] + " بت"
        ? "  الأيام المتبقية: " + aCert[:days_until_expiry]
    ok
    
    # البروتوكولات المدعومة
    if len(aReport[:supported_protocols]) > 0
        ? ""
        ? "البروتوكولات المدعومة:"
        for cProtocol in aReport[:supported_protocols]
            ? "  - " + cProtocol
        next
    ok
    
    # الشيفرات الضعيفة
    if len(aReport[:weak_ciphers]) > 0
        ? ""
        ? "تحذير - شيفرات ضعيفة مدعومة:"
        for cCipher in aReport[:weak_ciphers]
            ? "  - " + cCipher
        next
    ok
    
    # المشاكل الأمنية
    if len(aReport[:security_issues]) > 0
        ? ""
        ? "المشاكل الأمنية:"
        for cIssue in aReport[:security_issues]
            ? "  ⚠ " + cIssue
        next
    ok
    
    # التوصيات
    if len(aReport[:recommendations]) > 0
        ? ""
        ? "التوصيات:"
        for cRecommendation in aReport[:recommendations]
            ? "  💡 " + cRecommendation
        next
    ok

/*
==============================================================================
    مثال لاستخدام الأدوات المساعدة
==============================================================================
*/

func demonstrateUtils
    ? ""
    ? "=============================================="
    ? "مثال على استخدام الأدوات المساعدة"
    ? "=============================================="
    
    # تشفير وفك تشفير Base64
    cOriginalText = "مرحباً بك في مكتبة Praetorian"
    cEncoded = PraetorianUtilsInstance.base64Encode(cOriginalText)
    cDecoded = PraetorianUtilsInstance.base64Decode(cEncoded)
    
    ? "النص الأصلي: " + cOriginalText
    ? "مشفر Base64: " + cEncoded
    ? "فك التشفير: " + cDecoded
    
    # توليد كلمة مرور قوية
    cPassword = PraetorianUtilsInstance.generateStrongPassword(12)
    ? "كلمة مرور قوية: " + cPassword
    
    # حساب hash
    cHash = PraetorianUtilsInstance.getSHA256Hash(cOriginalText)
    ? "SHA256 Hash: " + cHash
    
    # التحقق من صحة IP
    cTestIP = "***********"
    bValidIP = PraetorianUtilsInstance.isValidIP(cTestIP)
    ? "هل " + cTestIP + " عنوان IP صحيح؟ " + (bValidIP ? "نعم" : "لا")

/*
==============================================================================
    مثال لاستخدام نظام التسجيل
==============================================================================
*/

func demonstrateLogging
    ? ""
    ? "=============================================="
    ? "مثال على استخدام نظام التسجيل"
    ? "=============================================="
    
    # إنشاء مثيل من نظام التسجيل
    oLogger = new PraetorianLogger
    
    # تعيين مستوى التسجيل
    oLogger.setLogLevel(LOG_LEVEL_DEBUG)
    
    # تسجيل رسائل مختلفة
    oLogger.debug("هذه رسالة تصحيح")
    oLogger.info("هذه رسالة معلومات")
    oLogger.warning("هذه رسالة تحذير")
    oLogger.error("هذه رسالة خطأ")
    
    # تسجيل عملية
    oLogger.startOperation("عملية تجريبية")
    oLogger.logOperationResult("عملية تجريبية", true, "نجحت العملية")
    oLogger.endOperation("عملية تجريبية")
    
    # طباعة إحصائيات السجل
    oLogger.printLogStats()

/*
==============================================================================
    تشغيل الأمثلة
==============================================================================
*/

# تشغيل المثال الرئيسي
main()

# تشغيل أمثلة إضافية
demonstrateUtils()
demonstrateLogging()

? ""
? "تم الانتهاء من جميع الأمثلة!"
? "يمكنك مراجعة ملف السجل 'praetorian.log' لمزيد من التفاصيل."

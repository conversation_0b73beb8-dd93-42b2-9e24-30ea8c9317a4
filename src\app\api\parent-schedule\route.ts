import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';

// GET /api/parent-schedule - جلب جدول حصص أبناء ولي الأمر المسجل دخوله
export async function GET(request: NextRequest) {
  try {
    // الحصول على معرف المستخدم من التوكن
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || userData.role !== 'PARENT') {
      return NextResponse.json(
        { message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المستخدم والملف الشخصي
    const user = await prisma.user.findUnique({
      where: {
        id: userData.id
      },
      include: {
        profile: true
      }
    });

    if (!user || !user.profile) {
      console.error('لم يتم العثور على المستخدم أو الملف الشخصي:', userData.id);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات المستخدم" },
        { status: 404 }
      );
    }

    // جلب معلومات ولي الأمر باستخدام اسم الملف الشخصي أو اسم المستخدم
    const parent = await prisma.parent.findFirst({
      where: {
        OR: [
          { name: user.profile.name },
          { name: userData.username }
        ]
      },
      include: {
        students: true
      }
    });

    if (!parent) {
      console.error('لم يتم العثور على ولي الأمر للمستخدم:', userData.username, 'مع الملف الشخصي:', user.profile.name);
      return NextResponse.json(
        { message: "لم يتم العثور على بيانات ولي الأمر" },
        { status: 404 }
      );
    }

    // التحقق من وجود أبناء
    if (!parent.students || parent.students.length === 0) {
      console.log('ولي الأمر موجود ولكن لا يوجد أبناء مسجلين:', parent.name);
      return NextResponse.json({
        children: [],
        message: "لا يوجد أبناء مسجلين لولي الأمر"
      });
    }

    // التحقق من وجود معرف الطالب في الاستعلام
    const searchParams = request.nextUrl.searchParams;
    const childId = searchParams.get('childId');

    // إذا تم تحديد معرف الطالب، تحقق من أنه ينتمي لولي الأمر
    if (childId) {
      const childIdNum = parseInt(childId);
      const isParentChild = parent.students.some(student => student.id === childIdNum);

      if (!isParentChild) {
        return NextResponse.json(
          { message: "غير مصرح بالوصول إلى بيانات هذا الطالب" },
          { status: 403 }
        );
      }

      // جلب بيانات الطالب المحدد
      const student = await prisma.student.findUnique({
        where: { id: childIdNum },
        include: {
          classe: true
        }
      });

      if (!student) {
        return NextResponse.json(
          { message: "لم يتم العثور على بيانات الطالب" },
          { status: 404 }
        );
      }

      // جلب جدول الحصص للفصل الذي ينتمي إليه الطالب
      const classSchedule = await prisma.classSchedule.findMany({
        where: {
          classeId: student.classeId ?? undefined
        },
        include: {
          teacherSubject: {
            include: {
              subject: true,
              teacher: true
            }
          }
        },
        orderBy: [
          { day: 'asc' },
          { startTime: 'asc' }
        ]
      });

      // تنسيق البيانات للعرض
      const schedule = classSchedule.map(cs => ({
        id: cs.id,
        day: cs.day,
        startTime: cs.startTime,
        endTime: cs.endTime,
        subjectId: cs.teacherSubject.subject.id,
        subjectName: cs.teacherSubject.subject.name,
        teacherId: cs.teacherSubject.teacher.id,
        teacherName: cs.teacherSubject.teacher.name
      }));

      return NextResponse.json({
        student: {
          id: student.id,
          name: student.name,
          grade: student.classe?.name || 'غير محدد'
        },
        schedule,
        message: "تم جلب جدول الحصص بنجاح"
      });
    } else {
      // جلب جدول حصص جميع الأبناء
      const childrenSchedules = await Promise.all(parent.students.map(async (student) => {
        // جلب بيانات الطالب
        const studentData = await prisma.student.findUnique({
          where: { id: student.id },
          include: {
            classe: true
          }
        });

        if (!studentData || !studentData.classeId) return null;

        // جلب جدول الحصص للفصل
        const classSchedule = await prisma.classSchedule.findMany({
          where: {
            classeId: studentData.classeId ?? undefined
          },
          include: {
            teacherSubject: {
              include: {
                subject: true,
                teacher: true
              }
            }
          },
          orderBy: [
            { day: 'asc' },
            { startTime: 'asc' }
          ]
        });

        // تنسيق البيانات للعرض
        const schedule = classSchedule.map(cs => ({
          id: cs.id,
          day: cs.day,
          startTime: cs.startTime,
          endTime: cs.endTime,
          subjectId: cs.teacherSubject.subject.id,
          subjectName: cs.teacherSubject.subject.name,
          teacherId: cs.teacherSubject.teacher.id,
          teacherName: cs.teacherSubject.teacher.name
        }));

        return {
          id: student.id,
          name: student.name,
          grade: studentData.classe?.name || 'غير محدد',
          schedule
        };
      }));

      // فلترة القيم null
      const children = childrenSchedules.filter(child => child !== null);

      return NextResponse.json({
        children,
        message: "تم جلب جدول الحصص بنجاح"
      });
    }
  } catch (error) {
    console.error('Error fetching parent schedule:', error);
    // إضافة المزيد من التفاصيل للخطأ
    const errorMessage = error instanceof Error ? error.message : 'حدث خطأ غير معروف';
    return NextResponse.json(
      {
        message: "حدث خطأ أثناء جلب جدول الحصص",
        error: errorMessage
      },
      { status: 500 }
    );
  }
}

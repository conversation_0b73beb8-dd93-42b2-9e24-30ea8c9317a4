import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getToken } from '@/lib/auth';
import { UserRole } from '@prisma/client';

// واجهة ملخص مدفوعات الطالب
interface StudentPaymentSummary {
  id: number;
  name: string;
  grade: string;
  totalRequired: number;
  totalPaid: number;
  totalRemaining: number;
  dueInvoices: number;
  lastPaymentDate?: string;
  paymentStatus: 'PAID' | 'PARTIAL' | 'UNPAID' | 'OVERDUE';
}

// واجهة ملخص مدفوعات الولي
interface ParentPaymentSummary {
  id: string;
  name: string;
  phone: string;
  email?: string;
  totalRequired: number;
  totalPaid: number;
  totalRemaining: number;
  totalStudents: number;
  lastPaymentDate?: string;
  paymentRate: number;
  // 🆕 حقول جديدة لتفاصيل مصادر المبالغ
  baseAmount: number;           // المبلغ الأساسي من amountPerStudent
  invoicesAmount: number;       // المبلغ من الفواتير
  amountPerStudent?: number;    // المبلغ المحدد لكل تلميذ
  amountSource: 'BASE' | 'INVOICES' | 'BOTH'; // مصدر المبلغ
  students: StudentPaymentSummary[];
}

// GET /api/payments/by-parent - جلب المدفوعات مجمعة حسب الولي
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 بدء جلب المدفوعات حسب الولي...');

    // التحقق من التوكن والصلاحيات
    const token = request.cookies.get("jwtToken")?.value;
    if (!token) {
      return NextResponse.json(
        { error: "غير مصرح به" },
        { status: 401 }
      );
    }

    const userData = await getToken(token);
    if (!userData || (userData.role !== UserRole.ADMIN && userData.role !== UserRole.TEACHER)) {
      return NextResponse.json(
        { error: "غير مصرح به، يجب أن تكون مسؤول أو معلم" },
        { status: 401 }
      );
    }

    // استخراج معاملات البحث والفلترة
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const month = searchParams.get('month') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;

    console.log('📊 معاملات البحث:', { search, status, month, page, limit });

    // بناء شروط البحث
    const whereConditions: any = {};
    if (search) {
      whereConditions.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    // جلب جميع الأولياء مع بيانات أبنائهم والفواتير والمدفوعات
    console.log('📊 جاري جلب بيانات الأولياء...');
    const parents = await prisma.parent.findMany({
      where: whereConditions,
      select: {
        id: true,
        name: true,
        phone: true,
        email: true,
        amountPerStudent: true, // إضافة حقل المبلغ لكل تلميذ
        // جلب الفواتير الجماعية للولي
        invoices: {
          where: month ? {
            AND: [
              { type: 'FAMILY' }, // الفواتير الجماعية فقط
              {
                OR: [
                  {
                    issueDate: {
                      gte: new Date(`${month}-01`),
                      lt: new Date(new Date(`${month}-01`).getFullYear(), new Date(`${month}-01`).getMonth() + 1, 1)
                    }
                  },
                  {
                    dueDate: {
                      gte: new Date(`${month}-01`),
                      lt: new Date(new Date(`${month}-01`).getFullYear(), new Date(`${month}-01`).getMonth() + 1, 1)
                    }
                  }
                ]
              }
            ]
          } : {
            type: 'FAMILY'
          },
          include: {
            payments: {
              where: {
                status: 'PAID',
                ...(month && {
                  date: {
                    gte: new Date(`${month}-01`),
                    lt: new Date(new Date(`${month}-01`).getFullYear(), new Date(`${month}-01`).getMonth() + 1, 1)
                  }
                })
              },
              orderBy: {
                date: 'desc'
              }
            }
          }
        },
        students: {
          include: {
            classe: true,
            invoices: {
              where: month ? {
                // فلترة الفواتير حسب الشهر إذا تم تحديده
                AND: [
                  {
                    OR: [
                      { type: 'INDIVIDUAL' }, // الفواتير الفردية للطلاب
                      { type: { not: 'FAMILY' } } // استبعاد الفواتير الجماعية من فواتير الطلاب
                    ]
                  },
                  {
                    OR: [
                      {
                        issueDate: {
                          gte: new Date(`${month}-01`),
                          lt: new Date(new Date(`${month}-01`).getFullYear(), new Date(`${month}-01`).getMonth() + 1, 1)
                        }
                      },
                      {
                        dueDate: {
                          gte: new Date(`${month}-01`),
                          lt: new Date(new Date(`${month}-01`).getFullYear(), new Date(`${month}-01`).getMonth() + 1, 1)
                        }
                      }
                    ]
                  }
                ]
              } : {
                OR: [
                  { type: 'INDIVIDUAL' },
                  { type: { not: 'FAMILY' } }
                ]
              },
              include: {
                payments: {
                  where: {
                    status: 'PAID',
                    ...(month && {
                      date: {
                        gte: new Date(`${month}-01`),
                        lt: new Date(new Date(`${month}-01`).getFullYear(), new Date(`${month}-01`).getMonth() + 1, 1)
                      }
                    })
                  },
                  orderBy: {
                    date: 'desc'
                  }
                }
              }
            },
            payments: {
              where: {
                status: 'PAID',
                ...(month && {
                  date: {
                    gte: new Date(`${month}-01`),
                    lt: new Date(new Date(`${month}-01`).getFullYear(), new Date(`${month}-01`).getMonth() + 1, 1)
                  }
                })
              },
              orderBy: {
                date: 'desc'
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      },
      skip,
      take: limit
    });

    console.log(`📈 تم جلب ${parents.length} ولي أمر`);

    // معالجة البيانات وحساب الملخصات
    const parentSummaries: ParentPaymentSummary[] = await Promise.all(parents.map(async (parent) => {
      console.log(`📋 معالجة بيانات الولي: ${parent.name}`);

      let totalRequired = 0;
      let totalPaid = 0;
      let lastPaymentDate: Date | null = null;

      // 🆕 حساب المبلغ الأساسي من amountPerStudent
      const baseAmount = parent.amountPerStudent && parent.students.length > 0
        ? parent.amountPerStudent * parent.students.length
        : 0;

      console.log(`💰 المبلغ الأساسي للولي ${parent.name}:`, {
        amountPerStudent: parent.amountPerStudent,
        studentsCount: parent.students.length,
        baseAmount
      });

      // حساب الفواتير الجماعية للولي
      const familyInvoices = parent.invoices || [];
      const familyTotalRequired = familyInvoices
        .filter(invoice => invoice.status !== 'CANCELLED')
        .reduce((sum, invoice) => sum + invoice.amount, 0);

      // حساب المدفوعات للفواتير الجماعية بطريقة صحيحة
      let familyTotalPaid = 0;
      for (const invoice of familyInvoices) {
        const familyInvoicePayments = await prisma.payment.findMany({
          where: {
            invoiceId: invoice.id,
            status: 'PAID'
          }
        });
        const invoicePaid = familyInvoicePayments.reduce((sum, payment) => sum + payment.amount, 0);
        familyTotalPaid += invoicePaid;

        console.log(`💰 فاتورة جماعية ${invoice.id} للولي ${parent.name}:`, {
          amount: invoice.amount,
          totalPaid: invoicePaid,
          paymentsCount: familyInvoicePayments.length,
          remaining: invoice.amount - invoicePaid
        });
      }

      const studentSummaries: StudentPaymentSummary[] = parent.students.map(student => {
        // حساب إجمالي المبلغ المطلوب من الفواتير المستحقة فقط (الفردية)
        const studentTotalRequired = student.invoices
          .filter(invoice => invoice.status !== 'CANCELLED')
          .reduce((sum, invoice) => sum + invoice.amount, 0);

        // حساب إجمالي المبلغ المدفوع من المدفوعات المؤكدة فقط
        const studentTotalPaid = student.payments
          .filter(payment => payment.status === 'PAID')
          .reduce((sum, payment) => sum + payment.amount, 0);

        // حساب المبلغ المتبقي (فقط إذا كان هناك فواتير مستحقة)
        const studentTotalRemaining = Math.max(0, studentTotalRequired - studentTotalPaid);

        // عدد الفواتير المستحقة (غير مدفوعة أو مدفوعة جزئياً أو متأخرة)
        const dueInvoices = student.invoices.filter(invoice =>
          invoice.status === 'UNPAID' || invoice.status === 'PARTIALLY_PAID' || invoice.status === 'OVERDUE'
        ).length;

        // تاريخ آخر دفعة للطالب
        const studentLastPayment = student.payments[0];
        const studentLastPaymentDate = studentLastPayment ? studentLastPayment.date.toISOString().split('T')[0] : undefined;

        // تحديث تاريخ آخر دفعة للولي
        if (studentLastPayment && (!lastPaymentDate || studentLastPayment.date > lastPaymentDate)) {
          lastPaymentDate = studentLastPayment.date;
        }

        // تحديد حالة الدفع للطالب بناءً على الفواتير والمدفوعات
        let paymentStatus: 'PAID' | 'PARTIAL' | 'UNPAID' | 'OVERDUE' = 'UNPAID';

        if (studentTotalRequired === 0) {
          // لا توجد فواتير مستحقة
          paymentStatus = 'PAID';
        } else if (studentTotalPaid >= studentTotalRequired) {
          // مدفوع بالكامل
          paymentStatus = 'PAID';
        } else if (studentTotalPaid > 0) {
          // مدفوع جزئياً
          paymentStatus = 'PARTIAL';
        } else if (student.invoices.some(invoice => invoice.status === 'OVERDUE')) {
          // متأخر
          paymentStatus = 'OVERDUE';
        } else {
          // غير مدفوع
          paymentStatus = 'UNPAID';
        }

        // إضافة إلى الإجماليات
        totalRequired += studentTotalRequired;
        totalPaid += studentTotalPaid;

        return {
          id: student.id,
          name: student.name,
          grade: student.classe?.name || 'غير محدد',
          totalRequired: studentTotalRequired,
          totalPaid: studentTotalPaid,
          totalRemaining: studentTotalRemaining,
          dueInvoices,
          lastPaymentDate: studentLastPaymentDate,
          paymentStatus
        };
      });

      // إضافة الفواتير الجماعية للإجماليات
      totalRequired += familyTotalRequired;
      totalPaid += familyTotalPaid;

      // 🆕 إضافة المبلغ الأساسي للإجمالي المطلوب
      totalRequired += baseAmount;

      // تحديث تاريخ آخر دفعة من الفواتير الجماعية
      familyInvoices.forEach(invoice => {
        invoice.payments.forEach(payment => {
          if (!lastPaymentDate || payment.date > lastPaymentDate) {
            lastPaymentDate = payment.date;
          }
        });
      });

      // 🆕 حساب تفاصيل مصادر المبالغ
      const invoicesAmount = totalRequired - baseAmount; // المبلغ من الفواتير فقط
      const amountSource = baseAmount > 0 && invoicesAmount > 0 ? 'BOTH'
        : baseAmount > 0 ? 'BASE'
        : 'INVOICES';

      // حساب معدل السداد
      const paymentRate = totalRequired > 0 ? Math.round((totalPaid / totalRequired) * 100) : 0;

      console.log(`📊 ملخص الولي ${parent.name}:`, {
        baseAmount,
        invoicesAmount,
        totalRequired,
        totalPaid,
        amountSource,
        paymentRate
      });

      return {
        id: parent.id.toString(),
        name: parent.name,
        phone: parent.phone,
        email: parent.email || undefined,
        totalRequired,
        totalPaid,
        totalRemaining: Math.max(0, totalRequired - totalPaid),
        totalStudents: parent.students.length,
        lastPaymentDate: lastPaymentDate ? lastPaymentDate.toISOString().split('T')[0] : undefined,
        paymentRate,
        // 🆕 إضافة تفاصيل مصادر المبالغ
        baseAmount,
        invoicesAmount,
        amountPerStudent: parent.amountPerStudent,
        amountSource,
        students: studentSummaries,
        familyInvoices: await Promise.all(familyInvoices.map(async (invoice) => {
          // جلب جميع المدفوعات المرتبطة بهذه الفاتورة الجماعية
          const familyInvoicePayments = await prisma.payment.findMany({
            where: {
              invoiceId: invoice.id,
              status: 'PAID'
            }
          });

          const totalPaid = familyInvoicePayments.reduce((sum, payment) => sum + payment.amount, 0);

          return {
            id: invoice.id,
            amount: invoice.amount,
            dueDate: invoice.dueDate,
            description: invoice.description,
            status: invoice.status,
            totalPaid
          };
        }))
      };
    }));

    // فلترة حسب الحالة إذا تم تحديدها
    let filteredSummaries = parentSummaries;
    if (status) {
      filteredSummaries = parentSummaries.filter(parent => {
        switch (status) {
          case 'PAID':
            return parent.totalRequired === 0 || parent.totalRemaining <= 0;
          case 'PARTIAL':
            return parent.totalRequired > 0 && parent.totalPaid > 0 && parent.totalRemaining > 0;
          case 'UNPAID':
            return parent.totalRequired > 0 && parent.totalPaid === 0;
          case 'OVERDUE':
            return parent.students.some(student => student.paymentStatus === 'OVERDUE');
          default:
            return true;
        }
      });
    }

    // حساب إحصائيات عامة
    const totalParents = filteredSummaries.length;
    const parentsWithDebts = filteredSummaries.filter(p => p.totalRemaining > 0).length;
    const totalDebtAmount = filteredSummaries.reduce((sum, p) => sum + p.totalRemaining, 0);
    const totalPaidAmount = filteredSummaries.reduce((sum, p) => sum + p.totalPaid, 0);
    const averagePaymentRate = totalParents > 0
      ? Math.round(filteredSummaries.reduce((sum, p) => sum + p.paymentRate, 0) / totalParents)
      : 0;

    console.log('✅ تم معالجة البيانات بنجاح');
    console.log('📊 الإحصائيات:', {
      totalParents,
      parentsWithDebts,
      totalDebtAmount,
      totalPaidAmount,
      averagePaymentRate
    });

    return NextResponse.json({
      success: true,
      data: filteredSummaries,
      pagination: {
        page,
        limit,
        total: totalParents,
        totalPages: Math.ceil(totalParents / limit)
      },
      statistics: {
        totalParents,
        parentsWithDebts,
        totalDebtAmount,
        totalPaidAmount,
        averagePaymentRate
      }
    });

  } catch (error) {
    console.error('❌ خطأ في جلب المدفوعات حسب الولي:', error);
    return NextResponse.json(
      { error: 'فشل في جلب المدفوعات حسب الولي' },
      { status: 500 }
    );
  }
}

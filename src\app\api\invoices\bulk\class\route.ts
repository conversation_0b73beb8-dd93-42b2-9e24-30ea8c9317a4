import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { InvoiceStatus } from '@prisma/client';
import { ActivityLogger, ActivityType } from '@/utils/activity-logger';

// POST /api/invoices/bulk/class - إنشاء فواتير لجميع طلاب فصل معين
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { classId, amount, dueDate, month, year, description } = body;

    // التحقق من وجود الحقول المطلوبة
    if (!classId || !amount || !dueDate || !month || !year) {
      return NextResponse.json(
        { error: 'الحقول المطلوبة غير مكتملة' },
        { status: 400 }
      );
    }

    // التحقق من صحة المبلغ
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'قيمة المبلغ غير صحيحة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الفصل
    const classe = await prisma.classe.findUnique({
      where: { id: parseInt(classId.toString()) }
    });

    if (!classe) {
      return NextResponse.json(
        { error: 'الفصل غير موجود' },
        { status: 404 }
      );
    }

    // جلب جميع طلاب الفصل
    const students = await prisma.student.findMany({
      where: { classeId: parseInt(classId.toString()) }
    });

    if (students.length === 0) {
      return NextResponse.json(
        { error: 'لا يوجد طلاب في هذا الفصل' },
        { status: 404 }
      );
    }

    // إنشاء الفواتير
    const invoices = await prisma.$transaction(
      students.map(student => 
        prisma.invoice.create({
          data: {
            studentId: student.id,
            amount,
            dueDate: new Date(dueDate),
            month: parseInt(month.toString()),
            year: parseInt(year.toString()),
            description: description || `رسوم شهر ${month}/${year} - ${classe.name}`,
            status: 'UNPAID' as InvoiceStatus
          }
        })
      )
    );

    // تسجيل نشاط إنشاء الفواتير
    try {
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.INVOICE,
          `إنشاء ${invoices.length} فاتورة جديدة بقيمة ${amount} لطلاب فصل ${classe.name} لشهر ${month}/${year}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط إنشاء الفواتير:', error);
    }

    return NextResponse.json({
      success: true,
      count: invoices.length,
      invoices
    });
  } catch (error) {
    console.error('خطأ في إنشاء الفواتير:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الفواتير' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { verifyToken } from '@/utils/verifyToken';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

// POST /api/course-materials/update - تحديث مادة تعليمية
export async function POST(request: NextRequest) {
  try {
    // التحقق من المصادقة
    const userData = await verifyToken(request);
    if (!userData) {
      return NextResponse.json(
        { success: false, message: "غير مصرح به" },
        { status: 401 }
      );
    }

    // جلب معلومات المعلم
    const teacher = await prisma.teacher.findFirst({
      where: {
        userId: userData.id
      }
    });

    if (!teacher) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على بيانات المعلم" },
        { status: 404 }
      );
    }

    // استخراج البيانات من الطلب
    const formData = await request.formData();
    const materialId = parseInt(formData.get('materialId') as string);
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const type = formData.get('type') as string;
    const courseId = parseInt(formData.get('courseId') as string);

    // التحقق من البيانات المطلوبة
    if (!materialId || !title || !type || isNaN(courseId)) {
      return NextResponse.json(
        { success: false, message: "البيانات غير مكتملة" },
        { status: 400 }
      );
    }

    // جلب المادة التعليمية الحالية
    const existingMaterial = await prisma.courseMaterial.findUnique({
      where: {
        id: materialId
      },
      include: {
        classSubject: {
          include: {
            teacherSubject: true
          }
        }
      }
    });

    if (!existingMaterial) {
      return NextResponse.json(
        { success: false, message: "لم يتم العثور على المادة التعليمية" },
        { status: 404 }
      );
    }

    // التحقق من أن المادة التعليمية تنتمي للمعلم
    if (existingMaterial.classSubject.teacherSubject.teacherId !== teacher.id) {
      return NextResponse.json(
        { success: false, message: "غير مصرح لك بتعديل هذه المادة التعليمية" },
        { status: 403 }
      );
    }

    let url = existingMaterial.url;

    // معالجة الملف أو الرابط الجديد إذا تم تقديمه
    if (type === 'link') {
      const newUrl = formData.get('url') as string;
      if (newUrl) {
        url = newUrl;
      }
    } else {
      const file = formData.get('file') as File;
      if (file) {
        // إنشاء اسم فريد للملف
        const fileExtension = file.name.split('.').pop();
        const fileName = `${uuidv4()}.${fileExtension}`;
        const filePath = join(process.cwd(), 'public', 'uploads', fileName);

        // حفظ الملف
        const fileBuffer = await file.arrayBuffer();
        await writeFile(filePath, Buffer.from(fileBuffer));

        // تعيين مسار الملف الجديد
        url = `/uploads/${fileName}`;
      }
    }

    // تحديث المادة التعليمية في قاعدة البيانات
    const updatedMaterial = await prisma.courseMaterial.update({
      where: {
        id: materialId
      },
      data: {
        title,
        description,
        type,
        url
      }
    });

    // تنسيق البيانات للعرض
    const formattedMaterial = {
      id: updatedMaterial.id,
      title: updatedMaterial.title,
      description: updatedMaterial.description || '',
      type: updatedMaterial.type,
      url: updatedMaterial.url,
      createdAt: updatedMaterial.createdAt.toISOString().split('T')[0]
    };

    return NextResponse.json({
      success: true,
      material: formattedMaterial,
      message: "تم تحديث المادة التعليمية بنجاح"
    });
  } catch (error) {
    console.error('Error updating course material:', error);
    return NextResponse.json(
      { success: false, message: "حدث خطأ أثناء تحديث المادة التعليمية" },
      { status: 500 }
    );
  }
}

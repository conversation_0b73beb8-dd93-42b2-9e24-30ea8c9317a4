import {  NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import {  ActivityType } from '@/lib/activity-logger';

// POST /api/admin/seed-activities
// هذا API مخصص فقط للاختبار وإنشاء بيانات تجريبية للنشاطات
export async function POST() {
  try {
    // التحقق من وجود مستخدم أدمن
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      return NextResponse.json(
        { error: 'لا يوجد مستخدم أدمن في النظام' },
        { status: 404 }
      );
    }

    // إنشاء مجموعة من النشاطات المختلفة
    const activities = [
      {
        userId: adminUser.id,
        type: ActivityType.LOGIN,
        description: `تسجيل دخول المستخدم ${adminUser.username}`
      },
      {
        userId: adminUser.id,
        type: ActivityType.STUDENT_ADD,
        description: 'إضافة طالب جديد: أحمد محمد'
      },
      {
        userId: adminUser.id,
        type: ActivityType.PAYMENT,
        description: 'تسجيل دفعة بقيمة 5000 د.ج للطالب عبد الرحمن'
      },
      {
        userId: adminUser.id,
        type: ActivityType.ATTENDANCE,
        description: 'تسجيل حضور للطالب محمد في الحصة 2'
      },
      {
        userId: adminUser.id,
        type: ActivityType.EXAM,
        description: 'إنشاء امتحان جديد: امتحان حفظ القرآن'
      },
      {
        userId: adminUser.id,
        type: ActivityType.KHATM,
        description: 'إنشاء مجلس ختم جديد: ختم سورة البقرة'
      },
      {
        userId: adminUser.id,
        type: ActivityType.UPDATE,
        description: 'تحديث بيانات الطالب خالد'
      }
    ];

    // إنشاء النشاطات في قاعدة البيانات
    const createdActivities = await prisma.activity.createMany({
      data: activities
    });

    return NextResponse.json({
      success: true,
      message: `تم إنشاء ${createdActivities.count} نشاط بنجاح`,
      count: createdActivities.count
    });
  } catch (error) {
    console.error('Error seeding activities:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء النشاطات التجريبية' },
      { status: 500 }
    );
  }
}

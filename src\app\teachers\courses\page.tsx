"use client";
import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FaBook, FaUserGraduate, FaArrowRight, FaEdit } from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface Course {
  id: number;
  subjectId: number;
  subjectName: string;
  className: string;
  classId: number;
  studentsCount: number;
  description: string;
}

const TeacherCoursesPage = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/teacher-courses');

        if (!response.ok) {
          throw new Error('فشل في جلب بيانات المقررات');
        }

        const data = await response.json();
        setCourses(data.courses);
      } catch (err) {
        console.error('Error fetching courses:', err);
        setError('حدث خطأ أثناء جلب بيانات المقررات');
        toast.error('فشل في جلب بيانات المقررات');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, []);

  return (
    <div className="container mx-auto py-6 space-y-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
            <FaBook className="text-[var(--primary-color)]" />
            المقررات الدراسية
          </h1>
          <p className="text-gray-500 mr-4 mt-2">عرض جميع المقررات والمواد الدراسية التي تقوم بتدريسها</p>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card className="border border-[#e0f2ef] shadow-md">
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : courses.length === 0 ? (
        <Card className="border border-[#e0f2ef] shadow-md">
          <CardContent className="pt-6">
            <div className="text-center text-gray-500 py-4">لا توجد مقررات متاحة</div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {courses.map((course) => (
            <Card key={course.id} className="overflow-hidden border border-[#e0f2ef] shadow-md hover:shadow-lg transition-shadow duration-300">
              <CardHeader className="bg-[#f8fffd] pb-2">
                <CardTitle className="flex items-center gap-2 text-[var(--primary-color)]">
                  <FaBook className="text-[var(--primary-color)]" />
                  <span>{course.subjectName}</span>
                </CardTitle>
                <CardDescription className="flex items-center gap-1 mt-1">
                  <FaUserGraduate className="text-gray-500" />
                  <span>{course.className} - {course.studentsCount} طالب</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {course.description || 'لا يوجد وصف متاح لهذه المادة.'}
                </p>
              </CardContent>
              <CardFooter className="bg-gray-50 flex justify-between">
                <Link href={`/teachers/courses/${course.id}/materials`}>
                  <Button variant="outline" size="sm" className="flex items-center gap-1 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white">
                    <FaEdit className="ml-1" />
                    إدارة المحتوى
                  </Button>
                </Link>
                <Link href={`/teachers/courses/${course.id}`}>
                  <Button variant="outline" size="sm" className="flex items-center gap-1 text-[var(--primary-color)] border-[var(--primary-color)] hover:bg-[var(--primary-color)] hover:text-white">
                    عرض التفاصيل
                    <FaArrowRight className="mr-1" />
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default TeacherCoursesPage;

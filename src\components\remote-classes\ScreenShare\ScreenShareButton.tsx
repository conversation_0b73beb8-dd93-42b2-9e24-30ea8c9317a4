'use client';

import React, { useState, useCallback } from 'react';
import { FaDesktop, FaStop } from 'react-icons/fa';
import { Button } from '@/components/ui/button';

// واجهة مخصصة تتضمن خاصية cursor
interface CustomDisplayMediaOptions extends DisplayMediaStreamOptions {
  video?: boolean | {
    cursor?: string;
    displaySurface?: string;
  };
}

interface ScreenShareButtonProps {
  onStartSharing: (stream: MediaStream) => void;
  onStopSharing: () => void;
  isSharing: boolean;
  disabled?: boolean;
  className?: string;
}

/**
 * Button component for starting and stopping screen sharing
 */
const ScreenShareButton: React.FC<ScreenShareButtonProps> = ({
  onStartSharing,
  onStopSharing,
  isSharing,
  disabled = false,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Handle starting screen sharing
   */
  const handleStartSharing = useCallback(async () => {
    if (isSharing || disabled || isLoading) return;

    setIsLoading(true);
    try {
      // Request screen sharing permission and get the stream
      const options: CustomDisplayMediaOptions = {
        video: {
          cursor: 'always',
          displaySurface: 'window',
        },
        audio: false,
      };
      const stream = await navigator.mediaDevices.getDisplayMedia(options);

      // Handle when user stops sharing via browser UI
      stream.getVideoTracks()[0].onended = () => {
        onStopSharing();
      };

      // Pass the stream to the parent component
      onStartSharing(stream);
    } catch (error) {
      console.error('Error starting screen share:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isSharing, disabled, isLoading, onStartSharing, onStopSharing]);

  /**
   * Handle stopping screen sharing
   */
  const handleStopSharing = useCallback(() => {
    if (!isSharing || disabled || isLoading) return;

    onStopSharing();
  }, [isSharing, disabled, isLoading, onStopSharing]);

  return (
    <Button
      onClick={isSharing ? handleStopSharing : handleStartSharing}
      disabled={disabled || isLoading}
      className={`flex items-center gap-2 ${isSharing ? 'bg-red-500 hover:bg-red-600' : 'bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]'} ${className}`}
      aria-label={isSharing ? 'إيقاف مشاركة الشاشة' : 'مشاركة الشاشة'}
      title={isSharing ? 'إيقاف مشاركة الشاشة' : 'مشاركة الشاشة'}
    >
      {isSharing ? (
        <>
          <FaStop className="text-white" />
          <span>إيقاف المشاركة</span>
        </>
      ) : (
        <>
          <FaDesktop className="text-white" />
          <span>مشاركة الشاشة</span>
        </>
      )}
    </Button>
  );
};

export default ScreenShareButton;

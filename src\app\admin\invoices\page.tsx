'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DatePicker } from 'antd';
import { Spin, message, Popconfirm, Tag } from 'antd';
import dayjs from 'dayjs';
import { FaFileInvoiceDollar, FaSearch, FaFilter, FaEdit, FaTrash, FaFilePdf, FaBell, FaPlus, FaPrint } from 'react-icons/fa';
import InvoiceModal from './InvoiceModal';
import BulkInvoiceModal from './BulkInvoiceModal';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';
interface Payment {
  id: number;
  amount: number;
  date: string;
  status: string;
}

interface Invoice {
  id: number;
  studentId?: number;
  parentId?: number;
  student?: {
    id: number;
    name: string;
  };
  parent?: {
    id: number;
    name: string;
  };
  amount: number;
  dueDate: string;
  issueDate: string;
  month: number;
  year: number;
  description?: string;
  status: string;
  payments: Payment[];
  paidAmount: number;
  remainingAmount: number;
  remindersSent: number;
  lastReminderDate?: string;
  notes?: string;
}

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedMonth, setSelectedMonth] = useState<dayjs.Dayjs | null>(dayjs());
  const [pagination, setPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
  });
  const [isInvoiceModalVisible, setIsInvoiceModalVisible] = useState(false);
  const [isBulkInvoiceModalVisible, setIsBulkInvoiceModalVisible] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  // جلب الفواتير
  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: pagination.current.toString(),
        query: searchQuery,
        status: statusFilter,
        month: selectedMonth ? selectedMonth.format('M') : '',
        year: selectedMonth ? selectedMonth.format('YYYY') : ''
      });

      const response = await fetch(`/api/invoices?${queryParams}`);
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      setInvoices(data.invoices);
      setPagination({
        ...pagination,
        total: data.pagination.total,
      });
    } catch (error: Error | unknown) {
      console.error('Error fetching invoices:', error);
      message.error('فشل في جلب الفواتير');
    } finally {
      setLoading(false);
    }
  };

  // جلب الفواتير عند تحميل الصفحة أو تغيير المعايير
  useEffect(() => {
    fetchInvoices();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.current, statusFilter, selectedMonth]);

  // البحث عن الفواتير
  const handleSearch = () => {
    setPagination({ ...pagination, current: 1 });
    fetchInvoices();
  };

  // إعادة تعيين المرشحات
  const resetFilters = () => {
    setSearchQuery('');
    setStatusFilter('');
    setSelectedMonth(dayjs());
    setPagination({ ...pagination, current: 1 });
  };

  // حذف فاتورة
  const handleDeleteInvoice = async (id: number) => {
    try {
      const response = await fetch(`/api/invoices?id=${id}`, {
        method: 'DELETE',
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      message.success('تم حذف الفاتورة بنجاح');
      fetchInvoices();
    } catch (error: Error | unknown) {
      console.error('Error deleting invoice:', error);
      message.error(error instanceof Error ? error.message : 'فشل في حذف الفاتورة');
    }
  };

  // إرسال تذكير للفاتورة
  const handleSendReminder = async (id: number) => {
    try {
      const response = await fetch('/api/invoices/reminders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ invoiceIds: [id] }),
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      message.success('تم إرسال التذكير بنجاح');
      fetchInvoices();
    } catch (error: Error | unknown) {
      console.error('Error sending reminder:', error);
      message.error(error instanceof Error ? error.message : 'فشل في إرسال التذكير');
    }
  };

  // عرض ملف PDF للفاتورة
  const handleViewPdf = (id: number) => {
    window.open(`/api/invoices/pdf/${id}`, '_blank');
  };

  // طباعة الفاتورة
  const handlePrintInvoice = (id: number) => {
    const printWindow = window.open(`/api/invoices/pdf/${id}`, '_blank');
    if (printWindow) {
      printWindow.onload = () => {
        printWindow.print();
      };
    } else {
      message.error('تعذر فتح نافذة الطباعة. يرجى التحقق من إعدادات المتصفح.');
    }
  };

  // تحديد لون حالة الفاتورة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'green';
      case 'PARTIALLY_PAID':
        return 'blue';
      case 'UNPAID':
        return 'gold';
      case 'OVERDUE':
        return 'red';
      case 'CANCELLED':
        return 'gray';
      default:
        return 'default';
    }
  };

  // تحويل حالة الفاتورة إلى العربية
  const getStatusText = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'مدفوعة';
      case 'PARTIALLY_PAID':
        return 'مدفوعة جزئيًا';
      case 'UNPAID':
        return 'غير مدفوعة';
      case 'OVERDUE':
        return 'متأخرة';
      case 'CANCELLED':
        return 'ملغاة';
      default:
        return status;
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.invoices.view">
      <div className="p-4 md:p-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2">
          <FaFileInvoiceDollar className="text-[var(--primary-color)]" />
          الفواتير الإلكترونية
        </h1>
        <QuickActionButtons
          entityType="invoices"
          actions={[
            {
              key: 'create',
              label: 'إنشاء فاتورة',
              icon: <FaPlus />,
              onClick: () => setIsInvoiceModalVisible(true),
              variant: 'primary'
            },
            {
              key: 'bulk-create',
              label: 'إنشاء فواتير متعددة',
              icon: <FaPlus />,
              onClick: () => setIsBulkInvoiceModalVisible(true),
              variant: 'primary'
            }
          ]}
          className="flex flex-col sm:flex-row gap-2 w-full md:w-auto"
        />

        {/* مكونات النوافذ المنبثقة */}
        <InvoiceModal
          isOpen={isInvoiceModalVisible}
          onCloseAction={() => {
            setIsInvoiceModalVisible(false);
            setSelectedInvoice(null);
          }}
          onSuccessAction={fetchInvoices}
          invoice={selectedInvoice}
        />
        <BulkInvoiceModal
          isOpen={isBulkInvoiceModalVisible}
          onCloseAction={() => setIsBulkInvoiceModalVisible(false)}
          onSuccessAction={fetchInvoices}
        />
      </div>

      {/* قسم البحث والتصفية */}
      <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">بحث</label>
              <div className="relative">
                <Input
                  placeholder="ابحث باسم الطالب..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="جميع الحالات" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">جميع الحالات</SelectItem>
                  <SelectItem value="PAID">مدفوعة</SelectItem>
                  <SelectItem value="PARTIALLY_PAID">مدفوعة جزئيًا</SelectItem>
                  <SelectItem value="UNPAID">غير مدفوعة</SelectItem>
                  <SelectItem value="OVERDUE">متأخرة</SelectItem>
                  <SelectItem value="CANCELLED">ملغاة</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الشهر/السنة</label>
              <DatePicker
                picker="month"
                value={selectedMonth}
                onChange={setSelectedMonth}
                format="MM/YYYY"
                allowClear
                placeholder="اختر الشهر"
                style={{ width: '100%' }}
              />
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 justify-center sm:justify-start">
            <Button
              onClick={handleSearch}
              className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] w-full sm:w-auto"
            >
              <FaSearch className="mr-1" />
              بحث
            </Button>
            <Button
              onClick={resetFilters}
              variant="outline"
              className="w-full sm:w-auto"
            >
              <FaFilter className="mr-1" />
              إعادة تعيين
            </Button>
          </div>
        </div>
      </div>

      {/* جدول الفواتير */}
      <div className="responsive-table-container">
        <table className="min-w-full bg-white border border-green-100 rounded-lg card-mode-table">
          <thead>
            <tr className="bg-[var(--primary-color)]">
              <th className="px-6 py-3 border-b text-right text-white font-bold">رقم الفاتورة</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">اسم الطالب</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">المبلغ</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">المدفوع</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">المتبقي</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold hide-on-mobile">تاريخ الإصدار</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">تاريخ الاستحقاق</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">الشهر/السنة</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">الحالة</th>
              <th className="px-6 py-3 border-b text-right text-white font-bold">الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={10} className="text-center py-4">
                  <Spin />
                </td>
              </tr>
            ) : invoices.length === 0 ? (
              <tr>
                <td colSpan={10} className="text-center py-4">لا توجد فواتير</td>
              </tr>
            ) : (
              invoices.map((invoice) => (
                <tr key={invoice.id} className="hover:bg-green-50/50 transition-colors duration-150">
                  <td className="px-6 py-4 border-b" data-label="رقم الفاتورة">{invoice.id}</td>
                  <td className="px-6 py-4 border-b" data-label="اسم الطالب">
                    {invoice.student ? invoice.student.name : (invoice.parent ? `فاتورة جماعية - ${invoice.parent.name}` : 'غير محدد')}
                  </td>
                  <td className="px-6 py-4 border-b" data-label="المبلغ">{invoice.amount} دج</td>
                  <td className="px-6 py-4 border-b" data-label="المدفوع">{invoice.paidAmount} دج</td>
                  <td className="px-6 py-4 border-b" data-label="المتبقي">{invoice.remainingAmount} دج</td>
                  <td className="px-6 py-4 border-b hide-on-mobile" data-label="تاريخ الإصدار">{new Date(invoice.issueDate).toLocaleDateString('ar-DZ')}</td>
                  <td className="px-6 py-4 border-b" data-label="تاريخ الاستحقاق">{new Date(invoice.dueDate).toLocaleDateString('ar-DZ')}</td>
                  <td className="px-6 py-4 border-b" data-label="الشهر/السنة">{invoice.month}/{invoice.year}</td>
                  <td className="px-6 py-4 border-b" data-label="الحالة">
                    <Tag color={getStatusColor(invoice.status)}>
                      {getStatusText(invoice.status)}
                    </Tag>
                  </td>
                  <td className="px-6 py-4 border-b actions" data-label="الإجراءات">
                    <div className="flex gap-2 mobile-action-buttons">
                      <OptimizedActionButtonGroup
                        entityType="invoices"
                        onEdit={() => {
                          setSelectedInvoice(invoice);
                          setIsInvoiceModalVisible(true);
                        }}
                        onDelete={() => handleDeleteInvoice(invoice.id)}
                        showEdit={true}
                        showDelete={invoice.payments.length === 0}
                        className="gap-2"
                        size="sm"
                      />
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleViewPdf(invoice.id)}
                        title="عرض PDF"
                      >
                        <FaFilePdf className="text-red-500" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handlePrintInvoice(invoice.id)}
                        title="طباعة الفاتورة"
                      >
                        <FaPrint className="text-blue-500" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleSendReminder(invoice.id)}
                        title="إرسال تذكير"
                        disabled={invoice.status === 'PAID' || invoice.status === 'CANCELLED'}
                      >
                        <FaBell className="text-yellow-500" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* التنقل بين الصفحات */}
      <div className="mt-4 flex flex-col sm:flex-row justify-center items-center gap-3">
        <div className="text-sm text-[var(--primary-color)] mb-2 sm:mb-0">
          الصفحة {pagination.current} من {Math.ceil(pagination.total / pagination.pageSize)}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPagination({ ...pagination, current: pagination.current - 1 })}
            disabled={pagination.current === 1 || loading}
            className="min-w-[80px]"
          >
            السابق
          </Button>
          <Button
            variant="outline"
            onClick={() => setPagination({ ...pagination, current: pagination.current + 1 })}
            disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize) || loading}
            className="min-w-[80px]"
          >
            التالي
          </Button>
        </div>
      </div>
      </div>
    </OptimizedProtectedRoute>
  );
}

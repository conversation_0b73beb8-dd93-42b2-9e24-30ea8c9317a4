import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/page-backgrounds/[pageName] - جلب خلفية صفحة محددة
export async function GET(
  request: NextRequest,
  { params }: { params: { pageName: string } }
) {
  try {
    const { pageName } = params;

    if (!pageName) {
      return NextResponse.json({
        success: false,
        error: 'اسم الصفحة مطلوب'
      }, { status: 400 });
    }

    const background = await prisma.pageBackground.findFirst({
      where: {
        pageName: pageName,
        isActive: true
      },
      orderBy: {
        priority: 'desc'
      }
    });

    if (!background) {
      return NextResponse.json({
        success: false,
        error: 'خلفية الصفحة غير موجودة أو غير نشطة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: background,
      message: 'تم جلب خلفية الصفحة بنجاح'
    });
  } catch (error) {
    console.error('Error fetching page background:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'حدث خطأ أثناء جلب خلفية الصفحة'
      },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'react-hot-toast';
import { EvaluationType } from '@prisma/client';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface EvaluationConfig {
  evaluationType: EvaluationType;
  weight: number;
  isRequired: boolean;
}

export default function EvaluationConfigPage() {
  const [configs, setConfigs] = useState<EvaluationConfig[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchConfigs();
  }, []);

  const fetchConfigs = async () => {
    try {
      const response = await fetch('/api/settings/evaluation-config');
      if (!response.ok) throw new Error('Failed to fetch configs');
      const data = await response.json();
      setConfigs(data);
    } catch (error: unknown) {
      console.error('Error fetching configs:', error);
      toast.error('حدث خطأ أثناء تحميل الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  const handleWeightChange = (index: number, value: string) => {
    const newConfigs = [...configs];
    newConfigs[index].weight = parseFloat(value) || 0;
    setConfigs(newConfigs);
  };

  const handleRequiredChange = (index: number, checked: boolean) => {
    const newConfigs = [...configs];
    newConfigs[index].isRequired = checked;
    setConfigs(newConfigs);
  };

  const handleSubmit = async () => {
    try {
      const response = await fetch('/api/settings/evaluation-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ evaluationConfigs: configs })
      });

      if (!response.ok) throw new Error('Failed to update configs');
      toast.success('تم حفظ الإعدادات بنجاح');
    } catch (error: unknown) {
      console.error('Error saving configs:', error);
      toast.error('حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const getEvaluationTypeLabel = (type: EvaluationType) => {
    const labels: Record<EvaluationType, string> = {
      WRITTEN_EXAM: 'امتحان تحريري',
      ORAL_EXAM: 'امتحان شفوي',
      HOMEWORK: 'واجب منزلي',
      PROJECT: 'مشروع',
      QURAN_RECITATION: 'تلاوة القرآن',
      QURAN_MEMORIZATION: 'حفظ القرآن',
      PRACTICAL_TEST: 'اختبار عملي',
      REMOTE_EXAM: 'امتحان عن بعد'
    };
    return labels[type] || type;
  };

  if (loading) return <div>جاري التحميل...</div>;

  return (
    <ProtectedRoute requiredPermission="admin.settings.evaluation-config.view">
      <Card>
      <CardHeader>
        <CardTitle>إعدادات التقييم</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {configs.map((config, index) => (
            <div key={config.evaluationType} className="flex items-center gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium">
                  {getEvaluationTypeLabel(config.evaluationType)}
                </label>
              </div>
              <Input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={config.weight}
                onChange={(e) => handleWeightChange(index, e.target.value)}
                className="w-24"
              />
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`required-${config.evaluationType}`}
                  checked={config.isRequired}
                  onCheckedChange={(checked) => handleRequiredChange(index, checked as boolean)}
                />
                <label htmlFor={`required-${config.evaluationType}`}>إلزامي</label>
              </div>
            </div>
          ))}
          <Button onClick={handleSubmit} className="mt-4">
            حفظ التغييرات
          </Button>
        </div>
      </CardContent>
      </Card>
    </ProtectedRoute>
  );
}
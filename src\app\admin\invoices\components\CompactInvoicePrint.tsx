'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';

import {
  FaPrint, FaDownload, FaEye, FaCog, FaReceipt,
  FaCompressAlt, FaLeaf, FaQrcode, FaImage
} from 'react-icons/fa';

// خيارات الفاتورة المدمجة
interface CompactInvoiceOptions {
  size: 'thermal' | 'half-a4' | 'business-card';
  includeQR: boolean;
  includeLogo: boolean;
  fontSize: 'small' | 'medium' | 'large';
  layout: 'vertical' | 'horizontal';
  paperSaving: boolean;
  highQuality: boolean;
}

interface CompactInvoicePrintProps {
  invoiceId: number;
  invoiceAmount: number;
  studentName: string;
  className?: string;
}

export default function CompactInvoicePrint({
  invoiceId,
  invoiceAmount,
  studentName,
  className = ''
}: CompactInvoicePrintProps) {
  const [options, setOptions] = useState<CompactInvoiceOptions>({
    size: 'thermal',
    includeQR: false,
    includeLogo: true,
    fontSize: 'medium',
    layout: 'vertical',
    paperSaving: false,
    highQuality: true
  });
  const [isOptionsOpen, setIsOptionsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { addToast } = useToast();

  // تنسيق العملة
  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString('fr-FR', { minimumFractionDigits: 2 })} دج`;
  };

  // معاينة الفاتورة المدمجة
  const handlePreview = async () => {
    try {
      setIsLoading(true);
      const queryParams = new URLSearchParams({
        ...options,
        includeQR: options.includeQR.toString(),
        includeLogo: options.includeLogo.toString(),
        paperSaving: options.paperSaving.toString(),
        highQuality: options.highQuality.toString(),
        preview: 'true'
      });

      const url = `/api/invoices/compact-pdf/${invoiceId}?${queryParams}`;
      window.open(url, '_blank');

      addToast({
        title: 'معاينة الفاتورة',
        description: 'تم فتح معاينة الفاتورة المدمجة',
        variant: 'default'
      });

    } catch (error) {
      console.error('خطأ في معاينة الفاتورة:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في معاينة الفاتورة المدمجة',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // طباعة الفاتورة المدمجة
  const handlePrint = async () => {
    try {
      setIsLoading(true);
      const queryParams = new URLSearchParams({
        ...options,
        includeQR: options.includeQR.toString(),
        includeLogo: options.includeLogo.toString(),
        paperSaving: options.paperSaving.toString(),
        highQuality: options.highQuality.toString()
      });

      const url = `/api/invoices/compact-pdf/${invoiceId}?${queryParams}`;
      const printWindow = window.open(url, '_blank');

      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
        };

        addToast({
          title: 'طباعة الفاتورة',
          description: 'تم فتح نافذة الطباعة للفاتورة المدمجة',
          variant: 'default'
        });
      } else {
        throw new Error('تعذر فتح نافذة الطباعة');
      }

    } catch (error) {
      console.error('خطأ في طباعة الفاتورة:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في طباعة الفاتورة المدمجة',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // تحميل الفاتورة المدمجة
  const handleDownload = async () => {
    try {
      setIsLoading(true);
      const queryParams = new URLSearchParams({
        ...options,
        includeQR: options.includeQR.toString(),
        includeLogo: options.includeLogo.toString(),
        paperSaving: options.paperSaving.toString(),
        highQuality: options.highQuality.toString()
      });

      const url = `/api/invoices/compact-pdf/${invoiceId}?${queryParams}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = `compact-invoice-${invoiceId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      addToast({
        title: 'تحميل الفاتورة',
        description: 'تم تحميل الفاتورة المدمجة بنجاح',
        variant: 'default'
      });

    } catch (error) {
      console.error('خطأ في تحميل الفاتورة:', error);
      addToast({
        title: 'خطأ',
        description: 'فشل في تحميل الفاتورة المدمجة',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // تحديث خيار معين
  const updateOption = (key: keyof CompactInvoiceOptions, value: any) => {
    setOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // الحصول على وصف الحجم
  const getSizeDescription = (size: string) => {
    switch (size) {
      case 'thermal':
        return 'حراري (80mm)';
      case 'half-a4':
        return 'نصف A4';
      case 'business-card':
        return 'بطاقة عمل';
      default:
        return size;
    }
  };

  return (
    <div className={`compact-invoice-print ${className}`} dir="rtl">
      {/* أزرار الإجراءات الرئيسية */}
      <div className="flex gap-2 items-center">
        <Button
          size="sm"
          variant="outline"
          onClick={handlePreview}
          disabled={isLoading}
          title="معاينة الفاتورة المدمجة"
        >
          <FaEye className="ml-1" />
          معاينة
        </Button>

        <Button
          size="sm"
          variant="outline"
          onClick={handlePrint}
          disabled={isLoading}
          title="طباعة الفاتورة المدمجة"
        >
          <FaPrint className="ml-1" />
          طباعة مدمجة
        </Button>

        <Button
          size="sm"
          variant="outline"
          onClick={handleDownload}
          disabled={isLoading}
          title="تحميل الفاتورة المدمجة"
        >
          <FaDownload className="ml-1" />
          تحميل
        </Button>

        <Button
          size="sm"
          variant="ghost"
          onClick={() => setIsOptionsOpen(!isOptionsOpen)}
          title="خيارات الطباعة"
        >
          <FaCog className="text-gray-500" />
        </Button>
      </div>

      {/* خيارات الطباعة المتقدمة */}
      {isOptionsOpen && (
        <Card className="mt-4 border-dashed">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <FaReceipt className="text-blue-600" />
              خيارات الفاتورة المدمجة
            </CardTitle>
            <CardDescription>
              تخصيص إعدادات الطباعة للفاتورة رقم {invoiceId} - {studentName}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* حجم الفاتورة */}
            <div>
              <label className="block text-sm font-medium mb-2">حجم الفاتورة</label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: 'thermal', label: 'حراري', icon: <FaReceipt /> },
                  { value: 'half-a4', label: 'نصف A4', icon: <FaCompressAlt /> },
                  { value: 'business-card', label: 'بطاقة', icon: <FaLeaf /> }
                ].map(size => (
                  <Button
                    key={size.value}
                    size="sm"
                    variant={options.size === size.value ? 'default' : 'outline'}
                    onClick={() => updateOption('size', size.value)}
                    className="flex items-center gap-1"
                  >
                    {size.icon}
                    {size.label}
                  </Button>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                الحجم المختار: {getSizeDescription(options.size)}
              </p>
            </div>

            {/* حجم الخط */}
            <div>
              <label className="block text-sm font-medium mb-2">حجم الخط</label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: 'small', label: 'صغير' },
                  { value: 'medium', label: 'متوسط' },
                  { value: 'large', label: 'كبير' }
                ].map(fontSize => (
                  <Button
                    key={fontSize.value}
                    size="sm"
                    variant={options.fontSize === fontSize.value ? 'default' : 'outline'}
                    onClick={() => updateOption('fontSize', fontSize.value)}
                  >
                    {fontSize.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* خيارات إضافية */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={options.includeQR}
                    onChange={(e) => updateOption('includeQR', e.target.checked)}
                    className="rounded"
                  />
                  <FaQrcode className="text-sm" />
                  <span className="text-sm">رمز QR</span>
                </label>

                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={options.includeLogo}
                    onChange={(e) => updateOption('includeLogo', e.target.checked)}
                    className="rounded"
                  />
                  <FaImage className="text-sm" />
                  <span className="text-sm">الشعار</span>
                </label>
              </div>

              <div className="space-y-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={options.paperSaving}
                    onChange={(e) => updateOption('paperSaving', e.target.checked)}
                    className="rounded"
                  />
                  <FaLeaf className="text-sm text-green-600" />
                  <span className="text-sm">توفير الورق</span>
                </label>

                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={options.highQuality}
                    onChange={(e) => updateOption('highQuality', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">جودة عالية</span>
                </label>
              </div>
            </div>

            {/* معلومات الفاتورة */}
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="flex justify-between items-center text-sm">
                <span>الطالب: {studentName}</span>
                <Badge variant="outline">{formatCurrency(invoiceAmount)}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* مؤشر التحميل */}
      {isLoading && (
        <div className="flex items-center gap-2 mt-2 text-sm text-gray-600">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          جاري المعالجة...
        </div>
      )}
    </div>
  );
}

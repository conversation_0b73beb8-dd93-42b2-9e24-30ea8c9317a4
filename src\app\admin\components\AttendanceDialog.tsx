'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { toast } from 'react-toastify';
import { format } from 'date-fns';

interface Class {
  id: number;
  name: string;
}

interface Student {
  id: number;
  name: string;
}

interface AttendanceDialogProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
}

export default function AttendanceDialog({ isOpen, onCloseAction, onSuccessAction }: AttendanceDialogProps) {
  const [classes, setClasses] = useState<Class[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('');
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [selectedHisass, setSelectedHisass] = useState<string>('1');
  const [selectedStatus, setSelectedStatus] = useState<string>('PRESENT');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingStudents, setIsLoadingStudents] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchClasses();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedClass) {
      fetchStudents(selectedClass);
    } else {
      setStudents([]);
    }
  }, [selectedClass]);

  const fetchClasses = async () => {
    try {
      const response = await fetch('/api/admin/classes');
      if (!response.ok) throw new Error('Failed to fetch classes');
      const data = await response.json();
      setClasses(data);
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('فشل في جلب بيانات الأقسام');
    }
  };

  const fetchStudents = async (classId: string) => {
    setIsLoadingStudents(true);
    try {
      const response = await fetch(`/api/local/students?classeId=${classId}`);
      if (!response.ok) throw new Error('Failed to fetch students');
      const data = await response.json();
      setStudents(data.students || []);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('فشل في جلب بيانات الطلاب');
    } finally {
      setIsLoadingStudents(false);
    }
  };

  const resetForm = () => {
    setSelectedClass('');
    setSelectedStudent('');
    setSelectedDate(format(new Date(), 'yyyy-MM-dd'));
    setSelectedHisass('1');
    setSelectedStatus('PRESENT');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedStudent || !selectedDate || !selectedHisass || !selectedStatus) {
      toast.error('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          studentId: parseInt(selectedStudent),
          date: selectedDate,
          hisass: parseInt(selectedHisass),
          status: selectedStatus
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save attendance');
      }

      toast.success('تم تسجيل الحضور بنجاح');
      resetForm();
      onSuccessAction();
      onCloseAction();
    } catch (error: unknown) {
      console.error('Error saving attendance:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء تسجيل الحضور');
    } finally {
      setIsLoading(false);
    }
  };

  const dialogFooter = (
    <>
      <Button
        type="button"
        variant="outline"
        onClick={onCloseAction}
        disabled={isLoading}
      >
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isLoading}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('attendanceForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isLoading ? 'جاري الحفظ...' : 'حفظ'}
      </Button>
    </>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title="تسجيل الحضور"
      variant="primary"
      footer={dialogFooter}
    >
      <form id="attendanceForm" onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="class-select">الفصل</Label>
            <Select value={selectedClass} onValueChange={setSelectedClass}>
              <SelectTrigger id="class-select">
                <SelectValue placeholder="اختر الفصل" />
              </SelectTrigger>
              <SelectContent>
                {classes.map((cls) => (
                  <SelectItem key={cls.id} value={cls.id.toString()}>
                    {cls.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="student-select">الطالب</Label>
            <Select
              value={selectedStudent}
              onValueChange={setSelectedStudent}
              disabled={!selectedClass || isLoadingStudents}
            >
              <SelectTrigger id="student-select">
                <SelectValue placeholder={isLoadingStudents ? "جاري تحميل الطلاب..." : "اختر الطالب"} />
              </SelectTrigger>
              <SelectContent>
                {students.map((student) => (
                  <SelectItem key={student.id} value={student.id.toString()}>
                    {student.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="date-input">التاريخ</Label>
            <input
              id="date-input"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="hisass-select">الحصة</Label>
            <Select value={selectedHisass} onValueChange={setSelectedHisass}>
              <SelectTrigger id="hisass-select">
                <SelectValue placeholder="اختر الحصة" />
              </SelectTrigger>
              <SelectContent>
                {[1, 2, 3, 4, 5, 6].map((num) => (
                  <SelectItem key={num} value={num.toString()}>
                    الحصة {num}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status-select">الحالة</Label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger id="status-select">
                <SelectValue placeholder="اختر الحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="PRESENT">حاضر</SelectItem>
                <SelectItem value="ABSENT">غائب</SelectItem>
                <SelectItem value="EXCUSED">غائب بعذر</SelectItem>
              </SelectContent>
            </Select>
          </div>

        </form>
      </AnimatedDialog>
  );
}

/**
 * مساعدات للتعامل مع الصور والملفات
 */

/**
 * التحقق من صحة رابط الصورة
 */
export const validateImageUrl = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }

    const img = new Image();
    
    img.onload = () => {
      resolve(true);
    };
    
    img.onerror = () => {
      resolve(false);
    };
    
    // إضافة timeout لتجنب الانتظار الطويل
    const timeout = setTimeout(() => {
      resolve(false);
    }, 5000); // 5 ثوان
    
    img.onload = () => {
      clearTimeout(timeout);
      resolve(true);
    };
    
    img.onerror = () => {
      clearTimeout(timeout);
      resolve(false);
    };
    
    img.src = url;
  });
};

/**
 * إصلاح مسار الصورة إذا كان مكسوراً
 */
export const fixImagePath = (originalPath: string): string => {
  if (!originalPath) return '';
  
  // إذا كان المسار يبدأ بـ http أو https، أرجعه كما هو
  if (originalPath.startsWith('http://') || originalPath.startsWith('https://')) {
    return originalPath;
  }
  
  // إذا كان المسار لا يبدأ بـ /، أضف /
  if (!originalPath.startsWith('/')) {
    return `/${originalPath}`;
  }
  
  return originalPath;
};

/**
 * الحصول على مسار بديل للصورة
 */
export const getFallbackImagePath = (originalPath: string): string => {
  // يمكن إضافة منطق لإرجاع صورة افتراضية أو مسار بديل
  return '/images/default-logo.svg'; // مسار الصورة الافتراضية
};

/**
 * تنظيف مسار الصورة من المعاملات الإضافية
 */
export const cleanImagePath = (path: string): string => {
  if (!path) return '';
  
  // إزالة معاملات الاستعلام مثل ?v=timestamp
  const cleanPath = path.split('?')[0];
  
  return cleanPath;
};

/**
 * التحقق من نوع الملف
 */
export const isValidImageType = (filename: string): boolean => {
  const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
  return validExtensions.includes(extension);
};

/**
 * تحويل حجم الملف إلى نص قابل للقراءة
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * إنشاء معاينة للصورة قبل الرفع
 */
export const createImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('فشل في قراءة الملف'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('فشل في قراءة الملف'));
    };
    
    reader.readAsDataURL(file);
  });
};

/**
 * ضغط الصورة قبل الرفع
 */
export const compressImage = (file: File, maxWidth: number = 800, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // حساب الأبعاد الجديدة
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // رسم الصورة المضغوطة
      ctx?.drawImage(img, 0, 0, width, height);
      
      // تحويل إلى blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            resolve(file); // إرجاع الملف الأصلي في حالة الفشل
          }
        },
        file.type,
        quality
      );
    };
    
    img.onerror = () => {
      resolve(file); // إرجاع الملف الأصلي في حالة الفشل
    };
    
    img.src = URL.createObjectURL(file);
  });
};

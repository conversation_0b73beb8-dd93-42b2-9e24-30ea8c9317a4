'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';
import { PlusCircle, Trash2, Loader2, BarChart as BarChartIcon, FileText, Download } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON> } from '@/components/charts/bar-chart';
import { <PERSON><PERSON><PERSON> } from '@/components/charts/pie-chart';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

type Student = {
  id: number;
  name: string;
};

type AchievementReport = {
  id: number;
  title: string;
  description: string | null;
  startDate: string;
  endDate: string;
  totalSessions: number;
  attendedSessions: number;
  totalAyahs: number;
  memorizedAyahs: number;
  reviewedAyahs: number;
  averageRating: number;
  createdAt: string;
  updatedAt: string;
  student: {
    id: number;
    name: string;
  };
};

// مكون الرسوم البيانية للتقرير
function ReportCharts({ report }: { report: AchievementReport }) {
  return (
    <div className="py-4">
      <Tabs defaultValue="attendance" className="w-full">
        <TabsList className="mb-4 w-full justify-start">
          <TabsTrigger value="attendance">الحضور والمشاركة</TabsTrigger>
          <TabsTrigger value="memorization">الحفظ والمراجعة</TabsTrigger>
          <TabsTrigger value="rating">التقييم</TabsTrigger>
        </TabsList>

        <TabsContent value="attendance" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-bold text-[var(--primary-color)]">نسبة الحضور</CardTitle>
              </CardHeader>
              <CardContent>
                <PieChart
                  data={{
                    labels: ['الجلسات المحضورة', 'الجلسات الغائبة'],
                    datasets: [
                      {
                        label: 'الحضور',
                        data: [report.attendedSessions, report.totalSessions - report.attendedSessions],
                        backgroundColor: [
                          'rgba(22, 155, 136, 0.7)',
                          'rgba(239, 68, 68, 0.7)'
                        ]
                      }
                    ]
                  }}
                  height={250}
                  width={250}
                  className="mx-auto"
                />
                <div className="text-center mt-4">
                  <p className="text-sm text-gray-500">
                    حضر الطالب {report.attendedSessions} من أصل {report.totalSessions} جلسة
                  </p>
                  <p className="text-lg font-bold text-[var(--primary-color)]">
                    نسبة الحضور: {Math.round((report.attendedSessions / report.totalSessions) * 100)}%
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-bold text-[var(--primary-color)]">معدل المشاركة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-full">
                  <div className="relative w-48 h-48 flex items-center justify-center">
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      <circle
                        className="text-gray-200"
                        strokeWidth="8"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                      />
                      <circle
                        className="text-[var(--primary-color)]"
                        strokeWidth="8"
                        strokeDasharray={`${(report.attendedSessions / report.totalSessions) * 251.2} 251.2`}
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="40"
                        cx="50"
                        cy="50"
                      />
                    </svg>
                    <span className="absolute text-3xl font-bold text-[var(--primary-color)]">
                      {Math.round((report.attendedSessions / report.totalSessions) * 100)}%
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mt-4">
                    معدل المشاركة في مجالس الختم
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="memorization" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-bold text-[var(--primary-color)]">الآيات المحفوظة والمراجعة</CardTitle>
              </CardHeader>
              <CardContent>
                <BarChart
                  data={{
                    labels: ['الآيات المحفوظة', 'الآيات المراجعة'],
                    datasets: [
                      {
                        label: 'عدد الآيات',
                        data: [report.memorizedAyahs, report.reviewedAyahs],
                        backgroundColor: [
                          'rgba(22, 155, 136, 0.7)',
                          'rgba(45, 212, 191, 0.7)'
                        ]
                      }
                    ]
                  }}
                  height={250}
                  width={400}
                  className="mx-auto"
                />
                <div className="text-center mt-4">
                  <p className="text-sm text-gray-500">
                    إجمالي الآيات المحفوظة: {report.memorizedAyahs} آية
                  </p>
                  <p className="text-sm text-gray-500">
                    إجمالي الآيات المراجعة: {report.reviewedAyahs} آية
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-bold text-[var(--primary-color)]">نسبة الإنجاز</CardTitle>
              </CardHeader>
              <CardContent>
                <PieChart
                  data={{
                    labels: ['الآيات المحفوظة', 'الآيات المتبقية'],
                    datasets: [
                      {
                        label: 'الآيات',
                        data: [report.memorizedAyahs, report.totalAyahs - report.memorizedAyahs],
                        backgroundColor: [
                          'rgba(22, 155, 136, 0.7)',
                          'rgba(203, 213, 225, 0.7)'
                        ]
                      }
                    ]
                  }}
                  height={250}
                  width={250}
                  className="mx-auto"
                />
                <div className="text-center mt-4">
                  <p className="text-sm text-gray-500">
                    حفظ الطالب {report.memorizedAyahs} من أصل {report.totalAyahs} آية
                  </p>
                  <p className="text-lg font-bold text-[var(--primary-color)]">
                    نسبة الإنجاز: {Math.round((report.memorizedAyahs / report.totalAyahs) * 100)}%
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rating" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-bold text-[var(--primary-color)]">متوسط تقييم الحفظ</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center">
                <div className="flex items-center justify-center mb-4">
                  {Array(5).fill(0).map((_, i) => (
                    <svg
                      key={i}
                      className={`w-12 h-12 ${i < Math.round(report.averageRating) ? 'text-yellow-500' : 'text-gray-300'}`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>
                <p className="text-3xl font-bold text-[var(--primary-color)]">
                  {report.averageRating.toFixed(1)} / 5
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  متوسط تقييم جودة الحفظ
                </p>

                <div className="w-full mt-8">
                  <div className="relative pt-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-[var(--primary-color)] bg-[var(--primary-color)]/10">
                          مستوى الأداء
                        </span>
                      </div>
                      <div className="text-right">
                        <span className="text-xs font-semibold inline-block text-[var(--primary-color)]">
                          {Math.round((report.averageRating / 5) * 100)}%
                        </span>
                      </div>
                    </div>
                    <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-[var(--primary-color)]/10 mt-2">
                      <div
                        style={{ width: `${(report.averageRating / 5) * 100}%` }}
                        className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-[var(--primary-color)]"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function KhatmReportsPage() {
  const [reports, setReports] = useState<AchievementReport[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState({
    reports: false,
    students: false,
    submit: false,
    generate: false
  });
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    description: '',
    studentId: '',
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
    endDate: new Date()
  });

  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    fetchReports();
    fetchStudents();
  }, []);

  const fetchReports = async () => {
    setLoading(prev => ({ ...prev, reports: true }));
    try {
      const response = await fetch('/api/khatm-reports');
      if (!response.ok) throw new Error('فشل في جلب تقارير الإنجاز');
      const data = await response.json();
      setReports(data.data || []);
    } catch (error: Error | unknown) {
      console.error('Error fetching achievement reports:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب تقارير الإنجاز',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, reports: false }));
    }
  };

  const fetchStudents = async () => {
    setLoading(prev => ({ ...prev, students: true }));
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('فشل في جلب الطلاب');
      const data = await response.json();

      // التأكد من أن البيانات مصفوفة
      if (Array.isArray(data)) {
        setStudents(data);
      } else if (data && Array.isArray(data.data)) {
        setStudents(data.data);
      } else if (data && Array.isArray(data.students)) {
        setStudents(data.students);
      } else {
        console.error('Invalid students data format:', data);
        setStudents([]);
      }
    } catch (error: Error | unknown) {
      console.error('Error fetching students:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب الطلاب',
        variant: 'destructive',
      });
      setStudents([]);
    } finally {
      setLoading(prev => ({ ...prev, students: false }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (name: string, value: string) => {
    if (value) {
      const newDate = new Date(value);
      setFormData(prev => ({ ...prev, [name]: newDate }));
    }
  };

  const resetForm = () => {
    setFormData({
      id: '',
      title: '',
      description: '',
      studentId: '',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
      endDate: new Date()
    });

  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(prev => ({ ...prev, generate: true }));

    try {
      const payload = {
        ...formData,
        startDate: formData.startDate.toISOString(),
        endDate: formData.endDate.toISOString()
      };

      if (!payload.studentId) {
        throw new Error('يرجى اختيار الطالب');
      }

      const response = await fetch('/api/khatm-reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم إنشاء تقرير الإنجاز بنجاح',
        });
        resetForm();
        setShowForm(false);
        fetchReports();
      } else {
        throw new Error(result.error || 'حدث خطأ أثناء إنشاء تقرير الإنجاز');
      }
    } catch (error) {
      console.error('Error generating achievement report:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء إنشاء تقرير الإنجاز',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, generate: false }));
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا التقرير؟')) {
      return;
    }

    try {
      const response = await fetch(`/api/khatm-reports?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'نجاح',
          description: 'تم حذف التقرير بنجاح',
        });
        fetchReports();
      } else {
        const result = await response.json();
        throw new Error(result.error || 'حدث خطأ أثناء حذف التقرير');
      }
    } catch (error) {
      console.error('Error deleting achievement report:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حذف التقرير',
        variant: 'destructive',
      });
    }
  };

  const downloadReport = async (id: number) => {
    try {
      const response = await fetch(`/api/khatm-reports/download?id=${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'فشل في تنزيل التقرير');
      }

      // تحويل الاستجابة إلى blob
      const blob = await response.blob();

      // إنشاء رابط تنزيل
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `تقرير_إنجاز_${id}.pdf`;

      // إضافة الرابط للصفحة وتنفيذ النقر عليه ثم إزالته
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'نجاح',
        description: 'تم تنزيل التقرير بنجاح',
      });
    } catch (error) {
      console.error('Error downloading report:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في تنزيل التقرير',
        variant: 'destructive',
      });
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.khatm-reports.view">
      <div className="container mx-auto py-6 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <Card className="shadow-md border-t-4 border-t-[var(--primary-color)]">
        <CardHeader className="flex flex-row items-center justify-between bg-gradient-to-r from-[#f8fffd] to-white">
          <div>
            <CardTitle className="text-2xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">تقارير إنجاز الختم</CardTitle>
            <p className="text-muted-foreground mt-1 mr-3">إنشاء وإدارة تقارير إنجاز الطلاب في مجالس الختم</p>
          </div>
          <QuickActionButtons
            entityType="khatm-reports"
            actions={[
              {
                key: 'create',
                label: 'إنشاء تقرير جديد',
                icon: <PlusCircle className="h-4 w-4" />,
                onClick: () => { resetForm(); setShowForm(true); },
                variant: 'primary',
                permission: 'admin.khatm-reports.create'
              }
            ]}
          />
        </CardHeader>
        <CardContent>
          {showForm && (
            <form onSubmit={handleSubmit} className="space-y-4 mb-6 p-6 border rounded-lg bg-[#f8fffd] shadow-sm">
              <h3 className="text-xl font-bold text-[var(--primary-color)] mb-4">إنشاء تقرير إنجاز جديد</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">عنوان التقرير</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="تقرير إنجاز الطالب"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="studentId">الطالب</Label>
                  <select
                    id="studentId"
                    name="studentId"
                    value={formData.studentId}
                    onChange={(e) => handleSelectChange('studentId', e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    required
                  >
                    <option value="" disabled>اختر الطالب</option>
                    {Array.isArray(students) && students.length > 0 ? (
                      students.map(student => (
                        <option key={student.id} value={student.id.toString()}>
                          {student.name}
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>لا يوجد طلاب</option>
                    )}
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startDate">تاريخ البداية</Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={formData.startDate.toISOString().split('T')[0]}
                    onChange={(e) => handleDateChange('startDate', e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">تاريخ النهاية</Label>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={formData.endDate.toISOString().split('T')[0]}
                    onChange={(e) => handleDateChange('endDate', e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="description">وصف التقرير</Label>
                  <Input
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="وصف مختصر للتقرير"
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => { setShowForm(false); resetForm(); }}
                >
                  إلغاء
                </Button>
                <Button
                  type="submit"
                  disabled={loading.generate}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                >
                  {loading.generate ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جاري الإنشاء...
                    </>
                  ) : 'إنشاء التقرير'}
                </Button>
              </div>
            </form>
          )}

          {loading.reports ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-[var(--primary-color)]" />
              <p className="mt-2 text-muted-foreground">جاري تحميل التقارير...</p>
            </div>
          ) : reports.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              <div className="flex flex-col items-center justify-center">
                <FileText className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-1">لا توجد تقارير إنجاز</h3>
                <p className="text-sm text-gray-500 mb-4">قم بإنشاء تقرير إنجاز جديد للبدء</p>
                <PermissionGuard requiredPermission="admin.khatm-reports.create">
                  <Button
                    onClick={() => { resetForm(); setShowForm(true); }}
                    className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-xs md:text-sm"
                  >
                    <PlusCircle className="ml-1 md:ml-2 h-3 w-3 md:h-4 md:w-4" />
                    <span className="hidden sm:inline">إنشاء تقرير جديد</span>
                    <span className="sm:hidden">إنشاء تقرير</span>
                  </Button>
                </PermissionGuard>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader className="bg-[var(--primary-color)]/10">
                <TableRow>
                  <TableHead className="text-[var(--primary-color)] font-bold px-2 md:px-4">العنوان</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold px-2 md:px-4">الطالب</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">الفترة</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">الجلسات</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">الآيات</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold hidden md:table-cell">التقييم</TableHead>
                  <TableHead className="text-[var(--primary-color)] font-bold px-2 md:px-4">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reports.map(report => (
                  <TableRow key={report.id}>
                    <TableCell className="px-2 md:px-4">{report.title}</TableCell>
                    <TableCell className="px-2 md:px-4">{report.student.name}</TableCell>
                    <TableCell className="hidden md:table-cell">
                      {format(new Date(report.startDate), 'yyyy/MM/dd', { locale: ar })} -
                      {format(new Date(report.endDate), 'yyyy/MM/dd', { locale: ar })}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {report.attendedSessions} / {report.totalSessions}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {report.memorizedAyahs} / {report.totalAyahs}
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      {report.averageRating.toFixed(1)} / 5
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1 md:gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-amber-600 border-amber-600 hover:bg-amber-50 px-2 md:px-3"
                              title="عرض الرسوم البيانية"
                            >
                              <BarChartIcon className="h-3 w-3 md:h-4 md:w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl">
                            <DialogHeader>
                              <DialogTitle className="text-xl font-bold text-[var(--primary-color)]">
                                الرسوم البيانية لتقرير: {report.title}
                              </DialogTitle>
                            </DialogHeader>
                            <ReportCharts report={report} />
                          </DialogContent>
                        </Dialog>
                        <QuickActionButtons
                          entityType="khatm-reports"
                          actions={[
                            {
                              key: 'download',
                              label: 'تنزيل التقرير',
                              icon: <Download className="h-3 w-3 md:h-4 md:w-4" />,
                              onClick: () => downloadReport(report.id),
                              variant: 'primary',
                              permission: 'admin.khatm-reports.download'
                            }
                          ]}
                          className="inline-flex"
                        />
                        <OptimizedActionButtonGroup
                          entityType="khatm-reports"
                          onDelete={() => handleDelete(report.id)}
                          showEdit={false}
                          showDelete={true}
                          size="sm"
                          className="gap-1 md:gap-2"
                          deleteConfirmTitle="هل أنت متأكد من حذف هذا التقرير؟"
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      </div>
    </OptimizedProtectedRoute>
  );
}

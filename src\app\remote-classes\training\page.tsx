'use client';

import React, { useState } from 'react';
import { FaArrowRight, FaChalkboardTeacher, FaUserGraduate, FaVideo, FaDesktop, FaChalkboard, FaVolumeUp } from 'react-icons/fa';
import Link from 'next/link';

/**
 * Training materials page for remote classes
 */
const TrainingPage: React.FC = () => {
  const [userType, setUserType] = useState<'teacher' | 'student' | null>(null);
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8fffd] to-white py-8" dir="rtl">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <Link
            href="/remote-classes"
            className="inline-flex items-center text-[var(--primary-color)] hover:text-[var(--secondary-color)] mb-4"
          >
            <FaArrowRight className="ml-1" />
            <span>العودة إلى الفصول الافتراضية</span>
          </Link>
          
          <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center gap-2 mb-2">
            <FaChalkboardTeacher className="text-[var(--primary-color)]" />
            المواد التدريبية للفصول الافتراضية
          </h1>
          <p className="text-gray-600 mr-4">
            دليل استخدام ميزات الفصول الافتراضية للمعلمين والطلاب
          </p>
        </div>
        
        {/* User type selection */}
        {!userType && (
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h2 className="text-2xl font-bold text-center text-[var(--primary-color)] mb-8">اختر نوع المستخدم</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <button
                onClick={() => setUserType('teacher')}
                className="flex flex-col items-center p-8 bg-[#f0fbf9] hover:bg-[#e0f7f4] border-2 border-[var(--primary-color)] rounded-lg transition-colors"
              >
                <FaChalkboardTeacher className="text-6xl text-[var(--primary-color)] mb-4" />
                <h3 className="text-xl font-bold text-[var(--primary-color)]">معلم</h3>
                <p className="text-gray-600 text-center mt-2">
                  مواد تدريبية للمعلمين حول كيفية إدارة الفصول الافتراضية
                </p>
              </button>
              
              <button
                onClick={() => setUserType('student')}
                className="flex flex-col items-center p-8 bg-[#f0fbf9] hover:bg-[#e0f7f4] border-2 border-[var(--primary-color)] rounded-lg transition-colors"
              >
                <FaUserGraduate className="text-6xl text-[var(--primary-color)] mb-4" />
                <h3 className="text-xl font-bold text-[var(--primary-color)]">طالب</h3>
                <p className="text-gray-600 text-center mt-2">
                  مواد تدريبية للطلاب حول كيفية المشاركة في الفصول الافتراضية
                </p>
              </button>
            </div>
          </div>
        )}
        
        {/* Teacher training materials */}
        {userType === 'teacher' && (
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[var(--primary-color)] text-white p-4">
                <h2 className="text-xl font-bold flex items-center gap-2">
                  <FaChalkboardTeacher />
                  دليل المعلم للفصول الافتراضية
                </h2>
              </div>
              
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Screen sharing */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaDesktop className="text-[var(--primary-color)]" />
                        مشاركة الشاشة
                      </h3>
                    </div>
                    <div className="p-4">
                      <ol className="list-decimal list-inside space-y-2">
                        <li>انقر على زر "مشاركة الشاشة" في شريط الأدوات</li>
                        <li>اختر ما تريد مشاركته (الشاشة بأكملها، نافذة، أو علامة تبويب)</li>
                        <li>يمكنك إيقاف المشاركة في أي وقت بالنقر على زر "إيقاف المشاركة"</li>
                        <li>يمكن للطلاب رؤية شاشتك ولكن لا يمكنهم التحكم فيها</li>
                      </ol>
                      <div className="mt-4">
                        <Link
                          href="/remote-classes/test/screen-share"
                          className="text-[var(--primary-color)] hover:underline"
                        >
                          تجربة مشاركة الشاشة
                        </Link>
                      </div>
                    </div>
                  </div>
                  
                  {/* Whiteboard */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaChalkboard className="text-[var(--primary-color)]" />
                        السبورة التفاعلية
                      </h3>
                    </div>
                    <div className="p-4">
                      <ol className="list-decimal list-inside space-y-2">
                        <li>استخدم أدوات الرسم المختلفة (قلم، ممحاة، نص، أشكال)</li>
                        <li>يمكنك تغيير الألوان وحجم الخط من شريط الأدوات</li>
                        <li>استخدم أزرار التراجع وإعادة الإجراء للتحكم في التغييرات</li>
                        <li>يمكنك تصدير السبورة كصورة للمشاركة مع الطلاب</li>
                        <li>يمكنك السماح للطلاب بالمشاركة في السبورة أو جعلها للقراءة فقط</li>
                      </ol>
                      <div className="mt-4">
                        <Link
                          href="/remote-classes/test/whiteboard"
                          className="text-[var(--primary-color)] hover:underline"
                        >
                          تجربة السبورة التفاعلية
                        </Link>
                      </div>
                    </div>
                  </div>
                  
                  {/* Audio/Video settings */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaVolumeUp className="text-[var(--primary-color)]" />
                        إعدادات الصوت والفيديو
                      </h3>
                    </div>
                    <div className="p-4">
                      <ol className="list-decimal list-inside space-y-2">
                        <li>تأكد من تشغيل الكاميرا والميكروفون قبل بدء الفصل</li>
                        <li>يمكنك ضبط جودة الفيديو حسب سرعة الإنترنت</li>
                        <li>فعّل خاصية معدل البت المتكيف للتكيف التلقائي مع ظروف الشبكة</li>
                        <li>يمكنك كتم صوت جميع الطلاب أو طالب محدد</li>
                        <li>تأكد من استخدام سماعات رأس لتجنب صدى الصوت</li>
                      </ol>
                      <div className="mt-4">
                        <Link
                          href="/remote-classes/test/video-audio"
                          className="text-[var(--primary-color)] hover:underline"
                        >
                          تجربة إعدادات الصوت والفيديو
                        </Link>
                      </div>
                    </div>
                  </div>
                  
                  {/* Class management */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaVideo className="text-[var(--primary-color)]" />
                        إدارة الفصل الافتراضي
                      </h3>
                    </div>
                    <div className="p-4">
                      <ol className="list-decimal list-inside space-y-2">
                        <li>تأكد من إنشاء الفصل قبل الموعد المحدد بوقت كافٍ</li>
                        <li>يمكنك التحكم في صلاحيات الطلاب (مشاركة الشاشة، السبورة، إلخ)</li>
                        <li>يمكنك تسجيل الفصل لمشاهدته لاحقًا</li>
                        <li>تأكد من مشاركة المواد التعليمية قبل بدء الفصل</li>
                        <li>يمكنك استخدام خاصية رفع اليد لتنظيم المشاركات</li>
                      </ol>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <button
                onClick={() => setUserType(null)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md transition-colors"
              >
                العودة إلى اختيار نوع المستخدم
              </button>
            </div>
          </div>
        )}
        
        {/* Student training materials */}
        {userType === 'student' && (
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="bg-[var(--primary-color)] text-white p-4">
                <h2 className="text-xl font-bold flex items-center gap-2">
                  <FaUserGraduate />
                  دليل الطالب للفصول الافتراضية
                </h2>
              </div>
              
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Joining classes */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaVideo className="text-[var(--primary-color)]" />
                        الانضمام إلى الفصل
                      </h3>
                    </div>
                    <div className="p-4">
                      <ol className="list-decimal list-inside space-y-2">
                        <li>انقر على رابط الفصل الافتراضي في الموعد المحدد</li>
                        <li>تأكد من تشغيل الكاميرا والميكروفون إذا طلب منك المعلم ذلك</li>
                        <li>التزم بقواعد الفصل التي يحددها المعلم</li>
                        <li>استخدم خاصية رفع اليد للمشاركة</li>
                      </ol>
                    </div>
                  </div>
                  
                  {/* Audio/Video settings */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaVolumeUp className="text-[var(--primary-color)]" />
                        إعدادات الصوت والفيديو
                      </h3>
                    </div>
                    <div className="p-4">
                      <ol className="list-decimal list-inside space-y-2">
                        <li>تأكد من تشغيل الكاميرا والميكروفون قبل بدء الفصل</li>
                        <li>استخدم سماعات رأس لتجنب صدى الصوت</li>
                        <li>اختر مكانًا هادئًا وجيد الإضاءة</li>
                        <li>يمكنك كتم صوتك عندما لا تتحدث</li>
                      </ol>
                      <div className="mt-4">
                        <Link
                          href="/remote-classes/test/video-audio"
                          className="text-[var(--primary-color)] hover:underline"
                        >
                          تجربة إعدادات الصوت والفيديو
                        </Link>
                      </div>
                    </div>
                  </div>
                  
                  {/* Whiteboard */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaChalkboard className="text-[var(--primary-color)]" />
                        استخدام السبورة التفاعلية
                      </h3>
                    </div>
                    <div className="p-4">
                      <ol className="list-decimal list-inside space-y-2">
                        <li>يمكنك المشاركة في السبورة إذا سمح لك المعلم بذلك</li>
                        <li>استخدم أدوات الرسم بحكمة وفقط للأغراض التعليمية</li>
                        <li>يمكنك حفظ محتوى السبورة كصورة للرجوع إليها لاحقًا</li>
                        <li>احترم مساهمات زملائك على السبورة</li>
                      </ol>
                      <div className="mt-4">
                        <Link
                          href="/remote-classes/test/whiteboard"
                          className="text-[var(--primary-color)] hover:underline"
                        >
                          تجربة السبورة التفاعلية
                        </Link>
                      </div>
                    </div>
                  </div>
                  
                  {/* Etiquette */}
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b border-gray-200">
                      <h3 className="font-bold text-lg flex items-center gap-2">
                        <FaChalkboardTeacher className="text-[var(--primary-color)]" />
                        آداب الفصل الافتراضي
                      </h3>
                    </div>
                    <div className="p-4">
                      <ul className="list-disc list-inside space-y-2">
                        <li>كن في الموعد المحدد للفصل</li>
                        <li>ارتدِ ملابس مناسبة كما لو كنت في الفصل الحقيقي</li>
                        <li>اكتم صوتك عندما لا تتحدث</li>
                        <li>استخدم خاصية رفع اليد للمشاركة</li>
                        <li>تجنب المحادثات الجانبية في الدردشة</li>
                        <li>ركز على الدرس وتجنب تصفح مواقع أخرى</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="text-center">
              <button
                onClick={() => setUserType(null)}
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-md transition-colors"
              >
                العودة إلى اختيار نوع المستخدم
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TrainingPage;

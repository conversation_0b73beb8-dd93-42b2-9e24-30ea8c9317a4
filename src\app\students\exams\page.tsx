'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'react-hot-toast';
import { Loader2, Search, Filter, Calendar, BookOpen, CheckCircle, Clock, AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Exam {
  id: number;
  evaluationType: string;
  month: string;
  description: string | null;
  maxPoints: number;
  passingPoints: number;
  examType: {
    id: number;
    name: string;
  } | null;
}

interface ExamPoint {
  id: number;
  examId: number;
  studentId: number;
  classSubjectId: number;
  grade: number;
  status: string;
  exam: Exam;
}

export default function StudentExamsPage() {
  const router = useRouter();

  const [exams, setExams] = useState<ExamPoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterMonth, setFilterMonth] = useState('');

  useEffect(() => {
    fetchExams();
  }, []);

  const fetchExams = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/student/exams');
      const result = await response.json();

      if (result.success) {
        setExams(result.data);
      } else {
        toast.error(result.error || 'حدث خطأ أثناء جلب الامتحانات');
        setExams([]);
      }
    } catch (error) {
      console.error('Error fetching exams:', error);
      toast.error('حدث خطأ أثناء جلب الامتحانات');
      setExams([]);
    } finally {
      setIsLoading(false);
    }
  };

  const getEvaluationTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      QURAN_MEMORIZATION: 'حفظ القرآن',
      QURAN_RECITATION: 'تلاوة القرآن',
      WRITTEN_EXAM: 'تحريري',
      ORAL_EXAM: 'شفهي',
      PRACTICAL_TEST: 'عملي',
      HOMEWORK: 'واجب منزلي',
      PROJECT: 'مشروع',
      REMOTE_EXAM: 'امتحان عن بعد'
    };
    return labels[type] || type;
  };

  const formatDate = (dateString: string): string => {
    const [year, month] = dateString.split('-');
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  const getStatusLabel = (status: string): string => {
    const labels: Record<string, string> = {
      PENDING: 'قيد الانتظار',
      COMPLETED: 'مكتمل',
      PASSED: 'ناجح',
      FAILED: 'راسب',
      EXCELLENT: 'ممتاز'
    };
    return labels[status] || status;
  };

  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      PENDING: 'bg-yellow-100 text-yellow-800',
      COMPLETED: 'bg-blue-100 text-blue-800',
      PASSED: 'bg-green-100 text-green-800',
      FAILED: 'bg-red-100 text-red-800',
      EXCELLENT: 'bg-purple-100 text-purple-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PASSED':
      case 'EXCELLENT':
        return <CheckCircle className="h-4 w-4 ml-1" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 ml-1" />;
      case 'FAILED':
        return <AlertTriangle className="h-4 w-4 ml-1" />;
      default:
        return null;
    }
  };

  const getAvailableMonths = () => {
    const months = new Set<string>();
    exams.forEach(exam => {
      months.add(exam.exam.month);
    });
    return Array.from(months).sort().reverse();
  };

  const getAvailableTypes = () => {
    const types = new Set<string>();
    exams.forEach(exam => {
      types.add(exam.exam.evaluationType);
    });
    return Array.from(types);
  };

  const filteredExams = exams.filter(exam => {
    const matchesSearch =
      getEvaluationTypeLabel(exam.exam.evaluationType).toLowerCase().includes(searchTerm.toLowerCase()) ||
      (exam.exam.examType?.name && exam.exam.examType.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      formatDate(exam.exam.month).toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = !filterType || exam.exam.evaluationType === filterType;
    const matchesStatus = !filterStatus || exam.status === filterStatus;
    const matchesMonth = !filterMonth || exam.exam.month === filterMonth;

    return matchesSearch && matchesType && matchesStatus && matchesMonth;
  });

  const handleTakeExam = (examId: number) => {
    router.push(`/students/exams/${examId}`);
  };

  const handleViewResults = (examId: number) => {
    router.push(`/students/exams/results/${examId}`);
  };

  return (
    <div className="container mx-auto p-4 bg-gradient-to-b from-[#f8fffd] to-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3">الامتحانات</h1>
      </div>

      <div className="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative col-span-1 md:col-span-2">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <Input
            placeholder="البحث في الامتحانات..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-12 text-right"
            dir="rtl"
          />
        </div>

        <div className="col-span-1">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع أنواع التقييم" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_types">جميع أنواع التقييم</SelectItem>
              {getAvailableTypes().map(type => (
                <SelectItem key={type} value={type}>
                  {getEvaluationTypeLabel(type)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="col-span-1">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-full text-right">
              <SelectValue placeholder="جميع الحالات" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_statuses">جميع الحالات</SelectItem>
              <SelectItem value="PENDING">قيد الانتظار</SelectItem>
              <SelectItem value="COMPLETED">مكتمل</SelectItem>
              <SelectItem value="PASSED">ناجح</SelectItem>
              <SelectItem value="FAILED">راسب</SelectItem>
              <SelectItem value="EXCELLENT">ممتاز</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="mb-4">
        <div className="flex flex-wrap gap-2">
          <Badge className="bg-gray-100 text-gray-800 cursor-pointer hover:bg-gray-200" onClick={() => setFilterMonth('')}>
            جميع الشهور
          </Badge>
          {getAvailableMonths().map(month => (
            <Badge
              key={month}
              className={`cursor-pointer ${filterMonth === month ? 'bg-[var(--primary-color)] text-white' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
              onClick={() => setFilterMonth(month === filterMonth ? '' : month)}
            >
              <Calendar className="h-3 w-3 ml-1" />
              {formatDate(month)}
            </Badge>
          ))}
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-[var(--primary-color)]" />
        </div>
      ) : filteredExams.length === 0 ? (
        <div className="text-center p-8 bg-white rounded-lg shadow">
          <Filter className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد امتحانات</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || filterType || filterStatus || filterMonth
              ? 'لم يتم العثور على امتحانات تطابق معايير البحث. حاول تغيير المعايير.'
              : 'لم يتم العثور على أي امتحانات متاحة لك حاليًا.'}
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <Table>
            <TableHeader className="bg-[var(--primary-color)]">
              <TableRow>
                <TableHead className="text-white text-right">نوع التقييم</TableHead>
                <TableHead className="text-white text-right">نوع الامتحان</TableHead>
                <TableHead className="text-white text-right">الشهر</TableHead>
                <TableHead className="text-white text-right">الدرجة</TableHead>
                <TableHead className="text-white text-right">الحالة</TableHead>
                <TableHead className="text-white text-right">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredExams.map((examPoint) => (
                <TableRow key={examPoint.id} className="hover:bg-gray-50">
                  <TableCell>{getEvaluationTypeLabel(examPoint.exam.evaluationType)}</TableCell>
                  <TableCell>{examPoint.exam.examType?.name || '-'}</TableCell>
                  <TableCell>{formatDate(examPoint.exam.month)}</TableCell>
                  <TableCell>
                    {examPoint.status !== 'PENDING'
                      ? `${examPoint.grade.toFixed(1)} / ${examPoint.exam.maxPoints}`
                      : '-'
                    }
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(examPoint.status)}>
                      {getStatusIcon(examPoint.status)}
                      {getStatusLabel(examPoint.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      {examPoint.status === 'PENDING' ? (
                        <Button
                          onClick={() => handleTakeExam(examPoint.examId)}
                          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
                        >
                          <BookOpen className="ml-2" size={16} />
                          بدء الامتحان
                        </Button>
                      ) : (
                        <Button
                          onClick={() => handleViewResults(examPoint.examId)}
                          variant="outline"
                        >
                          عرض النتائج
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}

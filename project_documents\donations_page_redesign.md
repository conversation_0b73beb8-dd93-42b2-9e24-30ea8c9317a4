# إعادة تصميم صفحة التبرعات

## نظرة عامة
تحويل صفحة التبرعات من نظام يعتمد على الحملات إلى نظام بسيط يعرض معلومات التبرع من الإعدادات.

## التغييرات المطلوبة

### 1. إزالة المكونات المتعلقة بالحملات

#### إزالة من الواجهات (Interfaces):
```typescript
// حذف هذه الواجهة
interface DonationCampaign {
  id: number
  title: string
  description: string
  targetAmount: number
  currentAmount: number
  startDate: string
  endDate: string | null
  isActive: boolean
  imageUrl: string | null
  progress: number
  remaining: number
}
```

#### إزالة من State:
```typescript
// حذف هذه المتغيرات
const [activeCampaigns, setActiveCampaigns] = useState<DonationCampaign[]>([])
const [isLoadingCampaigns, setIsLoadingCampaigns] = useState(false)
```

#### إزالة من useEffect:
- إزالة استدعاء `fetchActiveCampaigns`
- إزالة جلب الحملات من API

### 2. إضافة جلب معلومات التبرع من الإعدادات

#### إضافة State جديد:
```typescript
const [donationInfo, setDonationInfo] = useState<{
  phone1: string;
  phone2?: string;
  ccpAccount: string;
  cpaAccount: string;
  bdrAccount: string;
  description?: string;
} | null>(null)
const [isLoadingDonationInfo, setIsLoadingDonationInfo] = useState(false)
```

#### إضافة دالة جلب معلومات التبرع:
```typescript
const fetchDonationInfo = async () => {
  try {
    setIsLoadingDonationInfo(true)
    const response = await fetch('/api/settings')
    if (!response.ok) {
      throw new Error('فشل في جلب معلومات التبرع')
    }
    const data = await response.json()
    if (data.settings && data.settings.contactInfo && data.settings.contactInfo.donationInfo) {
      setDonationInfo(data.settings.contactInfo.donationInfo)
    }
  } catch (error) {
    console.error('خطأ في جلب معلومات التبرع:', error)
    toast.error('فشل في جلب معلومات التبرع')
  } finally {
    setIsLoadingDonationInfo(false)
  }
}
```

### 3. تعديل نموذج التبرع

#### إزالة حقل اختيار الحملة:
```typescript
// حذف هذا الحقل من formData
campaignId: null
```

#### تعديل دالة الإرسال:
```typescript
// إزالة campaignId من البيانات المرسلة
const donationData = {
  donorName: formData.donorName,
  amount: parseFloat(formData.amount),
  note: formData.note,
  paymentMethodId: selectedPaymentMethod?.id,
  ...(selectedPaymentMethod?.requiresCard && formData.cardDetails && {
    cardDetails: formData.cardDetails
  })
  // إزالة campaignId
}
```

### 4. إضافة قسم عرض معلومات التبرع

#### قسم معلومات التبرع الجديد:
```tsx
{/* قسم معلومات التبرع */}
{donationInfo && (
  <div className="bg-white p-8 rounded-lg shadow-md border border-[#e0f2ef] hover:shadow-lg transition-shadow duration-300">
    <h3 className="text-xl font-bold text-[var(--primary-color)] mb-4 flex items-center">
      <FaHandHoldingHeart className="ml-2" />
      معلومات التبرع
    </h3>
    
    {donationInfo.description && (
      <p className="text-gray-600 mb-6">{donationInfo.description}</p>
    )}
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* أرقام الهاتف */}
      <div className="space-y-3">
        <h4 className="font-semibold text-gray-800 flex items-center">
          <FaPhone className="ml-2 text-[var(--primary-color)]" />
          أرقام الهاتف
        </h4>
        <div className="space-y-2">
          <div className="flex items-center p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">الهاتف الأول:</span>
            <span className="mr-2 text-[var(--primary-color)] font-bold">{donationInfo.phone1}</span>
          </div>
          {donationInfo.phone2 && (
            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
              <span className="font-medium text-gray-700">الهاتف الثاني:</span>
              <span className="mr-2 text-[var(--primary-color)] font-bold">{donationInfo.phone2}</span>
            </div>
          )}
        </div>
      </div>
      
      {/* الحسابات البنكية */}
      <div className="space-y-3">
        <h4 className="font-semibold text-gray-800 flex items-center">
          <FaCreditCard className="ml-2 text-[var(--primary-color)]" />
          الحسابات البنكية
        </h4>
        <div className="space-y-2">
          <div className="flex items-center p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">حساب CCP:</span>
            <span className="mr-2 text-[var(--primary-color)] font-bold">{donationInfo.ccpAccount}</span>
          </div>
          <div className="flex items-center p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">حساب CPA:</span>
            <span className="mr-2 text-[var(--primary-color)] font-bold">{donationInfo.cpaAccount}</span>
          </div>
          <div className="flex items-center p-3 bg-gray-50 rounded-lg">
            <span className="font-medium text-gray-700">حساب BDR:</span>
            <span className="mr-2 text-[var(--primary-color)] font-bold">{donationInfo.bdrAccount}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
)}
```

### 5. إزالة أقسام الحملات

#### إزالة:
- قسم عرض الحملات النشطة
- إحصائيات الحملات
- شريط التقدم للحملات
- أي مراجع لـ `activeCampaigns`

### 6. تحديث الاستيرادات

#### إضافة:
```typescript
import { FaCreditCard } from 'react-icons/fa'
```

### 7. تنظيف الكود

#### إزالة:
- جميع الدوال المتعلقة بالحملات
- المتغيرات غير المستخدمة
- التعليقات المتعلقة بالحملات

## الهيكل الجديد للصفحة

1. **Hero Section** - العنوان والوصف
2. **نموذج التبرع** - بدون اختيار الحملة
3. **معلومات التبرع** - من الإعدادات
4. **معلومات عن الاستخدام** - كيف نستخدم التبرعات
5. **لماذا تدعمنا** - أهداف المشروع
6. **التبرعات الأخيرة** - إن وجدت
7. **Call to Action** - دعوة للتبرع

## ملاحظات مهمة

- الاحتفاظ بنموذج التبرع الأساسي
- إزالة جميع المراجع للحملات
- التأكد من عمل جلب معلومات التبرع من الإعدادات
- اختبار النموذج بدون campaignId
- التأكد من تحديث التقارير لعدم الاعتماد على الحملات

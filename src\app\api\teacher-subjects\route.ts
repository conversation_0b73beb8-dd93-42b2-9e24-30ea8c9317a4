import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    const teacherSubjects = await prisma.teacherSubject.findMany({
      include: {
        teacher: true,
        subject: true
      }
    });

    return NextResponse.json({
      success: true,
      data: teacherSubjects,
      message: "تم جلب علاقات المعلمين بالمواد بنجاح"
    });
  } catch (error) {
    console.error('Error fetching teacher subjects:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'حدث خطأ أثناء جلب علاقات المعلمين بالمواد',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
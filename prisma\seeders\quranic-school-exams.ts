import { PrismaClient, EvaluationType } from '@prisma/client';

const prisma = new PrismaClient();

// بيانات الامتحانات الفصلية النموذجية لمدرسة قرآنية
export async function seedQuranicSchoolExams() {
  console.log('🕌 بدء إنشاء الامتحانات الفصلية للمدرسة القرآنية...');

  try {
    // الحصول على المواد الموجودة
    const subjects = await prisma.subject.findMany();
    const examTypes = await prisma.examType.findMany();
    const students = await prisma.student.findMany({ include: { classe: true } });
    const classes = await prisma.classe.findMany();

    console.log(`📚 تم العثور على ${subjects.length} مادة، ${students.length} طالب، ${classes.length} فصل`);

    // إنشاء المواد الأساسية إذا لم تكن موجودة
    const defaultSubjects = [
      { name: 'حفظ القرآن الكريم', description: 'حفظ وتسميع القرآن الكريم' },
      { name: 'تلاوة وتجويد', description: 'تلاوة القرآن الكريم وأحكام التجويد' },
      { name: 'التفسير', description: 'تفسير آيات القرآن الكريم' },
      { name: 'الحديث الشريف', description: 'دراسة الأحاديث النبوية الشريفة' },
      { name: 'الفقه الإسلامي', description: 'أحكام الفقه الإسلامي' },
      { name: 'السيرة النبوية', description: 'سيرة الرسول صلى الله عليه وسلم' },
      { name: 'العقيدة الإسلامية', description: 'أصول العقيدة الإسلامية' },
      { name: 'الأخلاق والآداب', description: 'الأخلاق الإسلامية والآداب' }
    ];

    for (const subject of defaultSubjects) {
      await prisma.subject.upsert({
        where: { name: subject.name },
        update: {},
        create: subject
      });
    }

    // إنشاء أنواع الامتحانات إذا لم تكن موجودة
    const defaultExamTypes = [
      { name: 'امتحان شهري', description: 'امتحان شهري' },
      { name: 'امتحان فصلي', description: 'امتحان نهاية الفصل' },
      { name: 'امتحان نصف السنة', description: 'امتحان نصف السنة الدراسية' },
      { name: 'امتحان نهاية السنة', description: 'امتحان نهاية السنة الدراسية' }
    ];

    for (const examType of defaultExamTypes) {
      await prisma.examType.upsert({
        where: { name: examType.name },
        update: {},
        create: examType
      });
    }

    // تحديث البيانات
    const updatedSubjects = await prisma.subject.findMany();
    const updatedExamTypes = await prisma.examType.findMany();

    // تعريف الفصول الدراسية
    const academicTerms = [
      {
        name: 'الفصل الأول',
        months: ['2024-09', '2024-10', '2024-11', '2024-12'],
        description: 'الفصل الدراسي الأول'
      },
      {
        name: 'الفصل الثاني', 
        months: ['2025-01', '2025-02', '2025-03'],
        description: 'الفصل الدراسي الثاني'
      },
      {
        name: 'الفصل الثالث',
        months: ['2025-04', '2025-05', '2025-06'],
        description: 'الفصل الدراسي الثالث'
      }
    ];

    // إنشاء الامتحانات لكل فصل دراسي
    for (const term of academicTerms) {
      console.log(`📝 إنشاء امتحانات ${term.name}...`);

      for (const month of term.months) {
        // امتحان شهري لكل مادة
        for (const subject of updatedSubjects) {
          const monthlyExamType = updatedExamTypes.find(et => et.name === 'امتحان شهري');
          
          if (monthlyExamType) {
            const exam = await prisma.exam.create({
              data: {
                evaluationType: getEvaluationTypeForSubject(subject.name),
                month: month,
                description: `امتحان شهري - ${subject.name} - ${getMonthName(month)}`,
                maxPoints: 20,
                passingPoints: 10,
                subjectId: subject.id,
                typeId: monthlyExamType.id
              }
            });

            // إنشاء نقاط للطلاب
            await createExamPointsForStudents(exam.id, students, subject.name);
          }
        }

        // امتحان فصلي في نهاية كل فصل
        if (month === term.months[term.months.length - 1]) {
          for (const subject of updatedSubjects) {
            const termExamType = updatedExamTypes.find(et => et.name === 'امتحان فصلي');
            
            if (termExamType) {
              const exam = await prisma.exam.create({
                data: {
                  evaluationType: getEvaluationTypeForSubject(subject.name),
                  month: month,
                  description: `امتحان ${term.name} - ${subject.name}`,
                  maxPoints: 20,
                  passingPoints: 10,
                  subjectId: subject.id,
                  typeId: termExamType.id
                }
              });

              // إنشاء نقاط للطلاب مع درجات أعلى للامتحانات الفصلية
              await createExamPointsForStudents(exam.id, students, subject.name, true);
            }
          }
        }
      }
    }

    console.log('✅ تم إنشاء جميع الامتحانات الفصلية بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء الامتحانات:', error);
    throw error;
  }
}

// دالة لتحديد نوع التقييم حسب المادة
function getEvaluationTypeForSubject(subjectName: string) {
  const evaluationMap: Record<string, any> = {
    'حفظ القرآن الكريم': 'QURAN_MEMORIZATION',
    'تلاوة وتجويد': 'QURAN_RECITATION',
    'التفسير': 'WRITTEN_EXAM',
    'الحديث الشريف': 'WRITTEN_EXAM',
    'الفقه الإسلامي': 'WRITTEN_EXAM',
    'السيرة النبوية': 'WRITTEN_EXAM',
    'العقيدة الإسلامية': 'WRITTEN_EXAM',
    'الأخلاق والآداب': 'ORAL_EXAM'
  };

  return evaluationMap[subjectName] || 'WRITTEN_EXAM';
}

// دالة لتحويل الشهر إلى اسم عربي
function getMonthName(month: string): string {
  const monthNames: Record<string, string> = {
    '2024-09': 'سبتمبر 2024',
    '2024-10': 'أكتوبر 2024', 
    '2024-11': 'نوفمبر 2024',
    '2024-12': 'ديسمبر 2024',
    '2025-01': 'يناير 2025',
    '2025-02': 'فبراير 2025',
    '2025-03': 'مارس 2025',
    '2025-04': 'أبريل 2025',
    '2025-05': 'مايو 2025',
    '2025-06': 'يونيو 2025'
  };

  return monthNames[month] || month;
}

// دالة لإنشاء نقاط الامتحانات للطلاب
async function createExamPointsForStudents(
  examId: number, 
  students: any[], 
  subjectName: string, 
  isTermExam: boolean = false
) {
  for (const student of students) {
    // حساب الدرجة بناءً على نوع المادة ومستوى الطالب
    const baseGrade = calculateStudentGrade(student, subjectName, isTermExam);
    
    await prisma.exam_points.create({
      data: {
        examId: examId,
        studentId: student.id,
        grade: baseGrade,
        status: baseGrade >= 10 ? 'PASSED' : 'FAILED',
        note: generateStudentNote(baseGrade, subjectName),
        feedback: generateStudentFeedback(baseGrade, subjectName)
      }
    });
  }
}

// دالة لحساب درجة الطالب
function calculateStudentGrade(student: any, subjectName: string, isTermExam: boolean): number {
  // محاكاة مستويات مختلفة للطلاب
  const studentLevel = (student.id % 4) + 1; // مستويات من 1 إلى 4
  
  let baseGrade: number;
  
  switch (studentLevel) {
    case 1: // طالب ممتاز
      baseGrade = isTermExam ? 18 + Math.random() * 2 : 16 + Math.random() * 3;
      break;
    case 2: // طالب جيد جداً
      baseGrade = isTermExam ? 15 + Math.random() * 3 : 13 + Math.random() * 4;
      break;
    case 3: // طالب جيد
      baseGrade = isTermExam ? 12 + Math.random() * 3 : 10 + Math.random() * 4;
      break;
    case 4: // طالب يحتاج تحسين
      baseGrade = isTermExam ? 8 + Math.random() * 4 : 6 + Math.random() * 6;
      break;
    default:
      baseGrade = 10 + Math.random() * 8;
  }

  // تعديل الدرجة حسب نوع المادة
  if (subjectName.includes('حفظ القرآن')) {
    baseGrade += 1; // الطلاب عادة أفضل في الحفظ
  } else if (subjectName.includes('تلاوة')) {
    baseGrade += 0.5;
  }

  // التأكد من أن الدرجة في النطاق الصحيح
  return Math.min(Math.max(Math.round(baseGrade * 10) / 10, 0), 20);
}

// دالة لإنشاء ملاحظة للطالب
function generateStudentNote(grade: number, subjectName: string): string {
  if (grade >= 18) {
    return `أداء ممتاز في ${subjectName}. استمر على هذا المستوى المتميز.`;
  } else if (grade >= 15) {
    return `أداء جيد جداً في ${subjectName}. يمكن تحسينه أكثر.`;
  } else if (grade >= 12) {
    return `أداء جيد في ${subjectName}. يحتاج إلى مزيد من المراجعة.`;
  } else if (grade >= 10) {
    return `أداء مقبول في ${subjectName}. يحتاج إلى تركيز أكبر.`;
  } else {
    return `أداء ضعيف في ${subjectName}. يحتاج إلى مراجعة شاملة ومتابعة خاصة.`;
  }
}

// دالة لإنشاء تغذية راجعة للطالب
function generateStudentFeedback(grade: number, subjectName: string): string {
  const feedbacks = {
    excellent: [
      'بارك الله فيك، أداء رائع!',
      'ممتاز، واصل التفوق!',
      'أحسنت، نموذج يُحتذى به!'
    ],
    good: [
      'جيد، يمكن تحسينه أكثر',
      'أداء طيب، استمر في المراجعة',
      'بداية جيدة، واصل الجهد'
    ],
    needsImprovement: [
      'يحتاج إلى مزيد من المراجعة',
      'راجع الدروس مرة أخرى',
      'اطلب المساعدة من المعلم'
    ]
  };

  if (grade >= 15) {
    return feedbacks.excellent[Math.floor(Math.random() * feedbacks.excellent.length)];
  } else if (grade >= 10) {
    return feedbacks.good[Math.floor(Math.random() * feedbacks.good.length)];
  } else {
    return feedbacks.needsImprovement[Math.floor(Math.random() * feedbacks.needsImprovement.length)];
  }
}

export default seedQuranicSchoolExams;

import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { EvaluationType } from '@prisma/client';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "معرف الامتحان غير صالح", success: false },
        { status: 400 }
      );
    }

    const body = await request.json();
    console.log('PATCH /api/evaluation/exams/[id] - Request body:', body);

    const {
      evaluationType,
      month,
      requiresSurah = false,
      description = null,
      maxPoints = 100,
      passingPoints = 60,
      typeId = null,
      criteriaIds = [],
      subjectId = null,
      isPeriodic = false,
      period = null
    } = body;

    console.log('Parsed criteriaIds:', criteriaIds);

    // التحقق من صحة البيانات
    if (!evaluationType || typeof evaluationType !== 'string') {
      return NextResponse.json(
        {
          success: false,
          error: "نوع التقييم مطلوب وغير صالح",
          data: null
        },
        { status: 400 }
      );
    }

    if (!month || typeof month !== 'string' || !month.match(/^\d{4}-\d{2}$/)) {
      return NextResponse.json(
        {
          success: false,
          error: "الشهر مطلوب وغير صالح (يجب أن يكون بتنسيق YYYY-MM)",
          data: null
        },
        { status: 400 }
      );
    }

    // التحقق من وجود الامتحان
    const existingExam = await prisma.exam.findUnique({
      where: { id }
    });

    if (!existingExam) {
      return NextResponse.json(
        {
          success: false,
          error: "لم يتم العثور على الامتحان",
          data: null
        },
        { status: 404 }
      );
    }

    // السماح بتعديل الامتحانات دون قيود التكرار
    // تم إزالة التحقق من التكرار للسماح بمرونة أكبر في تعديل الامتحانات

    // تعيين requiresSurah تلقائيًا إذا كان نوع الامتحان هو حفظ القرآن
    const autoRequiresSurah = evaluationType === 'QURAN_MEMORIZATION' ? true : requiresSurah;

    // تحديث الامتحان وعلاقاته بمعايير التقييم
    const updatedExam = await prisma.$transaction(async (tx) => {
      // تحديث الامتحان
      // التحقق من وجود المادة الدراسية إذا تم تحديدها
      if (subjectId) {
        const subject = await tx.subject.findUnique({
          where: { id: parseInt(subjectId) }
        });

        if (!subject) {
          throw new Error("المادة الدراسية المحددة غير موجودة");
        }
      }

      const exam = await tx.exam.update({
        where: { id },
        data: {
          evaluationType: evaluationType as EvaluationType,
          month,
          requiresSurah: autoRequiresSurah,
          description,
          maxPoints: Number(maxPoints),
          passingPoints: Number(passingPoints),
          typeId: typeId ? Number(typeId) : null,
          subjectId: subjectId ? Number(subjectId) : null,
          isPeriodic: Boolean(isPeriodic),
          period: period,
        },
        include: {
          examType: true,
          subject: true
        }
      });

      // حذف جميع علاقات معايير التقييم الحالية
      await tx.examCriteria.deleteMany({
        where: { examId: id }
      });

      // إضافة علاقات معايير التقييم الجديدة
      try {
        if (criteriaIds && Array.isArray(criteriaIds) && criteriaIds.length > 0) {
          console.log(`Updating criteria for exam ${id}: ${criteriaIds.join(', ')}`);

          // استخدام createMany بدلاً من create في حلقة
          await tx.examCriteria.createMany({
            data: criteriaIds.map(criteriaId => ({
              examId: id,
              criteriaId: Number(criteriaId)
            }))
          });
        }
      } catch (criteriaError) {
        console.error("Error creating exam criteria:", criteriaError);
        if (criteriaError instanceof Error) {
          console.error("Criteria error details:", criteriaError.message);
          console.error("Criteria error stack:", criteriaError.stack);
        }
        // رمي الخطأ مرة أخرى للتعامل معه في الـ catch الخارجي
        throw criteriaError;
      }

      return exam;
    });

    return NextResponse.json(
      {
        data: updatedExam,
        success: true,
        message: "تم تحديث الامتحان بنجاح"
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating exam:", error);
    // طباعة تفاصيل الخطأ للتشخيص
    if (error instanceof Error) {
      console.error("Error details:", error.message);
      console.error("Error stack:", error.stack);
    }
    return NextResponse.json(
      {
        error: "حدث خطأ أثناء تحديث الامتحان",
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "معرف الامتحان غير صالح", success: false },
        { status: 400 }
      );
    }

    const exam = await prisma.exam.findUnique({
      where: { id },
      include: {
        examType: true,
        subject: true,
        examCriteria: {
          include: {
            criteria: true
          }
        },
        exam_points: {
          include: {
            student: {
              include: {
                classe: true
              }
            },
            classSubject: {
              include: {
                classe: true
              }
            }
          }
        }
      }
    });

    if (!exam) {
      return NextResponse.json(
        {
          success: false,
          error: "لم يتم العثور على الامتحان",
          data: null
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: exam,
      success: true,
      message: "تم جلب الامتحان بنجاح"
    });
  } catch (error) {
    console.error("Error fetching exam:", error);
    return NextResponse.json(
      {
        error: "حدث خطأ أثناء جلب الامتحان",
        success: false,
        data: null
      },
      { status: 500 }
    );
  }
}

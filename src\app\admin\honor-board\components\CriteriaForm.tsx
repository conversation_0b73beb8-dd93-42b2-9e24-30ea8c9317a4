import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'react-hot-toast';

type CriteriaFormProps = {
  initialData?: {
    id?: number;
    name: string;
    description: string;
    pointsThreshold: number;
    isActive: boolean;
  };
  onSuccess?: () => void;
  onCancel?: () => void;
};

export default function CriteriaForm({
  initialData = {
    name: '',
    description: '',
    pointsThreshold: 0,
    isActive: true
  },
  onSuccess,
  onCancel
}: CriteriaFormProps) {
  const [formData, setFormData] = useState(initialData);
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'pointsThreshold' ? parseInt(value) || 0 : value
    }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      isActive: checked
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // التحقق من صحة البيانات
      if (!formData.name.trim()) {
        toast.error('اسم المعيار مطلوب');
        return;
      }

      if (formData.pointsThreshold < 0) {
        toast.error('يجب أن يكون الحد الأدنى للنقاط قيمة موجبة');
        return;
      }

      const url = initialData.id 
        ? `/api/honor-criteria?id=${initialData.id}` 
        : '/api/honor-criteria';
      
      const method = initialData.id ? 'PATCH' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء حفظ المعيار');
      }
      
      const data = await response.json();
      toast.success(data.message || 'تم حفظ المعيار بنجاح');
      
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error saving criteria:', error);
      toast.error(error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ المعيار');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{initialData.id ? 'تعديل معيار التقييم' : 'إضافة معيار تقييم جديد'}</CardTitle>
        <CardDescription>
          {initialData.id 
            ? 'قم بتعديل تفاصيل معيار التقييم' 
            : 'أدخل تفاصيل معيار التقييم الجديد للطلاب المتميزين'}
        </CardDescription>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">اسم المعيار</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="مثال: التفوق الدراسي"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">الوصف</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="وصف تفصيلي للمعيار وكيفية تطبيقه"
              rows={3}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="pointsThreshold">الحد الأدنى للنقاط</Label>
            <Input
              id="pointsThreshold"
              name="pointsThreshold"
              type="number"
              min="0"
              value={formData.pointsThreshold}
              onChange={handleInputChange}
              placeholder="100"
            />
            <p className="text-sm text-gray-500">
              الحد الأدنى من النقاط التي يجب أن يحصل عليها الطالب لتحقيق هذا المعيار
            </p>
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={handleSwitchChange}
            />
            <Label htmlFor="isActive">نشط</Label>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={loading}
          >
            إلغاء
          </Button>
          <Button 
            type="submit" 
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]"
            disabled={loading}
          >
            {loading ? 'جاري الحفظ...' : initialData.id ? 'حفظ التغييرات' : 'إضافة المعيار'}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}

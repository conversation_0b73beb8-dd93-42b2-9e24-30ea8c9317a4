'use client';

import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { FaPrint, FaFileExcel, FaFilePdf, FaShareAlt } from 'react-icons/fa';
import { exportToExcel, exportToPdf } from '@/utils/export-utils';
import { useReactToPrint } from 'react-to-print';
import { toast } from 'react-toastify';
import ShareReportDialog from './ShareReportDialog';

// استيراد أنماط الطباعة
import '@/styles/print.css';

interface PrintableReportProps {
  title: string;
  fileName: string;
  children: React.ReactNode;
  excelData?: Record<string, unknown>[];
  pdfOptions?: {
    tables: {
      title?: string;
      headers: string[];
      data: (string | number | null)[][];
    }[];
    charts?: {
      title?: string;
      type: 'bar' | 'line' | 'pie' | 'doughnut';
      data: {
        labels: string[];
        datasets: {
          label: string;
          data: number[];
          backgroundColor?: string | string[];
          borderColor?: string | string[];
          borderWidth?: number;
        }[];
      };
    }[];
  };
  showShareButton?: boolean;
  reportId?: string;
  reportType?: string;
}

/**
 * مكون التقرير القابل للطباعة
 * يوفر واجهة موحدة لعرض وطباعة وتصدير التقارير
 */
const PrintableReport: React.FC<PrintableReportProps> = ({
  title,
  fileName,
  children,
  excelData,
  pdfOptions,
  showShareButton = true,
  reportId,
  reportType
}) => {
  const reportRef = useRef<HTMLDivElement>(null);
  const [isShareDialogOpen, setIsShareDialogOpen] = React.useState(false);

  // تاريخ إنشاء التقرير
  const reportDate = new Date().toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  // طباعة التقرير
  const handlePrint = useReactToPrint({
    contentRef: reportRef,
    documentTitle: title,
    onBeforePrint: () => {
      toast.info('جاري تحضير التقرير للطباعة...');
      return Promise.resolve();
    },
    onAfterPrint: () => {
      toast.success('تمت طباعة التقرير بنجاح');
    }
  });

  // تصدير التقرير إلى Excel
  const handleExportToExcel = () => {
    if (!excelData || excelData.length === 0) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      exportToExcel(
        excelData,
        `${fileName}.xlsx`,
        title
      );
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  // تصدير التقرير إلى PDF
  const handleExportToPdf = () => {
    if (!pdfOptions) {
      toast.error('لا توجد بيانات للتصدير');
      return;
    }

    try {
      exportToPdf({
        title,
        fileName: `${fileName}.pdf`,
        tables: pdfOptions.tables,
        charts: pdfOptions.charts
      });
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  return (
    <div>
      {/* أزرار الإجراءات */}
      <div className="flex justify-end gap-2 mb-4 print:hidden">
        <Button
          onClick={() => handlePrint()}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white flex items-center gap-1"
        >
          <FaPrint />
          <span>طباعة</span>
        </Button>

        {excelData && (
          <Button
            onClick={handleExportToExcel}
            className="bg-primary-color hover:bg-green-700 text-white flex items-center gap-1"
          >
            <FaFileExcel />
            <span>Excel</span>
          </Button>
        )}

        {pdfOptions && (
          <Button
            onClick={handleExportToPdf}
            className="bg-red-600 hover:bg-red-700 text-white flex items-center gap-1"
          >
            <FaFilePdf />
            <span>PDF</span>
          </Button>
        )}

        {showShareButton && (
          <Button
            onClick={() => setIsShareDialogOpen(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-1"
          >
            <FaShareAlt />
            <span>مشاركة</span>
          </Button>
        )}
      </div>

      {/* محتوى التقرير القابل للطباعة */}
      <div ref={reportRef} className="print-container">
        {/* رأس الصفحة للطباعة فقط */}
        <div className="print-only print-header">
          <div className="text-center">
            <h2>{title}</h2>
          </div>
        </div>

        {/* محتوى التقرير */}
        <div className="print-content">
          {/* عنوان التقرير */}
          <Card className="mb-6 print:border-0 print:shadow-none">
            <CardHeader className="pb-2">
              <CardTitle className="text-2xl text-center">{title}</CardTitle>
              <p className="text-center text-gray-500 text-sm">
                تاريخ التقرير: {reportDate}
              </p>
            </CardHeader>
          </Card>

          {/* محتوى التقرير الفعلي */}
          {children}
        </div>

        {/* تذييل الصفحة للطباعة فقط */}
        <div className="print-only print-footer">
          <div className="text-center">
            <p>
              تم إنشاء هذا التقرير بواسطة نظام إدارة مدرسة القرآن
              <br />
              صفحة <span className="page-number"></span>
            </p>
          </div>
        </div>
      </div>

      {/* نافذة مشاركة التقرير */}
      {showShareButton && reportId && reportType && (
        <ShareReportDialog
          isOpen={isShareDialogOpen}
          onClose={() => setIsShareDialogOpen(false)}
          reportId={reportId}
          reportType={reportType}
          reportTitle={title}
        />
      )}
    </div>
  );
};

export default PrintableReport;

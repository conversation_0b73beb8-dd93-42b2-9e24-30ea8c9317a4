import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

// GET /api/programs - جلب جميع البرامج
export async function GET() {
  try {
    const programs = await prisma.program.findMany({
      include: {
        features: {
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    return NextResponse.json(programs);
  } catch (error) {
    console.error('Error fetching programs:', error);
    return NextResponse.json(
      { error: 'فشل في جلب البرامج' },
      { status: 500 }
    );
  }
}

// POST /api/programs - إضافة برنامج جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, description, iconName, price, popular, features } = body;

    // التحقق من وجود الحقول المطلوبة
    if (!title || !description || !iconName || !price) {
      return NextResponse.json(
        { error: 'جميع الحقول المطلوبة يجب ملؤها' },
        { status: 400 }
      );
    }

    // إنشاء البرنامج مع ميزاته
    const program = await prisma.program.create({
      data: {
        title,
        description,
        iconName,
        price,
        popular: popular || false,
        features: {
          create: features.map((feature: string, index: number) => ({
            text: feature,
            order: index
          }))
        }
      },
      include: {
        features: true
      }
    });

    return NextResponse.json(program, { status: 201 });
  } catch (error) {
    console.error('Error creating program:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء البرنامج' },
      { status: 500 }
    );
  }
}

// PUT /api/programs - تحديث برنامج
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, title, description, iconName, price, popular, features } = body;

    // التحقق من وجود المعرف
    if (!id) {
      return NextResponse.json(
        { error: 'معرف البرنامج مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود البرنامج
    const existingProgram = await prisma.program.findUnique({
      where: { id: Number(id) },
      include: { features: true }
    });

    if (!existingProgram) {
      return NextResponse.json(
        { error: 'البرنامج غير موجود' },
        { status: 404 }
      );
    }

    // تحديث البرنامج
    const updatedProgram = await prisma.$transaction(async (tx) => {
      // حذف الميزات الحالية
      await tx.programFeature.deleteMany({
        where: { programId: Number(id) }
      });

      // تحديث البرنامج وإضافة ميزات جديدة
      return tx.program.update({
        where: { id: Number(id) },
        data: {
          title,
          description,
          iconName,
          price,
          popular: popular || false,
          features: {
            create: features.map((feature: string, index: number) => ({
              text: feature,
              order: index
            }))
          }
        },
        include: {
          features: {
            orderBy: {
              order: 'asc'
            }
          }
        }
      });
    });

    return NextResponse.json(updatedProgram);
  } catch (error) {
    console.error('Error updating program:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث البرنامج' },
      { status: 500 }
    );
  }
}

// DELETE /api/programs - حذف برنامج
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'معرف البرنامج مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود البرنامج
    const existingProgram = await prisma.program.findUnique({
      where: { id: Number(id) }
    });

    if (!existingProgram) {
      return NextResponse.json(
        { error: 'البرنامج غير موجود' },
        { status: 404 }
      );
    }

    // حذف البرنامج (سيتم حذف الميزات تلقائيًا بسبب onDelete: Cascade)
    await prisma.program.delete({
      where: { id: Number(id) }
    });

    return NextResponse.json({ message: 'تم حذف البرنامج بنجاح' });
  } catch (error) {
    console.error('Error deleting program:', error);
    return NextResponse.json(
      { error: 'فشل في حذف البرنامج' },
      { status: 500 }
    );
  }
}

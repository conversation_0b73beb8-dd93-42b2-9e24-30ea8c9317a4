# 🔧 إصلاحات APIs - نظام المدفوعات والفواتير

## 📋 نظرة عامة
خطة تفصيلية لإصلاح جميع APIs المتعلقة بالمدفوعات والفواتير لضمان عملها بطريقة صحيحة ومحسنة.

## 🚨 إصلاحات حرجة (أولوية عالية جداً)

### 1. إصلاح API المدفوعات الإدارية
**الملف:** `src/app/api/admin/payments/route.ts`

#### المشكلة الحالية
```typescript
// إنشاء دفعة رئيسية
const payment = await prisma.payment.create({...});

// ثم إنشاء دفعات إضافية لكل فاتورة (خطأ!)
for (const invoice of student.invoices) {
  await prisma.payment.create({...}); // دفعة مكررة!
}
```

#### الحل المطلوب
```typescript
// إنشاء دفعة واحدة فقط
const payment = await prisma.payment.create({
  data: {
    studentId: studentId,
    amount: parseFloat(amount),
    date: paymentDate,
    status: 'PAID',
    paymentMethodId: paymentMethodRecord.id,
    paymentMethodName: paymentMethod,
    notes: finalNotes,
    receiptNumber: receiptNumber || undefined
  }
});

// ربط الدفعة بالفواتير بدلاً من إنشاء دفعات جديدة
let remainingAmount = parseFloat(amount);
const invoicePayments = [];

for (const invoice of student.invoices) {
  if (remainingAmount <= 0) break;
  
  const paidAmount = await calculateInvoicePaidAmount(invoice.id);
  const remainingInvoiceAmount = invoice.amount - paidAmount;
  
  if (remainingInvoiceAmount > 0) {
    const paymentForInvoice = Math.min(remainingAmount, remainingInvoiceAmount);
    
    // إنشاء سجل ربط بدلاً من دفعة جديدة
    await prisma.invoicePayment.create({
      data: {
        invoiceId: invoice.id,
        paymentId: payment.id,
        amount: paymentForInvoice
      }
    });
    
    remainingAmount -= paymentForInvoice;
    invoicePayments.push({
      invoiceId: invoice.id,
      amount: paymentForInvoice
    });
    
    // تحديث حالة الفاتورة
    await updateInvoiceStatus(invoice.id);
  }
}
```

#### تحسينات إضافية
```typescript
// إضافة التحقق من صحة البيانات
function validatePaymentData(data) {
  const errors = [];
  
  if (!data.studentId || !Number.isInteger(data.studentId)) {
    errors.push('معرف التلميذ غير صحيح');
  }
  
  if (!data.amount || data.amount <= 0) {
    errors.push('مبلغ الدفعة يجب أن يكون أكبر من صفر');
  }
  
  if (!data.paymentMethod || data.paymentMethod.trim() === '') {
    errors.push('طريقة الدفع مطلوبة');
  }
  
  return errors;
}

// استخدام transaction لضمان سلامة البيانات
const result = await prisma.$transaction(async (tx) => {
  // إنشاء الدفعة
  const payment = await tx.payment.create({...});
  
  // ربط الفواتير
  for (const invoicePayment of invoicePayments) {
    await tx.invoicePayment.create({...});
    await updateInvoiceStatus(invoicePayment.invoiceId, tx);
  }
  
  return payment;
});
```

---

### 2. إصلاح API المدفوعات حسب الولي
**الملف:** `src/app/api/payments/by-parent/route.ts`

#### المشكلة الحالية
```typescript
// استعلام بطيء ومعقد
const parents = await prisma.parent.findMany({
  include: {
    invoices: { include: { payments: true } },
    students: { include: { invoices: { include: { payments: true } } } }
  }
});

// حسابات معقدة في الكود
const studentTotalRequired = student.invoices
  .filter(invoice => invoice.status !== 'CANCELLED')
  .reduce((sum, invoice) => sum + invoice.amount, 0);
```

#### الحل المطلوب
```typescript
// استخدام Raw SQL للحسابات المعقدة
const parentSummaries = await prisma.$queryRaw`
  SELECT 
    p.id,
    p.name,
    p.phone,
    p.email,
    COUNT(DISTINCT s.id) as totalStudents,
    COALESCE(SUM(CASE 
      WHEN i.status NOT IN ('CANCELLED') THEN i.amount 
      ELSE 0 
    END), 0) as totalRequired,
    COALESCE(SUM(CASE 
      WHEN pay.status = 'PAID' THEN pay.amount 
      ELSE 0 
    END), 0) as totalPaid,
    MAX(pay.date) as lastPaymentDate
  FROM Parent p
  LEFT JOIN Student s ON p.id = s.guardianId
  LEFT JOIN Invoice i ON s.id = i.studentId
  LEFT JOIN Payment pay ON s.id = pay.studentId
  WHERE 1=1
    ${search ? Prisma.sql`AND (p.name ILIKE ${`%${search}%`} OR p.phone ILIKE ${`%${search}%`})` : Prisma.empty}
    ${month ? Prisma.sql`AND EXTRACT(YEAR_MONTH FROM i.issueDate) = ${month.replace('-', '')}` : Prisma.empty}
  GROUP BY p.id, p.name, p.phone, p.email
  ORDER BY p.name
  LIMIT ${limit} OFFSET ${skip}
`;

// حساب الحالات بكفاءة
const enrichedSummaries = parentSummaries.map(parent => {
  const totalRemaining = Math.max(0, parent.totalRequired - parent.totalPaid);
  const paymentRate = parent.totalRequired > 0 
    ? Math.round((parent.totalPaid / parent.totalRequired) * 100) 
    : 0;
    
  let paymentStatus = 'UNPAID';
  if (parent.totalRequired === 0 || totalRemaining <= 0) {
    paymentStatus = 'PAID';
  } else if (parent.totalPaid > 0) {
    paymentStatus = 'PARTIAL';
  }
  
  return {
    ...parent,
    totalRemaining,
    paymentRate,
    paymentStatus
  };
});
```

#### إضافة تخزين مؤقت
```typescript
import { Redis } from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

// تخزين مؤقت للنتائج
const cacheKey = `payments-by-parent:${search}:${status}:${month}:${page}`;
const cachedResult = await redis.get(cacheKey);

if (cachedResult) {
  return NextResponse.json(JSON.parse(cachedResult));
}

// جلب البيانات وتخزينها
const result = await fetchPaymentsByParent(params);
await redis.setex(cacheKey, 300, JSON.stringify(result)); // 5 دقائق

return NextResponse.json(result);
```

---

### 3. إصلاح API الفواتير
**الملف:** `src/app/api/invoices/route.ts`

#### المشكلة الحالية
```typescript
// لا يتحقق من نوع الفاتورة
if (!studentId || !amount || !dueDate || !month || !year) {
  return NextResponse.json({ error: 'الحقول المطلوبة غير مكتملة' });
}
```

#### الحل المطلوب
```typescript
// التحقق الشامل من البيانات
function validateInvoiceData(data) {
  const errors = [];
  
  // التحقق من النوع والمتطلبات
  if (data.type === 'INDIVIDUAL') {
    if (!data.studentId) {
      errors.push('معرف التلميذ مطلوب للفواتير الفردية');
    }
    if (data.parentId) {
      errors.push('لا يمكن تحديد معرف الولي للفواتير الفردية');
    }
  } else if (data.type === 'FAMILY') {
    if (!data.parentId) {
      errors.push('معرف الولي مطلوب للفواتير الجماعية');
    }
    if (data.studentId) {
      errors.push('لا يمكن تحديد معرف التلميذ للفواتير الجماعية');
    }
  } else {
    errors.push('نوع الفاتورة غير صحيح');
  }
  
  // التحقق من المبلغ
  if (!data.amount || data.amount <= 0) {
    errors.push('مبلغ الفاتورة يجب أن يكون أكبر من صفر');
  }
  
  // التحقق من التواريخ
  if (!data.dueDate || !data.issueDate) {
    errors.push('تواريخ الفاتورة مطلوبة');
  } else if (new Date(data.dueDate) <= new Date(data.issueDate)) {
    errors.push('تاريخ الاستحقاق يجب أن يكون بعد تاريخ الإصدار');
  }
  
  return errors;
}

// API محسن لإنشاء الفاتورة
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // التحقق من البيانات
    const validationErrors = validateInvoiceData(body);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'بيانات غير صحيحة', details: validationErrors },
        { status: 400 }
      );
    }
    
    // التحقق من وجود الكيان المرتبط
    if (body.type === 'INDIVIDUAL') {
      const student = await prisma.student.findUnique({
        where: { id: body.studentId }
      });
      if (!student) {
        return NextResponse.json(
          { error: 'التلميذ غير موجود' },
          { status: 404 }
        );
      }
    } else {
      const parent = await prisma.parent.findUnique({
        where: { id: body.parentId },
        include: { students: true }
      });
      if (!parent) {
        return NextResponse.json(
          { error: 'الولي غير موجود' },
          { status: 404 }
        );
      }
      if (parent.students.length === 0) {
        return NextResponse.json(
          { error: 'الولي ليس لديه أبناء مسجلون' },
          { status: 400 }
        );
      }
    }
    
    // إنشاء الفاتورة
    const invoice = await prisma.invoice.create({
      data: {
        studentId: body.type === 'INDIVIDUAL' ? body.studentId : null,
        parentId: body.type === 'FAMILY' ? body.parentId : null,
        amount: body.amount,
        dueDate: new Date(body.dueDate),
        issueDate: new Date(body.issueDate || new Date()),
        month: body.month,
        year: body.year,
        description: body.description,
        type: body.type,
        status: 'UNPAID'
      },
      include: {
        student: true,
        parent: true
      }
    });
    
    return NextResponse.json({
      success: true,
      invoice,
      message: 'تم إنشاء الفاتورة بنجاح'
    });
    
  } catch (error) {
    console.error('Error creating invoice:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء إنشاء الفاتورة' },
      { status: 500 }
    );
  }
}
```

---

## 🔧 دوال مساعدة جديدة

### 1. حساب المبلغ المدفوع للفاتورة
```typescript
// utils/invoice-calculations.ts
export async function calculateInvoicePaidAmount(invoiceId: number): Promise<number> {
  const result = await prisma.payment.aggregate({
    where: {
      invoiceId: invoiceId,
      status: 'PAID'
    },
    _sum: {
      amount: true
    }
  });
  
  return result._sum.amount || 0;
}
```

### 2. تحديث حالة الفاتورة
```typescript
export async function updateInvoiceStatus(invoiceId: number, tx?: any): Promise<void> {
  const prismaClient = tx || prisma;
  
  const invoice = await prismaClient.invoice.findUnique({
    where: { id: invoiceId }
  });
  
  if (!invoice) return;
  
  const paidAmount = await calculateInvoicePaidAmount(invoiceId);
  const now = new Date();
  
  let newStatus: InvoiceStatus;
  
  if (paidAmount >= invoice.amount) {
    newStatus = 'PAID';
  } else if (paidAmount > 0) {
    newStatus = 'PARTIALLY_PAID';
  } else if (invoice.dueDate < now) {
    newStatus = 'OVERDUE';
  } else {
    newStatus = 'UNPAID';
  }
  
  if (newStatus !== invoice.status) {
    await prismaClient.invoice.update({
      where: { id: invoiceId },
      data: { status: newStatus }
    });
  }
}
```

### 3. تنظيف وتحقق البيانات
```typescript
// utils/data-validation.ts
export function sanitizeInput(input: any): any {
  if (typeof input === 'string') {
    return input.trim().replace(/[<>]/g, '');
  }
  if (typeof input === 'number') {
    return Math.abs(input);
  }
  return input;
}

export function validateAmount(amount: any): boolean {
  return typeof amount === 'number' && amount > 0 && amount < 1000000;
}

export function validateDate(date: any): boolean {
  const parsedDate = new Date(date);
  return !isNaN(parsedDate.getTime()) && parsedDate > new Date('2020-01-01');
}
```

---

## 📊 تحسينات الأداء

### 1. استخدام الفهارس المحسنة
```sql
-- إضافة فهارس مركبة للاستعلامات الشائعة
CREATE INDEX idx_payment_student_date ON Payment(studentId, date DESC);
CREATE INDEX idx_invoice_parent_status ON Invoice(parentId, status);
CREATE INDEX idx_invoice_student_status ON Invoice(studentId, status);
CREATE INDEX idx_payment_status_amount ON Payment(status, amount);
```

### 2. تحسين الاستعلامات
```typescript
// استخدام select محدد بدلاً من include شامل
const payments = await prisma.payment.findMany({
  select: {
    id: true,
    amount: true,
    date: true,
    status: true,
    student: {
      select: {
        id: true,
        name: true,
        guardian: {
          select: {
            id: true,
            name: true
          }
        }
      }
    }
  },
  where: conditions,
  orderBy: { date: 'desc' },
  take: limit,
  skip: offset
});
```

### 3. تجميع البيانات في قاعدة البيانات
```typescript
// استخدام aggregation بدلاً من معالجة في الكود
const statistics = await prisma.payment.groupBy({
  by: ['status'],
  _sum: {
    amount: true
  },
  _count: {
    id: true
  },
  where: {
    date: {
      gte: startDate,
      lte: endDate
    }
  }
});
```

---

## 🎯 خطة التنفيذ

### المرحلة الأولى (يوم 1-2)
1. إصلاح API المدفوعات الإدارية
2. منع إنشاء المدفوعات المكررة
3. تحسين ربط المدفوعات بالفواتير

### المرحلة الثانية (يوم 3-4)
1. إصلاح API المدفوعات حسب الولي
2. تحسين الاستعلامات والأداء
3. إضافة التخزين المؤقت

### المرحلة الثالثة (يوم 5)
1. إصلاح API الفواتير
2. تحسين التحقق من البيانات
3. إضافة الدوال المساعدة

---

**تاريخ الإنشاء:** 2025-06-24  
**المطور:** Augment Agent  
**الحالة:** جاهز للتنفيذ  
**الأولوية:** عالية جداً

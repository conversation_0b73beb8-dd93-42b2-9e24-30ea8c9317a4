/**
 * Class for handling real-time synchronization of whiteboard data
 * This is a simplified implementation that would need to be connected
 * to a real-time backend service like Socket.io or WebSockets
 */
export class WhiteboardSync {
  private whiteboardId: string;
  private userId: string;
  private roomId: string;
  private onDataReceived: (data: string) => void;
  private onUserCursorsUpdate: (cursors: UserCursor[]) => void;
  
  // Simulated connection status
  private connected: boolean = false;
  
  // Simulated user cursors
  private userCursors: UserCursor[] = [];
  
  // Simulated connection delay (ms)
  private simulatedDelay: number = 100;
  
  /**
   * Constructor
   * @param whiteboardId Unique ID of the whiteboard
   * @param userId ID of the current user
   * @param roomId ID of the room/session
   * @param onDataReceived Callback when new data is received
   * @param onUserCursorsUpdate Callback when user cursors are updated
   */
  constructor(
    whiteboardId: string,
    userId: string,
    roomId: string,
    onDataReceived: (data: string) => void,
    onUserCursorsUpdate: (cursors: UserCursor[]) => void
  ) {
    this.whiteboardId = whiteboardId;
    this.userId = userId;
    this.roomId = roomId;
    this.onDataReceived = onDataReceived;
    this.onUserCursorsUpdate = onUserCursorsUpdate;
  }
  
  /**
   * Connect to the synchronization service
   * In a real implementation, this would establish a WebSocket connection
   */
  public connect(): Promise<boolean> {
    return new Promise((resolve) => {
      // Simulate connection delay
      setTimeout(() => {
        this.connected = true;
        console.log(`WhiteboardSync: Connected to room ${this.roomId}`);
        
        // Simulate other users in the room
        this.userCursors = [
          {
            userId: 'user1',
            username: 'أحمد',
            position: { x: 100, y: 100 },
            color: '#ff0000'
          },
          {
            userId: 'user2',
            username: 'محمد',
            position: { x: 200, y: 150 },
            color: '#0000ff'
          }
        ];
        
        // Notify about initial cursors
        this.onUserCursorsUpdate(this.userCursors);
        
        resolve(true);
      }, this.simulatedDelay);
    });
  }
  
  /**
   * Disconnect from the synchronization service
   */
  public disconnect(): void {
    this.connected = false;
    console.log(`WhiteboardSync: Disconnected from room ${this.roomId}`);
  }
  
  /**
   * Send whiteboard data to other users
   * @param data Whiteboard data to send
   */
  public sendData(data: string): void {
    if (!this.connected) {
      console.warn('WhiteboardSync: Not connected, cannot send data');
      return;
    }
    
    console.log(`WhiteboardSync: Sending data (${data.length} bytes)`);
    
    // In a real implementation, this would send data through WebSocket
    // For this simulation, we just log it
  }
  
  /**
   * Update cursor position
   * @param position Cursor position {x, y}
   */
  public updateCursorPosition(position: { x: number, y: number }): void {
    if (!this.connected) {
      return;
    }
    
    // In a real implementation, this would send cursor position through WebSocket
    console.log(`WhiteboardSync: Updating cursor position to ${position.x},${position.y}`);
    
    // Simulate cursor movement of other users
    this.simulateOtherUsersCursorMovement();
  }
  
  /**
   * Simulate cursor movement of other users
   * This is just for demonstration purposes
   */
  private simulateOtherUsersCursorMovement(): void {
    // Randomly move other users' cursors
    this.userCursors = this.userCursors.map(cursor => {
      if (cursor.userId !== this.userId) {
        return {
          ...cursor,
          position: {
            x: cursor.position.x + (Math.random() * 20 - 10),
            y: cursor.position.y + (Math.random() * 20 - 10)
          }
        };
      }
      return cursor;
    });
    
    // Notify about cursor updates
    this.onUserCursorsUpdate(this.userCursors);
  }
  
  /**
   * Simulate receiving data from another user
   * This is just for demonstration purposes
   * @param data Whiteboard data
   * @param fromUserId User ID who sent the data
   */
  public simulateReceiveData(data: string, fromUserId: string): void {
    if (!this.connected) {
      return;
    }
    
    console.log(`WhiteboardSync: Received data from user ${fromUserId} (${data.length} bytes)`);
    
    // Notify about received data
    this.onDataReceived(data);
  }
}

/**
 * User cursor information
 */
export interface UserCursor {
  userId: string;
  username: string;
  position: {
    x: number;
    y: number;
  };
  color: string;
}

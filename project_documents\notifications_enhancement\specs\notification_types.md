# مواصفات أنواع الإشعارات المخصصة

## نظرة عامة
يدعم النظام المحسن أنواعاً مختلفة من الإشعارات المخصصة لتلبية احتياجات جميع أطراف العملية التعليمية.

## أنواع المجموعات المستهدفة

### 1. جميع المستخدمين (ALL_USERS)
**الوصف**: إرسال إشعار لجميع المستخدمين النشطين في النظام

**المعايير**:
- جميع المستخدمين بحالة نشطة
- استثناء المستخدمين المحظورين أو المعطلين
- شمول جميع الأدوار (مديرين، معلمين، طلاب، أولياء)

**حالات الاستخدام**:
- إعلانات عامة مهمة
- تحديثات النظام
- إجازات وعطل رسمية
- أحداث مدرسية كبرى

**مثال**:
```json
{
  "groupType": "ALL_USERS",
  "title": "إعلان مهم: إجازة عيد الفطر",
  "content": "تعلن إدارة المدرسة عن بداية إجازة عيد الفطر المبارك...",
  "type": "GENERAL"
}
```

### 2. حسب الدور (BY_ROLE)
**الوصف**: إرسال إشعارات لمستخدمين من دور محدد

#### 2.1 المعلمون فقط (TEACHER)
**المعايير**:
- المستخدمون بدور TEACHER
- المعلمون النشطون فقط

**حالات الاستخدام**:
- اجتماعات هيئة التدريس
- تحديثات المناهج
- ورش تدريبية للمعلمين
- تعليمات إدارية خاصة بالمعلمين

**مثال**:
```json
{
  "groupType": "BY_ROLE",
  "targetRole": "TEACHER",
  "title": "اجتماع هيئة التدريس",
  "content": "يرجى حضور اجتماع هيئة التدريس غداً الساعة 10 صباحاً...",
  "type": "GENERAL"
}
```

#### 2.2 الطلاب فقط (STUDENT)
**المعايير**:
- المستخدمون بدور STUDENT
- الطلاب المسجلين والنشطين

**حالات الاستخدام**:
- إعلانات الامتحانات
- مواعيد الأنشطة الطلابية
- تذكيرات الواجبات
- نتائج الامتحانات

**مثال**:
```json
{
  "groupType": "BY_ROLE",
  "targetRole": "STUDENT",
  "title": "تذكير: امتحان الرياضيات",
  "content": "امتحان الرياضيات للصف الثالث غداً الساعة 9 صباحاً...",
  "type": "EXAM"
}
```

#### 2.3 أولياء الأمور فقط (PARENT)
**المعايير**:
- المستخدمون بدور PARENT
- أولياء الأمور المسجلين

**حالات الاستخدام**:
- اجتماعات أولياء الأمور
- تقارير الأداء الأكاديمي
- تذكيرات المدفوعات
- أحداث عائلية

**مثال**:
```json
{
  "groupType": "BY_ROLE",
  "targetRole": "PARENT",
  "title": "اجتماع أولياء الأمور",
  "content": "ندعوكم لحضور اجتماع أولياء الأمور يوم السبت...",
  "type": "GENERAL"
}
```

#### 2.4 الإداريون (ADMIN/EMPLOYEE)
**المعايير**:
- المستخدمون بدور ADMIN أو EMPLOYEE
- الموظفون الإداريون

**حالات الاستخدام**:
- اجتماعات إدارية
- تحديثات السياسات
- تقارير مالية
- مهام إدارية

### 3. الاختيار المخصص (CUSTOM_SELECTION)
**الوصف**: اختيار مستخدمين محددين بأسمائهم

**المعايير**:
- قائمة محددة من معرفات المستخدمين
- إمكانية البحث والاختيار التفاعلي
- دعم الاختيار المتعدد

**حالات الاستخدام**:
- إشعارات شخصية
- مجموعات عمل محددة
- لجان خاصة
- متابعة حالات فردية

**مثال**:
```json
{
  "groupType": "CUSTOM_SELECTION",
  "targetUserIds": [123, 456, 789],
  "title": "اجتماع لجنة المناهج",
  "content": "اجتماع لجنة تطوير المناهج يوم الأحد...",
  "type": "GENERAL"
}
```

### 4. المجموعات المحددة مسبقاً (PREDEFINED_GROUP)
**الوصف**: مجموعات محفوظة مسبقاً للاستخدام المتكرر

**أمثلة المجموعات**:
- معلمو الصف الأول
- طلاب النشاط الرياضي
- لجنة الامتحانات
- مجلس الآباء

**المعايير**:
- مجموعات محفوظة في قاعدة البيانات
- قابلة للتحديث والتعديل
- إمكانية إنشاء مجموعات جديدة

## أنواع الإشعارات حسب المحتوى

### 1. إشعارات عامة (GENERAL)
- إعلانات إدارية
- أحداث مدرسية
- تحديثات عامة

### 2. إشعارات الدروس (LESSON)
- جداول الحصص
- تغييرات في المواعيد
- محتوى تعليمي جديد

### 3. إشعارات الامتحانات (EXAM)
- مواعيد الامتحانات
- نتائج الامتحانات
- تعليمات الامتحان

### 4. إشعارات الحضور (ATTENDANCE)
- تقارير الغياب
- تأخير الطلاب
- إحصائيات الحضور

### 5. إشعارات المدفوعات (PAYMENT)
- فواتير مستحقة
- تأكيد المدفوعات
- تذكيرات الدفع

### 6. إشعارات الإنجازات (ACHIEVEMENT)
- شهادات تقدير
- إنجازات أكاديمية
- مسابقات وجوائز

### 7. إشعارات الفصول الافتراضية (REMOTE_CLASS)
- روابط الفصول الافتراضية
- تحديثات تقنية
- مشاكل الاتصال

## معايير الأولوية

### أولوية عالية (HIGH)
- إشعارات طوارئ
- إلغاء الدروس
- أحداث مهمة وعاجلة

### أولوية متوسطة (MEDIUM)
- تذكيرات الامتحانات
- اجتماعات مهمة
- تحديثات المناهج

### أولوية منخفضة (LOW)
- إعلانات عامة
- أنشطة اختيارية
- معلومات إضافية

## إعدادات التخصيص

### تخصيص حسب الوقت
- إشعارات فورية
- إشعارات مجدولة
- إشعارات متكررة

### تخصيص حسب القناة
- إشعارات داخل النظام
- رسائل بريد إلكتروني
- رسائل نصية (SMS)
- إشعارات الهاتف المحمول

### تخصيص حسب التفاعل
- إشعارات تتطلب رد
- إشعارات للقراءة فقط
- إشعارات تفاعلية

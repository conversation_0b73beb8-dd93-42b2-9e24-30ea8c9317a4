import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { ActivityLogger, ActivityType } from '@/lib/activity-logger';

// GET /api/students
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;
    const unassigned = searchParams.get('unassigned') === 'true';

    // بناء شروط البحث
    type WhereCondition = {
      OR?: Array<{ name: { contains: string } } | { username: { contains: string } }>;
      guardianId?: null;
      classeId?: number | null;
    };

    let whereCondition: WhereCondition = {};

    if (query) {
      whereCondition = {
        OR: [
          { name: { contains: query } },
          { username: { contains: query } }
        ]
      };
    }

    // إذا تم طلب الطلاب غير المرتبطين بأولياء
    if (unassigned) {
      whereCondition.guardianId = null;
    }

    // إذا تم طلب الطلاب حسب الفصل
    const classeIdParam = searchParams.get('classeId');
    if (classeIdParam) {
      const classeId = parseInt(classeIdParam);
      if (!isNaN(classeId)) {
        whereCondition.classeId = classeId;
      }
    }

    const [students, total] = await Promise.all([
      prisma.student.findMany({
        where: whereCondition,
        include: {
          guardian: true,
          classe: true
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.student.count({
        where: whereCondition
      })
    ]);

    // إذا تم طلب الطلاب غير المرتبطين بأولياء، نرجع قائمة الطلاب فقط
    if (unassigned) {
      return NextResponse.json(students);
    }

    return NextResponse.json({
      students,
      total,
      pages: Math.ceil(total / limit)
    });
  } catch (error: unknown) {
    console.error('Error fetching students:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب بيانات التلاميذ' },
      { status: 500 }
    );
  }
}

// POST /api/students
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.age || !body.username) {
      return NextResponse.json(
        { error: 'يرجى ملء جميع الحقول المطلوبة' },
        { status: 400 }
      );
    }

    // تحويل العمر إلى رقم
    const age = parseInt(body.age);
    if (isNaN(age)) {
      return NextResponse.json(
        { error: 'العمر يجب أن يكون رقماً صحيحاً' },
        { status: 400 }
      );
    }

    // إعداد بيانات الإنشاء
    const createData: {
      name: string;
      age: number;
      username: string;
      phone?: string;
      guardian?: { connect: { id: number } };
      classe?: { connect: { id: number } };
    } = {
      name: body.name,
      age: age,
      username: body.username,
      phone: body.phone
    };

    // إضافة العلاقات إذا تم توفيرها
    if (body.guardianId) {
      const guardianId = parseInt(body.guardianId);
      if (isNaN(guardianId)) {
        return NextResponse.json(
          { error: 'معرف الولي غير صالح' },
          { status: 400 }
        );
      }
      createData.guardian = { connect: { id: guardianId } };
    }

    if (body.classeId) {
      const classeId = parseInt(body.classeId);
      if (isNaN(classeId)) {
        return NextResponse.json(
          { error: 'معرف القسم غير صالح' },
          { status: 400 }
        );
      }
      createData.classe = { connect: { id: classeId } };
    }

    // Create student with relationships
    const student = await prisma.student.create({
      data: createData,
      include: {
        guardian: true,
        classe: true
      }
    });

    // تسجيل نشاط إضافة طالب
    try {
      // محاولة الحصول على معرف المستخدم الحالي
      const adminUser = await prisma.user.findFirst({
        where: { role: 'ADMIN' }
      });

      if (adminUser) {
        await ActivityLogger.log(
          adminUser.id,
          ActivityType.STUDENT_ADD,
          `إضافة طالب جديد: ${student.name}`
        );
      }
    } catch (error) {
      console.error('خطأ في تسجيل نشاط إضافة طالب:', error);
      // لا نريد أن يفشل إضافة الطالب إذا فشل تسجيل النشاط
    }

    return NextResponse.json(student, {status: 201});
  } catch (error: unknown) {
    console.error('Error creating student:', error);

    // Handle specific database errors
    if (error && typeof error === 'object' && 'code' in error) {
      const prismaError = error as { code: string; meta?: { target?: string[] } };

      if (prismaError.code === 'P2002' && prismaError.meta?.target?.includes('username')) {
        return NextResponse.json(
          { error: 'اسم المستخدم مستخدم بالفعل' },
          { status: 400 }
        );
      }

      if (prismaError.code === 'P2025') {
        return NextResponse.json(
          { error: 'الولي أو القسم المحدد غير موجود' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء إضافة التلميذ' },
      { status: 500 }
    );
  }
}

// PUT /api/students/:id
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const idStr = pathSegments[pathSegments.length - 1];

    // التحقق من صحة المعرف
    if (!idStr) {
      return NextResponse.json(
        { error: 'معرف الطالب مطلوب' },
        { status: 400 }
      );
    }

    const id = parseInt(idStr);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف الطالب غير صالح' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.age || !body.username) {
      return NextResponse.json(
        { error: 'يرجى ملء جميع الحقول المطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const existingStudent = await prisma.student.findUnique({
      where: { id }
    });

    if (!existingStudent) {
      return NextResponse.json(
        { error: 'الطالب غير موجود' },
        { status: 404 }
      );
    }

    // إعداد بيانات التحديث
    const updateData: {
      name: string;
      age: number;
      username: string;
      phone?: string;
      guardian?: { connect: { id: number } };
      classe?: { connect: { id: number } };
    } = {
      name: body.name,
      age: parseInt(body.age),
      username: body.username,
      phone: body.phone
    };

    // إضافة العلاقات إذا تم توفيرها
    if (body.guardianId) {
      const guardianId = parseInt(body.guardianId);
      if (isNaN(guardianId)) {
        return NextResponse.json(
          { error: 'معرف الولي غير صالح' },
          { status: 400 }
        );
      }
      updateData.guardian = { connect: { id: guardianId } };
    }

    if (body.classeId) {
      const classeId = parseInt(body.classeId);
      if (isNaN(classeId)) {
        return NextResponse.json(
          { error: 'معرف القسم غير صالح' },
          { status: 400 }
        );
      }
      updateData.classe = { connect: { id: classeId } };
    }

    const student = await prisma.student.update({
      where: { id },
      data: updateData,
      include: {
        guardian: true,
        classe: true
      }
    });

    return NextResponse.json(student, {status: 200});
  } catch (error: unknown) {
    console.error('Error updating student:', error);

    // معالجة أخطاء قاعدة البيانات المحددة
    if (error && typeof error === 'object' && 'code' in error) {
      const prismaError = error as { code: string; meta?: { target?: string[] } };

      if (prismaError.code === 'P2002' && prismaError.meta?.target?.includes('username')) {
        return NextResponse.json(
          { error: 'اسم المستخدم مستخدم بالفعل' },
          { status: 400 }
        );
      }

      if (prismaError.code === 'P2025') {
        return NextResponse.json(
          { error: 'الولي أو القسم المحدد غير موجود' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء تحديث بيانات التلميذ' },
      { status: 500 }
    );
  }
}

// DELETE /api/students/:id
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const idStr = pathSegments[pathSegments.length - 1];

    // التحقق من صحة المعرف
    if (!idStr) {
      return NextResponse.json(
        { error: 'معرف الطالب مطلوب' },
        { status: 400 }
      );
    }

    const id = parseInt(idStr);
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'معرف الطالب غير صالح' },
        { status: 400 }
      );
    }

    // التحقق من وجود الطالب
    const existingStudent = await prisma.student.findUnique({
      where: { id }
    });

    if (!existingStudent) {
      return NextResponse.json(
        { error: 'الطالب غير موجود' },
        { status: 404 }
      );
    }

    // حذف الطالب
    await prisma.student.delete({
      where: { id }
    });

    return NextResponse.json({ success: true, message: 'تم حذف الطالب بنجاح' }, {status: 200});
  } catch (error: unknown) {
    console.error('Error deleting student:', error);

    // معالجة أخطاء قاعدة البيانات المحددة
    if (error && typeof error === 'object' && 'code' in error) {
      const prismaError = error as { code: string };

      if (prismaError.code === 'P2025') {
        return NextResponse.json(
          { error: 'الطالب غير موجود' },
          { status: 404 }
        );
      }

      if (prismaError.code === 'P2003') {
        return NextResponse.json(
          { error: 'لا يمكن حذف الطالب لوجود سجلات مرتبطة به' },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: 'حدث خطأ أثناء حذف التلميذ' },
      { status: 500 }
    );
  }
}
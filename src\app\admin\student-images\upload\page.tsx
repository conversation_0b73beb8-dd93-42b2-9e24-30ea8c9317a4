'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { ArrowLeft, Upload, X, Check, Image as ImageIcon } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface Student {
  id: number;
  name: string;
}

interface StudentAlbum {
  id: number;
  name: string;
}

interface UploadedImage {
  id: string;
  file: File;
  preview: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
  imageUrl?: string;
}

export default function UploadImagesPage() {
  const [students, setStudents] = useState<Student[]>([]);
  const [albums, setAlbums] = useState<StudentAlbum[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [selectedAlbum, setSelectedAlbum] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [isProfilePic, setIsProfilePic] = useState<boolean>(false);
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [loading, setLoading] = useState({
    students: true,
    albums: true
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch students and albums
  useEffect(() => {
    fetchStudents();
    fetchAlbums();
  }, []);

  const fetchStudents = async () => {
    setLoading(prev => ({ ...prev, students: true }));
    try {
      const response = await fetch('/api/students');
      if (!response.ok) throw new Error('فشل في جلب الطلاب');
      const data = await response.json();
      setStudents(data.students || []);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب الطلاب',
        variant: 'destructive',
      });
      setStudents([]);
    } finally {
      setLoading(prev => ({ ...prev, students: false }));
    }
  };

  const fetchAlbums = async () => {
    setLoading(prev => ({ ...prev, albums: true }));
    try {
      const response = await fetch('/api/student-albums');
      if (!response.ok) throw new Error('فشل في جلب الألبومات');
      const data = await response.json();
      setAlbums(data.data || []);
    } catch (error) {
      console.error('Error fetching albums:', error);
      toast({
        title: 'خطأ',
        description: 'فشل في جلب الألبومات',
        variant: 'destructive',
      });
      setAlbums([]);
    } finally {
      setLoading(prev => ({ ...prev, albums: false }));
    }
  };

  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const newImages: UploadedImage[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (!file.type.startsWith('image/')) continue;

      newImages.push({
        id: `${Date.now()}-${i}`,
        file,
        preview: URL.createObjectURL(file),
        status: 'pending',
        progress: 0
      });
    }

    setImages(prev => [...prev, ...newImages]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Remove image from list
  const removeImage = (id: string) => {
    setImages(prev => {
      const updatedImages = prev.filter(img => img.id !== id);
      // Revoke object URL to avoid memory leaks
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return updatedImages;
    });
  };

  // Upload all images
  const uploadAllImages = async () => {
    if (!selectedStudent) {
      toast({
        title: 'خطأ',
        description: 'يرجى اختيار الطالب',
        variant: 'destructive',
      });
      return;
    }

    if (images.length === 0) {
      toast({
        title: 'خطأ',
        description: 'يرجى اختيار صور للرفع',
        variant: 'destructive',
      });
      return;
    }

    setIsUploading(true);

    // Upload images one by one
    for (let i = 0; i < images.length; i++) {
      const image = images[i];
      if (image.status === 'success') continue;

      // Update status to uploading
      setImages(prev => prev.map(img =>
        img.id === image.id ? { ...img, status: 'uploading', progress: 0 } : img
      ));

      try {
        // Step 1: Upload the file
        const formData = new FormData();
        formData.append('file', image.file);
        formData.append('type', 'student');

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!uploadResponse.ok) {
          throw new Error(`فشل في رفع الملف ${image.file.name}`);
        }

        const uploadResult = await uploadResponse.json();
        const imageUrl = uploadResult.data.filePath;

        // Update progress
        setImages(prev => prev.map(img =>
          img.id === image.id ? { ...img, progress: 50, imageUrl } : img
        ));

        // Step 2: Create image record in database
        const imageData = {
          studentId: selectedStudent,
          imageUrl,
          description,
          isProfilePic: isProfilePic && i === 0, // Only set the first image as profile pic if selected
          albumId: selectedAlbum || null
        };

        const createResponse = await fetch('/api/student-images', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(imageData),
        });

        if (!createResponse.ok) {
          const result = await createResponse.json();
          throw new Error(result.error || 'فشل في حفظ بيانات الصورة');
        }

        // Update status to success
        setImages(prev => prev.map(img =>
          img.id === image.id ? { ...img, status: 'success', progress: 100 } : img
        ));
      } catch (error) {
        console.error(`Error uploading image ${image.file.name}:`, error);

        // Update status to error
        setImages(prev => prev.map(img =>
          img.id === image.id ? {
            ...img,
            status: 'error',
            progress: 0,
            error: error instanceof Error ? error.message : 'فشل في رفع الصورة'
          } : img
        ));
      }
    }

    setIsUploading(false);

    // Show success message
    const successCount = images.filter(img => img.status === 'success').length;
    if (successCount > 0) {
      toast({
        title: 'نجاح',
        description: `تم رفع ${successCount} من ${images.length} صورة بنجاح`,
      });
    }
  };

  // Clear all images
  const clearAllImages = () => {
    // Revoke all object URLs
    images.forEach(img => URL.revokeObjectURL(img.preview));
    setImages([]);
  };

  // Get status counts
  const getStatusCounts = () => {
    return {
      total: images.length,
      pending: images.filter(img => img.status === 'pending').length,
      uploading: images.filter(img => img.status === 'uploading').length,
      success: images.filter(img => img.status === 'success').length,
      error: images.filter(img => img.status === 'error').length
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <ProtectedRoute requiredPermission="admin.student-images.upload">
      <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link href="/admin/student-images">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة إلى إدارة الصور
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">رفع صور جديدة</h1>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>إعدادات الرفع</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="student">الطالب</Label>
                <Select
                  value={selectedStudent}
                  onValueChange={setSelectedStudent}
                  disabled={loading.students || isUploading}
                >
                  <SelectTrigger id="student">
                    <SelectValue placeholder="اختر الطالب" />
                  </SelectTrigger>
                  <SelectContent>
                    {students.map(student => (
                      <SelectItem key={student.id} value={student.id.toString()}>
                        {student.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="album">الألبوم</Label>
                <Select
                  value={selectedAlbum}
                  onValueChange={setSelectedAlbum}
                  disabled={loading.albums || isUploading}
                >
                  <SelectTrigger id="album">
                    <SelectValue placeholder="اختر الألبوم (اختياري)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">بدون ألبوم</SelectItem>
                    {albums.map(album => (
                      <SelectItem key={album.id} value={album.id.toString()}>
                        {album.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="description">وصف الصور</Label>
                <Textarea
                  id="description"
                  placeholder="أدخل وصفًا للصور (سيتم تطبيقه على جميع الصور)"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  disabled={isUploading}
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2 space-x-reverse">
                <Checkbox
                  id="isProfilePic"
                  checked={isProfilePic}
                  onCheckedChange={(checked) => setIsProfilePic(!!checked)}
                  disabled={isUploading}
                />
                <Label htmlFor="isProfilePic">
                  تعيين الصورة الأولى كصورة الملف الشخصي
                </Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>اختيار الصور</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                <Upload className="h-4 w-4 ml-2" />
                اختيار صور
              </Button>
              <Input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                onChange={handleFileSelect}
                disabled={isUploading}
              />

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={clearAllImages}
                  disabled={images.length === 0 || isUploading}
                >
                  <X className="h-4 w-4 ml-2" />
                  مسح الكل
                </Button>

                <Button
                  variant="default"
                  onClick={uploadAllImages}
                  disabled={images.length === 0 || isUploading || !selectedStudent}
                >
                  <Upload className="h-4 w-4 ml-2" />
                  رفع الكل
                </Button>
              </div>
            </div>

            {images.length > 0 && (
              <div className="bg-muted p-4 rounded-md">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium">ملخص الصور</h3>
                  <div className="text-sm text-muted-foreground">
                    {statusCounts.total} صورة ({statusCounts.success} تم رفعها, {statusCounts.error} فشل, {statusCounts.pending + statusCounts.uploading} في الانتظار)
                  </div>
                </div>

                {isUploading && (
                  <Progress
                    value={(statusCounts.success / statusCounts.total) * 100}
                    className="h-2 mb-4"
                  />
                )}
              </div>
            )}

            {images.length > 0 ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {images.map(image => (
                  <div
                    key={image.id}
                    className="relative border rounded-md overflow-hidden group"
                  >
                    <div className="relative aspect-square">
                      <Image
                        src={image.preview}
                        alt={image.file.name}
                        fill
                        className="object-cover"
                      />

                      {/* Status indicator */}
                      <div className={`absolute top-2 right-2 w-6 h-6 rounded-full flex items-center justify-center ${
                        image.status === 'success' ? 'bg-primary-color' :
                        image.status === 'error' ? 'bg-red-500' :
                        image.status === 'uploading' ? 'bg-blue-500' :
                        'bg-gray-500'
                      }`}>
                        {image.status === 'success' && <Check className="h-4 w-4 text-white" />}
                        {image.status === 'error' && <X className="h-4 w-4 text-white" />}
                        {image.status === 'uploading' && (
                          <div className="h-3 w-3 rounded-full border-2 border-white border-t-transparent animate-spin" />
                        )}
                        {image.status === 'pending' && <div className="h-2 w-2 rounded-full bg-white" />}
                      </div>

                      {/* Progress bar for uploading */}
                      {image.status === 'uploading' && (
                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
                          <div
                            className="h-full bg-blue-500"
                            style={{ width: `${image.progress}%` }}
                          />
                        </div>
                      )}
                    </div>

                    <div className="p-2 text-xs truncate">
                      {image.file.name}
                    </div>

                    {/* Remove button */}
                    {image.status !== 'uploading' && (
                      <button
                        className="absolute top-2 left-2 w-6 h-6 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => removeImage(image.id)}
                        disabled={isUploading}
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}

                    {/* Error message */}
                    {image.status === 'error' && (
                      <div className="absolute inset-0 bg-red-500 bg-opacity-70 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="text-white text-xs p-2 text-center">
                          {image.error || 'فشل في الرفع'}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 border-2 border-dashed rounded-md">
                <ImageIcon className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                <p className="text-muted-foreground">
                  اختر صورًا للرفع أو اسحبها وأفلتها هنا
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      </div>
    </ProtectedRoute>
  );
}

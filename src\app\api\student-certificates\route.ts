import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { Prisma } from '@prisma/client';

// GET /api/student-certificates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const certificateId = searchParams.get('certificateId');
    const id = searchParams.get('id');

    // بناء شروط البحث
    const where: Prisma.StudentCertificateWhereInput = {};

    if (id) {
      where.id = parseInt(id);
    }

    if (studentId) {
      where.studentId = parseInt(studentId);
    }

    if (certificateId) {
      where.certificateId = parseInt(certificateId);
    }

    // جلب شهادات الطلاب
    const studentCertificates = await prisma.studentCertificate.findMany({
      where,
      include: {
        student: {
          select: {
            id: true,
            name: true,
            username: true,
            classe: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        certificate: true
      },
      orderBy: {
        issueDate: 'desc'
      }
    });

    return NextResponse.json({
      data: studentCertificates,
      success: true,
      message: 'تم جلب شهادات الطلاب بنجاح'
    });
  } catch (error) {
    console.error('Error fetching student certificates:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء جلب شهادات الطلاب',
      success: false
    }, { status: 500 });
  }
}

// POST /api/student-certificates
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, certificateId, issueDate } = body;

    // التحقق من البيانات المطلوبة
    if (!studentId || !certificateId) {
      return NextResponse.json({
        error: 'معرف الطالب ومعرف الشهادة مطلوبان',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود الطالب
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return NextResponse.json({
        error: 'الطالب غير موجود',
        success: false
      }, { status: 404 });
    }

    // التحقق من وجود الشهادة
    const certificate = await prisma.certificate.findUnique({
      where: { id: parseInt(certificateId) }
    });

    if (!certificate) {
      return NextResponse.json({
        error: 'الشهادة غير موجودة',
        success: false
      }, { status: 404 });
    }

    // التحقق من عدم وجود شهادة مماثلة للطالب
    const existingCertificate = await prisma.studentCertificate.findFirst({
      where: {
        studentId: parseInt(studentId),
        certificateId: parseInt(certificateId)
      }
    });

    if (existingCertificate) {
      return NextResponse.json({
        error: 'تم إصدار هذه الشهادة للطالب مسبقاً',
        success: false
      }, { status: 400 });
    }

    // إنشاء شهادة للطالب
    const studentCertificate = await prisma.studentCertificate.create({
      data: {
        studentId: parseInt(studentId),
        certificateId: parseInt(certificateId),
        issueDate: issueDate ? new Date(issueDate) : new Date()
      },
      include: {
        student: {
          select: {
            name: true
          }
        },
        certificate: {
          select: {
            title: true
          }
        }
      }
    });

    return NextResponse.json({
      data: studentCertificate,
      success: true,
      message: `تم إصدار شهادة "${certificate.title}" للطالب "${student.name}" بنجاح`
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating student certificate:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء إصدار الشهادة للطالب',
      success: false
    }, { status: 500 });
  }
}

// DELETE /api/student-certificates?id=123
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        error: 'معرف شهادة الطالب مطلوب',
        success: false
      }, { status: 400 });
    }

    // التحقق من وجود شهادة الطالب
    const existingCertificate = await prisma.studentCertificate.findUnique({
      where: { id: parseInt(id) }
    });

    if (!existingCertificate) {
      return NextResponse.json({
        error: 'شهادة الطالب غير موجودة',
        success: false
      }, { status: 404 });
    }

    // حذف شهادة الطالب
    await prisma.studentCertificate.delete({
      where: { id: parseInt(id) }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف شهادة الطالب بنجاح'
    });
  } catch (error) {
    console.error('Error deleting student certificate:', error);
    return NextResponse.json({
      error: 'حدث خطأ أثناء حذف شهادة الطالب',
      success: false
    }, { status: 500 });
  }
}

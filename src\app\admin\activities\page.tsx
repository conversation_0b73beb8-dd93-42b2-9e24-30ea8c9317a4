'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  FaSignInAlt, FaSignOutAlt, FaUserPlus, FaEdit, FaGraduationCap,
  FaBookOpen, FaInfoCircle, FaMoneyBillWave, FaCalendarCheck
} from 'react-icons/fa';
import { Button } from '@/components/ui/button';
import { toast } from 'react-toastify';
import Link from 'next/link';
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface Activity {
  id: number;
  type: string;
  description: string;
  date: string;
  user: string;
}

interface Pagination {
  total: number;
  pages: number;
  current: number;
  limit: number;
}

const ActivitiesPage = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    pages: 0,
    current: 1,
    limit: 20
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchActivities = useCallback(async (page = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/activities?page=${page}&limit=${pagination.limit}`);

      if (!response.ok) {
        throw new Error('فشل في جلب النشاطات');
      }

      const data = await response.json();
      setActivities(data.activities);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching activities:', error);
      setError('حدث خطأ أثناء جلب النشاطات. يرجى المحاولة مرة أخرى.');
      toast.error('فشل في جلب النشاطات');
    } finally {
      setIsLoading(false);
    }
  }, [pagination.limit]);

  useEffect(() => {
    fetchActivities();
  }, [fetchActivities]);

  // تحديد أيقونة ولون النشاط بناءً على نوعه
  const getActivityStyle = (type: string) => {
    const activityStyles = {
      'LOGIN': { border: 'border-blue-500', icon: <FaSignInAlt className="text-blue-500" /> },
      'LOGOUT': { border: 'border-purple-500', icon: <FaSignOutAlt className="text-purple-500" /> },
      'REGISTRATION': { border: 'border-primary-color', icon: <FaUserPlus className="text-primary-color" /> },
      'PAYMENT': { border: 'border-yellow-500', icon: <FaMoneyBillWave className="text-yellow-500" /> },
      'ATTENDANCE': { border: 'border-red-500', icon: <FaCalendarCheck className="text-red-500" /> },
      'UPDATE': { border: 'border-blue-500', icon: <FaEdit className="text-blue-500" /> },
      'EXAM': { border: 'border-orange-500', icon: <FaGraduationCap className="text-orange-500" /> },
      'KHATM': { border: 'border-teal-500', icon: <FaBookOpen className="text-primary-color" /> },
      'STUDENT_ADD': { border: 'border-primary-color', icon: <FaUserPlus className="text-primary-color" /> }
    };

    return activityStyles[type as keyof typeof activityStyles] ||
      { border: 'border-gray-500', icon: <FaInfoCircle className="text-gray-500" /> };
  };

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return new Intl.DateTimeFormat('ar-EG', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'تاريخ غير صالح';
    }
  };

  // التنقل بين الصفحات
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.pages) {
      fetchActivities(newPage);
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.activities.view">
      <div className="container mx-auto p-4" dir="rtl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">سجل النشاطات</h1>
        <QuickActionButtons
          entityType="activities"
          actions={[
            {
              key: 'back',
              label: 'العودة للوحة التحكم',
              icon: <FaSignOutAlt />,
              onClick: () => window.location.href = '/admin',
              variant: 'outline'
            }
          ]}
        />
      </div>

      {isLoading && activities.length === 0 ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <div className="flex flex-col items-center justify-center text-center py-8 bg-white rounded-lg shadow-md">
          <div className="text-red-500 mb-4">{error}</div>
          <Button
            onClick={() => fetchActivities()}
            className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white"
          >
            إعادة المحاولة
          </Button>
        </div>
      ) : activities.length > 0 ? (
        <>
          <div className="bg-white rounded-lg shadow-md p-6 mb-4">
            <div className="space-y-4">
              {activities.map((activity) => {
                const activityStyle = getActivityStyle(activity.type);
                const formattedDate = formatDate(activity.date);

                return (
                  <div key={activity.id} className={`border-r-4 ${activityStyle.border} pr-4 flex items-start gap-3 p-3 hover:bg-gray-50 rounded-lg`}>
                    <div className="mt-1">{activityStyle.icon}</div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500">{formattedDate}</p>
                      <p className="text-gray-800 font-medium">{activity.description}</p>
                      <p className="text-xs text-gray-600">بواسطة: {activity.user}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-6">
              <Button
                onClick={() => handlePageChange(1)}
                disabled={pagination.current === 1}
                variant="outline"
                size="sm"
              >
                الأول
              </Button>
              <Button
                onClick={() => handlePageChange(pagination.current - 1)}
                disabled={pagination.current === 1}
                variant="outline"
                size="sm"
              >
                السابق
              </Button>

              <span className="mx-2">
                الصفحة {pagination.current} من {pagination.pages}
              </span>

              <Button
                onClick={() => handlePageChange(pagination.current + 1)}
                disabled={pagination.current === pagination.pages}
                variant="outline"
                size="sm"
              >
                التالي
              </Button>
              <Button
                onClick={() => handlePageChange(pagination.pages)}
                disabled={pagination.current === pagination.pages}
                variant="outline"
                size="sm"
              >
                الأخير
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="text-center text-gray-500 py-8 bg-white rounded-lg shadow-md">
          <FaInfoCircle className="mx-auto text-gray-400 text-2xl mb-2" />
          <p className="mb-2">لا توجد نشاطات</p>
          <p className="text-xs text-gray-400">ستظهر هنا النشاطات عند إضافتها</p>
        </div>
      )}
      </div>
    </OptimizedProtectedRoute>
  );
};

export default ActivitiesPage;

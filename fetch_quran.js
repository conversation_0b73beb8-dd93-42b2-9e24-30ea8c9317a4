import { PrismaClient } from '@prisma/client';
import axios from 'axios';

const prisma = new PrismaClient();

// تكوين عنوان API لرواية ورش
const API_BASE_URL = 'https://api.alquran.cloud/v1';
const WARSH_EDITION = 'quran-warsh';

// دالة لجلب معلومات السور
async function fetchSurahs() {
  try {
    const response = await axios.get(`${API_BASE_URL}/surah`);
    return response.data.data;
  } catch (error) {
    console.error('خطأ في جلب معلومات السور:', error.message);
    throw error;
  }
}

// دالة لجلب آيات سورة معينة برواية ورش
async function fetchSurahAyahs(surahNumber) {
  try {
    const response = await axios.get(`${API_BASE_URL}/surah/${surahNumber}/${WARSH_EDITION}`);
    return response.data.data;
  } catch (error) {
    console.error(`خطأ في جلب آيات السورة ${surahNumber}:`, error.message);
    throw error;
  }
}

// دالة لحفظ السور في قاعدة البيانات
async function saveSurahsToDatabase(surahs) {
  try {
    for (const surah of surahs) {
      await prisma.surah.upsert({
        where: { number: surah.number },
        update: {
          name: surah.name,
          totalAyahs: surah.numberOfAyahs
        },
        create: {
          number: surah.number,
          name: surah.name,
          totalAyahs: surah.numberOfAyahs
        }
      });
      console.log(`تم حفظ معلومات سورة ${surah.name}`);
    }
  } catch (error) {
    console.error('خطأ في حفظ السور:', error.message);
    throw error;
  }
}

// دالة لحفظ الآيات في قاعدة البيانات
async function saveAyahsToDatabase(surahNumber, ayahs) {
  try {
    const surah = await prisma.surah.findUnique({
      where: { number: surahNumber }
    });

    if (!surah) {
      throw new Error(`لم يتم العثور على السورة رقم ${surahNumber}`);
    }

    for (const ayah of ayahs) {
      const juz = await prisma.juz.upsert({
        where: { number: ayah.juz },
        create: { number: ayah.juz },
        update: {}
      });

      // حساب رقم الحزب بشكل صحيح (كل جزء يحتوي على حزبين)
      const hizbNumber = (ayah.juz - 1) * 2 + 1;
      const hizb = await prisma.hizb.upsert({
        where: { number: hizbNumber },
        create: { number: hizbNumber },
        update: {}
      });

      const existingAyah = await prisma.ayah.findFirst({
        where: {
          surahId: surah.id,
          number: ayah.numberInSurah
        }
      });

      await prisma.ayah.upsert({
        where: {
          id: existingAyah?.id ?? -1
        },
        update: {
          text: ayah.text,
          juzId: juz.id,
          hizbId: hizb.id
        },
        create: {
          number: ayah.numberInSurah,
          text: ayah.text,
          surahId: surah.id,
          juzId: juz.id,
          hizbId: hizb.id
        }
      });
    }
    console.log(`تم حفظ آيات السورة رقم ${surahNumber}`);
  } catch (error) {
    console.error(`خطأ في حفظ آيات السورة ${surahNumber}:`, error.message);
    throw error;
  }
}

// الدالة الرئيسية لجلب وحفظ القرآن الكريم
async function fetchAndSaveQuran() {
  try {
    console.log('بدء عملية جلب وحفظ القرآن الكريم برواية ورش...');

    // جلب وحفظ معلومات السور
    const surahs = await fetchSurahs();
    await saveSurahsToDatabase(surahs);

    // جلب وحفظ الآيات لكل سورة
    for (const surah of surahs) {
      const surahData = await fetchSurahAyahs(surah.number);
      await saveAyahsToDatabase(surah.number, surahData.ayahs);

      // إضافة تأخير بسيط بين الطلبات لتجنب تجاوز حدود API
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('تم الانتهاء من جلب وحفظ القرآن الكريم بنجاح!');
  } catch (error) {
    console.error('حدث خطأ أثناء العملية:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// تنفيذ الدالة الرئيسية
fetchAndSaveQuran();

// تصدير الدوال للاستخدام في أجزاء أخرى من التطبيق
export {
  fetchAndSaveQuran,
  fetchSurahs,
  fetchSurahAyahs
};
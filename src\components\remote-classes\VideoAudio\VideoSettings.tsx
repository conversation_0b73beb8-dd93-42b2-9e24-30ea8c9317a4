'use client';

import React, { useState, useEffect, useRef } from 'react';
import { FaVideo, FaVideoSlash, FaCog, FaMagic, FaSignal } from 'react-icons/fa';
import { Button } from '@/components/ui/button';
import { AdaptiveBitrateManager } from './AdaptiveBitrateManager';

interface VideoSettingsProps {
  onVideoChange: (enabled: boolean) => void;
  onQualityChange: (quality: 'low' | 'medium' | 'high') => void;
  onDeviceChange: (deviceId: string) => void;
  isVideoEnabled: boolean;
  currentQuality?: 'low' | 'medium' | 'high';
  currentDeviceId?: string;
  stream?: MediaStream | null;
  useAdaptiveBitrate?: boolean;
  onAdaptiveBitrateChange?: (enabled: boolean) => void;
}

/**
 * Component for video settings
 */
const VideoSettings: React.FC<VideoSettingsProps> = ({
  onVideoChange,
  onQualityChange,
  onDeviceChange,
  isVideoEnabled,
  currentQuality = 'medium',
  currentDeviceId,
  stream = null,
  useAdaptiveBitrate = false,
  onAdaptiveBitrateChange,
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([]);
  const [adaptiveBitrate, setAdaptiveBitrate] = useState(useAdaptiveBitrate);
  const [networkQuality, setNetworkQuality] = useState<'poor' | 'fair' | 'good'>('good');
  const videoRef = useRef<HTMLVideoElement>(null);
  const bitrateManagerRef = useRef<AdaptiveBitrateManager | null>(null);

  // Get available video devices
  useEffect(() => {
    const getDevices = async () => {
      try {
        // Request permission to access media devices
        await navigator.mediaDevices.getUserMedia({ video: true });

        // Get list of video devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        setVideoDevices(videoInputs);

        // Set default device if none is selected
        if (!currentDeviceId && videoInputs.length > 0) {
          onDeviceChange(videoInputs[0].deviceId);
        }
      } catch (error) {
        console.error('Error accessing video devices:', error);
      }
    };

    getDevices();
  }, [currentDeviceId, onDeviceChange]);

  // Initialize adaptive bitrate manager when stream changes
  useEffect(() => {
    if (stream && isVideoEnabled && adaptiveBitrate && videoRef.current) {
      // Clean up previous manager if exists
      if (bitrateManagerRef.current) {
        bitrateManagerRef.current.dispose();
      }

      // Create new manager
      const manager = new AdaptiveBitrateManager((quality) => {
        // Update quality when adaptive bitrate manager changes it
        onQualityChange(quality);

        // Update network quality indicator
        switch (quality) {
          case 'low':
            setNetworkQuality('poor');
            break;
          case 'medium':
            setNetworkQuality('fair');
            break;
          case 'high':
            setNetworkQuality('good');
            break;
        }
      });

      // Initialize with current video element and stream
      manager.initialize(videoRef.current, stream);
      bitrateManagerRef.current = manager;

      return () => {
        if (bitrateManagerRef.current) {
          bitrateManagerRef.current.dispose();
          bitrateManagerRef.current = null;
        }
      };
    }
  }, [stream, isVideoEnabled, adaptiveBitrate, onQualityChange]);

  // Toggle adaptive bitrate
  const toggleAdaptiveBitrate = () => {
    const newValue = !adaptiveBitrate;
    setAdaptiveBitrate(newValue);

    if (onAdaptiveBitrateChange) {
      onAdaptiveBitrateChange(newValue);
    }
  };

  // Toggle video
  const toggleVideo = () => {
    onVideoChange(!isVideoEnabled);
  };

  // Quality options
  const qualityOptions = [
    { value: 'low', label: 'منخفضة (360p)' },
    { value: 'medium', label: 'متوسطة (720p)' },
    { value: 'high', label: 'عالية (1080p)' },
  ];

  // Network quality indicator
  const getNetworkQualityIcon = () => {
    switch (networkQuality) {
      case 'poor':
        return <FaSignal className="text-red-500" title="جودة شبكة ضعيفة" />;
      case 'fair':
        return <FaSignal className="text-yellow-500" title="جودة شبكة متوسطة" />;
      case 'good':
        return <FaSignal className="text-primary-color" title="جودة شبكة جيدة" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          {isVideoEnabled ? (
            <FaVideo className="text-[var(--primary-color)]" />
          ) : (
            <FaVideoSlash className="text-gray-500" />
          )}
          <h3 className="font-medium">إعدادات الفيديو</h3>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={toggleVideo}
            variant={isVideoEnabled ? 'default' : 'outline'}
            className={isVideoEnabled ? 'bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]' : ''}
          >
            {isVideoEnabled ? 'إيقاف الكاميرا' : 'تشغيل الكاميرا'}
          </Button>
          <Button
            onClick={() => setShowSettings(!showSettings)}
            variant="outline"
            size="sm"
            className="p-0 h-8 w-8"
          >
            <FaCog />
          </Button>
        </div>
      </div>

      {/* Hidden video element for adaptive bitrate manager */}
      {stream && (
        <video
          ref={videoRef}
          style={{ display: 'none' }}
          autoPlay
          muted
          playsInline
        />
      )}

      {showSettings && (
        <div className="space-y-4 border-t pt-4">
          {/* Adaptive bitrate toggle */}
          <div className="flex items-center justify-between bg-gray-50 p-3 rounded-md">
            <div className="flex items-center gap-2">
              <FaMagic className={adaptiveBitrate ? "text-[var(--primary-color)]" : "text-gray-400"} />
              <div>
                <h4 className="font-medium text-sm">معدل بت متكيف</h4>
                <p className="text-xs text-gray-500">ضبط جودة الفيديو تلقائيًا حسب جودة الشبكة</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {adaptiveBitrate && getNetworkQualityIcon()}
              <button
                onClick={toggleAdaptiveBitrate}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                  adaptiveBitrate ? 'bg-[var(--primary-color)]' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    adaptiveBitrate ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Video quality (disabled when adaptive bitrate is on) */}
          <div>
            <label htmlFor="video-quality" className="block text-sm font-medium text-gray-700 mb-1">
              جودة الفيديو
            </label>
            <select
              id="video-quality"
              value={currentQuality}
              onChange={(e) => onQualityChange(e.target.value as 'low' | 'medium' | 'high')}
              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] ${
                adaptiveBitrate ? 'opacity-50' : ''
              }`}
              disabled={adaptiveBitrate}
            >
              {qualityOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {adaptiveBitrate && (
              <p className="text-xs text-gray-500 mt-1">
                الجودة تُضبط تلقائيًا عند تفعيل معدل البت المتكيف
              </p>
            )}
          </div>

          {/* Video device selection */}
          <div>
            <label htmlFor="video-device" className="block text-sm font-medium text-gray-700 mb-1">
              الكاميرا
            </label>
            <select
              id="video-device"
              value={currentDeviceId}
              onChange={(e) => onDeviceChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)]"
            >
              {videoDevices.map((device) => (
                <option key={device.deviceId} value={device.deviceId}>
                  {device.label || `الكاميرا ${videoDevices.indexOf(device) + 1}`}
                </option>
              ))}
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoSettings;

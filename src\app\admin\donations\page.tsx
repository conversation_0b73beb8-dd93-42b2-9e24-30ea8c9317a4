'use client'

import { useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'react-toastify'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import { FaHandHoldingHeart, FaSearch, FaEdit, FaTrash, FaSync, FaUsers, FaMoneyBillWave, FaChartLine, FaCreditCard, FaEye } from 'react-icons/fa'

import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute'
import PermissionGuard from '@/components/admin/PermissionGuard'
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons'
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard'

interface Donation {
  id: number
  donorName: string | null
  amount: number
  date: string
  note: string | null
  treasuryId: number
  paymentMethodId: number | null
  paymentMethod: {
    id: number
    name: string
  } | null
  cardDetails: {
    cardNumber: string
    cardExpiry: string
    cardCvv: string
  } | null
}

interface DonationResponse {
  donations: Donation[]
  pagination: {
    total: number
    pages: number
    page: number
    limit: number
  }
  stats: {
    totalAmount: number
    uniqueDonorsCount: number
    averageDonation: number
  }
}

export default function DonationsPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [donations, setDonations] = useState<Donation[]>([])
  const [stats, setStats] = useState({
    totalAmount: 0,
    uniqueDonorsCount: 0,
    averageDonation: 0
  })
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    page: 1,
    limit: 10
  })
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [selectedDonation, setSelectedDonation] = useState<Donation | null>(null)
  const [formData, setFormData] = useState({
    donorName: '',
    amount: '',
    note: ''
  })

  // جلب التبرعات
  const fetchDonations = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/donations?query=${searchQuery}&page=${pagination.page}&limit=${pagination.limit}`)

      if (!response.ok) {
        throw new Error('فشل في جلب التبرعات')
      }

      const data: DonationResponse = await response.json()
      setDonations(data.donations)
      setPagination(data.pagination)
      setStats(data.stats)
    } catch (error) {
      console.error('خطأ في جلب التبرعات:', error)
      toast.error('فشل في جلب التبرعات')
    } finally {
      setLoading(false)
    }
  }, [searchQuery, pagination.page, pagination.limit])

  // إضافة تبرع جديد
  const handleAddDonation = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (!formData.amount || isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) <= 0) {
        toast.error('الرجاء إدخال مبلغ صحيح')
        return
      }

      const response = await fetch('/api/donations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          donorName: formData.donorName || null,
          amount: parseFloat(formData.amount),
          note: formData.note || null
        })
      })

      if (!response.ok) {
        throw new Error('فشل في إضافة التبرع')
      }

      toast.success('تم إضافة التبرع بنجاح')
      setIsAddDialogOpen(false)
      setFormData({ donorName: '', amount: '', note: '' })
      fetchDonations()
    } catch (error) {
      console.error('خطأ في إضافة التبرع:', error)
      toast.error('فشل في إضافة التبرع')
    }
  }

  // تحديث تبرع
  const handleUpdateDonation = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedDonation) return

    try {
      if (!formData.amount || isNaN(parseFloat(formData.amount)) || parseFloat(formData.amount) <= 0) {
        toast.error('الرجاء إدخال مبلغ صحيح')
        return
      }

      const response = await fetch(`/api/donations/${selectedDonation.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          donorName: formData.donorName || null,
          amount: parseFloat(formData.amount),
          note: formData.note || null
        })
      })

      if (!response.ok) {
        throw new Error('فشل في تحديث التبرع')
      }

      toast.success('تم تحديث التبرع بنجاح')
      setIsEditDialogOpen(false)
      setSelectedDonation(null)
      setFormData({ donorName: '', amount: '', note: '' })
      fetchDonations()
    } catch (error) {
      console.error('خطأ في تحديث التبرع:', error)
      toast.error('فشل في تحديث التبرع')
    }
  }

  // حذف تبرع
  const handleDeleteDonation = async () => {
    if (!selectedDonation) return

    try {
      const response = await fetch(`/api/donations/${selectedDonation.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('فشل في حذف التبرع')
      }

      toast.success('تم حذف التبرع بنجاح')
      setIsDeleteDialogOpen(false)
      setSelectedDonation(null)
      fetchDonations()
    } catch (error) {
      console.error('خطأ في حذف التبرع:', error)
      toast.error('فشل في حذف التبرع')
    }
  }

  // تغيير الصفحة
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination({ ...pagination, page: newPage })
    }
  }

  // تحديث البيانات عند تغيير الصفحة أو البحث
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      fetchDonations()
    }, 500)

    return () => clearTimeout(delayDebounceFn)
  }, [fetchDonations])

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchDonations()
  }, [fetchDonations])

  // تهيئة نموذج التعديل
  const handleEditClick = (donation: Donation) => {
    setSelectedDonation(donation)
    setFormData({
      donorName: donation.donorName || '',
      amount: donation.amount.toString(),
      note: donation.note || ''
    })
    setIsEditDialogOpen(true)
  }

  // تهيئة نموذج الحذف
  const handleDeleteClick = (donation: Donation) => {
    setSelectedDonation(donation)
    setIsDeleteDialogOpen(true)
  }

  // عرض تفاصيل التبرع
  const handleViewDetails = (donation: Donation) => {
    setSelectedDonation(donation)
    setIsDetailsDialogOpen(true)
  }

  // إخفاء أرقام البطاقة ما عدا آخر 4 أرقام
  const maskCardNumber = (cardNumber: string) => {
    if (!cardNumber) return ''
    const last4 = cardNumber.slice(-4)
    return `**** **** **** ${last4}`
  }

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy/MM/dd', { locale: ar })
    } catch {
      return dateString
    }
  }

  return (
    <OptimizedProtectedRoute requiredPermission="admin.donations.view">
      <div className="space-y-6 bg-gradient-to-b from-[#f8fffd] to-white p-4 md:p-6 rounded-lg">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 className="text-2xl md:text-3xl font-bold text-[var(--primary-color)] border-r-4 border-[var(--primary-color)] pr-3 flex items-center">
          <FaHandHoldingHeart className="ml-2 text-[var(--primary-color)]" />
          إدارة التبرعات
        </h1>
        <div className="flex flex-wrap gap-2 w-full sm:w-auto">
          <QuickActionButtons
            entityType="donations"
            actions={[
              {
                key: 'create',
                label: 'تسجيل تبرع جديد',
                icon: <FaHandHoldingHeart />,
                onClick: () => setIsAddDialogOpen(true),
                variant: 'primary'
              }
            ]}
            className="w-full sm:w-auto"
          />
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogContent className="sm:max-w-[425px] bg-[#f8fffd] border border-[#e0f2ef]">
              <DialogHeader>
                <DialogTitle className="text-[var(--primary-color)] font-bold text-xl flex items-center">
                  <FaHandHoldingHeart className="ml-2" />
                  إضافة تبرع جديد
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleAddDonation}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                    <Label htmlFor="donorName" className="text-right sm:col-span-1">
                      اسم المتبرع
                    </Label>
                    <Input
                      id="donorName"
                      value={formData.donorName}
                      onChange={(e) => setFormData({ ...formData, donorName: e.target.value })}
                      className="sm:col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                    <Label htmlFor="amount" className="text-right sm:col-span-1">
                      المبلغ <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="amount"
                      type="number"
                      value={formData.amount}
                      onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                      className="sm:col-span-3"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-4 items-start gap-2 sm:gap-4">
                    <Label htmlFor="note" className="text-right sm:col-span-1 mt-2">
                      ملاحظات
                    </Label>
                    <Textarea
                      id="note"
                      value={formData.note}
                      onChange={(e) => setFormData({ ...formData, note: e.target.value })}
                      className="sm:col-span-3"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full sm:w-auto">
                    إضافة
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
            </Dialog>
        </div>
      </div>

      <div className="flex items-center space-x-2 bg-white p-4 rounded-lg shadow-md mb-6">
        <div className="relative w-full">
          <Input
            type="text"
            placeholder="البحث في التبرعات..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 border-[#e0f2ef] focus:border-[var(--primary-color)] focus:ring-[var(--primary-color)] w-full"
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        </div>
        <Button
          onClick={fetchDonations}
          className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white rounded-full p-2 min-w-10"
          disabled={loading}
          title="تحديث البيانات"
        >
          <FaSync className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden border border-[#e0f2ef]">
        <div className="responsive-table-container">
          <Table className="card-mode-table">
            <TableHeader>
              <TableRow>
                <TableHead className="bg-[var(--primary-color)] text-white" data-label="رقم التبرع">رقم التبرع</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white" data-label="اسم المتبرع">اسم المتبرع</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white" data-label="المبلغ">المبلغ</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white" data-label="التاريخ">التاريخ</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white hide-on-mobile" data-label="ملاحظات">ملاحظات</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white" data-label="طريقة الدفع">طريقة الدفع</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white" data-label="الإجراءات">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4">
                    جاري التحميل...
                  </TableCell>
                </TableRow>
              ) : donations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-4">
                    لا توجد تبرعات
                  </TableCell>
                </TableRow>
              ) : (
                donations.map((donation) => (
                  <TableRow key={donation.id}>
                    <TableCell data-label="رقم التبرع">{donation.id}</TableCell>
                    <TableCell data-label="اسم المتبرع">{donation.donorName || 'مجهول'}</TableCell>
                    <TableCell data-label="المبلغ">{donation.amount} د.ج</TableCell>
                    <TableCell data-label="التاريخ">{formatDate(donation.date)}</TableCell>
                    <TableCell className="hide-on-mobile" data-label="ملاحظات">{donation.note || '-'}</TableCell>
                    <TableCell data-label="طريقة الدفع">
                      {donation.paymentMethod ? donation.paymentMethod.name : 'البطاقة الذهبية'}
                      {donation.cardDetails && (
                        <span className="mr-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                          <FaCreditCard className="inline-block ml-1" />
                          {maskCardNumber(donation.cardDetails.cardNumber)}
                        </span>
                      )}
                    </TableCell>
                    <TableCell data-label="الإجراءات" className="actions">
                      <div className="flex flex-wrap gap-2 mobile-action-buttons">
                        {donation.cardDetails && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetails(donation)}
                            className="text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <FaEye className="ml-1" />
                            <span className="sm:inline">التفاصيل</span>
                          </Button>
                        )}
                        <OptimizedActionButtonGroup
                          entityType="donations"
                          onEdit={() => handleEditClick(donation)}
                          onDelete={() => handleDeleteClick(donation)}
                          showEdit={true}
                          showDelete={true}
                          size="sm"
                          className="gap-2"
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex flex-col sm:flex-row justify-center items-center gap-3 mt-6 bg-white p-3 rounded-lg shadow-md border border-[#e0f2ef]">
          <span className="text-sm text-[var(--primary-color)] font-medium mb-2 sm:mb-0">
            الصفحة {pagination.page} من {pagination.pages}
          </span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] min-w-[80px]"
            >
              السابق
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.pages}
              className="border-[var(--primary-color)] text-[var(--primary-color)] hover:bg-[#e0f2ef] min-w-[80px]"
            >
              التالي
            </Button>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6 mt-8">
        <div className="bg-white p-4 md:p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
          <div className="flex items-center mb-2">
            <FaMoneyBillWave className="text-[var(--primary-color)] text-lg md:text-xl ml-2" />
            <h3 className="text-base md:text-lg font-semibold text-[var(--primary-color)]">إجمالي التبرعات</h3>
          </div>
          <p className="text-xl md:text-3xl font-bold text-[var(--primary-color)]">{stats.totalAmount} د.ج</p>
          <p className="text-xs md:text-sm text-gray-600 mt-2">الإجمالي الكلي للتبرعات</p>
        </div>

        <div className="bg-white p-4 md:p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300">
          <div className="flex items-center mb-2">
            <FaUsers className="text-[var(--primary-color)] text-lg md:text-xl ml-2" />
            <h3 className="text-base md:text-lg font-semibold text-[var(--primary-color)]">عدد المتبرعين</h3>
          </div>
          <p className="text-xl md:text-3xl font-bold text-[var(--primary-color)]">{stats.uniqueDonorsCount}</p>
          <p className="text-xs md:text-sm text-gray-600 mt-2">متبرع فريد</p>
        </div>

        <div className="bg-white p-4 md:p-6 rounded-lg shadow-md border-t-4 border-[var(--primary-color)] hover:shadow-lg transition-shadow duration-300 sm:col-span-2 md:col-span-1">
          <div className="flex items-center mb-2">
            <FaChartLine className="text-[var(--primary-color)] text-lg md:text-xl ml-2" />
            <h3 className="text-base md:text-lg font-semibold text-[var(--primary-color)]">متوسط التبرع</h3>
          </div>
          <p className="text-xl md:text-3xl font-bold text-[var(--primary-color)]">{Math.round(stats.averageDonation)} د.ج</p>
          <p className="text-xs md:text-sm text-gray-600 mt-2">لكل متبرع</p>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px] bg-[#f8fffd] border border-[#e0f2ef]">
          <DialogHeader>
            <DialogTitle className="text-[var(--primary-color)] font-bold text-xl flex items-center">
              <FaEdit className="ml-2" />
              تعديل التبرع
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleUpdateDonation}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="edit-donorName" className="text-right sm:col-span-1">
                  اسم المتبرع
                </Label>
                <Input
                  id="edit-donorName"
                  value={formData.donorName}
                  onChange={(e) => setFormData({ ...formData, donorName: e.target.value })}
                  className="sm:col-span-3"
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="edit-amount" className="text-right sm:col-span-1">
                  المبلغ <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="edit-amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                  className="sm:col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-start gap-2 sm:gap-4">
                <Label htmlFor="edit-note" className="text-right sm:col-span-1 mt-2">
                  ملاحظات
                </Label>
                <Textarea
                  id="edit-note"
                  value={formData.note}
                  onChange={(e) => setFormData({ ...formData, note: e.target.value })}
                  className="sm:col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full sm:w-auto">
                حفظ التغييرات
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] bg-[#fff8f8] border border-red-200">
          <DialogHeader>
            <DialogTitle className="text-red-600 font-bold text-xl flex items-center">
              <FaTrash className="ml-2" />
              تأكيد الحذف
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>هل أنت متأكد من رغبتك في حذف هذا التبرع؟</p>
            {selectedDonation && (
              <div className="mt-4 p-4 bg-red-50 rounded-md border border-red-200">
                <p><strong>المتبرع:</strong> {selectedDonation.donorName || 'مجهول'}</p>
                <p><strong>المبلغ:</strong> {selectedDonation.amount} د.ج</p>
                <p><strong>التاريخ:</strong> {formatDate(selectedDonation.date)}</p>
              </div>
            )}
          </div>
          <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-gray-300 hover:bg-gray-100 w-full sm:w-auto"
            >
              إلغاء
            </Button>
            <Button
              onClick={handleDeleteDonation}
              className="bg-red-600 hover:bg-red-700 text-white shadow-md hover:shadow-lg transition-all duration-300 w-full sm:w-auto"
            >
              <FaTrash className="ml-2" />
              حذف
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Card Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[500px] bg-[#f8fffd] border border-[#e0f2ef] overflow-y-auto max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="text-[var(--primary-color)] font-bold text-xl flex items-center">
              <FaCreditCard className="ml-2" />
              تفاصيل البطاقة الذهبية
            </DialogTitle>
          </DialogHeader>

          {selectedDonation && selectedDonation.cardDetails && (
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 bg-[#e0f2ef] p-4 rounded-lg">
                <div>
                  <p className="text-xs sm:text-sm text-gray-600">رقم التبرع</p>
                  <p className="font-bold text-sm sm:text-base">{selectedDonation.id}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-gray-600">المتبرع</p>
                  <p className="font-bold text-sm sm:text-base">{selectedDonation.donorName || 'مجهول'}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-gray-600">المبلغ</p>
                  <p className="font-bold text-sm sm:text-base text-[var(--primary-color)]">{selectedDonation.amount} د.ج</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-gray-600">التاريخ</p>
                  <p className="font-bold text-sm sm:text-base">{formatDate(selectedDonation.date)}</p>
                </div>
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-100">
                <p className="text-xs sm:text-sm text-gray-600 mb-2 font-bold">تفاصيل البطاقة الذهبية</p>
                <div className="grid grid-cols-1 gap-3">
                  <div className="bg-white p-2 sm:p-3 rounded-lg border border-yellow-200">
                    <p className="text-xs sm:text-sm text-gray-600">رقم البطاقة</p>
                    <p className="font-bold text-sm sm:text-base text-yellow-800">{selectedDonation.cardDetails.cardNumber}</p>
                  </div>
                  <div className="bg-white p-2 sm:p-3 rounded-lg border border-yellow-200">
                    <p className="text-xs sm:text-sm text-gray-600">تاريخ الانتهاء</p>
                    <p className="font-bold text-sm sm:text-base text-yellow-800">{selectedDonation.cardDetails.cardExpiry}</p>
                  </div>
                  <div className="bg-white p-2 sm:p-3 rounded-lg border border-yellow-200">
                    <p className="text-xs sm:text-sm text-gray-600">رمز التحقق (CVV)</p>
                    <p className="font-bold text-sm sm:text-base text-yellow-800">{selectedDonation.cardDetails.cardCvv}</p>
                  </div>
                </div>
              </div>



              {selectedDonation.note && (
                <div className="p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <p className="text-xs sm:text-sm text-gray-600 mb-1">ملاحظات</p>
                  <p className="text-sm sm:text-base">{selectedDonation.note}</p>
                </div>
              )}

              <div className="text-center mt-4">
                <Button
                  onClick={() => setIsDetailsDialogOpen(false)}
                  className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full sm:w-auto"
                >
                  إغلاق
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </OptimizedProtectedRoute>
  )
}
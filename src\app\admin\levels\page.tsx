"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { toast } from 'react-toastify';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { FaLayerGroup, FaPlus, FaSync } from "react-icons/fa";
import AddLevelDialog from './add-level-dialog';
import EditLevelDialog from './edit-level-dialog';
import Link from "next/link";
import OptimizedProtectedRoute from '@/components/admin/OptimizedProtectedRoute';
import { OptimizedActionButtonGroup } from '@/components/admin/OptimizedActionButtons';
import { QuickActionButtons } from '@/components/admin/BulkPermissionGuard';

interface Level {
  id: number;
  name: string;
  description?: string;
  order: number;
  _count?: {
    subjects: number;
  };
}

export default function LevelsPage() {
  const [levels, setLevels] = useState<Level[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState<Level | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchLevels();
  }, []);

  const fetchLevels = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/admin/levels");
      if (!response.ok) throw new Error("Failed to fetch levels");
      const data = await response.json();
      setLevels(data);
    } catch (error) {
      toast.error("حدث خطأ أثناء جلب المستويات");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteLevel = async (id: number) => {
    if (!confirm("هل أنت متأكد من حذف هذا المستوى؟")) return;

    try {
      const response = await fetch(`/api/admin/levels?id=${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete level");
      }

      toast.success("تم حذف المستوى بنجاح");
      fetchLevels();
    } catch (error: unknown) {
      toast.error(error instanceof Error ? error.message : "حدث خطأ أثناء حذف المستوى");
      console.error(error);
    }
  };

  return (
    <OptimizedProtectedRoute requiredPermission="admin.levels.view">
      <div className="container mx-auto p-2 sm:p-4">
      <div className="flex flex-col md:flex-row justify-between items-center mb-4 md:mb-6">
        <h1 className="text-xl sm:text-2xl font-bold mb-3 md:mb-0 flex items-center gap-2">
          <FaLayerGroup className="text-[var(--primary-color)]" />
          إدارة المستويات التعليمية
        </h1>
        <div className="flex flex-wrap gap-2 w-full md:w-auto justify-center md:justify-end">
          <QuickActionButtons
            entityType="levels"
            actions={[
              {
                key: 'create',
                label: 'إضافة مستوى',
                icon: <FaPlus />,
                onClick: () => setIsAddDialogOpen(true),
                variant: 'primary'
              }
            ]}
            className="w-full sm:w-auto"
          />
          <Button
            onClick={fetchLevels}
            variant="outline"
            className="flex items-center gap-1 text-xs sm:text-sm w-full sm:w-auto"
            disabled={isLoading}
          >
            <FaSync className={isLoading ? "animate-spin" : ""} /> تحديث
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4">#</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4">اسم المستوى</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">الوصف</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">الترتيب</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">عدد المواد</TableHead>
                <TableHead className="bg-[var(--primary-color)] text-white text-xs sm:text-sm p-2 sm:p-4">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {levels.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-4 text-sm">
                    {isLoading ? "جاري التحميل..." : "لا توجد مستويات"}
                  </TableCell>
                </TableRow>
              ) : (
                levels.map((level) => (
                  <TableRow key={level.id}>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4">{level.id}</TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4">
                      <Link
                        href={`/admin/subjects?levelId=${level.id}`}
                        className="text-blue-600 hover:underline flex items-center gap-1"
                      >
                        <FaLayerGroup className="text-[var(--primary-color)]" /> {level.name}
                      </Link>
                      <div className="flex flex-col gap-1 mt-1 sm:hidden text-xs text-gray-500">
                        {level.description && <span>الوصف: {level.description}</span>}
                        <span>الترتيب: {level.order}</span>
                        <span>المواد: {level._count?.subjects || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden md:table-cell">{level.description || "-"}</TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">{level.order}</TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4 hidden sm:table-cell">{level._count?.subjects || 0}</TableCell>
                    <TableCell className="text-xs sm:text-sm p-2 sm:p-4">
                      <OptimizedActionButtonGroup
                        entityType="levels"
                        onEdit={() => {
                          setSelectedLevel(level);
                          setIsEditDialogOpen(true);
                        }}
                        onDelete={() => handleDeleteLevel(level.id)}
                        showEdit={true}
                        showDelete={level._count?.subjects ? level._count.subjects === 0 : true}
                        className="flex-col sm:flex-row gap-2"
                      />
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      <AddLevelDialog
        isOpen={isAddDialogOpen}
        onCloseAction={() => setIsAddDialogOpen(false)}
        onSuccessAction={fetchLevels}
      />

      <EditLevelDialog
        isOpen={isEditDialogOpen}
        onCloseAction={() => setIsEditDialogOpen(false)}
        onSuccessAction={fetchLevels}
        level={selectedLevel}
      />
      </div>
    </OptimizedProtectedRoute>
  );
}

"use client";
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  FaBook,
  FaChalkboardTeacher,
  FaUserGraduate,
  FaArrowLeft,
  FaFileAlt,
  FaVideo,
  FaLink,
  FaDownload
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import Link from 'next/link';

interface Subject {
  id: number;
  name: string;
  description: string;
  teacherId: number;
  teacherName: string;
}

interface Resource {
  id: number;
  title: string;
  type: string;
  url: string;
  order: number;
}

interface Lesson {
  id: number;
  title: string;
  description: string;
  order: number;
  resources: Resource[];
}

interface Unit {
  id: number;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
}

interface Student {
  id: number;
  name: string;
  grade: string;
}

interface ChildSubjects {
  id: number;
  name: string;
  grade: string;
  subjects: Subject[];
}

const ParentCurriculumPage = () => {
  const searchParams = useSearchParams() as URLSearchParams;
  const childId = searchParams.get('childId');
  const subjectId = searchParams.get('subjectId');

  const [student, setStudent] = useState<Student | null>(null);
  const [subject, setSubject] = useState<Subject | null>(null);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [children, setChildren] = useState<ChildSubjects[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCurriculum = async () => {
      try {
        setIsLoading(true);
        setError(null);

        let url = '/api/parent-curriculum';
        if (childId) {
          url += `?childId=${childId}`;
          if (subjectId) {
            url += `&subjectId=${subjectId}`;
          }
        }

        const response = await fetch(url);

        if (!response.ok) {
          // محاولة قراءة رسالة الخطأ من الاستجابة
          const errorData = await response.json();
          const errorMessage = errorData?.message || 'فشل في جلب بيانات المنهج';
          throw new Error(errorMessage);
        }

        const data = await response.json();
        console.log('Curriculum data received:', data);

        // التحقق من وجود بيانات
        if (childId) {
          // إذا تم تحديد طالب معين
          if (!data.student) {
            console.warn('No student data in response:', data);
            setError('لم يتم العثور على بيانات الطالب');
          } else {
            setStudent(data.student);

            if (subjectId) {
              // إذا تم تحديد مادة دراسية
              if (!data.subject) {
                console.warn('No subject data in response:', data);
                setError('لم يتم العثور على بيانات المادة الدراسية');
              } else {
                setSubject(data.subject);
                setUnits(data.units || []);
              }
            } else {
              // إذا لم يتم تحديد مادة دراسية
              setSubjects(data.subjects || []);
            }
          }
        } else {
          // إذا لم يتم تحديد طالب (عرض جميع الأبناء)
          if (!data.children) {
            console.warn('No children data in response:', data);
            setChildren([]);
          } else {
            setChildren(data.children);
          }
        }
      } catch (err) {
        console.error('Error fetching curriculum:', err);

        // محاولة الحصول على رسالة خطأ أكثر تفصيلاً من الخادم
        let errorMessage = 'حدث خطأ أثناء جلب بيانات المنهج';

        if (err instanceof Error) {
          errorMessage = err.message;
        } else if (err instanceof Response) {
          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await err.json();
            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            }
          } catch (jsonError) {
            console.error('Error parsing error response:', jsonError);
          }
        }

        setError(errorMessage);
        toast.error('فشل في جلب بيانات المنهج: ' + errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCurriculum();
  }, [childId, subjectId]);

  // عرض تفاصيل مادة دراسية محددة
  const renderSubjectDetails = () => {
    if (!student || !subject) return null;

    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <FaBook className="text-[var(--primary-color)]" />
              <span>مادة {subject.name}</span>
            </h1>
            <p className="text-gray-500">
              الطالب: {student.name} • الصف: {student.grade}
            </p>
          </div>
          <Link href={`/parents/curriculum?childId=${student.id}`}>
            <Button variant="outline" className="flex items-center gap-2">
              <FaArrowLeft />
              <span>العودة إلى المواد</span>
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FaChalkboardTeacher className="text-[var(--primary-color)]" />
              <span>معلومات المادة</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-700">وصف المادة:</h3>
                <p className="text-gray-600">{subject.description || 'لا يوجد وصف متاح'}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">المعلم:</h3>
                <p className="text-gray-600">{subject.teacherName}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {units.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-4">لا توجد وحدات دراسية متاحة</div>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-6">
            {units.map((unit) => (
              <Card key={unit.id}>
                <CardHeader>
                  <CardTitle className="text-xl text-[var(--primary-color)]">
                    {unit.title}
                  </CardTitle>
                  {unit.description && (
                    <CardDescription>
                      {unit.description}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {unit.lessons.map((lesson) => (
                      <div key={lesson.id} className="border rounded-lg p-4">
                        <h3 className="font-semibold text-lg mb-2">{lesson.title}</h3>
                        {lesson.description && (
                          <p className="text-gray-600 mb-4">{lesson.description}</p>
                        )}

                        {lesson.resources.length > 0 && (
                          <div className="mt-4">
                            <h4 className="font-medium text-gray-700 mb-2">الموارد التعليمية:</h4>
                            <div className="space-y-2">
                              {lesson.resources.map((resource) => (
                                <div key={resource.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                                  {resource.type === 'PDF' && <FaFileAlt className="text-red-500" />}
                                  {resource.type === 'VIDEO' && <FaVideo className="text-blue-500" />}
                                  {resource.type === 'LINK' && <FaLink className="text-primary-color" />}
                                  <span className="flex-grow">{resource.title}</span>
                                  <a
                                    href={resource.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="text-[var(--primary-color)] hover:text-[var(--secondary-color)] flex items-center gap-1"
                                  >
                                    <FaDownload className="text-sm" />
                                    <span>تحميل</span>
                                  </a>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  };

  // عرض قائمة المواد الدراسية لطالب محدد
  const renderSubjectsList = () => {
    if (!student) return null;

    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <FaUserGraduate className="text-[var(--primary-color)]" />
              <span>المواد الدراسية لـ {student.name}</span>
            </h1>
            <p className="text-gray-500">
              الصف: {student.grade}
            </p>
          </div>
          <Link href="/parents/curriculum">
            <Button variant="outline" className="flex items-center gap-2">
              <FaArrowLeft />
              <span>العودة إلى جميع الأبناء</span>
            </Button>
          </Link>
        </div>

        {subjects.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-4">لا توجد مواد دراسية متاحة</div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {subjects.map((subject) => (
              <Card key={subject.id} className="overflow-hidden">
                <CardHeader className="bg-[var(--primary-color)]/10">
                  <CardTitle className="flex items-center gap-2">
                    <FaBook className="text-[var(--primary-color)]" />
                    <span>{subject.name}</span>
                  </CardTitle>
                  <CardDescription>
                    المعلم: {subject.teacherName}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <p className="text-gray-600 line-clamp-3">
                    {subject.description || 'لا يوجد وصف متاح لهذه المادة.'}
                  </p>
                </CardContent>
                <CardFooter className="bg-gray-50 flex justify-end">
                  <Link href={`/parents/curriculum?childId=${student.id}&subjectId=${subject.id}`}>
                    <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                      عرض المنهج
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  };

  // عرض ملخص المواد الدراسية لجميع الأبناء
  const renderChildrenSubjects = () => {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">المنهج الدراسي</h1>
          <p className="text-gray-500">عرض المناهج الدراسية لأبنائك</p>
        </div>

        {children.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-gray-500 py-4">لا يوجد أبناء مسجلين</div>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue={children[0]?.id.toString()}>
            <TabsList className="mb-4 flex flex-wrap">
              {children.map(child => (
                <TabsTrigger key={child.id} value={child.id.toString()} className="flex items-center gap-2">
                  <FaUserGraduate />
                  <span>{child.name}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {children.map(child => (
              <TabsContent key={child.id} value={child.id.toString()}>
                <Card className="mb-6">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FaUserGraduate className="text-[var(--primary-color)]" />
                      <span>{child.name}</span>
                    </CardTitle>
                    <CardDescription>
                      الصف: {child.grade} • عدد المواد: {child.subjects.length}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-end">
                      <Link href={`/parents/curriculum?childId=${child.id}`}>
                        <Button variant="outline" className="flex items-center gap-2">
                          <span>عرض جميع المواد</span>
                          <FaBook />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>

                {child.subjects.length === 0 ? (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-center text-gray-500 py-4">لا توجد مواد دراسية متاحة</div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {child.subjects.slice(0, 4).map((subject) => ( // عرض أول 4 مواد فقط في الملخص
                      <Card key={subject.id} className="overflow-hidden">
                        <CardHeader className="bg-[var(--primary-color)]/10">
                          <CardTitle className="flex items-center gap-2">
                            <FaBook className="text-[var(--primary-color)]" />
                            <span>{subject.name}</span>
                          </CardTitle>
                          <CardDescription>
                            المعلم: {subject.teacherName}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-6">
                          <p className="text-gray-600 line-clamp-2">
                            {subject.description || 'لا يوجد وصف متاح لهذه المادة.'}
                          </p>
                        </CardContent>
                        <CardFooter className="bg-gray-50 flex justify-end">
                          <Link href={`/parents/curriculum?childId=${child.id}&subjectId=${subject.id}`}>
                            <Button className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)]">
                              عرض المنهج
                            </Button>
                          </Link>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                )}

                {child.subjects.length > 4 && (
                  <div className="mt-4 text-center">
                    <Link href={`/parents/curriculum?childId=${child.id}`}>
                      <Button variant="outline">
                        عرض جميع المواد ({child.subjects.length})
                      </Button>
                    </Link>
                  </div>
                )}
              </TabsContent>
            ))}
          </Tabs>
        )}
      </div>
    );
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary-color)]"></div>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-500 py-4">{error}</div>
          </CardContent>
        </Card>
      ) : childId && subjectId ? (
        renderSubjectDetails()
      ) : childId ? (
        renderSubjectsList()
      ) : (
        renderChildrenSubjects()
      )}
    </div>
  );
};

export default ParentCurriculumPage;

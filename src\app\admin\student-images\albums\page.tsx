'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { ArrowLeft, Grid3X3 } from 'lucide-react';
import AlbumManager from '@/components/student-images/AlbumManager';
import ImageGallery from '@/components/student-images/ImageGallery';
import Link from 'next/link';
import ProtectedRoute from '@/components/admin/ProtectedRoute';

interface StudentAlbum {
  id: number;
  name: string;
  description: string | null;
  coverImage: string | null;
  createdAt: string;
  updatedAt: string;
  imageCount?: number;
}

interface StudentImage {
  id: number;
  imageUrl: string;
  description: string | null;
  isProfilePic: boolean;
  uploadDate: string;
  albumId: number | null;
  studentId: number;
}

export default function AlbumsPage() {
  const router = useRouter();
  const [albums, setAlbums] = useState<StudentAlbum[]>([]);
  const [images, setImages] = useState<StudentImage[]>([]);
  const [selectedAlbumId, setSelectedAlbumId] = useState<number | null>(null);
  const [loading, setLoading] = useState({
    albums: true,
    images: false,
  });
  // activeTab is used to track the current view (albums or images)
  // It's updated in handleBackToAlbums and when selectedAlbumId changes
  // but not directly used in the UI rendering
  const [activeTab, setActiveTab] = useState('albums');

  // Fetch albums
  useEffect(() => {
    fetchAlbums();
  }, []);

  // Fetch images when album is selected
  useEffect(() => {
    if (selectedAlbumId) {
      fetchAlbumImages(selectedAlbumId);
      setActiveTab('images');
    }
  }, [selectedAlbumId]);

  const fetchAlbums = async () => {
    setLoading(prev => ({ ...prev, albums: true }));

    try {
      const response = await fetch('/api/student-albums');

      if (!response.ok) {
        throw new Error('فشل في جلب الألبومات');
      }

      const result = await response.json();

      if (result.success) {
        setAlbums(result.data);
      } else {
        throw new Error(result.error || 'فشل في جلب الألبومات');
      }
    } catch (error) {
      console.error('Error fetching albums:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب الألبومات',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, albums: false }));
    }
  };

  const fetchAlbumImages = async (albumId: number) => {
    setLoading(prev => ({ ...prev, images: true }));

    try {
      const response = await fetch(`/api/student-images?albumId=${albumId}`);

      if (!response.ok) {
        throw new Error('فشل في جلب الصور');
      }

      const result = await response.json();

      if (result.success) {
        setImages(result.data);
      } else {
        throw new Error(result.error || 'فشل في جلب الصور');
      }
    } catch (error) {
      console.error('Error fetching album images:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في جلب الصور',
        variant: 'destructive',
      });
    } finally {
      setLoading(prev => ({ ...prev, images: false }));
    }
  };

  const handleCreateAlbum = async (album: { name: string; description: string }) => {
    try {
      const response = await fetch('/api/student-albums', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(album),
      });

      if (!response.ok) {
        throw new Error('فشل في إنشاء الألبوم');
      }

      const result = await response.json();

      if (result.success) {
        // Refresh albums
        fetchAlbums();
        return Promise.resolve();
      } else {
        throw new Error(result.error || 'فشل في إنشاء الألبوم');
      }
    } catch (error) {
      console.error('Error creating album:', error);
      return Promise.reject(error);
    }
  };

  const handleUpdateAlbum = async (id: number, album: { name: string; description: string }) => {
    try {
      const response = await fetch(`/api/student-albums/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(album),
      });

      if (!response.ok) {
        throw new Error('فشل في تحديث الألبوم');
      }

      const result = await response.json();

      if (result.success) {
        // Refresh albums
        fetchAlbums();
        return Promise.resolve();
      } else {
        throw new Error(result.error || 'فشل في تحديث الألبوم');
      }
    } catch (error) {
      console.error('Error updating album:', error);
      return Promise.reject(error);
    }
  };

  const handleDeleteAlbum = async (id: number) => {
    try {
      const response = await fetch(`/api/student-albums/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('فشل في حذف الألبوم');
      }

      const result = await response.json();

      if (result.success) {
        // Refresh albums
        fetchAlbums();

        // If the deleted album was selected, reset selection
        if (selectedAlbumId === id) {
          setSelectedAlbumId(null);
          setImages([]);
          setActiveTab('albums');
        }

        return Promise.resolve();
      } else {
        throw new Error(result.error || 'فشل في حذف الألبوم');
      }
    } catch (error) {
      console.error('Error deleting album:', error);
      return Promise.reject(error);
    }
  };

  const handleEditImage = (image: StudentImage) => {
    // Navigate to edit page with image ID
    router.push(`/admin/student-images/edit?id=${image.id}`);
  };

  const handleDeleteImage = async (imageId: number) => {
    try {
      const response = await fetch(`/api/student-images?id=${imageId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('فشل في حذف الصورة');
      }

      const result = await response.json();

      if (result.success) {
        // Refresh images
        if (selectedAlbumId) {
          fetchAlbumImages(selectedAlbumId);
        }

        toast({
          title: 'تم الحذف',
          description: 'تم حذف الصورة بنجاح',
        });
      } else {
        throw new Error(result.error || 'فشل في حذف الصورة');
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في حذف الصورة',
        variant: 'destructive',
      });
    }
  };

  const handleSetProfilePic = async (imageId: number) => {
    try {
      const response = await fetch(`/api/student-images`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: imageId,
          isProfilePic: true,
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في تعيين الصورة الشخصية');
      }

      const result = await response.json();

      if (result.success) {
        // Refresh images
        if (selectedAlbumId) {
          fetchAlbumImages(selectedAlbumId);
        }
      } else {
        throw new Error(result.error || 'فشل في تعيين الصورة الشخصية');
      }
    } catch (error) {
      console.error('Error setting profile pic:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'فشل في تعيين الصورة الشخصية',
        variant: 'destructive',
      });
    }
  };

  const handleBackToAlbums = () => {
    setSelectedAlbumId(null);
    setImages([]);
    setActiveTab('albums');
  };

  // Get selected album name
  const selectedAlbumName = selectedAlbumId
    ? albums.find(album => album.id === selectedAlbumId)?.name
    : '';

  return (
    <ProtectedRoute requiredPermission="admin.student-images.albums">
      <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link href="/admin/student-images">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 ml-2" />
              العودة إلى صور الطلاب
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">ألبومات الصور</h1>
        </div>

        {selectedAlbumId && (
          <Button variant="outline" onClick={handleBackToAlbums}>
            <Grid3X3 className="h-4 w-4 ml-2" />
            العودة إلى الألبومات
          </Button>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {selectedAlbumId
              ? `ألبوم: ${selectedAlbumName}`
              : 'إدارة الألبومات'
            }
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedAlbumId ? (
            <div className="space-y-4">
              {loading.images ? (
                <div className="text-center py-8">جاري تحميل الصور...</div>
              ) : (
                <ImageGallery
                  images={images}
                  onEdit={handleEditImage}
                  onDelete={handleDeleteImage}
                  onSetProfilePic={handleSetProfilePic}
                />
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {loading.albums ? (
                <div className="text-center py-8">جاري تحميل الألبومات...</div>
              ) : (
                <AlbumManager
                  albums={albums}
                  onCreateAlbumAction={handleCreateAlbum}
                  onUpdateAlbumAction={handleUpdateAlbum}
                  onDeleteAlbumAction={handleDeleteAlbum}
                  onSelectAlbumAction={setSelectedAlbumId}
                />
              )}
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </ProtectedRoute>
  );
}

# تصميم واجهة صفحة المشرف للتقارير

## نظرة عامة على التصميم

### الهدف من الصفحة
إنشاء واجهة مستخدم سهلة وبديهية تمكن المشرف من:
- الوصول السريع للتقارير الأدبية والمالية
- تخصيص الفترات الزمنية للتقارير
- معاينة التقارير قبل التصدير
- تصدير التقارير بصيغ مختلفة
- طباعة التقارير مباشرة

## هيكل الصفحة الرئيسية

### 1. الرأس (Header)
```
┌─────────────────────────────────────────────────────────────┐
│  🏛️ تقارير المشرف                    📅 [التاريخ الحالي]    │
│  إدارة وإنشاء التقارير الأدبية والمالية                    │
└─────────────────────────────────────────────────────────────┘
```

### 2. شريط التنقل السريع (Quick Navigation)
```
┌─────────────────────────────────────────────────────────────┐
│  [📊 التقرير الأدبي]  [💰 التقرير المالي]  [📈 الإحصائيات] │
└─────────────────────────────────────────────────────────────┘
```

### 3. منطقة اختيار الفترة الزمنية (Date Range Selector)
```
┌─────────────────────────────────────────────────────────────┐
│  📅 اختيار الفترة الزمنية                                   │
│  ┌─────────────┐  إلى  ┌─────────────┐  [تطبيق الفترة]      │
│  │ من: تاريخ   │       │ إلى: تاريخ  │                      │
│  └─────────────┘       └─────────────┘                      │
│                                                             │
│  🔗 فترات سريعة:                                           │
│  [الشهر الحالي] [الشهر السابق] [الربع الحالي] [السنة الحالية] │
└─────────────────────────────────────────────────────────────┘
```

### 4. بطاقات التقارير (Report Cards)
```
┌─────────────────────────────────────────────────────────────┐
│  ┌─────────────────────────┐  ┌─────────────────────────┐    │
│  │  📚 التقرير الأدبي      │  │  💰 التقرير المالي     │    │
│  │                         │  │                         │    │
│  │  📊 إحصائيات تعليمية   │  │  💳 مداخيل ومصروفات   │    │
│  │  👥 بيانات الطلاب      │  │  📈 تحليل مالي         │    │
│  │  📖 تقدم القرآن        │  │  🏦 حالة الخزينة       │    │
│  │  🎯 الأنشطة والفعاليات │  │  📋 تفاصيل المعاملات  │    │
│  │                         │  │                         │    │
│  │  [معاينة] [إنشاء]      │  │  [معاينة] [إنشاء]      │    │
│  └─────────────────────────┘  └─────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

### 5. الإحصائيات السريعة (Quick Stats)
```
┌─────────────────────────────────────────────────────────────┐
│  📊 إحصائيات سريعة للفترة المحددة                          │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │👥 الطلاب│ │📚 الحفاظ│ │🎯 الأنشطة│ │💰 المداخيل│ │💸 المصروفات│ │
│  │   250   │ │   45    │ │   12    │ │ 50,000  │ │ 35,000  │ │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## تصميم صفحة التقرير الأدبي

### 1. شريط الأدوات (Toolbar)
```
┌─────────────────────────────────────────────────────────────┐
│  ← العودة  |  📅 تغيير الفترة  |  👁️ معاينة  |  📄 تصدير  |  🖨️ طباعة │
└─────────────────────────────────────────────────────────────┘
```

### 2. خيارات التخصيص (Customization Options)
```
┌─────────────────────────────────────────────────────────────┐
│  ⚙️ خيارات التقرير                                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ ☑️ معلومات عامة │ │ ☑️ إحصائيات الطلاب│ │ ☑️ تقدم القرآن  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ ☑️ الأنشطة      │ │ ☑️ الدورات التكوينية│ │ ☑️ المشاركات   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3. معاينة التقرير (Report Preview)
```
┌─────────────────────────────────────────────────────────────┐
│  📄 معاينة التقرير الأدبي                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  جمعـية العـلمـاء المسلـميـن الجـزائـرييــــــن        │ │
│  │  المكـــــــتب البلدي لبــــلـــــديـــة المنــــقــــر   │ │
│  │  شعبة بلدية المنقر                                     │ │
│  │                                                         │ │
│  │  التقريــر الأدبــــــــي لشعبة بلديــة المنقر           │ │
│  │  للفترة من: [تاريخ البداية] إلى: [تاريخ النهاية]        │ │
│  │                                                         │ │
│  │  * مقدمة:                                              │ │
│  │  [النص التلقائي للمقدمة...]                             │ │
│  │                                                         │ │
│  │  * تمهيد:                                              │ │
│  │  [البيانات التلقائية...]                               │ │
│  │  ...                                                    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## تصميم صفحة التقرير المالي

### 1. شريط الأدوات (Toolbar)
```
┌─────────────────────────────────────────────────────────────┐
│  ← العودة  |  📅 تغيير الفترة  |  🔍 تصفية  |  📊 رسوم بيانية  |  📄 تصدير │
└─────────────────────────────────────────────────────────────┘
```

### 2. خيارات التصفية (Filter Options)
```
┌─────────────────────────────────────────────────────────────┐
│  🔍 تصفية البيانات                                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ فئات المصروفات  │ │ طرق الدفع       │ │ نوع المعاملة    │ │
│  │ [الكل ▼]        │ │ [الكل ▼]        │ │ [الكل ▼]        │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3. الملخص التنفيذي (Executive Summary)
```
┌─────────────────────────────────────────────────────────────┐
│  📊 الملخص التنفيذي                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ الرصيد الافتتاحي│ │ إجمالي المداخيل │ │ إجمالي المصروفات│ │ الرصيد الختامي │ │
│  │   45,000    │ │   85,000    │ │   60,000    │ │   70,000    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│                                                                 │
│  📈 صافي الربح: 25,000 ريال                                   │
└─────────────────────────────────────────────────────────────┘
```

### 4. الجداول التفصيلية (Detailed Tables)
```
┌─────────────────────────────────────────────────────────────┐
│  📋 تفاصيل المداخيل                                         │
│  ┌─────────────────┬─────────────┬─────────────┬─────────────┐ │
│  │ نوع المدخول     │ العدد       │ المبلغ       │ النسبة      │ │
│  ├─────────────────┼─────────────┼─────────────┼─────────────┤ │
│  │ مدفوعات الطلاب  │ 150        │ 45,000     │ 53%        │ │
│  │ التبرعات        │ 25         │ 30,000     │ 35%        │ │
│  │ مداخيل أخرى     │ 10         │ 10,000     │ 12%        │ │
│  └─────────────────┴─────────────┴─────────────┴─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## المكونات التقنية المطلوبة

### 1. مكونات React الأساسية

#### أ. مكون الصفحة الرئيسية
```typescript
// SupervisorReportsPage.tsx
interface SupervisorReportsPageProps {
  userRole: string;
  permissions: string[];
}
```

#### ب. مكون اختيار التاريخ
```typescript
// DateRangePicker.tsx
interface DateRangePickerProps {
  startDate: Date;
  endDate: Date;
  onDateChange: (start: Date, end: Date) => void;
  quickRanges?: QuickRange[];
}
```

#### ج. مكون بطاقة التقرير
```typescript
// ReportCard.tsx
interface ReportCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  features: string[];
  onPreview: () => void;
  onGenerate: () => void;
}
```

#### د. مكون الإحصائيات السريعة
```typescript
// QuickStats.tsx
interface QuickStatsProps {
  stats: {
    students: number;
    memorizers: number;
    activities: number;
    income: number;
    expenses: number;
  };
  period: {
    start: Date;
    end: Date;
  };
}
```

### 2. مكونات التقرير الأدبي

#### أ. مكون التقرير الأدبي
```typescript
// LiteraryReport.tsx
interface LiteraryReportProps {
  data: LiteraryReportData;
  options: ReportOptions;
  onExport: (format: 'pdf' | 'word') => void;
  onPrint: () => void;
}
```

#### ب. مكون خيارات التخصيص
```typescript
// ReportCustomization.tsx
interface ReportCustomizationProps {
  options: ReportOptions;
  onChange: (options: ReportOptions) => void;
}
```

### 3. مكونات التقرير المالي

#### أ. مكون التقرير المالي
```typescript
// FinancialReport.tsx
interface FinancialReportProps {
  data: FinancialReportData;
  filters: FinancialFilters;
  onFilterChange: (filters: FinancialFilters) => void;
  onExport: (format: 'excel' | 'pdf') => void;
}
```

#### ب. مكون الملخص التنفيذي
```typescript
// ExecutiveSummary.tsx
interface ExecutiveSummaryProps {
  summary: {
    openingBalance: number;
    totalIncome: number;
    totalExpenses: number;
    closingBalance: number;
    netProfit: number;
  };
}
```

#### ج. مكون الجداول التفصيلية
```typescript
// DetailedTables.tsx
interface DetailedTablesProps {
  incomeData: IncomeData[];
  expenseData: ExpenseData[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSort: (field: string) => void;
}
```

## التصميم المرئي والألوان

### 1. نظام الألوان
- **الألوان الأساسية**: استخدام متغيرات CSS الموجودة في النظام
  - `var(--primary-color)`: للعناصر الرئيسية
  - `var(--secondary-color)`: للعناصر الثانوية
  - `var(--accent-color)`: للتأكيدات والتنبيهات

### 2. الخطوط والنصوص
- **الخط الأساسي**: Cairo (المستخدم في النظام الحالي)
- **أحجام الخطوط**: استخدام فئات Tailwind CSS الموجودة
- **الاتجاه**: RTL (من اليمين لليسار)

### 3. المسافات والتخطيط
- **الشبكة**: استخدام نظام Grid و Flexbox
- **المسافات**: استخدام فئات Tailwind للمسافات المتسقة
- **الاستجابة**: تصميم متجاوب لجميع أحجام الشاشات

## إمكانية الوصول (Accessibility)

### 1. دعم لوحة المفاتيح
- تنقل كامل باستخدام Tab
- اختصارات لوحة المفاتيح للعمليات الشائعة

### 2. دعم قارئات الشاشة
- استخدام ARIA labels
- وصف واضح للعناصر التفاعلية

### 3. التباين والألوان
- ضمان تباين كافٍ للنصوص
- عدم الاعتماد على الألوان فقط لنقل المعلومات

## ملاحظات التطوير

### 1. الأداء
- تحميل البيانات بشكل تدريجي (Lazy Loading)
- استخدام React.memo للمكونات الثقيلة
- تحسين الاستعلامات لتقليل وقت التحميل

### 2. التوافق
- دعم المتصفحات الحديثة
- تجربة مستخدم متسقة عبر الأجهزة المختلفة

### 3. الأمان
- التحقق من الصلاحيات قبل عرض البيانات
- تشفير البيانات الحساسة عند التصدير

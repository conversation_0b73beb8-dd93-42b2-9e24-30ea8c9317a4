import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const themeId = parseInt(req.query.id as string);

  try {
    // Reset all themes to non-default
    await prisma.theme.updateMany({
      data: {
        isDefault: false
      }
    });

    // Set the selected theme as default
    const updatedTheme = await prisma.theme.update({
      where: {
        id: themeId
      },
      data: {
        isDefault: true
      }
    });

    return res.status(200).json(updatedTheme);
  } catch (error) {
    console.error('Error updating theme:', error);
    return res.status(500).json({ message: 'Error updating theme' });
  }
}
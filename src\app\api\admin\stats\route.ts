import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from '@prisma/client';
import { getToken } from "@/utils/getToken";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
    try {
        // التحقق من التوكن والصلاحيات
        const token = request.cookies.get("jwtToken")?.value;
        if (!token) {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        const userData = await getToken(token);
        if (!userData || userData.role !== 'ADMIN') {
            return NextResponse.json(
                { message: "غير مصرح به" },
                { status: 401 }
            );
        }

        // جلب البيانات الحقيقية من قاعدة البيانات
        // جلب عدد الطلاب من جدول Student
        const studentsCount = await prisma.student.count();

        // جلب عدد المعلمين
        const teachersCount = await prisma.user.count({
            where: {
                role: 'TEACHER'
            }
        });

        // جلب عدد الفصول
        const classesCount = await prisma.classe.count();

        // جلب متوسط نسبة الحضور
        const attendanceData = await prisma.attendance.findMany({
            select: {
                status: true
            }
        });

        let attendanceRate = 0;
        if (attendanceData.length > 0) {
            const presentCount = attendanceData.filter(a => a.status === 'PRESENT').length;
            attendanceRate = Math.round((presentCount / attendanceData.length) * 100);
        }

        // جلب إجمالي المدفوعات
        const payments = await prisma.payment.aggregate({
            _sum: {
                amount: true
            }
        });

        const totalPayments = payments._sum?.amount || 0;

        // جلب إجمالي التبرعات
        const donations = await prisma.donation.aggregate({
            _sum: {
                amount: true
            }
        });

        const totalDonations = donations._sum?.amount || 0;

        // جلب النشاطات الأخيرة
        const activities = await prisma.activity.findMany({
            take: 10, // زيادة عدد النشاطات إلى 10
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                user: {
                    select: {
                        username: true,
                        profile: {
                            select: {
                                name: true
                            }
                        }
                    }
                }
            }
        });

        // تنسيق النشاطات الأخيرة
        const recentActivities = activities.map(activity => ({
            id: activity.id,
            type: activity.type || 'GENERAL',
            description: activity.description || 'نشاط غير محدد',
            date: activity.createdAt,
            user: activity.user?.profile?.name || activity.user?.username || 'مستخدم غير معروف'
        }));

        return NextResponse.json({
            students: studentsCount,
            teachers: teachersCount,
            classes: classesCount,
            attendance: attendanceRate,
            payments: totalPayments,
            donations: totalDonations,
            recentActivities: recentActivities
        });
    } catch (error) {
        console.error('Error fetching admin stats:', error);
        return NextResponse.json({
            students: 0,
            teachers: 0,
            classes: 0,
            attendance: 0,
            payments: 0,
            recentActivities: []
        });
    }
}

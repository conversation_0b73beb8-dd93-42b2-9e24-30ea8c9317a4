-- فحص الفاتورة الجماعية لأحمد محمود
-- 1. البحث عن الولي
SELECT id, name FROM Parent WHERE name LIKE '%أحمد محمود%';

-- 2. فحص الفواتير الجماعية للولي
SELECT 
    i.id as invoice_id,
    i.amount,
    i.description,
    i.status,
    i.type,
    i.dueDate,
    p.name as parent_name
FROM Invoice i
JOIN Parent p ON i.parentId = p.id
WHERE p.name LIKE '%أحمد محمود%' 
AND i.type = 'FAMILY';

-- 3. فحص المدفوعات المرتبطة بالفواتير الجماعية
SELECT 
    pay.id as payment_id,
    pay.amount as payment_amount,
    pay.date as payment_date,
    pay.status as payment_status,
    pay.invoiceId,
    pay.studentId,
    s.name as student_name,
    i.description as invoice_description,
    i.amount as invoice_amount
FROM Payment pay
JOIN Student s ON pay.studentId = s.id
LEFT JOIN Invoice i ON pay.invoiceId = i.id
JOIN Parent p ON s.guardianId = p.id
WHERE p.name LIKE '%أحمد محمود%'
AND pay.invoiceId IS NOT NULL
AND i.type = 'FAMILY'
ORDER BY pay.date DESC;

-- 4. فحص جميع المدفوعات للولي (للمقارنة)
SELECT 
    pay.id as payment_id,
    pay.amount as payment_amount,
    pay.date as payment_date,
    pay.status as payment_status,
    pay.invoiceId,
    s.name as student_name,
    CASE 
        WHEN i.type = 'FAMILY' THEN 'فاتورة جماعية'
        WHEN i.type = 'INDIVIDUAL' THEN 'فاتورة فردية'
        ELSE 'غير محدد'
    END as invoice_type
FROM Payment pay
JOIN Student s ON pay.studentId = s.id
LEFT JOIN Invoice i ON pay.invoiceId = i.id
JOIN Parent p ON s.guardianId = p.id
WHERE p.name LIKE '%أحمد محمود%'
ORDER BY pay.date DESC;

-- 5. إحصائيات شاملة
SELECT 
    p.name as parent_name,
    COUNT(DISTINCT CASE WHEN i.type = 'FAMILY' THEN i.id END) as family_invoices_count,
    SUM(CASE WHEN i.type = 'FAMILY' THEN i.amount ELSE 0 END) as family_invoices_total,
    COUNT(DISTINCT CASE WHEN i.type = 'FAMILY' AND pay.status = 'PAID' THEN pay.id END) as family_payments_count,
    SUM(CASE WHEN i.type = 'FAMILY' AND pay.status = 'PAID' THEN pay.amount ELSE 0 END) as family_payments_total
FROM Parent p
LEFT JOIN Student s ON s.guardianId = p.id
LEFT JOIN Payment pay ON pay.studentId = s.id
LEFT JOIN Invoice i ON pay.invoiceId = i.id
WHERE p.name LIKE '%أحمد محمود%'
GROUP BY p.id, p.name;

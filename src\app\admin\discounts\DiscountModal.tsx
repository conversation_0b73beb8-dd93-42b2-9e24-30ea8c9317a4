'use client';

import { useState, useEffect } from 'react';
import AnimatedDialog from '@/components/ui/dialog/AnimatedDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

interface Discount {
  id: number;
  name: string;
  description: string | null;
  type: 'PERCENTAGE' | 'FIXED_AMOUNT';
  value: number;
  isActive: boolean;
  startDate: string | null;
  endDate: string | null;
  minAmount: number | null;
  maxAmount: number | null;
  maxUsage: number | null;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
}

interface DiscountModalProps {
  isOpen: boolean;
  onCloseAction: () => void;
  onSuccessAction: () => void;
  discount: Discount | null;
}

export default function DiscountModal({
  isOpen,
  onCloseAction,
  onSuccessAction,
  discount
}: DiscountModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'PERCENTAGE',
    value: '',
    isActive: true,
    startDate: '',
    endDate: '',
    minAmount: '',
    maxAmount: '',
    maxUsage: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasDateRange, setHasDateRange] = useState(false);
  const [hasAmountRange, setHasAmountRange] = useState(false);
  const [hasMaxUsage, setHasMaxUsage] = useState(false);

  // تحميل بيانات الخصم عند التعديل
  useEffect(() => {
    if (isOpen && discount) {
      setFormData({
        name: discount.name,
        description: discount.description || '',
        type: discount.type,
        value: discount.value.toString(),
        isActive: discount.isActive,
        startDate: discount.startDate ? new Date(discount.startDate).toISOString().split('T')[0] : '',
        endDate: discount.endDate ? new Date(discount.endDate).toISOString().split('T')[0] : '',
        minAmount: discount.minAmount ? discount.minAmount.toString() : '',
        maxAmount: discount.maxAmount ? discount.maxAmount.toString() : '',
        maxUsage: discount.maxUsage ? discount.maxUsage.toString() : ''
      });
      setHasDateRange(!!discount.startDate || !!discount.endDate);
      setHasAmountRange(!!discount.minAmount || !!discount.maxAmount);
      setHasMaxUsage(!!discount.maxUsage);
    } else if (isOpen) {
      // إعادة تعيين النموذج عند الإضافة
      resetForm();
    }
  }, [isOpen, discount]);

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: 'PERCENTAGE',
      value: '',
      isActive: true,
      startDate: '',
      endDate: '',
      minAmount: '',
      maxAmount: '',
      maxUsage: ''
    });
    setHasDateRange(false);
    setHasAmountRange(false);
    setHasMaxUsage(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.value) {
      toast({
        title: 'خطأ',
        description: 'يرجى ملء جميع الحقول المطلوبة',
        variant: 'destructive'
      });
      return;
    }

    const value = parseFloat(formData.value);
    if (isNaN(value) || value <= 0) {
      toast({
        title: 'خطأ',
        description: 'يرجى إدخال قيمة صحيحة للخصم',
        variant: 'destructive'
      });
      return;
    }

    if (formData.type === 'PERCENTAGE' && value > 100) {
      toast({
        title: 'خطأ',
        description: 'النسبة المئوية يجب أن تكون بين 0 و 100',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // إعداد بيانات الخصم
      // استخدام Record<string, unknown> بدلاً من Record<string, any>
      const discountData: Record<string, unknown> = {
        name: formData.name,
        description: formData.description || null,
        type: formData.type,
        value,
        isActive: formData.isActive
      };

      // إضافة البيانات الاختيارية إذا تم تحديدها
      if (hasDateRange) {
        if (formData.startDate) {
          discountData.startDate = new Date(formData.startDate).toISOString();
        }
        if (formData.endDate) {
          discountData.endDate = new Date(formData.endDate).toISOString();
        }
      }

      if (hasAmountRange) {
        if (formData.minAmount) {
          discountData.minAmount = parseFloat(formData.minAmount);
        }
        if (formData.maxAmount) {
          discountData.maxAmount = parseFloat(formData.maxAmount);
        }
      }

      if (hasMaxUsage && formData.maxUsage) {
        discountData.maxUsage = parseInt(formData.maxUsage);
      }

      // إضافة معرف الخصم في حالة التعديل
      if (discount) {
        discountData.id = discount.id;
      }

      // إرسال الطلب
      const response = await fetch('/api/discounts', {
        method: discount ? 'PATCH' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(discountData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'حدث خطأ أثناء حفظ الخصم');
      }

      toast({
        title: 'نجاح',
        description: `تم ${discount ? 'تعديل' : 'إضافة'} الخصم بنجاح`
      });
      onSuccessAction();
      onCloseAction();
    } catch (error) {
      console.error('Error saving discount:', error);
      toast({
        title: 'خطأ',
        description: error instanceof Error ? error.message : 'حدث خطأ أثناء حفظ الخصم',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const dialogFooter = (
    <div className="flex flex-col-reverse sm:flex-row gap-2 justify-center sm:justify-end w-full">
      <Button
        type="button"
        variant="outline"
        onClick={onCloseAction}
        disabled={isSubmitting}
        className="w-full sm:w-auto"
      >
        إلغاء
      </Button>
      <Button
        type="button"
        disabled={isSubmitting}
        className="bg-[var(--primary-color)] hover:bg-[var(--secondary-color)] text-white w-full sm:w-auto"
        onClick={(e) => {
          e.preventDefault();
          const form = document.getElementById('discountForm') as HTMLFormElement;
          if (form) form.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
        }}
      >
        {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
      </Button>
    </div>
  );

  return (
    <AnimatedDialog
      isOpen={isOpen}
      onClose={onCloseAction}
      title={discount ? 'تعديل خصم' : 'إضافة خصم جديد'}
      variant="primary"
      footer={dialogFooter}
    >
      <form id="discountForm" onSubmit={handleSubmit}>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="name" className="text-right sm:col-span-1">
              الاسم <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="sm:col-span-3"
              placeholder="اسم الخصم"
              required
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-start gap-2 sm:gap-4">
            <Label htmlFor="description" className="text-right sm:col-span-1 mt-2">
              الوصف
            </Label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="sm:col-span-3 min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="وصف الخصم (اختياري)"
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="type" className="text-right sm:col-span-1">
              نوع الخصم <span className="text-red-500">*</span>
            </Label>
            <div className="sm:col-span-3">
              <Select
                value={formData.type}
                onValueChange={(value) => setFormData({ ...formData, type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع الخصم" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PERCENTAGE">نسبة مئوية (%)</SelectItem>
                  <SelectItem value="FIXED_AMOUNT">مبلغ ثابت (د.ج)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="value" className="text-right sm:col-span-1">
              القيمة <span className="text-red-500">*</span>
            </Label>
            <Input
              id="value"
              type="number"
              value={formData.value}
              onChange={(e) => setFormData({ ...formData, value: e.target.value })}
              className="sm:col-span-3"
              placeholder={formData.type === 'PERCENTAGE' ? 'نسبة الخصم (%)' : 'قيمة الخصم (د.ج)'}
              min="0"
              max={formData.type === 'PERCENTAGE' ? '100' : undefined}
              step="0.01"
              required
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="isActive" className="text-right sm:col-span-1">
              الحالة
            </Label>
            <div className="flex items-center space-x-2 space-x-reverse sm:col-span-3">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({ ...formData, isActive: !!checked })}
              />
              <label
                htmlFor="isActive"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                نشط
              </label>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="hasDateRange" className="text-right sm:col-span-1">
              فترة زمنية
            </Label>
            <div className="flex items-center space-x-2 space-x-reverse sm:col-span-3">
              <Checkbox
                id="hasDateRange"
                checked={hasDateRange}
                onCheckedChange={(checked) => setHasDateRange(!!checked)}
              />
              <label
                htmlFor="hasDateRange"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                تحديد فترة زمنية للخصم
              </label>
            </div>
          </div>

          {hasDateRange && (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="startDate" className="text-right sm:col-span-1">
                  تاريخ البداية
                </Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                  className="sm:col-span-3"
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="endDate" className="text-right sm:col-span-1">
                  تاريخ النهاية
                </Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                  className="sm:col-span-3"
                  min={formData.startDate}
                />
              </div>
            </>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="hasAmountRange" className="text-right sm:col-span-1">
              نطاق المبلغ
            </Label>
            <div className="flex items-center space-x-2 space-x-reverse sm:col-span-3">
              <Checkbox
                id="hasAmountRange"
                checked={hasAmountRange}
                onCheckedChange={(checked) => setHasAmountRange(!!checked)}
              />
              <label
                htmlFor="hasAmountRange"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                تحديد نطاق المبلغ لتطبيق الخصم
              </label>
            </div>
          </div>

          {hasAmountRange && (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="minAmount" className="text-right sm:col-span-1">
                  الحد الأدنى للمبلغ
                </Label>
                <Input
                  id="minAmount"
                  type="number"
                  value={formData.minAmount}
                  onChange={(e) => setFormData({ ...formData, minAmount: e.target.value })}
                  className="sm:col-span-3"
                  placeholder="الحد الأدنى للمبلغ (د.ج)"
                  min="0"
                  step="0.01"
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
                <Label htmlFor="maxAmount" className="text-right sm:col-span-1">
                  الحد الأقصى للمبلغ
                </Label>
                <Input
                  id="maxAmount"
                  type="number"
                  value={formData.maxAmount}
                  onChange={(e) => setFormData({ ...formData, maxAmount: e.target.value })}
                  className="sm:col-span-3"
                  placeholder="الحد الأقصى للمبلغ (د.ج)"
                  min={formData.minAmount || '0'}
                  step="0.01"
                />
              </div>
            </>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
            <Label htmlFor="hasMaxUsage" className="text-right sm:col-span-1">
              عدد الاستخدامات
            </Label>
            <div className="flex items-center space-x-2 space-x-reverse sm:col-span-3">
              <Checkbox
                id="hasMaxUsage"
                checked={hasMaxUsage}
                onCheckedChange={(checked) => setHasMaxUsage(!!checked)}
              />
              <label
                htmlFor="hasMaxUsage"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                تحديد الحد الأقصى لعدد مرات استخدام الخصم
              </label>
            </div>
          </div>

          {hasMaxUsage && (
            <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
              <Label htmlFor="maxUsage" className="text-right sm:col-span-1">
                الحد الأقصى للاستخدام
              </Label>
              <Input
                id="maxUsage"
                type="number"
                value={formData.maxUsage}
                onChange={(e) => setFormData({ ...formData, maxUsage: e.target.value })}
                className="sm:col-span-3"
                placeholder="الحد الأقصى لعدد مرات استخدام الخصم"
                min="1"
                step="1"
              />
            </div>
          )}
        </div>
      </form>
    </AnimatedDialog>
  );
}
